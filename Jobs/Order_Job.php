<?php
/**
 * 套票子票失败——取消订单队列
 *
 * 注：不可能取消半年前的 不兼容订单分库
 *
 * Class Order_Job
 */

use Business\JavaApi\Order\Query\OrderTrack;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Order\OrderTourist;
use Business\Order\RefundApprovalCenterService\AbstractApprovalCenterService;
use Library\Container;
use Library\JsonRpc\PftRpcClient;
use Library\Tools\YarClient;

use Business\Order\OrderTrackQuery;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Model\Order\OrderTools;

if (!class_exists('ALLFunction')) {
    include HTML_DIR . '/ota/ALLFunction.php';
}

class Order_Job
{
    public function __destruct()
    {
    }

    public function perform()
    {
        $jobType = $this->args['job_type'];
        $jobData = $this->args['job_data'];

        //记录日志
        pft_log('order/job', json_encode([$this->args], JSON_UNESCAPED_UNICODE));

        if ($jobType == 'track_async') {
            //写入订单追踪记录的时候，异步写入其他的任务数据
            $trackId   = $jobData['track_id'];
            $trackData = $jobData['track_data'];

            $ordernum       = $trackData['ordernum'];
            $action         = $trackData['action'];
            $tid            = $trackData['tid'];
            $tnum           = $trackData['tnum'];
            $leftNum        = $trackData['left_num'];
            $source         = $trackData['source'];
            $terminal       = $trackData['terminal'];
            $branchTerminal = $trackData['branchTerminal'];
            $idCard         = $trackData['id_card'];
            $siteId         = $trackData['SalerID'];
            $insertTime     = $trackData['insertTime'];
            $operMember     = $trackData['oper_member'];
            $msg            = $trackData['msg'];
            $isPrintPay     = isset($trackData['is_print_pay']) ? $trackData['is_print_pay'] : 0; //0不做识别 1仅取票  2是支付+取票

            // 添加历史库追踪记录
            $subOrderTrack = new \Model\Order\SubOrderQuery\SubOrderTrack();
            $historyRes    = $subOrderTrack->addTrack($ordernum, $action, $tid, $tnum, $leftNum, $source, $terminal,
                $branchTerminal, $idCard, $operMember, $siteId, $insertTime, $msg);

            //入队列
            //$esData         = $trackData;
            //$esData['id']   = $trackId;
            //$syncEsQueueRes = \Library\Resque\Queue::push('order', 'SyncEsOrder_Job',
            //    ['method' => 'insert', 'params' => json_encode($esData)]);

            ////添加进es追踪表 查看 是否同步
            //$syncData   = [
            //    'track_id'    => $trackId,
            //    'ordernum'    => $ordernum,
            //    'status'      => 1,
            //    'create_time' => strtotime($insertTime),
            //    'update_time' => strtotime($insertTime),
            //];
            //$esSyncTask = new \Model\Tools\EsSyncTask();
            //$esRes      = $esSyncTask->addEsStatusData($syncData);

            $memo = [];
            if ($isPrintPay) {
                $memo['is_print_pay'] = $isPrintPay;
            }
            if ($action == 5){   //新版终端报表需要  //这边都是快速验证才会在这边去写入实时报表
                $memo['verify_terminal'] = $terminal ?? 0;
                $memo['real_source']     = $source;
                $memo['real_terminal']   = $terminal ?? 0;
                $memo['is_quick_check']     = $trackData['is_quick_check'] ?? false;
            }elseif (in_array($action,[6,7])){ //撤销撤改
                $memo['refund_terminal'] = $terminal ? $terminal : $branchTerminal; //todo 不知道为啥取消那边要把传入的终端号插入到分终端字段上，这边只能跟着处理
                $memo['real_source']     = $source;
            }
            if(isset($trackData['ext_content']) && !empty($trackData['ext_content'])){
                $extContent = json_decode($trackData['ext_content'],true);
                if(isset($extContent['serial_number']) && !empty($extContent['serial_number'])){
                    $memo['serial_number'] = $extContent['serial_number'];
                }
            }
            //添加进实时报表任务记录表
            $realTimeBiz = new \Business\Report\RealTimeReportSet();
            $realTimeRes = $realTimeBiz->addRealTimeTask(0, $tid, $action, $ordernum, $tnum, $operMember,
                $branchTerminal, $insertTime, $siteId,$source, $memo);

            if ($branchTerminal && $action == 5) {
                $realTimeBiz->addRealTimeTask(0, $tid, $action, $ordernum, $tnum, $operMember,
                    0, $insertTime, $siteId);
            }

            //记录结果
            $logRes = json_encode([
                'track_id'    => $trackId,
                'history_res' => $historyRes,
                //'es_res'      => $esRes,
                'real_res'    => $realTimeRes,
                //'in_queue'    => $syncEsQueueRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('order/track_async', $logRes);

        }
        elseif ($jobType == 'check_async') {
            //订单快速验证，异步写入其他的任务数据
            $trackId   = $jobData['track_id'];
            $checkData = $jobData['track_data'];

            $ordernum       = $checkData['ordernum'];
            $orderInfo      = $checkData['orderInfo'];
            $tid            = $checkData['tid'];
            $lid            = $checkData['lid'];
            $canCheckedNum  = $checkData['canCheckedNum'];
            $mainTerminal   = $checkData['mainTerminal'];
            $branchTerminal = $checkData['branchTerminal'];
            $memberId       = $checkData['memberId'];
            $source         = $checkData['source'];
            $dtime          = $checkData['dtime'];
            $checkOrderNum  = $checkData['checkOrderNum'] ?? 1;  //是否今天验证过，验证过就不加1 否则加1，之前有问题给他优化了
            $extContent     = $checkData['ext_content'] ?? [];  //是否今天验证过，验证过就不加1 否则加1，之前有问题给他优化了
            $orderTools     = new \Model\Order\OrderTools();
            $newOrderInfo   = $orderTools->getOrderInfo($ordernum);
            //如果是团单
            $teamRes = -1;
            //判断是否是团单 或者报团计调下单,16渠道为计调下单，因为可以通过计调下单把订单变为团单，这边做个特殊处理
            //16-计调下单 24-团队订单 44-报团计调下单 10-云票务
            if (in_array($orderInfo['ordermode'], [24, 44, 16])) {
                $nowBranchUsedNum = $checkData['nowBranchUsedNum'];
                $trueNum          = $canCheckedNum - $nowBranchUsedNum;
                //判断下是否所有团单都已经验证
                $teamOrderModel = new \Model\Order\TeamOrderSearch();
                $teamOrderInfo  = $teamOrderModel->getMainOrderInfoBySonOrder($ordernum);
                if (!empty($teamOrderInfo)) {
                    //团队订单号
                    $teamOrderId       = $teamOrderInfo['main_ordernum'];
                    $teamOrderReportBz = new \Business\TeamOrder\TeamOrderReport();
                    if ($branchTerminal == 0 && $source != 53 && $trueNum > 0) {
                        $teamOperMember = empty($memberId) ? 1 : $memberId;
                        $teamOrderReportBz->addTeamRealTask($teamOrderId, $ordernum, $trueNum,
                            \Library\Constants\Team\TeamConst::_REAL_TASK_CHECK_, $teamOperMember, 1, $source);
                    }
                    $parentOrderInfo = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrderId);
                    if (!empty($parentOrderInfo)) {
                        $needUpdateTeamOrder = true;
                        $sonOrderId          = array_column($parentOrderInfo, 'son_ordernum');

                        $sonOrderInfo = $orderTools->getOrderInfo($sonOrderId, 'status');
                        if (!empty($sonOrderInfo) && is_array($sonOrderInfo)) {
                            foreach ($sonOrderInfo as $value) {
                                if (!in_array($value['status'], [1, 3, 8])) {
                                    //如果子订单不是已验证 取消 完结 那就不需要更新团队订单状态
                                    $needUpdateTeamOrder = false;
                                }
                            }
                        }

                        if ($needUpdateTeamOrder && $teamOrderInfo['status'] != 2) {
                            $teamOrderModel->setTeamOrderByOrder($teamOrderId, ['status' => 2]);
                            $time      = time();
                            $trackData = [
                                'order_id'    => $teamOrderId,
                                'time'        => $time,
                                'status'      => 0,
                                'update_time' => $time,
                            ];
                            $teamRes   = $teamOrderModel->addTeamTrack($trackData);
                        }
                    }
                }
            }

            //统计终端的验证数据
            $summaryOrder = new \Model\Report\TerminalCollect();
            $summaryRes   = $summaryOrder->addData($branchTerminal ?: $mainTerminal, $tid, $lid, $dtime, $source,
                $canCheckedNum, $checkOrderNum, 0,
                $memberId);

            $statisticRes = -1;
            if ($branchTerminal != 0) {
                $realTimeBiz  = new \Business\Statistics\RealTimeStatistics();
                $statisticRes = $realTimeBiz->realtimeBranchTerminalCheckedTask($ordernum, $canCheckedNum,
                    $branchTerminal, $memberId, $checkData['dtime']);
            }
            //会员体系验证后添加积分
            $memberSystemBiz = new \Business\Member\MemberSystem();
            $memberSystemRes = $memberSystemBiz->orderCheckFinishAddPoint($ordernum, $source);
            $orderExpireRes  = ['msg' => '不需要判断过期'];
            //判断下订单是不是需要过期了
            if (in_array($newOrderInfo['status'], [0, 7])) {
                $orderTouristBiz = new OrderTourist();
                $orderExpireRes  = $orderTouristBiz->orderTouristExpireChangeOrderStatus($ordernum);
            }
            //记录结果
            $logRes = json_encode([
                'track_id'     => $trackId,
                'teamRes'      => $teamRes,
                'summaryRes'   => $summaryRes,
                'statisticRes' => $statisticRes,
                'memberSystem' => $memberSystemRes,
                'expire_order' => $orderExpireRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('order/check_async', $logRes);
        }
        else if ($jobType == 'cancel_async_task') {
            //取消订单成功后的异步任务
            $ordernum        = $jobData['ordernum'];
            $isPayed         = $jobData['isPayed'];
            $orderInfo       = $jobData['orderInfo'];
            $ticketInfo      = $jobData['ticketInfo'];
            $refundData      = $jobData['refundData'];
            $refundParams    = $jobData['refundParams'];
            $pftSerialNumber = $refundData['pft_serial_number'];

            $orderAsyncBiz  = new \Business\Order\OrderAsync();
            $asyncHandleRes = $orderAsyncBiz->asyncRefundTask($ordernum, $isPayed, $orderInfo, $ticketInfo,
                $refundData, $refundParams);

            $logData = json_encode([
                'key'             => 'cancel_async_task',
                'ordernum'        => $ordernum,
                'isPayed'         => $isPayed,
                'refundData'      => $refundData,
                'pftSerialNumber' => $pftSerialNumber,
                'res'             => $asyncHandleRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('order_refund/debug', $logData);

        }
        else if ($jobType == 'audit_async_task') {
            //取消订单退票审核申请成功后的异步任务
            $ordernum        = $jobData['ordernum'];
            $orderInfo       = $jobData['orderInfo'];
            $auditInfo       = $jobData['auditInfo'];
            $pftSerialNumber = $auditInfo['pft_serial_number'];
            $isRefundAudit   = $jobData['is_refund_audit'];
            $orderAsyncBiz   = new \Business\Order\OrderAsync();
            $asyncHandleRes  = $orderAsyncBiz->asyncAuditTask($ordernum, $orderInfo, $auditInfo, $isRefundAudit);

            $logData = json_encode([
                'key'             => 'audit_async_task',
                'ordernum'        => $ordernum,
                'auditInfo'       => $auditInfo,
                'pftSerialNumber' => $pftSerialNumber,
                'isRefundAudit'   => $isRefundAudit,
                'res'             => $asyncHandleRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('order_refund/debug', $logData);
        }
        else if ($jobType == 'batch_order_fail_cancel') {
            $orderNumArr = $jobData['ordernum_arr'];
            $errMsg      = $jobData['error'];
            foreach ($orderNumArr as $ordernum) {
                $cancelNum        = -1;
                $cancelType       = 'common';
                $cancelRemarkArr  = ['remark' => $errMsg];
                $cancelSiteArr    = [];
                $cancelPersonArr  = [];
                $cancelSpecialArr = [
                    'is_need_audit' => false,
                    'is_rollback'   => true,
                    'is_syntax_rollback' => true, //是否系统原因导致的回滚，需要退回码费等特殊款项
                ];
                $cancelChannel    = \Library\Constants\OrderConst::BATCH_ORDER_CANCEL;
                \Library\Tools\Helpers::platformRefund($ordernum, $cancelNum, 1, $cancelChannel, $cancelType,
                    '', $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr
                );
            }
        }
        else if ($jobType == 'package_cancel') {
            //套票子票失败——取消订单
            $mainOrdernum = $jobData['ordernum'];
            $visibleArr   = isset($jobData['visibleArr']) ? $jobData['visibleArr'] : 0; //温泉手牌号
            $applyDid     = isset($jobData['apply_did']) ? $jobData['apply_did'] : 0; //温泉手牌绑定的供应商
            $errInfo      = $jobData['error_info'];

            //是否发送短信标识
            $smsNotify = isset($jobData['smsNotify']) ? $jobData['smsNotify'] : 1;
            $errorMemo = isset($jobData['error_memo']) ? $jobData['error_memo'] : '子票下单失败,错误';
            $server    = \Library\Tools\Helpers::GetSoapInside();

            $model      = new \Library\Model('localhost');
            $orderTools = new \Model\Order\OrderTools();
            $orderInfo  = $orderTools->getOrderInfo(strval($mainOrdernum),
                'member,ordertel,lid,pid,ordermode,ordername,tid');

            //云票务套票子票退单 主票的取消人记下单人
            if (!$orderInfo) {
                return false;
            }

            // $opId = 0;
            $opId = $orderInfo['member'];
            if ($orderInfo['ordermode'] == 10) {
                //云票务
                //$filter = [
                //    'ordernum' => $mainOrdernum,
                //    'action'   => 0,
                //];

                //$trackTable = \Library\Constants\Table\MainTableConst::TABLE_ORDER_TRACK;
                //$actionInfo = $model->table($trackTable)->where($filter)->find();

                //订单查询迁移二期
                $orderTrackQueryLib = new OrderTrackQuery();
                $actionInfo         = $orderTrackQueryLib->getTrackInfoByOrderAction([strval($mainOrdernum)], [0]);

                if (!empty($actionInfo)) {
                    $opId = $actionInfo['oper_member'];
                }
            }

            //取消的时候，不需要退票审核
            $cancelMsg        = "{$errorMemo}:{$errInfo}";
            $cancelExec       = 0; //强制取消
            $cancelNum        = -1;
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelMsg];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit' => false,
                'is_rollback'   => true,
                'is_syntax_rollback' => true, //是否系统原因导致的回滚，需要退回码费等特殊款项
            ];
            $cancelChannel    = \Library\Constants\OrderConst::PC_CANCEL;
            $result           = \Library\Tools\Helpers::platformRefund($mainOrdernum, $cancelNum, $opId, $cancelChannel,
                $cancelType,
                '', $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            \Business\Order\Modify::cancelLog([
                $mainOrdernum,
                $cancelNum,
                $opId,
                $cancelChannel,
                $cancelType,
                '',
                $cancelRemarkArr,
                $cancelSiteArr,
                $cancelPersonArr,
                $cancelSpecialArr,
            ], $result, '套票子票失败取消订单');

            $landApi  = new \Business\CommodityCenter\Land();
            $landArr  = $landApi->queryLandMultiQueryById([$orderInfo['lid']]);
            $landInfo = $landArr[0] ?? [];

            $sellerID = $landInfo['apply_did'];

            $newErrorMsg = $result['msg'] ? $result['msg'] : $result['data']['err_msg'];

            if ($result['code'] != 200) {
                //如果有取消失败的套票，统一进行告警通知
                $notifyMsg = "套票订单回滚失败：子票取消失败\n主票订单号：{$mainOrdernum}\n失败原因：{$newErrorMsg} \n处理方法：建议商家通过强制取消进行取消\n取消时间：" . date('Y-m-d H:i:s');
                \Library\Tools\Helpers::sendDingTalkGroupRobotMessageRaw($notifyMsg,
                    \Library\Constants\DingTalkRobots::BANK_JOURNAL);
                pft_log('queue/order_cancel', $notifyMsg);

                //套票名称
                $commodityProductBiz = new \Business\CommodityCenter\Product();
                $proname             = $commodityProductBiz->getProductInfoById($orderInfo['pid'])['p_name'];
                $mobile              = $landInfo['fax'];
                if (!$landInfo['fax']) {
                    $memberBiz = new \Business\Member\Member();
                    $applyInfo = $memberBiz->getInfo($applyDid);
                    $mobile    = $applyInfo['mobile'] ?? '';
                }

                if ($mobile) {
                    $warnMsg  = "存在：[{$proname}] 下单异常，请尽快到后台处理。订单号：{$mainOrdernum}，游客信息：{$orderInfo['ordername']} {$orderInfo['ordertel']} ，异常情况：{$errInfo}";
                    $messageServiceApi = Container::pull(MessageService::class);
                    [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'NOTIFY_WARN', $mobile,
                        [$warnMsg], $applyDid, $mainOrdernum, '套票取消异常，通知供应商', '', '', 1);
                    if ($approval) {
                        if (!EnvUtil::isRelease()) {
                            pft_log('message_service', json_encode(['套票取消异常，通知供应商', __METHOD__, [$mobile,
                                [$warnMsg], $applyDid, $mainOrdernum], $res], JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        /** @depreciate 放量结束后删除 */
                        //发送短信通知供应商
                        $smsSystem = new \Library\MessageNotify\SmsSystem();
                        $smsObj    = $smsSystem->getPlatformObject();
                        $warnMsg   = "存在：[{$proname}] 下单异常，请尽快到后台处理。订单号：{$mainOrdernum}，游客信息：{$orderInfo['ordername']} {$orderInfo['ordertel']} ，异常情况：{$errInfo}";
                        $smsRes    = $smsObj->doSendSMS($mobile, ['{1}' => $warnMsg], 'NOTIFY_WARN');
                        if ($smsRes['code'] == 200) {
                            //收短息费
                            $accountApi = new \Business\JavaApi\Fund\AccountBalance();
                            $accountApi->smsFee($applyDid, 1, 10, $mainOrdernum, "package_cancel_fail_{$mainOrdernum}", 1,
                                '套票取消异常，通知供应商');
                        }
                        if (!EnvUtil::isRelease()) {
                            pft_log('message_service', json_encode(['套票取消异常，通知供应商.old', __METHOD__, [$mobile,
                                [$warnMsg], $applyDid, $mainOrdernum], $smsRes], JSON_UNESCAPED_UNICODE));
                        }
                    }
                }

                return false;
            }

            if ($result['code'] == 200 && $smsNotify == 1) {
                $member       = new \Business\Member\Member();
                $memberInfo   = $member->getInfo($sellerID);
//                $sellerMobile = $memberInfo['mobile'];
                $sellerMobile =  $landInfo['fax'];

                pft_log('debug/lee', json_encode([
                    '温泉套票子票下单 发送短信前: ',
                    'visibleArr: ' . json_encode($visibleArr),
                    'ordertel: ' . $orderInfo['ordertel'],
                    'mobile: ' . $memberInfo['mobile'],
                ], JSON_UNESCAPED_UNICODE));

                if ($visibleArr) {
                    $orderInfo['ordertel'] = $memberInfo['mobile'];
                }

                //是手牌购买套票才去做解绑的动作
                if ($mainOrdernum && $visibleArr && $applyDid) {
                    //订单被取消后 解绑主票手牌订单
                    $presumingBiz = new \Business\Product\Presuming();
                    foreach ($visibleArr as $key => $value) {
                        $cancelRes = $presumingBiz->presumingCancel($mainOrdernum, $value, $applyDid);

                        if ($cancelRes['code'] == 200) {
                            pft_log('presuming/success',
                                json_encode(['订单修改成功', '(ordernum: ' . $mainOrdernum . ' visible_no: ' . $value . ')'],
                                    JSON_UNESCAPED_UNICODE));
                        } else {
                            pft_log('presuming/fail', json_encode([
                                '系统异常',
                                '(ordernum: ' . $mainOrdernum . ' visible_no: ' . $value . ')',
                                json_encode($cancelRes),
                            ], JSON_UNESCAPED_UNICODE));
                        }
                    }
                }
                $PackOrder = new \Business\PackTicket\PackOrder();
                try {
                    $PackOrder->packageOrderFail($mainOrdernum,$visibleArr,$orderInfo,$errInfo,$sellerID,$sellerMobile);
                } catch (\Exception $e) {
                    pft_log('order/package', json_encode([
                        'packageOrderFail',$e->getMessage(),$mainOrdernum,$visibleArr,$orderInfo,$errInfo,
                        $sellerID,$sellerMobile
                    ]));
                }
                /**
                try {
                    $sms = new \Library\MessageNotify\Platform\FzZwxSms($orderInfo['ordertel']);
                    //有传入手牌号 发送短信至供应商手机号中
                    if ($visibleArr) {
                        $visibleNo = implode(',', $visibleArr);
                        $res2      = $sms->presumingPackageOrderFail($mainOrdernum, $visibleNo, ";错误信息:{$errInfo},",
                            $sellerMobile);
                        pft_log('debug/lee',
                            json_encode(['温泉套票子票下单 发送短信后: ', json_encode($res2), 'mobile: ' . $sellerMobile],
                                JSON_UNESCAPED_UNICODE));
                        pft_log('order/package',
                            "{$mainOrdernum}|发送短信通知,;供应商结果:" . json_encode($res2, JSON_UNESCAPED_UNICODE));
                    } else {

                        $res  = $sms->packageOrderFail($mainOrdernum); //您的套票订单{1}由于子票订单提交失败{2}已被系统自动取消。
                        $res2 = $sms->packageOrderFail($mainOrdernum, ";错误信息:{$errInfo},", $sellerMobile);
                        pft_log('order/package',
                            "{$mainOrdernum}|发送短信通知,分销商结果:" . json_encode($res, JSON_UNESCAPED_UNICODE)
                            . ';供应商结果:' . json_encode($res2, JSON_UNESCAPED_UNICODE));
                    }
                } catch (Exception $e) {
                    pft_log('order/package', "发送短信通知,分销商结果:" . $e->getMessage());
                }
                 */

                return true;
            }
        }
        else if ($jobType == 'third_cancel') {
            //如果平台下单成功但是三方系统下单失败的情况下，需要直接取消平台订单
            $ordernum  = strval($jobData['ordernum']);
            $errInfo   = strval($jobData['error_info']);
            $errorMemo = isset($jobData['error_memo']) ? $jobData['error_memo'] : '三方系统下单失败';

            $model      = new \Library\Model('localhost');
            $orderTools = new \Model\Order\OrderTools();
            $orderInfo  = $orderTools->getOrderInfo($ordernum,
                'member,ordertel,lid,pid,ordermode,ordername,tid');

            $opId = $orderInfo['member'] ?: 1;

            //取消的时候，不需要退票审核
            $cancelNum        = -1;
            $cancelType       = 'common';
            $cancelRemarkArr  = [
                'remark' => "{$errorMemo}:{$errInfo}",
            ];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit'          => false, //不需要退票审核
                'is_force_cancel'        => true, //是否强制退票，强制退票的情况下，有些逻辑不需要判断
                'is_cancel_third_system' => false, //不需要往三方系统取消
                'is_rollback'            => true,
                'is_cancel_notice_third' => false,//是否取消后通知第三方 （这边是下单失败的不需要通知）
                'is_syntax_rollback' => true, //是否系统原因导致的回滚，需要退回码费等特殊款项
            ];
            $cancelChannel    = \Library\Constants\OrderConst::THIRD_SUBMIT_FAIL_CANCEL;
            $reqSerialNumber  = '';

            $cancelRes = \Library\Tools\Helpers::platformRefund($ordernum, $cancelNum, $opId, $cancelChannel,
                $cancelType, $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);

            //日志日志
            pft_log('order/cancel_third', json_encode([$ordernum, $cancelRemarkArr, $cancelRes]));

        }
        else if ($jobType == 'third_open_pay') {
            // 开放接口 三方系统下单成功后推送支付信息
            $orderNum   = $jobData['ordernum'];
            $applyId    = $jobData['ApplyId'];
            $landId     = $jobData['LandId'];
            $otaOpenBiz = new \Business\Ota\Open\Order();
            $pushPayRes = $otaOpenBiz->pushPay(['orderNum' => $orderNum, 'ApplyId' => $applyId, 'LandId' => $landId]);

            // 添加脚本运行日志
            pft_log('order/third_open_pay', json_encode([$pushPayRes, $jobData]));
        }
        else if ($jobType == 'third_open_cancel') {
            // 开放接口 三方系统回调取消审核结果太快 添加队列维护
            $orderNum     = $jobData['ordernum'];
            $action       = $jobData['action'];
            $surplusNum   = $jobData['surplusNum'];
            $fid          = $jobData['fid'];
            $auditRes     = $jobData['auditRes'];
            $auditNote    = $jobData['auditNote'];
            $refundAmount = $jobData['refundAmount'];
            $aid          = $jobData['aid'];
            $cancelMemo   = $jobData['cancelMemo'];
            $auditId      = $jobData['auditId'];

            $allFunction  = new \ALLFunction();
            $cancelResArr = $allFunction->Refund_note($action, $orderNum, $surplusNum, $fid, $auditRes, $auditNote,
                true, $refundAmount, $aid, $cancelMemo, $auditId);

            if ($auditRes == true && isset($cancelResArr['code']['code']) && $cancelResArr['code']['code'] == 200) {
                //4修改订单  3取消订单
                $oStatus = $surplusNum > 0 ? 4 : 3;
                $data    = ['oStatus' => $oStatus, 'oStnum' => $surplusNum];
                // 部分取消 或 全部取消订单
                $apiOrderModel   = new \Model\Ota\AllApiOrderModel();
                $apiOrderInfoArr = $apiOrderModel->getInfoByPftOrder($orderNum, 'id, apiOrder');
                $filter          = ['id' => $apiOrderInfoArr['id']];
                $apiOrderModel->updateTable($data, $filter);
            }

            // 添加脚本运行日志
            pft_log('order/third_open_cancel', json_encode([$cancelResArr, $jobData]));
        }
        else if ($jobType == 'taobao_cancel_back') {
            $pftOrder    = $jobData['pftOrder'];
            $surplusNum  = $jobData['surplusNum'];
            $otaOrderBiz = new \Business\Ota\Order();
            $reqRes      = $otaOrderBiz->noticeTaobaoCancelOrderqueue($pftOrder, $surplusNum);

            //日志日志
            pft_log('taobao/cancel/debug', json_encode([$reqRes, $jobData]));
        }
        else if ($jobType == 'rollback_order') {
            //下单过程中，平台内支付异常下单回滚或是往第三方系统下单失败下单和内部支付回滚
            $rollbackType = $jobData['rollbackType']; //回滚类型 ordered:下单后回滚, payed:支付后回滚
            $ordernum     = $jobData['ordernum'];
            $showInfo     = $jobData['showInfo'];
            $memberId     = $jobData['memberId'];
            $remotenum    = $jobData['remotenum'];
            $errMsg       = isset($jobData['errMsg']) ? $jobData['errMsg'] : '';

            if ($rollbackType == 'ordered') {
                //下单后回滚，回滚订单，库存，限购，演出等信息

            } elseif ($rollbackType == 'payed') {
                //支付后回滚，回滚订单，库存，限购，演出，支付

                //调用统一的退票接口
                $cancelNum        = -1;
                $cancelType       = 'common';
                $cancelRemarkArr  = ['remark' => $errMsg];
                $cancelSiteArr    = [];
                $cancelPersonArr  = [];
                $cancelSpecialArr = [
                    'is_need_audit'          => false,
                    'is_rollback'            => true,
                    'is_cancel_notice_third' => false,//是否取消后通知第三方 （这边是下单失败的不需要通知）
                    'is_syntax_rollback' => true, //是否系统原因导致的回滚，需要退回码费等特殊款项
                ];
                $opId             = 1;
                $cancelChannel    = \Library\Constants\OrderConst::PAY_FAIL_CANCEL;
                $refundRes        = \Library\Tools\Helpers::platformRefund($ordernum, $cancelNum, $opId, $cancelChannel,
                    $cancelType,
                    '', $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
                \Business\Order\Modify::cancelLog([
                    $ordernum,
                    $cancelNum,
                    $opId,
                    $cancelChannel,
                    $cancelType,
                    '',
                    $cancelRemarkArr,
                    $cancelSiteArr,
                    $cancelPersonArr,
                    $cancelSpecialArr,
                ], $refundRes, '支付后回滚');

                //顺便删除远端订单号的信息
                if ($memberId && $remotenum && $refundRes['code'] == 200) {
                    //$submitModel = new \Model\Order\OrderSubmit('localhost_wsdl');
                    //$delRes      = $submitModel->delRemoteOrdernum($memberId, $remotenum);

                    $queryParams = [$remotenum, $memberId];
                    $queryRes    = \Business\JavaApi\Order\Query\Container::query('transactionRemoteOrder',
                        'removeUniqueRemoteOrder', $queryParams);
                    $delRes      = false;
                    if ($queryRes['code'] == 200) {
                        $delRes = $queryRes['data'];
                    }
                }
            } elseif ($rollbackType == 'showed') {
                //回滚演出座位
                $submitBiz = new \Business\Order\BaseSubmit();
                $refundRes = $submitBiz->rollbackShowSeat($showInfo);
                $delRes    = -1;
            }

            pft_log('order/rollback', json_encode([$jobData, $refundRes, $delRes]));
        }
        else if ($jobType == 'order_pool') {  // avalon: 备注, 这块逻辑已经废弃，并不会再执行了。
            //下单的过程中如果凭证码不够，通过异步任务是初始化凭证码池子
            $lid          = $jobData['lid'];
            $ptype        = $jobData['ptype'];
            $resource     = $jobData['resource'];
            $terminalType = $jobData['terminalType'];

            //添加一个120秒的锁
            $cacheLib = \Library\Cache\Cache::getInstance('redis');
            $cacheKey = "order_pool_lock:{$lid}:{$ptype}:{$resource}:{$terminalType}";
            $lockTime = 120;

            $lockRes = $cacheLib->lock($cacheKey, 1, $lockTime);
            if (!$lockRes) {
                //还有任务在执行
                $res = ['code' => 500, 'msg' => '还有任务在执行中'];
            } else {
                //拿到执行锁
                $res = \Library\OrderCodePool::supplement($lid, $ptype, $resource, $terminalType);

                //释放锁
                $cacheLib->rm($cacheKey);
            }

            pft_log('order/pool', json_encode([$jobData, $res], JSON_UNESCAPED_UNICODE));
        }
        else if ($jobType == 'offline_order_check') {
            $this->offlineOrderCheck($jobData);
        }
        else if ($jobType == 'sync_pay_status') {
            //终端订单状态更新失败情况下，延时队列重试
            $ordernum     = $jobData['ordernum'];
            $delaySeconds = $jobData['delay_seconds'];
            $runSql       = $jobData['run_sql'];

            if ($delaySeconds > 20) {
                //不执行了
                $res = '延迟执行的时间超过20S，不进行重试了';
            } else {
                //继续执行
                $terminalDb = new \Model\Order\TerminalOrderHandler();

                //先进行查询
                $where     = ['ordernum' => strval($ordernum)];
                $table     = \Library\Constants\Table\MainTableConst::TABLE_SS_ORDER;
                $orderInfo = $terminalDb->table($table)->where($where)->field('ordernum,pay_status')->find();

                if ($orderInfo && $orderInfo['pay_status'] == 1) {
                    //主要是现在的那些0元的票，直接就是支付状态同步到终端数据库了，导致这边一直做无用功
                    //如果是已经支付的就不继续了
                    $res = '已经是支付的了';
                } else {
                    $terminalRes = $terminalDb->execute($runSql);

                    //如果还是失败，继续入延迟队列
                    if (!$terminalRes) {
                        $res = '处理失败，继续入延迟队列';

                        $delaySeconds = $delaySeconds + 3;
                        $delayData    = [
                            'job_type' => 'sync_pay_status',
                            'job_data' => [
                                'ordernum'      => $ordernum,
                                'delay_seconds' => $delaySeconds,
                                'run_sql'       => $runSql,
                            ],
                        ];
                        \Library\Resque\Queue::delay("+ {$delaySeconds} seconds", 'order', 'Order_Job', $delayData);
                    } else {
                        $res = '处理成功';
                    }
                }
            }

            pft_log('terminal_sql_error', json_encode([$ordernum, $res], JSON_UNESCAPED_UNICODE));
        }
        else if ($jobType == 'accept_checkData_sync') {
            $ordernum = $jobData['ordernum'];

            $orderTools = new \Model\Order\OrderTools();
            $orderInfo  = $orderTools->getOrderInfo($ordernum, 'ss.status');
            //todo 这个判断放尾巴
            $orderExpireRes = ['msg' => '不需要过期'];
            if (in_array($orderInfo['status'], [0, 7])) {
                $orderTouristBiz = new OrderTourist();
                $orderExpireRes  = $orderTouristBiz->orderTouristExpireChangeOrderStatus($ordernum);
            }
            //记录结果
            $logRes = json_encode([
                'ordernum'     => $ordernum,
                'expire_order' => $orderExpireRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('order/accept_check_sync', $logRes);
        }
        else if ($jobType == 'online_pay_fail_refund') {
            //这边都是未支付取消的
            $remark      = $jobData['remark'] ? mb_substr($jobData['remark'], 0, 20, 'utf-8') : '';
            $opId        = $jobData['opId'];
            $ordernum    = $jobData['ordernum'];
            $allOrdernum = [$ordernum];
            //调用统一的退票接口
            $cancelNum        = -1;
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $remark];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit' => false,
                'is_rollback'   => true,
            ];
            //获取订单信息
            $orderModel  = new OrderTools();
            $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid,ss.pay_status';
            $detailField = 'de.concat_id,de.aids';
            $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
            if ($orderInfo['pay_status'] != 2){  //不是未支付的不处理了
                return true;
            }
            if ($orderInfo['concat_id'] && $orderInfo['ordernum'] == $orderInfo['concat_id']) {
                //联票顺便取消掉下面的
                $linkOrderList = $orderModel->getLinkOrdersInfo($orderInfo['ordernum'],
                    'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

                if ($linkOrderList) {
                    $allOrdernum = array_unique(array_merge($allOrdernum, array_column($linkOrderList, 'ordernum')));
                }
            }
            $cancelChannel = \Library\Constants\OrderConst::ONLINE_PAY_FAIL_CANCEL;
            foreach ($allOrdernum as $key => $value) {
                $refundRes = \Library\Tools\Helpers::platformRefund($value, $cancelNum, $opId, $cancelChannel,
                    $cancelType, '', $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
                //记录结果
                $logRes = json_encode([
                    'ordernum' => $ordernum,
                    'res'      => $refundRes,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('order/online_pay_fail_refund', $logRes);
            }
        }
        elseif($jobType == 'batch_refund_job'){
            $revokeParams= $jobData['revokeParams'] ?? [];
            $refundParams= $jobData['refundParams'] ?? [];
            if(!empty($revokeParams)){
                $refundBiz    = new \Business\Order\Refund();
                $core        = new \Library\Constants\Api\TerminalConst();
                foreach ($revokeParams as $revokeItem){
                    if(isset($revokeItem['cancel_special_arr']['batch_refund_more_idx'])){
                        $actionName = $revokeItem['surplus_num'] == 0 ? '撤销' : '撤改';
                        $batchNum = count($revokeItem['cancel_special_arr']['batch_refund_more_idx']);
                        $revokeItem['cancel_audit_remark'] = $revokeItem['cancel_audit_remark']."_{$actionName}{$batchNum}张}";
                    }

                    $revokeResArr = $refundBiz->TerminalRevoke(
                        $revokeItem['order_num'],
                        $revokeItem['surplus_num'],
                        $revokeItem['terminal'],
                        $revokeItem['op_id'],
                        $revokeItem['cancel_idx_arr'],
                        0,
                        0,
                        $revokeItem['revoke_num'],
                        $revokeItem['cancel_audit_remark'],
                        false,
                        $revokeItem['cancel_special_arr']
                    );
                    if ($revokeResArr['code'] == $core::CORE_REVOKE_CHECKED) {
                        //收口退票成功统一推送
                        pft_log('debug/refundKafka', json_encode(['批量退成功',$revokeResArr],JSON_UNESCAPED_UNICODE));
                        continue;
                    }
                    elseif($revokeResArr['code'] == $core::CORE_REVOKE_SUCCESS){
                        $dataKafka = [
                            'op_id' => $revokeItem['op_id'],
                            'refund_status'  => 0,// 0审核中 1退票成功  2失败
                            'after_sale_num' => $revokeItem['cancel_special_arr']['after_sale_num'],
                            'req_serial_number' => $revokeItem['req_serial_number'] ?? '',
                            'order_num' => $revokeItem['order_num'],
                            'refund_idx' => empty($revokeItem['cancel_idx_arr']) ? ["1"] : $revokeItem['cancel_idx_arr'],
                            'cancel_type' => "revoke",
                            'order_status' => '',
                            'cancel_num' => $revokeItem['revoke_num'],
                            'left_num' => $revokeItem['surplus_num'],
                            'is_retry' => $revokeItem['cancel_special_arr']['is_retry'] ?? false,
                            'msg' => '撤销撤改申请成功'
                        ];
                        \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                    }
                    else{
                        $dataKafka = [
                            'op_id' => $revokeItem['op_id'],
                            'refund_status'  => 2,// 0审核中 1退票成功  2失败
                            'after_sale_num' => $revokeItem['cancel_special_arr']['after_sale_num'],
                            'req_serial_number' => $revokeItem['req_serial_number'] ?? '',
                            'order_num' => $revokeItem['order_num'],
                            'refund_idx' => empty($revokeItem['cancel_idx_arr']) ? ["1"] : $revokeItem['cancel_idx_arr'],
                            'cancel_type' => "revoke",
                            'order_status' => '',
                            'cancel_num' => $revokeItem['revoke_num'] ?? 0,
                            'left_num' => $revokeItem['surplus_num'] ?? 0,
                            'is_retry' => $revokeItem['cancel_special_arr']['is_retry'] ?? false,
                            'msg' => "撤销撤改失败,错误原因：[{$revokeResArr['code']}]{$revokeResArr['msg']}"
                        ];
                        pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka,$revokeResArr],JSON_UNESCAPED_UNICODE));
                        \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                    }
                }
            }
            if(!empty($refundParams)){
                foreach ($refundParams as $refundItem){
                    if(isset($refundItem['cancelSpecialArr']['batch_refund_more_idx'])){
                        $actionName = '取消';
                        $batchNum = count($refundItem['cancelSpecialArr']['batch_refund_more_idx']);
                        $refundItem['cancelRemarkArr'] = $refundItem['cancelRemarkArr']."_{$actionName}{$batchNum}张}";
                    }
                    $res = Helpers::platformRefund(
                        $refundItem['orderNum'],
                        $refundItem['cancelNum'],
                        $refundItem['opId'],
                        $refundItem['cancelChannel'],
                        $refundItem['cancelType'],
                        $refundItem['reqSerialNumber'],
                        $refundItem['cancelRemarkArr'],
                        $refundItem['cancelSiteArr'],
                        $refundItem['cancelPersonArr'],
                        $refundItem['cancelSpecialArr']
                    );
                    if ($res['code'] == 200) {
                        pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic',$res],JSON_UNESCAPED_UNICODE));
                        //收口退票成功统一推送
                        continue;
                    }
                    elseif($res['code'] == 1095){
                        $dataKafka = [
                            'op_id' => $refundItem['opId'],
                            'refund_status'  => 0,// 0审核中 1退票成功  2失败
                            'after_sale_num' => $refundItem['cancelSpecialArr']['after_sale_num'],
                            'req_serial_number' => $res['data']['req_serial_number'] ?? '',
                            'order_num' => $refundItem['orderNum'],
                            'refund_idx' => empty($refundItem['cancelPersonArr']['person_index']) ? ["1"]:[$refundItem['cancelPersonArr']['person_index']],
                            'cancel_type' => $refundItem['cancelType'],
                            'order_status' => '',
                            'cancel_num' => $res['data']['cancel_num'],
                            'left_num' => $res['data']['left_num'],
                            'is_retry' => $refundItem['cancelSpecialArr']['is_retry'] ?? false,
                            'msg' => '退票申请成功'
                        ];
                        \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                    }
                    else{
                        $dataKafka = [
                            'op_id' => $refundItem['opId'],
                            'refund_status'  => 2,// 0审核中 1退票成功  2失败
                            'after_sale_num' => $refundItem['cancelSpecialArr']['after_sale_num'],
                            'req_serial_number' => $res['data']['req_serial_number'] ?? '',
                            'order_num' => $refundItem['orderNum'],
                            'refund_idx' => empty($refundItem['cancelPersonArr']['person_index']) ? ["1"]:[$refundItem['cancelPersonArr']['person_index']],
                            'cancel_type' => $refundItem['cancelType'],
                            'order_status' => '',
                            'cancel_num' => $res['data']['cancel_num'] ?? 0,
                            'left_num' => $res['data']['left_num'] ?? 0,
                            'is_retry' => $refundItem['cancelSpecialArr']['is_retry'] ?? false,
                            'msg' => '退票失败,错误原因：'."[{$res['code']}]{$res['msg']}".($res['data']['err_msg']??'')
                        ];
                        pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka,$res,$refundParams],JSON_UNESCAPED_UNICODE));
                        \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                    }
                }
            }
        }
        elseif($jobType == 'batch_order_cancel') {
            $revokeList = $jobData['revoke_list'];
            $cancelList = $jobData['cancel_list'];
            foreach ($cancelList as $refundItem) {
                $res = \Library\Tools\Helpers::platformRefund($refundItem['order_num'], $refundItem['cancel_num'],$refundItem['op_id'] , $refundItem['cancel_channel'], $refundItem['cancel_type'],
                    $refundItem['req_serial_number'], $refundItem['cancel_remarkArr'], $refundItem['cancel_siteArr'], $refundItem['cancel_personArr'], $refundItem['cancel_specialArr']
                );
                if (isset($res['code']) && !in_array($res['code'], [200, 1095])) {
                    $dataKafka = [
                        'op_id' => $refundItem['op_id'],
                        'refund_status'  => 2,// 0审核中 1退票成功  2失败
                        'after_sale_num' => $refundItem['cancel_specialArr']['after_sale_num'],
                        'req_serial_number' => $res['res']['data']['req_serial_number'] ?? '',
                        'order_num' => $refundItem['order_num'],
                        'refund_idx' => empty($refundItem['cancel_personArr']['person_index']) ? ["1"] : [$refundItem['cancel_personArr']['person_index']],
                        'cancel_type' => $refundItem['cancel_type'],
                        'order_status' => '',
                        'cancel_num' => $res['res']['data']['cancel_num'] ?? 0,
                        'left_num' => $res['res']['data']['left_num'] ?? 0,
                        'is_retry' => $refundItem['cancel_specialArr']['is_retry'] ?? false,
                        'msg' => '退票失败,错误原因：'."[{$res['code']}]".($res['data']['err_msg']??'')
                    ];
                    pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka,$res,$refundItem],JSON_UNESCAPED_UNICODE));
                    \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                }
                elseif($res['code'] == 1095){
                    $dataKafka = [
                        'op_id' => $refundItem['opId'],
                        'refund_status'  => 0,// 0审核中 1退票成功  2失败
                        'after_sale_num' => $refundItem['cancel_specialArr']['after_sale_num'],
                        'req_serial_number' => $res['res']['data']['req_serial_number'] ?? '',
                        'order_num' => $refundItem['orderNum'],
                        'refund_idx' => empty($refundItem['cancel_personArr']['person_index']) ? ["1"] : [$refundItem['cancel_personArr']['person_index']],
                        'cancel_type' => $refundItem['cancel_type'],
                        'order_status' => '',
                        'cancel_num' => $res['res']['data']['cancel_num'],
                        'left_num' => $res['res']['data']['left_num'],
                        'is_retry' => $refundItem['cancel_specialArr']['is_retry'] ?? false,
                        'msg' => '退票申请成功'
                    ];
                    \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                }
                $logRes = json_encode([
                    'key' =>'batch_order_cancel',
                    'item' => $refundItem,
                    'res'      => $res,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('order/batch_order_cancel', $logRes);
            }

            foreach ($revokeList as $revokeItem) {
                $revokeErrMsg = '';
                $revokeResArr = [];
                $orderToolModel = new OrderTools();
                $orderInfoArr   = $orderToolModel->getOrderInfo($revokeItem['order_num'], 'id,tnum,aid,member,tid', false, false, 'apply.verified_num',false,false,true);
                if (empty($orderInfoArr['tnum']) || empty($orderInfoArr['verified_num'])) {
                    $revokeErrMsg = '订单参数错误';
                    $logRes = json_encode([
                        'key' =>'batch_order_cancel',
                        'item' => $revokeItem,
                        'data' => $orderInfoArr,
                        'msg'  =>'订单参数错误'
                    ], JSON_UNESCAPED_UNICODE);
                    pft_log('order/batch_order_cancel', $logRes);
                    goto FAIL_NOTICE;
                }
                $revokeItem['surplus_num'] = $orderInfoArr['tnum'] - $revokeItem['revoke_num'];
                if ($revokeItem['surplus_num'] < 0) {
                    $revokeErrMsg = '撤改数量有误';
                    $logRes = json_encode([
                        'key' =>'batch_order_cancel',
                        'item' => $revokeItem,
                        'data' => $orderInfoArr['tnum'],
                        'msg'  =>'撤改数量有误'
                    ], JSON_UNESCAPED_UNICODE);
                    pft_log('order/batch_order_cancel', $logRes);
                    goto FAIL_NOTICE;
                }
                if($revokeItem['revoke_num'] > $orderInfoArr['verified_num']) {
                    $revokeErrMsg = '撤改数量超过已验证数';
                    $logRes = json_encode([
                        'key' =>'batch_order_cancel',
                        'item' => $revokeItem,
                        'data' => $orderInfoArr['verified_num'],
                        'msg'  =>'撤改数量超过已验证数'
                    ], JSON_UNESCAPED_UNICODE);
                    pft_log('order/batch_order_cancel', $logRes);
                    goto FAIL_NOTICE;
                }

                $refundBiz    = new \Business\Order\Refund();
                $core        = new \Library\Constants\Api\TerminalConst();
                sleep(2);
                $revokeResArr = $refundBiz->TerminalRevoke($revokeItem['order_num'], $revokeItem['surplus_num'], $revokeItem['terminal'], $revokeItem['op_id'],
                    $revokeItem['cancel_idx_arr'], 0, 0, $revokeItem['revoke_num'], $revokeItem['cancel_audit_remark'], false, $revokeItem['cancel_special_arr']);
                if (!in_array($revokeResArr['code'],[$core::CORE_REVOKE_CHECKED,$core::CORE_REVOKE_SUCCESS])) {
                    $revokeErrMsg = "撤销撤改失败,错误原因：[{$revokeResArr['code']}]{$revokeResArr['msg']}";
                    FAIL_NOTICE:
                    $dataKafka = [
                        'op_id' => $revokeItem['op_id'],
                        'refund_status'  => 2,// 0审核中 1退票成功  2失败
                        'after_sale_num' => $revokeItem['cancel_special_arr']['after_sale_num'],
                        'req_serial_number' => '',
                        'order_num' => $revokeItem['order_num'],
                        'refund_idx' => empty($revokeItem['cancel_idx_arr']) ? ["1"]:$revokeItem['cancel_idx_arr'],
                        'cancel_type' => "revoke",
                        'order_status' => '',
                        'cancel_num' => $revokeItem['revoke_num'] ?? 0,
                        'left_num' => $revokeItem['surplus_num'] ?? 0,
                        'is_retry' => $revokeItem['cancel_special_arr']['is_retry'] ?? false,
                        'msg' => $revokeErrMsg
                    ];
                    pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka,$revokeResArr],JSON_UNESCAPED_UNICODE));
                    \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                }
                elseif($revokeResArr['code'] == $core::CORE_REVOKE_SUCCESS){
                    $dataKafka = [
                        'op_id' => $revokeItem['opId'],
                        'refund_status'  => 0,// 0审核中 1退票成功  2失败
                        'after_sale_num' => $revokeItem['cancel_special_arr']['after_sale_num'],
                        'req_serial_number' => '',
                        'order_num' => $revokeItem['order_num'],
                        'refund_idx' => empty($revokeItem['cancel_idx_arr']) ? ["1"]:$revokeItem['cancel_idx_arr'],
                        'cancel_type' => "revoke",
                        'order_status' => '',
                        'cancel_num' => $revokeItem['revoke_num'],
                        'left_num' => $revokeItem['surplus_num'],
                        'is_retry' => $revokeItem['cancel_special_arr']['is_retry'] ?? false,
                        'msg' => '撤销撤改申请成功'
                    ];
                    \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                }
                $logRes = json_encode([
                    'key' =>'batch_order_cancel',
                    'item' => $revokeItem,
                    'res'  => $revokeResArr,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('order/batch_order_cancel', $logRes);
            }
        }
        elseif($jobType == 'order_approval_notice') {
            $refundApprovalService =  new \Business\Order\RefundApprovalCenterService\RefundApprovalService();
            $params= $jobData['params'] ?? [];
            $res= $jobData['res'] ?? [];
            $refundApprovalService->approvalNotify($params,$res);
        }

    }

    /**
     * 离线订单异步验证
     * @author: guanpeng
     *
     * @param  array  $jobData
     *
     * @date: 2019/6/5
     */
    private function offlineOrderCheck($jobData)
    {
        $orderHandler = new \Model\Order\OrderHandler();
        foreach ($jobData as $ordernumList) {
            foreach ($ordernumList as $ordernum) {
                $orderHandler->CheckOrderSimply($ordernum, 1, null, "断网同步验证,请求订单号:{$ordernum}");
            }
        }
    }
}
