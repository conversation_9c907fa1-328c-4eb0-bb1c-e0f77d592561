<?php

/**
 * Ota 代发短信
 *
 * @date 2021-05-26
 * <AUTHOR>
 */

use Business\JsonRpcApi\MessageService\MessageService;
use Library\Container;
use Library\MessageNotify\OrderNotify;
use Library\Util\EnvUtil;
use Model\Order\OrderTools;
use Business\AppCenter\ModuleCommon;
use Library\Cache\Cache;

class OtaSms_Job
{
    private $_muTianYuUserId = [8937327];

    //10分钟内发送过
    private $_sendInTenminutes = 'ota:sms:send:ten:minutes:orderNum:';

    /**
     * 执行入口
     *
     * <AUTHOR>
     * @date   2021-05-26
     */
    public function perform()
    {
        $appId       = $this->args['appId'];
        $orderNum    = $this->args['orderNum'];
        $apiCode     = $this->args['apiCode'];
        $sendSms     = $this->args['sendSms'];
        $applyDid    = $this->args['applyDid'];
        $apiQrcode   = $this->args['apiQrCode'];
        $isAgentSend = $this->args['isAgentSend'];
        $template    = $this->args['template'];
        $isResend    = $this->args['isResend'];
        $mobile      = $this->args['mobile'] ?? '';
        $noTenLimit  = $this->args['noTenLimit'] ?? false;
        $isLink      = $this->args['isLink'] ?? true;
        $mobileArea  = $this->args['mobileArea'] ?? '';

        $pid  = posix_getpid();
        $ppid = posix_getppid();
        @pft_log('order/ota_sms',
            json_encode(['args' => $this->args, 'noTenLimit' => $noTenLimit, 'processId' => $pid, 'ppid' => $ppid],
                JSON_UNESCAPED_UNICODE));

        //给订单号添加10分钟内不能重复发送的限制
        if (!$noTenLimit){
            $cache = Cache::getInstance('redis');
            if ($cache->get($this->_sendInTenminutes . $orderNum)) {
                @pft_log('order/ota_sms', '订单十分钟内已发送过短信' . $orderNum);
                return true;
            }
            $cache->set($this->_sendInTenminutes . $orderNum, 1, '', 600);
        }

        if ($isAgentSend) {
            $link         = true;
            $apiCodeArr   = explode(',', $apiCode);
            $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
            $moduleRes       = $moduleCommonBiz->getModuleIdByMenu(['sms_diy_temp']);
            $moduleId        = 0;
            foreach ($moduleRes as $item) {
                if ($item['menu_id'] == 'sms_diy_temp') {
                    $moduleId = $item['module_id'];
                }
            }
            $appCenterBiz = new ModuleCommon();
            $moduleInfo   = $appCenterBiz->getModuleUsedByUidAndMid($applyDid, $moduleId);
            if (empty($moduleInfo[0]) || $moduleInfo[0]['status'] == 2) {
                $template = "";
            }
            if (!empty($applyDid) && in_array($applyDid, $this->_muTianYuUserId)) {
                // 发送慕田峪的特殊短信
                if (!empty($apiQrcode)) {
                    $code = explode('=', $apiQrcode)[1] ?? '';
                    if (!empty($code)) {
                        $apiOrderModel   = new \Model\Ota\AllApiOrderModel();
                        // 获取订单在all_api_order中的数据
                        $apiOrderInfoArr = $apiOrderModel->getInfoByPftOrder($orderNum, 'id, apiOrder, handleStatus, remoteorder, bCode, fid, oStnum');
                        // 根据订单号查找订单信息
                        $orderReferModel = new \Model\Order\OrderTools('localhost');
                        $orderField      = 'contacttel, ordertel, playtime, remotenum';
                        $orderInfoArr    = $orderReferModel->getOrderInfo($orderNum, $orderField);
                        $playDate        = $orderInfoArr['playtime'];
                        $showOrderNum    = empty($orderInfoArr['remotenum']) ? $apiOrderInfoArr['apiOrder'] : $orderInfoArr['remotenum'];

                        // 调用发送短信
                        // $msg = "您的订单：{$apiOrder} 已支付成功。请持本人身份证或凭二维码验证入园，二维码链接:http://pw.mutianyugreatwall.com/api/online/qrcode?orderNo={$code} 。"
                        // . "入园时间：{$playDate}如需退票，请务必在入园日当天24点前申请。票务咨询电话：010-61626505转869（9：00－16：00）；营销咨询电话：010-61626022。";

                        $msg = "您的订单：{$showOrderNum} 已支付成功。请持本人身份证或凭二维码验证入园，二维码链接:http://pw.mutianyugreatwall.com/api/online/qrcode?orderNo={$code} 。"
                               . "入园时间：{$playDate}如需退票，请务必在入园日当天24点前申请。如有问题请戳：http://dpurl.cn/s/qima7c";

                        if (!empty($mobile)) {
                            $mobileNew = OrderNotify::getInternationalCall($mobileArea, $mobile);
                        } else {
                            $mobileNew = OrderNotify::getInternationalCall($orderInfoArr['mobile_area'], $orderInfoArr['ordertel']);
                        }
                        if (!ismobile($mobileNew)) {
                            @pft_log('order/ota_sms', json_encode(['外国人手机号不支持发送短信', $orderNum, $mobileNew, $msg], JSON_UNESCAPED_UNICODE));
                            return false;
                        }
                        $messageServiceApi = Container::pull(MessageService::class);
                        [$approval, $sendRes] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'OTADFDX-CL-SIGN', $mobileNew,
                            ['慕田峪长城', $msg], $applyDid, $orderNum, '代发短信', '', '', 1, true);
                        if ($approval) {
                            if (!EnvUtil::isRelease()) {
                                pft_log('message_service', json_encode(['代发短信.OTADFDX-CL-SIGN', __METHOD__, [$mobileNew,
                                    [$msg], $applyDid, $orderNum, '慕田峪长城'], $sendRes], JSON_UNESCAPED_UNICODE));
                            }
                        } else {
                            /** @deprecated 放量结束后删除 */
                            $smsLib = new \Library\MessageNotify\ChuangLanSms();
                            $sendRes = $smsLib->customMsg($applyDid, '慕田峪长城', $msg, $mobileNew, '',
                                $orderNum, true, '代发短信');
                            //                        $fzZwxSmsLib = new \Library\MessageNotify\Platform\FzZwxSms($mobileNew);
                            //                        $sendRes     = $fzZwxSmsLib->customMsg($applyDid, '慕田峪长城', $msg, $mobileNew, '',
                            //                            $orderNum, true, '代发短信');
                            // pft_log('yunv/test', json_encode([$applyDid, $msg, $orderInfoArr['ordertel'], $sendRes]));
                            if (!EnvUtil::isRelease()) {
                                pft_log('message_service', json_encode(['代发短信.OTADFDX-CL-SIGN.old', __METHOD__, [$mobileNew,
                                    [$msg], $applyDid, $orderNum, '慕田峪长城'], $sendRes], JSON_UNESCAPED_UNICODE));
                            }
                        }
                        if ($sendRes['code'] == 200) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                }
            } else {
                if (in_array($appId, [105, 108, 109])) {
                    foreach ($apiCodeArr as $key => $value) {
                        $apiOrderModel   = new \Model\Ota\AllApiOrderModel();
                        $apiOrderInfoArr = $apiOrderModel->getInfoByPftOrder($orderNum, 'id, apiOrder, handleStatus, remoteorder, bCode, fid, oStnum, apiCode');

                        $orderToolMdl = new OrderTools('slave');
                        $order_info   = $orderToolMdl->getOrderInfo($orderNum,
                            'ss.ordernum,ss.member,ss.lid,ss.tid,ss.aid,ss.tnum,ss.ordername,ss.ordertel,ss.begintime,ss.endtime,ss.code,ss.remsg,ss.paymode,ss.ordermode,ss.ordertime,ss.playtime,ss.status,ss.visitors,ss.remotenum',
                            'de.concat_id,de.memo,de.product_ext,de.ext_content,de.series');
                        if (empty($order_info) || empty($order_info['ordertel'])) {
                            return false;
                        }

                        //增加一下是否期票的判断,是的话短信发有效期
                        $orderNotifyBiz = new \Business\Order\OrderNotify();
                        $checkIsCountdownRes = $orderNotifyBiz->checkOrderValidityIsCountdown($order_info['begintime'], $order_info['endtime']);
                        $beginTime           = $checkIsCountdownRes['begin_time'];
                        $endTime             = $checkIsCountdownRes['end_time'];
                        if ($beginTime == $endTime) {
                            $timeNote = $beginTime;
                        } else {
                            $timeNote = "{$beginTime}至{$endTime}";
                        }

                        $extContentArr = json_decode($order_info['ext_content'], true) ?? [];
                        $timeShareInfo = '';
                        if (isset($extContentArr['sectionTimeStr'])) {
                            $timeShareInfo = $extContentArr['sectionTimeStr'];
                        }

                        $javaApi    = new \Business\CommodityCenter\Ticket();
                        $tickets_list  =  [$order_info['tid'] => $order_info['tnum']];
                        $tid        = array_keys($tickets_list);
                        $tickets    = $javaApi->queryTicketInfoByIds($tid, 'id,pid,title,getaddr,pre_sale', '', '', '', true);

                        $this->unit = '张';
                        $pname      = '';
                        $getaddr    = '';
                        $pid_list   = [];
                        $preSaleArr = [];
                        $order_ticket = [];
                        foreach ($tickets_list as $tid => $tnum) {
                            foreach ($tickets as $ticket) {
                                if (!isset($ticket['ticket'][$tid])) {
                                    continue;
                                }
                                if ($tid == $order_info['tid']) {
                                    $master_pid = $ticket['ticket'][$tid]['pid'];
                                }
                                $order_ticket[] = ['tid' => $tid, 'title' => $ticket['ticket'][$tid]['title'], 'tnum' => $tnum . $this->unit];
                                $getaddr    = $ticket['ticket'][$tid]['getaddr'];
                                $pname      .= "\n{$ticket['land']['title']}{$ticket['ticket'][$tid]['title']}{$tnum}{$this->unit},";
                                $pid_list[] = $ticket['ticket'][$tid]['pid'];
                                $preSaleArr[$tid] = $ticket['ticket'][$tid]['pre_sale'];
                            }
                        }
                        $pname = str_replace(["\n", " ", "　",], '', $pname);
                        $pname = str_replace(['【', '】'], ['[', ']'], $pname);
                        //取短信链接
                        $link = " 12301.cc/" . OrderNotify::url_sms($orderNum) . ' ';
                        //获取演出信息
                        $series      = $order_info['series'] ?? '';
                        $seriesInfo  = (new  \Business\Order\OrderList())->getShowInfo($series);
                        $showZone    = $seriesInfo['partition'] ?? ""; //座位分区
                        $showSite    = $seriesInfo['seat'] ?? ""; //座位号
                        $showRound   = $seriesInfo['performance'] ?? "";//演出场次
                        $performTime = strstr($showRound, ' ') ? explode(' ', $showRound)[1] : "";//演出时间
                        $showBegin   = $seriesInfo['show_begin'] ?? '';//演出开始时间
                        $showEnd     = $seriesInfo['show_end'] ?? '';//演出结束时间
                        if (empty($template) && empty($getaddr)) {
                            $template = "点击查看订单详情： {link}，凭证号：{code}，消费日期：{playtime}。您已成功购买了{pname}，此为凭证，请妥善保管。";
                        } else if (empty($template) && !empty($getaddr)) {
                            $getaddr  = str_replace("【", "[", $getaddr);
                            $getaddr  = str_replace("】", "]", $getaddr);
                            $template = "点击查看订单详情： {link}，凭证号：{code}，消费日期：{playtime}。您已成功购买了{pname}，{getaddr}，此为凭证，请妥善保管。";
                        } else {
                            $getaddr  = str_replace("【", "[", $getaddr);
                            $getaddr  = str_replace("】", "]", $getaddr);
                        }

                        if (strpos($timeNote, '至') != false) {
                            $template = str_replace('消费日期', '有效期', $template);
                        }

                        if (!empty($mobile)) {
                            $mobileNew = OrderNotify::getInternationalCall($mobileArea, $mobile);
                        } else {
                            $mobileNew = OrderNotify::getInternationalCall($order_info['mobile_area'], $order_info['ordertel']);
                        }
                        if (!ismobile($mobileNew)) {
                            @pft_log('order/ota_sms', json_encode(['外国人手机号不支持发送短信', $orderNum, $mobileNew, $template], JSON_UNESCAPED_UNICODE));
                            return false;
                        }

                        $weather = '';
                        $coordinateLink = '';
                        if (in_array($order_info['product_type'], ['A', 'F','H'])) {
                            $smsFormat = [];
                            if ($template) {
                                $smsFormatTemplate = strip_tags(trim(html_entity_decode($template)));
                                $pattern = '/\{([^\}]+)\}/';
                                preg_match_all($pattern, $smsFormatTemplate, $matches);
                                $smsFormat = $matches[0] ?? [];
                            }
                            $extContentArr = json_decode($order_info['ext_content'], true) ?? [];
                            $OrderNotify = new \Business\Order\OrderNotify();
                            list($weather,$coordinateLink) = $OrderNotify->getWeatherPositionData($smsFormat,$order_info['tid'],
                            $order_info['lid'],$extContentArr,$order_info['playtime']);

                        }
                        $templateAllArr = [
                            "{ordernum}"    => $orderNum,
                            "{code}"        => $apiOrderInfoArr['apiCode'],
                            "{playtime}"    => $timeNote ?? $order_info['playtime'],
                            "{expire}"      => "{$order_info['begintime']}至{$order_info['endtime']}",
                            "{usetime}"     => $timeShareInfo,
                            "{pname}"       => $pname,
                            "{tnum}"        => $apiOrderInfoArr['oStnum'],
                            "{expiry}"      => $order_info['endtime'],
                            "{link}"        => $link,
                            "{getaddr}"     => $getaddr,
                            "{buyname}"     => $order_info['ordername'],
                            "{buymobile}"   => $mobileNew,
                            "{performtime}" => $performTime,
                            "{showround}"   => $showRound,
                            "{showzone}"    => $showZone,
                            "{showzonenum}" => $showZone . '/' . $showSite,
                            "{thirdcode}"   => $value,
                            "{thirdnum}"    => $apiOrderInfoArr['apiOrder'],
                            "{remotenum}"   => !empty($order_info['remotenum'])? $order_info['remotenum'] : "",
                            "{weather}"     => $weather,//"{天气信息}",
                            "{coordinate}"  => $coordinateLink,//"{坐标位置}",
                            "{showbegin}"   => $showBegin,//"{场次开始时间}",
                            "{showend}"     => $showEnd,//"{场次结束时间}",

                        ];

                        $send_data   = array_values($templateAllArr);
                        $smsTmp      = str_replace(array_keys($templateAllArr), $send_data, $template);
                        $messageServiceApi = Container::pull(MessageService::class);
                        [$approval, $sms] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'OTADFDX', $mobileNew,
                            [$smsTmp], $applyDid, $orderNum, '代发短信', '票付通', '', 1, true);
                        if ($approval) {
                            if (!EnvUtil::isRelease()) {
                                pft_log('message_service', json_encode(['代发短信.OTADFDX.' . $appId, __METHOD__, [$mobileNew,
                                    [$smsTmp], $applyDid, $orderNum], $sms], JSON_UNESCAPED_UNICODE));
                            }
                        } else {
                            /** @deprecated 放量结束后删除 */
                            $fzZwxSmsLib = new \Library\MessageNotify\Platform\FzZwxSms($mobileNew);
                            $sms = $fzZwxSmsLib->customMsg($applyDid, '票付通', $smsTmp, $mobileNew, '',
                                $orderNum, true, '代发短信');
                            if (!EnvUtil::isRelease()) {
                                pft_log('message_service', json_encode(['代发短信.OTADFDX.' . $appId . '.old', __METHOD__, [$mobileNew,
                                    [$smsTmp], $applyDid, $orderNum], $sms], JSON_UNESCAPED_UNICODE));
                            }
                        }
                        if ($key > 15) {
                            break;
                        }
                    }
                    if (!empty($sms) && $sms['code'] == 200) {
                        $orderCommonModel = new \Model\Order\OrderCommon();
                        $orderCommonModel->updateSmsTimes($orderNum);
                    }
                } else {
                    $apiOrderModel   = new \Model\Ota\AllApiOrderModel();
                    $apiOrderInfoArr = $apiOrderModel->getInfoByPftOrder($orderNum, 'id, apiOrder, handleStatus, remoteorder, bCode, fid, oStnum, apiCode');

                    $orderToolMdl = new OrderTools('slave');
                    $order_info   = $orderToolMdl->getOrderInfo($orderNum,
                        'ss.ordernum,ss.member,ss.lid,ss.tid,ss.aid,ss.tnum,ss.ordername,ss.ordertel,ss.begintime,ss.endtime,ss.code,ss.remsg,ss.paymode,ss.ordermode,ss.ordertime,ss.playtime,ss.status,ss.visitors,ss.remotenum',
                        'de.concat_id,de.memo,de.product_ext,de.ext_content,de.series');
                    if (empty($order_info) || empty($order_info['ordertel'])) {
                        return false;
                    }

                    //增加一下是否期票的判断,是的话短信发有效期
                    $orderNotifyBiz = new \Business\Order\OrderNotify();
                    $checkIsCountdownRes = $orderNotifyBiz->checkOrderValidityIsCountdown($order_info['begintime'], $order_info['endtime']);
                    $beginTime           = $checkIsCountdownRes['begin_time'];
                    $endTime             = $checkIsCountdownRes['end_time'];
                    if ($beginTime == $endTime) {
                        $timeNote = $beginTime;
                    } else {
                        $timeNote = "{$beginTime}至{$endTime}";
                    }

                    $extContentArr = json_decode($order_info['ext_content'], true) ?? [];
                    $timeShareInfo = '';
                    if (isset($extContentArr['sectionTimeStr'])) {
                        $timeShareInfo = $extContentArr['sectionTimeStr'];
                    }

                    $javaApi    = new \Business\CommodityCenter\Ticket();
                    $tickets_list  =  [$order_info['tid'] => $order_info['tnum']];
                    $tid        = array_keys($tickets_list);
                    $tickets    = $javaApi->queryTicketInfoByIds($tid, 'id,pid,title,getaddr,pre_sale', '', '', '', true);

                    $this->unit = '张';
                    $pname      = '';
                    $getaddr    = '';
                    $pid_list   = [];
                    $preSaleArr = [];
                    $order_ticket = [];
                    foreach ($tickets_list as $tid => $tnum) {
                        foreach ($tickets as $ticket) {
                            if (!isset($ticket['ticket'][$tid])) {
                                continue;
                            }
                            if ($tid == $order_info['tid']) {
                                $master_pid = $ticket['ticket'][$tid]['pid'];
                            }
                            $order_ticket[] = ['tid' => $tid, 'title' => $ticket['ticket'][$tid]['title'], 'tnum' => $tnum . $this->unit];
                            $getaddr    = $ticket['ticket'][$tid]['getaddr'];
                            $pname      .= "\n{$ticket['land']['title']}{$ticket['ticket'][$tid]['title']}{$tnum}{$this->unit}";
                            $pid_list[] = $ticket['ticket'][$tid]['pid'];
                            $preSaleArr[$tid] = $ticket['ticket'][$tid]['pre_sale'];
                        }
                    }
                    $pname = str_replace(["\n", " ", "　",], '', $pname);
                    $pname = str_replace(['【', '】'], ['[', ']'], $pname);
                    //取短信链接
                    $link = " 12301.cc/" . OrderNotify::url_sms($orderNum) . ' ';
                    //获取演出信息
                    $series      = $order_info['series'] ?? '';
                    $seriesInfo  = (new  \Business\Order\OrderList())->getShowInfo($series);
                    $showZone    = $seriesInfo['partition'] ?? ""; //座位分区
                    $showSite    = $seriesInfo['seat'] ?? ""; //座位号
                    $showRound   = $seriesInfo['performance'] ?? "";//演出场次
                    $performTime = strstr($showRound, ' ') ? explode(' ', $showRound)[1] : "";//演出时间
                    $showBegin   = $seriesInfo['show_begin'] ?? '';//演出开始时间
                    $showEnd     = $seriesInfo['show_end'] ?? '';//演出结束时间
                    if (empty($template) && empty($getaddr)) {
                        $template = "点击查看订单详情： {link}，凭证号：{code}，消费日期：{playtime}。您已成功购买了{pname}，此为凭证，请妥善保管。";
                    } else if (empty($template) && !empty($getaddr)) {
                        $getaddr  = str_replace("【", "[", $getaddr);
                        $getaddr  = str_replace("】", "]", $getaddr);
                        $template = "点击查看订单详情： {link}，凭证号：{code}，消费日期：{playtime}。您已成功购买了{pname}，{getaddr}，此为凭证，请妥善保管。";
                    } else {
                        $getaddr  = str_replace("【", "[", $getaddr);
                        $getaddr  = str_replace("】", "]", $getaddr);
                    }

                    // 没查询到模板无链接的短信
                    if ($isLink == false) {
                        $template = "凭证号：{code}。您已成功购买了{pname}，此为凭证,请妥善保管。";
                    }

                    if (strpos($timeNote, '至') != false) {
                        $template = str_replace('消费日期', '有效期', $template);
                    }

                    if (!empty($mobile)) {
                        $mobileNew = OrderNotify::getInternationalCall($mobileArea, $mobile);
                    } else {
                        $mobileNew = OrderNotify::getInternationalCall($order_info['mobile_area'], $order_info['ordertel']);
                    }
                    if (!ismobile($mobileNew)) {
                        @pft_log('order/ota_sms', json_encode(['外国人手机号不支持发送短信', $orderNum, $mobileNew, $template], JSON_UNESCAPED_UNICODE));
                        return false;
                    }

                    $weather = '';
                    $coordinateLink = '';


                    if (in_array($order_info['product_type'], ['A', 'F','H'])) {
                        $smsFormat = [];
                        if ($template) {
                            $smsFormatTemplate = strip_tags(trim(html_entity_decode($template)));
                            $pattern = '/\{([^\}]+)\}/';
                            preg_match_all($pattern, $smsFormatTemplate, $matches);
                            $smsFormat = $matches[0] ?? [];
                        }
                        $extContentArr = json_decode($order_info['ext_content'], true) ?? [];
                        $OrderNotify = new \Business\Order\OrderNotify();
                        list($weather,$coordinateLink) = $OrderNotify->getWeatherPositionData($smsFormat,$order_info['tid'],
                            $order_info['lid'],$extContentArr,$order_info['playtime']);
                    }

                    $templateAllArr = [
                        "{ordernum}"    => $orderNum,
                        "{code}"        => $apiOrderInfoArr['apiCode'],
                        "{playtime}"    => $timeNote ?? $order_info['playtime'],
                        "{expire}"      => "{$order_info['begintime']}至{$order_info['endtime']}",
                        "{usetime}"     => $timeShareInfo,
                        "{pname}"       => $pname,
                        "{tnum}"        => $apiOrderInfoArr['oStnum'],
                        "{expiry}"      => $order_info['endtime'],
                        "{link}"        => $link,
                        "{getaddr}"     => $getaddr,
                        "{buyname}"     => $order_info['ordername'],
                        "{buymobile}"   => $mobileNew,
                        "{performtime}" => $performTime,
                        "{showround}"   => $showRound,
                        "{showzone}"    => $showZone,
                        "{showzonenum}" => $showZone . '/' . $showSite,
                        "{thirdcode}"   => $apiOrderInfoArr['apiCode'],
                        "{thirdnum}"    => $apiOrderInfoArr['apiOrder'],
                        "{remotenum}"   => !empty($order_info['remotenum'])? $order_info['remotenum'] : "",
                        "{weather}"     => $weather,//"{天气信息}",
                        "{coordinate}"  => $coordinateLink,//"{坐标位置}",
                        "{showbegin}"   => $showBegin,//"{场次开始时间}",
                        "{showend}"     => $showEnd,//"{场次结束时间}",
                    ];

                    $send_data   = array_values($templateAllArr);
                    $smsTmp      = str_replace(array_keys($templateAllArr), $send_data, $template);
                    $messageServiceApi = Container::pull(MessageService::class);
                    [$approval, $sms] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'OTADFDX', $mobileNew,
                        [$smsTmp], $applyDid, $orderNum, '代发短信', '票付通', '', 1, true, true);
                    if ($approval) {
                        if (!EnvUtil::isRelease()) {
                            pft_log('message_service', json_encode(['代发短信.OTADFDX', __METHOD__, [$mobileNew,
                                [$smsTmp], $applyDid, $orderNum], $sms], JSON_UNESCAPED_UNICODE));
                        }
                    } else {
                        /** @deprecated 放量结束后删除 */
                        $fzZwxSmsLib = new \Library\MessageNotify\Platform\FzZwxSms($mobileNew);
                        $sms = $fzZwxSmsLib->customMsg($applyDid, '票付通', $smsTmp, $mobileNew, '',
                            $orderNum, true, '代发短信');
                        if (!EnvUtil::isRelease()) {
                            pft_log('message_service', json_encode(['代发短信.OTADFDX.old', __METHOD__, [$mobileNew,
                                [$smsTmp], $applyDid, $orderNum], $sms], JSON_UNESCAPED_UNICODE));
                        }
                        if ($sms['code'] == 200){
                            $orderCommonModel = new \Model\Order\OrderCommon();
                            $orderCommonModel->updateSmsTimes($orderNum);
                        }
                    }
                    @pft_log('order/ota_sms',
                        json_encode(['applyDid' => $applyDid, 'smsTmp' => $smsTmp, 'mobileNew' => $mobileNew, 'orderNum' => $orderNum, 'sms' => $sms],
                            JSON_UNESCAPED_UNICODE));
                }
            }

            return $sms;
        }
        return false;

    }

}
