<?php


namespace Business\CommodityCenter;


use Business\Base;
use Business\JavaApi\Product\ProductDistributionService;

class TicketAttribute extends Base
{
    /**
     * 获取分销链
     *
     * <AUTHOR>
     * @date   2020/2/19
     *
     * @param int       $fid                供应商id
     * @param int       $superiorId         上级供应商id
     * @param int       $status             分销状态 0有效 1无效 -1全部 默认获取有效的分销链
     * @param int       $active             当前使用供应商转分销打开状态 1表示打开状态
     * @param string    $shop               渠道id，多渠道则字符串传('1,2,3')
     * @param int       $supplyType         取得是自供应还是转分小 1：转分销 2：自供应  -1：自供应+转分销 默认值 -1
     * @param array     $pTypes             景区类型A=景点，B=线路，C=酒店，F=套票，G=餐饮，H=演出，I=年卡套餐'
     * @param int       $channel            销售渠道
     * @param int       $signPType          产品类型判断 1:in, 2:not in
     * @param string    $landTitle          景区名称模糊匹配搜索关键字
     * @param array     $landIds            景区id数组
     * @param int       $aid                多级供应商id
     * @param string    $pName              票类名称
     * @param array     $productIds         产品id数组
     * @param int       $signProductId      产品id判断 1: in, 2: not in
     * @param int       $maxExpirationDate  门票过期最大日期 1: >=当前时间
     * @param int       $pageNum            分页参数第几页
     * @param int       $pageSize           分页参数每页几条
     *
     * @return array
     */
    public function getTicketAttribute($fid, $superiorId = 0, $active = -1, $shop = '', $supplyType = -1, $pTypes = [], $signPType = 0,
        $status = 0, $landTitle = '', $landIds = [], $channel = 0, $aid = 0, $productIds = [], $signProductId = 0, $pName = '',
        $maxExpirationDate = 1, $pageNum = 0, $pageSize = 0)
    {
        if (empty($fid)) {
            return $this->returnData(203, "参数错误", []);
        }
        $productDistributionApi = new ProductDistributionService();
        //获取对应的分销链
        $result = $productDistributionApi->getUsuallyDistribution($fid, $superiorId, $active, $shop, $supplyType, $pTypes,
            $signPType, $status, $landTitle, $landIds, $channel, $aid, $productIds, $signProductId, $pName, $maxExpirationDate,
            $pageNum, $pageSize);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], "分销链获取错误", []);
        }
        $evoluteLit = $result['data']['list'];

        $return     = [];
        if (!empty($evoluteLit)) {
            foreach ($evoluteLit as $value) {
                $tmp = [
                    /**
                     * pft_p_apply_evolute表数据
                     */
                    'eid'        => $value['id'],          //数据id
                    'epx'        => $value['px'],          //推荐值
                    'tx'         => $value['tx'],          //排序
                    'level'      => $value['lvl'],         //分销级别
                    'channel'    => $value['channel'],     //销售渠道
                    'apply_sid'  => $value['sid'],         //上级供应ID
                    'sapply_sid' => $value['sourceid'],    //最初供应方ID
                    'aids'       => $value['aids'],        //各级供应商id字符串
                    'evo_active' => $value['active'],      //当前使用供应商1表示活动状态
                    'lid'        => $value['lid'],         //景区id
                    'pid'        => $value['pid'],         //票id
                    'apply_did'  => $value['sourceid'],    //景区供应商
                    'salerid'    => $value['sourceid'],    //景区供应商
                    'tid'        => $value['ticketId'],    //票类id
                ];
                if ($tmp['apply_sid'] == $tmp['sapply_sid']) {
                    $return[$tmp['pid']] = $tmp;
                } else {
                    $return[$tmp['pid'] . '_' . $tmp['aids']] = $tmp;
                }
            }
        }
        $returnData = [
            'list'  => $return,
            'total' => $result['data']['total'],
        ];

        return $this->returnData($result['code'], "数据获取成功", $returnData);
    }
}