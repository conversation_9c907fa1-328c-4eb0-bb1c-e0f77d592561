<?php

namespace Business\CommodityCenter;

use Business\Authority\DataAuthLimit;
use Business\Base;
use Business\JavaApi\CommodityCenter\Land as landBiz;
use Controller\Tpl\trademan;

class Land extends Base
{
    private $_javaApi  = null;
    private $tableName = [
        'uuLandDTO',
        'uuProductDTO',  //合并后最后一个数组单做主表字段
        'uuJqTicketDTO',
    ];

    public function __construct()
    {
        $this->_javaApi = new landBiz();
    }

    /**
     * 根据供应商id获取出自供应产品数量
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $status  门票状态
     *
     * @return int
     */
    public function countLandByApplyDidAndStatus($applyDid, $status = 1)
    {
        if (empty($applyDid)) {
            return 0;
        }

        $result = $this->_javaApi->countLandByApplyDidAndStatus($applyDid, $status);

        if ($result['code'] != 200 || empty($result['data'])) {
            return 0;
        } else {
            return $result['data'];
        }
    }

    /**
     * 根据供应商id获取出自供应产品数量
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $applyDid  供应商id
     * @param  array  $uuidArr  uuid模糊搜索
     *
     * @return int
     */
    public function queryLandIdListByUuid($applyDid, $uuidArr)
    {
        if (empty($applyDid) || empty($uuidArr)) {
            return [];
        }

        $uuidsArr = [];
        foreach ($uuidArr as $uuid) {
            $uuidsArr[] = (string)$uuid;
        }

        $result = $this->_javaApi->queryLandIdListByUuid($applyDid, $uuidsArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        } else {
            $lidArrMap = [];
            foreach ($result['data'] as $landMap) {
                foreach ($landMap as $uuid => $landid) {
                    $lidArrMap[$uuid] = $landid;
                }
            }

            return $lidArrMap;
        }
    }

    /**
     * 获取land表（单表）详情根据票id
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  int  $tid  票id
     *
     * @return array
     */
    public function getLandInfoByTidToJava($tid)
    {
        if (!$tid) {
            return [];
        }
        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $ticketArr     = [$tid];
        $condition     = [
            'landFlag' => 1,
        ];
        $javaData      = $ticketJavaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($ticketArr, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'][0]['uuLandDTO'] ? $javaData['data'][0]['uuLandDTO'] : [];
    }

    /**
     * 获取land表和ticket详情根据票id数组
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrTid  票id
     * @param  string  $landField  land表字段
     * @param  string  $ticketField  ticket表字段
     * @param  array  $asField  别名字段
     *
     * @return array
     */
    public function getLandInfoByArrTidToJava($arrTid, $landField = '', $ticketField = '', $asField = [], $isGetOne = false)
    {
        if (empty($arrTid)) {
            return [];
        }
        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $condition     = [
            'landFlag' => 1,
        ];
        $javaData      = $ticketJavaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($arrTid, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $landList = $this->_handleField($javaData['data'], $ticketField, '', $landField, $asField);
        if ($isGetOne) {
            return $landList[0] ? $landList[0] : [];
        } else {
            return $landList ? $landList : [];
        }
    }

    /**
     * 获取land表是否通知和传真字段
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrTid  票id
     *
     * @return array
     */
    public function getSellerNotifyInfoByArrTid($arrTid)
    {
        if (empty($arrTid)) {
            return [];
        }
        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $condition     = [
            'landFlag'             => 1,
            'cancelNotifySupplier' => 1,
            'faxNotNull'           => 1,
        ];
        $javaData      = $ticketJavaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($arrTid, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $landList = $this->_handleField($javaData['data'], '', '', 'fax,apply_did');

        return $landList ? $landList : [];
    }

    public function getLandInfoListBySid($sid, $title = '', $page = 1, $size = 10, $pType = '', $landStatus = 1, $subSid = 0)
    {
        $res = ['list' => [], 'total' => 0];
        if (!$sid) {
            return $res;
        }
        $landApi  = new \Business\JavaApi\CommodityCenter\Land();
        $javaData = $landApi->getLandInfoList($sid, [], $landStatus, $pType, $title, $page, $size, null, null, null, 0, [], $subSid);

        if ($javaData['code'] != 200 || !$javaData['data']) {
            return $javaData;
        }
        $res['list']  = $javaData['data']['list'] ? $javaData['data']['list'] : [];
        $res['total'] = $javaData['data']['total'] ? $javaData['data']['total'] : 0;

        return $res;
    }

    private function _handleField($list, $ticketField = '', $productField = '', $landField = '', $asField = [])
    {

        $data = [];
        if (!empty($list)) {
            if ($productField) {
                $productFieldArr = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($productField)));
            }
            if ($landField) {
                $landFieldArr = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($landField, [' ' => ''])));
            }
            if ($ticketField) {
                $ticketFieldArr = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($ticketField, [' ' => ''])));
            }

            if (empty($productFieldArr) && empty($landFieldArr) && empty($asField) && empty($ticketFieldArr)) {
                foreach ($list as $key => $value) {
                    $mergerData = [];
                    foreach ($this->tableName as $k => $v) {
                        if (isset($value[$v])) {
                            $mergerData = array_merge($mergerData, $value[$v]);
                        }
                    }
                    $data[$key] = $mergerData;
                }
            } else {
                foreach ($list as $key => $value) {
                    if ($productFieldArr) {
                        foreach ($value['uuProductDTO'] as $k1 => $v1) {
                            if (in_array($k1, $productFieldArr)) {
                                $data[$key][$k1] = $v1;
                            }
                            if (isset($asField['uuProductDTO'])) {
                                if (isset($asField['uuProductDTO'][$k1])) {
                                    $data[$key][$asField['uuProductDTO'][$k1]] = $v1;
                                }
                            }
                        }
                    }
                    if ($landFieldArr) {
                        foreach ($value['uuLandDTO'] as $k2 => $v2) {
                            if (in_array($k2, $landFieldArr)) {
                                $data[$key][$k2] = $v2;
                            }
                            if (isset($asField['uuLandDTO'])) {
                                if (isset($asField['uuLandDTO'][$k2])) {
                                    $data[$key][$asField['uuLandDTO'][$k2]] = $v2;
                                }
                            }
                        }
                    }
                    if ($ticketFieldArr) {
                        foreach ($value['uuJqTicketDTO'] as $k3 => $v3) {
                            if (in_array($k3, $ticketFieldArr)) {
                                $data[$key][$k3] = $v3;
                            }
                            if (isset($asField['uuJqTicketDTO'])) {
                                if (isset($asField['uuJqTicketDTO'][$k3])) {
                                    $data[$key][$asField['uuJqTicketDTO'][$k3]] = $v3;
                                }
                            }
                        }
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 获取终端销售景区产品
     * <AUTHOR> Li
     * @date 2020-05-11
     *
     * @param  int  $applyDid  供应商id
     * @param  array  $salerIdArr  商家id
     * @param  array  $pStatusArr  p_status
     * @param  int  $landStatus  景区状态
     * @param  int  $orderFlag  产品订单类型标识:0=通用,1=团队订单
     *
     * @return array
     */
    public function queryTerminalSaleLand($applyDid, $salerIdArr = null, $pStatusArr = null, $landStatus = null, $orderFlag = null)
    {
        if (empty($applyDid)) {
            return [];
        }

        $fieldArr = ['id', 'title', 'salerid', 'terminal', 'pType', 'imgpath', 'applyDid'];

        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Land();
        $javaData      = $ticketJavaApi->queryTerminalSaleLand($applyDid, $salerIdArr, $pStatusArr, $landStatus,
            $orderFlag);

        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        $landList = [];
        foreach ($javaData['data'] as $key => $value) {

            foreach ($fieldArr as $field => $item) {
                if (!isset($value[$item])) {
                    continue;
                }

                $tField = $item;
                if ($item == 'pType') {
                    $tField = 'ptype';
                } else if ($item == 'applyDid') {
                    $tField = 'sapply_did';
                }

                $landList[$key][$tField] = $value[$item];
            }
        }

        return $landList ? $landList : [];
    }

    /**
     * 获取终端销售景区产品
     * <AUTHOR> Li
     * @date 2020-05-11
     *
     * @param  int  $applyDid  供应商id
     * @param  string  $landTitle  商家id
     * @param  string  $pType  p_status
     * @param  array  $landStatusArr  景区状态
     *
     * @return array
     */
    public function queryApplyAllLand($applyDid, $landTitle = null, $pType = null, $landStatusArr = null)
    {
        if (empty($applyDid)) {
            return [];
        }

        $fieldArr = ['id', 'title', 'salerid', 'terminal', 'pType', 'imgpath'];

        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Land();
        $javaData      = $ticketJavaApi->queryApplyAllLand($applyDid, $landTitle, $pType, $landStatusArr);

        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        $landList = [];
        foreach ($javaData['data'] as $key => $value) {

            foreach ($fieldArr as $field => $item) {
                if (!isset($value[$item])) {
                    continue;
                }

                $tField = $item;
                if ($item == 'pType') {
                    $tField = 'p_type';
                }

                $landList[$key][$tField] = $value[$item];
            }
        }

        return $landList ? $landList : [];
    }

    /**
     * 下架会员下所有景区和景区下门票
     * <AUTHOR>
     * @date 2020/10/14
     *
     * @param  int  $memberId  会员ID
     *
     * @return array
     */
    public function deListingAllLand(int $memberId)
    {
        if (!$memberId) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数缺失', []);
        }

        $landService = new landBiz();
        $result      = $landService->deListingAllLand($memberId);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 根据会员Id分页获取景区信息
     * <AUTHOR>
     * @date 2020/10/14
     *
     * @param  int  $accountId  会员Id
     * @param  int  $startPage  开始页
     * @param  int  $pageSize  每页显示大小
     *
     * @return array
     */
    public function otaList(int $accountId, int $startPage, int $pageSize)
    {
        if (empty($accountId) || empty($startPage) || empty($pageSize)) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数缺失', []);
        }

        $landService = new landBiz();
        $result      = $landService->otaList($accountId, $startPage, $pageSize);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 绑定景区子景点信息
     * <AUTHOR>
     * @date 2020/10/14
     *
     * @param  int  $itemId  商品id
     * @param  int  $accountId  账号id
     * @param  int  $operatorId  操作员id
     * @param  string  $scenicidList  子景点列表信息
     *
     * @return array
     */
    public function bindSecnicList($itemId, $accountId, $operatorId, $scenicidList)
    {
        if (empty($itemId) || empty($accountId) || empty($operatorId) || empty($scenicidList)) {
            return $this->returnData(self::CODE_NO_CONTENT, '参数错误');
        }

        $landService = new landBiz();
        $result      = $landService->bindSecnicList(intval($itemId), intval($accountId), intval($operatorId), $scenicidList);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 景区资源绑定
     * <AUTHOR>
     * @date 2020/10/14
     *
     * @param  int  $lid  景区Id
     * @param  int  $resourceId  资源库Id
     *
     * @return array
     */
    public function resourceBind(int $lid, int $resourceId)
    {
        if (empty($lid) || empty($resourceId)) {
            return $this->returnData(self::CODE_NO_CONTENT, '参数错误');
        }

        $landService = new landBiz();
        $result      = $landService->resourceBind($lid, $resourceId);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取景区数据
     * <AUTHOR>
     * @date 2020/10/15
     *
     * @param  int  $landId  景区ID
     *
     * @return array
     */
    public function queryLandById(int $landId)
    {
        if (empty($landId)) {
            return $this->returnData(self::CODE_NO_CONTENT, '参数错误');
        }
        $landApi  = new \Business\JavaApi\Product\Land();
        $result = $landApi->queryLandById($landId);

        if ($result['code'] != 200 || empty($result['data'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        $data = $this->_changeLandInfoMap($result['data']);

        return $this->returnData($result['code'], $result['msg'], $data);
    }

    /**
     * 获取景区数据新旧工程参数转换
     * <AUTHOR>
     * @date 2020/10/15
     *
     * @param  array  $data 要转换的参数
     *
     * @return array
     */
    private function _changeLandInfoMap(array $data)
    {
        $notLike = [
            'applyDid'         => 'account_id',
            'addtime'          => 'add_time',//要转换成时间戳
            //'address'          => 'address',
            //'area'             => 'area',
            //'areacode'         => 'areacode',
            //'attribute'        => 'attribute',
            'cityName'         => 'city_name',
            'customMade'       => 'custom_made',
            'bhjq'             => 'details',
            'groupLimit'       => 'group_limit',
            'groupNumberLimit' => 'group_number_limit',
            'groupTicketLimit' => 'group_ticket_limit',
            //'id'               => 'id',
            'imgpath'          => 'img_path',
            'imgpathgrp'       => 'img_path_group',
            'jdjj'             => 'introduce',
            //'letters'          => 'letters',
            'jtype'            => 'level',
            'lngLatPos'        => 'lng_lat_pos',
            'title'            => 'name',
            'jqts'             => 'notice',
            'orderFlag'        => 'order_flag',
            //'oversea'          => 'oversea',
            'provinceName'     => 'province_name',
            'resourceid'       => 'resource_id',
            //'runtime'          => 'runtime',
            //'salerid'          => 'salerid',
            'qt'               => 'ship_route_code',
            'fax'              => 'sms_phone',
            //'status'           => 'status',
            //'tel'              => 'tel',
            //'terminal'         => 'terminal',
            'topic'            => 'topics',
            'jtzn'             => 'traffic',
            'pType'            => 'type',
            'uptime'           => 'update_time',
            'venusId'          => 'venus_id',
            'verifyStatus'     => 'verify_status',
            'operaterId'       => 'operater_id',
        ];

        foreach ($data as $key => $value) {
            if (isset($notLike[$key])) {
                if ($notLike[$key] == 'add_time' || $notLike[$key] == 'update_time') {
                    $data[$notLike[$key]] = strtotime($value);
                } else {
                    $data[$notLike[$key]] = $value;
                }

                unset($data[$key]);
            }
        }

        return $data;
    }

    /**
     * 通过景区id获取资源id
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $lid  景区id
     *
     * @return array
     */
    public function queryLandResourceId($lid)
    {
        if (!$lid) {
            return [];
        }

        $javaData = $this->_javaApi->queryLandResourceId($lid);

        if ($javaData['code'] != 200) {
            return [];
        }

        return $javaData['data'];
    }

    /**
     * 通过景区id获取景区的信息
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  array  $landIdArr  景区id数组
     *
     * @return array
     */
    public function queryLandInfoByIds($landIdArr)
    {
        if (!$landIdArr) {
            return [];
        }

        $javaData = $this->_javaApi->queryLandInfoByIds($landIdArr);
        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        return $javaData['data'];
    }

    /**
     * 查询最新的landId
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $sid  供应商id
     * @param  int  $startPage  开始页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryLastLandId()
    {
        $javaData = $this->_javaApi->queryLastLandId();

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return 0;
        }

        return $javaData['data'];
    }

    /**
     * 查询富文本字段
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $lid  景区id
     *
     * @return array
     */
    public function queryLandBLOBsByLandId($lid)
    {
        if (!$lid) {
            return [];
        }
        $javaData = $this->_javaApi->queryLandBLOBsByLandId($lid);

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        return $javaData['data'];
    }

    /**
     * 供应商查询
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  array  $sidArr  供应商id数组
     * @param  string  $title  景区名称(like)
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  bool  $ifText  是否查询富文本 默认false
     * @param  array  $statusArr  景区状态
     * @param  array  $verifyStatusArr  审核状态 0未审核1审核通过2关闭3删除
     * @param  array  $pTypeArr  产品类型(=)
     *
     * @return array
     */
    public function queryLandMultiQueryByApplyDid($sidArr, $title = '', $unEqualPType = '', $ifText = false, $statusArr = [], $verifyStatusArr = [], $pTypeArr = [])
    {
        if (!$sidArr) {
            return [];
        }
        $javaData = $this->_javaApi->queryLandMultiQueryByApplyDid($sidArr, $title, $unEqualPType, $ifText, $statusArr,
            $verifyStatusArr, $pTypeArr);
        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        return $javaData['data'];
    }

    /**
     * 精确查询
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  array  $lidArr  景区id数组
     * @param  string  $title  景区名称(like)
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  bool  $ifText  是否查询富文本 默认false
     * @param  string  $areaCode  地区编码(4位数)
     * @param  array  $terminalArr  终端号
     * @param  array  $statusArr  景区状态
     * @param  array  $verifyStatusArr  审核状态 0未审核1审核通过2关闭3删除
     * @param  array  $pTypeArr  产品类型(=)
     *
     * @return array
     */
    public function queryLandMultiQueryById($lidArr, $title = '', $unEqualPType = '', $ifText = false, $areaCode = '', $terminalArr = [], $statusArr = [], $verifyStatusArr = [], $pTypeArr = [])
    {
        if (!$lidArr) {
            return [];
        }

        //格式化下
        $lidArr   = array_values($lidArr);
        $javaData = $this->_javaApi->queryLandMultiQueryById($lidArr, $title, $unEqualPType, $ifText, $areaCode,
            $terminalArr, $statusArr, $verifyStatusArr, $pTypeArr);

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        return $this->_uncamelize($javaData)['data'];
    }

    /**
     * 精确查询
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  array  $lidArr  景区id数组
     * @param  string  $title  景区名称(like)
     * @param  string  $orderByClause  排序规则 例：'title,id desc'
     * @param  bool  $ifText  是否查询富文本 默认false
     * @param  array  $applyDidArr  供应商id
     * @param  array  $statusArr  景区状态
     * @param  array  $lettersArr  景区名称拼音首字母
     * @param  array  $pTypeArr  产品类型(=)
     * @param  array  $saleridArr  商家id数组
     * @param  string  $titleLetters  景区名称拼音首字母 (like)
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  string  $unEqualPTypeList  产品类型(!=)支持传多个
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     *
     * @return array
     */
    public function queryLandMultiQueryByAdminAndPaging($lidArr, $page = 1, $size = 10, $title = '', $orderByClause = '',
        $ifText = false, $applyDidArr = [], $statusArr = [], $lettersArr = [], $pTypeArr = [], $saleridArr = [],
        $titleLetters = null, $unEqualPType = null, $isCount = false, $unEqualPTypeList = [], $subMerchantId=0,
        array $extCondition = [])
    {
        if (!$lidArr && !$applyDidArr && !$saleridArr && !$title && !$pTypeArr && !$unEqualPType) {
            return [];
        }

        $javaData = $this->_javaApi->queryLandMultiQueryByAdminAndPaging($title, $titleLetters, $unEqualPType, $ifText,
            $orderByClause, $page, $size, $lidArr, $saleridArr, $applyDidArr, $statusArr, $lettersArr, $pTypeArr, $unEqualPTypeList,
            $subMerchantId, $extCondition);

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        if ($isCount) {
            return $javaData['data']['total'];
        }

        $result = $this->_uncamelize($javaData, true);
        return $result['data'];
    }

    /**
     * 资源查询
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  array  $applyDidArr  供应商id
     * @param  array  $resourceIdArr  供应商id
     * @param  string  $title  景区名称(like)
     * @param  array  $statusArr  景区状态
     * @param  array  $lettersArr  景区名称拼音首字母
     * @param  array  $pTypeArr  产品类型(=)
     *
     * @return array
     */
    public function queryLandMultiQueryByResourceId($resourceIdArr = [], $applyDidArr = [], $title = '', $statusArr = [], $lettersArr = [], $pTypeArr = [])
    {
        if (empty($resourceIdArr)) {
            return [];
        }

        $javaData = $this->_javaApi->queryLandMultiQueryByResourceId($resourceIdArr, $applyDidArr, $title, $statusArr,
            $lettersArr, $pTypeArr);

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        return $javaData['data'];
    }

    /**
     * 刷新景区的updateTime
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $lid  景区id
     *
     * @return array
     */
    public function refreshLandUpdateTime($lid)
    {
        if (!$lid) {
            return false;
        }
        $javaData = $this->_javaApi->refreshLandUpdateTime($lid);

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return false;
        }

        return true;
    }

    /**
     * 景区商家查询
     * <AUTHOR> Li
     * @date  2020-09-22
     *
     * @param  array  $saleridArr  商家id数组
     * @param  string  $title  景区名称(like)
     * @param  array  $statusArr  景区状态
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  array  $verifyStatusArr  审核状态 0未审核1审核通过2关闭3删除
     * @param  array  $pTypeArr  产品类型(=)
     *
     * @return array
     */
    public function queryLandMultiQueryBySalerid($saleridArr, $title = '', $statusArr = [], $unEqualPType = '', $verifyStatusArr = [], $pTypeArr = [])
    {
        if (!$saleridArr) {
            return [];
        }
        $javaData = $this->_javaApi->queryLandMultiQueryBySalerid($saleridArr, $title, $statusArr, $unEqualPType,
            $verifyStatusArr, $pTypeArr);

        if ($javaData['code'] != 200 || empty($javaData['data'])) {
            return [];
        }

        return $this->_uncamelize($javaData)['data'];
    }

    /**
     * 根据条件查询景区id
     * <AUTHOR>  Li
     * @date  2020-10-30
     *
     * @param  array  $landIdArr
     * @param  array  $applyDidArr
     * @param  string  $pType
     * @param  string  $areaCode
     * @param  int  $unEqualApplyDid
     *
     * @return array
     */
    public function queryLandIdList($landIdArr = [], $applyDidArr = [], $pType = null, $areaCode = null, $unEqualApplyDid = null)
    {
        if (!$landIdArr && !$applyDidArr) {
            return [];
        }

        $javaData = $this->_javaApi->queryLandIdList($landIdArr, $applyDidArr, $pType, $areaCode, $unEqualApplyDid);

        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'];
    }

    /**
     * 根据条件获取下拉产品列表
     * <AUTHOR>  Li
     * @date  2022-04-07
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     * @param  string  $pType  产品类型
     * @param  int  $status  产品状态 1上架 2下架
     * @param  string  $title  产品名称
     * @param  int|false  $subType  子产品类型
     *
     * @return array
     */
    public function selectLandByCond(int $applyDid, int $page = 1, int $size = 10, string $pType = null, int $status = 1, string $title = '', $subMerchantId=0, $subType = 0)
    {
        $return = [
            'list'  => [],
            'total' => 0,
        ];
        if (!$applyDid || !$page || !$size) {
            return $return;
        }

        $javaData = $this->_javaApi->selectLandByCond($applyDid, $page, $size, $pType, $status, $title, $subMerchantId, $subType);
        if ($javaData['code'] != 200 || !$javaData['data']['list']) {
            return $return;
        }

        $return = [
            'list' => $javaData['data']['list'],
            'total' => $javaData['data']['total'],
        ];

        return $return;
    }

    /**
     * 单表数组处理
     * <AUTHOR>  Li
     * @date 2020-05-19
     *
     * @param  array  $res  列表
     * @param  bool  $isList  是否是有list的分页处理
     *
     * @return array
     */
    public function _uncamelize($res, $isList = false)
    {
        if ($res['code'] == 200) {
            $speKey = [];
            $arrSpe = array_keys($speKey);

            $unCamelizeData = [];
            if ($isList) {
                $data = $res['data']['list'] ? $res['data']['list'] : [];
            } else {
                $data = $res['data'];
            }

            foreach ($data as $key => &$val) {
                if (is_int($key)) {
                    foreach ($val as $key2 => &$val2) {
                        if (is_array($val2) && !empty($val2)) {
                            $flag = [];
                            foreach ($val2 as $key3 => $val3) {
                                if (is_array($val3)) {
                                    foreach ($val3 as $key4 => $val4) {
                                        if (in_array($key4, $arrSpe)) {
                                            $flag[$key3][$speKey[$key4]] = $val4;
                                        } else {
                                            $flag[$key3][uncamelize($key4)] = $val4;
                                        }

                                    }
                                } else {
                                    $flag[uncamelize($key3)] = $val3;
                                }
                            }
                            $val2 = $flag;
                        }
                        if (in_array($key2, $arrSpe)) {
                            $unCamelizeData[$key][$speKey[$key2]] = $val2;
                        } else {
                            $unCamelizeData[$key][uncamelize($key2)] = $val2;
                        }
                    }
                } else {
                    if (is_array($val) && !empty($val)) {
                        $flag = [];
                        foreach ($val as $key3 => $val3) {
                            foreach ($val3 as $key4 => $val4) {
                                if (in_array($key4, $arrSpe)) {
                                    $flag[$key3][$speKey[$key4]] = $val4;
                                } else {
                                    $flag[$key3][uncamelize($key4)] = $val4;
                                }
                            }
                        }
                        $val = $flag;
                    }
                    if (in_array($key, $arrSpe)) {
                        $unCamelizeData[$speKey[$key]] = $val;
                    } else {
                        $unCamelizeData[uncamelize($key)] = $val;
                    }
                }
            }
            if ($isList) {
                $res['data']['list'] = $unCamelizeData;
            } else {
                $res['data'] = $unCamelizeData;
            }
        }

        return $res;
    }

    /**
     * 将传入查询字符串去空格去换行
     * <AUTHOR> Li
     * @date 2022-03-30
     *
     * @param  string  $replaceStr  需要替换的字符串
     *
     * @return array
     */
    public function replaceStr($replaceStr = '')
    {
        if (!$replaceStr || !is_string($replaceStr)) {
            return [];
        }

        return explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($replaceStr, [' ' => ''])));
    }
}