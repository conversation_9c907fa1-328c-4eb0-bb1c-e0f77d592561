<?php

namespace Business\CommodityCenter;

use Business\Base;
use Business\JavaApi\TicketApi;

class ProductBusiness extends Base
{
    /**
     * 批量保存/编辑快捷发布门票
     * <AUTHOR> Li
     * @date 2022-04-07
     *
     * @param  array  $data  门票属性详情  ticketItems: ['uuJqTicketDTO'=>[],'timePrice'=>[], 'confs'=>, 'uuLandFDTO'=>[]]
     * @param  int  $sid  供应商id
     * @param  int  $operatorId  操作人id
     *
     * @return array
     */
    public function setQuickTicket(array $data, int $sid, int $operatorId, $subSid = 0, $forApproval = false)
    {
        if (!$data || !$sid || !$operatorId) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数缺失');
        }

        $productApi = new \Business\JavaApi\CommodityCenter\ProductBusiness();
        $javaData   = $productApi->setQuickTicket($data, $sid, $operatorId, $subSid, $forApproval);
        if ($javaData['code'] != 200) {
            return $this->returnData($javaData['code'], $javaData['msg']);
        }

        return $this->returnData(self::CODE_SUCCESS, '票属性设置成功', $javaData['data']);
    }

    /**
     * 获取景区门票属性
     * <AUTHOR> Li
     * @date 2022-04-07
     *
     * @param  int  $sid  供应商id
     * @param  int  $ticketId  门票id
     *
     * @return array
     */
    public function getQuickTicket(int $sid, int $ticketId)
    {
        if (!$sid || !$ticketId) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\ProductBusiness();
        $javaData   = $productApi->getQuickTicket($sid, $ticketId);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'] ? $javaData['data'] : [];
    }
}