<?php

namespace Business\CommodityCenter;

use Business\JavaApi\CommodityCenter\LandF as landFBiz;
use function GuzzleHttp\Psr7\str;

class LandF
{
    private $_javaApi = null;

    public function __construct()
    {
        $this->_javaApi = new landFBiz();
    }

    /**
     * 根据条件查询套票信息
     * <AUTHOR> Li
     * @date 2020-01-20
     *
     * @param  string  $field  查询门票字段
     * @param  bool  $isGetField  是否getField格式返回
     * @param  array  $ticketIdArr  门票id数组
     * @param  array  $landIdArr  景区id数组
     * @param  array  $productIdArr  产品id数组
     *
     * @return array
     */
    public function queryLandFByLandIdAndTicketIdAndProductId($ticketIdArr = null, $landIdArr = null, $productIdArr = null,$field = '*',$isGetField = false)
    {
        if ( empty($landIdArr) && empty($ticketIdArr) && empty($productIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryLandFByLandIdAndTicketIdAndProductId($landIdArr, $ticketIdArr, $productIdArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }
        $data = $this->_formatJavaFieldData($result['data'],$field);
        if (empty($result)) {
            return [];
        }
        if ($isGetField){
            $data = $this->getMemberExtGetField($data, $field,true);
        }
        return $data ? $data : [];
    }
    /**
     * 字段参数过滤
     * <AUTHOR>
     *
     * @param  $data array 数据库查询返回值
     * @param  $field string 需要的值
     *
     * @date   2019-11-11
     * @return array
     */
    private function _formatJavaFieldData($data, $field = '*')
    {
        $result = $data;
        if ($field !== '*' && !empty($data)) {
            $filterField = explode(',', $field);
            foreach ($filterField as $kk => $vv) {
                $filterField[$kk] = trim($vv);
            }
            $filterData = [];
            $list       = $data;
            foreach ($list as $key => $value) {
                if (is_int($key)) {
                    foreach ($value as $k => $v) {
                        if (in_array($k, $filterField)) {
                            $filterData[$key][$k] = $v;
                        }
                    }
                } else {
                    if (in_array($key, $filterField)) {
                        $filterData[$key] = $value;
                    }
                }
            }
            $result = $filterData;
        }

        return $result;
    }
    /**
     * 复制了数据库的getfield然后改了改
     * <AUTHOR>
     *
     * @param  $data array 数据库查询返回值
     * @param  $field string 需要的值
     * @param  $sepa bool 是否要返回数组
     *
     * @date   2019-11-11
     * @return array|string
     */
    private function getMemberExtGetField($data, $field, $sepa = null)
    {
        if (strpos($field, ',') && false !== $sepa) { // 多字段
            $resultSet = $data;
            if (!empty($resultSet)) {
                if (is_string($resultSet)) {
                    return $resultSet;
                }
                $_field = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($field, [' ' => ''])));
                $key1   = $_field[0];
                $key2   = $_field[1];
                $cols  = array();
                $count = count($_field);
                foreach ($resultSet as $result) {
                    $name = $result[$key1];
                    if (2 == $count) {
                        $cols[$name] = $result[$key2];
                    } else {
                        $cols[$name] = is_string($sepa) ? implode($sepa, array_slice($result, 1)) : $result;
                    }
                }

                return $cols;
            }
        } else {   // 查找一条记录
            // 返回数据个数
            $options = [];
            if (true !== $sepa) {// 当sepa指定为true的时候 返回所有数据
                $options['limit'] = is_numeric($sepa) ? $sepa : 1;
            }
            $result = $data;

            if (!empty($result)) {
                if (is_string($result)) {
                    return $result;
                }
                if (true !== $sepa && isset($options['limit']) && 1 == $options['limit']) {
                    $data = reset($result[0]);

                    return $data;
                }
                foreach ($result as $val) {
                    $array[] = $val[$field];
                }

                return $array;
            }
        }

        return null;
    }
}