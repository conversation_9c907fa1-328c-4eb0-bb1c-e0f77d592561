<?php

namespace Business\CommodityCenter;

use Business\JavaApi\CommodityCenter\Ticket as ticketBiz;

class Ticket
{
    private $_javaApi  = null;
    private $tableName = [
        'uuLandDTO',
        'uuProductDTO',  //合并后最后一个数组单做主表字段
        'uuJqTicketDTO',
        'uuLandFDTO',
    ];

    public function __construct()
    {
        $this->_javaApi = new ticketBiz();
    }

    /**
     * 根据条件获取门票属性
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $ticketId  票id
     * @param  int  $sid  供应商id
     * @param  int  $fid  分销商id
     * @param  int  $channel  渠道
     *
     * @return array
     */
    public function queryTicketInfoById($ticketId, $tField = '', $pField = '', $lField = '', $lFField = '', $isGetField = false, $sid = null, $fid = null, $channel = null)
    {
        if (empty($ticketId)) {
            return [];
        }
        $result = $this->_javaApi->queryTicketInfoById($ticketId);

        //如果是系统异常的话，直接抛出异常
        if ($result['code'] == 500) {
            throw new \Exception('系统异常，请重试', 500);
        }

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            if ($tFieldArr) {
                foreach ($result['data']['uuJqTicketDTO'] as $tKey => $tValue) {
                    if (!in_array($tKey, $tFieldArr)) {
                        continue;
                    }
                    if ($isGetField) {
                        $tmpData['ticket'][$result['data']['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                    } else {
                        $tmpData['ticket'][$tKey] = $tValue;
                    }
                }
                foreach ($result['data']['thridTicketAttributesDTO'] as $tKey => $tValue) {
                    if (!in_array($tKey, $tFieldArr)) {
                        continue;
                    }
                    if ($isGetField) {
                        $tmpData['ticket'][$result['data']['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                    } else {
                        $tmpData['ticket'][$tKey] = $tValue;
                    }
                }
            } else {
                $tmpData['ticket'] = array_merge($result['data']['uuJqTicketDTO'],
                    $result['data']['thridTicketAttributesDTO']);
            }

            if ($pFieldArr) {
                foreach ($result['data']['uuProductDTO'] as $tKey => $tValue) {
                    if (!in_array($tKey, $pFieldArr)) {
                        continue;
                    }
                    if ($isGetField) {
                        $tmpData['product'][$result['data']['uuProductDTO'][$pFieldArr[0]]][$tKey] = $tValue;
                    } else {
                        $tmpData['product'][$tKey] = $tValue;
                    }
                }
            } else {
                $tmpData['product'] = $result['data']['uuProductDTO'];
            }
            if ($lFieldArr) {
                foreach ($result['data']['uuLandDTO'] as $tKey => $tValue) {
                    if (!in_array($tKey, $lFieldArr)) {
                        continue;
                    }
                    if ($isGetField) {
                        $tmpData['land'][$result['data']['uuLandDTO'][$lFieldArr[0]]][$tKey] = $tValue;
                    } else {
                        $tmpData['land'][$tKey] = $tValue;
                    }
                }
            } else {
                $tmpData['land'] = $result['data']['uuLandDTO'];
            }
            if ($lFFieldArr) {
                foreach ($result['data']['uuLandFDTO'] as $tKey => $tValue) {
                    if (!in_array($tKey, $lFFieldArr)) {
                        continue;
                    }
                    if ($isGetField) {
                        $tmpData['land_f'][$result['data']['uuLandFDTO'][$lFFieldArr[0]]][$tKey] = $tValue;
                    } else {
                        $tmpData['land_f'][$tKey] = $tValue;
                    }
                }
            } else {
                $tmpData['land_f'] = $result['data']['uuLandFDTO'];
            }
            if (isset($result['data']['confs']) && $result['data']['confs']) {
                foreach ($result['data']['confs'] as $tKey => $tValue) {
                    $tmpData['ext'][$tKey] = $tValue;
                }
            }
            if (isset($result['data']['specialTicketDTO']) && $result['data']['specialTicketDTO']) {
                foreach ($result['data']['specialTicketDTO'] as $tKey => $tValue) {
                    $tmpData['special'][$tKey] = $tValue;
                }
            }
            if (isset($result['data']['pftReserveStorageDTO']) && $result['data']['pftReserveStorageDTO']) {
                $tmpData['data']['pftReserveStorageDTO'] = $result['data']['pftReserveStorageDTO'];
            }
            $result = $tmpData;
        } else {
            $tmpData['ticket']  = $result['data']['uuJqTicketDTO'];
            $tmpData['product'] = $result['data']['uuProductDTO'];
            $tmpData['land']    = $result['data']['uuLandDTO'];
            $tmpData['land_f']  = $result['data']['uuLandFDTO'];
            $tmpData['ext']     = $result['data']['confs'];
            $tmpData['special'] = $result['data']['specialTicketDTO'];
            if (isset($result['data']['pftReserveStorageDTO'])) {
                $tmpData['ticket']['reserve_storage'] = $result['data']['pftReserveStorageDTO'];
            }
            if (isset($result['data']['landBuyLimitRuleConfig'])) {
                $tmpData['ticket']['land_buy_limit_rule_config'] = $result['data']['landBuyLimitRuleConfig'];
            } else {
                $tmpData['ticket']['land_buy_limit_rule_config'] = [
                    //购票限制相关
                    "buyer_limit_type"     => 0, // 取票人限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                    "buyer_order_days"     => 0, // 取票人限购订单笔数间隔天数
                    "buyer_order_total"    => 0, // 取票人限购订单笔数
                    "buyer_ticket_days"    => 0, // 取票人限购票笔数间隔天数
                    "buyer_ticket_total"   => 0, // 取票人限购票类总数
                    "tourist_limit_type"   => 0, // 游客限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                    "tourist_order_days"   => 0, // 游客限购订单笔数
                    "tourist_order_total"  => 0, // 游客限购订单笔数间隔天数
                    "tourist_ticket_days"  => 0, // 游客限购票笔数间隔天数
                    "tourist_ticket_total" => 0, // 游客限购票类总数
                    "id"                   => 0
                ];
            }
            $result = $tmpData;
        }

        return $result;
    }

    /**
     * 根据ticketIds 获取票据信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $ticketIdArr  门票ID数组
     *
     * @return array
     */
    public function queryTicketInfoByIds($ticketIdArr, $tField = '', $pField = '', $lField = '', $lFField = '', $isGetField = false)
    {
        if (empty($ticketIdArr)) {
            return [];
        }
        $result = $this->_javaApi->queryTicketInfoByIds($ticketIdArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['ticket'][$ticketInfo['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['ticket'][$tKey] = $tValue;
                        }
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['ticket'][$ticketInfo['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['ticket'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($pFieldArr) {
                    foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $pFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['product'][$ticketInfo['uuProductDTO'][$pFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['product'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }
                if ($lFieldArr) {
                    foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['land'][$ticketInfo['uuLandDTO'][$lFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['land'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }
                if ($lFFieldArr) {
                    if (!empty($ticketInfo['uuLandFDTO'])) {
                        foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                            if (!in_array($tKey, $lFFieldArr)) {
                                continue;
                            }
                            if ($isGetField) {
                                $tmpData[$idx]['land_f'][$ticketInfo['uuLandFDTO'][$lFFieldArr[0]]][$tKey] = $tValue;
                            } else {
                                $tmpData[$idx]['land_f'][$tKey] = $tValue;
                            }
                        }
                    }

                } else {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }

                if (isset($ticketInfo['confs']) && $ticketInfo['confs']) {
                    foreach ($ticketInfo['confs'] as $tKey => $tValue) {
                        $tmpData[$idx]['ext'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['specialTicketDTO']) && $ticketInfo['specialTicketDTO']) {
                    foreach ($ticketInfo['specialTicketDTO'] as $tKey => $tValue) {
                        $tmpData[$idx]['special'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['landBuyLimitRuleConfig']) && $ticketInfo['landBuyLimitRuleConfig']) {
                    $tmpData[$idx]['landBuyLimitRuleConfig'] = $ticketInfo['landBuyLimitRuleConfig'];
                }
            }

            $result = $tmpData;
        } else {
            foreach ($result['data'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
                $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
                $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
                $tmpData[$idx]['ext']     = $ticketInfo['confs'];
                $tmpData[$idx]['special'] = $ticketInfo['specialTicketDTO'];
                if (isset($ticketInfo['landBuyLimitRuleConfig']) && $ticketInfo['landBuyLimitRuleConfig']) {
                    $tmpData[$idx]['landBuyLimitRuleConfig'] = $ticketInfo['landBuyLimitRuleConfig'];
                }
            }

            $result = $tmpData;
        }

        return $result;
    }

    /**
     * 根据productIds 获取票据信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $productIdArr  产品ID数组
     *
     * @return array
     */
    public function queryTicketInfoByProductIds($productIdArr, $tField = '', $pField = '', $lField = '', $lFField = '', $isGetField = false)
    {
        if (empty($productIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketInfoByProductIds($productIdArr);

        //如果是系统异常的话，直接抛出异常
        if ($result['code'] == 500) {
            throw new \Exception('系统异常，请重试', 500);
        }

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['ticket'][$ticketInfo['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['ticket'][$tKey] = $tValue;
                        }
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['ticket'][$ticketInfo['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['ticket'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($pFieldArr) {
                    foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $pFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['product'][$ticketInfo['uuProductDTO'][$pFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['product'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }
                if ($lFieldArr) {
                    foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['land'][$ticketInfo['uuLandDTO'][$lFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['land'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }
                if ($lFFieldArr) {
                    foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['land_f'][$ticketInfo['uuLandFDTO'][$lFFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['land_f'][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }

                if (isset($ticketInfo['confs']) && $ticketInfo['confs']) {
                    foreach ($ticketInfo['confs'] as $tKey => $tValue) {
                        $tmpData[$idx]['ext'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['specialTicketDTO']) && $ticketInfo['specialTicketDTO']) {
                    foreach ($ticketInfo['specialTicketDTO'] as $tKey => $tValue) {
                        $tmpData[$idx]['special'][$tKey] = $tValue;
                    }
                }
            }

            $result = $tmpData;
        } else {
            foreach ($result['data'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
                $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
                $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
                $tmpData[$idx]['ext']     = $ticketInfo['confs'];
                $tmpData[$idx]['special'] = $ticketInfo['specialTicketDTO'];
            }

            $result = $tmpData;
        }

        return $result;
    }

    /**
     * 根据ticketIds+（title or p_type or landId） 获取票据信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $ticketIdArr  门票ID数组
     * @param  string  $title  标题
     * @param  array  $pTypeArr  产品类型
     * @param  int  $landId  景区id
     *
     * @return array
     */
    public function queryTicketInfoByTicketIdsAndTitleOrPTypeOrLandId($ticketIdArr, $tField = '', $pField = '', $lField = '', $lFField = '', $title = null, $pTypeArr = null, $landId = null)
    {

        if (empty($ticketIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketInfoByTicketIdsAndTitleOrPTypeOrLandId($ticketIdArr, $title, $pTypeArr,
            (int)$landId);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($pFieldArr) {
                    foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $pFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['product'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }
                if ($lFieldArr) {
                    foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }
                if ($lFFieldArr) {
                    foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land_f'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }
            }

            $result = $tmpData;
        } else {
            foreach ($result['data'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
                $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
                $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
            }

            $result = $tmpData;
        }

        return $result;
    }

    /**
     * 根据ProductIds 获取票据ID
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $productIdArr  产品ID数组
     *
     * @return array
     */
    public function queryTicketIdsByProductIds($productIdArr)
    {
        if (empty($productIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketIdsByProductIds($productIdArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $result = $result['data'];

        return $result;
    }

    /**
     * 根据ticketIds 获取商品ID
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $ticketIdArr  门票ID数组
     *
     * @return array
     */
    public function queryProductIdsByTicketIds($ticketIdArr)
    {
        if (empty($ticketIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryProductIdsByTicketIds($ticketIdArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }
        $result = $result['data'];

        return $result;
    }

    /**
     * 根据applyDid和其他条件查询票据信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $status  状态
     * @param  array  $pType  产品类型
     * @param  int  $pageNum  页码
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryTicketInfoByApplyDid($applyDidArr, $field = '', $status = null, $pType = null, $pageNum = null, $pageSize = null)
    {
        if (empty($applyDidArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketInfoByApplyDid($applyDidArr, $status, $pType, $pageNum, $pageSize);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            return [];
        }

        $tmpData = $tFieldArr = [];
        if ($field) {
            $tFieldArr = $this->replaceStr($field);
        }

        if ($field) {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx][uncamelize($tKey)] = $tValue;
                    }
                } else {
                    $tmpData[$idx] = $ticketInfo;
                }
            }

            $result['data']['list'] = $tmpData;
        }

        return $result;
    }

    /**
     * 根据applyDid和其他条件查询景区信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $status  状态
     * @param  int  $pageNum  页码
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryLandInfoByApplyDid($applyDid, $field = '', $status = null, $pageNum = null, $pageSize = null)
    {
        if (empty($applyDid)) {
            return [];
        }

        $result = $this->_javaApi->queryLandInfoByApplyDid($applyDid, $status, $pageNum, $pageSize);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = [];

        if ($field) {
            $tFieldArr = $this->replaceStr($field);
        }

        if ($field) {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx] = $ticketInfo;
                }
            }

            $result['data']['list'] = $tmpData;
        }

        return $result;
    }

    /**
     * 根据landId和其他条件查询景区信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $landId  景区id
     * @param  string  $title  标题
     *
     * @return array
     */
    public function queryTicketIdsBylandId($landId, $title = null)
    {
        if (empty($landId)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketIdsBylandId($landId, $title);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }
        $result = $result['data'];

        return $result;
    }

    /**
     * 根据applyDid和其他条件查询景区信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $landIdArr  景区id数组
     * @param  int  $pay  支付方式
     *
     * @return array | bool
     */
    public function queryTicketBylandIdAndPay($landIdArr, $field = '', $applyDid = null, $pay = null)
    {
        if (empty($landIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketBylandIdAndPay($landIdArr, $applyDid, $pay);

        if ($result['code'] != 200 || empty($result['data'])) {
            return false;
        }

        $tmpData = $tFieldArr = [];

        if ($field) {
            $tFieldArr = $this->replaceStr($field);
        }

        if ($field) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx] = $ticketInfo;
                }
            }

            $result = $tmpData;
        } else {
            $result = $result['data'];
        }

        return $result;
    }

    /**
     * 根据票id获取门票主表信息-uu_jq_ticket
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $ticketId  门票id
     *
     * @return array
     */
    public function queryTicketById($ticketId, $field = '', $isGetField = false)
    {
        if (empty($ticketId)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketById($ticketId);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = [];
        if ($field) {
            $tFieldArr = $this->replaceStr($field);
        }

        if ($field) {
            if ($tFieldArr) {
                foreach ($result['data'] as $tKey => $tValue) {
                    if (!in_array($tKey, $tFieldArr)) {
                        continue;
                    }
                    if ($isGetField) {
                        $tmpData[$result['data'][$tFieldArr[0]]][$tKey] = $tValue;
                    } else {
                        $tmpData[$tKey] = $tValue;
                    }
                }
            } else {
                $tmpData = $result['data'];
            }

            $result = $tmpData;
        } else {
            $result = $result['data'];
        }

        return $result;
    }

    /**
     * 批量获取门票列表
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $ticketIdArr  门票id数组
     *
     * @return array
     */
    public function batchQueryTicketByTicketIds($ticketIdArr, $field = '', $isGetField = false)
    {
        if (empty($ticketIdArr)) {
            return [];
        }

        $result = $this->_javaApi->batchQueryTicketByTicketIds($ticketIdArr);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = [];
        if ($field) {
            $tFieldArr = $this->replaceStr($field);
        }

        if ($field) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$ticketInfo[$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx] = $ticketInfo;
                }
            }

            $result = $tmpData;
        } else {
            $result = $result['data'];
        }

        return $result;
    }

    /**
     * 查询票扩展数据 atr
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $ticketId  门票id
     *
     * @return array
     */
    public function queryTicketAttrsById($ticketId)
    {
        if (empty($ticketId)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketAttrsById($ticketId);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $result = $result['data'];

        return $result;
    }

    /**
     * 根据票id获取门票扩展信息
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $ticketId  门票id
     *
     * @return array
     */
    public function queryTicketExtendInfo($ticketId)
    {
        if (empty($ticketId)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketExtendInfo($ticketId);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $result = $result['data'];

        return $result;
    }

    /**
     * 根据景区id 获取 景区下的票
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $landId  景区id
     * @param  int  $status  门票状态
     *
     * @return array
     */
    public function queryTicketListByLandId($landId, $field = '', $isGetField = false, $status = null)
    {
        if (empty($landId)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketListByLandId($landId, $status);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = [];
        if ($field) {
            $tFieldArr = $this->replaceStr($field);
        }

        if ($field) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$ticketInfo[$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx] = $ticketInfo;
                }
            }

            $result = $tmpData;
        } else {
            $result = $result['data'];
        }

        return $result;
    }

    /**
     * 根据landid +p.apply_limit + p.p_status
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $applyDisArr  供应商id数组
     * @param  array  $landIdArr  景区id数组
     * @param  array  $pTypeArr  产品类型属猪
     * @param  int  $applyLimit  产品审核状态
     * @param  int  $pStatus  产品状态
     * @param  int  $status  门票状态
     * @param  int  $lStatus  景区状态
     * @param  int  $pageNum  页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryTicketInfoByApplyDidAndLandIdAndPTypeAndApplyLimitAndPStatusAndStatus($applyDisArr = null, $landIdArr = null, $tField = '', $pField = '', $lField = '', $lFField = '', $pTypeArr = null, $applyLimit = null, $pStatus = null, $status = null, $lStatus = null, $title = null, $pageNum = null, $pageSize = null)
    {
        if (empty($applyDisArr) && empty($landIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketInfoByApplyDidAndLandIdAndPTypeAndApplyLimitAndPStatusAndStatus($applyDisArr,
            $landIdArr, $pTypeArr, $applyLimit, $pStatus, $status, $lStatus, $title, $pageNum, $pageSize);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($pFieldArr) {
                    foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $pFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['product'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }
                if ($lFieldArr) {
                    foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }
                if ($lFFieldArr) {
                    foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land_f'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }

                if (isset($ticketInfo['confs']) && $ticketInfo['confs']) {
                    foreach ($ticketInfo['confs'] as $tKey => $tValue) {
                        $tmpData[$idx]['ext'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['specialTicketDTO']) && $ticketInfo['specialTicketDTO']) {
                    foreach ($ticketInfo['specialTicketDTO'] as $tKey => $tValue) {
                        $tmpData[$idx]['special'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['landBuyLimitRuleConfig']) && $ticketInfo['landBuyLimitRuleConfig']) {
                    $tmpData[$idx]['landBuyLimitRuleConfig'] = $ticketInfo['landBuyLimitRuleConfig'];
                }
            }

            $result['data']['list'] = $tmpData;
        } else {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
                $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
                $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
                $tmpData['ext']           = $result['data']['confs'];
                $tmpData['special']       = $result['data']['specialTicketDTO'];
                if (isset($result['data']['pftReserveStorageDTO'])) {
                    $tmpData['ticket']['reserve_storage'] = $result['data']['pftReserveStorageDTO'];
                }
                if (isset($result['data']['landBuyLimitRuleConfig'])) {
                    $tmpData['ticket']['land_buy_limit_rule_config'] = $result['data']['landBuyLimitRuleConfig'];
                } else {
                    $tmpData['ticket']['land_buy_limit_rule_config'] = [
                        //购票限制相关
                        "buyer_limit_type"     => 0, // 取票人限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                        "buyer_order_days"     => 0, // 取票人限购订单笔数间隔天数
                        "buyer_order_total"    => 0, // 取票人限购订单笔数
                        "buyer_ticket_days"    => 0, // 取票人限购票笔数间隔天数
                        "buyer_ticket_total"   => 0, // 取票人限购票类总数
                        "tourist_limit_type"   => 0, // 游客限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                        "tourist_order_days"   => 0, // 游客限购订单笔数
                        "tourist_order_total"  => 0, // 游客限购订单笔数间隔天数
                        "tourist_ticket_days"  => 0, // 游客限购票笔数间隔天数
                        "tourist_ticket_total" => 0, // 游客限购票类总数
                        "id"                   => 0,
                    ];
                }
            }

            $result['data']['list'] = $tmpData;
        }

        return $result;
    }

    /**
     * 根据landid +p.apply_limit + p.p_status
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $productIdArr  产品id数组
     * @param  int  $applyDid  供应商ID
     * @param  int  $pay
     * @param  int  $applyLimit  产品审核状态
     * @param  array  $pStatus  产品状态
     * @param  int  $pageNum  页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryTicketInfoByProductIdAndApplyDidAndPayAndPStatusAndApplyLimit($productIdArr = null, $tField = '', $pField = '', $lField = '', $lFField = '', $applyDid = null, $pay = null, $applyLimit, $pStatus = null, $pageNum = null, $pageSize = null)
    {
        if (empty($productIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketInfoByProductIdAndApplyDidAndPayAndPStatusAndApplyLimit($productIdArr,
            $applyDid, $pay, $applyLimit, $pStatus, $pageNum, $pageSize);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($pFieldArr) {
                    foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $pFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['product'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }
                if ($lFieldArr) {
                    foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }
                if ($lFFieldArr) {
                    foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land_f'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }
            }

            $result['data']['list'] = $tmpData;
        } else {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
                $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
                $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
            }

            $result['data']['list'] = $tmpData;
        }

        return $result['data'];
    }

    /**
     * 根据 land_id + title + apply_did + apply_limit + p_status 返回total+返回翻页信息
     * <AUTHOR> Li
     * @date 2020-01-20
     *
     * @param  int  $landId  景区id
     * @param  int  $applyDid  供应商ID
     * @param  int  $applyLimit  产品审核状态
     * @param  int  $pStatus  产品状态
     * @param  string  $title  门票名称
     * @param  int  $pageNum  页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryTicketInfoByLandIdAndTitleAndApplyDidAndApplyLimitAndPStatus($landId = null, $tField = '', $pField = '', $lField = '', $lFField = '', $applyDid = null, $applyLimit = null, $pStatus = null, $title = null, $pageNum = null, $pageSize = null)
    {
        if (empty($landId) && empty($applyDid)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketInfoByLandIdAndTitleAndApplyDidAndApplyLimitAndPStatus($landId, $applyDid,
            $applyLimit, $pStatus, $title, $pageNum, $pageSize);

        if ($result['code'] != 200 || empty($result['data']['list'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                    foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['ticket'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($pFieldArr) {
                    foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $pFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['product'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }
                if ($lFieldArr) {
                    foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }
                if ($lFFieldArr) {
                    foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $lFFieldArr)) {
                            continue;
                        }
                        $tmpData[$idx]['land_f'][$tKey] = $tValue;
                    }
                } else {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }

                if (isset($ticketInfo['confs']) && $ticketInfo['confs']) {
                    foreach ($ticketInfo['confs'] as $tKey => $tValue) {
                        $tmpData[$idx]['ext'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['specialTicketDTO']) && $ticketInfo['specialTicketDTO']) {
                    foreach ($ticketInfo['specialTicketDTO'] as $tKey => $tValue) {
                        $tmpData[$idx]['special'][$tKey] = $tValue;
                    }
                }
                if (isset($ticketInfo['landBuyLimitRuleConfig']) && $ticketInfo['landBuyLimitRuleConfig']) {
                    $tmpData[$idx]['landBuyLimitRuleConfig'] = $ticketInfo['landBuyLimitRuleConfig'];
                }
            }

            $result['data']['list'] = $tmpData;
        } else {
            foreach ($result['data']['list'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
                $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
                $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
                $tmpData['ext']           = $result['data']['confs'];
                $tmpData['special']       = $result['data']['specialTicketDTO'];
                if (isset($result['data']['pftReserveStorageDTO'])) {
                    $tmpData['ticket']['reserve_storage'] = $result['data']['pftReserveStorageDTO'];
                }
                if (isset($result['data']['landBuyLimitRuleConfig'])) {
                    $tmpData['ticket']['land_buy_limit_rule_config'] = $result['data']['landBuyLimitRuleConfig'];
                } else {
                    $tmpData['ticket']['land_buy_limit_rule_config'] = [
                        //购票限制相关
                        "buyer_limit_type"     => 0, // 取票人限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                        "buyer_order_days"     => 0, // 取票人限购订单笔数间隔天数
                        "buyer_order_total"    => 0, // 取票人限购订单笔数
                        "buyer_ticket_days"    => 0, // 取票人限购票笔数间隔天数
                        "buyer_ticket_total"   => 0, // 取票人限购票类总数
                        "tourist_limit_type"   => 0, // 游客限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                        "tourist_order_days"   => 0, // 游客限购订单笔数
                        "tourist_order_total"  => 0, // 游客限购订单笔数间隔天数
                        "tourist_ticket_days"  => 0, // 游客限购票笔数间隔天数
                        "tourist_ticket_total" => 0, // 游客限购票类总数
                        "id"                   => 0,
                    ];
                }
            }

            $result['data']['list'] = $tmpData;
        }

        return $result;
    }

    /**
     * 根据条件查询套票信息
     * <AUTHOR> Li
     * @date 2020-01-20
     *
     * @param  array  $ticketIdArr  门票id数组
     * @param  int  $applyDid  供应商ID
     * @param  int  $applyLimit  产品审核状态
     * @param  int  $pStatus  产品状态
     * @param  int  $pageNum  页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function queryPackTicketByApplyDidAndTicketIdAndTitleAndPType($ticketIdArr = null, $field = '', $isGetField = false, $applyDid = null, $title = null, $pType = null)
    {
        if (empty($ticketIdArr) && empty($applyDid)) {
            return [];
        }

        $result = $this->_javaApi->queryPackTicketByApplyDidAndTicketIdAndTitleAndPType($ticketIdArr, $applyDid, $title,
            $pType);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = [];
        if ($field) {
            $tFieldArr = $this->replaceStr($field);
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$ticketInfo[$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx][$tKey] = $tValue;
                        }
                    }
                } else {
                    $tmpData[$idx] = $ticketInfo;
                }
            }

            $result = $tmpData;
        } else {
            $result = $result['data'];
        }

        return $result;
    }

    /**
     * 根据条件查询套票信息
     * <AUTHOR> Li
     * @date 2020-01-20
     *
     * @param  string  $tField  查询门票字段
     * @param  string  $pField  查询产品字段
     * @param  string  $lField  查询景区字段
     * @param  string  $lFField  查询景区扩展字段
     * @param  int  $landFlag  是否查询景区信息
     * @param  int  $productFlag  是否查询产品信息
     * @param  int  $landFFlag  是否查询景区扩展信息
     * @param  bool  $isGetField  是否getField格式返回
     * @param  array  $ticketIdArr  门票id数组
     * @param  array  $landIdArr  景区id数组
     * @param  array  $productIdArr  产品id数组
     * @param  array  $condition  其他扩展查询条件   $condition = [
     * 'pStatuss'             => $pStatusArr,           销售状态
     * 'applyLimit'           => $applyLimit,           0未审核1已审核2下架3被拒绝6删除
     * 'pay'                  => $pay,                  支付方式
     * 'ticketTitle'          => $ticketTitle,          门票名称
     * 'landTitle'            => $landTitle,            景区名称
     * 'letters'              => $letters,              景区名称的拼音首字母
     * 'cancelNotifySupplier' => $cancelNotifySupplier, 取消订单时短信通知供应商
     * 'faxNotNull'           => $faxNotNull,           fox字段非空 0：不过滤 1：排除空值数据 默认值 0
     * 'landFlag'             => $landFlag,             是否获取land信息 0：不获取 1：获取 默认值 0
     * 'productFlag'          => $productFlag,          是否获取product信息 0：不获取 1：获取 默认值 0
     * 'landFFlag'            => $landFFlag,            是否获取landF信息 0：不获取 1：获取 默认值 0
     * 'pTypes'               => $pTypeArr,             景区类型
     * 'status'               => $status,               门票状态
     * 'landStatus'           => $lStatus,              景区状态
     * ];
     *
     * @return array
     */
    public function queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($tField = '', $pField = '', $lField = '', $lFField = '', $isGetField = false, $ticketIdArr = null, $landIdArr = null, $applyDid = null, $productIdArr = null, $condition = [])
    {

        if (empty($ticketIdArr) && empty($landIdArr) && empty($applyDid) && empty($productIdArr)) {
            return [];
        }

        $result = $this->_javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($ticketIdArr, $landIdArr,
            $productIdArr, $applyDid, $condition);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        $tmpData = $tFieldArr = $pFieldArr = $lFieldArr = $lFFieldArr = [];
        if ($tField) {
            $tFieldArr = $this->replaceStr($tField);
        }
        if ($pField) {
            $pFieldArr = $this->replaceStr($pField);
        }
        if ($lField) {
            $lFieldArr = $this->replaceStr($lField);
        }
        if ($lFField) {
            $lFFieldArr = $this->replaceStr($lFField);
        }

        $landFlag    = isset($condition['landFlag']) ? 1 : 0;
        $productFlag = isset($condition['productFlag']) ? 1 : 0;
        $landFFlag   = isset($condition['landFFlag']) ? 1 : 0;

        if ($tField || $pField || $lField || $lFField) {
            foreach ($result['data'] as $idx => $ticketInfo) {
                if ($tFieldArr) {
                    foreach ($ticketInfo['uuJqTicketDTO'] as $tKey => $tValue) {
                        if (!in_array($tKey, $tFieldArr)) {
                            continue;
                        }
                        if ($isGetField) {
                            $tmpData[$idx]['ticket'][$ticketInfo['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                        } else {
                            $tmpData[$idx]['ticket'][$tKey] = $tValue;
                        }
                    }
                    if (isset($ticketInfo['thridTicketAttributesDTO']) && $ticketInfo['thridTicketAttributesDTO']) {
                        foreach ($ticketInfo['thridTicketAttributesDTO'] as $tKey => $tValue) {
                            if (!in_array($tKey, $tFieldArr)) {
                                continue;
                            }
                            if ($isGetField) {
                                $tmpData[$idx]['ticket'][$ticketInfo['uuJqTicketDTO'][$tFieldArr[0]]][$tKey] = $tValue;
                            } else {
                                $tmpData[$idx]['ticket'][$tKey] = $tValue;
                            }
                        }
                    }

                } else {
                    $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                }

                if ($productFlag && isset($ticketInfo['uuProductDTO'])) {
                    if ($pFieldArr) {
                        foreach ($ticketInfo['uuProductDTO'] as $tKey => $tValue) {
                            if (!in_array($tKey, $pFieldArr)) {
                                continue;
                            }
                            if ($isGetField) {
                                $tmpData[$idx]['product'][$ticketInfo['uuProductDTO'][$pFieldArr[0]]][$tKey] = $tValue;
                            } else {
                                $tmpData[$idx]['product'][$tKey] = $tValue;
                            }
                        }
                    } else {
                        $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                    }
                }

                if ($landFlag && isset($ticketInfo['uuLandDTO'])) {
                    if ($lFieldArr) {
                        foreach ($ticketInfo['uuLandDTO'] as $tKey => $tValue) {
                            if (!in_array($tKey, $lFieldArr)) {
                                continue;
                            }
                            if ($isGetField) {
                                $tmpData[$idx]['land'][$ticketInfo['uuLandDTO'][$lFieldArr[0]]][$tKey] = $tValue;
                            } else {
                                $tmpData[$idx]['land'][$tKey] = $tValue;
                            }
                        }
                    } else {
                        $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                    }
                }

                if ($landFFlag && isset($ticketInfo['uuLandFDTO'])) {
                    if ($lFFieldArr) {
                        foreach ($ticketInfo['uuLandFDTO'] as $tKey => $tValue) {
                            if (!in_array($tKey, $lFFieldArr)) {
                                continue;
                            }
                            if ($isGetField) {
                                $tmpData[$idx]['land_f'][$ticketInfo['uuLandFDTO'][$lFFieldArr[0]]][$tKey] = $tValue;
                            } else {
                                $tmpData[$idx]['land_f'][$tKey] = $tValue;
                            }
                        }
                    } else {
                        $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                    }
                }

            }

            $result = $tmpData;
        } else {
            foreach ($result['data'] as $idx => $ticketInfo) {
                $tmpData[$idx]['ticket'] = $ticketInfo['uuJqTicketDTO'];
                if ($productFlag && isset($ticketInfo['uuProductDTO'])) {
                    $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
                }

                if ($landFlag && isset($ticketInfo['uuLandDTO'])) {
                    $tmpData[$idx]['land'] = $ticketInfo['uuLandDTO'];
                }

                if ($landFFlag && isset($ticketInfo['uuLandFDTO'])) {
                    $tmpData[$idx]['land_f'] = $ticketInfo['uuLandFDTO'];
                }

            }

            $result = $tmpData;
        }

        return $result;
    }

    public function _uncamelize($res)
    {
        $uncamelizeData = [];
        foreach ($res as $key => $val) {
            if (is_array($val)) {
                foreach ($val as $key2 => $val2) {
                    if (is_array($val2)) {
                        foreach ($val2 as $key3 => $val3) {
                            $uncamelizeData[uncamelize($key)][uncamelize($key2)][uncamelize($key3)] = $val3;
                        }
                    } else {
                        $uncamelizeData[uncamelize($key)][uncamelize($key2)] = $val2;
                    }
                }
            } else {
                $uncamelizeData[uncamelize($key)] = $val;
            }
        }
        $res = $uncamelizeData;

        return $res;
    }

    /**
     * 获取land表是否通知和传真字段
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrTid  票id
     *
     * @return array
     */
    public function getSellerNotifyInfoByArrTid($arrTid)
    {
        if (empty($arrTid)) {
            return [];
        }
        $condition = [
            'landFlag'             => 1,
            'cancelNotifySupplier' => 1,
            'faxNotNull'           => 1,
        ];
        $javaData  = $this->_javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($arrTid, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $landList = $this->_handleField($javaData['data'], '', 'fax,apply_did');

        return $landList ? $landList : [];
    }

    public function getTicketAndLandInfoByArrPidHandle($arrPid)
    {
        if (empty($arrPid)) {
            return [];
        }
        $ticketField = 'pid';
        $landField   = 'title,imgpath';
        $asField     = [
            'uuJqTicketDTO' => [
                'id'    => 'tid',
                'title' => 'ttitle',
                'pid'   => 'id',
                'status' => 'status',
                'max_expiration_date' => 'max_expiration_date',
            ],
            'uuLandDTO'     => [
                'id' => 'landid',
            ],
            'confs' => [
                'annual_renewal_type'       => 'annual_renewal_type',
                'annual_renew_user_channel' => 'annual_renew_user_channel',
            ],

        ];
        $list        = $this->getTicketAndLandInfoByArrPid($arrPid, $ticketField, $landField, $asField);

        $resData     = [];
        foreach ($list as $key => $value) {
            $resData[$value['id']] = $value;
        }

        return $resData ? $resData : [];
    }

    /**
     * 根据pid获取ticket和land表数据
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrPid  产品id
     *
     * @return array
     */
    public function getTicketAndLandInfoByArrPid($arrPid, $ticketField = '', $landField = '', $asField = [])
    {
        if (empty($arrPid)) {
            return [];
        }
        $condition = [
            'landFlag' => 1,
        ];
        $javaData  = $this->_javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable(null, null,
            $arrPid, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $list = $this->_handleField($javaData['data'], $ticketField, $landField, '', '', $asField);

        return $list ? $list : [];
    }

    /**
     * 根据id获取ticket和land表数据
     *
     * @param  array  $arrTid  票id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/2/10
     *
     * */
    public function getTicketAndLandInfoByArrTidHandle($arrTid)
    {
        if (empty($arrTid)) {
            return [];
        }
        $ticketField = 'id,pid,chk_terminal_info';
        $landField   = 'title,imgpath';
        $asField     = [
            'uuJqTicketDTO' => [
                'id'    => 'tid',
                'title' => 'ttitle',
            ],
            'uuLandDTO'     => [
                'id' => 'landid',
            ],
        ];
        $list        = $this->getTicketAndLandInfoByArrTid($arrTid, $ticketField, $landField, $asField);
        $resData     = [];
        foreach ($list as $key => $value) {
            $resData[$value['id']] = $value;
        }

        return $resData ? $resData : [];
    }

    /**
     * 根据id获取ticket和land表数据
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrTid  票id
     *
     * @return array
     */
    public function getTicketAndLandInfoByArrTid($arrTid, $ticketField = '', $landField = '', $asField = [])
    {
        if (empty($arrTid)) {
            return [];
        }
        $condition = [
            'landFlag' => 1,
        ];
        $javaData  = $this->_javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($arrTid, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $list = $this->_handleField($javaData['data'], $ticketField, $landField, '', '', $asField);

        return $list ? $list : [];
    }

    /**
     * 根据pid和sid获取票信息(单表查)
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrPid  产品id数组
     * @param  int  $sid  供应商id
     *
     * @return array
     */
    public function getTicketInfoByArrPidAndSid($arrPid, $sid, $ticketField = 'id,pid,landid')
    {
        if (empty($arrPid) || !$sid) {
            return [];
        }
        $javaData = $this->_javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable(null, null,
            $arrPid, $sid, []);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        $list = $this->_handleField($javaData['data'], $ticketField);

        return $list ? $list : [];
    }

    /**
     * 根据pid和landTitle或者letter获取票信息
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrPid  产品id数组
     * @param  string  $title  景区名字
     * @param  string  $letter  景区英文缩写
     *
     * @return array
     */
    public function getTicketInfoByArrPidAndLandTitleOrLetter($arrPid, $title = '', $letter = '')
    {
        if (empty($arrPid)) {
            return [];
        }
        $landField = 'id,title';
        $condition = [
            'landFlag'  => 1,
            'landTitle' => $title,
            'letters'   => $letter,
        ];
        $javaData  = $this->_javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable(null, null, $arrPid,
            null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $list = $this->_handleField($javaData['data'], '', $landField);

        return $list ? $list : [];
    }

    /**
     * 根据条件获取门票属性
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  array  $queryInfoArr  景区ID [['ticketId'=,'sid'=>,'fid'=>,'channel'=>],] -- sid/fid/channel 参数废弃
     * @param  array  $queryInfoArr  景区ID [['ticketId'=],]
     *
     * @return array
     */
    public function queryTicketInfoByQueryInfos($queryInfoArr)
    {
        if (empty($queryInfoArr)) {
            return [];
        }
        $result = $this->_javaApi->queryTicketInfoByQueryInfos($queryInfoArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        foreach ($result['data'] as $idx => $ticketInfo) {

            $tmpData[$idx]['ticket']  = $ticketInfo['uuJqTicketDTO'];
            $tmpData[$idx]['product'] = $ticketInfo['uuProductDTO'];
            $tmpData[$idx]['land']    = $ticketInfo['uuLandDTO'];
            $tmpData[$idx]['land_f']  = $ticketInfo['uuLandFDTO'];
            $tmpData[$idx]['ext']     = $ticketInfo['confs'];
        }

        return $tmpData;
    }

    /**
     * 通过场馆id获取景区下门票自动校验时间
     *
     * <AUTHOR> Li
     * @date 2021-09-01
     *
     * @param  int  $sid  供应商id
     * @param  int  $venueId  场馆id
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function showAutoCheckByZoneId(int $sid, int $venueId, int $page = 1, int $size = 10)
    {
        if (!$sid || !$venueId) {
            return [];
        }
        $result = $this->_javaApi->showAutoCheckByZoneId($sid, $venueId, $page, $size);
        if ($result['code'] != 200 || empty($result['data']['list'])) {
            return [];
        }
        $return = [
            'list'  => $result['data']['list'],
            'total' => $result['data']['total'],
        ];

        return $return;
    }

    /**
     * 获取票名称列表
     * <AUTHOR> Li
     * @date 2022-04-07
     *
     * @param  int  $landId  景区id
     * @param  int  $status  景区状态  1上架 2下架
     * @param  string  $title  门票名称
     *
     * @return array
     */
    public function queryTicketIdAndNameByItemId(int $landId, int $status, string $title)
    {
        if (!$landId || !$status) {
            return [];
        }

        $result = $this->_javaApi->queryTicketIdAndNameByItemId($landId, $status, $title);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    private function _handleField($list, $ticketField = '', $landField = '', $productField = '', $landFField = '', $asField = [])
    {
        $data = [];
        if (!empty($list)) {
            if ($productField) {
                $productFieldArr = $this->_trimField(explode(',', $productField));
            }
            if ($landField) {
                $landFieldArr = $this->_trimField(explode(',', $landField));
            }
            if ($ticketField) {
                $ticketFieldArr = $this->_trimField(explode(',', $ticketField));
            }
            if ($landFField) {
                $landFFieldArr = $this->_trimField(explode(',', $landFField));
            }
            if (empty($productFieldArr) && empty($ticketFieldArr) && empty($landFFieldArr) && empty($landFieldArr) && empty($asField)) {
                foreach ($list as $key => $value) {
                    $mergerData = [];
                    foreach ($this->tableName as $k => $v) {
                        if (isset($value[$v])) {
                            $mergerData = array_merge($mergerData, $value[$v]);
                        }
                    }
                    $data[$key] = $mergerData;
                }
            } else {
                foreach ($list as $key => $value) {
                    if ($productFieldArr) {
                        if (isset($value['uuProductDTO'])) {
                            foreach ($value['uuProductDTO'] as $k1 => $v1) {
                                if (in_array($k1, $productFieldArr)) {
                                    $data[$key][$k1] = $v1;
                                }
                                if (isset($asField['uuProductDTO'])) {
                                    if (isset($asField['uuProductDTO'][$k1])) {
                                        $data[$key][$asField['uuProductDTO'][$k1]] = $v1;
                                    }
                                }
                            }
                        }
                    }
                    if ($ticketFieldArr) {
                        if (isset($value['uuJqTicketDTO'])) {
                            $ticketDtoName = 'uuJqTicketDTO';
                            foreach ($value[$ticketDtoName] as $k3 => $v3) {
                                if (in_array($k3, $ticketFieldArr)) {
                                    $data[$key][$k3] = $v3;
                                }
                                if (isset($asField[$ticketDtoName])) {
                                    if (isset($asField[$ticketDtoName][$k3])) {
                                        $data[$key][$asField[$ticketDtoName][$k3]] = $v3;
                                    }
                                }
                            }
                        }
                    }
                    if ($landFFieldArr) {
                        if (isset($value['uuLandFDTO'])) {
                            $landFDtoName = 'uuLandFDTO';
                            foreach ($value[$landFDtoName] as $k4 => $v4) {
                                if (in_array($k4, $landFFieldArr)) {
                                    $data[$key][$k4] = $v4;
                                }
                                if (isset($asField[$landFDtoName])) {
                                    if (isset($asField[$landFDtoName][$k4])) {
                                        $data[$key][$asField[$landFDtoName][$k4]] = $v4;
                                    }
                                }
                            }
                        }
                    }
                    if ($landFieldArr) {
                        if (isset($value['uuLandDTO'])) {
                            foreach ($value['uuLandDTO'] as $k2 => $v2) {
                                if (in_array($k2, $landFieldArr)) {
                                    $data[$key][$k2] = $v2;
                                }
                                if (isset($asField['uuLandDTO'])) {
                                    if (isset($asField['uuLandDTO'][$k2])) {
                                        $data[$key][$asField['uuLandDTO'][$k2]] = $v2;
                                    }
                                }
                            }
                        }
                    }

                    if (isset($value['confs'])) {
                        foreach ($value['confs'] as $k2 => $v2) {
                            if (in_array($k2, $landFieldArr)) {
                                $data[$key][$k2] = $v2;
                            }
                            if (isset($asField['confs'])) {
                                if (isset($asField['confs'][$k2])) {
                                    $data[$key][$asField['confs'][$k2]] = $v2;
                                }
                            }
                        }
                    }
                }
            }

        }

        return $data;
    }

    private function _trimField($data)
    {
        foreach ($data as $key => $value) {
            $data[$key] = trim($value);
        }

        return $data;
    }

    /**
     * 将传入查询字符串去空格去换行
     * <AUTHOR> Li
     * @date 2020-03-23
     *
     * @param  string  $replaceStr  需要替换的字符串
     *
     * @return array
     */
    private function replaceStr($replaceStr = '')
    {
        if (!$replaceStr || !is_string($replaceStr)) {
            return [];
        }

        return explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($replaceStr, [' ' => ''])));
    }

    /**
     * java新旧工程字段转换
     * <AUTHOR> Li
     * @date 2020-05-20
     *
     * @param  array  $ticketInfo  需要替换的字符串
     *
     * @return array
     */
    public function fieldConversion($ticketInfo = [])
    {
        if (empty($ticketInfo) || !is_array($ticketInfo)) {
            return [];
        }

        $res = [];
        if (isset($ticketInfo['land'])) {
            $mapField = load_config('old_land', 'field2java');
            foreach ($ticketInfo['land'] as $key => $value) {
                $mapKey       = isset($mapField[$key]) ? $mapField[$key] : $key;
                $res[$mapKey] = $value;
            }
        }

        if (isset($ticketInfo['product'])) {
            $mapField = load_config('old_product', 'field2java');
            foreach ($ticketInfo['product'] as $key => $value) {
                $mapKey       = isset($mapField[$key]) ? $mapField[$key] : $key;
                $res[$mapKey] = $value;
            }
        }

        if (isset($ticketInfo['land_f'])) {
            $mapField = load_config('old_land_f', 'field2java');
            foreach ($ticketInfo['land_f'] as $key => $value) {
                $mapKey       = isset($mapField[$key]) ? $mapField[$key] : $key;
                $res[$mapKey] = $value;
            }
        }

        if (isset($ticketInfo['ext'])) {
            $res['ext'] = $ticketInfo['ext'];
        }

        if (isset($ticketInfo['special'])) {
            $res['special_config'] = $ticketInfo['special']['special_config'];
        }

        if (isset($ticketInfo['ticket'])) {
            $mapField = load_config('old_ticket', 'field2java');
            foreach ($ticketInfo['ticket'] as $key => $value) {
                //延迟验证字段特殊处理下
                if ($key == 'delaytime') {
                    $tmpValue                 = explode('|', $value);
                    $res['verify_delay_hour'] = $tmpValue[0];
                    $res['verify_delay_minu'] = $tmpValue[1];
                }
                $mapKey       = isset($mapField[$key]) ? $mapField[$key] : $key;
                $res[$mapKey] = $value;

                if ($key == 'max_expiration_date') {
                    $res['max_expiration_date'] = date('Y-m-d 23:59:59', strtotime($value));
                }
            }
        }

        ksort($res);

        return $res;
    }
}
