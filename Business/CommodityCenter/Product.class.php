<?php

namespace Business\CommodityCenter;

use Business\Base;

class Product extends Base
{
    private $tableName = [
        'uuLandDTO',
        'uuProductDTO',  //合并后最后一个数组单做主表字段
        'uuJqTicketDTO',
        'uuLandFDTO',
        'uuLandFDTO',
        'confs'  //扩展数据
    ];

    /**
     * 根据产品id获取产品信息
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  int  $pid  产品id
     *
     * @return array
     */
    public function getProductInfoById($pid)
    {
        if (!$pid) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->queryProductById($pid);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'] ? $javaData['data'] : [];
    }

    /**
     * 根据多个产品id获取产品信息
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  array  $arrPid  产品id
     *
     * @return array
     */
    public function getProductInfoByIds($arrPid)
    {
        if (empty($arrPid) || !is_array($arrPid)) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->batchQueryProductById($arrPid);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'] ? $javaData['data'] : [];
    }

    /**
     * 根据多个产品id和关键之获取产品信息
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  array  $arrPid  产品id
     * @param  string  $keyWord  关键字
     *
     * @return array
     */
    public function getProductInfoByIdAndKeyWord($arrPid, $keyWord = null)
    {
        if (empty($arrPid) || !is_array($arrPid)) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->queryProductByIdsAndpNameandLetters($arrPid, $keyWord);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'] ? $javaData['data'] : [];
    }

    /**
     * 获取产品信息根据供应商和状态
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  int  $applyId  供应商id
     * @param  int  $pStatus  状态
     * @param  int  $pType  类型
     * @param  int  $applyLimit  限制
     *
     * @return array
     */
    public function getProductInfoByApplyAndStatusAndType($applyId, $pStatus = null, $pType = null, $applyLimit = null)
    {
        if (!$applyId) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->queryProductByApplyDidandPStatusandPType($applyId, $pStatus, $pType, $applyLimit);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'] ? $javaData['data'] : [];
    }

    /**
     * 获取产品信息根据景区id
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  array  $arrLid  供应商id
     * @param  array  $status  状态
     * @param  int  $applyLimit  限制
     *
     * @return array
     */
    public function getProductInfoByContactId($arrLid, $status = [], $applyLimit = null)
    {
        if (empty($arrLid) || !is_array($arrLid)) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->getProductInfoByContactIdAndStatusAndLimit($arrLid, $status, $applyLimit);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'] ? $javaData['data'] : [];
    }

    /**
     * 获取产品信息和景区信息(关联land表)
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  array  $arrPid  产品id
     *
     * @return array
     */
    public function getProductInfoAndLandInfoByArrPid($arrPid, $productField = '', $landField = '', $asField = [])
    {
        if (empty($arrPid) || !is_array($arrPid)) {
            return [];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->getProductInfoAndLandInfoByArrPid($arrPid);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        $res = $this->_handleField($javaData['data'], $productField, $landField, $asField);

        return $res ? $res : [];
    }

    public function getProductAllInfoByPidToJava($paramPid)
    {
        if (!$paramPid) {
            return [];
        }
        $isArray = false;
        if (is_array($paramPid)) {
            $arrPid  = $paramPid;
            $isArray = true;
        } else {
            $arrPid = [$paramPid];
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $javaData   = $productApi->queryTicketInfoByProductIds($arrPid);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $productField = 'apply_limit,salerid,p_status,apply_did';
        $landField    = 'p_type,order_flag,venus_id';
        $ticketField  = 'tprice,pay,delaydays,ddays,buy_limit_low,order_limit,buy_limit_up,status,refund_audit,order_start,
        batch_day_check,delaytime,order_end,batch_check,getaddr,discount_scale,overdue_refund,pre_sale';
        $landFField   = 'dhour,series_model,mdays,tourist_info,v_ID_support,zone_id,v_time_limit';
        $asField      = [
            'uuProductDTO'  => [
                'id' => 'pid',
            ],
            'uuLandDTO'     => [
                'id'    => 'lid',
                'title' => 'ltitle',
            ],
            'uuJqTicketDTO' => [
                'id'    => 'tid',
                'title' => 'ttitle',
            ],
        ];
        $result       = $handleData = $this->_handleField($javaData['data'], $productField, $landField, $asField,
            $ticketField, $landFField);
        if ($isArray) {
            return $result ? $result : [];
        } else {
            return $result[0] ? $result[0] : [];
        }
    }

    public function getAnnualProductList($applyId, $page = 1, $size = 10)
    {
        $res = ['list' => [], 'total' => 0];
        if (!$applyId) {
            return $res;
        }
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $javaData   = $productApi->getProductInfoListByTypeAndApplyId($applyId, 0, 'I', $page, $size);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return $res;
        }
        $list = $this->_handleField($javaData['data']['list'], 'id,p_name');
        $res['list'] = $list;
        $res['total'] = $javaData['data']['total'];
        return $res;
    }

    private function _handleField($list, $productField = '', $landField = '', $asField = [], $ticketField = '', $landFField = '')
    {
        $data = [];
        if (!empty($list)) {
            if ($productField) {
                $productFieldArr = $this->_trimField(explode(',', $productField));
            }
            if ($landField) {
                $landFieldArr = $this->_trimField(explode(',', $landField));
            }
            if ($ticketField) {
                $ticketFieldArr = $this->_trimField(explode(',', $ticketField));
            }
            if ($landFField) {
                $landFFieldArr = $this->_trimField(explode(',', $landFField));
            }
            if (empty($productFieldArr) && empty($ticketFieldArr) && empty($landFFieldArr) && empty($landFieldArr) && empty($asField)) {
                foreach ($list as $key => $value) {
                    $mergerData = [];
                    foreach ($this->tableName as $k => $v) {
                        if (isset($value[$v])) {
                            $mergerData = array_merge($mergerData, $value[$v]);
                        }
                    }
                    $data[$key] = $mergerData;
                }
            } else {
                foreach ($list as $key => $value) {
                    if ($productFieldArr) {
                        if (isset($value['uuProductDTO'])) {
                            foreach ($value['uuProductDTO'] as $k1 => $v1) {
                                if (in_array($k1, $productFieldArr)) {
                                    $data[$key][$k1] = $v1;
                                }
                                if (isset($asField['uuProductDTO'])) {
                                    if (isset($asField['uuProductDTO'][$k1])) {
                                        $data[$key][$asField['uuProductDTO'][$k1]] = $v1;
                                    }
                                }
                            }
                        }
                    }
                    if ($ticketFieldArr) {
                        if (isset($value['uuJqTicketDTO'])) {
                            $ticketDtoName = 'uuJqTicketDTO';
                            foreach ($value[$ticketDtoName] as $k3 => $v3) {
                                if (in_array($k3, $ticketFieldArr)) {
                                    $data[$key][$k3] = $v3;
                                }
                                if (isset($asField[$ticketDtoName])) {
                                    if (isset($asField[$ticketDtoName][$k3])) {
                                        $data[$key][$asField[$ticketDtoName][$k3]] = $v3;
                                    }
                                }
                            }
                        }
                    }
                    if ($landFFieldArr) {
                        if (isset($value['uuLandFDTO'])) {
                            $landFDtoName = 'uuLandFDTO';
                            foreach ($value[$landFDtoName] as $k4 => $v4) {
                                if (in_array($k4, $landFFieldArr)) {
                                    $data[$key][$k4] = $v4;
                                }
                                if (isset($asField[$landFDtoName])) {
                                    if (isset($asField[$landFDtoName][$k4])) {
                                        $data[$key][$asField[$landFDtoName][$k4]] = $v4;
                                    }
                                }
                            }
                        }
                    }
                    if ($landFieldArr) {
                        if (isset($value['uuLandDTO'])) {
                            foreach ($value['uuLandDTO'] as $k2 => $v2) {
                                if (in_array($k2, $landFieldArr)) {
                                    $data[$key][$k2] = $v2;
                                }
                                if (isset($asField['uuLandDTO'])) {
                                    if (isset($asField['uuLandDTO'][$k2])) {
                                        $data[$key][$asField['uuLandDTO'][$k2]] = $v2;
                                    }
                                }
                            }
                        }
                    }
                    $data[$key]['confs'] = $value['confs'] ?? [];
                }
            }
        }

        return $data;
    }

    private function _trimField($data)
    {
        foreach ($data as $key => $value) {
            $data[$key] = trim($value);
        }

        return $data;
    }
}