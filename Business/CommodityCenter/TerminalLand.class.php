<?php

namespace Business\CommodityCenter;

use Business\JavaApi\CommodityCenter\Land as landBiz;
use Business\JavaApi\CommodityCenter\TerminalLand as terminalLandBiz;

class TerminalLand
{
    private $_javaApi     = null;
    private $_terminalApi = null;
    private $tableName    = [
        'uuLandDTO',
        'uuProductDTO',  //合并后最后一个数组单做主表字段
        'uuJqTicketDTO',
    ];

    public function __construct()
    {
        $this->_javaApi     = new landBiz();
        $this->_terminalApi = new terminalLandBiz();
    }

    /**
     * 根据供应商id获取出自供应产品数量
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $status  门票状态
     *
     * @return int
     */
    public function countLandByApplyDidAndStatus($applyDid, $status = 1)
    {
        if (empty($applyDid)) {
            return 0;
        }

        $result = $this->_javaApi->countLandByApplyDidAndStatus($applyDid, $status);

        if ($result['code'] != 200 || empty($result['data'])) {
            return 0;
        } else {
            return $result['data'];
        }
    }

    /**
     * 根据供应商id获取出自供应产品数量
     * <AUTHOR> Li
     * @date 2020-01-09
     *
     * @param  int  $applyDid  供应商id
     * @param  array  $uuidArr  uuid模糊搜索
     *
     * @return int
     */
    public function queryLandIdListByUuid($applyDid, $uuidArr)
    {
        if (empty($applyDid) || empty($uuidArr)) {
            return [];
        }

        $uuidsArr = [];
        foreach ($uuidArr as $uuid) {
            $uuidsArr[] = (string)$uuid;
        }

        $result = $this->_javaApi->queryLandIdListByUuid($applyDid, $uuidsArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        } else {
            $lidArrMap = [];
            foreach ($result['data'] as $landMap) {
                foreach ($landMap as $uuid => $landid) {
                    $lidArrMap[$uuid] = $landid;
                }
            }

            return $lidArrMap;
        }
    }

    /**
     * 获取land表（单表）详情根据票id
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  int  $tid  票id
     *
     * @return array
     */
    public function getLandInfoByTidToJava($tid)
    {
        if (!$tid) {
            return [];
        }
        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $ticketArr     = [$tid];
        $condition     = [
            'landFlag' => 1,
        ];
        $javaData      = $ticketJavaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($ticketArr, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }

        return $javaData['data'][0]['uuLandDTO'] ? $javaData['data'][0]['uuLandDTO'] : [];
    }

    /**
     * 获取land表和ticket详情根据票id数组
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrTid  票id
     * @param  string  $landField  land表字段
     * @param  string  $ticketField  ticket表字段
     * @param  array  $asField  别名字段
     *
     * @return array
     */
    public function getLandInfoByArrTidToJava($arrTid, $landField = '', $ticketField = '', $asField = [], $isGetOne = false)
    {
        if (empty($arrTid)) {
            return [];
        }
        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $condition     = [
            'landFlag' => 1,
        ];
        $javaData      = $ticketJavaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($arrTid, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $landList = $this->_handleField($javaData['data'], $ticketField, '', $landField, $asField);
        if ($isGetOne) {
            return $landList[0] ? $landList[0] : [];
        } else {
            return $landList ? $landList : [];
        }
    }

    /**
     * 获取land表是否通知和传真字段
     * <AUTHOR>
     * @date 2020-02-09
     *
     * @param  array  $arrTid  票id
     *
     * @return array
     */
    public function getSellerNotifyInfoByArrTid($arrTid)
    {
        if (empty($arrTid)) {
            return [];
        }
        $ticketJavaApi = new \Business\JavaApi\CommodityCenter\Ticket();
        $condition     = [
            'landFlag'             => 1,
            'cancelNotifySupplier' => 1,
            'faxNotNull'           => 1,
        ];
        $javaData      = $ticketJavaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable($arrTid, null,
            null, null, $condition);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return [];
        }
        $landList = $this->_handleField($javaData['data'], '', '', 'fax,apply_did');

        return $landList ? $landList : [];
    }

    public function getAnnualLandInfoListBySid($sid, $page = 1, $size = 10)
    {
        $res = ['list' => [], 'total' => 0];
        if (!$sid) {
            return $res;
        }
        $landApi  = new \Business\JavaApi\CommodityCenter\Land();
        $javaData = $landApi->getLandInfoList($sid, null, 1, 'I', '', $page, $size);
        if ($javaData['code'] != 200 || !$javaData['data']) {
            return $res;
        }
        $res['list']  = $javaData['data']['list'] ? $javaData['data']['list'] : [];
        $res['total'] = $javaData['data']['total'] ? $javaData['data']['total'] : 0;

        return $res;
    }

    private function _handleField($list, $ticketField = '', $productField = '', $landField = '', $asField = [])
    {
        $data = [];
        if (!empty($list)) {
            if ($productField) {
                $productFieldArr = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($productField)));
            }
            if ($landField) {
                $landFieldArr = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($landField, [' ' => ''])));
            }
            if ($ticketField) {
                $ticketFieldArr = explode(',', str_replace(["\n\r", "\r", "\n"], '', strtr($ticketField, [' ' => ''])));
            }
            if (empty($productFieldArr) && empty($landFieldArr) && empty($asField) && empty($ticketFieldArr)) {
                foreach ($list as $key => $value) {
                    $mergerData = [];
                    foreach ($this->tableName as $k => $v) {
                        if (isset($value[$v])) {
                            $mergerData = array_merge($mergerData, $value[$v]);
                        }
                    }
                    $data[$key] = $mergerData;
                }
            } else {
                foreach ($list as $key => $value) {
                    if ($productFieldArr) {
                        foreach ($value['uuProductDTO'] as $k1 => $v1) {
                            if (in_array($k1, $productFieldArr)) {
                                $data[$key][$k1] = $v1;
                            }
                            if (isset($asField['uuProductDTO'])) {
                                if (isset($asField['uuProductDTO'][$k1])) {
                                    $data[$key][$asField['uuProductDTO'][$k1]] = $v1;
                                }
                            }
                        }
                    }
                    if ($landFieldArr) {
                        foreach ($value['uuLandDTO'] as $k2 => $v2) {
                            if (in_array($k2, $landFieldArr)) {
                                $data[$key][$k2] = $v2;
                            }
                            if (isset($asField['uuLandDTO'])) {
                                if (isset($asField['uuLandDTO'][$k2])) {
                                    $data[$key][$asField['uuLandDTO'][$k2]] = $v2;
                                }
                            }
                        }
                    }
                    if ($ticketFieldArr) {
                        foreach ($value['uuJqTicketDTO'] as $k3 => $v3) {
                            if (in_array($k3, $ticketFieldArr)) {
                                $data[$key][$k3] = $v3;
                            }
                            if (isset($asField['uuJqTicketDTO'])) {
                                if (isset($asField['uuJqTicketDTO'][$k3])) {
                                    $data[$key][$asField['uuJqTicketDTO'][$k3]] = $v3;
                                }
                            }
                        }
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 共享终端(合并终端)
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $salerid  主景区商家id
     * @param  array  $lidArr  被合并景区id集合
     *
     * @return int
     */
    public function mergeTerminal($salerid, $lidArr)
    {
        if (!$salerid || !$lidArr) {
            return false;
        }

        $result = $this->_terminalApi->mergeTerminal($salerid, $lidArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return false;
        }

        return true;
    }

    /**
     * 根据终端号查询景区信息
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  array  $terminalArr  终端号数组
     * @param  string  $title  景区名称(like)
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  array  $statusArr  景区状态
     * @param  array  $verifyStatusArr  审核状态 0未审核1审核通过2关闭3删除
     * @param  array  $pTypeArr  产品类型(=)
     *
     * @return int
     */
    public function queryLandMultiQueryByTerminal($terminalArr, $title = '', $unEqualPType = '', $statusArr = [], $verifyStatusArr = [], $pTypeArr = [])
    {
        if (!$terminalArr) {
            return [];
        }

        $result = $this->_terminalApi->queryLandMultiQueryByTerminal($terminalArr, $title, $unEqualPType, $statusArr, $verifyStatusArr, $pTypeArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 切换景区的8位凭证码和6位凭证码
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $landId  终端号数组
     * @param  int  $terminalType  终端号数组
     *
     * @return bool
     */
    public function switchLandTerminalType($landId, $terminalType)
    {
        if (!$landId || !$terminalType) {
            return false;
        }

        $result = $this->_terminalApi->switchLandTerminalType($landId, $terminalType);

        if ($result['code'] != 200 || empty($result['data'])) {
            return false;
        }

        return true;
    }

    /**
     * 根据终端号查询景区信息
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $sid  供应商id
     * @param  int  $lStatus  景区状态
     * @param  int  $orderFlag  产品订单类型标识:0=通用,1=团队订单
     * @param  array  $saleridArr  商家id数组
     * @param  array  $notInPStatusArr  产品状态(not in)
     *
     * @return int
     */
    public function terminalQuerySaleLandList($sid = null, $lStatus = null, $orderFlag = null, $saleridArr = [], $notInPStatusArr = [])
    {
        $result = $this->_terminalApi->terminalQuerySaleLandList($sid, $lStatus, $orderFlag, $saleridArr, $notInPStatusArr);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 通过景区salerid获取已经合并的景区
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $salerid  商家id
     * @param  array  $lStatusArr  景区状态
     *
     * @return int
     */
    public function queryMergedLandBySalerid($salerid, $lStatusArr = [])
    {
        if (!$salerid) {
            return [];
        }

        $result = $this->_terminalApi->queryMergedLandBySalerid($salerid, $lStatusArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 通过景区 salerid + (title) 获取还没有合并的景区
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $salerid  商家id
     * @param  int  $page  页码 默认值 1
     * @param  int  $size  每页条数 默认值 10
     * @param  string  $title  景区名称
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  array  $landIdArr  景区状态
     * @param  array  $lStatusArr  景区状态
     *
     * @return int
     */
    public function queryUnMergedLandBySalerid($salerid, $page = 1, $size = 10, $title = '', $unEqualPType = '', $landIdArr = [], $lStatusArr = [])
    {
        if (!$salerid) {
            return [];
        }

        $result = $this->_terminalApi->queryUnMergedLandBySalerid($salerid, $page, $size, $title, $unEqualPType, $landIdArr, $lStatusArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 通过景区 salerid + (title) 获取还没有合并的景区
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $salerid  商家id
     * @param  int  $page  页码 默认值 1
     * @param  int  $size  每页条数 默认值 10
     * @param  string  $title  景区名称
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  array  $landIdArr  景区状态
     * @param  array  $lStatusArr  景区状态
     *
     * @return int
     */
    public function queryTerminalShareList($nowterminal = null, $privid = null, $presalerid = null, $dstatus = null, $nowsalerid = null, $orderByClause = null, $page = 0, $size = 0)
    {
        $result = $this->_terminalApi->queryTerminalShareList($nowterminal, $privid, $presalerid, $dstatus, $nowsalerid, $orderByClause, $page, $size);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 查询分终端列表
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $sid  供应商id
     * @param  string  $sid  景区名称（like）
     * @param  string  $unEqualPType  产品类型(!=)
     * @param  array  $landStatusArr  景区状态
     * @return int
     */
    public function queryLandByApplyDidOfVerify($sid, $title = '', $unEqualPType = null, $landStatusArr = null)
    {
        if (!$sid) {
            return [];
        }
        $result = $this->_terminalApi->queryLandByApplyDidOfVerify($sid, $title, $unEqualPType, $landStatusArr);

        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 查询分终端列表
     * <AUTHOR> Li
     * @date  2020-09-01
     *
     * @param  int  $salerid  商家id
     *
     * @return int
     */
    public function queryTerminalShareBySalerid($salerid)
    {
        if (!$salerid) {
            return [];
        }
        $result = $this->_terminalApi->queryTerminalShareBySalerid($salerid);
        if ($result['code'] != 200 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }
}