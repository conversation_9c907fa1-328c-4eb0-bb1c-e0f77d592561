<?php
/**
 * 医护通产品相关业务
 * <AUTHOR>
 */

namespace Business\FreeTicket4DoctorNurse;

use Business\JavaApi\ProductApi;
use \Exception;
use Business\Base;
use Library\Traits\CacheTrait;
use Business\Member\Member;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Product\PackageTicket;
use Business\JavaApi\Ticket\TicketExtendAttr;
use Business\JavaApi\Product\Land as NewLandApi;
use Business\JavaApi\Product\Ticket as NewTicketApi;
use Business\JavaApi\Context\Product\ProductInfoContext;
use Business\JavaApi\Context\Product\TicketInfoContext;
use Business\JavaApi\Context\Product\LandFInfoContext;
use Business\JavaApi\Context\Product\CreateOrUpdateTicketContext;
use Model\DoctorNurse\DNModel;

class Product extends Base
{
    use CacheTrait;

    private $_memberBiz;
    private $_packageTicketBiz;
    private $_ticketExtConfBiz;
    private $_dnModel;

    //日志prefix
    private $_logPath = 'FreeDn/';

    private $_dnConfigTicketLandMapKey = 'dbc:t:l:map:%u';

    /**
     * @return DNModel
     */
    private function _getDNModel(): DNModel
    {
        if (!$this->_dnModel) {
            $this->_dnModel = new DNModel();
        }

        return $this->_dnModel;
    }

    /**
     * @return TicketExtendAttr
     */
    private function _getTicketExtConfBiz(): TicketExtendAttr
    {
        if (!$this->_ticketExtConfBiz) {
            $this->_ticketExtConfBiz = new TicketExtendAttr();
        }

        return $this->_ticketExtConfBiz;
    }

    /**
     * @return PackageTicket
     */
    private function _getPackageTicketBiz(): PackageTicket
    {
        if (!$this->_packageTicketBiz) {
            $this->_packageTicketBiz = new PackageTicket();
        }

        return $this->_packageTicketBiz;
    }

    /**
     * @return Member
     */
    private function _getMemberBiz(): Member
    {
        if (!$this->_memberBiz) {
            $this->_memberBiz = new Member();
        }

        return $this->_memberBiz;
    }

    /**
     * 获取用户自供应产品列表
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $page
     * @param  int  $pageSize
     *
     * @return array
     */
    public function getSelfProductList(int $applyId, int $page = 1, int $pageSize = 10): array
    {
        $code = 200;
        $msg  = '';
        $data = [];

        $productBiz   = new ProductApi();
        $newTicketApi = new NewTicketApi();
        $dnModel      = $this->_getDNModel();

        try {
            if (empty($applyId)) {
                throw new Exception('供应商Id为空', 400);
            }

            if (empty($page) || empty($pageSize)) {
                throw new Exception('分页数据为空', 400);
            }

            // 获取供应商的自供应列表
            $list = $productBiz->getSelfProductSaleList([
                'account_id' => $applyId,
                'page_num'   => $page,
                'page_size'  => $pageSize,
                'type'       => 'A',
            ]);

            if ($list['code'] != 200) {
                throw new Exception('列表数据为空', 400);
            }

            $dnConfigList = $dnModel->getDnConfigByApplyId($applyId, 1, 'landid, ticket_id');
            $landIdArr    = array_column($dnConfigList, 'landid');
            //$ticketLandMap = [];
            //foreach ($dnConfigList as $item) {
            //    $ticketLandMap[$item['landid']] = $item['ticket_id'];
            //}
            $openCount = count($dnConfigList);

            //$ticketIdArr = array_column($dnConfigList, 'ticket_id');
            //// 获取票信息判断是否上下架
            //$ticketsInfoRes = $newTicketApi->queryTicketInfoByIds($ticketIdArr);
            //if ($ticketsInfoRes['code'] != 200) {
            //    throw new Exception('获取票列表数据失败', 400);
            //}
            //
            //// 遍历生成 ticket_id => status map 同时记录开通总数
            //$openCount         = 0;
            //$ticketsInfoData   = $ticketsInfoRes['data'];
            //$ticketsStatusInfo = [];
            //foreach ($ticketsInfoData as $item) {
            //    $ticketsStatusInfo[$item['uuJqTicketDTO']['id']] = $item['uuJqTicketDTO']['status'];
            //    // 计算总数
            //    $openCount += ($item['uuJqTicketDTO']['status'] == 1) ? 1 : 0;
            //}

            foreach ($list['data']['products'] as $item) {
                $tmpArr = [
                    'id'      => $item['landId'],
                    'name'    => $item['name'],
                    'is_open' => in_array($item['landId'], $landIdArr),
                    //'is_open' => isset($ticketLandMap[$item['landId']]) ? ($ticketsStatusInfo[$ticketLandMap[$item['landId']]] == 1 ? 1 : 0) : 0,
                ];

                $data['list'][] = $tmpArr;
            }

            $data['total_count'] = $list['data']['totalCount'];
            $data['open_count']  = $openCount;

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 下架医护产品根据景点id
     * <AUTHOR>
     *
     * @param  int  $operator
     * @param  int  $applyId
     * @param  string  $landIds
     *
     * @return array
     */
    public function takeOffFreeTicketByLandIdArr(int $operator, int $applyId, string $landIds): array
    {
        $code = 200;
        $msg  = '';
        $data = [];

        $dnModel = $this->_getDNModel();

        try {
            if (empty($operator)) {
                throw new Exception('操作人为空', 400);
            }

            if (empty($applyId)) {
                throw new Exception('供应商Id为空', 400);
            }

            if (empty($landIds)) {
                throw new Exception('景区为空', 400);
            }

            $landIdArr = explode(',', $landIds);
            if (empty($landIdArr)) {
                throw new Exception('景区为空', 400);
            }

            // 获取配置表
            $dnConfigList = $dnModel->getDnConfigByApplyIdAndLandIdArr($applyId, $landIdArr, 'id, ticket_id');
            foreach ($dnConfigList as $item) {
                $this->_takeOffTicket($operator, $applyId, $item['ticket_id']);
            }

            // 关闭供应商下指定门票开通状态
            $updateRes = $dnModel->updateDnConfigStatusByApplyIdAndLandIdArr($applyId, $landIdArr, 2);
            if (!$updateRes) {
                pft_log($this->_logPath . 'update/fail',
                    '更新供应商下开通景区为关闭状态失败, applyId:' . $applyId . ',landIdArr:' . json_encode($landIdArr));
            }

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 下架供应商所有医护产品
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $operator
     *
     * @return array
     */
    public function takeOffFreeTicketByApplyId(int $operator, int $applyId): array
    {
        $code = 200;
        $msg  = '';
        $data = [];

        $dnModel = $this->_getDNModel();

        try {
            if (empty($operator)) {
                throw new Exception('操作人为空', 400);
            }

            if (empty($applyId)) {
                throw new Exception('供应商Id为空', 400);
            }

            // 找到该供应商所有的景区开通记录
            $dnConfigList = $dnModel->getDnConfigByApplyId($applyId, 0, 'id, ticket_id');
            if (empty($dnConfigList)) {
                throw new Exception('该供应商未开通医护门票', 400);
            }

            foreach ($dnConfigList as $item) {
                $this->_takeOffTicket($operator, $applyId, $item['ticket_id']);
            }

            // 将开通记录修改为关闭
            $updateRes = $dnModel->updateDnConfigStatusByApply($applyId, 2);
            if (!$updateRes) {
                pft_log($this->_logPath . 'update/fail', '更新供应商下开通景区为关闭状态失败, applyId:' . $applyId);
            }

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 下架产品 封装
     * <AUTHOR>
     *
     * @param  int  $operator
     * @param  int  $applyId
     * @param  int  $ticketId
     *
     * @return bool
     */
    private function _takeOffTicket(int $operator, int $applyId, int $ticketId): bool
    {
        $versionUuid = uniqid();

        $packageTicket = $this->_getPackageTicketBiz();
        $ticketExtConf = $this->_getTicketExtConfBiz();

        // 执行下架操作
        $takeOffRes = $packageTicket->deListPackTicket($ticketId, $operator, $versionUuid);
        if ($takeOffRes === false) {
            pft_log($this->_logPath . 'config/take_off', "门票:{$ticketId}下架失败, 操作员{$operator}");

            return false;
        }

        // 保存扩展属性
        $extData    = [['ticketId' => $ticketId, 'key' => 'ticket_shelf_rule', 'val' => 1]];
        $extSaveRes = $ticketExtConf->save($applyId, $operator, $extData, $versionUuid);
        if ($extSaveRes['code'] != 200) {
            pft_log($this->_logPath . 'config/take_off', "门票:{$ticketId}保存扩展属性失败, 操作员{$operator}");

            return false;
        }

        return true;
    }

    /**
     * 创建或更新免费门票,  如果执行更新门票
     * <AUTHOR>
     *
     * @param  int  $operator
     * @param  int  $applyId
     * @param  array  $lids
     * @param  string  $landTitle
     * @param  string  $passWay  通关方式：0动态码，1身份证，2人脸，多种方式逗号分隔
     * @param  int  $appointment  是否需要预约，0不需要，1需要
     *
     * @return array
     */
    public function createOrUpdateFreeTicket(int $operator, int $applyId, array $lids, string $landTitle, string $passWay = '0', int $appointment = 0): array
    {
        $code = 200;
        $msg  = '';

        try {
            if (empty($applyId)) {
                throw new Exception('供应商id为空', 400);
            }

            if (empty($lids)) {
                throw new Exception('请选择景区', 400);
            }

            if (time() > strtotime('2020-12-31')) {
                throw new Exception('无效操作', 400);
            }

            $memberBiz  = $this->_getMemberBiz();
            $memberInfo = $memberBiz->getInfo($applyId);
            if (empty($memberInfo)) {
                throw new Exception('供应商账号不存在', 400);
            }

            if (!in_array($memberInfo['dtype'], [0, 2, 6, 9, 18])) {
                throw new Exception('该类型账号不允许编辑票类', 400);
            }

            $result        = [];
            $newTicketBiz  = new NewTicketApi();
            $ticketApi     = new TicketApi();
            $landApi       = new NewLandApi();
            $dnModel       = new DNModel();
            $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
            $ticketApi->setOperator($applyId, $applyId);

            // 判断是否已经开通
            $dnConfigInfoArr = $dnModel->getCooperatorByLandIdArr($lids, 'id, landid, ticket_id');
            $dnConfigInfoRes = [];
            foreach ($dnConfigInfoArr as $item) {
                $dnConfigInfoRes[$item['landid']] = $item;
            }

            foreach ($lids as $lid) {

                $tmpResult = [
                    'code' => 200,
                    'msg'  => 'success',
                    'data' => [
                        'ticket' => ['code' => 200, 'msg' => '门票操作成功'],
                        'ext'    => ['code' => 200, 'msg' => '门票扩展属性保存成功'],
                        'price'  => ['code' => 200, 'msg' => '日历价格保存成功'],
                    ],
                ];

                $versionUuid = uniqid();

                if (isset($dnConfigInfoRes[$lid])) {
                    // 更新门票
                    // 获取门票信息
                    $ticketInfoRes = $newTicketBiz->queryTicketInfoById($dnConfigInfoRes[$lid]['ticket_id']);
                    if ($ticketInfoRes['code'] != 200) {
                        $tmpResult['code'] = 400;
                        $tmpResult['msg']  = '门票:' . $dnConfigInfoRes[$lid]['ticket_id'] . ' 获取门票信息失败';
                        $tmpResult['data'] = [];
                        $result[]          = $tmpResult;
                        continue;
                    }
                    $ticketInfoData = $ticketInfoRes['data'];

                    $createTicketContext = $this->_generateCreateTicketContext($applyId, $lid, $versionUuid,
                        $dnConfigInfoRes[$lid]['ticket_id'], $ticketInfoData['uuJqTicketDTO']['pid']);
                    $updateTicketRes     = $newTicketBiz->updateTicket($createTicketContext);
                    if ($updateTicketRes['code'] != 200) {
                        $tmpResult['code'] = 400;
                        $tmpResult['msg']  = '景区:' . $lid . ' 更新门票失败';
                        $tmpResult['data'] = [];
                        $result[]          = $tmpResult;
                        continue;
                    }

                    $productId = $ticketInfoData['uuJqTicketDTO']['pid'];
                    $ticketId  = $dnConfigInfoRes[$lid]['ticket_id'];
                    // 更新景区开通状态
                    $dnConfigStatusUpdateRes = $dnModel->updateDnConfigStatusByApplyIdAndLandIdArr($applyId, [$lid], 1);
                    if (!$dnConfigStatusUpdateRes) {
                        pft_log($this->_logPath . 'update/fail',
                            '更新供应商下开通景区为关闭状态失败, applyId:' . $applyId . ',landIdArr:' . json_encode([$lid]));
                    }

                } else {
                    // 获取景区信息
                    $landInfoRes = $landApi->queryLandById($lid);
                    if ($landInfoRes['code'] != 200) {
                        throw new Exception('获取景区信息失败', 400);
                    }
                    $area = $landInfoRes['data']['area'];

                    // 创建门票
                    $createTicketContext = $this->_generateCreateTicketContext($applyId, $lid, $versionUuid);
                    $createTicketRes     = $newTicketBiz->createTicket($createTicketContext);
                    if ($createTicketRes['code'] != 200) {
                        $tmpResult['code'] = 400;
                        $tmpResult['msg']  = '景区:' . $lid . ' 创建门票失败';
                        $tmpResult['data'] = [];
                        $result[]          = $tmpResult;
                        continue;
                    }

                    $productId = $createTicketRes['data']['productId'];
                    $ticketId  = $createTicketRes['data']['ticketId'];

                    // 记录景区开通记录
                    $res = $dnModel->addCooperator($applyId, $ticketId, $operator, $lid, $landTitle, $passWay,
                        $appointment,
                        date('Y-m-d'), '2020-12-31', $area);
                    if (!$res) {
                        // 记录失败日志
                        pft_log($this->_logPath . 'product/error', '记录景区开通记录失败, data:' . json_encode([
                                'applyId'   => $applyId,
                                'ticketId'  => $ticketId,
                                'operator'  => $operator,
                                'lid'       => $lid,
                                'landTitle' => '',
                                'starttime' => date('Y-m-d 00:00:00'),
                            ]));
                    }
                }

                // 生成票扩展属性
                $ticketExtArr    = $this->_generateTicketExt($applyId, $ticketId, $versionUuid);
                $ticketExtResult = $ticketExtConf->save($applyId, $applyId, $ticketExtArr, $versionUuid);
                if ($ticketExtResult['code'] != 200) {
                    $tmpResult['data']['ext']['code'] = 400;
                    $tmpResult['data']['ext']['msg']  = '门票扩展属性保存失败';
                }

                // 创建日历价格
                $priceList   = $this->_generatePrice();
                $priceResult = $ticketApi->setRangePrice($productId, $ticketId, $priceList);
                if ($priceResult['code'] != 200) {
                    $tmpResult['data']['price']['code'] = 400;
                    $tmpResult['data']['price']['msg']  = '日历价格保存失败';
                }

                $result[] = $tmpResult;
            }

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $result, $msg);
    }

    /**
     * 获取景点id对应的免费ticketId, 返回0代表不存在
     * <AUTHOR>
     *
     * @param  int  $landId
     *
     * @return int
     */
    public function getDnConfigTicketIdByLandId(int $landId): int
    {
        $cacheKey = $this->getCacheKey($this->_dnConfigTicketLandMapKey, [$landId]);
        $redisIns = $this->getRedisInstance();

        $data = $redisIns->get($cacheKey);

        if ($data === false) {
            $dnModel = new DNModel();
            $res     = $dnModel->getDnConfigByLandId($landId, 'landid, ticket_id');
            if (empty($res)) {
                // 不存在数据不记录，避免后续新增缓存问题
                return 0;
            } else {
                $redisIns->setex($cacheKey, 86400, $res['ticket_id']);
                $data = $res['ticket_id'];
            }
        }

        return $data;
    }

    /**
     * 获取景点id对应的免费ticketId, ,返回 [landid=>ticketid]数组
     * <AUTHOR>
     *
     * @param  array  $landIdArr
     *
     * @return array
     */
    public function getDbConfigTicketsIdByLandIdArr(array $landIdArr): array
    {
        $redisIns           = $this->getRedisInstance();
        $needQueryLandIdArr = [];
        $data               = [];

        // 缓存读取
        foreach ($landIdArr as $item) {
            $tmpCacheKey = $this->getCacheKey($item);
            $tmpData     = $redisIns->get($tmpCacheKey);
            if ($tmpData === false) {
                $needQueryLandIdArr[] = $item;
            } else {
                $data[$item] = $tmpData;
            }
        }

        // 批量查询
        if (!empty($needQueryLandIdArr)) {
            $dnModel = new DNModel();
            $res     = $dnModel->getDnConfigsByLandIdArr($landIdArr, 'landid, ticket_id');
            foreach ($res as $item) {
                $tmpCacheKey = $this->getCacheKey($item['landid']);
                $redisIns->setex($tmpCacheKey, 86400, $item['ticket_id']);
                $data[$item['landid']] = $item['ticket_id'];
            }
        }

        return $data;
    }

    /**
     * 生成日历价格数据
     * <AUTHOR>
     *
     * @return array
     */
    private function _generatePrice(): array
    {
        return [
            [
                'sdate'    => date('Y-m-d'),
                'edate'    => '2020-12-31',
                'ls'       => 0,
                'storage'  => -1,
                'js'       => 0,
                'weekdays' => '0,1,2,3,4,5,6',
                'm_price'  => 0,
                'w_price'  => 0,
            ],
        ];
    }

    /**
     * 生成票扩展属性
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $ticketId
     * @param  string  $versionUuid
     *
     * @return array
     */
    private function _generateTicketExt(int $applyId, int $ticketId, string $versionUuid): array
    {
        return [
            [
                'ticketId' => $ticketId,
                'key'      => 'face_open',
                'val'      => 0,
            ],
            [
                'ticketId' => $ticketId,
                'key'      => 'face_repeat_enter',
                'val'      => 0,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'face_enter_time',
                'val'      => 0,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'face_valid_time',
                'val'      => 0,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'after_draft_days',
                'val'      => 0,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'simple_ticket_title',
                'val'      => '',
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'simple_voice_title',
                'val'      => '',
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'code_show_type',
                'val'      => 1,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'order_mode',
                'val'      => 1,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'print_mode',
                'val'      => 4,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'check_mode',
                'val'      => 1,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'expire_before_notice_to_supplier',
                'val'      => 0,
            ],

            [
                'ticketId' => $ticketId,
                'key'      => 'ticket_shelf_rule',
                'val'      => 0,
            ],
            [
                'ticketId' => $ticketId,
                'key'      => 'preinstall_listing_date_rule',
                'val'      => '',
            ],
            [
                'ticketId' => $ticketId,
                'key'      => 'preinstall_delisting_date_rule',
                'val'      => '',
            ],
            [
                'ticketId' => $ticketId,
                'key'      => 'charge_off_rule',
                'val'      => 0,
            ],
            [
                'ticketId' => $ticketId,
                'key'      => 'entry_method',
                'val'      => 0,
            ],
            [
                'ticketId' => $ticketId,
                'key'      => 'is_show_ordernum',
                'val'      => 0,
            ],
        ];
    }

    /**
     * 生成票属性
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $lid
     * @param  string  $versionUuid
     * @param  int  $ticketId  如果存在则更新
     * @param  int  $pid
     *
     * @return
     */
    private function _generateCreateTicketContext(int $applyId, int $lid, string $versionUuid,
        int $ticketId = 0, int $pid = 0): CreateOrUpdateTicketContext
    {
        // 生成CreateOrUpdateTicketContext对象
        $ticketContext      = $this->_generateTicketContext($applyId, $lid, $ticketId, $pid);
        $landFContext       = $this->_generateLandFContext($applyId, $lid, $ticketId, $pid);
        $productInfoContext = $this->_generateProductInfoContext($applyId, $lid);

        $data = [
            'ticketInfo'        => $ticketContext,
            'landFInfo'         => $landFContext,
            'productInfo'       => $productInfoContext,
            'accountId'         => $applyId,
            'operaterId'        => $applyId,
            'smsPhone'          => '',
            'specialConfigInfo' => ['specialConfig' => ''],
            'versionUuid'       => $versionUuid,
        ];

        return new \Business\JavaApi\Context\Product\CreateOrUpdateTicketContext($data);
    }

    /**
     * 生成ProductInfoContext
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $lid
     *
     * @return ProductInfoContext
     */
    private function _generateProductInfoContext(int $applyId, int $lid): ProductInfoContext
    {
        return new ProductInfoContext([
            'pNumber'    => null,
            'pType'      => 'A',
            'applyLimit' => null,
            'storage'    => -1,
            'sLimitUp'   => null,
            'sLimitLow'  => null,
            'sAlert'     => null,
            'sale16u'    => null,
            'saleBelow'  => null,
            'pStatus'    => null,
            'dStatus'    => null,
            'verifyOp'   => null,
            'openTime'   => null,
            'endTime'    => null,
            'applyDid'   => null,
            'applyLcode' => null,
            'salerid'    => null,
            'contactId'  => null,
            'verifyTime' => null,
            'trashTime'  => null,
        ]);
    }

    /**
     * 生成landFInfoContext
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $lid
     * @param  int  $ticketId
     * @param  int  $pid
     *
     * @return LandFInfoContext
     */
    private function _generateLandFContext(int $applyId, int $lid, int $ticketId = 0, $pid = 0): LandFInfoContext
    {
        return new LandFInfoContext([
            'lid'                       => null,
            'tid'                       => $ticketId ?: -1,
            'pid'                       => $pid ?: 0,
            'pType'                     => 'A',
            'dhour'                     => '23:59',
            'confirmSms'                => null,
            'manualVerify'              => null,
            'mdays'                     => null,
            'mhour'                     => null,
            'gFund'                     => null,
            'startplace'                => null,
            'endplace'                  => null,
            'rdays'                     => null,
            'touristInfo'               => 2,
            'assStation'                => null,
            'seriesModel'               => null,
            'vMobileSupport'            => null,
            'vIdSupport'                => null,
            'vTimeLimit'                => '00:00|23:59',
            'entranceTimeLimit'         => null,
            'sendvoucher'               => null,
            'confirmWx'                 => 0,
            'zoneId'                    => null,
            'buyLimit'                  => 0,
            'buyLimitDate'              => 1,
            'buyLimitNum'               => 0,
            'ageLimitMin'               => '',
            'ageLimitMax'               => '',
            'orderSmsSupplier'          => 0,
            'orderSmsBuyer'             => 0,
            'cancelSmsBuyer'            => 0,
            'cancelSmsSupplier'         => 0,
            'riskWarning'               => '',
            'familyCardNum'             => null,
            'autoCheckedAt'             => 0,
            'smsSendSubticketValidTime' => null,
        ]);
    }

    /**
     * 生成ticketContext
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  int  $lid
     * @param  int  $ticketId
     * @param  int  $pid
     *
     * @return TicketInfoContext
     */
    private function _generateTicketContext(int $applyId, int $lid, int $ticketId = 0, int $pid = 0): TicketInfoContext
    {
        return new TicketInfoContext([
            'id'                    => $ticketId ?: null,
            'landid'                => $lid,
            'title'                 => '医护人员专用票',
            'tprice'                => 0,
            'reb'                   => 0,
            'rebp'                  => null,
            'discount'              => null,
            'delaydays'             => 0,
            'useEarlyDays'          => 0,
            'printTicketLimit'      => 0,
            'mobre'                 => null,
            'px'                    => null,
            'status'                => 1,
            'pay'                   => 1,
            'ifs'                   => null,
            'storage'               => -1,
            'storageOpen'           => null,
            'ddays'                 => 0,
            'getaddr'               => '',
            'smsLimit'              => null,
            'sLimitUp'              => null,
            'sLimitLow'             => null,
            'buyLimitUp'            => 0,
            'buyLimitLow'           => 1,
            'openTime'              => null,
            'endTime'               => null,
            'applyDid'              => null,
            'modifyOp'              => $applyId,
            'uuid'                  => null,
            'pid'                   => $pid ?: 0,
            'cancelAutoOnmin'       => 120,
            'delaytype'             => 2,
            'orderLimit'            => '',
            'orderEnd'              => '2020-12-31 23:59:59',
            'syncId'                => null,
            'syncStatus'            => null,
            'cancelCost'            => '',
            'reIntegral'            => null,
            'rebType'               => 0,
            'orderStart'            => date('Y-m-d 00:00:00'),
            'maxOrderDays'          => null,
            'mdetails'              => null,
            'mpath'                 => null,
            'sourcet'               => null,
            'overdueRefund'         => null,
            'batchCheck'            => 0,
            'batchDayCheck'         => 0,
            'batchDiffIdentities'   => null,
            'chkTerminalInfo'       => '',
            'refundAudit'           => 0,
            'delaytime'             => '0|0',
            'refundRule'            => 0,
            'refundEarlyTime'       => 0,
            'cancelNotifySupplier'  => 0,
            'revokeAudit'           => 1,
            'delayRefundTime'       => null,
            'numModify'             => 0,
            'expireAction'          => 1,
            'expireActionDays'      => 0,
            'expireActionFee'       => 0,
            'updateTime'            => null,
            'shop'                  => '6,8',
            'ifVerify'              => 0,
            'discountScale'         => 0,
            'modifyLimitTime'       => 0,
            'ticketChangingRange'   => 0,
            'ticketChangingAudit'   => 0,
            'ticketChangingWeekend' => 0,
            'maxExpirationDate'     => null,
            'notes'                 => '',
        ]);
    }

}