<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2020/2/25
 * Time: 14:12
 */

namespace Business\FreeTicket4DoctorNurse;

use Business\JsonRpcApi\MessageService\MessageService;
use Library\Business\WePay\WxPayApi;
use Library\Container;
use Library\ctid\RealNameIdentify;
use Library\Exception;
use Library\MessageNotify\PFTSMSInterface;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Library\Tools\IdCardSecurity;
use Library\Util\EnvUtil;
use Model\DoctorNurse\DNModel;

class Register
{
    private $govQueryApi = [
        'test' => 'http://47.99.180.193:8090/info', //部署在dev机器上
        'prod' => 'http://10.1.8.40:8090/info', //部署在云票务机器上
    ];

    /**
     * 调用新大陆实名制接口
     * @author: <PERSON><PERSON><PERSON><PERSON>
     * @date: 2020/2/25
     *
     * @param $name
     * @param $idCardNo
     * @param $imgBase64
     *
     * @return bool
     */
    private function realNameCheck($name, $idCardNo, $imgBase64)
    {
        return true;

        if (strtolower(ENV) != 'production') {
            return true;
        }
        // 实名制校验接口异常的话直接跳过
        try {
            $res = (new RealNameIdentify())->identify($name, $idCardNo, $imgBase64);
            if ($res['data']['authResultValid'] == true) {
                return true;
            }

            return false;
        } catch (Exception $e) {
            return true;
        }

    }

    /**
     * 医护人员校验
     * @author: Guangpeng Chen
     * @date: 2020/2/25
     *
     * @param $identity
     * @param $name
     * @param $province
     * @param $hospital
     *
     * @return array
     */
    private function identityCheck($identity, $name, $province, $hospital)
    {
        $identity = !$identity + 0;// 身份取反
        $params   = json_encode([
            'token'   => 'a6b167ecb9c5fa3b6327969681aa6f37',
            'id_type' => $identity,
            'name'    => $name,
            'prov'    => $province,
            'zy_unit' => $hospital,
        ], JSON_UNESCAPED_UNICODE);
        $env      = strtolower(ENV) == 'production' ? 'prod' : 'test';
        $url      = $this->govQueryApi[$env];
        $curl     = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL            => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING       => "",
            CURLOPT_MAXREDIRS      => 10,
            CURLOPT_TIMEOUT        => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST  => "POST",
            CURLOPT_POSTFIELDS     => $params,
            CURLOPT_HTTPHEADER     => array(
                "Content-Type: application/json",
            ),
        ));
        $json = curl_exec($curl);
        pft_log("doctor", "req={$params},res={$json}");
        curl_close($curl);
        $obj = json_decode($json, true);
        if ($obj['code'] == 200) {
            return ['code' => 200, 'data' => $obj['data']];
        } else {
            return ['code' => $obj['code'], 'msg' => $obj['err']];
        }
    }

    private function imageUpload($base64Img)
    {
        $imgResult = Helpers::uploadImage2AliOss('orderface', $base64Img, 'base64');
        if ($imgResult['code'] != 200 || empty($imgResult['data']['src'])) {
            $result['code'] = 400;
            $result['msg']  = '人脸图片上传失败';

            return $result;
        }
        $imgUrl = $imgResult['data']['src'];

        return [
            'code' => 200,
            'data' => [
                ['url' => $imgUrl],
            ],
            'msg'  => 'success',
        ];
    }

    /**
     * @author: Guangpeng Chen
     * @date: 2020/3/3
     * @param string $rawData 解密前的数据
     * @param string $key     解密key
     *
     * @return string
     */
    public static function idcardDec($rawData, $key)
    {
        return (new IdCardSecurity($key))->decrypt($rawData);
    }

    /**
     * @author: Guangpeng Chen
     * @date: 2020/3/3
     * @param $idCard
     *
     * @return array [加密后的数据,key]
     */
    public function idcardEnc($idCard)
    {
        $idCardSecKey              = WxPayApi::getNonceStr(8);
        $idCardSecObj              = new IdCardSecurity($idCardSecKey);
        return [$idCardSecObj->encrypt($idCard), $idCardSecKey];
    }
    /**
     * 保存信息
     * @author: Guangpeng Chen
     * @date: 2020/2/25
     *
     * @param $name
     * @param $mobile
     * @param $openid
     * @param $idCard
     * @param $identity
     * @param $province
     * @param $hospital
     * @param  string  $base64Img  人脸照片
     * @param  string  $license  证书编号
     *
     * @return array
     */
    public function saveBaseInfo($name, $mobile, $openid, $idCard, $identity, $province, $hospital, $base64Img = '', $license='')
    {
        $saveData = [
            'name'   => $name,
            'mobile' => $mobile,
            'openid' => $openid,
            'identity'   => $identity,
            'province'   => $province,
            'city'       => '',
            'hospital'   => $hospital,
            'created_at' => time(),
            'updated_at' => time(),
            'license'    => $license,
        ];
        list($saveData['idcard'],  $saveData['idcard_seckey']) = $this->idcardEnc($idCard);

        if (!empty($base64Img)) {
            $uploadRes = $this->imageUpload($base64Img);
            if ($uploadRes['code'] == 200) {
                $saveData['faceurl'] = $uploadRes['data']['url'];
            }
        }
        $model  = new DNModel();
        $chkRes = $model->checkDoctorExist($openid, $mobile);
        if ($chkRes['is_confirm'] == 2) {
            return ['code' => 400, 'data' => [], 'msg' => '您已经注册过了'];
        }
        $step1 = $this->realNameCheck($name, $idCard, $base64Img);
        if (!$step1) {
            return ['code' => 400, 'data' => [], 'msg' => '实名制校验失败，请确认身份证与姓名是否匹配'];
        }
        $saveData['is_confirm'] = 1;
        $lastId = $model->saveData($saveData, $chkRes['id']??0);
        if ($lastId) {
            $jobId = Queue::push('backend', 'Backend_Job', [
                'action'   => 'checkDoctorIdentity',
                'lastid'   => $lastId,
                'name'     => $name,
                'prov'     => $province,
                'identity' => $identity,
                'hospital' => $hospital,
            ]);

            return ['code' => 200, 'data' => [], 'msg' => '提交成功，审核通过后会以短信通知您'];
        }

        return ['code' => 400, 'data' => [], 'msg' => '系统异常'];
    }

    public function asyncJob($lastId, $name, $province, $identity, $hospital)
    {
        //TODO:认证需要异步任务处理
        $result = $this->identityCheck($identity, $name, $province, $hospital);
        // 验证码错误返回4002
        if ($result['code'] == 4002) {
            $times = 0;
            do {
                $result = $this->identityCheck($identity, $name, $province, $hospital);
                $times  += 1;
                if ($result != 4002) {
                    break;
                }
            } while ($times < 3);
        }
        $model = new DNModel();
        if ($result['code'] == 200) {
            // 更新

            $info       = $model->getDnInfoById($lastId);
            if ($info['license']!='' && $info['license'] != $result['data']['license']) {
                $updateData = [
                    'is_confirm'  => 3,
                    'confirm_msg' => "证书编号不符，卫健委:{$result['data']['license']},保存:{$info['license']}",
                ];
                $smsMsg     = "{$info['name']}，您的医护人员免费入园信息审核未通过，原因：执业证书编码与国家卫健委的数据不符。";
            } else {
                $smsMsg     = "尊敬的白衣战士，{$info['name']}，您好！您的医护人员免费入园信息已校验通过。可登录小程序查看动态码以及预约游玩景区！";
                $updateData = [
                    'is_confirm' => 2,
                    'license'    => $result['data']['license'],
                    'hospital'   => $result['data']['hospital'],
                    'updated_at' => time(),
                ];
            }
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, PFTSMSInterface::ISDIY, $info['mobile'],
                [$smsMsg], 0, '', '医码游', '票付通', '', 0, true);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['医码游', __METHOD__, [$info['mobile'], $smsMsg], $res], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @deprecated 放量结束后删除 */
                $smsObj = SmsFactory::getFactory($info['mobile'], 'fzzwx');
                $res = $smsObj->customMsg(1, "票付通", $smsMsg, $info['mobile'], '', '', false, '医码游');
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['医码游.old', __METHOD__, [$info['mobile'], $smsMsg], $res], JSON_UNESCAPED_UNICODE));
                }
            }
        } else {
            $updateData = [
                'is_confirm'  => 3,
                'confirm_msg' => $result['msg'],
            ];
        }
        $model->updateDnInfoById($lastId, $updateData);

        // 发送通知
        return [];
    }

    public function sendNotice()
    {

    }
}