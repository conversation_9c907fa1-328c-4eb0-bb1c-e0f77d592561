<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: Administrator
 * Date: 2020/2/27
 * Time: 16:49
 * 长度：14位
 * 6位随机数标识(数字)
 * 4位时间戳：格式hhmm，用来判断是否过期，防止过多请求redis
 * 2位校验码(数字)：前12位数字+指定密钥,进行 crc32b 哈希加密,对100求余,得出2位数字
 */

namespace Business\FreeTicket4DoctorNurse;

use Library\Cache\Cache;
use Library\Cache\CacheRedis;

class PassCode
{
    const CODE_PREFIX = '96';
    const CACHE_KEY_PREFIX = 'dncode:';
    const EXPIRE_TIME = 60;
    const SECURITY_KEY = "pftdn*";

    private function codeHash($code)
    {
        return hexdec(hash('crc32b', $code . self::SECURITY_KEY)) % 100;
    }

    public function generateCode($dnInfoId)
    {
        $hi       = date('Hi');
        $code     = self::CODE_PREFIX . mt_rand(100000, 999999) . $hi;
        $code     .= str_pad(self::codeHash($code), 2, '0', STR_PAD_LEFT);
        $cacheKey = self::CACHE_KEY_PREFIX . $code;
        /**
         * @var $redis CacheRedis
         */
        $redis = Cache::getInstance('redis');
        $redis->set($cacheKey, $dnInfoId, '', 60);

        return $code;
    }

    /**
     * 根据code从redis里面获取id
     * @author: Guangpeng Chen
     * @date: 2020/2/27
     * @param $code
     *
     * @return int
     */
    public function getInfoIdByCode($code)
    {
        $cacheKey = self::CACHE_KEY_PREFIX . $code;
        /**
         * @var $redis CacheRedis
         */
        $redis = Cache::getInstance('redis');

        return $redis->get($cacheKey) + 0;
    }

    /**
     * 校验二维码是否过期
     * @author: Guangpeng Chen
     * @date: 2020/2/27
     *
     * @param $code
     *
     * @return bool
     */
    public function codeTimeCheck($code)
    {
        return substr($code, 8, 4) == date("Hi");
    }

    /**
     * 二维码校验位检验
     * @author: Guangpeng Chen
     * @date: 2020/2/27
     *
     * @param $code
     *
     * @return bool
     */
    public function codeFlagCheck($code)
    {
        return self::codeHash(substr($code, 0, 12)) == substr($code, -2);
    }
}