<?php
/**
 * 医护下单流程
 * <AUTHOR>
 */

namespace Business\FreeTicket4DoctorNurse;

use \Exception;
use Business\Base;
use Business\JavaApi\Product\Ticket as newTicketApi;
use Business\JavaApi\Product\Land as LandJavaBiz;
use Library\Traits\CacheTrait;
use Model\DoctorNurse\DNModel;
use Model\Product\Land;

class Order extends Base
{
    use CacheTrait;

    private $_landModel;
    private $_dnModel;
    private $_dnOrdersLogKey = 'dn:order:log:id:%u:lid%u';
    // 日志路径
    private $_orderLogFailPath = 'dn/order_log/fail';
    // 用户预约景区key,  set结构
    private $_dnOrderAppointmentKey = 'dn:order:apo:id:%u';

    private function getDNModel(): DNModel
    {
        if (empty($this->_dnModel)) {
            $this->_dnModel = new DNModel();
        }

        return $this->_dnModel;
    }

    /**
     * 获取landModel
     * <AUTHOR>
     * @date 2018-06-25
     */
    private function getLandModel(): Land
    {
        if (empty($this->landModel)) {
            $this->_landModel = new Land('slave');
        }

        return $this->_landModel;
    }

    /**
     * 提前预约
     * <AUTHOR>
     *
     * @param  string  $openid
     * @param  int  $lid
     *
     * @return array
     */
    public function orderAppointment(string $openid, int $lid): array
    {
        $code = 200;
        $msg  = '提前预约成功';
        $data = [];

        $dnModel       = $this->getDNModel();
        $biz           = new Product();
        $registerBiz   = new Register();
        $newTicketApi  = new newTicketApi();
        $redisInstance = $this->getRedisInstance();

        try {
            // 通过景区获取免费门票配置
            $ticketId = $biz->getDnConfigTicketIdByLandId($lid);

            if (!$ticketId) {
                throw new Exception('该景点未开通医护门票', 401);
            }

            $ticketInfoRes = $newTicketApi->queryTicketInfoById($ticketId);
            if ($ticketInfoRes['code'] != 200) {
                throw new Exception('未获取到票信息', 401);
            }
            $ticketInfo = $ticketInfoRes['data'];

            $dnInfo = $dnModel->getDnInfoByOpenId($openid);
            if (empty($dnInfo)) {
                throw new Exception('您还未绑定医护人员信息', 401);
            }

            if ($dnInfo['is_confirm'] != 2) {
                throw new Exception('您的医护人员信息还未审核通过', 401);
            }

            if (empty($dnInfo['idcard'])) {
                throw new Exception('您的医护人员信息身份证为空', 401);
            }

            // 同一景点用户一天只能预约一次
            $orderAppointmentKey = $this->getCacheKey($this->_dnOrderAppointmentKey, [$dnInfo['id']]);
            if ($redisInstance->sismember($orderAppointmentKey, $lid)) {
                throw new Exception('同一个景点一天只能提前预约一次', 401);
            }

            // 下单
            $orderResJson = self::getSoap()->QuickOrder($ticketId, '', 1, 11, $dnInfo['name'], 1, 0, false,
                $ticketInfo['uuJqTicketDTO']['applyDid'], false,
                $registerBiz->idcardDec($dnInfo['idcard'], $dnInfo['idcard_seckey']),
                false, $dnInfo['mobile']);
            $orderRes     = json_decode($orderResJson, true);
            if ($orderRes['code'] != 200) {
                pft_log($this->_orderLogFailPath, '下单失败,res:' . $orderResJson);
                throw new Exception('下单失败，请重刷二维码购买', 401);
            }

            $data = $orderRes['data'];
            // 用户预约成功记录
            if ($redisInstance->exists($orderAppointmentKey)) {
                $redisInstance->sAdd($orderAppointmentKey, $lid);
            } else {
                $redisInstance->sAdd($orderAppointmentKey, $lid);
                $redisInstance->expire($orderAppointmentKey, strtotime(date('Y-m-d 23:59:59')) - time());
            }

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 免费订单刷码即验证流程
     * <AUTHOR>
     *
     * @param  string  $authCode
     * @param  int  $applyDid
     * @param  int  $lid
     *
     * @return array
     */
    public function orderByCodeForWx(string $authCode, int $applyDid, int $lid): array
    {
        $code = 200;
        $msg  = '';
        $data = [];

        $pcBiz         = new PassCode();
        $biz           = new Product();
        $registerBiz   = new Register();
        $dnModel       = $this->getDNModel();
        $redisInstance = $this->getRedisInstance();

        try {
            // 通过景区获取免费门票配置
            $ticketId = $biz->getDnConfigTicketIdByLandId($lid);

            if (!$ticketId) {
                throw new Exception('未开通医护门票', 401);
            }

            $landId = $lid;

            if (!$pcBiz->codeTimeCheck($authCode)) {
                throw new Exception('动态码已过期，请刷新', 401);
            }

            if (!$pcBiz->codeFlagCheck($authCode)) {
                throw new Exception('动态码异常，请刷新', 401);
            }

            $dnId = $pcBiz->getInfoIdByCode($authCode);
            if (!$dnId) {
                throw new Exception('动态码异常，请刷新', 401);
            }

            $dnInfo = $dnModel->getDnInfoById($dnId, 'id, name, mobile, is_confirm, idcard, 
            identity, idcard_seckey, hospital');

            if (empty($dnInfo)) {
                throw new Exception('您还未绑定医护人员信息', 401);
            }

            if ($dnInfo['is_confirm'] != 2) {
                throw new Exception('您的医护人员信息还未审核通过', 401);
            }

            if (empty($dnInfo['idcard'])) {
                throw new Exception('您的医护人员信息身份证为空', 401);
            }

            // 码入园限制1小时一次
            $dnOrdersLogKey = $this->getCacheKey($this->_dnOrdersLogKey, [$dnId, $landId]);
            $dnOrdersLogRes = $redisInstance->get($dnOrdersLogKey);
            if ($dnOrdersLogRes) {
                throw new Exception('1小时内该景区只能游玩一次', 401);
            }

            $data['dn']           = $dnInfo;
            $data['dn']['idcard'] = str_pad(substr($registerBiz->idcardDec($dnInfo['idcard'], $dnInfo['idcard_seckey']),
                0, 5),
                '18', '*', STR_PAD_RIGHT);

            // 下单
            $orderResJson = self::getSoap()->QuickOrder($ticketId, $authCode, 1, 14, $dnInfo['name'], 1, 0, false,
                $applyDid, true, $registerBiz->idcardDec($dnInfo['idcard'], $dnInfo['idcard_seckey']));
            $orderRes     = json_decode($orderResJson, true);
            if ($orderRes['code'] != 200) {
                pft_log($this->_orderLogFailPath, '下单失败,res:' . $orderResJson);
                throw new Exception('下单失败，请重刷二维码购买', 401);
            }

            $data['order'] = $orderRes['data'];

            // 记录医护人员入园信息
            $logRes = $dnModel->addOrderLog($dnId, $landId, $orderRes['data']['ordernum']);
            if (!$logRes) {
                pft_log($this->_orderLogFailPath, '记录医护人员入园信息失败,data:' . json_encode([
                        'dnId'     => $dnId,
                        'landId'   => $landId,
                        'ordernum' => $orderRes['data']['ordernum'],
                    ]));
            }
            // 记录医护人员入园缓存
            $redisInstance->setex($dnOrdersLogKey, 3600, 1);

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 免费订单刷码即验证流程
     * <AUTHOR>
     *
     * @param  string  $authCode
     * @param  int  $applyDid
     * @param  int  $terminalId
     *
     * @return array
     */
    public function orderByCode(string $authCode, int $applyDid, int $terminalId): array
    {
        $code = 200;
        $msg  = '';
        $data = [];

        $pcBiz         = new PassCode();
        $biz           = new Product();
        $registerBiz   = new Register();
        $landModel     = $this->getLandModel();
        $dnModel       = $this->getDNModel();
        $redisInstance = $this->getRedisInstance();

        try {
            // 先判断是否存在合并终端号
            $nowTerminal = $landModel->getNowTerminal($terminalId);

            if ($nowTerminal) {
                // 合并终端批量获取景区, 通过合并终端获取所有的终端号
                $terminalIdArr = $landModel->getPreTerminal($nowTerminal);
                if (empty($terminalIdArr)){
                    throw new Exception('未找到景区', 401);
                }
                $terminalApi = new \Business\CommodityCenter\TerminalLand();
                $landInfoArr = $terminalApi->queryLandMultiQueryByTerminal($terminalIdArr);
                if (empty($landInfoArr)) {
                    throw new Exception('未找到景区', 401);
                }

                $ticketIdData = $biz->getDbConfigTicketsIdByLandIdArr(array_column($landInfoArr, 'id'));
                if (empty($ticketIdData)) {
                    throw new Exception('未开通医护门票', 401);
                }

                $landId = array_pop(array_column($landInfoArr, 'id'));

                $ticketId = array_pop($ticketIdData);

            } else {
                // 直接获取当前景区
                $terminalApi = new \Business\CommodityCenter\TerminalLand();
                $landInfoArr = $terminalApi->queryLandMultiQueryByTerminal([$terminalId]);
                $landInfo    = [];
                if ($landInfoArr) {
                    $landInfo = $landInfoArr[0];
                }
                if (empty($landInfo)) {
                    throw new Exception('未找到景区', 401);
                }

                // 通过景区获取免费门票配置
                $ticketId = $biz->getDnConfigTicketIdByLandId($landInfo['id']);

                if (!$ticketId) {
                    throw new Exception('未开通医护门票', 401);
                }

                $landId = $landInfo['id'];
            }

            if (!$pcBiz->codeTimeCheck($authCode)) {
                throw new Exception('动态码已过期，请刷新', 401);
            }

            if (!$pcBiz->codeFlagCheck($authCode)) {
                throw new Exception('动态码异常，请刷新', 401);
            }

            $dnId = $pcBiz->getInfoIdByCode($authCode);
            if (!$dnId) {
                throw new Exception('动态码异常，请刷新', 401);
            }

            $dnInfo = $dnModel->getDnInfoById($dnId, 'id, name, mobile, is_confirm, idcard, 
            identity, idcard_seckey, hospital');

            if (empty($dnInfo)) {
                throw new Exception('您还未绑定医护人员信息', 401);
            }

            if ($dnInfo['is_confirm'] != 2) {
                throw new Exception('您的医护人员信息还未审核通过', 401);
            }

            if (empty($dnInfo['idcard'])) {
                throw new Exception('您的医护人员信息身份证为空', 401);
            }

            // 码入园限制1小时一次
            $dnOrdersLogKey = $this->getCacheKey($this->_dnOrdersLogKey, [$dnId, $landId]);
            $dnOrdersLogRes = $redisInstance->get($dnOrdersLogKey);
            if ($dnOrdersLogRes) {
                throw new Exception('1小时内该景区只能游玩一次', 401);
            }

            $data['dn']           = $dnInfo;
            $data['dn']['idcard'] = str_pad(substr($registerBiz->idcardDec($dnInfo['idcard'], $dnInfo['idcard_seckey']),
                0, 5),
                '18', '*', STR_PAD_RIGHT);

            // 下单
            $orderResJson = self::getSoap()->QuickOrder($ticketId, $authCode, 1, 14, $dnInfo['name'], 1, 0, false,
                $applyDid, true, $registerBiz->idcardDec($dnInfo['idcard'], $dnInfo['idcard_seckey']));
            $orderRes     = json_decode($orderResJson, true);
            if ($orderRes['code'] != 200) {
                pft_log($this->_orderLogFailPath, '下单失败,res:' . $orderResJson);
                throw new Exception('下单失败，请重刷二维码购买', 401);
            }

            $data['order'] = $orderRes['data'];

            // 记录医护人员入园信息
            $logRes = $dnModel->addOrderLog($dnId, $landId, $orderRes['data']['ordernum']);
            if (!$logRes) {
                pft_log($this->_orderLogFailPath, '记录医护人员入园信息失败,data:' . json_encode([
                        'dnId'     => $dnId,
                        'landId'   => $landId,
                        'ordernum' => $orderRes['data']['ordernum'],
                    ]));
            }
            // 记录医护人员入园缓存
            $redisInstance->setex($dnOrdersLogKey, 86400, 1);

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 医护人员游玩列表添加预约状态
     * <AUTHOR>
     *
     * @param  array  $list
     * @param  string  $openid
     *
     * @return array
     */
    public function handleCoopsLandsAddAppointment(array $list, string $openid): array
    {
        if (empty($list) || empty($openid)) {
            return $list;
        }

        $dnModel = $this->getDNModel();
        $dnInfo  = $dnModel->getDnInfoByOpenId($openid);
        if (empty($dnInfo)) {
            return $list;
        }

        $redisInstance       = $this->getRedisInstance();
        $orderAppointmentKey = $this->getCacheKey($this->_dnOrderAppointmentKey, [$dnInfo['id']]);
        if (!$redisInstance->exists($orderAppointmentKey)) {
            return $list;
        }

        $appointmentList = $redisInstance->smembers($orderAppointmentKey);
        if (empty($appointmentList)) {
            return $list;
        }

        foreach ($list as &$item) {
            if (in_array($item['landid'], $appointmentList)) {
                $item['is_appointment'] = 1;
            } else {
                $item['is_appointment'] = 0;
            }
        }

        return $list;
    }

    /**
     * 订单统计
     * <AUTHOR>
     *
     * @param  int  $applyId
     * @param  string  $checkStartTime
     * @param  string  $checkEndTime
     * @param  int  $page
     * @param  int  $pageSize
     *
     * @return array
     */
    public function orderStatistics(int $applyId, string $checkStartTime, string $checkEndTime, int $page, int $pageSize): array
    {
        $code = 200;
        $msg  = '';
        $data = [];

        try {

            if (empty($applyId)) {
                throw new Exception('供应商为空', 400);
            }

            if (empty($checkEndTime) || empty($checkStartTime) || !strtotime($checkEndTime) || !strtotime($checkStartTime)) {
                throw new Exception('时间格式错误', 400);
            }

            if (empty($page) || empty($pageSize)) {
                throw new Exception('参数格式错误', 400);
            }

            // 获取订单信息
            $dnModel       = $this->getDNModel();
            $registerBiz   = new Register();
            $list          = $dnModel->orderStatisticsByApplyId($applyId, strtotime($checkStartTime),
                strtotime($checkEndTime), $page, $pageSize);
            $todayDnMember = 0;

            if (!empty($list['list'])) {
                // 获取景区数据
                $landBiz        = new LandJavaBiz();
                $landsInfoRes   = $landBiz->queryLandByIds(array_column($list['list'], 'lid'));
                $landsIdNameMap = [];
                if ($landsInfoRes['code'] != 200) {
                    throw new Exception('获取景区数据失败', 400);
                }

                foreach ($landsInfoRes['data'] as $item) {
                    $landsIdNameMap[$item['id']] = $item['title'];
                }

                foreach ($list['list'] as &$item) {
                    $item['land_name'] = isset($landsIdNameMap[$item['lid']]) ? $landsIdNameMap[$item['lid']] : '';
                    $item['idcard']    = str_pad(substr($registerBiz->idcardDec($item['idcard'],
                        $item['idcard_seckey']),
                        0, 5),
                        '18', '*', STR_PAD_RIGHT);
                    $item['mobile']    = str_pad(substr($item['mobile'], 0, 5), 11, '*', STR_PAD_RIGHT);

                    // 计算今日用户
                    if ($item['created_at'] > strtotime(date('Y-m-d 00:00:00'))
                        && $item['created_at'] < strtotime(date('Y-m-d 23:59:59'))) {
                        $todayDnMember++;
                    }
                }
            }

            $data = [
                'list'  => $list['list'],
                'total' => $list['total'],
                'today' => $todayDnMember,
            ];

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

}