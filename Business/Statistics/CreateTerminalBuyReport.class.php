<?php

namespace Business\Statistics;

use Library\Cache\Cache;
use Library\Util\DebugUtil;
use Model\Order\OrderTrack;
use Model\Report\StatisticsV2;

class CreateTerminalBuyReport extends CreateReportBase
{
    //分销商供应商ID
    protected $_fid = 0;
    protected $_statisticsModelV2;
    protected $_orderDiscountApi;

    public function __construct($logPath)
    {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'terminal_buy_report:start');
        parent::__construct();
    }

    /**
     * 开始跑脚本
     */
    public function runTask($date, $fid = 0, $separeTimeType = 0)
    {
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }
        if (!is_numeric($fid)) {
            return ['code' => 403, 'msg' => 'fid格式错误'];
        }
        $this->_fid = $fid;
        $msg  = '';
        try {
            $tmpRes = (new StatisticsV2())->_clearPreData($date, 'terminal_buy');
            if ($tmpRes === false) {
                return ['code' => 403, 'msg' => '记录删除失败'];
            }
            $maxNum   = 6;
            $dateList = $separeTimeType ? $this->_getDateListOrder($date) : $this->_getDateListNew($date);
            $dateNum  = count($dateList);
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $times = ceil($dateNum / $maxNum);

            for ($i = 0; $i < $times; $i++) {
                sleep(1);
                
                $handleDateList = array_slice($dateList, $i * $maxNum, $maxNum);
                $procNum = count($handleDateList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, $procNum, 1);
                $task->run($handleDateList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "terminal_buy_report:end:{$msg}");
    }


    /**
     * 子进程脚本l
     */
    public function runWorker($dateList)
    {
        if (is_array($dateList)) {
            $dateList = array_shift($dateList);
        }
        //进程id
        $pid   = getmypid();
        $cache = Cache::getInstance('redis');

        $cacheKey   = 'statis_report:terminal_buy_report';
        $cacheValue = "$pid:{$dateList[0]}-{$dateList[1]}";

        $cache->sAdd($cacheKey, $cacheValue);
        $cache->expire($cacheKey, 3600 * 12);

        $code = 200;
        $msg  = '';

        try {

            $this->runOrderV2Worker($dateList);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str = 'terminal_buy_report:';
        $str .= $dateList[0] . ' - ' . $dateList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        pft_log($this->_logPath, $str);
        $code == 200 && $cache->sRem($cacheKey, $cacheValue);
    }

    private function runOrderV2Worker($date)
    {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) ||
            (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24)) {
            throw new \Exception('时间格式错误', 403);
        }
        $day       = date('Ymd', strtotime($date[0]));
        $total     = $this->_getOrderTrackTotal($date[0], $date[1], $this->_fid); //$where['action'] = ['in', [4, 16]];
        $totalPage = ceil($total / $this->_selectSize);
        //如果没有获取到数据的话，就直接返回了
        if ($totalPage <= 0) {
            return true;
        }
        //分页处理数据
        $res = true;
        for ($page = 1; $page <= $totalPage; $page++) {
            $list  = $this->_getOrderTrackList($date[0], $date[1], $page, $this->_selectSize, $this->_fid);   //购票+撤销撤改
            /*DebugUtil::info([
                'tag' => 'terminal_buy_list',
                'date' => $date,
                'list' => $list,
            ], 'terminal_buy_list', 'scenic_biz01');*/
            if ($list === false) {
                return false;
            }
            // 通过订单号获取当时的操作员
            // 获取action =0 (下单的操作人)
            $orderNumArr       = array_values(array_unique(array_column($list, 'ordernum')));
            $operMemberListRes = [];
            foreach (array_chunk($orderNumArr, 100) as $chunk) {
                $temp = $this->_getTrackOrderListByOrderNums($chunk);
                $operMemberListRes = array_merge($operMemberListRes, $temp ?? []);
            }

            if ($operMemberListRes === false) {
                throw new \Exception("查询操作员出错");
            }
            $operMemberList = [];
            $sourceList     = [];
            foreach ($operMemberListRes as $item) {
                $operMemberList[$item['ordernum']] = $item['oper_member'];
                $sourceList[$item['ordernum']]     = $item['source'];
            }
            //是不是最后一个分片
            $isLastSlice = ($page == $totalPage) ? true : false;
            $res         = $this->_handleTerminalBuyData($day, $list, $isLastSlice, $operMemberList, $sourceList);
        }

        return $res ? true : false;
    }

    /**
     * 终端购票实时汇总报表统计数据处理
     *
     * @param  string  $day  哪天
     * @param  array  $checkedData  验证和撤销数据
     * @param  boolean  $isLastSlice  是不是最后一个分片
     * @param  array  $operMemberList  操作员列表
     *
     * @return
     */
    public function _handleTerminalBuyData($day, $checkedData, $isLastSlice, $operMemberList, $sourceList)
    {
        //首先获取之前的缓存数据
        $cacheData = $this->_cacheDataArr($day, 'terminal_buy', 'get');

        //需要插入数据库的汇总数据
        $cacheInsertData = isset($cacheData['insert_data']) ? $cacheData['insert_data'] : [];

        //记录近日已经验证的订单ID数组，用来统计近日验证订单数（checked_orders） -- 特别是针对分批验证
        $cacheOrderData = isset($cacheData['order_data']) ? $cacheData['order_data'] : [];

        //统一获取订单的tid和lid
        $orderIdArr = array_column($checkedData, 'ordernum');
        $orderIdArr = array_values(array_unique($orderIdArr));

        if (!$this->_statisticsModelV2) {
            $this->_statisticsModelV2 = new StatisticsV2();
        }
        $orderList = $this->_statisticsModelV2->_getOrderBaseInfo($orderIdArr, $extField = 'ordernum, tid, lid, tprice, paymode');
        //如果追踪记录里面的branchTerminal数据不存在，直接不进行统计了
        foreach ($checkedData as $key => $item) {
            if (!$item['branchTerminal']) {
                unset($checkedData[$key]);
            }
        }
        //获取订单优惠券抵扣金额
        //获取优惠金额信息
        if (!$this->_orderDiscountApi) {
            $this->_orderDiscountApi = new \Business\JavaApi\Order\OrderTicketDiscounts();
        }
        $orderDiscountInfo = $this->_orderDiscountApi->batchQueryList($orderIdArr);
        $orderDiscountInfo = empty($orderDiscountInfo['data']) ? [] : $orderDiscountInfo['data'];
        $couponDiscount    = [];
        foreach ($orderDiscountInfo as $value) {
            if ($value['couponType'] == 2) {
                $couponDiscount[$value['orderNum']] += $value['couponPrice'];
            }
        }

        foreach ($checkedData as $item) {
            $orderNum   = $item['ordernum'];
            $action     = $item['action'];
            $tnum       = $item['tnum'];
            $terminal   = $item['branchTerminal'];
            $source     = isset($sourceList[$orderNum]) ? $sourceList[$orderNum] : -1;
            $operMember = $operMemberList[$orderNum];

            if (!isset($orderList[$orderNum])) {
                continue;
            }

            //支付方式说明
            //支付方式:0=账户余额,1=支付宝,2=授信支付,3=产品自销,4=现场支付,5=微信支付,6=会员卡支付,7=银联支付,8=环迅支付,9=现金支付,10=会员卡,11=卡拉卡,12=拉卡拉商户，13=年卡特权支付,14=民生微信支付,15=平安银行,16=民生支付宝支付

            $tid     = $orderList[$orderNum]['tid'];
            $lid     = $orderList[$orderNum]['lid'];
            $tprice  = $orderList[$orderNum]['tprice'];
            $paymode = intval($orderList[$orderNum]['paymode']);

            if (!isset($cacheInsertData[$tid][$paymode][$operMember][$terminal][$source])) {
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source] = [
                    'lid'                 => $lid,
                    'buy_tickets'         => 0,
                    'buy_money'           => 0,
                    'coupon_money'        => 0,
                    'revoke_coupon_money' => 0,
                ];
            }
            if (in_array($action, [4, 16])) {
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['buy_tickets'] += $tnum;
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['buy_money']   += $tnum * $tprice;
            } else {
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['buy_tickets'] += 0;
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['buy_money']   += 0;
            }

            if (in_array($action, [6, 7])) {
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['revoke_tickets'] += $tnum;
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['revoke_money']   += $tnum * $tprice;
                //如果是撤销撤改，需要获取下撤销撤改的优惠券退款金额
                $extContent       = @json_decode($item['ext_content'], true);
                $actionIdxs       = $extContent['serial_number'] ?? '';
                $tmpDiscountMoney = 0;
                if (!empty($actionIdxs)) {
                    $idxArr = [];
                    //非下单支付行为的直接取对应积分下标获取对应积分数值
                    $idxInfo = explode(',', $actionIdxs);
                    foreach ($idxInfo as $item) {
                        $idx = explode('-', $item);
                        for ($i = $idx[0]; $i <= $idx[1]; $i++) {
                            $idxArr[] = $i;
                        }
                    }
                    //通过下标获取到对应下标优惠券抵扣金额数值
                    $tempOrderDiscountRes = $this->_orderDiscountApi->list($orderNum, 0, $idxArr);
                    foreach ($tempOrderDiscountRes['data'] as $tmp) {
                        if ($tmp['couponType'] == 2) {
                            $tmpDiscountMoney += $tmp['couponPrice'];
                        }
                    }
                }
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['revoke_coupon_money'] += $tmpDiscountMoney;
            } else {
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['revoke_tickets'] += 0;
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['revoke_money']   += 0;
            }
            //下单操作才有优惠券抵扣金额
            if ($action == 4) {
                $cacheInsertData[$tid][$paymode][$operMember][$terminal][$source]['coupon_money'] += $couponDiscount[$orderNum] ?? 0;
            }

        }

        //如果不是最后一个分片，就先将数据写入缓存文件
        if (!$isLastSlice) {
            $tmpCacheData = [
                'insert_data' => $cacheInsertData,
            ];

            $this->_cacheDataArr($day, 'terminal_buy', 'set', $tmpCacheData);
            unset($cacheInsertData, $cacheOrderData);

            return true;
        } else {
            //如果是最后一个，就将之前的数据清除
            $this->_cacheDataArr($day, 'terminal_buy', 'clear');

            unset($cacheOrderData);
        }
        $updateTime = time();
        //分批插入数据
        $insertData = [];

        //数据插入结果 - 如果没有数据就默认为成功
        $lastId = true;

        foreach ($cacheInsertData as $tid => $tidData) {
            foreach ($tidData as $payway => $paywayData) {
                foreach ($paywayData as $operMember => $operMemberData) {
                    foreach ($operMemberData as $terminal => $terminalData) {
                        foreach ($terminalData as $sourceKey => $sourceData) {
                            $tmp                = $sourceData;
                            $tmp['tid']         = $tid;
                            $tmp['terminal']    = $terminal;
                            $tmp['pay_way']     = $payway;
                            $tmp['day']         = $day;
                            $tmp['oper_member'] = $operMember;
                            $tmp['update_time'] = $updateTime;
                            $tmp['source']      = $sourceKey;

                            $insertData[] = $tmp;

                            if (count($insertData) >= $this->_insertLimit) {
                                //达到条数就插入数据
                                $lastId = $this->_insertDataArr($insertData, 'terminal_buy');

                                //初始化
                                $insertData = [];
                            }
                        }
                    }
                }
            }
        }

        /*DebugUtil::info([
            'tag' => 'terminal_buy_handle2',
            'cacheInsertData' => $cacheInsertData,
        ], 'terminal_buy_handle', 'scenic_biz01');*/
        //将没有插入的数据最后插入
        if ($insertData) {
            $lastId = $this->_statisticsModelV2->_insertDataArr($insertData, 'terminal_buy');
        }

        //返回
        return $lastId;
    }


    /**
     * 获取订单追踪记录列表
     * <AUTHOR>
     * @date   2016-08-05
     *
     * @param  string  $startTime  具体哪天 - 2016-10-23
     * @param  string  $endTime  具体哪天 - 2016-10-23
     * @param  string  $type  all_checked ：应收应付类型，terminal_checked：终端实时汇总，all_order：支付+取消
     *
     * @return
     */
    private function _getOrderTrackList($startTime, $endTime, $page = 1, $size = 5000, $fid = 0)
    {
        if (!$this->_trackModel) {
            $this->_trackModel = new OrderTrack(true);
        }
        $where = [
            'insertTime' => ['between', [$startTime, $endTime]],
            'tnum'       => ['gt', 0],
            'action'     => ['in', [6, 7, 4, 16]],
        ];
        if ($fid) {
            $where['apply_did'] = $fid;
        }
        $field = 'id, ordernum, tnum, action, branchTerminal, oper_member, source, ext_content';

        $res = $this->_trackModel->getList($where, $field, $page, $size);

        return $res;
    }

    /**
     * 获取订单追踪记录总数
     * <AUTHOR>
     * @date   2016-08-05
     *
     * @param  string  $day  具体哪天 - 2016-10-23
     * @param  string  $type  checked ：检票，order:预定列表，cancel：取消，revoke：撤销, expire: 过期, unpaid: 未支付，all_checked: 验证+撤销，all_order:支付+取消
     *
     * @return
     */
    private function _getOrderTrackTotal($startTime, $endTime, $applyDid = 0)
    {
        if (!$this->_trackModel) {
            $this->_trackModel = new OrderTrack(true);
        }

        $where = [
            'insertTime' => ['between', [$startTime, $endTime]],
            'tnum'       => ['gt', 0],
            'action'     => ['in', [6, 7, 4, 16]],
        ];
        if ($applyDid) {
            $where['apply_did'] = $applyDid;
        }
        return $this->_trackModel->getTotal($where);
    }
}