<?php

namespace Business\Statistics;
use \Library\Cache\Cache;
use Library\Constants\Stats\ExecutionCustomConfigKey as ExecutionCustomConfigKeyConstants;

/**
 * 新版预定报表（按小时汇总）
 *
 * <AUTHOR>
 * @date   2018-0806
 */
class CreateOrderV2HourReport extends CreateReportBase
{

    //分销商供应商ID
    protected $_fid = 0;

    protected  $_reportResultError = '';
    protected  $_reportResultSuccess = '';

    public function __construct($logPath,$date='')
    {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'order_v2_hour:start');
        self::buildReportResult($date);
        parent::__construct();
    }

    public function buildReportResult($date){
        $today = $date ?? date('Y-m-d');
        $this->_reportResultError   = "report_result:order_v2_hour:{$today}:error";
        $this->_reportResultSuccess   = "report_result:order_v2_hour:{$today}:success";
    }

    public function getReportResultKey(){
        return [$this->_reportResultError,$this->_reportResultSuccess];
    }
    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function runTask($date, $fid = 0, $separeTimeType = 0)
    {
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        $this->_fid = $fid;

        $code = 200;
        $msg  = '';

        try {

            $date     = date('Y-m-d', strtotime($date));
            $intDate  = date('Ymd', strtotime($date));
            $hourList       = [];
            $clearDateList  = [];

            for ($i = 0; $i <= 23; $i++) {
                $hour = $i < 10 ? "0{$i}" : $i;
                $hourList[] = [$date . " {$hour}:00:00", $date . " {$hour}:59:59"];
                $clearDateList[] = $intDate . $hour;
            }

            // if ($separeTimeType) {
            //     $hourList = $this->_getDateHourForHoliday($date);
            // }
            $hourList = $this->_getExecutionTimeSegments($date, ExecutionCustomConfigKeyConstants::HOUR_KEY);

            $hourNum = count($hourList);
            $maxNum  = 6;

            $this->_clearPreHourData($clearDateList, 'order_v2_hour', $this->_fid);
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $times = ceil($hourNum / $maxNum);

            for ($i = 0; $i < $times; $i++) {
                sleep(1);

                $handleHourList = array_slice($hourList, $i * $maxNum, $maxNum);
                $procNum = count($handleHourList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, $procNum, 1);
                $task->run($handleHourList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }
            $cache = \Library\Cache\RedisCache::Connect('master');
            if(!$cache->exists($this->_reportResultError)){
                $cache->sAdd($this->_reportResultSuccess, 'order_v2_hour');
                $cache->expire($this->_reportResultSuccess, 60*60*24*7);
            }
            $cache->close();

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "order_v2_hour:end:{$msg}");
    }


    /**
     * 子进程脚本l
     */
    public function runWorker($hourList)
    {
        if (is_array($hourList)) {
            $hourList = array_shift($hourList);
        }

        //进程id
        $pid   = getmypid();
        $cache = \Library\Cache\RedisCache::Connect('master');

        $cacheKey   = 'statis_report:create_data:order_v2_hour';
        $cacheValue = "$pid:{$hourList[0]}-{$hourList[1]}";

        $cache->sAdd($cacheKey, $cacheValue);
        $cache->expire($cacheKey, 3600 * 12);

        $code = 200;
        $msg  = '';

        try {

            $this->runOrderV2HourWorker($hourList);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str = 'order_v2_hour:';
        $str .= $hourList[0] . ' - ' . $hourList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        if($code != 200){
            $cache->sAdd($this->_reportResultError, $cacheValue);
            $cache->expire($this->_reportResultError, 60*60*24*7);
        }

        pft_log($this->_logPath, $str);
        $code == 200 && $cache->sRem($cacheKey, $cacheValue);
        $cache->close();
    }

    private function runOrderV2HourWorker($date)
    {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) ||
            (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24)) {
            throw new \Exception('时间格式错误', 403);
        }

        if (!is_numeric($this->_fid)) {
            throw new \Exception('fid格式错误', 403);
        }

        $dayHour = date('Ymd', strtotime($date[0])) . date('H', strtotime($date[0]));
        $filterOrderNum = [];

        if ($this->_fid) {
            $filterOrderNum = $this->_getFidOrderNum($date);
        }

        $total     = $this->_getTrackTotal($date, 'order_v2', $filterOrderNum);
        $totalPage = ceil($total / $this->_selectSize);

        $checkedData = [];
        $mainTid     = [];
        //站点
        $siteIdArr = [];
        //售后编号
        $afterSaleCodeArr = [];

        //订单退票手续费对应的track_id
        $cancelTrackData = [];

        //分页获取数据
        for ($page = 1; $page <= $totalPage; $page++) {
            $list = $this->_getTrackList($date, 'order_v2', $page, $this->_selectSize, $filterOrderNum);
            //如果查询出错了,直接返回错误
            if ($list === false) {
                throw new \Exception("查询出错");
            }

            $orderNumArr       = array_column($list, 'ordernum');
            $operMemberListRes = $this->_getTrackOrderListByOrderNums($orderNumArr);

            if ($operMemberListRes === false) {
                throw new \Exception("查询操作员出错");
            }

            //操作人和站点取追踪表下单数据
            $operMemberList = [];
            foreach ($operMemberListRes as $item) {
                $operMemberList[$item['ordernum']] = $item['oper_member'];
                $siteIdArr[$item['ordernum']]      = $item['SalerID'];
            }

            foreach ($list as $item) {
                $orderNum   = strval($item['ordernum']);
                $extContent = json_decode($item['ext_content'], true);

                //售后信息
                $afterSaleNum  = $extContent['afterSaleNum'] ?? 0;
                $afterSaleCode = $extContent['afterSaleCode'] ?? '';

                //累加数据  预订/取消/撤销
                if (isset($checkedData[$orderNum])) {
                    if (in_array($item['action'], [1, 2])) {
                        $checkedData[$orderNum]['cancel'] += $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($extContent['serial_number']) && $extContent['serial_number']) {
                            $checkedData[$orderNum]['point_cancel_idx'][$item['oper_member']][$item['action']][] = $extContent['serial_number'] ?? '';
                        }
                    }

                    if (in_array($item['action'], [4, 16])) {
                        $checkedData[$orderNum]['order'] += $item['tnum'];
                    }

                    if (in_array($item['action'], [6, 7])) {
                        $checkedData[$orderNum]['revoke'] += $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($extContent['serial_number']) && $extContent['serial_number']) {
                            $checkedData[$orderNum]['point_revoke_idx'][$item['oper_member']][$item['action']][] = $extContent['serial_number'] ?? '';
                        }
                    }

                    //出票/取票状态累加(状态值:3)重打印8不计算
                    if (in_array($item['action'], [3])) {
                        $checkedData[$orderNum]['print_num'] += $item['tnum'];
                    }

                    //售后信息
                    if ($item['action'] == 38) {
                        //售后数量
                        $checkedData[$orderNum]['after_sale'] += $afterSaleNum;
                    }
                } else {
                    if (in_array($item['action'], [1, 2])) {
                        $checkedData[$orderNum]['cancel']    = $item['tnum'];
                        $checkedData[$orderNum]['revoke']    = 0;
                        $checkedData[$orderNum]['order']     = 0;
                        $checkedData[$orderNum]['print_num'] = 0;
                        //本次操作优惠的下标信息
                        if (isset($extContent['serial_number']) && $extContent['serial_number']) {
                            $checkedData[$orderNum]['point_cancel_idx'][$item['oper_member']][$item['action']][] = $extContent['serial_number'] ?? '';
                        }
                    }

                    if (in_array($item['action'], [4, 16])) {
                        $checkedData[$orderNum]['order']     = $item['tnum'];
                        $checkedData[$orderNum]['cancel']    = 0;
                        $checkedData[$orderNum]['revoke']    = 0;
                        $checkedData[$orderNum]['print_num'] = 0;
                    }

                    if (in_array($item['action'], [6, 7])) {
                        $checkedData[$orderNum]['order']     = 0;
                        $checkedData[$orderNum]['cancel']    = 0;
                        $checkedData[$orderNum]['revoke']    = $item['tnum'];
                        $checkedData[$orderNum]['print_num'] = 0;
                        //本次操作优惠的下标信息
                        if (isset($extContent['serial_number']) && $extContent['serial_number']) {
                            $checkedData[$orderNum]['point_revoke_idx'][$item['oper_member']][$item['action']][] = $extContent['serial_number'] ?? '';
                        }
                    }
                    //新增取票状态
                    if ($item['action'] == 3) {
                        $checkedData[$orderNum]['order']     = 0;
                        $checkedData[$orderNum]['cancel']    = 0;
                        $checkedData[$orderNum]['revoke']    = 0;
                        $checkedData[$orderNum]['print_num'] = $item['tnum'];
                    }

                    //售后信息
                    if ($item['action'] == 38) {
                        $checkedData[$orderNum]['order']     = 0;
                        $checkedData[$orderNum]['cancel']    = 0;
                        $checkedData[$orderNum]['revoke']    = 0;
                        $checkedData[$orderNum]['print_num'] = 0;
                        //售后数量
                        $checkedData[$orderNum]['after_sale'] = $afterSaleNum;
                    }

                    if ($item['action'] == 0) {
                        $checkedData[$orderNum]['order']     = 0;
                        $checkedData[$orderNum]['cancel']    = 0;
                        $checkedData[$orderNum]['revoke']    = 0;
                        $checkedData[$orderNum]['print_num'] = 0;
                    }
                }

                //撤销撤改操作人员取实际的操作人员
                $operMember = in_array($item['action'],
                    [1, 2, 3, 6, 7, 38]) ? $item['oper_member'] : $operMemberList[$item['ordernum']];

                if ($item['action'] != 0) {
                    //记录订单不同操作的操作人员及操作票数
                    if (isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember])) {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['tnum'] += $item['tnum'];
                    } else {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['tnum'] = $item['tnum'];
                    }
                }

                //记录售后编号的操作情况
                if ($item['action'] == 38 && !empty($afterSaleCode)) {
                    if (isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode])) {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode]['after_sale_num'] += $afterSaleNum;

                    } else {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode]['after_sale_num'] = $afterSaleNum;
                    }

                    $afterSaleCodeArr[] = $afterSaleCode;
                }

                //手续费操作id记录
                if (in_array($item['action'], [1, 2, 6, 7])) {

                    //生成统计key，用来区分汇总后track_id
                    $checkedDataKey = $this->generateKeywordString(['oper_member', $item['action'], $operMember]);

                    if (!isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'])) {
                        //手续费操作id记录
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'] = [];
                        $cancelTrackData[$orderNum][$checkedDataKey] = [];
                    }

                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'][] = intval($item['id']);
                    $cancelTrackData[$orderNum][$checkedDataKey][] = intval($item['id']);
                }
            }
        }

        if (isset($list)) {
            unset($list);
        }

        //分页处理数据
        $res       = true;
        $total     = count($checkedData);
        $totalPage = ceil($total / $this->_pieceNum);
        $packInfo  = [];

        for ($i = 0; $i < $totalPage; $i++) {
            //套票子票的订单集合
            $packOrderArr = [];
            //套票子票的订单信息集合
            $packOrderInfo = [];

            $queryIds = array_slice($checkedData, $i * $this->_pieceNum, $this->_pieceNum, true);

            //通过订单ID获取订单信息
            $orderIdArr = array_keys($queryIds, true);

            //获取退票手续费的数据
            $cancelServiceArr = [];
            if (!empty($cancelTrackData)) {
                $cancelServiceArr = $this->getCancelServiceByTrackIdGroup($this->matchArrayKeys($cancelTrackData, $orderIdArr));
            }

            //每个订单号对应的主订单号 如果是主订单 则自己和自己对应
            $packInfo  = $this->_getPackInfo($orderIdArr);
            $allOrder  = [];
            $mainOrder = [];
            foreach ($packInfo as $key => $item) {
                //取出所有主订单号
                if ($item == 1) {
                    //主订单
                    $mainOrder[] = $key;
                    array_push($allOrder, (string)$key);
                } else {
                    $mainOrder[] = $item;
                    array_push($allOrder, (string)$key);
                    array_push($allOrder, (string)$item);
                }
            }

            //主票订单非当天下单
            $notTodayPackMainOrder = $this->_filterPackMainOrderNotToday($mainOrder, $date[0], 4, true);

            $allOrder = array_merge($allOrder, $orderIdArr);
            $res      = $this->_getOrderInfo($allOrder);

            $orderList  = [];
            $tmpUserArr = [];
            $tidArr     = [];

            //套票主票订单信息
            $packMainOrderList       = [];
            //套票主票用户id
            $packMainOrderTmpUserArr = [];

            foreach ($res as $item) {
                //过滤未支付订单
                if ($item['pay_status'] == 2) {
                    continue;
                }
                $orderNum = $item['ordernum'];

                unset($item['ordernum']);

                if (in_array($orderNum, $orderIdArr)) {
                    $orderList[$orderNum] = $item;

                    if ($item['ordermode'] == 11) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 43) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 18) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 15) {
                        $tmpUserArr[] = $item['member'];
                    }
                }

                //订单号对应的tid
                $tidArr[$orderNum] = $item['tid'];
                //获取套票主票对应的tid
                if (in_array($orderNum, $mainOrder)) {
                    $mainTid[] = $item['tid'];

                    $packMainOrderList[$orderNum] = $item;
                }

                if ($item['ordermode'] == 23) {
                    //套票的子票
                    $packOrder[] = $orderNum;
                }

                //补全操作的套票主票为非当日套票主票需要的散客信息
                if (in_array($orderNum, $notTodayPackMainOrder)) {
                    if (in_array($item['ordermode'], [11, 43, 18,15])) {
                        isset($item['member']) && $packMainOrderTmpUserArr[] = $item['member'];
                    }
                }
            }

            unset($res);

            //套票主订单分销链
            $packMainOrderChain = [];
            //用户信息，用来判断散客
            $packMainOrderSankeList = [];
            if (!empty($notTodayPackMainOrder)) {
                //分销链查询
                $packMainOrderChainRes = \Business\JavaApi\Order\Query\Container::query('OrderAidsSplitQueryService',
                    'getOrderDistributionChain', [$notTodayPackMainOrder]);
                if ($packMainOrderChainRes['code'] != 200 || empty($packMainOrderChainRes['data'])) {
                    pft_log($this->_logPath, json_encode(['预订套票主票分销链获取失败', $notTodayPackMainOrder, $packMainOrderChainRes['msg'] ?? ''], JSON_UNESCAPED_UNICODE));
                }

                $packMainOrderChain = is_array($packMainOrderChainRes['data']) ? $packMainOrderChainRes['data'] : [];

                //用户信息，用来判断散客
                if (!empty($packMainOrderTmpUserArr)) {
                    $packMainOrderSankeList  = $this->_getSankeList(array_unique($packMainOrderTmpUserArr));
                }
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord($orderIdArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取下单的积分、优惠券信息
            $pointAndCouponInfo = $this->_getOrderPointsRecord($orderIdArr);
            //获取订单对应积分、优惠券优惠下标明细
            $discountIdxList = $this->_getOrderPointsIdxRecord($orderIdArr);

            $pointDetail = [
                'pointAndCouponInfo' => $pointAndCouponInfo,
                'discountIdxList'    => $discountIdxList,
            ];

            //获取订单详情额外信息
            $orderDetailRes  = $this->_getOrderDetailByOrderNum(array_merge(array_keys($packMainOrderList), $orderIdArr));
            //获取子商户信息
            $orderSubSidMap = $orderDetailRes['orderSubSidMap'] ?? [];

            //微商城用户获取用户信息。用来判断是不是散客
            $sankeList = [];
            if ($tmpUserArr) {
                $tmpUserArr = array_unique($tmpUserArr);
                $sankeList  = $this->_getSankeList($tmpUserArr);
            }

            //取出主票对应的发布人
            $ticketApplyInfo = $this->_getTicketApplyInfo($mainTid);

            //获取票的顶级供应商
            $ticketApplyData = $this->_getTicketApplyInfo(array_column($orderList, 'tid'));

            //获取分销链数据
            $chainList = $this->_getBuyChain($orderIdArr);

            if ($chainList === false) {
                throw new \Exception("获取分销链失败");
            }

            if (count($chainList) == 0) {
                continue;
            }

            //获取售后相关金额
            $afterSaleCodeArr  = array_values(array_unique($afterSaleCodeArr));
            $afterSaleOrderMap = $this->queryTransactiondetailMap($afterSaleCodeArr);

            //取出套票子票订单的sale_money
            foreach ($chainList as $item) {
                if (in_array($item['orderid'], $packOrderArr)) {
                    $packOrderInfo[$item['orderid']][$item['buyerid']] = $item['sale_money'];
                }
            }

            //是不是最后一个分片
            $isLastSlice = ($i == ($totalPage - 1)) ? true : false;

            $res = $this->_handleOrderV2HourData($dayHour, $chainList, $queryIds, $orderList, $cancelServiceArr, $isLastSlice,
                $sankeList, $tidArr, $packInfo, $ticketApplyInfo, $couponList, $siteIdArr, $pointDetail, $notTodayPackMainOrder,
                $packMainOrderList, $packMainOrderChain, $packMainOrderSankeList, $orderSubSidMap, $ticketApplyData, $afterSaleOrderMap);

            if (empty($res)) {
                throw new \Exception("处理数据失败");
            }
        }
    }

    private function _handleOrderV2HourData($dayHour, $chainList, $checkedData, $orderList, $cancelInfo, $isLastSlice, $sankeList, $tidArr, $packInfo, $ticketApplyInfo, $couponList, $siteIdArr, $pointDetail, $notTodayPackMainOrder = [], $packMainOrderList = [], $packMainOrderChain = [], $packMainOrderSankeList = [], $orderSubSidMap = [], $ticketApplyData = [], $afterSaleOrderMap = [])
    {
        //首先获取之前的缓存数据
        $splitList = $this->_cacheDataArr($dayHour, 'order_v2_hour', 'get');
        $statisBiz = new \Business\Statistics\statistics();

        $lastSettlementDiscountInfo = [];
        for ($i = 0; $i < count($chainList); $i++) {
            $item = $chainList[$i];

            $orderNum = $item['orderid'];
            if (!array_key_exists($orderNum, $checkedData)) {
                continue;
            }

            if (!array_key_exists($orderNum, $orderList)) {
                continue;
            }

            $orderInfo   = $orderList[$orderNum];
            $checkedInfo = $checkedData[$orderNum];

            //积分、优惠券下单信息
            $pointAndCouponInfo = $pointDetail['pointAndCouponInfo'];
            //积分、优惠券操作下标信息
            $discountIdxList = $pointDetail['discountIdxList'] ?? [];
            //积分、优惠券取消操作下标数据
            $pointCancelIdxArr = $checkedInfo['point_cancel_idx'] ?? [];
            //积分、优惠券撤销操作下标数据
            $pointRevokeIdxArr = $checkedInfo['point_revoke_idx'] ?? [];

            //汇总积分、优惠券取消修改撤销撤改优惠金额
            $pointAmountDetail = $this->_handlePointsIdxAmount($orderNum, $discountIdxList, $pointCancelIdxArr, $pointRevokeIdxArr);

            $fid      = $item['sellerid'];
            $sellerId = $item['buyerid'];

            //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
            $oldSeller = $item['buyerid'];
            $level     = $item['level'];
            $saleMoney = $item['sale_money'];

            //购买价
            $costMoney = intval($item['cost_money']);

            $paymode  = $item['pmode'];

            //站点ID
            $siteId = isset($siteIdArr[$orderNum]) ? $siteIdArr[$orderNum] : 0;

            $ticketNum = $checkedInfo['order'];
            $cancelNum = $checkedInfo['cancel'];
            $revokeNum = $checkedInfo['revoke'];
            //售后数量
            $afterSaleNum  = $checkedInfo['after_sale'] ?? 0;
            //取票数
            $printNum  = $checkedInfo['print_num'];
            //$operMember = isset($checkedInfo['oper_member']) && $checkedInfo['oper_member'] ? $checkedInfo['oper_member'] : $item['buyerid'];
            //操作人员信息
            $operMemberInfo = isset($checkedInfo['oper_member']) && $checkedInfo['oper_member'] ? $checkedInfo['oper_member'] : [];

            if (empty($ticketNum) && empty($cancelNum) && empty($revokeNum) && empty($printNum) && empty($afterSaleNum)) {
                continue;
            }

            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];
            $channel = $orderInfo['ordermode'];

            //子商户id
            $subMerchantId = $orderSubSidMap[$orderNum] ?? 0;

            //存在子商户id，并且非顶级供应商，不展示
            if ($subMerchantId) {
                //获取下订单票的顶级供应商
                $orderTicketApplyDid = $ticketApplyData[$tid] ?? 0;
                if ($orderTicketApplyDid != $fid) {
                    //非顶级供应商，子商户不可见
                    $subMerchantId = 0;
                }
            }

            //计算优惠券使用金额
            if (isset($couponList[$orderNum][$fid][$sellerId])) {
                //优惠金额
                $eMoney = $couponList[$orderNum][$fid][$sellerId];
            } else {
                $eMoney = 0;
            }

            $discountDetail          = self::handleDiscountInfo($pointAndCouponInfo, [], $orderNum, $fid, $sellerId, $oldSeller);
            $discountMoney           = $discountDetail[self::DISCOUNT]['order_amount'];
            $settlementDiscountMoney = $discountDetail[self::SETTLEMENT_DISCOUNT]['order_amount'];

            //通过渠道将散客进行归类
            if (in_array($level, [-1, 0])) {
                $tmp = $this->_getSankeId($sellerId, $fid, $channel, $sankeList);

                if ($tmp !== false) {
                    //最后一级就是散客的情况(自供自销也算在这里面)
                    if ($level == 0) {
                        $level = 1;
                    } else {
                        $level = -3;
                    }
                    $sellerId = $tmp;

                    //预售券兑换的订单，末级销售金额为0
                    if ($this->_checkSaleMoneyZero($channel, $paymode) && !$this->_needAddSplit($level, $sellerId, $fid)) {
                        $saleMoney = 0;
                    }
                }
            }

            $mainTid  = 0;
            $mainType = '';
            if (isset($packInfo[$orderNum]) && $packInfo[$orderNum] == 1) {
                //套票主票
                $mainTid = 0;
            } elseif (isset($packInfo[$orderNum])) {
                //主票的订单号对应的票类ID
                $mainTid  = $tidArr[$packInfo[$orderNum]];
                $mainType = $packMainOrderList[$packInfo[$orderNum]]['product_type'] ?? '';
            }
            //
            // //由于系统一开始的问题 套票子票在整条分销链中全部记为套票子票下单 这是错误的
            // //只有被将该票打包成套票的供应商出售这笔订单的销售渠道会记为套票子票下单
            // //在这里做个判断 如果子票的主票的发布人 是当前处理的分销链层级的卖家 则跳过（预订报表过滤子票下单）
            if (!empty($mainTid)) {
                //----1. 主票订单号的票类ID的发布人 ApplyDid
                $applyDid = $ticketApplyInfo[$mainTid];
                //----2. 如果主票的发布人是当前处理的分销链的层级的卖票的人sell_id 跳过
                if ($applyDid != $fid) {
                    $mainTid  = 0;
                    $mainType = '';
                }

                //套票打包方，主票下单不当天需要补全主票维度，指标全部为0
                if ($applyDid == $fid && in_array($packInfo[$orderNum], $notTodayPackMainOrder)) {
                    $mainOrderNum = $packInfo[$orderNum];
                    list($mSellerId, $mPid, $mChannel, $mPaymode, $mOperMember, $mSiteId, $mLid, $mTid, $mLevel, $mSubMerchantId) = $this->_packMainOrderDecode($fid,
                        $mainOrderNum, $packMainOrderList, $packMainOrderChain, $packMainOrderSankeList, $orderSubSidMap);
                    $mMainTid  = 0;
                    $mMainType = '';
                    if ($mLid && $mPid && $mTid  && !is_null($mSellerId) && !is_null($mLevel) && !is_null($mPaymode)) {
                        if (!isset($splitList[$fid][$mSellerId][$mPid][$mMainTid][$mMainType][$mChannel][$mPaymode][$mSubMerchantId][$mOperMember][$mSiteId])) {
                            $splitList[$fid][$mSellerId][$mPid][$mMainTid][$mMainType][$mChannel][$mPaymode][$mSubMerchantId][$mOperMember][$mSiteId] = [
                                'lid'                     => $mLid,
                                'tid'                     => $mTid,
                                'lvl'                     => $mLevel,
                                'order_num'               => 0,
                                'order_ticket'            => 0,
                                'cancel_num'              => 0,
                                'cancel_ticket'           => 0,
                                'revoke_num'              => 0,
                                'revoke_ticket'           => 0,
                                'cost_money'              => 0,
                                'sale_money'              => 0,
                                'cancel_cost_money'       => 0,
                                'cancel_sale_money'       => 0,
                                'revoke_cost_money'       => 0,
                                'revoke_sale_money'       => 0,
                                'service_money'           => 0,
                                'after_sale_ticket_num'   => 0,
                                'after_sale_refund_money' => 0,
                                'after_sale_income_money' => 0,
                                'print_num'               => 0,
                                'orders_info'             => [[$mainOrderNum, 0]],
                                'cancel_orders_info'      => [],
                                'revoke_orders_info'      => [],
                                'print_orders_info'       => [],
                                'after_sale_info'         => [],
                            ];
                        } else {
                            $splitList[$fid][$mSellerId][$mPid][$mMainTid][$mMainType][$mChannel][$mPaymode][$mSubMerchantId][$mOperMember][$mSiteId]['orders_info'][] = [
                                $mainOrderNum,
                                0
                            ];
                        }
                    }
                }
            }

            //特殊处理
            $splitTmp = $this->_needAddSplit($level, $sellerId, $fid);

            if ($splitTmp) {
                //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                $splitData = [
                    'orderid'             => $orderNum,
                    'sellerid'            => $sellerId,
                    'buyerid'             => $this->_getChannelSanke($channel),
                    'level'               => -3, //非0/-1
                    'cost_money'          => $saleMoney,
                    'sale_money'          => $needMoney, //默认使用零售价的钱
                    'pmode'               => $paymode,
                ];

                //如果需要补全一条报表记录，且是预售券兑换订单，末级销售金额为0
                if ($this->_checkSaleMoneyZero($channel, $paymode)) {
                    $splitData['sale_money'] = 0;
                }

                $chainList[] = $splitData;
            }

            //针对单个供应商分销商时
            if ($this->_fid && $item['sellerid'] != $this->_fid) {
                continue;
            }

            //黑名单判断必须放在链路补全后，避免数据缺失
            if ($this->isInBlacklist($fid)) {
                continue;
            }

            //去除退票手续费
            $orderCancelInfo = $cancelInfo[$orderNum] ?? [];

            if (!isset($splitList[$fid])) {
                $splitList[$fid] = [];
            }

            if (!isset($splitList[$fid][$sellerId])) {
                $splitList[$fid][$sellerId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid])) {
                $splitList[$fid][$sellerId][$pid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid])) {
                $splitList[$fid][$sellerId][$pid][$mainTid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId] = [];
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {
                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember] = [];
                    }
                }
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {

                    $operMember = $oper;

                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId] = [
                            'lid'                     => $lid,
                            'tid'                     => $tid,
                            'lvl'                     => $level,
                            'order_num'               => 0,
                            'order_ticket'            => 0,
                            'cancel_num'              => 0,
                            'cancel_ticket'           => 0,
                            'revoke_num'              => 0,
                            'revoke_ticket'           => 0,
                            'cost_money'              => 0,
                            'sale_money'              => 0,
                            'cancel_cost_money'       => 0,
                            'cancel_sale_money'       => 0,
                            'revoke_cost_money'       => 0,
                            'revoke_sale_money'       => 0,
                            'service_money'           => 0,
                            'after_sale_ticket_num'   => 0,
                            'after_sale_refund_money' => 0,
                            'after_sale_income_money' => 0,
                            'print_num'               => 0,
                            'orders_info'             => [],
                            'cancel_orders_info'      => [],
                            'revoke_orders_info'      => [],
                            'print_orders_info'       => [],
                            'after_sale_info'         => [],
                        ];
                    }
                }
            }

            //整合数据
            if ($ticketNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //预订
                    if (!in_array($action, [4, 16])) {
                        continue;
                    }

                    $orderPoints           = $discountMoney;
                    $settlementOrderPoints = $settlementDiscountMoney;
                    //加票不记录积分
                    if ($action == 16) {
                        $orderPoints           = 0;
                        $settlementOrderPoints = 0;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];


                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$operTicketNum]['settlementOrderPoints'] =$this->getCostDiscountMoney($orderPoints, $settlementOrderPoints);

                        //上一级的卖出优惠金额 是下一级的买入优惠金额
                        $buySettlementOrder  = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$operTicketNum]['settlementOrderPoints'] ?? 0;

                        //支付数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['order_num']     += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['orders_info'][] = [
                            $orderNum,
                            $operTicketNum,
                            $orderPoints,
                            $settlementOrderPoints ?: $buySettlementOrder,
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['sale_money']    += $saleMoney * $operTicketNum - $eMoney - $this->getSaleDiscountMoney($orderPoints, $settlementOrderPoints);
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cost_money']    += $costMoney * $operTicketNum - $buySettlementOrder;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['order_ticket']  += $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['service_money'] += 0;

                    }
                }
            }

            if ($cancelNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //预订
                    if (!in_array($action, [1, 2])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];
                        $trackIds = $ticketInfo['track_ids'] ?? [];

                        //手续费
                        $serviceMoney = $this->mateCancelServiceMoneyByTrackIdGroup($fid, $trackIds, $orderCancelInfo);

                        $buySettlementCancel = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$operTicketNum]['cancelSettlementPoints'] ?? 0;

                        $actionDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operMember, 'cancel_amount');
                        $cancelPoints           = $actionDiscount[self::DISCOUNT];
                        $cancelSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$operTicketNum]['cancelSettlementPoints'] = $this->getCostDiscountMoney($cancelPoints, $cancelSettlementPoints);

                        //取消数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_num']           += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_sale_money']    += ($saleMoney * $operTicketNum - $eMoney - $this->getSaleDiscountMoney($cancelPoints, $cancelSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_orders_info'][] = [
                            $orderNum,
                            $operTicketNum,
                            $cancelPoints,
                            $cancelSettlementPoints ?: $buySettlementCancel,
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_cost_money']    += $costMoney * $operTicketNum - $buySettlementCancel;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['order_ticket']  += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_ticket'] += $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['service_money'] += $serviceMoney;
                    }
                }
            }

            if ($revokeNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //预订
                    if (!in_array($action, [6, 7])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];
                        $trackIds = $ticketInfo['track_ids'] ?? [];

                        //手续费
                        $serviceMoney = $this->mateCancelServiceMoneyByTrackIdGroup($fid, $trackIds, $orderCancelInfo);

                        $buySettlementRevoke = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$operTicketNum]['revokeSettlementPoints'] ?? 0;

                        $actionDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operMember, 'revoke_amount');
                        $revokePoints           = $actionDiscount[self::DISCOUNT];
                        $revokeSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$operTicketNum]['revokeSettlementPoints'] = $this->getCostDiscountMoney($revokePoints, $revokeSettlementPoints);


                        //取消数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_num']           += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_sale_money']    += ($saleMoney * $operTicketNum - $eMoney - $this->getSaleDiscountMoney($revokePoints, $revokeSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_orders_info'][] = [
                            $orderNum,
                            $operTicketNum,
                            $revokePoints,
                            $revokeSettlementPoints ?: $buySettlementRevoke,
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_cost_money']    += $costMoney * $operTicketNum - $buySettlementRevoke;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['order_ticket']  += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['cancel_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['revoke_ticket'] += $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['service_money'] += $serviceMoney;
                    }
                }
            }

            //取票数量统计
            if ($printNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //取票
                    if (!in_array($action, [3])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember    = $oper;
                        $printNum = $ticketInfo['tnum'];
                        //取票数量累加
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['print_num'] += $printNum;
                        //取票订单信息记录
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['print_orders_info'][] = [
                            $orderNum,
                            $printNum,
                        ];
                    }
                }
            }

            //售后数据
            if ($afterSaleNum > 0) {
                foreach ($operMemberInfo as $action => $info) {

                    if ($action != 38) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;

                        $afterSaleInfo = $ticketInfo['after_sale_info'] ?? [];

                        foreach ($afterSaleInfo as $afterSaleCode => $afterInfo) {
                            $afterSaleNum = $afterInfo['after_sale_num'] ?? 0;

                            //售后退回金额
                            $afterSaleRefundMoney = $afterSaleOrderMap[$orderNum][$afterSaleCode]['sellerId'][$fid] ?? 0;
                            //售后收入单价
                            $afterSaleIncomeMoney = $afterSaleOrderMap[$orderNum][$afterSaleCode]['buyerId'][$fid] ?? 0;

                            //售后订单明细 0.订单号 1.售后数量 2.编号 3.退回金额 4.收入金额
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['after_sale_info'][]       = [
                                $orderNum,
                                $afterSaleNum,
                                (string)$afterSaleCode,
                                $afterSaleRefundMoney,
                                $afterSaleIncomeMoney,
                            ];
                            //处理相同维度指标累加
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['after_sale_refund_money'] += $afterSaleRefundMoney;
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['after_sale_income_money'] += $afterSaleIncomeMoney;
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['after_sale_ticket_num']   += $afterSaleNum;
                        }
                    }
                }
            }
        }

        //如果不是最后一个分片，就先将数据写入缓存文件
        if (!$isLastSlice) {
            $this->_cacheDataArr($dayHour, 'order_v2_hour', 'set', $splitList);
            unset($splitList);

            return true;
        } else {
            //如果是最后一个，就将之前的数据清除
            $this->_cacheDataArr($dayHour, 'order_v2_hour', 'clear');
        }

        $updateTime = time();
        //分批插入数据
        $insertData   = []; //预定报表数据
        foreach ($splitList as $fid => $fidData) {
            foreach ($fidData as $sellerId => $sellerData) {
                foreach ($sellerData as $pid => $pidData) {
                    foreach ($pidData as $mainTid => $mainTidData) {
                        foreach ($mainTidData as $mainType => $mainTypeData) {
                            foreach ($mainTypeData as $channel => $channelData) {
                                foreach ($channelData as $paymode => $paymodeData) {
                                    foreach ($paymodeData as $subMerchantId => $subMerchantData) {
                                        foreach ($subMerchantData as $operMember => $operMemberData) {
                                            foreach ($operMemberData as $siteId => $data) {
                                                //入库数据处理
                                                $tmp                       = $data;
                                                $tmp['fid']                = $fid;
                                                $tmp['pay_way']            = $paymode;
                                                $tmp['orders_info']        = json_encode($tmp['orders_info']);
                                                $tmp['cancel_orders_info'] = json_encode($tmp['cancel_orders_info']);
                                                $tmp['revoke_orders_info'] = json_encode($tmp['revoke_orders_info']);
                                                $tmp['print_orders_info']  = json_encode($tmp['print_orders_info']); //取票订单信息处理
                                                $tmp['after_sale_info']    = json_encode($tmp['after_sale_info']);
                                                $tmp['reseller_id']        = $sellerId;
                                                $tmp['pid']                = $pid;
                                                $tmp['date_hour']          = $dayHour;
                                                $tmp['channel']            = $channel;
                                                $tmp['operate_id']         = $operMember;
                                                $tmp['update_time']        = $updateTime;
                                                $tmp['site_id']            = $siteId;
                                                $tmp['main_tid']           = $mainTid;
                                                $tmp['sub_merchant_id']    = $subMerchantId;
                                                $tmp['main_type']          = $mainType;

                                                $insertData[] = $tmp;

                                                if (count($insertData) >= $this->_insertLimit) {
                                                    //达到条数就插入数据
                                                    $lastId = $this->_insertDataArr($insertData, 'order_v2_hour');
                                                    //初始化
                                                    $insertData = [];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //将没有插入的数据最后插入
        if ($insertData) {
            $lastId = $this->_insertDataArr($insertData, 'order_v2_hour');
        }

        //返回
        return $lastId;
    }

    /**
     * 获取单个分销商供应商订单数据
     * <AUTHOR>
     * @date   2019-07-08
     *
     * @param  int  $fid  分销商供应商ID
     * @param  array  $date  时间区间
     * @param  string  $type  类型
     *
     * @return array
     */
    private function _getFidOrderNum($date)
    {
        //支付订单
        $payOrder = $this->_getOrderListByFid($this->_fid, $date[0], $date[1], 'order_v2');
        //取消订单
        $cancelOrder = $this->_getOrderByType($this->_fid, $date, 'cancel');
        //撤销撤改
        $revokeOrder = $this->_getOrderByType($this->_fid, $date, 'revoke');

        return array_unique(array_merge($payOrder, $cancelOrder, $revokeOrder));
    }
}