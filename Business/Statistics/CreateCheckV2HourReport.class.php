<?php
namespace Business\Statistics;

use \Library\Cache\Cache;
use Library\Constants\Stats\ExecutionCustomConfigKey as ExecutionCustomConfigKeyConstants;

/**
 * 验证报表新版（按小时汇总）
 *
 * <AUTHOR>
 * @date  2018-08-06
 */
class CreateCheckV2HourReport extends CreateReportBase {

    //分销商供应商ID
    protected $_fid = 0;
    //是否分终端入园计入报表版本
    private $_isEnterIntoReportRule = false;

    protected  $_reportResultError = '';
    protected  $_reportResultSuccess = '';

    public function __construct($logPath,$date='') {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'check_v2_hour:start');
        self::buildReportResult($date);
        parent::__construct();
    }

    public function buildReportResult($date){
        $today = $date?? date('Y-m-d');
        $this->_reportResultError   = "report_result:check_v2_hour:{$today}:error";
        $this->_reportResultSuccess   = "report_result:check_v2_hour:{$today}:success";
    }

    public function getReportResultKey(){
        return [$this->_reportResultError,$this->_reportResultSuccess];
    }
    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function runTask($date, $fid = 0, $separeTimeType = 0)
    {
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        $this->_fid = $fid;
        //分终端数据使用入园计入报表版本
        $this->_isEnterIntoReportRule = strtotime(self::ENTER_INTO_REPORT_VERSION_DATE) < strtotime($date) ? true : false;

        //验证报表单独跑某个商家的数据有问题，目前订单相关表捞不出某个商家某天的验证订单数据：当一笔订单多张票在不同日期验证时
        if ($fid) {
            return ['code' => 403, 'msg' => '暂不支持'];
        }

        $code = 200;
        $msg  = '';

        try {

            $date     = date('Y-m-d', strtotime($date));
            $intDate  = date('Ymd', strtotime($date));

            $hourList       = [];
            $clearDateList  = [];

            for ($i = 0; $i <= 23; $i++) {
                $hour = $i < 10 ? "0{$i}" : $i;
                $hourList[] = [$date . " {$hour}:00:00", $date . " {$hour}:59:59"];
                $clearDateList[] = $intDate . $hour;
            }

            // if ($separeTimeType) {
            //     $hourList = $this->_getDateHourForHoliday($date);
            // }
            $hourList = $this->_getExecutionTimeSegments($date, ExecutionCustomConfigKeyConstants::HOUR_KEY);

            $hourNum = count($hourList);
            $maxNum  = 6;

            $this->_clearPreHourData($clearDateList, 'checked_v2_hour', $this->_fid);
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $times = ceil($hourNum / $maxNum);

            for ($i = 0; $i < $times; $i++) {
                sleep(1);
                $handleHourList = array_slice($hourList, $i * $maxNum, $maxNum);
                $procNum = count($handleHourList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, $procNum, 1);
                $task->run($handleHourList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }
            $cache = \Library\Cache\RedisCache::Connect('master');
            if(!$cache->exists($this->_reportResultError)){
                $cache->sAdd($this->_reportResultSuccess, 'checked_v2_hour');
                $cache->expire($this->_reportResultSuccess, 60*60*24*7);
            }
            $cache->close();

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "checked_v2_hour:end:{$msg}");
    }

    /**
     * 子进程脚本
     */
    public function runWorker($hourList)
    {
        if (is_array($hourList)) {
            $hourList = array_shift($hourList);
        }

        $pid   = getmypid();
        $cache = \Library\Cache\RedisCache::Connect('master');

        $cacheKey   = 'statis_report:create_data:check_v2_hour';
        $cacheValue = "$pid:{$hourList[0]}-{$hourList[1]}";

        $cache->sAdd($cacheKey, $cacheValue);
        $cache->expire($cacheKey, 3600 * 12);

        $code = 200;
        $msg  = '';

        try {
            $this->runCheckV2HourWorker($hourList);
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str  = 'checked_v2_hour:';
        $str .= $hourList[0] . ' - ' . $hourList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;
        if($code != 200){
            $cache->sAdd($this->_reportResultError, $cacheValue);
            $cache->expire($this->_reportResultError, 60*60*24*7);
        }
        pft_log($this->_logPath, $str);
        $code == 200 && $cache->sRem($cacheKey, $cacheValue);
        $cache->close();
    }

    private function runCheckV2HourWorker($date) {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) ||
            (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24 )) {
            throw new \Exception('时间格式错误', 403);
        }

        $dayHour = date('Ymd', strtotime($date[0])) . date('H', strtotime($date[0]));

        $total     = $this->_getTrackTotal($date, 'checked_v2');
        $totalPage = ceil($total / $this->_selectSize);

        $checkedData   = [];
        //站点
        $siteIdArr     = [];
        //售后编号
        $afterSaleCodeArr = [];

        //订单退票手续费对应的track_id
        $cancelTrackData = [];

        //分页获取数据
        for ($page = 1; $page <= $totalPage; $page++) {
            $list = $this->_getTrackList($date, 'checked_v2', $page, $this->_selectSize);
            //如果查询出错了,直接返回错误
            if ($list === false) {
                throw new \Exception("查询出错");
            }

            foreach ($list as $item) {
                $orderNum      = strval($item['ordernum']);
                $tmpExtContent = json_decode($item['ext_content'], true);
                $needWriteNum  = $tmpExtContent['need_write_num'] ?? 0 ;

                //售后信息
                $afterSaleNum         = $tmpExtContent['afterSaleNum'] ?? 0;
                $afterSaleCode        = $tmpExtContent['afterSaleCode'] ?? '';

                if ($this->_isEnterIntoReportRule) {
                    //分终端已入园后的到期自动验证及验证记录需要过滤
                    if (5 == $item['action'] && in_array($item['source'], [53, 54, 32, 85])) {
                        continue;
                    }

                    if (33 == $item['action'] && !in_array($item['source'], [32, 85])) {
                        continue;
                    }
                } else {

                    if (5 == $item['action'] && $item['source'] == 54) {
                        continue;
                    }

                    if (33 == $item['action']) {
                        continue;
                    }
                }

                //累加数据  验证 完结 撤销撤改
                if (isset($checkedData[$orderNum])) {
                    if (in_array($item['action'], [5])) {
                        $checkNum = $needWriteNum ? $needWriteNum : $item['tnum'];
                        $checkedData[$orderNum]['check'] += $checkNum;
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    if (in_array($item['action'], [17])) {

                        //完结数为0时 可能是因为分终端验证没有全部验证的原因
                        if ($item['tnum'] == 0) {
                            $finishNum = $this->branchTerminalFinshNum($orderNum);
                            $checkedData[$orderNum]['finish'] += $finishNum;
                        } else {
                            $checkedData[$orderNum]['finish'] += $item['tnum'];
                        }
                    }

                    if (in_array($item['action'], [6,7])) {
                        $checkedData[$orderNum]['revoke'] += $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_revoke_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //分终端入园计入报表
                    if ($this->_isEnterIntoReportRule && in_array($item['action'], [33]) && in_array($item['source'], [32, 85])) {
                        $checkedData[$orderNum]['check'] += $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //售后信息
                    if (in_array($item['action'], [38])) {
                        //售后数量
                        $checkedData[$orderNum]['after_sale'] += $afterSaleNum;
                    }

                } else {

                    if (in_array($item['action'], [5])) {
                        $checkNum = $needWriteNum ? $needWriteNum : $item['tnum'];
                        $checkedData[$orderNum]['check']  = $checkNum;
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                        //取订单的站点
                        $siteIdArr[$orderNum] = $item['SalerID'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    if (in_array($item['action'], [17])) {

                        //完结数为0时 可能是因为分终端验证没有全部验证的原因
                        if ($item['tnum'] == 0) {
                            $finishNum = $this->branchTerminalFinshNum($orderNum);
                            $checkedData[$orderNum]['finish'] += $finishNum;
                        } else {
                            $checkedData[$orderNum]['finish'] += $item['tnum'];
                        }
                        //   $checkedData[$orderNum]['finish'] = $item['tnum'];
                        $checkedData[$orderNum]['check']  = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                    }

                    if (in_array($item['action'], [6, 7])) {
                        $checkedData[$orderNum]['check']  = 0;
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_revoke_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }

                    }

                    //分终端入园计入报表
                    if ($this->_isEnterIntoReportRule && in_array($item['action'], [33]) && in_array($item['source'], [32, 85])) {
                        $checkedData[$orderNum]['check']  = $item['tnum'];
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                        //取订单的站点
                        $siteIdArr[$orderNum] = $item['SalerID'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //售后信息
                    if (in_array($item['action'], [38])) {
                        $checkedData[$orderNum]['check']  = 0;
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                        //售后数量
                        $checkedData[$orderNum]['after_sale'] = $afterSaleNum;
                    }
                }

                $operMember = $item['oper_member'];

                //记录订单不同操作的操作人员及操作票数
                if (isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember])) {
                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['tnum'] += $item['tnum'];

                } else {
                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['tnum'] = $item['tnum'];

                }

                //记录售后编号的操作情况
                if (in_array($item['action'], [38]) && !empty($afterSaleCode)) {
                    if (isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode])) {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode]['after_sale_num'] += $afterSaleNum;

                    } else {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode]['after_sale_num'] = $afterSaleNum;
                    }

                    $afterSaleCodeArr[] = $afterSaleCode;
                }

                //手续费操作id记录
                if (in_array($item['action'], [6, 7])) {

                    //生成统计key，用来区分汇总后track_id
                    $checkedDataKey = $this->generateKeywordString(['oper_member', $item['action'], $operMember]);

                    if (!isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'])) {
                        //手续费操作id记录
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'] = [];
                        $cancelTrackData[$orderNum][$checkedDataKey] = [];
                    }

                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'][] = intval($item['id']);
                    $cancelTrackData[$orderNum][$checkedDataKey][] = intval($item['id']);
                }
            }
        }

        if (isset($list)) {
            unset($list);
        }

        //分页处理数据
        $res       = true;
        $total     = count($checkedData);
        $totalPage = ceil($total / $this->_pieceNum);

        for ($i = 0; $i < $totalPage; $i++) {
            //套票子票的订单集合
            $packOrderArr = [];
            //套票子票的订单信息集合
            $packOrderInfo = [];

            $queryIds = array_slice($checkedData, $i * $this->_pieceNum, $this->_pieceNum, true);

            //通过订单ID获取订单信息
            $orderIdArr = array_keys($queryIds, true);

            //获取退票手续费的数据
            $cancelServiceArr = [];
            if (!empty($cancelTrackData)) {
                $cancelServiceArr = $this->getCancelServiceByTrackIdGroup($this->matchArrayKeys($cancelTrackData, $orderIdArr));
            }

            //获取主票信息
            $packInfo   = $this->_getPackInfo($orderIdArr);
            $mainOrder  = [];
            //包含主票子票的订单号集合
            $allOrder   = [];
            foreach ($packInfo as $key => $item) {
                //取出所有主订单号
                if ($item == 1) {
                    //主订单
                    $mainOrder[] = $key;
                    array_push($allOrder, (string)$key);
                } else {
                    $mainOrder[] = $item;
                    array_push($allOrder, (string)$key);
                    array_push($allOrder, (string)$item);
                }
            }

            //主票订单非当天下单
            $notTodayPackMainOrder = $this->_filterPackMainOrderNotToday($mainOrder, $date[0], 5, true);

            $allOrder = array_merge($allOrder, $orderIdArr);
            $res        = $this->_getOrderInfo($allOrder);

            //每个订单号对应的主订单号 如果是主订单 则自己和自己对应
            $orderList  = [];
            $tmpUserArr = [];
            $tidArr     = [];
            $mainTid    = [];

            //套票主票订单信息
            $packMainOrderList       = [];
            //套票主票用户id
            $packMainOrderTmpUserArr = [];

            foreach ($res as $item) {

                $orderNum = $item['ordernum'];
                //订单号对应的tid
                $tidArr[$orderNum] = $item['tid'];
                //获取套票主票对应的tid
                if (in_array($orderNum, $mainOrder)) {
                    $mainTid[] = $item['tid'];

                    $packMainOrderList[$orderNum] = $item;
                }

                if (in_array($orderNum, $orderIdArr)) {

                    if (empty($queryIds[$orderNum]['check']) && empty($queryIds[$orderNum]['finish']) && empty($queryIds[$orderNum]['revoke']) && empty($queryIds[$orderNum]['after_sale'])) {
                        //由于分终端的特殊性 如果没有验证票数和完结票数 过滤掉此订单
                        unset($queryIds[$orderNum]);
                        continue;
                    }

                    unset($item['ordernum']);
                    $orderList[$orderNum] = $item;

                    if ($item['ordermode'] == 11) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 43) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 18) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 15) {
                        $tmpUserArr[] = $item['member'];
                    }
                }

                //补全操作的套票主票为非当日套票主票需要的散客信息
                if (in_array($orderNum, $notTodayPackMainOrder)) {
                    if (in_array($item['ordermode'], [11, 43, 18,15])) {
                        isset($item['member']) && $packMainOrderTmpUserArr[] = $item['member'];
                    }
                }
            }

            unset($res);

            //套票主订单分销链
            $packMainOrderChain = [];
            //用户信息，用来判断散客
            $packMainOrderSankeList = [];
            if (!empty($notTodayPackMainOrder)) {

                //分销链查询
                $packMainOrderChainRes = \Business\JavaApi\Order\Query\Container::query('OrderAidsSplitQueryService',
                    'getOrderDistributionChain', [$notTodayPackMainOrder]);
                if ($packMainOrderChainRes['code'] != 200 || empty($packMainOrderChainRes['data'])) {
                    pft_log($this->_logPath, json_encode(['验证套票主票分销链获取失败', $notTodayPackMainOrder, $packMainOrderChainRes['msg'] ?? ''], JSON_UNESCAPED_UNICODE));
                }

                $packMainOrderChain = is_array($packMainOrderChainRes['data']) ? $packMainOrderChainRes['data'] : [];

                //用户信息，用来判断散客
                if (!empty($packMainOrderTmpUserArr)) {
                    $packMainOrderSankeList  = $this->_getSankeList(array_unique($packMainOrderTmpUserArr));
                }
            }

            //获取优惠信息
            $couponList = [];
            $onSaleRecord = $this->_getOnSaleRecord($orderIdArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取下单的积分、优惠券信息
            $pointAndCouponInfo = $this->_getOrderPointsRecord($orderIdArr);
            //获取订单对应积分、优惠券优惠下标明细
            $discountIdxList = $this->_getOrderPointsIdxRecord($orderIdArr);

            $pointDetail = [
                'pointAndCouponInfo' => $pointAndCouponInfo,
                'discountIdxList'    => $discountIdxList,
            ];

            //获取订单详情额外信息
            $orderDetailRes  = $this->_getOrderDetailByOrderNum(array_merge(array_keys($packMainOrderList), $orderIdArr));
            //获取子商户信息
            $orderSubSidMap = $orderDetailRes['orderSubSidMap'] ?? [];

            //微商城用户获取用户信息。用来判断是不是散客
            $sankeList = [];
            if ($tmpUserArr) {
                $tmpUserArr = array_unique($tmpUserArr);
                $sankeList  = $this->_getSankeList($tmpUserArr);
            }

            //获取分销链数据
            $chainList = $this->_getBuyChain($orderIdArr);

            //取出主票对应的发布人
            $ticketApplyInfo = $this->_getTicketApplyInfo($mainTid);

            //获取票的顶级供应商
            $ticketApplyData = $this->_getTicketApplyInfo(array_column($orderList, 'tid'));

            //获取售后相关金额
            $afterSaleCodeArr = array_values(array_unique($afterSaleCodeArr));
            $afterSaleOrderMap = $this->queryTransactiondetailMap($afterSaleCodeArr);

            if ($chainList === false) {
                throw new \Exception("获取分销链失败");
            }

            if (count($chainList) == 0) {
                continue;
            }

            //取出套票子票订单的sale_money
            foreach ($chainList as $item) {
                if (in_array($item['orderid'], $packOrderArr)) {
                    $packOrderInfo[$item['orderid']][$item['buyerid']] = $item['sale_money'];
                }
            }

            //是不是最后一个分片
            $isLastSlice = ($i == ($totalPage - 1)) ? true : false;

            $res = $this->_handleCheckedV2Data($dayHour, $chainList, $queryIds, $orderList, $cancelServiceArr, $isLastSlice,
                $sankeList, $packInfo, $tidArr, $ticketApplyInfo, $siteIdArr, $couponList, $pointDetail, $notTodayPackMainOrder,
                $packMainOrderList, $packMainOrderChain, $packMainOrderSankeList, $orderSubSidMap, $ticketApplyData,$afterSaleOrderMap);

            if (empty($res)) {
                throw new \Exception("处理数据失败");
            }
        }
    }

    private function _handleCheckedV2Data($dayHour, $chainList, $checkedData, $orderList, $cancelInfo, $isLastSlice, $sankeList, $packInfo, $tidArr, $ticketApplyInfo, $siteIdArr, $couponList, $pointDetail, $notTodayPackMainOrder = [], $packMainOrderList = [], $packMainOrderChain = [], $packMainOrderSankeList = [], $orderSubSidMap = [], $ticketApplyData = [], $afterSaleOrderMap = [])
    {
        //首先获取之前的缓存数据
        $splitList = $this->_cacheDataArr($dayHour, 'checked_v2_hour', 'get');
        $statisBiz = new \Business\Statistics\statistics();

        $lastSettlementDiscountInfo = [];
        for ($i = 0; $i < count($chainList); $i++) {
            $item = $chainList[$i];

            $orderNum = $item['orderid'];

            if (!array_key_exists($orderNum, $checkedData)) {
                continue;
            }

            if (!array_key_exists($orderNum, $orderList)) {
                continue;
            }

            $orderInfo   = $orderList[$orderNum];
            $checkedInfo = $checkedData[$orderNum];

            $fid      = $item['sellerid'];
            $sellerId = $item['buyerid'];

            //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
            $oldSeller = $item['buyerid'];
            $level     = $item['level'];
            $saleMoney = $item['sale_money'];

            //购买价
            $costMoney = intval($item['cost_money']);

            $paymode    = $item['pmode'];

            $ticketNum  = $checkedInfo['check'];
            $finishNum  = $checkedInfo['finish'];
            $revokeNum  = $checkedInfo['revoke'];
            //售后数量
            $afterSaleNum  = $checkedInfo['after_sale'] ?? 0;

            //积分、优惠券下单信息
            $pointAndCouponInfo = $pointDetail['pointAndCouponInfo'];
            //积分、优惠券操作下标信息
            $discountIdxList = $pointDetail['discountIdxList'] ?? [];
            //积分、优惠券验证操作下标数据
            $pointCheckIdxArr = $checkedInfo['point_check_idx'] ?? [];
            //积分、优惠券撤销操作下标数据
            $pointRevokeIdxArr = $checkedInfo['point_revoke_idx'] ?? [];

            //汇总积分、优惠券取消修改撤销撤改优惠金额
            $pointAmountDetail = $this->_handlePointsIdxAmount($orderNum, $discountIdxList, [], $pointRevokeIdxArr, $pointCheckIdxArr, true);

            //过滤掉都是0的数据
            if ($ticketNum == 0 && $finishNum == 0 && $revokeNum == 0 && $afterSaleNum == 0) {
                continue;
            }

            //操作人员信息
            $operMemberInfo = isset($checkedInfo['oper_member']) && $checkedInfo['oper_member'] ? $checkedInfo['oper_member'] : [];

            //站点ID
            $siteId  = isset($siteIdArr[$orderNum]) ? $siteIdArr[$orderNum] : 0;
            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];
            $channel = $orderInfo['ordermode'];

            //子商户id
            $subMerchantId = $orderSubSidMap[$orderNum] ?? 0;

            //存在子商户id，并且非顶级供应商，不展示
            if ($subMerchantId) {
                //获取下订单票的顶级供应商
                $orderTicketApplyDid = $ticketApplyData[$tid] ?? 0;
                if ($orderTicketApplyDid != $fid) {
                    //非顶级供应商，子商户不可见
                    $subMerchantId = 0;
                }
            }

            //验证时间
            $dTime = date('Ymd', strtotime($orderInfo['dtime']));
            //预订时间
            $orderTime = date('Ymd', strtotime($orderInfo['ordertime']));

            if ($dTime == $orderTime) {
                $isCheckSameDay = 1;
            } else {
                $isCheckSameDay = 2;
            }

            //计算优惠券使用金额
            if (isset($couponList[$orderNum][$fid][$sellerId])) {
                //优惠金额
                $eMoney = $couponList[$orderNum][$fid][$sellerId];
            } else {
                $eMoney = 0;
            }

            $discountDetail          = self::handleDiscountInfo($pointAndCouponInfo, [], $orderNum, $fid, $sellerId, $oldSeller);
            $discountMoney           = $discountDetail[self::DISCOUNT]['order_amount'];
            $settlementDiscountMoney = $discountDetail[self::SETTLEMENT_DISCOUNT]['order_amount'];

            //通过渠道将散客进行归类
            if (in_array($level, [-1, 0])) {
                $tmp = $this->_getSankeId($sellerId, $fid, $channel, $sankeList);

                if ($tmp !== false) {
                    //最后一级就是散客的情况(自供自销也算在这里面)
                    if ($level == 0) {
                        $level = 1;
                    } else {
                        $level = -3;
                    }
                    $sellerId = $tmp;

                    //预售券兑换的订单，末级销售金额为0
                    if ($this->_checkSaleMoneyZero($channel, $paymode) && !$this->_needAddSplit($level, $sellerId, $fid)) {
                        $saleMoney = 0;
                    }
                }
            }

            $mainTid  = 0;
            $mainType = '';
            if (isset($packInfo[$orderNum]) && $packInfo[$orderNum] == 1) {
                //套票主票
                $mainTid = 0;
            } elseif (isset($packInfo[$orderNum])) {
                //主票的订单号对应的票类ID
                $mainTid  = $tidArr[$packInfo[$orderNum]];
                $mainType = $packMainOrderList[$packInfo[$orderNum]]['product_type'] ?? '';
            }

            //特殊处理
            $splitTmp = $this->_needAddSplit($level, $sellerId, $fid);

            if ($splitTmp) {
                //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                $splitData = [
                    'orderid'    => $orderNum,
                    'sellerid'   => $sellerId,
                    'buyerid'    => $this->_getChannelSanke($channel),
                    'level'      => -3, //非0/-1
                    'cost_money' => $saleMoney,
                    'sale_money' => $needMoney, //默认使用零售价的钱
                    'pmode'      => $paymode,
                ];

                //如果需要补全一条报表记录，且是预售券兑换订单，末级销售金额为0
                if ($this->_checkSaleMoneyZero($channel, $paymode)) {
                    $splitData['sale_money'] = 0;
                }

                $chainList[] = $splitData;
            }

            //针对单个供应商分销商时
            if ($this->_fid && $item['sellerid'] != $this->_fid) {
                continue;
            }

            //黑名单判断必须放在链路补全后，避免数据缺失
            if ($this->isInBlacklist($fid)) {
                continue;
            }

            //由于系统一开始的问题 套票子票在整条分销链中全部记为套票子票下单 这是错误的
            //只有被将该票打包成套票的供应商出售这笔订单的销售渠道会记为套票子票下单
            //在这里做个判断 如果子票的主票的发布人 是当前处理的分销链层级的卖家 则跳过（预订报表过滤子票下单）
            if (!empty($mainTid)) {
                //----1. 主票订单号的票类ID的发布人 ApplyDid
                $applyDid = $ticketApplyInfo[$mainTid];
                //----2. 如果主票的发布人是当前处理的分销链的层级的卖票的人sell_id 跳过
                if ($applyDid != $fid) {
                    $mainTid  = 0;
                    $mainType = '';
                }

                //套票打包方，主票下单不当天需要补全主票维度，指标全部为0
                if ($applyDid == $fid && in_array($packInfo[$orderNum], $notTodayPackMainOrder)) {
                    $mainOrderNum = $packInfo[$orderNum];
                    list($mSellerId, $mPid, $mChannel, $mPaymode, $mOperMember, $mSiteId, $mLid, $mTid, $mLevel, $mSubMerchantId) = $this->_packMainOrderDecode($fid,
                        $mainOrderNum, $packMainOrderList, $packMainOrderChain, $packMainOrderSankeList, $orderSubSidMap);
                    $mMainTid  = 0;
                    $mMainType = '';
                    if ($mLid && $mPid && $mTid  && !is_null($mSellerId) && !is_null($mLevel) && !is_null($mPaymode)) {
                        //记录下补全的订单号
                       if (!isset($splitList[$fid][$mSellerId][$mPid][$mMainTid][$mMainType][$mChannel][$mPaymode][$mSubMerchantId][$mOperMember][$isCheckSameDay][$mSiteId])) {
                            $splitList[$fid][$mSellerId][$mPid][$mMainTid][$mMainType][$mChannel][$mPaymode][$mSubMerchantId][$mOperMember][$isCheckSameDay][$mSiteId] = [
                                'lid'                     => $mLid,
                                'tid'                     => $mTid,
                                'lvl'                     => $mLevel,
                                'order_num'               => 0,
                                'order_ticket'            => 0,
                                'finish_num'              => 0,
                                'finish_ticket'           => 0,
                                'revoke_num'              => 0,
                                'revoke_ticket'           => 0,
                                'cost_money'              => 0,
                                'sale_money'              => 0,
                                'finish_cost_money'       => 0,
                                'finish_sale_money'       => 0,
                                'revoke_cost_money'       => 0,
                                'revoke_sale_money'       => 0,
                                'service_money'           => 0,
                                'after_sale_ticket_num'   => 0,
                                'after_sale_refund_money' => 0,
                                'after_sale_income_money' => 0,
                                'orders_info'             => [[$mainOrderNum, 0]],
                                'finish_orders_info'      => [],
                                'revoke_orders_info'      => [],
                                'after_sale_info'         => [],
                            ];
                        } else {
                            $splitList[$fid][$mSellerId][$mPid][$mMainTid][$mMainType][$mChannel][$mPaymode][$mSubMerchantId][$mOperMember][$isCheckSameDay][$mSiteId]['orders_info'][] = [$mainOrderNum, 0];
                        }
                    }
                }
            }

            //去除退票手续费
            $orderCancelInfo = $cancelInfo[$orderNum] ?? [];

            if (!isset($splitList[$fid])) {
                $splitList[$fid] = [];
            }

            if (!isset($splitList[$fid][$sellerId])) {
                $splitList[$fid][$sellerId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid])) {
                $splitList[$fid][$sellerId][$pid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid])) {
                $splitList[$fid][$sellerId][$pid][$mainTid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId] = [];
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {
                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember] = [];
                    }
                }
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {
                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay] = [];
                    }
                }
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {
                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId] = [
                            'lid'                     => $lid,
                            'tid'                     => $tid,
                            'lvl'                     => $level,
                            'order_num'               => 0,
                            'order_ticket'            => 0,
                            'finish_num'              => 0,
                            'finish_ticket'           => 0,
                            'revoke_num'              => 0,
                            'revoke_ticket'           => 0,
                            'cost_money'              => 0,
                            'sale_money'              => 0,
                            'finish_cost_money'       => 0,
                            'finish_sale_money'       => 0,
                            'revoke_cost_money'       => 0,
                            'revoke_sale_money'       => 0,
                            'service_money'           => 0,
                            'after_sale_ticket_num'   => 0,
                            'after_sale_refund_money' => 0,
                            'after_sale_income_money' => 0,
                            'orders_info'             => [],
                            'finish_orders_info'      => [],
                            'revoke_orders_info'      => [],
                            'after_sale_info'         => [],
                        ];
                    }
                }
            }

            //整合数据
            if ($ticketNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //验证
                    if (!in_array($action, [5, 33])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];

                        //上一级的卖出优惠金额 是下一级的买入优惠金额
                        $buySettlementCheck  = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$operTicketNum]['checkSettlementPoints'] ?? 0;

                        $actionDiscount        = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operMember, 'check_amount');
                        $checkPoints           = $actionDiscount[self::DISCOUNT];
                        $checkSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$operTicketNum]['checkSettlementPoints'] = $this->getCostDiscountMoney($checkPoints, $checkSettlementPoints);

                        //支付数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['order_num'] += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['orders_info'][] = [
                            $orderNum,
                            $operTicketNum,
                            $checkPoints,
                            $checkSettlementPoints ?: $buySettlementCheck
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['sale_money'] += ($saleMoney * $operTicketNum - $eMoney - $this->getSaleDiscountMoney($checkPoints, $checkSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['cost_money'] += $costMoney * $operTicketNum - $buySettlementCheck;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['order_ticket'] += $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_ticket'] += 0;
                    }
                }
            }

            if ($finishNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //完结
                    if (!in_array($action, [17])) {
                        continue;
                    }

                    $buySettlementFinish = $lastSettlementDiscountInfo[$orderNum]['finishSettlementPoints'] ?? 0;

                    $actionDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $action, $discountMoney, $settlementDiscountMoney, 0, 'finish_amount');
                    $finishPoints           = $actionDiscount[self::DISCOUNT];
                    $finishSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                    $lastSettlementDiscountInfo[$orderNum]['finishSettlementPoints'] = $this->getCostDiscountMoney($finishPoints, $finishSettlementPoints);

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;
                        $finishNum  = $ticketInfo['tnum'];

                        //取消数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_num'] += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_sale_money'] += ($saleMoney * $finishNum - $eMoney - $this->getSaleDiscountMoney($finishPoints, $finishSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_orders_info'][] = [
                            $orderNum,
                            $finishNum,
                            $finishPoints,
                            $finishSettlementPoints ?: $buySettlementFinish
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_cost_money'] += $costMoney * $finishNum - $buySettlementFinish;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['order_ticket']  += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_ticket'] += $finishNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_ticket'] += 0;
                    }
                }
            }

            if ($revokeNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //撤改
                    if (!in_array($action, [6, 7])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;
                        $revokeNum  = $ticketInfo['tnum'];
                        $trackIds = $ticketInfo['track_ids'] ?? [];

                        //手续费
                        $serviceMoney = $this->mateCancelServiceMoneyByTrackIdGroup($fid, $trackIds, $orderCancelInfo);

                        $buySettlementRevoke = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$revokeNum]['revokeSettlementPoints'] ?? 0;

                        $actionDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operMember, 'revoke_amount');
                        $revokePoints           = $actionDiscount[self::DISCOUNT];
                        $revokeSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$revokeNum]['revokeSettlementPoints'] = $this->getCostDiscountMoney($revokePoints, $revokeSettlementPoints);

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_num'] += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_sale_money'] += ($saleMoney * $revokeNum - $eMoney - $this->getSaleDiscountMoney($revokePoints, $revokeSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_orders_info'][] = [
                            $orderNum,
                            $revokeNum,
                            $revokePoints,
                            $revokeSettlementPoints ?: $buySettlementRevoke
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_cost_money'] += $costMoney * $revokeNum - $buySettlementRevoke;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['order_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['finish_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['revoke_ticket'] += $revokeNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['service_money'] += $serviceMoney;
                    }
                }
            }

            //售后数据
            if ($afterSaleNum > 0) {
                foreach ($operMemberInfo as $action => $info) {

                    if (!in_array($action, [38])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;

                        $afterSaleInfo = $ticketInfo['after_sale_info'] ?? [];

                        foreach ($afterSaleInfo as $afterSaleCode => $afterInfo) {
                            $afterSaleNum = $afterInfo['after_sale_num'] ?? 0;

                            //售后退回金额
                            $afterSaleRefundMoney = $afterSaleOrderMap[$orderNum][$afterSaleCode]['sellerId'][$fid] ?? 0;
                            //售后收入单价
                            $afterSaleIncomeMoney = $afterSaleOrderMap[$orderNum][$afterSaleCode]['buyerId'][$fid] ?? 0;

                            //售后订单明细 0.订单号 1.售后数量 2.编号 3.退回金额 4.收入金额
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['after_sale_info'][]       = [
                                $orderNum,
                                $afterSaleNum,
                                (string)$afterSaleCode,
                                $afterSaleRefundMoney,
                                $afterSaleIncomeMoney,
                            ];
                            //处理相同维度指标累加
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['after_sale_refund_money'] += $afterSaleRefundMoney;
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['after_sale_income_money'] += $afterSaleIncomeMoney;
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$isCheckSameDay][$siteId]['after_sale_ticket_num']   += $afterSaleNum;
                        }
                    }
                }
            }
        }

        //如果不是最后一个分片，就先将数据写入缓存文件
        if (!$isLastSlice) {
            $this->_cacheDataArr($dayHour, 'checked_v2_hour', 'set', $splitList);
            unset($splitList);
            return true;
        } else {
            //如果是最后一个，就将之前的数据清除
            $this->_cacheDataArr($dayHour, 'checked_v2_hour', 'clear');
        }

        $updateTime = time();
        //分批插入数据
        $insertData = [];
        $lastId = true;

        foreach ($splitList as $fid => $fidData) {
            foreach ($fidData as $sellerId => $sellerData) {
                foreach ($sellerData as $pid => $pidData) {
                    foreach ($pidData as $mainTid => $mainTidData) {
                        foreach ($mainTidData as $mainType => $mainTypeData) {
                            foreach ($mainTypeData as $channel => $channelData) {
                                foreach ($channelData as $paymode => $paymodeData) {
                                    foreach ($paymodeData as $subMerchantId => $subMerchantData) {
                                        foreach ($subMerchantData as $operMember => $operData) {
                                            foreach ($operData as $isCheckedToday => $isCheckedData) {
                                                foreach ($isCheckedData as $siteId => $data) {

                                                    if ($data['revoke_ticket'] == 0 && $data['finish_ticket'] == 0 && $data['order_ticket'] == 0 && $data['after_sale_ticket_num'] == 0) {
                                                        //这边放行补全的套票主票信息
                                                        if (!$this->_checkInInfosOrderNum($data['orders_info'],
                                                            $notTodayPackMainOrder)) {
                                                            continue;
                                                        }
                                                    }
                                                    //入库数据处理
                                                    $tmp                       = $data;
                                                    $tmp['fid']                = $fid;
                                                    $tmp['pay_way']            = $paymode;
                                                    $tmp['orders_info']        = json_encode($tmp['orders_info']);
                                                    $tmp['finish_orders_info'] = json_encode($tmp['finish_orders_info']);
                                                    $tmp['revoke_orders_info'] = json_encode($tmp['revoke_orders_info']);
                                                    $tmp['after_sale_info']    = json_encode($tmp['after_sale_info']);
                                                    $tmp['reseller_id']        = $sellerId;
                                                    $tmp['pid']                = $pid;
                                                    $tmp['date_hour']          = $dayHour;
                                                    $tmp['channel']            = $channel;
                                                    $tmp['operate_id']         = $operMember;
                                                    $tmp['today_check']        = $isCheckedToday;
                                                    $tmp['update_time']        = $updateTime;
                                                    $tmp['site_id']            = $siteId;
                                                    $tmp['main_tid']           = $mainTid;
                                                    $tmp['sub_merchant_id']    = $subMerchantId;
                                                    $tmp['main_type']          = $mainType;

                                                    $insertData[] = $tmp;

                                                    if (count($insertData) >= $this->_insertLimit) {
                                                        //达到条数就插入数据
                                                        $lastId = $this->_insertDataArr($insertData, 'checked_v2_hour');
                                                        //初始化
                                                        $insertData = [];
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //将没有插入的数据最后插入
        if ($insertData) {
            $lastId = $this->_insertDataArr($insertData, 'checked_v2_hour');
        }

        //返回
        return $lastId;
    }


    /**
     * 获取分终端验证完结订单数（因为在订单追踪表中分终端验证未全部验证时完结数量为0）
     *
     * @param $order
     * @param $field
     * @param array $extraConf
     * @return array|mixed
     */
    public function branchTerminalFinshNum($orderNum)
    {
        //完结订单数
        $finishNum      = 0 ;
        $lastCheckTrack = $this->_getSubOrderTrackModel()->getTrackList($orderNum, 5, 32, 'left_num');
        $leftNum        = isset($lastCheckTrack['left_num']) ? $lastCheckTrack['left_num'] : 0;

        if ($leftNum > 0) {
            //订单原始信息
            $applyinfo = $this->_getOrderToolModel()->getOrderApplyInfo([$orderNum], 'origin_num, refund_num');
            if (is_array($applyinfo) && !empty($applyinfo)) {
                $finishNum = $applyinfo[0]['origin_num'] - $applyinfo[0]['refund_num'];
            }
        }

        // 转换字符类型
        $finishNum = (int)$finishNum;

        return $finishNum;
    }
}