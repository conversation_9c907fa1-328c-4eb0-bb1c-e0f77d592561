<?php

namespace Business\Statistics;

use Business\Report\MinuteReport as MinuteReportBiz;
use \Library\Cache\Cache;
use Library\Constants\Stats\ExecutionCustomConfigKey as ExecutionCustomConfigKeyConstants;
use Model\Report\ReportRelation as ReportRelationModel;

/**
 * 分钟验证报表新版
 *
 * <AUTHOR>
 * @date   2018-08-06
 */
class CreateCheckV2MinuteReport extends CreateReportBase
{

    //允许的用户
    protected $_allowFids = MinuteReportBiz::ALLOW_MEMBER_IDS;

    //分销商供应商ID
    protected $_fid = 0;

    public function __construct($logPath)
    {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'minute_checked_v2:start');
        parent::__construct();
    }

    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function runTask($date, $fid = 0)
    {
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        if ($fid) {
            $this->_fid       = $fid;
            $this->_allowFids = [$fid];
        }

        $msg  = '';

        try {

            $maxNum   = 6;
            // $dateList = $this->_getDateMinuteList($date);
            $dateList = $this->_getExecutionTimeSegments($date, ExecutionCustomConfigKeyConstants::FIVE_MINUTE_KEY);
            $dateNum  = count($dateList);
            $this->_clearPreData($date, 'minute_checked_v2', $this->_fid);
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $times = ceil($dateNum / $maxNum);

            for ($i = 0; $i < $times; $i++) {
                sleep(1);
                $handleDateList = array_slice($dateList, $i * $maxNum, $maxNum);
                $procNum        = count($handleDateList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, $procNum, 1);
                $task->run($handleDateList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "minute_checked_v2:end:{$msg}");
    }

    /**
     * 子进程脚本
     */
    public function runWorker($dateList)
    {
        if (is_array($dateList)) {
            $dateList = array_shift($dateList);
        }

        $code = 200;
        $msg  = '';

        try {
            $page = 1;
            $size = 100;

            //处理数据
            $reportRelationModel = new ReportRelationModel();

            do {
                $result = $reportRelationModel->getListPageBySid($this->_allowFids, $page, $size);
                if (empty($result)) {
                    break;
                }

                //获取门票id并去重
                $tidArr = array_values(array_unique(array_column($result, 'tid')));

                $this->runCheckV2Worker($dateList, $tidArr);

            } while ($page++);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str = 'minute_checked_v2:';
        $str .= $dateList[0] . ' - ' . $dateList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        pft_log($this->_logPath, $str);
    }

    private function runCheckV2Worker($date, $tidArr)
    {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) || (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24)) {
            throw new \Exception('时间格式错误', 403);
        }

        if (!is_numeric($this->_fid)) {
            throw new \Exception('fid格式错误', 403);
        }

        if (empty($tidArr)) {
            throw new \Exception('tid格式错误', 403);
        }

        $dateMinute     = date('YmdHi', strtotime($date[0]));

        $selectSize = 100;

        $total     = $this->getMinuteTrackList($date, $tidArr, true);
        $totalPage = ceil($total / $selectSize);

        $checkedData = [];
        //站点
        $siteIdArr = [];
        //售后编号
        $afterSaleCodeArr = [];

        //订单退票手续费对应的track_id
        $cancelTrackData = [];

        //分页获取数据
        for ($page = 1; $page <= $totalPage; $page++) {
            $list = $this->getMinuteTrackList($date, $tidArr, false, $page, $selectSize);

            foreach ($list as $item) {
                $orderNum      = strval($item['ordernum']);
                $tmpExtContent = json_decode($item['ext_content'], true);
                $needWriteNum  = $tmpExtContent['need_write_num'] ?? 0;

                //售后信息
                $afterSaleNum         = $tmpExtContent['afterSaleNum'] ?? 0;
                $afterSaleCode        = $tmpExtContent['afterSaleCode'] ?? '';

                //分终端已入园后的到期自动验证及验证记录需要过滤
                if (5 == $item['action'] && in_array($item['source'], [53, 54, 32, 85])) {
                    continue;
                }

                if (33 == $item['action'] && !in_array($item['source'], [32, 85])) {
                    continue;
                }

                //累加数据  验证 完结 撤销撤改
                if (isset($checkedData[$orderNum])) {

                    if (in_array($item['action'], [5])) {
                        $checkNum                        = $needWriteNum ? $needWriteNum : $item['tnum'];
                        $checkedData[$orderNum]['check'] += $checkNum;
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    if (in_array($item['action'], [17])) {

                        //完结数为0时 可能是因为分终端验证没有全部验证的原因
                        if ($item['tnum'] == 0) {
                            $finishNum                        = $this->branchTerminalFinshNum($orderNum);
                            $checkedData[$orderNum]['finish'] += $finishNum;
                        } else {
                            $checkedData[$orderNum]['finish'] += $item['tnum'];
                        }
                    }

                    if (in_array($item['action'], [6, 7])) {
                        $checkedData[$orderNum]['revoke'] += $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_revoke_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //分终端入园计入报表
                    if (in_array($item['action'], [33]) && in_array($item['source'], [32, 85])) {
                        $checkedData[$orderNum]['check'] += $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //售后信息
                    if (in_array($item['action'], [38])) {
                        //售后数量
                        $checkedData[$orderNum]['after_sale'] += $afterSaleNum;
                    }

                } else {

                    if (in_array($item['action'], [5])) {

                        $checkNum                         = $needWriteNum ? $needWriteNum : $item['tnum'];
                        $checkedData[$orderNum]['check']  = $checkNum;
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                        //取订单的站点
                        $siteIdArr[$orderNum] = $item['SalerID'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    if (in_array($item['action'], [17])) {

                        //完结数为0时 可能是因为分终端验证没有全部验证的原因
                        if ($item['tnum'] == 0) {
                            $finishNum                        = $this->branchTerminalFinshNum($orderNum);
                            $checkedData[$orderNum]['finish'] += $finishNum;
                        } else {
                            $checkedData[$orderNum]['finish'] += $item['tnum'];
                        }
                        //   $checkedData[$orderNum]['finish'] = $item['tnum'];
                        $checkedData[$orderNum]['check']  = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                    }

                    if (in_array($item['action'], [6, 7])) {
                        $checkedData[$orderNum]['check']  = 0;
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = $item['tnum'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_revoke_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //分终端入园计入报表
                    if (in_array($item['action'], [33]) && in_array($item['source'], [32, 85])) {
                        $checkedData[$orderNum]['check']  = $item['tnum'];
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                        //取订单的站点
                        $siteIdArr[$orderNum] = $item['SalerID'];
                        //本次操作优惠的下标信息
                        if (isset($tmpExtContent['serial_number']) && $tmpExtContent['serial_number']) {
                            $checkedData[$orderNum]['point_check_idx'][$item['oper_member']][$item['action']][] = $tmpExtContent['serial_number'] ?? '';
                        }
                    }

                    //售后信息
                    if (in_array($item['action'], [38])) {
                        $checkedData[$orderNum]['check']  = 0;
                        $checkedData[$orderNum]['finish'] = 0;
                        $checkedData[$orderNum]['revoke'] = 0;
                        //售后数量
                        $checkedData[$orderNum]['after_sale'] = $afterSaleNum;
                    }
                }

                $operMember = $item['oper_member'];

                //记录订单不同操作的操作人员及操作票数
                if (isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember])) {
                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['tnum'] += $item['tnum'];

                } else {
                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['tnum'] = $item['tnum'];

                }

                //记录售后编号的操作情况
                if (in_array($item['action'], [38]) && !empty($afterSaleCode)) {
                    if (isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode])) {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode]['after_sale_num'] += $afterSaleNum;

                    } else {
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['after_sale_info'][$afterSaleCode]['after_sale_num'] = $afterSaleNum;
                    }

                    $afterSaleCodeArr[] = $afterSaleCode;
                }

                //手续费操作id记录
                if (in_array($item['action'], [6, 7])) {

                    //生成统计key，用来区分汇总后track_id
                    $checkedDataKey = $this->generateKeywordString(['oper_member', $item['action'], $operMember]);

                    if (!isset($checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'])) {
                        //手续费操作id记录
                        $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'] = [];
                        $cancelTrackData[$orderNum][$checkedDataKey] = [];
                    }

                    $checkedData[$orderNum]['oper_member'][$item['action']][$operMember]['track_ids'][] = intval($item['id']);
                    $cancelTrackData[$orderNum][$checkedDataKey][] = intval($item['id']);
                }
            }
        }

        if (isset($list)) {
            unset($list);
        }

        //分页处理数据
        $total     = count($checkedData);
        $totalPage = ceil($total / $this->_pieceNum);

        for ($i = 0; $i < $totalPage; $i++) {

            //套票子票的订单集合
            $packOrderArr = [];
            //套票子票的订单信息集合
            $packOrderInfo = [];

            $queryIds = array_slice($checkedData, $i * $this->_pieceNum, $this->_pieceNum, true);

            //通过订单ID获取订单信息
            $orderIdArr = array_keys($queryIds, true);

            //获取退票手续费的数据
            $cancelServiceArr = [];
            if (!empty($cancelTrackData)) {
                $cancelServiceArr = $this->getCancelServiceByTrackIdGroup($this->matchArrayKeys($cancelTrackData, $orderIdArr));
            }

            //获取主票信息
            $packInfo  = $this->_getPackInfo($orderIdArr);
            $mainOrder = [];
            //包含主票子票的订单号集合
            $allOrder = [];
            foreach ($packInfo as $key => $item) {
                //取出所有主订单号
                if ($item == 1) {
                    //主订单
                    $mainOrder[] = $key;
                    array_push($allOrder, (string)$key);
                } else {
                    $mainOrder[] = $item;
                    array_push($allOrder, (string)$key);
                    array_push($allOrder, (string)$item);
                }
            }

            $allOrder = array_merge($allOrder, $orderIdArr);
            $res      = $this->_getOrderInfo($allOrder);
            //每个订单号对应的主订单号 如果是主订单 则自己和自己对应

            $orderList  = [];
            $tmpUserArr = [];
            $tidArr     = [];
            $mainTid    = [];
            //套票主票订单信息
            $packMainOrderList = [];

            foreach ($res as $item) {

                $orderNum = $item['ordernum'];
                //订单号对应的tid
                $tidArr[$orderNum] = $item['tid'];
                //获取套票主票对应的tid
                if (in_array($orderNum, $mainOrder)) {
                    $mainTid[] = $item['tid'];
                    //套票主订单信息
                    $packMainOrderList[$orderNum] = $item;
                }

                if (in_array($orderNum, $orderIdArr)) {
                    if (empty($queryIds[$orderNum]['check']) && empty($queryIds[$orderNum]['finish']) && empty($queryIds[$orderNum]['revoke']) && empty($queryIds[$orderNum]['after_sale'])) {
                        //由于分终端的特殊性 如果没有验证票数和完结票数 过滤掉此订单
                        unset($queryIds[$orderNum]);
                        continue;
                    }

                    unset($item['ordernum']);
                    $orderList[$orderNum] = $item;

                    if ($item['ordermode'] == 11) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 43) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 18) {
                        $tmpUserArr[] = $item['member'];
                    }
                    if ($item['ordermode'] == 15) {
                        $tmpUserArr[] = $item['member'];
                    }
                }
            }

            unset($res);

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord($orderIdArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取下单的积分、优惠券信息
            $pointAndCouponInfo = $this->_getOrderPointsRecord($orderIdArr);
            //获取订单对应积分、优惠券优惠下标明细
            $discountIdxList = $this->_getOrderPointsIdxRecord($orderIdArr);

            $pointDetail = [
                'pointAndCouponInfo' => $pointAndCouponInfo,
                'discountIdxList'    => $discountIdxList,
            ];

            //微商城用户获取用户信息。用来判断是不是散客
            $sankeList = [];
            if ($tmpUserArr) {
                $tmpUserArr = array_unique($tmpUserArr);
                $sankeList  = $this->_getSankeList($tmpUserArr);
            }

            //获取分销链数据
            $chainList = $this->_getBuyChain($orderIdArr);

            //取出主票对应的发布人
            $ticketApplyInfo = $this->_getTicketApplyInfo($mainTid);

            if ($chainList === false) {
                throw new \Exception("获取分销链失败");
            }

            if (count($chainList) == 0) {
                continue;
            }

            //获取售后相关金额
            $afterSaleCodeArr = array_values(array_unique($afterSaleCodeArr));
            $afterSaleOrderMap = $this->queryTransactiondetailMap($afterSaleCodeArr);

            //取出套票子票订单的sale_money
            foreach ($chainList as $item) {
                if (in_array($item['orderid'], $packOrderArr)) {
                    $packOrderInfo[$item['orderid']][$item['buyerid']] = $item['sale_money'];
                }
            }

            //是不是最后一个分片
            $isLastSlice = ($i == ($totalPage - 1)) ? true : false;

            $res = $this->_handleCheckedV2Data($dateMinute, $chainList, $queryIds, $orderList, $cancelServiceArr, $isLastSlice,
                $sankeList, $packInfo, $tidArr, $ticketApplyInfo, $siteIdArr, $couponList, $pointDetail, $packMainOrderList, $afterSaleOrderMap);

            if (empty($res)) {
                throw new \Exception("处理数据失败");
            }
        }
    }

    private function _handleCheckedV2Data($day, $chainList, $checkedData, $orderList, $cancelInfo, $isLastSlice, $sankeList, $packInfo, $tidArr, $ticketApplyInfo, $siteIdArr, $couponList, $pointDetail, $packMainOrderList, $afterSaleOrderMap = [])
    {
        //首先获取之前的缓存数据
        $splitList = $this->_cacheDataArr($day, 'minute_checked_v2', 'get');
        $statisBiz = new \Business\Statistics\statistics();

        $lastSettlementDiscountInfo = [];
        for ($i = 0; $i < count($chainList); $i++) {
            $item = $chainList[$i];

            $orderNum = $item['orderid'];

            if (!array_key_exists($orderNum, $checkedData)) {
                continue;
            }

            if (!array_key_exists($orderNum, $orderList)) {
                continue;
            }

            $orderInfo   = $orderList[$orderNum];
            $checkedInfo = $checkedData[$orderNum];

            $fid      = $item['sellerid'];
            $sellerId = $item['buyerid'];

            //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
            $oldSeller = $item['buyerid'];
            $level     = $item['level'];
            $saleMoney = $item['sale_money'];

            //购买价
            $costMoney = intval($item['cost_money']);

            $paymode = $item['pmode'];

            $ticketNum = $checkedInfo['check'];
            $finishNum = $checkedInfo['finish'];
            $revokeNum = $checkedInfo['revoke'];
            //售后数量
            $afterSaleNum  = $checkedInfo['after_sale'] ?? 0;

            //积分、优惠券下单信息
            $pointAndCouponInfo = $pointDetail['pointAndCouponInfo'];
            //积分、优惠券操作下标信息
            $discountIdxList = $pointDetail['discountIdxList'] ?? [];
            //积分、优惠券验证操作下标数据
            $pointCheckIdxArr = $checkedInfo['point_check_idx'] ?? [];
            //积分、优惠券撤销操作下标数据
            $pointRevokeIdxArr = $checkedInfo['point_revoke_idx'] ?? [];

            //汇总积分、优惠券取消修改撤销撤改优惠金额
            $pointAmountDetail = $this->_handlePointsIdxAmount($orderNum, $discountIdxList, [], $pointRevokeIdxArr,
                $pointCheckIdxArr, true);

            //过滤掉都是0的数据
            if ($ticketNum == 0 && $finishNum == 0 && $revokeNum == 0 && $afterSaleNum == 0) {
                continue;
            }

            //操作人员信息
            $operMemberInfo = isset($checkedInfo['oper_member']) && $checkedInfo['oper_member'] ? $checkedInfo['oper_member'] : [];

            //站点ID
            $siteId  = isset($siteIdArr[$orderNum]) ? $siteIdArr[$orderNum] : 0;
            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];
            $channel = $orderInfo['ordermode'];
            //验证时间
            $dTime = date('Ymd', strtotime($orderInfo['dtime']));
            //预订时间
            $orderTime = date('Ymd', strtotime($orderInfo['ordertime']));

            if ($dTime == $orderTime) {
                $isCheckSameDay = 1;
            } else {
                $isCheckSameDay = 2;
            }

            //计算优惠券使用金额
            if (isset($couponList[$orderNum][$fid][$sellerId])) {
                //优惠金额
                $eMoney = $couponList[$orderNum][$fid][$sellerId];
            } else {
                $eMoney = 0;
            }

            $discountDetail          = self::handleDiscountInfo($pointAndCouponInfo,
                [], $orderNum, $fid, $sellerId, $oldSeller);
            $discountMoney           = $discountDetail[self::DISCOUNT]['order_amount'];
            $settlementDiscountMoney = $discountDetail[self::SETTLEMENT_DISCOUNT]['order_amount'];

            //通过渠道将散客进行归类
            if (in_array($level, [-1, 0])) {
                $tmp = $this->_getSankeId($sellerId, $fid, $channel, $sankeList);

                if ($tmp !== false) {
                    //最后一级就是散客的情况(自供自销也算在这里面)
                    if ($level == 0) {
                        $level = 1;
                    } else {
                        $level = -3;
                    }
                    $sellerId = $tmp;

                    //预售券兑换的订单，末级销售金额为0
                    if ($this->_checkSaleMoneyZero($channel, $paymode) && !$this->_needAddSplit($level, $sellerId, $fid)) {
                        $saleMoney = 0;
                    }
                }
            }
            $mainTid  = 0;
            $mainType = '';
            if (isset($packInfo[$orderNum]) && $packInfo[$orderNum] == 1) {
                //套票主票
                $mainTid = 0;
            } elseif (isset($packInfo[$orderNum])) {
                //主票的订单号对应的票类ID
                $mainTid  = $tidArr[$packInfo[$orderNum]];
                $mainType = $packMainOrderList[$packInfo[$orderNum]]['product_type'] ?? '';
            }

            //特殊处理
            $splitTmp = $this->_needAddSplit($level, $sellerId, $fid);

            if ($splitTmp) {
                //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                $splitData = [
                    'orderid'    => $orderNum,
                    'sellerid'   => $sellerId,
                    'buyerid'    => $this->_getChannelSanke($channel),
                    'level'      => -3, //非0/-1
                    'cost_money' => $saleMoney,
                    'sale_money' => $needMoney, //默认使用零售价的钱
                    'pmode'      => $paymode,
                ];

                //如果需要补全一条报表记录，且是预售券兑换订单，末级销售金额为0
                if ($this->_checkSaleMoneyZero($channel, $paymode)) {
                    $splitData['sale_money'] = 0;
                }

                $chainList[] = $splitData;
            }

            //针对单个供应商分销商时
            if (($this->_fid && $item['sellerid'] != $this->_fid) || !in_array($item['sellerid'], $this->_allowFids)) {
                continue;
            }

            //由于系统一开始的问题 套票子票在整条分销链中全部记为套票子票下单 这是错误的
            //只有被将该票打包成套票的供应商出售这笔订单的销售渠道会记为套票子票下单
            //在这里做个判断 如果子票的主票的发布人 是当前处理的分销链层级的卖家 则跳过（预订报表过滤子票下单）
            if (!empty($mainTid)) {
                //----1. 主票订单号的票类ID的发布人 ApplyDid
                $applyDid = $ticketApplyInfo[$mainTid];
                //----2. 如果主票的发布人是当前处理的分销链的层级的卖票的人sell_id 跳过

                if ($applyDid != $fid) {
                    $mainTid  = 0;
                    $mainType = '';
                }
            }

            //去除退票手续费
            $orderCancelInfo = $cancelInfo[$orderNum] ?? [];

            if (!isset($splitList[$fid])) {
                $splitList[$fid] = [];
            }

            if (!isset($splitList[$fid][$sellerId])) {
                $splitList[$fid][$sellerId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid])) {
                $splitList[$fid][$sellerId][$pid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid])) {
                $splitList[$fid][$sellerId][$pid][$mainTid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode] = [];
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {

                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember] = [];
                    }
                }
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {

                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay] = [];
                    }
                }
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {

                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId] = [
                            'lid'                     => $lid,
                            'tid'                     => $tid,
                            'level'                   => $level,
                            'order_num'               => 0,
                            'order_ticket'            => 0,
                            'finish_num'              => 0,
                            'finish_ticket'           => 0,
                            'revoke_num'              => 0,
                            'revoke_ticket'           => 0,
                            'cost_money'              => 0,
                            'sale_money'              => 0,
                            'service_money'           => 0,
                            'finish_cost_money'       => 0,
                            'finish_sale_money'       => 0,
                            'revoke_cost_money'       => 0,
                            'revoke_sale_money'       => 0,
                            'after_sale_ticket_num'   => 0,
                            'after_sale_refund_money' => 0,
                            'after_sale_income_money' => 0,
                            'orders_info'             => [],
                            'finish_orders_info'      => [],
                            'revoke_orders_info'      => [],
                            'after_sale_info'         => [],
                        ];
                    }
                }
            }

            //整合数据
            if ($ticketNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //验证
                    if (!in_array($action, [5, 33])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];

                        //上一级的卖出优惠金额 是下一级的买入优惠金额
                        $buySettlementCheck = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$operTicketNum]['checkSettlementPoints'] ?? 0;

                        $actionDiscount        = self::handleStatisticsActionDiscount($pointAmountDetail,
                            $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operMember, 'check_amount');
                        $checkPoints           = $actionDiscount[self::DISCOUNT];
                        $checkSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                        //记录一下分销优惠金额
                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$operTicketNum]['checkSettlementPoints'] = $this->getCostDiscountMoney($checkPoints, $checkSettlementPoints);

                        //支付数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['order_num']     += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['orders_info'][] = [
                            $orderNum,
                            $operTicketNum,
                            $checkPoints,
                            $checkSettlementPoints ?: $buySettlementCheck,
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['sale_money']    += ($saleMoney * $operTicketNum - $eMoney - $this->getSaleDiscountMoney($checkPoints, $checkSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['cost_money']    += $costMoney * $operTicketNum - $buySettlementCheck;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['order_ticket']  += $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_ticket'] += 0;
                    }
                }
            }

            if ($finishNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //完结
                    if (!in_array($action, [17])) {
                        continue;
                    }

                    $buySettlementFinish = $lastSettlementDiscountInfo[$orderNum]['finishSettlementPoints'] ?? 0;

                    $actionDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail,
                        $orderNum, $action, $discountMoney, $settlementDiscountMoney, 0, 'finish_amount');
                    $finishPoints           = $actionDiscount[self::DISCOUNT];
                    $finishSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                    //记录一下分销优惠金额
                    $lastSettlementDiscountInfo[$orderNum]['finishSettlementPoints'] = $this->getCostDiscountMoney($finishPoints, $finishSettlementPoints);

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;
                        $finishNum  = $ticketInfo['tnum'];

                        //取消数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_num']           += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_sale_money']    += ($saleMoney * $finishNum - $eMoney - $this->getSaleDiscountMoney($finishPoints, $finishSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_orders_info'][] = [
                            $orderNum,
                            $finishNum,
                            $finishPoints,
                            $finishSettlementPoints ?: $buySettlementFinish,
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_cost_money']    += $costMoney * $finishNum - $buySettlementFinish;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['order_ticket']  += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_ticket'] += $finishNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_ticket'] += 0;
                    }
                }
            }

            if ($revokeNum > 0) {

                foreach ($operMemberInfo as $action => $info) {
                    //撤改
                    if (!in_array($action, [6, 7])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;
                        $revokeNum  = $ticketInfo['tnum'];
                        $trackIds = $ticketInfo['track_ids'] ?? [];

                        //手续费
                        $serviceMoney = $this->mateCancelServiceMoneyByTrackIdGroup($fid, $trackIds, $orderCancelInfo);

                        $buySettlementRevoke = $lastSettlementDiscountInfo[$orderNum][$operMember][$fid][$revokeNum]['revokeSettlementPoints'] ?? 0;

                        $actionDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail,
                            $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operMember, 'revoke_amount');
                        $revokePoints           = $actionDiscount[self::DISCOUNT];
                        $revokeSettlementPoints = $actionDiscount[self::SETTLEMENT_DISCOUNT];

                        //记录一下分销优惠金额
                        $lastSettlementDiscountInfo[$orderNum][$operMember][$sellerId][$revokeNum]['revokeSettlementPoints'] = $this->getCostDiscountMoney($revokePoints, $revokeSettlementPoints);

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_num']           += 1;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_sale_money']    += ($saleMoney * $revokeNum - $eMoney - $this->getSaleDiscountMoney($revokePoints, $revokeSettlementPoints));
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_orders_info'][] = [
                            $orderNum,
                            $revokeNum,
                            $revokePoints,
                            $revokeSettlementPoints ?: $buySettlementRevoke,
                        ];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_cost_money']    += $costMoney * $revokeNum - $buySettlementRevoke;

                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['order_ticket']  += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['finish_ticket'] += 0;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['revoke_ticket'] += $revokeNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['service_money'] += $serviceMoney;
                    }
                }
            }

            //售后数据
            if ($afterSaleNum > 0) {
                foreach ($operMemberInfo as $action => $info) {

                    if (!in_array($action, [38])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {

                        $operMember = $oper;

                        $afterSaleInfo = $ticketInfo['after_sale_info'] ?? [];

                        foreach ($afterSaleInfo as $afterSaleCode => $afterInfo) {
                            $afterSaleNum = $afterInfo['after_sale_num'] ?? 0;

                            //售后退回金额
                            $afterSaleRefundMoney = $afterSaleOrderMap[$orderNum][$afterSaleCode]['sellerId'][$fid] ?? 0;
                            //售后收入单价
                            $afterSaleIncomeMoney = $afterSaleOrderMap[$orderNum][$afterSaleCode]['buyerId'][$fid] ?? 0;

                            //售后订单明细 0.订单号 1.售后数量 2.编号 3.退回金额 4.收入金额
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['after_sale_info'][]       = [
                                $orderNum,
                                $afterSaleNum,
                                (string)$afterSaleCode,
                                $afterSaleRefundMoney,
                                $afterSaleIncomeMoney,
                            ];
                            //处理相同维度指标累加
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['after_sale_refund_money'] += $afterSaleRefundMoney;
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['after_sale_income_money'] += $afterSaleIncomeMoney;
                            $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$operMember][$isCheckSameDay][$siteId]['after_sale_ticket_num']   += $afterSaleNum;
                        }
                    }
                }
            }
        }

        //如果不是最后一个分片，就先将数据写入缓存文件
        if (!$isLastSlice) {
            $this->_cacheDataArr($day, 'minute_checked_v2', 'set', $splitList);
            unset($splitList);

            return true;
        } else {
            //如果是最后一个，就将之前的数据清除
            $this->_cacheDataArr($day, 'minute_checked_v2', 'clear');
        }

        $updateTime = time();
        $date       = date("Ymd", strtotime($day));
        $dateMinute = date("YmdHi", strtotime($day));

        //分批插入数据
        $insertData = [];
        $lastId     = true;

        foreach ($splitList as $fid => $fidData) {
            foreach ($fidData as $sellerId => $sellerData) {
                foreach ($sellerData as $pid => $pidData) {
                    foreach ($pidData as $mainTid => $mainTidData) {
                        foreach ($mainTidData as $mainType => $mainTypeData) {
                            foreach ($mainTypeData as $channel => $channelData) {
                                foreach ($channelData as $paymode => $paymodeData) {
                                    foreach ($paymodeData as $operMember => $operData) {
                                        foreach ($operData as $isCheckedToday => $isCheckedData) {
                                            foreach ($isCheckedData as $siteId => $data) {

                                                if ($data['revoke_ticket'] == 0 && $data['finish_ticket'] == 0 && $data['order_ticket'] == 0 && $data['after_sale_ticket_num'] == 0) {
                                                    continue;
                                                }

                                                //入库数据处理
                                                $tmp = $data;

                                                $tmp['fid']                = $fid;
                                                $tmp['pay_way']            = $paymode;
                                                $tmp['orders_info']        = json_encode($tmp['orders_info']);
                                                $tmp['finish_orders_info'] = json_encode($tmp['finish_orders_info']);
                                                $tmp['revoke_orders_info'] = json_encode($tmp['revoke_orders_info']);
                                                $tmp['after_sale_info']    = json_encode($tmp['after_sale_info']);
                                                $tmp['reseller_id']        = $sellerId;
                                                $tmp['pid']                = $pid;
                                                $tmp['date']               = $date;
                                                $tmp['date_minute']        = $dateMinute;
                                                $tmp['channel']            = $channel;
                                                $tmp['operate_id']         = $operMember;
                                                $tmp['today_check']        = $isCheckedToday;
                                                $tmp['update_time']        = $updateTime;
                                                $tmp['site_id']            = $siteId;
                                                $tmp['main_tid']           = $mainTid;
                                                $tmp['main_type']          = $mainType;

                                                $insertData[] = $tmp;

                                                if (count($insertData) >= $this->_insertLimit) {
                                                    //达到条数就插入数据
                                                    $lastId = $this->_insertDataArr($insertData, 'minute_checked_v2');
                                                    //初始化
                                                    $insertData = [];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //将没有插入的数据最后插入
        if ($insertData) {
            $lastId = $this->_insertDataArr($insertData, 'minute_checked_v2');
        }

        //返回
        return $lastId;
    }

    /**
     * 获取分终端验证完结订单数（因为在订单追踪表中分终端验证未全部验证时完结数量为0）
     *
     * @param         $order
     * @param         $field
     * @param  array  $extraConf
     *
     * @return array|mixed
     */
    public function branchTerminalFinshNum($orderNum)
    {
        //完结订单数
        $finishNum      = 0;
        $lastCheckTrack = $this->_getSubOrderTrackModel()->getTrackList($orderNum, 5, 32, 'left_num');
        $leftNum        = isset($lastCheckTrack['left_num']) ? $lastCheckTrack['left_num'] : 0;

        if ($leftNum > 0) {
            //订单原始信息
            $applyinfo = $this->_getOrderToolModel()->getOrderApplyInfo([$orderNum], 'origin_num, refund_num');
            if (is_array($applyinfo) && !empty($applyinfo)) {
                $finishNum = $applyinfo[0]['origin_num'] - $applyinfo[0]['refund_num'];
            }
        }

        // 转换字符类型
        $finishNum = (int)$finishNum;

        return $finishNum;
    }


    /**
     * 获取验证追踪记录
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param  array  $date
     * @param  array  $tidArr
     * @param  false  $isTotal
     * @param  int    $page
     * @param  int    $pageSize
     *
     * @return array|int|mixed
     */
    public function getMinuteTrackList($date, $tidArr, $isTotal = false, $page = 1, $pageSize = 10)
    {
        $startTime = date('Y-m-d H:i:s', strtotime($date[0]));
        $endTime   = date('Y-m-d H:i:s', strtotime($date[1]));
        $forceMainDb = $orderNum = $source = $actionNotIn = $operMember = $tnumGreaterEqThan = null;
        $queryParams = [
            $page,
            $pageSize,
            $forceMainDb,
            $orderNum,
            [5, 6, 7, 17, 33, 38],
            $source,
            $actionNotIn,
            $operMember,
            $tidArr,
            $startTime,
            $endTime,
            $tnumGreaterEqThan,
            ['insertTime', 'id'],
            true,
        ];

        $queryRes = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'findByParamsPage', $queryParams);
        if ($queryRes['code'] != self::CODE_SUCCESS || empty($queryRes['data']['list'])) {
            return $isTotal ? 0 : [];
        }

        //返回总数
        if ($isTotal) {
            return $queryRes['data']['total'] ?? 0;
        }

        $data = [];
        foreach ($queryRes['data']['list'] as $value) {
            $data[] = [
                'id'             => $value['id'],
                'ordernum'       => $value['ordernum'],
                'branchTerminal' => $value['branchterminal'],
                'SalerID'        => $value['salerid'],
                'oper_member'    => $value['operMember'],
                'action'         => $value['action'],
                'tnum'           => $value['tnum'],
                'left_num'       => $value['leftNum'],
                'insertTime'     => $value['inserttime'],
                'source'         => $value['source'],
                'msg'            => $value['msg'],
                'tid'            => $value['tid'],
                'ext_content'    => $value['extContent'],
            ];
        }

        return $data;
    }
}