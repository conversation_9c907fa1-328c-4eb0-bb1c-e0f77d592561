<?php
/**
 * 销售报表 报表下载中心的数据中间处理层
 * <AUTHOR>
 * @date   2017-4-10
 */

namespace Business\Statistics;

use Business\AnnualCard\AnnualCardManage;
use Business\AnnualCard\Task;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\DataAuthLogic;
use Business\CommodityCenter\Product;
use Business\Common\DictPayModeService;
use Business\JavaApi\Member\MemberRelationQuery;
use Business\JsonRpcApi\ScenicLocalService\BranchTerminalReportMonth;
use Business\Order\OrderAidsSplitQuery;
use Business\Order\OrderInfoHiding;
use Business\Product\AnnualCardTask;
use Business\Product\ExchangeCouponProduct as ExchangeCouponProductBiz;
use Business\Product\SubProduct as SubProductBiz;
use Business\Statistics\StatisticsDiscounts as StatisticsDiscountsBiz;
use Library\Constants\Order\OrderPayMode as OrderPayModeConstants;
use Library\Constants\TerminalConst;
use Library\Constants\Card\ExternalCard;
use Library\Traits\IdentityConversionTrait;
use Library\Util\JsonUtil;
use Model\Member\Member;
use Model\Order\Coupon;
use Model\Order\OrderQuery;
use Model\Order\OrderSearch;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\SubOrderQuery\SubOrderDetail;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Model\Order\SubOrderQuery\SubOrderTrack;
use Model\Product\AnnualCard;
use Model\Product\Land;
use Model\Product\Terminal;
use Model\Product\Ticket;
use Model\Report\AnnualCardStatistics;
use Business\Member\Member as MemberBiz;
use Business\Statistics\StatisticsHandle as StatisticsHandleBiz;
use Business\Statistics\StatisticsAuth as StatisticsAuthBiz;
use Business\DownloadCenter\HandleExportDetailMapping as HandleExportDetailMappingBiz;

class statistics extends \Business\Base
{
    use IdentityConversionTrait;

    private $_payStatus = [
        0 => '现场支付',
        1 => '已支付',
        2 => '未支付',
    ];

    private $_tradeTypeInfo = [
        0 => '售卡',
        1 => '续费',
        2 => '补卡',
        3 => '取消',
        4 => '激活',
        5 => '验证',
    ];

    private $_annual_action_convert = [
        23 => 0,
        1  => '续费',
        2  => '补卡',
        3  => '取消',
        4  => '激活',
        5  => '验证',
    ];

    private $_cardStatus = [
        0 => '未激活',
        1 => '已激活',
        2 => '已禁用',
        4 => '已挂失',
    ];

    //以下报表的月汇总数据是从日汇总表取的
    private $_monthFromDayReport = [
        13, //套票预定报表
        15, //套票验证报表
        33, //检票报表
    ];

    //特殊处理的支付方式 这三种只计算数量 不计算金额 对应 business.conf.php的payway_list
    private $_specialPayWay = [3, 4, 13];

    //带支付方式的验证报表  查询应收数据时 如果支付方式不在数组内 统一归为账户余额
    private $_specialPayWayTwo = [0, 2, 9];

    private $_couponModel = null;

    private $_selectSize = 2000;

    private $_subDetailModel;

    //新版预订报表统计指标对应的名称
    private $_needTarget = ['order_ticket' => '预订票数', 'cancel_ticket' => '取消票数', 'revoke_ticket' => '撤销票数'];

    //新版验证报表统计指标对应的名称
    private $_needCheckTarget = [
        'finish_ticket'    => '完结票数',
        'today_ticket'     => '当日下单验证票数',
        'not_today_ticket' => '非当日下单验证票数',
        'total_ticket'     => '验证总数',
        'all_ticket'       => '总票数',
    ];

    private $_loginInfo = [];

    //追踪记录ES分页大小
    private $_trackOrderPageSize = 1000;

    private function _getSubDetailModel()
    {
        if (empty($this->_subDetailModel)) {
            $this->_subDetailModel = new SubOrderDetail();
        }

        return $this->_subDetailModel;
    }

    private $_classSettleResArr = [];
    private $_annualCardInfo    = [];

    //验证报表允许取票状态类型
    private $_allow_print_checked = [2, 4, 6, 8];

    //预订报表允许取票状态类型
    private $_allow_print_booked = [1, 3, 5, 7, 18, 20];

    //csv实例  这边上一级生成文件较大时，可以直接传一个csv实例进来直接写入文件，避免在合并数组数据时报错
    private $_CSVObject;

    /**
     * 设置csv实例
     * <AUTHOR>
     * @date 2022/5/3
     *
     * @param object $csv 实例
     *
     * @return $this
     */
    public function setCSVObject($csv)
    {
        if (!empty($csv)) {
            $this->_CSVObject = $csv;
        }

        return $this;
    }

    /**
     * 是否设置了实例
     * <AUTHOR>
     * @date 2022/5/3
     *
     * @return bool
     */
    private function _isSoonWrite()
    {
        if (!empty($this->_CSVObject)) {
            return true;
        }

        return false;
    }

    /**
     * 写入csv文件
     * <AUTHOR>
     * @date 2022/5/3
     *
     * @param  array  $data
     *
     * @return bool
     */
    private function _CSVWrite(array $data)
    {
        if ($this->_isSoonWrite()) {
            foreach ($data as $item) {
                $this->_CSVObject->CSVWrite($item);
            }
            return true;
        }

        return false;
    }

    /**
     * 移除csv实例
     * <AUTHOR>
     * @date 2022/5/3
     *
     * @return bool
     */
    private function _rmCSVObject()
    {
        unset($this->_CSVObject);

        return true;
    }


    /**
     * 参数处理 预订/验证/取消/撤销 报表
     *
     * @param $request
     * @param $fid
     *
     * @return array
     */
    public function paramsHandle($request, $fid)
    {
        //根据不同账号获取数据
        $memberModel = new \Model\Member\Member();
        $memberInfo  = $memberModel->getMemberInfo($fid);
        $dtype       = $memberInfo['dtype'];
        $account     = $memberInfo['account'];

        $landId    = isset($request['land_id']) ? intval($request['land_id']) : false;
        $pid       = isset($request['pid']) ? intval($request['pid']) : false;
        $operateId = isset($request['operate_id']) ? intval($request['operate_id']) : false;
        $payWay    = isset($request['pay_way']) ? intval($request['pay_way']) : false;

        $merchantId  = isset($request['merchant_id']) ? intval($request['merchant_id']) : false;
        $beginDate   = isset($request['begin_date']) ? $request['begin_date'] : false;
        $endDate     = isset($request['end_date']) ? $request['end_date'] : false;
        $ticketId    = isset($request['ticket_id']) ? intval($request['ticket_id']) : false;
        $resellerId  = isset($request['reseller_id']) ? intval($request['reseller_id']) : false;
        $excludeTest = isset($request['exclude_test']) ? intval($request['exclude_test']) : false;
        $channel     = isset($request['channel']) ? $request['channel'] : false;
        $isDetail    = isset($request['is_detail']) ? $request['is_detail'] : false;
        $countWay    = isset($request['count_way']) ? $request['count_way'] : false;

        if (!empty($isDetail)) {
            switch ($countWay) {
                case 'product':
                    //按产品
                    if (I('param.detail_lid')) {
                        $landId = intval(I('param.detail_lid'));
                    } else {
                        $landId = intval($request['detail_lid']);
                    }
                    break;
                case 'ticket':
                    //按门票
                    if (I('param.detail_tid')) {
                        $ticketId = intval(I('param.detail_tid'));
                    } else {
                        $ticketId = intval($request['detail_tid']);
                    }
                    break;
                case 'date':
                    //按日期
                    if (I('param.detail_date')) {
                        $beginDate = intval(I('param.detail_date'));
                        $endDate   = intval(I('param.detail_date'));
                    } else {
                        $beginDate = intval($request['detail_date']);
                        $endDate   = intval($request['detail_date']);
                    }
                    break;
                case 'reseller':
                    //按分销商
                    if (I('param.detail_reseller_id')) {
                        $resellerId = intval(I('param.detail_reseller_id'));
                    } else {
                        $resellerId = intval($request['detail_reseller_id']);
                    }
                    break;
                case 'channel':
                    //按销售渠道
                    if (I('param.detail_channel')) {
                        $channel = intval(I('param.detail_channel'));
                    } else {
                        $channel = intval($request['detail_channel']);
                    }
                    break;
                case 'resellerAndTicket':
                    if (I('param.detail_reseller_id') || I('param.detail_tid')) {
                        $resellerId = intval(I('param.detail_reseller_id'));
                        $ticketId   = intval(I('param.detail_tid'));
                    } else {
                        $resellerId = intval($request['detail_reseller_id']);
                        $ticketId   = intval($request['detail_tid']);
                    }
                    break;
                case 'lidAndTicket':
                    if (I('param.detail_lid') || I('param.detail_tid')) {
                        $landId   = intval(I('param.detail_lid'));
                        $ticketId = intval(I('param.detail_tid'));
                    } else {
                        $landId   = intval($request['detail_lid']);
                        $ticketId = intval($request['detail_tid']);
                    }
                    break;
            }
        }

        $isResource = false;

        if ($dtype == 7) {
            //集团账号
            $queryParams = [$fid];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
                'queryPageGroupSonIds', $queryParams);
            $memberIdArr = [$fid];
            if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                $memberIdArr = array_merge($memberIdArr, $queryRes['data']['list']);
            }
            //$shipModel   = new \Model\Member\MemberRelationship($fid);
            //$memberIdArr = $shipModel->getResellers($fid, $dtype, true, false);
            $level = false;
        } elseif ($dtype == 2 || $dtype == 3) {
            //资源方账号
            $landModel = new \Model\Product\Land();
            $tmp       = $landModel->getListByResource($account, $dtype);

            $landList = [];
            foreach ($tmp as $item) {
                $landList[] = $item['id'];
            }

            //如果都没有景区的话，直接返回空
            if (!$landList) {
                return ['code' => false, 'msg' => '没有景区'];
            }

            //如果不是查询具体景区的话，就查询资源账号下的所有景点
            if (!in_array($landId, $landList)) {
                $landId = $landList;
            }

            $level       = 1;
            $memberIdArr = false;
            $isResource  = true;
        } else if ($fid == 1) {
            //管理员
            $level       = 1;
            $memberIdArr = $merchantId;
        } else {
            $memberIdArr = $fid;
            $level       = false;
        }

        $fid = $memberIdArr;

        if (!strtotime($beginDate) || !strtotime($endDate)) {
            return ['code' => false, 'msg' => '时间格式错误'];
        }

        $beginTime = strtotime($beginDate);
        $endTime   = strtotime($endDate);

        // 如果有传递时分秒，则分割出时分秒
        $beginArr = explode(' ', $beginDate);
        $endArr   = explode(' ', $endDate);

        //处理查询条件
        $where = [];

        $where['isResource'] = $isResource;

        if ((count($beginArr) == 2) || (count($endArr) == 2)) {

            if (count($beginArr) == 2 && substr($beginDate, 0, 10) == date('Y-m-d')) {
                $where['update_time'][] = array('EGT', $beginTime);
            }

            if (count($endArr) == 2 && substr($endDate, 0, 10) == date('Y-m-d')) {
                $where['update_time'][] = array('ELT', $endTime);
            }
        }

        $beginDate = date('Ymd', $beginTime);
        $endDate   = date('Ymd', $endTime);

        $where['date'] = array(array('EGT', $beginDate), array('ELT', $endDate));

        //数据会有问题，暂时都设置为分销商
        $searchType = 0;

        if ($fid !== false) {
            if ($fid) {
                if ($searchType == 0) {
                    $where['fid'] = is_array($fid) ? ['in', $fid] : intval($fid);
                } else {
                    $where['reseller_id'] = is_array($fid) ? ['in', $fid] : intval($fid);
                }
            }
        }

        if ($level !== false) {
            $where['level'] = is_array($level) ? ['in', $level] : intval($level);
        }

        if (!empty($fid) && $level == 1) {
            unset($where['level']);
        }

        if ($ticketId !== false) {
            if ($ticketId) {
                $where['tid'] = is_array($ticketId) ? ['in', $ticketId] : intval($ticketId);
            }
        }

        if ($landId !== false) {
            if ($landId) {
                $where['lid'] = is_array($landId) ? ['in', $landId] : intval($landId);
            }
        }

        if ($resellerId !== false) {
            if ($resellerId) {
                if ($searchType == 0) {
                    $where['reseller_id'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
                } else {
                    $where['fid'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
                }
            }
        }

        $excludeUids = false;
        if ($excludeTest) {
            $excludeUids = load_config('test_uids', 'account');
        }

        //排除账号
        if ($excludeUids !== false) {
            $excludeUids = trim(strval($excludeUids), ',');
            if ($excludeUids) {
                $where['_string'] = "fid not in ($excludeUids)";
            }
        }

        //分销渠道
        if ($channel !== false) {
            $where['channel'] = $channel;
        }

        //产品ID
        if ($pid !== false) {
            $where['pid'] = $pid;
        }

        //操作员ID
        if ($operateId !== false) {
            $where['operate_id'] = $operateId;
        }

        //支付方式
        if ($payWay !== false) {
            $where['pay_way'] = $payWay;
        }

        //返回参数
        return ['code' => true, 'where' => $where];
    }

    /**
     * 处理报表导出文件名
     * <AUTHOR>
     *
     * @param  string  $begTime  开始时间
     * @param  string  $endTime  结束时间
     * @param  int  $landId  景区id
     * @param  int  $disId  分销商id
     * @param  string  $countWay  查询类型
     * @param  int  $uploadType  其他报表导出类型 分销商 0  or 供应商 1
     * @param  int  $types  应收应付报表 类型 1 应收报表(分) 2 应付报表(供)
     *
     * @return array
     */
    public function getUploadInfoExt($begTime = '', $endTime = '', $landId = '', $disId = '', $countWay = '', $uploadType = '', $types = '')
    {
        if ($landId) {
            $land   = new Land();
            $landId = $land->getLandInfo($landId);
            $land   = $landId['title'];
        }

        if (is_numeric($disId)) {
            $member = new Member();
            $info   = $member->getMemberInfo($disId, 'id', 'dname');
            $dis    = $info['dname'];
        }

        if ($countWay) {
            switch ($countWay) {
                case 'product':
                    //按产品
                    $countType = "按产品汇总";
                    break;
                case 'ticket':
                    //按门票
                    $countType = "按门票汇总";
                    break;
                case 'date':
                    //按日期
                    $countType = "按日期汇总";
                    break;
                case 'reseller':
                    //按分销商
                    $countType = "按分销商汇总";
                    break;
                case 'channel':
                    //按销售渠道
                    $countType = "按销售渠道汇总";
                    break;
                case 'resellerAndTicket':
                    $countType = "按分销商+票类汇总";
                    break;
            }
        }
        if ($types == '1' || $uploadType == '0') {
            $typeName = '全部分销商';
        }
        if ($types == '2' || $uploadType == '1') {
            $typeName = '全部供应商';
        }
        $data = [
            'begTime'  => $begTime ? date('Ymd', strtotime($begTime)) : "开始时间",
            'endTime'  => $endTime ? date('Ymd', strtotime($endTime)) : "结束时间",
            'land'     => $land ? mb_substr($land, 0, 6, "utf-8") : "全产品名称",
            'dis'      => $dis ? mb_substr($dis, 0, 6, "utf-8") : $typeName,
            'countWay' => $countType ? $countType : "无",
        ];

        return $data;
    }

    /*
     * 参数处理 应付应收报表 参数参考报表的代码
     */
    public function paramsHandleV2($request, $fid)
    {
        $beginDate   = isset($request['begin_date']) ? strval($request['begin_date']) : 0;
        $endDate     = isset($request['end_date']) ? strval($request['end_date']) : 0;
        $type        = isset($request['type']) ? intval($request['type']) : 1;
        $landId      = isset($request['land_id']) ? intval($request['land_id']) : 0;
        $resellerId  = isset($request['reseller_id']) ? intval($request['reseller_id']) : 0;
        $merchantId  = isset($request['merchant_id']) ? intval($request['merchant_id']) : 0;
        $excludeTest = isset($request['exclude_test']) ? intval($request['exclude_test']) : 0;

        if (isset($request['detail_reseller_id'])) {
            $resellerId = intval($request['detail_reseller_id']);
        }

        if (isset($request['detail_pid'])) {
            $pid = intval($request['detail_pid']);
        }

        $beginTime = strtotime($beginDate);
        $endTime   = strtotime($endDate);

        if ($beginTime == false || $endTime == false) {
            return ['code' => false, 'msg' => '时间格式错误'];
        }

        if (!in_array($type, [1, 2])) {
            return ['code' => false, 'msg' => '类型错误'];
        }

        // 如果有传递时分秒，则分割出时分秒
        $beginArr = explode(' ', $beginDate);
        $endArr   = explode(' ', $endDate);

        //处理查询条件
        $where = [];

        if ((count($beginArr) == 2) || (count($endArr) == 2)) {

            if (count($beginArr) == 2 && substr($beginDate, 0, 10) == date('Y-m-d')) {
                $where['update_time'][] = array('EGT', $beginTime);
            }

            if (count($endArr) == 2 && substr($endDate, 0, 10) == date('Y-m-d')) {
                $where['update_time'][] = array('ELT', $endTime);
            }

        }

        $beginDate = date('Ymd', $beginTime);
        $endDate   = date('Ymd', $endTime);

        if ($fid != 1) {
            $merchantId = '';
        }

        if (!empty($merchantId)) {
            $fid = $merchantId;
        }

        $excludeIds = false;
        if ($excludeTest) {
            $excludeIds = load_config('test_uids', 'account');
        }

        $where['date'] = ['between', [$beginDate, $endDate]];

        if ($type == 1) {
            $where['fid'] = $fid;
            if (!empty($resellerId)) {
                $where['reseller_id'] = $resellerId;
            }
        } else {
            $where['reseller_id'] = $fid;
            if (!empty($resellerId)) {
                $where['fid'] = $resellerId;
            }
        }

        if (!empty($landId)) {
            $where['lid'] = $landId;
        }

        //排除账号
        if ($excludeIds !== false) {
            $excludeIds = trim(strval($excludeIds), ',');
            if ($excludeIds) {
                $where['_string'] = "fid not in ($excludeIds)";
            }
        }

        //票类ID
        if (!empty($pid)) {
            $where['pid'] = $pid;
        }

        //特殊加个type 用时需要unset
        $where['type'] = $type;

        //返回参数
        return ['code' => true, 'where' => $where];

    }

    /**
     * 分终端汇总导出明细
     * Create by zhangyangzhen
     * Date: 2019/5/21
     * Time: 15:51
     *
     * @param  array  $where  查询条件
     * @param  array  $titleArr  头部列表名称
     * @param  int  $page  页码
     * @param  int  $size  每页数量
     * @param  bool  $archiverDb  是否查询历史库
     * @param  string  $yearMark  归档年份标识
     *
     * @return array
     */
    public function getBranchTerminalInfo($where, $titleArr, $page = 1, $size = 500, $archiverDb = false, $yearMark = '', $dateType = 1)
    {
        $db = 'summary';
        if ($archiverDb) {
            $db = 'summary_history';
        }

        unset($where['isResource']);
        unset($where['date_type']);

        if ($dateType == 2) {
            $branchTerminalReportMonth = new BranchTerminalReportMonth();
            $data         = $branchTerminalReportMonth->exportBranchTerminalMonthList($where, $page, $size);
            $data         = $data['data'];
        } else {
            $model        = new \Model\Report\Statistics($db, $yearMark);
            $terminalType = $dateType == 3 ? 22 : 12;
            $data         = $model->getInfoV2($where, $terminalType, $page, $size);
        }
       

        $lidArr          = [];
        $orderArr        = [];
        $terminalArr     = [];
        $orderReportInfo = [];

        //操作人员
        $operatorArr = [];
        //$action          = 33;   //原先是5 后面分终端的action改成了33了  这边同步下
        //$source          = 32;
        $orderOperatorMap = [];

        if (!empty($data)) {
            foreach ($data as $item) {
                $ordersInfo    = $item['orders_info'];
                $lidArr[]      = $item['lid'];
                $tid           = $item['tid'];
                $terminal      = $item['terminal'];
                $terminalArr[] = $terminal;
                $operatorArr[] = $item['operate_id'];

                unset($item['orders_info']);
                $ordersInfo = json_decode($ordersInfo, true);

                foreach ($ordersInfo as $key => $value) {

                    $tmpOrder                                             = $value[0];
                    $orderRealNum                                         = $value[1];
                    $orderOperatorMap[$tmpOrder][$tid][$item['terminal']] = $item['operate_id'];

                    // 订单信息
                    if (is_array($value)) {
                        $orderArr[] = $tmpOrder;
                        if (!isset($orderReportInfo[$tmpOrder])) {
                            $orderReportInfo[$tmpOrder]                            = $item;
                            $orderReportInfo[$tmpOrder]['realTnum']                = $orderRealNum;
                            $orderReportInfo[$tmpOrder]['terminalList'][$terminal] = $orderRealNum;
                        } else {
                            $orderReportInfo[$tmpOrder]['terminalList'][$terminal] += $orderRealNum;
                        }
                    }
                }
            }
        }

        //获取全部产品id
        $lidArr  = array_unique($lidArr);
        $landRes = [];
        $mainTerminalList = [];
        if (!empty($lidArr)) {
            $landApi  = new \Business\CommodityCenter\Land();
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);

            //$landModel = new Land();
            //$landInfo  = $landModel->getLandInfoByMuli($lidArr, 'id, title, terminal');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item;
                }
                $mainTerminalList = array_column($landInfo, 'terminal');
            }
            unset($landInfo);
        }

        //获取全部分终端名称
        $terminalArr = array_unique($terminalArr);
        $terminalRes = [];
        if (!empty($terminalArr)) {
            $terminalModel = new Terminal();
            $terminalInfo  = $terminalModel->getSubTerminalInfo($terminalArr, true, 0);
            if (!empty($terminalInfo)) {
                foreach ($terminalInfo as $item) {
                    $terminalRes[$item['preTerminal']] = $item['name'];
                }
            }
        }
        // 导出部分的处理。
        foreach($terminalArr as $terId){
            if(!array_key_exists($terId, $terminalRes) && in_array($terId, $mainTerminalList)) {
                $terminalRes[$terId] = TerminalConst::MAIN_TERMINAL_NAME;
            }
        }

        $memberInfoMap = [];
        if (!empty($operatorArr)) {
            $memberInfo    = (new MemberBiz())->getMemberInfoByMulti(array_unique($operatorArr));
            $memberInfoMap = array_column($memberInfo, 'dname', 'id');
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);
        //根据订单号获取信息
        $orderTools = new OrderTools();
        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');
        $return         = [];
        if (!empty($orderArr)) {

            $data         = $orderTools->getOrderList($orderArr, 'ordernum, tid, lid, status', false, false,
                'verified_num');
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = ['in', $tidArr];
            //var_dump($data);exit;
            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    //订单号
                    $orderNum    = $item['ordernum'];
                    $orderInfo   = $orderReportInfo[$orderNum];
                    $tid         = $orderInfo['tid'];
                    $realTnum    = $orderInfo['realTnum'];
                    $lidName     = isset($landRes[$item['lid']]['title']) ? $landRes[$item['lid']]['title'] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';

                    $ticketInfo = $ticketList[$tid];
                    //操作人员
                    $terminale    = $orderInfo['terminal'] ?? 0;
                    $operator     = $orderOperatorMap[$orderNum][$tid][$terminale] ?? 0;
                    $operatorName = $memberInfoMap[$operator] ?? '';

                    foreach ($orderInfo['terminalList'] as $terminal => $tNum) {
                        $return[] = [
                            //订单号
                            $orderNum,
                            //订单状态
                            $orderStatus,
                            //已验证数
                            $item['verified_num'],
                            //员工
                            $operatorName,
                            //景区名称
                            $lidName,
                            //分终端/景点 名称
                            $terminalRes[$terminal] ?: '未知',
                            //门票名称
                            $ticketInfo,
                            //终端检票数量
                            $tNum,
                        ];
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 获取预订报表订单信息
     *
     * @param $where
     *
     * @return array
     *
     * @param  int  $type  1 预订  2 验证  3, 预定月报表， 4，验证月报表, 5, 套票预定 6,套票验证 7，套票月预定  8,套票月验证
     */
    public function getOrderInfoV2($where, $titleArr, $page = 1, $size = 500, $type = 1, $archiverDb = false, $yearMark = '',
        $noNeed = false, $loginInfo = [], $queryTime = [], $oldHead = [], $customField = null, $extFilter = [])
    {
        $db = 'summary';
        if ($archiverDb) {
            $db = 'summary_history';
        }

        $model              = new \Model\Report\Statistics($db, $yearMark);
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isLand             = $where['is_land'];
        $isResource         = $where['isResource'];
        unset($where['isResource']);
        unset($where['is_land']);
        unset($where['date_type']);
        if ($isLand) {
            $isResource = 1;
        }

        //套票子票不展示 加下过滤条件
        $isFilterSonTicket = $extFilter['filter_son_ticket'] ?? false;
        if ($isFilterSonTicket && in_array($type, [1, 3, 18, 13, 20, 23, 25])) {
            $where = $model->filterSonTicket($where, $extFilter);
        }

        $field = 'orders_info,cancel_orders_info,revoke_orders_info,reseller_id,fid,lid,tid';
        //过滤归档 取票字段:print_num,print_orders_info  只允许 1预订 3预定月报表 5套票预定 7套票月预定
        if (in_array($type, $this->_allow_print_booked) && !$archiverDb) {
            $field = $field . ',print_num,print_orders_info';
        } else {
            //移除取票状态
            $rmPrintNum = 'print_num';
            foreach ($titleArr as $k => $v) {
                if ($v == $rmPrintNum) {
                    unset($titleArr[$k]);
                }
            }
        }

        //报表加下售后字段, 预订小时、日、窗口定制字段、月、分钟
        if ($this->_isOrderType($type)) {
            $field .= ',after_sale_info';
        }

        $data = [];
        // 预定、验证、套票预定、套票验证 15-检票报表 18-预订小时报表 19-验证小时报表 25-预订报表（分钟） 26-验证报表（分钟）
        // fixme 大部分报表没透传用户信息进来，会导致异常
        if (in_array($type, [1, 2, 3, 4, 5, 6, 7, 8, 15, 18, 19, 20, 25, 26]) && !empty($loginInfo['memberID'])) {
            $filterData = [];
            if (isset($where['lid'])) {
                if (is_array($where['lid'])) {
                    $filterData['lidList'] = $where['lid'][1];
                } else {
                    $filterData['lidList'] = [$where['lid']];
                }
            }
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($loginInfo['memberID']);
            $condition = $dataAuthLimit->transInOrNotCondition($filterData);
            if ($condition !== false) {
                if (!empty($condition['lidList'])) {
                    $where['lid'] = ['in', $condition['lidList']];
                }
                if (!empty($condition['notLidList'])) {
                    $where['_complex'][] = ['lid' => ['not in', $condition['notLidList']]];
                }
                $data = $model->getInfoV2($where, $type, $page, $size, $field);
            }
        } else {
            $data = $model->getInfoV2($where, $type, $page, $size, $field);
        }
        $orderArr               = [];
        $resellerArr            = [];
        $fidArr                 = [];
        $lidArr                 = [];
        $orderReportInfo        = [];
        $cancelReportInfo       = [];
        $revokeReportInfo       = [];
        $printReportInfo        = [];
        $windowRealReportInfo   = [];
        $windowCancelReportInfo = [];
        $cancelServiceMap       = [];
        $cacelOrder             = [];

        //售后信息
        $afterSaleReportInfo = [];

        //积分消费信息
        $pointsInfo = [
            'orderPointsInfo'  => [],
            'cancelPointsInfo' => [],
            'finishPointsInfo' => [],
            'revokePointsInfo' => [],
        ];

        foreach ($data as $item) {
            $ordersInfo       = $item['orders_info'];
            $cancelOrdersInfo = $item['cancel_orders_info'];
            $revokeOrdersInfo = $item['revoke_orders_info'];
            //窗口实售金额订单
            $windowRealOrdersInfo = $item['window_real_orders_info'] ?? '';
            $windowRealOrdersInfo = json_decode($windowRealOrdersInfo, true);
            //窗口金额订单
            $windowCancelOrdersInfo = $item['window_cancel_orders_info'] ?? '';
            $windowCancelOrdersInfo = json_decode($windowCancelOrdersInfo, true);
            //售后信息
            $afterSaleOrdersInfo = $item['after_sale_info'] ?? '[]';
            $afterSaleOrdersInfo = json_decode($afterSaleOrdersInfo, true);
            $afterSaleOrdersInfo = $afterSaleOrdersInfo ?: [];

            unset($item['orders_info']);
            $resellerArr[]    = $item['reseller_id'];
            $fidArr[]         = $item['fid'];
            $lidArr[]         = $item['lid'];
            $ordersInfo       = json_decode($ordersInfo, true);
            $cancelOrdersInfo = json_decode($cancelOrdersInfo, true);
            $revokeOrdersInfo = json_decode($revokeOrdersInfo, true);

            //取票数量处理
            if (in_array($type, $this->_allow_print_booked) && !$archiverDb) {
                $printOrdersInfo = empty($item['print_orders_info']) ? '' : $item['print_orders_info'];//兼容一些异常，不会直接抛错
                $printOrdersInfo = json_decode($printOrdersInfo, true);
                $printOrdersInfo = $printOrdersInfo ? $printOrdersInfo : [];
            }

            foreach ($ordersInfo as $key => $value) {
                $orderNum        = $value[0];
                $tnum            = $value[1];
                $pointMoney      = $value[2] ?? 0;
                $settlementMoney = $value[3] ?? 0;

                // 订单信息
                if (is_array($value)) {
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }

                    //分账预定和验证报表不叠加，因为单独售卖的产品可能会有多个分账对象，会记录多条数据
                    if (!in_array($type, [10, 11])) {
                        $orderReportInfo[$orderNum]['tnum']                              += $tnum;
                        $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                        $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                    } else {
                        $orderReportInfo[$orderNum]['tnum']                              = $tnum;
                        $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            = $pointMoney;
                        $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] = $settlementMoney;
                    }

                } else {
                    $orderArr[] = $key;
                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $orderReportInfo[$key]['tnum']                                   += $value;
                    $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                }
            }

            foreach ($cancelOrdersInfo as $key => $value) {
                $orderNum        = $value[0];
                $tnum            = $value[1];
                $pointMoney      = $value[2] ?? 0;
                $settlementMoney = $value[3] ?? 0;

                // 取消订单信息
                if (is_array($value)) {
                    $orderArr[]   = $orderNum;
                    $cacelOrder[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }

                    $cancelReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['cancelPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['cancelPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                } else {
                    $orderArr[]   = $key;
                    $cacelOrder[] = $key;
                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $cancelReportInfo[$key]['tnum']                                   += $value;
                    $pointsInfo['cancelPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['cancelPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                }
            }

            foreach ($revokeOrdersInfo as $key => $value) {
                $orderNum        = $value[0];
                $tnum            = $value[1];
                $pointMoney      = $value[2] ?? 0;
                $settlementMoney = $value[3] ?? 0;
                // 撤销订单信息
                if (is_array($value)) {

                    $orderArr[]   = $orderNum;
                    $cacelOrder[] = $orderNum;

                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $revokeReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                } else {

                    $orderArr[]   = $key;
                    $cacelOrder[] = $key;

                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }

                    $revokeReportInfo[$key]['tnum']                                   += $value;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $pointMoney;
                }
            }

            if (is_array($windowRealOrdersInfo)) {
                foreach ($windowRealOrdersInfo as $key => $value) {
                    $orderNum   = $value[0];
                    $num        = $value[1];
                    if (is_array($value)) {
                        $orderArr[] = $orderNum;
                        if (!isset($orderReportInfo[$orderNum])) {
                            $orderReportInfo[$orderNum] = $item;
                        }
                        $windowRealReportInfo[$orderNum]['tnum'] += $num;
                    } else {
                        $orderArr[] = $key;
                        if (!isset($orderReportInfo[$key])) {
                            $orderReportInfo[$key] = $item;
                        }
                        $windowRealReportInfo[$key]['tnum'] += $value;
                    }
                }
            }

            if (is_array($windowCancelOrdersInfo)) {
                foreach ($windowCancelOrdersInfo as $key => $value) {
                    $orderNum   = $value[0];
                    $num        = $value[1];
                    if (is_array($value)) {
                        $orderArr[] = $orderNum;
                        if (!isset($orderReportInfo[$orderNum])) {
                            $orderReportInfo[$orderNum] = $item;
                        }
                        $windowCancelReportInfo[$orderNum]['tnum'] += $num;
                    } else {
                        $orderArr[] = $key;
                        if (!isset($orderReportInfo[$key])) {
                            $orderReportInfo[$key] = $item;
                        }
                        $windowCancelReportInfo[$key]['tnum'] += $value;
                    }
                }
            }

            if (!empty($windowCancelOrdersInfo)) {
                foreach ($windowCancelOrdersInfo as $key => $value) {
                    $orderNum   = $value[0];
                    $num        = $value[1];
                    $cacelOrder[] = $key;
                    $cancelOrderArr[] = $orderNum;
                }

                //退票手续费
                //$cancelInfo = (new OrderQuery())->getCancelServiceByOrderId($cancelOrderArr, 'orderid, service_info');
                //foreach ($cancelInfo as $tmpCancelInfo) {
                //    $serviceInfo = json_decode($tmpCancelInfo['service_info'], true);
                //    foreach ($serviceInfo as $tmpServiceInfo) {
                //        if ($loginInfo['sid'] = $tmpServiceInfo['fid']) {
                //            if (isset($cancelServiceMap[$tmpCancelInfo['orderid']])) {
                //                $cancelServiceMap[$tmpCancelInfo['orderid']] += $tmpServiceInfo['money'];
                //            } else {
                //                $cancelServiceMap[$tmpCancelInfo['orderid']] = $tmpServiceInfo['money'];
                //            }
                //        }
                //    }
                //}
            }

            if (is_array($printOrdersInfo)) {
                foreach ($printOrdersInfo as $key => $value) {
                    $orderNum   = $value[0];
                    $num        = $value[1];
                    // 取票订单信息
                    if (is_array($value)) {
                        //$orderArr[] = $value[0];
                        if (!in_array($orderNum, $orderArr)) {
                            $orderArr[] = $orderNum;
                        }
                        if (!isset($orderReportInfo[$orderNum])) {
                            $orderReportInfo[$orderNum] = $item;
                        }
                        $printReportInfo[$orderNum]['tnum'] += $num;

                    } else {
                        //$orderArr[] = $key;
                        if (!in_array($orderNum, $orderArr)) {
                            $orderArr[] = $key;
                        }
                        if (!isset($orderReportInfo[$key])) {
                            $orderReportInfo[$key] = $item;
                        }
                        $printReportInfo[$key]['tnum'] += $value;
                    }
                }
            }

            //售后信息
            foreach ($afterSaleOrdersInfo as $value) {
                $orderNum             = $value[0];
                $afterSaleTicketNum   = $value[1];
                $afterSaleTicketCode  = $value[2] ?? '';
                $afterSaleRefundMoney = $value[3] ?? 0;
                $afterSaleIncomeMoney = $value[4] ?? 0;
                $orderArr[]           = $orderNum;
                if (!isset($orderReportInfo[$orderNum])) {
                    $orderReportInfo[$orderNum] = $item;
                }
                if (!isset($afterSaleReportInfo[$orderNum])) {
                    $afterSaleReportInfo[$orderNum] = [];
                }

                if (!isset($afterSaleReportInfo[$orderNum]['code'])) {
                    $afterSaleReportInfo[$orderNum]['code'] = [];
                }

                $afterSaleReportInfo[$orderNum]['tnum']   += $afterSaleTicketNum;
                $afterSaleReportInfo[$orderNum]['refund'] += $afterSaleRefundMoney;
                $afterSaleReportInfo[$orderNum]['income'] += $afterSaleIncomeMoney;

                //存在则写入
                $afterSaleTicketCode && $afterSaleReportInfo[$orderNum]['code'][] = $afterSaleTicketCode;
            }
        }

        if (!empty($cacelOrder)) {
            if (!empty($customField)) {
                if ($customField->isCustomCancelService()) {
                    $cancelServiceMap = $this->_getOrderServiceMap($cacelOrder, $where['fid']);
                }
            } else {
                $cancelServiceMap = $this->_getOrderServiceMap($cacelOrder, $where['fid']);
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = ['data' => []];
            if (!empty($customField)) {
                if ($customField->isCustomGroup()) {
                    $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);
                }
            } else {
                $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);
            }
            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = [];
        }

        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');

        $this->_loginInfo = $loginInfo;

        //分批处理
        $return = $this->_blockHandleOrderInfoV2(
            $orderArr,
            $fidArr,
            $lidArr,
            $resellerArr,
            $isResource,
            $resellerMap,
            $noNeed,
            $inList,
            $isLand,
            $titleArr,
            $orderReportInfo,
            $cancelReportInfo,
            $revokeReportInfo,
            $printReportInfo,
            $windowRealReportInfo,
            $windowCancelReportInfo,
            $cancelServiceMap,
            $queryTime,
            $type,
            $pointsInfo,
            $oldHead,
            $customField,
            $afterSaleReportInfo
        );

        return $return;
    }

    /**
     * 获取预订报表订单信息
     *
     * @param $where
     */
    public function getOrderInfo($where, $page = 1, $size = 500)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isResource         = $where['isResource'];
        unset($where['isResource']);

        $data = $model->getInfo($where, 1, $page, $size);

        $orderArr        = [];
        $resellerArr     = [];
        $fidArr          = [];
        $lidArr          = [];
        $orderReportInfo = [];
        foreach ($data as $item) {
            $ordersInfo = $item['orders_info'];
            unset($item['orders_info']);
            $resellerArr[] = $item['reseller_id'];
            $fidArr[]      = $item['fid'];
            $lidArr[]      = $item['lid'];
            $ordersInfo    = json_decode($ordersInfo, true);
            foreach ($ordersInfo as $key => $value) {
                if (is_array($value)) {
                    $orderArr[] = $value[0];
                    if (!isset($orderReportInfo[$value[0]])) {
                        $orderReportInfo[$value[0]]         = $item;
                        $orderReportInfo[$value[0]]['tnum'] = 0;
                    }
                    $orderReportInfo[$value[0]]['tnum'] += $value[1];
                } else {
                    $orderArr[] = $key;
                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $orderReportInfo[$key]['tnum'] += $value;
                }
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }

        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');
        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();

        $memberNameRes = [];
        $memberComRes  = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
            foreach ($memberInfo2 as $item) {
                $memberComRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        //$landModel = new Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            $landApi  = new \Business\CommodityCenter\Land();
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);

        //根据订单号获取信息
        $orderModel = new OrderQuery();
        $orderTools = new OrderTools();

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            //$buyChainModel = new BuyChain();
            //$fxRes         = $buyChainModel->getSplitListByOrderIdSub($orderArr,
            //    'orderid, buyerid, sellerid, cost_money, sale_money, pmode');

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr,
                'orderid, apply_id, refund_num, verified_num, origin_num, lprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');

        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        $return = [];
        if (!empty($orderArr)) {
            $ticketModel  = new Ticket();
            $data         = $orderTools->getOrderList($orderArr);
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr, 'orderid, memo');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']] = $item['memo'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo    = $orderReportInfo[$item['ordernum']];
                    $resellerId   = $orderInfo['reseller_id'];
                    $fid          = $orderInfo['fid'];
                    $tnum         = $orderInfo['tnum'];
                    $tid          = $orderInfo['tid'];
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';
                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    $payMode = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';

                    $fidName     = isset($memberNameRes[$fid]) ? $memberNameRes[$fid] : '未知';
                    $lidName     = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';
                    //                    $coupon       = isset($couponRes[$item['ordernum']]) ? $couponRes[$item['ordernum']] : [];
                    //优惠券数量
                    //                    $couponNum    = 0;
                    //                    //优惠券金额
                    //                    $couponMoney  = 0;
                    //                    //优惠券名称
                    //                    $couponName   = '';
                    //                    foreach ($coupon as $value) {
                    //                        if ($value['aid'] != $fid || $value['fid'] != $resellerId) {
                    //                            continue;
                    //                        }
                    //                        $couponNum++;
                    //                        $couponMoney += $value['coupon_value'];
                    //                        $couponName  .= $value['coupon_name'] . ',';
                    //                    }
                    //                    $couponName   = trim($couponName, ',');
                    //算出结算价

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;
                    //下单金额
                    $allMoney = $price * $tnum - $eMoney;
                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //订单备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';
                    //门票名
                    $ticketInfo = $ticketList[$tid];
                    if ($isResource) {
                        $return[] = [
                            //订单号
                            $item['ordernum'],
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付状态
                            $this->_payStatus[$item['pay_status']],
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //产品名称
                            $lidName . ' ' . $ticketInfo,
                            //卖出支付方式
                            $payMode,
                            //上级供应商
                            $fidName,
                            //出售单价
                            round($price / 100, 2),
                            //订单状态
                            $orderStatus,
                            //身份证
                            "\t" . $item['personid'],
                            //下单门票数
                            $tnum,
                            //下单金额
                            round($allMoney / 100, 2),
                            //                        //优惠券名称
                            //                        $couponName,
                            //                        //优惠券数量
                            //                        $couponNum,
                            //                        //优惠券金额
                            //                        round($couponMoney / 100, 2),
                            //备注
                            $memo,
                        ];
                    } else {
                        $return[] = [
                            //订单号
                            $item['ordernum'],
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付状态
                            $this->_payStatus[$item['pay_status']],
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //产品名称
                            $lidName . ' ' . $ticketInfo,
                            //卖出支付方式
                            $payMode,
                            //上级供应商
                            $fidName,
                            //出售单价
                            round($price / 100, 2),
                            //订单状态
                            $orderStatus,
                            //身份证
                            "\t" . $item['personid'],
                            //下单门票数
                            $tnum,
                            //下单金额
                            round($allMoney / 100, 2),
                            //                        //优惠券名称
                            //                        $couponName,
                            //                        //优惠券数量
                            //                        $couponNum,
                            //                        //优惠券金额
                            //                        round($couponMoney / 100, 2),
                            //备注
                            $memo,
                        ];
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 获取检票报表订单信息
     *
     * @param $where
     * @param  int  $page
     * @param  int  $size
     * @param  int  $type  1 预订  2 验证  3, 预定月报表， 4，验证月报表, 5, 套票预定 6,套票验证 7，套票月预定  8,套票月验证
     *
     * @return array
     */
    public function getCheckInfoV2($where, $titleArr, $page = 1, $size = 500, $type = 2, $noNeed = false,
        $archiverDb = false, $yearMark = '', $loginInfo = [], $customField = null, $extFilter = [])
    {
        $db = 'summary';
        if ($archiverDb) {
            $db = 'summary_history';
        }
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $model              = new \Model\Report\Statistics($db, $yearMark);
        $isResource         = $where['isResource'];
        $isLand             = $where['is_land'];
        unset($where['isResource']);
        unset($where['is_land']);
        if ($isLand) {
            $isResource = 1;
        }

        //套票子票不展示 加下过滤条件
        $isFilterSonTicket = $extFilter['filter_son_ticket'] ?? false;
        if ($isFilterSonTicket && in_array($type, [2, 4, 19, 14, 24, 26])) {
            $where = $model->filterSonTicket($where, $extFilter);
        }

        $field = 'id,orders_info,finish_orders_info,revoke_orders_info,reseller_id,fid,lid,tid,today_check';
        if ($type == 15) {
            $field = 'orders_info, reseller_id, fid, lid, tid';
        }

        //验证报表加下售后字段
        if ($this->_isCheckedType($type)) {
            $field .= ',after_sale_info';
        }
        
        if($type == 15) {   // 检票报表，特殊处理。
            $field .= ",card_type,update_time,ext_content";
        }

        $data = [];
        // 预定、验证、套票预定、套票验证 15-检票报表 18-预订小时报表 19-验证小时报表 25-预订报表（分钟） 26-验证报表（分钟）
        // fixme 大部分报表没有透传用户信息，会导致异常
        if (in_array($type, [1, 2, 3, 4, 5, 6, 7, 8, 15, 18, 19, 20, 25, 26]) && !empty($loginInfo['memberID'])) {
            $filterData = [];
            if (isset($where['lid'])) {
                if (is_array($where['lid'])) {
                    $filterData['lidList'] = $where['lid'][1];
                } else {
                    $filterData['lidList'] = [$where['lid']];
                }
            }
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($loginInfo['memberID']);
            $condition = $dataAuthLimit->transInOrNotCondition($filterData);
            if ($condition !== false) {
                if (!empty($condition['lidList'])) {
                    $where['lid'] = ['in', $condition['lidList']];
                }
                if (!empty($condition['notLidList'])) {
                    $where['_complex'][] = ['lid' => ['not in', $condition['notLidList']]];
                }
                $data = $model->getInfoV2($where, $type, $page, $size, $field);
            }
        } else {
            $data = $model->getInfoV2($where, $type, $page, $size, $field);
        }
        $cardCheckData = [];
        $newDataSet = [];
        if($type == 15){
            foreach($data as $_){
                if(in_array($_['card_type'], ExternalCard::cardSet)){
                    $cardCheckData[] = $_;
                } else {
                    $newDataSet[] = $_;
                }
            }
            $data = $newDataSet;
        }
        // DebugUtil::dumpe($cardCheckData, $data);
        
        $orderArr        = [];
        $resellerArr     = [];
        $lidArr          = [];
        $fidArr          = [];
        $orderReportInfo = [];

        $finishReportInfo = [];
        $revokeReportInfo = [];
        $cancelServiceMap = [];

        //售后信息
        $afterSaleReportInfo = [];

        //积分消费信息
        $pointsInfo = [
            'checkPointsInfo'  => [],
            'cancelPointsInfo' => [],
            'finishPointsInfo' => [],
            'revokePointsInfo' => [],
        ];

        $specialIds = [];

        //标记：在开发取票统计的时候发现验证报表导出的和平台展示不一致，原因是同一订单（多票）跨天验证，被合并统计。
        //后面单独找时间优化下
        foreach ($data as $item) {
            $ordersInfo       = $item['orders_info'];
            $finishOrdersInfo = $item['finish_orders_info'];
            $revokeOrdersInfo = $item['revoke_orders_info'];

            $afterSaleOrdersInfo = $item['after_sale_info'] ?? '[]';

            unset($item['orders_info']);
            $resellerArr[]    = $item['reseller_id'];
            $fidArr[]         = $item['fid'];
            $lidArr[]         = $item['lid'];
            $ordersInfo       = json_decode($ordersInfo, true);
            $finishOrdersInfo = json_decode($finishOrdersInfo, true);
            $revokeOrdersInfo = json_decode($revokeOrdersInfo, true);

            $afterSaleOrdersInfo = json_decode($afterSaleOrdersInfo, true);

            $ordersInfo       = $ordersInfo ? $ordersInfo : [];
            $finishOrdersInfo = $finishOrdersInfo ? $finishOrdersInfo : [];
            $revokeOrdersInfo = $revokeOrdersInfo ? $revokeOrdersInfo : [];

            $afterSaleOrdersInfo = $afterSaleOrdersInfo ?: [];

            foreach ($ordersInfo as $key => $value) {
                $orderNum        = $value[0];
                $tnum            = $value[1];
                $pointMoney      = $value[2] ?? 0;
                $settlementMoney = $value[3] ?? 0;
                if (is_array($value)) {
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }

                    $orderReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['checkPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['checkPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                    //type=15 终端过闸报表，会多次记录分终端验证的数量
                    // if ($type != 15) {
                    //     $orderReportInfo[$value[0]]['tnum'] += $value[1];
                    // } else {
                    //     $orderReportInfo[$value[0]]['tnum'] = $value[1];
                    // }

                    if (in_array($orderNum,  ['67150887111430', '67143462712493'])) {
                        $specialIds[$orderNum][] = $item['id'];
                    }
                } else {
                    $orderArr[] = $key;
                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $orderReportInfo[$key]['tnum']                                   += $value;
                    $pointsInfo['checkPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['checkPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                    if (in_array($orderNum,  ['67150887111430', '67143462712493'])) {
                        $specialIds[$orderNum][] = $item['id'];
                    }
                }
            }

            foreach ($finishOrdersInfo as $key => $value) {
                $orderNum        = $value[0];
                $tnum            = $value[1];
                $pointMoney      = $value[2] ?? 0;
                $settlementMoney = $value[3] ?? 0;
                if (is_array($value)) {
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $finishReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['finishPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['finishPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                } else {
                    $orderArr[] = $key;
                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $finishReportInfo[$key]['tnum']                                   += $value;
                    $pointsInfo['finishPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['finishPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                }
            }

            foreach ($revokeOrdersInfo as $key => $value) {
                $orderNum        = $value[0];
                $tnum            = $value[1];
                $pointMoney      = $value[2] ?? 0;
                $settlementMoney = $value[3] ?? 0;
                if (is_array($value)) {
                    $orderArr[]   = $orderNum;
                    $cacelOrder[] = $orderNum;

                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $revokeReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                } else {

                    $orderArr[]   = $key;
                    $cacelOrder[] = $key;

                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $revokeReportInfo[$key]['tnum']                                   += $value;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;
                }
            }

            //售后信息
            foreach ($afterSaleOrdersInfo as $value) {
                $orderNum             = $value[0];
                $afterSaleTicketNum   = $value[1];
                $afterSaleTicketCode  = $value[2] ?? '';
                $afterSaleRefundMoney = $value[3] ?? 0;
                $afterSaleIncomeMoney = $value[4] ?? 0;
                $orderArr[]           = $orderNum;
                if (!isset($orderReportInfo[$orderNum])) {
                    $orderReportInfo[$orderNum] = $item;
                }
                if (!isset($afterSaleReportInfo[$orderNum])) {
                    $afterSaleReportInfo[$orderNum] = [];
                }

                if (!isset($afterSaleReportInfo[$orderNum]['code'])) {
                    $afterSaleReportInfo[$orderNum]['code'] = [];
                }

                $afterSaleReportInfo[$orderNum]['tnum']   += $afterSaleTicketNum;
                $afterSaleReportInfo[$orderNum]['refund'] += $afterSaleRefundMoney;
                $afterSaleReportInfo[$orderNum]['income'] += $afterSaleIncomeMoney;

                //存在则写入
                $afterSaleTicketCode && $afterSaleReportInfo[$orderNum]['code'][] = $afterSaleTicketCode;
            }
        }

        if (!empty($cacelOrder)) {
            if (!empty($customField)) {
                if ($customField->isCustomCancelService()) {
                    $cancelServiceMap = $this->_getOrderServiceMap($cacelOrder, $where['fid']);
                }
            } else {
                $cancelServiceMap = $this->_getOrderServiceMap($cacelOrder, $where['fid']);
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = ['data' => []];
            if (!empty($customField)) {
                if ($customField->isCustomGroup()) {
                    $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);
                }
            } else {
                $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);
            }

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = [];
        }
        unset($data);

        $specialShowEMoney = [];
        if (!empty($specialIds) && in_array($type, [2, 4, 19])) {
            $specialLogIds = [
                2  => [566486012, 628311016],//日
                4  => [36124065],//月
                19 => [290895697, 358050589],//小时
            ];
            foreach ($specialIds as $specialOrderNum => $specialId) {
                $specialShowEMoney[$specialOrderNum] = false;
                if (!empty(array_intersect($specialLogIds[$type], $specialId))) {
                    $specialShowEMoney[$specialOrderNum] = true;
                }
            }
        }

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //散客
        $resellerMap      = load_config('reseller_map', 'trade_record');
        $resellerMap      = array_column($resellerMap, 'id');
        $this->_loginInfo = $loginInfo;
        //分批处理
        $return = $this->_blockHandlCheckInfoV2(
            $orderArr,
            $fidArr,
            $lidArr,
            $resellerArr,
            $isResource,
            $resellerMap,
            $noNeed,
            $inList,
            $isLand,
            $titleArr,
            $orderReportInfo,
            $finishReportInfo,
            $revokeReportInfo,
            $cancelServiceMap,
            $type,
            $pointsInfo,
            $specialShowEMoney,
            $afterSaleReportInfo,
            $customField
        );
        if(!empty($cardCheckData)) {
            $cardCheckReport = $this->handleCardCheckReport($cardCheckData);
            if(!empty($cardCheckReport)) {
                $return = array_merge($return, $cardCheckReport);
            }
        }

        return $return;
    }

    /**
     * 获取检票报表订单信息
     *
     * @param $where
     * @param  int  $page
     * @param  int  $size
     */
    public function getCheckInfo($where, $page = 1, $size = 500)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isResource         = $where['isResource'];
        unset($where['isResource']);
        $data = $model->getInfo($where, 2, $page, $size);

        $orderArr        = [];
        $resellerArr     = [];
        $lidArr          = [];
        $fidArr          = [];
        $orderReportInfo = [];
        foreach ($data as $item) {

            $ordersInfo = $item['orders_info'];
            unset($item['orders_info']);
            $resellerArr[] = $item['reseller_id'];
            $fidArr[]      = $item['fid'];
            $lidArr[]      = $item['lid'];
            $ordersInfo    = json_decode($ordersInfo, true);
            foreach ($ordersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                if (is_array($value)) {
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $orderReportInfo[$orderNum]['tnum'] += $tnum;
                } else {
                    $orderArr[] = $key;
                    if (!isset($orderReportInfo[$key])) {
                        $orderReportInfo[$key] = $item;
                    }
                    $orderReportInfo[$key]['tnum'] += $value;
                }
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }
        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');
        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();

        $memberNameRes = [];
        $memberComRes  = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
            foreach ($memberInfo2 as $item) {
                $memberComRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        //$landModel = new Land();
        $landApi   = new \Business\CommodityCenter\Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            $buyChainModel = new SubOrderSplit();
            $fxRes         = $buyChainModel->getListByOrderArr($orderArr,
                'orderid, buyerid, sellerid, cost_money, sale_money, pmode, level');

            //订单查询迁移三期
            //$orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            //$fxRes = $orderAidsSplitQueryLib->getListByOrderArrNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderToolModel = new OrderTools('slave');
            $field          = 'orderid, apply_id, refund_num, verified_num, origin_num, lprice';
            $lPriceRes      = $orderToolModel->getOrderApplyInfo($orderArr, $field);
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
        }

        //获取验证人员和渠道
        $checkInfo = [];
        if (!empty($orderArr)) {
            $orderTrackModel = new OrderTrack();
            $sourceType      = $orderTrackModel::getSourceList();
            $checkRes        = $orderTrackModel->getListByOrders($orderArr, 'ordernum,source,oper_member, insertTime',
                5);
            if (is_array($checkRes)) {
                foreach ($checkRes as $item) {
                    $resellers[]                  = $item['oper_member'];
                    $checkInfo[$item['ordernum']] = $item;
                }
            }
        }

        //获取订单操作员信息
        if (!empty($resellers)) {
            $resellerArrs = array_unique($resellers);
            //根据操作员id获取信息
            $memberOperator = [];

            if (!empty($resellerArrs)) {
                $memberInfo = $memberModel->getMemberInfoByMulti($resellerArrs, 'id', 'id, dname');
                foreach (\SplFixedArray::fromArray($memberInfo) as $item) {
                    $memberOperator[$item['id']] = $item['dname'];
                }
                unset($memberInfo);
            }
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');

        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        //根据订单号获取信息
        $return = [];
        if (!empty($orderArr)) {
            //根据订单号获取信息
            $orderTools  = new OrderTools();
            $ticketModel = new Ticket();
            $data        = $orderTools->getOrderList($orderArr);

            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr, 'orderid, memo');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']] = $item['memo'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo    = $orderReportInfo[$item['ordernum']];
                    $resellerId   = $orderInfo['reseller_id'];
                    $fid          = $orderInfo['fid'];
                    $tnum         = $orderInfo['tnum'];
                    $tid          = $orderInfo['tid'];
                    $checkInfoExt = $checkInfo[$item['ordernum']];
                    $operaMember  = $checkInfoExt['oper_member'];
                    $source       = $checkInfoExt['source'];

                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = isset($resourcePrice[$item['ordernum']]) ? $resourcePrice[$item['ordernum']] : 0;
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    $payMode = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';

                    $fidName     = isset($memberNameRes[$fid]) ? $memberNameRes[$fid] : '未知';
                    $lidName     = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';
                    $checkPeople = isset($memberOperator[$operaMember]) && $operaMember != 1 ? $memberOperator[$operaMember] : '未知';

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;

                    //下单金额
                    $allMoney = $price * $tnum - $eMoney;
                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //门票名
                    $ticketInfo = $ticketList[$tid];
                    //备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';

                    $return[] = [
                        //订单号
                        $item['ordernum'],
                        //下单时间
                        $item['ordertime'],
                        //预计游玩时间
                        $item['playtime'],
                        //开始有效时间
                        $item['begintime'],
                        //截止有效时间
                        $item['endtime'],
                        //完成时间
                        $checkInfoExt['insertTime'],
                        //支付状态
                        $this->_payStatus[$item['pay_status']],
                        //分销商企业名称
                        $comName,
                        //分销商账户名称
                        $resellerName,
                        //分组
                        $group,
                        //取票人
                        $item['ordername'],
                        //取票手机
                        $item['ordertel'],
                        //产品名称
                        $lidName . ' ' . $ticketInfo,
                        //卖出支付方式
                        $payMode,
                        //上级供应商
                        $fidName,
                        //出售单价
                        round($price / 100, 2),
                        //订单状态
                        $orderStatus,
                        //身份证
                        "\t" . $item['personid'],
                        //凭证码
                        $item['code'],
                        //下单门票数
                        $tnum,
                        //下单金额
                        round($allMoney / 100, 2),
                        //验证人员
                        $checkPeople,
                        //验证渠道
                        $sourceType[$source],
                        //备注
                        $memo,
                    ];
                }
            }
        }

        return $return;
    }

    /**
     * 获取取消报表订单信息
     */
    public function getCancelInfo($where, $page = 1, $size = 100)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isResource         = $where['isResource'];
        unset($where['isResource']);

        $data            = $model->getInfo($where, 3, $page, $size);
        $orderArr        = [];
        $resellerArr     = [];
        $lidArr          = [];
        $fidArr          = [];
        $orderReportInfo = [];
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');
        foreach ($data as $item) {
            $ordersInfo = $item['orders_info'];
            unset($item['orders_info']);
            $resellerArr[] = $item['reseller_id'];
            $fidArr[]      = $item['fid'];
            $lidArr[]      = $item['lid'];
            $ordersInfo    = json_decode($ordersInfo, true);
            foreach ($ordersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                $orderArr[] = $orderNum;
                if (!isset($orderReportInfo[$orderNum])) {
                    $orderReportInfo[$orderNum] = $item;
                }
                $orderReportInfo[$orderNum]['tnum'] += $tnum;
            }
        }
        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }
        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //获取全部订单号

        $orderArr = array_unique($orderArr);

        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();

        $memberNameRes = [];
        $memberComRes  = [];

        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
            foreach ($memberInfo2 as $item) {
                $memberComRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        //$landModel = new Land();
        $landApi   = new \Business\CommodityCenter\Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取手续费
        $orderQueryModel = new OrderQuery();
        $cancelInfo      = $orderQueryModel->getCancelServiceByOrderId($orderArr, 'orderid, service_info');
        $serviceArr      = [];
        if (!empty($cancelInfo) && is_array($cancelInfo)) {
            $serviceRes = [];
            foreach ($cancelInfo as $item) {
                $serviceInfo = @json_decode($item['service_info'], true);
                if (isset($serviceRes[$item['orderid']])) {
                    $serviceRes[$item['orderid']] = array_merge($serviceRes[$item['orderid']], $serviceInfo);
                } else {
                    $serviceRes[$item['orderid']] = $serviceInfo;
                }
            }

            if (!empty($serviceRes)) {
                foreach ($serviceRes as $key => $item) {
                    foreach ($item as $value) {
                        if (isset($serviceArr[$key][$value['aid']][$value['fid']])) {
                            $serviceArr[$key][$value['aid']][$value['fid']] += $value['money'];
                        } else {
                            $serviceArr[$key][$value['aid']][$value['fid']] = $value['money'];
                        }
                    }
                }
            }
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            $buyChainModel = new SubOrderSplit();
            $fxRes         = $buyChainModel->getListByOrderArr($orderArr,
                'orderid, buyerid, sellerid, cost_money, sale_money, pmode, level');

            //订单查询迁移三期
            //$orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            //$fxRes = $orderAidsSplitQueryLib->getListByOrderArrNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderToolModel = new OrderTools('slave');
            $field          = 'orderid, apply_id, refund_num, verified_num, origin_num, lprice';
            $lPriceRes      = $orderToolModel->getOrderApplyInfo($orderArr, $field);
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');

        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        //根据订单号获取信息
        $orderTools  = new OrderTools();
        $ticketModel = new Ticket();

        $return = [];
        if (!empty($orderArr)) {
            $data         = $orderTools->getOrderList($orderArr);
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr, 'orderid, memo');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']] = $item['memo'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo    = $orderReportInfo[$item['ordernum']];
                    $resellerId   = $orderInfo['reseller_id'];
                    $fid          = $orderInfo['fid'];
                    $tnum         = $orderInfo['tnum'];
                    $tid          = $orderInfo['tid'];
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }
                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    $payMode = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';

                    $fidName     = isset($memberNameRes[$fid]) ? $memberNameRes[$fid] : '未知';
                    $lidName     = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';
                    //退票手续费
                    $serviceMoney = isset($serviceArr[$item['ordernum']][$fid][$resellerId]) ? $serviceArr[$item['ordernum']][$fid][$resellerId] : 0;
                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;
                    //下单金额
                    $allMoney = $price * $tnum - $eMoney;
                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //门票名
                    $ticketInfo = $ticketList[$tid];
                    //备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';

                    $return[] = [
                        //订单号
                        $item['ordernum'],
                        //下单时间
                        $item['ordertime'],
                        //预计游玩时间
                        $item['playtime'],
                        //开始有效时间
                        $item['begintime'],
                        //截止有效时间
                        $item['endtime'],
                        //完成时间
                        $item['dtime'],
                        //取消时间
                        $item['ctime'],
                        //支付状态
                        $this->_payStatus[$item['pay_status']],
                        //分销商企业名称
                        $comName,
                        //分销商账户名称
                        $resellerName,
                        //分组
                        $group,
                        //取票人
                        $item['ordername'],
                        //取票手机
                        $item['ordertel'],
                        //产品名称
                        $lidName . ' ' . $ticketInfo,
                        //卖出支付方式
                        $payMode,
                        //上级供应商
                        $fidName,
                        //出售单价
                        round($price / 100, 2),
                        //订单状态
                        $orderStatus,
                        //身份证
                        "\t" . $item['personid'],
                        //取消门票数
                        $tnum,
                        //取消金额
                        round($allMoney / 100, 2),
                        //手续费
                        round($serviceMoney / 100, 2),
                        //订单备注
                        $memo,
                    ];
                }

            }
        }

        return $return;
    }

    /**
     * 获取撤销撤改信息
     * <AUTHOR>
     * @date   2017-4-12
     */
    public function getRevokeInfo($where, $page = 1, $size = 100)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isResource         = $where['isResource'];
        unset($where['isResource']);

        $data = $model->getInfo($where, 4, $page, $size);

        $orderArr        = [];
        $resellerArr     = [];
        $lidArr          = [];
        $fidArr          = [];
        $orderReportInfo = [];
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');
        foreach ($data as $item) {
            $ordersInfo = $item['orders_info'];
            unset($item['orders_info']);
            $resellerArr[] = $item['reseller_id'];
            $fidArr[]      = $item['fid'];
            $lidArr[]      = $item['lid'];
            $ordersInfo    = json_decode($ordersInfo, true);
            foreach ($ordersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                $orderArr[] = $orderNum;
                if (!isset($orderReportInfo[$orderNum])) {
                    $orderReportInfo[$orderNum] = $item;
                }
                $orderReportInfo[$orderNum]['tnum'] += $tnum;
            }
        }
        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况

            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }

        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //获取全部订单号
        $orderArr = array_unique($orderArr);

        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();

        $memberNameRes = [];
        $memberComRes  = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');

            foreach ($memberInfo2 as $item) {
                $memberComRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        //$landModel = new Land();
        $landApi  = new \Business\CommodityCenter\Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取手续费
        $orderQueryModel = new OrderQuery();
        $cancelInfo      = $orderQueryModel->getCancelServiceByOrderId($orderArr, 'orderid, service_info');
        $serviceArr      = [];
        if (!empty($cancelInfo) && is_array($cancelInfo)) {
            $serviceRes = [];
            foreach ($cancelInfo as $item) {
                $serviceInfo = @json_decode($item['service_info'], true);
                if (isset($serviceRes[$item['orderid']])) {
                    $serviceRes[$item['orderid']] = array_merge($serviceRes[$item['orderid']], $serviceInfo);
                } else {
                    $serviceRes[$item['orderid']] = $serviceInfo;
                }
            }

            if (!empty($serviceRes)) {
                foreach ($serviceRes as $key => $item) {
                    foreach ($item as $value) {
                        if (isset($serviceArr[$key][$value['aid']][$value['fid']])) {
                            $serviceArr[$key][$value['aid']][$value['fid']] += $value['money'];
                        } else {
                            $serviceArr[$key][$value['aid']][$value['fid']] = $value['money'];
                        }
                    }
                }
            }
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            //$buyChainModel = new BuyChain();
            //$fxRes         = $buyChainModel->getListByOrderId($orderArr);

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr, 'orderid,lprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');

        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        //根据订单号获取信息
        $orderModel  = new OrderQuery();
        $orderTools  = new OrderTools();
        $ticketModel = new Ticket();

        $return = [];
        if (!empty($orderArr)) {
            $data         = $orderTools->getOrderList($orderArr);
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $orderTools->getOrderDetailInfo($orderArr, 'orderid, memo');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']] = $item['memo'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo    = $orderReportInfo[$item['ordernum']];
                    $resellerId   = $orderInfo['reseller_id'];
                    $fid          = $orderInfo['fid'];
                    $tnum         = $orderInfo['tnum'];
                    $tid          = $orderInfo['tid'];
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    $payMode = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';

                    $fidName     = isset($memberNameRes[$fid]) ? $memberNameRes[$fid] : '未知';
                    $lidName     = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';
                    //退票手续费
                    $serviceMoney = isset($serviceArr[$item['ordernum']][$fid][$resellerId]) ? $serviceArr[$item['ordernum']][$fid][$resellerId] : 0;
                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;
                    //下单金额
                    $allMoney = $price * $tnum - $eMoney;
                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //门票名
                    $ticketInfo = $ticketList[$tid];
                    //备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';

                    $return[] = [
                        //订单号
                        $item['ordernum'],
                        //下单时间
                        $item['ordertime'],
                        //预计游玩时间
                        $item['playtime'],
                        //开始有效时间
                        $item['begintime'],
                        //截止有效时间
                        $item['endtime'],
                        //完成时间
                        $item['dtime'],
                        //取消时间
                        $item['ctime'],
                        //支付状态
                        $this->_payStatus[$item['pay_status']],
                        //分销商企业名称
                        $comName,
                        //分销商账户名称
                        $resellerName,
                        //分组
                        $group,
                        //取票人
                        $item['ordername'],
                        //取票手机
                        $item['ordertel'],
                        //产品名称
                        $lidName . ' ' . $ticketInfo,
                        //卖出支付方式
                        $payMode,
                        //上级供应商
                        $fidName,
                        //出售单价
                        round($price / 100, 2),
                        //订单状态
                        $orderStatus,
                        //身份证
                        "\t" . $item['personid'],
                        //取消门票数
                        $tnum,
                        //取消金额
                        round($allMoney / 100, 2),
                        //手续费
                        round($serviceMoney / 100, 2),
                        //备注
                        $memo,
                    ];
                }
            }
        }

        return $return;
    }

    /**
     * 应收应付
     *
     * @param $where
     * @param  int  $page
     * @param  int  $size
     *
     * @return array
     */
    public function getPayWayCheck($where, $page = 1, $size = 100)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        //$type = 1  查找分销商   = 2  查找供应商
        $type = $where['type'];
        unset($where['type']);

        $isResource = $where['isResource'];
        unset($where['isResource']);

        $data = $model->getInfo($where, 5, $page, $size);

        $orderArr    = [];
        $resellerArr = [];

        $lidArr = [];
        $fidArr = [];
        $pidArr = [];

        foreach ($data as $item) {
            $checkOrdersInfo  = $item['orders_info'];
            $cancelOrdersInfo = $item['cancel_orders_info'];
            unset($item['orders_info']);
            $resellerArr[]   = $item['reseller_id'];
            $fidArr[]        = $item['fid'];
            $lidArr[]        = $item['lid'];
            $pidArr[]        = $item['pid'];
            $checkOrdersInfo = json_decode($checkOrdersInfo, true);

            foreach ($checkOrdersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                $orderArr[]                                     = $orderNum;
                $orderNum[$orderNum][$item['date']]             = $tnum;
                $checkOrderReportInfo[$orderNum][$item['date']] = $item;
            }
            $cancelOrdersInfo = json_decode($cancelOrdersInfo, true);
            foreach ($cancelOrdersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                $orderArr[]                                      = $orderNum;
                $cancelNum[$orderNum][$item['date']]             = $tnum;
                $cancelOrderReportInfo[$orderNum][$item['date']] = $item;
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况

            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }

        unset($data);

        //获取支付方式的配置
        $payWayList = load_config('payway_list', 'business');
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);

        //获取全部订单号
        $orderArr = array_unique($orderArr);
        sort($orderArr);

        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();
        $memberRes   = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach (\SplFixedArray::fromArray($memberInfo) as $item) {
                $memberRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
            foreach (\SplFixedArray::fromArray($memberInfo2) as $item) {
                if (!$item['com_name']) {
                    continue;
                }
                $memberRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取产品信息
        $pidInfo = [];
        if (!empty($pidArr)) {
            $commodityProductBiz = new Product();
            $pidRes              = $commodityProductBiz->getProductInfoByIds($pidArr);

            foreach ($pidRes as $item) {
                $pidInfo[$item['id']] = $item['p_name'];
            }
            unset($pidRes);
        }

        //获取分销链信息
        $fxInfo        = [];
        $orderInfo     = [];
        $resourcePrice = [];

        if (!empty($orderArr)) {
            //$buyChainModel = new BuyChain();
            //$fxRes         = $buyChainModel->getListByOrderId($orderArr);

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']] = $item['sale_money'];
                }
            }
            unset($fxRes);

            //获取订单信息
            $orderQueryModel = new OrderTools();
            $orderRes        = $orderQueryModel->getOrderList($orderArr, 'ordernum, member');
            if (is_array($orderRes)) {
                foreach ($orderRes as $item) {
                    $orderInfo[$item['ordernum']] = $item['member'];
                }
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

        }

        //        //获取优惠券信息
        //        $couponRes  = [];
        //        if (!empty($orderArr)) {
        //            $couponModel = new Coupon();
        //            $couponInfo  = $couponModel->getListByOrderId($orderArr, 'fid, aid, ordernum, coupon_name, coupon_value');
        //            if (!empty($couponInfo)) {
        //                foreach ($couponInfo as $item) {
        //                    $couponRes[$item['ordernum']][] = $item;
        //                }
        //            }
        //            unset($couponInfo);
        //        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr, 'orderid,lprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
            unset($lPriceRes);
        }

        //获取手续费
        $orderQueryModel = new OrderQuery();
        $cancelInfo      = $orderQueryModel->getCancelServiceByOrderId($orderArr, 'orderid, service_info');
        $serviceArr      = [];
        if (!empty($cancelInfo) && is_array($cancelInfo)) {
            $serviceRes = [];
            foreach ($cancelInfo as $item) {
                $serviceInfo = @json_decode($item['service_info'], true);
                if (isset($serviceRes[$item['orderid']])) {
                    $serviceRes[$item['orderid']] = array_merge($serviceRes[$item['orderid']], $serviceInfo);
                } else {
                    $serviceRes[$item['orderid']] = $serviceInfo;
                }
            }

            if (!empty($serviceRes)) {
                foreach ($serviceRes as $key => $item) {
                    foreach ($item as $value) {
                        if (isset($serviceArr[$key][$value['aid']][$value['fid']])) {
                            $serviceArr[$key][$value['aid']][$value['fid']] += $value['money'];
                        } else {
                            $serviceArr[$key][$value['aid']][$value['fid']] = $value['money'];
                        }
                    }
                }
            }
        }

        $return = [];
        foreach ($orderArr as $item) {

            if (isset($checkOrderReportInfo[$item])) {
                foreach ($checkOrderReportInfo[$item] as $date => $value) {
                    if ($type == 1) {
                        $member = isset($memberRes[$value['reseller_id']]) ? $memberRes[$value['reseller_id']] : '未知';
                    } else {
                        $member = isset($memberRes[$value['fid']]) ? $memberRes[$value['fid']] : '未知';
                    }
                    $num = isset($orderNum[$item][$date]) ? $orderNum[$item][$date] : 0;
                    if (in_array($value['reseller_id'], $resellerMap) || $value['reseller_id'] == 112) {
                        //如果是散客 分销商从订单信息里取
                        $value['reseller_id'] = $orderInfo[$item];
                    }
                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item][$value['fid']][$value['reseller_id']])) {
                            $price = $fxInfo[$item][$value['fid']][$value['reseller_id']];
                        } elseif (isset($lPriceInfo[$item])) {
                            $price = $lPriceInfo[$item];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //                    $coupon       = isset($couponRes[$item]) ? $couponRes[$item] : [];
                    //                    //优惠券数量
                    //                    $couponNum    = 0;
                    //                    //优惠券金额
                    //                    $couponMoney  = 0;
                    //                    //优惠券名称
                    //                    $couponName   = '';
                    //
                    //                    foreach ($coupon as $v) {
                    //                        if ($v['aid'] != $value['fid'] || $v['fid'] != $value['reseller_id']) {
                    //                            continue;
                    //                        }
                    //                        $couponNum++;
                    //                        $couponMoney += $v['coupon_value'];
                    //                        $couponName  .= $v['coupon_name'] . ',';
                    //                    }
                    //                    $couponName = trim($couponName, ',');

                    $allPrice = $num * $price;
                    if (in_array($value['pay_way'], $this->_specialPayWay)) {
                        $allPrice = 0;
                    }
                    if (!in_array($value['pay_way'], $this->_specialPayWayTwo) && $type == 1) {
                        $value['pay_way'] = 0;
                    }
                    //优惠金额
                    $eMoney = isset($couponList[$item][$value['fid']][$value['reseller_id']]) ?
                        $couponList[$item][$value['fid']][$value['reseller_id']] : 0;
                    //分组情况'
                    $group    = $inList[$value['reseller_id']] ?: '';
                    $return[] = [
                        //订单号
                        $item,
                        //操作
                        '验证',
                        //日期
                        $date,
                        //产品
                        $pidInfo[$value['pid']],
                        //数量
                        $num,
                        //企业名称
                        $member,
                        //分组
                        $group,
                        //收款账户
                        $payWayList[$value['pay_way']],
                        //产品金额
                        round($allPrice / 100, 2),
                        //手续费
                        0,
                        //实际金额
                        round(($allPrice - $eMoney) / 100, 2),
                        //                        //优惠券名称
                        //                        $couponName,
                        //                        //优惠券数量
                        //                        $couponNum,
                        //                        //优惠券金额
                        //                        round($couponMoney / 100, 2),
                    ];
                }
            }

            if (isset($cancelOrderReportInfo[$item])) {
                foreach ($cancelOrderReportInfo[$item] as $date => $value) {
                    if ($type == 1) {
                        $member = isset($memberRes[$value['reseller_id']]) ? $memberRes[$value['reseller_id']] : '未知';
                    } else {
                        $member = isset($memberRes[$value['fid']]) ? $memberRes[$value['fid']] : '未知';
                    }
                    if (in_array($value['reseller_id'], $resellerMap) || $value['reseller_id'] == 112) {
                        //如果是散客 分销商从订单信息里取
                        $value['reseller_id'] = $orderInfo[$item];
                    }
                    //出售单价 比较特殊 要考虑到分销链
                    if (isset($fxInfo[$item][$value['fid']][$value['reseller_id']])) {
                        $price = $fxInfo[$item][$value['fid']][$value['reseller_id']];
                    } elseif (isset($lPriceInfo[$item])) {
                        $price = $lPriceInfo[$item];
                    } else {
                        $price = 0;
                    }
                    //                    $coupon       = isset($couponRes[$item]) ? $couponRes[$item] : [];
                    //                    //优惠券数量
                    //                    $couponNum    = 0;
                    //                    //优惠券金额
                    //                    $couponMoney  = 0;
                    //                    //优惠券名称
                    //                    $couponName   = '';
                    //                    foreach ($coupon as $v) {
                    //                        if ($v['aid'] != $value['fid'] || $v['fid'] != $value['reseller_id']) {
                    //                            continue;
                    //                        }
                    //                        $couponNum++;
                    //                        $couponMoney += $v['coupon_value'];
                    //                        $couponName  .= $v['coupon_name'] . ',';
                    //                    }
                    //                    $couponName = trim($couponName, ',');

                    //退票手续费
                    $serviceMoney = isset($serviceArr[$item][$value['fid']][$value['reseller_id']]) ? $serviceArr[$item][$value['fid']][$value['reseller_id']] : 0;
                    $num          = isset($cancelNum[$item][$date]) ? $cancelNum[$item][$date] : 0;
                    $allPrice     = $num * $price;
                    if (in_array($value['pay_way'], $this->_specialPayWay)) {
                        $allPrice = 0;
                    }
                    if (!in_array($value['pay_way'], $this->_specialPayWayTwo) && $type == 1) {
                        $value['pay_way'] = 0;
                    }
                    //优惠金额
                    $eMoney = isset($couponList[$item][$value['fid']][$value['reseller_id']]) ?
                        $couponList[$item][$value['fid']][$value['reseller_id']] : 0;
                    //分组情况

                    $group = $inList[$value['reseller_id']] ?: '';

                    $return[] = [
                        //订单号
                        $item,
                        //操作
                        '撤销撤改',
                        //日期
                        $date,
                        //产品
                        $pidInfo[$value['pid']],
                        //数量
                        $num,
                        //企业名称
                        $member,
                        //分组
                        $group,
                        //收款账户
                        $payWayList[$value['pay_way']],
                        //产品金额
                        round($allPrice / 100, 2),
                        //手续费
                        round($serviceMoney / 100, 2),
                        //实际金额
                        round(($allPrice - $serviceMoney - $eMoney) / 100, 2),
                        //                        //优惠券名称
                        //                        $couponName,
                        //                        //优惠券数量
                        //                        $couponNum,
                        //                        //优惠券金额
                        //                        round($couponMoney / 100, 2),
                    ];
                }
            }
        }

        return $return;
    }

    /**
     * 获取过期完结报表订单信息
     *
     * @param $where
     */
    public function getFinishInfo($where, $page = 1, $size = 100)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isResource         = $where['isResource'];
        unset($where['isResource']);

        $data = $model->getInfo($where, 6, $page, $size);

        $orderArr        = [];
        $resellerArr     = [];
        $fidArr          = [];
        $lidArr          = [];
        $orderReportInfo = [];
        foreach ($data as $item) {
            $ordersInfo = $item['orders_info'];
            unset($item['orders_info']);
            $resellerArr[] = $item['reseller_id'];
            $fidArr[]      = $item['fid'];
            $lidArr[]      = $item['lid'];
            $ordersInfo    = json_decode($ordersInfo, true);
            foreach ($ordersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                $orderArr[] = $orderNum;
                if (!isset($orderReportInfo[$orderNum])) {
                    $orderReportInfo[$orderNum] = $item;
                }
                $orderReportInfo[$orderNum]['tnum'] += $tnum;
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }
        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();

        $memberNameRes = [];
        $comNameRes    = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
            foreach ($memberInfo2 as $item) {
                $comNameRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        //$landModel = new Land();
        $landApi  = new \Business\CommodityCenter\Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);

        //根据订单号获取信息
        $orderModel = new OrderQuery();
        $orderTools = new OrderTools();

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            //$buyChainModel = new BuyChain();
            //$fxRes         = $buyChainModel->getListByOrderId($orderArr);

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    //如果是资源方，直接取第一级的成本价
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr, 'orderid,lprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');

        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        $return = [];
        if (!empty($orderArr)) {
            $ticketModel = new Ticket();

            $data         = $orderTools->getOrderList($orderArr);
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $orderTools->getOrderDetailInfo($orderArr, 'orderid, memo');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']] = $item['memo'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo  = $orderReportInfo[$item['ordernum']];
                    $resellerId = $orderInfo['reseller_id'];
                    $fid        = $orderInfo['fid'];
                    $tnum       = $orderInfo['tnum'];
                    $tid        = $orderInfo['tid'];

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    $payMode = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';

                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName      = isset($comNameRes[$resellerId]) ? $comNameRes[$resellerId] : '未知';
                    $fidName      = isset($memberNameRes[$fid]) ? $memberNameRes[$fid] : '未知';
                    $lidName      = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus  = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';

                    //下单金额
                    $allMoney = $price * $tnum;
                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //门票名
                    $ticketInfo = $ticketList[$tid];
                    //备注
                    $memo     = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';
                    $return[] = [
                        //订单号
                        $item['ordernum'],
                        //下单时间
                        $item['ordertime'],
                        //预计游玩时间
                        $item['playtime'],
                        //开始有效时间
                        $item['begintime'],
                        //截止有效时间
                        $item['endtime'],
                        //支付状态
                        $this->_payStatus[$item['pay_status']],
                        //分销商企业名称
                        $comName,
                        //分销商账户名称
                        $resellerName,
                        //分组
                        $group,
                        //取票人
                        $item['ordername'],
                        //取票手机
                        $item['ordertel'],
                        //产品名称
                        $lidName . ' ' . $ticketInfo,
                        //卖出支付方式
                        $payMode,
                        //上级供应商
                        $fidName,
                        //出售单价
                        round($price / 100, 2),
                        //订单状态
                        $orderStatus,
                        //身份证
                        "\t" . $item['personid'],
                        //下单门票数
                        $tnum,
                        //下单金额
                        round($allMoney / 100, 2),
                        //备注
                        $memo,
                    ];
                }
            }
        }

        return $return;
    }

    /**
     * @param $orderArr
     *
     * @return array
     */
    public function getOrderTwoInfo($where, $page = 1, $size = 500)
    {
        $model              = new \Model\Report\Statistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isResource         = $where['isResource'];
        unset($where['isResource']);

        $data = $model->getInfo($where, 6, $page, $size);

        $orderArr        = [];
        $resellerArr     = [];
        $fidArr          = [];
        $lidArr          = [];
        $orderReportInfo = [];
        foreach ($data as $item) {
            $ordersInfo = $item['orders_info'];
            unset($item['orders_info']);
            $resellerArr[] = $item['reseller_id'];
            $fidArr[]      = $item['fid'];
            $lidArr[]      = $item['lid'];
            $ordersInfo    = json_decode($ordersInfo, true);
            foreach ($ordersInfo as $key => $value) {
                $orderNum   = $value[0];
                $tnum       = $value[1];
                $orderArr[] = $orderNum;
                if (!isset($orderReportInfo[$orderNum])) {
                    $orderReportInfo[$orderNum] = $item;
                }
                $orderReportInfo[$orderNum]['tnum'] += $tnum;
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = null;
        }
        unset($data);

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();

        $memberNameRes = [];
        $comNameRes    = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
            foreach ($memberInfo2 as $item) {
                $comNameRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        //$landModel = new Land();
        $landApi  = new \Business\CommodityCenter\Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title');
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);

        //根据订单号获取信息
        $orderTools = new OrderTools();

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            //$buyChainModel = new SubOrderSplit();
            //$fxRes         = $buyChainModel->getListByOrderArr($orderArr,
            //    'orderid, buyerid, sellerid, cost_money, sale_money, pmode, level');

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getListByOrderArrNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    //如果是资源方，直接取第一级的成本价
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取零售价
        $lPriceInfo = [];
        if (!empty($orderArr)) {
            $orderToolModel = new OrderTools('slave');
            $field          = 'orderid, apply_id, refund_num, verified_num, origin_num, lprice';
            $lPriceRes      = $orderToolModel->getOrderApplyInfo($orderArr, $field);
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');

        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        $return = [];
        if (!empty($orderArr)) {
            $ticketModel  = new Ticket();
            $data         = $orderTools->getOrderList($orderArr);
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr, 'orderid, memo');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']] = $item['memo'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo  = $orderReportInfo[$item['ordernum']];
                    $resellerId = $orderInfo['reseller_id'];
                    $fid        = $orderInfo['fid'];
                    $tnum       = $orderInfo['tnum'];
                    $tid        = $orderInfo['tid'];

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    $payMode = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';

                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName      = isset($comNameRes[$resellerId]) ? $comNameRes[$resellerId] : '未知';
                    $fidName      = isset($memberNameRes[$fid]) ? $memberNameRes[$fid] : '未知';
                    $lidName      = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus  = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';

                    //下单金额
                    $allMoney = $price * $tnum;
                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //门票名
                    $ticketInfo = $ticketList[$tid];
                    //备注
                    $memo     = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';
                    $return[] = [
                        //订单号
                        $item['ordernum'],
                        //下单时间
                        $item['ordertime'],
                        //预计游玩时间
                        $item['playtime'],
                        //开始有效时间
                        $item['begintime'],
                        //截止有效时间
                        $item['endtime'],
                        //支付状态
                        $this->_payStatus[$item['pay_status']],
                        //分销商企业名称
                        $comName,
                        //分销商账户名称
                        $resellerName,
                        //分组
                        $group,
                        //取票人
                        $item['ordername'],
                        //取票手机
                        $item['ordertel'],
                        //产品名称
                        $lidName . ' ' . $ticketInfo,
                        //卖出支付方式
                        $payMode,
                        //上级供应商
                        $fidName,
                        //出售单价
                        round($price / 100, 2),
                        //订单状态
                        $orderStatus,
                        //身份证
                        "\t" . $item['personid'],
                        //下单门票数
                        $tnum,
                        //下单金额
                        round($allMoney / 100, 2),
                        //备注
                        $memo,
                    ];
                }
            }
        }

        return $return;
    }

    private function _getOnSaleRecord($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        if (!$this->_couponModel) {
            $this->_couponModel = new Coupon('localhost');
        }

        $piece = ceil(count($orderArr) / $this->_selectSize);
        $field = 'ordernum, fid, aid, eMoney';
        $res   = [];

        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);

            $tmp = $this->_couponModel->getOrderCouponInfoByArr($queryIds, $field);
            if ($tmp && is_array($tmp)) {
                $res = array_merge($res, $tmp);
            }
        }

        return $res;
    }

    /**
     * 统计员工的日销售报表数据
     * <AUTHOR>
     * @date    2017-10-02
     *
     * @param  string  $beginTimeStamp  起始时间戳
     * @param  string  $endTimeStamp  结束时间戳
     * @param  int  $opId  统计人员的id
     * @param  int  $lid  景区的id
     * @param  int  $tid  门票的id
     * @param  int  $isCountOp  操作人id
     * @param  int  $parentId  账号主id
     *
     * @return array
     *
     */
    public function countStaffSaleData($beginTimeStamp, $endTimeStamp, $opId, $lid = 0, $tid = 0, $isCountOp = 0, $parentId = '')
    {
        // $beginTimeStamp  = '1481527650';
        // $endTimeStamp    = '1506475622';

        //$orderQueryModel = new OrderQuery();
        // 起始时间   结束时间  销售者id  景点id
        //$queryRes = $orderQueryModel->CTS_SaleSummary($beginTimeStamp, $endTimeStamp, $opId, $lid, $tid, $isCountOp);
        $queryRes = $this->getWebCloudReportData($beginTimeStamp, $endTimeStamp, $opId, $lid, $tid, $isCountOp,
            $parentId);

        // 对数据进行按照景区的分配进行
        if (!$queryRes) {
            // 无数据
            return ['code' => 401, 'msg' => '数据为空'];
        }

        // 统计的支付方式进行
        // 1:支付宝 2:授信支付 5:微信支付 9:现金支付 17:虚拟会员卡支付 18:计时卡支付 36:POS支付
        $payArr    = [1, 2, 3, 5, 9, 17, 18, 36,28, 29, 74, 255];
        $payWayArr = [
            '1'  => 'alipay',
            '2'  => 'creditpay',
            '5'  => 'wxpay',
            '9'  => 'cashpay',
            '18' => 'timecardpay',
            '17' => 'cardsolution',
            '36' => 'pospay',
            '28'=> 'swiftpass',
            '29' => 'yeepay',
            '74' => 'no_need_pay',
            '255' => 'unknown',
        ];
        // 初始化返回数组
        $data = [];
        foreach ($queryRes as $queryKey => $queryValue) {
            // 剔除不显示的支付方式
            if (!in_array($queryValue['mode'], $payArr)) {
                continue;
            }

            foreach ($queryValue['tickets'] as $ticketKey => $ticketValue) {
//                if ($ticketValue['sk']['money'] == 0) {
//                    $ticketValue['sk']['tnum'] = 0;
//                    $ticketValue['tk']['tnum'] = 0;
//                }
                // 本次循环实际销售数量
                $realIncomeNum = $ticketValue['sk']['tnum'] - $ticketValue['tk']['tnum'];
                // 本次循环实际销售金额
                $realIncomeMoney = $ticketValue['sk']['money'] - $ticketValue['tk']['money'];

                /**
                 * 开始统计各项目总和
                 */
                $data[0]['name']              = '总计：';
                $data[0]['lid']               = '0';
                $data[0]['income']['tnum']    += $ticketValue['sk']['tnum'];
                $data[0]['income']['money']   += $ticketValue['sk']['money'];
                $data[0]['income']['invoice'] += $ticketValue['invoice'];
                $data[0]['refund']['tnum']    += $ticketValue['tk']['tnum'];
                $data[0]['refund']['money']   += $ticketValue['tk']['money'];

                // 初始化总的支付数据
                if (!$data[0]['realIncome']) {
                    foreach ($payWayArr as $payWayValue) {
                        $data[0]['realIncome'][$payWayValue] = 0;
                    }
                }

                if (!$data[0]['realOutcome']) {
                    foreach ($payWayArr as $payWayValue) {
                        $data[0]['realOutcome'][$payWayValue] = 0;
                    }
                }

                // 统计实际销售的数量
                $data[0]['realIncome']['tnum'] += $realIncomeNum;

                // 统计实际的金额的实际总销售金额
                $data[0]['realIncome']['money'] += $realIncomeMoney;
                //$data[0]['realIncome'][$payWayArr[$queryValue['mode']]] += $realIncomeMoney;
                $data[0]['realIncome'][$payWayArr[$queryValue['mode']]]  += $ticketValue['sk']['money'];
                $data[0]['realOutcome'][$payWayArr[$queryValue['mode']]] += $ticketValue['tk']['money'];
                $data[0]['ticket']                                       = [];

                /**
                 * 开始统计景区的各项信息
                 */
                // 获取景区的名称
                $data[$ticketValue['lid']]['name'] = $ticketValue['scenic'];
                $data[$ticketValue['lid']]['lid']  = $ticketValue['lid'];

                // 统计总的销售数量
                $data[$ticketValue['lid']]['income']['tnum']    += $ticketValue['sk']['tnum'];
                $data[$ticketValue['lid']]['income']['money']   += $ticketValue['sk']['money'];
                $data[$ticketValue['lid']]['income']['invoice'] += $ticketValue['invoice'];
                // 统计总的退款数量
                $data[$ticketValue['lid']]['refund']['tnum']  += $ticketValue['tk']['tnum'];
                $data[$ticketValue['lid']]['refund']['money'] += $ticketValue['tk']['money'];

                // 初始化总的支付数据
                if (!$data[$ticketValue['lid']]['realIncome']) {
                    foreach ($payWayArr as $payWayValue) {
                        $data[$ticketValue['lid']]['realIncome'][$payWayValue] = 0;
                    }
                }

                // 初始化总的支付数据
                if (!$data[$ticketValue['lid']]['realOutcome']) {
                    foreach ($payWayArr as $payWayValue) {
                        $data[$ticketValue['lid']]['realOutcome'][$payWayValue] = 0;
                    }
                }

                // 统计实际销售的数量
                $data[$ticketValue['lid']]['realIncome']['tnum'] += $realIncomeNum;

                // 统计实际的金额的实际总销售金额
                $data[$ticketValue['lid']]['realIncome']['money'] += $realIncomeMoney;
                //$data[$ticketValue['lid']]['realIncome'][$payWayArr[$queryValue['mode']]] += $realIncomeMoney;
                $data[$ticketValue['lid']]['realIncome'][$payWayArr[$queryValue['mode']]]  += $ticketValue['sk']['money'];
                $data[$ticketValue['lid']]['realOutcome'][$payWayArr[$queryValue['mode']]] += $ticketValue['tk']['money'];

                /**
                 * 开始统计门票的各项信息
                 */
                // 统计单独门票的销售数量
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['tname']           = $ticketValue['ticket'];
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['tid']             = $ticketValue['id'];
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['income']['tnum']  += $ticketValue['sk']['tnum'];
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['income']['money'] += $ticketValue['sk']['money'];

                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['income']['invoice'] += $ticketValue['invoice'];

                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['refund']['tnum']  += $ticketValue['tk']['tnum'];
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['refund']['money'] += $ticketValue['tk']['money'];

                // 初始化总的支付数据
                if (!$data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realIncome']) {
                    foreach ($payWayArr as $payWayValue) {
                        $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realIncome'][$payWayValue] = 0;
                    }
                }

                // 初始化总的支付数据
                if (!$data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realOutcome']) {
                    foreach ($payWayArr as $payWayValue) {
                        $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realOutcome'][$payWayValue] = 0;
                    }
                }

                // 统计单独门票实际销售的数量
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realIncome']['tnum'] += $realIncomeNum;
                // 统计实际的金额的实际总销售金额
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realIncome']['money'] += $realIncomeMoney;
                //$data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realIncome'][$payWayArr[$queryValue['mode']]] += $realIncomeMoney;
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realIncome'][$payWayArr[$queryValue['mode']]]  += $ticketValue['sk']['money'];
                $data[$ticketValue['lid']]['ticket'][$ticketValue['id']]['realOutcome'][$payWayArr[$queryValue['mode']]] += $ticketValue['tk']['money'];
            }
            unset($queryRes[$queryKey]);
        }

        $returnData = array_values($data);
        foreach ($returnData as $returnKey => $returnValue) {
            $returnData[$returnKey]['ticket'] = array_values($returnValue['ticket']);
        }

        unset($data);

        if ($returnData) {
            return ['code' => 200, 'msg' => '数据取得成功', 'data' => $returnData];
        } else {
            return ['code' => 401, 'msg' => '数据为空'];
        }
    }


    /**
     * 云票务报表处理
     *
     * @date   2017-10-12
     * <AUTHOR> Lan
     *
     * @param  array  $excelArray  待处理的数组
     * @param  string  $title  表格标题
     *
     * @return array
     */
    public static function handleStaffReport($excelArray, $title = '')
    {
        $condition = ['打印销售报表明细', $title];
        $head      = ['', '', '售票数量', '售票收入', '退票数', '退票支出', '实际售票', '销售收入', '实际收入'];

        $excelData = [];

        foreach ($excelArray as $excelValue) {
//            if ($excelValue['income']['money'] == 0) {
//                continue;
//            }
            if ($excelValue['lid'] == 0) {
                $tname = '';
            } else {
                $tname = '合计';
            }
            // 循环景点的信息
            $excelData[] = [
                $excelValue['name'],
                $tname,
                $excelValue['income']['tnum'],
                $excelValue['income']['money'] / 100,

                $excelValue['refund']['tnum'],
                $excelValue['refund']['money'] / 100,

                $excelValue['realIncome']['tnum'],
                $excelValue['realIncome']['money'] / 100,
                $excelValue['income']['invoice'] / 100,
            ];
            if ($excelValue['ticket']) {
                // 循环门票的信息
                foreach ($excelValue['ticket'] as $ticketVal) {
//                    if ($ticketVal['income']['money'] == 0) {
//                        continue;
//                    }
                    $excelData[] = [
                        '',
                        $ticketVal['tname'],
                        $ticketVal['income']['tnum'],
                        $ticketVal['income']['money'] / 100,

                        $ticketVal['refund']['tnum'],
                        $ticketVal['refund']['money'] / 100,

                        $ticketVal['realIncome']['tnum'],
                        $ticketVal['realIncome']['money'] / 100,
                        $ticketVal['income']['invoice'] / 100,
                    ];
                }
            }

            // 加一行空的 好看点
            $excelData[] = [''];
        }

        array_unshift($excelData, $head);
        array_unshift($excelData, $condition);

        return $excelData;
    }

    /**
     * 新云票务报表处理
     *
     * @date   2018-08-02
     * <AUTHOR>
     *
     * @param  array  $excelArray  待处理的数组
     * @param  string  $title  表格标题
     *
     * @return array
     */
    public static function newStaffReport($excelArray, $title = '', $ticketData = [], $offlineData = [])
    {
        $condition  = ['打印销售报表明细', $title];
        $head       = ['', '', '售票数量', '售票收入', '退票数', '退票支出', '实际售票', '销售收入', '实际收入'];
        $headMoney  = ['收入汇总（售票总额-退票总额）', '现金收入', '授信收入', '微信收入', '支付宝收入', 'POS支付', '易宝'];
        $headTicket = ['门票汇总', '售票打印', '重打印数量', '取票打印', '退票作废'];

        $printData = [0, 0, 0, 0, 0];
        $sumPrint  = 0;
        foreach ($ticketData as $item) {
            if ($item['type'] == 1) {
                $printData[1] += $item['ticket'];
            }
            if ($item['type'] == 2) {
                $printData[2] += $item['ticket'];
            }
            if ($item['type'] == 3) {
                $printData[3] += $item['ticket'];
            }
            if ($item['type'] == 4) {
                $printData[4] += $item['ticket'];
            }
            if (in_array($item['type'], [1, 2, 3])) {
                $sumPrint += $item['ticket'];
            }
        }
        $printData[0] = $sumPrint;

        $excelData = [];

        foreach ($excelArray as $excelValue) {
            if ($excelValue['lid'] == 0) {
                $tname = '';
            } else {
                $tname = '合计';
            }
            // 循环景点的信息
            $excelData[] = [
                $excelValue['name'],
                $tname,
                $excelValue['income']['tnum'],
                $excelValue['income']['money'] / 100,

                $excelValue['refund']['tnum'],
                $excelValue['refund']['money'] / 100,

                $excelValue['realIncome']['tnum'],
                $excelValue['realIncome']['money'] / 100,
                $excelValue['income']['invoice'] / 100,
            ];
            if ($excelValue['ticket']) {
                // 循环门票的信息
                foreach ($excelValue['ticket'] as $ticketVal) {
                    $excelData[] = [
                        '',
                        $ticketVal['tname'],
                        $ticketVal['income']['tnum'],
                        $ticketVal['income']['money'] / 100,

                        $ticketVal['refund']['tnum'],
                        $ticketVal['refund']['money'] / 100,

                        $ticketVal['realIncome']['tnum'],
                        $ticketVal['realIncome']['money'] / 100,
                        $ticketVal['income']['invoice'] / 100,
                    ];
                }
            }

            // 加一行空的 好看点
            $excelData[] = [''];
        }

        $moneyData = [
            $excelArray[0]['realIncome']['money'] / 100,
            ($excelArray[0]['realIncome']['cashpay'] - $excelArray[0]['realOutcome']['cashpay']) / 100,
            ($excelArray[0]['realIncome']['creditpay'] - $excelArray[0]['realOutcome']['creditpay']) / 100,
            ($excelArray[0]['realIncome']['wxpay'] - $excelArray[0]['realOutcome']['wxpay']) / 100,
            ($excelArray[0]['realIncome']['alipay'] - $excelArray[0]['realOutcome']['alipay']) / 100,
            ($excelArray[0]['realIncome']['pospay'] - $excelArray[0]['realOutcome']['pospay']) / 100,
            ($excelArray[0]['realIncome']['yeepay'] - $excelArray[0]['realOutcome']['yeepay']) / 100,
            ($excelArray[0]['realIncome']['swiftpass'] - $excelArray[0]['realOutcome']['swiftpass']) / 100,
        ];

//        $moneyData = array(
//            'money'     => $excelArray[0]['realIncome']['money'],
//            'cashpay'   => $excelArray[0]['realIncome']['cashpay'],
//            'creditpay' => $excelArray[0]['realIncome']['creditpays'],
//            'wxpay'     => $excelArray[0]['realIncome']['wxpay'],
//            'alipay'    => $excelArray[0]['realIncome']['alipay'],
//        );

        array_unshift($excelData, $head);
        array_unshift($excelData, $printData);
        array_unshift($excelData, $headTicket);
        array_unshift($excelData, $moneyData);
        array_unshift($excelData, $headMoney);
        array_unshift($excelData, $condition);

        if ($offlineData) {
            foreach ($offlineData as $offline) {
                $excelData[] = $offline;
            }
        }

        return $excelData;
    }

    /**
     * 统计班结报表相关的数据
     * <AUTHOR>
     * @date   2017-10-16
     *
     * @param  string  $beginTime  '2017-10-18 00:00:00';    // 起始时间
     * @param  string  $endTime  '2017-10-18 23:59:59';    // 结束时间
     * @param  array  $channel  [0, 2, 4, 5];             // 渠道id 数组
     * @param  int  $type  统计类型  1：按照人员  2：按照终端  3：人员和终端
     * @param  array  $opIdArr  人员数据数组 [3385, 1002759, 6874]
     * @param  array  $applyDid  终端供应数据数组 [3385]
     * @param  array  $terminal  [10553] 终端号  里面一定要int
     *
     * @return array
     *
     */
    public function countTicketClassReportData($beginTime, $endTime, $channel = [20, 2, 4, 5],
                                               $type = 1, $opIdArr = [], $applyDid = [])
    {
        // 参数判断
        if (!$beginTime || !$endTime) {
            return ['code' => 203, 'msg' => '参数错误'];
        }

        // 渠道值控制
        $channelDiff = array_diff($channel, [20, 2, 4, 5]);
        if ($channelDiff) {
            return ['code' => 203, 'msg' => '不允许的渠道统计'];
        }

        // 由于各个渠道的 入值 方式不同 ，故只能分渠道统计
        $channelNum = count($channel);
        if ($channelNum != 1) {
            return ['code' => 203, 'msg' => '统计渠道数量超出限制'];
        }

        // source :0=终端机,2=自助机,3=外部通知更新,4=云票务,5=云闸机
        switch ($type) {
            case '1':
                if (!$opIdArr) {
                    return ['code' => 203, 'msg' => '参数错误'];
                }
                // 统计的操作人
                $opId = $opIdArr;
                // 统计的渠道集合
                $source = $channel ?: [20, 2, 4];
                break;
            case '2':
                if (!$applyDid) {
                    return ['code' => 203, 'msg' => '参数错误'];
                }
                $opId   = $applyDid;
                $source = $channel ?: [5];
                break;
            case '3':
                if (!$opIdArr || !$applyDid) {
                    return ['code' => 203, 'msg' => '参数错误'];
                }
                $opId = array_merge($opIdArr, $applyDid);
                $opId = array_unique($opId);

                $source = $channel ?: [20, 2, 4, 5];
                break;
        }

        // 数据获取
        //$trackModel = new \Model\Order\OrderTrack();
        //$action = [3];
        // 渠道该该人员在取票的记录
        //$allOrderTrackResArr = $trackModel->getTrackByTimeOp($beginTime, $endTime, $opId, $action, $source);
        $trackEsApi = new \Business\JavaApi\Log\OrderTrack();
        $params = $this->_generateTrackArgsForEs($beginTime, $endTime, $opId, [3], [], $source);
        $allOrderTrackResArr = $trackEsApi->queryOrderTrackLog($params);
        if (!$allOrderTrackResArr) {
            return ['code' => 203, 'msg' => '无数据--4213'];
        }
        $allOrderNum = array_column($allOrderTrackResArr, 'ordernum');
        $allOrderNum = array_unique($allOrderNum);
        //$payAction = [4];
        // 渠道所有订单支付的记录
        //$allOrderPayTrackResArr = $trackModel->getTrackByOrderActionSource($allOrderNum, $payAction, $source);
        $params = $this->_generateTrackArgsForEs('', '', [], [4], $allOrderNum, $source);
        $allOrderPayTrackResArr = $trackEsApi->queryOrderTrackLog($params);
        if (empty($allOrderPayTrackResArr)) {
            return ['code' => 203, 'msg' => '无数据--4225'];
        }
        $allOrderPayTrackResOrderKeyArr = [];
        foreach ($allOrderPayTrackResArr as $item) {
            $allOrderPayTrackResOrderKeyArr[$item['ordernum']] = $item;
        }
        $tidArr = array_column($allOrderTrackResArr, 'tid');
        $tidArr = array_unique($tidArr);
        // 对门票进行景区一对一
        $ticketModel   = new \Model\Product\Ticket();
        $ticketInfoArr = $ticketModel->getTicketList($tidArr, 'landid, id, title');
        // 获取数据中所有景区的id
        //$lidArr = array_column($ticketInfoArr, 'landid');
        //$lidArr = array_unique($lidArr);
        // 获取所有线下票务的订单号集合
        //$orderNumArr = $allOrderNum;
        // 获取所有订单信息
        $orderQueryModel = new \Model\Order\OrderTools();
        $orderInfoArr    = $orderQueryModel->getOrderInfo($allOrderNum,
            'ordernum, tprice, paymode, ordermode, pay_status, status');
        $orderInfoArr    = array_key($orderInfoArr, 'ordernum');
        $data = $this->_handleTicketClassReportData($allOrderTrackResArr, $ticketInfoArr, $orderInfoArr, $opId,
            $allOrderPayTrackResOrderKeyArr);
        if ($data) {
            return ['code' => 200, 'msg' => 'success', 'data' => $data];
        }
        return ['code' => 203, 'msg' => '无数据'];
    }

    private function _handleTicketClassReportData($trackResArr, $ticketInfoArr, $orderInfoArr, $opIdArr, $orderPayTrackResOrderKeyArr)
    {
//        echo "<pre>";  print_r($trackResArr); echo "</pre>"; exit;
        // 统计的支付方式进行
        // 1:支付宝 2:授信支付 5:微信支付 9:现金支付
        /*$payArr    = [9, 5, 1, 2, 14, 16, 36, 0, 28, 29];
        $payWayArr = [
            '1'  => 'alipay',
            '2'  => 'creditpay',
            '5'  => 'wxpay',
            '9'  => 'cashpay',
            '14' => 'wxpay',
            '16' => 'alipay',
            '36' => 'pospay',
            '0'  => 'balancepay',
            '29' => 'yeepay',
            '28' => 'swiftpass',
        ];*/
        $payWayArr  = load_config('payway_list');
        $payWayArr  = array_keys($payWayArr);
        $noMoneyPayArr = [
            '2',
            '0',
        ];

        // 调用数据处理
        $data = [];
        foreach ($trackResArr as $trackKey => $trackVal) {

            // 剔除不需要统计的（eg:云票务下单 散客支付的数据）
            if (!in_array($trackVal['oper_member'], $opIdArr)) {
                continue;
            }

            // 上面赋值完 还是0  则过滤
            if ($trackVal['oper_member'] == 0) {
                continue;
            }

            $tid            = $trackVal['tid'];
            $lid            = $ticketInfoArr[$tid]['landid'];
            $tprice         = sprintf("%.2f", ($orderInfoArr[$trackVal['ordernum']]['tprice'] / 100));
            $orderPayMode   = $orderInfoArr[$trackVal['ordernum']]['paymode'];
            $orderMode      = $orderInfoArr[$trackVal['ordernum']]['ordermode'];
            $orderPayStatus = $orderInfoArr[$trackVal['ordernum']]['pay_status'];
            $payChannel     = $orderPayTrackResOrderKeyArr[$trackVal['ordernum']]['source'];

            // 过滤未支付的 计算
            if ($orderPayStatus == 2) {
                continue;
            }

            // 过滤套票子票的计算
            if ($orderMode == 23) {
                continue;
            }

            // 剔除不正常的下单来源 -- 剔除线下售票的
            if (in_array($orderMode, [10, 12, 14, 15])) {
                // 10=云票务 12=自助机,14=闸机购票,15=智能终端
                continue;
            }

            //班结过滤年卡特权支付（不作处理）
            if ($orderMode == 18) {
                continue;
            }
            if ($orderPayMode == 13) {
                continue;
            }
//            if (!in_array($orderPayMode, $payArr)) {
//                continue;
//            }

            // 默认在员工维度下处理
            $dataKey  = 'staff';
            $opOrTeId = $trackVal['oper_member'];
            $price    = ($trackVal['tnum'] * $tprice);

            // 根据不同的支付方式进行不同的初始化统计
            if (in_array($orderPayMode, $noMoneyPayArr)) {
                // 参数初始
                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['num']            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['money']['money'] += 0;

                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['num']                            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['money']['money']                 += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['num']            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['money']['money'] += 0;

                foreach ($payWayArr as $payWayValue) {
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['money'][$payWayValue]                          += 0;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['money'][$payWayValue]                 += 0;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['money'][$payWayValue] += 0;
                }

                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['num']                          += $trackVal['tnum'];
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['num']                 += $trackVal['tnum'];
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['num'] += $trackVal['tnum'];
                if ($orderMode == 24 && $payChannel == $trackVal['source']) {
                    // 总汇总
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['money']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['money'][$orderPayMode] += $price;

                    // 景区汇总
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['money']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['money'][$orderPayMode] += $price;

                    // 门票汇总
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['money']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['money'][$orderPayMode] += $price;
                }

                // 参数初始化---统计门票信息
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['ticketInfo']  = $ticketInfoArr[$tid];
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['orderInfo'][] = $trackVal['ordernum'];

            } else {
                // 参数初始
                $data[$dataKey][$opOrTeId]['money']['allCount']['num']            += 0;
                $data[$dataKey][$opOrTeId]['money']['allCount']['money']['money'] += 0;

                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['num']                            += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['money']['money']                 += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['num']            += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['money']['money'] += 0;

                foreach ($payWayArr as $payWayValue) {
                    $data[$dataKey][$opOrTeId]['money']['allCount']['money'][$payWayValue]                          += 0;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['money'][$payWayValue]                 += 0;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['money'][$payWayValue] += 0;
                }

                $data[$dataKey][$opOrTeId]['money']['allCount']['num']                          += $trackVal['tnum'];
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['num']                 += $trackVal['tnum'];
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['num'] += $trackVal['tnum'];
                if ($orderMode == 24 && $payChannel == $trackVal['source']) {
                    // 总汇总
                    $data[$dataKey][$opOrTeId]['money']['allCount']['money']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['allCount']['money'][$orderPayMode] += $price;

                    // 景区汇总
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['money']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['money'][$orderPayMode] += $price;

                    // 门票汇总
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['money']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['money'][$orderPayMode] += $price;
                }

                // 参数初始化---统计门票信息
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['ticketInfo']  = $ticketInfoArr[$tid];
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['orderInfo'][] = $trackVal['ordernum'];
            }
        }

        return $data;
    }

    private function _generateTrackArgsForEs($beginTime = '', $endTime = '', $opId = [], $action = [], $allOrderNum = [], $source = [])
    {
        $params = [
            'pageNum' => 1,
            'pageSize' => $this->_trackOrderPageSize,
        ];
        if ($beginTime) {
            $params['startTime'] = $beginTime;
        }
        if ($endTime) {
            $params['overTime'] = $endTime;
        }
        //如果参数只有一个值 使用单个值的参数
        if (!empty($opId)) {
            if (count($opId) == 1) {
                $params['operMember'] = $opId[0];
            } else {
                $params['operMemberList'] = $opId;
            }
        }
        if (!empty($action)) {
            if (count($action) == 1) {
                $params['action'] = $action[0];
            } else {
                $params['actionList'] = $action;
            }
        }
        if (!empty($allOrderNum)) {
            if (count($allOrderNum) == 1) {
                $params['ordernum'] = $allOrderNum[0];
            } else {
                //使用array_unique去重后索引可能会变需要重建索引
                $params['ordernumList'] = array_values($allOrderNum);
            }
        }
        if (!empty($source)) {
            $params['sourceList'] = $source;
        }
        return $params;
    }
    /**
     * 统计班结报表相关的数据
     * <AUTHOR>
     * @date   2017-10-16
     *
     * @param  string  $beginTime  '2017-10-18 00:00:00';    // 起始时间
     * @param  string  $endTime  '2017-10-18 23:59:59';    // 结束时间
     * @param  array  $channel  [0, 2, 4, 5];             // 渠道id 数组
     * @param  int  $type  统计类型  1：按照人员  2：按照终端  3：人员和终端
     * @param  array  $opIdArr  人员数据数组 [3385, 1002759, 6874]
     * @param  array  $applyDid  终端供应数据数组 [3385]
     * @param  array  $terminal  [10553] 终端号  里面一定要int
     *
     * @return array
     *
     */
    public function countClassReportData($beginTime, $endTime, $channel = [20, 2, 4, 5],
                                         $type = 1, $opIdArr = [], $applyDid = [], $terminal = [])
    {
        // 参数判断
        if (!$beginTime || !$endTime) {
            return ['code' => 203, 'msg' => '参数错误--4474'];
        }

        // 渠道值控制
        $channelDiff = array_diff($channel, [20, 2, 4, 5]);
        if ($channelDiff) {
            return ['code' => 203, 'msg' => '不允许的渠道统计'];
        }

        // 由于各个渠道的 入值 方式不同 ，故只能分渠道统计
        $channelNum = count($channel);
        if ($channelNum != 1) {
            return ['code' => 203, 'msg' => '统计渠道数量超出限制'];
        }

        // source :0=终端机,2=自助机,3=外部通知更新,4=云票务,5=云闸机
        switch ($type) {
            case '1':
                if (!$opIdArr) {
                    return ['code' => 203, 'msg' => '参数错误--4493'];
                }
                // 统计的操作人
                $opId = $opIdArr;
                // 统计的渠道集合
                $source = $channel ?: [20, 2, 4];
                break;
            case '2':
                if (!$terminal || !$applyDid) {
                    return ['code' => 203, 'msg' => '参数错误--4502'];
                }
                $opId   = $applyDid;
                $source = $channel ?: [5];
                break;
            case '3':
                if (!$opIdArr || !$terminal || !$applyDid) {
                    return ['code' => 203, 'msg' => '参数错误--4509'];
                }
                $opId = array_merge($opIdArr, $applyDid);
                $opId = array_unique($opId);

                $source = $channel ?: [20, 2, 4, 5];
                break;
        }

        // 数据获取
        //$trackModel = new \Model\Order\OrderTrack();

        // 根据不用的渠道入值来判断所取得 收入类action
        switch ($source[0]) {
            case '20':
                // 安卓终端选取 下单action=0 为收入来源  -- 下单和支付是同时
                // $action[] = 0;
                $action = [0, 1, 2, 6, 7];
                break;
            case '2':
                // 自助机选取 出票 action=0 为收入来源   -- 下单的人员统计
                // $action[] = 0;
                $action = [0, 1, 2, 6, 7];
                break;
            case '4':
                // $action[] = 0;
                // 云票务选取 下单action=0 为收入来源    -- 下单和支付是同时的
                $action = [0, 1, 2, 6, 7];
                break;
            case '5':
                // $action[] = 5;
                // 云闸机选取  验证action=5 为收入来源   -- 支付和验证是同时的
                $action = [5, 4, 1, 2, 6, 7];
                break;
        }
        $trackEsApi = new \Business\JavaApi\Log\OrderTrack();
        // 取得该时间段该 人员对 所有的操作的订单（收入类和支出类）----- 不限 操作来源
        //$allOrderTrackResArr = $trackModel->getTrackByTimeOp($beginTime, $endTime, $opId, $action, '');
        $params = $this->_generateTrackArgsForEs($beginTime, $endTime, $opId, $action);
        $allOrderTrackResArr = $trackEsApi->queryOrderTrackLog($params);
        if (!$allOrderTrackResArr) {
            return ['code' => 203, 'msg' => '无数据--4550'];
        }

        $allOrderNum = array_column($allOrderTrackResArr, 'ordernum');
        $allOrderNum = array_unique($allOrderNum);

        // 获取所有下单来源是 线下系统[ 固定渠道下的 ]的订单号 --- 不论是谁下单的
        //$orderArr = $trackModel->getTrackByOrderActionSource($allOrderNum, [0], $source);
        $params = $this->_generateTrackArgsForEs('', '', [], [0], $allOrderNum, $source);
        $orderArr = $trackEsApi->queryOrderTrackLog($params);
        // print_r($orderArr);exit;

        if (!$orderArr) {
            return ['code' => 203, 'msg' => '无数据--4563'];
        }

        $tidArr = array_column($orderArr, 'tid');
        $tidArr = array_unique($tidArr);
        // 对门票进行景区一对一
        $ticketModel   = new \Model\Product\Ticket();
        $ticketInfoArr = $ticketModel->getTicketList($tidArr, 'landid, id, title');

        // 获取数据中所有景区的id
        //$lidArr = array_column($ticketInfoArr, 'landid');
        //$lidArr = array_unique($lidArr);

        // 获取所有线下票务的订单号集合
        $orderNumArr = array_column($orderArr, 'ordernum');

        // 获取所有订单信息
        $orderQueryModel = new \Model\Order\OrderTools();
        $orderInfoArr    = $orderQueryModel->getOrderInfo($orderNumArr,
            'ordernum, tprice, paymode, ordermode, pay_status, status');
        $orderInfoArr    = array_key($orderInfoArr, 'ordernum');

        // 如果是云票务的订单  未使用未支付的 不让班结算、
        if (in_array(4, $channel) && $orderInfoArr) {
            $unPayOrder = [];
            foreach ($orderInfoArr as $orderInfoArrKey => $orderInfoArrVal) {
                if ($orderInfoArrVal['status'] == 0 && $orderInfoArrVal['pay_status'] == 2) {
                    $unPayOrder[] = $orderInfoArrKey;
                }
            }
            if ($unPayOrder) {
                // return ['code' => 203, 'msg' => '您还有未完成支付的订单' . implode(',', $unPayOrder)];
                pft_log('change_shifts_log_param', '未支付完成的订单：' . implode(',', $unPayOrder));
            }
        }

        $memOrderArr    = []; // 人员订单
        $terOrderArr    = []; // 终端订单
        $orderKeyMemArr = []; // 订单为key的下单操作人为值数组
        // 对订单统计方式进行 分 人员 和 终端区分
        foreach ($orderArr as $terVal) {
            if ($terVal['source'] == 5) {
                $terOrderArr[] = $terVal;
            } else {
                $memOrderArr[]                       = $terVal;
                $orderKeyMemArr[$terVal['ordernum']] = $terVal['oper_member'];
            }
        }

        // 取得所有人员方式统计的订单号
        $memOrderNumArr = array_column($memOrderArr, 'ordernum');
        $terOrderNumArr = array_column($terOrderArr, 'ordernum');

        // 获取这些人员订单的信息
        $opId           = array_merge($opId, [0]);    // 添加被0操作数据 --- （必须是在结算范围内才能处理 0 归到下单操作人的统计里面）
        //$memTrackResArr = $trackModel->getTrackByTimeOp($beginTime, $endTime, $opId, $action, '', $memOrderNumArr);
        $params = $this->_generateTrackArgsForEs($beginTime, $endTime, $opId, $action, $memOrderNumArr);
        $memTrackResArr = $trackEsApi->queryOrderTrackLog($params);
        if (!empty($terOrderNumArr)) {
            $params = $this->_generateTrackArgsForEs('', '', [], [4, 1, 2, 6, 7, 5], $terOrderNumArr);
            $terTrackResArr = $trackEsApi->queryOrderTrackLog($params);
            //pft_log('javaRpc/order_track_es/dev', '4624===='.json_encode($params)."====terTrackResArr===".json_encode($terTrackResArr));
        }
        // 获取终端区分方式的终端号
        //$terTrackResArr = $trackModel->getTrackByOrderActionSource($terOrderNumArr, [4, 1, 2, 6, 7, 5]);

        // 得出订单对应的终端号 和 需要统计的终端
        $verifyTerArr = [];
        // 得到终端类型需要订单号相关统计追踪记录 并 是在需要统计的终端号中
        $terTrackResArrs = [];
        if (!empty($terTrackResArr)) {
            foreach ($terTrackResArr as $terTrackVal) {
                // 对 验证 的 提取出终端号
                if ($terTrackVal['action'] == 5 && in_array($terTrackVal['terminal'], $terminal)) {
                    $verifyTerArr[$terTrackVal['ordernum']] = $terTrackVal;
                }
            }
            foreach ($terTrackResArr as $terTrackValue) {
                if ($terTrackValue['action'] != 5 && $verifyTerArr[$terTrackValue['ordernum']]) {
                    $terTrackResArrs[] = $terTrackValue;
                }
            }
        }
        if (!$memTrackResArr && !$terTrackResArrs) {
            return ['code' => 203, 'msg' => '无数据--4647'];
        }
        $trackResArr = array_merge($memTrackResArr, $terTrackResArrs);
        $data = $this->_handleClassReportData($trackResArr, $verifyTerArr, $ticketInfoArr, $orderInfoArr, $opId,
            $terminal, $orderKeyMemArr);
        if ($data) {
            return ['code' => 200, 'msg' => 'success', 'data' => $data];
        }
        return ['code' => 203, 'msg' => '无数据'];
    }

    /**
     * 处理班结报表的数据
     * <AUTHOR>
     * @date   2017-10-17
     *
     * @param  array  $trackResArr  // 记录数组
     * @param  array  $verifyTerArr  // 记录数组中终端验证的数组 --用于找终端号
     * @param  array  $ticketInfoArr  // 门票信息数组
     * @param  array  $orderInfoArr  // 订单信息数组
     * @param  array  $opIdArr  // 操作人数组
     * @param  array  $terminalArr  // 终端号数组
     *
     * @return array
     *
     */
    private function _handleClassReportData($trackResArr, $verifyTerArr, $ticketInfoArr, $orderInfoArr, $opIdArr, $terminalArr, $orderKeyMemArr)
    {
//        echo "<pre>";  print_r($trackResArr); echo "</pre>"; exit;
        // 收入类action
        $incomeAction = [4];

        // 根据不同下单来源 使用不用的收入类action
        $cloudTicketIncomeAction = [0];   // 云票务

        $brakeMachineIncomeAction = [4];   // 闸机

        $selfServiceIncomeAction = [0];   // 自助机

        $androidIncomeAction = [0];    // 安卓智能终端

        // 支出类action
        $outcomeAction = [1, 2, 6, 7];
        // 统计的支付方式进行
        // 1:支付宝 2:授信支付 5:微信支付 9:现金支付 0:余额支付
        /*$payArr    = [9, 5, 1, 2, 14, 16, 36, 0, 29, 28];
        $payWayArr = [
            '1'  => 'alipay',
            '2'  => 'creditpay',
            '5'  => 'wxpay',
            '9'  => 'cashpay',
            '14' => 'wxpay',
            '16' => 'alipay',
            '36' => 'pospay',
            '0'  => 'balancepay',
            '29' => 'yeepay',
            '28' => 'swiftpass',
        ];*/
        $payWayArr  = load_config('payway_list');
        $payWayArr  = array_keys($payWayArr);
        $noMoneyPayArr = [
            '2',
            '0',
        ];

        // 调用数据处理
        $data = [];
        foreach ($trackResArr as $trackKey => $trackVal) {

            // 剔除不需要统计的（eg:云票务下单 散客支付的数据）
            if (!in_array($trackVal['oper_member'], $opIdArr)) {
                continue;
            }

            // 操作人是0 归为是下单操作人操作的数据
            if ($trackVal['oper_member'] == 0) {
                $trackVal['oper_member'] = $orderKeyMemArr[$trackVal['ordernum']];
            }

            // 上面赋值完 还是0  则过滤
            if ($trackVal['oper_member'] == 0) {
                continue;
            }

            $isIncome  = 0;
            $isOutcome = 0;

            $tid            = $trackVal['tid'];
            $lid            = $ticketInfoArr[$tid]['landid'];
            $tprice         = sprintf("%.2f", ($orderInfoArr[$trackVal['ordernum']]['tprice'] / 100));
            $orderPayMode   = $orderInfoArr[$trackVal['ordernum']]['paymode'];
            $orderMode      = $orderInfoArr[$trackVal['ordernum']]['ordermode'];
            $orderPayStatus = $orderInfoArr[$trackVal['ordernum']]['pay_status'];

            // 过滤未支付的 计算
            if ($orderPayStatus == 2) {
                continue;
            }

            // 过滤套票子票的计算
            if ($orderMode == 23) {
                continue;
            }
            //班结过滤年卡特权支付（不作处理）
            if ($orderMode == 18) {
                continue;
            }
            // 剔除不正常的下单来源
            if (!in_array($orderMode, [10, 12, 14, 15])) {
                // 12=自助机,14=闸机购票,15=智能终端
                continue;
            }

            if (!in_array($orderPayMode, $payWayArr)) {
                continue;
            }
            // 默认在员工维度下处理
            $dataKey  = 'staff';
            $opOrTeId = $trackVal['oper_member'];
            // 剔除在闸机下单的数据---（闸机下单-验证 在为即下即验）
            $terOrderNumArr = array_column($verifyTerArr, 'ordernum');
            if (in_array($trackVal['ordernum'], $terOrderNumArr)) {
                // 闸机数据处理流程
                $dataKey = 'terminal';

                // 如果分终端号有值 则分给分终端的闸机操作
                if ($verifyTerArr[$trackVal['ordernum']]['branchTerminal']) {
                    $opOrTeId = $verifyTerArr[$trackVal['ordernum']]['branchTerminal'];
                } else {
                    $opOrTeId = $verifyTerArr[$trackVal['ordernum']]['terminal'];
                }
            }

            $price = ($trackVal['tnum'] * $tprice);

            if ($orderMode == 10 && in_array($trackVal['action'], $cloudTicketIncomeAction)) {
                // 云票务下单 要计算出票的
                $isIncome = 1;
            }

            if ($orderMode == 12 && in_array($trackVal['action'], $selfServiceIncomeAction)) {
                // 12=自助机,14=闸机购票,15=智能终端
                $isIncome = 1;
            }

            if ($orderMode == 14 && in_array($trackVal['action'], $brakeMachineIncomeAction)) {
                // 12=自助机,14=闸机购票,15=智能终端
                $isIncome = 1;
            }

            if ($orderMode == 15 && in_array($trackVal['action'], $androidIncomeAction)) {
                // 12=自助机,14=闸机购票,15=智能终端
                $isIncome = 1;
            }

            // if (in_array($orderMode, [12, 14, 15]) && in_array($trackVal['action'], $incomeAction)) {
            //     // 12=自助机,14=闸机购票,15=智能终端
            //     $isIncome = 1;
            // }

            // 根据不同的支付方式进行不同的初始化统计
            if (in_array($orderPayMode, $noMoneyPayArr)) {
                // 收入类参数初始
                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['inNum']            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['inMoney']['money'] += 0;

                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['inNum']                            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['inMoney']['money']                 += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['inNum']            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['inMoney']['money'] += 0;

                // 支出类参数初始
                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['outNum']                                     += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['allCount']['outMoney']['money']                          += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['outNum']                            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['outMoney']['money']                 += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['outNum']            += 0;
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['outMoney']['money'] += 0;

                foreach ($payWayArr as $payWayValue) {
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['inMoney'][$payWayValue]                          += 0;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['inMoney'][$payWayValue]                 += 0;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['inMoney'][$payWayValue] += 0;

                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['outMoney'][$payWayValue]                          += 0;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['outMoney'][$payWayValue]                 += 0;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['outMoney'][$payWayValue] += 0;
                }

                if ($isIncome) {
                    // 总汇总
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['inNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['inMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['inMoney'][$orderPayMode] += $price;

                    // 景区汇总
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['inNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['inMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['inMoney'][$orderPayMode] += $price;

                    // 门票汇总
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['inNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['inMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['inMoney'][$orderPayMode] += $price;
                }

                if (in_array($trackVal['action'], $outcomeAction)) {

                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['outNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['outMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['allCount']['outMoney'][$orderPayMode] += $price;

                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['outNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['outMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['outMoney'][$orderPayMode] += $price;

                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['outNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['outMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['outMoney'][$orderPayMode] += $price;
                }
                // 参数初始化---统计门票信息
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['ticketInfo']  = $ticketInfoArr[$tid];
                $data[$dataKey][$opOrTeId]['noMoney']['detailCount'][$lid]['ticket'][$tid]['orderInfo'][] = $trackVal['ordernum'];

            } else {
                // 收入类参数初始
                $data[$dataKey][$opOrTeId]['money']['allCount']['inNum']            += 0;
                $data[$dataKey][$opOrTeId]['money']['allCount']['inMoney']['money'] += 0;

                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['inNum']                            += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['inMoney']['money']                 += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['inNum']            += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['inMoney']['money'] += 0;

                // 支出类参数初始
                $data[$dataKey][$opOrTeId]['money']['allCount']['outNum']                                     += 0;
                $data[$dataKey][$opOrTeId]['money']['allCount']['outMoney']['money']                          += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['outNum']                            += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['outMoney']['money']                 += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['outNum']            += 0;
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['outMoney']['money'] += 0;

                foreach ($payWayArr as $payWayValue) {
                    $data[$dataKey][$opOrTeId]['money']['allCount']['inMoney'][$payWayValue]                          += 0;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['inMoney'][$payWayValue]                 += 0;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['inMoney'][$payWayValue] += 0;

                    $data[$dataKey][$opOrTeId]['money']['allCount']['outMoney'][$payWayValue]                          += 0;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['outMoney'][$payWayValue]                 += 0;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['outMoney'][$payWayValue] += 0;
                }

                if ($isIncome) {
                    // 总汇总
                    $data[$dataKey][$opOrTeId]['money']['allCount']['inNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['money']['allCount']['inMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['allCount']['inMoney'][$orderPayMode] += $price;

                    // 景区汇总
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['inNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['inMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['inMoney'][$orderPayMode] += $price;

                    // 门票汇总
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['inNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['inMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['inMoney'][$orderPayMode] += $price;
                }

                if (in_array($trackVal['action'], $outcomeAction)) {

                    $data[$dataKey][$opOrTeId]['money']['allCount']['outNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['money']['allCount']['outMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['allCount']['outMoney'][$orderPayMode] += $price;

                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['outNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['outMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['outMoney'][$orderPayMode] += $price;

                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['outNum']                              += $trackVal['tnum'];
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['outMoney']['money']                   += $price;
                    $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['outMoney'][$orderPayMode] += $price;
                }
                // 参数初始化---统计门票信息
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['ticketInfo']  = $ticketInfoArr[$tid];
                $data[$dataKey][$opOrTeId]['money']['detailCount'][$lid]['ticket'][$tid]['orderInfo'][] = $trackVal['ordernum'];
            }
        }

//         print_r($data);exit;
        return $data;
    }

    /**
     * 班结统计数据入库
     *
     * @param  string  $beginTime  起始时间
     * @param  string  $endTime  结束时间
     * @param  array  $channel  渠道
     * @param  array  $opId  人员数组
     * @param  array  $applyDid  上级id
     * @param  array  $terminal  终端号数组
     * @param  int  $type  类型 1： 人员 2 ：终端
     *
     * @return array
     *
     */
    public function setClassSettleReport($beginTime, $endTime, $channel, $type, $opId, $applyDid, $terminal)
    {
        if (!$beginTime || !$endTime || !$channel || !$type || (!$opId && !$applyDid && !$terminal)) {
            return ['code' => 203, 'msg' => '参数错误'];
        }

        pft_log('change_shifts_log_param', '发起班结请求：' . json_encode(func_get_args()));

        $btime            = strtotime($beginTime);
        $etime            = strtotime($endTime);
        $classSettleModel = new \Model\Report\ClassSettle();
        // 批量接口防止重录
        $opIdArr = [];
        foreach ($opId as $opIdVal) {
            //$isExist = 0;
            $isExist = $classSettleModel->isExistRecord($btime, $etime, $channel[0], $opIdVal, $type);

            if ($isExist < 1) {
                $opIdArr[] = $opIdVal;
            }
        }

        $terminalArr = [];
        foreach ($terminal as $terminalVal) {
            //$isExist = 0;
            $isExist = $classSettleModel->isExistRecord($btime, $etime, $channel[0], $terminalVal, $type);

            if ($isExist < 1) {
                $terminalArr[] = $terminalVal;
            }
        }

        if (!$opIdArr && !$terminalArr) {
            return ['code' => 200, 'msg' => 'repeat'];
        }

        // 获取售票 汇总信息
        $allRes = $this->countClassReportData($beginTime, $endTime, $channel, $type, $opIdArr, $applyDid, $terminalArr);
        // 获取取票 汇总信息
        $allTicketRes = $this->countTicketClassReportData($beginTime, $endTime, $channel, $type, $opIdArr, $applyDid);

        if ($allRes['code'] != 200 && $allTicketRes['code'] != 200) {
            return ['code' => 203, 'msg' => $allRes['msg'] . $allTicketRes['msg']];
        }

        // print_r($allRes);exit;
        $staffClassSettleDataArr       = [];
        $terminalClassSettleDataArr    = [];
        $staffTicketClassSettleDataArr = [];
        if ($allRes['data']['staff']) {
            $type                    = 1;
            $staffClassSettleDataArr = $this->handleSetClassSettleData($allRes['data']['staff'], $beginTime, $endTime,
                $channel, $type, $applyDid);
        }

        if ($allRes['data']['terminal']) {
            $type                       = 2;
            $terminalClassSettleDataArr = $this->handleSetClassSettleData($allRes['data']['terminal'], $beginTime,
                $endTime, $channel, $type, $applyDid);
        }

        if ($allTicketRes['data']['staff']) {
            $type                          = 1;
            $staffTicketClassSettleDataArr = $this->_handleSetTicketClassSettleData($allTicketRes['data']['staff'],
                $beginTime, $endTime, $channel, $type, $applyDid);
        }

        $classSettleDataArr = array_merge($staffClassSettleDataArr, $terminalClassSettleDataArr);

        $statisticsModel = new \Model\Report\Statistics();
        if (!empty($classSettleDataArr)) {
            $setSaleReportRes = $statisticsModel->setClassSettle($classSettleDataArr);
        }
        if (!empty($staffTicketClassSettleDataArr)) {
            $setTicketReportRes = $statisticsModel->setClassTicketSettle($staffTicketClassSettleDataArr);
        }

        if ($setSaleReportRes || $setTicketReportRes) {
            return ['code' => 200, 'msg' => 'success'];
        }
        return ['code' => 203, 'msg' => 'error'];
    }

    /**
     * 处理设置班结数据入库
     *
     * @param  array  $opTerDataArr  人员终端的数据数组
     * @param  string  $beginTime  起始时间
     * @param  string  $endTime  结束时间
     * @param  string  $channel  渠道
     * @param  int  $type  数据类型  1：人员  2：终端
     *
     * @return array
     *
     */
    private function handleSetClassSettleData($opTerDataArr, $beginTime, $endTime, $channel, $type, $applyDid)
    {
        $btime              = strtotime($beginTime);
        $etime              = strtotime($endTime);
        $channel            = implode(',', $channel);
        $applyDid           = implode(',', $applyDid);
        $classSettleDataArr = [];
        $classSettleModel   = new \Model\Report\ClassSettle();
        $payWays = $this->getPayWayConfig();
        $payWayKeys = array_keys($payWays);
        $classRecordArr = [];
        foreach ($opTerDataArr as $opTerIdKey => $item) {

            if (!empty($this->_classSettleResArr[$type][$opTerIdKey])) {
                $recordId = $this->_classSettleResArr[$type][$opTerIdKey];
            } else {
                $recordId = $classSettleModel->create($btime, $etime, $channel, $opTerIdKey, $type, $applyDid);
//                $recordId = '1234';
                $classRecordArr[$type][$opTerIdKey] = $recordId;
                $this->_classSettleResArr           = $classRecordArr;
            }

            foreach ($item as $typeKey => $opDataVal) {
                $allCountMoneyArr = [
                    'money'      => ($opDataVal['allCount']['inMoney']['money'] - $opDataVal['allCount']['outMoney']['money']),
                ];
                foreach ($payWayKeys as $payWayKey) {
                    $allCountMoneyArr[$payWayKey] = ($opDataVal['allCount']['inMoney'][$payWayKey] - $opDataVal['allCount']['outMoney']['$payWayKey']);
                }
                $allCountMoneyJson    = json_encode($allCountMoneyArr);
                $classSettleDataArr[] = [
                    'csr_id'      => $recordId,
                    'op_id'       => $type == 1 ? $opTerIdKey : 0,
                    'terminal'    => $type == 2 ? $opTerIdKey : 0,
                    'lid'         => 0,
                    'tid'         => 0,
                    'sale_num'    => $opDataVal['allCount']['inNum'],
                    'refund_num'  => $opDataVal['allCount']['outNum'],
                    'money'       => $allCountMoneyJson,
                    'type'        => $typeKey == 'money' ? 1 : 4,
                    'orders_info' => '',
                ];

                foreach ($opDataVal['detailCount'] as $detailKey => $detailVal) {
                    $landCountMoneyArr = [
                        'money'      => ($detailVal['inMoney']['money'] - $detailVal['outMoney']['money']),
                    ];
                    foreach ($payWayKeys as $payWayKey) {
                        $landCountMoneyArr[$payWayKey] = ($detailVal['inMoney'][$payWayKey] - $detailVal['outMoney'][$payWayKey]);
                    }
                    $landCountMoneyJson   = json_encode($landCountMoneyArr);
                    $classSettleDataArr[] = [
                        'csr_id'      => $recordId,
                        'op_id'       => $type == 1 ? $opTerIdKey : 0,
                        'terminal'    => $type == 2 ? $opTerIdKey : 0,
                        'lid'         => $detailKey,
                        'tid'         => 0,
                        'sale_num'    => $detailVal['inNum'],
                        'refund_num'  => $detailVal['outNum'],
                        'money'       => $landCountMoneyJson,
//                        'type' => 2,
                        'type'        => $typeKey == 'money' ? 2 : 5,
                        'orders_info' => '',
                    ];

                    foreach ($detailVal['ticket'] as $ticketKey => $ticketVal) {
                        $ticketCountMoneyArr = [
                            'money'      => ($ticketVal['inMoney']['money'] - $ticketVal['outMoney']['money']),
                        ];
                        foreach ($payWayKeys as $payWayKey) {
                            $ticketCountMoneyArr[$payWayKey] = ($ticketVal['inMoney'][$payWayKey] - $ticketVal['outMoney'][$payWayKey]);
                        }
                        $ticketCountMoneyJson = json_encode($ticketCountMoneyArr);
                        $classSettleDataArr[] = [
                            'csr_id'      => $recordId,
                            'op_id'       => $type == 1 ? $opTerIdKey : 0,
                            'terminal'    => $type == 2 ? $opTerIdKey : 0,
                            'lid'         => $detailKey,
                            'tid'         => $ticketKey,
                            'sale_num'    => $ticketVal['inNum'],
                            'refund_num'  => $ticketVal['outNum'],
                            'money'       => $ticketCountMoneyJson,
                            'type'        => $typeKey == 'money' ? 3 : 6,
                            'orders_info' => json_encode($ticketVal['orderInfo']),
                        ];
                    }
                }
            }
        }

        return $classSettleDataArr;
    }

    /**
     * 处理设置班结数据入库
     *
     * @param  array  $opTerDataArr  人员终端的数据数组
     * @param  string  $beginTime  起始时间
     * @param  string  $endTime  结束时间
     * @param  string  $channel  渠道
     * @param  int  $type  数据类型  1：人员  2：终端
     *
     * @return array
     *
     */
    private function _handleSetTicketClassSettleData($opTerDataArr, $beginTime, $endTime, $channel, $type, $applyDid)
    {
        $btime              = strtotime($beginTime);
        $etime              = strtotime($endTime);
        $channel            = implode(',', $channel);
        $applyDid           = implode(',', $applyDid);
        $classSettleDataArr = [];
        $classSettleModel   = new \Model\Report\ClassSettle();
        $payWays = $this->getPayWayConfig();
        $payWayKeys = array_keys($payWays);
        foreach ($opTerDataArr as $opTerIdKey => $item) {
            if (!empty($this->_classSettleResArr[$type][$opTerIdKey])) {
                $recordId = $this->_classSettleResArr[$type][$opTerIdKey];
            } else {
                $recordId                           = $classSettleModel->create($btime, $etime, $channel, $opTerIdKey,
                    $type, $applyDid);
                $classRecordArr[$type][$opTerIdKey] = $recordId;
                $this->_classSettleResArr           = $classRecordArr;
            }

            foreach ($item as $typeKey => $opDataVal) {
                $allCountMoneyArr = [
                    'money'      => $opDataVal['allCount']['money']['money'],
                ];
                foreach ($payWayKeys as $payWayKey) {
                    $allCountMoneyArr[$payWayKey] = $opDataVal['allCount']['money'][$payWayKey];
                }
                $allCountMoneyJson    = json_encode($allCountMoneyArr);
                $classSettleDataArr[] = [
                    'csr_id'      => $recordId,
                    'op_id'       => $type == 1 ? $opTerIdKey : 0,
                    'terminal'    => $type == 2 ? $opTerIdKey : 0,
                    'lid'         => 0,
                    'tid'         => 0,
                    'sale_num'    => $opDataVal['allCount']['num'],
                    'refund_num'  => 0,
                    'money'       => $allCountMoneyJson,
                    'type'        => $typeKey == 'money' ? 1 : 4,
                    'orders_info' => '',
                ];

                foreach ($opDataVal['detailCount'] as $detailKey => $detailVal) {
                    $landCountMoneyArr = [
                        'money'      => $detailVal['money']['money'],
                    ];
                    foreach ($payWayKeys as $payWayKey) {
                        $landCountMoneyArr[$payWayKey] = $detailVal['money'][$payWayKey];
                    }
                    $landCountMoneyJson   = json_encode($landCountMoneyArr);
                    $classSettleDataArr[] = [
                        'csr_id'      => $recordId,
                        'op_id'       => $type == 1 ? $opTerIdKey : 0,
                        'terminal'    => $type == 2 ? $opTerIdKey : 0,
                        'lid'         => $detailKey,
                        'tid'         => 0,
                        'sale_num'    => $detailVal['num'],
                        'refund_num'  => 0,
                        'money'       => $landCountMoneyJson,
//                        'type' => 2,
                        'type'        => $typeKey == 'money' ? 2 : 5,
                        'orders_info' => '',
                    ];

                    foreach ($detailVal['ticket'] as $ticketKey => $ticketVal) {
                        $ticketCountMoneyArr = [
                            'money'      => $ticketVal['money']['money'],
                        ];
                        foreach ($payWayKeys as $payWayKey) {
                            $ticketCountMoneyArr[$payWayKey] = $ticketVal['money'][$payWayKey];
                        }
                        $ticketCountMoneyJson = json_encode($ticketCountMoneyArr);
                        $classSettleDataArr[] = [
                            'csr_id'      => $recordId,
                            'op_id'       => $type == 1 ? $opTerIdKey : 0,
                            'terminal'    => $type == 2 ? $opTerIdKey : 0,
                            'lid'         => $detailKey,
                            'tid'         => $ticketKey,
                            'sale_num'    => $ticketVal['num'],
                            'refund_num'  => 0,
                            'money'       => $ticketCountMoneyJson,
                            'type'        => $typeKey == 'money' ? 3 : 6,
                            'orders_info' => json_encode($ticketVal['orderInfo']),
                        ];
                    }
                }
            }
        }

        return $classSettleDataArr;
    }

    /**
     * 参数处理 预订/验证/取消/撤销 报表
     * <AUTHOR>
     * @date 2017-11-20
     *
     * @param $request
     * @param $fid
     *
     * @return array
     */
    public function paramsHandleNew($request, $fid)
    {
        //根据不同账号获取数据
        $memberModel = new \Model\Member\Member();
        $memberInfo  = $memberModel->getMemberInfo($fid);
        $dtype       = $memberInfo['dtype'];
        $account     = $memberInfo['account'];

        $landId     = isset($request['lid']) ? explode(',', $request['lid']) : false;
        $pid        = isset($request['pid']) ? explode(',', $request['pid']) : false;
        $operateId  = !empty($request['operate_id']) ? explode(',', $request['operate_id']) : false;
        $payWay     = isset($request['pay_way']) ? explode(',', $request['pay_way']) : false;
        $beginDate  = isset($request['begin_date']) ? $request['begin_date'] : false;
        $endDate    = isset($request['end_date']) ? $request['end_date'] : false;
        $merchantId = isset($request['merchant_id']) ? intval($request['merchant_id']) : false;
        $ticketId   = isset($request['tid']) ? explode(',', $request['tid']) : false;
        $siteId     = isset($request['site_id']) ? $request['site_id'] : false;
        $notPid     = !empty($request['not_pid']) ? explode(',', $request['not_pid']) : false;
        $notTid     = !empty($request['not_tid']) ? explode(',', $request['not_tid']) : false;

        //小时报表相关参数    20:00   23:59
        $startHourTime = isset($request['start_hour_time']) ? $request['start_hour_time'] : false;
        $endHourTime   = isset($request['end_hour_time']) ? $request['end_hour_time'] : false;

        //是否单日验证
        $todayCheck = isset($request['today_check']) ? $request['today_check'] : false;

        $resellerId = false;
        //分账对象id
        $objectId = isset($request['object_id']) ? $request['object_id'] : false;
        //分终端
        $branchTerminal = isset($request['terminal']) ? explode(',', $request['terminal']) : false;
        //年卡票ID
        $cardTid = isset($request['card_tid']) ? explode(',', $request['card_tid']) : false;
        //年卡产品ID
        $cardPid = isset($request['card_pid']) ? explode(',', $request['card_pid']) : false;
        //卡类型
        $cardType = isset($request['card_type']) ? explode(',', $request['card_type']) : false;
        //终端类型
        $terminalType = isset($request['terminal_type']) ? explode(',', $request['terminal_type']) : false;
        //省份
        $province = isset($request['province']) ? explode(',', $request['province']) : false;
        //城市
        $city = isset($request['city']) ? explode(',', $request['city']) : false;
        //场馆
        $venueId = isset($request['venue_id']) && !empty($request['venue_id']) ? explode(',', $request['venue_id']) : false;
        //分区
        $zoneId = isset($request['zone_id']) && !empty($request['zone_id']) ? explode(',', $request['zone_id']) : false;
        //场次
        $roundId = isset($request['round_id']) && !empty($request['round_id']) ? explode(',', $request['round_id']) : false;

        //操作人可能为0的情况，放行
        if ($request['operate_id'] === 0 || $request['operate_id'] === '0') {
            $operateId = [0];
        }

        // 如果传递分销商组，则获取分销商组中的分销商id 并赋值， 如果有reseller_id条件则已reseller_id为主
        if (isset($request['reseller_group'])) {
            $resellerId = explode(',', $request['reseller_group']);
        }
        if (isset($request['reseller_id'])) {
            $resellerId = explode(',', $request['reseller_id']);
        }
        $excludeTest = isset($request['exclude_test']) ? intval($request['exclude_test']) : false;
        $channel     = isset($request['channel']) ? explode(',', $request['channel']) : false;
        $date        = isset($request['date']) ? $request['date'] : false;
        $dateType    = isset($request['date_type']) ? $request['date_type'] : 1;
        //主票 套票报表用到
        $mainTid   = isset($request['main_tid']) ? $request['main_tid'] : false;
        $judgeType = isset($request['judgeType']) ? $request['judgeType'] : false;
        //演出场次时间段选择
        $seriesTimeBegin = isset($request['series_time_begin']) ? $request['series_time_begin'] : false;
        $seriesTimeEnd   = isset($request['series_time_end']) ? $request['series_time_end'] : false;

        $isResource = false;

        //小时报表是否支持跨天
        $hourSupportDaySpan = (bool)intval($request['hour_support_day_span'] ?? 0);
        $dateType != 3 && $hourSupportDaySpan = false;
        if ($hourSupportDaySpan) {
            $hourSupportDaySpan = StatisticsAuthBiz::judgeHourReportSupportDaySpan($fid);
        }

        if ($dtype == 7) {
            //集团账号
            $queryParams = [$fid];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
                'queryPageGroupSonIds', $queryParams);
            $memberIdArr = [$fid];
            if ($queryRes['code'] == 200 && !empty($queryRes['data']['list'])) {
                $memberIdArr = array_merge($memberIdArr, $queryRes['data']['list']);
            }
            //$shipModel   = new \Model\Member\MemberRelationship($fid);
            //$memberIdArr = $shipModel->getResellers($fid, $dtype, true, false);
            $level = false;
        } elseif ($dtype == 2 || $dtype == 3) {
            //资源方账号
            $landModel = new \Model\Product\Land();
            $tmp       = $landModel->getListByResource($account, $dtype);

            $landList = [];
            foreach ($tmp as $item) {
                $landList[] = $item['id'];
            }

            //如果都没有景区的话，直接返回空
            if (!$landList) {
                return ['code' => false, 'msg' => '没有景区'];
            }

            //如果不是查询具体景区的话，就查询资源账号下的所有景点
            if (!in_array($landId, $landList)) {
                $landId = $landList;
            }

            $landInfo = (new \Business\CommodityCenter\TerminalLand())->terminalQuerySaleLandList(null,
                null, null, [$account]);
            if (empty($landInfo[0]['applyDid'])) {
                return ['code' => false, 'msg' => '查询资源账号信息接口错误'];
            }

            $level       = [0, 1];
            $memberIdArr = $landInfo[0]['applyDid'];
            $isResource  = true;

        } else if ($fid == 1) {
            //管理员
            $level       = 1;
            $memberIdArr = $merchantId;
        } else {
            $memberIdArr = $fid;
            $level       = false;
        }

        $fid = $memberIdArr;

        //处理查询条件
        $where = [];

        $where['isResource'] = $isResource;

        // 记录日报表还是月报表
        $where['date_type'] = $dateType;

        // 判断时间
        if ($date !== false) {
            // 日期,套票报表和检票报表的月报表数据就是从日报表中获取的，date格式有所不同，需要处理一下
            if ($dateType == 2 && in_array($judgeType, $this->_monthFromDayReport)) {
                $date = trim($date);
                //修复下，日期可能是30或者31天
                $beginDate     = $date . '01';
                $endDate       = strtotime("$beginDate 00:00:00 +1 month");
                $endDate       = date("Ymd", ($endDate - 1));
                $where['date'] = ['between', [$beginDate, $endDate]];

            } elseif ($dateType == 3) {
                if ($hourSupportDaySpan) {
                    list($beginDate, $endDate) = StatisticsHandleBiz::hourReportDateDecode($date);
                    if (empty($beginDate) || empty($endDate)) {
                        return ['code' => false, 'msg' => '小时报表日期格式错误'];
                    }
                    //已存在小时，重置为空
                    $startHourTime = $endHourTime   = '';
                }
                $beginDate          = date("YmdH", strtotime($beginDate . ' ' . $startHourTime));
                $endDate            = date("YmdH", strtotime($endDate . ' ' . $endHourTime));
                $where['date_hour'] = ['between', [$beginDate, $endDate]];

            } else {
                $where['date'] = $date;
            }

        } else {
            if ($dateType == 2) {
                $beginDate     = date('Ym', strtotime($beginDate));
                $endDate       = date('Ym', strtotime($endDate));
                $where['date'] = ['between', [$beginDate, $endDate]];

            } elseif ($dateType == 3) {   //小时汇总报表
                $beginDate = date("YmdH", strtotime($beginDate . ' ' . $startHourTime));

                //跨日小时报表需要处理下结束时间
                if ($hourSupportDaySpan) {
                    $endDate = StatisticsHandleBiz::hourReportEndDate($endDate . ' ' . $endHourTime);
                } else {
                    $endDate = date("YmdH", strtotime($endDate . ' ' . $endHourTime));
                }

                $where['date_hour'] = ['between', [$beginDate, $endDate]];

            } else {
                $beginDate     = date('Ymd', strtotime($beginDate));
                $endDate       = date('Ymd', strtotime($endDate));
                $where['date'] = ['between', [$beginDate, $endDate]];
            }
        }

        //数据会有问题，暂时都设置为分销商
        $searchType = 0;

        if ($fid !== false) {
            if ($fid) {
                if ($searchType == 0) {
                    $where['fid'] = is_array($fid) ? ['in', $fid] : intval($fid);
                } else {
                    $where['reseller_id'] = is_array($fid) ? ['in', $fid] : intval($fid);
                }
            }
        }

        if ($level !== false) {
            $where['level'] = is_array($level) ? ['in', $level] : intval($level);
        }

        if (!empty($fid) && $level == 1) {
            unset($where['level']);
        }

        if ($ticketId !== false) {
            if ($ticketId) {
                $where['tid'] = is_array($ticketId) ? ['in', $ticketId] : intval($ticketId);
            }
        }

        if ($landId !== false) {
            if ($landId) {
                $where['lid'] = is_array($landId) ? ['in', $landId] : intval($landId);
            }
        }

        if ($resellerId !== false) {
            if ($resellerId) {
                if ($searchType == 0) {
                    $where['reseller_id'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
                } else {
                    $where['fid'] = is_array($resellerId) ? ['in', $resellerId] : intval($resellerId);
                }
            }
        }

        $excludeUids = false;
        if ($excludeTest) {
            $excludeUids = load_config('test_uids', 'account');
        }

        //排除账号
        if ($excludeUids !== false) {
            $excludeUids = trim(strval($excludeUids), ',');
            if ($excludeUids) {
                $where['_string'] = "fid not in ($excludeUids)";
            }
        }

        //分销渠道
        if ($channel !== false) {
            $where['channel'] = ['in', $channel];
        }

        //产品ID
        if ($pid !== false) {
            $where['pid'] = ['in', $pid];
        }

        //操作员ID
        if ($operateId !== false) {
            $where['operate_id'] = ['in', $operateId];
        }

        //支付方式
        if ($payWay !== false) {
            $where['pay_way'] = ['in', $payWay];
        }

        //主票tid
        if ($mainTid !== false) {
            $where['main_tid'] = $mainTid;
        }

        //站点
        if ($siteId !== false) {
            $where['site_id'] = $siteId;
        }

        //分账对象id
        if ($objectId !== false) {
            $where['oid'] = (int)$objectId;
        }

        if ($branchTerminal !== false) {
            $where['terminal'] = is_array($branchTerminal) ? ['in', $branchTerminal] : (int)$branchTerminal;
        }

        if ($cardTid !== false) {
            $where['card_tid'] = is_array($cardTid) ? ['in', $cardTid] : (int)$cardTid;
        }

        if ($cardPid !== false) {
            $where['card_pid'] = is_array($cardPid) ? ['in', $cardPid] : (int)$cardPid;
        }

        if ($cardType !== false) {
            $where['card_type'] = is_array($cardType) ? ['in', $cardType] : (int)$cardType;
        }

        if ($terminalType !== false) {
            $where['terminal_type'] = is_array($terminalType) ? ['in', $terminalType] : (int)$terminalType;
        }

        if ($province !== false) {
            $where['province'] = is_array($province) ? ['in', $province] : (int)$province;
        }

        if ($city !== false) {
            $where['city'] = is_array($city) ? ['in', $city] : (int)$city;
        }

        if (empty($pid) && !empty($notPid)) {
            $where['pid'] = ['not in', $notPid];
        }

        if (empty($ticketId) && !empty($notTid)) {
            $where['tid'] = ['not in', $notTid];
        }

        //是否单日验证
        if ($todayCheck) {
//            $where['today_check'] = (int) $todayCheck;
        }

        //演出场次时间段选择
        if (!empty($seriesTimeBegin) && !empty($seriesTimeEnd)) {
            $where['series_time_begin'] = ['EGT', $seriesTimeBegin];
            $where['series_time_end']   = ['ELT', $seriesTimeEnd];
        }

        //报表子票维度字段处理
        if (!empty($request['mlid']) && !empty($request['mtid']) && empty($request['stid'])) {
            $where['lid'] = $request['mlid'];
            $where['tid'] = $request['mtid'];
        } else if (!empty($request['stid'])) {
            $where['tid'] = $request['stid'];
            unset($where['lid']);
        }

        //子商户处理
        $subMerchantIds = trim($request['sub_merchant_id'] ?? '', ',');
        if (!empty($subMerchantIds)) {
            $subSidArr = explode(',', $subMerchantIds);
            $where['sub_merchant_id'] = ['in', $subSidArr];
        }

        //场馆
        if ($venueId !== false) {
            $where['venue_id'] = is_array($venueId) ? ['in', $venueId] : (int)$venueId;
        }
        //分区
        if ($zoneId !== false) {
            $where['zone_id'] = is_array($zoneId) ? ['in', $zoneId] : (int)$zoneId;
        }
        //场次
        if ($roundId !== false) {
            $where['round_id'] = is_array($roundId) ? ['in', $roundId] : (int)$roundId;
        }

        //返回参数
        return ['code' => true, 'where' => $where];
    }

    /**
     * 参数处理 应付应收报表 参数参考报表的代码
     * <AUTHOR>
     */
    public function paramsHandleNewV2($request, $fid)
    {
        $beginDate   = isset($request['begin_date']) ? strval($request['begin_date']) : 0;
        $endDate     = isset($request['end_date']) ? strval($request['end_date']) : 0;
        $type        = isset($request['type']) ? intval($request['type']) : 1;
        $landId      = isset($request['land_id']) ? intval($request['land_id']) : 0;
        $resellerId  = isset($request['reseller_id']) ? intval($request['reseller_id']) : 0;
        $merchantId  = isset($request['merchant_id']) ? intval($request['merchant_id']) : 0;
        $excludeTest = isset($request['exclude_test']) ? intval($request['exclude_test']) : 0;

        if (isset($request['detail_reseller_id'])) {
            $resellerId = intval($request['detail_reseller_id']);
        }

        if (isset($request['detail_pid'])) {
            $pid = intval($request['detail_pid']);
        }

        if (!in_array($type, [1, 2])) {
            return ['code' => false, 'msg' => '类型错误'];
        }

        //处理查询条件
        $where = [];

        // 判断时间
        if ($beginDate !== false && $endDate !== false) {

            if (!strtotime($beginDate) || !strtotime($endDate)) {
                return ['code' => false, 'msg' => '时间格式错误'];
            }

            $beginTime = strtotime($beginDate);
            $endTime   = strtotime($endDate);

            // 如果有传递时分秒，则分割出时分秒
            $beginArr = explode(' ', $beginDate);
            $endArr   = explode(' ', $endDate);

            if ((count($beginArr) == 2) || (count($endArr) == 2)) {

                if (count($beginArr) == 2 && substr($beginDate, 0, 10) == date('Y-m-d')) {
                    $where['update_time'][] = array('EGT', $beginTime);
                }

                if (count($endArr) == 2 && substr($endDate, 0, 10) == date('Y-m-d')) {
                    $where['update_time'][] = array('ELT', $endTime);
                }
            }

            $beginDate = date('Ymd', $beginTime);
            $endDate   = date('Ymd', $endTime);

            $where['date'] = array(array('EGT', $beginDate), array('ELT', $endDate));
        }

        if ($fid != 1) {
            $merchantId = '';
        }

        if (!empty($merchantId)) {
            $fid = $merchantId;
        }

        $excludeIds = false;
        if ($excludeTest) {
            $excludeIds = load_config('test_uids', 'account');
        }

        if ($type == 1) {
            $where['fid'] = $fid;
            if (!empty($resellerId)) {
                $where['reseller_id'] = $resellerId;
            }
        } else {
            $where['reseller_id'] = $fid;
            if (!empty($resellerId)) {
                $where['fid'] = $resellerId;
            }
        }

        if (!empty($landId)) {
            $where['lid'] = $landId;
        }

        //排除账号
        if ($excludeIds !== false) {
            $excludeIds = trim(strval($excludeIds), ',');
            if ($excludeIds) {
                $where['_string'] = "fid not in ($excludeIds)";
            }
        }

        //票类ID
        if (!empty($pid)) {
            $where['pid'] = $pid;
        }

        //特殊加个type 用时需要unset
        $where['type'] = $type;

        //返回参数
        return ['code' => true, 'where' => $where];
    }

    /**
     * 处理报表特定维度和数据库字段的映射关系
     * <AUTHOR>
     * @date 2020/2/2 19:10
     *
     * @param $itemValue 模板选中的特定维度
     *
     * @return array
     */
    public function handleItemValueParam($itemValue)
    {
        $filter = [];
        foreach ($itemValue as $key => $value) {
            switch ($key) {
                // 分销商
                case 'distor':
                    $filter['reseller_id'] = implode(',', array_column($value, 'id'));
                    break;
                case 'distor_group':
                    $groupIdArr         = array_column($value, 'id');
                    $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
                    $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds($groupIdArr);
                    if ($groupFidRes['code'] == 200 && !empty($groupFidRes['data'])) {
                        $filter['reseller_group'] = implode(',', array_column($groupFidRes['data'], 'fid'));
                    }
                    break;
                case 'production':
                    $filter['pid'] = implode(',', array_column($value, 'id'));
                    break;
                case 'scenic':
                    $filter['lid'] = implode(',', array_column($value, 'id'));
                    break;
                case 'ticket':
                    $filter['tid'] = implode(',', array_column($value, 'id'));
                    break;
                case 'conductor':
                    $filter['operate_id'] = implode(',', array_column($value, 'id'));
                    break;
                case 'channel':
                    $filter['channel'] = implode(',', array_column($value, 'id'));
                    break;
                case 'pay_way':
                    $filter['pay_way'] = implode(',', array_column($value, 'id'));
                    break;
                case 'site_id':
                    $filter['site_id'] = implode(',', array_column($value, 'id'));
                    break;
                case 'card_type':
                    $filter['card_type'] = implode(',', array_column($value, 'id'));
                    break;
                case 'card_pid':
                    $filter['card_pid'] = implode(',', array_column($value, 'id'));
                    break;
                case 'terminal_type':
                    $filter['terminal_type'] = implode(',', array_column($value, 'id'));
                    break;
                case 'oid':
                    $filter['oid'] = implode(',', array_column($value, 'id'));
                    break;
            }
        }

        return $filter;
    }

    /**
     * 设置用户的报表模板（从未设置过模板的用户）
     * <AUTHOR>
     * @date   2018-4-2
     *
     * @param  $userId
     *
     */
    public function setReportTemplate($userId)
    {
        if (empty($userId)) {
            return;
        }
        // 写入报表模板
        $queue = new \Library\Resque\Queue();
        $jobId = $queue->push('report', 'ReportTemplate_Job',
            array(
                'userId' => $userId,
            )
        );
    }

    /**
     * 获取云票务web版的报表数据
     *
     * @date   2018-05-12
     * <AUTHOR> Lan
     *
     * @param  string  $beginTime  开始时间
     * @param  string  $endTime  结束时间
     * @param  int  $operator  操作员
     * @param  int  $landId  景区ID
     * @param  int  $ticketId  门票ID
     * @param  bool  $isStaff  是否员工
     * @param  int  $parentId  账号主id
     *
     * @return array
     */
    public function getWebCloudReportData($beginTime, $endTime, $operator, $landId = 0, $ticketId = 0, $isStaff = false, $parentId = '')
    {
        $model     = new \Model\Report\Statistics();
        $orderData = $model->getOrderData($beginTime, $endTime, $operator, $landId, $ticketId, $isStaff, $parentId);

        $cancelData = $model->getCancelData($beginTime, $endTime, $operator, $landId, $ticketId, $isStaff, $parentId);

        if (empty($orderData) && empty($cancelData)) {
            return [];
        }

        $handleData  = [];
        $ticketIdArr = [];

        if ($orderData) {
            foreach ($orderData as $value) {
//                if ($value['sale_money'] == 0) {
//                    continue ;
//                }
                $ticketIdArr[] = $value['tid'];

                $handleData[$value['pay_way']][$value['tid']][2] += $value['service_money'];

                $handleData[$value['pay_way']][$value['tid']][1]['tnum']  += $value['order_ticket'];
                $handleData[$value['pay_way']][$value['tid']][1]['money'] += $value['sale_money'];

                $handleData[$value['pay_way']][$value['tid']][0]['tnum']  += $value['revoke_ticket'];
                $handleData[$value['pay_way']][$value['tid']][0]['money'] += $value['revoke_sale_money'];
            }
        }

        if ($cancelData) {
            foreach ($cancelData as $val) {
//                if ($val['cancel_money'] == 0) {
//                    continue ;
//                }
                $ticketIdArr[] = $val['tid'];

                $handleData[$val['pay_way']][$val['tid']][0]['tnum']  += $val['ticket_num'];
                $handleData[$val['pay_way']][$val['tid']][0]['money'] += $val['cancel_money'];
            }
        }

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($ticketIdArr, 'id,title', '', 'id,title');

        $ticketTemp = [];
        foreach ($ticketArr as $ticketInfos) {
            $ticketTemp[$ticketInfos['ticket']['id']] = [
                'id'     => $ticketInfos['ticket']['id'],
                'ttitle' => $ticketInfos['ticket']['title'],
                'lid'    => $ticketInfos['land']['id'],
                'ltitle' => $ticketInfos['land']['title'],
            ];
        }

        $payWayList = load_config('payway_list');
        $output     = [];

        foreach ($handleData as $payWay => $tickets) {
            $output[$payWay] = [
                'mode' => $payWay,
                'name' => $payWayList[$payWay],
                'tk'   => ['tnum' => 0, 'money' => 0],
                'sk'   => ['tnum' => 0, 'money' => 0],
                'sxf'  => 0,
            ];

            foreach ($tickets as $tid => $item) {
                // 增加判断
                $tkNum   = $item[0]['tnum'] ?? 0;
                $skNum   = $item[1]['tnum'] ?? 0;
                $tkMoney = $item[0]['money'] ?? 0;
                $skMoney = $item[1]['money'] ?? 0;
                $sxf     = $item[2] ?? 0;

                $output[$payWay]['tk']['tnum']  += $tkNum;
                $output[$payWay]['tk']['money'] += $tkMoney;

                $output[$payWay]['sk']['tnum']  += $skNum;
                $output[$payWay]['sk']['money'] += $skMoney;
                $output[$payWay]['sxf']         += $sxf;

                $output[$payWay]['tickets'][] = [
                    'id'      => $tid,
                    'lid'     => $ticketTemp[$tid]['lid'],
                    'scenic'  => $ticketTemp[$tid]['ltitle'],
                    'ticket'  => $ticketTemp[$tid]['ttitle'],
                    'tk'      => $item[0] ?? ['tnum' => 0, 'money' => 0],
                    'sk'      => $item[1] ?? ['tnum' => 0, 'money' => 0],
                    'sxf'     => $item[2] ?? 0,
                    'invoice' => 0,
                ];
            }
        }

        return array_values($output);
    }

    /**
     * 报表判断是否存在搜索条件
     * <AUTHOR>
     * @date 2018-08-06
     */
    public function reportSearchCondition($params)
    {
        if (isset($params['lid']) || isset($params['pid']) || isset($params['tid']) || isset($params['reseller_id']) || isset($params['channel']) || isset($params['operate_id']) || isset($params['pay_way'])) {
            return true;
        }

        return false;
    }

    /**
     * 映射模板默认值
     * <AUTHOR>
     * @date 2018-08-06
     *
     * @param  array  $itemValue  模板基础值
     *
     * @return array
     */
    public function templateValueMap($itemValue, $itemNotValue = [])
    {
        $reseller      = null;
        $resellerGroup = null;
        $pid           = null;
        $lid           = null;
        $tid           = null;
        $operateId     = '';
        $channel       = null;
        $payWay        = null;
        $siteId        = null;
        $cardType      = null;
        $cardPid       = null;
        $terminalType  = null;
        $notPid        = '';
        $notTid        = '';
        $venueId       = '';

        foreach ($itemNotValue as $key => $value) {
            switch ($key) {
                case 'production':
                    $notPid = implode(',', array_column($itemNotValue[$key], 'id'));
                    break;
                case 'ticket':
                    $notTid = implode(',', array_column($itemNotValue[$key], 'id'));
                    break;
            }
        }

        foreach ($itemValue as $key => $value) {
            switch ($key) {
                // 分销商
                case 'distor':
                    $reseller = implode(',', array_column($value, 'id'));
                    break;
                case 'distor_group':
                    $groupIdArr = array_column($value, 'id');
                    //加载分组模型
                    $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
                    $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds($groupIdArr);
                    if ($groupFidRes['code'] == 200 && !empty($groupFidRes['data'])) {
                        $resellerGroup = implode(',', array_column($groupFidRes['data'], 'fid'));
                    }

                    break;
                case 'production':
                    $pid = implode(',', array_column($value, 'id'));
                    break;
                case 'scenic':
                    $lid = implode(',', array_column($value, 'id'));
                    break;
                case 'ticket':
                    $tid = implode(',', array_column($value, 'id'));
                    break;
                case 'conductor':
                    $operateId = implode(',', array_column($value, 'id'));
                    break;
                case 'channel':
                    $channel = implode(',', array_column($value, 'id'));
                    break;
                case 'pay_way':
                    $payWay = implode(',', array_column($value, 'id'));
                    break;
                case 'site_id':
                    $siteId = implode(',', array_column($value, 'id'));
                    break;
                case 'card_type':
                    $cardType = implode(',', array_column($value, 'id'));
                    break;
                case 'card_pid':
                    $cardPid = implode(',', array_column($value, 'id'));
                    break;
                case 'terminal_type':
                    $terminalType = implode(',', array_column($value, 'id'));
                    break;
                case 'venue_id':
                    $venueId = implode(',', array_column($value, 'id'));
                    break;
            }
        }

        return [
            $reseller,
            $resellerGroup,
            $pid,
            $lid,
            $tid,
            $operateId,
            $channel,
            $payWay,
            $siteId,
            $cardType,
            $cardPid,
            $terminalType,
            $notPid,
            $notTid,
            $venueId,
        ];
    }

    /**
     * 通过分销商组获取分销商
     * <AUTHOR>
     * @date 2018-08-08
     *
     * @param  string  $resellerGroup  分销商数组字符串
     *
     * @return array|string 分销商字符串
     *
     */
    public function getResellerByGroup($resellerGroup)
    {
        if (empty($resellerGroup)) {
            return '';
        }

        $groupIdArr = explode(',', $resellerGroup);
        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds($groupIdArr);
        $resellerGroup      = [];
        if ($groupFidRes['code'] == 200 && !empty($groupFidRes['data'])) {
            $resellerGroup = implode(',', array_column($groupFidRes['data'], 'fid'));
        }

        return $resellerGroup;
    }

    /*
     * 年卡报表参数处理 参数参考报表的代码
     */
    public function annualCardParamsHandle($request, $fid)
    {
        $beginDate     = isset($request['begin_date']) ? strval($request['begin_date']) : 0;
        $date          = isset($request['date']) ? strval($request['date']) : 0;
        $endDate       = isset($request['end_date']) ? strval($request['end_date']) : 0;
        $lid           = isset($request['lid']) ? strval($request['lid']) : '';
        $tid           = isset($request['tid']) ? strval($request['tid']) : '';
        $pid           = isset($request['pid']) ? strval($request['pid']) : '';
        $resellerId    = isset($request['reseller_id']) ? strval($request['reseller_id']) : '';
        $channel       = isset($request['channel']) ? strval($request['channel']) : '';
        $operateId     = isset($request['operate_id']) ? strval($request['operate_id']) : '';
        $siteId        = isset($request['site_id']) ? strval($request['site_id']) : '';
        $payWay        = isset($request['pay_way']) ? strval($request['pay_way']) : '';
        $cardPid       = isset($request['card_pid']) ? strval($request['card_pid']) : '';
        $resellerGroup = isset($request['reseller_group']) ? strval($request['reseller_group']) : '';
        $type          = isset($request['type']) ? strval($request['type']) : 0;
        $tradeType     = isset($request['trade_type']) ? strval($request['trade_type']) : -1;
        $judgeType     = isset($request['judgeType']) ? strval($request['judgeType']) : 30;

        if ($date) {
            $beginDate = $endDate = $date;
        } else {
            if (!strtotime($beginDate) || !strtotime($endDate)) {
                return ['code' => false, 'msg' => '时间格式错误'];
            }

            $beginDate = date('Ymd', strtotime($beginDate));
            $endDate   = date('Ymd', strtotime($endDate));
        }

        $where = [
            'date' => ['between', [$beginDate, $endDate]],
        ];

        // 管理员查询权限
        if (!empty($fid)) {
            $where['fid'] = $fid;
        }

        if (!empty($lid)) {
            // 景区id 前面资源方有做处理 有可能是数组， 这里需要判断
            if (is_array($lid)) {
                $where['lid'] = $lid;
            } else {
                $lidArr = explode(',', $lid);
                // 判断是否模糊条件查询
                if (count($lidArr) > 1) {
                    $where['lid'] = ['in', $lidArr];
                } else {
                    $where['lid'] = $lid;
                }
            }
        }

        if (!empty($pid)) {
            $pidArr       = explode(',', $pid);
            $where['pid'] = ['in', $pidArr];
        }

        if (!empty($tid)) {
            $tidArr       = explode(',', $tid);
            $where['tid'] = ['in', $tidArr];
        }

        if (!empty($resellerId)) {
            // 如果存在单独分销商查询  以单独分销商为基准
            $resellerIdArr        = explode(',', $resellerId);
            $where['reseller_id'] = ['in', $resellerIdArr];
        }

        if (!empty($resellerGroup)) {
            // 分销商组查询
            $resellerGroup        = explode(',', $resellerGroup);
            $where['reseller_id'] = ['in', $resellerGroup];
        }

        if (!empty($channel) || $channel === '0') {
            $channelArr       = explode(',', $channel);
            $where['channel'] = ['in', $channelArr];
        }

        if (!empty($operateId)) {
            $operateIdArr        = explode(',', $operateId);
            $where['operate_id'] = ['in', $operateIdArr];
        }

        if (!empty($payWay) || $payWay === '0') {
            $payWayArr        = explode(',', $payWay);
            $where['pay_way'] = ['in', $payWayArr];
        }

        if ($siteId != '') {
            $siteIdArr        = explode(',', $siteId);
            $where['site_id'] = ['in', $siteIdArr];
        }

        if (!empty($cardPid)) {
            $cardPidArr        = explode(',', $cardPid);
            $where['card_pid'] = ['in', $cardPidArr];
        }

        //预订及激活的交易类型
        if ($judgeType == 30 || $judgeType == 31) {
            $type = 1;
        }
        if ($type) {
            if ($tradeType != -1) {
                $tradeTypeArr        = explode(',', $tradeType);
                $where['trade_type'] = ['in', $tradeTypeArr];
            } else {
                $where['trade_type'] = ['in', [0, 1, 2, 3]];
            }
        }

        //返回参数
        return ['code' => true, 'where' => $where];

    }

    /**
     * 获取年卡报表订单信息
     *
     * @param $where
     *
     * @return array
     *
     * @param  array  $where  查询条件
     * @param  int  $type  查询类型 30 年卡预订 31 年卡激活 32 年卡验证
     */
    public function getAnnualCardOrderInfo($where, $titleArr = [], $page = 1, $size = 500, $type = 30)
    {
        $model              = new \Model\Report\AnnualCardStatistics();
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $isLand     = $where['is_land'];
        $isResource = $where['isResource'];
        unset($where['isResource']);
        unset($where['is_land']);
        unset($where['date_type']);
        if ($isLand) {
            $isResource = 1;
        }

        //操作类型，32=年卡验证  30=年卡预定  31=年卡激活
        $action = $type == 32 ? AnnualCardStatistics::TYPE_CHECKED : AnnualCardStatistics::TYPE_ORDER;
        $data   = $model->getAnnualCardInfo($where, $action, $page, $size);

        $orderArr          = [];
        $resellerArr       = [];
        $fidArr            = [];
        //$lidArr            = [];
        $pidArr            = [];
        $cardPidArr        = [];
        $orderReportInfo   = [];
        $cancelReportInfo  = [];
        $orderTradeTypeArr = [];
        foreach ($data as $item) {
            $ordersInfo       = $item['orders_info'];
            $cancelOrdersInfo = $item['cancel_orders_info'];
            unset($item['orders_info']);
            unset($item['cancel_orders_info']);
            $resellerArr[]    = $item['reseller_id'];
            $fidArr[]         = $item['fid'];
            $fidArr[]         = $item['operate_id'];
            //$lidArr[]         = $item['lid'];
            $pidArr[]         = $item['pid'];
            $cardPidArr[]     = $item['card_pid'];
            $ordersInfo       = json_decode($ordersInfo, true);
            $cancelOrdersInfo = json_decode($cancelOrdersInfo, true);

            if ($ordersInfo) {
                foreach ($ordersInfo as $key => $value) {
                    // 订单信息
                    if (is_array($value)) {
                        $tmpOrdernum = strval($value[0]);
                        $orderArr[]  = $tmpOrdernum;

                        if (!isset($orderReportInfo[$tmpOrdernum])) {
                            $orderReportInfo[$tmpOrdernum] = $item;
                        }
                        $orderReportInfo[$tmpOrdernum]['tnum']                                     += $value[1];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cost_money']        = $item['cost_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['sale_money']        = $item['sale_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cancel_sale_money'] = $item['cancel_sale_money'];
                    } else {
                        $tmpOrdernum = strval($key);
                        $orderArr[]  = $tmpOrdernum;

                        if (!isset($orderReportInfo[$tmpOrdernum])) {
                            $orderReportInfo[$tmpOrdernum] = $item;
                        }
                        $orderReportInfo[$tmpOrdernum]['tnum']                                     += $value;
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cost_money']        = $item['cost_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['sale_money']        = $item['sale_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cancel_sale_money'] = $item['cancel_sale_money'];
                    }
                }
            }
            if ($cancelOrdersInfo) {
                foreach ($cancelOrdersInfo as $key => $value) {
                    // 订单信息
                    if (is_array($value)) {
                        $tmpOrdernum = strval($value[0]);
                        $orderArr[]  = $tmpOrdernum;

                        if (!isset($cancelReportInfo[$tmpOrdernum])) {
                            $cancelReportInfo[$tmpOrdernum] = $item;
                        }
                        $cancelReportInfo[$tmpOrdernum]['tnum']                                    += $value[1];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cost_money']        = $item['cost_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['sale_money']        = $item['sale_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cancel_sale_money'] = $item['cancel_sale_money'];

                        //同一笔订单即存在下单 又存在取消的 保留取消的
                        if (isset($orderTradeTypeArr[$tmpOrdernum][0])) {
                            unset($orderTradeTypeArr[$tmpOrdernum][0]);
                        }
                    } else {
                        $tmpOrdernum = strval($key);
                        $orderArr[]  = $tmpOrdernum;

                        if (!isset($cancelReportInfo[$tmpOrdernum])) {
                            $cancelReportInfo[$tmpOrdernum] = $item;
                        }
                        $cancelReportInfo[$tmpOrdernum]['tnum']                                    += $value;
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cost_money']        = $item['cost_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['sale_money']        = $item['sale_money'];
                        $orderTradeTypeArr[$tmpOrdernum][$item['trade_type']]['cancel_sale_money'] = $item['cancel_sale_money'];

                        //同一笔订单即存在下单 又存在取消的 保留取消的
                        if (isset($orderTradeTypeArr[$tmpOrdernum][0])) {
                            unset($orderTradeTypeArr[$tmpOrdernum][0]);
                        }
                    }
                }
            }
        }

        //取出分销商 （去重）
        if (!empty($resellerArr)) {
            $resellerArrInfo = array_unique($resellerArr);
            //取出该供应商下面的分销商分组情况
            $groupRes = $getEvoluteGroupBiz->getFidCountBySid($where['fid'], true);

            if (!$groupRes['data']) {
                $inList = [];
            } else {
                $fenArr = array_values($resellerArrInfo);

                $inList = [];
                //将分销商对应到相对应的分组
                foreach ($fenArr as $val) {
                    if (isset($groupRes['data'][$val])) {
                        $inList[$val] = $groupRes['data'][$val];
                    }
                }

                $out = array_keys($inList);
                $end = array_diff($fenArr, $out);

                //获取分销商分组情况
                foreach ($end as $val) {
                    $inList[$val] = '';
                }
            }
        } else {
            $inList = [];
        }

        //获取全部分销商
        $resellerArr = array_unique($resellerArr);
        $fidArr      = array_unique($fidArr);
        $resellerArr = array_merge($resellerArr, $fidArr);
        //散客
        $resellerMap = load_config('reseller_map', 'trade_record');
        $resellerMap = array_column($resellerMap, 'id');
        //根据分销商id获取信息
        $memberModel   = new Member();
        $memberNameRes = [];
        $memberComRes  = [];
        if (!empty($resellerArr)) {
            $memberInfo = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');
            foreach ($memberInfo as $item) {
                $memberNameRes[$item['id']] = $item['dname'];
            }
            $memberBiz   = new \Business\Member\Member();
            $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'id, fid, com_name');

            foreach ($memberInfo2 as $item) {
                $memberComRes[$item['fid']] = $item['com_name'];
            }
            unset($memberInfo);
            unset($memberInfo2);
        }

        $pidArr              = array_unique($pidArr);
        $commodityProductBiz = new Product();
        if (!empty($pidArr)) {
            $pidInfo = $commodityProductBiz->getProductInfoByIds($pidArr);
            foreach ($pidInfo as $item) {
                $pidRes[$item['id']] = $item['p_name'];
            }
            unset($pidInfo);
        }

        //卡套餐数据处理
        $cardPidArr = array_unique($cardPidArr);
        if (!empty($cardPidArr)) {
            $cardPidInfo = $commodityProductBiz->getProductInfoByIds($cardPidArr);
            foreach ($cardPidInfo as $item) {
                $cardPidRes[$item['id']] = $item['p_name'];
            }
            unset($cardPidInfo);
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);
        //根据订单号获取信息
        $orderTools      = new OrderTools();
        $annualCardModel = new AnnualCard();

        $sourceType    = load_config('order_mode');
        $sourceType[0] = '正常分销商下单';
        $sourceType[1] = '普通用户支付';
        $sourceType[2] = '用户手机支付';

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        if (!empty($orderArr)) {
            //$buyChainModel = new BuyChain();
            //$fxRes         = $buyChainModel->getSplitListByOrderIdSub($orderArr,
            //    'orderid, buyerid, sellerid, cost_money, sale_money, pmode');

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']      = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode']    = $item['pmode'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['cost_money'] = $item['cost_money'];
                }
            }
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取零售价
        $lPriceInfo = [];
        $aPriceInfo = [];
        //获取供应价
        if (!empty($orderArr)) {
            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr,
                'orderid, apply_id, refund_num, verified_num, origin_num, lprice, aprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                    $aPriceInfo[$item['orderid']] = $item['aprice'];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');
        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');
        $return         = [];
        if (!empty($orderArr)) {
            $data = $orderTools->getOrderList($orderArr);

            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr,
                'orderid, memo, aids, aids_price');
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']]   = $item['memo'];
                $aidsArr[$item['orderid']]      = $item['aids'];
                $aidsPriceArr[$item['orderid']] = $item['aids_price'];
            }

            //获取订单对应的虚拟卡号等信息
            $tmpOrderNum         = array_chunk($orderArr, 200);
            $annualCardOrderInfo = [];
            foreach ($tmpOrderNum as $value) {
                $tmpAnnualCardOrderInfo = $annualCardModel->getVirtualNoByOrderNumArr($value);
                $annualCardOrderInfo    += $tmpAnnualCardOrderInfo;
            }
            $annualCardPidAndTid = $this->_getAnnualCardTidAndPid($orderArr);

            //虚拟卡号数组
            $virtualNoArr     = array_column($annualCardOrderInfo, 'virtual_no');
            $annualCardStatus = $annualCardModel->getCardInfoByVirtual($virtualNoArr, 'id,virtual_no,status,remarks,ext_info');
            $affiliatesData   = [];
            if ($annualCardStatus) {
                $annualcardMap = array_column($annualCardStatus, null, 'virtual_no');
                if (in_array($type, [30, 31])) {
                    $cardIdArr   = array_column($annualCardStatus, 'id');
                    //获取下附属卡信息
                    $annualCardManage = new AnnualCardManage();
                    $affiliatesMap    = $annualCardManage->batchGetAffiliatesPerson($cardIdArr);
                    $affiliatesData   = [];
                    foreach ($affiliatesMap['data'] as $value) {
                        $affiliatesData[$value['card_id']][] = $value;
                    }
                }
            }

            if (!empty($data)) {
                //由于年卡补卡数据订单号无法正常查询到 所以需要整理出补卡相关的订单做特殊处理
                $tmpOrderArr     = array_column($data, 'ordernum');
                $specialOrderArr = array_diff($orderArr, $tmpOrderArr);
                foreach ($data as $item) {
                    $orderInfo  = $orderReportInfo[$item['ordernum']];
                    $cancelInfo = $cancelReportInfo[$item['ordernum']];
                    $resellerId = $orderInfo['reseller_id'] ?? $cancelInfo['reseller_id'];
                    $fid        = $orderInfo['fid'] ?? $orderInfo['reseller_id'];;
                    $cancelnum = $item['status'] == 3 ? 1 : 0;
                    $tnum      = 1;
                    $aids      = isset($aidsArr[$item['ordernum']]) ? $aidsArr[$item['ordernum']] : '';
                    $aid       = $item['aid'];
                    $memberId  = $item['member'];
                    //单价
                    $tPrice       = $item['tprice'];
                    $supplierName = isset($memberNameRes[$aid]) ? $memberNameRes[$aid] : '未知';
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';

                    $subOrderQuery = new SubOrderTrack();
                    $orderSource   = $subOrderQuery->getTrackListByOrderAndAction($item['ordernum'], 5, 'source');

                    //订单追踪表没有验证（action=5）的记录，就是手持机特权支付的，因为闸机过闸都会有action=5验证的记录
                    if (empty($orderSource) || !$orderSource) {
                        $channel = 15;
                    } else {
                        $trackSource = load_config('order_mode_track_map');
                        $trackSource = array_flip($trackSource);
                        $channel     = $trackSource[$orderSource['source']];
                    }

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }

                    $payMode     = isset($payTypeArr[$payMode]) ? $payTypeArr[$payMode] : '未知';
                    $fidName     = isset($memberComRes[$fid]) ? $memberComRes[$fid] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';

                    //算出结算价
                    if (!empty($aids)) {
                        //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                        $splitAids = $aids . ',' . $memberId;
                    } elseif ($aid != $memberId) {
                        //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                        $splitAids = $aid . ',' . $memberId;
                    } else {
                        //自供自销
                        $splitAids = $aid . ',' . $aid;
                    }

                    $splitAidsArr = explode(',', $splitAids);
                    $key          = array_search($fid, $splitAidsArr);

                    $tmpAidsPrice = isset($aidsPriceArr[$item['ordernum']]) ? $aidsPriceArr[$item['ordernum']] : '';
                    if (!empty($tmpAidsPrice)) {
                        $tmpAidsPrice = $tmpAidsPrice . ',' . $tPrice;
                    } else {
                        $tmpAidsPrice = $tPrice;
                    }
                    $tmpAidsPrice = explode(',', $tmpAidsPrice);
                    //买进单价
                    $costPrice = isset($tmpAidsPrice[$key - 1]) ? $tmpAidsPrice[$key - 1] : $aPriceInfo[$item['ordernum']];

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;
                    //下单金额
                    $allMoney = empty($tnum) ? 0 : ($price * $tnum - $eMoney);
                    //结算金额
                    $allCostMoney = empty($tnum) ? 0 : ($costPrice * $tnum);
                    //分组情况
                    $group = $inList[$resellerId] ?: '未知';
                    //订单备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';
                    //取消金额
                    $cancelMoney = $item['status'] == 3 ? round($allMoney / 100, 2) : 0;

                    $exReturn = [];

                    if ($titleArr) {
                        foreach ($titleArr as $key => $value) {
                            switch ($value) {
                                case 'order_ticket':
                                    $exReturn[] = $tnum;
                                    break;
                            };
                        }
                    }
                    $tmpAffiliatesData = $affiliatesData[$annualCardOrderInfo[$item['ordernum']]['card_id']] ?? [];
                    $newTmpAffiliatesData = [];
                    foreach ($tmpAffiliatesData as $affiliates) {
                        $tmp = [
                            $affiliates['name'],
                            $this->conversionIdentityToName($affiliates['voucher_type']),
                            $affiliates['id_card'],
                        ];
                        $newTmpAffiliatesData = array_merge($newTmpAffiliatesData, $tmp);
                    }
                    $annualExtInfo = $annualcardMap[$annualCardOrderInfo[$item['ordernum']]['virtual_no']]['ext_info'] ?? "";
                    $annualExtInfo = empty($annualExtInfo) ? [] : json_decode($annualExtInfo, true);
                    switch ($type) {
                        case 30:
                            if (isset($orderTradeTypeArr[$item['ordernum']])) {
                                foreach ($orderTradeTypeArr[$item['ordernum']] as $tradeType => $money) {
                                    $return[] = array_merge([
                                        //订单号
                                        $item['ordernum'],
                                        //下单时间
                                        $item['ordertime'],
                                        date("Y-m-d H:i:s", $annualCardOrderInfo[$item['ordernum']]['add_time']),
                                        //支付状态
                                        $this->_payStatus[$item['pay_status']],
                                        //分销商企业名称
                                        $comName,
                                        //分销商账户名称
                                        $resellerName,
                                        //分组
                                        $group,
                                        //年卡产品名称
                                        $annualCardOrderInfo[$item['ordernum']]['p_name'],
                                        //交易类型
                                        $this->_tradeTypeInfo[$tradeType],
                                        //操作员
                                        $memberNameRes[$annualCardOrderInfo[$item['ordernum']]['operator_id']],
                                        //渠道
                                        $sourceType[$annualCardOrderInfo[$item['ordernum']]['source']],
                                        //卖出支付方式
                                        $payMode,
                                        //用户姓名
                                        $annualCardOrderInfo[$item['ordernum']]['user_name'],
                                        //用户手机号
                                        $annualCardOrderInfo[$item['ordernum']]['user_mobile'],
                                        //用户身份证号
                                        "\t" . $annualCardOrderInfo[$item['ordernum']]['user_id_card'] . " ",
                                        empty($annualExtInfo['voucher_type']) ? "" : $this->conversionIdentityToName($annualExtInfo['voucher_type']), // 证件类型
                                        //实体卡号
                                        $annualCardOrderInfo[$item['ordernum']]['card_no'],
                                        //虚拟卡号
                                        $annualCardOrderInfo[$item['ordernum']]['virtual_no'],
                                        //物理卡号
                                        $annualCardOrderInfo[$item['ordernum']]['physics_no'],
                                        //上级供应商
                                        $fidName,
                                        //出售单价
                                        $tradeType != 2 ? round($price / 100, 2) : round($money['cost_money'] / 100, 2),
                                        //订单状态
                                        $orderStatus,
                                        $this->_cardStatus[$annualcardMap[$annualCardOrderInfo[$item['ordernum']]['virtual_no']]['status']] ?? $orderStatus,
                                        //下单金额
                                        $tradeType != 2 ? round($allMoney / 100, 2) : round($money['sale_money'] / 100,
                                            2),
                                        //结算价
                                        $tradeType != 2 ? round($allCostMoney / 100,
                                            2) : round($money['sale_money'] / 100, 2),
                                        //备注
                                        $memo,
                                        //远程订单号
                                        "\t" . $item['remotenum'] . " ",
                                        $tnum,
                                        $cancelnum,
                                        $tnum - $cancelnum,
                                        $cancelMoney,
                                    ], $exReturn, $newTmpAffiliatesData);
                                }
                            }
                            break;
                        case 31:
                            if (isset($orderTradeTypeArr[$item['ordernum']])) {
                                foreach ($orderTradeTypeArr[$item['ordernum']] as $tradeType => $money) {
                                    $return[] = array_merge([
                                        //订单号
                                        $item['ordernum'],
                                        //下单时间
                                        $item['ordertime'],
                                        //激活时间
                                        date("Y-m-d H:i:s", $annualCardOrderInfo[$item['ordernum']]['add_time']),
                                        //年卡产品名称
                                        $annualCardOrderInfo[$item['ordernum']]['p_name'],
                                        //分销商企业名称
                                        $comName,
                                        //分销商账户名称
                                        $resellerName,
                                        //分组
                                        $group,
                                        //操作员
                                        $memberNameRes[$annualCardOrderInfo[$item['ordernum']]['operator_id']],
                                        //渠道
                                        $sourceType[$annualCardOrderInfo[$item['ordernum']]['source']],
                                        //出售单价
                                        round($price / 100, 2),
                                        $tnum,
                                        //下单金额
                                        round($allMoney / 100, 2),
                                        //卖出支付方式
                                        $payMode,
                                        //用户姓名
                                        $annualCardOrderInfo[$item['ordernum']]['user_name'],
                                        //用户手机号
                                        $annualCardOrderInfo[$item['ordernum']]['user_mobile'],
                                        //用户身份证号
                                        "\t" . $annualCardOrderInfo[$item['ordernum']]['user_id_card'] . " ",
                                        empty($annualExtInfo['voucher_type']) ? "" : $this->conversionIdentityToName($annualExtInfo['voucher_type']), // 证件类型
                                        //实体卡号
                                        $annualCardOrderInfo[$item['ordernum']]['card_no'],
                                        //虚拟卡号
                                        $annualCardOrderInfo[$item['ordernum']]['virtual_no'],
                                        //物理卡号
                                        $annualCardOrderInfo[$item['ordernum']]['physics_no'],
                                        //上级供应商
                                        $fidName,
                                        //年卡激活备注
                                        $annualcardMap[$annualCardOrderInfo[$item['ordernum']]['virtual_no']]['remarks'] ?? ''
                                    ], $exReturn, $newTmpAffiliatesData);
                                }
                            }
                            break;
                        case 32:
                            $return[] = array_merge([
                                //订单号
                                $item['ordernum'],
                                //下单时间
                                $item['ordertime'],
                                //支付时间
                                date("Y-m-d H:i:s", $annualCardOrderInfo[$item['ordernum']]['add_time']),
                                //支付状态
                                $this->_payStatus[$item['pay_status']],
                                //年卡产品名称（卡套餐）
                                $cardPidRes[$annualCardPidAndTid[$item['ordernum']]['card_pid']],
                                //用户姓名
                                $annualCardOrderInfo[$item['ordernum']]['user_name'],
                                //用户手机号
                                $annualCardOrderInfo[$item['ordernum']]['user_mobile'],
                                //用户身份证号
                                "\t" . $annualCardOrderInfo[$item['ordernum']]['user_id_card'] . " ",
                                //实体卡号
                                $annualCardOrderInfo[$item['ordernum']]['card_no'],
                                //虚拟卡号
                                $annualCardOrderInfo[$item['ordernum']]['virtual_no'],
                                //物理卡号
                                $annualCardOrderInfo[$item['ordernum']]['physics_no'],
                                //产品名称
                                $pidRes[$item['pid']],
                                //上级供应商
                                $supplierName,
                                //购买单价
                                round($price / 100, 2),
                                //订单状态
                                $orderStatus,
                                //凭证码
                                $item['code'],
                                //验证金额
                                round($annualCardOrderInfo[$item['ordernum']]['pay_money'] / 100, 2),
                                //结算金额
                                round($allCostMoney / 100, 2),
                                //人员
                                $memberNameRes[$annualCardOrderInfo[$item['ordernum']]['operator_id']],
                                //渠道
                                $sourceType[$channel],
                                //订单备注
                                $memo,
                                //远程订单号
                                "\t" . $item['remotenum'] . " ",
                                //验证总数
                                $annualCardOrderInfo[$item['ordernum']]['num'],
                                //总票数
                                $tnum,
                            ], $exReturn);
                            break;
                    }
                }
                if (!empty($specialOrderArr)) {
                    //针对无法获取到正确订单号对应补卡这部分报表数据  特殊处理
                    $resellerId   = 11;
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';
                    $fidName      = '未知';
                    $orderStatus  = '已使用';
                    $exReturn     = [];
                    //分组情况
                    $group = $inList[$resellerId] ?: '未知';
                    foreach ($orderTradeTypeArr as $orderNum => $item) {
                        $tmpAffiliatesData = $affiliatesData[$annualCardOrderInfo[$orderNum]['card_id']] ?? [];
                        $newTmpAffiliatesData = [];
                        foreach ($tmpAffiliatesData as $affiliates) {
                            $tmp = [
                                $affiliates['name'],
                                $this->conversionIdentityToName($affiliates['voucher_type']),
                                $affiliates['id_card'],
                            ];
                            $newTmpAffiliatesData = array_merge($newTmpAffiliatesData, $tmp);
                        }
                        if (in_array($orderNum, $specialOrderArr)) {
                            $tnum = $annualCardOrderInfo[$orderNum]['num'];
                            if ($titleArr) {
                                foreach ($titleArr as $key => $value) {
                                    switch ($value) {
                                        case 'order_ticket':
                                            $exReturn[] = $tnum;
                                            break;
                                    };
                                }
                            }
                            foreach ($item as $tradeType => $money) {
                                $payMode = isset($payTypeArr[$annualCardOrderInfo[$orderNum]['payway']]) ? $payTypeArr[$annualCardOrderInfo[$orderNum]['payway']] : '未知';
                                switch ($type) {
                                    case 30:
                                        $return[] = array_merge([
                                            //订单号
                                            $orderNum,
                                            //下单时间
                                            date("Y-m-d H:i:s", $annualCardOrderInfo[$orderNum]['add_time']),
                                            date("Y-m-d H:i:s", $annualCardOrderInfo[$orderNum]['add_time']),
                                            //支付状态
                                            '已支付',
                                            //分销商企业名称
                                            $comName,
                                            //分销商账户名称
                                            $resellerName,
                                            //分组
                                            $group,
                                            //年卡产品名称
                                            $annualCardOrderInfo[$orderNum]['p_name'],
                                            //交易类型
                                            $this->_tradeTypeInfo[$tradeType],
                                            //操作员
                                            $memberNameRes[$annualCardOrderInfo[$orderNum]['operator_id']],
                                            //渠道
                                            $sourceType[$annualCardOrderInfo[$orderNum]['source']],
                                            //卖出支付方式
                                            $payMode,
                                            //用户姓名
                                            $annualCardOrderInfo[$orderNum]['user_name'],
                                            //用户手机号
                                            $annualCardOrderInfo[$orderNum]['user_mobile'],
                                            //用户身份证号
                                            "\t" . $annualCardOrderInfo[$orderNum]['user_id_card'] . " ",
                                            //实体卡号
                                            $annualCardOrderInfo[$orderNum]['card_no'],
                                            //虚拟卡号
                                            $annualCardOrderInfo[$orderNum]['virtual_no'],
                                            //物理卡号
                                            $annualCardOrderInfo[$orderNum]['physics_no'],
                                            //上级供应商
                                            $fidName,
                                            //出售单价
                                            round($annualCardOrderInfo[$orderNum]['pay_money'] / 100, 2),
                                            //订单状态
                                            $orderStatus,
                                            //下单金额
                                            round($annualCardOrderInfo[$orderNum]['pay_money'] / 100,
                                                2),
                                            //结算价
                                            round($annualCardOrderInfo[$orderNum]['pay_money'] / 100, 2),
                                            //备注
                                            "",
                                            //远程订单号
                                            "",
                                            //预订票数
                                            $tnum,
                                            //取消票数
                                            0,
                                            //实售票数
                                            $tnum,
                                            //取消金额
                                            0,
                                        ], $exReturn, $newTmpAffiliatesData);
                                        break;
                                }
                            }
                        }
                    }
                }
            } else {
                //针对无法获取到正确订单号对应补卡这部分报表数据  特殊处理
                $exReturn     = [];
                $resellerId   = 11;
                $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';
                $fidName      = '未知';
                $orderStatus  = '已使用';
                //分组情况
                $group = $inList[$resellerId] ?: '未知';
                foreach ($orderTradeTypeArr as $orderNum => $item) {
                    $tmpAffiliatesData = $affiliatesData[$annualCardOrderInfo[$orderNum]['card_id']] ?? [];
                    $newTmpAffiliatesData = [];
                    foreach ($tmpAffiliatesData as $affiliates) {
                        $tmp = [
                            $affiliates['name'],
                            $this->conversionIdentityToName($affiliates['voucher_type']),
                            $affiliates['id_card'],
                        ];
                        $newTmpAffiliatesData = array_merge($newTmpAffiliatesData, $tmp);
                    }
                    $tnum = $annualCardOrderInfo[$orderNum]['num'];
                    if ($titleArr) {
                        foreach ($titleArr as $key => $value) {
                            switch ($value) {
                                case 'order_ticket':
                                    $exReturn[] = $tnum;
                                    break;
                            };
                        }
                    }
                    foreach ($item as $tradeType => $money) {
                        $payMode = isset($payTypeArr[$annualCardOrderInfo[$orderNum]['payway']]) ? $payTypeArr[$annualCardOrderInfo[$orderNum]['payway']] : '未知';
                        switch ($type) {
                            case 30:
                                $return[] = array_merge([
                                    //订单号
                                    $orderNum,
                                    //下单时间
                                    date("Y-m-d H:i:s", $annualCardOrderInfo[$orderNum]['add_time']),
                                    date("Y-m-d H:i:s", $annualCardOrderInfo[$orderNum]['add_time']),
                                    //支付状态
                                    '已支付',
                                    //分销商企业名称
                                    $comName,
                                    //分销商账户名称
                                    $resellerName,
                                    //分组
                                    $group,
                                    //年卡产品名称
                                    $annualCardOrderInfo[$orderNum]['p_name'],
                                    //交易类型
                                    $this->_tradeTypeInfo[$tradeType],
                                    //操作员
                                    $memberNameRes[$annualCardOrderInfo[$orderNum]['operator_id']],
                                    //渠道
                                    $sourceType[$annualCardOrderInfo[$orderNum]['source']],
                                    //卖出支付方式
                                    $payMode,
                                    //用户姓名
                                    $annualCardOrderInfo[$orderNum]['user_name'],
                                    //用户手机号
                                    $annualCardOrderInfo[$orderNum]['user_mobile'],
                                    //用户身份证号
                                    "\t" . $annualCardOrderInfo[$orderNum]['user_id_card'] . " ",
                                    //实体卡号
                                    $annualCardOrderInfo[$orderNum]['card_no'],
                                    //虚拟卡号
                                    $annualCardOrderInfo[$orderNum]['virtual_no'],
                                    //物理卡号
                                    $annualCardOrderInfo[$orderNum]['physics_no'],
                                    //上级供应商
                                    $fidName,
                                    //出售单价
                                    round($annualCardOrderInfo[$orderNum]['pay_money'] / 100, 2),
                                    //订单状态
                                    $orderStatus,
                                    //下单金额
                                    round($annualCardOrderInfo[$orderNum]['pay_money'] / 100,
                                        2),
                                    //结算价
                                    round($annualCardOrderInfo[$orderNum]['pay_money'] / 100, 2),
                                    //备注
                                    "",
                                    //远程订单号
                                    "",
                                    //预订票数
                                    $tnum,
                                    //取消票数
                                    0,
                                    //实售票数
                                    $tnum,
                                    //取消金额
                                    0,
                                ], $exReturn, $newTmpAffiliatesData);
                                break;
                        }
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 转分销产品列表导出参数处理
     * <AUTHOR>
     * @date 2019-05-20
     * @return array
     */
    public function productEvoluteParamsHandle($params)
    {
        $oversea          = isset($params['oversea']) ? $params['oversea'] : '';
        $province         = isset($params['province']) ? $params['province'] : '';
        $city             = isset($params['city']) ? $params['city'] : '';
        $type             = isset($params['type']) ? $params['type'] : '';
        $supplier         = isset($params['supplier']) ? $params['supplier'] : '';
        $title            = isset($params['title']) ? $params['title'] : '';
        $openDistribution = isset($params['openDistribution']) ? $params['openDistribution'] : '';


        list($type, $subType) = SubProductBiz::decodeTypeAndSubType($type);

        return [
            'oversea'          => $oversea,
            'province'         => $province,
            'city'             => $city,
            'type'             => $type,
            'supplier'         => $supplier,
            'title'            => $title,
            'openDistribution' => $openDistribution,
            'subType'          => $subType,
        ];
    }

    /**
     * 获取转分销产品列表
     * <AUTHOR>
     * @date 2019-05-21
     *
     * @param  array  $list
     *
     * @return array
     */
    public function getProductEvoluteList($list)
    {
        if (empty($list)) {
            return [];
        }

        // 获取所有用户dname
        $memberIds   = array_column($list, 'supplierId');
        $memberModel = new Member();

        $api = new \Business\JavaApi\Member\MemberQuery();
        $res = $api->queryMemberByMemberQueryInfo(['idList' => $memberIds]);
        if ($res['code'] != 200 || !$res['data']) {
            return [];
        }

        $memberInfos    = array_column($res['data'], 'memberInfo');
        $memberInfosRes = [];
        foreach ($memberInfos as $item) {
            $memberInfosRes[$item['id']] = $item['dname'];
        }

        // 获取省 市
        $provinces = load_config('province', 'account');
        $cities    = load_config('cities', 'account');
        $areas     = [];
        foreach ($provinces as $key => $value) {
            $areas[$value["area_id"]] = $value["area_name"];
        }
        foreach ($cities as $key => $value) {
            foreach ($value as $key2 => $value2) {
                $areas[$value2['area_id']] = $value2["area_name"];
            }
        }

        $data = [];

        foreach ($list as $item) {

            list($prov, $city) = explode('|', $item['area']);

            $data[] = [
                $areas[$prov],
                $areas[$city],
                $item['lid'],
                $item['salerid'],
                $item['ttitle'],
                $memberInfosRes[$item['supplierId']],
                '在售',
                $item['jtype'],
                $item['topic'],
                $item['verifyTimeStr'],
                round($item['costPrice'] / 100, 2),
                round($item['counterPrice'] / 100, 2),
                round($item['retailPrice'] / 100, 2),
                $item['getaddr'],
            ];
        }

        return $data;
    }

    /**
     * 自供应产品列表导出参数处理
     * <AUTHOR>
     * @date 2019-05-20
     * @return array
     */
    public function productParamsHandle($params)
    {
        $keyword         = isset($params['keyword']) ? $params['keyword'] : '';
        $search_tag      = isset($params['search_tag']) ? $params['search_tag'] : 0;
        $terminal        = isset($params['terminal']) ? $params['terminal'] : '';
        $province        = isset($params['province']) ? $params['province'] : '';
        $city            = isset($params['city']) ? $params['city'] : '';
        $product_type    = isset($params['product_type']) ? $params['product_type'] : '';
        $start_date      = isset($params['start_date']) ? $params['start_date'] : '';
        $end_date        = isset($params['end_date']) ? $params['end_date'] : '';
        $is_show_expire  = isset($params['is_show_expire']) ? $params['is_show_expire'] : 0;
        $is_show_del     = isset($params['is_show_del']) ? $params['is_show_del'] : 0;
        $is_only_product = isset($params['is_only_product']) ? $params['is_only_product'] : 0;
        $is_filter       = isset($params['is_filter']) ? $params['is_filter'] : 0;
        $supplier_id     = isset($params['supplier_id']) ? $params['supplier_id'] : '';
        $about_expire    = isset($params['about_expire']) ? $params['about_expire'] : 0;

        //日期判断
        if ($start_date !== "" && !chk_date($start_date)) {
            return array("code" => 400, "msg" => "开始时间格式不正确");
        }

        if ($end_date !== "" && !chk_date($end_date)) {
            return array("code" => 400, "msg" => "结束时间格式不正确");
        }

        $is_show_expire  = $is_show_expire == 1 ? true : false;
        $is_only_product = $is_only_product == 1 ? true : false;
        $is_show_del     = $is_show_del == 1 ? true : false;
        $is_filter       = $is_filter == 1 ? true : false;
        $about_expire    = $about_expire == 1 ? true : false;

        //检查搜索的值
        switch ($search_tag) {
            case 0:
                $item_name = $keyword;//0产品名称
                break;
            case 1:
                $item_id = $keyword;//1产品id
                break;
            case 2:
                $item_code = $keyword;//2产品编号
                break;
            default:
                $item_name = $keyword;
        }

        //初始化搜索值
        $item_id   = !empty($item_id) ? (int)$item_id : '';
        $item_name = !empty($item_name) ? $item_name : '';
        $item_code = !empty($item_code) ? (int)$item_code : '';

        //参数key值重写方便接口调用
        $data = [
            "province"         => $province,      //省份code
            "city"             => $city,          //城市code
            "type"             => $product_type,  //景区类型，多个类型使用逗号分隔
            "terminal_num"     => $terminal,      //终端号
            "start_date"       => $start_date,    //起始日期
            "end_date"         => $end_date,      //结束日期
            "item_name"        => $item_name,     //产品名称
            "item_id"          => $item_id,       //产品Id
            "item_code"        => $item_code,     //产品编号
            "only_show_item"   => $is_only_product,//是否仅列出产品
            "show_expire"      => $is_show_expire,//是否显示过期产品
            "show_delete"      => $is_show_del,    //是否显示删除产品
            "supplier_id"      => $supplier_id,   //供应商id
            "only_show_filter" => $is_filter,       //是否过滤测试产品
            "show_ticket_num"  => 2000,              //导出票的数量
            "about_expire"     => $about_expire,   //是否展示即将过期产品
            "sub_type"         => $params['sub_type'] ?? 0,   //产品子类型
        ];
        if (!empty($params['subSid'])){
            $data['subSid'] = $params['subSid'];
        }

        return array("code" => 0, "data" => $data);
    }

    /**
     * 获取自供应产品列表
     * <AUTHOR>
     * @date 2019-05-20
     *
     * @param  array  $products  产品列表
     * @param  int  $isOnlyProduct  是否只列出产品
     * @param  int  $searchType  搜索类型
     *
     * @return array
     */
    public function getProductList($products, $isOnlyProduct, $searchType)
    {
        if (empty($products)) {
            return [];
        }

        //用于处理过期类型的票价格 状态
        $Business = new \Business\Product\ProductZgyList;
        $products = $Business->priceOverdue($products);

        //这块地区排序是旧页面拿过来的
        foreach ($products as $prod) {
            list($q, $sort[]) = explode('|', $prod['area']);
        }
        array_multisort($sort, $products); //根据地区来排序

        $data = [];

        if ($isOnlyProduct == 1) {
            foreach ($products as $item) {
                $data[] = [
                    $item["provice"],
                    $item["city"],
                    $item['landId'],
                    $item['salerid'],
                    $item['name'],
                    $item['supplierName'],
                    $searchType == 1 ? '在售' : '仓库',
                    $item['level'],
                    $item['topic'], //景区主题
                    $item['addtime'] ? date("Y-m-d h:i:s", $item['addtime'] / 1000) : "",
                ];
            }
        } else {
            //获取一次产品类型
            $lidArr   = array_column($products, 'landId');
            $javaApi  = new \Business\CommodityCenter\Land();
            $landInfo = $javaApi->queryLandMultiQueryById($lidArr);
            $landMap  = [];
            if ($landInfo) {
                $landMap  = array_column($landInfo, 'p_type', 'id');
                unset($landInfo, $javaApi, $lidArr);
            }

            foreach ($products as $item) {
                if (count($item['tickets'])) {
                    foreach ($item['tickets'] as $ticket) {
                        $landNam = $item['name'] . $ticket['name'];
                        $data[]  = [
                            $item["provice"],
                            $item["city"],
                            $item['landId'],
                            $item['salerid'],
                            $ticket['ticketId'],
                            $landNam,
                            $item['supplierName'],
                            $searchType == 1 ? '在售' : '仓库',
                            $item['level'],
                            $item['topic'], //景区主题
                            $ticket['verifyTime'] ? date("Y-m-d h:i:s", $ticket['verifyTime'] / 1000) : "",
                            (int)$ticket['costPrice'] / 100,
                            (int)$ticket['counterPrice'] / 100,
                            (int)$ticket['retailPrice'] / 100,
                            $ticket['getaddr'], //取票信息
                        ];
                    }
                }
            }
        }

        return $data;
    }

    /**
     * 获取年卡的tid和pid
     * Create by zhangyangzhen
     * Date: 2019/5/29
     * Time: 19:10
     *
     * @param $orderArr
     */
    private function _getAnnualCardTidAndPid($orderArr)
    {
        if (!$orderArr || empty($orderArr)) {
            return [];
        }

        foreach ($orderArr as $key => $ordernum) {
            if (isset($this->_annualCardInfo[$ordernum])) {
                unset($orderArr[$key]);
            }
        }

        $annualModel = new AnnualCard();
        $tradeRecord = $annualModel->getTradeRecordByOrderNum($orderArr, 'ordernum, card_id');

        $cardIdArr  = array_unique(array_column($tradeRecord, 'card_id'));
        $cardRes    = $annualModel->getMultiAnnualCard($cardIdArr, 'id, pid');
        $cardPidArr = array_values($cardRes);

        $ticketModel = new Ticket();
        $ticketArr   = $ticketModel->getTidArrByPid($cardPidArr);

        foreach ($tradeRecord as $key => $val) {
            $cardPid = $cardRes[$val['card_id']];
            $cardTid = $ticketArr[$cardPid];

            $this->_annualCardInfo[$val['ordernum']] = ['card_pid' => $cardPid, 'card_tid' => $cardTid];
        }

        return $this->_annualCardInfo;
    }

    /**
     * 年卡批量下单导出参数处理
     * <AUTHOR> Li
     * @date  2020-07-08
     */
    public function annualCardBatchParamsHandle($request)
    {
        $sid       = isset($request['sid']) ? strval($request['sid']) : 0;
        $trackIds  = isset($request['track_ids']) ? strval($request['track_ids']) : 0;
        $trackType = isset($request['track_type']) ? strval($request['track_type']) : 0;

        if (!$sid || !$trackIds) {
            return ['code' => false, 'msg' => '缺少必要参数'];
        }

        $where = [
            'sid'        => $sid,
            'track_id'   => explode(',', $trackIds),
            'track_type' => $trackType,
        ];

        //返回参数
        return ['code' => true, 'where' => $where];

    }

    /**
     * 获取年卡报表订单信息
     * @param array $params 查询条件
     * @param int $page 当前页数
     * @param int $size 每页条数
     * @return array
     * @throws \Exception
     * <AUTHOR> Li
     * @date  2020-07-08
     *
     */
    public function getAnnualCardTaskDetail(array $params, int $page = 1, int $size = 1000): array
    {
        $trackModel = new \Model\Product\AnnualCardTask();
        $result     = $trackModel->getTaskDetailList($params['sid'], $params['track_id'], $page, $size);
        if (empty($result)) {
            return [];
        }
        $taskInfoArr = $trackModel->getTaskInfoByIdArr($params['sid'], $params['track_id']);
        $taskMap     = array_column($taskInfoArr, null, 'id');
        $tidArr      = array_column($taskInfoArr, 'tid');
        $javaApi     = new \Business\CommodityCenter\Ticket();
        $ticketRes   = $javaApi->queryTicketInfoByIds($tidArr);

        $ticketInfoArr = [];
        if (!empty($ticketRes)) {
            foreach ($ticketRes as $ticket) {
                $ticketInfoArr[$ticket['ticket']['id']]['ltitle'] = $ticket['land']['title'];
                $ticketInfoArr[$ticket['ticket']['id']]['ttitle'] = $ticket['ticket']['title'];
            }
        }

        $return = [];
        $list   = $result['list'];
        $biz = new AnnualCardTask();
        $list = $biz->formatTaskDetail($list, $taskInfoArr[0]['task_type']);
        foreach ($list as $item) {
            //操作失败的 都没有有效期
            $avalidTime = ($item['avalid_begin'] && $item['avalid_end']) ?  date('Y-m-d', $item['avalid_begin']) . ' - '. date('Y-m-d', $item['avalid_end']) : '--';
            switch ($params['track_type'])
            {
                case 0:
                    $extInfo         = json_decode($item['ext'], true);
                    $voucherTypeName = $this->conversionIdentityToName($extInfo['voucher_type']);
                    $return[]        = [
                        Task::TASK_MAP_TITLE[$taskMap[$item['track_id']]['task_type']] ?? '未知',                    //操作类型
                        $item['ordernum'],                                                               //订单号
                        $ticketInfoArr[$taskMap[$item['track_id']]['tid']]['ltitle'] ?? '',              //产品名称
                        $ticketInfoArr[$taskMap[$item['track_id']]['tid']]['ttitle'] ?? '',              //票名称
                        $item['pay_money'] / 100,                                                        //订单金额
                        $item['virtual_no'] ?? '',                                                       //虚拟卡号
                        $item['card_no'] ?? '无',                                                        //实体卡号
                        $item['dname'] ?? '无',                                                          //用户姓名
                        $item['id_card_no'] ? $item['id_card_no'] . "\t" : '无',                         //身份证号
                        empty($voucherTypeName) ? "身份证" : $voucherTypeName,                           //证件类型
                        $item['mobile'] ?? '无',                                                         //手机号
                        $avalidTime,                                                                     //有效期
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 1:
                    $extInfo         = json_decode($item['ext'], true);
                    $voucherTypeName = $this->conversionIdentityToName($extInfo['voucher_type']);
                    $return[]        = [
                        $item['dname'],                                                                  //会员名称
                        $item['mobile'],                                                                 //手机号
                        $item['id_card_no'] ? $item['id_card_no'] . "\t" : '--',                         //身份证号
                        empty($voucherTypeName) ? "身份证" : $voucherTypeName,                           //证件类型
                        $item['virtual_no'] ?? '--',                                                     //虚拟卡号
                        $item['card_no'] ?? '--',                                                        //实体卡号
                        $item['physics_no'] ?? '--',                                                     //物理ID
                        $avalidTime,                                                                     //有效期
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 2:
                    $ext          = json_decode($item['ext'], true);
                    $oldValidTime = ($ext['avalid_begin'] && $ext['avalid_end']) ?  date('Y-m-d', $ext['avalid_begin']) . ' - '. date('Y-m-d', $ext['avalid_end']) : '--';
                    $return[]     = [
                        $item['virtual_no'] ?? '--',                                                                                 //虚拟卡号
                        isset($ext['annual_status']) ? \Business\AnnualCard\Task::STATUS_MAP[$ext['annual_status']] : '未知状态',     //年卡状态
                        $oldValidTime,   //年卡原始有效期
                        $avalidTime, //年卡现有有效期
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 3:
                    $return[] = [
                        $item['virtual_no'] ?? '--',                                                     //虚拟卡号
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 4:
                case 5:
                    $ext = !empty($item['ext']) ? json_decode($item['ext'], true) : [];
                    $return[] = [
                        $item['virtual_no'] ?? '--',                                                    //虚拟卡号
                        isset($ext['annual_status']) ? \Business\AnnualCard\Task::STATUS_MAP[$ext['annual_status']] : '未知状态',     //年卡状态
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 6:
                case 7:
                    $ext = !empty($item['ext']) ? json_decode($item['ext'], true) : [];
                    $groupNames = !empty($ext['group_names']) ? implode("\n", $ext['group_names']) : '--';
                    $return[] = [
                        $item['virtual_no'] ?? '--',                                                    //虚拟卡号
                        isset($ext['annual_status']) ? \Business\AnnualCard\Task::STATUS_MAP[$ext['annual_status']] : '未知状态',     //年卡状态
                        $groupNames, //新增或删除的特权
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 8:
                    $return[] = [
                        $item['physics_no'] ?? '--',
                        $item['card_no'] ?? '--',
                        $item['virtual_no'] ?? '--',
                        $item['active_valid_period'] ?? '--',//激活有效期
                        $item['use_valid_period'] ?? '--',//使用有效期
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 9:
                    $return[] = [
                        $item['card_no'] ?? '--',
                        $item['physics_no'] ?? '--',
                        $item['d_status'],  //任务状态
                        $item['remark'],
                    ];
                    break;
                case 10:
                    $return[] = [
                        empty($taskMap[$item['track_id']]['dname']) ? $item['dname'] : $taskMap[$item['track_id']]['dname'],       //用户姓名
                        empty($taskMap[$item['track_id']]['mobile']) ? $item['mobile'] : $taskMap[$item['track_id']]['mobile'],      //手机号
                        $item['virtual_no'] ?? '',    //虚拟卡号
                        $item['card_no'] ?? '无',     //实体卡号
                        !empty($item['physics_no']) ? $item['physics_no'] : '--',  //物理卡号
                        $item['d_status'],            //任务状态
                        $item['remark'],              //备注
                    ];
                    break;
                default:
                    throw new \Exception('操作类型异常');
            }
        }

        return $return;
    }

    /**
     * 分批处理数据
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param $orderArr
     * @param $fidArr
     * @param $lidArr
     * @param $resellerArr
     * @param $isResource
     * @param $resellerMap
     * @param $noNeed
     * @param $inList
     * @param $isLand
     * @param $titleArr
     * @param $orderReportInfo
     * @param $cancelReportInfo
     * @param $revokeReportInfo
     * @param $printReportInfo
     *
     * @return array|bool
     */
    private function _blockHandleOrderInfoV2(
        $orderArr,
        $fidArr,
        $lidArr,
        $resellerArr,
        $isResource,
        $resellerMap,
        $noNeed,
        $inList,
        $isLand,
        $titleArr,
        $orderReportInfo,
        $cancelReportInfo,
        $revokeReportInfo,
        $printReportInfo,
        $windowRealReportInfo,
        $windowCancelOrdersInfo,
        $cancelServiceMap,
        $queryTime,
        $reportType,
        $pointsInfo,
        $oldHead,
        $customField,
        $afterSaleReportInfo = []
    )
    {
        $maxsize  = 500;
        $pathLog  = 'statistics/block/order/debug';
        $orderArr = is_array($orderArr) ? $orderArr : [];

        //获取全部订单号
        $orderArr = array_unique($orderArr);

        if (count($orderArr) < $maxsize) {
            pft_log($pathLog, "数据太少，不走分批处理，本次订单数：" . count($orderArr));

            return $this->_handleGetOrderInfoV2(
                $orderArr,
                $fidArr,
                $lidArr,
                $resellerArr,
                $isResource,
                $resellerMap,
                $noNeed,
                $inList,
                $isLand,
                $titleArr,
                $orderReportInfo,
                $cancelReportInfo,
                $revokeReportInfo,
                $printReportInfo,
                $windowRealReportInfo,
                $windowCancelOrdersInfo,
                $cancelServiceMap,
                $queryTime,
                $reportType,
                $pointsInfo,
                $customField,
                $afterSaleReportInfo
            );
        }

        pft_log($pathLog, "分批处理开始，处理订单总数：" . count($orderArr));

        //分批处理
        $orderList = array_chunk($orderArr, $maxsize);
        $ret       = [];
        $soonWrite = false; //标识是否已经写入csv文件了，已经写入返回true
        foreach ($orderList as $arr) {
            $data = $this->_handleGetOrderInfoV2(
                $arr,
                $fidArr,
                $lidArr,
                $resellerArr,
                $isResource,
                $resellerMap,
                $noNeed,
                $inList,
                $isLand,
                $titleArr,
                $orderReportInfo,
                $cancelReportInfo,
                $revokeReportInfo,
                $printReportInfo,
                $windowRealReportInfo,
                $windowCancelOrdersInfo,
                $cancelServiceMap,
                $queryTime,
                $reportType,
                $pointsInfo,
                $customField,
                $afterSaleReportInfo
            );
            //数据合并
            if (!empty($data)) {
                //有数据并且是传了csv实例进来的，需要标记成已经写入文件的
                if ($this->_isSoonWrite()) {
                    foreach ($data as &$item) {
                        //订单号转换成字符串
                        $item[0] .= "\t";

                        //按照导出配置重新排序下
                        $item = HandleExportDetailMappingBiz::handleDataFiledByIdx($item, $oldHead, $customField);
                    }
                    unset($item);

                    //直接写入到csv文件
                    $this->_CSVWrite($data);

                    $soonWrite = true;
                } else {
                    $ret = array_merge($ret, $data);
                }
            }
        }

        //处理完数据需要移除csv实例
        $this->_rmCSVObject();

        return $soonWrite ? true : $ret;

    }

    /**
     * 处理预订数据
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param $orderArr
     * @param $fidArr
     * @param $lidArr
     * @param $resellerArr
     * @param $isResource
     * @param $resellerMap
     * @param $noNeed
     * @param $inList
     * @param $isLand
     * @param $titleArr
     * @param $orderReportInfo
     * @param $cancelReportInfo
     * @param $revokeReportInfo
     * @param $printReportInfo
     *
     * @return array
     */
    private function _handleGetOrderInfoV2(
        $orderArr,
        $fidArr,
        $lidArr,
        $resellerArr,
        $isResource,
        $resellerMap,
        $noNeed,
        $inList,
        $isLand,
        $titleArr,
        $orderReportInfo,
        $cancelReportInfo,
        $revokeReportInfo,
        $printReportInfo,
        $windowRealReportInfo,
        $windowCancelOrdersInfo = [],
        $cancelServiceMap = [],
        $queryTime = [],
        $reportType = 0,
        $pointsInfo = [],
        $customField = null,
        $afterSaleReportInfo = []
    )
    {
        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();
        $orderQueryBiz  = new \Business\Order\Query();

        //获取下单操作员工ID
        $orderOpArr = [];
        $trackModel = new OrderTrack();

        //合并订单号字段
        $mergeOrderMap = [];

        if (!empty($orderArr)) {
            $trackList  = $trackModel->getListByOrders($orderArr, 'ordernum,oper_member,SalerID,action', 0);
            $orderOpArr = array_column($trackList, 'oper_member', 'ordernum');

            $splitModel    = new SubOrderSplit();
            //$splitList     = $splitModel->getSplitByOrderAndBuyer($orderArr, $fidArr);

            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $splitList = [];
            if (!empty($customField)) {
                if ($customField->isCustomUpApplyName()) {
                    $splitList = $orderAidsSplitQueryLib->getOrderDistributionChainByOrdernumsAndBuyerids($orderArr, $fidArr);
                }
            } else {
                $splitList = $orderAidsSplitQueryLib->getOrderDistributionChainByOrdernumsAndBuyerids($orderArr, $fidArr);
            }
            $sellerIdArr   = array_unique(array_column($splitList, 'sellerid'));
            $splitOrderArr = array_column($splitList, 'sellerid', 'orderid');

            $windowRealOrderInfo      = array_keys($windowRealReportInfo);
            $windowRealOrderSplitList = [];
            if (!empty($customField)) {
                if ($customField->isCustomWindowRealMoney()) {
                    $windowRealOrderSplitList = $splitModel->getListByOrderArr($windowRealOrderInfo,
                        'buyerid, level, orderid, sale_money');
                }
            } else {
                $windowRealOrderSplitList = $splitModel->getListByOrderArr($windowRealOrderInfo,
                    'buyerid, level, orderid, sale_money');
            }

            //云票务窗口实收金额
            $orderWindowRealMoneyMap = [];
            foreach ($windowRealOrderSplitList as $tmpSplit) {
                if (in_array($tmpSplit['level'], [0, -1]) || $tmpSplit['buyerid'] == 112) {
                    $orderWindowRealMoneyMap[$tmpSplit['orderid']] = $tmpSplit['sale_money'];
                }
            }

            $windowCancelOrderInfo      = array_keys($windowCancelOrdersInfo);
            $windowCancelOrderSplitList = [];
            if (!empty($customField)) {
                if ($customField->isCustomWindowCancelMoney()) {
                    $windowCancelOrderSplitList = $splitModel->getListByOrderArr($windowCancelOrderInfo,
                        'buyerid, level, orderid, sale_money');
                }
            } else {
                $windowCancelOrderSplitList = $splitModel->getListByOrderArr($windowCancelOrderInfo,
                    'buyerid, level, orderid, sale_money');
            }
            //云票务窗口取消金额
            $orderWindowCancelMoneyMap = [];
            foreach ($windowCancelOrderSplitList as $tmpSplit) {
                if (in_array($tmpSplit['level'], [0, -1]) || $tmpSplit['buyerid'] == 112) {
                    $orderWindowCancelMoneyMap[$tmpSplit['orderid']] = $tmpSplit['sale_money'];
                }
            }

            if ($this->_isOrderType($reportType)) {
                //查询合并订单号
                if (!empty($customField)) {
                    if ($customField->isCustomMergeOrdernum()) {
                        $mergeOrderMap = $orderQueryBiz->getCmbMapByOrdeerNumArr($orderArr);
                    }
                } else {
                    $mergeOrderMap = $orderQueryBiz->getCmbMapByOrdeerNumArr($orderArr);
                }
                //合并支付订单号取交易单号，交易单号不存在则则取订单号
                foreach ($orderArr as $orderId) {
                    if (!empty($mergeOrderMap[$orderId])) {
                        continue;
                    }
                    $mergeOrderMap[$orderId] = $orderId;
                }
            }
        }

        if (!empty($cancelReportInfo)) {
            $cancelOrderArr     = array_keys($cancelReportInfo);
            $cancelRealOrderArr = array_intersect($orderArr, $cancelOrderArr);

            $cancelOrderTmp     = $trackModel->getListByOrdersBatch($cancelRealOrderArr, 'ordernum, left_num', 4);
            $cancelOrderMap     = array_column($cancelOrderTmp, 'left_num', 'ordernum');

            $cancelTrackList    = $trackModel->getListByOrdersBatch($cancelRealOrderArr, 'ordernum,left_num,insertTime', ['IN', [1, 2]]);
            $cancelNewTrackList = [];

            foreach ($cancelTrackList as $tmpCancel) {
                if (!empty($queryTime['begin']) && !empty($queryTime['end'])) {
                    $tmpTime = strtotime($tmpCancel['insertTime']);
                    if ($tmpTime >= $queryTime['begin'] && $tmpTime <= $queryTime['end'] && $tmpCancel['left_num'] <= $cancelOrderMap[$tmpCancel['ordernum']]) {
                        $cancelNewTrackList[] = $tmpCancel;
                    }
                }
            }

            if (empty($cancelNewTrackList)) {
                $orderCancelTime = array_column($cancelTrackList, 'insertTime', 'ordernum');
            } else {
                $orderCancelTime = array_column($cancelNewTrackList, 'insertTime', 'ordernum');
            }
        }

        if (!empty($revokeReportInfo)) {
            $revokeOrderArr     = array_keys($revokeReportInfo);
            $revokeRealOrderArr = array_intersect($orderArr, $revokeOrderArr);

            $revokeTrackList  = $trackModel->getListByOrdersBatch($revokeRealOrderArr, 'ordernum, insertTime', ['IN', [6,7]]);
            $revokeCancelTime = array_column($revokeTrackList, 'insertTime', 'ordernum');
        }


        $memberNameRes = [];
        $memberComRes  = [];
        if (!empty($resellerArr) || !empty($orderOpArr)) {
            $memberIdArr = array_merge($resellerArr, $orderOpArr, $sellerIdArr);
            $memberInfo  = [];
            if (!empty($customField)) {
                if ($customField->isCustomResellerName() || $customField->isCustomUpApplyName() || $customField->isCustomOpName()) {
                    $memberInfo = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, dname');
                }
            } else {
                $memberInfo = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, dname');
            }
            $memberNameRes = array_column($memberInfo, 'dname', 'id');
            unset($memberInfo);

            if (!empty($resellerArr)) {
                $memberInfo2 = [];
                if (!empty($customField)) {
                    if ($customField->isCustomComName()) {
                        $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
                    }
                } else {
                    $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
                }
                $memberComRes = array_column($memberInfo2, 'com_name', 'fid');
                unset($memberInfo2);
            }
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        $landApi  = new \Business\CommodityCenter\Land();
        $landRes   = [];
        $landPtype = [];
        if (!empty($lidArr)) {
            if (!empty($customField)) {
                if ($customField->isCustomProductName() || $customField->isCustomPrintStatus() || $customField->isCustomPrintTime()) {
                    $landInfo = $landApi->queryLandMultiQueryById($lidArr);
                    if (!empty($landInfo)) {
                        foreach ($landInfo as $item) {
                            $landRes[$item['id']]   = $item['title'];
                            $landPtype[$item['id']] = $item['p_type'];
                        }
                    }
                }
            } else {
                $landInfo = $landApi->queryLandMultiQueryById($lidArr);
                if (!empty($landInfo)) {
                    foreach ($landInfo as $item) {
                        $landRes[$item['id']]   = $item['title'];
                        $landPtype[$item['id']] = $item['p_type'];
                    }
                }
            }
            unset($landInfo);
        }

        //获取全部订单号
        $orderArr   = array_unique($orderArr);
        //根据订单号获取信息
        $orderTools = new OrderTools();

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        //全部供应商id
        $suppliersIds = [];

        if (!empty($orderArr)) {
            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew(array_values($orderArr));
            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if (in_array($item['level'], [0, 1])) {
                        $suppliersIds[] = $item['sellerid'];
                    }
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']      = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode']    = $item['pmode'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['cost_money'] = $item['cost_money'];
                }
            }
        }

        $orderHidingBuz = new OrderInfoHiding();
        if ($suppliersIds) {
            $suppliersIds = array_unique($suppliersIds);
            //查询中间商隐藏客户关键信息配置
            $orderHidingBuz->batchGetHidingConfig($suppliersIds);
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取积分信息
        $pointsAndDiscount  = (new StatisticsDiscountsBiz())->batchOrderDiscountInfo($orderArr, $this->_loginInfo['sid']);
        $pointAndCouponInfo = $pointsAndDiscount['pointAndCouponInfo'];
        $discountList       = $pointsAndDiscount['discountInfo'];
        $extContentArr      = $pointsAndDiscount['extContentArr'];

        //获取零售价
        $lPriceInfo = [];
        $aPriceInfo = [];
        //获取供应价
        if (!empty($orderArr)) {
            $orderModeDesc    = load_config('order_mode');
            $orderModeDesc[0] = '正常分销商下单';
            $orderModeDesc[1] = '普通用户支付';
            $orderModeDesc[2] = '用户手机支付';

            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr,
                'orderid, apply_id, refund_num, verified_num, origin_num, lprice, aprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                    $aPriceInfo[$item['orderid']] = $item['aprice'];
                }
            }
        }

        $orderTrackData = [];
        if (!empty($customField)) {
            if ($customField->isCustomPayTime()) {
                $trackLib = new \Business\JavaApi\Order\Query\OrderTrack();
                $trackRes = $trackLib->findByParams($orderArr, [4]);

                if ($trackRes['code'] == 200 && isset($trackRes['data'])) {
                    foreach ($trackRes['data'] as $trackItem) {
                        $orderTrackData[$trackItem['ordernum']]['pay'] = ['insertTime' => strtotime($trackItem['inserttime'])];
                    }
                }
            }
        } else {
            $trackLib = new \Business\JavaApi\Order\Query\OrderTrack();
            $trackRes = $trackLib->findByParams($orderArr, [4]);

            if ($trackRes['code'] == 200 && isset($trackRes['data'])) {
                foreach ($trackRes['data'] as $trackItem) {
                    $orderTrackData[$trackItem['ordernum']]['pay'] = ['insertTime' => strtotime($trackItem['inserttime'])];
                }
            }
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');
        //支付方式配置
        $tmpPayConf = load_config('pay_mode_two', 'orderSearch');
        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');

        $return = [];
        if (!empty($orderArr)) {
            $orderOperMemberArr = [];
            //获取下单操作人
            $orderOperMemberRes = $trackModel->getListByOrders($orderArr, 'ordernum,source,oper_member, insertTime',
                0);
            if (is_array($orderOperMemberRes)) {
                foreach ($orderOperMemberRes as $item) {
                    $orderOperMemberArr[$item['ordernum']] = $item['oper_member'];
                }
            }
            $data = $orderTools->getOrderList($orderArr);
            $orderSearchModel = new OrderSearch('slave');
            $addonOrder = [];
            if (!empty($customField)) {
                if ($customField->isCustomThirdOrdernum() || $customField->isCustomPrintStatus() || $customField->isCustomPrintTime()) {
                    $addonOrder = $orderSearchModel->getOrderAddon($orderArr,
                        'orderid, tordernum, ifprint, update_time', true);
                }
            } else {
                $addonOrder = $orderSearchModel->getOrderAddon($orderArr,
                    'orderid, tordernum, ifprint, update_time', true);
            }

            //取票信息处理
            $orderPtypeArr = [];
            if (!empty($data) && !empty($addonOrder)) {
                foreach ($data as $item) {
                    $orderPtypeArr[$item['ordernum']] = isset($landPtype[$item['lid']]) ? $landPtype[$item['lid']] : '';
                }
            }

            //获取取票信息和站点信息  从变更记录获取
            $getOrderPrintStateMap = [];
            if (!empty($orderPtypeArr)) {
                if (!empty($customField)) {
                    if ($customField->isCustomSiteName() || $customField->isCustomPrintStatus() || $customField->isCustomPrintTime()) {
                        $getOrderPrintStateMap = (new \Business\Order\OrderList())->getOrderSiteAndPrintMap($orderArr,
                            0, true, $orderPtypeArr, $addonOrder);
                    }
                } else {
                    $getOrderPrintStateMap = (new \Business\Order\OrderList())->getOrderSiteAndPrintMap($orderArr, 0,
                        true, $orderPtypeArr, $addonOrder);
                }
            }

            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);
            $ticketList = [];
            if (!empty($customField)) {
                if ($customField->isCustomProductName()) {
                    //根据票ID集合获取票名
                    $javaApi   = new \Business\CommodityCenter\Ticket();
                    $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
                    if (!empty($ticketArr)) {
                        foreach ($ticketArr as $ticket) {
                            $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                        }
                    }
                }
            } else {
                //根据票ID集合获取票名
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
                if (!empty($ticketArr)) {
                    foreach ($ticketArr as $ticket) {
                        $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                    }
                }
            }

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr,
                'orderid, memo, aids, aids_price,aids_money');

            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']]   = $item['memo'];
                $aidsArr[$item['orderid']]      = $item['aids'];
                $aidsPriceArr[$item['orderid']] = $item['aids_price'];
                $aidsMoneyArr[$item['orderid']] = $item['aids_money'];
                $seriesArr[$item['orderid']]    = $item['series'] ? unserialize($item['series']) : '';
            }

            //获取支付中心订单号
            $payCenterOrderMap = [];
            if (!empty($data) && $this->_isOrderType($reportType)) {
                $tradeOrderIds = [];
                foreach ($data as $itemTmp) {
                    if (!empty($itemTmp['trade_order_id'])) {
                        $tradeOrderIds[] = $itemTmp['trade_order_id'];
                    } else {
                        $tradeOrderIds[] = $itemTmp['ordernum'];
                    }
                }
                if (!empty($tradeOrderIds)) {
                    //支付中心订单号
                    if (!empty($customField)) {
                        if ($customField->isCustomPayCenterOrdernum()) {
                            $payCenterOrderMap = (new \Business\NewJavaApi\Order\FeignClient())->tradeOrderQueryPayIdMap($tradeOrderIds);
                        }
                    } else {
                        $payCenterOrderMap = (new \Business\NewJavaApi\Order\FeignClient())->tradeOrderQueryPayIdMap($tradeOrderIds);
                    }
                }
            }

            //商户自定义收款名称
            $fidPayModeMap = [];
            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo  = $orderReportInfo[$item['ordernum']];
                    $thirdOrder = $addonOrder[$item['ordernum']]['tordernum'] ?? '';
                    $resellerId = $orderInfo['reseller_id'];
                    $fid        = $orderInfo['fid'];
                    $tnum       = isset($orderInfo['tnum']) ? $orderInfo['tnum'] : 0;
                    $cancelNum  = isset($cancelReportInfo[$item['ordernum']]['tnum']) ? $cancelReportInfo[$item['ordernum']]['tnum'] : 0;
                    $revokeNum  = isset($revokeReportInfo[$item['ordernum']]['tnum']) ? $revokeReportInfo[$item['ordernum']]['tnum'] : 0;
                    $printNum   = isset($printReportInfo[$item['ordernum']]['tnum']) ? $printReportInfo[$item['ordernum']]['tnum'] : 0;

                    //交易单号
                    $tradeOrderId = $item['ordernum'];
                    if (!empty($item['trade_order_id'])) {
                        $tradeOrderId = $item['trade_order_id'];
                    }

                    if (!isset($fidPayModeMap[$fid])) {
                        $fidPayModeMap[$fid] = DictPayModeService::getInstance()->businessPayModeConf($fid);
                    }

                    //售后数量
                    $afterSaleTicketNum = $afterSaleReportInfo[$item['ordernum']]['tnum'] ?? 0;

                    $windowRealNum   = isset($windowRealReportInfo[$item['ordernum']]['tnum']) ? $windowRealReportInfo[$item['ordernum']]['tnum'] : 0;
                    $windowCancelNum = isset($windowCancelOrdersInfo[$item['ordernum']]['tnum']) ? $windowCancelOrdersInfo[$item['ordernum']]['tnum'] : 0;

                    $tid      = $orderInfo['tid'];
                    $aids     = isset($aidsArr[$item['ordernum']]) ? $aidsArr[$item['ordernum']] : '';
                    $aid      = $item['aid'];
                    $memberId = $item['member'];
                    //单价
                    $tPrice       = $item['tprice'];
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '';
                    $comName      = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '';
                    $upApplyId    = $splitOrderArr[$item['ordernum']] ?: $fid;
                    $upApplyName  = $memberNameRes[$upApplyId] ?: '未知';

                    $orderMode     = $item['ordermode'];
                    $orderOperMember = $orderOperMemberArr[$item['ordernum']];
                    $reportChannel = $orderModeDesc[$orderMode];

                    //记录下原始的买入者
                    $oldResellerId = $orderInfo['reseller_id'];

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = $resourcePrice[$item['ordernum']];
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }

                    //权益支付订单(预存码和预售券权益订单)，单价需要置为0元
                    if ($fid == $aid || $fid == $memberId) {
                        if ($this->_checkSaleMoneyZero($orderMode, $payMode)) {
                            $price = 0;
                        }
                    }

                    //商户支付方式，含自定义
                    $selfPayMode = $fidPayModeMap[$fid][$payMode] ?? '未知';
                    //默认支付方式
                    $payMode = $payTypeArr[$payMode] ?? '未知';

                    // 支付时间
                    $payTime     = isset($orderTrackData[$item['ordernum']]['pay']['insertTime']) ? date("Y-m-d H:i:s",
                        $orderTrackData[$item['ordernum']]['pay']['insertTime']) : '';
                    $lidName     = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';

                    //算出结算价
                    if (!empty($aids)) {
                        //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                        $splitAids = $aids . ',' . $memberId;
                    } elseif ($aid != $memberId) {
                        //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                        $splitAids = $aid . ',' . $memberId;
                    } else {
                        //自供自销
                        $splitAids = $aid . ',' . $aid;
                    }

                    $splitAidsArr = explode(',', $splitAids);
                    $key          = array_search($fid, $splitAidsArr);

                    $isMiddle = true;
                    //针对中间级分销商隐藏游客信息
                    if (!empty($this->_loginInfo['memberID'])) {
                        $sid  = $this->_getSid($this->_loginInfo['memberID']);
                        $item = $orderHidingBuz->batchHideCustomerInformation($item, $splitAidsArr, $tid, $sid,
                            $this->_loginInfo['memberID']);

                        $orderOperMemberSid = $this->_getSid($orderOperMember);//获取下单操作人的主账号id
                        if($sid == $orderOperMemberSid && $sid == $memberId){
                            //末级分销商 管理端产品预订页面下单 memberId为自己 aid为上级分销商
                            //如果下单人的主账号id 是当前登录人主账号的sid  且是 订单表的memberId  那么就是末级分销商 或者顶级供应商
                            $isMiddle = false;
                        }else{
                            $isMiddle = true;
                            if($sid == $aid && ($orderMode != 0 && $orderMode != 23)){
                                //散客下单情况 orderMode就不是0的情况 且散客下单 aid为末级分销商
                                $isMiddle = false;
                            }
                        }
                    }

                    $tmpAidsPrice = isset($aidsPriceArr[$item['ordernum']]) ? $aidsPriceArr[$item['ordernum']] : '';

                    //注意: 过滤的是"",不是0,0的话是成本价0
                    if ($tmpAidsPrice != '') {
                        $tmpAidsPrice = $tmpAidsPrice . ',' . $tPrice;
                    } else {
                        $tmpAidsPrice = $tPrice;
                    }

                    $tmpAidsPrice = explode(',', $tmpAidsPrice);
                    //买进单价
                    $costPrice = isset($tmpAidsPrice[$key - 1]) ? $tmpAidsPrice[$key - 1] : $aPriceInfo[$item['ordernum']];

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;

                    //获取优惠金额
                    $discountDetail   = \Business\Statistics\CreateReportBase::handleDiscountInfo($pointAndCouponInfo, $pointsInfo, $item['ordernum'], $fid, $resellerId, $oldResellerId);
                    $orderPointMoney  = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['order_amount'];
                    $cancelPointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['cancel_amount'];
                    $revokePointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['revoke_amount'];

                    //分销销售优惠
                    $saleSettlementOrderMoney  = 0;
                    $saleSettlementRevokeMoney = 0;
                    $saleSettlementCancelMoney = 0;
                    //分销采购优惠
                    $buySettlementOrderMoney  = 0;
                    $buySettlementRevokeMoney = 0;
                    $buySettlementCancelMoney = 0;
                    if ($discountDetail['is_sale_discount']) {
                        $saleSettlementOrderMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['order_amount'];
                        $saleSettlementRevokeMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['revoke_amount'];
                        $saleSettlementCancelMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['cancel_amount'];
                    } else {
                        $buySettlementOrderMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['order_amount'];
                        $buySettlementRevokeMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['revoke_amount'];
                        $buySettlementCancelMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['cancel_amount'];
                    }

                    //计算折扣比例
                    //销售折扣
                    $saleDiscountMoney = 0;
                    //采购折扣
                    $buyDiscountMoney  = 0;
                    if (isset($discountList[$item['ordernum']])) {
                        $saleDiscountMoney = $discountList[$item['ordernum']]['sale'][$fid] ?? 0;
                        $buyDiscountMoney  = $discountList[$item['ordernum']]['buy'][$fid] ?? 0;
                    }

                    //分组情况
                    $group = $inList[$resellerId] ?: '';
                    //订单备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';

                    $exReturn     = [];
                    $tmpAidsMoney = isset($aidsMoneyArr[$item['ordernum']]) ? $aidsMoneyArr[$item['ordernum']] : '';
                    $orderSelfPayMode = [];
                    //分销价格链 (包含支付方式) && 分销链上的每一级支付方式
                    if ($tmpAidsMoney !== '') {
                        $splitMoney                        = \Library\Tools::aidMoneyFormat($tmpAidsMoney);
                        $orderSplitMoney[$item['orderid']] = $splitMoney;
                        foreach ($splitMoney as $keyMoney => $valMoney) {
                            if ($valMoney['payway'] == 0) {
                                //授信支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                            } elseif ($valMoney['payway'] == 2) {
                                //预存支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                            } else if ($valMoney['payway'] == 1 && $valMoney['type'] == 0) {
                                //余额支付而且是第一条
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                            }
                        }
                    }
                    $orderPayMode[$item['ordernum']][] = $payMode;
                    //商户支付方式，含自定义
                    $orderSelfPayMode[$item['ordernum']][] = $selfPayMode;

                    //买进的支付方式
                    $buyPayMode = $orderPayMode[$item['ordernum']][$key - 1] ?? '成本';
                    //卖出的支付方式
                    $sellPayMode = $orderSelfPayMode[$item['ordernum']][$key] ?? '自采';

                    //取票时间
                    $printTime   = isset($getOrderPrintStateMap[$item['ordernum']]['updateTime']) ? $getOrderPrintStateMap[$item['ordernum']]['updateTime'] : '';
                    //取票状态
                    $printStatus = isset($getOrderPrintStateMap[$item['ordernum']]['ifPrint']) ? $getOrderPrintStateMap[$item['ordernum']]['ifPrint'] : '';
                    //站点
                    $siteName    = isset($getOrderPrintStateMap[$item['ordernum']]['siteName']) ? $getOrderPrintStateMap[$item['ordernum']]['siteName'] : '';

                    //产品名称
                    $productName = $lidName . ' ' . ($ticketList[$tid] ?? '');
                    $tmpHead     = [$item['ordernum'], $productName];
                    $landAccountDelKey = [14, 16, 18, 20, 24, 28, 29, 30, 36, 37];

                    //预订新增字段, 资源账号没有
                    if ($this->_isOrderType($reportType)) {
                        $landAccountDelKey[] = 40;
                        $landAccountDelKey[] = 41;
                    }

                    if (in_array($reportType, [23, 13])) { //演出报表导出
                        //演出场次
                        $tmpHead[] = !empty($seriesArr[$item['ordernum']]) ? explode(' ', $seriesArr[$item['ordernum']][4])[1] : '';
                        $landAccountDelKey = array_map(function($v) {
                            return $v + 1;
                        }, $landAccountDelKey);
                    }

                    //三方订单号
                    $thirdOrderNum = substr($thirdOrder, 0, strpos($thirdOrder, '&'));
                    if (empty(trim($thirdOrderNum)) && !empty($thirdOrder) && !strstr($thirdOrder, '&')) {
                        $thirdOrderNum = $thirdOrder;
                    }

                    if ($tnum) {
                        $extReturn = [];
                        if ($this->_isOrderType($reportType)) {
                            $extReturn = array_merge($extReturn, [
                                //分时时间段
                                $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                                //支付中心订单号
                                $payCenterOrderMap[$tradeOrderId]?? '',
                                //合并订单号
                                $mergeOrderMap[$item['ordernum']] ?? '',
                                //售后数量
                                0,
                                //售后退回
                                0,
                                //售后收入
                                0,
                                //售后编号
                                '',
                            ]);
                        }
                        if (20 == $reportType) {
                            $windowRealMoney = empty($orderWindowRealMoneyMap[$item['ordernum']]) ? 0 : $windowRealNum * $orderWindowRealMoneyMap[$item['ordernum']] / 100;
                            $extReturn = array_merge($extReturn, [$windowRealMoney, 0]);
                        }

                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //销售优惠金额
                                ($eMoney + $orderPointMoney + $saleSettlementOrderMoney + (($saleDiscountMoney) * $tnum)) / 100,
                                //采购优惠金额
                                (($buyDiscountMoney * $tnum) + $buySettlementOrderMoney) / 100,
                                //取消销售优惠金额
                                0,
                                //取消采购优惠金额
                                0,]);
                        }

                        $return[] = array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付时间
                            $payTime,
                            //取消时间
                            '-',
                            //撤销撤改时间
                            '-',
                            //订单状态
                            $orderStatus,
                            //预定票数
                            $tnum,
                            //取消票数
                            0,
                            //撤销撤改票数
                            0,
                            //销售单价
                            $saleUnitMoney = round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //销售金额
                            ($price * $tnum  + ($saleDiscountMoney * $tnum)) / 100,
                            //采购金额
                            ($costPrice * $tnum + ($buyDiscountMoney * $tnum) ) / 100,
                            //取消销售金额
                            0,
                            //取消采购金额
                            0,
                            //撤销撤改销售金额
                            0,
                            //撤销撤改采购金额
                            0,
                            //退票手续费
                            0,
                            //取票状态
                            $printStatus,
                            //取票时间
                            $printTime,
                            //取票数量
                            $printNum,
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($cancelNum) {
                        $extReturn = [];
                        if ($this->_isOrderType($reportType)) {
                            $extReturn = array_merge($extReturn, [
                                //分时时间段
                                $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                                //支付中心订单号
                                $payCenterOrderMap[$tradeOrderId]?? '',
                                //合并订单号
                                $mergeOrderMap[$item['ordernum']] ?? '',
                                //售后数量
                                0,
                                //售后退回
                                0,
                                //售后收入
                                0,
                                //售后编号
                                '',
                            ]);
                        }
                        if (20 == $reportType) {
                            $windowCancelMoney = empty($orderWindowCancelMoneyMap[$item['ordernum']]) ? 0 : $windowCancelNum * $orderWindowCancelMoneyMap[$item['ordernum']] / 100;
                            $extReturn = array_merge($extReturn, [0, $windowCancelMoney]);
                        }

                        $tmpEmoney = $eMoney;
                        //订单状态为非取消或者撤销的情况  不展示优惠券
                        if (!in_array($item['status'], [3, 6])) {
                            $tmpEmoney = 0;
                        }

                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //销售优惠金额
                                0,
                                //采购优惠金额
                                0,
                                //取消销售优惠金额
                                ($tmpEmoney + $cancelPointMoney + $saleSettlementCancelMoney + ($saleDiscountMoney * $cancelNum)) / 100,
                                //取消采购优惠金额
                                ($buyDiscountMoney * $cancelNum + $buySettlementCancelMoney) / 100,]);
                        }

                        $return[] = array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付时间
                            $payTime,
                            //取消时间
                            $orderCancelTime[$item['ordernum']] ?? '',
                            //撤销撤改时间
                            !isset($orderCancelTime[$item['ordernum']]) ? ($revokeCancelTime[$item['ordernum']] ?? '') : '',
                            //订单状态
                            $orderStatus,
                            //预定票数
                            0,
                            //取消票数
                            $cancelNum,
                            //撤销撤改票数
                            0,
                            //销售单价
                            round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //销售金额
                            0,
                            //采购金额
                            0,
                            //取消销售金额
                            ($price * $cancelNum + ($saleDiscountMoney * $cancelNum)) / 100,
                            //取消采购金额
                            ($costPrice * $cancelNum + ($buyDiscountMoney * $cancelNum)) / 100,
                            //撤销撤改销售金额
                            0,
                            //撤销撤改采购金额
                            0,
                            //退票手续费
                            $cancelServiceMap[$item['ordernum']] ?? 0,
                            //取票状态
                            $printStatus,
                            //取票时间
                            $printTime,
                            //取票数量
                            0,
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($revokeNum) {
                        $extReturn = [];
                        if ($this->_isOrderType($reportType)) {
                            $extReturn = array_merge($extReturn, [
                                //分时时间段
                                $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                                //支付中心订单号
                                $payCenterOrderMap[$tradeOrderId]?? '',
                                //合并订单号
                                $mergeOrderMap[$item['ordernum']] ?? '',
                                //售后数量
                                0,
                                //售后退回
                                0,
                                //售后收入
                                0,
                                //售后编号
                                '',
                            ]);
                        }
                        if (20 == $reportType) {
                            $windowCancelMoney = empty($orderWindowCancelMoneyMap[$item['ordernum']]) ? 0 : $windowCancelNum * $orderWindowCancelMoneyMap[$item['ordernum']] / 100;
                            $extReturn = array_merge($extReturn, [0, $windowCancelMoney]);
                        }

                        $tmpEmoney = $eMoney;
                        //订单状态为非取消或者撤销的情况  不展示优惠券
                        if (!in_array($item['status'], [3, 6])) {
                            $tmpEmoney = 0;
                        }

                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //销售优惠金额
                                0,
                                //采购优惠金额
                                0,
                                //取消销售优惠金额
                                ($tmpEmoney + $revokePointMoney + $saleSettlementRevokeMoney + ($saleDiscountMoney * $revokeNum)) / 100,
                                //取消采购优惠金额
                                ($buyDiscountMoney * $revokeNum + $buySettlementRevokeMoney) / 100,]);
                        }

                        $return[] =  array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付时间
                            $payTime,
                            //取消时间
                            '',
                            //撤销撤改时间
                            $revokeCancelTime[$item['ordernum']] ?? '',
                            //订单状态
                            $orderStatus,
                            //预定票数
                            0,
                            //取消票数
                            0,
                            //撤销撤改票数
                            $revokeNum,
                            //销售单价
                            round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //销售金额
                            0,
                            //采购金额
                            0,
                            //取消销售金额
                            0,
                            //取消采购金额
                            0,
                            //撤销撤改销售金额
                            ($price * $revokeNum + ($saleDiscountMoney * $revokeNum)) / 100,
                            //撤销撤改采购金额
                            ($costPrice * $revokeNum + ($buyDiscountMoney * $revokeNum)) / 100,
                            //退票手续费
                            $cancelServiceMap[$item['ordernum']] ?? 0,
                            //取票状态
                            $printStatus,
                            //取票时间
                            $printTime,
                            //取票数量
                            0,
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($afterSaleTicketNum && $this->_isOrderType($reportType)) {
                        $extReturn = [
                            //分时时间段
                            $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                            //支付中心订单号
                            $payCenterOrderMap[$tradeOrderId]?? '',
                            //合并订单号
                            $mergeOrderMap[$item['ordernum']] ?? '',
                            //售后数量
                            $afterSaleReportInfo[$item['ordernum']]['tnum'] ?? 0,
                            //售后退回
                            round(($afterSaleReportInfo[$item['ordernum']]['refund'] ?? 0) / 100, 2),
                            //售后收入
                            round(($afterSaleReportInfo[$item['ordernum']]['income'] ?? 0) / 100, 2),
                            //售后编号
                            "\t" . implode("\n", $afterSaleReportInfo[$item['ordernum']]['code'] ?? []) ." ",
                        ];

                        if (20 == $reportType) {
                            $extReturn = array_merge($extReturn, [0, 0]);
                        }

                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //销售优惠金额
                                0,
                                //采购优惠金额
                                0,
                                //取消销售优惠金额
                                0,
                                //取消采购优惠金额
                                0,
                            ]);
                        }

                        $return[] = array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付时间
                            $payTime,
                            //取消时间
                            '',
                            //撤销撤改时间
                            '',
                            //订单状态
                            $orderStatus,
                            //预定票数
                            0,
                            //取消票数
                            0,
                            //撤销撤改票数
                            0,
                            //销售单价
                            round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //销售金额
                            0,
                            //采购金额
                            0,
                            //取消销售金额
                            0,
                            //取消采购金额
                            0,
                            //撤销撤改销售金额
                            0,
                            //撤销撤改采购金额
                            0,
                            //退票手续费
                            0,
                            //取票状态
                            $printStatus,
                            //取票时间
                            $printTime,
                            //取票数量
                            0,
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($printNum && !$tnum && !$cancelNum && !$revokeNum && !$afterSaleTicketNum) {
                        $extReturn = [];
                        if ($this->_isOrderType($reportType)) {
                            $extReturn = array_merge($extReturn, [
                                //分时时间段
                                $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                                //支付中心订单号
                                $payCenterOrderMap[$tradeOrderId]?? '',
                                //合并订单号
                                $mergeOrderMap[$item['ordernum']] ?? '',
                                //售后数量
                                0,
                                //售后退回
                                0,
                                //售后收入
                                0,
                                //售后编号
                                '',
                            ]);
                        }
                        if (20 == $reportType) {
                            $windowRealMoney = empty($orderWindowRealMoneyMap[$item['ordernum']]) ? 0 : $windowRealNum * $orderWindowRealMoneyMap[$item['ordernum']] / 100;
                            $extReturn = array_merge($extReturn, [0, 0]);
                        }

                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //销售优惠金额
                                0,
                                //采购优惠金额
                                0,
                                //取消销售优惠金额
                                0,
                                //取消采购优惠金额
                                0,]);
                        }

                        $return[] = array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //支付时间
                            $payTime,
                            //取消时间
                            '-',
                            //撤销撤改时间
                            '-',
                            //订单状态
                            $orderStatus,
                            //预定票数
                            0,
                            //取消票数
                            0,
                            //撤销撤改票数
                            0,
                            //销售单价
                            round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //销售金额
                            0,
                            //采购金额
                            0,
                            //取消销售金额
                            0,
                            //取消采购金额
                            0,
                            //撤销撤改销售金额
                            0,
                            //撤销撤改采购金额
                            0,
                            //退票手续费
                            0,
                            //取票状态
                            $printStatus,
                            //取票时间
                            $printTime,
                            //取票数量
                            $printNum,
                            //取票人
                            $item['ordername'],
                            //取票手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($isLand) {//资源账号部分字段不显示
                        foreach ($return as &$tmpData) {
                            foreach ($landAccountDelKey as $key) {
                                unset($tmpData[$key]);
                            }
                        }
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 分批处理验证数据
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param $orderArr
     * @param $fidArr
     * @param $lidArr
     * @param $resellerArr
     * @param $isResource
     * @param $resellerMap
     * @param $noNeed
     * @param $inList
     * @param $isLand
     * @param $titleArr
     * @param $orderReportInfo
     * @param $finishReportInfo
     * @param $revokeReportInfo
     *
     * @return array|bool
     */
    private function _blockHandlCheckInfoV2(
        $orderArr,
        $fidArr,
        $lidArr,
        $resellerArr,
        $isResource,
        $resellerMap,
        $noNeed,
        $inList,
        $isLand,
        $titleArr,
        $orderReportInfo,
        $finishReportInfo,
        $revokeReportInfo,
        $cancelServiceMap = [],
        $reportType = 0,
        $pointsInfo = [],
        $specialShowEMoney = [],
        $afterSaleReportInfo = [],
        $customField = null
    )
    {
        $maxsize  = 500;
        $pathLog  = 'statistics/block/check/debug';
        $orderArr = is_array($orderArr) ? $orderArr : [];

        //获取全部订单号
        $orderArr = array_unique($orderArr);

        if (count($orderArr) < $maxsize) {
            pft_log($pathLog, "数据太少，不走分批处理，本次订单数：" . count($orderArr));

            return $this->_handleGetCheckInfoV2(
                $orderArr,
                $fidArr,
                $lidArr,
                $resellerArr,
                $isResource,
                $resellerMap,
                $noNeed,
                $inList,
                $isLand,
                $titleArr,
                $orderReportInfo,
                $finishReportInfo,
                $revokeReportInfo,
                $cancelServiceMap,
                $reportType,
                $pointsInfo,
                $specialShowEMoney,
                $afterSaleReportInfo,
                $customField
            );
        }

        pft_log($pathLog, "分批处理开始，处理订单总数：" . count($orderArr));

        //分批处理
        $orderList = array_chunk($orderArr, $maxsize);
        $ret       = [];
        foreach ($orderList as $arr) {
            $data = $this->_handleGetCheckInfoV2(
                $arr,
                $fidArr,
                $lidArr,
                $resellerArr,
                $isResource,
                $resellerMap,
                $noNeed,
                $inList,
                $isLand,
                $titleArr,
                $orderReportInfo,
                $finishReportInfo,
                $revokeReportInfo,
                $cancelServiceMap,
                $reportType,
                $pointsInfo,
                $specialShowEMoney,
                $afterSaleReportInfo,
                $customField
            );
            //数据合并
            if (!empty($data)) {
                $ret = array_merge($ret, $data);
            }
        }

        return $ret;

    }

    /**
     * 处理验证数据
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param $orderArr
     * @param $fidArr
     * @param $lidArr
     * @param $resellerArr
     * @param $isResource
     * @param $resellerMap
     * @param $noNeed
     * @param $inList
     * @param $isLand
     * @param $titleArr
     * @param $orderReportInfo
     * @param $finishReportInfo
     * @param $revokeReportInfo
     *
     * @return array
     */
    private function _handleGetCheckInfoV2(
        $orderArr,
        $fidArr,
        $lidArr,
        $resellerArr,
        $isResource,
        $resellerMap,
        $noNeed,
        $inList,
        $isLand,
        $titleArr,
        $orderReportInfo,
        $finishReportInfo,
        $revokeReportInfo,
        $cancelServiceMap = [],
        $reportType = 0,
        $pointsInfo = [],
        $specialShowEMoney = [],
        $afterSaleReportInfo = [],
        $customField = null
    )
    {
        //根据分销商id获取信息
        $memberModel = new Member();
        $memberBiz   = new \Business\Member\Member();
        $orderQueryBiz  = new \Business\Order\Query();

        //合并订单号字段
        $mergeOrderMap = [];
        //获取下单操作员工ID
        $orderOpArr = [];
        if (!empty($orderArr)) {
            $trackModel = new OrderTrack();
            $trackList  = $trackModel->getListByOrders($orderArr, 'ordernum,oper_member', 0);
            $orderOpArr = array_column($trackList, 'oper_member', 'ordernum');

            $splitModel    = new SubOrderSplit();
            // $splitList     = $splitModel->getSplitByOrderAndBuyer($orderArr, $fidArr);

            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            if (!empty($customField)) {
                $splitList = [];
                if ($customField->isCustomUpApplyName()) {
                    $splitList = $orderAidsSplitQueryLib->getOrderDistributionChainByOrdernumsAndBuyerids($orderArr,
                        $fidArr);
                }
            } else {
                $splitList = $orderAidsSplitQueryLib->getOrderDistributionChainByOrdernumsAndBuyerids($orderArr,
                    $fidArr);
            }
            $sellerIdArr   = array_unique(array_column($splitList, 'sellerid'));
            $splitOrderArr = array_column($splitList, 'sellerid', 'orderid');

            if ($this->_isCheckedType($reportType)) {
                //查询合并订单号
                if (!empty($customField)) {
                    if ($customField->isCustomMergeOrdernum()) {
                        $mergeOrderMap = $orderQueryBiz->getCmbMapByOrdeerNumArr($orderArr);
                    }
                } else {
                    $mergeOrderMap = $orderQueryBiz->getCmbMapByOrdeerNumArr($orderArr);
                }
                //合并支付订单号取交易单号，交易单号不存在则则取订单号
                foreach ($orderArr as $orderId) {
                    if (!empty($mergeOrderMap[$orderId])) {
                        continue;
                    }
                    $mergeOrderMap[$orderId] = $orderId;
                }
            }
        }

        //站点信息 actionCode = 5获取验证站点
        if (!empty($customField)) {
            $getOrderPrintStateMap = [];
            if ($customField->isCustomSiteName()) {
                $getOrderPrintStateMap = (new \Business\Order\OrderList())->getOrderSiteAndPrintMap($orderArr, 5);
            }
        } else {
            $getOrderPrintStateMap = (new \Business\Order\OrderList())->getOrderSiteAndPrintMap($orderArr, 5);
        }

        $memberNameRes = [];
        $memberComRes  = [];
        if (!empty($resellerArr) || !empty($orderOpArr)) {
            $memberIdArr   = array_merge($resellerArr, $orderOpArr, $sellerIdArr);
            if (!empty($customField)) {
                $memberInfo = [];
                if ($customField->isCustomResellerName() || $customField->isCustomUpApplyName() || $customField->isCustomOpName() || $customField->isCustomCheckOpName()) {
                    $memberInfo = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, dname');
                }
            } else {
                $memberInfo = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, dname');
            }
            $memberNameRes = array_column($memberInfo, 'dname', 'id');
            unset($memberInfo);

            if (!empty($resellerArr)) {
                if (!empty($customField)) {
                    $memberInfo2 = [];
                    if ($customField->isCustomComName()) {
                        $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
                    }
                } else {
                    $memberInfo2 = $memberBiz->getInfoMemberExtListFromJava($resellerArr, 'fid, com_name');
                }
                $memberComRes = array_column($memberInfo2, 'com_name', 'fid');
                unset($memberInfo2);
            }
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        $landApi  = new \Business\CommodityCenter\Land();
        $landRes   = [];
        if (!empty($lidArr)) {
            if (!empty($customField)) {
                $landInfo = [];
                if ($customField->isCustomProductName()) {
                    $landInfo = $landApi->queryLandMultiQueryById($lidArr);
                }
            } else {
                $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            }
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取积分信息
        $pointsAndDiscount  = (new StatisticsDiscountsBiz())->batchOrderDiscountInfo($orderArr, $this->_loginInfo['sid']);
        $pointAndCouponInfo = $pointsAndDiscount['pointAndCouponInfo'];
        $discountList       = $pointsAndDiscount['discountInfo'];
        $extContentArr      = $pointsAndDiscount['extContentArr'];

        $orderTrackData = [];
        $trackLib       = new \Business\JavaApi\Order\Query\OrderTrack();
        if (!empty($customField)) {
            $trackRes = [];
            if ($customField->isCustomPayTime()) {
                $trackRes = $trackLib->findByParams($orderArr, [4]);
            }
        } else {
            $trackRes = $trackLib->findByParams($orderArr, [4]);
        }

        if (isset($trackRes['code']) && $trackRes['code'] == 200 && isset($trackRes['data'])) {
            foreach ($trackRes['data'] as $trackItem) {
                $orderTrackData[$trackItem['ordernum']]['pay'] = ['insertTime' => strtotime($trackItem['inserttime'])];
            }
        }

        //获取分销链信息
        $fxInfo        = [];
        $resourcePrice = [];
        //全部供应商id
        $suppliersIds = [];
        if (!empty($orderArr)) {
            $buyChainModel = new SubOrderSplit();
            $fxRes         = $buyChainModel->getListByOrderArr($orderArr,
                'orderid, buyerid, sellerid, cost_money, sale_money, pmode, level');

            //订单查询迁移三期
            //$orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            //$fxRes = $orderAidsSplitQueryLib->getListByOrderArrNew($orderArr);

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if (in_array($item['level'], [0, 1])) {
                        $suppliersIds[] = $item['sellerid'];
                    }
                    if ($isResource && in_array($item['level'], [0, 1])) {
                        $resourcePrice[$item['orderid']] = $item['cost_money'];
                        continue;
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }
        $orderHidingBuz = new OrderInfoHiding();

        if ($suppliersIds) {
            $suppliersIds = array_unique($suppliersIds);
            //查询中间商隐藏客户关键信息配置
            $orderHidingBuz->batchGetHidingConfig($suppliersIds);
        }

        //获取零售价
        $lPriceInfo = [];
        $aPriceInfo = [];
        if (!empty($orderArr)) {
            $orderToolModel = new OrderTools('slave');
            $field          = 'orderid, apply_id, refund_num, verified_num, origin_num, aprice, lprice';
            $lPriceRes      = $orderToolModel->getOrderApplyInfo($orderArr, $field);
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                    $aPriceInfo[$item['orderid']] = $item['aprice'];
                }
            }
        }

        //获取验证人员和渠道
        $checkInfo = [];
        $orderOperMemberArr = [];
        if (!empty($orderArr)) {
            $orderTrackModel = new OrderTrack();
            $sourceType      = $orderTrackModel::getSourceList();
            $orderModeDesc = load_config('order_mode');
            $checkRes        = $orderTrackModel->getListByOrders($orderArr, 'ordernum,source,oper_member, insertTime',
                5);
            if (is_array($checkRes)) {
                foreach ($checkRes as $item) {
                    $resellers[]                  = $item['oper_member'];
                    $checkInfo[$item['ordernum']] = $item;
                }
            }
            //获取下单操作人
            $orderOperMemberRes        = $orderTrackModel->getListByOrders($orderArr, 'ordernum,source,oper_member, insertTime',
                0);
            if (is_array($orderOperMemberRes)){
                foreach ($orderOperMemberRes as $item){
                    $orderOperMemberArr[$item['ordernum']] = $item['oper_member'];
                }
            }
        }

        //获取订单操作员信息
        if (!empty($resellers)) {
            $resellerArrs = array_unique($resellers);
            //根据操作员id获取信息
            $memberOperator = [];

            if (!empty($resellerArrs)) {
                $memberInfo = $memberModel->getMemberInfoByMulti($resellerArrs, 'id', 'id, dname');
                foreach (\SplFixedArray::fromArray($memberInfo) as $item) {
                    $memberOperator[$item['id']] = $item['dname'];
                }
                unset($memberInfo);
            }
        }

        //获取全部订单号
        $orderArr       = array_unique($orderArr);
        //获取支付方式的配置
        $payTypeArr     = load_config('order_pay_mode', 'business');
        //获取订单状态的配置
        $orderStatusArr = load_config('order_status', 'trade_record');
        //支付方式配置
        $tmpPayConf     = load_config('pay_mode_two', 'orderSearch');

        //根据订单号获取信息
        $return = [];
        if (!empty($orderArr)) {
            //根据订单号获取信息
            $orderTools       = new OrderTools();
            $orderSearchModel = new OrderSearch('slave');

            $data       = $orderTools->getOrderList($orderArr);
            if (!empty($customField)) {
                $addonOrder = [];
                if ($customField->isCustomThirdOrdernum()) {
                    $addonOrder = $orderSearchModel->getOrderAddon($orderArr, 'orderid, tordernum', true);
                }
            } else {
                $addonOrder = $orderSearchModel->getOrderAddon($orderArr, 'orderid, tordernum', true);
            }

            //订单详细信息获取
            $detailInfo = [];
            if (!empty($customField)) {
                if ($customField->isCustomFirstTime()) {
                    $detailInfo  = $orderToolModel->getOrderDetailInfo($orderArr, 'orderid, ext_content');
                }
            } else {
                $detailInfo  = $orderToolModel->getOrderDetailInfo($orderArr, 'orderid, ext_content');
            }
            $firstDtimes = [];
            foreach ($detailInfo as $item) {
                $extContent                    = json_decode($item['ext_content'], true) ?? [];
                $firstDtimes[$item['orderid']] = isset($extContent['firstDtime']) ? $extContent['firstDtime'] : '--';
            }
            $tidArr       = array_column($data, 'tid');
            $wheres['id'] = array('in', $tidArr);

            //根据票ID集合获取票名
            $javaApi = new \Business\CommodityCenter\Ticket();
            if (!empty($customField)) {
                if ($customField->isCustomProductName()) {
                    $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
                }
            } else {
                $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            }
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt      = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr,
                'orderid, memo, aids, aids_price,aids_money');
            $aidsArr      = [];
            $aidsPriceArr = [];
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']]   = $item['memo'];
                $aidsArr[$item['orderid']]      = $item['aids'];
                $aidsPriceArr[$item['orderid']] = $item['aids_price'];
                $aidsMoneyArr[$item['orderid']] = $item['aids_money'];
                $seriesArr[$item['orderid']]    = $item['series'] ? unserialize($item['series']) : '';
            }

            //获取支付中心订单号
            $payCenterOrderMap = [];
            if (!empty($data) && $this->_isCheckedType($reportType)) {
                $tradeOrderIds = [];
                foreach ($data as $itemTmp) {
                    if (!empty($itemTmp['trade_order_id'])) {
                        $tradeOrderIds[] = $itemTmp['trade_order_id'];
                    } else {
                        $tradeOrderIds[] = $itemTmp['ordernum'];
                    }
                }
                if (!empty($tradeOrderIds)) {
                    //支付中心订单号
                    if (!empty($customField)) {
                        if ($customField->isCustomPayCenterOrdernum()) {
                            $payCenterOrderMap = (new \Business\NewJavaApi\Order\FeignClient())->tradeOrderQueryPayIdMap($tradeOrderIds);
                        }
                    } else {
                        $payCenterOrderMap = (new \Business\NewJavaApi\Order\FeignClient())->tradeOrderQueryPayIdMap($tradeOrderIds);
                    }
                }
            }
            //商户自定义收款名称
            $fidPayModeMap = [];
            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo      = $orderReportInfo[$item['ordernum']];
                    $thirdOrder     = $addonOrder[$item['ordernum']];
                    $resellerId     = $orderInfo['reseller_id'];
                    $fid            = $orderInfo['fid'];
                    $tnum           = isset($orderInfo['tnum']) ? $orderInfo['tnum'] : 0;
                    $todayTicket    = ($orderInfo['today_check'] == 1) ? $tnum : 0;
                    $notTodayTicket = ($orderInfo['today_check'] == 1) ? 0 : $tnum;
                    $finishTnum     = isset($finishReportInfo[$item['ordernum']]['tnum']) ? $finishReportInfo[$item['ordernum']]['tnum'] : 0;
                    $aids           = isset($aidsArr[$item['ordernum']]) ? $aidsArr[$item['ordernum']] : '';
                    $aid            = $item['aid'];
                    $memberId       = $item['member'];
                    $tPrice         = $item['tprice'];
                    $tid            = $orderInfo['tid'];
                    $checkInfoExt   = $checkInfo[$item['ordernum']];
                    $operaMember    = $checkInfoExt['oper_member'];
                    $source         = $checkInfoExt['source'];
                    $orderMode      = $item['ordermode'];

                    //交易单号
                    $tradeOrderId = $item['ordernum'];
                    if (!empty($item['trade_order_id'])) {
                        $tradeOrderId = $item['trade_order_id'];
                    }

                    if (!isset($fidPayModeMap[$fid])) {
                        $fidPayModeMap[$fid] = DictPayModeService::getInstance()->businessPayModeConf($fid);
                    }

                    $orderOperMember = $orderOperMemberArr[$item['ordernum']];

                    $reportChannel = in_array($orderMode, [17, 20, 58]) ? $orderModeDesc[$orderMode] : $sourceType[$source];
                    $resellerName  = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';
                    $comName       = isset($memberComRes[$resellerId]) ? $memberComRes[$resellerId] : '未知';
                    $upApplyId     = $splitOrderArr[$item['ordernum']] ?: $fid;
                    $upApplyName   = $memberNameRes[$upApplyId] ?: '未知';

                    //记录下原始的买入者
                    $oldResellerId = $orderInfo['reseller_id'];

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    //结算单价
                    if (!$isResource) {
                        //出售单价 比较特殊 要考虑到分销链
                        if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                            $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                        } elseif (isset($lPriceInfo[$item['ordernum']])) {
                            $price = $lPriceInfo[$item['ordernum']];
                        } else {
                            $price = 0;
                        }
                    } else {
                        $price = isset($resourcePrice[$item['ordernum']]) ? $resourcePrice[$item['ordernum']] : 0;
                    }

                    //结算价
                    if (!empty($aids)) {
                        //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                        $splitAids = $aids . ',' . $memberId;
                    } elseif ($aid != $memberId) {
                        //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                        $splitAids = $aid . ',' . $memberId;
                    } else {
                        //自供自销
                        $splitAids = $aid . ',' . $aid;
                    }

                    $splitAidsArr = explode(',', $splitAids);
                    $key          = array_search($fid, $splitAidsArr);

                    $isMiddle = true;
                    //针对中间级分销商隐藏游客信息
                    if (!empty($this->_loginInfo['memberID'])) {
                        $sid  = $this->_getSid($this->_loginInfo['memberID']);
                        $item = $orderHidingBuz->batchHideCustomerInformation($item, $splitAidsArr, $tid, $sid,
                            $this->_loginInfo['memberID']);

                        $orderOperMemberSid = $this->_getSid($orderOperMember);//获取下单操作人的主账号id
                        if($sid == $orderOperMemberSid && $sid == $memberId){
                            //末级分销商 管理端产品预订页面下单 memberId为自己 aid为上级分销商
                            //如果下单人的主账号id 是当前登录人主账号的sid  且是 订单表的memberId  那么就是末级分销商 或者顶级供应商
                            $isMiddle = false;
                        }else{
                            $isMiddle = true;
                            if($sid == $aid && ($orderMode != 0 && $orderMode != 23)){
                                //散客下单情况 orderMode就不是0的情况 且散客下单 aid为末级分销商
                                $isMiddle = false;
                            }
                        }
                    }

                    $tmpAidsPrice = isset($aidsPriceArr[$item['ordernum']]) ? $aidsPriceArr[$item['ordernum']] : '';
                    //if (!empty($tmpAidsPrice)) {
                    //注意: 过滤的是"",不是0,0的话是成本价0
                    if ($tmpAidsPrice != '') {
                        $tmpAidsPrice = $tmpAidsPrice . ',' . $tPrice;
                    } else {
                        $tmpAidsPrice = $tPrice;
                    }

                    $tmpAidsPrice = explode(',', $tmpAidsPrice);
                    //买进单价
                    $costPrice = isset($tmpAidsPrice[$key - 1]) ? $tmpAidsPrice[$key - 1] : $aPriceInfo[$item['ordernum']];
                    // 支付时间
                    $payTime = isset($orderTrackData[$item['ordernum']]['pay']['insertTime']) ? date("Y-m-d H:i:s",
                        $orderTrackData[$item['ordernum']]['pay']['insertTime']) : '';

                    $lidName     = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';
                    $orderStatus = isset($orderStatusArr[$item['status']]) ? $orderStatusArr[$item['status']] : '未知';
                    $checkPeople = isset($memberOperator[$operaMember]) && $operaMember != 1 ? $memberOperator[$operaMember] : '未知';

                    //订单验证和撤销的票数
                    $orderCheckNum  = $orderReportInfo[$item['ordernum']]['tnum'] ?? 0;
                    $orderRevokeNum = $revokeReportInfo[$item['ordernum']]['tnum'] ?? 0;

                    //售后数量
                    $afterSaleTicketNum = $afterSaleReportInfo[$item['ordernum']]['tnum'] ?? 0;

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ?
                        $couponList[$item['ordernum']][$fid][$resellerId] : 0;

                    //获取优惠金额
                    $discountDetail   = \Business\Statistics\CreateReportBase::handleDiscountInfo($pointAndCouponInfo, $pointsInfo, $item['ordernum'], $fid, $resellerId, $oldResellerId);
                    $checkPointMoney  = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['check_amount'];
                    $revokePointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['revoke_amount'];
                    $finishPointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['finish_amount'];

                    //分销销售优惠
                    $saleSettlementCheckMoney  = 0;
                    $saleSettlementRevokeMoney = 0;
                    $saleSettlementFinishMoney = 0;
                    //分销采购优惠
                    $buySettlementCheckMoney  = 0;
                    $buySettlementRevokeMoney = 0;
                    $buySettlementFinishMoney = 0;
                    if ($discountDetail['is_sale_discount']) {
                        $saleSettlementCheckMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['check_amount'];
                        $saleSettlementRevokeMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['revoke_amount'];
                        $saleSettlementFinishMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['finish_amount'];
                    } else {
                        $buySettlementCheckMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['check_amount'];
                        $buySettlementRevokeMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['revoke_amount'];
                        $buySettlementFinishMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['finish_amount'];
                    }

                    //计算折扣比例
                    //销售折扣
                    $saleDiscountMoney = 0;
                    //采购折扣
                    $buyDiscountMoney  = 0;
                    if (isset($discountList[$item['ordernum']])) {
                        $saleDiscountMoney = $discountList[$item['ordernum']]['sale'][$fid] ?? 0;
                        $buyDiscountMoney  = $discountList[$item['ordernum']]['buy'][$fid] ?? 0;
                    }

                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }

                    //权益支付订单(预存码和预售券权益订单)，单价需要置为0元
                    if ($fid == $aid || $fid == $memberId) {
                        if ($this->_checkSaleMoneyZero($orderMode, $payMode)) {
                            $price = 0;
                        }
                    }

                    //商户支付方式，含自定义
                    $selfPayMode = $fidPayModeMap[$fid][$payMode] ?? '未知';
                    //默认支付方式
                    $payMode = $payTypeArr[$payMode] ?? '未知';

                    $tmpAidsMoney = isset($aidsMoneyArr[$item['ordernum']]) ? $aidsMoneyArr[$item['ordernum']] : '';
                    $orderSelfPayMode = [];
                    //分销价格链 (包含支付方式) && 分销链上的每一级支付方式
                    if ($tmpAidsMoney !== '') {
                        $splitMoney                        = \Library\Tools::aidMoneyFormat($tmpAidsMoney);
                        $orderSplitMoney[$item['orderid']] = $splitMoney;
                        foreach ($splitMoney as $keyMoney => $valMoney) {
                            if ($valMoney['payway'] == 0) {
                                //授信支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                            } elseif ($valMoney['payway'] == 2) {
                                //预存支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                            } else if ($valMoney['payway'] == 1 && $valMoney['type'] == 0) {
                                //余额支付而且是第一条
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                            }
                        }
                    }
                    $orderPayMode[$item['ordernum']][] = $payMode;
                    //商户支付方式，含自定义
                    $orderSelfPayMode[$item['ordernum']][] = $selfPayMode;

                    //买进的支付方式
                    $buyPayMode = $orderPayMode[$item['ordernum']][$key - 1] ?? '成本';
                    //卖出的支付方式
                    $sellPayMode = $orderSelfPayMode[$item['ordernum']][$key] ?? '自采';
                    //站点
                    $siteName = isset($getOrderPrintStateMap[$item['ordernum']]['siteName']) ? $getOrderPrintStateMap[$item['ordernum']]['siteName'] : '';
                    //备注
                    $memo = isset($dataExtRes[$item['ordernum']]) ? $dataExtRes[$item['ordernum']] : '';
                    //分组
                    $group = $inList[$resellerId] ?: '';

                    //产品名称
                    $productName = $lidName . ' ' . $ticketList[$tid];
                    $tmpHead     = [$item['ordernum'], $productName];
                    $landAccountDelKey = [16, 19, 21, 23, 24, 29, 30, 31, 37];

                    //验证新增字段, 资源账号没有
                    if ($this->_isCheckedType($reportType)) {
                        $landAccountDelKey[] = 42;
                        $landAccountDelKey[] = 43;
                        $landAccountDelKey[] = 44;
                        $landAccountDelKey[] = 45;
                        $landAccountDelKey[] = 46;
                        $landAccountDelKey[] = 47;

                    }

                    if (in_array($reportType, [24, 14])) { //演出验证
                        //演出场次
                        $tmpHead[] = !empty($seriesArr[$item['ordernum']]) ? explode(' ', $seriesArr[$item['ordernum']][4])[1] : '';
                        $landAccountDelKey = array_map(function($v) {
                            return $v + 1;
                        }, $landAccountDelKey);
                    }

                    //三方订单号
                    $thirdOrderNum = substr($thirdOrder, 0, strpos($thirdOrder, '&'));
                    if (empty(trim($thirdOrderNum)) && !empty($thirdOrder) && !strstr($thirdOrder, '&')) {
                        $thirdOrderNum = $thirdOrder;
                    }

                    if ($orderCheckNum || $finishTnum) {
                        //分销销售优惠 = 中台折扣 + 分销优惠（减免 + 特价票）
                        $saleTotalDiscountMoney = $saleSettlementCheckMoney + $saleSettlementFinishMoney;
                        //分销采购优惠 = 中台折扣 + 分销优惠（减免 + 特价票）
                        $buyTotalDiscountMoney  = $buySettlementCheckMoney + $buySettlementFinishMoney;
                        $extReturn              = [];
                        $tmpCheckMoney          = $orderCheckNum ? ($price * $orderCheckNum + ($saleDiscountMoney * $orderCheckNum)) / 100 : 0;

                        if ($this->_isCheckedType($reportType)) {
                            $extReturn = array_merge($extReturn, [
                                //分时时间段
                                $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                                //支付中心订单号
                                $payCenterOrderMap[$tradeOrderId]?? '',
                                //合并订单号
                                $mergeOrderMap[$item['ordernum']] ?? '',
                                //售后数量
                                0,
                                //售后退回
                                0,
                                //售后收入
                                0,
                                //售后编号
                                '',
                            ]);
                        }

                        if (!$isResource) {
                            if (isset($specialShowEMoney[$item['ordernum']]) && !$specialShowEMoney[$item['ordernum']]) {
                                $eMoney = 0;
                            }
                            $extReturn = array_merge($extReturn, [
                                //验证优惠金额
                                ($eMoney + $checkPointMoney + $finishPointMoney + $saleTotalDiscountMoney + ($saleDiscountMoney * ($tnum + $finishTnum))) / 100,
                                //采购优惠金额
                                (($buyDiscountMoney * ($tnum + $finishTnum)) + $buyTotalDiscountMoney) / 100,
                                //取消验证优惠金额
                                0,
                                //取消采购优惠金额
                                0,]);
                        }

                        $return[] = array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //首次入园时间
                            $item['product_type'] == 'J' ?  '-' : $firstDtimes[$item['ordernum']],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //完成时间
                            $checkInfoExt['insertTime'],
                            //支付时间
                            $payTime,
                            //撤销撤改时间
                            '-',
                            //订单状态
                            $orderStatus,
                            //总票数
                            $tnum + $finishTnum,
                            //验证数
                            $tnum,
                            //完结数
                            $finishTnum,
                            //撤销票数
                            0,
                            //销售单价
                            $saleUnitMoney = round($price / 100, 2),
                            //采购单价
                            round($costPrice / 100, 2),
                            //验证金额
                            $tmpCheckMoney,
                            //完结金额
                            ($price * $finishTnum + ($saleDiscountMoney * $finishTnum)) / 100,
                            //结算金额
                            ($costPrice * ($tnum + $finishTnum) + ($buyDiscountMoney * ($tnum + $finishTnum)))  / 100,
                            //撤销金额
                            0,
                            //撤销/撤改采购金额
                            0,
                            //退票手续费
                            0,
                            //当日下单验证数
                            $todayTicket,
                            //非当日下单验证数
                            $notTodayTicket,
                            //凭证码
                            "\t" . $item['code'] . " ",
                            //取票人
                            $item['ordername'],
                            //手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                             //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证人员
                            ($sid ?? 0) == $item['apply_did'] ? $checkPeople : '-',
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }


                    if ($orderRevokeNum) {
                        $tmpEmoney = $eMoney;
                        //订单状态为非取消或者撤销的情况  不展示优惠券
                        if (!in_array($item['status'], [3, 6])) {
                            $tmpEmoney = 0;
                        }
                        $extReturn = [];
                        if ($this->_isCheckedType($reportType)) {
                            $extReturn = array_merge($extReturn, [
                                //分时时间段
                                $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                                //支付中心订单号
                                $payCenterOrderMap[$tradeOrderId]?? '',
                                //合并订单号
                                $mergeOrderMap[$item['ordernum']] ?? '',
                                //售后数量
                                0,
                                //售后退回
                                0,
                                //售后收入
                                0,
                                //售后编号
                                '',
                            ]);
                        }
                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //验证优惠金额
                                0,
                                //采购优惠金额
                                0,
                                //取消验证优惠金额
                                ($tmpEmoney + $revokePointMoney + $saleSettlementRevokeMoney + ($saleDiscountMoney * $orderRevokeNum)) / 100,
                                //取消采购优惠金额
                                ($buyDiscountMoney * $orderRevokeNum + $buySettlementRevokeMoney) / 100,]);
                        }

                        $return[] =  array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //首次入园时间
                            $item['product_type'] == 'J' ?  '-' : $firstDtimes[$item['ordernum']],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //完成时间
                            $checkInfoExt['insertTime'],
                            //支付时间
                            $payTime,
                            //撤销撤改时间
                            $item['ctime'],
                            //订单状态
                            $orderStatus,
                            //总票数
                            0 - $orderRevokeNum,
                            //验证数
                            0,
                            //完结数
                            0,
                            //撤销票数
                            $orderRevokeNum,
                            //销售单价
                            $saleUnitMoney = round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //验证金额
                            0,
                            //完结金额
                            0,
                            //结算金额
                            0,
                            //撤销金额
                            ($price * $orderRevokeNum + ($saleDiscountMoney * $orderRevokeNum)) / 100,
                            //撤销采购金额
                            ($costPrice * $orderRevokeNum + ($buyDiscountMoney * $orderRevokeNum)) / 100,
                            //退票手续费
                            $cancelServiceMap[$item['ordernum']] ?? 0,
                            //当日下单验证数
                            0,
                            //非当日下单验证数
                            0,
                            //凭证码
                            "\t" . $item['code'] . " ",
                            //取票人
                            $item['ordername'],
                            //手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证人员
                            ($sid ?? 0) == $item['apply_did'] ? $checkPeople : '-',
                            //验证渠道
                            $reportChannel,
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($afterSaleTicketNum && $this->_isCheckedType($reportType)) {
                        $extReturn = [];
                        $extReturn = array_merge($extReturn, [
                            //分时时间段
                            $extContentArr[$item['ordernum']]['sectionTimeStr'] ?? '',
                            //支付中心订单号
                            $payCenterOrderMap[$tradeOrderId]?? '',
                            //合并订单号
                            $mergeOrderMap[$item['ordernum']] ?? '',
                            //售后数量
                            $afterSaleReportInfo[$item['ordernum']]['tnum'] ?? 0,
                            //售后退回
                            round(($afterSaleReportInfo[$item['ordernum']]['refund'] ?? 0) / 100, 2),
                            //售后收入
                            round(($afterSaleReportInfo[$item['ordernum']]['income'] ?? 0) / 100, 2),
                            //售后编号
                            "\t" . implode("\n", $afterSaleReportInfo[$item['ordernum']]['code'] ?? []) ." ",
                        ]);
                        if (!$isResource) {
                            $extReturn = array_merge($extReturn, [
                                //验证优惠金额
                                0,
                                //采购优惠金额
                                0,
                                //取消验证优惠金额
                                0,
                                //取消采购优惠金额
                                0,
                            ]);
                        }

                        $return[] = array_merge($tmpHead, [
                            //下单时间
                            $item['ordertime'],
                            //预计游玩时间
                            $item['playtime'],
                            //首次入园时间
                            $item['product_type'] == 'J' ? '-' : $firstDtimes[$item['ordernum']],
                            //开始有效时间
                            $item['begintime'],
                            //截止有效时间
                            $item['endtime'],
                            //完成时间
                            $checkInfoExt['insertTime'],
                            //支付时间
                            $payTime,
                            //撤销撤改时间
                            '-',
                            //订单状态
                            $orderStatus,
                            //总票数
                            0,
                            //验证数
                            0,
                            //完结数
                            0,
                            //撤销票数
                            0,
                            //销售单价
                            round($price / 100, 2),
                            //采购单价
                            $costPrice / 100,
                            //验证金额
                            0,
                            //完结金额
                            0,
                            //结算金额
                            0,
                            //撤销金额
                            0,
                            //撤销采购金额
                            0,
                            //退票手续费
                            0,
                            //当日下单验证数
                            0,
                            //非当日下单验证数
                            0,
                            //凭证码
                            "\t" . $item['code'] . " ",
                            //取票人
                            $item['ordername'],
                            //手机
                            $item['ordertel'],
                            //身份证
                            "\t" . $item['personid'] . " ",
                            //分销商企业名称
                            $comName,
                            //分销商账户名称
                            $resellerName,
                            //分组
                            $group,
                            //上级供应商
                            $upApplyName,
                            //卖出支付方式
                            $sellPayMode,
                            //买入支付方式
                            $buyPayMode,
                            //远程订单号
                            "\t" . $item['remotenum'] . " ",
                            //第三方订单号
                            "\t" . $thirdOrderNum . " ",
                            //下单员工
                            $isMiddle ? '-' : $memberNameRes[$orderOpArr[$item['ordernum']]],
                            //验证人员
                            '-',
                            //验证渠道
                            '-',
                            //站点
                            $siteName,
                            //备注
                            $memo,
                        ], $extReturn);
                    }

                    if ($isLand) {
                        foreach ($return as &$tmpData) {
                            foreach ($landAccountDelKey as $key) {
                                unset($tmpData[$key]);
                            }
                        }
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 获取主账号id
     * <AUTHOR>
     * @date  2021-05-07
     *
     * @param  int  $fid  当前登录id
     *
     * @return int
     */
    private function _getSid($fid)
    {
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        $time       = 3600 * 24;//缓存时间
        $cacheKey   = 'user_top_sid:uid:' . $fid;
        $sid        = $cacheRedis->get($cacheKey);
        if (!$sid) {
            $memberModel = new \Model\Member\Member();
            $memberInfo  = $memberModel->getMemberInfo($fid, 'id', 'dtype, account');
            $sid         = $fid;
            //如果是员工账号需要去获取主账号
            if ($memberInfo['dtype'] == 6) {
                $shipModel  = new MemberRelationQuery();
                $parentInfo = $shipModel->queryRelationshipEmployerMemberInfo($fid);
                if ($parentInfo) {
                    $sid = $parentInfo['id'];
                }
            } elseif (in_array($memberInfo['dtype'], [2, 3])) {
                // 资源方账号登录，获取供应商ID
                $landApi   = new \Business\CommodityCenter\Land();
                $applyInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [], [],
                    [$memberInfo['account']]);
                if (!empty($applyInfoRes['list'])) {
                    $sid = $applyInfoRes['list'][0]['apply_did'];
                }
            }
            $cacheRedis->set($cacheKey, $sid, $time);
        }

        return $sid;
    }

    /**
     * 根据查询的报表模板和参数生成查询参数
     *
     * <AUTHOR>
     * @date 2021/07/14
     *
     * @param  array  $itemValue
     * @param  array  $itemNotValue
     *
     * @return  array
     */
    public function handleSearchCondition(array $params, array $itemValue, array $itemNotValue)
    {
        $notPid = $notTid = '';
        $operateId = $venueId = '';
        if (!empty($itemValue) || !empty($itemNotValue)) {
            list($resellerId, $resellerGroup, $pid, $lid, $tid, $operateId, $channel, $payWay, $siteId, $cardType,
                $cardPid, $terminalType, $notPid, $notTid, $venueId) = $this->templateValueMap($itemValue, $itemNotValue);
        }

        //平台数据权限
        if (!empty($params['member_id'])) {
            $memberModel = new \Model\Member\Member();
            $memberInfo  = $memberModel->getMemberInfo($params['member_id'], 'id', 'dtype,account');

            if (isset($memberInfo['dtype'])) {
                $dataScope   = (new AuthLogicBiz())->memberDataResource($params['fid'], $params['member_id'], $memberInfo['dtype']);
                if (!$dataScope) {
                    $params['operate_id'] = $params['member_id'];
                }
            }
        }

        if (!empty($params['operate_id'])) {
            $operateId = $params['operate_id'];
        }

        if (isset($params['operator_scope'])) {
            $queryOperatorData = $this->getQueryOperatorByScope($params['fid'], $params['operator_scope'], $operateId);
            $operateId = $queryOperatorData['operator'] ?? '';
            $operateId == -1 && $operateId = '';
        }

        return [
            $params['reseller_id'] ?: $resellerId,
            $params['reseller_group'] ?: $resellerGroup,
            $params['pid'] ?: $pid,
            $params['lid'] ?: $lid,
            $params['tid'] ?: $tid,
            $operateId,
            $params['channel'] ?: $channel,
            $params['pay_way'] ?: $payWay,
            $params['site_id'] ?: $siteId,
            $params['card_type'] ?: $cardType,
            $params['card_pid'] ?: $cardPid,
            $params['terminal_type'] ?: $terminalType,
            $notPid,
            $notTid,
            $params['venue_id'] ?: $venueId,
        ];
    }


    /**
     * 根据模板设置的员工和请求的员工范围参数获取查询的员工id  (参数 pid 和 notPid 至少有一个为空)
     *  返回值说明
     *  not_pid => [1,2,3]      查询的pid参数不包含值中的产品id
     *  pid     => [1,2,3]      查询的pid参数只包含值中的产品id
     *  not_pid 和 pid 只会返回一种类型
     *
     * <AUTHOR>
     * @date 2021/09/08
     *
     * @param   integer  $fid                   主账号id
     * @param   string   $operatorScope         员工状态范围    0=正常 1=禁用 2=已删除 -1=不限
     * @param   string   $operatorId            报表模板中特殊维度设置的查询特定员工
     *
     * @return  array
     */
    public function getQueryOperatorByScope(int $fid, string $operatorScope, string $operatorId)
    {
        if ($operatorScope == -1) {
            //搜索所有的员工
            return ['operator' => $operatorId] ;
        }

        $cacheKey   = "pft001:statis_report:operator_data_" . $fid .'_' . $operatorScope;
        $cache      = \Library\Cache\Cache::getInstance('redis');
        $cacheValue = $cache->get($cacheKey);
        $operatorIdArr = [];

        if ($cacheValue) {
            $operatorIdArr = json_decode($cacheValue, true);
        } else {
            $state = explode(',', $operatorScope);
            //获取员工信息
            $memberRes = (new Member())->getStaffList($fid, '', 1, 1000, [], $state);
            if (isset($memberRes['list'])) {
                $operatorIdArr = array_column($memberRes['list'], 'id');
                in_array(0, $state) && $operatorIdArr[] = $fid;
                $cache->set($cacheKey, json_encode($operatorIdArr));
                $cache->expire($cacheKey, 60);
            }
        }

        $operArr = [];
        if ($operatorId) {
            $operatorArr = explode(',', $operatorId);
            foreach ($operatorArr as $tmpOper) {
                in_array($tmpOper, $operatorIdArr) && $operArr[] = $tmpOper;
            }
        } else {
            $operArr = $operatorIdArr;
        }

        if (empty($operArr)) {
            return ['operator' => -1];
        } else {
            return ['operator' => implode(',', $operArr)];
        }
    }


    /**
     * 订单退票手续费
     * Create by xiexy
     * Date: 2022/03/31
     * Time: 11:09
     * @param   int      $fid           计算手续费的用户id
     * @param   array    $serviceInfo   退款手续费数据
     *
     * @return  array
     */
    private function _getOrderServiceMap(array $cacelOrder, int $fid)
    {
        $cacelOrder       = array_unique($cacelOrder);
        $cancelServiceMap = [];

        $cancelInfo = (new OrderQuery())->getCancelServiceByOrderId($cacelOrder, 'orderid, service_info');
        foreach ($cancelInfo as $tmpCancelInfo) {
            $serviceInfo = json_decode($tmpCancelInfo['service_info'], true);
            $tmpMoney    = $this->countServiceMoney($fid, $serviceInfo);
            if (isset($cancelServiceMap[$tmpCancelInfo['orderid']])) {
                $cancelServiceMap[$tmpCancelInfo['orderid']] += ($tmpMoney /100);
            } else {
                $cancelServiceMap[$tmpCancelInfo['orderid']] = $tmpMoney  / 100;
            }
        }

        return $cancelServiceMap;
    }


    /**
     * 计算手续费
     * Create by xiexy
     * Date: 2022/03/31
     * Time: 11:09
     * @param   int      $fid           计算手续费的用户id
     * @param   array    $serviceInfo   退款手续费数据
     *
     * @return  int
     */
    public function countServiceMoney(int $fid, array $serviceInfo)
    {
        //当前级用户给上级的手续费
        $serviceMoneyUp = 0;
        //当前级用户收的手续费
        $serviceMoney   = 0;

        foreach ($serviceInfo as $tmpInfo) {
            if ($tmpInfo['fid'] == $fid) {
                $serviceMoneyUp = $tmpInfo['money'];
            }

            if ($tmpInfo['aid'] == $fid) {
                $serviceMoney = $tmpInfo['money'];
            }
        }

        $money = $serviceMoney - $serviceMoneyUp;
        return $money < 0 ? 0 : $money;
    }

    /**
     * 处理兼容年月查询导出
     * 目前部分报表只有日报表没有月报表，所以这边特殊处理下
     * <AUTHOR>
     * @date   2022/8/27
     *
     * @param  array  $params    导出参数
     *
     * @return array
     */
    public function paramsDateHandle(array $params)
    {
        //处理兼容年月查询导出
        $dateType = $params['date_type'] ?? 1;
        if ($dateType != 1 && isset($params['date'][1]) && is_array($params['date'][1])) {
            $beginDate = $params['date'][1][0] ?? 0;
            $endDate   = $params['date'][1][1] ?? 0;
            if ($beginDate && $endDate && strlen(strval($beginDate)) == 6 && strlen(strval($endDate)) == 6) {
                $beginDate      = $beginDate . '01';
                $endDate        = $endDate . '01';
                $endDate        = strtotime("$endDate 00:00:00 +1 month");
                $endDate        = date("Ymd", ($endDate - 1));
                $params['date'] = ['between', [$beginDate, $endDate]];
            }
        }

        return $params;
    }

    // /**
    //  * 【废弃】
    //  * 【\Business\Statistics\StatisticsDiscounts::getOrderDiscountInfo】替换
    //  * 获取订单积分使用信息及订单折扣信息
    //  *
    //  * @param  array  $orderArr  订单号数组
    //  * @param  int  $memberId  登录用户id
    //  *
    //  * @return array
    //  */
    // private function _getOrderPointsAndDiscount($orderArr, $memberId)
    // {
    //     $return = [
    //         'pointInfo'     => [],
    //         'discountInfo'  => [],
    //         'extContentArr' => [],
    //     ];
    //     if (!$orderArr) {
    //         return $return;
    //     }
    //
    //     $discountApi = new \Business\JavaApi\Order\OrderTicketDiscounts();
    //     $orderApi    = new \Business\JavaApi\Order\Query\OrderDetailQuery();
    //     $totalPage   = ceil(count($orderArr) / $this->_selectSize);
    //
    //     //积分和优惠券信息信息
    //     $pointAndCouponInfo = [];
    //     //折扣比例信息
    //     $discountInfo = [];
    //     //扩展信息数组
    //     $extContentArr = [];
    //     for ($i = 0; $i < $totalPage; $i++) {
    //         $queryIds       = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);
    //         $orderDetailArr = $orderApi->getOrderWithDetail($queryIds,true);
    //
    //         if ($orderDetailArr['code'] == 200) {
    //             //到中台查询当前订单的营销优惠信息
    //             $discountRes = $discountApi->calcDiscountByOrderNums($queryIds,true);//废弃
    //             if ($discountRes['code'] == 200 && !empty($discountRes['data'])) {
    //                 $discountDetail = array_column($discountRes['data'], null, 'orderNum');
    //             }
    //             //组装数据
    //             foreach ($orderDetailArr['data'] as $orderDetItem) {
    //                 $extContent   = json_decode($orderDetItem['fxDetails']['extContent'], true);
    //                 //记录下订单对应的扩展字段信息
    //                 $extContentArr[$orderDetItem['ordernum']] = $extContent;
    //                 //积分数据
    //                 if (isset($discountDetail[$orderDetItem['ordernum']]) && (\Business\Order\BaseCheck::checkUseDiscount($orderDetItem['ordernum'], [$orderDetItem['ordernum'] => $extContent]) || \Business\Order\BaseCheck::checkUseSettlementDiscount($orderDetItem['ordernum'], [$orderDetItem['ordernum'] => $extContent]))) {
    //                     $pointAndCouponInfo[$orderDetItem['ordernum']][$orderDetItem['aid']][$orderDetItem['member']] = [
    //                         \Business\Statistics\CreateReportBase::DISCOUNT            => ($discountDetail[$orderDetItem['ordernum']]['couponPrice'] ?? 0) +
    //                                                   ($discountDetail[$orderDetItem['ordernum']]['pointPrice'] ?? 0) +
    //                                                   ($discountDetail[$orderDetItem['ordernum']]['memberDiscountPrice'] ?? 0),
    //                         \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => ($discountDetail[$orderDetItem['ordernum']]['settlementDeductPrice'] ?? 0) +
    //                                                  ($discountDetail[$orderDetItem['ordernum']]['settlementSpecialPrice'] ?? 0),
    //                     ];
    //                 }
    //                 //折扣数据
    //                 $settlementDiscount = $extContent['settlementDiscount'];
    //                 if ($settlementDiscount) {
    //                     //判断是否是顶级供应商
    //                     if ($memberId == $orderDetItem['applyDid']) {
    //                         $discountInfo[$orderDetItem['ordernum']]['is_discount'] = true;
    //                     } else {
    //                         $discountInfo[$orderDetItem['ordernum']]['is_discount'] = false;
    //                     }
    //                     //售出优惠折扣（顶级）
    //                     $discountInfo[$orderDetItem['ordernum']]['sale'][$orderDetItem['applyDid']] = $extContent['settlementDiscount'][0]['couponMoney'];
    //                     //购买优惠折扣（一级）
    //                     $discountInfo[$orderDetItem['ordernum']]['buy'][$extContent['settlementDiscount'][0]['fid']] = $extContent['settlementDiscount'][0]['couponMoney'];
    //                 }
    //             }
    //
    //             $return = ['pointAndCouponInfo' => $pointAndCouponInfo, 'discountInfo' => $discountInfo, 'extContentArr' => $extContentArr];
    //         }
    //     }
    //
    //     return $return;
    // }

    /**
     * 处理台账预售和取消导出明细
     * <AUTHOR>
     * @date   2023/3/9
     *
     * @param  int     $fid                  商户第
     * @param  array   $orderArr             订单号
     * @param  array   $lidArr               景区id
     * @param  array   $resellerArr          分销商信息
     * @param  array   $resellerMap          分销商信息
     * @param  array   $orderReportInfo      报表信息
     * @param  array   $cancelReportInfo     取消报表信息
     * @param  string  $reportType           报表类型
     * @param  array   $pointsInfo           积分信息
     * @param  array   $sonTicketOrderArr    打包方子票订单集
     *
     * @return array
     */
    public function handleStandingBookOrderInfo($fid, $orderArr, $lidArr, $resellerArr, $resellerMap, $orderReportInfo, $cancelReportInfo, $reportType, $pointsInfo = [], $sonTicketOrderArr = [])
    {
        //根据分销商id获取信息
        $memberModel = new Member();

        $memberNameRes = [];
        if (!empty($resellerArr)) {
            $memberIdArr   = $resellerArr;
            $memberInfo    = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, dname');
            $memberNameRes = array_column($memberInfo, 'dname', 'id');
            unset($memberInfo);
        }

        //获取全部产品id
        $lidArr    = array_unique($lidArr);
        $landApi   = new \Business\CommodityCenter\Land();
        $landRes   = [];
        $landPtype = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']]   = $item['title'];
                    $landPtype[$item['id']] = $item['p_type'];
                }
            }
            unset($landInfo);
        }

        //台账子票订单继承主票销售渠道和支付方式
        if (!empty($sonTicketOrderArr)) {
            $packMainOrderInfo = $this->getMainOrderChannelAndPaywayBySonOrder($fid, $sonTicketOrderArr);
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);
        //根据订单号获取信息
        $orderTools = new OrderTools();

        //获取分销链信息
        $fxInfo = [];
        //全部供应商id
        $suppliersIds = [];

        if (!empty($orderArr)) {
            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew(array_values($orderArr));
            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if (in_array($item['level'], [0, 1])) {
                        $suppliersIds[] = $item['sellerid'];
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']      = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode']    = $item['pmode'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['cost_money'] = $item['cost_money'];
                }
            }
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取积分信息
        $pointsAndDiscount  = (new StatisticsDiscountsBiz())->batchOrderDiscountInfo($orderArr, $fid);
        $pointAndCouponInfo = $pointsAndDiscount['pointAndCouponInfo'];

        //获取零售价
        $lPriceInfo = [];
        $aPriceInfo = [];
        //获取供应价
        if (!empty($orderArr)) {
            $orderModeDesc    = load_config('order_mode');
            $orderModeDesc[0] = '正常分销商下单';
            $orderModeDesc[1] = '普通用户支付';
            $orderModeDesc[2] = '用户手机支付';

            $orderQueryModel = new OrderQuery();
            $lPriceRes       = $orderQueryModel->getApplyInfoByIdsSub($orderArr,
                'orderid, apply_id, refund_num, verified_num, origin_num, lprice, aprice');
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                    $aPriceInfo[$item['orderid']] = $item['aprice'];
                }
            }
        }

        $orderTrackData = [];
        $trackLib       = new \Business\JavaApi\Order\Query\OrderTrack();
        $trackRes       = $trackLib->findByParams($orderArr, [4]);

        if ($trackRes['code'] == 200 && isset($trackRes['data'])) {
            foreach ($trackRes['data'] as $trackItem) {
                $orderTrackData[$trackItem['ordernum']]['pay'] = ['insertTime' => strtotime($trackItem['inserttime'])];
            }
        }

        //商户自定义收款方式
        $payWayMap = DictPayModeService::getInstance()->businessPayModeConf($fid);
        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');
        //支付方式配置
        $tmpPayConf = load_config('pay_mode_two', 'orderSearch');

        $return = [];
        if (!empty($orderArr)) {
            $data = $orderTools->getOrderList($orderArr);

            $tidArr = array_column($data, 'tid');
            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr,
                'orderid, memo, aids, aids_price,aids_money');

            foreach ($dataExt as $item) {
                $aidsArr[$item['orderid']]      = $item['aids'];
                $aidsPriceArr[$item['orderid']] = $item['aids_price'];
                $aidsMoneyArr[$item['orderid']] = $item['aids_money'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo  = $orderReportInfo[$item['ordernum']];
                    $resellerId = $orderInfo['reseller_id'];
                    $fid        = $orderInfo['fid'];
                    $tnum       = isset($orderInfo['tnum']) ? $orderInfo['tnum'] : 0;
                    $cancelNum  = isset($cancelReportInfo[$item['ordernum']]['tnum']) ? $cancelReportInfo[$item['ordernum']]['tnum'] : 0;

                    $tid      = $orderInfo['tid'];
                    $aids     = isset($aidsArr[$item['ordernum']]) ? $aidsArr[$item['ordernum']] : '';
                    $aid      = $item['aid'];
                    $memberId = $item['member'];
                    //单价
                    $tPrice       = $item['tprice'];
                    $resellerName = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '';

                    $orderMode     = $item['ordermode'];
                    $reportChannel = $orderModeDesc[$orderMode];

                    //记录下原始的买入者
                    $oldResellerId = $orderInfo['reseller_id'];

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    //出售单价 比较特殊 要考虑到分销链
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                        $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                    } elseif (isset($lPriceInfo[$item['ordernum']])) {
                        $price = $lPriceInfo[$item['ordernum']];
                    } else {
                        $price = 0;
                    }

                    //支付方式有多种
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    //商户支付方式，含自定义
                    $selfPayMode = $payWayMap[$payMode] ?? '未知';
                    //默认支付方式
                    $payMode = $payTypeArr[$payMode] ?? '未知';

                    // 支付时间
                    $payTime = isset($orderTrackData[$item['ordernum']]['pay']['insertTime']) ? date("Y-m-d H:i:s",
                        $orderTrackData[$item['ordernum']]['pay']['insertTime']) : '';
                    $lidName = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';

                    //算出结算价
                    if (!empty($aids)) {
                        //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                        $splitAids = $aids . ',' . $memberId;
                    } elseif ($aid != $memberId) {
                        //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                        $splitAids = $aid . ',' . $memberId;
                    } else {
                        //自供自销
                        $splitAids = $aid . ',' . $aid;
                    }

                    $splitAidsArr = explode(',', $splitAids);
                    $key          = array_search($fid, $splitAidsArr);

                    $tmpAidsPrice = isset($aidsPriceArr[$item['ordernum']]) ? $aidsPriceArr[$item['ordernum']] : '';

                    //注意: 过滤的是"",不是0,0的话是成本价0
                    if ($tmpAidsPrice != '') {
                        $tmpAidsPrice = $tmpAidsPrice . ',' . $tPrice;
                    } else {
                        $tmpAidsPrice = $tPrice;
                    }

                    $tmpAidsPrice = explode(',', $tmpAidsPrice);
                    //买进单价
                    $costPrice = isset($tmpAidsPrice[$key - 1]) ? $tmpAidsPrice[$key - 1] : $aPriceInfo[$item['ordernum']];

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ? $couponList[$item['ordernum']][$fid][$resellerId] : 0;

                    //获取优惠金额
                    $discountDetail   = \Business\Statistics\CreateReportBase::handleDiscountInfo($pointAndCouponInfo, $pointsInfo, $item['ordernum'], $fid, $resellerId, $oldResellerId);
                    $orderPointMoney  = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['order_amount'];
                    $cancelPointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['cancel_amount'];

                    //分销优惠金额
                    $orderSettlementMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['order_amount'];
                    $cancelSettlementMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['cancel_amount'];

                    $tmpAidsMoney = isset($aidsMoneyArr[$item['ordernum']]) ? $aidsMoneyArr[$item['ordernum']] : '';
                    $orderSelfPayMode = [];
                    //分销价格链 (包含支付方式) && 分销链上的每一级支付方式
                    if ($tmpAidsMoney !== '') {
                        $splitMoney                        = \Library\Tools::aidMoneyFormat($tmpAidsMoney);
                        $orderSplitMoney[$item['orderid']] = $splitMoney;
                        foreach ($splitMoney as $keyMoney => $valMoney) {
                            if ($valMoney['payway'] == 0) {
                                //授信支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                            } elseif ($valMoney['payway'] == 2) {
                                //预存支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                            } else if ($valMoney['payway'] == 1 && $valMoney['type'] == 0) {
                                //余额支付而且是第一条
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                            }
                        }
                    }
                    $orderPayMode[$item['ordernum']][] = $payMode;
                    //商户支付方式，含自定义
                    $orderSelfPayMode[$item['ordernum']][] = $selfPayMode;
                    //买进的支付方式
                    $buyPayMode = $orderPayMode[$item['ordernum']][$key - 1] ?? '成本';
                    //卖出的支付方式
                    $sellPayMode = $orderSelfPayMode[$item['ordernum']][$key] ?? '自采';

                    //台账子票需要继承主票销售渠道和支付方式
                    if (in_array($item['ordernum'], $sonTicketOrderArr) && isset($packMainOrderInfo[$item['ordernum']])) {
                        //买进的支付方式
                        $buyPayMode = $packMainOrderInfo[$item['ordernum']]['buy_pay_mode'] ?? '';
                        //卖出的支付方式
                        $sellPayMode = $packMainOrderInfo[$item['ordernum']]['sell_pay_mode'] ?? '';
                        //销售渠道
                        $reportChannel = $packMainOrderInfo[$item['ordernum']]['channel_name'] ?? '';
                    }

                    //产品名称
                    $productName = $lidName . ' ' . $ticketList[$tid];

                    if ($tnum && $reportType == \Business\Statistics\StandingBook::REPORT_TYPE_SALE) {
                        $tmp = [
                            'ordernum'     => $item['ordernum'],
                            'pname'        => $productName,
                            'order_time'   => $item['ordertime'],
                            'pay_time'     => $payTime,
                            'order_ticket' => $tnum,
                            'sale_price'   => round($price / 100, 2),
                            'cost_price'   => round($costPrice / 100, 2),
                            'sale_money'   => round(($price * $tnum - ($eMoney + $orderPointMoney + $orderSettlementMoney)) / 100, 2),
                            'cost_money'   => round(($costPrice * $tnum) / 100, 2),
                            'channel'      => $reportChannel,
                            'reseller'     => $resellerName,
                            'sale_pay_way' => $sellPayMode,
                            'cost_pay_way' => $buyPayMode,
                        ];

                        $return[] = $tmp;
                    }

                    if ($cancelNum && $reportType == \Business\Statistics\StandingBook::REPORT_TYPE_CANCEL) {
                        $tmpEmoney = $eMoney;
                        //订单状态为非取消或者撤销的情况  不展示优惠券
                        if (!in_array($item['status'], [3, 6])) {
                            $tmpEmoney = 0;
                        }
                        $tmp = [
                            'ordernum'     => $item['ordernum'],
                            'pname'        => $productName,
                            'order_time'   => $item['ordertime'],
                            'pay_time'     => $payTime,
                            'order_ticket' => $cancelNum,
                            'sale_price'   => round($price / 100, 2),
                            'cost_price'   => round($costPrice / 100, 2),
                            'sale_money'   => round(($price * $cancelNum - ($tmpEmoney + $cancelPointMoney + $cancelSettlementMoney)) / 100, 2),
                            'cost_money'   => round(($costPrice * $cancelNum) / 100, 2),
                            'channel'      => $reportChannel,
                            'reseller'     => $resellerName,
                            'sale_pay_way' => $sellPayMode,
                            'cost_pay_way' => $buyPayMode,
                        ];

                        $return[] = $tmp;
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 处理台账验证和撤销撤改
     * <AUTHOR>
     * @date   2023/3/9
     *
     * @param  int     $fid                  商户第
     * @param  array   $orderArr             订单号
     * @param  array   $lidArr               景区id
     * @param  array   $resellerArr          分销商信息
     * @param  array   $resellerMap          分销商信息
     * @param  array   $orderReportInfo      报表信息
     * @param  array   $finishReportInfo     完结报表信息
     * @param  array   $revokeReportInfo     撤销撤改报表信息
     * @param  string  $reportType           报表类型
     * @param  array   $pointsInfo           积分信息
     * @param  array   $sonTicketOrderArr    打包方子票订单集
     *
     * @return array
     */
    public function handleStandingBookCheckInfo($fid, $orderArr, $lidArr, $resellerArr, $resellerMap, $orderReportInfo, $finishReportInfo, $revokeReportInfo, $reportType, $pointsInfo = [], $sonTicketOrderArr = [])
    {
        //根据分销商id获取信息
        $memberModel = new Member();

        $memberNameRes = [];
        if (!empty($resellerArr)) {
            $memberIdArr   = $resellerArr;
            $memberInfo    = $memberModel->getMemberInfoByMulti($memberIdArr, 'id', 'id, dname');
            $memberNameRes = array_column($memberInfo, 'dname', 'id');
            unset($memberInfo);
        }

        //获取全部产品id
        $lidArr  = array_unique($lidArr);
        $landApi = new \Business\CommodityCenter\Land();
        $landRes = [];
        if (!empty($lidArr)) {
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            if (!empty($landInfo)) {
                foreach ($landInfo as $item) {
                    $landRes[$item['id']] = $item['title'];
                }
            }
            unset($landInfo);
        }

        //台账子票订单继承主票销售渠道和支付方式
        if (!empty($sonTicketOrderArr)) {
            $packMainOrderInfo = $this->getMainOrderChannelAndPaywayBySonOrder($fid, $sonTicketOrderArr);
        }

        //获取优惠信息
        $couponList = [];
        if (!empty($orderArr)) {
            $onSaleRecord = $this->_getOnSaleRecord($orderArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }
        }

        //获取积分信息
        $pointsAndDiscount  = (new StatisticsDiscountsBiz())->batchOrderDiscountInfo($orderArr, $fid);
        $pointAndCouponInfo = $pointsAndDiscount['pointAndCouponInfo'];

        $orderTrackData = [];
        $trackLib       = new \Business\JavaApi\Order\Query\OrderTrack();
        $trackRes       = $trackLib->findByParams($orderArr, [4]);

        if ($trackRes['code'] == 200 && isset($trackRes['data'])) {
            foreach ($trackRes['data'] as $trackItem) {
                $orderTrackData[$trackItem['ordernum']]['pay'] = ['insertTime' => strtotime($trackItem['inserttime'])];
            }
        }

        //获取分销链信息
        $fxInfo = [];
        //全部供应商id
        $suppliersIds = [];
        if (!empty($orderArr)) {
            $buyChainModel = new SubOrderSplit();
            $fxRes         = $buyChainModel->getListByOrderArr($orderArr,
                'orderid, buyerid, sellerid, cost_money, sale_money, pmode, level');

            if (is_array($fxRes)) {
                foreach ($fxRes as $item) {
                    if (in_array($item['level'], [0, 1])) {
                        $suppliersIds[] = $item['sellerid'];
                    }
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['money']   = $item['sale_money'];
                    $fxInfo[$item['orderid']][$item['sellerid']][$item['buyerid']]['paymode'] = $item['pmode'];
                }
            }
        }

        //获取零售价
        $lPriceInfo = [];
        $aPriceInfo = [];
        if (!empty($orderArr)) {
            $orderToolModel = new OrderTools('slave');
            $field          = 'orderid, apply_id, refund_num, verified_num, origin_num, aprice, lprice';
            $lPriceRes      = $orderToolModel->getOrderApplyInfo($orderArr, $field);
            if (is_array($lPriceRes)) {
                foreach ($lPriceRes as $item) {
                    $lPriceInfo[$item['orderid']] = $item['lprice'];
                    $aPriceInfo[$item['orderid']] = $item['aprice'];
                }
            }
        }

        //获取验证人员和渠道
        $checkInfo = [];
        if (!empty($orderArr)) {
            $orderTrackModel  = new OrderTrack();
            $orderModeDesc    = load_config('order_mode');
            $orderModeDesc[0] = '正常分销商下单';
            $orderModeDesc[1] = '普通用户支付';
            $orderModeDesc[2] = '用户手机支付';
            $checkRes         = $orderTrackModel->getListByOrders($orderArr, 'ordernum,source,oper_member, insertTime',
                5);
            if (is_array($checkRes)) {
                foreach ($checkRes as $item) {
                    $resellers[]                  = $item['oper_member'];
                    $checkInfo[$item['ordernum']] = $item;
                }
            }
        }

        //获取全部订单号
        $orderArr = array_unique($orderArr);
        //商户自定义收款方式
        $payWayMap = DictPayModeService::getInstance()->businessPayModeConf($fid);
        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode', 'business');
        //支付方式配置
        $tmpPayConf = load_config('pay_mode_two', 'orderSearch');

        //根据订单号获取信息
        $return = [];
        if (!empty($orderArr)) {
            //根据订单号获取信息
            $orderTools = new OrderTools();
            $data       = $orderTools->getOrderList($orderArr);

            //订单详细信息获取
            $detailInfo  = $orderToolModel->getOrderDetailInfo($orderArr, 'orderid, ext_content');
            $firstDtimes = [];
            foreach ($detailInfo as $item) {
                $extContent                    = json_decode($item['ext_content'], true) ?? [];
                $firstDtimes[$item['orderid']] = isset($extContent['firstDtime']) ? $extContent['firstDtime'] : '--';
            }
            $tidArr = array_column($data, 'tid');

            //根据票ID集合获取票名
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
            $ticketList = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticket) {
                    $ticketList[$ticket['ticket']['id']] = $ticket['ticket']['title'];
                }
            }

            //获取订单备注
            $dataExt      = $this->_getSubDetailModel()->getDetailListByOrderArr($orderArr,
                'orderid, memo, aids, aids_price,aids_money');
            $aidsArr      = [];
            $aidsPriceArr = [];
            foreach ($dataExt as $item) {
                $dataExtRes[$item['orderid']]   = $item['memo'];
                $aidsArr[$item['orderid']]      = $item['aids'];
                $aidsPriceArr[$item['orderid']] = $item['aids_price'];
                $aidsMoneyArr[$item['orderid']] = $item['aids_money'];
            }

            if (!empty($data)) {
                foreach ($data as $item) {
                    $orderInfo    = $orderReportInfo[$item['ordernum']];
                    $resellerId   = $orderInfo['reseller_id'];
                    $fid          = $orderInfo['fid'];
                    $finishTnum   = isset($finishReportInfo[$item['ordernum']]['tnum']) ? $finishReportInfo[$item['ordernum']]['tnum'] : 0;
                    $aids         = isset($aidsArr[$item['ordernum']]) ? $aidsArr[$item['ordernum']] : '';
                    $aid          = $item['aid'];
                    $memberId     = $item['member'];
                    $tPrice       = $item['tprice'];
                    $tid          = $orderInfo['tid'];
                    $orderMode    = $item['ordermode'];
                    $reportChannel = $orderModeDesc[$orderMode] ?? '';
                    $resellerName  = isset($memberNameRes[$resellerId]) ? $memberNameRes[$resellerId] : '未知';

                    //记录下原始的买入者
                    $oldResellerId = $orderInfo['reseller_id'];

                    if (in_array($resellerId, $resellerMap) || $resellerId == 112) {
                        //如果是散客 分销商从订单信息里取
                        $resellerId = $item['member'];
                    }

                    //结算单价
                    //出售单价 比较特殊 要考虑到分销链
                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['money'])) {
                        $price = $fxInfo[$item['ordernum']][$fid][$resellerId]['money'];
                    } elseif (isset($lPriceInfo[$item['ordernum']])) {
                        $price = $lPriceInfo[$item['ordernum']];
                    } else {
                        $price = 0;
                    }

                    //结算价
                    if (!empty($aids)) {
                        //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                        $splitAids = $aids . ',' . $memberId;
                    } elseif ($aid != $memberId) {
                        //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                        $splitAids = $aid . ',' . $memberId;
                    } else {
                        //自供自销
                        $splitAids = $aid . ',' . $aid;
                    }

                    $splitAidsArr = explode(',', $splitAids);
                    $key          = array_search($fid, $splitAidsArr);

                    $tmpAidsPrice = isset($aidsPriceArr[$item['ordernum']]) ? $aidsPriceArr[$item['ordernum']] : '';
                    //注意: 过滤的是"",不是0,0的话是成本价0
                    if ($tmpAidsPrice != '') {
                        $tmpAidsPrice = $tmpAidsPrice . ',' . $tPrice;
                    } else {
                        $tmpAidsPrice = $tPrice;
                    }

                    $tmpAidsPrice = explode(',', $tmpAidsPrice);
                    //买进单价
                    $costPrice = isset($tmpAidsPrice[$key - 1]) ? $tmpAidsPrice[$key - 1] : $aPriceInfo[$item['ordernum']];
                    // 支付时间
                    $payTime = isset($orderTrackData[$item['ordernum']]['pay']['insertTime']) ? date("Y-m-d H:i:s",
                        $orderTrackData[$item['ordernum']]['pay']['insertTime']) : '';

                    $lidName = isset($landRes[$item['lid']]) ? $landRes[$item['lid']] : '未知';

                    //订单验证和撤销的票数
                    $orderCheckNum  = $orderReportInfo[$item['ordernum']]['tnum'] ?? 0;
                    $orderRevokeNum = $revokeReportInfo[$item['ordernum']]['tnum'] ?? 0;

                    //优惠金额
                    $eMoney = isset($couponList[$item['ordernum']][$fid][$resellerId]) ? $couponList[$item['ordernum']][$fid][$resellerId] : 0;


                    //获取优惠金额
                    $discountDetail   = \Business\Statistics\CreateReportBase::handleDiscountInfo($pointAndCouponInfo, $pointsInfo, $item['ordernum'], $fid, $resellerId, $oldResellerId);
                    $checkPointMoney  = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['check_amount'];
                    $revokePointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['revoke_amount'];
                    $finishPointMoney = $discountDetail[\Business\Statistics\CreateReportBase::DISCOUNT]['finish_amount'];

                    //分销优惠金额
                    $checkSettlementMoney  = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['check_amount'];
                    $revokeSettlementMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['revoke_amount'];
                    $finishSettlementMoney = $discountDetail[\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT]['finish_amount'];

                    if (isset($fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'])) {
                        $payMode = $fxInfo[$item['ordernum']][$fid][$resellerId]['paymode'];
                    } else {
                        $payMode = $item['paymode'];
                    }
                    //商户支付方式，含自定义
                    $selfPayMode = $payWayMap[$payMode] ?? '未知';
                    //默认支付方式
                    $payMode = $payTypeArr[$payMode] ?? '未知';

                    $tmpAidsMoney = isset($aidsMoneyArr[$item['ordernum']]) ? $aidsMoneyArr[$item['ordernum']] : '';
                    $orderSelfPayMode = [];
                    //分销价格链 (包含支付方式) && 分销链上的每一级支付方式
                    if ($tmpAidsMoney !== '') {
                        $splitMoney                        = \Library\Tools::aidMoneyFormat($tmpAidsMoney);
                        foreach ($splitMoney as $keyMoney => $valMoney) {
                            if ($valMoney['payway'] == 0) {
                                //授信支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[1]['name'];
                            } elseif ($valMoney['payway'] == 2) {
                                //预存支付
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[38]['name'];
                            } else if ($valMoney['payway'] == 1 && $valMoney['type'] == 0) {
                                //余额支付而且是第一条
                                $orderPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                                $orderSelfPayMode[$item['ordernum']][] = $tmpPayConf[0]['name'];
                            }
                        }
                    }
                    $orderPayMode[$item['ordernum']][] = $payMode;
                    //商户支付方式，含自定义
                    $orderSelfPayMode[$item['ordernum']][] = $selfPayMode;

                    $buyPayMode = $orderPayMode[$item['ordernum']][$key - 1] ?? '成本';
                    //卖出的支付方式
                    $sellPayMode = $orderSelfPayMode[$item['ordernum']][$key] ?? '自采';

                    //台账子票需要继承主票销售渠道和支付方式
                    if (in_array($item['ordernum'], $sonTicketOrderArr) && isset($packMainOrderInfo[$item['ordernum']])) {
                        //买进的支付方式
                        $buyPayMode = $packMainOrderInfo[$item['ordernum']]['buy_pay_mode'] ?? '';
                        //卖出的支付方式
                        $sellPayMode = $packMainOrderInfo[$item['ordernum']]['sell_pay_mode'] ?? '';
                        //销售渠道
                        $reportChannel = $packMainOrderInfo[$item['ordernum']]['channel_name'] ?? '';
                    }

                    //产品名称
                    $productName = $lidName . ' ' . $ticketList[$tid];

                    if (($orderCheckNum || $finishTnum) && $reportType == \Business\Statistics\StandingBook::REPORT_TYPE_CHECK) {
                        $tmp = [
                            'ordernum'     => $item['ordernum'],
                            'pname'        => $productName,
                            'order_time'   => $item['ordertime'],
                            'pay_time'     => $payTime,
                            'order_ticket' => $orderCheckNum + $finishTnum,
                            'sale_price'   => round($price / 100, 2),
                            'cost_price'   => round($costPrice / 100, 2),
                            'sale_money'   => round(($price * ($orderCheckNum + $finishTnum) + -($eMoney + $checkPointMoney + $finishPointMoney + $checkSettlementMoney + $finishSettlementMoney)) / 100,
                                2),
                            'cost_money'   => round(($costPrice * ($orderCheckNum + $finishTnum)) / 100, 2),
                            'channel'      => $reportChannel,
                            'reseller'     => $resellerName,
                            'sale_pay_way' => $sellPayMode,
                            'cost_pay_way' => $buyPayMode,
                        ];

                        $return[] = $tmp;
                    }

                    if ($orderRevokeNum && $reportType == \Business\Statistics\StandingBook::REPORT_TYPE_REVOKE) {
                        $tmpEmoney = $eMoney;
                        //订单状态为非取消或者撤销的情况  不展示优惠券
                        if (!in_array($item['status'], [3, 6])) {
                            $tmpEmoney = 0;
                        }
                        $tmp = [
                            'ordernum'     => $item['ordernum'],
                            'pname'        => $productName,
                            'order_time'   => $item['ordertime'],
                            'pay_time'     => $payTime,
                            'order_ticket' => $orderRevokeNum,
                            'sale_price'   => round($price / 100, 2),
                            'cost_price'   => round($costPrice / 100, 2),
                            'sale_money'   => round(($price * $orderRevokeNum + -($tmpEmoney + $revokePointMoney + $revokeSettlementMoney)) / 100,
                                2),
                            'cost_money'   => round(($costPrice * $orderRevokeNum) / 100, 2),
                            'channel'      => $reportChannel,
                            'reseller'     => $resellerName,
                            'sale_pay_way' => $sellPayMode,
                            'cost_pay_way' => $buyPayMode,
                        ];

                        $return[] = $tmp;
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 子票订单号查询主票订单信息
     * 台账报表需要继承主票销售渠道和买入卖出支付方式
     * <AUTHOR>
     * @date   2023/3/13
     *
     * @param  int    $fid           商户id
     * @param  array  $sonOrderArr   打包方子票集合
     *
     * @return array
     */
    public function getMainOrderChannelAndPaywayBySonOrder($fid, $sonOrderArr = [])
    {
        if (empty($sonOrderArr)) {
            return [];
        }

        $orderModeDesc    = load_config('order_mode');
        $orderModeDesc[0] = '正常分销商下单';
        $orderModeDesc[1] = '普通用户支付';
        $orderModeDesc[2] = '用户手机支付';

        //根据订单号获取信息
        $orderTools      = new OrderTools();
        $orderAddonTools = new \Model\Order\SubOrderQuery\SubOrderAddon();

        $sonOrderArr  = array_unique($sonOrderArr);
        $mainorderRes = $orderAddonTools->getPackInfoByOrderArr(array_unique($sonOrderArr), 'orderid, pack_order');

        $packRes = [];
        foreach ($mainorderRes as $item) {
            if ($item == 1 || !isset($item['orderid'])) {
                continue;
            }
            $packRes[$item['orderid']]['main_order'] = $item['pack_order'];
        }

        //去重后的主票订单信息
        $mainOrder = array_unique(array_column($packRes, 'main_order'));
        if (empty($mainOrder)) {
            pft_log('son_order/error', json_encode([$fid, $sonOrderArr]));

            return [];
        }

        //获取支付方式的配置
        $payTypeArr = load_config('order_pay_mode');
        //支付方式配置
        $tmpPayConf = load_config('pay_mode_two', 'orderSearch');

        //获取分销链信息
        $fxInfo = [];

        //订单查询迁移三期
        $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
        $fxRes                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew(array_values($mainOrder));
        if (is_array($fxRes)) {
            foreach ($fxRes as $item) {
                $fxInfo[$item['orderid']][$item['sellerid']]['paymode'] = $item['pmode'];
            }
        }

        //获取订单备注
        $dataExt = $this->_getSubDetailModel()->getDetailListByOrderArr($mainOrder,
            'orderid, memo, aids, aids_price,aids_money');

        foreach ($dataExt as $item) {
            $aidsMoneyArr[$item['orderid']] = $item['aids_money'];
            $aidsArr[$item['orderid']]      = $item['aids'];
        }

        //主票销售渠道
        $data = $orderTools->getOrderList($mainOrder);
        if (!empty($data)) {
            foreach ($data as $val) {
                $aids     = $aidsArr[$val['ordernum']] ?? '';
                $aid      = $val['aid'];
                $memberId = $val['member'];

                $orderMode     = $val['ordermode'];
                $reportChannel = $orderModeDesc[$orderMode] ?? '';

                //主票买入和卖出支付方式
                if (isset($fxInfo[$val['ordernum']][$fid]['paymode'])) {
                    $payMode = $fxInfo[$val['ordernum']][$fid]['paymode'];
                } else {
                    $payMode = $val['paymode'];
                }

                $payMode = $payTypeArr[$payMode] ?? '未知';

                $tmpAidsMoney = $aidsMoneyArr[$val['ordernum']] ?? '';
                //分销价格链 (包含支付方式) && 分销链上的每一级支付方式
                if ($tmpAidsMoney !== '') {
                    $splitMoney                        = \Library\Tools::aidMoneyFormat($tmpAidsMoney);
                    foreach ($splitMoney as $keyMoney => $valMoney) {
                        if ($valMoney['payway'] == 0) {
                            //授信支付
                            $orderPayMode[$val['ordernum']][] = $tmpPayConf[1]['name'];
                        } elseif ($valMoney['payway'] == 2) {
                            //预存支付
                            $orderPayMode[$val['ordernum']][] = $tmpPayConf[38]['name'];
                        } else if ($valMoney['payway'] == 1 && $valMoney['type'] == 0) {
                            //余额支付而且是第一条
                            $orderPayMode[$val['ordernum']][] = $tmpPayConf[0]['name'];
                        }
                    }
                }

                $orderPayMode[$val['ordernum']][] = $payMode;

                //算出结算价
                if (!empty($aids)) {
                    //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                    $splitAids = $aids . ',' . $memberId;
                } elseif ($aid != $memberId) {
                    //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                    $splitAids = $aid . ',' . $memberId;
                } else {
                    //自供自销
                    $splitAids = $aid . ',' . $aid;
                }

                $splitAidsArr = explode(',', $splitAids);
                $key          = array_search($fid, $splitAidsArr);

                //买进的支付方式
                $buyPayMode = isset($orderPayMode[$val['ordernum']][$key - 1]) ? $orderPayMode[$val['ordernum']][$key - 1] : '成本';
                //卖出的支付方式
                $sellPayMode = isset($orderPayMode[$val['ordernum']][$key]) ? $orderPayMode[$val['ordernum']][$key] : '自采';

                foreach ($packRes as $orderid => $tmp) {
                    if (isset($tmp['main_order']) && $tmp['main_order'] == $val['ordernum']) {
                        $packRes[$orderid]['channel']       = $orderMode;
                        $packRes[$orderid]['channel_name']  = $reportChannel;
                        $packRes[$orderid]['buy_pay_mode']  = $buyPayMode;
                        $packRes[$orderid]['sell_pay_mode'] = $sellPayMode;
                    }
                }
            }
        }

        return $packRes;
    }

    /**
     * 预订相关报表判断
     * <AUTHOR>
     * @date   2023/10/12
     *
     * @param int $reportType  报表类型
     *
     * @return bool
     */
    private function _isOrderType($reportType)
    {
        if (in_array($reportType, [1, 3, 18, 20, 25])) {
            return true;
        }

        return  false;
    }

    /**
     * 验证相关报表判断
     * <AUTHOR>
     * @date   2023/10/12
     *
     * @param int $reportType  报表类型
     *
     * @return bool
     */
    private function _isCheckedType($reportType)
    {
        if (in_array($reportType, [2, 4, 19, 26])) {
            return true;
        }

        return  false;
    }
    private function getPayWayConfig()
    {
        return load_config('pay_way', 'shiftSummary');
    }
    private function getPayModeConfig()
    {
        return load_config('pay_mode', 'shiftSummary');
    }
    
    private function handleCardCheckReport($cardCheckData){
        $return = [];
        foreach($cardCheckData as $item){
            $cardType = $item['card_type'];
            $productName = ExternalCard::cardTypeText[$cardType] ?? '';
            
            $extraInfo = [];
            if(!empty($item['ext_content'])){
                $extraInfo = JsonUtil::decode($item['ext_content']);
            }
            $memo = [];
            $extraCardInfo = $extraInfo['card_info'] ?? null;
            $opName = '-';
            if(!empty($extraCardInfo) && is_array($extraCardInfo)) {
                foreach($extraCardInfo as $one){
                    if(!empty($one['device_key'])){
                        $memo[] = '设备特征码:'.$one['device_key'];
                    }
                    if(!empty($one['device_name'])){
                        $memo[] = '设备名称:'.$one['device_name'];
                    }
                    
                    if(!empty($one['op_name']) && $opName == '-') {
                        $opName = $one['op_name'];
                    }
                }
            }
            
            $memoStr = '';
            if(!empty($memo)) {
                $memoStr = join(', ', $memo);
            }
            $return[] = [
                // 订单号码
                '-',
                // 产品名称
                $productName,
                //下单时间
                '-',
                //预计游玩时间
                '-',
                //首次入园时间
                date('Y-m-d H:i:s', $item['update_time']),
                //开始有效时间
                '-',
                //截止有效时间
                '-',
                //完成时间
                '-',
                //支付时间
                '',
                //撤销撤改时间
                '-',
                //订单状态
                '-',
                //总票数
                1,
                //验证数
                1,
                //完结数
                1,
                //撤销票数
                0,
                //销售单价
                '-',
                //采购单价
                '-',
                //验证金额
                '-',
                //完结金额
                '-',
                //结算金额
                '',
                //撤销金额
                0,
                //撤销/撤改采购金额
                0,
                //退票手续费
                0,
                //当日下单验证数
                '-',
                //非当日下单验证数
                '-',
                //凭证码
                '-',
                //取票人
                '-',
                //手机
                '-',
                //身份证
                '-',
                //分销商企业名称
                '-',
                //分销商账户名称
                '-',
                //分组
                '-',
                //上级供应商
                '-',
                //卖出支付方式
                '-',
                //买入支付方式
                '-',
                //远程订单号
                '-',
                //第三方订单号
                '-',
                //下单员工
                '-',
                //验证人员
                $opName,
                //验证渠道
                '-',
                //站点
                '-',
                //备注
                $memoStr,
            ];
        }
        return $return;
    }

    /**
     * 退票手续费
     * <AUTHOR>
     * @date   2024/05/15
     *
     * @param  array  $cacelOrder
     * @param  int    $fid
     *
     * @return array
     */
    public function getOrderServiceMap(array $cacelOrder, int $fid)
    {
        return $this->_getOrderServiceMap($cacelOrder, $fid);
    }

    /**
     * 获取优惠券信息
     * <AUTHOR>
     * @date   2024/05/15
     *
     * @param  array  $orderArr
     *
     * @return array
     */
    public function getOnSaleRecord($orderArr)
    {
        return $this->_getOnSaleRecord($orderArr);
    }

    // /**
    //  * 获取报表优惠信息
    //  * <AUTHOR>
    //  * @date   2024/05/15
    //  *
    //  * @param  array  $orderArr
    //  * @param  int    $fid
    //  *
    //  * @return array|array[]
    //  */
    // public function getOrderPointsAndDiscount($orderArr, $fid)
    // {
    //     return $this->_getOrderPointsAndDiscount($orderArr, $fid);
    // }

    /**
     * 获取订单明细
     * <AUTHOR>
     * @date   2024/05/15
     *
     * @return SubOrderDetail
     */
    protected function getSubDetailModel()
    {
        return $this->_getSubDetailModel();
    }

    /**
     * 报表处理获取主账号
     * <AUTHOR>
     * @date   2024/05/15
     *
     * @param  int  $fid
     *
     * @return int
     */
    protected function getSid($fid)
    {
        return $this->_getSid($fid);
    }

    /**
     * 验证卖出金额是否为0元
     * <AUTHOR>
     * @date   2024/07/30
     *
     * @param int $channel 渠道
     * @param int $payMode 支付方式
     *
     * @return bool
     */
    protected function _checkSaleMoneyZero($channel, $payMode)
    {
        //预售券权益支付
        $checkExchangeCoupon = ExchangeCouponProductBiz::isExchangeCouponRedeemOrders($channel, $payMode);
        //预存码权益支付
        $checkPreDepositCode = $payMode == OrderPayModeConstants::PRE_DEPOSIT_CODE_PAY_TYPE;

        if ($checkExchangeCoupon || $checkPreDepositCode) {
            return true;
        }

        return false;
    }

}