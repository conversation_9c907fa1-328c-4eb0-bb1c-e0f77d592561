<?php
/**
 * 实时报表
 */

namespace Business\Statistics;

use Business\JavaApi\Order\Query\OrderDetailQuery;
use Business\Report\ReserveReport as ReserveReportBiz;
use Exception;
use Library\Constants\Order\OrderPayMode as OrderPayModeConstants;
use Library\Constants\Team\TeamConst;
use Library\Constants\ThemeConst;
use Library\Resque\Queue;
use Library\Util\Team\TeamUtil;
use Model\EnterLand\EnterLand;
use Model\Member\Member;
use Model\Order\BuyChain;
use Model\Order\OrderQuery;
use Model\Order\OrderRefer;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\SubOrderQuery\SubOrderAddon;
use Model\Order\SubOrderQuery\SubOrderMain;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Model\Order\TeamOrderSearch;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Report\Statistics;
use Model\Report\StatisticsV3;
use Business\Admin\ModuleConfig;
use Business\Statistics\statistics as statisticsBiz;
use Model\Report\PftReportRealTask as ReportRealTaskModel;
use Business\Report\ShowReserveReport as ShowReserveReportBiz;
use Library\Constants\Order\OrderChannel as OrderChannelConstants;
use Business\Product\ExchangeCouponProduct as ExchangeCouponProductBiz;

class RealTimeStatistics extends RealTimeStatisticsBase
{
    private $_buyChainModel;
    private $_subOrderSplitModel;
    private $_subOrderAddonModel;
    private $_subOrderMainModel;
    private $_queryModel;
    private $_memberModel;
    private $_statisticsModel;
    private $_orderQueryModel;
    private $_landModel;
    private $_ticketModel;
    private $_resellerMap;
    private $_defaultReseller = 112;
    private $_subOrderTrackModel;
    private $_orderReferModel;
    private $_enterLandModel;
    private $_orderTrackModel;
    private $_reserveAction = [1, 2, 3]; //预约报表 预订、取消、撤销撤改
    private $_reportRealTaskModel;
    private $_reserveReport;
    private $_showReserveReport;

    private $_statisticsBiz;

    public function __construct()
    {
        $resellerMapConfig = load_config('reseller_map', 'trade_record');
        if (!$this->_resellerMap) {
            foreach ($resellerMapConfig as $key => $item) {
                $this->_resellerMap[$key] = $item['id'];
            }
        }

        $this->_statisticsBiz = new statisticsBiz();
    }



    /**
     * 取消报表 实时数据
     * @TODO 此处会有个bug  取退票手续费的时候 是按订单号从uu_order_cancel_info表取最后一条数据
     * @TODO 如果在队列生成A订单 报表前 uu_order_cancel_info 多次插入了该订单的数据 会造成数据错误
     *
     * @param  $orderNum int  订单号
     * @param  $num      int  票数
     * @param  $action   int  1 修改/取消   2 撤销/撤改
     */
    public function createCancelData($orderNum, $num, $time, $action, $operateMember, $flag = -1, $actionIdxs = '')
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            $field     = 'lid, tid, ordermode, ordertime, totalmoney, paymode, pay_status';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            //$orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            if ($orderInfo['pay_status'] == 2) {
                throw new Exception("订单未支付");
            }

            $channel = $orderInfo['ordermode'];
            $lid     = $orderInfo['lid'];
            $tid     = $orderInfo['tid'];
            $payMode = $orderInfo['paymode'];
            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money';
            //$tmp = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //获取退票手续费信息
            $cancelInfo = $this->_getServiceMoney($orderNum);

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取本次操作积分抵扣金额
            $discountDetail           = $this->_getOrderPointsDetail($orderNum, $extContentInfo, $flag, $actionIdxs);
            $discountAmount           = $discountDetail['discountAmount'];              //游客优惠
            $settlementDiscountAmount = $discountDetail['settlementDiscountAmount'];    //分销优惠

            //组装优惠信息
            $discountInfo[$orderNum][$flag][$orderInfo['aid']][$orderInfo['member']] = [
                \Business\Statistics\CreateReportBase::DISCOUNT            => $discountAmount,
                \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => $settlementDiscountAmount,
            ];

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item               = $tmp[$i];
                $fid                = $item['sellerid'];
                $sellerId           = $item['buyerid'];
                $level              = $item['level'];
                $settlementDiscount = $item['settlement_discount'] ?? 0; //分销优惠金额

                //最末级优惠金额的判断
                $tmpCostMoney = $level == -4 ? $orderInfo['totalmoney'] : $item['cost_money'] * $num - $settlementDiscount;
                $costMoney    = $tmpCostMoney;

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //计算游客优惠使用情况
                $discountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT])) {
                    $discountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT];
                }

                //计算分销优惠使用情况
                $settlementDiscountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT])) {
                    $settlementDiscountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT];
                }

                $saleMoney = $item['sale_money'] * $num - $eMoney - $discountMoney - $settlementDiscountMoney;

                //去除退票手续费
                $serviceMoneyIn  = 0;
                $serviceMoneyOut = 0;

                if (!empty($cancelInfo)) {
                    foreach ($cancelInfo as $k => $v) {
                        if ($v['fid'] == $sellerId && $v['aid'] == $fid) {
                            $serviceMoneyIn = $v['money'];
                        }
                        if ($v['fid'] == $fid) {
                            $serviceMoneyOut = $v['money'];
                        }
                    }
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                $insert[]  = [
                    'fid'               => $fid,
                    'date'              => date('Ymd', $time),
                    'lid'               => $lid,
                    'tid'               => $tid,
                    'reseller_id'       => $sellerId,
                    'channel'           => $channel,
                    'level'             => in_array($level, [0, 1]) ? 1 : $level,
                    'order_num'         => 1,
                    'ticket_num'        => $num,
                    'cancel_money'      => $saleMoney,
                    'cost_money'        => $costMoney,
                    'service_money_in'  => $serviceMoneyIn,
                    'service_money_out' => $serviceMoneyOut,
                    'orders_info'       => json_encode([$orderNum, $num, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount]),
                    'update_time'       => $time,
                    'operate_id'        => $operateMember,
                    'pay_way'           => $payMode,
                ];

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);
                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'             => $orderNum,
                        'sellerid'            => $sellerId,
                        'buyerid'             => $this->_getChannelSanke($channel),
                        'level'               => -4, //非0/-1
                        'cost_money'          => $saleMoney, //购买的钱
                        'sale_money'          => $needMoney, //默认使用零售价的钱
                        'settlement_discount' => $settlementDiscountMoney + $discountMoney, //分销优惠金额
                    ];

                    $tmp[] = $splitData;
                }
            }

            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                if ($action == 1) {
                    $res = $this->_statisticsModel->insertDataArr($insert, 'cancel');
                } else {
                    $res = $this->_statisticsModel->insertDataArr($insert, 'revoke');
                }

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }


    /**
     * 预定+取消 实时数据
     *
     * @param  $orderNum int  订单号
     * @param  $num      int  票数
     * @param  $action   int  1 预定 2 取消修改
     * @param  $operMember   int  操作员，如果没有的话，就使用分销商ID
     * @param  int  $flag  操作类型  1修改 2取消 4支付 7撤改 16加票
     * @param  string  $actionIdxs  操作序号，连续用 x-x表示，若出现不联系，多段逗号间隔  '1-100,101-200'
     */
    public function createOrderPayWayData($orderNum, $num, $time, $action, $operMember = 0, $flag = -1, $actionIdxs = '')
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            // $orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel = $orderInfo['ordermode'];
            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];
            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
            //$tmp   = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //撤销撤改获取退票手续费信息
            $cancelInfo = [];
            if ($action == 2) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取本次操作积分抵扣金额
            $discountDetail           = $this->_getOrderPointsDetail($orderNum, $extContentInfo, $flag, $actionIdxs);
            $discountAmount           = $discountDetail['discountAmount'];              //游客优惠
            $settlementDiscountAmount = $discountDetail['settlementDiscountAmount'];    //分销优惠

            //TODO:: 后期多级分销优惠的可能会有问题
            //组装优惠信息
            $discountInfo[$orderNum][$flag][$orderInfo['aid']][$orderInfo['member']] = [
                \Business\Statistics\CreateReportBase::DISCOUNT            => $discountAmount,
                \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => $settlementDiscountAmount,
            ];

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level              = $item['level'];
                $saleMoney          = $item['sale_money'];
                $paymode            = $item['pmode'];
                $settlementDiscount = $item['settlement_discount'] ?? 0; //分销优惠金额

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //计算游客优惠使用情况
                $discountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT])) {
                    $discountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT];
                }

                //计算分销优惠使用情况
                $settlementDiscountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT])) {
                    $settlementDiscountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT];
                }

                //去除退票手续费
                $serviceMoney = 0;
                if ($action == 2) {
                    if (!empty($cancelInfo)) {
                        foreach ($cancelInfo as $k => $v) {
                            if ($v['fid'] == $sellerId && $v['aid'] == $fid) {
                                $serviceMoney = $v['money'];
                            }
                        }
                    }
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                //验证订单书
                $checkOrderNum = 0;
                //取消订单数
                $cancelOrderNum = 0;
                //验证票数
                $checkTicketNum = 0;
                //取消票数
                $cancelTicketNum = 0;
                //验证金额
                $checkMoney = 0;
                //撤销金额
                $cancelMoney = 0;
                //验证信息
                $checkOrderInfo = [];
                //取消信息
                $cancelOrderInfo = [];

                //成本价
                $totalCostMoney = 0;

                if ($action == 1) {
                    //预定支付
                    $checkOrderNum    = 1;
                    $checkTicketNum   = $num;
                    $checkMoney       = $checkTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $checkOrderInfo[] = [$orderNum, $checkTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];

                    //如果是顶级供应商
                    if (in_array($level, [0, 1])) {
                        $totalCostMoney = $checkTicketNum * $costMoney;
                    }
                } else {
                    //撤销撤改
                    $cancelOrderNum    = 1;
                    $cancelTicketNum   = $num;
                    $cancelMoney       = $cancelTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $cancelOrderInfo[] = [$orderNum, $cancelTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];

                    //如果是顶级供应商
                    if (in_array($level, [0, 1])) {
                        $totalCostMoney = (-$cancelTicketNum * $costMoney );
                    }
                }

                $insert[] = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    'date'               => date('Ymd', $time),
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'channel'            => $channel,
                    'order_num'          => $checkOrderNum,
                    'ticket_num'         => $checkTicketNum,
                    'cancel_order_num'   => $cancelOrderNum,
                    'cancel_ticket_num'  => $cancelTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $checkMoney,
                    'cancel_money'       => $cancelMoney,
                    'service_money'      => $serviceMoney,
                    'cost_money'         => $totalCostMoney - $settlementDiscount,
                    'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'cancel_orders_info' => json_encode($cancelOrderInfo),
                    'update_time'        => $time,
                    'oper_member'        => $operMember,
                ];

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'             => $orderNum,
                        'sellerid'            => $sellerId,
                        'buyerid'             => $this->_getChannelSanke($channel),
                        'level'               => -3, //非0/-1
                        'sale_money'          => $needMoney, //默认使用零售价的钱
                        'pmode'               => $paymode,
                        'settlement_discount' => $level == 0 ? $settlementDiscountMoney : 0,  //分销优惠金额
                    ];

                    $tmp[] = $splitData;
                }
            }

            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'order_payway');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }
        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }


    /**
     * 第二版预订报表 实时数据
     */
    public function createOrderTwoData($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0, $flag = 0, $actionIdxs = '', $memo = '')
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_reportRealTaskModel) {
                $this->_reportRealTaskModel = new ReportRealTaskModel();
            }

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status, playtime, ss,salerid';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            //$orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            $orderDateHour = date('Ymd', $time) . date('H', $time);

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $pid       = $orderInfo['pid'];
            $tid       = $orderInfo['tid'];
            $playtime  = $orderInfo['playtime'];
            $payStatus = $orderInfo['pay_status'];
            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            //子商户id
            $subMerchantId = $extContentInfo['subSid'] ?? 0;

            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            //检查下是不是支付前的操作，加票16、修改1如果是支付前是不计入报表
            $inReport = true;
            if (in_array($flag, [1, 16])) {
                //获取订单支付时间戳，用于判断支付前或者支付后的操作， 追踪记录表的时间戳可能与实时报表的时间戳有延迟导致的差异，所以还是需要去取下real_task里面对应的时间戳
                $payTimeListRes = $this->_getTrackOrderListByOrderNumsAndAction([$orderNum]);
                $payTimeListMap = array_column($payTimeListRes, 'insertTime', 'ordernum');
                $trackPayTime   = $payTimeListMap[$orderNum] ?? 0;
                //为了防止时间格式问题导致查询不到支付时间戳，异常直接放行
                $trackPayTimeCheck = false;
                date('Y-m-d H:i:s', strtotime($trackPayTime)) == $trackPayTime && $trackPayTimeCheck = true;
                if ($trackPayTimeCheck) {
                    $trackPayTime = strtotime($trackPayTime);
                    //根据支付时间查询报表中间表task支付插入时间，来对比，避免程序延迟导致对比时间误差
                    $taskPayRes = $this->_reportRealTaskModel->getTasFlagInfo($orderNum, ($trackPayTime - 10),
                        ($trackPayTime + 10), 4);
                    //获取写入时间
                    $taskPayTimeListMap = array_column($taskPayRes, 'insert_time', 'ordernum');
                    $payTime            = $taskPayTimeListMap[$orderNum] ?? 0;
                    if ($payTime && $time && $time < $payTime) {
                        $inReport = false;
                        pft_log('real_time/not_in_report', "订单：$orderNum, 类型：$flag (加票16、修改1), 在支付前，不计入报表");
                    }
                }
            }
            if (!$inReport) {
                //无需计入报表
                return ['code' => $code, 'msg' => $msg];
            }

            $applyDid  = 0;
            $mainTid   = 0;
            $mainOrder = '';
            $mainType  = '';

            $packMainOrderInfo  = [];
            $packMainOrderChain = [];

            if ($channel == 23) {
                //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
                if (!empty($packRes)) {
                    if ($packRes['pack_order'] == 1) {
                        //主票
                        $mainTid = $tid;
                    } else {
                        //子票
                        $mainOrder         = $packRes['pack_order'];
                        $mainOrderInfo     = $this->_queryModel->getOrderInfo($mainOrder, 'lid,pid,tid,ordermode');
                        $mainTid           = $mainOrderInfo['tid'] ?? 0;
                        $packMainOrderInfo = $mainOrderInfo;
                        $packMainOrderChain = $this->_getOrderChain($mainOrder);
                        if (empty($packMainOrderChain)) {
                            throw new Exception("没找到分销链信息");
                        }
                    }
                    //主订单号的票类信息
                    $javaApi    = new \Business\CommodityCenter\Ticket();
                    $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                    $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                    $mainType   = $ticketInfo['land']['p_type'] ?? '';
                }
            }

            //存在子商户id，并且非顶级供应商，不展示
            $orderTicketApplyDid = 0;
            if ($subMerchantId) {
                //获取下订单票的顶级供应商
                $orderTicketInfo     = $this->_ticketModel->getTicketInfoById($tid, 'apply_did');
                $orderTicketApplyDid = $orderTicketInfo['apply_did'] ?? 0;
            }

            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
            // $tmp   = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //取消修改/撤销撤改获取退票手续费信息
            $cancelInfo = [];
            if (in_array($action, [2, 3])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取本次操作积分抵扣金额
            $discountDetail           = $this->_getOrderPointsDetail($orderNum, $extContentInfo, $flag, $actionIdxs);
            $discountAmount           = $discountDetail['discountAmount'];              //游客优惠
            $settlementDiscountAmount = $discountDetail['settlementDiscountAmount'];    //分销优惠

            //组装优惠信息
            $discountInfo[$orderNum][$flag][$orderInfo['aid']][$orderInfo['member']] = [
                \Business\Statistics\CreateReportBase::DISCOUNT            => $discountAmount,
                \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => $settlementDiscountAmount,
            ];

            //日汇总的报表数据
            $insertDay  = [];
            //小时汇总的报表数据
            $insertHour = [];
            //日汇总预约报表
            $insertReserve = [];

            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level              = $item['level'];
                $saleMoney          = $item['sale_money'];
                $paymode            = $item['pmode'];
                $settlementDiscount = $item['settlement_discount'] ?? 0; //分销优惠金额

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //计算游客优惠使用情况
                $discountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT])) {
                    $discountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT];
                }

                //计算分销优惠使用情况
                $settlementDiscountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT])) {
                    $settlementDiscountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT];
                }

                //去除退票手续费
                $serviceMoney = 0;
                if (in_array($action, [2, 3]) && !empty($cancelInfo)) {
                    $serviceMoney = $this-> _countServiceMoney($fid, $cancelInfo);
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;

                        //预售券兑换的订单，末级销售金额为0
                        if ($this->_checkSaleMoneyZero($channel, $paymode) && !$this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid)) {
                            $saleMoney = 0;
                        }
                    }
                }

                //预订订单数
                $bookOrderNum = 0;
                //预订票数
                $bookTicketNum = 0;
                //预订 票 销售金额
                $bookSaleMoney = 0;
                //预订 票 购买金额
                $bookCostMoney = 0;
                //取消订单数
                $cancelOrderNum = 0;
                //取消票数
                $cancelTicketNum = 0;
                //取消 票 出售金额
                $cancelSaleMoney = 0;
                //取消 票 购买金额
                $cancelCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;
                //取票数
                $printNum        = 0;
                //售后数量
                $afterSaleTicketNum = 0;
                //售后退回金额
                $afterSaleRefundNum = 0;
                //售后收入金额
                $afterSaleIncomeNum = 0;

                //验证信息
                $checkOrderInfo = [];
                //取消信息
                $cancelOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];
                //取票订单信息
                $printOrderInfo = [];
                //售后信息
                $afterSaleOrderInfo = [];

                if ($action == 1) {
                    //预定支付
                    $bookOrderNum     = 1;
                    $bookTicketNum    = $num;
                    $bookSaleMoney    = $bookTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $bookCostMoney    = $bookTicketNum * $costMoney - $settlementDiscount;
                    $checkOrderInfo[] = [$orderNum, $bookTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];
                } elseif ($action == 2) {
                    //取消修改
                    $cancelOrderNum    = 1;
                    $cancelTicketNum   = $num;
                    $cancelSaleMoney   = $cancelTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $cancelCostMoney   = $cancelTicketNum * $costMoney - $settlementDiscount;
                    $cancelOrderInfo[] = [$orderNum, $cancelTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];
                } elseif ($action == 4) {
                    //取票
                    $printNum          = $num;
                    $printOrderInfo[] = [$orderNum, $printNum];
                } elseif ($action == 38) {
                    //售后
                    $afterSaleCode = $memo['afterSaleCode'] ?? [];
                    $afterSaleChainInfo = $this->_getAfterSaleChain($afterSaleCode);
                    $afterSaleTicketNum = $memo['afterSaleNum'];
                    foreach ($afterSaleChainInfo as $value) {
                        if ($value['buyerId'] == $fid) {
                            $afterSaleIncomeNum = $value['refundAmount'] ?? 0;
                        }
                        if ($value['sellerId'] == $fid) {
                            $afterSaleRefundNum = $value['refundAmount'] ?? 0;
                        }
                    }
                    $afterSaleOrderInfo[] = [
                        $orderNum,
                        $afterSaleTicketNum,
                        $afterSaleCode,
                        $afterSaleRefundNum,
                        $afterSaleIncomeNum,
                    ];
                } else {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney - $settlementDiscount;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'             => $orderNum,
                        'sellerid'            => $sellerId,
                        'buyerid'             => $this->_getChannelSanke($channel),
                        'level'               => -3, //非0/-1
                        'cost_money'          => $saleMoney,
                        'sale_money'          => $needMoney, //默认使用零售价的钱
                        'pmode'               => $paymode,
                        'settlement_discount' => $settlementDiscountMoney + $discountMoney,  //分销优惠金额+支持下卖出优惠金额
                    ];

                    //如果需要补全一条报表记录，且是预售券兑换订单，末级销售金额为0
                    if ($this->_checkSaleMoneyZero($channel, $paymode)) {
                        $splitData['sale_money'] = 0;
                    }

                    $tmp[] = $splitData;
                }

                //黑名单判断必须放在链路补全后，避免数据缺失
                if ($this->isInBlacklist($fid)) {
                    continue;
                }

                //fid是套票发布人 才会记录子订单主票id
                $mainTidNew  = $mainTid;
                $mainTypeNew = $mainType;
                if ($applyDid != $fid) {
                    $mainTidNew  = 0;
                    $mainTypeNew = '';
                }

                //补全主票预订信息
                $mainOrderInsert = [];
                if ($applyDid == $fid && $this->_filterPackMainOrderNotToday($mainOrder)) {
                    //补全主票订单号
                    list($mSellerId, $mPid, $mChannel, $mPaymode, $mOperMember, $mSiteId, $mLid, $mTid, $mLevel, $mSubMerchantId) = $this->_packMainOrderDecode($fid,
                        $mainOrder, $packMainOrderInfo, $packMainOrderChain);
                    if ($mLid && $mPid && $mTid  && !is_null($mSellerId) && !is_null($mLevel) && !is_null($mPaymode)) {
                        $mainOrderInsert = [
                            'fid'                     => $fid,
                            'reseller_id'             => $mSellerId,
                            'date'                    => date('Ymd', $time),
                            'lid'                     => $mLid,
                            'pid'                     => $mPid,
                            'tid'                     => $mTid,
                            'channel'                 => $mChannel,
                            'order_num'               => 0,
                            'order_ticket'            => 0,
                            'cancel_num'              => 0,
                            'cancel_ticket'           => 0,
                            'revoke_num'              => 0,
                            'print_num'               => 0,
                            'revoke_ticket'           => 0,
                            'pay_way'                 => $mPaymode,
                            'sale_money'              => 0,
                            'cost_money'              => 0,
                            'cancel_cost_money'       => 0,
                            'cancel_sale_money'       => 0,
                            'revoke_cost_money'       => 0,
                            'revoke_sale_money'       => 0,
                            'service_money'           => 0,
                            'level'                   => $mLevel,
                            'orders_info'             => json_encode([[$mainOrder, 0]]),
                            'cancel_orders_info'      => '[]',
                            'revoke_orders_info'      => '[]',
                            'print_orders_info'       => '[]',
                            'update_time'             => $time,
                            'operate_id'              => $mOperMember,
                            'site_id'                 => $mSiteId,
                            'main_tid'                => 0,
                            'sub_merchant_id'         => $mSubMerchantId,
                            'main_type'               => '',
                            'after_sale_ticket_num'   => 0,
                            'after_sale_refund_money' => 0,
                            'after_sale_income_money' => 0,
                            'after_sale_info'         => '[]',
                        ];
                    }
                }


                //补全主票小时报表信息
                $mainOrderHourInsert = [];
                if ($applyDid == $fid && $this->_filterPackMainOrderNotToday($mainOrder, 4, true)) {
                    //补全主票订单号
                    list($mSellerId, $mPid, $mChannel, $mPaymode, $mOperMember, $mSiteId, $mLid, $mTid, $mLevel, $mSubMerchantId) = $this->_packMainOrderDecode($fid,
                        $mainOrder, $packMainOrderInfo, $packMainOrderChain);

                    if ($mLid && $mPid && $mTid  && !is_null($mSellerId) && !is_null($mLevel) && !is_null($mPaymode)) {
                        $mainOrderHourInsert = [
                            'fid'                     => $fid,
                            'reseller_id'             => $mSellerId,
                            'date_hour'               => $orderDateHour,
                            'lid'                     => $mLid,
                            'pid'                     => $mPid,
                            'tid'                     => $mTid,
                            'channel'                 => $mChannel,
                            'order_num'               => 0,
                            'order_ticket'            => 0,
                            'cancel_num'              => 0,
                            'cancel_ticket'           => 0,
                            'revoke_num'              => 0,
                            'print_num'               => 0,
                            'revoke_ticket'           => 0,
                            'pay_way'                 => $mPaymode,
                            'sale_money'              => 0,
                            'cost_money'              => 0,
                            'cancel_cost_money'       => 0,
                            'cancel_sale_money'       => 0,
                            'revoke_cost_money'       => 0,
                            'revoke_sale_money'       => 0,
                            'service_money'           => 0,
                            'lvl'                     => $mLevel,
                            'orders_info'             => json_encode([[$mainOrder, 0]]),
                            'cancel_orders_info'      => '[]',
                            'revoke_orders_info'      => '[]',
                            'print_orders_info'       => '[]',
                            'update_time'             => $time,
                            'operate_id'              => $mOperMember,
                            'site_id'                 => $mSiteId,
                            'main_tid'                => 0,
                            'sub_merchant_id'         => $mSubMerchantId,
                            'main_type'               => '',
                            'after_sale_ticket_num'   => 0,
                            'after_sale_refund_money' => 0,
                            'after_sale_income_money' => 0,
                            'after_sale_info'         => '[]',
                        ];
                    }
                }

                if ($subMerchantId && $orderTicketApplyDid != $fid) {
                    //非顶级供应商，子商户不可见
                    $subMerchantId = 0;
                }

                $baseInsert = [
                    'fid'                     => $fid,
                    'reseller_id'             => $sellerId,
                    // 'date'               => date('Ymd', $time),
                    'lid'                     => $lid,
                    'pid'                     => $pid,
                    'tid'                     => $tid,
                    'channel'                 => $channel,
                    'order_num'               => $bookOrderNum,
                    'order_ticket'            => $bookTicketNum,
                    'cancel_num'              => $cancelOrderNum,
                    'cancel_ticket'           => $cancelTicketNum,
                    'revoke_num'              => $revokeOrderNum,
                    'print_num'               => $printNum,
                    'revoke_ticket'           => $revokeTicketNum,
                    'pay_way'                 => $paymode,
                    'sale_money'              => $bookSaleMoney,
                    'cost_money'              => $bookCostMoney,
                    'cancel_cost_money'       => $cancelCostMoney,
                    'cancel_sale_money'       => $cancelSaleMoney,
                    'revoke_cost_money'       => $revokeCostMoney,
                    'revoke_sale_money'       => $revokeSaleMoney,
                    'service_money'           => $serviceMoney,
                    // 'level'              => $level,
                    'orders_info'             => json_encode($checkOrderInfo),
                    'cancel_orders_info'      => json_encode($cancelOrderInfo),
                    'revoke_orders_info'      => json_encode($revokeOrderInfo),
                    'print_orders_info'       => json_encode($printOrderInfo),
                    'update_time'             => $time,
                    'operate_id'              => $operMember,
                    'site_id'                 => $siteId,
                    'main_tid'                => $mainTidNew,
                    'sub_merchant_id'         => $subMerchantId,
                    'main_type'               => $mainTypeNew,
                    'after_sale_ticket_num'   => $afterSaleTicketNum,
                    'after_sale_refund_money' => $afterSaleRefundNum,
                    'after_sale_income_money' => $afterSaleIncomeNum,
                    'after_sale_info'         => json_encode($afterSaleOrderInfo, JSON_UNESCAPED_UNICODE),
                ];

                $insertDay[]  = array_merge($baseInsert, ['date' => date('Ymd', $time), 'level' => $level]);
                $insertHour[] = array_merge($baseInsert, ['date_hour' => $orderDateHour, 'lvl' => $level]);

                if (in_array($action, $this->_reserveAction)) {
                    $playtime = str_replace('-', '', $playtime);
                    if (!empty($playtime)) {
                        unset($baseInsert['print_num']);
                        unset($baseInsert['print_orders_info']);
                        unset($baseInsert['main_tid']);
                        unset($baseInsert['main_type']);
                        unset($baseInsert['sub_merchant_id']);
                        unset($baseInsert['after_sale_ticket_num']);
                        unset($baseInsert['after_sale_refund_money']);
                        unset($baseInsert['after_sale_income_money']);
                        unset($baseInsert['after_sale_info']);
                        $reserve = [
                            'play_date'  => $playtime,
                            'order_date' => date('Ymd', $time),
                            'lvl'        => $level,
                        ];
                        $insertReserve[] = array_merge($baseInsert, $reserve);
                    }
                }
            }

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            if (!empty($insertDay)) {
                $res = $this->_statisticsModel->insertDataArr($insertDay, 'order_v2');
                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }

            if (!empty($insertHour)) {
                $resHour = $this->_statisticsModel->insertDataArr($insertHour, 'order_v2_hour');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$insertHour, $errMsg]));
                    //throw new \Exception($errMsg);
                }
            }

            if (!empty($insertReserve)) {
                $resHour = $this->_statisticsModel->insertDataArr($insertReserve, 'reserve_order');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$insertReserve, $errMsg]));
                }
            }

            //主票数据补全
            if (!empty($mainOrderInsert)) {
                $res = $this->_statisticsModel->insertDataArr([$mainOrderInsert], 'order_v2');
                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$mainOrderInsert, $errMsg]));
                }
            }

            //小时报表主票数据补全
            if (!empty($mainOrderHourInsert)) {
                $resHour = $this->_statisticsModel->insertDataArr([$mainOrderHourInsert], 'order_v2_hour');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$mainOrderHourInsert, $errMsg]));
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }


    /**
     * 定制型的报表
     */
    public function createCustomizeData($orderNum, $num, $time, $siteId = 0, $action = 4, $operator = 0)
    {
        $code = true;
        $msg  = '';

        try {

            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status, playtime';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $pid       = $orderInfo['pid'];
            $tid       = $orderInfo['tid'];
            $payStatus = $orderInfo['pay_status'];

            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            //子商户id
            $subMerchantId = $extContentInfo['subSid'] ?? 0;

            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            $field = 'id, ordernum, tnum, left_num, action, branchTerminal, oper_member, source, SalerID, ext_content';
            $filter['ordernum'] = ['IN', [$orderNum]];
            $trackList = $this->_getOrderTrackModel()->getList($filter, $field);
            if (empty($trackList)) {
                throw new Exception("获取订单追踪数据失败");
            }

            $inReport = false;
            $operMember = 0;
            $cancelTrackId = 0;
            //支付时间
            $payTime    = 0;
            //取消时间
            $cancelTime = 0;

            if ($action != 4) {
                $operMember = $operator;
            }

            foreach ($trackList as $tmpTrack) {
                //6, 7  撤销撤改
                if (!in_array($tmpTrack['action'], [1, 2, 4, 6, 7])) {
                    continue;
                }

                if ($action == 4) {
                    $payTime    = strtotime($tmpTrack['insertTime']);
                    $operMember = $tmpTrack['oper_member'];
                    $tmpExtContent = json_decode($tmpTrack['ext_content'], true);
                }

                if ($tmpTrack['action'] != $action) {
                    continue;
                }

                //todo : 多次操作取消取不到正确的退票手续费  应该取设置的每张票的手续费
                if (in_array($tmpTrack['action'], [1, 2])) {
                    $cancelTrackId = $tmpTrack['id'];
                    $cancelTime = strtotime($tmpTrack['insertTime']);
                }

                //是否是云票务在线支付的  source = 4 是云票务现金支付的 pay_source = 4 是云票务在线支付的（微信支付宝）
                if ($tmpTrack['source'] == 4 || (!empty($tmpExtContent['pay_source']) && $tmpExtContent['pay_source'] == 4)) {
                    $inReport = true;
                }
            }

            //取消操作时间在支付时间前
            if ($inReport && $cancelTime && $payTime) {
                ($cancelTime < $payTime) && $inReport = false;
            }

            if (!$inReport) {
                //无需计入报表
                return ['code' => $code, 'msg' => $msg];
            }

            $applyDid = 0;
            $mainTid  = 0;
            $mainType = '';

            if ($channel == 23) {
                //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
                if (!empty($packRes)) {
                    if ($packRes['pack_order'] == 1) {
                        //主票
                        $mainTid = $tid;
                    } else {
                        //子票
                        $mainOrder     = $packRes['pack_order'];
                        $mainOrderInfo = $this->_queryModel->getOrderInfo($mainOrder, 'lid,pid,tid,ordermode');
                        $mainTid       = $mainOrderInfo['tid'] ?? 0;
                    }
                    //主订单号的票类信息
                    $javaApi    = new \Business\CommodityCenter\Ticket();
                    $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                    $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                    $mainType   = $ticketInfo['land']['p_type'] ?? '';
                }
            }

            //存在子商户id，并且非顶级供应商，不展示
            $orderTicketApplyDid = 0;
            if ($subMerchantId) {
                //获取下订单票的顶级供应商
                $orderTicketInfo     = $this->_ticketModel->getTicketInfoById($tid, 'apply_did');
                $orderTicketApplyDid = $orderTicketInfo['apply_did'] ?? 0;
            }

            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            $money = 0;
            //先处理一次
            foreach ($tmp as $tmpSplit) {
                if (in_array($tmpSplit['level'], [-1, 0]) || $tmpSplit['buyerid'] == 112) {
                    $money = $tmpSplit['sale_money'];
                }
            }

            $windowCancelMoney = 0;
            $windowRealMoney   = 0;

            //开通需要汇总云票务窗口实收金额的用户
            $openWindowRealMoneyUser = ModuleConfig::getSidByAppId(ModuleConfig::STATIS_REPORT_WINDOW_REAL_MONEY);

            $cancelInfo   = [];
            $serviceMoney = 0;

            for ($i = 0; $i < count($tmp); $i++) {

                $windowCancelMoneyOrder = [];
                $checkOrderInfo = [];

                $item           = $tmp[$i];
                $fid            = $item['sellerid'];
                $sellerId       = $item['buyerid'];
                $originSellerId = $sellerId;

                if (!in_array($fid, $openWindowRealMoneyUser)) {
                    continue;
                }

                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level    = $item['level'];
                $paymode  = $item['pmode'];
                $saleMoney  = $item['sale_money'];

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);
                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        $level    = $level == 0 ? 1 : -3;
                        $sellerId = $tmpId;
                    }
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);
                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;
                    $splitData = [
                        'orderid'    => $orderNum,
                        'sellerid'   => $sellerId,
                        'buyerid'    => $this->_getChannelSanke($channel),
                        'level'      => -3, //非0/-1
                        'cost_money' => $saleMoney,
                        'sale_money' => $needMoney, //默认使用零售价的钱
                        'pmode'      => $paymode,
                    ];

                    $tmp[] = $splitData;
                }

                //fid是套票发布人 才会记录子订单主票id
                $mainTidNew  = $mainTid;
                $mainTypeNew = $mainType;
                if ($applyDid != $fid) {
                    $mainTidNew  = 0;
                    $mainTypeNew = '';
                }

                if ($action == 4) {
                    //支付
                    $checkOrderInfo[] = [$orderNum, $num];
                    $windowRealMoney  = $num * $money;

                } elseif (in_array($action, [1, 2, 6, 7])) {
                    //取消
                    if (empty($cancelInfo)) {
                        $cancelInfo = (new OrderQuery())->getCancelServiceByOrderId([$orderNum], 'track_id, service_info');
                    }

                    if (!empty($cancelInfo)) {
                        foreach ($cancelInfo as $k => $v) {
                            if ($v['track_id'] == $cancelTrackId) {
                                $serviceInfo = json_decode($v['service_info'], true);
                                foreach ($serviceInfo as $tmpV) {
                                    if ($tmpV['fid'] == $originSellerId && $tmpV['aid'] == $fid) {
                                        $serviceMoney = $tmpV['money'];
                                    }
                                }
                            }
                        }
                    }

                    $windowCancelMoney = $money * $num;
                    $windowCancelMoneyOrder[] = [$orderNum, $num];

                } else {
                    continue;
                }

                if ($subMerchantId && $orderTicketApplyDid != $fid) {
                    //非顶级供应商，子商户不可见
                    $subMerchantId = 0;
                }

                $insertData[] = [
                    'fid'                         => $fid,
                    'reseller_id'                 => $sellerId,
                    'cal_date'                    => date('Ymd', $time),
                    'lid'                         => $lid,
                    'pid'                         => $pid,
                    'tid'                         => $tid,
                    'channel'                     => $channel,
                    'lvl'                         => $level,
                    'pay_way'                     => $paymode,
                    'window_real_money'           => $windowRealMoney,
                    'window_real_orders_info'     => json_encode($checkOrderInfo),
                    'window_cancel_money'         => $windowCancelMoney,
                    'window_cancel_orders_info'   => json_encode($windowCancelMoneyOrder),
                    'window_cancel_service_money' => $serviceMoney,
                    'update_time'                 => $time,
                    'operate_id'                  => $operMember,
                    'site_id'                     => $siteId,
                    'main_tid'                    => $mainTidNew,
                    'sub_merchant_id'             => $subMerchantId,
                    'main_type'                   => $mainTypeNew,
                ];
            }

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            if (!empty($insertData)) {
                $insertRes = $this->_statisticsModel->insertDataArr($insertData, 'customize_report');
                if (!$insertRes) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$insertData, $errMsg]));
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }



    /**
     * 第二版验证报表 实时数据
     */
    public function createCheckTwoData($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0, $flag = -1, $actionIdxs = '', $realTaskId = 0, $memo ='')
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_subOrderAddonModel) {
                $this->_subOrderAddonModel = new SubOrderAddon();
            }

            if (!$this->_subOrderMainModel) {
                $this->_subOrderMainModel = new SubOrderMain();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, status, tnum';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            // $orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            //判断下单时间是不是今日
            $isTodayCheck = 2;
            if (date('Ymd', strtotime($orderInfo['ordertime'])) == date('Ymd')) {
                $isTodayCheck = 1;
            }

            $channel = $orderInfo['ordermode'];
            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];
            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            //子商户id
            $subMerchantId = $extContentInfo['subSid'] ?? 0;

            //有使用积分且操作没有下标的 直接抛异常
            if ((\Business\Order\BaseCheck::checkUseDiscount($orderNum, [$orderNum => $extContentInfo]) || \Business\Order\BaseCheck::checkUseSettlementDiscount($orderNum, [$orderNum => $extContentInfo])) && !$actionIdxs && $flag == 5) {
                //发起数据同步的Job
                $orderPointJobData = [
                    'job_type' => 'check',
                    'job_data' => ['orderNum' => $orderNum, 'real_task_flag' => $flag, 'real_task_id' => $realTaskId],
                ];
                Queue::push('order', 'OrderPointSync_Job', $orderPointJobData);
                throw new Exception("该订单积分信息未同步");
            }

            $applyDid  = 0;
            $mainTid   = 0;
            $mainOrder = '';
            $mainType  = '';

            $packMainOrderInfo  = [];
            $packMainOrderChain = [];

            if ($channel == 23) {
                //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                $packRes = $this->_subOrderAddonModel->getPackInfoByOrderSingle($orderNum, 'pack_order');
                if (!empty($packRes)) {
                    if ($packRes['pack_order'] == 1) {
                        //主票
                        $mainTid = $tid;
                    } else {
                        //子票
                        $mainOrder     = $packRes['pack_order'];
                        $mainOrderInfo = $this->_subOrderMainModel->getOrderListByOrderSingle($mainOrder, 'lid,pid,tid,ordermode');
                        //$queryParams = [$mainOrder];
                        //$queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernum', $queryParams);
                        //if ($queryRes['code'] != 200) {
                        //    $log = [
                        //        'parame'   => $mainOrder,
                        //        'apiData'  => $queryRes,
                        //        'function' => 'queryOrderInfoByOrdernum',
                        //    ];
                        //    pft_log('createCheckTwoData/Api/error', json_encode($log));
                        //}
                        //$mainOrderInfo = $queryRes['data'];
                        $mainTid            = $mainOrderInfo['tid'] ?? 0;
                        $packMainOrderInfo  = $mainOrderInfo;
                        $packMainOrderChain = $this->_getOrderChain($mainOrder);
                        if (empty($packMainOrderChain)) {
                            throw new Exception("没找到分销链信息");
                        }
                    }
                    //主订单号的票类信息
                    $javaApi    = new \Business\CommodityCenter\Ticket();
                    $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                    $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                    $mainType   = $ticketInfo['land']['p_type'] ?? '';

                }
            }

            //存在子商户id，并且非顶级供应商，不展示
            $orderTicketApplyDid = 0;
            if ($subMerchantId) {
                //获取下订单票的顶级供应商
                $orderTicketInfo     = $this->_ticketModel->getTicketInfoById($tid, 'apply_did');
                $orderTicketApplyDid = $orderTicketInfo['apply_did'] ?? 0;
            }

            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
            //$tmp   = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取本次操作积分抵扣金额
            $discountDetail           = $this->_getOrderPointsDetail($orderNum, $extContentInfo, $flag, $actionIdxs);
            $discountAmount           = $discountDetail['discountAmount'];              //游客优惠
            $settlementDiscountAmount = $discountDetail['settlementDiscountAmount'];    //分销优惠

            //组装优惠信息
            $discountInfo[$orderNum][$flag][$orderInfo['aid']][$orderInfo['member']] = [
                \Business\Statistics\CreateReportBase::DISCOUNT            => $discountAmount,
                \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => $settlementDiscountAmount,
            ];

            $orderDateHour = date('Ymd', $time) . date('H', $time);
            //日汇总的报表数据
            $insertDay  = [];
            //小时汇总的报表数据
            $insertHour = [];

            if (in_array($action, [3])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level              = $item['level'];
                $saleMoney          = $item['sale_money'];
                $paymode            = $item['pmode'];
                $settlementDiscount = $item['settlement_discount'] ?? 0; //分销优惠金额

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //计算游客优惠使用情况
                $discountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT])) {
                    $discountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT];
                }

                //计算分销优惠使用情况
                $settlementDiscountMoney = 0;
                if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT])) {
                    $settlementDiscountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT];
                }

                //取消修改/撤销撤改获取退票手续费信息
                $serviceMoney = 0;
                if (in_array($action, [3]) && !empty($cancelInfo)) {
                    $serviceMoney = $this-> _countServiceMoney($fid, $cancelInfo);
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;

                        //预售券兑换的订单，末级销售金额为0
                        if ($this->_checkSaleMoneyZero($channel, $paymode) && !$this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid)) {
                            $saleMoney = 0;
                        }
                    }
                }

                //验证订单数
                $checkOrderNum = 0;
                //验证票数
                $checkTicketNum = 0;
                //验证 票 销售金额
                $checkSaleMoney = 0;
                //验证 票 购买金额
                $checkCostMoney = 0;
                //完结订单数
                $finishOrderNum = 0;
                //完结票数
                $finishTicketNum = 0;
                //完结 票 出售金额
                $finishSaleMoney = 0;
                //完结 票 购买金额
                $finishCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;
                //售后数量
                $afterSaleTicketNum = 0;
                //售后退回金额
                $afterSaleRefundNum = 0;
                //售后收入金额
                $afterSaleIncomeNum = 0;
                //售后编号
                $afterSaleNum = '';


                //验证信息
                $checkOrderInfo = [];
                //完结信息
                $finishOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];
                //售后信息
                $afterSaleOrderInfo = [];

                if ($action == 1) {
                    //验证
                    $checkOrderNum = 1;
                    //$checkTicketNum    = $num;
                    $checkTicketNum   = $num;
                    $checkSaleMoney   = $checkTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $checkCostMoney   = $checkTicketNum * $costMoney - $settlementDiscount;
                    $checkOrderInfo[] = [$orderNum, $checkTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];
                } elseif ($action == 2) {
                    //完结修改
                    $finishOrderNum  = 1;
                    $finishTicketNum = $num;

                    if ($num == 0) {
                        $finishTicketNum = $this->branchTerminalFinshNum($orderNum);
                    }
                    $finishSaleMoney   = $finishTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $finishCostMoney   = $finishTicketNum * $costMoney - $settlementDiscount;
                    $finishOrderInfo[] = [$orderNum, $finishTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];
                } elseif ($action == 3) {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney - $settlementDiscount;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum, $discountMoney, $settlementDiscountMoney ?: $settlementDiscount];
                }
                elseif ($action == 38) {
                    //售后
                    $afterSaleCode = $memo['afterSaleCode'] ?? [];
                    $afterSaleChainInfo = $this->_getAfterSaleChain($afterSaleCode);
                    $afterSaleTicketNum = $memo['afterSaleNum'];
                    $fid = $item['sellerid'];
                    foreach ($afterSaleChainInfo as $value){
                        if($value['buyerId'] == $fid){
                            $afterSaleIncomeNum = $value['refundAmount'] ?? 0;
                        }
                        if($value['sellerId'] == $fid){
                            $afterSaleRefundNum = $value['refundAmount'] ?? 0;
                        }
                    }
                    $afterSaleNum = $afterSaleCode;
                    $afterSaleOrderInfo[] = [$orderNum, $afterSaleTicketNum, $afterSaleNum, $afterSaleRefundNum, $afterSaleIncomeNum];
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'             => $orderNum,
                        'sellerid'            => $sellerId,
                        'buyerid'             => $this->_getChannelSanke($channel),
                        'level'               => -3, //非0/-1
                        'cost_money'          => $saleMoney,
                        'sale_money'          => $needMoney, //默认使用零售价的钱
                        'pmode'               => $paymode,
                        'settlement_discount' => $settlementDiscountMoney + $discountMoney,
                    ];

                    //如果需要补全一条报表记录，且是预售券兑换订单，末级销售金额为0
                    if ($this->_checkSaleMoneyZero($channel, $paymode)) {
                        $splitData['sale_money'] = 0;
                    }

                    $tmp[] = $splitData;
                }

                //黑名单判断必须放在链路补全后，避免数据缺失
                if ($this->isInBlacklist($fid)) {
                    continue;
                }

                //fid是套票发布人 才会记录子订单主票id
                $mainTidNew  = $mainTid;
                $mainTypeNew = $mainType;
                if ($applyDid != $fid) {
                    $mainTidNew  = 0;
                    $mainTypeNew = '';
                }

                //补全主票预订信息
                $mainOrderInsert = [];
                if ($applyDid == $fid && $this->_filterPackMainOrderNotToday($mainOrder, 5)) {
                    //补全主票订单号
                    list($mSellerId, $mPid, $mChannel, $mPaymode, $mOperMember, $mSiteId, $mLid, $mTid, $mLevel, $mSubMerchantId) = $this->_packMainOrderDecode($fid,
                        $mainOrder, $packMainOrderInfo, $packMainOrderChain);

                    if ($mLid && $mPid && $mTid  && !is_null($mSellerId) && !is_null($mLevel) && !is_null($mPaymode)) {
                        $mainOrderInsert = [
                            'fid'                => $fid,
                            'reseller_id'        => $mSellerId,
                            'date'               => date('Ymd', $time),
                            'lid'                => $mLid,
                            'pid'                => $mPid,
                            'tid'                => $mTid,
                            'channel'            => $mChannel,
                            'order_num'          => 0,
                            'order_ticket'       => 0,
                            'finish_num'         => 0,
                            'finish_ticket'      => 0,
                            'pay_way'            => $mPaymode,
                            'sale_money'         => 0,
                            'cost_money'         => 0,
                            'finish_cost_money'  => 0,
                            'finish_sale_money'  => 0,
                            'level'              => $mLevel,
                            'orders_info'        => json_encode([[$mainOrder, 0]]),
                            'finish_orders_info' => '[]',
                            'update_time'        => $time,
                            'operate_id'         => $mOperMember,
                            'today_check'        => 2,
                            'revoke_num'         => 0,
                            'revoke_ticket'      => 0,
                            'revoke_orders_info' => '[]',
                            'revoke_cost_money'  => 0,
                            'revoke_sale_money'  => 0,
                            'site_id'            => $mSiteId,
                            'service_money'      => 0,
                            'main_tid'           => 0,
                            'sub_merchant_id'    => $mSubMerchantId,
                            'after_sale_ticket_num' => $afterSaleTicketNum,
                            'after_sale_refund_money' => $afterSaleRefundNum,
                            'after_sale_income_money' => $afterSaleIncomeNum,
                            'after_sale_info' => json_encode($afterSaleOrderInfo,JSON_UNESCAPED_UNICODE),
                            'main_type'          => '',
                        ];
                    }
                }


                //补全主票小时报表信息
                $mainOrderHourInsert = [];
                if ($applyDid == $fid && $this->_filterPackMainOrderNotToday($mainOrder, 5, true)) {
                    //补全主票订单号
                    list($mSellerId, $mPid, $mChannel, $mPaymode, $mOperMember, $mSiteId, $mLid, $mTid, $mLevel, $mSubMerchantId) = $this->_packMainOrderDecode($fid,
                        $mainOrder, $packMainOrderInfo, $packMainOrderChain);

                    if ($mLid && $mPid && $mTid  && !is_null($mSellerId) && !is_null($mLevel) && !is_null($mPaymode)) {
                        $mainOrderHourInsert = [
                            'fid'                => $fid,
                            'reseller_id'        => $mSellerId,
                            'date_hour'          => $orderDateHour,
                            'lid'                => $mLid,
                            'pid'                => $mPid,
                            'tid'                => $mTid,
                            'channel'            => $mChannel,
                            'order_num'          => 0,
                            'order_ticket'       => 0,
                            'finish_num'         => 0,
                            'finish_ticket'      => 0,
                            'pay_way'            => $mPaymode,
                            'sale_money'         => 0,
                            'cost_money'         => 0,
                            'finish_cost_money'  => 0,
                            'finish_sale_money'  => 0,
                            'lvl'                => $mLevel,
                            'orders_info'        => json_encode([[$mainOrder, 0]]),
                            'finish_orders_info' => '[]',
                            'update_time'        => $time,
                            'operate_id'         => $mOperMember,
                            'today_check'        => 2,
                            'revoke_num'         => 0,
                            'revoke_ticket'      => 0,
                            'revoke_orders_info' => '[]',
                            'revoke_cost_money'  => 0,
                            'revoke_sale_money'  => 0,
                            'site_id'            => $mSiteId,
                            'service_money'      => 0,
                            'main_tid'           => 0,
                            'sub_merchant_id'    => $mSubMerchantId,
                            'after_sale_ticket_num' => $afterSaleTicketNum,
                            'after_sale_refund_money' => $afterSaleRefundNum,
                            'after_sale_income_money' => $afterSaleIncomeNum,
                            'after_sale_info' => json_encode($afterSaleOrderInfo,JSON_UNESCAPED_UNICODE),
                            'main_type'          => '',
                        ];
                    }
                }

                if ($subMerchantId && $orderTicketApplyDid != $fid) {
                    //非顶级供应商，子商户不可见
                    $subMerchantId = 0;
                }

                $baseInsert = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    // 'date'               => date('Ymd', $time),
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'channel'            => $channel,
                    'order_num'          => $checkOrderNum,
                    'order_ticket'       => $checkTicketNum,
                    'finish_num'         => $finishOrderNum,
                    'finish_ticket'      => $finishTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $checkSaleMoney,
                    'cost_money'         => $checkCostMoney,
                    'finish_cost_money'  => $finishCostMoney,
                    'finish_sale_money'  => $finishSaleMoney,
                    // 'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'finish_orders_info' => json_encode($finishOrderInfo),
                    'update_time'        => $time,
                    'operate_id'         => $operMember,
                    'today_check'        => $isTodayCheck,
                    'revoke_num'         => $revokeOrderNum,
                    'revoke_ticket'      => $revokeTicketNum,
                    'revoke_orders_info' => json_encode($revokeOrderInfo),
                    'revoke_cost_money'  => $revokeCostMoney,
                    'revoke_sale_money'  => $revokeSaleMoney,
                    'site_id'            => $siteId,
                    'service_money'      => $serviceMoney,
                    'main_tid'           => $mainTidNew,
                    'sub_merchant_id'    => $subMerchantId,
                    'after_sale_ticket_num' => $afterSaleTicketNum,
                    'after_sale_refund_money' => $afterSaleRefundNum,
                    'after_sale_income_money' => $afterSaleIncomeNum,
                    'after_sale_info' => json_encode($afterSaleOrderInfo,JSON_UNESCAPED_UNICODE),
                    'main_type'          => $mainTypeNew,
                ];

                $insertDay[]  = array_merge($baseInsert, ['date' => date('Ymd', $time), 'level'  => $level]);
                $insertHour[] = array_merge($baseInsert, ['date_hour' => $orderDateHour, 'lvl' => $level]);
            }

            if (!empty($insertDay)) {
                $res = $this->_statisticsModel->insertDataArr($insertDay, 'checked_v2');
                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode(['checked_v2-insertDay',$insertDay, $errMsg]));
                    throw new Exception($errMsg);
                }
            }

            if (!empty($insertHour)) {
                $resHour = $this->_statisticsModel->insertDataArr($insertHour, 'checked_v2_hour');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode(['checked_v2_hour-insertHour',$insertHour, $errMsg]));
                    //throw new \Exception($errMsg);
                }
            }

            //验证主票信息补全
            if (!empty($mainOrderInsert)) {
                $res = $this->_statisticsModel->insertDataArr([$mainOrderInsert], 'checked_v2');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode(['checked_v2-$mainOrderInsert',$mainOrderInsert, $errMsg]));
                }
            }

            //验证主票小时报表补全
            if (!empty($mainOrderHourInsert)) {
                $resHour = $this->_statisticsModel->insertDataArr([$mainOrderHourInsert], 'checked_v2_hour');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode(['checked_v2_hour-$mainOrderHourInsert',$mainOrderHourInsert, $errMsg]));
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
            pft_log('real_statis', json_encode([$orderNum, $msg]));
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 套票预订 实时数据
     */
    public function createPackOrder($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0)
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            $field = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status';

            $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
            if (empty($packRes)) {
                //不是套票
                throw new Exception("不是套票");
            }

            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            // $orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $pid       = $orderInfo['pid'];
            $tid       = $orderInfo['tid'];
            $payStatus = $orderInfo['pay_status'];
            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            //如果当前是子订单号 获取主订单号的票类ID 如果是主订单号 取获取当前订单号的票类ID
            if ($packRes['pack_order'] == 1) {
                //主票
                $isMain  = true;
                $mainTid = $tid;
            } else {
                //子票 获取主票订单号
                $mainOrderInfo = $this->_queryModel->getOrderInfo($packRes['pack_order'], 'tid');
                $isMain        = false;
                $mainTid       = $mainOrderInfo['tid'];
            }

            //主订单号的票类发布人
            $ticketInfo = $this->_ticketModel->getTicketInfoById($mainTid, 'apply_did');
            $applyDid   = isset($ticketInfo['apply_did']) ? $ticketInfo['apply_did'] : 0;

            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
            // $tmp   = $this->_buyChainModel->getListByOrderId($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //取消修改/撤销撤改获取退票手续费信息
            $cancelInfo = [];
            if (in_array($action, [2, 3])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level     = $item['level'];
                $saleMoney = $item['sale_money'];
                $paymode   = $item['pmode'];

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                //去除退票手续费
                $serviceMoney = 0;
                if (in_array($action, [2, 3]) && !empty($cancelInfo)) {
                    $serviceMoney = $this->_countServiceMoney($fid, $cancelInfo);
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'    => $orderNum,
                        'sellerid'   => $sellerId,
                        'buyerid'    => $this->_getChannelSanke($channel),
                        'level'      => -3, //非0/-1
                        'sale_money' => $needMoney, //默认使用零售价的钱
                        'cost_money' => $saleMoney,
                        'pmode'      => $paymode,
                    ];

                    $tmp[] = $splitData;
                }

                //由于系统一开始的问题 套票子票在整条分销链中全部记为套票子票下单 这是错误的
                //只有被将该票打包成套票的供应商出售这笔订单的销售渠道会记为套票子票下单
                //在这里做个判断 如果子票的主票的发布人 不是当前处理的分销链层级的卖家 则跳过
                if ($applyDid != $fid) {
                    continue;
                }

                //黑名单判断必须放在链路补全后，避免数据缺失
                if ($this->isInBlacklist($fid)) {
                    continue;
                }

                //预订订单数
                $bookOrderNum = 0;
                //预订票数
                $bookTicketNum = 0;
                //预订 票 销售金额
                $bookSaleMoney = 0;
                //预订 票 购买金额
                $bookCostMoney = 0;
                //取消订单数
                $cancelOrderNum = 0;
                //取消票数
                $cancelTicketNum = 0;
                //取消 票 出售金额
                $cancelSaleMoney = 0;
                //取消 票 购买金额
                $cancelCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;
                //取票数
                $printNum        = 0;

                //验证信息
                $checkOrderInfo = [];
                //取消信息
                $cancelOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];
                //取票订单信息
                $printOrderInfo = [];

                if ($action == 1) {
                    //预定支付
                    $bookOrderNum     = 1;
                    $bookTicketNum    = $num;
                    $bookSaleMoney    = $bookTicketNum * $saleMoney;
                    $bookCostMoney    = $bookTicketNum * $costMoney;
                    $checkOrderInfo[] = [$orderNum, $bookTicketNum];
                } elseif ($action == 2) {
                    //取消修改
                    $cancelOrderNum    = 1;
                    $cancelTicketNum   = $num;
                    $cancelSaleMoney   = $cancelTicketNum * $saleMoney;
                    $cancelCostMoney   = $cancelTicketNum * $costMoney;
                    $cancelOrderInfo[] = [$orderNum, $cancelTicketNum];
                } elseif ($action == 4) {
                    //取票
                    $printNum          = $num;
                    $printOrderInfo[] = [$orderNum, $printNum];
                } else {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum];
                }

                $insert[] = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    'date'               => date('Ymd', $time),
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'channel'            => $channel,
                    'order_num'          => $bookOrderNum,
                    'order_ticket'       => $bookTicketNum,
                    'cancel_num'         => $cancelOrderNum,
                    'cancel_ticket'      => $cancelTicketNum,
                    'revoke_num'         => $revokeOrderNum,
                    'print_num'          => $printNum,
                    'revoke_ticket'      => $revokeTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $bookSaleMoney,
                    'cost_money'         => $bookCostMoney,
                    'cancel_cost_money'  => $cancelCostMoney,
                    'cancel_sale_money'  => $cancelSaleMoney,
                    'revoke_cost_money'  => $revokeCostMoney,
                    'revoke_sale_money'  => $revokeSaleMoney,
                    'service_money'      => $serviceMoney,
                    'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'cancel_orders_info' => json_encode($cancelOrderInfo),
                    'revoke_orders_info' => json_encode($revokeOrderInfo),
                    'print_orders_info'  => json_encode($printOrderInfo),
                    'update_time'        => $time,
                    'operate_id'         => $operMember,
                    'main_tid'           => $isMain ? 0 : $mainTid,
                    'is_main'            => $isMain ? 1 : 2,
                    'site_id'            => $siteId,
                ];
            }

            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'pack_order');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 套票验证 实时数据
     */
    public function createPackCheck($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0)
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderMainModel) {
                $this->_subOrderMainModel = new SubOrderMain();
            }

            if (!$this->_subOrderAddonModel) {
                $this->_subOrderAddonModel = new SubOrderAddon();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            $packRes = $this->_subOrderAddonModel->getPackInfoByOrderSingle($orderNum, 'pack_order');
            if (empty($packRes)) {
                //不是套票
                throw new Exception("不是套票");
            }

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            // $orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            //判断下单时间是不是今日
            $isTodayCheck = 2;
            if (date('Ymd', strtotime($orderInfo['ordertime'])) == date('Ymd')) {
                $isTodayCheck = 1;
            }

            $channel = $orderInfo['ordermode'];
            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];

            //如果当前是子订单号 获取主订单号的票类ID 如果是主订单号 取获取当前订单号的票类ID
            if ($packRes['pack_order'] == 1) {
                //主票
                $isMain  = true;
                $mainTid = $tid;
            } else {
                //子票 获取主票订单号
                $isMain        = false;
                $mainOrderInfo = $this->_subOrderMainModel->getOrderListByOrderSingle($packRes['pack_order'], 'tid');
                //$queryParams = [$packRes['pack_order']];
                //$queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernum', $queryParams);
                //if ($queryRes['code'] != 200) {
                //    $log = [
                //        'parame'   => $packRes['pack_order'],
                //        'apiData'  => $queryRes,
                //        'function' => 'queryOrderInfoByOrdernum',
                //    ];
                //    pft_log('createPackCheck/Api/error', json_encode($log));
                //}
                //$mainOrderInfo = $queryRes['data'];
                $mainTid       = $mainOrderInfo['tid'];
            }

            //主订单号的票类发布人
            $ticketInfo = $this->_ticketModel->getTicketInfoById($mainTid, 'apply_did');
            $applyDid   = isset($ticketInfo['apply_did']) ? $ticketInfo['apply_did'] : 0;

            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
            //$tmp   = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);
            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            if (in_array($action, [6, 7])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item      = $tmp[$i];
                $fid       = $item['sellerid'];
                $sellerId  = $item['buyerid'];
                $level     = $item['level'];
                $saleMoney = $item['sale_money'];
                $paymode   = $item['pmode'];

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'    => $orderNum,
                        'sellerid'   => $sellerId,
                        'buyerid'    => $this->_getChannelSanke($channel),
                        'level'      => -3, //非0/-1
                        'sale_money' => $needMoney, //默认使用零售价的钱
                        'cost_money' => $saleMoney,
                        'pmode'      => $paymode,
                    ];

                    $tmp[] = $splitData;
                }

                //由于系统一开始的问题 套票子票在整条分销链中全部记为套票子票下单 这是错误的
                //只有被将该票打包成套票的供应商出售这笔订单的销售渠道会记为套票子票下单
                //在这里做个判断 如果子票的主票的发布人 不是当前处理的分销链层级的卖家 则跳过
                if ($applyDid != $fid) {
                    continue;
                }

                //黑名单判断必须放在链路补全后，避免数据缺失
                if ($this->isInBlacklist($fid)) {
                    continue;
                }

                $serviceMoney = 0;
                if (in_array($action, [6, 7]) && !empty($cancelInfo)) {
                    $this->_countServiceMoney($fid, $cancelInfo);
                }

                //验证订单数
                $checkOrderNum = 0;
                //验证票数
                $checkTicketNum = 0;
                //验证 票 销售金额
                $checkSaleMoney = 0;
                //验证 票 购买金额
                $checkCostMoney = 0;
                //完结订单数
                $finishOrderNum = 0;
                //完结票数
                $finishTicketNum = 0;
                //完结 票 出售金额
                $finishSaleMoney = 0;
                //完结 票 购买金额
                $finishCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;

                //验证信息
                $checkOrderInfo = [];
                //完结信息
                $finishOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];

                if ($action == 1) {
                    //验证
                    $checkOrderNum = 1;
                    //$checkTicketNum    = $num;
                    $checkTicketNum   = $num;
                    $checkSaleMoney   = $checkTicketNum * $saleMoney - $eMoney;
                    $checkCostMoney   = $checkTicketNum * $costMoney;
                    $checkOrderInfo[] = [$orderNum, $checkTicketNum];
                } elseif ($action == 2) {
                    //完结修改
                    $finishOrderNum    = 1;
                    $finishTicketNum   = $num;
                    $finishSaleMoney   = $finishTicketNum * $saleMoney - $eMoney;
                    $finishCostMoney   = $finishTicketNum * $costMoney;
                    $finishOrderInfo[] = [$orderNum, $finishTicketNum];
                } else {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum];
                }

                $insert[] = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    'date'               => date('Ymd', $time),
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'channel'            => $channel,
                    'order_num'          => $checkOrderNum,
                    'order_ticket'       => $checkTicketNum,
                    'finish_num'         => $finishOrderNum,
                    'finish_ticket'      => $finishTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $checkSaleMoney,
                    'cost_money'         => $checkCostMoney,
                    'finish_cost_money'  => $finishCostMoney,
                    'finish_sale_money'  => $finishSaleMoney,
                    'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'finish_orders_info' => json_encode($finishOrderInfo),
                    'update_time'        => $time,
                    'operate_id'         => $operMember,
                    'today_check'        => $isTodayCheck,
                    'main_tid'           => $isMain ? 0 : $mainTid,
                    'is_main'            => $isMain ? 1 : 2,
                    'revoke_ticket'      => $revokeTicketNum,
                    'revoke_cost_money'  => $revokeCostMoney,
                    'revoke_sale_money'  => $revokeSaleMoney,
                    'revoke_num'         => $revokeOrderNum,
                    'revoke_orders_info' => json_encode($revokeOrderInfo),
                    'site_id'            => $siteId,
                    'service_money'      => $serviceMoney,
                ];
            }

            if (!empty($insert)) {
                $res = $this->_statisticsModel->insertDataArr($insert, 'pack_checked');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 资源中心预定报表
     * @author: zhangyz
     * @date: 2020/4/28
     *
     * @param  string  $orderNum
     * @param  int  $num
     * @param  string  $time
     * @param  int  $action
     * @param  int  $operMember
     * @param  int  $siteId
     *
     * @return array
     */
    public function createResourceOrderData($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0)
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            $tmp = $this->_getOrderChain($orderNum);

            //过滤掉非资源中心的订单，因为查找分销链有用到缓存，而且分销链数据在别的报表有用，故在这边做一层过滤
            foreach ($tmp as $k => $v) {
                if ($v['member_relationship'] == 0) {
                    unset($tmp[$k]);
                }
            }

            //如果分销链不存在（不是资源中心的订单），直接return true，如果抛出异常，表示该条订单有问题。
            //导致pft_report_real_task表的done值不会改为1(已完成)，后续脚本又会重新获取该条队列记录，反复重试
            if (empty($tmp)) {
                return ['code' => true, 'msg' => '不是资源中心订单'];
            }
            $tmp = array_values($tmp);//重置数组的key，后面循环有用到

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $pid       = $orderInfo['pid'];
            $tid       = $orderInfo['tid'];
            $payStatus = $orderInfo['pay_status'];
            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            $applyDid = 0;
            if ($channel == 23) {
                //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
                if (!empty($packRes)) {
                    if ($packRes['pack_order'] == 1) {
                        //主票
                        $mainTid = $tid;
                    } else {
                        //子票
                        $mainOrder     = $packRes['pack_order'];
                        $mainOrderInfo = $this->_queryModel->getOrderInfo($mainOrder, 'tid');
                        $mainTid       = isset($mainOrderInfo['tid']) ? $mainOrderInfo['tid'] : 0;
                    }
                    //主订单号的票类发布人
                    $ticketInfo = $this->_ticketModel->getTicketInfoById($mainTid, 'apply_did');
                    $applyDid   = isset($ticketInfo['apply_did']) ? $ticketInfo['apply_did'] : 0;
                }
            }

            //取消修改/撤销撤改获取退票手续费信息
            $cancelInfo = [];
            if (in_array($action, [2, 3])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level      = $item['level'];
                $saleMoney  = $item['sale_money'];
                $paymode    = $item['pmode'];
                $isResource = $item['isResource'] ? true : false;

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //去除退票手续费
                $serviceMoney = 0;
                if (in_array($action, [2, 3])) {
                    if (!empty($cancelInfo)) {
                        foreach ($cancelInfo as $k => $v) {
                            if ($v['fid'] == $sellerId && $v['aid'] == $fid) {
                                $serviceMoney = $v['money'];
                            }
                        }
                    }
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                //预订订单数
                $bookOrderNum = 0;
                //预订票数
                $bookTicketNum = 0;
                //预订 票 销售金额
                $bookSaleMoney = 0;
                //预订 票 购买金额
                $bookCostMoney = 0;
                //取消订单数
                $cancelOrderNum = 0;
                //取消票数
                $cancelTicketNum = 0;
                //取消 票 出售金额
                $cancelSaleMoney = 0;
                //取消 票 购买金额
                $cancelCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;

                //验证信息
                $checkOrderInfo = [];
                //取消信息
                $cancelOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];

                if ($action == 1) {
                    //预定支付
                    $bookOrderNum     = 1;
                    $bookTicketNum    = $num;
                    $bookSaleMoney    = $bookTicketNum * $saleMoney - $eMoney;
                    $bookCostMoney    = $bookTicketNum * $costMoney;
                    $checkOrderInfo[] = [$orderNum, $bookTicketNum];
                } elseif ($action == 2) {
                    //取消修改
                    $cancelOrderNum    = 1;
                    $cancelTicketNum   = $num;
                    $cancelSaleMoney   = $cancelTicketNum * $saleMoney - $eMoney;
                    $cancelCostMoney   = $cancelTicketNum * $costMoney;
                    $cancelOrderInfo[] = [$orderNum, $cancelTicketNum];
                } else {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum];
                }

                if ($level == 0 || ($level == -1 && !$isResource)) {
                    //如果是末级分销链而且不是自供自销、散客购买，需要添加一条记录
                    if ($sellerId != $this->_defaultReseller && $sellerId != $fid) {
                        //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                        $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                        $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                        $splitData = [
                            'orderid'    => $orderNum,
                            'sellerid'   => $sellerId,
                            'buyerid'    => $this->_getChannelSanke($channel),
                            'level'      => -3, //非0/-1
                            'cost_money' => $saleMoney,
                            'sale_money' => $needMoney, //默认使用零售价的钱
                            'pmode'      => $paymode,
                        ];
                        $tmp[]     = $splitData;
                    }
                } elseif ($level == 1 || ($level == 2 && !$isResource)) {
                    //如果是第一级或者中间级，找buyer的下一级分销链，补充一条buyer的记录
                    $splitTmp = $this->getOrderChainBySellerId($orderNum, $sellerId);
                    if ($splitTmp) {
                        $splitData = [
                            'orderid'    => $orderNum,
                            'sellerid'   => $splitTmp['sellerid'],
                            'buyerid'    => $splitTmp['buyerid'],
                            'level'      => $splitTmp['level'],
                            'cost_money' => $splitTmp['cost_money'],
                            'sale_money' => $splitTmp['sale_money'],
                            'pmode'      => $splitTmp['pmode'],
                            'isResource' => true,
                        ];

                        $tmp[] = $splitData;
                    }
                }

                //fid是套票发布人 不统计
                if ($applyDid == $fid) {
                    continue;
                }

                $province  = $city = 0;
                $landModel = new Land();
                $area      = $landModel->getLandInfoByLandId($lid, 'area');
                if (!empty($area)) {
                    $areaArr  = explode('|', $area['area']);
                    $province = $areaArr[0];
                    $city     = $areaArr[1];
                }

                $insert[] = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    'date'               => date('Ymd', $time),
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'province'           => $province,
                    'city'               => $city,
                    'channel'            => $channel,
                    'order_num'          => $bookOrderNum,
                    'order_ticket'       => $bookTicketNum,
                    'cancel_num'         => $cancelOrderNum,
                    'cancel_ticket'      => $cancelTicketNum,
                    'revoke_num'         => $revokeOrderNum,
                    'revoke_ticket'      => $revokeTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $bookSaleMoney,
                    'cost_money'         => $bookCostMoney,
                    'cancel_cost_money'  => $cancelCostMoney,
                    'cancel_sale_money'  => $cancelSaleMoney,
                    'revoke_cost_money'  => $revokeCostMoney,
                    'revoke_sale_money'  => $revokeSaleMoney,
                    'service_money'      => $serviceMoney,
                    'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'cancel_orders_info' => json_encode($cancelOrderInfo),
                    'revoke_orders_info' => json_encode($revokeOrderInfo),
                    'update_time'        => $time,
                    'operate_id'         => $operMember,
                    'site_id'            => $siteId,
                ];
            }

            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'resource_order');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }
        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 云票务出票报表
     *
     * <AUTHOR>
     * @date   2018-07-02
     *
     * @param  int  $orderNum  订单号
     * @param  int  $num  数量
     * @param  int  $time  插入时间
     * @param  int  $operateMember  操作人
     * @param  array  $memo  额外信息记录
     */
    public function createTicket($orderNum, $num, $time, $operateMember, $memo = [])
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }
            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, ss.aid, ss.paymode, ss.tprice, member';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            //$orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');
            if (empty($orderInfo)) {
                throw new Exception("没找到订单");
            }
            $teamFlag = in_array($orderInfo['ordermode'], [TeamConst::_GROUP_RESERVATION_DISTRIBUTOR, TeamConst::_GROUP_RESERVATION_SUPPLER]);
            //$aid        = $orderInfo['aid'];
            $memberBiz   = new \Business\Member\MemberRelation();
            $memberMap   = $memberBiz->getMemberStaffInfoBySonIdToJava($operateMember,"parent_id");
            if (!empty($memberMap)) {
                $aid = $memberMap['parent_id'];
            } else {
                $aid = $operateMember;
            }
            $tid        = $orderInfo['tid'];
            $fid        = $orderInfo['member'];
            $lid        = $orderInfo['lid'];
            $payMode    = $orderInfo['paymode'];
            $saleMoney  = $orderInfo['tprice'] * $num;
            $sourceType = !empty($memo['is_print_pay']) ? $memo['is_print_pay'] : 0;
            $insert[]  = [
                'aid'         => $aid,
                'fid'         => $fid,
                'lid'         => $lid,
                'tid'         => $tid,
                'num'         => $num,
                'pay_mode'    => $payMode,
                'insert_time' => time(),
                'date'        => date('Ymd', $time),
                'operate_id'  => $operateMember,
                'sale_money'  => $saleMoney,
                'orders_info' => json_encode([$orderNum]), //需要json格式
                'source_type' => $sourceType, //1新规则取票报表；2是取票+取票支付
                //是否是团单 由于orders_info是单个没有合并处理 因此可以用team_flag
                //如果合并处理需要改动这里 按source_type  team_flag等维度分组
                'team_flag' => (int)$teamFlag,
            ];
            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'ticket');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }
        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 云票务团队订单汇总报表
     *
     * <AUTHOR>
     * @date   2018-07-02
     *
     * @param  int  $orderNum  订单号
     * @param  int  $num  数量
     * @param  int  $time  插入时间
     * @param  int  $operateMember  操作人
     */
    public function createTeamOrderPay($orderNum, $num, $time, $operateMember, $flag = 4)
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            $field     = 'ordernum, ordermode, ss.pay_status, ss.aid, ss.paymode, ss.tprice, member';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');
            //$orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单", 203);
            }
            if ($orderInfo['pay_status'] == 2) {
                throw new Exception("未支付", 203);
            }
            //产品要求只记录云票务的团单
            if (!in_array($orderInfo['ordermode'], [TeamConst::_GROUP_RESERVATION_DISTRIBUTOR, TeamConst::_GROUP_RESERVATION_SUPPLER])) {
                throw new Exception("不是云票务团队订单01");
            }
            //如果是平台下单 云票务加票类型不会写入firstSaleChannel为云票务 但是追踪记录里的source是4-云票务
            //尝试查询下订单追踪记录表
            if ($flag == 16) {
                $trackEsApi = new \Business\JavaApi\Log\OrderTrack();
                $addTicketTrackList = $trackEsApi->queryOrderTrackLog([
                    'ordernum' => $orderNum,
                    'action' => 16,
                    'pageNum' => 1,
                    'pageSize' => 100,
                ]);
                //非当前时间的 还有非云票务的加票都不写入报表
                if (!empty($addTicketTrackList)) {
                    // 按照 insertTime 降序排序
                    usort($addTicketTrackList, function ($a, $b) {
                        return strtotime($b['insertTime']) - strtotime($a['insertTime']);
                    });
                    // 取出 insertTime 最大的那一条
                    $latest = $addTicketTrackList[0];
                    if ($latest['source'] != 4) {
                        throw new Exception("不是云票务团队订单加票02", 203);
                    }
                    //pft_report_real_task表里关于加票的操作人记录是不正确的 可能需要中台来排查问题 这里用追踪记录里的替换下
                    //$operateMember = $latest['operMember'];
                }
            } else {
                //根据订单的ordermode=44且uu_order_fx_details.ext_content.firstSaleChannel=30区分出是从云票务下的报团计调订单
                $detail = (new OrderDetailQuery())->getOrderWithDetail([$orderNum]);
                if ($detail['code'] != 200) {
                    throw new Exception('查询订单详情失败：msg=' . $detail['msg'] . ' inside_error=' . $detail['inside_error'], 203);
                }
                $detail = current($detail['data']);
                $extContents = json_decode($detail['fxDetails']['extContent'], true);
                if (empty($extContents) || $extContents['firstSaleChannel'] != 30) {
                    throw new Exception("不是云票务团队订单03", 203);
                }
            }
            $aid       = $orderInfo['aid'];
            $fid       = $orderInfo['member'];
            $payMode   = $orderInfo['paymode'];
            $saleMoney = $orderInfo['tprice'] * $num;

            //判断是否套票子票
            $orderInfo = $this->_queryModel->getPackOrdersInfoByOrderId($orderNum, 'ifpack');
            if (!empty($orderInfo) && $orderInfo[0]['ifpack'] == 2) {
                throw new Exception("套票子订单不统计");
            }

            $insert[] = [
                'aid'         => $aid,
                'fid'         => $fid,
                'num'         => $num,
                'pay_way'     => $payMode,
                'insert_time' => time(),
                'date'        => date('Ymd', $time),
                'operate_id'  => $operateMember,
                'sale_money'  => $saleMoney,
                'orders_info' => json_encode([[$orderNum, $num]]),
            ];
            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'team_order_pay');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }
        } catch (Exception $e) {
            $code = $e->getCode();
            if ($code == 203) {
                TeamUtil::info([
                    'tag' => 'createTeamOrderPay',
                    'title' => 'Service云票务团队订单汇总报表',
                    'orderNum' => $orderNum,
                    'flag' => $flag,
                    'msg' => $e->getMessage(),
                ]);
            }
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];

    }

    /**
     * 团队实时验证报表
     *
     * <AUTHOR>
     * @date   2018-06-25
     */
    public function createTeamCheck($data)
    {
        $code = true;
        $msg  = '';
        try {
            //团队订单号
            $teamOrderId = $data['order_id'];
            //获取团单数据
            if (empty($this->_teamModel)) {
                $this->_teamModel = new TeamOrderSearch();
            }

            $teamInfo = $this->_teamModel->getMainOrderInfoByOrderNum($teamOrderId);
            if (empty($teamInfo)) {
                throw new Exception('不存在该团队订单号', 403);
            }

            //获取团单关联的子订单
            $teamSonInfo = $this->_teamModel->getSonOrderInfoByMainOrderNum($teamOrderId);
            if (empty($teamSonInfo)) {
                throw new Exception('该团单未关联子订单号', 403);
            }

            $sonOrderNum = array_column($teamSonInfo, 'son_ordernum');

            if (empty($this->_referModel)) {
                $this->_referModel = new OrderRefer();
            }
            $orderToolModel = new \Model\Order\OrderTools('slave');
            $packOrderNum   = $orderToolModel->getPackSubOrder($sonOrderNum, 'orderid, pack_order');
            $packRes        = [];
            //套票子票订单号
            $packSon = [];
            foreach ($packOrderNum as $item) {
                $packRes[$item['pack_order']][] = $item['orderid'];
                $sonOrderNum[]                  = $item['orderid'];
                $packSon[]                      = $item['orderid'];
            }

            $orderInfo = $orderToolModel->getOrderInfo($sonOrderNum, 'ordernum, tnum, totalmoney, lid, status');
            $orderRes  = [];
            $lidArr    = [];
            foreach ($orderInfo as $value) {
                //已取消订单过滤
                if ($value['status'] == 3) {
                    continue;
                }
                $orderRes[$value['ordernum']] = $value;
                //不统计套票主票的景点个数
                if (!isset($packRes[$value['ordernum']])) {
                    $lidArr[] = $value['lid'];
                }
            }

            $lidArr    = array_unique($lidArr);
            $lidNum    = count($lidArr);
            $saleMoney = 0;
            $numArr    = [];

            $ordersInfo = [];
            foreach ($orderInfo as $value) {
                //已取消订单过滤
                if ($value['status'] == 3) {
                    continue;
                }

                //过滤掉套票子票
                if (in_array($value['ordernum'], $packSon)) {
                    continue;
                }

                $saleMoney += $value['totalmoney'];

                // 相同产品下面的票数需要加起来 取子票的 不取主票
                if (isset($packRes[$value['ordernum']]) && !empty($packRes[$value['ordernum']]) && is_array($packRes[$value['ordernum']])) {
                    foreach ($packRes[$value['ordernum']] as $item) {
                        $num = isset($orderRes[$item]) ? $orderRes[$item]['tnum'] : 0;
                        if (isset($numArr[$orderRes[$item]['lid']])) {
                            $numArr[$orderRes[$item]['lid']] += $num;
                        } else {
                            $numArr[$orderRes[$item]['lid']] = $num;
                        }
                    }
                } else if (isset($numArr[$value['lid']])) {
                    $numArr[$value['lid']] += $value['tnum'];
                } else {
                    $numArr[$value['lid']] = $value['tnum'];
                }

                $ordersInfo[] = $value['ordernum'];
            }
            // 获取最小的票数
            $checkMinNum = min(array_values($numArr));
            $checkMaxNum = max(array_values($numArr));
            //获取每个景区的主题
            //if (empty($this->_landModel)) {
            //    $this->_landModel = new Land();
            //}
            $landApi  = new \Business\CommodityCenter\Land();
            $landInfo = $landApi->queryLandMultiQueryById($lidArr);
            //$landInfo = $this->_landModel->getLandInfoByMuli($lidArr, 'id, topic');
            $landRes  = [];
            foreach ($landInfo as $value) {
                $landRes[$value['id']] = $value['topic'];
            }
            $topicArr = [];
            //每个团单里 同一个lid的子单的景区主题只统计一次
            $lidCalculate = [];
            foreach ($orderInfo as $value) {
                //如果是主票 直接获取
                if (isset($packRes[$value['ordernum']]) && is_array($packRes[$value['ordernum']]) && !empty($packRes[$value['ordernum']])) {
                    foreach ($packRes[$value['ordernum']] as $item) {
                        //取出子订单的信息
                        $tmpOrderInfo = isset($orderRes[$item]) ? $orderRes[$item] : [];
                        if (empty($tmpOrderInfo)) {
                            continue;
                        }
                        if (in_array($tmpOrderInfo['lid'], $lidCalculate)) {
                            continue;
                        }
                        if (isset($landRes[$tmpOrderInfo['lid']])) {
                            $topicArr = array_merge($topicArr, explode(',', $landRes[$tmpOrderInfo['lid']]));
                        }
                        $lidCalculate[] = $tmpOrderInfo['lid'];
                    }
                } else {
                    if (in_array($value['lid'], $lidCalculate)) {
                        continue;
                    }
                    if (isset($landRes[$value['lid']])) {
                        $topicArr = array_merge($topicArr, explode(',', $landRes[$value['lid']]));
                    }
                    $lidCalculate[] = $value['lid'];
                }
            }

            $systemTopic = ThemeConst::THEMES;
            //按顺序排序
            $systemTopicRes = [];
            $topicArrCount  = array_count_values($topicArr);
            foreach ($systemTopic as $tmpThemes) {
                if (in_array($tmpThemes, $topicArr)) {
                    $systemTopicRes[] = $tmpThemes . $topicArrCount[$tmpThemes];
                }
            }
            $topicStr = json_encode($systemTopicRes);
            $topicMd5 = md5($topicStr);

            $aid       = $teamInfo['aid'];
            $fid       = $teamInfo['fid'];
            $guide     = $teamInfo['guide'];
            $province  = $teamInfo['province'];
            $city      = $teamInfo['city'];
            $isLodging = $teamInfo['is_lodging'];
            $buyerGroupId = $teamInfo['group_id'];
            //之前的报团的buyer_id不能为散客 并且只支持一级分销
            $insertData[] = [
                'date'          => date('Ymd'),
                'seller_id'     => $aid,
                'buyer_id'      => $fid,
                'guide'         => $guide,
                'team_lid_num'  => $lidNum,
                'province'      => $province,
                'city'          => $city,
                'sale_money'    => $saleMoney,
                'check_num'     => $checkMaxNum,
                'check_min_num' => $checkMinNum,
                'orders_info'   => json_encode($ordersInfo),
                'team_order_id' => $teamOrderId,
                'topic_str'     => $topicStr,
                'topic_md5'     => $topicMd5,
                'insert_time'   => time(),
                'level'         => 0,
                'is_lodging'    => $isLodging,
                'buyer_group_id' => $buyerGroupId,
            ];

            $insertData[] = [
                'date'          => date('Ymd'),
                'seller_id'     => $fid,
                'buyer_id'      => 112,
                'guide'         => $guide,
                'team_lid_num'  => $lidNum,
                'province'      => $province,
                'city'          => $city,
                'sale_money'    => $saleMoney,
                'check_num'     => $checkMaxNum,
                'check_min_num' => $checkMinNum,
                'orders_info'   => json_encode($ordersInfo),
                'team_order_id' => $teamOrderId,
                'topic_str'     => $topicStr,
                'topic_md5'     => $topicMd5,
                'insert_time'   => time(),
                'level'         => -1,
                'is_lodging'    => $isLodging,
                'buyer_group_id' => 0,
            ];

            if (empty($this->_staV3Model)) {
                $this->_staV3Model = new StatisticsV3();
            }

            $res = $this->_staV3Model->addCheckAllData($insertData);
            if (empty($res)) {
                throw new Exception('插入数据失败', 400);
            }
        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 演出预定报表 实时数据
     *
     * @param  string  $orderNum  订单好
     * @param  int  $num  操作数量
     * @param  string  $time  操作时间
     * @param  int  $action  变更状态: 0=下单,1=修改,2=取消,3=出票,4=支付,验证,撤销,7=撤改,8=重打印,9=离线订单下载,10=处理退票申请,11=提交退票申请,12=过期,13=同意退票申请,14=拒绝退票申请,15=核销,16=订单加票,17=完结
     * @param  int  $operMember  操作人id
     * @param  int  $siteId  站点id
     * @param  int  $flag  实时报表操作类型 0=下单,1=修改,2=取消,3=出票,4=支付,验证,撤销,7=撤改,8=重打印,9=离线订单下载,10=处理退票申请,11=提交退票申请,12=过期,13=同意退票申请,14=拒绝退票申请,15=核销,16=订单加票,17=完结
     *
     * @return array
     */
    public function createSeriesOrderData($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0, $flag = 0)
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_orderQueryModel) {
                $this->_orderQueryModel = new OrderQuery();
            }

            if (!$this->_reportRealTaskModel) {
                $this->_reportRealTaskModel = new ReportRealTaskModel();
            }

            $field = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status';

//            $orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            // 判断是否是演出票种
            if (!$this->_checkSeriesOrder($orderInfo['lid'])) {
                return ['code' => $code, 'msg' => '不是演出票种'];
            }

            // 获取演出场次信息
            list($seriesTimeBegin, $seriesTimeEnd) = $this->_getSeriesTime($orderNum);
            if (empty($seriesTimeBegin) || empty($seriesTimeEnd)) {
                throw new Exception("没找到场次信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $pid       = $orderInfo['pid'];
            $tid       = $orderInfo['tid'];
            $payStatus = $orderInfo['pay_status'];
            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            //检查下是不是支付前的操作，加票16、修改1如果是支付前是不计入报表
            $inReport = true;
            if (in_array($flag, [1, 16])) {
                //获取订单支付时间戳，用于判断支付前或者支付后的操作， 追踪记录表的时间戳可能与实时报表的时间戳有延迟导致的差异，所以还是需要去取下real_task里面对应的时间戳
                $payTimeListRes = $this->_getTrackOrderListByOrderNumsAndAction([$orderNum]);
                $payTimeListMap = array_column($payTimeListRes, 'insertTime', 'ordernum');
                $trackPayTime   = $payTimeListMap[$orderNum] ?? 0;
                //为了防止时间格式问题导致查询不到支付时间戳，异常直接放行
                $trackPayTimeCheck = false;
                date('Y-m-d H:i:s', strtotime($trackPayTime)) == $trackPayTime && $trackPayTimeCheck = true;
                if ($trackPayTimeCheck) {
                    $trackPayTime = strtotime($trackPayTime);
                    //根据支付时间查询报表中间表task支付插入时间，来对比，避免程序延迟导致对比时间误差
                    $taskPayRes = $this->_reportRealTaskModel->getTasFlagInfo($orderNum, ($trackPayTime - 10),
                        ($trackPayTime + 10), 4);
                    //获取写入时间
                    $taskPayTimeListMap = array_column($taskPayRes, 'insert_time', 'ordernum');
                    $payTime            = $taskPayTimeListMap[$orderNum] ?? 0;
                    if ($payTime && $time && $time < $payTime) {
                        $inReport = false;
                        $tmpMsg   = "订单：$orderNum, 类型：$flag (加票16、修改1), 在支付前，不计入报表";
                        pft_log('real_time/not_in_report', "订单：$orderNum, 类型：$flag (加票16、修改1), 在支付前，不计入报表");
                    }
                }
            }
            if (!$inReport) {
                //无需计入报表
                return ['code' => $code, 'msg' => $tmpMsg ?? '无需计入报表'];
            }

            $applyDid = 0;
            $mainTid  = 0;
            $mainType = '';
            if ($channel == 23) {
                //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
                if (!empty($packRes)) {
                    if ($packRes['pack_order'] == 1) {
                        //主票
                        $mainTid = $tid;
                    } else {
                        //子票
                        $mainOrder     = $packRes['pack_order'];
                        $mainOrderInfo = $this->_queryModel->getOrderInfo($mainOrder, 'tid');
                        $mainTid       = $mainOrderInfo['tid'] ?? 0;
                    }
                    //主订单号的票类信息
                    $javaApi    = new \Business\CommodityCenter\Ticket();
                    $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                    $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                    $mainType   = $ticketInfo['land']['p_type'] ?? '';
                }
            }

//            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
//            $tmp   = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);

            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //取消修改/撤销撤改获取退票手续费信息
            $cancelInfo = [];
            if (in_array($action, [2, 3])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level     = $item['level'];
                $saleMoney = $item['sale_money'];
                $paymode   = $item['pmode'];

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];
                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //去除退票手续费
                $serviceMoney = 0;
                if (in_array($action, [2, 3]) && !empty($cancelInfo)) {
                    $serviceMoney = $this->_countServiceMoney($fid, $cancelInfo);
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                //预订订单数
                $bookOrderNum = 0;
                //预订票数
                $bookTicketNum = 0;
                //预订 票 销售金额
                $bookSaleMoney = 0;
                //预订 票 购买金额
                $bookCostMoney = 0;
                //取消订单数
                $cancelOrderNum = 0;
                //取消票数
                $cancelTicketNum = 0;
                //取消 票 出售金额
                $cancelSaleMoney = 0;
                //取消 票 购买金额
                $cancelCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;

                //验证信息
                $checkOrderInfo = [];
                //取消信息
                $cancelOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];

                if ($action == 1) {
                    //预定支付
                    $bookOrderNum     = 1;
                    $bookTicketNum    = $num;
                    $bookSaleMoney    = $bookTicketNum * $saleMoney - $eMoney;
                    $bookCostMoney    = $bookTicketNum * $costMoney;
                    $checkOrderInfo[] = [$orderNum, $bookTicketNum];
                } elseif ($action == 2) {
                    //取消修改
                    $cancelOrderNum    = 1;
                    $cancelTicketNum   = $num;
                    $cancelSaleMoney   = $cancelTicketNum * $saleMoney - $eMoney;
                    $cancelCostMoney   = $cancelTicketNum * $costMoney;
                    $cancelOrderInfo[] = [$orderNum, $cancelTicketNum];
                } else {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum];
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'    => $orderNum,
                        'sellerid'   => $sellerId,
                        'buyerid'    => $this->_getChannelSanke($channel),
                        'level'      => -3, //非0/-1
                        'cost_money' => $saleMoney,
                        'sale_money' => $needMoney, //默认使用零售价的钱
                        'pmode'      => $paymode,
                    ];

                    $tmp[] = $splitData;
                }

                //fid是套票发布人 才会记录子订单主票id
                $mainTidNew  = $mainTid;
                $mainTypeNew = $mainType;
                if ($applyDid != $fid) {
                    $mainTidNew  = 0;
                    $mainTypeNew = '';
                }

                $insert[] = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    'date'               => date('Ymd', $time),
                    'series_time_begin'  => $seriesTimeBegin,
                    'series_time_end'    => $seriesTimeEnd,
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'channel'            => $channel,
                    'order_num'          => $bookOrderNum,
                    'order_ticket'       => $bookTicketNum,
                    'cancel_num'         => $cancelOrderNum,
                    'cancel_ticket'      => $cancelTicketNum,
                    'revoke_num'         => $revokeOrderNum,
                    'revoke_ticket'      => $revokeTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $bookSaleMoney,
                    'cost_money'         => $bookCostMoney,
                    'cancel_cost_money'  => $cancelCostMoney,
                    'cancel_sale_money'  => $cancelSaleMoney,
                    'revoke_cost_money'  => $revokeCostMoney,
                    'revoke_sale_money'  => $revokeSaleMoney,
                    'service_money'      => $serviceMoney,
                    'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'cancel_orders_info' => json_encode($cancelOrderInfo),
                    'revoke_orders_info' => json_encode($revokeOrderInfo),
                    'update_time'        => $time,
                    'operate_id'         => $operMember,
                    'site_id'            => $siteId,
                    'main_tid'           => $mainTidNew,
                    'main_type'          => $mainTypeNew,
                ];
            }

            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'series_order');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }
        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 第二版验证报表 实时数据
     */
    public function createSeriesCheckedData($orderNum, $num, $time, $action, $operMember = 0, $siteId = 0)
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_subOrderAddonModel) {
                $this->_subOrderAddonModel = new SubOrderAddon();
            }

            if (!$this->_subOrderMainModel) {
                $this->_subOrderMainModel = new SubOrderMain();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_orderQueryModel) {
                $this->_orderQueryModel = new OrderQuery();
            }

            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');

//            $orderInfo = $this->_queryModel->getOrderInfo($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            // 判断是否演出票种
            if (!$this->_checkSeriesOrder($orderInfo['lid'])) {
                return ['code' => $code, 'msg' => '不是演出票种'];
            }

            // 获取演出场次信息
            list($seriesTimeBegin, $seriesTimeEnd) = $this->_getSeriesTime($orderNum);
            if (empty($seriesTimeBegin) || empty($seriesTimeEnd)) {
                throw new Exception("没找到场次信息");
            }

            //判断下单时间是不是今日
            $isTodayCheck = 2;
            if (date('Ymd', strtotime($orderInfo['ordertime'])) == date('Ymd')) {
                $isTodayCheck = 1;
            }

            $channel = $orderInfo['ordermode'];
            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];

            $applyDid = 0;
            $mainTid  = 0;
            $mainType = '';
            if ($channel == 23) {
                //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                $packRes = $this->_subOrderAddonModel->getPackInfoByOrderSingle($orderNum, 'pack_order');
                if (!empty($packRes)) {
                    if ($packRes['pack_order'] == 1) {
                        //主票
                        $mainTid = $tid;
                    } else {
                        //子票
                        $mainOrder     = $packRes['pack_order'];
                        $mainOrderInfo = $this->_subOrderMainModel->getOrderListByOrderSingle($mainOrder, 'tid');
                        $mainTid       = $mainOrderInfo['tid'] ?? 0;
                    }
                    //主订单号的票类信息
                    $javaApi    = new \Business\CommodityCenter\Ticket();
                    $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                    $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                    $mainType   = $ticketInfo['land']['p_type'] ?? '';
                }
            }

//            $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode';
//            $tmp   = $this->_subOrderSplitModel->getListByOrderSingle($orderNum, $field);
            $tmp = $this->_getOrderChain($orderNum);

            if (empty($tmp)) {
                throw new Exception("没找到分销链信息");
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            if (in_array($action, [3])) {
                $cancelInfo = $this->_getServiceMoney($orderNum);
            }

            $insert = [];
            for ($i = 0; $i < count($tmp); $i++) {
                $item     = $tmp[$i];
                $fid      = $item['sellerid'];
                $sellerId = $item['buyerid'];
                //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                $level     = $item['level'];
                $saleMoney = $item['sale_money'];
                $paymode   = $item['pmode'];

                //记录顶级供应商的成本价
                $costMoney = $item['cost_money'];

                //操作用户
                $operMember = $operMember ? $operMember : $item['buyerid'];

                if (isset($couponList[$orderNum][$fid][$sellerId])) {
                    //优惠金额
                    $eMoney = $couponList[$orderNum][$fid][$sellerId];
                } else {
                    $eMoney = 0;
                }

                //通过渠道将散客进行归类
                if (in_array($level, [-1, 0])) {
                    $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                    if ($tmpId !== false) {
                        //最后一级就是散客的情况(自供自销也算在这里面)
                        if ($level == 0) {
                            $level = 1;
                        } else {
                            $level = -3;
                        }
                        $sellerId = $tmpId;
                    }
                }

                $serviceMoney = 0;
                if (in_array($action, [3]) && !empty($cancelInfo)) {
                    $serviceMoney = $this->_countServiceMoney($fid, $cancelInfo);
                }

                //验证订单数
                $checkOrderNum = 0;
                //验证票数
                $checkTicketNum = 0;
                //验证 票 销售金额
                $checkSaleMoney = 0;
                //验证 票 购买金额
                $checkCostMoney = 0;
                //完结订单数
                $finishOrderNum = 0;
                //完结票数
                $finishTicketNum = 0;
                //完结 票 出售金额
                $finishSaleMoney = 0;
                //完结 票 购买金额
                $finishCostMoney = 0;
                //撤销订单数
                $revokeOrderNum = 0;
                //撤销票数
                $revokeTicketNum = 0;
                //撤销 票 出售金额
                $revokeSaleMoney = 0;
                //撤销 票 购买金额
                $revokeCostMoney = 0;

                //验证信息
                $checkOrderInfo = [];
                //完结信息
                $finishOrderInfo = [];
                //撤销信息
                $revokeOrderInfo = [];

                if ($action == 1) {
                    //验证
                    $checkOrderNum    = 1;
                    $checkTicketNum   = $num;
                    $checkSaleMoney   = $checkTicketNum * $saleMoney - $eMoney;
                    $checkCostMoney   = $checkTicketNum * $costMoney;
                    $checkOrderInfo[] = [$orderNum, $checkTicketNum];
                } elseif ($action == 2) {
                    //完结修改
                    $finishOrderNum    = 1;
                    $finishTicketNum   = $num;
                    $finishSaleMoney   = $finishTicketNum * $saleMoney - $eMoney;
                    $finishCostMoney   = $finishTicketNum * $costMoney;
                    $finishOrderInfo[] = [$orderNum, $finishTicketNum];
                } elseif ($action == 3) {
                    //撤销撤改
                    $revokeOrderNum    = 1;
                    $revokeTicketNum   = $num;
                    $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney;
                    $revokeCostMoney   = $revokeTicketNum * $costMoney;
                    $revokeOrderInfo[] = [$orderNum, $revokeTicketNum];
                }

                //特殊处理
                $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                if ($splitTmp) {
                    //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                    $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                    $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                    $splitData = [
                        'orderid'    => $orderNum,
                        'sellerid'   => $sellerId,
                        'buyerid'    => $this->_getChannelSanke($channel),
                        'level'      => -3, //非0/-1
                        'cost_money' => $saleMoney,
                        'sale_money' => $needMoney, //默认使用零售价的钱
                        'pmode'      => $paymode,
                    ];

                    $tmp[] = $splitData;
                }

                //fid是套票发布人 才会记录子订单主票id
                $mainTidNew  = $mainTid;
                $mainTypeNew = $mainType;
                if ($applyDid != $fid) {
                    $mainTidNew  = 0;
                    $mainTypeNew = '';
                }

                $insert[] = [
                    'fid'                => $fid,
                    'reseller_id'        => $sellerId,
                    'date'               => date('Ymd', $time),
                    'series_time_begin'  => $seriesTimeBegin,
                    'series_time_end'    => $seriesTimeEnd,
                    'lid'                => $lid,
                    'pid'                => $pid,
                    'tid'                => $tid,
                    'channel'            => $channel,
                    'order_num'          => $checkOrderNum,
                    'order_ticket'       => $checkTicketNum,
                    'finish_num'         => $finishOrderNum,
                    'finish_ticket'      => $finishTicketNum,
                    'pay_way'            => $paymode,
                    'sale_money'         => $checkSaleMoney,
                    'cost_money'         => $checkCostMoney,
                    'finish_cost_money'  => $finishCostMoney,
                    'finish_sale_money'  => $finishSaleMoney,
                    'level'              => $level,
                    'orders_info'        => json_encode($checkOrderInfo),
                    'finish_orders_info' => json_encode($finishOrderInfo),
                    'update_time'        => $time,
                    'operate_id'         => $operMember,
                    'today_check'        => $isTodayCheck,
                    'revoke_num'         => $revokeOrderNum,
                    'revoke_ticket'      => $revokeTicketNum,
                    'revoke_orders_info' => json_encode($revokeOrderInfo),
                    'revoke_cost_money'  => $revokeCostMoney,
                    'revoke_sale_money'  => $revokeSaleMoney,
                    'site_id'            => $siteId,
                    'service_money'      => $serviceMoney,
                    'main_tid'           => $mainTidNew,
                    'main_type'          => $mainTypeNew,
                ];
            }

            if (!empty($insert)) {
                if (!$this->_statisticsModel) {
                    $this->_statisticsModel = new Statistics();
                }

                $res = $this->_statisticsModel->insertDataArr($insert, 'series_checked');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }
        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 分终端验证实时汇总报表统计任务
     * Create by zhangyangzhen
     * Date: 2019/4/18
     * Time: 10:20
     *
     * @param  string  $ordernum  订单号
     * @param  int  $num  数量
     * @param  int  $branchTerminal  终端号
     * @param  int  $operMember  操作员id
     *
     * @return bool
     */
    public function realtimeBranchTerminalCheckedTask($ordernum, $num, $branchTerminal, $operMember = 0, $time = 0,$source = -1)
    {
        $code = true;
        $msg  = '';

        try {

            $num = intval($num);
            if (!$ordernum || $num <= 0 || !$branchTerminal) {
                return false;
            }

            //订单详细
            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member';
            $orderInfo = $this->_getOrder($ordernum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                return false;
            }

            $tid = $orderInfo['tid'];
            $lid = $orderInfo['lid'];

            $buyChain = $this->_getOrderChain($ordernum);

            if (empty($buyChain)) {
                return false;
            }

            //找最顶级分销链
            foreach ($buyChain as $chain) {
                if (in_array($chain['level'], [0, 1])) {
                    $fid = $chain['sellerid'];
                    break;
                }
            }

            $tempOrderInfo = [[$ordernum, $num]];
            $time          = $time > 0 ? $time : time();

            $baseData = [
                'fid'           => $fid,
                'tid'           => $tid,
                'lid'           => $lid,
                'terminal'      => $branchTerminal ?: 0,
                'operate_id'    => $operMember ?: 0,
                'check_orders'  => 1,
                'check_tickets' => $num,
                'check_people'  => 0, //旧版分终端报表无法统计入园人数，暂记为0
                'orders_info'   => json_encode($tempOrderInfo),
                'update_time'   => time(),
                'source'        => $source
            ];

            $date = date('Ymd', $time);
            $insertDay[]  = array_merge($baseData, ['date' => $date]);
            $insertHour[] = array_merge($baseData, ['date_hour' => $date . date('H', $time)]);

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            if (!empty($insertDay)) {
                $res = $this->_statisticsModel->insertDataArr($insertDay, 'branchTerminal');
                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }

            if (!empty($insertHour)) {
                $resHour = $this->_statisticsModel->insertDataArr($insertHour, 'branchTerminalHour');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    //throw new \Exception($errMsg);
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return $res ? true : false;
    }

    /**
     * 多景点入园实时汇总报表统计任务
     * Create by zhangyangzhen
     * Date: 2019/10/29
     * Time: 10:27
     *
     * @param  string  $ordernum  订单号
     * @param  int  $num  数量
     * @param  int  $branchTerminal  终端号
     * @param  int  $operMember  操作员id
     * @param  array  $recordId  [1001,5]最后一条入园记录的ID以及入园记录条数
     *
     * @return  bool
     */
    public function realtimeScenicCheckTask($ordernum, $num, $branchTerminal, $operMember = 0, $recordId = [], $time = 0)
    {
        $num = intval($num);
        if (!$ordernum || $num <= 0 || !$branchTerminal || empty($recordId)) {
            return false;
        }

        $lastId = $recordId[0];
        $limit  = $recordId[1];

        //新版的入园核销分离，判断是否是多景点(分终端不一样),不是的话直接跳过
        $isScenicChk = $this->_isScenicChk($ordernum);
        if ($isScenicChk === false) {
            return true;
        }

        //订单详细
        $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member';
        $orderInfo = $this->_getOrder($ordernum, $field, false, false, 'lprice');

        if (empty($orderInfo)) {
            return false;
        }

        $tid = $orderInfo['tid'];
        $lid = $orderInfo['lid'];

        $buyChain = $this->_getOrderChain($ordernum);

        if (empty($buyChain)) {
            return false;
        }

        //找最顶级分销链
        foreach ($buyChain as $chain) {
            if (in_array($chain['level'], [0, 1])) {
                $fid = $chain['sellerid'];
                break;
            }
        }

        //获取该订单验证人次数据，通过该订单号和终端号获取验证的数据，再通过已验证的门票码数量得到已过的人次
        $enterLandModel = $this->_getEnterLandModel();

        //获取具体对应的入园记录，一次验证可能会有多条入园记录
        $thisCheck = $enterLandModel->getOrderPlayRecordById($ordernum, $lastId, 'scenic_id,voucher,times,update_time',
            $limit);

        $times = $people = array_sum(array_column($thisCheck, 'times'));

        //判断在此之前对应的订单号、终端号的验证记录。同一次验证可能会有多条入园记录，但是终端号scenic_id都是一样的，udpate_time取最早的一条时间
        $historyCheck = $enterLandModel->getHistoryCheckRecord($ordernum, $thisCheck[0]['scenic_id'], '',
            $thisCheck[0]['update_time']);

        if (!empty($historyCheck)) {
            $historyTimes = array_column($historyCheck, 'times');

            //验证票数要么是1，要么等于票数
            if (in_array($orderInfo['tnum'], $historyTimes)) {
                //如果之前有整批入园的记录，说明所有人都已经入园过了，这边入园人数不再统计
                $people = 0;
            } else {
                //如果之前没有整批入园的记录，都是分批入园的。先看本次验证是分批还是整批
                //整批验证，要看之前入园过的人数
                $historyVoucher = array_unique(array_column($historyCheck, 'voucher'));
                if ($times == $orderInfo['tnum']) {
                    $historyCount = count($historyVoucher);
                    $people       = ($times - $historyCount) > 0 ? $times - $historyCount : 0;
                } else {
                    //分批验证，看之前验证记录中是否有当前凭证码（或门票码等）的验证记录
                    $thisCheckVoucher = array_column($thisCheck, 'voucher');
                    $diffVoucher      = array_diff($thisCheckVoucher, $historyVoucher);
                    if (!empty($diffVoucher)) {
                        $people = count($diffVoucher);
                    } else {
                        $people = 0;
                    }
                }
            }
        }

        $tempOrderInfo = [[$ordernum, $num]];

        $data = [
            'date'          => date('Ymd', $time),
            'fid'           => $fid,
            'tid'           => $tid,
            'lid'           => $lid,
            'terminal'      => $branchTerminal ?: 0,
            'operate_id'    => $operMember ?: 0,
            'check_orders'  => 1,
            'check_tickets' => $times,
            'check_people'  => $people,
            'orders_info'   => json_encode($tempOrderInfo),
            'update_time'   => time(),
        ];

        if (!$this->_statisticsModel) {
            $this->_statisticsModel = new Statistics();
        }

        $res = $this->_statisticsModel->insertDataArr([$data], 'branchTerminal');

        return $res ? true : false;
    }

    /**
     * 判断是不是散客
     *
     * 一、channel为11(微信商城) 15(智能终端) 18(年卡)为潜在可能性的散客 还需判断
     *                   1.用户名和手机号一样  是散客
     *                   2.名字里带有YKT的    是散客  一卡通的比较特殊 渠道是用智能终端 需要区分终端散客和一卡通散客
     * 二、channel为13的都是散客
     * 三、供应商和分销商id一样的是散客
     * 四、分销商为112的是散客
     *
     * @param  int  $member  分销商
     * @param  int  $aid  供应商
     * @param  int  $channel  渠道
     *
     * @return bool
     */
    private function _getSankeId($member, $aid, $channel)
    {
        //是否散客
        $isSanKe = false;
        //一卡通
        $isYkt   = false;
        if (in_array($channel, [11, 15, 18, 43])) {
            if (!$this->_memberModel) {
                $this->_memberModel = new Member();
            }

            $res = $this->_memberModel->getMemberInfo($member, 'id', 'id, account, mobile, dtype');
            if ($res['account'] == $res['mobile']) {
                $isSanKe = true;
            }

            if (strpos($res['account'], 'YKT') !== false) {
                $isSanKe = true;
                $isYkt   = true;
            }

            if (in_array($channel, [11, 18, 43]) && $res['dtype'] == 5) {
                //账号长度大于等于11位的为微信散客
                $isSanKe = true;
            }

        } elseif ($channel == 13) {
            $isSanKe = true;
        }

        if ($member == $aid) {
            $isSanKe = true;
        }

        if ($member == $this->_defaultReseller) {
            $isSanKe = true;
        }

        // 小程序也归集到散客
        if (56 == $channel) {
            $isSanKe = true;
        }

        //通兑换兑换
        if ($channel == OrderChannelConstants::EXCHANGE_COUPON_REDEEM_CHANNEL) {
            $isSanKe = true;
        }

        if ($isSanKe) {
            if (array_key_exists($channel, $this->_resellerMap)) {
                if ($isYkt) {
                    return $this->_resellerMap[22];
                } else {
                    return $this->_resellerMap[$channel];
                }
            } else {
                return $this->_defaultReseller;
            }
        } else {
            return false;
        }
    }

    /**
     * 判断是不是需要往split多加一条记录
     *
     * @param  int  $level  所处的级别：1=第一级，0=既是1级也是末级，-1=最末级，2=中间级别
     * @param  string  $orderTime  下单时间
     * @param  int  $resellerId  购买用户ID
     * @param  int  $sellerId  卖一方
     *
     * @return bool
     */
    private function _needAddSplit($level, $orderTime, $resellerId, $sellerId)
    {
        //如果已经是散客购买或是自供自销的就不添加
        if ($resellerId == $this->_defaultReseller || ($resellerId == $sellerId)) {
            return false;
        }

        //处于最末级
        if ($level == -1 || $level == 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 通过渠道获取对应的散客
     *
     * @param  $channel
     *
     * @return
     */
    private function _getChannelSanke($channel)
    {
        if (array_key_exists($channel, $this->_resellerMap)) {
            return $this->_resellerMap[$channel];
        } else {
            return $this->_defaultReseller;
        }
    }


    /**
     * 判断是否是多景点入园
     * Create by zhangyangzhen
     * Date: 2019/10/29
     * Time: 11:24
     *
     * @param  string  $ordernum  订单号
     *
     * @return bool
     */
    private function _isScenicChk($ordernum)
    {
        $enterLand  = $this->_getEnterLandModel();
        $scenicInfo = $enterLand->getOrderScenicByOrdernum($ordernum);

        if (!empty($scenicInfo) && $scenicInfo['enter_type'] == 3) {
            return true;
        }

        return false;
    }

    /**
     * 获取演出场次开始跟结束时间
     * <AUTHOR>
     * @date 2019-05-16
     *
     * @param  string  $orderNum  订单号
     *
     * @return array
     */
    private function _getSeriesTime($orderNum)
    {
        // 获取演出场次信息
        $orderToolModel  = new \Model\Order\OrderTools();
        $orderDetailInfo = $orderToolModel->getOrderDetailInfo($orderNum, 'orderid, series');
        $series          = @unserialize($orderDetailInfo['series']);
        if (empty($series)) {
            return [];
        }
        // 截取演出场次时间
        $seriesTime = explode(' ', $series[4])[1];
        // 获取演出时间
        //list($seriesTimeBegin, $seriesTimeEnd) = explode('-', $seriesTime);
        list($seriesTimeBegin, $seriesTimeEnd) = \Business\PftShow\ShowManage::showTimeChange($seriesTime);
        $seriesTimeBegin = date('Hi', strtotime($seriesTimeBegin));
        $seriesTimeEnd   = date('Hi', strtotime($seriesTimeEnd));

        return [$seriesTimeBegin, $seriesTimeEnd];
    }

    /**
     * 判断订单是否演出票种
     * <AUTHOR>
     * @date 2019-05-17
     *
     * @param  int  $lid  景区ID
     *
     * @return boolean
     */
    private function _checkSeriesOrder($lid)
    {
        if (empty($lid)) {
            return false;
        }

        if (!$this->_landModel) {
            $this->_landModel = new Land();
        }

        // 获取景区信息
        $res = $this->_landModel->getLandInfo($lid, false, 'p_type');

        return ($res['p_type'] == 'H');
    }



    /**
     * 获取分终端验证完结订单数（因为在订单追踪表中分终端验证未全部验证时完结数量为0）
     *
     * @param $order
     * @param $field
     * @param  array  $extraConf
     *
     * @return array|mixed
     */
    public function branchTerminalFinshNum($orderNum)
    {
        if (empty($this->_subOrderTrackModel)) {
            $this->_subOrderTrackModel = new \Model\Order\SubOrderQuery\SubOrderTrack();
        }
        if (empty($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer();
        }

        //完结订单数
        $finishNum      = 0;
        $lastCheckTrack = $this->_subOrderTrackModel->getTrackList($orderNum, 5, 32, 'left_num');
        $leftNum        = isset($lastCheckTrack['left_num']) ? $lastCheckTrack['left_num'] : 0;

        if ($leftNum > 0) {
            $orderToolModel = new \Model\Order\OrderTools('slave');
            //订单原始信息
            $applyinfo = $orderToolModel->getOrderApplyInfo([$orderNum], 'origin_num, refund_num');
            if (is_array($applyinfo) && !empty($applyinfo)) {
                $finishNum = $applyinfo[0]['origin_num'] - $applyinfo[0]['refund_num'];
            }
        }

        // 转换字符类型
        $finishNum = (int)$finishNum;

        return $finishNum;
    }

    /**
     * 终端验证报表
     * <AUTHOR>
     * @date 2021/11/29
     *
     * @param string $ordernum 订单号
     * @param int $num 验证票数
     * @param int $branchTerminal  分终端号
     * @param  int  $operMember 操作人员
     * @param  int  $time 时间戳
     * @param  array  $msg  扩展信息 json串
     * @param  string  $type  类型 revoke 撤销撤改  check验证
     *
     * @return bool
     */
    public function realtimeTerminalCheckedTask($ordernum, $num, $branchTerminal, $operMember = 0, $time = 0, $type = '', $msg = [])
    {
        try {
            $num = intval($num);

            if (!$ordernum || $num <= 0 || empty($type) || !in_array($type, ['check', 'revoke'])) {
                return false;
            }

            //对应pft_order_track表中的source字段  32=分终端入园   53=分终端入园后的到期自动验证
            $trackSource = isset($msg['trackSource']) ? $msg['trackSource'] : -1;
            //实际渠道
            $readSource = isset($msg['real_source']) ? $msg['real_source'] : -1;
            //主终端号
            $mainTerminal = isset($msg['verify_terminal']) ? $msg['verify_terminal'] : 0;
            //实际终端号
            $realTerminal = isset($msg['real_terminal']) ? $msg['real_terminal'] : 0;
            //撤销撤改传的终端号
            $refundTerminal = isset($msg['refund_terminal']) ? $msg['refund_terminal'] : 0;
            //门票是否在某个分终端上首次入园 1=是 2=否
            $isBranchFirst = isset($msg['is_branch_first']) ? $msg['is_branch_first'] : 0;
            //是否是快速验证过来的
            $isQuickCheck = isset($msg['is_quick_check']) ? $msg['is_quick_check'] : false;

            if ($type == 'revoke') {
                $mainTerminal = $refundTerminal;
            }

            $terminal = '';
            //实际验证来源 验证
            if ($readSource == -1) {
                return false;
            }

            //终端号 验证
            if ($trackSource == 32 || ($trackSource == -1 && $isQuickCheck && !empty($branchTerminal))) {
                if ($branchTerminal == 0) {
                    return false;
                }
                $terminal = $branchTerminal;
            } else {
                if ($mainTerminal == 0) {
                    return false;
                }

                $terminal = $mainTerminal;

                if ($realTerminal > 0 && $mainTerminal != $realTerminal) {
                    $type != 'revoke' && $terminal = $realTerminal;
                }
            }

            if (!$terminal) {
                return false;
            }

            //订单详细
            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member';
            $orderInfo = $this->_getOrder($ordernum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                return false;
            }

            $tid     = $orderInfo['tid'];
            $lid     = $orderInfo['lid'];
            $buyerId = $orderInfo['member'];

            $isAddOrderInfo = true; //是否写入订单信息
            //分终端第一次验证时，才记录金额
            if ($trackSource == 32 && $isBranchFirst != 1) {
                $isAddOrderInfo = false;
            }

            //卖出金额
            $saleMoney = 0;
            //撤销撤改金额
            $revokeMoney = 0;
            if ($isAddOrderInfo) {
                $buyChain = $this->_getOrderChain($ordernum);
                foreach ($buyChain as $chainInfo) {
                    //获取实际购买的金额
                    if (isset($chainInfo['buyerid']) && $chainInfo['buyerid'] == $buyerId) {
                        switch ($type) {
                            case 'check':
                                $saleMoney = $chainInfo['sale_money'];
                                break;
                            case 'revoke':
                                $revokeMoney = $chainInfo['sale_money'];
                                break;
                        }
                    }
                }
            }

            //检票数
            $checkTicket = 0;
            //检票订单数
            $checkOrder = 0;
            //订单数
            $ordersNum = 0;
            //订单票数
            $orderTicket = 0;
            //撤销撤改订单数
            $revokeNum = 0;
            //撤销撤改票数
            $revokeTicket = 0;
            //验证订单信息
            $ordersInfo = [];
            //检票订单信息
            $checkOrdersInfo = [];
            //撤销撤改订单信息
            $revokeOrdersInfo = [];

            //按类型处理数据
            switch ($type) {
                case 'check':
                    $checkTicket      = $num;
                    $checkOrder       = 0;
                    $ordersNum        = $isAddOrderInfo ? 1 : 0;
                    $orderTicket      = $isAddOrderInfo ? $num : 0;
                    $revokeNum        = 0;
                    $revokeTicket     = 0;
                    $order            = json_encode([[$ordernum, $num]]);
                    $ordersInfo       = $isAddOrderInfo ? $order : '{}';
                    $checkOrdersInfo  = '{}';
                    $revokeOrdersInfo = '{}';
                    $reportBiz        = new \Business\Statistics\CreateTerminalCheckedReport('statistic_report');
                    if ($reportBiz->checkOrderNumCount(date("Ymd", $time), $ordernum, true)) {
                        $checkOrder      = 1;
                        $checkOrdersInfo = $order;
                    }

                    break;
                case 'revoke':
                    $checkTicket      = 0;
                    $checkOrder       = 0;
                    $ordersNum        = 0;
                    $orderTicket      = 0;
                    $revokeNum        = 1;
                    $revokeTicket     = $num;
                    $order            = json_encode([[$ordernum, $num]]);
                    $ordersInfo       = '{}';
                    $checkOrdersInfo  = '{}';
                    $revokeOrdersInfo = $order;

                    break;
            }

            $insert[] = [
                'day'                => date("Ymd", $time),
                'lid'                => $lid,
                'tid'                => $tid,
                'terminal'           => $terminal,
                'check_source'       => $readSource,
                'checked_orders'     => $checkOrder,
                'checked_tickets'    => $checkTicket,
                'revoke_tickets'     => $revokeTicket,
                'update_time'        => time(),
                'op_id'              => $operMember,
                'order_num'          => $ordersNum,
                'order_ticket'       => $orderTicket,
                'revoke_num'         => $revokeNum,
                'sale_money'         => $saleMoney * $num,
                'revoke_money'       => $revokeMoney * $num,
                'orders_info'        => $ordersInfo,
                'check_orders_info'  => $checkOrdersInfo,
                'revoke_orders_info' => $revokeOrdersInfo,
            ];

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            $data = true;
            if (!empty($insert)) {
                $res = $this->_statisticsModel->insertDataArr($insert, 'terminal_checked');

                if (!$res) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    throw new Exception($errMsg);
                }
            }

        } catch (Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            pft_log('real_statis', json_encode(['terminal_checked', $msg, $code]));
            $data = false;
        }

        return $data ? true : false;
    }

    /**
     * 入园模型
     * Create by zhangyangzhen
     * Date: 2019/10/29
     * Time: 11:09
     * @return EnterLand
     */
    private function _getEnterLandModel()
    {
        if (!$this->_enterLandModel) {
            $this->_enterLandModel = new EnterLand();
        }

        return $this->_enterLandModel;
    }


    private function _getOrderTrackModel()
    {
        if (!$this->_orderTrackModel) {
            $this->_orderTrackModel = new OrderTrack(true);
        }

        return $this->_orderTrackModel;
    }


    /**
     * 计算手续费
     * Create by xiexy
     * Date: 2022/03/31
     * Time: 11:09
     * @param   int      $fid           计算手续费的用户id
     * @param   array    $serviceInfo   退款手续费数据
     *
     * @return  int
     */
    private function _countServiceMoney(int $fid, array $serviceInfo)
    {
        return $this->_statisticsBiz->countServiceMoney($fid, $serviceInfo);
    }

    /**
     * 跨天操作补全套票主票数据参数解析
     * <AUTHOR>
     * @date   2023/2/11
     *
     * @param  int    $fid                   商户id
     * @param  array  $orderNum              订单号
     * @param  array  $orderInfo             套票主票订单信息
     * @param  array  $packMainOrderChain    套票主票分销链信息
     *
     * @return array
     * @throws
     */
    private function _packMainOrderDecode($fid, $orderNum, $orderInfo = [], $packMainOrderChain = [])
    {
        $mLid     = $orderInfo['lid'] ?? 0;
        $mPid     = $orderInfo['pid'] ?? 0;
        $mTid     = $orderInfo['tid'] ?? 0;
        $mChannel = $orderInfo['ordermode'];

        //扩展信息
        $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

        //子商户id
        $subMerchantId = $extContentInfo['subSid'] ?? 0;

        $mSellerId = $mLevel = $mPaymode = null;

        foreach ($packMainOrderChain as $item) {
            if ($item['orderid'] == $orderNum && $item['sellerid'] == $fid) {
                $mSellerId = $item['buyerid'];
                $mLevel    = $item['level'];
                $mPaymode  = $item['pmode'];
            }
        }

        //通过渠道将散客进行归类
        if ($mSellerId && $fid && in_array($mLevel, [-1, 0])) {
            $tmp = $this->_getSankeId($mSellerId, $fid, $mChannel);

            if ($tmp !== false) {
                //最后一级就是散客的情况(自供自销也算在这里面)
                if ($mLevel == 0) {
                    $mLevel = 1;
                } else {
                    $mLevel = -3;
                }
                $mSellerId = $tmp;
            }
        }

        //站点ID
        $mSiteId = 0;

        //操作人
        $mOperMember = 0;

        return [
            $mSellerId,
            $mPid,
            $mChannel,
            $mPaymode,
            $mOperMember,
            $mSiteId,
            $mLid,
            $mTid,
            $mLevel,
            $subMerchantId,
        ];
    }

    /**
     * 预约实时报表
     * <AUTHOR>
     * @date   2023/11/7
     *
     * @return array
     */
    public function createOrderReserveData($orderNum, $num, $time, $action, $operMember = 0, $flag = 0, $actionIdxs = '')
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_reportRealTaskModel) {
                $this->_reportRealTaskModel = new ReportRealTaskModel();
            }

            if (empty($this->_reserveReport)) {
                $this->_reserveReport = new ReserveReportBiz();
            }


            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status, playtime, ss,salerid';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $tid       = $orderInfo['tid'];
            $playtime  = $orderInfo['playtime'];
            $payStatus = $orderInfo['pay_status'];
            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            //检查下是不是支付前的操作，加票16、修改1如果是支付前是不计入报表
            $inReport = true;
            if (in_array($flag, [1, 16])) {
                //获取订单支付时间戳，用于判断支付前或者支付后的操作， 追踪记录表的时间戳可能与实时报表的时间戳有延迟导致的差异，所以还是需要去取下real_task里面对应的时间戳
                $payTimeListRes = $this->_getTrackOrderListByOrderNumsAndAction([$orderNum]);
                $payTimeListMap = array_column($payTimeListRes, 'insertTime', 'ordernum');
                $trackPayTime   = $payTimeListMap[$orderNum] ?? 0;
                //为了防止时间格式问题导致查询不到支付时间戳，异常直接放行
                $trackPayTimeCheck = false;
                date('Y-m-d H:i:s', strtotime($trackPayTime)) == $trackPayTime && $trackPayTimeCheck = true;
                if ($trackPayTimeCheck) {
                    $trackPayTime = strtotime($trackPayTime);
                    //根据支付时间查询报表中间表task支付插入时间，来对比，避免程序延迟导致对比时间误差
                    $taskPayRes = $this->_reportRealTaskModel->getTasFlagInfo($orderNum, ($trackPayTime - 10),
                        ($trackPayTime + 10), 4);
                    //获取写入时间
                    $taskPayTimeListMap = array_column($taskPayRes, 'insert_time', 'ordernum');
                    $payTime            = $taskPayTimeListMap[$orderNum] ?? 0;
                    if ($payTime && $time && $time < $payTime) {
                        $inReport = false;
                        pft_log('real_time/not_in_report', "订单：$orderNum, 类型：$flag (加票16、修改1), 在支付前，不计入报表");
                    }
                }
            }
            if (!$inReport) {
                //无需计入报表
                return ['code' => $code, 'msg' => $msg];
            }

            //未预约不记录
            $extProduct = json_decode($orderInfo['product_ext'], true);
            if (isset($extProduct['reservationOrder']) && $extProduct['reservationOrder'] == 1 && !isset($extProduct['reservationOperateTime'])) {
                //无需计入报表
                return ['code' => $code, 'msg' => $msg];
            }

            //预约订单号
            $reserveOrderArr = [];
            //改签订单号
            $changeOrderArr = [];

            if ($action == 4) {
                //预约
                $reserveOrderArr[] = $orderNum;
            }  elseif ($action == 5) {
                //改签
                $changeOrderArr[] = $orderNum;
            }

            //日汇总预约报表
            $insertReserve = [];
            if (!in_array($flag, [32, 22])) {
                //预约操作标记
                $reserveMakeMap = [];
                //改签操作标记
                $changeMakeMap    = [];
                $playAndChangeRes = $this->_getTrackOrderListByOrderNumsAndActionArr([$orderNum], [32, 22]);
                foreach ($playAndChangeRes as $info) {
                    if (in_array($info['action'], [22])) {
                        if (!isset($changeMakeMap[strval($info['ordernum'])])) {
                            $changeMakeMap[strval($info['ordernum'])] = [];
                        }
                        $changeMakeMap[strval($info['ordernum'])][] = $info['insertTime'];
                    }
                    if (in_array($info['action'], [32])) {
                        if (!isset($reserveMakeMap[strval($info['ordernum'])])) {
                            $reserveMakeMap[strval($info['ordernum'])] = [];
                        }
                        $reserveMakeMap[strval($info['ordernum'])][] = $info['insertTime'];
                    }
                }

                //有效期预处理
                $playDateMap = $this->_reserveReport->getPlayDateAndActionTime($reserveMakeMap, $changeMakeMap);

                $applyDid = 0;
                $mainTid  = 0;
                $mainType = '';

                if ($channel == 23) {
                    //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                    $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
                    if (!empty($packRes)) {
                        if ($packRes['pack_order'] == 1) {
                            //主票
                            $mainTid = $tid;
                        } else {
                            //子票
                            $mainOrder     = $packRes['pack_order'];
                            $mainOrderInfo = $this->_queryModel->getOrderInfo($mainOrder, 'lid,pid,tid,ordermode');
                            $mainTid       = $mainOrderInfo['tid'] ?? 0;
                        }
                        //主订单号的票类信息
                        $javaApi    = new \Business\CommodityCenter\Ticket();
                        $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                        $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                        $mainType   = $ticketInfo['land']['p_type'] ?? '';
                    }
                }
                $tmp = $this->_getOrderChain($orderNum);
                if (empty($tmp)) {
                    throw new Exception("没找到分销链信息");
                }

                //获取优惠信息
                $couponList   = [];
                $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
                foreach ($onSaleRecord as $item) {
                    $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
                }

                //获取本次操作积分抵扣金额
                $discountDetail           = $this->_getOrderPointsDetail($orderNum, $extContentInfo, $flag,
                    $actionIdxs);
                $discountAmount           = $discountDetail['discountAmount'];              //游客优惠
                $settlementDiscountAmount = $discountDetail['settlementDiscountAmount'];    //分销优惠

                //组装优惠信息
                $discountInfo[$orderNum][$flag][$orderInfo['aid']][$orderInfo['member']] = [
                    \Business\Statistics\CreateReportBase::DISCOUNT            => $discountAmount,
                    \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => $settlementDiscountAmount,
                ];

                for ($i = 0; $i < count($tmp); $i++) {
                    $item     = $tmp[$i];
                    $fid      = $item['sellerid'];
                    $sellerId = $item['buyerid'];
                    //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                    $level              = $item['level'];
                    $saleMoney          = $item['sale_money'];
                    $paymode            = $item['pmode'];
                    $settlementDiscount = $item['settlement_discount'] ?? 0; //分销优惠金额

                    //操作用户
                    $operMember = $operMember ?: $item['buyerid'];

                    if (isset($couponList[$orderNum][$fid][$sellerId])) {
                        //优惠金额
                        $eMoney = $couponList[$orderNum][$fid][$sellerId];
                    } else {
                        $eMoney = 0;
                    }

                    //计算游客优惠使用情况
                    $discountMoney = 0;
                    if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT])) {
                        $discountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT];
                    }

                    //计算分销优惠使用情况
                    $settlementDiscountMoney = 0;
                    if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT])) {
                        $settlementDiscountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT];
                    }

                    //通过渠道将散客进行归类
                    if (in_array($level, [-1, 0])) {
                        $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                        if ($tmpId !== false) {
                            //最后一级就是散客的情况(自供自销也算在这里面)
                            if ($level == 0) {
                                $level = 1;
                            } else {
                                $level = -3;
                            }
                            $sellerId = $tmpId;

                            //预售券兑换的订单，末级销售金额为0
                            if ($this->_checkSaleMoneyZero($channel, $paymode) && !$this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid)) {
                                $saleMoney = 0;
                            }
                        }
                    }

                    //预订票数
                    $bookTicketNum = 0;
                    //预订 票 销售金额
                    $bookSaleMoney = 0;
                    //取消票数
                    $cancelTicketNum = 0;
                    //取消 票 出售金额
                    $cancelSaleMoney = 0;
                    //撤销票数
                    $revokeTicketNum = 0;
                    //撤销 票 出售金额
                    $revokeSaleMoney = 0;
                    //验证信息
                    $checkOrderInfo = [];
                    //取消信息
                    $cancelOrderInfo = [];
                    //撤销信息
                    $revokeOrderInfo = [];

                    if ($action == 1) {
                        //预定支付
                        $bookTicketNum    = $num;
                        $bookSaleMoney    = $bookTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                        $checkOrderInfo[] = [
                            $orderNum,
                            $bookTicketNum,
                            $discountMoney,
                            $settlementDiscountMoney ?: $settlementDiscount,
                        ];
                    } elseif ($action == 2) {
                        //取消修改
                        $cancelTicketNum   = $num;
                        $cancelSaleMoney   = $cancelTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                        $cancelOrderInfo[] = [
                            $orderNum,
                            $cancelTicketNum,
                            $discountMoney,
                            $settlementDiscountMoney ?: $settlementDiscount,
                        ];
                    } elseif ($action == 3) {
                        //撤销撤改
                        $revokeTicketNum   = $num;
                        $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                        $revokeOrderInfo[] = [
                            $orderNum,
                            $revokeTicketNum,
                            $discountMoney,
                            $settlementDiscountMoney ?: $settlementDiscount,
                        ];
                    } else {
                        break;
                    }

                    //特殊处理
                    $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                    if ($splitTmp) {
                        //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                        $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                        $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                        $splitData = [
                            'orderid'             => $orderNum,
                            'sellerid'            => $sellerId,
                            'buyerid'             => $this->_getChannelSanke($channel),
                            'level'               => -3, //非0/-1
                            'cost_money'          => $saleMoney,
                            'sale_money'          => $needMoney, //默认使用零售价的钱
                            'pmode'               => $paymode,
                            'settlement_discount' => $settlementDiscountMoney + $discountMoney,  //分销优惠金额
                        ];

                        //如果需要补全一条报表记录，且是预售券兑换订单，末级销售金额为0
                        if ($this->_checkSaleMoneyZero($channel, $paymode)) {
                            $splitData['sale_money'] = 0;
                        }

                        $tmp[] = $splitData;
                    }

                    //黑名单判断必须放在链路补全后，避免数据缺失
                    if ($this->isInBlacklist($fid)) {
                        continue;
                    }

                    //fid是套票发布人 才会记录子订单主票id
                    $mainTidNew  = $mainTid;
                    $mainTypeNew = $mainType;
                    if ($applyDid != $fid) {
                        $mainTidNew  = 0;
                        $mainTypeNew = '';
                    }

                    $playDate = 0;
                    //存在多个游玩日期，匹配对应区间的游玩日期
                    if (isset($playDateMap[$orderNum])) {
                        foreach ($playDateMap[$orderNum] as $timeStr) {
                            if ($timeStr['begin'] == 0 && $time <= $timeStr['end']) {
                                $playDate = $timeStr['play_date'];
                                break;
                            }
                            if ($timeStr['end'] == 0 && $time >= $timeStr['begin']) {
                                $playDate = $timeStr['play_date'];
                                break;
                            }
                            if ($timeStr['begin'] != 0 && $timeStr['end'] != 0 && $time <= $timeStr['end'] && $time >= $timeStr['begin']) {
                                $playDate = $timeStr['play_date'];
                                break;
                            }
                        }

                        if (empty($playDate)) {
                            continue;
                        }
                    }

                    //游玩日期
                    $playtime = str_replace('-', '', $playtime);

                    $playtime = empty($playDate) ? $playtime : $playDate;

                    //预约日期不存在，跳过
                    if (empty($playtime)) {
                        continue;
                    }

                    $insertReserve[] = [
                        'fid'                => $fid,
                        'reseller_id'        => $sellerId,
                        'order_date'         => date('Ymd', $time),
                        'play_date'          => $playtime,
                        'lid'                => $lid,
                        'tid'                => $tid,
                        'operate_id'         => $operMember,
                        'pay_way'            => $paymode,
                        'channel'            => $channel,
                        'main_tid'           => $mainTidNew,
                        'order_ticket'       => $bookTicketNum,
                        'cancel_ticket'      => $cancelTicketNum,
                        'revoke_ticket'      => $revokeTicketNum,
                        'sale_money'         => $bookSaleMoney,
                        'cancel_sale_money'  => $cancelSaleMoney,
                        'revoke_sale_money'  => $revokeSaleMoney,
                        'orders_info'        => json_encode($checkOrderInfo),
                        'cancel_orders_info' => json_encode($cancelOrderInfo),
                        'revoke_orders_info' => json_encode($revokeOrderInfo),
                        'update_time'        => $time,
                        'main_type'          => $mainTypeNew,
                    ];
                }
            }

            //预约数据
            if (!empty($reserveOrderArr)) {
                $insertReserveOrder = $this->_reserveReport->getReserveAndChangeOrderDetail(array_values(array_unique($reserveOrderArr)),
                    date('Y-m-d 00:00:00', $time), date('Y-m-d 23:59:59', $time));
                if (!empty($insertReserveOrder)) {
                    $insertReserve = array_merge($insertReserve, $insertReserveOrder);
                }
            }

            //改签数据
            if (!empty($changeOrderArr)) {
                $insertReserveChange    = $this->_reserveReport->getReserveAndChangeOrderDetail(array_values(array_unique($changeOrderArr)),
                    date('Y-m-d 00:00:00', $time), date('Y-m-d 23:59:59', $time), 22);
                if (!empty($insertReserveChange)) {
                    $insertReserve = array_merge($insertReserve, $insertReserveChange);
                }
            }

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            if (!empty($insertReserve)) {
                $resHour = $this->_statisticsModel->insertDataArr($insertReserve, 'order_reserve_daily');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$insertReserve, $errMsg]));
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }

    /**
     * 根据渠道和支付方式判断是否是预售券兑换订单
     * <AUTHOR>
     * @date   2024/02/18
     *
     * @param $channel
     * @param $payMode
     *
     * @return bool
     */
    private function _isExchangeCouponRedeemOrders($channel, $payMode)
    {
        return ExchangeCouponProductBiz::isExchangeCouponRedeemOrders($channel, $payMode);
    }

    /**
     * 验证卖出金额是否为0元
     * <AUTHOR>
     * @date   2024/06/06
     *
     * @param $channel
     * @param $payMode
     *
     * @return bool
     */
    private function _checkSaleMoneyZero($channel, $payMode)
    {
        //预售券权益支付
        $checkExchangeCoupon = ExchangeCouponProductBiz::isExchangeCouponRedeemOrders($channel, $payMode);
        //预存码权益支付
        $checkPreDepositCode = $payMode == OrderPayModeConstants::PRE_DEPOSIT_CODE_PAY_TYPE;

        if ($checkExchangeCoupon || $checkPreDepositCode) {
            return true;
        }

        return false;
    }

    /**
     * 演出预约实时报表
     *
     * @param  string  $orderNum  订单号
     * @param  int  $num  操作数量
     * @param  string  $time  操作时间
     * @param  int  $action  变更状态: 0=下单,1=修改,2=取消,3=出票,4=支付,验证,撤销,7=撤改,8=重打印,9=离线订单下载,10=处理退票申请,11=提交退票申请,12=过期,13=同意退票申请,14=拒绝退票申请,15=核销,16=订单加票,17=完结
     * @param  int  $operMember  操作人id
     * @param  int  $flag  实时报表操作类型 0=下单,1=修改,2=取消,3=出票,4=支付,验证,撤销,7=撤改,8=重打印,9=离线订单下载,10=处理退票申请,11=提交退票申请,12=过期,13=同意退票申请,14=拒绝退票申请,15=核销,16=订单加票,17=完结
     * @param  string  $actionIdxs  优惠码索引
     *
     * @return array
     */
    public function createShowOrderReserveData($orderNum, $num, $time, $action, $operMember = 0, $flag = 0, $actionIdxs = '')
    {
        $code = true;
        $msg  = '';
        try {
            if (!$this->_buyChainModel) {
                $this->_buyChainModel = new BuyChain();
            }

            if (!$this->_queryModel) {
                $this->_queryModel = new OrderTools();
            }

            if (!$this->_ticketModel) {
                $this->_ticketModel = new Ticket();
            }

            if (!$this->_subOrderSplitModel) {
                $this->_subOrderSplitModel = new SubOrderSplit();
            }

            if (!$this->_reportRealTaskModel) {
                $this->_reportRealTaskModel = new ReportRealTaskModel();
            }

            if (empty($this->_showReserveReport)) {
                $this->_showReserveReport = new ShowReserveReportBiz();
            }


            $field     = 'ordernum, ordertime, ordermode, lid, ss.pid, ss.tid, member, pay_status, playtime, ss,salerid';
            $orderInfo = $this->_getOrder($orderNum, $field, false, false, 'lprice');

            if (empty($orderInfo)) {
                throw new Exception("没找到订单信息");
            }

            $channel   = $orderInfo['ordermode'];
            $lid       = $orderInfo['lid'];
            $tid       = $orderInfo['tid'];
            $playtime  = $orderInfo['playtime'];
            $payStatus = $orderInfo['pay_status'];
            $ptype     = $orderInfo['product_type'];

            //扩展信息
            $extContentInfo = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];

            if ($payStatus == 2) {
                throw new Exception("订单未支付");
            }

            //非演出的 不往下走
            if ($ptype != 'H') {
                //无需计入报表
                return ['code' => $code, 'msg' => '非演出报表，无需计入报表'];
            }

            //检查下是不是支付前的操作，加票16、修改1如果是支付前是不计入报表
            $inReport = true;
            //判断实时报表操作类型 修改/加票的情况 需要判断支付前的操作  支付前的加减票无需记录到报表中
            if (in_array($flag, [1, 16])) {
                //获取订单支付时间戳，用于判断支付前或者支付后的操作， 追踪记录表的时间戳可能与实时报表的时间戳有延迟导致的差异，所以还是需要去取下real_task里面对应的时间戳
                $payTimeListRes = $this->_getTrackOrderListByOrderNumsAndAction([$orderNum]);
                $payTimeListMap = array_column($payTimeListRes, 'insertTime', 'ordernum');
                $trackPayTime   = $payTimeListMap[$orderNum] ?? 0;
                //为了防止时间格式问题导致查询不到支付时间戳，异常直接放行
                $trackPayTimeCheck = false;
                date('Y-m-d H:i:s', strtotime($trackPayTime)) == $trackPayTime && $trackPayTimeCheck = true;
                if ($trackPayTimeCheck) {
                    $trackPayTime = strtotime($trackPayTime);
                    //根据支付时间查询报表中间表task支付插入时间，来对比，避免程序延迟导致对比时间误差
                    $taskPayRes = $this->_reportRealTaskModel->getTasFlagInfo($orderNum, ($trackPayTime - 10),
                        ($trackPayTime + 10), 4);
                    //获取写入时间
                    $taskPayTimeListMap = array_column($taskPayRes, 'insert_time', 'ordernum');
                    $payTime            = $taskPayTimeListMap[$orderNum] ?? 0;
                    if ($payTime && $time && $time < $payTime) {
                        $inReport = false;
                        $tmpMsg   = "订单：$orderNum, 类型：$flag (加票16、修改1), 在支付前，不计入报表";
                        pft_log('real_time/not_in_report', "订单：$orderNum, 类型：$flag (加票16、修改1), 在支付前，不计入报表");
                    }
                }
            }
            if (!$inReport) {
                //无需计入报表
                return ['code' => $code, 'msg' => $tmpMsg ?? '无需计入报表'];
            }

            //未预约不记录
            $extProduct = json_decode($orderInfo['product_ext'], true);
            if (isset($extProduct['reservationOrder']) && $extProduct['reservationOrder'] == 1 && !isset($extProduct['reservationOperateTime'])) {
                //无需计入报表
                return ['code' => $code, 'msg' => '未预约订单，无需计入报表'];
            }

            //因为上面获取订单信息有缓存 需要重新获取订单的演出场次信息
            $orderDetail = $this->_queryModel->getOrderDetailInfo($orderNum);
            if (empty($orderDetail['series'])) {
                //没数据说明还没预约 无需计入报表
                return ['code' => $code, 'msg' => '未预约订单，无需计入报表'];
            }

            $series  = unserialize($orderDetail['series']);
            $venueId = $series[0] ?: 0;
            $roundId = isset($series[10]) && $series[10] ? $series[10] : ($series[1] ?: 0);
            $zoneId  = $series[2] ?: 0;

            //预约订单号
            $showReserveOrderArr = [];
            //改签订单号
            $showChangeOrderArr = [];

            if ($action == 4) {
                //预约
                $showReserveOrderArr[] = $orderNum;
            }  elseif ($action == 5) {
                //改签
                $showChangeOrderArr[] = $orderNum;
            }

            //日汇总预约报表
            $insertReserve = [];
            if (!in_array($flag, [32, 22])) {
                //预约操作标记
                $reserveMakeMap = [];
                //改签操作标记
                $changeMakeMap    = [];
                $playAndChangeRes = $this->_getTrackOrderListByOrderNumsAndActionArr([$orderNum], [32, 22]);
                foreach ($playAndChangeRes as $info) {
                    if (in_array($info['action'], [22])) {
                        if (!isset($changeMakeMap[strval($info['ordernum'])])) {
                            $changeMakeMap[strval($info['ordernum'])] = [];
                        }
                        $changeMakeMap[strval($info['ordernum'])][] = $info['insertTime'];
                    }
                    if (in_array($info['action'], [32])) {
                        if (!isset($reserveMakeMap[strval($info['ordernum'])])) {
                            $reserveMakeMap[strval($info['ordernum'])] = [];
                        }
                        $reserveMakeMap[strval($info['ordernum'])][] = $info['insertTime'];
                    }
                }

                //有效期预处理
                $playDateMap = $this->_showReserveReport->getPlayDateAndActionTime($reserveMakeMap, $changeMakeMap);

                $applyDid = 0;
                $mainTid  = 0;
                $mainType = '';

                if ($channel == 23) {
                    //如果是子票下单 需要判断主票的供应商是不是当前层级的sell_id 是的话 需要过滤子票
                    $packRes = $this->_queryModel->getPackInfoBySingleOrder($orderNum);
                    if (!empty($packRes)) {
                        if ($packRes['pack_order'] == 1) {
                            //主票
                            $mainTid = $tid;
                        } else {
                            //子票
                            $mainOrder     = $packRes['pack_order'];
                            $mainOrderInfo = $this->_queryModel->getOrderInfo($mainOrder, 'lid,pid,tid,ordermode');
                            $mainTid       = $mainOrderInfo['tid'] ?? 0;
                        }
                        //主订单号的票类信息
                        $javaApi    = new \Business\CommodityCenter\Ticket();
                        $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'apply_did', '', 'p_type');
                        $applyDid   = $ticketInfo['ticket']['apply_did'] ?? 0;
                        $mainType   = $ticketInfo['land']['p_type'] ?? '';
                    }
                }
                $tmp = $this->_getOrderChain($orderNum);
                if (empty($tmp)) {
                    throw new Exception("没找到分销链信息");
                }

                //获取优惠信息
                $couponList   = [];
                $onSaleRecord = $this->_getOnSaleRecord([$orderNum]);
                foreach ($onSaleRecord as $item) {
                    $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
                }

                //获取本次操作积分抵扣金额
                $discountDetail           = $this->_getOrderPointsDetail($orderNum, $extContentInfo, $flag,
                    $actionIdxs);
                $discountAmount           = $discountDetail['discountAmount'];              //游客优惠
                $settlementDiscountAmount = $discountDetail['settlementDiscountAmount'];    //分销优惠

                //组装优惠信息
                $discountInfo[$orderNum][$flag][$orderInfo['aid']][$orderInfo['member']] = [
                    \Business\Statistics\CreateReportBase::DISCOUNT            => $discountAmount,
                    \Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT => $settlementDiscountAmount,
                ];

                for ($i = 0; $i < count($tmp); $i++) {
                    $item     = $tmp[$i];
                    $fid      = $item['sellerid'];
                    $sellerId = $item['buyerid'];
                    //此处多复制一份 用以判断退票手续费 否则通过渠道将散客进行归类后 无法获取到退票手续费
                    $level              = $item['level'];
                    $saleMoney          = $item['sale_money'];
                    $paymode            = $item['pmode'];
                    $settlementDiscount = $item['settlement_discount'] ?? 0; //分销优惠金额

                    //操作用户
                    $operMember = $operMember ?: $item['buyerid'];

                    if (isset($couponList[$orderNum][$fid][$sellerId])) {
                        //优惠金额
                        $eMoney = $couponList[$orderNum][$fid][$sellerId];
                    } else {
                        $eMoney = 0;
                    }

                    //计算游客优惠使用情况
                    $discountMoney = 0;
                    if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT])) {
                        $discountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::DISCOUNT];
                    }

                    //计算分销优惠使用情况
                    $settlementDiscountMoney = 0;
                    if (isset($discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT])) {
                        $settlementDiscountMoney = $discountInfo[$orderNum][$flag][$fid][$sellerId][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT];
                    }

                    //预订票数
                    $bookTicketNum = 0;
                    //预订 票 销售金额
                    $bookSaleMoney = 0;
                    //取消票数
                    $cancelTicketNum = 0;
                    //取消 票 出售金额
                    $cancelSaleMoney = 0;
                    //撤销票数
                    $revokeTicketNum = 0;
                    //撤销 票 出售金额
                    $revokeSaleMoney = 0;
                    //验证信息
                    $checkOrderInfo = [];
                    //取消信息
                    $cancelOrderInfo = [];
                    //撤销信息
                    $revokeOrderInfo = [];

                    if ($action == 1) {
                        //预定支付
                        $bookTicketNum    = $num;
                        $bookSaleMoney    = $bookTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                        $checkOrderInfo[] = [
                            $orderNum,
                            $bookTicketNum,
                            $discountMoney,
                            $settlementDiscountMoney ?: $settlementDiscount,
                        ];
                    } elseif ($action == 2) {
                        //取消修改
                        $cancelTicketNum   = $num;
                        $cancelSaleMoney   = $cancelTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                        $cancelOrderInfo[] = [
                            $orderNum,
                            $cancelTicketNum,
                            $discountMoney,
                            $settlementDiscountMoney ?: $settlementDiscount,
                        ];
                    } elseif ($action == 3) {
                        //撤销撤改
                        $revokeTicketNum   = $num;
                        $revokeSaleMoney   = $revokeTicketNum * $saleMoney - $eMoney - $discountMoney - $settlementDiscountMoney;
                        $revokeOrderInfo[] = [
                            $orderNum,
                            $revokeTicketNum,
                            $discountMoney,
                            $settlementDiscountMoney ?: $settlementDiscount,
                        ];
                    } else {
                        break;
                    }

                    //通过渠道将散客进行归类
                    if (in_array($level, [-1, 0])) {
                        $tmpId = $this->_getSankeId($sellerId, $fid, $channel);

                        if ($tmpId !== false) {
                            //最后一级就是散客的情况(自供自销也算在这里面)
                            if ($level == 0) {
                                $level = 1;
                            } else {
                                $level = -3;
                            }
                            $sellerId = $tmpId;
                        }
                    }

                    //特殊处理
                    $splitTmp = $this->_needAddSplit($level, $orderInfo['ordertime'], $sellerId, $fid);

                    if ($splitTmp) {
                        //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                        $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                        $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                        $splitData = [
                            'orderid'             => $orderNum,
                            'sellerid'            => $sellerId,
                            'buyerid'             => $this->_getChannelSanke($channel),
                            'level'               => -3, //非0/-1
                            'cost_money'          => $saleMoney,
                            'sale_money'          => $needMoney, //默认使用零售价的钱
                            'pmode'               => $paymode,
                            'settlement_discount' => $settlementDiscountMoney + $discountMoney,  //分销优惠金额
                        ];

                        $tmp[] = $splitData;
                    }

                    //fid是套票发布人 才会记录子订单主票id
                    $mainTidNew  = $mainTid;
                    $mainTypeNew = $mainType;
                    if ($applyDid != $fid) {
                        $mainTidNew  = 0;
                        $mainTypeNew = '';
                    }

                    $playDate = 0;
                    //存在多个游玩日期，匹配对应区间的游玩日期
                    if (isset($playDateMap[$orderNum])) {
                        foreach ($playDateMap[$orderNum] as $timeStr) {
                            if ($timeStr['begin'] == 0 && $time <= $timeStr['end']) {
                                $playDate = $timeStr['play_date'];
                                break;
                            }
                            if ($timeStr['end'] == 0 && $time >= $timeStr['begin']) {
                                $playDate = $timeStr['play_date'];
                                break;
                            }
                            if ($timeStr['begin'] != 0 && $timeStr['end'] != 0 && $time <= $timeStr['end'] && $time >= $timeStr['begin']) {
                                $playDate = $timeStr['play_date'];
                                break;
                            }
                        }

                        if (empty($playDate)) {
                            continue;
                        }
                    }

                    //游玩日期
                    $playtime = str_replace('-', '', $playtime);

                    $playtime = empty($playDate) ? $playtime : $playDate;

                    //预约日期不存在，跳过
                    if (empty($playtime)) {
                        continue;
                    }

                    $insertReserve[] = [
                        'fid'                => $fid,
                        'reseller_id'        => $sellerId,
                        'order_date'         => date('Ymd', $time),
                        'play_date'          => $playtime,
                        'lid'                => $lid,
                        'tid'                => $tid,
                        'venue_id'           => $venueId,
                        'zone_id'            => $zoneId,
                        'round_id'           => $roundId,
                        'operate_id'         => $operMember,
                        'pay_way'            => $paymode,
                        'channel'            => $channel,
                        'main_tid'           => $mainTidNew,
                        'order_ticket'       => $bookTicketNum,
                        'cancel_ticket'      => $cancelTicketNum,
                        'revoke_ticket'      => $revokeTicketNum,
                        'sale_money'         => $bookSaleMoney,
                        'cancel_sale_money'  => $cancelSaleMoney,
                        'revoke_sale_money'  => $revokeSaleMoney,
                        'orders_info'        => json_encode($checkOrderInfo),
                        'cancel_orders_info' => json_encode($cancelOrderInfo),
                        'revoke_orders_info' => json_encode($revokeOrderInfo),
                        'update_time'        => $time,
                        'main_type'          => $mainTypeNew,
                    ];
                }
            }

            //预约数据
            if (!empty($showReserveOrderArr)) {
                $insertReserveOrder = $this->_showReserveReport->getReserveAndChangeOrderDetail(array_values(array_unique($showReserveOrderArr)),
                    date('Y-m-d 00:00:00', $time), date('Y-m-d 23:59:59', $time));
                if (!empty($insertReserveOrder)) {
                    $insertReserve = array_merge($insertReserve, $insertReserveOrder);
                }
            }

            //改签数据
            if (!empty($showChangeOrderArr)) {
                $insertReserveChange    = $this->_showReserveReport->getReserveAndChangeOrderDetail(array_values(array_unique($showChangeOrderArr)),
                    date('Y-m-d 00:00:00', $time), date('Y-m-d 23:59:59', $time), 22);
                if (!empty($insertReserveChange)) {
                    $insertReserve = array_merge($insertReserve, $insertReserveChange);
                }
            }

            if (!$this->_statisticsModel) {
                $this->_statisticsModel = new Statistics();
            }

            if (!empty($insertReserve)) {
                $resHour = $this->_statisticsModel->insertDataArr($insertReserve, 'show_order_reserve_daily');
                if (!$resHour) {
                    $errMsg = $this->_statisticsModel->getDbError();
                    pft_log('real_statis', json_encode([$insertReserve, $errMsg]));
                }
            }

        } catch (Exception $e) {
            $code = false;
            $msg  = $e->getMessage();
        }

        return ['code' => $code, 'msg' => $msg];
    }
}
