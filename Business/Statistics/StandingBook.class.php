<?php
/**
 * 台账报表业务处理
 * <AUTHOR>
 * @date   2023/3/2
 */

namespace Business\Statistics;

use Business\Base;
use Business\CommodityCenter\Ticket as TicketBiz;
use Business\Common\DictPayModeService;
use Business\Report\NotChecked as NotCheckedBiz;
use Model\Report\StandingBook as StandingBookModel;
use Business\Statistics\statistics as StatisticsBiz;

class StandingBook extends Base
{
    public $sid; //主账号id
    public $memberId; //当前账户id
    public $operateId; //操作员id
    public $dtype; //当前账号类型

    //报表模板数据模型
    private $_standingBookModel = null;

    //销售渠道信息
    private $_channelInfo = null;
    //支付方式
    private $_paywayInfo = null;

    //标题最大字数
    const TITLE_MAX = 20;

    //最大分组
    const GROUP_MAX = 10;

    //单个分组最大票数
    const GROUP_TID_MAX = 100;

    //台账报表类型
    const REPORT_TYPE_MAP      = [
        'overall'   => '汇总表',
        'sale'      => '预售表',
        'check'     => '验证表',
        'cancel'    => '取消表',
        'revoke'    => '撤销撤改表',
        'not_check' => '未验证报表',
    ];
    const REPORT_TYPE_OVERALL  = 'overall';
    const REPORT_TYPE_SALE     = 'sale';
    const REPORT_TYPE_CHECK    = 'check';
    const REPORT_TYPE_CANCEL   = 'cancel';
    const REPORT_TYPE_REVOKE   = 'revoke';
    const REPORT_TYPE_NOTCHECK = 'not_check';

    //统计维度
    const REPORT_ITEM_VALUE = [
        'pay_way' => '支付方式',
        'channel' => '销售渠道',
    ];

    //允许记录报表的用户
    //本地、内网
    const REPORT_ALLOW_USER_IDS_LOCAL = [3385, 6970];
    //dev预生产
    const REPORT_ALLOW_USER_IDS_TEST = [
        3385,//123624慢慢
        6970,//dev123933慢慢
        3203186,//李哈哈改名了
        3247264,//1级分销链zhangh
        3247272,//2级分销链
        3247279,//3级分销链
        3247286,//4级分销链
        3247293,//5级分销链
        3207191,//玩聚DEV景区测试账号
    ];
    //生产、灰度
    const REPORT_ALLOW_USER_IDS_PRO = [
        13769393,//壶口瀑布
        3385,//慢慢624
        6970,//123933慢慢
        10319429,//慢慢测试mmtest2
        10319430,//mmtest3
        10319433,//mmtest4
        10319438,//mmtest5
        10319441,//mmtest6
        27098015,//1级分销链（未验证专用）
        27098021,//2级分销链（未验证专用）
        27098027,//3级分销链（未验证专用）
        27098032,//4级分销链（未验证专用）
        27098041,//5级分销链（未验证专用）
        13702234,//玩聚生产测试账号
        27097633,//未验证报表专用
        6889,//小飘12游乐园
        7855157,//源脉温泉
        29821585,//瘦西湖
        2006706,//三亚C秀
    ];

    public function __construct()
    {
        $channelInfo        = load_config('order_mode');
        $channelInfo[0]     = '正常分销商下单';
        $channelInfo[1]     = '普通用户支付';
        $channelInfo[2]     = '用户手机支付';
        $this->_channelInfo = $channelInfo;

        $this->_paywayInfo = load_config('payway_list');

        $this->_standingBookModel = new StandingBookModel();
    }

    /**
     * 根据类型查询模板列表
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  string  $type    台账报表类型
     *
     * @return array
     */
    public function getTemplateList($type)
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            $this->_checkTemplateReportType($type);

            //获取全部数据
            $res = $this->_standingBookModel->getTemplate($this->sid, $this->memberId, $type);
            if (empty($res)) {
                throw new \Exception('无数据', 200);
            }

            //商户自定义收款方式
            $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($this->sid);

            //数据处理
            $data = [];
            foreach ($res as $tmp) {
                $data[] = $this->_handleTemplateFormat($tmp, true);
            }

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取模板详情
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  int  $templateId    模板id
     *
     * @return array
     */
    public function getTemplateInfo($templateId)
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            if (!$templateId) {
                throw new \Exception('模板id缺失', 203);
            }

            //获取数据
            $res = $this->_standingBookModel->getTemplateInfo($templateId, $this->sid, $this->memberId);
            if (empty($res)) {
                throw new \Exception('无数据', 200);
            }

            //商户自定义收款方式
            $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($this->sid);

            //数据处理
            $data = $this->_handleTemplateFormat($res);

            //加下报表类型
            $data['report_type'] = $res['report_type'];

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 添加报表模板
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  string  $title          模板名称
     * @param  string  $reportType     报表类型
     * @param  int     $showPrice      是否展示票价 1是0否
     * @param  array   $channel        展示渠道
     * @param  array   $payWay         展示支付方式
     * @param  array   $groupTicket    组合票类
     *
     * @return array
     */
    public function addTemplate($title, $reportType, $showPrice, $channel = [], $payWay = [], $groupTicket = [])
    {
        $code = 200;
        $msg  = '新增成功';
        $data = [];
        try {
            $this->_checkTemplateReportType($reportType);

            //验证名称
            $this->_checkTemplateName($title);

            //验证统计维度
            $this->_checkItemValue($reportType, $channel, $payWay);

            //统计维度
            $itemValue = $this->_enCodeItemValue($reportType, $showPrice, $channel, $payWay);

            //组合票验证
            $this->_checkGroupTicket($groupTicket);

            //获取数据
            $data = $this->_standingBookModel->addTemplateConfig($this->sid, $this->memberId, $title, $reportType,
                $itemValue, $groupTicket);
            if (!$data) {
                throw new \Exception('新增失败', 203);
            }

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 编辑报表模板
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  int     $templateId     模板id
     * @param  string  $title          模板名称
     * @param  int     $showPrice      是否展示票价 1是0否
     * @param  array   $channel        展示渠道
     * @param  array   $payWay         展示支付方式
     * @param  array   $groupTicket    组合票类
     *
     * @return array
     */
    public function editTemplate($templateId, $title, $showPrice, $channel = [], $payWay = [], $groupTicket = [])
    {
        $code = 200;
        $msg  = '编辑成功';
        $data = [];
        try {
            //根据模板id验证模板是否存在
            $info = $this->_checkTemplateById($templateId);

            //获取报表类型
            $reportType = $info['report_type'];

            //验证名称
            $this->_checkTemplateName($title);

            //验证统计维度
            $this->_checkItemValue($reportType, $channel, $payWay);

            //统计维度
            $itemValue = $this->_enCodeItemValue($reportType, $showPrice, $channel, $payWay);

            //组合票验证
            $this->_checkGroupTicket($groupTicket);

            //获取数据
            $data = $this->_standingBookModel->editTemplateConfig($templateId, $this->sid, $this->memberId, $title,
                $itemValue, $groupTicket);
            if (!$data) {
                throw new \Exception('编辑失败', 203);
            }

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 删除模板
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  int  $templateId    模板id
     *
     * @return array
     */
    public function delTemplate($templateId)
    {
        $code = 200;
        $msg  = '删除成功';
        $data = [];
        try {
            //根据模板id验证模板是否存在
            $this->_checkTemplateById($templateId);

            //删除
            $res = $this->_standingBookModel->delTemplateConfig($templateId, $this->sid, $this->memberId);
            if (!$res) {
                throw new \Exception('删除失败', 400);
            }

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取汇总列表
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  string  $startDt       开始时间
     * @param  string  $endDt         结束时间
     * @param  int     $templateId    模板id
     *
     * @return array
     */
    public function getOverallList($startDt, $endDt, $templateId)
    {
        $code = 200;
        $msg  = '成功';
        $data = [];
        try {
            //根据模板id验证模板是否存在
            $templateData = $this->_checkTemplateById($templateId, self::REPORT_TYPE_OVERALL);

            //商户自定义收款方式
            $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($this->sid);

            //报表模板数据处理
            $template = $this->_handleTemplateFormat($templateData);

            //是否展示票价
            $showPrice = $template['show_price'];

            //销售渠道
            $channel = $template['channel'];
            //支付方式
            $payWay = $template['pay_way'];

            //分组门票信息
            $groupArr = $template['group'];
            //获取分组里面的门票id
            $tidArr = $this->getTemplateGroupTids($groupArr);

            //查询条件
            $filter = $this->_handleQueryParams($startDt, $endDt, $channel, $payWay, $tidArr);

            //未验证报表
            $firstDt     = date("Y-m-d", (strtotime("$startDt 00:00:00") - 1));
            $lastDt      = date("Y-m-d", strtotime("$endDt 23:59:59"));
            $notCheckBiz = new NotCheckedBiz();
            //期初未验证
            $firstRes          = $notCheckBiz->getLedgerListSummary($this->sid, $firstDt, $tidArr, [], [], 1,
                count($tidArr));
            $firstNotCheckList = $firstRes['data']['list'] ?? [];
            //期末未验证
            $lastRes          = $notCheckBiz->getLedgerListSummary($this->sid, $lastDt, $tidArr, [], [], 1,
                count($tidArr));
            $lastNotCheckList = $lastRes['data']['list'] ?? [];

            //本期预售、取消
            $saleAndCancelList = $this->_standingBookModel->getOrderList($filter, 'tid');
            //本期验证、撤销撤改
            $checkAndRevokeList = $this->_standingBookModel->getCheckList($filter, 'tid');

            $saleAndCancelMap  = array_column($saleAndCancelList, null, 'tid');
            $checkAndRevokeMap = array_column($checkAndRevokeList, null, 'tid');
            $firstNotCheckMap  = array_column($firstNotCheckList, null, 'tid');
            $lastNotCheckMap   = array_column($lastNotCheckList, null, 'tid');

            $ticketInfoMap = $this->_getTicketInfo($tidArr);

            $list = [];
            //组名、景区名、门票名、维度名、具体维度
            foreach ($groupArr as $groupId => $group) {
                $groupTitle = $group['title'];
                foreach ($group['tids'] as $tid) {
                    $saleTicket   = $saleAndCancelMap[$tid]['order_ticket'] ?? 0;
                    $saleMoney    = $saleAndCancelMap[$tid]['sale_money'] ?? 0;
                    $cancelTicket = $saleAndCancelMap[$tid]['cancel_ticket'] ?? 0;
                    $cancelMoney  = $saleAndCancelMap[$tid]['cancel_sale_money'] ?? 0;
                    $checkTicket  = ($checkAndRevokeMap[$tid]['order_ticket'] ?? 0) + ($checkAndRevokeMap[$tid]['finish_ticket'] ?? 0);
                    $checkMoney   = ($checkAndRevokeMap[$tid]['sale_money'] ?? 0) + ($checkAndRevokeMap[$tid]['finish_sale_money'] ?? 0);
                    $revokeTicket = $checkAndRevokeMap[$tid]['revoke_ticket'] ?? 0;
                    $revokeMoney  = $checkAndRevokeMap[$tid]['revoke_sale_money'] ?? 0;
                    $firstTicket  = $firstNotCheckMap[$tid]['order_ticket'] ?? 0;
                    $firstMoney   = $firstNotCheckMap[$tid]['sale_money'] ?? 0;
                    $lastTicket   = $lastNotCheckMap[$tid]['order_ticket'] ?? 0;
                    $lastMoney    = $lastNotCheckMap[$tid]['sale_money'] ?? 0;

                    $tmp = [
                        'group_title'     => $groupTitle,
                        'group_id'        => $groupId,
                        'lid'             => $ticketInfoMap[$tid]['land_id'] ?? 0,
                        'lid_name'        => $ticketInfoMap[$tid]['land_name'] ?? '',
                        'tid'             => $tid,
                        'tid_name'        => $ticketInfoMap[$tid]['ticket_name'] ?? '',
                        'first_not_check' => $this->_cellsFormat($firstTicket, $firstMoney),
                        'sale'            => $this->_cellsFormat($saleTicket, $saleMoney),
                        'check'           => $this->_cellsFormat($checkTicket, $checkMoney),
                        'cancel'          => $this->_cellsFormat($cancelTicket, $cancelMoney),
                        'last_not_check'  => $this->_cellsFormat($lastTicket, $lastMoney),
                        'revoke'          => $this->_cellsFormat($revokeTicket, $revokeMoney),
                    ];

                    $list[] = $tmp;
                }
            }

            $data['list']       = $list;
            $data['show_price'] = (bool)$showPrice;

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取未验证报表
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  string  $startDt       查询日期
     * @param  int     $templateId    模板id
     *
     * @return array
     */
    public function getNotCheckList($startDt, $templateId)
    {
        $code = 200;
        $msg  = '成功';
        $data = [];
        try {
            //商户自定义收款方式
            $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($this->sid);

            //根据模板id验证模板是否存在
            $templateData = $this->_checkTemplateById($templateId, self::REPORT_TYPE_NOTCHECK);

            //报表模板数据处理
            $template = $this->_handleTemplateFormat($templateData);

            //是否展示票价
            $showPrice = $template['show_price'];

            //分组门票信息
            $groupArr = $template['group'];
            //获取分组里面的门票id
            $tidArr = $this->getTemplateGroupTids($groupArr);

            //统计维度
            $itemType = $template['item_type'];
            $channel  = $template['channel'];
            $payWay   = $template['pay_way'];

            //未验证报表
            $firstDt = date("Y-m-d", strtotime("$startDt 00:00:00"));

            $notCheckBiz = new NotCheckedBiz();

            //未验证
            $res = $notCheckBiz->getLedgerListSummary($this->sid, $firstDt, $tidArr, $channel, $payWay, 1,
                count($tidArr));

            $notCheckList = $res['data']['list'] ?? [];

            $notCheckMap = array_column($notCheckList, null, 'tid');

            $ticketInfoMap = $this->_getTicketInfo($tidArr);

            $list = [];
            //组名、景区名、门票名、维度名、具体维度
            foreach ($groupArr as $groupId => $group) {
                $groupTitle = $group['title'];
                foreach ($group['tids'] as $tid) {
                    $channelList = $notCheckMap[$tid]['channel'] ?? [];
                    $payWayList  = $notCheckMap[$tid]['pay_way'] ?? [];

                    $channelArr = [];
                    $payWayArr  = [];

                    foreach ($channel as $key) {
                        $channelArr[] = [
                            'key'   => $key,
                            'title' => $this->_channelInfo[$key] ?? '未知',
                            'child' => $this->_cellsFormat($channelList[$key]['total'], $channelList[$key]['money']),
                        ];
                    }
                    unset($key);
                    foreach ($payWay as $key) {
                        $payWayArr[] = [
                            'key'   => $key,
                            'title' => $this->_paywayInfo[$key] ?? '未知',
                            'child' => $this->_cellsFormat($payWayList[$key]['total'], $payWayList[$key]['money']),
                        ];
                    }
                    $tmp = [
                        'group_title' => $groupTitle,
                        'group_id'    => $groupId,
                        'lid'         => $ticketInfoMap[$tid]['land_id'] ?? 0,
                        'lid_name'    => $ticketInfoMap[$tid]['land_name'] ?? '',
                        'tid'         => $tid,
                        'tid_name'    => $ticketInfoMap[$tid]['ticket_name'] ?? '',
                        'list'        => [],
                    ];
                    switch ($itemType) {
                        case 'channel':
                            $tmp['list'] = $channelArr;
                            break;
                        case 'pay_way':
                            $tmp['list'] = $payWayArr;
                            break;
                    }

                    $list[] = $tmp;
                }
            }

            $data['list']       = $list;
            $data['type']       = $itemType;
            $data['show_price'] = (bool)$showPrice;
            $data['type_name']  = self::REPORT_ITEM_VALUE[$itemType] ?? '';
            $data['title']      = self::REPORT_TYPE_MAP[self::REPORT_TYPE_NOTCHECK] ?? '未知报表';
            $data['date_range'] = date("Y年m月d日", strtotime($startDt));

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取预售或取消
     * <AUTHOR>
     * @date   2023/3/7
     *
     * @param  string  $startDt       开始
     * @param  string  $endDt         结束
     * @param  int     $templateId    模板id
     * @param  string  $type          类型: sale、cancel
     *
     * @return array
     */
    public function getSaleAndCancelList($startDt, $endDt, $templateId, $type = 'sale')
    {
        $code = 200;
        $msg  = '成功';
        $data = [];
        try {
            //商户自定义收款方式
            $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($this->sid);

            $reportType = $type == self::REPORT_TYPE_SALE ? self::REPORT_TYPE_SALE : self::REPORT_TYPE_CANCEL;

            //根据模板id验证模板是否存在
            $templateData = $this->_checkTemplateById($templateId, $reportType);

            //报表模板数据处理
            $template = $this->_handleTemplateFormat($templateData);

            //是否展示票价
            $showPrice = $template['show_price'];

            //统计维度
            $itemType = $template['item_type'];

            //统计渠道
            $channel = $template['channel'];

            //统计支付方式
            $payWay = $template['pay_way'];

            //分组门票信息
            $groupArr = $template['group'];
            //获取分组里面的门票id
            $tidArr = $this->getTemplateGroupTids($groupArr);

            //查询条件
            $filter = $this->_handleQueryParams($startDt, $endDt, $channel, $payWay, $tidArr);

            $groupBy = 'tid, channel, pay_way';

            //预售、取消
            $saleAndCancelRes = $this->_standingBookModel->getOrderList($filter, $groupBy);

            //数据需要转化下
            $saleList   = [];
            $cancelList = [];
            foreach ($saleAndCancelRes as $item) {
                //预售数据处理
                if (!isset($saleList[$item['tid']])) {
                    $saleList[$item['tid']] = [
                        'tid'     => $item['tid'],
                        'channel' => [],
                        'pay_way' => [],
                    ];
                }
                //渠道归总
                if (!isset($saleList[$item['tid']]['channel'][$item['channel']])) {
                    $saleList[$item['tid']]['channel'][$item['channel']] = [
                        'total' => $item['order_ticket'],
                        'money' => $item['sale_money'],
                    ];
                } else {
                    $saleList[$item['tid']]['channel'][$item['channel']]['total'] += $item['order_ticket'];
                    $saleList[$item['tid']]['channel'][$item['channel']]['money'] += $item['sale_money'];
                }
                //支付方式归总
                if (!isset($saleList[$item['tid']]['pay_way'][$item['pay_way']])) {
                    $saleList[$item['tid']]['pay_way'][$item['pay_way']] = [
                        'total' => $item['order_ticket'],
                        'money' => $item['sale_money'],
                    ];
                } else {
                    $saleList[$item['tid']]['pay_way'][$item['pay_way']]['total'] += $item['order_ticket'];
                    $saleList[$item['tid']]['pay_way'][$item['pay_way']]['money'] += $item['sale_money'];
                }

                //取消数据处理
                if (!isset($cancelList[$item['tid']])) {
                    $cancelList[$item['tid']] = [
                        'tid'     => $item['tid'],
                        'channel' => [],
                        'pay_way' => [],
                    ];
                }
                //取消渠道归总
                if (!isset($cancelList[$item['tid']]['channel'][$item['channel']])) {
                    $cancelList[$item['tid']]['channel'][$item['channel']] = [
                        'total' => $item['cancel_ticket'],
                        'money' => $item['cancel_sale_money'],
                    ];
                } else {
                    $cancelList[$item['tid']]['channel'][$item['channel']]['total'] += $item['cancel_ticket'];
                    $cancelList[$item['tid']]['channel'][$item['channel']]['money'] += $item['cancel_sale_money'];
                }
                //取消支付方式归总
                if (!isset($cancelList[$item['tid']]['pay_way'][$item['pay_way']])) {
                    $cancelList[$item['tid']]['pay_way'][$item['pay_way']] = [
                        'total' => $item['cancel_ticket'],
                        'money' => $item['cancel_sale_money'],
                    ];
                } else {
                    $cancelList[$item['tid']]['pay_way'][$item['pay_way']]['total'] += $item['cancel_ticket'];
                    $cancelList[$item['tid']]['pay_way'][$item['pay_way']]['money'] += $item['cancel_sale_money'];
                }

            }

            $ticketInfoMap = $this->_getTicketInfo($tidArr);

            $saleAndCancelMap = $type == self::REPORT_TYPE_SALE ? $saleList : $cancelList;

            $list = [];
            //组名、景区名、门票名、维度名、具体维度
            foreach ($groupArr as $groupId => $group) {
                $groupTitle = $group['title'];
                foreach ($group['tids'] as $tid) {
                    $channelList = $saleAndCancelMap[$tid]['channel'] ?? [];
                    $payWayList  = $saleAndCancelMap[$tid]['pay_way'] ?? [];

                    $channelArr = [];
                    $payWayArr  = [];

                    foreach ($channel as $key) {
                        $channelArr[] = [
                            'key'   => $key,
                            'title' => $this->_channelInfo[$key] ?? '未知',
                            'child' => $this->_cellsFormat($channelList[$key]['total'], $channelList[$key]['money']),
                        ];
                    }
                    unset($key);
                    foreach ($payWay as $key) {
                        $payWayArr[] = [
                            'key'   => $key,
                            'title' => $this->_paywayInfo[$key] ?? '未知',
                            'child' => $this->_cellsFormat($payWayList[$key]['total'], $payWayList[$key]['money']),
                        ];
                    }

                    $tmp = [
                        'group_title' => $groupTitle,
                        'group_id'    => $groupId,
                        'lid'         => $ticketInfoMap[$tid]['land_id'] ?? 0,
                        'lid_name'    => $ticketInfoMap[$tid]['land_name'] ?? '',
                        'tid'         => $tid,
                        'tid_name'    => $ticketInfoMap[$tid]['ticket_name'] ?? '',
                        'list'        => [],
                    ];
                    switch ($itemType) {
                        case 'channel':
                            $tmp['list'] = $channelArr;
                            break;
                        case 'pay_way':
                            $tmp['list'] = $payWayArr;
                            break;
                    }

                    $list[] = $tmp;
                }
            }

            $data['list']       = $list;
            $data['type']       = $itemType;
            $data['show_price'] = (bool)$showPrice;
            $data['type_name']  = self::REPORT_ITEM_VALUE[$itemType] ?? '';
            $data['title']      = self::REPORT_TYPE_MAP[$type] ?? '未知报表';
            $data['date_range'] = date("Y年m月d日", strtotime($startDt)) . ' - ' . date("Y年m月d日", strtotime($endDt));

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取验证或撤销撤改
     * <AUTHOR>
     * @date   2023/3/7
     *
     * @param  string  $startDt       开始
     * @param  string  $endDt         结束
     * @param  int     $templateId    模板id
     * @param  string  $type          类型：check、revoke
     *
     * @return array
     */
    public function getCheckAndRevokeList($startDt, $endDt, $templateId, $type = 'check')
    {
        $code = 200;
        $msg  = '成功';
        $data = [];
        try {
            //商户自定义收款方式
            $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($this->sid);

            $reportType = $type == self::REPORT_TYPE_CHECK ? self::REPORT_TYPE_CHECK : self::REPORT_TYPE_REVOKE;

            //根据模板id验证模板是否存在
            $templateData = $this->_checkTemplateById($templateId, $reportType);

            //报表模板数据处理
            $template = $this->_handleTemplateFormat($templateData);

            //是否展示票价
            $showPrice = $template['show_price'];

            //统计维度
            $itemType = $template['item_type'];

            //统计渠道
            $channel = $template['channel'];

            //统计支付方式
            $payWay = $template['pay_way'];

            //分组门票信息
            $groupArr = $template['group'];
            //获取分组里面的门票id
            $tidArr = $this->getTemplateGroupTids($groupArr);

            //查询条件
            $filter = $this->_handleQueryParams($startDt, $endDt, $channel, $payWay, $tidArr);

            $groupBy = 'tid, channel, pay_way';

            //验证、撤销撤改
            $checkAndRevokeRes = $this->_standingBookModel->getCheckList($filter, $groupBy);

            //数据需要转化下
            $checkList  = [];
            $revokeList = [];
            foreach ($checkAndRevokeRes as $item) {
                //验证数据处理
                if (!isset($checkList[$item['tid']])) {
                    $checkList[$item['tid']] = [
                        'tid'     => $item['tid'],
                        'channel' => [],
                        'pay_way' => [],
                    ];
                }
                //渠道归总
                if (!isset($checkList[$item['tid']]['channel'][$item['channel']])) {
                    $checkList[$item['tid']]['channel'][$item['channel']] = [
                        'total' => $item['order_ticket'] + $item['finish_ticket'],
                        'money' => $item['sale_money'] + $item['finish_sale_money'],
                    ];
                } else {
                    $checkList[$item['tid']]['channel'][$item['channel']]['total'] += $item['order_ticket'] + $item['finish_ticket'];
                    $checkList[$item['tid']]['channel'][$item['channel']]['money'] += $item['sale_money'] + $item['finish_sale_money'];
                }
                //支付方式归总
                if (!isset($checkList[$item['tid']]['pay_way'][$item['pay_way']])) {
                    $checkList[$item['tid']]['pay_way'][$item['pay_way']] = [
                        'total' => $item['order_ticket'] + $item['finish_ticket'],
                        'money' => $item['sale_money'] + $item['finish_sale_money'],
                    ];
                } else {
                    $checkList[$item['tid']]['pay_way'][$item['pay_way']]['total'] += $item['order_ticket'] + $item['finish_ticket'];
                    $checkList[$item['tid']]['pay_way'][$item['pay_way']]['money'] += $item['sale_money'] + $item['finish_sale_money'];
                }

                //撤销撤改
                if (!isset($revokeList[$item['tid']])) {
                    $revokeList[$item['tid']] = [
                        'tid'     => $item['tid'],
                        'channel' => [],
                        'pay_way' => [],
                    ];
                }
                //撤销撤改渠道归总
                if (!isset($revokeList[$item['tid']]['channel'][$item['channel']])) {
                    $revokeList[$item['tid']]['channel'][$item['channel']] = [
                        'total' => $item['revoke_ticket'],
                        'money' => $item['revoke_sale_money'],
                    ];
                } else {
                    $revokeList[$item['tid']]['channel'][$item['channel']]['total'] += $item['revoke_ticket'];
                    $revokeList[$item['tid']]['channel'][$item['channel']]['money'] += $item['revoke_sale_money'];
                }
                //撤销撤改支付方式归总
                if (!isset($revokeList[$item['tid']]['pay_way'][$item['pay_way']])) {
                    $revokeList[$item['tid']]['pay_way'][$item['pay_way']] = [
                        'total' => $item['revoke_ticket'],
                        'money' => $item['revoke_sale_money'],
                    ];
                } else {
                    $revokeList[$item['tid']]['pay_way'][$item['pay_way']]['total'] += $item['revoke_ticket'];
                    $revokeList[$item['tid']]['pay_way'][$item['pay_way']]['money'] += $item['revoke_sale_money'];
                }

            }

            $ticketInfoMap = $this->_getTicketInfo($tidArr);

            $checkAndRevokeMap = $type == self::REPORT_TYPE_CHECK ? $checkList : $revokeList;

            $list = [];
            //组名、景区名、门票名、维度名、具体维度
            foreach ($groupArr as $groupId => $group) {
                $groupTitle = $group['title'];
                foreach ($group['tids'] as $tid) {
                    $channelList = $checkAndRevokeMap[$tid]['channel'] ?? [];
                    $payWayList  = $checkAndRevokeMap[$tid]['pay_way'] ?? [];

                    $channelArr = [];
                    $payWayArr  = [];

                    foreach ($channel as $key) {
                        $channelArr[] = [
                            'key'   => $key,
                            'title' => $this->_channelInfo[$key] ?? '未知',
                            'child' => $this->_cellsFormat($channelList[$key]['total'], $channelList[$key]['money']),
                        ];
                    }
                    unset($key);
                    foreach ($payWay as $key) {
                        $payWayArr[] = [
                            'key'   => $key,
                            'title' => $this->_paywayInfo[$key] ?? '未知',
                            'child' => $this->_cellsFormat($payWayList[$key]['total'], $payWayList[$key]['money']),
                        ];
                    }

                    $tmp = [
                        'group_title' => $groupTitle,
                        'group_id'    => $groupId,
                        'lid'         => $ticketInfoMap[$tid]['land_id'] ?? 0,
                        'lid_name'    => $ticketInfoMap[$tid]['land_name'] ?? '',
                        'tid'         => $tid,
                        'tid_name'    => $ticketInfoMap[$tid]['ticket_name'] ?? '',
                        'list'        => [],
                    ];
                    switch ($itemType) {
                        case 'channel':
                            $tmp['list'] = $channelArr;
                            break;
                        case 'pay_way':
                            $tmp['list'] = $payWayArr;
                            break;
                    }

                    $list[] = $tmp;
                }
            }

            $data['list']       = $list;
            $data['type']       = $itemType;
            $data['show_price'] = (bool)$showPrice;
            $data['type_name']  = self::REPORT_ITEM_VALUE[$itemType] ?? '';
            $data['title']      = self::REPORT_TYPE_MAP[$type] ?? '未知报表';
            $data['date_range'] = date("Y年m月d日", strtotime($startDt)) . ' - ' . date("Y年m月d日", strtotime($endDt));

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取预售或取消数据
     * <AUTHOR>
     * @date   2023/3/7
     *
     * @param  array   $params    查询参数集
     * @param  string  $type      类型: sale、cancel
     * @param  int     $page      页码
     * @param  int     $size      页数
     *
     * @return array
     */
    public function getSaleAndCanceInfo($params, $type = self::REPORT_TYPE_SALE, $page = 1, $size = 10)
    {
        $code = 200;
        $msg  = '成功';
        $data = [];
        try {
            $reportType = $type == self::REPORT_TYPE_SALE ? self::REPORT_TYPE_SALE : self::REPORT_TYPE_CANCEL;

            $startDt = $params['begin'];
            $endDt   = $params['end'];
            $channel = $params['channel'];
            $payWay  = $params['pay_way'];
            $tidArr  = $params['tids'];

            //查询条件
            $filter = $this->_handleQueryParams($startDt, $endDt, $channel, $payWay, $tidArr);

            //预售、取消
            $res = $this->_standingBookModel->getInfoList($filter, StandingBookModel::STANDING_BOOK_ORDER, $page,
                $size);

            $orderArr          = [];
            $resellerArr       = [];
            $fidArr            = [];
            $lidArr            = [];
            $orderReportInfo   = [];
            $cancelReportInfo  = [];
            $sonTicketOrderArr = [];

            //积分消费信息
            $pointsInfo = [
                'orderPointsInfo'  => [],
                'cancelPointsInfo' => [],
                'finishPointsInfo' => [],
                'revokePointsInfo' => [],
            ];

            foreach ($res as $item) {
                $ordersInfo       = $item['orders_info'];
                $cancelOrdersInfo = $item['cancel_orders_info'];
                $resellerArr[]    = $item['reseller_id'];
                $fidArr[]         = $item['fid'];
                $lidArr[]         = $item['lid'];
                unset($item['orders_info']);

                $ordersInfo       = json_decode($ordersInfo, true);
                $cancelOrdersInfo = json_decode($cancelOrdersInfo, true);

                foreach ($ordersInfo as $value) {
                    $orderNum        = $value[0];
                    $tnum            = $value[1];
                    $pointMoney      = $value[2] ?? 0;
                    $settlementMoney = $value[3] ?? 0;

                    // 订单信息
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $orderReportInfo[$orderNum]['tnum']       += $tnum;
                    $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['orderPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;


                    if (!empty($item['main_tid'])) {
                        $sonTicketOrderArr[] = $orderNum;
                    }
                }

                foreach ($cancelOrdersInfo as $value) {
                    $orderNum        = $value[0];
                    $tnum            = $value[1];
                    $pointMoney      = $value[2] ?? 0;
                    $settlementMoney = $value[3] ?? 0;

                    // 取消订单信息
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }

                    $cancelReportInfo[$orderNum]['tnum']       += $tnum;
                    $pointsInfo['cancelPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['cancelPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                    if (!empty($item['main_tid'])) {
                        $sonTicketOrderArr[] = $orderNum;
                    }
                }
            }

            unset($res);

            //获取全部分销商
            $resellerArr = array_unique($resellerArr);
            $fidArr      = array_unique($fidArr);
            $resellerArr = array_merge($resellerArr, $fidArr);
            //散客
            $resellerMap = load_config('reseller_map', 'trade_record');
            $resellerMap = array_column($resellerMap, 'id');
            //子票订单去重
            $sonTicketOrderArr = array_unique($sonTicketOrderArr);

            $statisticsBiz = new StatisticsBiz();

            $data = $statisticsBiz->handleStandingBookOrderInfo($this->sid, $orderArr, $lidArr, $resellerArr,
                $resellerMap, $orderReportInfo, $cancelReportInfo, $reportType, $pointsInfo, $sonTicketOrderArr);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取验证或撤销撤改数据
     * <AUTHOR>
     * @date   2023/3/7
     *
     * @param  array   $params    查询参数集
     * @param  string  $type      类型: sale、cancel
     * @param  int     $page      页码
     * @param  int     $size      页数
     *
     * @return array
     */
    public function getCheckAndRevokeInfo($params, $type = self::REPORT_TYPE_CHECK, $page = 1, $size = 10)
    {
        $code = 200;
        $msg  = '成功';
        $data = [];
        try {
            $reportType = $type == self::REPORT_TYPE_CHECK ? self::REPORT_TYPE_CHECK : self::REPORT_TYPE_REVOKE;

            $startDt = $params['begin'];
            $endDt   = $params['end'];
            $channel = $params['channel'];
            $payWay  = $params['pay_way'];
            $tidArr  = $params['tids'];

            //查询条件
            $filter = $this->_handleQueryParams($startDt, $endDt, $channel, $payWay, $tidArr);

            //验证、撤销撤改
            $res = $this->_standingBookModel->getInfoList($filter, StandingBookModel::STANDING_BOOK_CHECKED, $page,
                $size);

            $orderArr        = [];
            $resellerArr     = [];
            $lidArr          = [];
            $fidArr          = [];
            $orderReportInfo = [];

            $finishReportInfo = [];
            $revokeReportInfo = [];

            $sonTicketOrderArr = [];

            //积分消费信息
            $pointsInfo = [
                'checkPointsInfo'  => [],
                'cancelPointsInfo' => [],
                'finishPointsInfo' => [],
                'revokePointsInfo' => [],
            ];

            foreach ($res as $item) {
                $ordersInfo       = $item['orders_info'];
                $finishOrdersInfo = $item['finish_orders_info'];
                $revokeOrdersInfo = $item['revoke_orders_info'];
                $resellerArr[]    = $item['reseller_id'];
                $fidArr[]         = $item['fid'];
                $lidArr[]         = $item['lid'];
                unset($item['orders_info']);

                $ordersInfo       = json_decode($ordersInfo, true);
                $finishOrdersInfo = json_decode($finishOrdersInfo, true);
                $revokeOrdersInfo = json_decode($revokeOrdersInfo, true);

                $ordersInfo       = $ordersInfo ?: [];
                $finishOrdersInfo = $finishOrdersInfo ?: [];
                $revokeOrdersInfo = $revokeOrdersInfo ?: [];

                foreach ($ordersInfo as $value) {
                    $orderNum        = $value[0];
                    $tnum            = $value[1];
                    $pointMoney      = $value[2] ?? 0;
                    $settlementMoney = $value[3] ?? 0;

                    // 订单信息
                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }

                    $orderReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['checkPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['checkPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                    if (!empty($item['main_tid'])) {
                        $sonTicketOrderArr[] = $orderNum;
                    }
                }

                foreach ($finishOrdersInfo as $key => $value) {
                    $orderNum        = $value[0];
                    $tnum            = $value[1];
                    $pointMoney      = $value[2] ?? 0;
                    $settlementMoney = $value[3] ?? 0;

                    $orderArr[] = $orderNum;
                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $finishReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['finishPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['finishPointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                    if (!empty($item['main_tid'])) {
                        $sonTicketOrderArr[] = $orderNum;
                    }
                }

                foreach ($revokeOrdersInfo as $key => $value) {
                    $orderNum        = $value[0];
                    $tnum            = $value[1];
                    $pointMoney      = $value[2] ?? 0;
                    $settlementMoney = $value[3] ?? 0;

                    $orderArr[]   = $orderNum;
                    $cacelOrder[] = $orderNum;

                    if (!isset($orderReportInfo[$orderNum])) {
                        $orderReportInfo[$orderNum] = $item;
                    }
                    $revokeReportInfo[$orderNum]['tnum']                              += $tnum;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::DISCOUNT]            += $pointMoney;
                    $pointsInfo['revokePointsInfo'][$orderNum][\Business\Statistics\CreateReportBase::SETTLEMENT_DISCOUNT] += $settlementMoney;

                    if (!empty($item['main_tid'])) {
                        $sonTicketOrderArr[] = $orderNum;
                    }
                }
            }

            unset($res);

            //获取全部分销商
            $resellerArr = array_unique($resellerArr);
            $fidArr      = array_unique($fidArr);
            $resellerArr = array_merge($resellerArr, $fidArr);
            //散客
            $resellerMap = load_config('reseller_map', 'trade_record');
            $resellerMap = array_column($resellerMap, 'id');
            //子票订单去重
            $sonTicketOrderArr = array_unique($sonTicketOrderArr);

            $statisticsBiz = new StatisticsBiz();

            $data = $statisticsBiz->handleStandingBookCheckInfo($this->sid, $orderArr, $lidArr, $resellerArr,
                $resellerMap, $orderReportInfo, $finishReportInfo, $revokeReportInfo, $reportType, $pointsInfo,
                $sonTicketOrderArr);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 取登录用户的直销和转分销景区(只获取上下架的)
     * <AUTHOR>
     * @date   2023/3/16
     *
     * @param  string  $keyWord    景区关键词 搜索
     * @param  int     $page       页码
     * @param  int     $size       页数
     *
     * @return array
     */
    public function getTemplateGroupLand($keyWord, $page = 1, $size = 10)
    {
        $javaApi = new \Business\JavaApi\Product\EvoluteListQuery();
        $result  = $javaApi->queryEvoluteLandByFid($this->sid, $keyWord, $page, $size, ['Q'], [1, 2]);

        $list = $result['list'] ?? [];
        if (empty($list)) {
            return $this->returnData(200, "无数据", []);
        }

        $data = [];
        foreach ($list as $item) {
            $data[$item['id']] = [
                'id'   => $item['id'],
                'name' => $item['title'],
            ];
        }

        return $this->returnData(200, "获取成功", array_values($data));
    }

    /**
     * 获取登录用户的直销和转分销产品(只获取上下架的)
     * <AUTHOR>
     * @date   2023/3/16
     *
     * @param  int     $detailId    景区id
     * @param  string  $keyWord     产品关键词 搜索
     * @param  int     $page        页码
     * @param  int     $size        页数
     *
     * @return array
     */
    public function getTemplateGroupTicket($detailId, $keyWord = '', $page = 1, $size = 10)
    {
        $javaApi = new \Business\JavaApi\Product\EvoluteListQuery();
        $result  = $javaApi->queryEvoluteProductByFid($this->sid, [1, 2], $keyWord, $page, $size, ['Q'], intval($detailId));

        $list = $result['list'] ?? [];
        if (empty($list)) {
            return $this->returnData(200, "无数据", []);
        }

        $tidArr     = array_column($list, 'tid');
        $ticketInfo = [];
        if (!empty($tidArr)) {
            $ticketBz     = new \Business\CommodityCenter\Ticket();
            $ticketResult = $ticketBz->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('id,title', '', 'id,title',
                '', false, $tidArr, null, null, null, ['landFlag' => 1]);
            foreach ($ticketResult as $value) {
                $ticketInfo[$value['ticket']['id']]['ttitle'] = $value['ticket']['title'];
                $ticketInfo[$value['ticket']['id']]['title']  = $value['land']['title'];
                $ticketInfo[$value['ticket']['id']]['lid']    = $value['land']['id'];
            }
        }

        $data = [];
        foreach ($list as $item) {
            $data[$item['tid']] = [
                'pid'      => $item['pid'],
                'pid_name' => $item['title'],
                'id'       => $item['tid'],
                'name'     => $ticketInfo[$item['tid']]['ttitle'] ?? '',
                'lid'      => $ticketInfo[$item['tid']]['lid'] ?? '',
                'lid_name' => $ticketInfo[$item['tid']]['title'] ?? '',
            ];
        }

        return $this->returnData(200, "获取成功", array_values($data));
    }

    /**
     * 解析统计维度
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  string  $data    数据
     *
     * @return object
     */
    private function _deCodeItemValue($data)
    {
        $default = [
            'show_price' => 0,
            'channel'    => [],
            'pay_way'    => [],
            'item_type'  => '',
        ];
        if (empty($data)) {
            return (object)$default;
        }
        $arr                   = json_decode($data, true);
        $default['show_price'] = $arr['show_price'] ?? 0;
        $default['channel']    = $arr['channel'] ?? [];
        $default['pay_way']    = $arr['pay_way'] ?? [];

        if (!empty($default['channel'])) {
            $default['item_type'] = 'channel';
        }

        if (!empty($default['pay_way'])) {
            $default['item_type'] = 'pay_way';
        }

        return (object)$default;
    }

    /**
     * 组合解析
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  array  $data    组合数据
     *
     * @return object
     */
    private function _deCodeGroupTicketItem($data)
    {
        $default = [
            'title' => '',
            'tids'  => [],
        ];
        if (empty($data)) {
            return (object)$default;
        }
        $default['title'] = $data['title'] ?? '';
        $default['tids']  = $data['tids'] ?? [];

        return (object)$default;
    }

    /**
     * 统计维度转换
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  string  $reportType    报表类型
     * @param  int     $showPrice     是否展示票价
     * @param  array   $channel       渠道
     * @param  array   $payWay        支付方式
     *
     * @return array
     */
    private function _enCodeItemValue($reportType, $showPrice, $channel = [], $payWay = [])
    {
        $itemValue = [
            'show_price' => $showPrice,
        ];

        if (in_array($reportType, [
            self::REPORT_TYPE_SALE,
            self::REPORT_TYPE_CHECK,
            self::REPORT_TYPE_REVOKE,
            self::REPORT_TYPE_CANCEL,
            self::REPORT_TYPE_NOTCHECK,
        ])) {
            if (!empty($channel)) {
                $itemValue['channel'] = is_array($channel) ? array_map('intval', $channel) : [];
            }
            if (!empty($payWay)) {
                $itemValue['pay_way'] = is_array($payWay) ? array_map('intval', $payWay) : [];
            }
        }

        return $itemValue;
    }

    /**
     * 模板信息格式化
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  array  $tmp         模板信息
     * @param  bool   $backName    是否返回名称
     *
     * @return array
     */
    private function _handleTemplateFormat($tmp, $backName = false)
    {
        $itemValue   = $this->_deCodeItemValue($tmp['item_value']);
        $groupTicket = empty($tmp['group_ticket']) ? [] : json_decode($tmp['group_ticket'], true);
        $channelName = [];
        $payWayName  = [];
        if ($backName) {
            foreach ($itemValue->channel as $k) {
                $channelName[] = [
                    'id'   => $k,
                    'name' => $this->_channelInfo[(string)$k] ?? '未知',
                ];
            }
            unset($k);
            foreach ($itemValue->pay_way as $k) {
                $payWayName[] = [
                    'id'   => $k,
                    'name' => $this->_paywayInfo[$k] ?? '未知',
                ];
            }

            //分组票信息查询
            $tidArr        = $this->getTemplateGroupTids($groupTicket);
            $ticketInfoMap = $this->_getTicketInfo($tidArr);
            foreach ($groupTicket as &$val) {
                if (empty($val['tids'])) {
                    $val['list'] = [];
                    continue;
                }
                $tids    = $val['tids'];
                $landArr = [];
                foreach ($ticketInfoMap as $tid => $info) {
                    if (!in_array($tid, $tids)) {
                        continue;
                    }
                    if (!isset($landArr[$info['land_id']])) {
                        $landArr[$info['land_id']] = [
                            'lid'     => $info['land_id'],
                            'title'   => $info['land_name'] ?? '',
                            'tickets' => [
                                [
                                    'id'   => $tid,
                                    'name' => $info['ticket_name'] ?? '',
                                ],
                            ],
                        ];
                    } else {
                        $landArr[$info['land_id']]['tickets'][] = [
                            'id'   => $tid,
                            'name' => $info['ticket_name'] ?? '',
                        ];
                    }
                }
                $val['list'] = array_values($landArr);
            }
        }

        return [
            'id'           => $tmp['id'],
            'title'        => $tmp['title'],
            'show_price'   => $itemValue->show_price,
            'channel'      => $itemValue->channel,
            'pay_way'      => $itemValue->pay_way,
            'item_type'    => $itemValue->item_type,
            'group'        => $groupTicket,
            'channel_name' => $channelName,
            'pay_way_name' => $payWayName,
        ];
    }

    /**
     * 验证组合数据
     * <AUTHOR>
     * @date   2023/3/3
     *
     * @param  array  $groupTicket    组合票数据
     *
     * @return bool
     * @throws
     */
    private function _checkGroupTicket($groupTicket)
    {
        if (count($groupTicket) > self::GROUP_MAX) {
            throw new \Exception('创建组合上限最大为10', 203);
        }

        $allTicket = [];
        foreach ($groupTicket as $tmp) {
            $tmp = $this->_deCodeGroupTicketItem($tmp);
            if (empty($tmp->tids)) {
                throw new \Exception('组合内产品不能为空', 203);
            }
            if (count($tmp->tids) > self::GROUP_TID_MAX) {
                throw new \Exception('选择票种最大支持' . self::GROUP_TID_MAX . '种', 203);
            }
            $title = $tmp->title;
            if (empty($title)) {
                throw new \Exception('组合名称不能为空', 203);
            }
            //支持英文、汉字、数字
            if (!preg_match("/^[A-Za-z0-9\x{4e00}-\x{9fa5}]+$/u", $title)) {
                throw new \Exception('名称仅支持中文、英文、阿拉伯数字', 203);
            }
            if (mb_strlen($title) > self::TITLE_MAX) {
                throw new \Exception('创建组合名称，最大支持20个字', 203);
            }

            $allTicket = array_merge($allTicket, $tmp->tids);
        }

        //同个模板，重复票种验证
        if (count($allTicket) != count(array_unique($allTicket))) {
            throw new \Exception('存在重复的票种，请重试', 203);
        }

        return true;
    }

    /**
     * 统计维度验证
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  string  $reportType    报表类型
     * @param  array   $channel       销售渠道
     * @param  array   $payWay        支付方式
     *
     * @return bool
     * @throws
     */
    private function _checkItemValue($reportType, $channel = [], $payWay = [])
    {
        //统计维度验证 销售渠道+ 支付方式 是单选
        if (($reportType != self::REPORT_TYPE_OVERALL && empty($channel) && empty($payWay)) || (!empty($channel) && !empty($payWay))) {
            throw new \Exception('统计维度格式错误', 203);
        }

        return true;
    }

    /**
     * 日期格式验证
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  string  $dt    日期字串
     *
     * @return string
     * @throws
     */
    private function _checkDtFormat($dt)
    {
        //验证格式：2023-03-01
        if (date("Y-m-d", strtotime("$dt 00:00:00")) != $dt) {
            throw new \Exception('查询日期错误', 203);
        }

        return date("Ymd", strtotime("$dt 00:00:00"));
    }

    /**
     * 检测模板名称
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  string  $title    名称
     *
     * @return bool
     * @throws
     */
    private function _checkTemplateName($title)
    {
        if (mb_strlen($title) > self::TITLE_MAX) {
            throw new \Exception('模板名称字数最多20个字', 203);
        }
        //支持英文、汉字、数字
        if (!preg_match("/^[A-Za-z0-9\x{4e00}-\x{9fa5}]+$/u", $title)) {
            throw new \Exception('名称仅支持中文、英文、阿拉伯数字', 203);
        }

        return true;
    }

    /**
     * 根据模板id验证模板是否存在
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  int  $templateId    模板id
     *
     * @return array|false|string
     * @throws
     */
    private function _checkTemplateById($templateId, $reportType = '')
    {
        if (!intval($templateId)) {
            throw new \Exception('模板id缺失', 203);
        }

        //获取数据
        $info = $this->_standingBookModel->getTemplateInfo($templateId, $this->sid, $this->memberId);
        if (empty($info)) {
            throw new \Exception('模板不存在', 400);
        }

        //验证模板类型
        if (!empty($reportType) && $reportType != $info['report_type']) {
            throw new \Exception('模板类型不符合', 400);
        }

        return $info;
    }

    /**
     * 验证报表模板类型
     * <AUTHOR>
     * @date   2023/3/7
     *
     * @param  string  $type    报表类型
     *
     * @return bool
     * @throws
     */
    private function _checkTemplateReportType($type)
    {
        if (!in_array($type, array_keys(self::REPORT_TYPE_MAP))) {
            throw new \Exception('查询报表类型错误', 203);
        }

        return true;
    }

    /**
     * 报表查询参数处理
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  string  $startDt    开始
     * @param  string  $endDt      结束
     * @param  array   $channel    销售渠道
     * @param  array   $payWay     支付方式
     * @param  array   $tidArr     门票id集合
     *
     * @return array
     * @throws
     */
    private function _handleQueryParams($startDt, $endDt, $channel = [], $payWay = [], $tidArr = [])
    {
        //时间格式校验
        $startDt = $this->_checkDtFormat($startDt);
        $endDt   = $this->_checkDtFormat($endDt);

        $dtMin = date("Ymd", strtotime('-91 day'));
        $dtMax = date("Ymd", strtotime('-1 day'));

        //时间范围校验
        if (!($dtMin <= $startDt && $dtMax >= $startDt) || !(($dtMin <= $endDt && $dtMax >= $endDt))) {
            throw new \Exception('日期范围选择错误', 203);
        }

        //查询条件
        $filter = [
            'fid'      => $this->sid,
            'sta_date' => ['BETWEEN', [$startDt, $endDt]],
        ];

        if (!empty($tidArr)) {
            //去重
            $tidArr        = array_unique($tidArr);
            $filter['tid'] = ['IN', $tidArr];
        }

        //统计维度
        if (!empty($channel) && is_array($channel)) {
            $filter['channel'] = ['IN', array_unique($channel)];
        }
        if (!empty($payWay) && is_array($payWay)) {
            $filter['pay_way'] = ['IN', array_unique($payWay)];
        }

        return $filter;
    }

    /**
     * 单元格数据格式化
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  int  $total    数量
     * @param  int  $money    金额
     *
     * @return array
     */
    private function _cellsFormat($total, $money)
    {
        return [
            'total' => intval($total ?? 0),
            'money' => round(($money ?? 0) / 100, 2),
        ];
    }

    /**
     * 获取报表模板的分组票id
     * <AUTHOR>
     * @date   2023/3/6
     *
     * @param  array  $groupArr    分组信息
     *
     * @return array
     */
    public function getTemplateGroupTids($groupArr)
    {
        $tidArr = [];
        foreach ($groupArr as $tmp) {
            $tidArr = array_merge($tidArr, $tmp['tids'] ?? []);
        }
        if (!empty($tidArr)) {
            //去重
            $tidArr = array_unique($tidArr);
        }

        return array_values($tidArr);
    }

    /**
     * 批量获取门票和景区名称
     * <AUTHOR>
     * @date   2023/3/7
     *
     * @param  array  $tidArr    门票id
     *
     * @return array
     */
    private function _getTicketInfo($tidArr)
    {
        if (empty($tidArr)) {
            return [];
        }

        $ticketArr     = (new TicketBiz())->queryTicketInfoByIds($tidArr, 'id,title,pid', '', 'id,title');
        $ticketInfoMap = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $ticketInfoMap[$ticket['ticket']['id']] = [
                    'land_name'   => $ticket['land']['title'] ?? '',
                    'land_id'     => $ticket['land']['id'] ?? 0,
                    'ticket_name' => $ticket['ticket']['title'] ?? '',
                ];
            }
        }

        return $ticketInfoMap;
    }
}