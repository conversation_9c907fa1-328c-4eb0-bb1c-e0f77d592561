<?php
/**
 * 验证月报表类
 *
 * <AUTHOR>
 * @date   2019-09-16
 */
namespace Business\Statistics;


class CreateCheckMonthReport extends CreateMonthReportBase
{

    private $_logPath;
    private $_selectSize = 1000;
    //进程数
    private $_processNum = 2;
    //月份
    private $_month;


    public function __construct($logPath) {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'checked_month:start');
        parent::__construct();
    }


    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-09-16
     */
    public function runTask($month)
    {
        if (!strtotime($month)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        $this->_month = $month;

        $code = 200;
        $msg  = '';

        try {

            $this->_clearPreData($month, 'checked_v2_month');
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $task     = new \Library\MulityProcessHelper($this, $this->_processNum, 1);
            // $lidList = $this->_getLidList($this->_processNum, $month, 'checked_v2');
            $fidList = $this->_getFidList($this->_processNum, $month, 'checked_v2');
            //使用多进程之前 释放掉已有的所有链接
            $this->_unsetAllModel();
            $task->run($fidList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉

        } catch (\Exception $e) {

            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "checked_v2_month:end:{$msg}");
    }


    /**
     * 子进程脚本
     */
    public function runWorker($fidList)
    {
        $code = 200;
        $msg  = '';

        if (is_array($fidList)) {
            $fidList = array_shift($fidList);
        }

        try {

            $this->_runCheckedMonthWorker($fidList);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str  = 'checked_v2_month:';
        //     $str .= $lidList[0] . ' - ' . $lidList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        pft_log($this->_logPath, $str);
    }



    public function _runCheckedMonthWorker($fidList)
    {
        // 拼接时间
        $begin = $this->_month . '01';
        $end   = $this->_month . '31';

        $statisModel = $this->_getStatisModel();

        foreach ($fidList as $tmpFid) {

            // 获取表总数
            $total     = $statisModel->_getReportV2TotalByFid($begin, $end, 'checked_v2', $tmpFid);
            $totalPage = ceil($total / $this->_selectSize);

            for ($page = 1; $page <= $totalPage; $page++) {

                $insertData = [];
                $data = [];

                $list = $statisModel->_getReportV2ListByFid($begin, $end, $page, $this->_selectSize, 'checked_v2', $tmpFid);
                // 如果查询出错了， 直接返回错误
                if ($list === false) {
                    throw new \Exception("查询出错");
                }

                $time = time();

                foreach ($list as $item) {

                    //报表的所有维度字段
                    $lid = $item['lid'];
                    $pid = $item['pid'];
                    $fid = $item['fid'];
                    $tid = $item['tid'];

                    $sellerId      = $item['reseller_id'];
                    $channel       = $item['channel'];
                    $paymode       = $item['pay_way'];
                    $level         = $item['level'];
                    $site          = $item['site_id'];
                    $operate       = $item['operate_id'];
                    $mainTid       = $item['main_tid'];
                    $subMerchantId = $item['sub_merchant_id'];
                    $mainType      = $item['main_type'];
                    //是否当天验证
                    $isCheckSameDay = $item['today_check'];

                    $orderArr       = json_decode($item['orders_info'], true);
                    $finishOrderArr = json_decode($item['finish_orders_info'], true);
                    $revokeOrderArr = json_decode($item['revoke_orders_info'], true);
                    $afterSaleArr = [];
                    if(isset($item['after_sale_info']) && !empty($item['after_sale_info'])){
                        $afterSaleArr   = json_decode($item['after_sale_info'], true);
                    }

                    if (!isset($data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate])) {
                         $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]  = [
                            'order_num'               => $item['order_num'],
                            'order_ticket'            => $item['order_ticket'],
                            'finish_num'              => $item['finish_num'],
                            'finish_ticket'           => $item['finish_ticket'],
                            'revoke_num'              => $item['revoke_num'],
                            'revoke_ticket'           => $item['revoke_ticket'],
                            'cost_money'              => $item['cost_money'],
                            'sale_money'              => $item['sale_money'],
                            'finish_cost_money'       => $item['finish_cost_money'],
                            'finish_sale_money'       => $item['finish_sale_money'],
                            'revoke_cost_money'       => $item['revoke_cost_money'],
                            'revoke_sale_money'       => $item['revoke_sale_money'],
                            'orders_info'             => $orderArr,
                            'finish_orders_info'      => $finishOrderArr,
                            'revoke_orders_info'      => $revokeOrderArr,
                            'after_sale_info'         => $afterSaleArr,
                            'service_money'           => $item['service_money'],
                            'after_sale_ticket_num'   => $item['after_sale_ticket_num'],
                            'after_sale_refund_money' => $item['after_sale_refund_money'],
                            'after_sale_income_money' => $item['after_sale_income_money'],
                        ];

                    } else {
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['order_num'] += $item['order_num'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['order_ticket'] += $item['order_ticket'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['finish_num'] += $item['finish_num'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['finish_ticket'] += $item['finish_ticket'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['revoke_num'] += $item['revoke_num'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['revoke_ticket'] += $item['revoke_ticket'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['sale_money'] += $item['sale_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['cost_money'] += $item['cost_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['finish_cost_money'] += $item['finish_cost_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['finish_sale_money'] += $item['finish_sale_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['revoke_cost_money'] += $item['revoke_cost_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['revoke_sale_money'] += $item['revoke_sale_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['service_money'] += $item['service_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['after_sale_ticket_num'] += $item['after_sale_ticket_num'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['after_sale_refund_money'] += $item['after_sale_refund_money'];
                        $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['after_sale_income_money'] += $item['after_sale_income_money'];

                        if (is_array($orderArr) && !empty($orderArr)) {
                            foreach ($orderArr as $order) {
                                $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['orders_info'][] = $order;
                            }
                        }

                        if (is_array($finishOrderArr) && !empty($finishOrderArr)) {
                            foreach ($finishOrderArr as $order) {
                                $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['finish_orders_info'][] = $order;
                            }
                        }

                        if (is_array($revokeOrderArr) && !empty($revokeOrderArr)) {
                            foreach ($revokeOrderArr as $order) {
                                $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['revoke_orders_info'][] = $order;
                            }
                        }

                        //售后信息处理
                        if (is_array($afterSaleArr) && !empty($afterSaleArr)) {
                            foreach ($afterSaleArr as $order) {
                                $data[$fid][$tid][$isCheckSameDay][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['after_sale_info'][] = $order;
                            }
                        }
                    }
                }
                foreach ($data as $fid => $fidData) {
                    foreach ($fidData as $tid => $tidData) {
                        foreach ($tidData as $isCheckSameDay => $checkData) {
                            foreach ($checkData as $sellerId => $sellerData) {
                                foreach ($sellerData as $lid => $lidData) {
                                    foreach ($lidData as $pid => $pidData) {
                                        foreach ($pidData as $mainTid => $mainTidData) {
                                            foreach ($mainTidData as $mainType => $mainTypeData) {
                                                foreach ($mainTypeData as $channel => $channelData) {
                                                    foreach ($channelData as $paymode => $paymodeData) {
                                                        foreach ($paymodeData as $subMerchantId => $subMerchantData) {
                                                            foreach ($subMerchantData as $level => $levelData) {
                                                                foreach ($levelData as $site => $siteData) {
                                                                    foreach ($siteData as $operate => $numData) {

                                                                        $tmp = [
                                                                            'date'               => $this->_month,
                                                                            'fid'                => $fid,
                                                                            'tid'                => $tid,
                                                                            'lid'                => $lid,
                                                                            'pid'                => $pid,
                                                                            'site_id'            => $site,
                                                                            'main_tid'           => $mainTid,
                                                                            'main_type'          => $mainType,
                                                                            'level'              => $level,
                                                                            'channel'            => $channel,
                                                                            'pay_way'            => $paymode,
                                                                            'operate_id'         => $operate,
                                                                            'reseller_id'        => $sellerId,
                                                                            'update_time'        => $time,
                                                                            'today_check'        => $isCheckSameDay,
                                                                            'sub_merchant_id'    => $subMerchantId,
                                                                            'orders_info'        => json_encode($numData['orders_info']),
                                                                            'finish_orders_info' => json_encode($numData['finish_orders_info']),
                                                                            'revoke_orders_info' => json_encode($numData['revoke_orders_info']),
                                                                            'after_sale_info'    => json_encode($numData['after_sale_info']),
                                                                        ];

                                                                        $insertData[] = array_merge($numData, $tmp);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //更新数据
                if (!empty($insertData)) {
                    $lastId = $statisModel->_insertDataArr($insertData, 'checked_v2_month');
                }
            }
        }
    }

}