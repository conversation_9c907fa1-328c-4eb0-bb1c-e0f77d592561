<?php

namespace Business\Statistics;
use \Library\Cache\Cache;
use  Business\Admin\ModuleConfig;

/**
 * 平台报表特殊定制型
 *
 * <AUTHOR>
 * @date  2021-07-06
 */
class CreateCustomizeReport extends CreateReportBase
{

    //分销商供应商ID
    protected $_fid = [];


    public function __construct($logPath)
    {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'customize_report:start');
        parent::__construct();
    }


    /**
     * 开始跑脚本
     *
     */
    public function runTask($date, $fid = 0)
    {
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        if ($fid) {
            $this->_fid = [$fid];
        } else {

            //开通了云票务窗口实收金额功能的用户
            $sidList = ModuleConfig::getSidByAppid(ModuleConfig::STATIS_REPORT_WINDOW_REAL_MONEY);
            // $sidList = [6970];
            if (empty($sidList)) {
                return ['code' => 200, 'msg' => '没有开通云票务窗口实收金额功能的用户'];
            }

            $this->_fid = $sidList;
        }

        $msg  = '';

        try {

            $maxNum   = 6;
            $dateList = $this->_getDateListNew($date);
            $dateNum  = count($dateList);

            foreach ($this->_fid as $tmpFid) {
                $this->_clearPreData($date, 'customize_report', $tmpFid);
            }

            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $times = ceil($dateNum / $maxNum);
            for ($i = 0; $i < $times; $i++) {
                sleep(1);
                $handleDateList = array_slice($dateList, $i * $maxNum, $maxNum);
                $procNum = count($handleDateList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task = new \Library\MulityProcessHelper($this, $procNum, 1);
                $task->run($handleDateList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }

            //$date = ['2021-08-10 09:00:00', '2021-08-10 22:00:00'];
            //$this->runCustomizeWorker($date);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "customize_report:end:{$msg}");
    }


    /**
     * 子进程脚本l
     */
    public function runWorker($dateList)
    {
        if (is_array($dateList)) {
            $dateList = array_shift($dateList);
        }

        $code = 200;
        $msg  = '';

        try {
            $this->runCustomizeWorker($dateList);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str = 'customize_report:';
        $str .= $dateList[0] . ' - ' . $dateList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;
        pft_log($this->_logPath, $str);
    }


    public function runCustomizeWorker($date)
    {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) ||
            (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24)) {
            throw new \Exception('时间格式错误', 403);
        }

        $day = date('Ymd', strtotime($date[0]));
        $filterOrderNum = $this->_getFidOrderNum($date);
        $total     = $this->_getTrackTotal($date, 'order_v2', $filterOrderNum);
        $totalPage = ceil($total / $this->_selectSize);

        $checkedData = [];
        $mainTid     = [];
        //站点
        $siteIdArr = [];

        //订单退票手续费对应的track_id
        $cancelTrackData = [];

        //分页获取数据
        for ($page = 1; $page <= $totalPage; $page++) {
            $list = $this->_getTrackList($date, 'order_v2', $page, $this->_selectSize, $filterOrderNum);
            //如果查询出错了,直接返回错误
            if ($list === false) {
                throw new \Exception("查询出错");
            }

            foreach ($list as $item) {

                $action     = $item['action'];
                $tnum       = $item['tnum'];
                $orderNum   = strval($item['ordernum']);
                $operMember = $item['oper_member'];

                if (!in_array($action, [1, 2, 4, 6 ,7])) {
                    continue;
                }

                $tmpExtContent = json_decode($item['ext_content'], true);
                //是否是云票务在线支付或取消的  source = 4 是云票务现金支付或取消的 pay_source = 4 是云票务在线支付的（微信支付宝）
                if ($item['source'] != 4 && (empty($tmpExtContent['pay_source']) || $tmpExtContent['pay_source'] != 4)) {
                    continue;
                }

                //累加数据  预订
                if (isset($checkedData[$orderNum])) {

                    if (in_array($action, [1, 2 ,6, 7])) {
                        $checkedData[$orderNum]['cancel'] += $tnum;
                    }

                    if ($action == 4) {
                        $checkedData[$orderNum]['order'] += $tnum;
                    }
                } else {

                    if (in_array($action, [1, 2 ,6, 7])) {
                        $checkedData[$orderNum]['cancel'] = $tnum;
                        $checkedData[$orderNum]['order']  = 0;
                    }

                    if ($action == 4) {
                        $checkedData[$orderNum]['order']  = $tnum;
                        $checkedData[$orderNum]['cancel'] = 0;
                    }
                }

                //记录订单不同操作的操作人员及操作票数
                if (isset($checkedData[$orderNum]['oper_member'][$action][$operMember])) {
                    $checkedData[$orderNum]['oper_member'][$action][$operMember]['tnum'] += $tnum;
                } else {
                    $checkedData[$orderNum]['oper_member'][$action][$operMember]['tnum'] = $tnum;
                }

                //手续费操作id记录
                if (in_array($item['action'], [1, 2, 6, 7])) {

                    //生成统计key，用来区分汇总后track_id
                    $checkedDataKey = $this->generateKeywordString(['oper_member', $action, $operMember]);

                    if (!isset($checkedData[$orderNum]['oper_member'][$action][$operMember]['track_ids'])) {
                        //手续费操作id记录
                        $checkedData[$orderNum]['oper_member'][$action][$operMember]['track_ids'] = [];
                        $cancelTrackData[$orderNum][$checkedDataKey] = [];
                    }

                    $checkedData[$orderNum]['oper_member'][$action][$operMember]['track_ids'][] = intval($item['id']);
                    $cancelTrackData[$orderNum][$checkedDataKey][] = intval($item['id']);
                }
            }
        }

        if (isset($list)) {
            unset($list);
        }

        //分页处理数据
        $total     = count($checkedData);
        $totalPage = ceil($total / $this->_pieceNum);

        for ($i = 0; $i < $totalPage; $i++) {
            //套票子票的订单集合
            $packOrderArr = [];
            //套票子票的订单信息集合
            $packOrderInfo = [];

            $queryIds = array_slice($checkedData, $i * $this->_pieceNum, $this->_pieceNum, true);
            //通过订单ID获取订单信息
            $orderIdArr = array_keys($queryIds, true);

            //获取退票手续费的数据
            $cancelServiceArr = [];
            if (!empty($cancelTrackData)) {
                $cancelServiceArr = $this->getCancelServiceByTrackIdGroup($this->matchArrayKeys($cancelTrackData, $orderIdArr));
            }

            //每个订单号对应的主订单号 如果是主订单 则自己和自己对应
            $packInfo  = $this->_getPackInfo($orderIdArr);
            $allOrder  = [];
            $mainOrder = [];
            foreach ($packInfo as $key => $item) {
                //取出所有主订单号
                if ($item == 1) {
                    //主订单
                    $mainOrder[] = $key;
                    array_push($allOrder, (string)$key);
                } else {
                    $mainOrder[] = $item;
                    array_push($allOrder, (string)$key);
                    array_push($allOrder, (string)$item);
                }
            }
            $allOrder = array_merge($allOrder, $orderIdArr);
            $res      = $this->_getOrderInfo($allOrder);

            $orderList  = [];
            $tmpUserArr = [];
            $tidArr     = [];
            //套票主票订单信息
            $packMainOrderList = [];

            foreach ($res as $item) {
                //过滤未支付订单
                if ($item['pay_status'] == 2) {
                    continue;
                }

                $orderNum = $item['ordernum'];
                unset($item['ordernum']);

                if (in_array($orderNum, $orderIdArr)) {
                    $orderList[$orderNum] = $item;
                    if (in_array($item['ordermode'], [11, 15, 18, 43])) {
                        $tmpUserArr[] = $item['member'];
                    }
                }

                //订单号对应的tid
                $tidArr[$orderNum] = $item['tid'];
                //获取套票主票对应的tid
                if (in_array($orderNum, $mainOrder)) {
                    $mainTid[] = $item['tid'];
                    //套票主订单信息
                    $packMainOrderList[$orderNum] = $item;
                }

                if ($item['ordermode'] == 23) {
                    //套票的子票
                    $packOrder[] = $orderNum;
                }
            }

            unset($res);

            //获取订单详情额外信息
            $orderDetailRes  = $this->_getOrderDetailByOrderNum($orderIdArr);
            //获取子商户信息
            $orderSubSidMap = $orderDetailRes['orderSubSidMap'] ?? [];

            //微商城用户获取用户信息。用来判断是不是散客
            $sankeList = [];
            if ($tmpUserArr) {
                $tmpUserArr = array_unique($tmpUserArr);
                $sankeList  = $this->_getSankeList($tmpUserArr);
            }

            //取出主票对应的发布人
            $ticketApplyInfo = $this->_getTicketApplyInfo($mainTid);

            //获取票的顶级供应商
            $ticketApplyData = $this->_getTicketApplyInfo(array_column($orderList, 'tid'));

            //获取分销链数据
            $chainList = $this->_getBuyChain($orderIdArr);

            if ($chainList === false) {
                throw new \Exception("获取分销链失败");
            }

            if (count($chainList) == 0) {
                continue;
            }

            //$orderWindowRealMoneyMap = [];
            //
            ////取出套票子票订单的sale_money
            //foreach ($chainList as $item) {
            //    $tmpOrderId = $item['orderid'];
            //    if (in_array($tmpOrderId, $packOrderArr)) {
            //        $packOrderInfo[$tmpOrderId][$item['buyerid']] = $item['sale_money'];
            //    }
            //    //订单的窗口实收金额
            //    if ($item['level'] == -1 || $item['buyerid'] == 112) {
            //        $orderWindowRealMoneyMap[$tmpOrderId] = $item['sale_money'];
            //    }
            //}

            //是不是最后一个分片
            $isLastSlice = ($i == ($totalPage - 1)) ? true : false;
            $res = $this->_handleCustomizeData($day, $chainList, $queryIds, $orderList, $cancelServiceArr, $isLastSlice, $sankeList, $tidArr,
                $packInfo, $ticketApplyInfo, $siteIdArr, $packMainOrderList, $orderSubSidMap, $ticketApplyData);
            if (empty($res)) {
                throw new \Exception("处理数据失败");
            }
        }
    }


    private function _handleCustomizeData($day, $chainList, $checkedData, $orderList, $cancelInfo, $isLastSlice, $sankeList, $tidArr, $packInfo, $ticketApplyInfo, $siteIdArr, $packMainOrderList, $orderSubSidMap = [], $ticketApplyData = [])
    {
        //首先获取之前的缓存数据
        $splitList = $this->_cacheDataArr($day, 'customize_report', 'get');

        //窗口实收金额
        $moneyMap = [];
        //先处理一次
        foreach ($chainList as $tmpSplit) {
            $orderNum = $tmpSplit['orderid'];
            if (in_array($tmpSplit['level'], [-1, 0]) || $tmpSplit['buyerid'] == 112) {
                $moneyMap[$orderNum] = $tmpSplit['sale_money'];
            }
        }

        for ($i = 0; $i < count($chainList); $i++) {
            $item     = $chainList[$i];
            $orderNum = $item['orderid'];
            if (!array_key_exists($orderNum, $checkedData)) {
                continue;
            }

            if (!array_key_exists($orderNum, $orderList)) {
                continue;
            }

            $money       = $moneyMap[$orderNum];
            $orderInfo   = $orderList[$orderNum];
            $checkedInfo = $checkedData[$orderNum];

            $fid       = $item['sellerid'];
            $sellerId  = $item['buyerid'];
            $level     = $item['level'];
            $saleMoney = $item['sale_money'];
            $paymode   = $item['pmode'];
            $oldSeller = $item['buyerid'];

            if (!in_array($fid, $this->_fid)) {
                continue;
            }

            //站点ID
            $siteId    = isset($siteIdArr[$orderNum]) ? $siteIdArr[$orderNum] : 0;
            $ticketNum = $checkedInfo['order'];
            $cancelNum = $checkedInfo['cancel'];

            //操作人员信息
            $operMemberInfo = isset($checkedInfo['oper_member']) && $checkedInfo['oper_member'] ? $checkedInfo['oper_member'] : [];
            if (empty($ticketNum) && empty($cancelNum)) {
                continue;
            }

            $lid     = $orderInfo['lid'];
            $pid     = $orderInfo['pid'];
            $tid     = $orderInfo['tid'];
            $channel = $orderInfo['ordermode'];

            //子商户id
            $subMerchantId = $orderSubSidMap[$orderNum] ?? 0;

            //存在子商户id，并且非顶级供应商，不展示
            if ($subMerchantId) {
                //获取下订单票的顶级供应商
                $orderTicketApplyDid = $ticketApplyData[$tid] ?? 0;
                if ($orderTicketApplyDid != $fid) {
                    //非顶级供应商，子商户不可见
                    $subMerchantId = 0;
                }
            }

            //通过渠道将散客进行归类
            if (in_array($level, [-1, 0])) {
                $tmp = $this->_getSankeId($sellerId, $fid, $channel, $sankeList);
                if ($tmp !== false) {
                    //最后一级就是散客的情况(自供自销也算在这里面)
                    $level = $level == 0 ? 1 : -3;
                    $sellerId = $tmp;
                }
            }

            $mainTid  = 0;
            $mainType = '';
            if (isset($packInfo[$orderNum]) && $packInfo[$orderNum] == 1) {
                //套票主票
                $mainTid = 0;
            } elseif (isset($packInfo[$orderNum])) {
                //主票的订单号对应的票类ID
                $mainTid  = $tidArr[$packInfo[$orderNum]];
                $mainType = $packMainOrderList[$packInfo[$orderNum]]['product_type'] ?? '';
            }

            if (!empty($mainTid)) {
                $applyDid = $ticketApplyInfo[$mainTid];
                if ($applyDid != $fid) {
                    $mainTid  = 0;
                    $mainType = '';
                }
            }

            $splitTmp = $this->_needAddSplit($level, $sellerId, $fid);
            if ($splitTmp) {
                //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                $splitData = [
                    'orderid'             => $orderNum,
                    'sellerid'            => $sellerId,
                    'buyerid'             => $this->_getChannelSanke($channel),
                    'level'               => -3, //非0/-1
                    'cost_money'          => $saleMoney,
                    'sale_money'          => $needMoney, //默认使用零售价的钱
                    'pmode'               => $paymode,
                ];

                $chainList[] = $splitData;
            }

            //针对单个供应商分销商时
            if (!in_array($item['sellerid'],  $this->_fid)) {
                continue;
            }

            //去除退票手续费
            $orderCancelInfo = $cancelInfo[$orderNum] ?? [];

            if (!isset($splitList[$fid])) {
                $splitList[$fid] = [];
            }

            if (!isset($splitList[$fid][$sellerId])) {
                $splitList[$fid][$sellerId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid])) {
                $splitList[$fid][$sellerId][$pid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid])) {
                $splitList[$fid][$sellerId][$pid][$mainTid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode] = [];
            }
            if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId])) {
                $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId] = [];
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {
                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember] = [];
                    }
                }
            }

            foreach ($operMemberInfo as $action => $info) {
                foreach ($info as $oper => $ticketInfo) {
                    $operMember = $oper;
                    if (!isset($splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId])) {
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId] = [
                            'lid'                         => $lid,
                            'tid'                         => $tid,
                            'lvl'                         => $level,
                            'window_real_money'           => 0,
                            'window_real_orders_info'     => [],
                            'window_cancel_money'         => 0,
                            'window_cancel_orders_info'   => [],
                            'window_cancel_service_money' => 0,
                        ];
                    }
                }
            }

            //整合数据
            if ($ticketNum > 0) {
                foreach ($operMemberInfo as $action => $info) {
                    //预订
                    if (!in_array($action, [4])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {
                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];
                        //支付数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['window_real_money'] += $money * $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['window_real_orders_info'][] = [$orderNum, $operTicketNum];
                    }
                }
            }

            if ($cancelNum > 0) {
                foreach ($operMemberInfo as $action => $info) {
                    //预订
                    if (!in_array($action, [1, 2, 6, 7])) {
                        continue;
                    }

                    foreach ($info as $oper => $ticketInfo) {
                        $operMember    = $oper;
                        $operTicketNum = $ticketInfo['tnum'];
                        $trackIds = $ticketInfo['track_ids'] ?? [];

                        //手续费计算
                        $serviceMoney = 0;
                        if (!empty($trackIds) && !empty($orderCancelInfo)) {
                            $arrayValues = array_values($trackIds);
                            sort($arrayValues);

                            //生成id的key
                            $trackKey = $this->generateKeywordString($arrayValues);

                            $cancelInfoItem = $orderCancelInfo[$trackKey] ?? [];

                            //todo::这边窗口取消手续费不知道为什么怎取，先延用之前的
                            if (!empty($cancelInfoItem)) {
                                foreach ($cancelInfoItem as $k => $v) {
                                    if ($v['fid'] == $oldSeller && $v['aid'] == $fid) {
                                        $serviceMoney = $v['money'];
                                    }
                                }
                            }
                        }

                        //支付数据处理
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['window_cancel_money'] += $money * $operTicketNum;
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['window_cancel_orders_info'][] = [$orderNum, $operTicketNum];
                        $splitList[$fid][$sellerId][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$operMember][$siteId]['window_cancel_service_money'] += $serviceMoney;
                    }
                }
            }
        }

        //如果不是最后一个分片，就先将数据写入缓存文件
        if (!$isLastSlice) {
            $this->_cacheDataArr($day, 'customize_report', 'set', $splitList);
            unset($splitList);
            return true;

        } else {
            //如果是最后一个，就将之前的数据清除
            $this->_cacheDataArr($day, 'customize_report', 'clear');
        }

        $updateTime = time();
        $date       = str_replace('-', '', $day);

        //分批插入数据
        $insertData   = [];
        foreach ($splitList as $fid => $fidData) {
            foreach ($fidData as $sellerId => $sellerData) {
                foreach ($sellerData as $pid => $pidData) {
                    foreach ($pidData as $mainTid => $mainTidData) {
                        foreach ($mainTidData as $mainType => $mainTypeData) {
                            foreach ($mainTypeData as $channel => $channelData) {
                                foreach ($channelData as $paymode => $paymodeData) {
                                    foreach ($paymodeData as $subMerchantId => $subMerchantData) {
                                        foreach ($subMerchantData as $operMember => $operMemberData) {
                                            foreach ($operMemberData as $siteId => $data) {
                                                //入库数据处理
                                                $tmp = $data;

                                                $tmp['fid']                       = $fid;
                                                $tmp['pay_way']                   = $paymode;
                                                $tmp['window_real_orders_info']   = json_encode($tmp['window_real_orders_info']);
                                                $tmp['window_cancel_orders_info'] = json_encode($tmp['window_cancel_orders_info']);
                                                $tmp['reseller_id']               = $sellerId;
                                                $tmp['pid']                       = $pid;
                                                $tmp['cal_date']                  = $date;
                                                $tmp['channel']                   = $channel;
                                                $tmp['operate_id']                = $operMember;
                                                $tmp['update_time']               = $updateTime;
                                                $tmp['site_id']                   = $siteId;
                                                $tmp['main_tid']                  = $mainTid;
                                                $tmp['sub_merchant_id']           = $subMerchantId;
                                                $tmp['main_type']                 = $mainType;

                                                $insertData[] = $tmp;
                                                if (count($insertData) >= $this->_insertLimit) {
                                                    //达到条数就插入数据
                                                    $lastId = $this->_insertDataArr($insertData, 'customize_report');
                                                    //初始化
                                                    $insertData = [];
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        //将没有插入的数据最后插入
        if ($insertData) {
            $lastId = $this->_insertDataArr($insertData, 'customize_report');
        }

        //返回
        return $lastId;
    }


    /**
     * 获取单个分销商供应商订单数据
     * <AUTHOR>
     * @date   2019-07-08
     *
     * @param  int  $fid  分销商供应商ID
     * @param  array  $date  时间区间
     * @param  string  $type  类型
     *
     * @return array
     */
    private function _getFidOrderNum($date)
    {
        $totalOrder = [];
        foreach ($this->_fid as $tmpFid) {
            //支付订单
            $fidPayOrder = $this->_getOrderListByFid($tmpFid, $date[0], $date[1], 'order_v2');
            //取消订单
            $cancelOrder = $this->_getOrderByType($tmpFid, $date, 'cancel');
            $totalOrder    = array_unique(array_merge($fidPayOrder, $cancelOrder, $totalOrder));
        }

        return $totalOrder;
    }
}