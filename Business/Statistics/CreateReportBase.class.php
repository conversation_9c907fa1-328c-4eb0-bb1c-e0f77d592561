<?php

namespace Business\Statistics;

use Business\NewJavaApi\AfterSale\AfterSaleTransaction as AfterSaleTransactionBiz;
use Business\Order\OrderQueryJavaService;
use Library\Constants\Order\OrderPayMode as OrderPayModeConstants;
use Library\Constants\Stats\ExecutionCustomConfigKey as ExecutionCustomConfigKeyConstants;
use Model\Member\Member;
use Model\Order\BuyChain;
use Model\Order\Coupon;
use Model\Order\OrderQuery;
use Model\Order\OrderRefer;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\SubOrderQuery\SubOrderAddon;
use Model\Order\SubOrderQuery\SubOrderSplit;
use Model\Order\SubOrderQuery\SubOrderTrack;
use Model\Order\TeamOrderSearch;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Report\Statistics;
use Model\Product\AnnualCard;
use Library\Constants\Order\OrderChannel as OrderChannelConstants;
use Business\Product\ExchangeCouponProduct as ExchangeCouponProductBiz;
use Library\Constants\Discount\DiscountType as OrderDiscountTypeConstants;
use Business\Statistics\StatisticsDiscounts as StatisticsDiscountsBiz;
use Library\Util\ReportBlacklistUtil;

/**
 * 生成报表的基类
 *
 * <AUTHOR>
 * @date   2018-07-13
 */
class CreateReportBase extends \Business\Base
{
    //分终端入园计入报表版本上线时间
    const ENTER_INTO_REPORT_VERSION_DATE = '2021-05-31 20:30:00';

    protected $_logPath;
    protected $_trackModel;
    protected $_orderToolModel;
    protected $_teamOrderModel;
    protected $_chainModel;
    protected $_orderReferModel;
    protected $_statisticsV3Model;
    protected $_landModel;
    protected $_statisticsModel;
    protected $_couponModel;
    protected $_memberModel;
    protected $_SubOrderSplitModel;
    protected $_resellerMap;
    protected $_orderQueryModel;
    protected $_subOrderTrackModel;
    protected $_subOrderAddonModel;
    protected $_ticketModel;
    protected $_annualCardModel;
    protected $_subOrderMainModel;
    protected $_orderTicketDiscountsApi;

    //
    protected $_pieceNum = 2000;
    //批量插入数据库的条数 - 达到这个数据的时候插入
    protected $_insertLimit = 1000;
    //
    protected $_selectSize = 2000;
    //默认的散客
    private $_defaultReseller = 112;

    //分批删除步数
    protected $_deleteSubNum = 2000;
    //每次订单号查询的限制
    protected $_orderSelectSize = 500;
    //修改票的action
    protected $_changeAction = 1;
    //取消订单的action
    protected $_cancelAction = 2;
    //完结的action
    const FINISH_ACTION = 17;
    protected $_finishAction = self::FINISH_ACTION;
    //优惠类型
    private $_couponTypeMap = OrderDiscountTypeConstants::ALL_DISCOUNT_TYPE;
    //游客优惠类型
    const DISCOUNT_COUPON_TYPE_MAP = OrderDiscountTypeConstants::TOURIST_DISCOUNT_TYPE;
    //分销优惠类型
    const SETTLEMENT_DISCOUNT_COUPON_TYPE_MAP = OrderDiscountTypeConstants::SETTLEMENT_DISCOUNT_TYPE;
    //游客优惠
    const DISCOUNT = OrderDiscountTypeConstants::TOURIST_DISCOUNT;
    //分销优惠
    const SETTLEMENT_DISCOUNT = OrderDiscountTypeConstants::SETTLEMENT_DISCOUNT;

    protected function _getTrackModel($force = false)
    {
        if (empty($this->_trackModel) || $force) {
            $this->_trackModel = new OrderTrack(true);
        }

        return $this->_trackModel;
    }

    protected function _getOrderToolModel($force = false)
    {
        if (empty($this->_orderToolModel) || $force) {
            $this->_orderToolModel = new OrderTools();
        }

        return $this->_orderToolModel;
    }

    protected function _getTeamOrderModel($force = false)
    {
        if (empty($this->_teamOrderModel) || $force) {
            $this->_teamOrderModel = new TeamOrderSearch();
        }

        return $this->_teamOrderModel;
    }

    protected function _getChainModel($force = false)
    {
        if (empty($this->_chainModel) || $force) {
            $this->_chainModel = new BuyChain();
        }

        return $this->_chainModel;
    }

    protected function _getOrderReferModel($force = false)
    {
        if (empty($this->_orderReferModel) || $force) {
            $this->_orderReferModel = new OrderRefer();
        }

        return $this->_orderReferModel;
    }

    protected function _getStatisticsV3Model($force = false)
    {
        if (empty($this->_statisticsV3Model) || $force) {
            $this->_statisticsV3Model = new \Model\Report\StatisticsV3();
        }

        return $this->_statisticsV3Model;
    }

    protected function _getStatisticsModel()
    {
        if (empty($this->_statisticsModel)) {
            $this->_statisticsModel = new Statistics();
        }

        return $this->_statisticsModel;
    }

    protected function _getLandModel($force = false)
    {
        if (empty($this->_landModel) || $force) {
            $this->_landModel = new Land();
        }

        return $this->_landModel;
    }

    protected function _getCouponModel()
    {
        if (empty($this->_couponModel)) {
            $this->_couponModel = new Coupon();
        }

        return $this->_couponModel;
    }

    protected function _getMemberModel()
    {
        if (empty($this->_memberModel)) {
            $this->_memberModel = new Member();
        }

        return $this->_memberModel;
    }

    protected function _getSubOrderSplitModel()
    {
        if (empty($this->_SubOrderSplitModel)) {
            $this->_SubOrderSplitModel = new SubOrderSplit();
        }

        return $this->_SubOrderSplitModel;
    }

    protected function _getOrderQueryModel()
    {
        if (empty($this->_orderQueryModel)) {
            $this->_orderQueryModel = new OrderQuery();
        }

        return $this->_orderQueryModel;
    }

    protected function _getSubOrderTrackModel()
    {
        if (empty($this->_subOrderTrackModel)) {
            $this->_subOrderTrackModel = new SubOrderTrack();
        }

        return $this->_subOrderTrackModel;
    }

    protected function _getSubOrderAddonModel()
    {
        if (empty($this->_subOrderAddonModel)) {
            $this->_subOrderAddonModel = new SubOrderAddon();
        }

        return $this->_subOrderAddonModel;
    }

    protected function _getTicketModel()
    {
        if (empty($this->_ticketModel)) {
            $this->_ticketModel = new Ticket();
        }

        return $this->_ticketModel;
    }

    protected function _getAnnualCardModel()
    {
        if (empty($this->_annualCardModel)) {
            $this->_annualCardModel = new AnnualCard();
        }

        return $this->_annualCardModel;
    }
    protected function _getOrderTicketDiscountsApi()
    {
        if (empty($this->_orderTicketDiscountsApi)) {
            $this->_orderTicketDiscountsApi = new \Business\JavaApi\Order\OrderTicketDiscounts();
        }

        return $this->_orderTicketDiscountsApi;
    }

    protected function _unsetAllModel()
    {
        if ($this->_statisticsModel) {
            $this->_statisticsModel->forceShutdown();
        }
        unset($this->_trackModel);
        unset($this->_orderToolModel);
        unset($this->_teamOrderModel);
        unset($this->_chainModel);
        unset($this->_orderReferModel);
        unset($this->_statisticsV3Model);
        unset($this->_landModel);
        unset($this->_statisticsModel);
        unset($this->_orderQueryModel);
        unset($this->_SubOrderSplitModel);
        unset($this->_subOrderTrackModel);
        unset($this->_subOrderAddonModel);
    }

    public function __construct()
    {
        $resellerMapConfig = load_config('reseller_map', 'trade_record');
        foreach ($resellerMapConfig as $key => $item) {
            $this->_resellerMap[$key] = $item['id'];
        }
        set_time_limit(0);


    }

    protected function _getTrackTotal($date, $type, $orderNum = [])
    {
        $actionArr = $this->_getAction($type);
        if (empty($actionArr)) {
            throw new \Exception('action列表不存在' . $type, 403);
        }

        $filter = [
            'action' => ['in', $actionArr],
        ];

        $startTime = date('Y-m-d H:i:s', strtotime($date[0]));
        $endTime   = date('Y-m-d H:i:s', strtotime($date[1]));

        $filter['insertTime'] = ['between', [$startTime, $endTime]];

        //由于分终端验证时 未全部验证的 完结票数为0  所以要取等于0的记录
        if ($type == 'checked_v2') {
            $isChangeTicket = false;
            $filter['tnum'] = ['egt', 0];
        } else {
            $isChangeTicket = true;
            $filter['tnum'] = ['gt', 0];
        }

        if (!empty($orderNum) && is_array($orderNum)) {
            $filter['ordernum'] = ['IN', $orderNum];
        }

        /*$queryParams = [$endTime, $startTime, $isChangeTicket, $actionArr];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'queryOrderTrackCountByInsertTime',
            $queryParams);
        $totel       = 0;
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $totel = $queryRes['data'];
        }*/

        // return $totel;
        return $this->_getTrackModel()->getTotal($filter);
    }

    /**
     * 获取追踪数据
     */
    protected function _getTrackList($date, $type, $page, $size, $orderNum = [])
    {
        $actionArr = $this->_getAction($type);
        if (empty($actionArr)) {
            throw new \Exception('action列表不存在' . $type, 403);
        }

        $filter = [
            'action' => ['in', $actionArr],
        ];

        $startTime = date('Y-m-d H:i:s', strtotime($date[0]));
        $endTime   = date('Y-m-d H:i:s', strtotime($date[1]));

        $filter['insertTime'] = ['between', [$startTime, $endTime]];

        //由于分终端验证时 未全部验证的 完结票数为0  所以要取等于0的记录
        if ($type == 'checked_v2') {
            $filter['tnum'] = ['egt', 0];
        } else {
            $filter['tnum'] = ['gt', 0];
        }

        if (!empty($orderNum) && is_array($orderNum)) {
            $filter['ordernum'] = ['IN', $orderNum];
        }

        $field     = 'id, ordernum, tnum, left_num, action, branchTerminal, oper_member, source, SalerID, ext_content, insertTime';
        $trackList = $this->_getTrackModel()->getList($filter, $field, $page, $size);

        return is_array($trackList) ? $trackList : [];
    }

    // 获取最大颗粒度分片，节假日可用（200万<当日订单量<500万）
    // 0点/6点（1小时一批），1点~5点（整批），8点~15点最大档（10分钟），7点/16点~23点中档（30分钟）
    // 最后预测每批次大概（0.5%~2.2%）之间，尽量确保最大分片处理不超过20万单
    protected function _getMaxShardingList($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $list = [
            ['00:00:00', '00:59:59'],
            ['01:00:00', '01:29:59'],['01:30:00', '01:59:59'],
            ['02:00:00', '03:59:59'],
            ['04:00:00', '05:59:59'],
            ['06:00:00', '06:59:59'],
            ['07:00:00', '07:29:59'], ['07:30:00', '07:59:59'],
            ['08:00:00', '08:09:59'], ['08:10:00', '08:19:59'], ['08:20:00', '08:29:59'], ['08:30:00', '08:39:59'], ['08:40:00', '08:49:59'], ['08:50:00', '08:59:59'],
            ['09:00:00', '09:09:59'], ['09:10:00', '09:19:59'], ['09:20:00', '09:29:59'], ['09:30:00', '09:39:59'], ['09:40:00', '09:49:59'], ['09:50:00', '09:59:59'],
            ['10:00:00', '10:09:59'], ['10:10:00', '10:19:59'], ['10:20:00', '10:29:59'], ['10:30:00', '10:39:59'], ['10:40:00', '10:49:59'], ['10:50:00', '10:59:59'],
            ['11:00:00', '11:09:59'], ['11:10:00', '11:19:59'], ['11:20:00', '11:29:59'], ['11:30:00', '11:39:59'], ['11:40:00', '11:49:59'], ['11:50:00', '11:59:59'],
            ['12:00:00', '12:09:59'], ['12:10:00', '12:19:59'], ['12:20:00', '12:29:59'], ['12:30:00', '12:39:59'], ['12:40:00', '12:49:59'], ['12:50:00', '12:59:59'],
            ['13:00:00', '13:09:59'], ['13:10:00', '13:19:59'], ['13:20:00', '13:29:59'], ['13:30:00', '13:39:59'], ['13:40:00', '13:49:59'], ['13:50:00', '13:59:59'],
            ['14:00:00', '14:09:59'], ['14:10:00', '14:19:59'], ['14:20:00', '14:29:59'], ['14:30:00', '14:39:59'], ['14:40:00', '14:49:59'], ['14:50:00', '14:59:59'],
            ['15:00:00', '15:09:59'], ['15:10:00', '15:19:59'], ['15:20:00', '15:29:59'], ['15:30:00', '15:39:59'], ['15:40:00', '15:49:59'], ['15:50:00', '15:59:59'],
            ['16:00:00', '16:09:59'], ['16:10:00', '16:19:59'], ['16:20:00', '16:29:59'], ['16:30:00', '16:39:59'], ['16:40:00', '16:49:59'], ['16:50:00', '16:59:59'],
            ['17:00:00', '17:09:59'], ['17:10:00', '17:19:59'], ['17:20:00', '17:29:59'], ['17:30:00', '17:39:59'], ['17:40:00', '17:49:59'], ['17:50:00', '17:59:59'],
            ['18:00:00', '18:29:59'], ['18:30:00', '18:59:59'],
            ['19:00:00', '19:29:59'], ['19:30:00', '19:59:59'],
            ['20:00:00', '20:29:59'], ['20:30:00', '20:59:59'],
            ['21:00:00', '21:29:59'], ['21:30:00', '21:59:59'],
            ['22:00:00', '22:09:59'], ['22:10:00', '22:19:59'], ['22:20:00', '22:29:59'], ['22:30:00', '22:39:59'], ['22:40:00', '22:49:59'], ['22:50:00', '22:59:59'],
            ['23:00:00', '23:29:59'], ['23:30:00', '23:59:59'],
        ];

        return array_map(function ($item) use ($date) {
            return [$date . ' ' . $item[0], $date . ' ' . $item[1]];
        }, $list);
    }

    // 中间颗粒度分片。周末使用（100万<当日订单量<200万）
    // 0点~6点（整批），7点-8点（1小时一批），9点~15点（半小时一批），16点（1小时一批），17点~19点（整批），20点-23点（整批）
    // 最后预测每批次大概（2%~6%）之间，尽量确保最大分片处理不超过20万单
    protected function _getMiddleShardingList($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $list = [
            ['00:00:00', '06:59:59'],
            ['07:00:00', '07:59:59'],
            ['08:00:00', '08:59:59'],
            ['09:00:00', '09:29:59'], ['09:30:00', '09:59:59'],
            ['10:00:00', '10:29:59'], ['10:30:00', '10:59:59'],
            ['11:00:00', '11:29:59'], ['11:30:00', '11:59:59'],
            ['12:00:00', '12:29:59'], ['12:30:00', '12:59:59'],
            ['13:00:00', '13:29:59'], ['13:30:00', '13:59:59'],
            ['14:00:00', '14:29:59'], ['14:30:00', '14:59:59'],
            ['15:00:00', '15:29:59'], ['15:30:00', '15:59:59'],
            ['16:00:00', '16:59:59'],
            ['17:00:00', '19:59:59'],
            ['20:00:00', '23:59:59'],
        ];

        return array_map(function ($item) use ($date) {
            return [$date . ' ' . $item[0], $date . ' ' . $item[1]];
        }, $list);
    }

    // 获取最小颗粒度分片。 日常使用（当日订单量<100万）
    // 0点~6点（整批），7点-8点（整批），9点~14点（1个小时一批），15点~16点（整批），17点~23点（整批），
    // 最后预测每批次大概10%左右，尽量确保最大分片处理不超过20万单
    protected function _getMinShardingList($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $list = [
            ['00:00:00', '06:59:59'],
            ['07:00:00', '08:59:59'],
            ['09:00:00', '09:59:59'],
            ['10:00:00', '10:59:59'],
            ['11:00:00', '11:59:59'],
            ['12:00:00', '12:59:59'],
            ['13:00:00', '13:59:59'],
            ['14:00:00', '14:59:59'],
            ['15:00:00', '16:59:59'],
            ['17:00:00', '23:59:59'],
        ];

        return array_map(function ($item) use ($date) {
            return [$date . ' ' . $item[0], $date . ' ' . $item[1]];
        }, $list);
    }

    /**
     * 拆解日期
     */
    protected function _getDateList($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $date = date('Y-m-d', strtotime($date));

        $dateList = [
            [$date . ' 00:00:00', $date . ' 08:59:59'],
            [$date . ' 09:00:00', $date . ' 12:59:59'],
            [$date . ' 13:00:00', $date . ' 16:59:59'],
            [$date . ' 17:00:00', $date . ' 23:59:59'],
        ];

        return $dateList;
    }

    /**
     * 拆解日期(新)
     */
    protected function _getDateListNew($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $date = date('Y-m-d', strtotime($date));

        $dateList = [
            [$date . ' 00:00:00', $date . ' 09:59:59'],
            [$date . ' 10:00:00', $date . ' 11:59:59'],
            [$date . ' 12:00:00', $date . ' 14:59:59'],
            [$date . ' 15:00:00', $date . ' 16:59:59'],
            [$date . ' 17:00:00', $date . ' 23:59:59'],
        ];

        return $dateList;
    }

    /**
     * 拆解日期(新)
     */
    protected function _getDateListCheck($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $date = date('Y-m-d', strtotime($date));
        $dateList = [
            [$date . ' 00:00:00', $date . ' 06:59:59'],
            [$date . ' 07:00:00', $date . ' 07:59:59'],
            [$date . ' 08:00:00', $date . ' 08:29:59'],
            [$date . ' 08:30:00', $date . ' 08:59:59'],
            [$date . ' 09:00:00', $date . ' 09:29:59'],
            [$date . ' 09:30:00', $date . ' 09:59:59'],

            [$date . ' 10:00:00', $date . ' 10:09:59'],
            [$date . ' 10:10:00', $date . ' 10:19:59'],
            [$date . ' 10:20:00', $date . ' 10:29:59'],
            [$date . ' 10:30:00', $date . ' 10:39:59'],
            [$date . ' 10:40:00', $date . ' 10:49:59'],
            [$date . ' 10:50:00', $date . ' 10:59:59'],

            [$date . ' 11:00:00', $date . ' 11:09:59'],
            [$date . ' 11:10:00', $date . ' 11:19:59'],
            [$date . ' 11:20:00', $date . ' 11:29:59'],
            [$date . ' 11:30:00', $date . ' 11:39:59'],
            [$date . ' 11:40:00', $date . ' 11:49:59'],
            [$date . ' 11:50:00', $date . ' 11:59:59'],

            [$date . ' 12:00:00', $date . ' 12:09:59'],
            [$date . ' 12:10:00', $date . ' 12:19:59'],
            [$date . ' 12:20:00', $date . ' 12:29:59'],
            [$date . ' 12:30:00', $date . ' 12:39:59'],
            [$date . ' 12:40:00', $date . ' 12:49:59'],
            [$date . ' 12:50:00', $date . ' 12:59:59'],


            [$date . ' 13:00:00', $date . ' 13:29:59'],
            [$date . ' 13:30:00', $date . ' 13:59:59'],
            [$date . ' 14:00:00', $date . ' 14:29:59'],
            [$date . ' 14:30:00', $date . ' 14:59:59'],
            [$date . ' 15:00:00', $date . ' 15:29:59'],
            [$date . ' 15:30:00', $date . ' 15:59:59'],
            [$date . ' 16:00:00', $date . ' 16:29:59'],
            [$date . ' 16:30:00', $date . ' 16:59:59'],
            [$date . ' 17:00:00', $date . ' 17:59:59'],
            [$date . ' 18:00:00', $date . ' 18:59:59'],
            [$date . ' 19:00:00', $date . ' 19:59:59'],
            [$date . ' 20:00:00', $date . ' 20:59:59'],
            [$date . ' 21:00:00', $date . ' 21:59:59'],
            [$date . ' 22:00:00', $date . ' 22:59:59'],
            [$date . ' 23:00:00', $date . ' 23:59:59'],
        ];

        return $dateList;
    }


    /**
     * 拆解日期(新)
     */
    protected function _getDateListOrder($date)
    {
        if (!strtotime($date)) {
            throw new \Exception($date . '日期格式错误');
        }

        $date = date('Y-m-d', strtotime($date));
        $dateList = [
            [$date . ' 00:00:00', $date . ' 06:59:59'],
            [$date . ' 07:00:00', $date . ' 07:59:59'],
            [$date . ' 08:00:00', $date . ' 08:29:59'],
            [$date . ' 08:30:00', $date . ' 08:59:59'],
            [$date . ' 09:00:00', $date . ' 09:29:59'],
            [$date . ' 09:30:00', $date . ' 09:59:59'],
            [$date . ' 10:00:00', $date . ' 10:29:59'],
            [$date . ' 10:30:00', $date . ' 10:59:59'],
            [$date . ' 11:00:00', $date . ' 11:29:59'],
            [$date . ' 11:30:00', $date . ' 11:59:59'],
            [$date . ' 12:00:00', $date . ' 12:29:59'],
            [$date . ' 12:30:00', $date . ' 12:59:59'],
            [$date . ' 13:00:00', $date . ' 13:29:59'],
            [$date . ' 13:30:00', $date . ' 13:59:59'],
            [$date . ' 14:00:00', $date . ' 14:29:59'],
            [$date . ' 14:30:00', $date . ' 14:59:59'],
            [$date . ' 15:00:00', $date . ' 15:29:59'],
            [$date . ' 15:30:00', $date . ' 15:59:59'],
            [$date . ' 16:00:00', $date . ' 16:29:59'],
            [$date . ' 16:30:00', $date . ' 16:59:59'],
            [$date . ' 17:00:00', $date . ' 17:59:59'],
            [$date . ' 18:00:00', $date . ' 18:59:59'],
            [$date . ' 19:00:00', $date . ' 19:59:59'],
            [$date . ' 20:00:00', $date . ' 20:59:59'],
            [$date . ' 21:00:00', $date . ' 21:59:59'],
            [$date . ' 22:00:00', $date . ' 22:59:59'],
            [$date . ' 23:00:00', $date . ' 23:59:59'],
        ];

        return $dateList;
    }


    /**
     * 获取追踪表类型
     */
    protected function _getAction($type)
    {
        $action = [];
        switch ($type) {
            case 'checked_v2':
                $action = [5, 6, 7, 17, 33, 38];
                break;
            case 'order_v2':
                $action = [1, 2, 3, 6, 7, 4, 16, 38];
                break;
            case 'order':
                $action = [4, 16];
                break;
            case 'checked':
                $action = [5];
                break;
            case 'cancel':
                $action = [1, 2];
                break;
            case 'order_pay':
                $action = [1, 2, 4, 6, 7, 16];
                break;
            case 'check_pay':
                $action = [5, 6, 7, 17];
                break;
            case 'series_order':
                $action = [0, 1, 2, 6, 7, 4, 16];
                break;
            case 'series_checked':
                $action = [0, 5, 6, 7, 17];
                break;
            case 'annual_card_order':
                $action = [18, 19, 20, 22];
                break;
            case 'finish':
                $action = [17];
                break;
            case 'revoke':
                $action = [6, 7];
                break;
            case 'reserve_order':
                $action = [1, 2, 6, 7, 4, 16];
                break;
            case 'order_reserve_daily':
            case 'show_order_reserve_daily':
                $action = [1, 2, 3, 6, 7, 4, 16, 22, 32];
                break;
        }

        return $action;
    }

    /**
     * 跑脚本前清除数据
     */
    public function _clearPreData($day, $type, $fid = 0)
    {
        $date = date('Ymd', strtotime($day));

        //首先删除之前的缓存文件数据
        $res = $this->_cacheDataArr($date, $type, 'clear');

        //如果删除失败了，返回失败
        if ($res == false) {
            throw new \Exception('清除缓存失败:' . $type);
        }

        switch ($type) {
            case 'order':
                //预定报表
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_order', $day);
                break;
            case 'team_check':
                //团队验证
                $model = $this->_getStatisticsV3Model();
                $res   = $model->deleteCheckAllData($day);
                break;
            case 'team_order':
                //团队预定
                $model = $this->_getStatisticsV3Model();
                $res   = $model->deleteCheckAllData($day);
                break;
            case 'checked':
                //验证报表
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_checked', $day);
                break;
            case 'checked_v2':
                //新版验证报表
                $model = $this->_getStatisticsModel();
                //$res   = $model->deleteTableData('pft_report_checked_two', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_checked_two', $day, $fid);//新版验证报表
                break;
            case 'pack_checked':
                //套票验证报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_pack_checked', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_pack_checked', $day, $fid);//套票验证报表
                break;
            case 'cancel':
                //取消报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_cancel', $day);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_cancel', $day);
                break;
            case 'order_pay':
                //营业汇总
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_order_payway', $day);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_order_payway', $day);
                break;
            case 'check_pay':
                //收支汇总
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_checked_payway', $day);
                break;
            case 'order_v2':
                //新版预定报表
                $model = $this->_getStatisticsModel();
                //$res   = $model->deleteTableData('pft_report_order_two', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_order_two', $day, $fid);//新版预定报表
                break;
            case 'pack_order':
                //套票预定报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_pack_order', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_pack_order', $day, $fid);//套票预定报表
                break;
            case 'series_order':
                // 演出报表
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_series_order', $day);
                break;
            case 'series_checked':
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_series_checked', $day);
                break;
            case 'terminal_pass':
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTerminalPassData($day);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_terminal_pass', $day);//终端验证报表
                break;
            case 'resource_order':
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_resource_order', $day, $fid);
                break;
            case 'reserve_order':
                //预约报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_order_reserve', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_order_reserve', $day, $fid);
                break;
            case 'customize_report':
                //特殊定制型报表
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_customize', $day, $fid);
                break;
            case 'terminal_checked':
                //终端验证报表
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteTableData('pft_report_terminal_checked', $day);
                break;
            case 'order_v2_sta':
                //台账预订报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData(\Model\Report\StandingBook::STANDING_BOOK_ORDER, $day);
                //分批删除
                $res = $this->subDeleteTableData(\Model\Report\StandingBook::STANDING_BOOK_ORDER, $day);
                break;
            case 'checked_v2_sta':
                //台账验证报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData(\Model\Report\StandingBook::STANDING_BOOK_CHECKED, $day);
                //分批删除
                $res = $this->subDeleteTableData(\Model\Report\StandingBook::STANDING_BOOK_CHECKED, $day);
                break;
            case 'minute_order_v2':
                //分钟预订报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_order_five_minute', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_order_five_minute', $day, $fid);
                break;
            case 'minute_checked_v2':
                //分钟验证报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_checked_five_minute', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_checked_five_minute', $day, $fid);
                break;
            case 'order_reserve_daily':
                //预约日报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_order_reserve_daily', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_order_reserve_daily', $day, $fid);
                break;
            case 'show_order_reserve_daily':
                //演出预约日报表
                $model = $this->_getStatisticsModel();
                // $res   = $model->deleteTableData('pft_report_show_order_reserve_daily', $day, $fid);
                //分批删除
                $res = $this->subDeleteTableData('pft_report_show_order_reserve_daily', $day, $fid);
                break;
            case 'annual_card_checked':
                //年卡验证报表
                $model = $this->_getStatisticsModel();
                //分批删除
                $res = $this->subDeleteTableData('pft_report_annual_card_checked', $day);
                break;
            case 'annual_card_order':
                //年卡验证报表
                $model = $this->_getStatisticsModel();
                //分批删除
                $res = $this->subDeleteTableData('pft_report_annual_card_order', $day);
                break;

            //case 'terminal_branch':
            //    $model = $this->_getStatisticsModel();
            //    $res   = $model->deleteTableData('pft_report_customize', $day, $fid);
            //    $table = $this->_branchTerminalCheckedTable;
            //    break;
        }

        if ($res === false) {
            throw new \Exception('删除数据出错:' . $model->getDbError());
        }
    }

    /**
     * 跑脚本前清除数据
     */
    protected function _clearPreHourData(array $houtList, string $type, $fid = 0)
    {
        // $res = $this->_cacheDataArr($date, $type, 'clear');
        switch ($type) {
            case 'order_v2_hour':
                //$model = $this->_getStatisticsModel();
                // $res   = $model->deleteHourTableData('pft_report_order_two_hour', $houtList, $fid);
                $table = 'pft_report_order_two_hour';

                break;
            case 'checked_v2_hour':
                // $model = $this->_getStatisticsModel();
                // $res   = $model->deleteHourTableData('pft_report_checked_two_hour', $houtList, $fid);
                $table = 'pft_report_checked_two_hour';

                break;
            case 'terminal_branch_hour':
                // $model = $this->_getStatisticsModel();
                // $res   = $model->deleteHourTableData('terminal_branch_hour_summary', $houtList, $fid);
                $table = 'terminal_branch_hour_summary';

                break;
            case 'terminal_pass_hour':
                // $model = $this->_getStatisticsModel();
                // $res   = $model->deleteHourTableData('pft_report_terminal_pass_hour', $houtList, $fid);
                $table = 'pft_report_terminal_pass_hour';

                break;
            case 'team_order_v2_hour': //团单预订小时报表
                $table = \Model\Team\TeamOrderReport::_REPORT_TEAM_ORDER_;

                break;
            case 'team_check_v2_hour': //团单验证小时报表
                $table = \Model\Team\TeamOrderReport::_REPORT_TEAM_ORDER_CHECK_;

                break;

            default:
                throw new \Exception('未知type:' . $type);
        }

        $model = $this->_getStatisticsModel();
        $res   = $this->subDeleteTableData($table, $houtList, $fid);//分批删除

        if ($res === false) {
            throw new \Exception('删除数据出错:' . $model->getDbError());
        }
    }

    /**
     * 刪除分钟报表数据
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param  string  $type
     * @param  string  $date
     * @param  array   $minuteList
     * @param  int     $fid
     *
     * @return bool
     * @throws
     */
    protected function _clearPreMinuteData(string $type, string $date = '', array $minuteList = [], int $fid = 0)
    {
        switch ($type) {
            case 'minute_order_v2':
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteMinuteReportTableData('pft_report_order_five_minute', $date, $minuteList, $fid);
                break;
            case 'minute_checked_v2':
                $model = $this->_getStatisticsModel();
                $res   = $model->deleteMinuteReportTableData('pft_report_checked_five_minute', $date, $minuteList, $fid);
                break;
            default:
                throw new \Exception('未知type:' . $type);
        }

        if ($res === false) {
            throw new \Exception('删除数据出错:' . $model->getDbError());
        }

        return true;
    }

    /**
     * 分批删除
     * <AUTHOR>
     * @date 2021/5/12
     *
     * @param $table
     * @param $day
     * @param $fid
     *
     * @return bool|mixed
     */
    public function subDeleteTableData($table, $day, $fid = 0)
    {
        $maxSize  = $this->_deleteSubNum;
        $pathLog  = 'statistic_report/del/debug'; //日志目录
        $errorMsg = []; //分批错误记录

        $model = $this->_getStatisticsModel();

        //团单预订和验证小时报表这边处理下，不是summary库是pft001库
        if (in_array($table, [\Model\Team\TeamOrderReport::_REPORT_TEAM_ORDER_, \Model\Team\TeamOrderReport::_REPORT_TEAM_ORDER_CHECK_])) {
            $model = new Statistics('pft001');
        }

        $total = $model->getDeleteTableDataTotal($table, $day, $fid);

        $stime    = microtime(true); //开始时间

        if ($total > $maxSize) {
            //分页删除
            $maxTimes = ceil($total / $maxSize);

            for ($page = 1; $page <= $maxTimes; $page++) {
                $res = $model->deleteTableDataByLimit($table, $day, $fid, $maxSize);
                if ($res === false) {
                    //仅记录下
                    $errorMsg[] = [$table, $day, $fid, $page, $maxSize];
                }
            }
        } else {
            //直接删除
            $res = $model->deleteTableData($table, $day, $fid);
        }

        $stimediff = (microtime(true) - $stime) * 1000;
        pft_log($pathLog, "日期：$day ， $table 删除完成，总计花费：$stimediff 毫秒，删除总数： $total ，每批处理： $maxSize ，异常数据：" . json_encode($errorMsg));

        return $res;
    }

    /**
     * 文件缓存
     *
     * @param $day
     * @param $type
     * @param  string  $operation
     * @param  array  $dataArr
     *
     * @return array|bool|mixed
     */
    protected function _cacheDataArr($day, $type, $operation = 'get', $dataArr = [])
    {
        $pid       = getmypid();
        $path      = BASE_LOG_DIR . "/Calculate/{$type}";
        $cacheFile = $path . "/{$day}_{$pid}.log";

        //创建目录
        if (!file_exists($path)) {
            @mkdir($path, 0777, true);
        }

        if ($operation == 'get') {
            //获取数据
            if (file_exists($cacheFile)) {
                $tmp = file_get_contents($cacheFile);
                $tmp = @json_decode($tmp, true);

                return $tmp ? $tmp : [];
            } else {
                return [];
            }
        } elseif ($operation == 'set') {
            //缓存数据
            $tmp = @json_encode($dataArr);
            $res = file_put_contents($cacheFile, $tmp);
            chmod($cacheFile, 0777);

            return true;
        } else {
            //清除缓存数据
            $res = true;
            if (file_exists($cacheFile)) {
                $res = @unlink($cacheFile);
            }

            return $res;
        }
    }

    /**
     * 获取订单数据
     *
     * @param $orderArr
     * @param  bool  $isGetPayStatus
     * @param  string  $extField
     *
     * @return array
     */
    protected function _getOrderInfo($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        $orderToolModel = $this->_getOrderToolModel();

        //分批获取订单数据，怕一次性数据太多了
        $piece = ceil(count($orderArr) / $this->_selectSize);
        $res   = [];

        $extField = 'ordernum, dtime, tprice, ordertime, dtime, totalmoney, ordermode, lid, tid, ss.pid, member, pay_status, status, tnum, ss.playtime, product_type';
        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);

            //将订单ID转换为字符类型
            $queryIds = array_map(function ($v) {
                return strval($v);
            }, $queryIds);

            $tmp = $orderToolModel->getOrderList($queryIds, $extField, false, false, 'lprice');

            if ($tmp && is_array($tmp)) {
                $res = array_merge($res, $tmp);
            }
        }

        return $res;
    }

    /**
     * 获取优惠信息
     *
     * @param $orderArr
     *
     * @return array
     */
    protected function _getOnSaleRecord($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        $couponModel = new Coupon('localhost');

        $piece = ceil(count($orderArr) / $this->_selectSize);
        $field = 'ordernum, fid, aid, eMoney';
        $res   = [];

        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);

            $tmp = $couponModel->getOrderCouponInfoByArr($queryIds, $field);

            if ($tmp && is_array($tmp)) {
                $res = array_merge($res, $tmp);
            }
        }

        return $res;
    }

    /**
     * 获取散客用户
     *
     * @param  array  $userIdArr
     *
     * @return
     */
    protected function _getSankeList($userIdArr)
    {
        if (!$userIdArr) {
            return [];
        }

        $memberModel = $this->_getMemberModel();
        $res         = $memberModel->getMemberInfoByMulti($userIdArr, 'id', 'id, account, mobile, dtype');
        $res         = $res ? $res : [];

        $sankeArr = [];
        foreach ($res as $item) {
            if ($item['account'] == $item['mobile']) {
                $sankeArr[] = $item['id'];
            }
            //名字带有YKT开通的 是一卡通的散客用户
            if (strpos($item['account'], 'YKT') !== false) {
                $sankeArr[] = $item['id'];
            }
            if ($item['dtype'] == 5 && strlen($item['account']) >= 11) {
                //账号长度大于等于11的为微信生成的普通用户，默认为散客
                $sankeArr[] = $item['id'];
            }
        }

        return $sankeArr;
    }

    /**
     * 获取分销链数据
     *
     * @param  $orderArr
     *
     * @return
     */
    protected function _getBuyChain($orderArr, $isResource = false)
    {
        if (!$orderArr) {
            return [];
        }

        $subOrderSplitModel = $this->_getSubOrderSplitModel();

        //分批获取分销链数据，怕一次性数据太多了
        $piece = ceil(count($orderArr) / $this->_selectSize);
        $field = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode, member_relationship';
        $res   = [];

        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);

            //将订单ID转换为字符类型
            $queryIds = array_map(function ($v) {
                return strval($v);
            }, $queryIds);

            $tmp = $subOrderSplitModel->getListByOrderArr($queryIds, $field, $isResource);

            if ($tmp && is_array($tmp)) {
                $res = array_merge($res, $tmp);
            }
        }

        return $res;
    }

    /**
     * 获取散客ID
     *
     * @param $member
     * @param $aid
     * @param $channel
     * @param $sankeList
     *
     * @return bool
     */
    protected function _getSankeId($member, $aid, $channel, $sankeList)
    {
        $needChannelSanke = false;
        $isYkt            = false;
        if ($member == $aid) {
            if ($channel == 23) {
                return false;
            }

            //自供自销
            $needChannelSanke = true;
        } else {
            //非自供自销
            if ($channel == 11 || $channel == 43) {
                //微商场会会有分销商和散客购买的情况
                if (in_array($member, $sankeList)) {
                    $needChannelSanke = true;
                } else {
                    //正常分销商购买
                    $needChannelSanke = false;
                }
            } else if ($channel == 13) {
                //二级店铺都是散客购买的
                $needChannelSanke = true;
            } else if ($channel == 18) {
                //年卡
                if (in_array($member, $sankeList)) {
                    $needChannelSanke = true;
                } else {
                    //正常分销商购买
                    $needChannelSanke = false;
                }
            } else if ($channel == 15) {
                //智能终端里的园区一卡通
                if (in_array($member, $sankeList) && $member != 112) {
                    $needChannelSanke = true;
                    $isYkt            = true;
                } else {
                    //正常分销商购买
                    $needChannelSanke = false;
                }
            }
            if ($member == $this->_defaultReseller) {
                //自助机，云票务这些新加的渠道，散客都是使用112，这边按渠道进行归类
                $needChannelSanke = true;
            }

            // 小程序也归集到散客
            if (56 == $channel) {
                $needChannelSanke = true;
            }

            //通兑换兑换
            if ($channel == OrderChannelConstants::EXCHANGE_COUPON_REDEEM_CHANNEL) {
                $needChannelSanke = true;
            }
        }

        if ($needChannelSanke) {
            if (array_key_exists($channel, $this->_resellerMap)) {
                if ($isYkt) {
                    return $this->_resellerMap[22];
                } else {
                    return $this->_resellerMap[$channel];
                }
            } else {
                return $this->_defaultReseller;
            }
        } else {
            return false;
        }
    }

    /**
     * 判断是不是需要往split多加一条记录
     * <AUTHOR>
     * @date   2016-08-10
     *
     * @param  int  $level  所处的级别：1=第一级，0=既是1级也是末级，-1=最末级，2=中间级别
     * @param  int  $resellerId  购买用户ID
     * @param  int  $sellerId  卖一方
     *
     * @return bool
     */
    protected function _needAddSplit($level, $resellerId, $sellerId)
    {
        //如果已经是散客购买或是自供自销的就不添加
        if ($resellerId == 112 || ($resellerId == $sellerId)) {
            return false;
        }

        //处于最末级
        if ($level == -1 || $level == 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 通过订单号和卖家ID查询分销链信息
     * @author: zhangyz
     * @date: 2020/5/9
     *
     * @param $ordernum
     * @param $sellerId
     *
     * @return array
     */
    protected function getOrderChainBySellerId($ordernum, $sellerId)
    {
        $splitModel = new SubOrderSplit();
        $field      = 'orderid, buyerid, sellerid, level, cost_money, sale_money, pmode, member_relationship';
        $orderChain = $splitModel->getOrderSplitByOrderIdArr([$ordernum], $sellerId, $field, true);

        return is_array($orderChain) ? $orderChain[0] : [];
    }

    /**
     * 通过渠道获取对应的散客
     * <AUTHOR>
     * @date   2017-03-02
     *
     * @param  $channel
     *
     * @return
     */
    protected function _getChannelSanke($channel)
    {
        if (array_key_exists($channel, $this->_resellerMap)) {
            return $this->_resellerMap[$channel];
        } else {
            return $this->_defaultReseller;
        }
    }

    /**
     * 批量插入统计数据
     * <AUTHOR>
     * @date   2016-08-03
     *
     * @param  $dataArr
     *
     * @return
     */
    protected function _insertDataArr($dataArr, $type)
    {
        $statisticsModel = new Statistics();
        $res             = $statisticsModel->addAllData($dataArr, $type);

        return $res;
    }

    /**
     * 根据订单追踪ID获取退票手续费数据
     * <AUTHOR>
     * @date   2016-08-26
     *
     * @param  $trackIdArr array 订单追踪ID数组
     *
     * @return
     */
    protected function _getCancelInfo($trackIdArr)
    {
        $model = $this->_getOrderQueryModel();

        if (!$trackIdArr) {
            return [];
        }

        //分批获取订单数据，怕一次性数据太多了
        $piece = ceil(count($trackIdArr) / $this->_selectSize);
        $res   = [];

        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($trackIdArr, $i * $this->_selectSize, $this->_selectSize);

            //将追踪ID替换为数字类型
            $queryIds = array_map(function ($v) {
                return intval($v);
            }, $queryIds);

            $tmp = $model->getCancelServiceByTrack($queryIds);

            if ($tmp && is_array($tmp)) {
                if (!$res) {
                    //如果第一批数据不做处理
                    $res = $tmp;
                } else {
                    //将相同订单的退票手续费进行合并
                    foreach ($tmp as $tmpOrderId => $info) {
                        if (isset($res[$tmpOrderId])) {
                            foreach ($res[$tmpOrderId] as $key => &$item) {
                                if (isset($info[$key]) && isset($info[$key]['money'])) {
                                    $item['money'] += intval($info[$key]['money']);
                                }
                            }
                        } else {
                            //直接合并
                            $res[$tmpOrderId] = $info;
                        }
                    }
                }
            }
        }

        return $res;
    }

    /**
     * 根据订单号获取订单追踪记录列表下单的操作员
     * <AUTHOR>
     * @date 2018-01-10
     *
     * @param $orderNums
     *
     * @return array
     */
    protected function _getTrackOrderListByOrderNums($orderNums)
    {
        if (!$orderNums) {
            return [];
        }

        $model = $this->_getSubOrderTrackModel();

        $field = 'ordernum, oper_member, SalerID, source';

        $res = $model->getTrackListByOrderArrAndAction($orderNums, 0, $field);

        return $res;
    }

    /**
     * 过滤订单号 留下是套票的订单号（主票和子票）
     *
     * @param $orderArr  需要过滤的订单号数组
     *
     * @return array|mixed
     */
    protected function _getPackInfo($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        $model    = $this->_getSubOrderAddonModel();
        $packInfo = $model->getPackInfoByOrderArr($orderArr, 'orderid, pack_order');

        $packRes = [];
        foreach ($packInfo as $item) {
            $packRes[$item['orderid']] = $item['pack_order'];
        }

        return $packRes;
    }

    /**
     * 通过票类id获取发布商id
     * <AUTHOR>
     *
     * @param  array  $tid  发布商ID
     */
    protected function _getTicketApplyInfo($tid)
    {
        if (empty($tid)) {
            return [];
        }

        $model = $this->_getTicketModel();

        if (!is_array($tid)) {
            $tid = [$tid];
        }

        $ticketInfo = $model->getMuchTicketInfo($tid, 'id, apply_did');

        return is_array($ticketInfo) ? $ticketInfo : [];
    }


    /**
     * 获取演出场次信息
     * <AUTHOR>
     * @date 2019-05-15
     *
     * @param  array  $orderArr  订单号数组
     *
     * @return array
     */
    protected function _getSeriesInfo($orderArr)
    {
        if (empty($orderArr)) {
            return [];
        }

        $model           = new \Model\Order\OrderTools();
        $orderDetailInfo = $model->getOrderDetailInfo($orderArr, 'orderid, series');

        $res = [];

        // 订单号 => 演出信息
        foreach ($orderDetailInfo as $item) {
            if (empty($item['series']) || !unserialize($item['series'])) {
                continue;
            }

            // 反序列化演出信息
            $item['series'] = unserialize($item['series']);

            $res[$item['orderid']] = $item;
        }

        return $res;
    }

    /**
     * 获取演出票种订单号
     * <AUTHOR>
     * @date 2019-05-21
     *
     * @param  array  $list  订单追踪表数据
     *
     * @return array
     */
    protected function _filterSeriesOrderList($list)
    {
        if (empty($list)) {
            return [];
        }

        $ordernums = array_column($list, 'ordernum');

        // 获取lid
        $orderQueryJavaBiz = new OrderQueryJavaService('master');
        $selectParams      = [
            'field' => 'ordernum, lid',
            'limit' => $this->_selectSize,
        ];
        $orderInfos    = $orderQueryJavaBiz->getOrderInfoByOrderNumAndOtherParam($ordernums, [], $selectParams);
        $orderInfosRes = [];
        foreach ($orderInfos as $item) {
            $orderInfosRes[$item['ordernum']] = $item['lid'];
        }

        // 获取对应lid的
        $lidArr       = array_column($orderInfos, 'lid');
        //$landModel    = $this->_getLandModel();
        //$landInfos    = $landModel->getLandInfoByMuli($lidArr, 'id, p_type');
        $landApi      = new \Business\CommodityCenter\Land();
        $landInfos    = $landApi->queryLandMultiQueryById($lidArr);
        $landInfosRes = [];
        if ($landInfos) {
            foreach ($landInfos as $item) {
                $landInfosRes[$item['id']] = $item['p_type'];
            }
        }

        // 过滤非演出票种
        foreach ($list as $key => $value) {
            if (!isset($landInfosRes[$orderInfosRes[$value['ordernum']]]) || $landInfosRes[$orderInfosRes[$value['ordernum']]] != 'H') {
                unset($list[$key]);
            }
        }

        return $list;
    }

    /**
     * 过滤套票模式下主票不是套票类型的订单
     * <AUTHOR>
     * @date   2023/2/10
     *
     * @param  array  $mainTid    订单门票id
     *
     * @return mixed
     * @throws
     */
    protected function _filterPackTypeOrderTid(array $mainTid)
    {
        if (empty($mainTid)) {
            return [];
        }

        $javaApi    = new \Business\CommodityCenter\Ticket();
        $ticketInfo = $javaApi->queryTicketInfoByIds($mainTid, 'id,pid,title', '', 'title,p_type');
        if (empty($ticketInfo)) {
            pft_log('statistic_report/error', "queryTicketInfoByIds 获取主票信息失败:".json_encode([$mainTid],JSON_UNESCAPED_UNICODE));
            return [];
//            throw new \Exception('获取主票信息失败');
        }

        $notPackTid = [];
        foreach ($ticketInfo as $ticket) {
            if ($ticket['land']['p_type'] != 'F') {
                $notPackTid[] = $ticket['ticket']['id'];
            }
        }

        return $notPackTid;
    }

    /**
     * 获取单个商家的订单号
     * <AUTHOR>
     * @date 2019-07-01
     *
     * @param  integer  $fid  供应商id
     * @param  dateTime  $startTime  开始时间
     * @param  dateTime  $endTime  结束时间
     * @param  string  $type  报表类型
     *
     * @return array
     */
    protected function _getOrderListByFid($fid, $startTime, $endTime, $type)
    {
        if (!in_array($type, ['order_v2', 'checked_v2'])) {
            return [];
        }

        //下单时间
        $orderStart = false;
        $orderEnd   = false;

        //验证时间
        $verifyStart = false;
        $verifyEnd   = false;

        $size = 10000;

        //预订
        if ($type == 'order_v2') {
            $orderStart = $startTime;
            $orderEnd   = $endTime;
        }

        //验证
        if ($type == 'checked_v2') {
            $verifyStart = $startTime;
            $verifyEnd   = $endTime;
        }

        ////在线支付订单
        //$onlinePayOrder = $this->_getOrderReferModel()->getList($fid, false, false, false, false,
        //    $orderStart, $orderEnd, false,false, $verifyStart, $verifyEnd, false, false,
        //    false, 1, false, false, false, false, false, false, false,
        //    false, false, false, false,1, $size, false, false);
        //
        ////现场支付订单
        //$cashPayOrder = $this->_getOrderReferModel()->getList($fid, false, false, false, false,
        //    $orderStart, $orderEnd, false,false, $verifyStart, $verifyEnd, false, false,
        //    false, 0, false, false, false, false, false, false, false,
        //    false, false, false, false,1, $size, false, false);
        //$orderNumList = array_column(array_merge($onlinePayOrder['list'], $cashPayOrder['list']), 'ordernum');

        $orderBiz = new \Business\Order\OrderList();
        $queryRes = $orderBiz->getBusinessOrdernumsNotPageByOrderService($fid, $fid, 0, [$fid], false, false
            ,1, $size, false, false, false, false, $orderStart, $orderEnd, false
            , false, $verifyStart, $verifyEnd, false, false, false, [0, 1]);
        $orderNumList = $queryRes['data']['list'];

        return $orderNumList;
    }

    /**
     * 获取分销商供应商订单数据
     * <AUTHOR>
     * @date   2019-07-08
     *
     * @param  int  $fid  分销商供应商ID
     * @param  array  $date  时间区间
     * @param  string  $type  类型
     *
     * @return array
     */
    protected function _getOrderByType($fid, $date, $type)
    {
        if (!in_array($type, ['cancel', 'finish', 'revoke'])) {
            throw new \Exception("参数type错误");
        }

        $total     = $this->_getTrackTotal($date, $type);
        $totalPage = ceil($total / $this->_selectSize);

        $tmpOrderList  = [];
        $typeOrderList = [];
        $fidOrderList  = [];

        for ($page = 1; $page <= $totalPage; $page++) {
            //分销链数据
            $trackList = $this->_getTrackList($date, $type, $page, $this->_selectSize);

            if ($trackList === false) {
                continue;
            }

            foreach ($trackList as $item) {
                $tmpOrderList[] = strval($item['ordernum']);
            }

            //获取订单
            if (!empty($tmpOrderList)) {
                $orderList = $this->_getOrderToolModel()->getOrderInfo($tmpOrderList, 'ordernum, pay_status') ?: [];
                foreach ($orderList as $orderInfo) {
                    in_array($orderInfo['pay_status'], [0, 1]) && $typeOrderList[] = $orderInfo['ordernum'];
                }
            }

            //分销商数据过滤
            if ($typeOrderList) {
                //获取分销链数据
                $chainList = $this->_getBuyChain($typeOrderList);
                for ($i = 0; $i < count($chainList); $i++) {

                    $item     = $chainList[$i];
                    $orderNum = $item['orderid'];

                    if ($fid == $item['buyerid'] || $fid == $item['sellerid']) {
                        !isset($fidOrderList[$orderNum]) && $fidOrderList[] = $orderNum;
                    }
                }
            }
            unset($tmpOrderList, $typeOrderList);
        }

        return array_unique($fidOrderList);
    }

    /**
     * 小时报表日期拆解
     * <AUTHOR>
     * @date   2022/6/10
     *
     * @param string $date 日期天
     *
     * @return array
     */
    protected function _getDateHourList($date)
    {
        $date     = date('Y-m-d', strtotime($date));
        $intDate  = date('Ymd', strtotime($date));

        $hourList       = [];
        $clearDateList  = [];

        for ($i = 0; $i <= 23; $i++) {
            $hour = $i < 10 ? "0{$i}" : $i;
            $hourList[] = [$date . " {$hour}:00:00", $date . " {$hour}:59:59"];
            $clearDateList[] = $intDate . $hour;
        }

        return [
            $clearDateList,
            $hourList
        ];
    }

    /**
     * 节假日大数据量情况下，更小力度切分
     */
    protected function _getDateHourForHoliday($date) {
        $date = date('Y-m-d', strtotime($date));

        $hourList = [];
        for ($i = 0; $i <= 23; $i++) {
            $hour = $i < 10 ? "0{$i}" : $i;
            if (8 <= $i && $i <= 17) {
                $hourList[] = [$date . " {$hour}:00:00", $date . " {$hour}:09:59"];
                $hourList[] = [$date . " {$hour}:10:00", $date . " {$hour}:19:59"];
                $hourList[] = [$date . " {$hour}:20:00", $date . " {$hour}:29:59"];
                $hourList[] = [$date . " {$hour}:30:00", $date . " {$hour}:39:59"];
                $hourList[] = [$date . " {$hour}:40:00", $date . " {$hour}:49:59"];
                $hourList[] = [$date . " {$hour}:50:00", $date . " {$hour}:59:59"];
            } else {
                $hourList[] = [$date . " {$hour}:00:00", $date . " {$hour}:29:59"];
                $hourList[] = [$date . " {$hour}:30:00", $date . " {$hour}:59:59"];
            }
        }
        return $hourList;
    }

    /**
     * 获取指定日期五分钟间隔列表
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param $date
     *
     * @return array
     */
    protected function _getDateMinuteList($date)
    {
        if (empty($date)) {
            return [];
        }
        $date        = date('Y-m-d', strtotime($date));
        $dateList    = [];
        $dateTime    = strtotime(sprintf("%s 00:00:00", $date));
        for ($i = 0; $i < 288; $i++) {
            $tmpTime     = $dateTime + 300 * $i;
            $dateList [] = [date("Y-m-d H:i:s", $tmpTime), date("Y-m-d H:i:s", $tmpTime + 299)];
        }

        return $dateList;
    }

    /**
     * 根据订单号获取订单追踪记录列表支付信息
     * <AUTHOR>
     * @date   2022/9/8
     *
     * @param array $orderNums  订单号数组
     *
     * @return array
     */
    protected function _getTrackOrderListByOrderNumsAndAction(array $orderNums, int $action = 4, string $field = '')
    {
        if (!$orderNums) {
            return [];
        }

        $model = $this->_getSubOrderTrackModel();

        if (empty($field)) {
            $field = 'insertTime, action, ordernum';
        }

        $res = $model->getTrackListByOrderArrAndAction($orderNums, $action, $field);

        return $res;
    }

    /**
     * 套票主票订单非当天下单操作
     * <AUTHOR>
     * @date   2023/2/11
     *
     * @param  array   $mainOrder    主票订单号
     * @param  int     $action       操作类型
     * @param  bool    $isHour       是否是小时报表
     * @param  string  $currentDt    当前对比日期
     *
     * @return array|mixed
     */
    protected function _filterPackMainOrderNotToday(array $mainOrder, string $currentDt, int $action = 4, bool $isHour = false)
    {
        if (empty($mainOrder) || empty($currentDt) || !strtotime($currentDt)) {
            return [];
        }

        $trackTimeListRes = $this->_getTrackOrderListByOrderNumsAndAction($mainOrder, $action);

        $trackTimeListMap = [];
        //可能存在多条记录，取临近的一条作比较
        foreach ($trackTimeListRes as $tmp) {
            $orderId = $tmp['ordernum'];
            $tmpTime = intval(strtotime($tmp['insertTime']));
            if (isset($trackTimeListMap[$orderId])) {
                $trackTimeListMap[$orderId] < $tmpTime && $trackTimeListMap[$orderId] = $tmpTime;
            } else {
                $trackTimeListMap[$orderId] = $tmpTime;
            }
        }

        $today     = date("Ymd", strtotime($currentDt));
        $todayHour = date("YmdH", strtotime($currentDt));

        $mark = $isHour ? $todayHour : $today;

        $notTodayOrder = [];
        foreach ($trackTimeListMap as $ordernum => $insertTime) {
            $insert = $isHour ? date("YmdH", $insertTime) : date("Ymd", $insertTime);

            if ($mark != $insert) {
                $notTodayOrder[] = $ordernum;
            }
        }

        return $notTodayOrder;
    }

    /**
     * 跨天操作补全套票主票数据参数解析
     * <AUTHOR>
     * @date   2023/2/11
     *
     * @param  int     $fid                            商户id
     * @param  string  $orderNum                       订单号
     * @param  array   $packMainOrderList              套票主票订单信息
     * @param  array   $packMainOrderChain             套票主票分销链信息
     * @param  array   $packMainOrderSankeList         套票主票散客信息
     *
     * @return array
     * @throws
     */
    protected function _packMainOrderDecode($fid, $orderNum, $packMainOrderList = [], $packMainOrderChain = [], $packMainOrderSankeList = [], $orderSubSidMap = [])
    {
        $orderInfo   = $packMainOrderList[$orderNum] ?? [];

        $mLid     = $orderInfo['lid'] ?? 0;
        $mPid     = $orderInfo['pid'] ?? 0;
        $mTid     = $orderInfo['tid'] ?? 0;
        $mChannel = $orderInfo['ordermode'];

        //子商户id
        $subMerchantId = $orderSubSidMap[$orderNum] ?? 0;

        $mSellerId = $mLevel = $mPaymode = null;

        foreach ($packMainOrderChain as $item) {
            if ($item['orderid'] == $orderNum && $item['sellerid'] == $fid) {
                $mSellerId = $item['buyerid'];
                $mLevel    = $item['level'];
                $mPaymode  = $item['pmode'];
            }
        }

        //通过渠道将散客进行归类
        if ($mSellerId && $fid && in_array($mLevel, [-1, 0])) {
            $tmp = $this->_getSankeId($mSellerId, $fid, $mChannel, $packMainOrderSankeList);

            if ($tmp !== false) {
                //最后一级就是散客的情况(自供自销也算在这里面)
                if ($mLevel == 0) {
                    $mLevel = 1;
                } else {
                    $mLevel = -3;
                }
                $mSellerId = $tmp;
            }
        }

        //站点ID
        $mSiteId = 0;

        //操作人
        $mOperMember = 0;

        return [
            $mSellerId,
            $mPid,
            $mChannel,
            $mPaymode,
            $mOperMember,
            $mSiteId,
            $mLid,
            $mTid,
            $mLevel,
            $subMerchantId,
        ];
    }

    /**
     * 判断infos订单号是否存在关联
     * <AUTHOR>
     * @date   2023/2/14
     *
     * @param  array  $infos
     * @param  array  $orderIds
     *
     * @return bool|mixed
     */
    protected function _checkInInfosOrderNum(array $infos, array $orderIds = [])
    {
        $check = false;
        foreach ($infos as $item) {
            if (!empty($item[0]) && in_array($item[0], $orderIds)) {
                $check = true;
                break;
            }
        }

        return $check;
    }

    /**
     * 获取订单积分使用信息
     * 【summaryDiscountByOrderNums】接口废弃，这个方法也废弃。
     * 用【$this->getOrderDiscountRecord()】方法替代
     *
     * @param  array  $orderArr  订单号数组
     *
     * @return array
     */
    protected function _getOrderPointsRecord($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        return (new StatisticsDiscountsBiz())->batchOrderDiscountRecord($orderArr);

        // $orderApi    = new \Business\JavaApi\Order\Query\OrderDetailQuery();
        // $totalPage   = ceil(count($orderArr) / $this->_selectSize);
        // $return      = [];
        //
        // for ($i = 0; $i < $totalPage; $i++) {
        //     $queryIds       = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);
        //     $orderDetailArr = $orderApi->getOrderWithDetail($queryIds,true);
        //
        //     if ($orderDetailArr['code'] == 200) {
        //         //获取一次下单时的优惠金额
        //         $discountRes = $this->_getOrderTicketDiscountsApi()->calcDiscountByOrderNums($queryIds,true);
        //         if ($discountRes['code'] == 200 && !empty($discountRes['data'])) {
        //             $discountDetail = array_column($discountRes['data'], null, 'orderNum');
        //         }
        //
        //         foreach ($orderDetailArr['data'] as $orderDetItem) {
        //             $extContent = json_decode($orderDetItem['fxDetails']['extContent'], true);
        //             if (\Business\Order\BaseCheck::checkUseDiscount($orderDetItem['ordernum'], [$orderDetItem['ordernum'] => $extContent]) || \Business\Order\BaseCheck::checkUseSettlementDiscount($orderDetItem['ordernum'], [$orderDetItem['ordernum'] => $extContent])) {
        //                 $return[$orderDetItem['ordernum']][$orderDetItem['aid']][$orderDetItem['member']] =
        //                     [
        //                         //游客优惠金额
        //                         self::DISCOUNT => ($discountDetail[$orderDetItem['ordernum']]['couponPrice'] ?? 0) +
        //                             ($discountDetail[$orderDetItem['ordernum']]['pointPrice'] ?? 0) +
        //                             ($discountDetail[$orderDetItem['ordernum']]['memberDiscountPrice'] ?? 0),
        //                         //分销优惠金额
        //                         self::SETTLEMENT_DISCOUNT =>
        //                             ($discountDetail[$orderDetItem['ordernum']]['settlementDeductPrice'] ?? 0) +
        //                             ($discountDetail[$orderDetItem['ordernum']]['settlementSpecialPrice'] ?? 0)
        //                     ];
        //             }
        //         }
        //     }
        // }
        //
        // return $return;
    }

    /**
     * 获取订单积分使用信息
     *
     * @param  array  $orderArr  订单号数组
     *
     * @return array
     */
    protected function _getOrderPointsIdxRecord($orderArr)
    {
        if (!$orderArr) {
            return [];
        }

        $totalPage = ceil(count($orderArr) / $this->_orderSelectSize);
        $return    = [];

        for ($i = 0; $i < $totalPage; $i++) {
            $queryIds = array_slice($orderArr, $i * $this->_orderSelectSize, $this->_orderSelectSize);
            $queryRes = $this->_getOrderTicketDiscountsApi()->batchQueryList($queryIds,true);
            if ($queryRes['code'] == 200 && $queryRes['data']) {
                foreach ($queryRes['data'] as $item) {
                    // $return[$item['orderNum']][$item['serialNum']][$item['couponType']] = $item;
                    //由于接口返回的是优惠明细，需要重新聚合计算下
                    if (!isset($return[$item['orderNum']][$item['serialNum']][$item['couponType']])) {
                        $return[$item['orderNum']][$item['serialNum']][$item['couponType']] = [
                            "couponPrice" => 0,
                            "couponType"  => $item['couponType'],
                            "couponValue" => '',
                            "description" => '',
                            "orderNum"    => $item['orderNum'],
                            //叠加优惠的时候，实际支付金额都一样，这边不做累加
                            "payPrice"    => $item['payPrice'],
                            "serialNum"   => $item['serialNum'],
                            "status"      => $item['status'],
                        ];
                    }
                    $return[$item['orderNum']][$item['serialNum']][$item['couponType']]['couponPrice'] += $item['couponPrice'];
                    if (!empty($item['couponValue'])) {
                        $return[$item['orderNum']][$item['serialNum']][$item['couponType']]['couponValue'] .= ',' . $item['couponValue'];
                    } else {
                        $return[$item['orderNum']][$item['serialNum']][$item['couponType']]['couponValue'] .= $item['couponValue'];
                    }
                    if (!empty($item['description'])) {
                        $return[$item['orderNum']][$item['serialNum']][$item['couponType']]['description'] .= ',' . $item['description'];
                    } else {
                        $return[$item['orderNum']][$item['serialNum']][$item['couponType']]['description'] .= $item['description'];
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 处理积分优惠汇总信息
     *
     * @param  string  $orderNum  订单号
     * @param  array  $discountIdxList  积分、优惠券操作下标信息
     * @param  array  $pointCancelIdxArr  积分取消操作下标数据
     * @param  array  $pointRevokeIdxArr  积分撤销操作下标数据
     * @param  array  $pointCheckIdxArr  积分验证操作下标数据
     * @param  bool  $isCheckReport  是否是验证报表
     *
     * @return array
     */
    protected function _handlePointsIdxAmount(string $orderNum, array $discountIdxList, array $pointCancelIdxArr = [], array $pointRevokeIdxArr = [], array $pointCheckIdxArr = [], bool $isCheckReport = false): array
    {
        if (!$orderNum || !$discountIdxList || (!$pointCancelIdxArr && !$pointRevokeIdxArr && !$pointCheckIdxArr && !$isCheckReport)) {
            return [];
        }

        //下标结构处理
        if ($pointCancelIdxArr) {
            foreach ($pointCancelIdxArr as $opr => $actionInfo) {
                foreach ($actionInfo as $action => $value) {
                    $idxArr = [];
                    foreach ($value as $actionIdxs) {
                        $idxInfo = explode(',', $actionIdxs);
                        foreach ($idxInfo as $item) {
                            $idx = explode('-', $item);
                            for ($i = $idx[0]; $i <= $idx[1]; $i++) {
                                $idxArr[] = $i;
                            }
                        }
                    }
                    $pointCancelIdxArr[$opr][$action] = array_values(array_unique($idxArr));
                }
            }
        }

        //下标结构处理
        if ($pointRevokeIdxArr) {
            foreach ($pointRevokeIdxArr as $opr => $actionInfo) {
                foreach ($actionInfo as $action => $value) {
                    $idxArr = [];
                    foreach ($value as $actionIdxs) {
                        $idxInfo = explode(',', $actionIdxs);
                        foreach ($idxInfo as $item) {
                            $idx = explode('-', $item);
                            for ($i = $idx[0]; $i <= $idx[1]; $i++) {
                                $idxArr[] = $i;
                            }
                        }
                    }
                    $pointRevokeIdxArr[$opr][$action] = array_values(array_unique($idxArr));
                }
            }
        }

        //下标结构处理
        if ($pointCheckIdxArr) {
            foreach ($pointCheckIdxArr as $opr => $actionInfo) {
                foreach ($actionInfo as $action => $value) {
                    $idxArr = [];
                    foreach ($value as $actionIdxs) {
                        $idxInfo = explode(',', $actionIdxs);
                        foreach ($idxInfo as $item) {
                            $idx = explode('-', $item);
                            for ($i = $idx[0]; $i <= $idx[1]; $i++) {
                                $idxArr[] = $i;
                            }
                        }
                    }
                    $pointCheckIdxArr[$opr][$action] = array_values(array_unique($idxArr));
                }
            }
        }

        $return = [];

        //循环积分操作下标信息
        foreach ($discountIdxList as $tmpOrderNum => $item) {
            //再循环下标 汇总出每种操作优惠总额
            //取消操作的优惠金额汇总
            if ($tmpOrderNum == $orderNum && $pointCancelIdxArr) {
                foreach ($pointCancelIdxArr as $opr => $actionInfo) {
                    foreach ($actionInfo as $action => $value) {
                        foreach ($value as $cancelIdx) {
                            foreach ($this->_couponTypeMap as $couponType) {
                                if (isset($item[$cancelIdx][$couponType]) && in_array($couponType, self::DISCOUNT_COUPON_TYPE_MAP)) {
                                    $return[$orderNum][$opr][$action][self::DISCOUNT]['cancel_amount'] += $item[$cancelIdx][$couponType]['couponPrice'];
                                }
                                if (isset($item[$cancelIdx][$couponType]) && in_array($couponType, self::SETTLEMENT_DISCOUNT_COUPON_TYPE_MAP)) {
                                    $return[$orderNum][$opr][$action][self::SETTLEMENT_DISCOUNT]['cancel_amount'] += $item[$cancelIdx][$couponType]['couponPrice'];
                                }
                            }
                        }
                    }
                }
            }
            //撤销撤改操作的优惠金额汇总
            if ($tmpOrderNum == $orderNum && $pointRevokeIdxArr) {
                foreach ($pointRevokeIdxArr as $opr => $actionInfo) {
                    foreach ($actionInfo as $action => $value) {
                        foreach ($value as $revokeIdx) {
                            foreach ($this->_couponTypeMap as $couponType) {
                                if (isset($item[$revokeIdx][$couponType]) && in_array($couponType, self::DISCOUNT_COUPON_TYPE_MAP)) {
                                    $return[$orderNum][$opr][$action][self::DISCOUNT]['revoke_amount'] += $item[$revokeIdx][$couponType]['couponPrice'];
                                }
                                if (isset($item[$revokeIdx][$couponType]) && in_array($couponType, self::SETTLEMENT_DISCOUNT_COUPON_TYPE_MAP)) {
                                    $return[$orderNum][$opr][$action][self::SETTLEMENT_DISCOUNT]['revoke_amount'] += $item[$revokeIdx][$couponType]['couponPrice'];
                                }
                            }
                        }
                    }
                }
            }
            //验证操作的优惠金额汇总
            if ($tmpOrderNum == $orderNum && $pointCheckIdxArr) {
                foreach ($pointCheckIdxArr as $opr => $actionInfo) {
                    foreach ($actionInfo as $action => $value) {
                        foreach ($value as $checkIdx) {
                            foreach ($this->_couponTypeMap as $couponType) {
                                if (isset($item[$checkIdx][$couponType]) && in_array($couponType, self::DISCOUNT_COUPON_TYPE_MAP)) {
                                    $return[$orderNum][$opr][$action][self::DISCOUNT]['check_amount'] += $item[$checkIdx][$couponType]['couponPrice'];
                                }

                                if (isset($item[$checkIdx][$couponType]) && in_array($couponType, self::SETTLEMENT_DISCOUNT_COUPON_TYPE_MAP)) {
                                    $return[$orderNum][$opr][$action][self::SETTLEMENT_DISCOUNT]['check_amount'] += $item[$checkIdx][$couponType]['couponPrice'];
                                }
                            }
                        }
                    }
                }
            }

            //验证报表需要记录完结信息（未使用的）
            if ($tmpOrderNum == $orderNum && $isCheckReport) {
                foreach ($item as $value) {
                    foreach ($value as $discountInfo) {
                        if ($discountInfo['status'] == 1 && in_array($discountInfo['couponType'], self::DISCOUNT_COUPON_TYPE_MAP)) {
                            $return[$orderNum][$this->_finishAction][self::DISCOUNT]['finish_amount'] += $discountInfo['couponPrice'];
                        }
                        if ($discountInfo['status'] == 1 && in_array($discountInfo['couponType'], self::SETTLEMENT_DISCOUNT_COUPON_TYPE_MAP)) {
                            $return[$orderNum][$this->_finishAction][self::SETTLEMENT_DISCOUNT]['finish_amount'] += $discountInfo['couponPrice'];
                        }
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 处理优惠金额数据
     *
     * @param  array  $pointAndCouponInfo  下单时的优惠金额
     * @param  array  $pointsInfo  每种操作对应的优惠金额
     * @param  string  $orderNum  订单号
     * @param  int  $fid  上级id
     * @param  int  $resellerId  下级id
     * @param  int  $opid  当前登录主账号id
     *
     * @return array
     */
    public static function handleDiscountInfo($pointAndCouponInfo, $pointsInfo, $orderNum, $fid, $resellerId, $oldResellerId = 0)
    {
        //游客优惠金额
        $orderPointMoney  = 0;
        $cancelPointMoney = 0;
        $checkPointMoney  = 0;
        $revokePointMoney = 0;
        $finishPointMoney = 0;
        if (isset($pointAndCouponInfo[$orderNum][$fid][$resellerId][self::DISCOUNT]) && $pointAndCouponInfo[$orderNum][$fid][$resellerId][self::DISCOUNT] > 0) {
            //供应商的情况
            $orderPointMoney  = $pointAndCouponInfo[$orderNum][$fid][$resellerId][self::DISCOUNT] ?? 0;
            $checkPointMoney  = $pointsInfo['checkPointsInfo'][$orderNum][self::DISCOUNT] ?? 0;
            $cancelPointMoney = $pointsInfo['cancelPointsInfo'][$orderNum][self::DISCOUNT] ?? 0;
            $revokePointMoney = $pointsInfo['revokePointsInfo'][$orderNum][self::DISCOUNT] ?? 0;
            $finishPointMoney = $pointsInfo['finishPointsInfo'][$orderNum][self::DISCOUNT] ?? 0;
        }

        //分销优惠金额
        $orderSettlementMoney  = 0;
        $checkSettlementMoney  = 0;
        $cancelSettlementMoney = 0;
        $revokeSettlementMoney = 0;
        $finishSettlementMoney = 0;
        $isSettlementDiscount  = false;
        if (isset($pointAndCouponInfo[$orderNum][$fid][$resellerId][self::SETTLEMENT_DISCOUNT]) && $pointAndCouponInfo[$orderNum][$fid][$resellerId][self::SETTLEMENT_DISCOUNT] > 0) {
            $isSettlementDiscount = true;
            //供应商的情况
            $orderSettlementMoney  = $pointAndCouponInfo[$orderNum][$fid][$resellerId][self::SETTLEMENT_DISCOUNT] ?? 0;
            $checkSettlementMoney  = $pointsInfo['checkPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $cancelSettlementMoney = $pointsInfo['cancelPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $revokeSettlementMoney = $pointsInfo['revokePointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $finishSettlementMoney = $pointsInfo['finishPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
        } elseif ($pointsInfo) {
            //分销商的情况  报团优惠只有末级才有， 所以要判断下报表中增补数据的购买者为112的才为分销优惠
            $orderSettlementMoney  = $pointsInfo['orderPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $checkSettlementMoney  = $pointsInfo['checkPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $cancelSettlementMoney = $pointsInfo['cancelPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $revokeSettlementMoney = $pointsInfo['revokePointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
            $finishSettlementMoney = $pointsInfo['finishPointsInfo'][$orderNum][self::SETTLEMENT_DISCOUNT] ?? 0;
        }

        $discountDetail = [
            self::DISCOUNT => [
                'order_amount'  => $orderPointMoney,
                'check_amount'  => $checkPointMoney,
                'cancel_amount' => $cancelPointMoney,
                'revoke_amount' => $revokePointMoney,
                'finish_amount' => $finishPointMoney,
            ],
            self::SETTLEMENT_DISCOUNT => [
                'order_amount'  => $orderSettlementMoney,
                'check_amount'  => $checkSettlementMoney,
                'cancel_amount' => $cancelSettlementMoney,
                'revoke_amount' => $revokeSettlementMoney,
                'finish_amount' => $finishSettlementMoney,
            ],
            'is_sale_discount' => $isSettlementDiscount,
        ];

        return $discountDetail;
    }

    /**
     * 处理操作类型对应的优惠金额
     *
     * @param  array  $pointAmountDetail  操作对应的优惠信息
     * @param  string  $orderNum  订单号
     * @param  int  $action  操作类型
     * @param  int  $discountMoney  游客优惠金额
     * @param  int  $settlementDiscountMoney  分销优惠金额
     * @param  string  $type  操作类型
     *
     * @return int[]
     */
    public static function handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $action, $discountMoney, $settlementDiscountMoney, $operateId, $type = '')
    {
        $discountAmount = 0;

        //如果是完结动作， 没有操作人
        if ($action == self::FINISH_ACTION) {
            if ($discountMoney && isset($pointAmountDetail[$orderNum]) && $pointAmountDetail[$orderNum][$action][self::DISCOUNT][$type]) {
                $discountAmount = $pointAmountDetail[$orderNum][$action][self::DISCOUNT][$type];
            }
        } elseif ($discountMoney && isset($pointAmountDetail[$orderNum]) && $pointAmountDetail[$orderNum][$operateId][$action][self::DISCOUNT][$type]) {
            //获取末级优惠
            $discountAmount = $pointAmountDetail[$orderNum][$operateId][$action][self::DISCOUNT][$type];
        }

        $settlementAmount = 0;
        //如果是完结动作， 没有操作人
        if ($action == self::FINISH_ACTION) {
            if ($settlementDiscountMoney && isset($pointAmountDetail[$orderNum]) && $pointAmountDetail[$orderNum][$action][self::SETTLEMENT_DISCOUNT][$type]) {
                $settlementAmount = $pointAmountDetail[$orderNum][$action][self::SETTLEMENT_DISCOUNT][$type];
            }
        } elseif ($settlementDiscountMoney && isset($pointAmountDetail[$orderNum]) && $pointAmountDetail[$orderNum][$operateId][$action][self::SETTLEMENT_DISCOUNT][$type]) {
            //获取末级优惠
            $settlementAmount = $pointAmountDetail[$orderNum][$operateId][$action][self::SETTLEMENT_DISCOUNT][$type];
        }

        $actionDiscount = [
            self::DISCOUNT            => $discountAmount,
            self::SETTLEMENT_DISCOUNT => $settlementAmount,
        ];

        return $actionDiscount;
    }

    /**
     * 获取订单详情
     * <AUTHOR>
     * @date   2023/9/27
     *
     * @param array $orderArr 订单集
     *
     * @return array[]
     */
    protected function _getOrderDetailByOrderNum($orderArr)
    {
        $return = [
            'orderSubSidMap' => [],
            'productExtMap'  => [],
        ];

        if (!$orderArr || !is_array($orderArr)) {
            return $return;
        }

        //去重
        $orderArr = array_values(array_unique($orderArr));

        $orderApi  = new \Business\JavaApi\Order\Query\OrderDetailQuery();
        $totalPage = ceil(count($orderArr) / $this->_selectSize);

        for ($i = 0; $i < $totalPage; $i++) {
            $queryIds       = array_slice($orderArr, $i * $this->_selectSize, $this->_selectSize);
            $orderDetailArr = $orderApi->getOrderWithDetail($queryIds,true);

            if ($orderDetailArr['code'] == 200) {
                foreach ($orderDetailArr['data'] as $orderDetItem) {
                    $extContent = json_decode($orderDetItem['fxDetails']['extContent'], true);
                    //获取子商户id
                    if (isset($extContent['subSid'])) {
                        !isset($return['orderSubSidMap']) && $return['orderSubSidMap'] = [];
                        $return['orderSubSidMap'][$orderDetItem['ordernum']] = $extContent['subSid'];
                    }
                    //产品扩展信息
                    $productExt = json_decode($orderDetItem['fxDetails']['productExt'], true);
                    $return['productExtMap'][$orderDetItem['ordernum']] = $productExt;
                }
            }
        }

        return $return;
    }

    /**
     * 根据关键字生成字串
     * <AUTHOR>
     * @date   2023/11/16
     *
     * @param  array  $keyWord
     *
     * @return string
     */
    protected function generateKeywordString(array $keyWord)
    {
        $str = '';
        if (empty($keyWord)) {
            return $str;
        }

        $keyWord = array_map(function ($tmp) {
            if (is_array($tmp)) {
                return 'array';
            }
            if (is_object($tmp)) {
                return 'object';
            }

            return $tmp;
        }, $keyWord);

        return implode('|', $keyWord);
    }

    /**
     * 根据数组的key匹配多个数据
     * <AUTHOR>
     * @date   2023/11/17
     *
     * @param  array  $data 原数据
     * @param  array  $keys  匹配的key
     *
     * @return array
     */
    protected function matchArrayKeys(array $data, array $keys)
    {
        if (empty($data)) {
            return [];
        }

        if (empty($keys)) {
            return $data;
        }

        $tmpData = [];
        foreach ($data as $key => $tmp) {
            if (!in_array($key, $keys)) {
                continue;
            }
            $tmpData[$key] = $tmp;
        }

        return $tmpData;
    }

    /**
     * 按分组操作id获取退票手续费
     * <AUTHOR>
     * @date   2023/11/16
     *
     * @param array $cancelTrackData
     *
     * {
     * "70018743418743": {
     * "66979270|66979271|66979272": [
     * {
     * "fid": 10220978,
     * "aid": 3385,
     * "money": 1600
     * },
     * {
     * "fid": 3385,
     * "aid": 6970,
     * "money": 1400
     * }
     * ],
     * "66979273": [
     * {
     * "fid": 10220978,
     * "aid": 3385,
     * "money": 1600
     * },
     * {
     * "fid": 3385,
     * "aid": 6970,
     * "money": 1400
     * }
     * ]
     * }
     * }
     *
     * @return array
     */
    protected function getCancelServiceByTrackIdGroup($cancelTrackData)
    {
        if (empty($cancelTrackData) || !is_array($cancelTrackData)) {
            return [];
        }

        //每个订单数据处理下
        $cancelTrackData = array_map(function ($tmp) {
            return array_values($tmp);
        }, $cancelTrackData);

        //每个订单内分组合并
        $trackIdArr = array_map(function ($tmp) {
            return array_merge(...$tmp);
        }, $cancelTrackData);

        if (empty($trackIdArr)) {
            return [];
        }

        //合并全部订单操作id
        $trackIdArr = array_merge(...$trackIdArr);

        $model = $this->_getOrderQueryModel();

        //分批获取订单数据，怕一次性数据太多了
        $piece = ceil(count($trackIdArr) / $this->_selectSize);

        $return  = [];
        for ($i = 0; $i < $piece; $i++) {
            $queryIds = array_slice($trackIdArr, $i * $this->_selectSize, $this->_selectSize);

            $res = $model->getCancelServiceByTrackIds($queryIds);

            foreach ($res as $value) {
                $info = @json_decode($value['service_info'], true);
                if (empty($info)) {
                    continue;
                }

                $trackId = $value['track_id'];

                //按操作id分组汇总订单手续费
                foreach ($cancelTrackData as $orderId => $trackArr) {
                    if ($value['orderid'] != $orderId) {
                        continue;
                    }
                    foreach ($trackArr as $ids) {
                        if (!in_array($trackId, $ids)) {
                            continue;
                        }

                        //分组内的操作id排序下
                        sort($ids);

                        //生成id的key
                        $trackKey = $this->generateKeywordString($ids);

                        //初始化数据
                        if (!isset($return[$orderId][$trackKey])) {
                            $return[$orderId][$trackKey] = $info;
                            continue;
                        }

                        //合并数据
                        foreach ($return[$orderId][$trackKey] as $key => &$item) {
                            if (isset($info[$key]) && isset($info[$key]['money'])) {
                                $item['money'] += intval($info[$key]['money']);
                            }
                        }

                        unset($item);
                    }
                }
            }
        }

        return $return;
    }

    /**
     * 根据追踪记录id匹配对应手续费
     * <AUTHOR>
     * @date   2023/11/17
     *
     * @param  int    $fid
     * @param  array  $trackIds
     * @param  array  $orderCancelInfo
     *
     * @return int
     */
    protected function mateCancelServiceMoneyByTrackIdGroup($fid, $trackIds, $orderCancelInfo)
    {
        if (!$fid || empty($trackIds) || empty($orderCancelInfo)) {
            return 0;
        }

        $arrayValues = array_values($trackIds);
        sort($arrayValues);

        //生成id的key
        $trackKey = $this->generateKeywordString($arrayValues);

        $cancelInfo = $orderCancelInfo[$trackKey] ?? [];
        if (empty($cancelInfo)) {
            return 0;
        }

        $statisBiz = new \Business\Statistics\statistics();

        return $statisBiz->countServiceMoney($fid, $cancelInfo);
    }

    /**
     * 多订单号多类型查询
     * <AUTHOR>
     * @date   2023/11/13
     *
     * @param  array   $orderNums
     * @param  array   $actionArr
     * @param  string  $field
     *
     * @return array
     */
    protected function _getTrackOrderListByOrderNumsAndActionArr($orderNums, $actionArr, $field = '')
    {
        if (!$orderNums) {
            return [];
        }

        $model = $this->_getSubOrderTrackModel();

        if (empty($field)) {
            $field = 'ordernum, tnum, action, oper_member, ext_content, insertTime';
        }

        return $model->getTrackListByOrderArrAndActionArr($orderNums, $actionArr, $field);
    }

    /**
     * 根据渠道和支付方式判断是否是预售券兑换订单
     * <AUTHOR>
     * @date   2024/02/18
     *
     * @param $channel
     * @param $payMode
     *
     * @return bool
     */
    protected function _isExchangeCouponRedeemOrders($channel, $payMode)
    {
        return ExchangeCouponProductBiz::isExchangeCouponRedeemOrders($channel, $payMode);
    }

    /**
     * 验证卖出金额是否为0元
     * <AUTHOR>
     * @date   2024/06/06
     *
     * @param $channel
     * @param $payMode
     *
     * @return bool
     */
    protected function _checkSaleMoneyZero($channel, $payMode)
    {
        //预售券权益支付
        $checkExchangeCoupon = ExchangeCouponProductBiz::isExchangeCouponRedeemOrders($channel, $payMode);
        //预存码权益支付
        $checkPreDepositCode = $payMode == OrderPayModeConstants::PRE_DEPOSIT_CODE_PAY_TYPE;

        if ($checkExchangeCoupon || $checkPreDepositCode) {
            return true;
        }

        return false;
    }

    /**
     * 售后编号获取售后数据
     * <AUTHOR>
     * @date   2023/10/24
     *
     * @param  array  $afterSaleCodeArr 售后编号
     *
     * @return array
     */
    protected function queryTransactiondetailMap(array $afterSaleCodeArr)
    {
        if (empty($afterSaleCodeArr)) {
            return [];
        }

        try {
            $afterSaleTransactionBiz = new AfterSaleTransactionBiz();
            $afterSaleCodeArr = array_values(array_unique($afterSaleCodeArr));
            $pageSize = 200;
            $total    = ceil(count($afterSaleCodeArr) / $pageSize);

            $return = [];
            //分批处理数据
            for ($i = 0; $i < $total; $i++) {
                $queryIds = array_slice($afterSaleCodeArr, $i * $pageSize, $pageSize);
                $res = $afterSaleTransactionBiz->queryTransactiondetail($queryIds);

                if ($res['code'] == 200 && !empty($res['data'])) {
                    $data = is_array($res['data']) ? $res['data'] : [];
                    foreach ($data as $tmp) {
                        if (!isset($return[$tmp['orderId']][$tmp['afterSalesNo']]['sellerId'][$tmp['sellerId']])) {
                            $return[$tmp['orderId']][$tmp['afterSalesNo']]['sellerId'][$tmp['sellerId']] = $tmp['refundAmount'] ?? 0;
                        }
                        if (!isset($return[$tmp['orderId']][$tmp['afterSalesNo']]['buyerId'][$tmp['buyerId']])) {
                            $return[$tmp['orderId']][$tmp['afterSalesNo']]['buyerId'][$tmp['buyerId']] = $tmp['refundAmount'] ?? 0;
                        }
                    }
                }
            }
        }catch (\Exception $e) {
            $return = [];
        }

        return $return;
    }

    /**
     * 获取脚本执行时间段
     * <AUTHOR>
     * @date   2025/04/22
     *
     * @param  string  $date    日期 yyyy-mm-dd
     * @parame string $key  颗粒度配置key 为空默认：common 通用配置
     *
     * @return array
     * @throws
     */
    protected function _getExecutionTimeSegments($date, $key = '')
    {
        $dateList = (new \Business\Statistics\ExecutionCustomConfig())->getExecutionTimeSegmentsConfig($date, $key);
        if (empty($dateList)) {
            //获取默认兜底配置
            switch ($key) {
                case ExecutionCustomConfigKeyConstants::HOUR_KEY:
                    $dateList = $this->_getDateHourForHoliday($date);
                    break;
                case ExecutionCustomConfigKeyConstants::FIVE_MINUTE_KEY:
                    $dateList = $this->_getDateMinuteList($date);
                    break;
                default:
                    $dateList = $this->_getMaxShardingList($date);
            }
        }

        return $dateList;
    }

    /**
     * 获取买入优惠金额
     * <AUTHOR>
     * @date   2025/03/27
     *
     * @param $touristMoney
     * @param $settlementMoney
     *
     * @return int
     */
    public function getCostDiscountMoney($touristMoney, $settlementMoney)
    {
        if (empty($touristMoney)) {
            $touristMoney = 0;
        }
        if (empty($settlementMoney)) {
            $settlementMoney = 0;
        }
        return (int)$settlementMoney + (int)$touristMoney;
    }

    /**
     * 获取卖出优惠金额
     * <AUTHOR>
     * @date   2025/03/27
     *
     * @param $touristMoney
     * @param $settlementMoney
     *
     * @return int
     */
    public function getSaleDiscountMoney($touristMoney, $settlementMoney)
    {
        if (empty($touristMoney)) {
            $touristMoney = 0;
        }
        if (empty($settlementMoney)) {
            $settlementMoney = 0;
        }
        return (int)$touristMoney + (int)$settlementMoney;
    }


    /**
     * 判断 fid 是否在报表黑名单中
     *
     * @param int $fid 商户ID
     * @return bool
     */
    protected function isInBlacklist(int $fid): bool
    {
        return ReportBlacklistUtil::isInBlacklist($fid);
    }
}