<?php
/**
 * 报表统计字段
 * <AUTHOR>
 * @date   2022/11/24
 */

namespace Business\Statistics\StatisticsField;

use Business\CommodityCenter\Product;
use Business\Common\DictPayModeService;
use Business\Member\MemberRelation;
use Model\Member\Member;
use Model\Product\Area;
use Model\Product\Terminal;
use Model\Product\Ticket;
use Model\Report\SeparateConfig as SeparateConfigModel;
use Model\Report\TerminalPass;
use Model\Terminal\SiteManage;
use Business\PftShow\ShowManage;

class FieldInfo
{
    private $_landInfo;
    private $_mLandInfo;
    private $_mPidInfo;
    private $_ticketInfo;
    private $_mTicketInfo;
    private $_sTicketInfo;
    private $_cardTidInfo;
    private $_resellerInfo;
    private $_resellerGroup;
    private $_channelInfo;
    private $_operateInfo;
    private $_paywayInfo;
    private $_costPayWayInfo;
    private $_siteArr;
    private $_mainPidInfo;
    private $_didInfo;
    private $_subMerchantInfo;
    private $_ticketSidInfo;
    private $_packSidInfo;
    private $_seriesTimeInfo;
    private $_cardPidInfo;
    private $_terminalInfo;
    //分账对象
    private $_objArr = [];
    private $_provinceCityCodeInfo;
    private $_venuesInfo;
    private $_zoneInfo;
    private $_roundInfo;

    private $_tradeTypeInfo = [
        0 => '售卡',
        1 => '续费',
        2 => '补卡',
        3 => '取消',
        4 => '激活',
        5 => '验证',
    ];

    /**
     * 获取报表名称信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $res     原始数据
     * @param  array  $item    统计维度
     * @param  int    $sid     商户id
     */
    public function getTitleInfo(array $res, array $item, int $sid)
    {
        foreach ($item as $value) {
            switch ($value) {
                case 'mlid':
                    $this->_mLandInfo = $this->_getLandInfo(array_column($res, 'mlid'));
                    break;

                case 'lid':
                    $this->_landInfo = $this->_getLandInfo(array_column($res, 'lid'));
                    break;

                case 'pid':
                    $this->_pidInfo = $this->_getPidInfo(array_column($res, 'pid'));
                    break;

                case 'mpid':
                    $this->_mPidInfo = $this->_getPidInfo(array_column($res, 'mpid'));
                    break;

                case 'mtid':
                    $this->_mTicketInfo = $this->_getTicketInfo(array_column($res, 'mtid'));
                    break;

                case 'stid':
                    $this->_sTicketInfo = $this->_getTicketInfo(array_column($res, 'stid'));
                    break;

                case 'tid':
                    $tidArr    = array_column($res, 'tid');
                    $tmpTidArr = array_column($res, 'main_tid');

                    $this->_ticketInfo = $this->_getTicketInfo($tidArr, $tmpTidArr);
                    break;

                case 'card_tid':
                    $this->_cardTidInfo = $this->_getCardTid(array_column($res, 'card_tid'));
                    break;

                case 'reseller_id':
                    $this->_resellerInfo  = $this->_getResellerInfo(array_column($res, 'reseller_id'));
                    //$this->_resellerGroup = $this->_getResellerGroupInfo($sid);
                    break;

                case 'om':
                case 'channel':
                    $channelInfo        = load_config('order_mode');
                    $channelInfo[0]     = '正常分销商下单';
                    $channelInfo[1]     = '普通用户支付';
                    $channelInfo[2]     = '用户手机支付';
                    $this->_channelInfo = $channelInfo;
                    break;

                case 'operate_id':
                    $this->_operateInfo = $this->_getOperateInfo(array_column($res, 'operate_id'), $sid);
                    break;

                case 'cpm'://买入支付方式
                    $this->_costPayWayInfo = load_config('payway_list');
                    break;
                case 'csm'://卖出支付方式
                case 'pay_way':
                     $this->_paywayInfo = DictPayModeService::getInstance()->businessPayWayListConf($sid);
                    break;

                case 'site_id':
                    $this->_siteArr = $this->_getSiteInfo(array_column($res, 'site_id'), $sid);
                    break;

                case 'oid' : //分账对象
                    $this->_objArr = $this->_getOidInfo(array_unique(array_column($res, 'oid')));
                    break;

                case 'main_pid': //主票产品名称
                    $this->_mainPidInfo = $this->_getMainPidInfo(array_unique(array_column($res, 'main_pid')));
                    break;

                case 'series_time': // 场次
                    $this->_seriesTimeInfo = $this->_getSeriesTimeInfo($res);
                    break;

                case 'terminal':
                    $this->_terminalInfo = $this->_getTerminalInfo(array_column($res, 'terminal'), $sid);
                    break;

                case 'card_pid':
                    $this->_cardPidInfo = $this->_getCardPidInfo(array_unique(array_column($res, 'card_pid')));
                    break;

                case 'did':
                    $this->_didInfo = $this->_getResellerInfo(array_column($res, 'did'));
                    break;

                case 'province':
                    $this->_provinceCityCodeInfo = $this->_getAddressInfo();
                    break;

                case 'sub_merchant_id'://子商户字段解析
                    $this->_subMerchantInfo  = $this->_getResellerInfo(array_column($res, 'sub_merchant_id'));
                    break;

                case 'ticket_sid':
                    $this->_ticketSidInfo = $this->_getResellerInfo(array_column($res, 'ticket_sid'));
                    break;
                case 'pack_sid':
                    $this->_packSidInfo = $this->_getResellerInfo(array_column($res, 'pack_sid'));
                    break;
                case 'venue_id':
                    $this->_venuesInfo = $this->_getVenueInfo(array_column($res, 'venue_id'));
                    break;
                case 'zone_id':
                    $this->_zoneInfo = $this->_getZoneInfo(array_column($res, 'zone_id'));
                    break;
                case 'round_id':
                    $this->_roundInfo = $this->_getRoundInfo(array_column($res, 'round_id'));
                    break;
            }
        }
    }

    /**
     * 获取维度名称
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  string      $item     统计维度
     * @param  int|string  $value    值
     *
     * @return mixed|string
     */
    public function getItemTitle(string $item, $value)
    {
        switch ($item) {
            case 'mlid':
                $res = $this->_mLandInfo[$value] ?? '未知';
                break;

            case 'lid':
                $res = $this->_landInfo[$value] ?? '未知';
                break;

            case 'pid':
                $res = $this->_pidInfo[$value] ?? '未知';
                break;

            case 'mpid':
                $res = $this->_mPidInfo[$value] ?? '未知';
                break;

            case 'mtid':
                $res = $this->_mTicketInfo[$value] ?? '主票';
                break;

            case 'stid':
                $res = $this->_sTicketInfo[$value] ?? '--';
                break;

            case 'tid':
                $res = $this->_ticketInfo[$value] ?? '主票';
                break;

            case 'reseller_id':
                $resGroup = isset($this->_resellerGroup[$value]) ? "({$this->_resellerGroup[$value]})" : '';
                $res      = isset($this->_resellerInfo[$value]) ? $this->_resellerInfo[$value] . $resGroup : '未知';
                break;

            case 'om':
            case 'channel':
                $res = $this->_channelInfo[$value] ?? '未知';
                break;

            case 'operate_id':
                $res = $this->_operateInfo[$value] ?? '';
                break;

            case 'cpm'://买入支付方式
                $res = $this->_costPayWayInfo[$value] ?? '未知';
                break;
            case 'csm':
            case 'pay_way':
                $res = $this->_paywayInfo[$value] ?? '未知';
                break;
            case 'reception_date':
            case 'play_date':
            case 'date':
                $res = $value;
                break;
            case 'main_tid':
                //子票tid
                $res = $this->_ticketInfo[$value] ?? '未知';
                break;
            case 'site_id':
                //站点
                $res = $this->_siteArr[$value] ?? '';
                break;

            case 'oid' ://分账对象
                $res = $this->_objArr[$value] ?? '未知分账对象';
                break;
            case 'main_pid' ://主票产品
                $res = $this->_mainPidInfo[$value] ?? '未知套票产品';
                break;
            case 'series_time': // 场次
                $res = $this->_seriesTimeInfo[$value] ?? '';
                break;
            case 'terminal' :
                $res = $this->_terminalInfo[$value] ?? '未知分终端';
                break;
            case 'card_pid'://卡套餐
                $res = $this->_cardPidInfo[$value] ?? '';
                break;
            case 'card_tid'://年卡门票
                $res = $this->_cardTidInfo[$value] ?? '';
                break;
            case 'card_type'://卡类型
                $cardTypeConf = TerminalPass::__CARD_TYPE__;
                $res          = $cardTypeConf[$value] ?? '';
                break;
            case 'trade_type'://年卡交易类型
                $res = $this->_tradeTypeInfo[$value] ?? '未知交易类型';
                break;
            case 'terminal_type':
                $trackSource = load_config('track_source');
                $res         = $trackSource[$value] ?? '';
                break;
            case 'province':
            case 'city':
                $res = $this->_provinceCityCodeInfo[$value] ?? '未知区域';
                break;
            case 'participate_period':
                if ($value == 0) {
                    $res = '--';
                } else {
                    $res = $value;
                }
                break;
            case 'did':
                $res = $value ? ($this->_didInfo[$value] ?? '未知') : '散客';
                break;
            case 'sub_merchant_id':
                $res = $this->_subMerchantInfo[$value] ?? '';
                break;
            case 'tprice':
                $res = $value;
                break;
            case 'ticket_sid':
                $res = $this->_ticketSidInfo[$value] ?? '未知';
                break;
            case 'pack_sid':
                $res = $this->_packSidInfo[$value] ?? '未知';
                break;
            case 'venue_id':
                $res = $this->_venuesInfo[$value] ?? '--';
                break;
            case 'zone_id':
                $res = $this->_zoneInfo[$value] ?? '--';
                break;
            case 'round_id':
                $res = $this->_roundInfo[$value] ?? '--';
                break;
            case 'fid'://fid,不做处理，原样返回
                $res = $value;
                break;
            default:
                $res = '';
                break;
        }

        return $res;
    }

    /**
     * 获取景区信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $lidArr    景区id数组
     *
     * @return array
     */
    private function _getLandInfo(array $lidArr)
    {
        if (empty($lidArr)) {
            return [];
        }

        $landApi = new \Business\CommodityCenter\Land();
        $landRes = $landApi->queryLandMultiQueryById($lidArr);

        $landInfo = [];
        foreach ($landRes as $v) {
            $landInfo[$v['id']] = $v['title'];
        }

        return $landInfo;
    }

    /**
     * 获取产品信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $pidArr    产品id数组
     *
     * @return array
     */
    private function _getPidInfo(array $pidArr)
    {
        if (empty($pidArr)) {
            return [];
        }

        $pidRes  = (new Product())->getProductInfoByIds($pidArr);
        $pidInfo = [];
        foreach ($pidRes as $v) {
            $pidInfo[$v['id']] = $v['p_name'];
        }

        return $pidInfo;
    }

    /**
     * 获取门票信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $tidArr       门票id数组
     * @param  array  $tmpTidArr    主票id数组
     *
     * @return array
     */
    private function _getTicketInfo(array $tidArr = [], array $tmpTidArr = [])
    {
        if (empty($tidArr) && empty($tmpTidArr)) {
            return [];
        }
        $tidArr = array_merge($tidArr, $tmpTidArr);

        $ticketModel = new Ticket();
        $ticketInfo  = $ticketModel->getTicketList($tidArr, 'id, title');

        $ticketNewInfo = [];
        foreach ($ticketInfo as $k => $v) {
            $ticketNewInfo[$k] = $v['title'];
        }

        return $ticketNewInfo;
    }

    /**
     * 获取卡门票信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $tidArr    门票id数组
     *
     * @return array
     */
    private function _getCardTid(array $tidArr)
    {
        if (empty($tidArr)) {
            return [];
        }
        $ticketModel = new Ticket();
        $ticketInfo  = $ticketModel->getTicketList($tidArr, 'id, title');

        $ticketNewInfo = [];
        foreach ($ticketInfo as $k => $v) {
            $ticketNewInfo[$k] = $v['title'];
        }

        return $ticketNewInfo;
    }

    /**
     * 获取分销商信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $resellerArr    分销商id数组
     *
     * @return array
     */
    private function _getResellerInfo(array $resellerArr)
    {
        if (empty($resellerArr)) {
            return [];
        }
        $memberModel = new Member();
        $resellerRes = $memberModel->getMemberInfoByMulti($resellerArr, 'id', 'id, dname');

        $resellerInfo = [];
        foreach ($resellerRes as $v) {
            $resellerInfo[$v['id']] = $v['dname'];
        }

        return $resellerInfo;
    }

    /**
     * 获取分销商分组信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  int  $fid    商户id
     *
     * @return array|mixed
     */
    private function _getResellerGroupInfo(int $fid)
    {
        if (!$fid) {
            return [];
        }
        // 获取分组
        $getEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $resellerGroup      = $getEvoluteGroupBiz->getFidCountBySid($fid, true);

        return $resellerGroup['data'] ?? [];
    }

    /**
     * 获取操作人信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $operateArr    操作人id数组
     * @param  int    $sid           商户id
     *
     * @return array
     */
    private function _getOperateInfo(array $operateArr, int $sid)
    {
        if (empty($operateArr)) {
            return [];
        }
        $operateArr  = array_unique($operateArr);
        $memberModel = new Member();
        $operateRes  = $memberModel->getMemberInfoByMulti($operateArr, 'id', 'id,dtype,dname');

        // 通过主账号id 查找底下所有员工的id
        $memberRelationBiz = new MemberRelation();
        $staffListArr      = $memberRelationBiz->getMemberStaffListToJava($sid, $operateArr, 'son_id');
        $staffArr          = array_column($staffListArr, 'son_id');
        // 添加自己本身
        $staffArr[] = $sid;
        //把报表查出来的用户ID里面，不属于登录账号下的员工的剔除，但是要保留景区账号(dtype=2)
        $operateInfo = [];
        if (!empty($operateRes)) {
            foreach ($operateRes as $v) {
                if (in_array($v['id'], $staffArr) || $v['dtype'] == 2) {
                    $operateInfo[$v['id']] = $v['dname'];
                }
            }
        }

        return $operateInfo;
    }

    /**
     * 获取站点信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $siteIdArr    站点id数组
     * @param  int    $sid          商户id
     *
     * @return array
     */
    private function _getSiteInfo(array $siteIdArr, int $sid)
    {
        if (empty($siteIdArr)) {
            return [];
        }
        $siteManageModel = new SiteManage();
        $siteInfo        = $siteManageModel->getDataBySiteIdArrAndSid($siteIdArr, $sid, 'id, sname');
        $siteRes         = [];
        foreach ($siteInfo as $site) {
            $siteRes[$site['id']] = $site['sname'];
        }

        return $siteRes;
    }

    /**
     * 获取分账对象信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $separateObjArr
     *
     * @return array
     */
    private function _getOidInfo(array $separateObjArr)
    {
        if (empty($separateObjArr)) {
            return [];
        }
        $separateObjList = (new SeparateConfigModel())->getObjectById($separateObjArr);
        $tmpObjList      = [];
        foreach ($separateObjList as $obj) {
            $tmpObjList[$obj['id']] = $obj['name'];
        }

        return $tmpObjList;
    }

    /**
     * 获取主票产品信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $pidArr    产品id数组
     *
     * @return array
     */
    private function _getMainPidInfo(array $pidArr)
    {
        if (empty($pidArr)) {
            return [];
        }
        $commodityProductBiz = new Product();
        $pidRes              = $commodityProductBiz->getProductInfoByIds($pidArr);
        $pidInfo             = [];
        foreach ($pidRes as $v) {
            $pidInfo[$v['id']] = $v['p_name'];
        }

        return $pidInfo;
    }

    /**
     * 获取演出场次
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $res    原始数据
     *
     * @return array
     */
    private function _getSeriesTimeInfo(array $res)
    {
        if (empty($res)) {
            return [];
        }
        $seriesTimeRes = [];
        foreach ($res as $item) {
            // 场次与票类绑定
            $timeKey = $item['series_time_begin'] . $item['series_time_end'];
            if (!isset($seriesTimeRes[$timeKey])) {
                $seriesTimeRes[$timeKey] = date('H:i', strtotime($item['series_time_begin'])) . '-' . date('H:i',
                        strtotime($item['series_time_end']));
            }
        }

        return $seriesTimeRes;
    }

    /**
     * 获取终端信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $terminalArr    分终端id数组
     * @param  int    $sid            商户id
     *
     * @return array
     */
    private function _getTerminalInfo(array $terminalArr, int $sid)
    {
        if (empty($terminalArr)) {
            return [];
        }
        $isSuper       = false;
        $terminalModel = new Terminal();
        $terminalInfo  = $terminalModel->getSubTerminalInfo($terminalArr, $isSuper, $sid);

        $terminal = [];
        if (!empty($terminalInfo)) {
            foreach ($terminalInfo as $val) {
                $terminal[$val['preTerminal']] = $val['name'];
            }
        }

        return $terminal;
    }

    /**
     * 获取产品信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @param  array  $cardPidArr    产品id数组
     *
     * @return array
     */
    private function _getCardPidInfo(array $cardPidArr)
    {
        if (empty($cardPidArr)) {
            return [];
        }
        $commodityProductBiz = new Product();

        $pidRes = $commodityProductBiz->getProductInfoByIds($cardPidArr);

        $pidInfo = [];
        foreach ($pidRes as $v) {
            $pidInfo[$v['id']] = $v['p_name'];
        }

        return $pidInfo;
    }

    /**
     * 获取区域信息
     * <AUTHOR>
     * @date   2022/11/24
     *
     * @return array
     */
    private function _getAddressInfo()
    {
        $areaModel = new Area();

        return $areaModel->getCodeByType([1, 2]);
    }

    /**
     * 通过场馆id获取场馆信息
     *
     * @param  array  $venueIdArr  场馆id数组
     *
     * @return array
     */
    private function _getVenueInfo(array $venueIdArr)
    {
        if (empty($venueIdArr)) {
            return [];
        }
        $showManageBiz = new ShowManage();

        $venueRes = $showManageBiz->getVenueInfoByIdArr($venueIdArr);
        if ($venueRes['code'] != 200 || empty($venueRes['data'])) {
            return [];
        }

        $venueInfo = [];
        foreach ($venueRes['data'] as $v) {
            $venueInfo[$v['venue_id']] = $v['venue_name'];
        }

        return $venueInfo;
    }

    /**
     * 通过分区id获取分区信息
     *
     * @param  array  $zoneIdArr  分区id数组
     *
     * @return array
     */
    private function _getZoneInfo(array $zoneIdArr)
    {
        if (empty($zoneIdArr)) {
            return [];
        }
        $showManageBiz = new ShowManage();

        $zoneRes = $showManageBiz->getZoneInfoByIdArr($zoneIdArr);
        if ($zoneRes['code'] != 200 || empty($zoneRes['data'])) {
            return [];
        }

        $zoneInfo = [];
        foreach ($zoneRes['data'] as $v) {
            $zoneInfo[$v['zone_id']] = $v['zone_name'];
        }

        return $zoneInfo;
    }

    /**
     * 通过场次id获取场次信息
     *
     * @param  array  $roundIdArr  场馆id数组
     *
     * @return array
     */
    private function _getRoundInfo(array $roundIdArr)
    {
        if (empty($roundIdArr)) {
            return [];
        }
        $showManageBiz = new ShowManage();

        $roundRes = $showManageBiz->getRoundInfoByIdArr($roundIdArr);
        if ($roundRes['code'] != 200 || empty($roundRes['data'])) {
            return [];
        }

        $roundInfo = [];
        foreach ($roundRes['data'] as $v) {
            $roundInfo[$v['round_id']] = $v['round_name'] . "({$v['round_time']})";
        }

        return $roundInfo;
    }
}