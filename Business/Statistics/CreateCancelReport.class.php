<?php
namespace Business\Statistics;

use Library\MulityProcessHelper;

/**
 * 取消报表
 *
 * <AUTHOR>
 * @date   2018-08-02
 */
class CreateCancelReport extends CreateReportBase {

    public function __construct($logPath) {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'cancel:start');
        parent::__construct();
    }

    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-06-19
     */
    public function runTask($date, $timeSepareType = 0) {
        if (!strtotime($date)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        $code = 200;
        $msg  = '';
        try {
            $this->_clearPreData($date, 'cancel');
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $dateList = $timeSepareType ? $this->_getDateListOrder($date) : $this->_getDateListNew($date);
            $maxNum   = 5;
            $dateNum  = count($dateList);
            $times = ceil($dateNum / $maxNum);
            for ($i = 0; $i < $times; $i++) {
                sleep(1);

                $handleDateList = array_slice($dateList, $i * $maxNum, $maxNum);
                $procNum = count($handleDateList);

                //使用多进程之前 释放掉已有的所有链接
                $this->_unsetAllModel();
                $task     = new MulityProcessHelper($this, $procNum, 1);
                $task->run($handleDateList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "cancel:end:{$msg}");
    }

    /**
     * 子进程脚本
     */
    public function runWorker($dateList) {
        $code = 200;
        $msg  = '';

        if (is_array($dateList)) {
            $dateList = array_shift($dateList);
        }
        try {
            $this->runCancelWorker($dateList);
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str  = 'cancel';
        $str .= $dateList[0] . ' - ' . $dateList[1] . ':';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;

        pft_log($this->_logPath, $str);
    }

    /**
     *
     * @return bool
     */
    private function runCancelWorker($date) {
        if (!is_array($date) || !isset($date[0]) || !isset($date[1]) || !strtotime($date[0]) || !strtotime($date[1]) ||
            (strtotime($date[1]) - strtotime($date[0]) > 3600 * 24 )) {
            throw new \Exception('时间格式错误', 403);
        }

        $day       = date('Ymd', strtotime($date[0]));
        $total     = $this->_getTrackTotal($date, 'cancel');
        $totalPage = ceil($total / $this->_selectSize);

        $checkedData = [];
        $trackIdArr  = [];

        for ($page = 1; $page <= $totalPage; $page++) {
            $list = $this->_getTrackList($date, 'cancel', $page, $this->_selectSize);

            if ($list === false) {
                return false;
            }

            foreach ($list as $item) {
                $orderNum   = strval($item['ordernum']);
                $extContent = json_decode($item['ext_content'], true);


                //累加数据
                if (isset($checkedData[$orderNum])) {
                    $checkedData[$orderNum]['tnum'] += $item['tnum'];
                    //本次操作优惠的下标信息
                    if (isset($extContent['serial_number']) && $extContent['serial_number']) {
                        $checkedData[$orderNum]['point_cancel_idx'][$item['oper_member']][$item['action']][] = $extContent['serial_number'] ?? '';
                    }

                } else {
                    unset($item['ordernum']);
                    $checkedData[$orderNum] = $item;
                    //本次操作优惠的下标信息
                    if (isset($extContent['serial_number']) && $extContent['serial_number']) {
                        $checkedData[$orderNum]['point_cancel_idx'][$item['oper_member']][$item['action']][] = $extContent['serial_number'] ?? '';
                    }

                }

                $trackIdArr[] = intval($item['id']);
            }
        }

        //获取退票手续费的数据
        $cancelServiceArr = $this->_getCancelInfo($trackIdArr);

        //分页处理数据
        $res       = true;
        $total     = count($checkedData);
        $totalPage = ceil($total / $this->_pieceNum);
        for ($i = 0; $i < $totalPage; $i++) {
            $queryIds = array_slice($checkedData, $i * $this->_pieceNum, $this->_pieceNum, true);

            //通过订单ID获取订单信息
            $orderIdArr = array_keys($queryIds, true);
            $res        = $this->_getOrderInfo($orderIdArr);
            $orderList  = [];
            $tmpUserArr = [];
            foreach ($res as $item) {
                $orderNum = $item['ordernum'];

                //将取消未支付的订单给过滤掉
                if ($item['pay_status'] == 2) {
                    if (($key = array_search($orderNum, $orderIdArr)) || $key !== false) {
                        unset($orderIdArr[$key]);
                    }
                }

                unset($item['ordernum']);
                $orderList[$orderNum] = $item;

                if ($item['ordermode'] == 11) {
                    $tmpUserArr[] = $item['member'];
                }
                if ($item['ordermode'] == 43) {
                    $tmpUserArr[] = $item['member'];
                }
                if ($item['ordermode'] == 18) {
                    $tmpUserArr[] = $item['member'];
                }
                if ($item['ordermode'] == 15) {
                    $tmpUserArr[] = $item['member'];
                }
            }

            //获取优惠信息
            $couponList   = [];
            $onSaleRecord = $this->_getOnSaleRecord($orderIdArr);
            foreach ($onSaleRecord as $item) {
                $couponList[$item['ordernum']][$item['aid']][$item['fid']] = $item['eMoney'];
            }

            //获取下单的积分、优惠券信息
            $pointAndCouponInfo = $this->_getOrderPointsRecord($orderIdArr);
            //获取订单对应积分、优惠券优惠下标明细
            $discountIdxList = $this->_getOrderPointsIdxRecord($orderIdArr);

            $pointDetail = [
                'pointAndCouponInfo' => $pointAndCouponInfo,
                'discountIdxList'    => $discountIdxList,
            ];

            //微商城用户获取用户信息。用来判断是不是散客
            $sankeList = [];
            if ($tmpUserArr) {
                $tmpUserArr = array_unique($tmpUserArr);
                $sankeList  = $this->_getSankeList($tmpUserArr);
            }

            //获取分销链数据
            $chainList = $this->_getBuyChain($orderIdArr);
            if ($chainList === false) {
                return false;
            }

            if (count($chainList) == 0) {
                continue;
            }

            //是不是最后一个分片
            $isLastSlice = ($i == ($totalPage - 1)) ? true : false;

            $res = $this->_handleCancelData($day, $chainList, $queryIds, $orderList, $cancelServiceArr, $isLastSlice, 'cancel', $couponList, $sankeList, $pointDetail);
        }

        //返回
        return $res ? true : false;
    }

    /**
     * 取消/撤销报表数据处理
     * <AUTHOR>
     * @date   2016-08-04
     *
     * @param  $splitList 分销链数据
     * @param  $checkedData 预定数据
     * @param  $orderList 订单数据
     * @param  $isLastSlice 是不是最后一个分片
     * @param  $cancelServiceArr 退票手续费信息
     * @param  $type cancel/revoke 取消和撤销数据一致，只是存放的表不一致
     * @return
     */
    private function _handleCancelData($day, $chainList, $checkedData, $orderList, $cancelServiceArr = [], $isLastSlice, $type = 'cancel', $couponList, $sankeList, $pointDetail)
    {
        //首先获取之前的缓存数据
        $splitList = $this->_cacheDataArr($day, $type, 'get');

        $table = $type == 'cancel' ? 'cancel' : 'revoke';

        for ($i = 0; $i < count($chainList); $i++) {
            $item = $chainList[$i];

            $orderNum                 = $item['orderid'];
            $settlementDiscountCancel = $item['cancelSettlementPoints'] ?? 0;//修复下，避免json的null值

            if (!array_key_exists($orderNum, $checkedData)) {
                continue;
            }

            if (!array_key_exists($orderNum, $orderList)) {
                continue;
            }

            $orderInfo   = $orderList[$orderNum];
            $checkedInfo = $checkedData[$orderNum];
            $cancelInfo  = array_key_exists($orderNum, $cancelServiceArr) ? $cancelServiceArr[$orderNum] : false;

            //积分、优惠券下单信息
            $pointAndCouponInfo = $pointDetail['pointAndCouponInfo'];
            //积分、优惠券操作下标信息
            $discountIdxList = $pointDetail['discountIdxList'] ?? [];
            //积分、优惠券取消操作下标数据
            $pointCancelIdxArr = $checkedInfo['point_cancel_idx'] ?? [];
            //积分、优惠券撤销操作下标数据
            $pointRevokeIdxArr = $checkedInfo['point_revoke_idx'] ?? [];

            //汇总积分、优惠券取消修改撤销撤改优惠金额
            $pointAmountDetail = $this->_handlePointsIdxAmount($orderNum, $discountIdxList, $pointCancelIdxArr, $pointRevokeIdxArr);

            $fid       = $item['sellerid'];
            $sellerId  = $item['buyerid'];
            $oldSeller = $item['buyerid'];
            $level     = $item['level'];
            $costMoney = $item['cost_money'];
            $saleMoney = $item['sale_money'];
            $payWay    = $item['pmode'];
            $ticketNum = $checkedInfo['tnum'];
            $operateId = $checkedInfo['oper_member'];

            $lid     = $orderInfo['lid'];
            $tid     = $orderInfo['tid'];
            $channel = $orderInfo['ordermode'];

            //通过渠道将散客进行归类
            if (in_array($level, [-1, 0])) {
                $tmp = $this->_getSankeId($sellerId, $fid, $channel, $sankeList);

                if ($tmp !== false) {
                    //最后一级就是散客的情况(自供自销也算在这里面)
                    if ($level == 0) {
                        $level = 1;
                    } else {
                        $level = -3;
                    }
                    $sellerId = $tmp;
                }
            }

            //去除退票手续费
            $serviceMoneyIn  = 0;
            $serviceMoneyOut = 0;

            if ($cancelInfo) {
                foreach ($cancelInfo as $k => $v) {
                    if ($v['fid'] == $oldSeller && $v['aid'] == $fid) {
                        $serviceMoneyIn = $v['money'];
                    }
                    if ($v['fid'] == $fid) {
                        $serviceMoneyOut = $v['money'];
                    }
                }
            }

            if (!isset($splitList[$fid])) {
                $splitList[$fid] = [];
            }

            if (!isset($splitList[$fid][$sellerId])) {
                $splitList[$fid][$sellerId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$tid])) {
                $splitList[$fid][$sellerId][$tid] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$tid][$operateId])) {
                $splitList[$fid][$sellerId][$tid][$operateId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$tid][$operateId])) {
                $splitList[$fid][$sellerId][$tid][$operateId] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$tid][$operateId][$payWay])) {
                $splitList[$fid][$sellerId][$tid][$operateId][$payWay] = [];
            }

            if (!isset($splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel])) {
                $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel] = [
                    'lid'               => $lid,
                    //'channel'           => $channel,
                    'level'             => in_array($level, [0, 1]) ? 1 : $level,
                    'cancel_money'      => 0,
                    'cost_money'        => 0,
                    'service_money_in'  => 0,
                    'service_money_out' => 0,
                    'ticket_num'        => 0,
                    'order_num'         => 0,
                    'orders_info'       => [],
                ];
            }

            if (isset($couponList[$orderNum][$fid][$oldSeller])) {
                //优惠金额
                $eMoney = $couponList[$orderNum][$fid][$oldSeller];
            } else {
                $eMoney = 0;
            }

            //购买的sellerid要取oldSellerID因为报表会将散客归类到sellerid里面去，所以这里要取旧sellerid
            $discountDetail          = self::handleDiscountInfo($pointAndCouponInfo, [], $orderNum, $fid, $oldSeller, $oldSeller);
            $discountMoney           = $discountDetail[self::DISCOUNT]['order_amount'];
            $settlementDiscountMoney = $discountDetail[self::SETTLEMENT_DISCOUNT]['order_amount'];

            //计算积分使用情况 PS:: 因为取消报表的 取消和修改是汇总再一起的， 所以取积分优惠金额需要特殊根据action来统计下
            $changeDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $this->_changeAction, $discountMoney, $settlementDiscountMoney, $operateId, 'cancel_amount');
            $cancelDiscount         = self::handleStatisticsActionDiscount($pointAmountDetail, $orderNum, $this->_cancelAction, $discountMoney, $settlementDiscountMoney, $operateId, 'cancel_amount');
            $cancelPoints           = $changeDiscount[self::DISCOUNT] + $cancelDiscount[self::DISCOUNT];
            $cancelSettlementPoints = $changeDiscount[self::SETTLEMENT_DISCOUNT] + $cancelDiscount[self::SETTLEMENT_DISCOUNT];

            //整合数据
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['ticket_num']           += $ticketNum;
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['order_num']            += 1;
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['cancel_money']         += $saleMoney * $ticketNum - $eMoney - $this->getSaleDiscountMoney($cancelPoints, $cancelSettlementPoints);
            //最末级优惠金额的判断
            $tmpCostMoney = $level == -4 ? $orderInfo['totalmoney'] : $costMoney * $ticketNum - $settlementDiscountCancel;
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['cost_money']           += $tmpCostMoney;
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['service_money_in']     += $serviceMoneyIn;
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['service_money_out']    += $serviceMoneyOut;
            $splitList[$fid][$sellerId][$tid][$operateId][$payWay][$channel]['orders_info'][]         = [$orderNum, $ticketNum, $cancelPoints, $cancelSettlementPoints ?: $settlementDiscountCancel];

            //特殊处理
            $splitTmp = $this->_needAddSplit($level, $sellerId, $fid);
            if ($splitTmp) {

                //因为价格配置的原因，导致之前有些订单中的lprice(零售价)为-1，这边做下处理
                $lprice    = isset($orderInfo['lprice']) ? $orderInfo['lprice'] : $saleMoney;
                $needMoney = $lprice == -1 ? $saleMoney : $lprice;

                $splitData = [
                    'orderid'                => $orderNum,
                    'sellerid'               => $sellerId,
                    'buyerid'                => $this->_getChannelSanke($channel),
                    'level'                  => -4,  //非0/-1
                    'cost_money'             => $saleMoney, //购买的钱
                    'sale_money'             => $needMoney, //默认使用零售价的钱
                    'cancelSettlementPoints' => $this->getCostDiscountMoney($cancelPoints, $cancelSettlementPoints),
                ];

                $chainList[] = $splitData;
            }
        }

        //如果不是最后一个分片，就先将数据写入缓存文件
        if (!$isLastSlice) {
            $this->_cacheDataArr($day, $type, 'set', $splitList);
            unset($splitList);
            return true;
        } else {
            //如果是最后一个，就将之前的数据清除
            $this->_cacheDataArr($day, $type, 'clear');
        }

        $updateTime = time();
        $date       = str_replace('-', '', $day);

        //分批插入数据
        $insertData = [];

        foreach ($splitList as $fid => $fidData) {
            foreach ($fidData as $sellerId => $sellerData) {
                foreach ($sellerData as $tid => $tidData) {
                    foreach ($tidData as $operateId => $operateData) {
                        foreach ($operateData as $payWay => $payWayData) {
                            foreach ($payWayData as $channel => $tmpData) {
                                $tmp = $tmpData;
                                $tmp['orders_info'] = json_encode($tmp['orders_info']);
                                $tmp['fid'] = $fid;
                                $tmp['reseller_id'] = $sellerId;
                                $tmp['tid'] = $tid;
                                $tmp['date'] = $date;
                                $tmp['update_time'] = $updateTime;
                                $tmp['channel'] = $channel;
                                $tmp['operate_id'] = $operateId;
                                $tmp['pay_way'] = $payWay;

                                $insertData[] = $tmp;

                                if (count($insertData) >= 10) {
                                    //达到条数就插入数据
                                    $lastId = $this->_insertDataArr($insertData, $table);

                                    //初始化
                                    $insertData = [];
                                }
                            }
                        }
                    }
                }
            }
        }

        //将没有插入的数据最后插入
        if ($insertData) {
            $lastId = $this->_insertDataArr($insertData, $table);
        }

        //返回
        return $lastId;
    }
}
