<?php

namespace Business\Statistics;

class CreateCustomizeMonthReport extends CreateMonthReportBase
{
    private $_logPath;
    //进程数
    private $_processNum = 2;
    //月份
    private $_month;

    private $_selectSize = 1000;


    public function __construct($logPath) {
        $this->_logPath = $logPath;
        pft_log($this->_logPath, 'customize_month:start');
        parent::__construct();
    }


    /**
     * 开始跑脚本
     *
     * <AUTHOR>
     * @date   2018-09-16
     */
    public function runTask($month)
    {
        if (!strtotime($month)) {
            return ['code' => 403, 'msg' => '时间格式错误'];
        }

        $this->_month = $month;

        $code = 200;
        $msg  = '';

        try {

            $this->_clearPreData($month, 'customize_month');
            defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
            $task    = new \Library\MulityProcessHelper($this, $this->_processNum, 1);
            $lidList = $this->_getLidList($this->_processNum, $month, 'customize_report');
            //使用多进程之前 释放掉已有的所有链接
            $this->_unsetAllModel();
            $task->run($lidList); // 如果前面连接了数据库、redis等，最好在这之前关闭掉

        } catch (\Exception $e) {

            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        pft_log($this->_logPath, "customize_month:end:{$msg}");
    }


    /**
     * 子进程脚本
     */
    public function runWorker($lidList)
    {
        $code = 200;
        $msg  = '';

        if (is_array($lidList)) {
            $lidList = array_shift($lidList);
        }

        try {
            $this->_runOrderMonthWorker($lidList);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        $str  = 'customize_month:';
        $str .= $code == 200 ? ':执行成功' : ':执行失败,原因:' . $msg;
        pft_log($this->_logPath, $str);
    }


    private function _runOrderMonthWorker($lidList)
    {
        $begin = $this->_month . '01';
        $end   = $this->_month . '31';

        $statisModel = $this->_getStatisModel();
        foreach ($lidList as $lid) {
            // 获取月报表总数
            $total      = $statisModel->_getReportV2Total($begin, $end, 'customize_report', $lid);
            $totalPage  = ceil($total / $this->_selectSize);

            for($page = 1; $page <= $totalPage; $page++) {
                $insertData = [];
                $data       = [];

                $list = $statisModel->_getReportV2List($begin, $end, $page, $this->_selectSize, 'customize_report', $lid);
                // 如果查询出错了， 直接返回错误
                if ($list === false) {
                    throw new \Exception("查询出错");
                }

                $time = time();
                foreach ($list as $item) {

                    //报表的所有维度字段
                    $lid = $item['lid'];
                    $pid = $item['pid'];
                    $fid = $item['fid'];
                    $tid = $item['tid'];

                    $sellerId      = $item['reseller_id'];
                    $channel       = $item['channel'];
                    $paymode       = $item['pay_way'];
                    $level         = $item['lvl'];
                    $site          = $item['site_id'];
                    $operate       = $item['operate_id'];
                    $mainTid       = $item['main_tid'];
                    $subMerchantId = $item['sub_merchant_id'];
                    $mainType      = $item['main_type'];

                    $orderArr       = json_decode($item['window_real_orders_info'], true);
                    $cancelOrderArr = json_decode($item['window_cancel_orders_info'], true);

                    empty($orderArr) && $orderArr = [];
                    empty($cancelOrderArr) && $cancelOrderArr = [];

                    if (!isset($data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate])) {
                        $data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate] = [
                            'window_real_money'          => $item['window_real_money'],
                            'window_cancel_money'         => $item['window_cancel_money'],
                            'window_real_orders_info'     => $orderArr,
                            'window_cancel_orders_info'   => $cancelOrderArr,
                            'window_cancel_service_money' => $item['window_cancel_service_money'],
                        ];

                    } else {

                        $data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['window_real_money']  += $item['window_real_money'];
                        $data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['window_cancel_money'] += $item['window_cancel_money'];
                        $data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['window_cancel_service_money'] += $item['window_cancel_service_money'];

                        if (is_array($orderArr) && !empty($orderArr)) {
                            foreach ($orderArr as $order) {
                                $data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['window_real_orders_info'][] = $order;
                            }
                        }

                        if (is_array($cancelOrderArr) && !empty($cancelOrderArr)) {
                            foreach ($cancelOrderArr as $order) {
                                $data[$fid][$tid][$sellerId][$lid][$pid][$mainTid][$mainType][$channel][$paymode][$subMerchantId][$level][$site][$operate]['window_cancel_orders_info'][] = $order;
                            }
                        }
                    }
                }

                foreach ($data as $fid => $fidData) {
                    foreach ($fidData as $tid => $tidData) {
                        foreach ($tidData as $sellerId => $sellerData) {
                            foreach ($sellerData as $lid => $lidData) {
                                foreach ($lidData as $pid => $pidData) {
                                    foreach ($pidData as $mainTid => $mainTidData) {
                                        foreach ($mainTidData as $mainType => $mainTypeData) {
                                            foreach ($mainTypeData as $channel => $channelData) {
                                                foreach ($channelData as $paymode => $paymodeData) {
                                                    foreach ($paymodeData as $subMerchantId => $subMerchantData) {
                                                        foreach ($subMerchantData as $level => $levelData) {
                                                            foreach ($levelData as $site => $siteData) {
                                                                foreach ($siteData as $operate => $numData) {
                                                                    $tmp = [
                                                                        'cal_date'                    => $this->_month,
                                                                        'fid'                         => $fid,
                                                                        'tid'                         => $tid,
                                                                        'lid'                         => $lid,
                                                                        'pid'                         => $pid,
                                                                        'lvl'                         => $level,
                                                                        'channel'                     => $channel,
                                                                        'site_id'                     => $site,
                                                                        'main_tid'                    => $mainTid,
                                                                        'main_type'                   => $mainType,
                                                                        'pay_way'                     => $paymode,
                                                                        'reseller_id'                 => $sellerId,
                                                                        'operate_id'                  => $operate,
                                                                        'update_time'                 => $time,
                                                                        'sub_merchant_id'             => $subMerchantId,
                                                                        'window_real_money'           => $numData['window_real_money'],
                                                                        'window_real_orders_info'     => json_encode($numData['window_real_orders_info']),
                                                                        'window_cancel_money'         => $numData['window_cancel_money'],
                                                                        'window_cancel_orders_info'   => json_encode($numData['window_cancel_orders_info']),
                                                                        'window_cancel_service_money' => $numData['window_cancel_service_money'],
                                                                    ];

                                                                    $insertData[] = array_merge($numData, $tmp);
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //更新数据
                if (!empty($insertData)) {
                    $lastId = $statisModel->_insertDataArr($insertData, 'customize_month');
                    unset($insertData);
                }
            }
        }
    }

}