<?php
/**
 * User: yangwx
 * Date: 2020-12-29
 */

namespace Business\AgreementTicket;

use Library\JsonRpc\PftRpcClient;
use Business\Base;

class BaseCall extends Base
{

    const __AGREEMENT_TICKET_TESTAPI__ = 'testApi';

    const __AGREEMENT_TICKET_QUERYCONFIGPAGE__ = 'queryConfigPage';//配置数据分页列表查询

    const __AGREEMENT_TICKET_ADDCONFIG__ = 'addConfig';//新增配置数据

    const __AGREEMENT_TICKET_BATCHADDCONFIG__ = 'batchAddConfig';//新增配置数据

    const __AGREEMENT_TICKET_QUERY_CONFIG_BY_AIDFIDTIDARRAY__ = 'queryConfigByAidFidTidArray';//根据供应商id，分销商id，票id数组，查询已有配置的票列表

    const _AGREEMENT_TICKET_BATCH_OPERATE_LIST__ = 'batchOperateList';//查询批量操作列表

    const _AGREEMENT_TICKET_BATCH_OPERATE_DETAIL__ = 'batchOperateDetail';//查询批量操作详情列表

    const __AGREEMENT_TICKET_MODCONFIG__ = 'modConfig';//编辑配置数据

    const __AGREEMENT_TICKET_DELCONFIG__ = 'delConfig';//删除配置数据

    const __AGREEMENT_TICKET_QUERYCONFIGINFO__ = 'queryConfigInfo';//获取配置信息

    const __AGREEMENT_TICKET_QUERYCONFIGSTORAGEINFO__ = 'queryConfigStorageInfo';//获取配置和库存信息

    const __AGREEMENT_TICKET_QUERYSTORAGEINFO__ = 'queryStorageInfo';//获取库存信息

    const __AGREEMENT_TICKET_QUERYORDERRECORD__ = 'queryOrderRecord';//获取购票记录

    const __AGREEMENT_TICKET_ORDERJUDGESTORAGE__ = 'orderJudgeStorage';//下单前库存判断(有库存冻结逻辑仅限下单前调用)

    const __AGREEMENT_TICKET_DEDUCTSTORAGE__ = 'deductStorage';//下单成功后库存扣减

    const __AGREEMENT_TICKET_UNFREEZESTORAGE__ = 'unfreezeStorage';//下单释放后库存释放

    const __AGREEMENT_TICKET_CANCELSTORAGE__ = 'cancelStorage';//退票后库存处理

    const __AGREEMENT_TICKET_ISAGREEMENTTICKETORDER__ = 'isAgreementTicketOrder';//是否是协议票订单

    const __AGREEMENT_TICKET_QUERYSTORAGEBYUSETIME__ = 'queryStorageByUseTime';//根据启用时间获取库存数据

    const __AGREEMENT_TICKET_QUERYSTORAGEBYORDERPAGE__ = 'queryStorageByOrderPage';//获取订单预订页面的库存

    const __AGREEMENT_TICKET_SYNCISDISTRIBUTIONTOEVOLUTE__ = 'syncIsDistributionToEvolute';//同步向下分销配置数据到分销系统

    public function __construct()
    {

    }

    /**
     * 请求服务接口
     * <AUTHOR>
     * @date 2020/12/29
     *
     * @param  string  $method  请求的方法
     * @param  array  $params  请求的参数
     * @param  string  $module  对应模块
     *
     * @return array
     */
    public function call(string $method, array $params = null, string $module = null)
    {
        if (empty($method)) {
            return $this->returnData(204, '方法缺失', []);
        }

        if (empty($module)) {
            $module = 'plat';
        }

        try {
            $lib  = new PftRpcClient('agreement_ticket');
            $res  = $lib->call($method, $params, $module);
            $data = $res['data'];
            $code = $res['code'];
            $msg  = $res['msg'];
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        return $this->returnData($code, $msg, $data);
    }

}