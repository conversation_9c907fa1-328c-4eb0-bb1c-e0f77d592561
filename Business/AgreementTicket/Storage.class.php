<?php
/**
 * 协议票库存业务逻辑层
 * <AUTHOR>
 * @date 2020-12-29
 */

namespace Business\AgreementTicket;

use Library\Resque\Queue;

class Storage extends BaseCall
{
    public function __construct()
    {

    }

    /**
     * 获取库存信息
     * <AUTHOR>
     * @date 2021/1/2
     *
     * @param  int  $configId  配置ID
     *
     * @return array
     */
    public function queryStorageInfo(int $configId)
    {
        if (!$configId) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'configId' => $configId,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYSTORAGEINFO__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取购票记录
     * <AUTHOR>
     * @date 2021/1/4
     *
     * @param  int  $configId  配置ID
     * @param  int  $page  页码
     * @param  int  $size  每页大小
     * @param  string|null  $orderNum  订单号
     *
     * @return array
     */
    public function queryOrderRecord(int $configId, int $page, int $size, string $orderNum = null)
    {
        if (!$configId) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'configId' => $configId,
            'page'     => $page,
            'size'     => $size,
        ];

        if (!is_null($orderNum)) {
            $params['orderNum'] = $orderNum;
        }

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYORDERRECORD__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 下单前库存判断(有库存冻结逻辑仅限下单前调用)
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @param  int  $aid  供应商ID
     * @param  int  $fid  分销商ID
     * @param  int  $tid  票ID
     * @param  int  $tnum  票数
     *
     * @return array
     */
    public function orderJudgeStorage(int $aid, int $fid, int $tid, int $tnum, int $uid)
    {
        if (!$aid || !$fid || !$tid || !$tnum || !$uid) {
            return $this->returnData(400, '协议票库存判断参数异常');
        }

        $params = [
            'aid'  => $aid,
            'fid'  => $fid,
            'tid'  => $tid,
            'tnum' => $tnum,
            'uid'  => $uid,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_ORDERJUDGESTORAGE__, $params);
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 释放冻结库存
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @param  int  $configId  配置ID
     * @param  int  $num  票数
     *
     * @return array
     */
    public function unfreezeStorage(int $configId, int $num)
    {
        if (!$configId || !$num) {
            return $this->returnData(400, '协议票库存判断参数异常');
        }

        $params = [
            'configId' => $configId,
            'tnum'     => $num,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_UNFREEZESTORAGE__, $params);

        if ($result['code'] != 200) {
            //记入日志
            $logData = array_merge($result, $params);
            pft_log('agreementTicket/storage/unfreezeStorage', '释放冻结库存失败:' . json_encode($logData, JSON_UNESCAPED_UNICODE));
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 下单后库存扣减
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @param  int  $configId  配置ID
     * @param  int  $orderNum  订单号
     * @param  int  $num  票数
     * @param  int  $orderTime  下单时间
     *
     * @return array
     */
    public function deductStorage(int $configId, string $orderNum, int $num, int $orderTime)
    {
        if (!$configId || !$orderNum || !$num || !$orderTime) {
            return $this->returnData(400, '协议票库存扣减参数异常');
        }

        $params = [
            'configId'  => $configId,
            'orderNum'  => $orderNum,
            'tnum'      => $num,
            'orderTime' => $orderTime,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_DEDUCTSTORAGE__, $params);

        if ($result['code'] != 200) {
            //记入日志
            $logData = array_merge($result, $params);
            pft_log('agreementTicket/storage/deductStorage', '下单后库存扣减失败:' . json_encode($logData, JSON_UNESCAPED_UNICODE));
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 退票后库存处理
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @param  int  $orderNum  订单号
     * @param  int  $num  票数
     *
     * @return array
     */
    public function cancelStorage(string $orderNum, int $num)
    {
        if (!$orderNum || !$num) {
            return $this->returnData(400, '协议票库存扣减参数异常');
        }

        $params = [
            'orderNum'  => $orderNum,
            'tnum'      => $num,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_CANCELSTORAGE__, $params);

        if ($result['code'] != 200) {
            //记入日志
            $logData = array_merge($result, $params);
            pft_log('agreementTicket/storage/cancelStorage', '退票后库存处理失败:' . json_encode($logData, JSON_UNESCAPED_UNICODE));
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 是否是协议票订单
     * <AUTHOR>
     * @date    2021-01-05
     *
     * @param  string  $orderId  订单号
     *
     * @return  boolean
     */
    public function isAgreementTicketOrder(string $orderId)
    {
        if (!$orderId) {
            return $this->returnData(400, '是否是协议票订单参数异常');
        }

        $params = [
            'orderNum'  => $orderId,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_ISAGREEMENTTICKETORDER__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 根据启用时间获取库存数据
     * <AUTHOR>
     * @date 2021/1/13
     *
     * @param  int  $applyDid  供应商ID
     * @param  int  $configId  配置ID
     * @param  int  $useType  类型
     * @param  int|null  $startTime  开始时间
     * @param  int|null  $endTime  结束时间
     *
     * @return array
     */
    public function queryStorageByUseTime(int $applyDid, int $configId, int $useType, int $startTime = null, int $endTime = null)
    {
        if (!$configId || !$useType) {
            return $this->returnData(400, '根据启用时间获取库存数据参数异常');
        }

        $params = [
            'configId' => $configId,
            'useType'  => $useType,
            'applyDid' => $applyDid,
        ];

        if (!is_null($startTime)) {
            $params['startTime'] = $startTime;
        }

        if (!is_null($endTime)) {
            $params['endTime'] = $endTime;
        }

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYSTORAGEBYUSETIME__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 退票释放协议库存队列任务
     * <AUTHOR>
     * @date 2020/9/14
     *
     * @param  string  $orderNum  订单号
     * @param  int  $tnum  取消的数量
     *
     * @return string
     */
    public static function pushCancelStorage(string $orderNum, int $tnum)
    {
        $data  = [
            'action' => 'cancel_storage',
            'data'   => ['orderNum' => $orderNum, 'tnum' => $tnum],
        ];
        //pft_log('agreementTicket/AgreementTicket_Job/perform', json_encode($data, JSON_UNESCAPED_UNICODE));
        $jobId = Queue::push('independent_system', 'AgreementTicket_Job', $data);

        if (empty($jobId)) {
            pft_log('agreementTicket/storage/pushCancelStorage', '退票释放协议库存队列任务失败:' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }

        return $jobId;
    }

    /**
     * 协议票退票逻辑
     * <AUTHOR>
     * @date 2021/1/5
     *
     * @param  string  $orderNum 订单号
     * @param  int  $tnum 票数
     *
     * @return boolean
     */
    public function cancelStorageHandle(string $orderNum, int $tnum)
    {
        $isOrderRes = $this->isAgreementTicketOrder($orderNum);

        //只有请求成功并且返回不是协议票订单的业务标识才能认定为不是协议票订单
        if ($isOrderRes['code'] == 200 && $isOrderRes['data']['isOrder'] === false) {
            return true;
        }

        //判断是否是协议票订单的时候网络问题或者服务异常的情况也要请求退票接口
        $res = $this->cancelStorage($orderNum, $tnum);
        $res['orderNum'] = $orderNum;
        $res['tnum']     = $tnum;
        pft_log('agreementTicket/storage/cancelStorageHandle', json_encode($res, JSON_UNESCAPED_UNICODE));
        if ($res['code'] != 200) {
            return false;
        }
        return true;
    }

    /**
     * 获取订单预订页面的库存
     * <AUTHOR>
     * @date 2021/6/16
     *
     * @param  int  $fid  分销商ID
     * @param  int  $sid  上级供应商ID
     * @param  int  $pid  产品ID
     * @param  int  $time  时间
     *
     * @return array
     */
    public function queryStorageByOrderPage(int $fid, int $sid, int $pid, int $time)
    {
        if (!$fid || !$sid || !$pid || !$time) {
            return $this->returnData(400, '获取订单预订页面的库存参数异常');
        }

        $params = [
            'fid' => $fid,
            'pid'  => $pid,
            'sid' => $sid,
            'time' => $time,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYSTORAGEBYORDERPAGE__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

}