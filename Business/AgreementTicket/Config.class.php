<?php
/**
 * 协议票配置业务逻辑层
 * <AUTHOR>
 * @date 2020-12-29
 */

namespace Business\AgreementTicket;

use Business\JavaApi\Member\MemberQuery;
use Business\JavaApi\Product\Land;
use Business\Member\Member;
use Business\NewJavaApi\Distribution\Product;
use Library\Resque\Queue;

class Config extends BaseCall
{
    public function __construct()
    {

    }

    /**
     * 获取配置列表
     * <AUTHOR>
     * @date 2020/12/30
     *
     * @param  int  $applyDid  供应商ID
     * @param  int  $page  页码
     * @param  int  $size  每页大小
     * @param  int|null  $lid  景区ID
     * @param  int|null  $tid  票ID
     * @param  string|null  $title  名称
     * @param  string|null  $dname  名称
     * @param  string|null  $account  账户
     * @param  string|null  $mobile  手机号
     *
     * @return array
     */
    public function queryConfigPage(int $applyDid, int $page, int $size, int $lid = null, int $tid = null, string $title = null, string $dname = null, string $account = null, string $mobile = null, string $cname = null)
    {

        if (!$applyDid || !$page || !$size) {
            return $this->returnData(400, '参数异常');
        }

        $fid = null;
        if (!empty($dname) || !empty($account) || !empty($mobile) || !empty($cname)) {
            $memberQueryModel = new MemberQuery();
            $res              = $memberQueryModel->queryMemberInfoByAccountOrMobileOrName($account, $mobile, $dname, 0,
                $cname);
            if ($res['code'] != 200 || empty($res['data'])) {
                return $this->returnData(200, ['list' => [], 'total' => []], '成功');
            }

            $fid = array_column($res['data'], 'id');
        }

        $params = [
            'applyDid' => $applyDid,
            'page'     => $page,
            'size'     => $size,
        ];

        if (!is_null($lid)) {
            $params['lid'] = $lid;
        }

        if (!is_null($tid)) {
            $params['tid'] = $tid;
        }

        if (!is_null($title)) {
            $params['title'] = $title;
        }

        if (!is_null($fid)) {
            $params['fid'] = $fid;
        }

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYCONFIGPAGE__, $params);
        if (empty($result['data']['list'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        $tids = array_column($result['data']['list'], 'ticket_id');
        $fids = array_column($result['data']['list'], 'fid');

        $memberBuz = new Member();
        $userInfo  = $memberBuz->getList($fids);

        $javaApi       = new \Business\CommodityCenter\Ticket();
        $tmpTicketInfo = $javaApi->queryTicketInfoByIds($tids);

        $productInfo = [];
        foreach ($tmpTicketInfo as $value) {
            $productInfo[$value['ticket']['id']] = [
                'land_title'   => $value['land']['title'],
                'ticket_title' => $value['ticket']['title'],
            ];
        }

        foreach ($result['data']['list'] as &$tmp) {
            $tmp['land_title']   = $productInfo[$tmp['ticket_id']]['land_title'] ?? '';
            $tmp['ticket_title'] = $productInfo[$tmp['ticket_id']]['ticket_title'] ?? '';
            $tmp['dname']        = $userInfo[$tmp['fid']]['dname'] ?? '';
            $tmp['account']      = $userInfo[$tmp['fid']]['account'] ?? '';
            $tmp['cname']        = $userInfo[$tmp['fid']]['cname'] ?? '';
            $tmp['mobile']       = $userInfo[$tmp['fid']]['mobile'] ?? '';
        }

        //$this->pushAppCenter(3385, 1);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 查询批量操作列表
     *
     * @param int $applyDid
     * @param int $pageNum
     * @param int $pageSize
     * @param int $state 批量操作的处理状态 0进行中，1完成
     * @param string $beginTime
     * @param string $endTime
     *
     * @return array
     */
    public function batchOperateList($applyDid, $pageNum, $pageSize, $state = -1, $beginTime = '', $endTime = '')
    {
        if (!$applyDid || !$pageNum || !$pageSize) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'applyDid'  => $applyDid,
            'pageNum'   => $pageNum,
            'pageSize'  => $pageSize,
            'state'     => $state,
            'beginTime' => $beginTime ? strtotime($beginTime) : 0,
            'endTime'   => $endTime ? strtotime($endTime) : 0,
        ];

        $result = $this->call(self::_AGREEMENT_TICKET_BATCH_OPERATE_LIST__, $params);
        if (empty($result['data']['list'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        $fids        = array_column($result['data']['list'], 'fid');
        $operatorIds = array_column($result['data']['list'], 'operater_id');
        $memberBuz   = new Member();
        $userInfo    = $memberBuz->getList(array_unique(array_merge($fids, $operatorIds)));
        foreach ($result['data']['list'] as &$tmp) {
            $tmp['distributor_dname']   = $userInfo[$tmp['fid']]['dname'] ?? '';
            $tmp['distributor_account'] = $userInfo[$tmp['fid']]['account'] ?? '';
            $tmp['operator_name']       = $userInfo[$tmp['operater_id']]['dname'] ?? '';
            $tmp['operator_account']    = $userInfo[$tmp['operater_id']]['account'] ?? '';
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 查询批量操作详情列表
     *
     * @param int $batchId 批量操作表id
     * @param int $pageNum 页码
     * @param int $pageSize 每页数量
     * @param int $state 状态：0失败，1成功；
     * @param int $lid 产品id
     * @param int $tid 票id
     *
     * @return array
     */
    public function batchOperateDetail($batchId, $pageNum, $pageSize, $state = -1, $lid = 0, $tid = 0)
    {
        if (!$batchId || !$pageNum || !$pageSize) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'batchId'  => $batchId,
            'pageNum'  => $pageNum,
            'pageSize' => $pageSize,
            'state'    => $state,
            'lid'      => $lid,
            'tid'      => $tid,
        ];

        $result = $this->call(self::_AGREEMENT_TICKET_BATCH_OPERATE_DETAIL__, $params);
        if (empty($result['data']['list'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $tidList       = array_column($result['data']['list'], 'ticket_id');
        $javaApi       = new \Business\CommodityCenter\Ticket();
        $tmpTicketInfo = $javaApi->queryTicketInfoByIds($tidList);

        $productInfo = [];
        foreach ($tmpTicketInfo as $value) {
            $productInfo[$value['ticket']['id']] = [
                'land_title'   => $value['land']['title'],
                'ticket_title' => $value['ticket']['title'],
            ];
        }

        foreach ($result['data']['list'] as &$tmp) {
            $tmp['land_title']   = $productInfo[$tmp['ticket_id']]['land_title'] ?? '';
            $tmp['ticket_title'] = $productInfo[$tmp['ticket_id']]['ticket_title'] ?? '';
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 新增配置数据
     * <AUTHOR>
     * @date 2020/12/30
     *
     * @param  int  $applyDid  供应商ID
     * @param  string  $title  名称
     * @param  int  $fid  分销商ID
     * @param  int  $lid  景区ID
     * @param  int  $tid  票ID
     * @param  int  $useType  启用类型 1=启用时间内有效 2=永久有效
     * @param  int  $store  协议库存
     * @param  int  $operater  操作用户ID
     * @param  int  $isDistribution  是否向下分销 1=否 2=是
     * @param  int  $startTime  启用时间的开始时间
     * @param  int  $endTime  启用时间的结束时间
     *
     * @return array
     */
    public function addConfig
    (
        int $applyDid,
        string $title,
        int $fid,
        int $lid,
        int $tid,
        int $useType,
        int $store,
        int $operater,
        int $isDistribution,
        int $startTime = null,
        int $endTime = null
    )
    {

        if (!$applyDid || !$title || !$fid || !$lid || !$tid || !$useType || !$store || !$operater || !$isDistribution) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'applyDid'       => $applyDid,
            'title'          => $title,
            'fid'            => $fid,
            'lid'            => $lid,
            'tid'            => $tid,
            'useType'        => $useType,
            'store'          => $store,
            'operater'       => $operater,
            'isDistribution' => $isDistribution,
        ];

        if (!is_null($startTime)) {
            $params['startTime'] = $startTime;
        }

        if (!is_null($endTime)) {
            $params['endTime'] = $endTime;
        }

        $result = $this->call(self::__AGREEMENT_TICKET_ADDCONFIG__, $params);

        if ($result['code'] == 200) {
            //更新票属性标识
            $this->saveExtInfo($applyDid, $operater, [$tid], 1);
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 批量新增配置数据
     * @param int $applyDid
     * @param int $fid
     * @param array $configList
     * @param int $operater
     *
     * @return array
     */
    public function batchAddConfig($applyDid, $fid, $configList, $operater)
    {
        $checkedConfigList = [];
        foreach ($configList as $value) {
            if (!$value['title'] || !$value['lid'] || !$value['tid'] || !$value['useType'] || !is_numeric($value['store'])
                || !$value['store'] || !$value['isDistribution']) {
                return $this->returnData(400, '参数异常');
            }
            $checkedConfigList[] = [
                'title'          => $value['title'],
                'lid'            => (int)$value['lid'],
                'tid'            => (int)$value['tid'],
                'useType'        => (int)$value['useType'],
                'store'          => $value['store'],
                'startTime'      => $value['startTime'] ? strtotime($value['startTime']) : 0,
                'endTime'        => $value['endTime'] ? strtotime($value['endTime']) : 0,
                'isDistribution' => (int)$value['isDistribution'],
            ];
        }

        $params = [
            'applyDid'   => $applyDid,
            'fid'        => $fid,
            'configList' => $checkedConfigList,
            'operater'   => $operater,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_BATCHADDCONFIG__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 批量查询票是否已配置
     * @param $applyDid
     * @param $fid
     * @param $tidArray
     *
     * @return array
     */
    public function queryConfigByAidFidTidArray($applyDid, $fid, $tidArray)
    {
        if (!$applyDid || !$fid || !$tidArray || !is_array($tidArray)) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'applyDid' => $applyDid,
            'fid'      => $fid,
            'tidArray' => $tidArray,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_QUERY_CONFIG_BY_AIDFIDTIDARRAY__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 更新配置数据
     * <AUTHOR>
     * @date 2020/12/30
     *
     * @param  int  $id  主键ID
     * @param  int  $applyDid  供应商ID
     * @param  string  $title  名称
     * @param  int  $useType  启用类型 1=启用时间内有效 2=永久有效
     * @param  int  $store  协议库存
     * @param  int  $operater  操作用户ID
     * @param  int  $isDistribution  是否向下分销 1=否 2=是
     * @param  int  $startTime  启用时间的开始时间
     * @param  int  $endTime  启用时间的结束时间
     *
     * @return array
     */
    public function modConfig(int $id, int $applyDid, string $title, int $useType, int $store, int $operater, int $isDistribution, int $startTime = null, int $endTime = null)
    {
        if (!$id || !$applyDid || !$title || !$useType || !$store || !$operater || !$isDistribution) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'id'             => $id,
            'applyDid'       => $applyDid,
            'title'          => $title,
            'useType'        => $useType,
            'store'          => $store,
            'operater'       => $operater,
            'isDistribution' => $isDistribution,
        ];

        if (!is_null($startTime)) {
            $params['startTime'] = $startTime;
        }

        if (!is_null($endTime)) {
            $params['endTime'] = $endTime;
        }

        $result = $this->call(self::__AGREEMENT_TICKET_MODCONFIG__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取配置信息
     * <AUTHOR>
     * @date 2021/1/2
     *
     * @param  int  $configId  配置ID
     * @param  int  $applyDid  供应商ID
     *
     * @return array
     */
    public function queryConfigInfo(int $configId, int $applyDid)
    {
        if (!$configId) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'configId' => $configId,
            'applyDid' => $applyDid,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYCONFIGINFO__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取配置库存信息
     * <AUTHOR>
     * @date 2021/1/2
     *
     * @param  int  $configId  配置ID
     *
     * @return array
     */
    public function queryConfigStorageInfo(int $configId, int $applyDid)
    {
        if (!$configId || !$applyDid) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'configId' => $configId,
            'applyDid' => $applyDid,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_QUERYCONFIGSTORAGEINFO__, $params);

        if (empty($result['data'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        $tid = $result['data']['ticket_id'];
        $fid = $result['data']['fid'];

        $memberBuz = new Member();
        $userInfo  = $memberBuz->getInfo($fid);

        $javaApi       = new \Business\CommodityCenter\Ticket();
        $tmpTicketInfo = $javaApi->queryTicketInfoById($tid);

        $productInfo = [];
        foreach ($tmpTicketInfo as $value) {
            $productInfo[$value['ticket']['id']] = [
                'land_title'   => $value['land']['title'],
                'ticket_title' => $value['ticket']['title'],
            ];
        }

        $result['data']['land_title']     = $tmpTicketInfo['land']['title'] ?? '';
        $result['data']['ticket_title']   = $tmpTicketInfo['ticket']['title'] ?? '';
        $result['data']['dname']          = $userInfo['dname'] ?? '';
        $result['data']['account']        = $userInfo['account'] ?? '';
        $result['data']['cname']          = $userInfo['cname'] ?? '';
        $result['data']['mobile']         = $userInfo['mobile'] ?? '';
        $result['data']['isDistribution'] = $result['data']['is_distribution'];

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 保存门票是否协议票扩展信息
     * <AUTHOR>
     * @date 2021/1/4
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberId  操作员id
     * @param  array  $ticketIdArr  门票id数组
     * @param  int  $state  1启用；0禁用
     *
     * @return array
     */
    public function saveExtInfo(int $sid, int $memberId, array $ticketIdArr, int $state)
    {
        if (is_array($ticketIdArr)) {
            foreach ($ticketIdArr as $ticketId) {
                $params[] = [
                    'ticketId' => $ticketId,
                    'key'      => 'is_agreement_ticket',
                    'val'      => $state,
                ];
            }
        } else {
            $params[] = [
                'ticketId' => $ticketIdArr,
                'key'      => 'is_agreement_ticket',
                'val'      => $state,
            ];
        }

        if (empty($params)) {
            return false;
        }

        //获取票类扩展属性字典
        $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
        $extSaveRes    = $ticketExtConf->save($sid, $memberId, $params);

        if ($extSaveRes['code'] != 200) {
            $log = [
                'sid'         => $sid,
                'memberId'    => $memberId,
                'ticketIdArr' => $ticketIdArr,
                'state'       => $state,
                'res'         => $extSaveRes,
            ];
            pft_log('agreementTicket/config/saveExtInfo', json_encode($log, JSON_UNESCAPED_UNICODE));

            return false;
        }

        return true;
    }

    /**
     * 删除配置数据
     * <AUTHOR>
     * @date 2020/12/30
     *
     * @param  int  $id  主键ID
     * @param  int  $applyDid  供应商ID
     * @param  int  $operater  操作用户ID
     *
     * @return array
     */
    public function delConfig(int $id, int $applyDid, int $operater)
    {
        if (!$id || !$applyDid || !$operater) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'id'       => $id,
            'applyDid' => $applyDid,
            'operater' => $operater,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_DELCONFIG__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 同步向下分销配置数据到分销系统
     * <AUTHOR>
     * @date 2021/6/17
     *
     * @param  int  $uid  用户ID
     * @param  int  $fid  分销商ID
     * @param  int  $tid  票ID
     * @param  int  $isDistribution  是否向下分销 1=不允许向下分销 2=允许向下分销
     *
     * @return array
     */
    public function syncIsDistributionToEvolute(int $uid, int $fid, int $tid, int $isDistribution)
    {
        if (!$uid || !$fid || !$tid || !$isDistribution) {
            return $this->returnData(400, '参数异常');
        }

        $params = [
            'uid'   => $uid,
            'fid'   => $fid,
            'tid'   => $tid,
            'isDistribution' => $isDistribution,
        ];

        $result = $this->call(self::__AGREEMENT_TICKET_SYNCISDISTRIBUTIONTOEVOLUTE__, $params);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 应用中心应用状态同步
     * <AUTHOR>
     * @date 2021/6/17
     *
     * @param  int  $uid  用户ID
     * @param  int  $state  应用状态 1=开启 2=关闭
     *
     */
    public function appCenterSync(int $uid, int $state): void
    {
        $saveState = ($state == 1) ? 1 : 0;

        pft_log('agreementTicket/appCenterSync', 'debug:' . "{$uid}||{$state}");

        $page = 1;
        $size = 200;
        while (true) {
            $configRes = $this->queryConfigPage($uid, $page, $size);

            if ($configRes['code'] != 200) {
                break;
            }

            if (empty($configRes['data']['list'])) {
                break;
            }

            //$tids = array_column($configRes['data']['list'], 'ticket_id');
            //$saveRes = $this->saveExtInfo($uid, $uid, $tids, $saveState);

            foreach ($configRes['data']['list'] as $value) {

                $saveRes = $this->saveExtInfo($uid, $uid, [$value['ticket_id']], $saveState);
                pft_log('agreementTicket/appCenterSync', 'saveExtInfo:' . json_encode([
                        'req' => [$uid, $uid, $value['ticket_id'], $saveState],
                        'res' => $saveRes,
                    ]));


                if ($state == 1) {
                    //应用开启
                    $isDistribution = $value['is_distribution'];

                } else {
                    //应用关闭
                    $isDistribution = 2;
                }

                $evoluteRes = $this->syncIsDistributionToEvolute($value['apply_did'], $value['fid'],
                    $value['ticket_id'],
                    $isDistribution);
                pft_log('agreementTicket/appCenterSync', 'syncIsDistributionToEvolute:' . json_encode([
                        'req' => [
                            $value['apply_did'],
                            $value['fid'],
                            $value['ticket_id'],
                            $isDistribution,
                        ],
                        'res' => $evoluteRes,
                    ]));
            }

            $page++;
        }

    }

    /**
     * 查询有分销的票列表（带有tid是否已配置）
     * @param $params
     *
     * @return array
     */
    public function queryTicketPaging($params)
    {
        if (!$params['fid'] || !$params['sid'] || !$params['pageNum'] || !$params['pageSize']) {
            return $this->returnData(400, '参数异常');
        }
        $product              = new Product();
        $params['queryTotal'] = 2;//每页都返回总数
        $params['active']     = 1;
        //查询分销的票列表
        $ticketResult         = $product->queryDistributionTicketTitlePageFullParameter($params);
        if ($ticketResult['code'] != self::CODE_SUCCESS){
            return $this->returnData($ticketResult['code'], $ticketResult['msg'], $ticketResult['data']);
        }
        $lidList              = array_column($ticketResult['data']['list'], 'lid');
        $tidList              = array_column($ticketResult['data']['list'], 'tid');
        //查询已配置的tid
        $configured           = $this->queryConfigByAidFidTidArray($params['sid'], $params['fid'], $tidList);
        $configuredMap        = array_column($configured['data'], null, 'ticket_id');

        $landService          = new Land();
        //查询产品名称
        $landInfoRes          = $landService->queryLandByIds($lidList);
        $landTitleMap         = array_column($landInfoRes['data'], 'title', 'id');
        $returnData           = [
            'list'  => [],
            'total' => $ticketResult['data']['total'] ?? 0,
        ];
        foreach ($ticketResult['data']['list'] as $value) {
            $temp                 = [
                'configured' => isset($configuredMap[$value['tid']]),//已配置的为true，在页面上无法勾选，未配置false，可以勾选
                'landTitle'  => $landTitleMap[$value['lid']] ?? '',
            ];
            $returnData['list'][] = array_merge($value, $temp);
        }

        return $this->returnData($ticketResult['code'], $ticketResult['msg'], $returnData);
    }

    /**
     * 推入队列
     * <AUTHOR>
     * @date 2021/6/17
     *
     * @param  int  $uid  用户ID
     * @param  int  $state  应用状态 1=开通 2=关闭
     *
     * @return string
     */
    //public function pushAppCenter(int $uid, int $state)
    //{
    //    $data  = [
    //        'action' => 'app_center_sync',
    //        'data'   => ['uid' => $uid, 'state' => $state],
    //    ];
    //    $jobId = Queue::push('independent_system', 'AgreementTicket_Job', $data);
    //
    //    if (empty($jobId)) {
    //        pft_log('agreementTicket/config/pushAppCenter',
    //            '应用状态更新队列任务失败:' . json_encode($data, JSON_UNESCAPED_UNICODE));
    //    }
    //
    //    return $jobId;
    //}
}