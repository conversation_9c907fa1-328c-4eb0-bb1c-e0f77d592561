<?php
/**
 * PC平台特殊团队预约订单
 * Author: xwh
 * Date: 2018/11/25
 */

namespace Business\ReservationSpecialTeam;

use Business\AppCenter\BaseCall;
use Business\Authority\AuthContext;
use Business\JavaApi\Product\ChannelInspectConvert;
use Business\Order\OrderList;
use Business\Order\OrderStatus;
use Business\Order\PlatformSubmit;
use Business\Base;
use Model\Order\OrderRefer;
use Model\Order\OrderTools;
use Model\Order\RefundAuditModel;
use Model\Order\SpecilTeamOrder;
use Model\Order\TeamOrderSearch;
use Model\Product\Land;
use Process\Order\OrderParams;
use Business\JavaApi\Product\Ticket;
use Business\Authority\AuthLogic as AuthLogicBiz;

class TeamOrder extends Base
{
    private $_allOrderStatusConf;
    public function __construct()
    {
        $this->_allOrderStatusConf = (new OrderStatus())->getAllTypeOrderStatusMap();
    }

    private $_sortName = ['plat_time', 'create_time'];
    const RESERVATION_SPECIAL_CHANNEL = 55;
    private $_head = [
        '预约订单号',
        '接待产品及票种',
        '联系人',
        '手机号',
        '班级/单位名称',
        '级别',
        '人数',
        '参观日期',
        '参观时段',
        '录入时间',
        '填报人',
        '核销人',
        '状态',
        '更多',
        '备注',
    ];

    /**
     * 录入特殊团队订单
     * <AUTHOR>
     * @date 2021-03-24
     *
     * @param  int $memberId 供应商id
     * @param  int $opid 操作人id
     * @param  int $pid 产品id
     * @param  int $aid 产品供应商id
     * @param  string $begintime 游玩日期
     * @param  string $ordername 联系人名称
     * @param  string $unitName 团体名称
     * @param  int $specialLevel 特殊团体等级
     * @param  int $specialType 特殊团体类型
     * @param  string $ordertel 联系人手机号
     * @param  array $proidList 合并付款门票参数
     *
     * @return array
     */
    public function reservationSpecialTeamOrder($memberId, $opid, $pid, $aid, $begintime, $ordername, $unitName, $specialLevel, $specialType, $ordertel, array $proidList)
    {
        if (!$proidList) {
            return [400, '订单参数缺失'];
        }
        $proidList = json_encode($proidList);

        $data = compact('proidList',
            'memberId',
            'opid',
            'pid',
            'aid',
            'begintime',
            'ordername',
            'unitName',
            'specialLevel',
            'specialType',
            'ordertel'
        );
        $result = (new BaseCall())->appCenterCall(BaseCall::_RESERVATIONSPECIALTEAMORDER_, $data, 'special_reservation');

        return ["code"=>$result['code'],"msg"=>$result['msg'],"data"=>$result['data']];
    }

    /**
     * 根据条件获取特殊团队接待订单信息
     * <AUTHOR>
     * @date 2020/10/18
     *
     * @param  int $fid 供应商id
     * @param  int $timeType 时间类型 0 游玩时间 1 下单时间
     * @param  int $type 特殊团队 0 政务接待 1 干部学院
     * @param  string $startTime 开始时间
     * @param  string $endTime 结束时间
     * @param  int $status 状态 -1全部   0  '未使用',1  '已使用', 2  '已过期' 3 '被取消',
     * @param  string $ordernum 订单好
     * @param  int $lid 景点id
     * @param  int $tid 票id
     * @param  string $phone 联系人电话号
     * @param  string $unitName 单位名称/班级名称
     * @param  string $name 联系人
     * @param  int $page
     * @param  int $size
     * @param  string $sortName 排序 【'plat_time' => 游玩时间段 ,'create_time'】
     * @param  int $sortType -1全部 0 升 1 降
     *
     * @return array
     */
    public function querySpecialTeamOrderList(int $fid, int $type, int $timeType = -1, string $startTime = '', string $endTime = '', int $status = -1, string $ordernum = '', int $lid = 0, int $tid = 0, string $phone = null, string $unitName = null, string $name = null, int $page = 1, int $size = 10, string $sortName = '', int $sortType = -1)
    {
        if (!$fid) {
            return $this->returnData(400, '用户ID参数错误', []);
        }

        if (!in_array($type,[0,1])) {
            return $this->returnData(400, '特殊团队类型参数错误', []);
        }

        if ($sortName && !in_array($sortName,$this->_sortName)) {
            return $this->returnData(400, '排序参数错误', []);
        }
        $data = compact('fid', 'timeType', 'startTime', 'endTime', 'status', 'ordernum', 'lid', 'tid', 'phone','type',
            'unitName', 'name', 'page', 'size', 'sortName', 'sortType');

        $result = (new BaseCall())->appCenterCall(BaseCall::_GETSPECIALORDERLIST_, $data, 'special_reservation');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }
        $orderlist    = $result['data']['orderNums'];
        $total        = $result['data']['total'];
        $orderExtInfo = $result['data']['orderInfo'];

        //数据处理
        $list = $this->_handleOrderResult($orderlist);
        if ($list) {
            //获取核销人 下单人
            $orderopUsers    = array_column($orderExtInfo, 'opid');
            $orderelectUsers = array_column($list, 'elect_id');
            $userId          = array_unique(array_merge($orderopUsers, $orderelectUsers));
            $userInfo        = (new \Business\Member\Member())->getMemberInfoByMulti($userId);
            $userInfo && $userInfo = array_column($userInfo, 'dname', 'id');
            $list = array_column($list, null, 'ordernum');
            foreach ($list as $orderNum => &$item) {
                $item['unit_name'] = $orderExtInfo[$orderNum]['unit_name'];
                //特殊团队 0 政务接待 1 干部学院
                $item['special_type'] = $orderExtInfo[$orderNum]['special_type'];
                //接待级别 0国家级 1 省级 2 市级 3地方级
                $item['special_level'] = $orderExtInfo[$orderNum]['special_level'];
                //录入人
                $item['opname'] = $userInfo[$orderExtInfo[$orderNum]['opid']];
                //核销人
                $item['elect_name'] = $item['elect_id'] ? $userInfo[$item['elect_id']] : '';
                //参观时段
                $item['participate_period'] = $orderExtInfo[$orderNum]['participate_period'] ?: '-';
                $item['more_info'] = $orderExtInfo[$orderNum]['more_info'] ?: '';
                $item['remarks'] = $orderExtInfo[$orderNum]['remarks'] ?: '';
            }
        }

        $list = array_values($list);
        $resData = [
            'list'  => $list,
            'total' => $total,
        ];

        return $this->returnData(200, $result['msg'], $resData);
    }

    /**
     * 订单查询结果数据处理
     * <AUTHOR>
     * @date   2021-03-25
     * @param  array  $resOrderIdArr
     * @return array
     */
    private function _handleOrderResult($resOrderIdArr)
    {
        if (empty($resOrderIdArr)) {
            return $resOrderIdArr;
        }
        $orderIdArr = $resOrderIdArr;
        //获取包含子票的所有订单的信息
        $orderToolModel  = new OrderTools('slave');
        $tmpResOrderInfo = $orderToolModel->getOrderInfo($orderIdArr,
            'ordernum, contacttel, ordertel, ordername,ordertime,dtime,begintime,endtime, status, pay_status, lid, tid, pid, aid, member, tnum, tprice, paymode, ordermode, totalmoney, code, playtime, salerid, visitors, product_type');

        if (!$tmpResOrderInfo) {
            return $resOrderIdArr;
        }
        //获取子票信息
        $packOrderIds = [];
        foreach ($tmpResOrderInfo as $item){
            if ($item['product_type'] == "F") {
                $packOrderIds[] = $item['ordernum'];
            }
        }
        //团单子票订单号集合
        $packOrderInfo = $this->_getPackOrder($packOrderIds);
        //查找出套票子票订单信息集合
        $packOrderRes = $packOrderInfo['packOrderRes'];

        $detailInfo = $orderToolModel->getOrderDetailInfo($orderIdArr,
            'orderid, ext_content');
        $detailInfo = array_column($detailInfo, null, 'orderid');
        //套票子票的订单号集合
        $packOrderArr = $packOrderInfo['packOrderArr'];
        $packResOrderInfo = [];
        $packOrderArr && $packResOrderInfo = $orderToolModel->getOrderInfo($packOrderArr,
            'ordernum, code');

        $packResOrderInfo && $packResOrderInfo = array_column($packResOrderInfo,'code','ordernum');

        //订单验证信息获取
        $OrderTrackInfo = ((new OrderRefer())->getOrderTrackByOrderId($resOrderIdArr, 5, 'ordernum, oper_member'));

        $OrderTrackInfo && $OrderTrackInfo = array_column($OrderTrackInfo,'oper_member','ordernum');

        $orderInfo = [];
        foreach ($tmpResOrderInfo as $tmpItem) {
            $orderInfo[$tmpItem['ordernum']] = $tmpItem;
        }
        unset($tmpResOrderInfo);
        //订单号和数量之间的关联
        $ticketNumArr = [];
        //订单号关联的tid
        $ticketArr = [];
        //订单号关联的lid
        $landArr = [];
        //订单号关联的状态
        $statusArr = [];
        //渠道所需分销链数据
        $channelSplitArr = [];
        //订单号关联的凭证码
        $codeArr = [];
        foreach ($orderInfo as $key => $item) {
            $ticketNumArr[$item['ordernum']]  = $item['tnum'];
            $ticketArr[$item['ordernum']]     = $item['tid'];
            $landArr[$item['ordernum']]       = $item['lid'];
            $statusArr[$item['ordernum']]     = $item['status'];
            $payStatusArr[$item['ordernum']]  = $item['pay_status'];
            $totalMoneyArr[$item['ordernum']] = $item['totalmoney'];
            $codeArr[$item['ordernum']]       = $item['code'];
            //渠道所需分销链数据
            $channelSplitArr[] = [
                'ticketId'  => $item['tid'],
                'fid'       => $item['member'],
                'sid'       => $item['aid'],
                'visitors'  => $item['visitors'],
                'ordermode' => $item['ordermode'],
            ];
        }
        $lidArr = array_unique(array_column($orderInfo, 'lid'));
        $data = [];

        foreach ($resOrderIdArr as $resOrdernum) {
            if (isset($orderInfo[$resOrdernum])) {
                $item   = $orderInfo[$resOrdernum];
                $data[] = [
                    'ordernum'   => $item['ordernum'],
                    'ordertime'  => $item['ordertime'],
                    'dtime'      => $item['dtime'],
                    'playtime'   => $item['playtime'],
                    'begintime'  => $item['begintime'],
                    'endtime'    => $item['endtime'],
                    'lid'        => $item['lid'],
                    'pid'        => $item['pid'],
                    'tid'        => $item['tid'],
                    'status'     => $item['status'],
                    'pay_status' => $item['pay_status'],
                    'ordertel'   => $item['ordertel'],
                    'ordername'  => $item['ordername'],
                    'ordermode'  => $item['ordermode'],
                ];
            }
        }
        //获取景区的名称和类型信息
        $landInfo = $this->_getLandInfo($lidArr);
        $landRes  = $landInfo['landRes'];
        $pTypeRes = $landInfo['pTypeRes'];
        //获取票类信息
        $ticketRes = $this->_getTicketChannelInfo($channelSplitArr);
        //处理游玩时间信息
        $packageTimeShareInfos = [];

        //获取子票票类信息
        $sticketId = [];
        foreach ($detailInfo as $detai){
            if (!empty($detai['ext_content'])) {
                $extContent = json_decode($detai['ext_content'],true);
                $packageTimeShareInfo = $extContent['packageTimeShareInfo'] ?? [];
                $packageTimeShareInfos[$detai['orderid']] = $packageTimeShareInfo;
                $sticketId =  array_merge($sticketId,array_keys($packageTimeShareInfo));
            }
        }

        $sticketId = array_unique($sticketId);
        $sticketInfo = (new \Model\Product\Ticket())->getTicketList($sticketId);

        foreach ($packageTimeShareInfos as &$detai){
            if (!empty($detai)) {
                foreach ($detai as $key => &$item){
                    $item['title'] = $sticketInfo[$key]['title'];
                }
            }
            unset($item);
        }
        unset($detai);

        foreach ($data as $item) {
            $orderNum = $item['ordernum'];
            $lid     = $landArr[$item['ordernum']];
            $tid     = $ticketArr[$item['ordernum']];
            $beginTime  = $item['begintime'];
            $endTime    = $item['endtime'];
            $contacttel = $item['ordertel'];
            //订单状态
            $status = $statusArr[$item['ordernum']];
            //景区类型
            $pType   = isset($pTypeRes[$item['lid']]) ? $pTypeRes[$item['lid']] : 0;
            //下单票数
            $tNum = isset($ticketNumArr[$orderNum]) ? $ticketNumArr[$orderNum] : 0;
            $tmpStatus = $this->_allOrderStatusConf[$pType][$status] ?? '';
            $code    = $codeArr[$item['ordernum']];
            $temp = [
                'type'              => 'common',
                //订单号
                'ordernum'          => $item['ordernum'],
                //下单时间
                'ordertime'         => $item['ordertime'],
                //验证时间
                'dtime'             => $item['dtime'],
                //预计游玩时间
                'playtime'          => $item['playtime'],
                //开始时间
                'begintime'         => $beginTime,
                //结束时间
                'endtime'           => $endTime,
                //联系人
                'contact_name'      => $item['ordername'],
                //联系人电话
                'contact_tel'       => $contacttel,

                //景区名字
                'lid'               => [
                    'id'    => $lid,
                    'title' => isset($landRes[$lid]) ? $landRes[$lid] : '',
                ],
                //票类名字
                'tid'               => [
                    'id'    => $tid,
                    'title' => isset($ticketRes[$tid]['title']) ? $ticketRes[$tid]['title'] : '',
                ],
                //数量
                'tnum'              => $tNum,
                'p_type'            => $pType,
                //状态 @TODO  部分验证 部分取消
                'status'            => $tmpStatus,
                //验证人id
                'elect_id'          => $OrderTrackInfo[$orderNum] ?? 0,
                //验证码
                'code'              => $code,
                //子票订单信息
                'packOrderArr'      => $packOrderRes[$item['ordernum']] ?? [],
                //游玩时间信息
                'play_time_info'    => $packageTimeShareInfos[$item['ordernum']] ?? []
            ];
            $result[] = $temp;
        }

        return array_values($result);
    }


    /**
     * 获取景区信息
     * <AUTHOR>
     * @date 2021-03-25
     * @return array
     */
    private function _getLandInfo($lidArr)
    {
        $landModel = new Land();
        //获取景区名称信息信息
        $landRes = [];
        //获取景区类型信息
        $pTypeRes = [];
        //商家信息，获取salerid
        $selaRes  = [];
        //$landInfo = $landModel->getLandInfoByMuli($lidArr, 'id, title, p_type, imgpath, salerid');

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr);
        if (!empty($landInfo) && is_array($landInfo)) {
            foreach ($landInfo as $item) {
                $landRes[$item['id']]  = $item['title'];
                $pTypeRes[$item['id']] = $item['p_type'];
                $imageRes[$item['id']] = $item['imgpath'];
                $selaRes[$item['id']]  = $item['salerid'];
            }
        }
        unset($landInfo);

        return [
            'landRes'  => $landRes,
            'pTypeRes' => $pTypeRes,
            'imgRes'   => $imageRes,
            'selaRes'  => $selaRes,
        ];
    }


    /**
     * 获取订单对应的票属性
     * <AUTHOR>
     * @date 2021-03-25
     *
     * @param $data
     * [
     *     [
     *       'ordernum' => string,   // 订单号
     *       'fid'      => int,    // 分销商id
     *       'sid'      => int,    // 上级供应商id
     *       'visitors' => int,    // 1游客， 2分销商
     *       'ordermode'=> int,    // 下单渠道
     *     ]
     * ]
     *
     * @return array
     */
    private function _getTicketChannelInfo($data)
    {
        $channelInspectConvertBiz = new ChannelInspectConvert();
        //票属性批量转换属性
        $ticketInfoRes            = $channelInspectConvertBiz->batchConvert($data);
        if ($ticketInfoRes['code'] != 200) {
            return [];
        }

        $ticketInfoRes = $ticketInfoRes['data'];
        // 转换票属性
        $ticketInfo = [];
        foreach ($ticketInfoRes as $item) {
            $ticketInfo[] = Ticket::mapAttribute($item['uuJqTicketDTO'], 'ticket,confs');
        }

        $ticketInfo = is_array($ticketInfo) ? $ticketInfo : [];
        $ticketRes  = [];

        foreach ($ticketInfo as $item) {
            $ticketRes[$item['id']]['title']                 = $item['title'];
            $ticketRes[$item['id']]['num_modify']            = $item['num_modify'];
            $ticketRes[$item['id']]['getaddr']               = $item['getaddr'];
            $ticketRes[$item['id']]['batch_check']           = $item['batch_check'];
            $ticketRes[$item['id']]['reb']                   = $item['reb'];
            $ticketRes[$item['id']]['reb_type']              = $item['reb_type'];
            $ticketRes[$item['id']]['cancel_cost']           = $item['cancel_cost'];
            $ticketRes[$item['id']]['Mdetails']              = $item['Mdetails'] ?? '';
            $ticketRes[$item['id']]['apply_did']             = $item['apply_did'];
            $ticketRes[$item['id']]['status']                = $item['status'];
            $ticketRes[$item['id']]['refund_rule']           = strval($item['refund_rule']);
            $ticketRes[$item['id']]['cancel_auto_onMin']     = $item['cancel_auto_onMin'];
            $ticketRes[$item['id']]['refund_early_time']     = $item['refund_early_time'];
            $ticketRes[$item['id']]['refund_after_time']     = $item['refund_after_time'];
            $ticketRes[$item['id']]['ticket_changing_range'] = $item['ticket_changing_range'];
            $ticketRes[$item['id']]['is_show_seat']          = '';
            $ticketRes[$item['id']]['pre_sale']              = $item['preSale'];
        }
        unset($landInfo, $ticketInfo);

        return $ticketRes;
    }

    /**
     * 获取套票子票
     */
    private function _getPackOrder($orderIdArr)
    {
        if (!$orderIdArr) {
            return [
                'packOrderRes' => [],
                'packOrderArr' => [],
            ];
        }
        $model     = new OrderTools();
        $packOrder = $model->getPackSubOrder($orderIdArr, 'orderid, pack_order');

        //获取包含子票的所有订单的信息
        $packOrderRes = [];
        $packOrderArr = [];
        foreach ($packOrder as $item) {
            $packOrderArr[] = $item['orderid'];
        }
        //获取子票code
        $tmpResOrderInfo = $model->getOrderInfo($packOrderArr);
        $tmpResOrderInfo && $tmpResOrderInfo = array_column($tmpResOrderInfo,'code','ordernum');

        foreach ($packOrder as $item) {
            $data = [];
            $data['orderid'] = $item['orderid'];
            $data['code'] = $tmpResOrderInfo[$item['orderid']];
            //主票包含的子票订单号
            $packOrderRes[$item['pack_order']][] = $data;
        }
        unset($packOrder);
        return [
            'packOrderRes' => $packOrderRes,
            'packOrderArr' => $packOrderArr,
        ];
    }
    /**
     * 更新特殊预定团队订单状态
     * <AUTHOR>
     * @date 2021-03-25
     *
     * @param  string $orderNum 订单号
     * @param  int $status 订单状态
     *
     * @return boolean
     */
    public function updataSpecialTeamOrderStatus(string $orderNum, array $data)
    {
        if (!$orderNum || !$data) {
            return false;
        }

        //获取订单号是否在特殊团队订单表中
        $model = new SpecilTeamOrder();
        $id = $model->getOrderByOrdernumId($orderNum);
        if (!$id) {
            return false;
        }

        //更新
        $result = $model->saveOrderInfoById($id, $data);
        if (!$result) {
            return false;
        }
        return true;
    }

    /**
     * 检测库存为0时可预约的权限
     * <AUTHOR>
     * @date 2021/7/7
     *
     * @param  int  $specialType  类型 0=政务接待 1=干部学院
     * @param  int  $uid  用户ID
     * @param  array  $proidList  票数据
     *
     * @return array
     */
    public function checkBtnAuthMenuStorage(int $specialType, int $uid, array $proidList, int $sid = 0, int $dtype = null)
    {
        $elementTag = '';
        if ($specialType == 0) {
            $elementTag = 'governmentReception';
        }
        if ($specialType == 1) {
            $elementTag = 'cadreAcademy';
        }
        if (empty($elementTag)) {
            return false;
        }

        //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
        $result = (new AuthLogicBiz())->elementResource($sid, $uid, $elementTag, $dtype);

        //政务接待
        if ($elementTag == 'governmentReception') {
            if (in_array('governmentReception_storage', $result)) {
                return true;
            }

            foreach ($proidList as $value) {
                if ($value['more_storage'] != 0) {
                    return false;
                }
            }

            return true;
        }

        //干部学院
        if ($elementTag == 'cadreAcademy') {
            if (in_array('cadreAcademy_storage', $result)) {
                return true;
            }

            foreach ($proidList as $value) {
                if ($value['more_storage'] != 0) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }


    /**
     * 获取特殊团队预约接待报表的数据
     * <AUTHOR>
     * @date   2021-08-07
     * @param  int $beginDate 开始时间 20210703
     * @param  int $endDate 开始时间 20210703
     * @param  mixed $fid 主账号id
     * @param  mixed $lid 景区id
     * @param  int $pid 产品id
     * @param  string $tid 票id   111,22222
     * @param  string $operateId 录入人员id  111,22222
     * @param  string $groupBy
     * @param  int $page
     * @param  int $size
     * @param  int $isExcel 是否为导出
     *
     * @return array
     */
    public function specialTeamOrderReport(int $beginDate, int $endDate, $fid, string $lid = '', int $pid = 0, string $tid = '', string $operateId = '', string $groupBy = '', int $page = 1, int $size = 15, int $isExcel = 0){

        if (!$beginDate || !$endDate || !$fid) {
            return bizReturn(400, '参数缺失');
        }

        $data = [
            'fid'=> $fid,
            'lid'=> $lid,
            'pid'=> $pid,
            'tid'=> $tid,
            'group'=> $groupBy,
            'page'=> $page,
            'size'=> $size,
            'is_excel'=> $isExcel,
            'end_time'=> $endDate,
            'operate_id'=> $operateId,
            'start_time'=> $beginDate,
        ];

        $result = (new BaseCall())->appCenterCall(BaseCall::_SPECIALORDERREPORT_, $data, 'special_reservation');

        return ["code"=>$result['code'],"msg"=>$result['msg'],"data"=>$result['data']];
    }

    /**
     * 获取特殊团队预约接待报表的数据
     * <AUTHOR>
     * @date   2021-08-07
     * @param  int $beginDate 开始时间 20210703
     * @param  int $endDate 开始时间 20210703
     * @param  mixed $fid 主账号id
     * @param  mixed $lid 景区id
     * @param  int $pid 产品id
     * @param  string $tid 票id   111,22222
     * @param  string $operateId 录入人员id  111,22222
     * @param  int $page
     * @param  int $size
     *
     * @return array
     */
    public function excelList(int $beginDate, int $endDate, $fid, string $lid = '', int $pid = 0, string $tid = '', string $operateId = '', string $participatePeriod = '',int $page = 1, int $size = 15){

        if (!$beginDate || !$endDate || !$fid) {
            return bizReturn(400, '参数缺失');
        }

        $data = [
            'fid'=> $fid,
            'lid'=> $lid,
            'pid'=> $pid,
            'tid'=> $tid,
            'page'=> $page,
            'size'=> $size,
            'end_time'=> $endDate,
            'operate_id'=> $operateId,
            'start_time'=> $beginDate,
            'participate_period'=> $participatePeriod,
        ];

        $result = (new BaseCall())->appCenterCall(BaseCall::_SPECIALORDEREXCELLIST_, $data, 'special_reservation');

        if ($result['data']['list']) {
            //获取核销人 下单人
            $orderopUsers    = array_column($result['data']['list'], 'opid');
            $orderelectUsers = array_column($result['data']['list'], 'elect_id');
            $userId          = array_unique(array_merge($orderopUsers, $orderelectUsers));
            $userInfo        = (new \Business\Member\Member())->getMemberInfoByMulti($userId);
            $userInfo && $userInfo = array_column($userInfo, 'dname', 'id');
            $orderNums       =  array_column($result['data']['list'], 'order_num');
            $orderInfo       = $this->_handleOrderResult($orderNums);
            $orderInfo && $orderInfo = array_column($orderInfo, null, 'ordernum');

            foreach ($result['data']['list'] as &$item){
                $item['lid'] = [];
                $item['tid'] = [];
                $item['tnum'] = 0;
                $item['status'] = '未知';
                $item['elect_name'] = '';
                $item['opname'] = '';
                $item['ordertime'] = '';
                if ($orderInfo[$item['order_num']]) {
                    $item['lid'] = $orderInfo[$item['order_num']]['lid'];
                    $item['tid'] = $orderInfo[$item['order_num']]['tid'];
                    $item['tnum'] = $orderInfo[$item['order_num']]['tnum'];
                    $item['status'] = $orderInfo[$item['order_num']]['status'];
                    $item['ordertime'] = $orderInfo[$item['order_num']]['ordertime'];
                }
                $userInfo[$item['opid']] && $item['opname'] = $userInfo[$item['opid']];
                $userInfo[$item['elect_id']] && $item['elect_name'] = $userInfo[$item['elect_id']];
            }
            unset($item);
            $result['data']['list'] = $this->handleListExport($result['data']['list']);
        }

        return ["code"=>$result['code'],"msg"=>$result['msg'],"data"=>$result['data']];
    }

    /**
     * 处理导出的数据
     *
     * @param array $list 导出的数据
     *
     *
     * @return array
     * <AUTHOR>
     * @date 2021/3/9
     *
     *
     */
    public function handleListExport($list)
    {
        if (empty($list)) {
            return [];
        }
        $data = [];
        foreach ($list as $value) {
            $moreInfo = $value['more_info'];
            $more     = '';
            if (!empty($moreInfo) && $moreInfo != '[]') {
                $moreInfo = json_decode($moreInfo, true);
                if ($moreInfo) {
                    foreach ($moreInfo as $item){
                        $more .= $item['text'] . ':' .$item['documentValue'] . '  ';
                    }
                }
            }
            $data[] = [
                'ordernum'           => $value['order_num'] ."\t", //预约订单号
                'title'              => $value['lid']['title'] . $value['tid']['title'], //接待产品及列表
                'contact_name'       => $value['order_name'], //联系人
                'contact_tel'        => $value['order_tel'] ."\t", //手机号
                'unit_name'          => $value['unit_name'], //班级/单位名称
                'special_level'      => $value['special_level'], //级别
                'tnum'               => $value['tnum'], //人数
                'playtime'           => date('Y-m-d H:i:s',$value['play_time']), //参观日期
                'participate_period' => $value['participate_period'], //参观时段
                'ordertime'          => $value['ordertime'], //录入时间
                'opname'             => $value['opname'], //填报人
                'elect_name'         => $value['elect_name'], //核销人
                'status'             => $value['status'], //状态
                'more_info'          => $more,
                'remarks'            => $value['remarks'],
            ];
        }

        return $data;
    }

}