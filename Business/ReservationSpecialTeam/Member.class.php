<?php
/**
 * 平台特殊团队预约
 * Author: xwh
 * Date: 2018/11/25
 */

namespace Business\ReservationSpecialTeam;

use Business\AppCenter\BaseCall;
use Business\Base;


class Member extends Base
{
    /**
     * 获取当前用户特殊团队预约常用联系人
     * <AUTHOR>
     * @date 2021/3/19
     *
     * @param  int $memberId 供应商
     * @param  int $page
     * @param  int $size
     *
     * @return array
     */
    public function getContactsList(int $memberId, int $page = 1, int $size = 30)
    {
        if (!$memberId) {
            return $this->returnData(400, '缺少参数', []);
        }

        $data = compact('memberId','page', 'size');
        $result = (new BaseCall())->appCenterCall(BaseCall::_GETCONTACTSLIST_, $data, 'special_reservation');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(400, '查询异常', []);
        }

        return $this->returnData(200, '成功', $result['data']);
    }


    /**
     * 根据id删除特殊团队预约常用联系人
     * <AUTHOR>
     * @date 2021/3/19
     *
     * @param  int $sid 主账号id
     * @param  int $id
     *
     * @return array
     */
    public function delContacts(int $sid, int $id)
    {
        if (!$sid || !$id) {
            return $this->returnData(400, '缺少参数', []);
        }

        $data = compact('sid','id');
        $result = (new BaseCall())->appCenterCall(BaseCall::_DELCONTACTS_, $data, 'special_reservation');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(400, '删除异常', []);
        }

        return $this->returnData(200, '成功');
    }

    /**
     * 创建特殊团队预约常用联系人
     * <AUTHOR>
     * @date  2021-03-22
     *
     * @param  int $sid 供应商id
     * @param  int $opid 操作人
     * @param  string $name 联系人
     * @param  string $tel 联系人手机号
     * @param  string $unitname 单位名称/班级名称
     * @param  int $level 接待级别 0国家级 1 省级 2 市级 3地方级 4 一般级
     *
     * @return array
     * @throws
     */
    public function addContacts(int $sid, int $opid, string $name, string $tel, string $unitname, int $level)
    {
        if (!$sid || !$sid || !$opid || !$name || !$tel || !$unitname) {
            return $this->returnData(400, '缺少参数', []);
        }

        $data = compact('sid','opid', 'name', 'tel', 'unitname', 'level');
        $result = (new BaseCall())->appCenterCall(BaseCall::_ADDCONTACTS_, $data, 'special_reservation');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(400, '创建异常', []);
        }

        return $this->returnData(200, '成功');
    }
}