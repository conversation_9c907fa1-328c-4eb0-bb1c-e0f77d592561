<?php

namespace Business\OpenPlatformMiniApp;

use Business\Base;
use Library\Resque\Queue;
use Library\wechat\OpenplatformMiniApp as OpenplatformMiniAppLib;
use Model\Wechat\open;

class OpenPlatformMiniApp extends Base
{

    private $_headErrorMsg = [
        40097 => "参数错误",
        41006 => "media_id 不能为空",
        40007 => "非法的 media_id",
        46001 => "media_id 不存在",
        47001 => "数据格式错误",
        40009 => "图片尺寸太大",
        53202 => "本月头像修改次数已用完",
        41005 => "缺少多媒体文件数据，传输素材无视频或图片内容",
    ];

    private $_signatureErrorMsg = [
        40097 => "参数错误",
        53200 => "本月功能介绍修改次数已用完",
        53201 => "功能介绍内容命中黑名单关键字",
    ];

    private $_nickNameErrorMsg = [
        91001 => "不是公众号快速创建的小程序",
        91002 => "小程序发布后不可改名",
        91003 => "改名状态不合法，小程序发布前可改名的次数为2次，请确认改名次数是否已达上限",
        91004 => "昵称不合法",
        91005 => "昵称 15 天主体保护",
        91006 => "昵称命中微信号",
        91007 => "昵称已被占用",
        91008 => "昵称命中 7 天侵权保护期",
        91011 => "查不到昵称修改审核单信息",
        91009 => "需要提交材料",
        91013 => "占用名字过多",
        91014 => "+号规则 同一类型关联名主体不一致",
        91015 => "原始名不同类型主体不一致",
        91016 => "名称占用者 ≥2",
    ];

    private $_checkNickNameErrorMsg = [
        53010 => "名称格式不合法",
        53011 => "名称检测命中频率限制",
        53012 => "禁止使用该名称",
        53013 => "公众号：名称与已有公众号名称重复;小程序：该名称与已有小程序名称重复",
        53014 => "公众号：公众号已有{名称 A+}时，需与该帐号相同主体才可申请{名称 A};小程序：小程序已有{名称 A+}时，需与该帐号相同主体才可申请{名称 A}",
        53015 => "公众号：该名称与已有小程序名称重复，需与该小程序帐号相同主体才可申请;小程序：该名称与已有公众号名称重复，需与该公众号帐号相同主体才可申请",
        53016 => "公众号：该名称与已有多个小程序名称重复，暂不支持申请;小程序：该名称与已有多个公众号名称重复，暂不支持申请",
        53017 => "公众号：小程序已有{名称 A+}时，需与该帐号相同主体才可申请{名称 A};小程序：公众号已有{名称 A+}时，需与该帐号相同主体才可申请{名称 A}",
        53018 => "名称命中微信号",
        53019 => "名称在保护期内",
    ];

    /**
     * 小程序代注册接口
     *
     * @param int $sid 商家id
     * @param string $cpnName 企业名称
     * @param string $cpnCode 企业代码
     * @param int $cpnCodeType 企业代码类型（1：统一社会信用代码， 2：组织机构代码，3：营业执照注册号）
     * @param string $legalPersonaWeChat 法人微信号
     * @param string $legalPersonaName 法人姓名
     * @param string $componentPhone 第三方联系电话
     *
     * @return array|bool
     * *<AUTHOR>
     * @date 2020/3/28
     *
     */
    public function fasterRegister($sid, $cpnName, $cpnCode, $cpnCodeType, $legalPersonaWeChat, $legalPersonaName, $componentPhone)
    {
        if (empty($cpnName) || empty($cpnCode) || empty($cpnCodeType) || empty($legalPersonaWeChat) || empty($legalPersonaName)
            || empty($componentPhone) || !in_array($cpnCodeType, [1, 2, 3])) {
            return $this->returnData(203, "参数错误");
        }
        $res = OpenplatformMiniAppLib::fastRegisterWeapp($cpnName, $cpnCode, $cpnCodeType, $legalPersonaWeChat,
            $legalPersonaName,
            $componentPhone);

        if ($res['errcode'] != 0) {
            return $this->returnData(400, $res['errmsg']);
        }
        $openMode = new open();
        //先查询是否存在失败的记录，有失败的记录直接更新
        $exist = $openMode->getRegisterFailInfo($sid, 'id');
        if ($exist) {
            $result = $openMode->updateMiniAppRegisterInfo($exist['id'], OPEN_WECHAT_APPID, $cpnName, $cpnCode,
                $cpnCodeType,
                $legalPersonaWeChat, $legalPersonaName, $componentPhone);
        } else {
            $result = $openMode->addMiniAppApplyInfo($sid, OPEN_WECHAT_APPID, $cpnName, $cpnCode, $cpnCodeType,
                $legalPersonaWeChat, $legalPersonaName, $componentPhone);
        }
        if ($result === false) {
            pft_log('small_app/regiter', "商家{$sid}代注册信息入库失败，失败原因" . $openMode->getDbError());
        }

        return $this->returnData(200, "代注册任务提交成功");
    }

    /**
     * 修改用户小程序信息
     *
     * @param string $appId 商家appid
     * @param string $nicName 修改后的昵称
     * @param string $headImgUrl 头像素材途径url
     * @param string $signature 简介内容
     * @param string $license 组织机构代码证或营业执照 mediaid
     * @param string $namingOtherStuff 其他证明材料
     *
     * @return array|bool
     * *<AUTHOR>
     * @date 2020/4/11
     *
     */
    public function updateOpenSmallAppInfo($appId, $nicName, $headImgUrl, $signature, $license, $namingOtherStuff)
    {
        if (empty($appId)) {
            return $this->returnData(203, "参数错误");
        }
        $returnData = [];
        //修改名称调用修改名称接口
        if ($nicName !== false) {
            //400:修改错误   204:任务提交成功需要审核   200:不需要审核修改成功
            $nickCode = 200;
            $nickMsg  = "名称修改成功";
            //检测是否有正在审核中的昵称修改任务
            $openMode = new open();
            $result   = $openMode->getInVerifyNicknameChangelog($appId);
            if (!empty($result)) {
                $nickCode = 400;
                $nickMsg  = "已存在昵称修改任务，不可提交多条修改任务";
            } else {
                //提交微信审核
                $updateNickNameResult = OpenplatformMiniAppLib::setNickName($appId, $nicName, $license,
                    $namingOtherStuff);
                if ($updateNickNameResult['errcode'] != 0) {
                    $nickCode = 400;
                    $nickMsg  = $this->_nickNameErrorMsg[$updateNickNameResult['errcode']] ?? $updateNickNameResult['errmsg'];
                } elseif (!empty($updateNickNameResult['audit_id'])) {
                    // 若接口返回 audit_id，说明名称修改需要审核
                    $nickCode = 204;
                    $nickMsg  = "名称修改任务已提交成功";
                    //将任务添加到表里
                    $openMode->insertNicknameChangelog($appId, $nicName);
                }
            }
            $returnData['name'] = [
                'code' => $nickCode,
                'msg'  => $nickMsg,
            ];
        }
        //修改头像
        if ($headImgUrl !== false) {
            $mediaCode = 200;
            $mediaMsg  = "修改成功";
            //400:上传图片失败、修改头像失败   200:修改成功
            //将图片上传到微信
            $mediaResult = OpenplatformMiniAppLib::uploadFileToWX($appId, $headImgUrl);
            if ($mediaResult['errcode'] != 0) {
                $mediaCode = 400;
                $mediaMsg  = $mediaResult['errmsg'];
            } else {
                //图片上传成功，修改小程序头像
                $mediaId          = $mediaResult['media_id'];
                $updateHeadResult = OpenplatformMiniAppLib::updateHeadPhoto($appId, $mediaId);
                if ($updateHeadResult['errcode'] != 0) {
                    $mediaCode = $updateHeadResult['errcode'];
                    $mediaMsg  = $this->_headErrorMsg[$updateHeadResult['errcode']] ?? $updateHeadResult['errmsg'];
                }
            }
            $returnData['headPhoto'] = [
                'code' => $mediaCode,
                'msg'  => $mediaMsg,
            ];
        }
        //修改简介
        if ($signature !== false) {
            $updateSignatureResult = OpenplatformMiniAppLib::updateSignature($appId, $signature);
            if ($updateSignatureResult['errcode'] != 0) {
                $returnData['signature'] = [
                    'code' => 400,
                    'msg'  => $this->_signatureErrorMsg[$updateSignatureResult['errcode']] ?? $updateSignatureResult['errmsg'],
                ];
            } else {
                $returnData['signature'] = [
                    'code' => 200,
                    'msg'  => "修改成功",
                ];
            }
        }
        //修改完成，重新拉取一遍小程序信息
        $minAppInfo = OpenplatformMiniAppLib::getAccountBasicInfo($appId);
        if ($minAppInfo['errcode'] == 0) {
            //更新表里的信息
            $openMode = new open();
            $openMode->updateWxOpenNickNameAndHeadInfoByAppId($appId, $minAppInfo['nickname'],
                $minAppInfo['head_image_info']['head_image_url']);
            $openMode->updateSmallAppInfoByAppId($appId, $minAppInfo['nickname'],
                $minAppInfo['signature_info']['signature'],
                $minAppInfo['head_image_info']['head_image_url']);
        }

        return $this->returnData(200, "修改成功", $returnData);
    }

    /**
     * 上传临时素材到微信
     *
     * @param string $appId 商家appId
     * @param string $fileSrc 图片路径
     * @param string $type 媒体文件类型，分别有图片（image）、语音（voice）、视频（video）和缩略图（thumb）
     *
     * @return array| bool
     * <AUTHOR>
     * @date 2020/4/13
     *
     */
    public function uploadImageToWx($appId, $fileSrc, $type = 'image')
    {
        if (empty($fileSrc) || empty($appId)) {
            return $this->returnData(203, "参数错误");
        }
        $res = OpenplatformMiniAppLib::uploadFileToWX($appId, $fileSrc, $type);
        if (isset($res['errcode']) && $res['errcode'] != 0) {
            return $this->returnData(400, "图片上传失败");
        }

        return $this->returnData(200, "图片上传成功", $res['media_id']);
    }

    /**
     * 检测小程序昵称是否可以修改
     *
     * @param int $appId 商家appId
     * @param int $nickName 修改的昵称
     *
     * @return array|bool
     * <AUTHOR>
     * @date 2020/4/16
     *
     */
    public function checkNickNameCanUsed($appId, $nickName)
    {
        if (empty($appId) || empty($nickName)) {
            return $this->returnData(203, "参数错误");
        }

        $res = OpenplatformMiniAppLib::checkVerifyNickName($appId, $nickName);
        if ($res['errcode'] != 0) {
            return $this->returnData(400, $this->_checkNickNameErrorMsg[$res['errcode']] ?? $res['errmsg']);
        } elseif ($res['hit_condition'] === true) {
            //命中关键字
            return $this->returnData(201, $res['wording']);
        }

        return $this->returnData(200, "该昵称可以修改");
    }

    /**
     * 为小程序设置代码模板
     *
     * @param array $id 配置的小程序主键id
     * @param string $templateId 代码库中的代码模版 ID
     * @param string $userVersion 代码版本号，开发者可自定义（长度不要超过 64 个字符）
     * @param string $userDesc 代码描述，开发者可自定义
     * @param string $extJson 第三方自定义的配置
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/17
     *
     * */
    public function commitTemplate($id, $templateId = '', $userVersion = '', $userDesc = '', $extJson = '')
    {
        if (in_array('', [$templateId, $userVersion, $userDesc, $extJson])) {
            return $this->returnData(203, "参数错误");
        }
        //获取托管的小程序列表
        $wxOpenMode = new open();
        //如果id为空，则为批量设置小程序id，
        if (empty($id)) {
            //用异步任务处理，调用队列处理
            $data  = [
                'template_id'  => $templateId,
                'user_version' => $userVersion,
                'user_desc'    => $userDesc,
                'ext_json'     => $extJson,
            ];
            $jobId = Queue::push('wx_template', 'CommitTemplate_Job', $data);
            pft_log('queue/commit_template', "{$templateId}提交的任务id：" . $jobId);

            return $this->returnData(200, "批量设置功能已提交，详情到日志中查看");
        } else {
            $field       = 'appid, nick_name, id';
            $miniAppInfo = $wxOpenMode->getOpenMiniAppInfoById($id, $field);
            if (empty($miniAppInfo)) {
                return $this->returnData(400, "设置失败，获取小程序信息失败");
            }
        }
        //为每个托管的小程序设置模板
        //上传小程序模板
        $uploadTemplateResult = OpenplatformMiniAppLib::commitTemplate($miniAppInfo['appid'], $templateId, $userVersion,
            $userDesc, $extJson);
        if ($uploadTemplateResult['errcode'] != 0) {
            return $this->returnData($uploadTemplateResult['errcode'], $uploadTemplateResult['errmsg']);
        }
        //获取已上传的代码的页面列表
        $pageListInfo = OpenplatformMiniAppLib::getPage($miniAppInfo['appid']);
        $category     = OpenplatformMiniAppLib::getCategory($miniAppInfo['appid']);
        $itemList     = [];
        if ($pageListInfo['errcode'] == 0) {
            foreach ($pageListInfo['page_list'] as $item) {
                $itemList[] = [
                    'address'      => $item,
                    'tag'          => "旅游,服务",
                    'first_class'  => $category['category_list'][0]['first_class'] ?? "",
                    'first_id'     => $category['category_list'][0]['first_id'] ?? 0,
                    'second_class' => $category['category_list'][0]['second_class'] ?? "",
                    'second_id'    => $category['category_list'][0]['second_id'] ?? 0,
                ];
                if (count($itemList) >= 5) {
                    break;
                }
            }
        }
        //为小程序设置业务域名
        $requestDomain   = [
            "https://api.12301.cc",
            "https://apis.map.qq.com",
            "https://cooperator.12301.cc",
            "https://cooperator.gray.12301.cc",
            "https://pay.12301.cc",
        ];
        $modifyDomainRes = OpenplatformMiniAppLib::modifyDomain($miniAppInfo['appid'], 'add', $requestDomain);
        if ($modifyDomainRes['errcode'] != 0) {
            pft_log('open_mini_app', "小程序{$miniAppInfo['appid']}设置业务域名失败" .
                                     json_encode($modifyDomainRes, JSON_UNESCAPED_UNICODE));
        }
        //提交审核
        $submitResult = OpenplatformMiniAppLib::submitAudit($miniAppInfo['appid'], $itemList);
        if ($submitResult['errcode'] != 0) {
            return $this->returnData($submitResult['errcode'], $submitResult['errmsg']);
        }
        //审核提交成功，插入数据库
        $insertResult = $wxOpenMode->insertTemplateInfo($miniAppInfo['appid'], $submitResult['auditid'], $templateId);
        if ($insertResult === false) {
            pft_log('open_mini_app', "插入模板审核记录失败，原因：" . $wxOpenMode->getDbError());
        }

        return $this->returnData(200, "设置成功");
    }

    /**
     * 获取托管小程序列表
     *
     * @param string $templateId 模板id
     * @param string $companyName 公司名称
     * @param string $nikeName 小程序昵称
     * @param int $pageNum 第几页
     * @param int $pageSize 每页几条
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/22
     *
     *
     */
    public function getOpenMiniAppList($templateId, $companyName, $nikeName, $pageNum = 1, $pageSize = 10)
    {
        $openMode = new open();
        //如果存在公司名称，先获取该公司审核过的小程序列表
        $miniAppArr = [];
        if ($companyName) {
            $companyMiniAppList = $openMode->getCompanyMiniAppList($companyName, 'mini_appid');
            if (empty($companyMiniAppList)) {
                return $this->returnData(200, '获取成功');
            }
            $miniAppArr = array_column($companyMiniAppList, 'mini_appid');
        }
        $field  = "id, fid, nick_name, appid, create_time, update_time, template_id";
        $result = $openMode->getOpenMiniAppList($templateId, $miniAppArr, $nikeName, $pageNum, $pageSize, $field);
        if (!empty($result['list'])) {
            //获取小程序所属公司
            $miniAppIdArr = array_column($result['list'], 'appid');
            $companyInfo  = $openMode->getCompanyInfoByMiniAppIdArr($miniAppIdArr, 'mini_appid,cpn_name');
            $companyInfo  = array_key($companyInfo, 'mini_appid');
            foreach ($result['list'] as $key => $value) {
                $result['list'][$key]['cpn_name'] = isset($companyInfo[$value['appid']]['cpn_name']) ? $companyInfo[$value['appid']]['cpn_name'] : "";
            }
        }

        return $this->returnData(200, '获取成功', $result);
    }

    /**
     * 获取可以设置的所有类目
     *
     * @param int $id 托管小程序主键id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     */
    public function getAllCategories($id)
    {
        if (empty($id)) {
            return $this->returnData(203, "参数错误");
        }
        $wxOpenMode  = new open();
        $field       = 'appid';
        $miniAppInfo = $wxOpenMode->getOpenMiniAppInfoById($id, $field);
        if (empty($miniAppInfo)) {
            return $this->returnData(204, "小程序托管信息为空");
        }
        $categories = OpenplatformMiniAppLib::getAllCategories($miniAppInfo['appid']);
        if ($categories['errcode'] != 0) {
            return $this->returnData(204, "可设置类目列表获取失败");
        }

        return $this->returnData(200, "获取成功", $categories['categories_list']);
    }

    /**
     * 获取可以设置的所有类目
     *
     * @param int $id 托管小程序主键id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     */
    public function getCategories($id)
    {
        if (empty($id)) {
            return $this->returnData(203, "参数错误");
        }
        $wxOpenMode  = new open();
        $field       = 'appid';
        $miniAppInfo = $wxOpenMode->getOpenMiniAppInfoById($id, $field);
        if (empty($miniAppInfo)) {
            return $this->returnData(204, "小程序托管信息为空");
        }
        $categories = OpenplatformMiniAppLib::getCategories($miniAppInfo['appid']);
        if ($categories['errcode'] != 0) {
            return $this->returnData(204, $categories['errmsg']);
        }
        $returnData = [
            'categories'     => $categories['categories'], //已设置的类目信息列表
            'limit'          => $categories['limit'], //一个更改周期内可以添加类目的次数
            'quota'          => $categories['quota'], //本更改周期内还可以添加类目的次数
            'category_limit' => $categories['category_limit'], //最多可以设置的类目数量
        ];

        return $this->returnData(200, "获取成功", $returnData);
    }

    /**
     * 添加类目
     *
     * @param int $id 托管小程序主键id
     * @param array $categoryInfo 类目配置信息
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     *
     *
     */
    public function addCategory($id, $categoryInfo)
    {
        if (empty($id) || empty($categoryInfo)) {
            return $this->returnData(203, "参数错误");
        }
        $wxOpenMode  = new open();
        $field       = 'appid';
        $miniAppInfo = $wxOpenMode->getOpenMiniAppInfoById($id, $field);
        if (empty($miniAppInfo)) {
            return $this->returnData(204, "小程序托管信息为空");
        }
        $addResult = OpenplatformMiniAppLib::addCategory($miniAppInfo['appid'], $categoryInfo);
        if ($addResult['errcode'] != 0) {
            return $this->returnData($addResult['errcode'], $addResult['errmsg']);
        }

        return $this->returnData(200, "设置成功");
    }

    /**
     * 删除类目
     *
     * @param int $id 托管小程序主键id
     * @param int $first 一级类目id
     * @param int $second 二级类目id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     *
     *
     */
    public function deleteCategory($id, $first, $second)
    {
        if (empty($id) || (empty($first) && empty($second))) {
            return $this->returnData(203, "参数错误");
        }
        $wxOpenMode  = new open();
        $field       = 'appid';
        $miniAppInfo = $wxOpenMode->getOpenMiniAppInfoById($id, $field);
        if (empty($miniAppInfo)) {
            return $this->returnData(204, "小程序托管信息为空");
        }
        $addResult = OpenplatformMiniAppLib::deleteCategory($miniAppInfo['appid'], $first, $second);
        if ($addResult['errcode'] != 0) {
            return $this->returnData(204, $addResult['errmsg']);
        }

        return $this->returnData(200, "删除成功");
    }

    /**
     * 发布已通过审核的小程序
     *
     * @param int $id 托管小程序主键id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/7/13
     *
     *
     */
    public function releaseTemplate($id)
    {
        if (empty($id)) {
            return $this->returnData(203, "参数错误");
        }
        $wxOpenMode  = new open();
        $field       = 'appid';
        $miniAppInfo = $wxOpenMode->getOpenMiniAppInfoById($id, $field);
        if (empty($miniAppInfo)) {
            return $this->returnData(204, "小程序托管信息为空");
        }
        $addResult = OpenplatformMiniAppLib::releaseTemplate($miniAppInfo['appid']);
        if ($addResult['errcode'] != 0) {
            return $this->returnData(204, $addResult['errmsg']);
        }

        return $this->returnData(200, "发布成功");
    }

}