<?php

namespace Business\TerminalManage;

use Business\Base;
use Business\JavaApi\CommodityCenter\Ticket as CommodityCenterTicket;
use Business\Order\Face;
use Business\Order\OrderStatus;
use Business\PftSystem\FaceCompare as PftSystemFaceCompare;
use Library\Resque\Queue;
use Model\Order\OrderTools;
use Model\Order\OrderTourist;
use Model\Order\SubOrderQuery;
use Model\Terminal\FaceCompare;

/**
 * 订单人脸管理
 *
 * <AUTHOR>
 */
class OrderFaceManage extends Base
{

    public $bindSource = [
        1 => '云票务',
        2 => '小程序/短信/微商城',
        3 => '闸机反向采脸',
        4 => '后台管理'
    ];

    /**
     * 获取人脸订单列表
     *
     * @param integer $sid 供应商id
     * @param string $keyword 关键字
     * @param integer $page 当前页码
     * @param integer $size 每页条数
     * @return array
     * <AUTHOR>
     */
    public function getOrderList(int $sid, string $keyword, int $page = 1, int $size = 10)
    {
        if (!$sid || !$keyword || !$page || !$size) {
            return $this->returnData(204, '参数错误');
        }

        $return = [
            'list'  => [],
            'total' => 0
        ];

        list($searchType, $ordernums) = $this->_transOrdernums($sid, $keyword);
        if (!$ordernums) {
            return $this->returnData(200, '', $return);
        }

        //订单号查询的范围：还未绑脸，已经绑脸，和部分绑脸的订单
        //手机号，身份证，联系人姓名查询范围：已经绑脸，和部分绑脸的订单
        $faceModel = new FaceCompare();
        //查询已经绑脸的订单
        $orderList = $faceModel->getOrderListWithOrdernum($ordernums, 'ordernum');
        if ($orderList) {
            $numMap = [];
            foreach ($orderList as $item) {
                if (isset($numMap[$item['ordernum']])) {
                    $numMap[$item['ordernum']]++;
                } else {
                    $numMap[$item['ordernum']] = 1;
                }
            }
            $ordernums = array_column($orderList, 'ordernum');
        } else {
            //查询未绑脸的订单
            if ($searchType == 'ordernum') {
                $ordernums = $this->_searchWithTicketAttr($keyword);
                $numMap = [$keyword => 0];
            }
            if (!$ordernums) {
                return $this->returnData(200, '', $return);
            }
        }

        //获取订单信息
        $orderModel = new OrderTools('slave');
        $orderInfos = $orderModel->getOrderInfo(array_keys($numMap), 'ordernum,tnum,ordertel,status,apply_did,ordertime', false, false, false, false, 'id desc');

        $orderStatusBiz = new OrderStatus(); 
        $statusMap = $orderStatusBiz->getOrderStatusWithType('A');
        $statusMap = array_key($statusMap, 'code');
        foreach ($orderInfos as $k=> &$item) {
            if ($item['apply_did'] != $sid) {
                unset($orderInfos[$k]);
                continue;
            }
//            $item['tnum']        = $item['tnum'];
            $item['bind_num']    = $numMap[$item['ordernum']];
            $item['status_text'] = $statusMap[$item['status']]['name'];
        }

        unset($item);

        if (!$orderInfos) {
            return $this->returnData(200, '', $return);
        }

        $return['total'] = count($orderInfos);

        $fruits = new \ArrayIterator($orderInfos);
        foreach (new \LimitIterator($fruits, ($page - 1) * $size, $size) as $item) {
            $return['list'][] = $item;
        }

        return $this->returnData(200, '', $return);
    }



    /**
     * 获取人脸订单详情
     *
     * @param integer $sid 供应商id
     * @param string $ordernum 订单号
     * @return array
     * <AUTHOR>
     */
    public function getOrderDetail(int $sid, string $ordernum)
    {
        if (!$sid || !$ordernum) {
            return $this->returnData(204, '参数错误');
        }

        //获取订单游客信息
        $orderModel = new OrderTourist('slave');
        $toursInfo  = $orderModel->getTouristInfoByOrderId($ordernum, 'orderid,idcard,chk_code,idx,apply_did');

        if (!$toursInfo) {
            return $this->returnData(204, '未查到订单信息');
        }

        if ($toursInfo[0]['apply_did'] != $sid) {
            return $this->returnData(204, '无权查看');
        }

        //获取人脸绑定信息
        $faceModel = new FaceCompare();
        $bindInfo  = $faceModel->getOrderListWithOrdernum([$ordernum], 'idx,face_info_id,created_time,source');

        $faceInfoIds = array_column($bindInfo, 'face_info_id');
        //获取人脸图片
        $faceUrls = $faceModel->getFaceInfoByFaceIds($faceInfoIds, 'id,face_url');

        $bindInfo = array_key($bindInfo, 'idx');
        $faceUrls = array_key($faceUrls, 'id');

        $return = [];
        foreach ($toursInfo as $item) {
            $idx = $item['idx'];

            $return[] = [
                'idx'         => $item['idx'],
                'chk_code'    => $item['chk_code'],
                'idcard'      => $item['idcard'],
                'add_time'    => $bindInfo[$idx]['created_time'] ?? '',
                'source_text' => $this->bindSource[$bindInfo[$idx]['source']],
                'bind_text'   => $bindInfo[$idx] ? '已绑定' : '未绑定',
                'face_url'    => $faceUrls[$bindInfo[$idx]['face_info_id']]['face_url'] ?? ''
            ];
        }

        return $this->returnData(200, '', $return);
    }


    /**
     * 解除人脸绑定
     *
     * @param integer $sid
     * @param integer $oper
     * @param string $ordernum
     * @param integer $idx
     * @return array
     * <AUTHOR>
     */
    public function unbindFace(int $sid, int $oper, string $ordernum, int $idx, $source = 0)
    {
        if (!$sid || !$oper || !$ordernum || !$idx) {
            return $this->returnData(204, '参数错误');
        }
        $faceScenicBz = new \Business\JsonRpcApi\ScenicLocalService\Face();
        $result       = $faceScenicBz->unbindFace($sid, $oper, $ordernum, $idx, $source);

        return $result;

        //订单权限判断
        //获取订单信息
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum, 'ss.apply_did',false,'addon.ifpack');

        if (!$orderInfo) {
            return $this->returnData(204, '未找到订单');
        }

        if ($orderInfo['apply_did'] != $sid) {
            return $this->returnData(204, '无权操作');
        }

        if ($orderInfo['ifpack']){
            return $this->returnData(204, '套票订单暂不支持删除操作');
        }
        $faceModel = new FaceCompare();

        $bindInfo  = $faceModel->checkOrderFaceExist($idx, $ordernum);
        if (!$bindInfo) {
            return $this->returnData(204, '未绑定过人脸');
        }

        if ($bindInfo['is_delete'] == 2) {
            return $this->returnData(204, '人脸已使用完毕或者已过期，无法删除');
        }
        $faceInfo = $faceModel->getFaceInfoByFaceId(false,$bindInfo['faceid'],'face_url');
        $oldFaceUrl = $faceInfo['face_url'];
        $extContent = json_decode($bindInfo['ext_content'],true);
        $extContent['last_is_delete'] = $bindInfo['is_delete'];
        $extContent['last_face_url']  = $oldFaceUrl;
        $result = $faceModel->deleteCanUsedFace($ordernum, $idx,$extContent);
        $oldFaceUrl = '';
        //$result = $faceModel->deleteFaceBind();
        if ($result) {
            $faceModel->deleteCanUserFaceInfo($bindInfo['faceid']);
            Queue::delay(600, 'backend', 'Backend_Job', [
                    'action'  => 'delBaiduFaceOrder',
                    'groupid' => $bindInfo['groupid'],
                    'faceid'  => $bindInfo['faceid'],
                ]
            );

        }
        pft_log('orderface', "unbind:{$ordernum}:{$oper}:" . intval($result).'face_old_url'.$oldFaceUrl);

        if ($result) {
            return $this->returnData(200, '解绑成功');
        } else {
            return $this->returnData(204, '解绑失败');
        }
    }


    /**
     * 人脸检测
     *
     * @param string $imgUrl
     * @return array
     * <AUTHOR>
     */
    public function faceDetect(string $imgUrl, $sid = 0): array
    {
        if (!$imgUrl) {
            return $this->returnData(204, '参数错误');
        }
        $face   = new PftSystemFaceCompare('baidu');
        $result = $face->detectFace(
            base64_encode(file_get_contents($imgUrl)), [
                'image_type'=>'BASE64',
                'liveness_control'=>'NORMAL',
            ],
            $sid
        );
        if ($result['code'] == 200) {
            return $this->returnData(200, '检测通过');
        }
        return $this->returnData($result['code'], $result['msg']);
    }

    /**
     * 补脸
     *
     * @param integer $sid
     * @param integer $oper
     * @param string $ordernum
     * @param integer $idx
     * @param string $imgUrl
     * @return array
     * <AUTHOR>
     */
    public function bindFace(int $sid, int $oper, string $ordernum, int $idx, string $imgUrl)
    {
        if (!$sid || !$oper || !$ordernum || !$idx || !$imgUrl) {
            return $this->returnData(204, '参数错误');
        }

        $detectRes = $this->faceDetect($imgUrl, $sid);
        if ($detectRes['code'] != 200) {
            return $detectRes;
        }

        //获取订单信息
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum, 'apply_did');

        if (!$orderInfo) {
            return $this->returnData(204, '未找到订单');
        }

        //订单权限判断
        if ($orderInfo['apply_did'] != $sid) {
            return $this->returnData(204, '无权操作');
        }

        //获取人脸绑定信息
        $faceModel = new FaceCompare();
        $faceBiz   = new Face();

        $bindInfo  = $faceModel->checkOrderFaceExist($idx, $ordernum);
        if ($bindInfo) {
            if ($bindInfo['is_delete'] == 2){  //这种是操作删除后
                if ($bindInfo['end_date'] < date('Ymd')){
                    return $this->returnData(204, '已超过人脸有效期不能绑定');
                }
                $result = $faceBiz->faceUpdate($ordernum, $idx, $imgUrl,0,'',true, 4, $oper);
            }else{
                //更新
                $result = $faceBiz->faceUpdate($ordernum, $idx, $imgUrl, 0, '', false, 4, $oper);
            }
        } else {
            //创建
            $result = $faceBiz->handleFace($ordernum, $imgUrl, $idx, '', '', '匿名游客', 'F', 4,
                0, false, [], [], $oper);
        }

        pft_log('orderface', "bind:{$ordernum}:{$oper}:" . json_encode($result));

        if ($result['code'] == 200) {
            return $this->returnData(200, '绑定成功');
        } else {
            return $this->returnData(204, $result['msg']);
        }
    }


    private function _transOrdernums($sid, $keyword)
    {
        if (ismobile($keyword)) {
            //手机号查单
            $type = 'mobile';
            $ordernums = $this->_getOrdernumsWithMobile($keyword);
        } elseif (idcard_checksum18($keyword)) {
            //身份证查单
            $type = 'idcard';
            $ordernums = $this->_getOrdernumsWithIdcard($keyword);
        } elseif (is_numeric($keyword)) {
            $type = 'ordernum';
            $ordernums = [$keyword];
        } else {
            $type = 'name';
            //姓名查单
            $ordernums = $this->_getOrdernumsWithName($keyword);
        }

        return [$type, $ordernums];
    }


    private function _getOrdernumsWithMobile(string $mobile)
    {
        $orderModel = new SubOrderQuery();
        $ordernums = $orderModel->getOrderListByMobile($mobile);

        return $ordernums ?: [];
    }


    private function _getOrdernumsWithIdcard(string $idcard)
    {
        $orderModel = new SubOrderQuery();
        $ordernums = $orderModel->getOrderListByIdcard($idcard);

        return $ordernums ?: [];
    }


    private function _getOrdernumsWithName(string $name)
    {
        $orderModel = new SubOrderQuery();
        $ordernums = $orderModel->getOrderListByName($name);

        return $ordernums ?: [];
    }


    private function _searchWithTicketAttr(string $ordernum)
    {
        //获取订单信息
        $orderModel = new OrderTools('slave');
        $orderInfo  = $orderModel->getOrderInfo($ordernum, 'tid');
        if (!$orderInfo) {
            return [];
        }
        $tid = $orderInfo['tid'];

        $ticketApi  = new CommodityCenterTicket();
        $ticketRes = $ticketApi->queryTicketAttrsById($tid);
        if ($ticketRes['code'] != 200) {
            return [];
        }

        $ticketInfo = $ticketRes['data'];
        $ticketInfo = array_key($ticketInfo, 'key');

        if (isset($ticketInfo['face_open']) && $ticketInfo['face_open']['val'] == 1){
            return [$ordernum];
        }

        return [];
    }
    
}