<?php
/**
 * 终端管理人脸业务
 * User: Administrator
 * Date: 2020/11/11
 * Time: 11:41
 */

namespace Business\TerminalManage;

use Business\Base;
use Model\Terminal\FaceCompare;
use Library\Cache\Cache;

class TerminalFace extends Base
{
    /**
     * 修改扩展信息
     * <AUTHOR>
     *
     * @param  string  $id  主键id
     * @param  array  $extData  扩展参数
     *
     * @date 2020-11-11
     */
    public function editGroupExtInfo($id, $extData = [])
    {
        if (!$id || !$extData) {
            return $this->returnData(204, '参数错误');
        }
        $faceCompareMdl = new FaceCompare();
        $faceData       = $faceCompareMdl->getFaceGroupInfoById($id, 'ext_info, groupid');
        if (empty($faceData)) {
            return $this->returnData(204, '未找到信息');
        }
        $jsonData = $faceData['ext_info'] ? json_decode($faceData['ext_info'], true) : [];
        $setData  = json_encode(array_merge($jsonData, $extData),JSON_UNESCAPED_UNICODE);
        $res      = $faceCompareMdl->updateFaceGroupExtInfoById($id, $setData);
        if (!$res) {
            return $this->returnData(204, '保存失败');
        }
        $redis = Cache::getInstance('redis');
        $redis->rm("land:faceplatform:" . $faceData['groupid']);

        return $this->returnData(200, '保存成功');
    }
}