<?php
namespace Business\TerminalManage;

use Business\Base;
use Business\Member\MemberRelation;
use Model\TerminalManage\TerminalAccountR;
use Model\TerminalManage\TicketConfig;
use Business\JavaApi\Member\Query\MemberQuery;

/**
 * 供应商设备管理
 *
 * <AUTHOR>
 */
class SupplierTicket extends Base
{

    /**
     * 获取门票列表
     * User: lanwanhui
     * Date: 2022/5/9
     *
     * @param int    $sid            上级id
     * @param int    $mid            会员id
     * @param int    $dtype          账号类型
     * @param string $account        账号
     * @param int    $page           页码
     * @param int    $pageSize       页面大小
     * @param string $terminalNo     终端号
     * @param int    $ticketConfigId 模板id
     *
     * @return array
     */
    public function getTicketList($sid, $mid, $dtype, $account, $page, $pageSize, $terminalNo, $ticketConfigId)
    {

        $returnData = [
            'list'      => [],
            'page_size' => $pageSize,
            'total'     => 0,
            'page'      => $page,
        ];

        $accountList = [$account];

        if ($dtype != 6) {

            $memberRelationBiz  = new MemberRelation();
            $relationList       = $memberRelationBiz->getMemberRelationListToJava([$sid], [], [], [1, 2], [],'son_id');
            $sonIdList          = array_column($relationList, 'son_id');
            $sonIdList          = array_values(array_unique($sonIdList));

            $sonIdList = array_chunk($sonIdList, 2000);
            foreach ($sonIdList as $sonIdTmpList) {
                $memberRes          = (new MemberQuery())->batchMemberInfoByIds($sonIdTmpList);
                $sonInfoList        = (isset($memberRes['data']) && is_array($memberRes['data'])) ? $memberRes['data'] : [];
                foreach ($sonInfoList as $sonInfo) {
                    $accountList[] = $sonInfo['account'];
                }
            }
        }

        if (empty($accountList)) {
            return $this->returnDataV2(200, $returnData);
        }

        // 查找当前登录用户的终端号列表
        $terminalAccountModel = new TerminalAccountR();
        $terminals            = $terminalAccountModel->getTerminalByUserName($terminalNo, $accountList);
        $terminalNoArr        = array_column($terminals, 'TerminalNo');

        if (empty($terminalNoArr)) {
            return $this->returnDataV2(200, $returnData);
        }

        $ticketConfigModel = new TicketConfig();
        $res               = $ticketConfigModel->getTicketConfigs('', 0, $terminalNoArr, $ticketConfigId, $page, $pageSize);

        foreach ($res as $k => $v) {
            $res[$k]['TicketJsonConfig'] = json_decode($res[$k]['TicketJsonConfig'], true);
        }

        $returnData['list']      = $res;
        $returnData['total']     = $ticketConfigModel->getTicketConfigsCount('', 0, $terminalNoArr, $ticketConfigId);

        return $this->returnDataV2(200, $returnData);
    }


    /**
     * 编辑门票
     * User: lanwanhui
     * Date: 2022/5/9
     *
     * @param int   $ticketConfigId   门票模板id
     * @param array $ticketConfigInfo 门票模板信息
     *
     * @return array
     */
    public function editTicketConfig($ticketConfigId, $ticketConfigInfo)
    {
        $ticketConfigModel = new TicketConfig();
        $res = $ticketConfigModel->editTicketConfig($ticketConfigId, $ticketConfigInfo);
        if ($res['code'] == 200) {
            return $this->returnDataV2(200, [], '设置成功');
        } else {
            return $this->returnDataV2(400, [], '设置失败');
        }
    }


    /**
     * 删除门票模板
     * User: lanwanhui
     * Date: 2022/5/9
     *
     * @param int $ticketConfigId 门票模板id
     *
     * @return array
     */
    public function delTicketConfig($ticketConfigId)
    {
        $ticketConfigModel = new TicketConfig();
        $res = $ticketConfigModel->delTicketConfig($ticketConfigId);
        if ($res['code'] == 200) {
            return $this->returnDataV2(200, [], '删除成功');
        } else {
            return $this->returnDataV2(400, [], '删除失败');
        }
    }
}