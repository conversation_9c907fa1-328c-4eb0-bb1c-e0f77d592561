<?php
namespace Business\TerminalManage;

use Business\Base;
use Business\Product\Product;
use Business\CommodityCenter\Land;
use Business\CommodityCenter\Ticket;
use Model\Product\Terminal;

/**
 * 设备关系获取产品相关配置
 * <AUTHOR>
 */
class ProductConfig extends Base
{

    /**
     * 根据供应商id 获取景区列表
     * User: lanwanhui
     * Date: 2022/5/16
     *
     * @param int    $applyDid  供应商id
     * @param string $keyWord   搜索关键字
     * @param int    $lid       景区id
     * @param int    $pageSize 分页大小
     * @param string $pType    产品类型
     *
     * @return array
     */
    public function getLandListByApplyDid($applyDid, $keyWord, $lid, $pType, $pageSize)
    {
        if (empty($applyDid)) {
            return $this->returnDataV2(204, [], '参数错误');
        }

        if (empty($pType)) {
            $pTypeArr = [];
        } else {
            $pTypeArr = explode(',', $pType);
        }

        $productBiz = new Product();
        $landList = $productBiz->getSaleProductByCondition($applyDid, 0, $lid, 0, $keyWord,  $pTypeArr,  0, [],  0, 1,$pageSize);

        $lidArr = isset($landList['list']) ? array_column($landList['list'], 'lid') : [];

        $javaAPi  = new Land();
        $landList = $javaAPi->queryLandMultiQueryById($lidArr);

        $res = $landList ?: [];

        return $this->returnDataV2(200, $res);
    }

    /**
     * 根据景区id获取门票
     * User: lanwanhui
     * Date: 2022/5/16
     *
     * @param int $lid 景区id
     *
     * @return array
     */
    public function getTicketListByLid($lid)
    {
        if (empty($lid)) {
            return $this->returnDataV2(204, [], '参数错误');
        }

        $ticketBiz  = new Ticket();

        $ticketList = $ticketBiz->queryTicketListByLandId($lid, 'id,title', false, 1);
        $ticketList = $ticketList ?: [];

        return $this->returnDataV2(200, $ticketList);
    }


    /**
     * 获取终端列表
     * User: lanwanhui
     * Date: 2022/5/16
     *
     * @return
     */
    public function getAllTerminal($applyDid, $terminal)
    {

        if (empty($terminal) || empty($applyDid)) {
            return $this->returnDataV2(204, [], '参数错误');
        }

        $terminalModel = new Terminal();

        $list          = $terminalModel->getSubList($terminal, false, $applyDid);

        if (empty($list)) {
            $list = [
                [
                    'preTerminal' => (string)$terminal,
                    'name'        => '主终端',
                ]
            ];
        }else{  //合并终端的nowterminal变成合并终端后的所以主终端搜不到
            if (!in_array($terminal,array_column($list,'preTerminal'))){
                $list[] =   [
                    'preTerminal' => (string)$terminal,
                    'name'        => '主终端',
                ];
            }
        }

        return $this->returnDataV2(200, $list, '获取成功');

    }

}