<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2019/6/21
 * Time: 18:18
 */
namespace Business\TerminalManage;
use Business\Base;
use Library\Cache\Cache;
use Library\Tools\YarClient;
use Library\Util\DebugUtil;
use Model\TerminalManage\DeviceSignLog;
use Model\DataCollection\DataCollection;

class TerminalManage extends Base {
    private $yarClient;

    public function __construct()
    {
        $this->yarClient = new YarClient('device');
    }

    /**
     * 设置签到码服务记录日志
     *
     * @date   2020-11-02
     * <AUTHOR>
     *
     * @param  int  id  主键id
     *
     * @return array
     */
    public function getDeviceSignInfoLogService($id){
        if (!$id){
            return $this->returnData(204,'','参数错误');
        }
        $deviceSignLogMdl = new DeviceSignLog();
        $resInfo = $deviceSignLogMdl->getDeviceSignInfoById($id);
        if (!$resInfo){
            return $this->returnData(204,'','获取失败');
        }
        $deviceInfo = json_decode($resInfo['device_sign_info'],true);
        return $this->returnData(200,'获取成功',$deviceInfo);
    }
    /**
     * 获取相关设备图片
     *
     * @date   2021-02-01
     * <AUTHOR>
     *
     * @param  string  $deviceKey  特征码
     * @param  int  $type  图片类型
     *
     * @return array
     */
    public function getDevicePictureService($deviceKey,$type){
        if (!$deviceKey || !$type){
            return $this->returnData(204,'','参数错误');
        }
        $yarRes  = $this->yarClient->call('Device/Device/getDeviceLunBoPicture', [$deviceKey,$type]);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['msg'] ?? '');
        }

        $response = $yarRes['res'];
        return $response;
    }

    /**
     * 获取闸机出入园数据
     * User: lanwanhui
     * Date: 2022/5/18
     *
     * @param string $deviceKey 特征码
     * $dateT 日期 2024-07-16
     *
     * @return array
     */
    public function gateDeviceHomeData($deviceKey,$dateT, $relevance=true)
    {
        $paramArr = [
            'device_key' => $deviceKey,
            'date' => $dateT,
        ];
        $yarRes  = $this->yarClient->call('Device/Device/gateDeviceHomeData', [$deviceKey,$paramArr]);

        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['msg'] ?? '');
        }

        if ($yarRes['res']['code'] != 200) {
            return $this->returnData(204, $yarRes['res']['msg'] ?? '');
        }

        $data = $yarRes['res']['data'] ?? [];

        $deviceInfoList = $data['deviceList'] ?? [];    // 关联的设备列表

        $deviceInfoList = array_column($deviceInfoList, null, 'device_key');
        $deviceKeyList  = array_column($deviceInfoList,'device_key');

        $dataCollection = new DataCollection();
        $ymd = date('Ymd', strtotime($dateT));
        $dayAccountList = $dataCollection->getDeviceDataByDateAndDeviceKeyList($ymd, $deviceKeyList);
        \Library\Util\DebugUtil::info([
            'title' => '需要请求的设备入园数据',
            '$deviceKeyList' => $deviceKeyList,
            '$ymd' => $ymd,
            '$dayAccountList' => $dayAccountList,
        ]);

        //$data['totalStaffInNum'] = 0;
        foreach ($dayAccountList as $value) {
            $deviceKeyTmp = $value['device_key'];
            if ($value['roll'] == 0) {
                $deviceInfoList[$deviceKeyTmp]['in_num'] = $value['num'];
                $data['totalInNum'] = $data['totalInNum'] + $value['num'];
            } elseif ($value['roll'] == 1) {
                $deviceInfoList[$deviceKeyTmp]['out_num'] = $value['num'];
                $data['totalOutNum'] = $data['totalOutNum'] + $value['num'];
            } elseif ($value['roll'] == 2) {
                $deviceInfoList[$deviceKeyTmp]['staff_in_num'] = $value['num'];
                $data['totalStaffInNum'] = $data['totalStaffInNum'] + $value['num'];
            } elseif ($value['roll'] == 4) {
                $deviceInfoList[$deviceKeyTmp]['pre_check_num'] = $value['num'];
                $data['totalPreCheckNum'] = $data['totalPreCheckNum'] + $value['num'];
            }
        }

        foreach ($deviceInfoList as $k => $value) {
            $inNum      = $value['in_num'] ?? 0;
            $staffInNum = $value['staff_in_num'] ?? 0;
            $inNum      = $inNum - $staffInNum;
            $inNum      = max($inNum, 0);
            $deviceInfoList[$k]['in_num'] = strval($inNum);
        }

        //$data['totalInNum'] = $data['totalInNum'] - $data['totalStaffInNum'];
        $data['totalInNum'] = max($data['totalInNum'], 0);

        //本机入园
        $data['inNum']      = $deviceInfoList[$deviceKey]['in_num'] ?? 0;
        //本机出园
        $data['outNum']     = $deviceInfoList[$deviceKey]['out_num'] ?? 0;
        //本机员工卡放行
        $data['staffInNum'] = $deviceInfoList[$deviceKey]['staff_in_num'] ?? 0;
        //本机预检票数
        $data['preCheckNum'] = $deviceInfoList[$deviceKey]['pre_check_num'] ?? 0;
        if($relevance) {
            $data['deviceList'] = array_values($deviceInfoList);
        }

        return $this->returnData(200, 'success', $data);
    }

    /**
     * 获取自助机小程序支付配置
     *
     * @date   2022-12-12
     * <AUTHOR>
     *
     * @param  string  $deviceKey  特征码
     *
     * @return mixed|string
     */
    public function getSelfDevicePayConf($deviceKey)
    {
        if (!$deviceKey){
            return '';
        }
        $cacheData = self::cacheHandler($deviceKey);
        if ($cacheData) {
            return ['uri'=>$cacheData['smallMiniPayUri'], 'pay_type'=>$cacheData['pay_type']];
        }

        try {
            $yarRes  = $this->yarClient->call('Device/Device/getServiceSetting', [$deviceKey]);
            $serviceSettingConfig = $yarRes['res']['data'] ?? [];
//            $smallMiniPayUri = $serviceSettingConfig['smallMiniPayUri'] ?: '';
            self::cacheHandler($deviceKey, 'SET', $serviceSettingConfig);
//            return $smallMiniPayUri;
            return ['uri'=>$serviceSettingConfig['smallMiniPayUri'], 'pay_type'=>$serviceSettingConfig['pay_type']];

        } catch (\Throwable $e) {
            return '';
        }

    }
    public static function cacheHandler($deviceKey, $action='GET', $data=null) {
        $key   = "device:setting:{$deviceKey}";
        /**
         * @var $cache \Redis
         */
        $cache = Cache::getInstance('redis');
        if ($action == 'GET') {
            $res   = $cache->hGetAll($key);
            if (!empty($res)) {
                return $res;
            }
            return false;
        }
        if ($action =='DEL') {
            $cache->del($key);
        }
        if ($action == 'SET') {
            $cache->hMSet($key, $data);
            $cache->expire($key, 3600);
        }

    }
}