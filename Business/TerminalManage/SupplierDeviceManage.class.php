<?php

namespace Business\TerminalManage;

use Business\Base;
use Business\CommodityCenter\Ticket;
use Business\Statistics\EntryExitStatService;
use Library\Constants\BizCode;
use Library\Constants\Device;
use Library\Tools\YarClient;
use Library\Util\DebugUtil;
use Library\Util\EnvUtil;
use Model\TerminalManage\VersionManage;
use function GuzzleHttp\Psr7\str;

/**
 * 供应商设备管理(newzd给管理员（内部）用,以示区分)
 *
 * <AUTHOR>
 */
class SupplierDeviceManage extends Base
{

    private $yarClient;

    public function __construct()
    {
        $this->yarClient = new YarClient('device');
    }


    /**
     * 获取数据设备概览
     *
     * @param integer $sid
     * @return array
     * <AUTHOR>
     */
    public function getDeviceOverview(int $sid): array
    {
        if (!$sid) {
            return $this->returnData(204, '参数错误');
        }

        $yarRes  = $this->yarClient->call('Device/SupplierDevice/getDeviceOverview', [$sid]);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['msg'] ?? '');
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        $overView = $response['data'];
        $overView = array_key($overView, 'device_type');

        foreach (Device::DEVICE_TYPE_ARR as $type => $name) {
            if (!isset($overView[$type])) {
                $overView[$type] = [
                    'device_type' => $type,
                    'cnt' => 0,
                ];
            }
        }
        return $this->returnData(200, '', $overView);
    }


    /**
     * 获取设备型号列表
     *
     * @param integer $sid
     * @param integer $deviceType
     * @return array
     * <AUTHOR>
     */
    public function getDeviceModelList(int $sid, int $deviceType)
    {
        if (!$sid || !$deviceType) {
            return $this->returnData(204, '参数错误');
        }

        $yarRes  = $this->yarClient->call('Device/Device/getDeviceSystemVersionList', [$deviceType]);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['msg'] ?? '');
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        $list = array_values($response['data']);

        return $this->returnData(200, '', $list);
    }


    /**
     * 获取版本号列表
     *
     * @param integer $sid
     * @param integer $deviceType
     * @param string $deviceModel
     * <AUTHOR>
     */
    public function getSoftwareVersions(int $sid, int $deviceType, string $deviceModel)
    {
        if (!$sid || !$deviceType || !$deviceModel) {
            return $this->returnData(204, '参数错误');
        }

        $yarRes  = $this->yarClient->call('Device/Device/getDeviceEditionBySystemVersion', [$deviceType, $deviceModel]);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['error_msg'] ?? '');
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        $list = array_column($response['data']['list'], 'version_no');

        return $this->returnData(200, '', $list);

    }

    /**
     * 获取设备列表
     *
     * @param integer   $sid             供应商id
     * @param string    $keyword         搜索关键字
     * @param string    $keywordType         搜索关键字类型
     * @param integer   $deviceType      设备类型 1:手持机，2：自助机，3：闸机，4：win自助机，5：web云票务
     * @param integer   $productId       设备型号,取自产品类型的型号
     * @param string    $softwareVersion 软件版本
     * @param integer   $onlineState     1在线2离线
     * @param integer   $nodeId          设备节点id
     * @param integer   $classify    模块包类型
     * @param integer   $authType          授权状态  1:已授权 2：未授权
     * @param integer   $page            当前页码
     * @param integer   $size            每页条数
     * @return array
     * <AUTHOR>
     */
    public function getDeviceList(int $sid, string $keyword = '', string $keywordType = '',  int $deviceType = -1, int $productId = -1,
        string $softwareVersion = '', int $onlineState = 1, int $nodeId = 0, $classify = 0, $authType = 0, int $page = 1, int $size = 10)
    {
        if (!$sid) {
            return $this->returnData(204, '参数错误');
        }

        $yarRes  = $this->yarClient->call('Device/SupplierDevice/getDeviceList', func_get_args());
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['msg']);
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        $total = $response['data']['total'];
        $list  = $response['data']['list'] ?: [];

        $reponseList = [];
        if ($list) {
            $deviceKeys = array_column($list, 'device_key');
            $yarRes  = $this->yarClient->call('Device/Device/getDeviceUpgradeRecord', [$deviceKeys]);
            if ($yarRes['code'] != 200) {
                return $this->returnData(204, $yarRes['error_msg'] ?? '');
            }

            $response = $yarRes['res'];
            if ($response['code'] != 200) {
                return $this->returnData(204, $response['msg']);
            }

            $updateRecords = $response['data'];

            foreach ($list as $item) {
                $temp = [
                    'device_key'         => $item['device_key'],
                    'device_name'        => $item['device_name'],
                    'device_type'        => $item['device_type'],
                    'sequence_code'      => $item['sequence_code'] ?? '',
                    'device_model'       => $item['system_version_no'],
                    'pro_model_no'       => $item['pro_model_no'],
                    'product_id'         => $item['product_id'],
                    'software_version'   => $item['version_no'],
                    'node_info'          => $item['node_info'],
                    'node_id'            => $item['node_id'],
                    'state'              => $item['status'],
                    'last_update_status' => $updateRecords[$item['device_key']]['status'],
                    'last_update_res'    => $updateRecords[$item['device_key']]['msg'],
                    'create_time'        => $item['create_time'],
                ];

                $reponseList[] = $temp;
            }
        }

        $return = [
            'list'  => $reponseList,
            'total' => $total
        ];

        return $this->returnData(200, '', $return);
    }



    /**
     * 获取设备详情
     *
     * @param integer $sid
     * @param string $deviceKey
     * @return array
     * <AUTHOR>
     */
    public function getDeviceDetail(int $sid, string $deviceKey)
    {
        if (!$sid || !$deviceKey) {
            return $this->returnData(204, '参数错误');
        }

        $yarRes  = $this->yarClient->call('Device/Device/getGatheringDeviceInfo', [$deviceKey, 0, 'web']);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['error_msg'] ?? '');
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        $data = $response['data'];

        $deviceInfo = $data['device_info'];
        $deviceAddrInfo = $data['device_address_info'];

        $loginInfo = [
            'device_name' => $deviceInfo['device_name'],
            'run_state'   => $deviceInfo['status'],
            'device_key'  => $deviceInfo['device_key'],
            'software_version' => 'v' . $deviceInfo['version_no'],
            'login_account'    => $deviceInfo['online_account'],
            'online_dtype'     => $deviceInfo['online_dtype'],
            'ternimal_no'      => $deviceInfo['terminal_no'],
            'login_site'       => $deviceInfo['site_name']
        ];

        $networkInfo = [
            'net_type' => 'WIFI',
            'mac_addr' => $deviceAddrInfo['mac_address'],
            'ip_addr'  => $deviceAddrInfo['ip_address'],
        ];

        $hardware = [
            'device_name'   => $deviceInfo['device_name'],
            'sequence_code' => $deviceInfo['sequence_code'],
            'device_key'    => $deviceInfo['device_key'],
            'device_type_text'  => $deviceInfo['device_type_text'],
            'device_type'   => $deviceInfo['device_type'],
            'device_model'  => $data['device_product_info']['pro_model_no'],
            'sn'            => $deviceInfo['sn'],
            'source'        => $data['device_user_info']['origin_type_text'],
            'bind_info'     => "{$data['device_user_info']['supplier_name']}({$data['device_user_info']['account']})",
            'address'       => $deviceAddrInfo['address'],
            'active_time'   => $deviceInfo['create_time'],
            'product_id'    => $deviceInfo['product_id'],
            'pro_model_no'  => $data['device_product_info']['pro_model_no'],
            'version_has_new'=> $deviceInfo['has_new'],
            'system_setting' => $data['device_system_setting_info'],
            'client_setting' => $data['device_client_setting_info'],
        ];

        $remark = $deviceInfo['remark'];

        $return = [
            'login_info'        => $loginInfo,
            'network_info'      => $networkInfo,
            'remark'            => $remark,
            'hardware'          => $hardware,
            'license_bind_info' => $data['license_bind_info'],
        ];

        $otherInfo = $this->_otherDetailInfo($data);
        $return = array_merge($return, $otherInfo);

        return $this->returnData(200, '', $return);

    }


    private function _otherDetailInfo($data)
    {
        $deviceType = $data['device_info']['device_type'];

        $return = [];
        switch ($deviceType) {
            //手持机
            case 1:
                $return = [
                    'package'  => $this->_injectPackageInfo($data),
                ];
                break;
            //自助机
            case 2:
                $return = [
                    'package'  => $this->_injectPackageInfo($data),
                ];
                break;
            //闸机
            case 3:
                $return = [
                    'business' => $this->_injectBusinessInfo($data),
                ];
                break;
            //win自助机
            case 4:
                $return = [
                    'local_setting' => $this->_injectSettingInfo($data),
                ];
                break;
            case 5:
            //web云票务
                $return = [
                    'local_setting' => $this->_injectSettingInfo($data),
                ];
                break;
            default:
                break;
        }

        return $return;
    }


    private function _injectBusinessInfo($data)
    {
        $deviceInfo = $data['last_sign_info'];
        return $deviceInfo['signInfo'];
    }


    private function _injectPackageInfo($data)
    {
        $deviceInfo = $data['device_info'];
        $authInfo   = $data['device_auth_info'];
        return [
            'package_name' => $deviceInfo['package_name'],
            'auth_status'  => $authInfo['auth_status_text'],
            'auth_start_time' => $authInfo['auth_start_time'],
            'auth_end_time' => $authInfo['auth_end_time']
        ];
    }

    private function _injectSettingInfo($data)
    {
        return $data['device_setting_info'];
    }

    
    /**
     * 更新设备名称
     *
     * @param integer $sid
     * @param string $deviceKey
     * @param string $deviceName
     * @return array
     * <AUTHOR>
     */
    public function updateDeviceName(int $sid, string $deviceKey, string $deviceName)
    {
        if (!$sid || !$deviceKey || !$deviceName) {
            return $this->returnData(204, '参数错误');
        }

        if (mb_strlen($deviceName) > 30) {
            return $this->returnData(204, '设备名称不能超过30个字符');
        }

        $yarRes  = $this->yarClient->call('Device/Device/updateDeviceInfo', [$sid, $deviceKey, 'device_name', $deviceName]);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['error_msg']);
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        return $this->returnData(200, '更新成功');
    }


    /**
     * 更新设备备注
     *
     * @param integer $sid
     * @param string $deviceKey
     * @param string $remark
     * @return array
     * <AUTHOR>
     */
    public function updateDeviceRemark(int $sid, string $deviceKey, string $remark)
    {
        if (!$sid || !$deviceKey || !$remark) {
            return $this->returnData(204, '参数错误');
        }

        $yarRes  = $this->yarClient->call('Device/Device/updateDeviceInfo', [$sid, $deviceKey, 'remark', $remark]);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['error_msg']);
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        return $this->returnData(200, '更新成功');
    }


    /**
     * 获取版本列表
     *
     * @param integer $deviceType
     * @param integer $productId
     * @return array
     * <AUTHOR>
     */
    public function getUpgradeInfo(int $deviceType, int $productId)
    {
        if (!$deviceType) {
            return $this->returnData(204, '参数错误');
        }
        
        switch ($deviceType) {
            case 1:
                // 手持机
                $category = [0, 7, 21, 25];
                break;
            case 2:
                // 自助机
                $category = [14];
                break;
            case 3:
                //闸机
                $category = [8, 23];
                break;
            default:
                $category = [];
        }

        $versionManage = new VersionManage();
        $versionList = $versionManage->getVersionManages(1, 10, $category, '',
            'VersionManagerId,VersionManagerNo,TrDateTime,UpdateDescription',
            'VersionManagerId desc', $deviceType, $productId);

        return $this->returnData(200, '', $versionList);
    }


    /**
     * 设备推送升级
     *
     * @param integer $sid
     * @param string $deviceKey
     * @param integer $versionId
     * @return array
     * <AUTHOR>
     */
    public function pushUpgradeMsg(int $sid, string $deviceKey, int $versionId)
    {
        if (!$sid || !$deviceKey || !$versionId) {
            return $this->returnData(204, '参数错误');
        }

        $getRes = $this->getDeviceList($sid, $deviceKey, 'device_key');
        if ($getRes['code'] != 200) {
            return $getRes;
        }

        if (!$getRes['data']['total']) {
            return $this->returnData(204, '未找到设备，请检查设备是否在线');
        }

        $versionManage = new VersionManage();
        $versionInfo = $versionManage->getVerionInfoByVersionId($versionId);
        if (!$versionInfo) {
            return $this->returnData(204, '未找到要推送的版本信息');
        }

        $paramArr = array(
            'app_url'            => $versionInfo['AppUrl'],
            'device_key'         => $deviceKey,
            'op_id'              => $sid,
            'upgrade_type'       => 1,
            'version_code'       => $versionInfo['versionCode'],
            'version_manager_no' => $versionInfo['VersionManagerNo'],
            'update_description' => $versionInfo['UpdateDescription'],
        );

        $yarRes = $this->yarClient->call('Device/Device/upgradeDevice', $paramArr);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, '设备断开连接，请检查网络或重启设备，再重试');
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        return $this->returnData(200, '推送成功');
    }


    /**
     * 修改设备密码
     * User: lanwanhui
     * Date: 2021/10/21
     *
     * @param string $deviceKey  特征码
     * @param string $passwd     密码
     * @param int    $opId       操作员id
     *
     * @return array
     */
    public function saveDiffDevicePasswd($deviceKey, $passwd, $opId): array
    {
        $paramArr = [
            'device_key'       => $deviceKey,
            'passwd'           => $passwd,
            'op_id'            => $opId,
            'ip'               => ip2long(get_client_ip()),
        ];

        $yarRes  = $this->yarClient->call('Device/Device/saveDiffDevicePasswd', $paramArr);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['error_msg']);
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        return $this->returnData(200, '修改成功');
    }
    public function saveBranchTerminalPasswd($deviceKey, $passwd, $opId): array
    {
        $paramArr = [
            'device_key'       => $deviceKey,
            'passwd'           => $passwd,
            'op_id'            => $opId,
            'ip'               => ip2long(get_client_ip()),
        ];

        $yarRes  = $this->yarClient->call('Device/Device/saveBranchTerminalPasswd', $paramArr);
        if ($yarRes['code'] != 200) {
            return $this->returnData(204, $yarRes['error_msg']);
        }

        $response = $yarRes['res'];
        if ($response['code'] != 200) {
            return $this->returnData(204, $response['msg']);
        }

        return $this->returnData(200, '修改成功');
    }

    /**
     * 获取验证数据
     *
     * @param int $sid 商家id
     * @param string $nodeIds 节点id
     * @param string $beginDate
     * @param string $endDate
     * @param string $lids
     * @param string $tids
     * @param int $excel 是否导出Excel 0：否 1：是
     *
     * @date 2022/03/02
     * @auther yangjianhui
     * @return array
     */
    public function getTicketVerifyData($sid, $nodeIds, $beginDate, $endDate, $lids, $tids, $excel)
    {
        if (empty($beginDate) || empty($endDate) || empty($sid)) {
            return $this->returnData(203, "参数错误");
        }
        $paramArr = [
            'sid' => $sid,
        ];
        $tree     = $this->yarClient->call('Device/NodeManage/getNodeTree', $paramArr);
        if ($tree['code'] != 200) {
            return $this->returnData(204, $tree['error_msg']);
        }
        $tree     = $tree['res'];
        if ($tree['code'] != 200) {
            return $this->returnData(204, $tree['msg']);
        }
        $treeData = $tree['data'];
        if (empty($treeData)) {
            return $this->returnData(200, "获取成功");
        }
        $url      = "http://data.api.12301.group/api/gateway/odas/inout/ticket_verification?sid=" . $sid . "&start_date=" . $beginDate .
                    "&end_date=" . $endDate;
        if ($nodeIds) {
            $url .= "&node_id=" . $nodeIds;
        }

        //请求BI接口获取访客数量
        $headers     = array(
            "API-TOKEN:BB3EB0A2CDF9719C644BF4D78692087A",
        );
        $nodeIdArr = [];
        if(!empty($nodeIds)){
            $nodeIdArr = explode(',', $nodeIds);
            if(!empty($nodeIdArr)) {
                array_walk($nodeIdArr, function(&$value) {
                    $value = intval($value);
                });
            }
        }
        try {
            if(EnvUtil::isLocal() && $sid = 6970) {
                $sid = 37045537;
                $treeData = $this->fakeMockTreeNode($treeData);
            }
            $res = EntryExitStatService::getInstance()->ticketVerification($sid, $beginDate, $endDate, $nodeIdArr);
            $data = $res;
        } catch (\Throwable $e) {
            $emsg = $e->getMessage();
            return $this->returnData(BizCode::CODE_PARAM_ERROR, $emsg);
        }
        // $res         = $this->http_request($url, $headers);
        // $data        = $res['data'];
        $existTidArr = array_column($data, 'tid');
        //获取票信息
        $ticketApi  = new Ticket();
        $ticketRes  = $ticketApi->queryTicketInfoByIds($existTidArr);
        $ticketInfo = [];   // tid => ticketInfo
        foreach ($ticketRes as $value) {
            $ticketInfo[$value['ticket']['id']] = array_merge($value['ticket']);
        }
        foreach ($data as $key => $value) { // 遍历处理添加门票名称
            $data[$key]['ticket_name'] = $ticketInfo[$value['tid']]['title'];
        }
        //导出走另外的逻辑
        if ($excel) {
            $returnData = $this->_handleVerifyExcelData($data);

            return $this->returnData(200, "获取成功", $returnData);
        }
        $result = $this->_handleData($treeData, $data);
        $sumNum = array_sum(array_column($data, 'cnt'));
        $returnData = [
            'list'    => $result,
            'sum_num' => $sumNum,
        ];

        return $this->returnData(200, "获取成功", $returnData);
    }



    private function fakeMockTreeNode($treeList)
    {
        if (empty($treeList)) {
            return [];
        }
        foreach ($treeList as $key => $value) {
            //是否末级节点
            if ($value['is_last_node'] != 1) {
                $treeList[$key]['children'] = $this->fakeMockTreeNode($value['children']);
            }
            // 处理最末级节点的统计数据
            if($value['id'] == 56) {
                $value['id'] = 196;
                $value['node_name'] = $value['node_name'].'@mock';
                $treeList[$key] = $value;
            }
        }

        return $treeList;
    }

    /**
     * 获取验证数据
     *
     * @param int $sid 商家id
     * @param string $nodeIds 节点id
     * @param string $beginDate 开始时间
     * @param string $endDate 结束时间
     * @param string $lids 景区id
     * @param string $tids 票id
     * @param int $baseOnOrder 0-不基于订单，1-基于订单
     * @param int $type 统计类型 1：按小时 2：按天 3：按月
     * @param string $hour 获取的小时字段列表
     * @param int $excel 是否导出Excel 0：否 1：是
     * @date 2022/03/02
     * @auther yangjianhui
     * @return array
     */
    public function getInOutData($sid, $nodeIds, $beginDate, $endDate, $lids, $tids, $baseOnOrder, $type, $hour, $excel)
    {
        if (empty($beginDate) || empty($endDate) || empty($sid)) {
            return $this->returnData(203, "参数错误");
        }
        $paramArr = [
            'sid' => $sid,
        ];
        $tree = $this->yarClient->call('Device/NodeManage/getNodeTree', $paramArr);
        if ($tree['code'] != 200) {
            return $this->returnData(204, $tree['error_msg']);
        }
        $tree     = $tree['res'];
        if ($tree['code'] != 200) {
            return $this->returnData(204, $tree['msg']);
        }
        $treeData = $tree['data'];
        DebugUtil::debug(['tree_data' => $treeData]);
        if (empty($treeData)) {
            return $this->returnData(200, "获取成功");
        }
        $begin = date('Y-m-d', strtotime($beginDate));
        $end   = date('Y-m-d', strtotime($endDate));
        try {
            if(EnvUtil::isLocal()) {
                $sid = 37045537;
                $treeData = $this->fakeMockTreeNode($treeData);
                DebugUtil::debug(['tree_data  🟢' => $treeData]);
            }
            switch ($type) {
                case 1: // 按小时统计
                    $data = EntryExitStatService::getInstance()->dataByHour($sid, $begin, $end,$nodeIds, $baseOnOrder);
                    break;
                case 2: // 按天统计
                    $data = EntryExitStatService::getInstance()->dataByDay($sid, $begin, $end,$nodeIds, $baseOnOrder);
                    break;
                default:    // 按月统计
                    $begin = date('Ym', strtotime($beginDate));
                    $end   = date('Ym', strtotime($endDate));
                    $data = EntryExitStatService::getInstance()->dataByMonth($sid, $begin, $end,$nodeIds, $baseOnOrder);
                    break;
            }
        } catch (\Throwable $e) {
            return $this->returnData(204, $e->getMessage());
        }
//        $url .= "?sid=" . $sid . "&start_date=" . $begin .
//                "&end_date=" . $end;
//        if ($nodeIds) {
//            $url .= "&node_id=" . $nodeIds;
//        }
//        if ($baseOnOrder != 2) {  // 0 - 非基于订单核验；1 - 基于订单核验；2 - 全部
//            $url .= "&based_on_order=" . $baseOnOrder;
//        }
        //请求BI接口获取访客数量
//        $headers = array(
//            "API-TOKEN:{$token}",
//        );
//        $res     = $this->http_request($url, $headers);
//        \Library\Util\DebugUtil::deBug(['~~~~~~' => $res]);
//        $data    = $res['data'];
        //过滤小时时间段数据
        if ($type == 1 && !empty($data) && !empty($hour)) {
            $hour = explode(',', $hour);
            foreach ($data as $key => $value) {
                if (in_array($value['hour'], $hour)) {
                    continue;
                }
                unset($data[$key]);
            }
        }

//        $today = date('Y-m-d');
//        $curMonth = date('Y-m');
//
//
//        $isIncludeToday  = false;
//        if($begin <= $today && $today <= $end) {
//            $isIncludeToday = true;
//        }
//        $todayData = [];
//        $mergeData = [];
//        if($isIncludeToday) {
//            if($type == 1) {    // 按小时汇总
//                // 如果结束时间包含今天。 那么需要查询今天的，然后合并。
//            } elseif($type == 2){ // 如果包含今天。
//                // 因为目前就接口没有今天的数据，所以，今天的数据要单独请求。
//            } elseif($type == 3){
//                // 如果当月 当月的数据要从 date('Y-m-1') ~ date('Y-m-d') 数据查询。
//            }
//        }
        // 根据类型的不同，合并的方式也不同。
//        if(!empty($mergeData)) {
//            if($type == 1) {    // 小时
//
//                foreach($mergeData as $row){
//                    $new = [];
//                    $new['date'] = $row['date'];
//                    $new['node_id'] = $row['node_id'];
//                    $new['hour'] = $row['hour'];
//                    $new['in'] = $row['in'];
//                    $new['out'] = $row['out'];
//                    $data[] = $new;
//                }
//            } elseif($type == 2) {
//                // 按天的。 需要把今天的合并为一天。
//                // $mergeData 数据也只有今天的。
//                $newData = [];
//                foreach($mergeData as $row){
//                    $node_id = $row['node_id'];
//                    if(!isset($newData[$node_id])){
//                        $newData[$node_id] = [
//                            'node_id' => $node_id,
//                            'date' => $today,
//                            'in' => 0,
//                            'out' => 0,
//                        ];
//                    }
//                    // 不同时段+一起。就是当天的。
//                    $newData[$node_id]['in'] += $row['in'];
//                    $newData[$node_id]['out'] += $row['out'];
//                }
//                $newData = array_values($newData);
//                if(!empty($newData)) {
//                    $data = array_merge($data, $newData);
//                }
//            } elseif($type == 3){
//                // 如果是按月。 那么旧接口是没有当月的，那么需要构造当月的数据，因为选择的事件范围包含了当月数据。
//                // 统计维度
//                $newData = [];
//                // 先处理 1号到昨天的数据， 然后 再追加当天的查询数据。
//                foreach($mergeData as $row) {
//                    $node_id = $row['node_id'];
//                    // 维度  节点-年月
//                    $key = "$node_id@$curMonth";
//                    if(!isset($newData[$key])){
//                        $newData[$key] = [
//                            'node_id' => $node_id,
//                            'date' => $curMonth,
//                            'in' => 0,
//                            'out' => 0,
//                        ];
//                    }
//                    $newData[$key]['in'] += $row['in'];
//                    $newData[$key]['out'] += $row['out'];
//                }
//                // 还要追加今日数据到 当前月。
//                foreach($todayData as $row){
//                    $node_id = $row['node_id'];
//                    $key = "$node_id@$curMonth";
//                    if(!isset($newData[$key])){
//                        $newData[$key] = [
//                            'node_id' => $node_id,
//                            'date' => $curMonth,
//                            'in' => 0,
//                            'out' => 0,
//                        ];
//                    }
//                    $newData[$key]['in'] += $row['in'];
//                    $newData[$key]['out'] += $row['out'];
//                }
//                $newData = array_values($newData);
//                if(!empty($newData)) {
//                    $data = array_merge($data, $newData);
//                }
//            }
//        }

        //导出走另外的逻辑
        if ($excel) {
            $returnData = $this->_handleInOutExcelData($data, $type);

            return $this->returnData(200, "获取成功", $returnData);
        }
        $result  = $this->_handleData($treeData, $data);
        $sumNum  = array_sum(array_column($data, 'cnt'));   // 计算总和
        $returnData = [
            'list'    => $result,
            'sum_num' => $sumNum,
        ];

        return $this->returnData(200, "获取成功", $returnData);
    }


    /**
     * 加header头的curl
     *
     * @param String $url 请求的地址
     * @param array $header 自定义的header数据
     * @param array $data 请求参数
     *
     * @return String
     */
    public static function http_request($url, $header, $data = [])
    {
        $curl = curl_init();
        if (!empty($header)) {
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_HEADER, 0);//返回response头部信息
        }

        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_TIMEOUT, 3);          //单位 秒，也可以使用
        if (!empty($data)) {
            curl_setopt($curl, CURLOPT_HTTPGET, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
        $output = curl_exec($curl);
        curl_close($curl);

        return json_decode($output, true);
    }

    /**
     * 处理返回数据
     *
     * @param array  $treeList 节点树信息
     * @param array $data 数据
     * @date 2022/03/02
     * @auther yangjianhui
     * @return array
     */
    private function _handleData($treeList, $data)
    {
        if (empty($treeList)) {
            return [];
        }
        foreach ($treeList as $key => $value) {
            //是否末级节点
            if ($value['is_last_node'] != 1) {
                $treeList[$key]['children'] = $this->_handleData($value['children'], $data);
            }
            // 处理最末级节点的统计数据
            $sum = 0;
            foreach ($data as $k => $val) { // 三方接口返回的 node_id 表示节点ID.
                if ($val['node_id'] == $value['id']) {  // 而节点树里的id 表示节点ID
                    $treeList[$key]['pass_info'][] = $val;  // 这里追加出入园信息。
                    $sum += $val['cnt'];
                }
            }
            $treeList[$key]['sum'] = $sum;
        }

        return $treeList;
    }

    /**
     * 处理导出excel的数据
     *
     * @param array $data 出入园数据
     *
     * @return array
     * @date 2022/03/04
     * @auther yangjianhui
     */
    public function _handleVerifyExcelData($data)
    {
        if (empty($data)) {
            return [];
        }
        $nodeIdArr   = array_column($data, 'node_id');
        $excelParams = [
            'node_id_arr' => $nodeIdArr,
        ];
        //获取节点信息
        $excelData = $this->yarClient->call('Device/NodeManage/getCompeteNodeInfo', $excelParams);
        if ($excelData['code'] != 200) {
            return $this->returnData(204, $excelData['error_msg']);
        }
        $excelData = $excelData['res'];
        if ($excelData['code'] != 200) {
            return $this->returnData(204, $excelData['msg']);
        }
        $excelData = $excelData['data'];
        if (empty($excelData)) {
            return [];
        }
        $maxNodeNum = 0;
        foreach ($excelData as $value) {
            $tmpCount = count($value);
            if ($tmpCount > $maxNodeNum) {
                $maxNodeNum = $tmpCount;
            }
        }
        $list = [];
        foreach ($data as $value) {
            $tmpNodeData = $excelData[$value['node_id']];
            //节点已经被删了，导出不能展示
            if (empty($tmpNodeData)) {
                continue;
            }
            $tmpList = [];
            for ($i = 0; $i < $maxNodeNum; $i++) {
                $tmpList[] = $tmpNodeData[$i]['node_name'] ?? "--";
            }
            $tmpList[] = $value['ticket_name'];
            $tmpList[] = $value['cnt'];
            $list[]    = $tmpList;
        }

        return ['list' => $list, 'max_node' => $maxNodeNum];
    }

    public function _handleInOutExcelData($data, $type)
    {

        if (empty($data)) {
            return [];
        }
        $nodeIdArr   = array_column($data, 'node_id');
        $excelParams = [
            'node_id_arr' => $nodeIdArr,
        ];
        //获取节点信息
        $excelData = $this->yarClient->call('Device/NodeManage/getCompeteNodeInfo', $excelParams);
        if ($excelData['code'] != 200) {
            return $this->returnData(204, $excelData['error_msg']);
        }
        $excelData = $excelData['res'];
        if ($excelData['code'] != 200) {
            return $this->returnData(204, $excelData['msg']);
        }
        $excelData = $excelData['data'];
        if (empty($excelData)) {
            return [];
        }
        $maxNodeNum = 0;
        foreach ($excelData as $value) {
            $tmpCount = count($value);
            if ($tmpCount > $maxNodeNum) {
                $maxNodeNum = $tmpCount;
            }
        }
        $list = [];
        foreach ($data as $value) {
            $tmpNodeData = $excelData[$value['node_id']];
            //节点已经被删了，导出不能展示
            if (empty($tmpNodeData)) {
                continue;
            }
            $tmpList = [];
            $date = $value['date'];
            if ($type == 1) {
                $date .= " ".$value['hour'] . ":00:00";
            }
            $tmpList[] = $date;
            for ($i = 0; $i < $maxNodeNum; $i++) {
                $tmpList[] = $tmpNodeData[$i]['node_name'] ?? "--";
            }
            $tmpList[] = $value['in'];
            $tmpList[] = $value['out'];
            $list[]    = $tmpList;
        }

        return ['list' => $list, 'max_node' => $maxNodeNum];
    }
	
	public function getNodeTree($sid){
		$paramArr = [
			'sid' => $sid,
		];
		$tree     = $this->yarClient->call('Device/NodeManage/getNodeTree', $paramArr);
		if ($tree['code'] != 200) {
			return $this->returnData(204, $tree['error_msg']);
		}
		$tree     = $tree['res'];
		if ($tree['code'] != 200) {
			return $this->returnData(204, $tree['msg']);
		}
		$treeData = $tree['data'];
		if (empty($treeData)) {
			return $this->returnData(200, "获取成功");
		}
		
		return $this->returnData(200, "获取成功", $treeData);
	}
}
