<?php

namespace Business\Finance;

use Business\Base;
use Business\JavaApi\Member\MemberQuery;
use Business\Member\MemberRelation;
use Business\NewJavaApi\TradeCenter\TradeJournalHotData;
use Library\Constants\Account\BookSubject;
use Model\TradeRecord\PftTransReport;
use Pimple\Container;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Order\OrderTools;
use Library\Constants\Account\TemplateItem;

use Business\JavaApi\Trade\Query;

class TradeQuery extends Base {

    private $_tradeType = [
        -1 => '全部', 
        0  => '收入', 
        1  => '支出'
    ];

    public function __construct() {
        \Library\Tools\Helpers::composerAutoload();
        
        $this->container = new Container();

        $this->container['member_model'] = function () {
            return new Member('slave');
        };
        $this->container['trade_api'] = function () {
            return new Query();
        };
        $this->container['member_business'] = function (){
            return new \Business\Member\Member();
        };
        $this->container['trade_center_api'] = function (){
            return new \Business\NewJavaApi\TradeCenter\TradeJournalHotData();
        };
    }

    /**
     * 获取交易记录查询配置
     * <AUTHOR>
     * @date   2019-03-08
     * @vacation tradeRecord
     * @return array
     */
    public function queryConfig($memberId, $saccount) {
        //账本列表
        $subjectBooks  = $this->_getSubjectBooks($memberId);
        //费用项分类
        $itemTypes     = $this->_getItemTypes();
        //费用项列表
        $items         = $this->_getItems();
        //查询时间段划分
        $dateRange     = $this->_getDateRange();
        //最大查询月份数
        $monthLimit    = $this->_getMonthLimit();
        //用户身份
        $memberType    = $this->_getMemberType($memberId);

        $vacationBiz = new \Business\PftSystem\VacationModeBiz();
        $open = $vacationBiz->judgeForPage('tradeRecord', $saccount);
        if ($open === false) {
            $invacation = 1;
        } else {
            $invacation = 0;
        }

        $return = ['subject_books' => $subjectBooks, 'item_type' => $itemTypes, 'items' => $items, 'date_range' => $dateRange, 'month_limit' => $monthLimit, 'member_type' => $memberType, 'in_vacation' => $invacation];

        return $this->returnData(200, '', $return);
    }

    /**
     * 获取交易记录列表
     * <AUTHOR>
     * @date   2019-03-08
     *
     * @param  int        $memberId       会员id
     * @param  string     $beginTime      开始时间
     * @param  string     $endTime        结束时间
     * @param  int|array  $subjectCode    账本科目
     * @param  int|array  $itemCode       费用项
     * @param  int        $tradeType      交易类型
     * @param  int        $page
     * @param  int        $size
     * @param  array      $option         查询选项
     * @param  int       $queryHot        是否查询热数据
     *
     * @return array
     */
    public function getTradeList($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page = 1, $size = 10, $option = [], $queryHot = 0) {

        if (!$memberId || !$beginTime || !$endTime) {
           return $this->returnData(204, '参数错误');
        }

        //查询热数据
        if ($queryHot) {
            return $this->queryHotTradeList($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
        }

        //查询用户身份判断
        $memberInfo = $this->container['member_model']->getMemberInfo($memberId, 'id','id,dtype,group_id');

        $checkRes = $this->_checkParams($memberInfo['id'], $memberInfo['dtype'], $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }

        switch ($memberInfo['dtype']) {
            //管理员账号
            case 9:
                $queryRes = $this->_queryTradeListForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
                break;
            //集团账号
            case 7:
                $queryRes = $this->_queryTradeListForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
                break;
            //普通账号
            default:
                $queryRes = $this->_queryTradeListForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
                break;
        }

        if ($queryRes['code'] == 200) {
            $list = $queryRes['data'] ? $this->_dataTranslate($queryRes['data'], $memberInfo['dtype']) : [];
            return $this->returnData(200, '', ['list' => $list]);
        } else {
            return $this->returnData(204, '系统繁忙，请稍后再试', []);
        }
    }

    /**
     * 获取交易记录列表 （热数据查询）
     * <AUTHOR>
     * @date   2024/11/18
     *
     * @param $memberId
     * @param $beginTime
     * @param $endTime
     * @param $subjectCode
     * @param $itemCode
     * @param $tradeType
     * @param $page
     * @param $size
     * @param $option
     *
     * @return array
     */
    public function queryHotTradeList($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page = 1, $size = 10, $option = [])
    {
        if (!$memberId || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }
        //优先处理参数
        $midArr = [$memberId];
        if (!empty($option['aid'])) {
            $midArr[] = $option['aid'];
        }
        $memberBiz     = new \Business\Member\Member();
        $memberInfoMap = $memberBiz->getMemberInfoByMulti($midArr, 'id', true);
        $memberInfo    = $memberInfoMap[$memberId] ?? [];
        if (empty($memberInfo) || (!empty($option['aid']) && empty($memberInfoMap[$option['aid']]))) {
            return $this->returnData(204, '用户不存在');
        }
        !empty($option['aid']) && isset($memberInfoMap[$option['aid']]['account_id']) && $option['aAccountId'] = $memberInfoMap[$option['aid']]['account_id'];
        isset($memberInfo['account_id']) && $option['fAccountId'] = $memberInfo['account_id'];

        $checkRes = $this->_checkParams($memberInfo['id'], $memberInfo['dtype'], $beginTime, $endTime, $subjectCode,
            $itemCode, $tradeType, $page, $size, $option);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        //管理员账号，集团账号不支持查询
        if (in_array($memberInfo['dtype'], [7, 9])) {
            return $this->returnData(200, '', ['list' => []]);
        }

        //热数据查询
        $queryRes = $this->_queryTradeListForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page,
            $size, $option);

        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            $queryRes['data'] = $this->_hotDataFormatCompatible($queryRes['data']);
            $list = $queryRes['data'] ? $this->_dataTranslate($queryRes['data']) : [];

            return $this->returnData(200, '', ['list' => $list]);
        }

        return $this->returnData(204, '系统繁忙，请稍后再试', []);
    }

    public function getTradeListByStartId($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId = 0, $size = 10, $option = [])
    {
        if (!$memberId || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        //查询用户身份判断
        $memberInfo = $this->container['member_model']->getMemberInfo($memberId, 'id','id,dtype,group_id');

        $checkRes = $this->_checkParams($memberInfo['id'], $memberInfo['dtype'], $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }

        switch ($memberInfo['dtype']) {
            //管理员账号
            case 9:
                $queryRes = $this->_queryTradeListForAdminByStartId($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option);
                break;
            //集团账号
            case 7:
                $queryRes = $this->_queryTradeListForGroupByStartId($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option);
                break;
            //普通账号
            default:
                $queryRes = $this->_queryTradeListForMemberByStartId($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option);
                break;
        }

        if ($queryRes['code'] == 200) {
            $list = $queryRes['data'] ? $this->_dataTranslate($queryRes['data'], $memberInfo['dtype']) : [];
            return $this->returnData(200, '', ['list' => $list]);
        } else {
            return $this->returnData(204, '系统繁忙，请稍后再试', []);
        }
    }

    public function getTradeListByPageQuery($memberInfo, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page = 0, $size = 10, $option = [], $requestId = null, $queryHot = 0)
    {
        if (!$memberInfo || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        if ($queryHot) {
            return $this->queryHotTradeListByPageQuery($memberInfo, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId);
        }

        $checkRes = $this->_checkParams($memberInfo['id'], $memberInfo['dtype'], $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }

        switch ($memberInfo['dtype']) {
            //管理员账号
            case 9:
                $queryRes = $this->_queryTradeListForAdminByPageQuery($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId);
                break;
            //集团账号
            case 7:
                $queryRes = $this->_queryTradeListForGroupByPageQuery($memberInfo['id'], $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId);
                break;
            //普通账号
            default:
                $option['fAccountId'] = $memberInfo['account_id'];
                $queryRes = $this->_queryTradeListForMemberByPageQuery($memberInfo['id'], $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId);
                break;
        }

        if ($queryRes['code'] == 200) {
            $list = $queryRes['data'] ? $this->_dataTranslate($queryRes['data'], $memberInfo['dtype']) : [];
            return $this->returnData(200, '', ['list' => $list]);
        } else {
            return $this->returnData(204, '系统繁忙，请稍后再试', []);
        }
    }

    public function queryHotTradeListByPageQuery($memberInfo, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page = 0, $size = 10, $option = [], $requestId = null)
    {
        if (!$memberInfo || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        $checkRes = $this->_checkParams($memberInfo['id'], $memberInfo['dtype'], $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }

        //管理员账号，集团账号不支持查询
        if (in_array($memberInfo['dtype'], [7, 9])) {
            return $this->returnData(200, '', ['list' => []]);
        }
        $option['fAccountId'] = $memberInfo['account_id'];
        $queryRes = $this->_queryTradeListForHotDataByPageQuery($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId);

        if ($queryRes['code'] == 200) {
            $queryRes['data'] = $this->_hotDataFormatCompatible($queryRes['data']);
            $list = $queryRes['data'] ? $this->_dataTranslate($queryRes['data']) : [];
            return $this->returnData(200, '', ['list' => $list]);
        }

        return $this->returnData(204, '系统繁忙，请稍后再试', []);
    }


    private function _checkParams($roleId, $roleType, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option) {

        if (!in_array($tradeType, array_keys($this->_tradeType))) {
            return $this->returnData(204, '不合法的交易记录类型');
        }

        if ($roleType == 7) {
            if ($option['fid']) {
                $memberRelationBiz = new MemberRelation();
                $trueRelation  = $memberRelationBiz->getMemberGroupInfoToJava($roleId,$option['fid'],'id',0);

                if (!$trueRelation) {
                    return $this->returnData(204, '无权查看');
                }
            }
        }

        //只保留集团账号和管理员账号
        if (in_array($roleType, [7,9]) && ((strtotime($endTime) - strtotime($beginTime)) / 3600 / 24 > 92)) {
            return $this->returnData(204, '查询时间跨度不能超过92天');
        }

        return $this->returnData(200);
    }


    /**
     * 普通会员身份获取交易记录列表
     * <AUTHOR>
     * @date   2019-03-08
     * @param  int      $memberId    会员id
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    private function _queryTradeListForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option) {

        $condition = $this->_parseConditionForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['page'] = $page;
        $condition['size'] = $size;

        return $this->container['trade_api']->memberQuery($condition);
    }

    private function _queryTradeListForMemberByStartId($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option) {

        $condition = $this->_parseConditionForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['startId'] = $startId;
        $condition['size'] = $size;

        return $this->container['trade_api']->memberQueryByStartId($condition);
    }

    private function _queryTradeListForMemberByPageQuery($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId) {

        $condition = $this->_parseConditionForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['page'] = $page;
        $condition['size'] = $size;
        $condition['requestId'] = $requestId;

        /** @var Query $tradeApi */
        $tradeApi = $this->container['trade_api'];
        return $tradeApi->memberQueryByPageQuery($condition);
    }

    private function _queryTradeListForHotDataByPageQuery($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId)
    {
        $condition              = $this->_parseConditionForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['pageNum']   = $page;
        $condition['pageSize']  = $size;
        $condition['requestId'] = $requestId;

        /** @var TradeJournalHotData $tradeApi */
        $tradeApi = $this->container['trade_center_api'];

        return $tradeApi->compatibleExportQuery($condition);
    }

    private function _queryTradeListForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option)
    {
        $condition = $this->_parseConditionForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['pageNum'] = $page;
        $condition['pageSize'] = $size;

        /** @var TradeJournalHotData $tradeApi */
        $tradeApi = $this->container['trade_center_api'];

        return $tradeApi->compatiblePage($condition);
    }

    private function _summaryForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {

        $condition = $this->_parseConditionForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);

        /** @var TradeJournalHotData $tradeApi */
        $tradeApi = $this->container['trade_center_api'];

        $queryRes = $tradeApi->compatibleSummary($condition);

        return $this->_summaryDataTranslate($queryRes['code'] == 200 ? $queryRes['data'] : []);
    }


    /**
     * 管理员身份获取交易记录列表
     * <AUTHOR>
     * @date   2019-03-08
     * @param  string    $beginTime   开始时间
     * @param  string    $endTime     结束时间
     * @param  int|array $subjectCode 账本科目
     * @param  int|array $itemCode    费用项
     * @param  int       $tradeType   交易类型
     * @param  array     $option      查询选项
     * @return array
     */
    private function _queryTradeListForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option) {

        $condition = $this->_parseConditionForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['page'] = $page;
        $condition['size'] = $size;

        return $this->container['trade_api']->adminQuery($condition);
    }

    private function _queryTradeListForAdminByStartId($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option)
    {
        $condition = $this->_parseConditionForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['startId'] = $startId;
        $condition['size'] = $size;

        return $this->container['trade_api']->adminQueryByStartId($condition);
    }

    private function _queryTradeListForAdminByPageQuery($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId)
    {
        $condition = $this->_parseConditionForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['page'] = $page;
        $condition['size'] = $size;
        $condition['requestId'] = $requestId;

        /** @var Query $tradeApi */
        $tradeApi = $this->container['trade_api'];
        return $tradeApi->adminQueryByPageQuery($condition);
    }


    /**
     * 集团用户身份获取交易记录列表
     * <AUTHOR>
     * @date   2019-03-08
     * @param  int      $memberId    会员id
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    private function _queryTradeListForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option) {

        $condition = $this->_parseConditionForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['page'] = $page;
        $condition['size'] = $size;

        return $this->container['trade_api']->groupQuery($condition);
    }

    private function _queryTradeListForGroupByStartId($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $startId, $size, $option)
    {
        $condition = $this->_parseConditionForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['startId'] = $startId;
        $condition['size'] = $size;

        return $this->container['trade_api']->groupQueryByStartId($condition);
    }

    private function _queryTradeListForGroupByPageQuery($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $page, $size, $option, $requestId)
    {
        $condition = $this->_parseConditionForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        $condition['page'] = $page;
        $condition['size'] = $size;
        $condition['requestId'] = $requestId;

        /** @var Query $tradeApi */
        $tradeApi = $this->container['trade_api'];
        return $tradeApi->groupQueryByPageQuery($condition);
    }

    private function _dataTranslate($data, $dtype = 0) {

        //获取用户名
        $memberArr = array_merge(array_column($data, 'fid'),  array_column($data, 'aid'), array_column($data, 'opid'));
        $memberArr = array_unique($memberArr);
//        $comNameMap   = $this->container['member_model']->getMemberExtInfoMulti($memberArr, 'fid,com_name');
        $comNameMap   = $this->container['member_business']->getMemberExtListGetFieldToJava($memberArr, 'fid,com_name');
        $accNameMap   = $this->container['member_model']->getMemberInfoByMulti($memberArr, 'id', 'id,dname,account', 1);
        $javaApi = new \Business\NewJavaApi\Order\OrderDistribution();
        $comNameMap[1] = '票付通信息科技';
        $accNameMap[1] = ['id' => 1, 'dname' => '票付通信息科技', 'account' => 'admin'];
        //自身账本科目名称（自身账本可能存在自定义名称）
        $fid = in_array($dtype, [7, 9]) ? 0 : ($data[0]['fid'] ?? 0);
        $subjectSelfMap = array_key($this->_getSubjectBooks($fid, false), 'code');
        //账本科目名称
        $subjectMap = array_key($this->_getSubjectBooks(), 'code');
        //费用项名称
        $itemMap = array_key($this->_getItems(), 'code');
        //订单号数组
        $orderArr = array_values(array_unique(array_column($data, 'orderid')));
        //获取订单状态
        $orderModel = new OrderTools('slave');
        $orderMap = $orderModel->getOrderInfo($orderArr, 'ordernum,status') ?: [];
        $orderMap = array_key($orderMap, 'ordernum');
        $orderStatusMap = load_config('order_status');
        $itemCategory   = load_config('item_category', 'trade_record');
        $categoryMap    = load_config('trade_item', 'trade_record');

        //获取一次订单类型
        $dataRes = [];
        while($orderArr) {
            $chunk = array_splice($orderArr, 0, 50);
            $orderTypeRes = $javaApi->getPtypeByOrderNumArr($chunk);
            if ($orderTypeRes['code'] == 200 && !empty($orderTypeRes['data'])) {
                $dataRes = array_merge($dataRes, $orderTypeRes['data']);
            }
        }
        $orderType = array_column($dataRes, 'itemType', 'orderId');

        $list = [];
        foreach ($data as $item) {

            $self = [
                'id'             => $item['fid'],
                'account'        => $accNameMap[$item['fid']]['account'],
                'name'           => $accNameMap[$item['fid']]['dname'],
                'acc_name'       => $accNameMap[$item['fid']]['dname'],
                'com_name'       => $comNameMap[$item['fid']],
                'subject'        => $subjectSelfMap[$item['subjectCode']]['name'], //账本科目名称
                'actual_subject' => $subjectSelfMap[$item['subjectCode']]['actual_name'] ?? '', //实际账本科目名称
            ];

            $opposite = [
                'id'       => $item['aid'],
                'account'  => $accNameMap[$item['aid']]['account'],
                'name'     => $accNameMap[$item['aid']]['dname'],
                'acc_name' => $accNameMap[$item['aid']]['dname'],
                'com_name' => $comNameMap[$item['aid']],
                'subject'  => $subjectMap[$item['tradeSubjectCode']]['name']
            ];

            if ($item['subjectCode'] < 2000 || $item['subjectCode'] == 2701) {
                $leftMoney = '';
            } else {
                $leftMoney = $item['lmoney'];
            }

            if (isset($orderMap[$item['orderid']])) {
                $isOrder = 1;
                $status  = $orderStatusMap[$orderMap[$item['orderid']]['status']];
            } else {
                $isOrder = 0;
                $status  = '';
            }

            $money = $item['daction'] == 0 ? $item['dmoney'] : 0 - $item['dmoney'];

            $itemName = $itemMap[$item['templateItemCode']]['name'];

            $list[] = [
                'trade_id'    => $item['id'],
                'trade_time'  => $item['rectime'],
                'order_id'    => $item['orderid'],
                'trade_no'    => $item['tradeNo'],
                'self'        => $self,
                'opposite'    => $opposite,
                'item_name'   => $itemName,
                'item_belong' => $categoryMap[$itemCategory[$item['dtype']][0]],
                'money'       => $money,
                'left_money'  => $leftMoney,
                'dtype'       => $item['dtype'],
                'daction'     => $item['daction'],
                'remark'      => $item['memo'],
                'order_status'=> $status,
                'status'      => $item['status'],
                'is_order'    => $isOrder,
                'oper'        => $accNameMap[$item['opid']]['dname'] ?: '',
                'p_type'      => $orderType[$item['orderid']] ?: '',
                'cmb_id'      => $item['cmbId'],
                'tid'         => $item['tid'],
                'pay_id'      => $item['payId'],
            ];
        }

        return $list;
    }

    /**
     * 获取交易记录汇总
     * <AUTHOR>
     * @date   2019-03-08
     * @param  int      $memberId    会员id
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    public function summary($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option = [], $queryHot = 0) {

        if (!$memberId || !$beginTime || !$endTime) {
           return $this->returnData(204, '参数错误');
        }

        if ($queryHot) {
            return $this->queryHotSummary($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
        }

        //查询用户身份判断
        $memberInfo = $this->container['member_model']->getMemberInfo($memberId, 'id','dtype,group_id');

        switch ($memberInfo['dtype']) {
            //管理员账号
            case 9:
                $data = $this->_summaryForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
                break;
            //集团账号
            case 7:
                $data = $this->_summaryForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
                break;
            //普通账号
            default:
                $data = $this->_summaryForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);
                break;
        }

        return $this->returnData(200, '', $data);
    }

    /**
     * 获取交易记录汇总(热数据查询)
     * <AUTHOR>
     * @date   2024/11/18
     *
     * @param $memberId
     * @param $beginTime
     * @param $endTime
     * @param $subjectCode
     * @param $itemCode
     * @param $tradeType
     * @param $option
     *
     * @return array
     */
    public function queryHotSummary($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option = [])
    {
        if (!$memberId || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        //优先处理参数
        $midArr = [$memberId];
        if (!empty($option['aid'])) {
            $midArr[] = $option['aid'];
        }
        $memberBiz     = new \Business\Member\Member();
        $memberInfoMap = $memberBiz->getMemberInfoByMulti($midArr, 'id', true);
        $memberInfo    = $memberInfoMap[$memberId] ?? [];
        if (empty($memberInfo) || (!empty($option['aid']) && empty($memberInfoMap[$option['aid']]))) {
            return $this->returnData(204, '用户不存在');
        }
        !empty($option['aid']) && isset($memberInfoMap[$option['aid']]['account_id']) && $option['aAccountId'] = $memberInfoMap[$option['aid']]['account_id'];
        isset($memberInfo['account_id']) && $option['fAccountId'] = $memberInfo['account_id'];

        //管理员账号，集团账号不支持查询
        if (in_array($memberInfo['dtype'], [7, 9])) {
            return $this->returnData(200, '', ['list' => []]);
        }

        $data = $this->_summaryForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);

        return $this->returnData(200, '', $data);
    }


    /**
     * 普通会员交易记录查询汇总
     * <AUTHOR>
     * @date   2019-03-08
     * @param  int      $memberId    会员id
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    private function _summaryForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {
        
        $condition = $this->_parseConditionForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);

        $queryRes = $this->container['trade_api']->memberSummary($condition);

        return $this->_summaryDataTranslate($queryRes['code'] == 200 ? $queryRes['data'] : []);
    }


    /**
     * 管理员获取交易记录列表
     * <AUTHOR>
     * @date   2019-03-08
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    private function _summaryForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {
        
        $condition = $this->_parseConditionForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);

        $queryRes = $this->container['trade_api']->adminSummary($condition);

        return $this->_summaryDataTranslate($queryRes['code'] == 200 ? $queryRes['data'] : []);
    }


    /**
     * 集团账号交易记录查询汇总
     * <AUTHOR>
     * @date   2019-03-08
     * @param  int      $memberId    会员id
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    private function _summaryForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {
        
        $condition = $this->_parseConditionForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option);

        $queryRes = $this->container['trade_api']->groupSummary($condition);

        return $this->_summaryDataTranslate($queryRes['code'] == 200 ? $queryRes['data'] : []);
    }


    private function _summaryDataTranslate($origin) {
        if ($origin) {
            $data = [
                'expense'   => $origin['expense'],
                'expenseNum'=> $origin['expenseNum'],
                'income'    => $origin['income'],
                'incomeNum' => $origin['incomeNum'],
                'netIncome' => $origin['netIncome']
            ];
        } else {
            $data = [
                'expense'   => 0,
                'expenseNum'=> 0,
                'income'    => 0,
                'incomeNum' => 0,
                'netIncome' => 0
            ];
        }
        return $data;
    }


    private function _parseConditionForMember($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {

        $condition = [
            // 'fid'           => $memberId,
            'begin_date'    => $beginTime,
            'end_date'      => $endTime,
            // 'subject_codes' => [$subjectCode],
            // 'item_codes'    => [$itemCode],
        ];

        if ($memberId) {
            $condition['fid'] = $memberId;
        }
        if ($itemCode) {
            $condition['item_codes'] = is_array($itemCode) ? $itemCode : [$itemCode];
        }
        if ($subjectCode) {
            $condition['subject_codes'] = is_array($subjectCode) ? $subjectCode : [$subjectCode];
        }

        if ($option['order_id']) {
            $condition['order_arr'] = [$option['order_id']];
        }
        if ($option['trade_no']) {
            $condition['tradeno_arr'] = [$option['trade_no']];
        }
        if ($option['aid']) {
            $condition['aid'] = $option['aid'];
        }
        if (!empty($option['fAccountId'])) {
            $condition['accountIds'] = [$option['fAccountId']];
        }
        if (!empty($option['aAccountId'])) {
            $condition['tradeAccountIds'] = [$option['aAccountId']];
        }
        if (!empty($option['tradeScope'])) {
            $condition['tradeScope'] = $option['tradeScope'];
        }
        if ($tradeType != -1) {
            $condition['daction'] = $tradeType;
        }

        return $condition;
    }

    private function _parseConditionForAdmin($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {

        $condition = [
            'begin_date'    => $beginTime,
            'end_date'      => $endTime,
            // 'subject_codes' => [$subjectCode],//非必填
            // 'item_codes'    => [$itemCode],//非必填
        ];

        if ($itemCode) {
            $condition['item_codes'] = is_array($itemCode) ? $itemCode : [$itemCode];
        }
        if ($subjectCode) {
            $condition['subject_codes'] = is_array($subjectCode) ? $subjectCode : [$subjectCode];
        }
        if ($option['order_id']) {
            $condition['order_arr'] = [$option['order_id']];
        }
        if ($option['trade_no']) {
            $condition['tradeno_arr'] = [$option['trade_no']];
        }
        if ($option['fid']) {
            $condition['fid_arr'] = [$option['fid']];
        }
        if (!empty($option['fAccountId'])) {
            $condition['accountIds'] = [$option['fAccountId']];
        }
        if ($option['aid']) {
            $condition['aid_arr'] = [$option['aid']];
        }
        if (!empty($option['aAccountId'])) {
            $condition['tradeAccountIds'] = [$option['aAccountId']];
        }
        if ($tradeType != -1) {
            $condition['daction'] = $tradeType;
        }

        return $condition;
    }

    private function _parseConditionForGroup($memberId, $beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option) {

        $condition = [
            'group_fid'     => $memberId,
            'begin_date'    => $beginTime,
            'end_date'      => $endTime,
            // 'subject_codes' => [$subjectCode],
            // 'item_codes'    => [$itemCode],
        ];

        if ($itemCode) {
            $condition['item_codes'] = is_array($itemCode) ? $itemCode : [$itemCode];
        }
        if ($subjectCode) {
            $condition['subject_codes'] = is_array($subjectCode) ? $subjectCode : [$subjectCode];
        }
        if ($option['order_id']) {
            $condition['order_arr'] = [$option['order_id']];
        }
        if ($option['trade_no']) {
            $condition['tradeno_arr'] = [$option['trade_no']];
        }
        if ($option['fid']) {
            $condition['fid_arr'] = [$option['fid']];
        }
        if ($option['fAccountId']) {
            $condition['accountIds'] = [$option['fAccountId']];
        }
        if ($option['aid']) {
            $condition['aid_arr'] = [$option['aid']];
        }
        if ($option['aAccountId']) {
            $condition['tradeAccountIds'] = [$option['aAccountId']];
        }
        if ($tradeType != -1) {
            $condition['daction'] = $tradeType;
        }

        return $condition;
    }

    /**
     * 获取一条交易记录的详情
     * <AUTHOR>
     * @date   2019-03-11
     * @param  int     $memberId  会员id
     * @param  int     $tradeId   交易记录id
     * @param  string  $beginTime 开始时间
     * @param  string  $endTime   结束时间
     * @return array
     */
    public function getTradeDetail($memberId, $tradeId, $beginTime, $endTime) {

        if (!$memberId || !$tradeId || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        $queryRes = $this->container['trade_api']->queryById($tradeId, $beginTime, $endTime);
        if ($queryRes['code'] != 200 && $queryRes['data']) {
            return $queryRes;
        }

        $journal = $queryRes['data'];
        if ($memberId != 1 && !in_array($memberId, [$journal['fid'], $journal['aid']])) {
            return $this->returnData(204, '没有权限查看');
        }

        $list = $this->_dataTranslate([$queryRes['data']]);
        $journal = $list[0];
        
        return $this->returnData(200, '', $journal);
    }

    /**
     * 微平台交易记录查询
     * <AUTHOR>
     * @date   2019-03-27
     * @param  integer    $memberId    会员id
     * @param  string     $beginTime   开始时间
     * @param  string     $endTime     结束时间
     * @param  integer    $subjectCode 账本编码
     * @param  integer    $itemType    费用项大类
     * @param  integer    $page        当前页码
     * @param  integer    $size        每页条数
     * @return array
     */
    public function getTradeListForMicroPlatform($memberId, $beginTime, $endTime, $subjectCode = 0, $itemType = 0, $page = 1, $size = 10, $option) {

        if (!$memberId || !$beginTime || !$endTime) {
           return $this->returnData(204, '参数错误');
        }

        $itemCode = $this->_parseItemCode($itemType);
        return $this->getTradeList($memberId, $beginTime, $endTime, $subjectCode, $itemCode, -1, $page, $size, $option);
    }


    /**
     * 微平台交易记录汇总
     * <AUTHOR>
     * @date   2019-03-08
     * @param  int      $memberId    会员id
     * @param  string   $beginTime   开始时间
     * @param  string   $endTime     结束时间
     * @param  int      $subjectCode 账本科目
     * @param  int      $itemCode    费用项
     * @param  int      $tradeType   交易类型
     * @param  array    $option      查询选项
     * @return array
     */
    public function summaryForMicroPlatform($memberId, $beginTime, $endTime, $subjectCode, $itemType) {

        if (!$memberId || !$beginTime || !$endTime) {
           return $this->returnData(204, '参数错误');
        }

        $itemCode = $this->_parseItemCode($itemType);
        return $this->summary($memberId, $beginTime, $endTime, $subjectCode, $itemCode, -1);
    }


    private function _parseItemCode($itemType) {
        if ($itemType == 0) {
            return 0;
        } else {
            //解析对应的费用项
            $category   = load_config('item_category', 'trade_record');
            $dtype2item = load_config('dtype_2_item_code', 'trade_record');
            $itemCode = [];
            foreach ($category as $dtype => $item) {
                if ($item[0] == $itemType) {
                    $itemCode[] = $dtype2item[$dtype];
                }
            }
            return $itemCode;
        }
    }


    /**
     * 统计详细版
     * <AUTHOR>
     * @date   2019-03-27
     * @param  integer    $memberId  会员id
     * @param  string     $beginTime 开始时间
     * @param  string     $endTime   结束时间
     * @param  integer    $type      1收入2支出
     * @return array
     */
    public function summaryInDetail($memberId, $beginTime, $endTime, $type) {

        if (!$memberId || !$beginTime || !$endTime || !$type) {
            return $this->returnData(204, '参数错误');
        }

        $daction = $type == 1 ? 0 : 1;

        $queryRes = $this->container['trade_api']->summaryForItemcode($memberId, $beginTime, $endTime, $daction);
        if ($queryRes['code'] == 200) {
            $list = $queryRes['data'];
            $totalMoney = array_sum(array_column($list, 'totalDmoney'));
            $totalTrans = array_sum(array_column($list, 'totalNumber'));

            $itemCodeMap = array_key($this->_getItems(), 'code');

            $tradeItems    = load_config('trade_item', 'trade_record');
            $tradeCategory = load_config('item_category', 'trade_record');
            $item2dtype    = array_flip(load_config('dtype_2_item_code', 'trade_record'));

            $smallItems = $bigItems = [];
            foreach ($list as $item) {
                $bigItemName = $tradeItems[$tradeCategory[$item2dtype[$item['itemCode']]][0]];

                if ($totalMoney == 0) {
                    $percentage = 0;
                } else {
                    $percentage = sprintf("%.2f", $item['totalDmoney'] / $totalMoney);
                }

                $smallItems[$bigItemName][] = [
                    'name'        => $itemCodeMap[$item['itemCode']]['name'],
                    'total_money' => $item['totalDmoney'],
                    'total_trans' => $item['totalNumber'],
                    'percentage'  => $percentage
                ];
            }

            foreach ($tradeItems as $key => $item) {
                $bigItems[$key]['name'] = $item;
                $bigItems[$key]['total_money'] = 0;
                foreach ($list as $val) {
                    if ($key == $tradeCategory[$item2dtype[$val['itemCode']]][0]) {
                        $bigItems[$key]['total_money'] += $val['totalDmoney'];
                    }
                }
                if ($totalMoney == 0) {
                    $bigItems[$key]['percentage'] = 0;
                } else {
                    $bigItems[$key]['percentage'] = sprintf("%.2f", $bigItems[$key]['total_money'] / $totalMoney);
                }
            }

            $data = [
                'total_money' => $totalMoney,
                'total_trans' => $totalTrans,
                'big_items'   => $bigItems,
                'small_items' => $smallItems
            ];
        } else {
            $data = [
                'total_money' => 0,
                'total_trans' => 0,
                'big_items'   => 0,
                'small_items' => 0
            ];
        }

        return $this->returnData(200, '', $data);
    }


    /**
     * 获取授信相关的交易记录列表
     * <AUTHOR>
     * @date   2019-11-07
     * @param  string      $type      操作类型
     * @param  int         $fid       分销商id
     * @param  integer     $aid       供应商
     * @param  string      $beginTime 开始时间
     * @param  string      $endTime   结束时间
     * @param  int|integer $page      当前页码
     * @param  int|integer $size      条数
     * @return array
     */
    public function getCreditList(string $type, int $fid, int $aid, string $beginTime, string $endTime, int $page = 1, int $size = 15) 
    {
        if (!$type || !$fid || !$page || !$size || !$aid || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        if (!in_array($type, ['credit', 'preSave', 'deal', 'adjust'])) {
            return $this->returnData(204, '查询的交易记录类型未定义');
        }

        switch ($type) {
            case 'credit':
                $items = [TemplateItem::ORDER_DEDUCT_CODE, TemplateItem::ORDER_REFUND_CODE, TemplateItem::OFFLINE_REFUND_CODE, TemplateItem::REFUND_SERVICE_FEE_CODE, TemplateItem::ORDER_REVOKE_CODE,TemplateItem::REFUND_CREDIT_RECHARGE_CODE, TemplateItem::INCREASE_TICKET_NUM_CODE, TemplateItem::ADJUST_CREDIT_LINE];
                // 产品要求注释item_code，回款需要
                $items = [];
                break;
            case 'preSave':
                $items = [TemplateItem::OFFLINE_REFUND_CODE, TemplateItem::REFUND_CREDIT_RECHARGE_CODE];
                break;
            case 'deal':
                $items = [TemplateItem::ORDER_DEDUCT_CODE, TemplateItem::ORDER_REFUND_CODE, TemplateItem::ORDER_REVOKE_CODE, TemplateItem::REFUND_SERVICE_FEE_CODE];
                break;
            case 'adjust':
                $items = [TemplateItem::ADJUST_CREDIT_LINE];
                break;
        }

        $queryRes = $this->container['trade_api']->creditListQuery($beginTime, $endTime, $items, $fid, $aid, $page, $size);
        if ($queryRes['code'] == 200) {
            //获取操作人姓名
            $opArr     = array_unique(array_column($queryRes['data'], 'opid'));
            $opNameMap = $this->container['member_model']->getMemberInfoByMulti($opArr, 'id', 'id,dname', 1);
            //费用项
            $itemsMap  = array_key($this->_getItems(), 'code');
            $list  = [];

            foreach ($queryRes['data'] as $item) {
                $opName = '';
                //是否有权限看
                if (in_array($item['opid'], [$item['fid'], $item['aid']])) {
                    $opName = $opNameMap[$item['opid']]['dname'] ?? '';
                }
                $list[] = [
                    'fee_item'      => $itemsMap[$item['templateItemCode']]['name'],
                    'rectime'       => $item['rectime'],
                    'orderid'       => $item['orderid'],
                    'dmoney'        => $item['dmoney'],
                    'lmoney'        => $item['lmoney'],
                    'limit'         => $item['tradeLimitMoney'],
                    'daction'       => $item['daction'],
                    'used_limit'    => $item['usedLimit'],
                    'usable_money'  => $item['usableMoney'],
                    'remain_limit'  => $item['remainLimit'],
                    'oper'          => $opName,
                    'memo'          => $item['memo']
                ];
            }
            return $this->returnData(200, '', ['list' => $list]);
        } else {
            return $this->returnData(204, $queryRes['msg']);
        }
    }


    /**
     * 导出授信交易记录
     * <AUTHOR>
     * @date   2019-11-07
     * @param  string      $type      操作类型
     * @param  int         $fid       分销商id
     * @param  integer     $aid       供应商
     * @param  string      $beginTime 开始时间
     * @param  string      $endTime   结束时间
     * @return array
     */
    public function exportCreditList(string $type, int $fid, int $aid, string $beginTime, string $endTime)
    {
        if (!$type || !$fid || !$aid || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        //每页500条
        $pageSize = 500;
        //页码
        $pageNum  = 1;
        //总页数，最大仅支持40页查询
        $maxPage  = 40;
        //excel内容
        $excelData = [];

        //分页查询
        while ($pageNum <= $maxPage) {
            if ($pageNum == 1) {
                if ($type == 'adjust') {
                    $head = [0 => '时间', 1 => '订单号', 2 => '本次调整额度(元)', 3 => '调整后总授信额度(元)', 4 => '已用额度(元)', 5 => '剩余额度(元)',  6 => '可用金额(元)',  7 => '操作人', 8 => '备注'];
                } else {
                    $head = [0 => '时间', 1 => '订单号', 2 => '操作类型', 3 => '交易金额(元)', 4 => '已用额度(元)', 5 => '剩余额度(元)', 6 => '可用金额(元)', 7 => '操作人', 8 => '备注'];
                }
                $excelData[] = $head;
            }
            $getRes = $this->getCreditList($type, $fid, $aid, $beginTime, $endTime, $pageNum, $pageSize);
            if ($getRes['code'] != 200) {
                return $this->returnData(204, $getRes['msg'] ?? '导出失败');
            }

            $data = $getRes['data']['list'] ?? [];
            //没有数据直接跳出
            if (empty($data)) {
                break;
            }

            foreach ($data as $item) {
                $hasUsedLimit = $item['lmoney'] < 0 ? -$item['lmoney'] : 0;
                $tmp = [
                    0 => date('Y-m-d H:i:s', $item['rectime']),
                    1 => $item['orderid'] . "\t",
                ];
                if ($type == 'adjust') {
                    $tmp[2] = money_fmt($item['dmoney']);
                    $tmp[2] = $item['daction'] == 1 ? -$tmp[2] : $tmp[2];
                    if ($item['limit'] == 10000000000) {
                        //不限额度
                        $tmp[3] = '不限';
                    } else {
                        $tmp[3] = money_fmt($item['limit']);
                    }
                } else {
                    $tmp[2] = $item['fee_item'];
                    $tmp[3] = money_fmt($item['dmoney']);
                    $tmp[3] = $item['daction'] == 1 ? -$tmp[3] : $tmp[3];
                }

                $tmp[4] = money_fmt($hasUsedLimit);
                if ($item['limit'] == 10000000000) {
                    $tmp[5] = '不限';
                    $tmp[6] = '不限';
                } else {
                    $tmp[5] = money_fmt($item['limit'] - $hasUsedLimit);
                    $tmp[6] = money_fmt($item['limit'] + $item['lmoney']);
                }
                $tmp[7] = $item['oper'];
                $tmp[8] = $item['memo'];

                $excelData[] = $tmp;
            }

            $pageNum++;
        }

        return $this->returnData(200, '', $excelData);

        // $getRes = $this->getCreditList($type, $fid, $aid, $beginTime, $endTime, 1, 99999);
        // if ($getRes['code'] != 200) {
        //     return $getRes;
        // }
        //
        // if ($type == 'adjust') {
        //     $head = [0 => '时间', 1 => '订单号', 2 => '本次调整额度(元)', 3 => '调整后总授信额度(元)', 4 => '已用额度(元)', 5 => '剩余额度(元)',  6 => '可用金额(元)',  7 => '操作人', 8 => '备注'];
        // } else {
        //     $head = [0 => '时间', 1 => '订单号', 2 => '操作类型', 3 => '交易金额(元)', 4 => '已用额度(元)', 5 => '剩余额度(元)', 6 => '可用金额(元)', 7 => '操作人', 8 => '备注'];
        // }
        //
        // $data[] = $head;
        // foreach ($getRes['data']['list'] as $item) {
        //     $hasUsedLimit = $item['lmoney'] < 0 ? -$item['lmoney'] : 0;
        //     $tmp = [
        //         0 => date('Y-m-d H:i:s', $item['rectime']),
        //         1 => $item['orderid'] . "\t",
        //     ];
        //     if ($type == 'adjust') {
        //         $tmp[2] = money_fmt($item['dmoney']);
        //         $tmp[2] = $item['daction'] == 1 ? -$tmp[2] : $tmp[2];
        //         if ($item['limit'] == 10000000000) {
        //             //不限额度
        //             $tmp[3] = '不限';
        //         } else {
        //             $tmp[3] = money_fmt($item['limit']);
        //         }
        //     } else {
        //         $tmp[2] = $item['fee_item'];
        //         $tmp[3] = money_fmt($item['dmoney']);
        //         $tmp[3] = $item['daction'] == 1 ? -$tmp[3] : $tmp[3];
        //     }
        //
        //     $tmp[4] = money_fmt($hasUsedLimit);
        //     if ($item['limit'] == 10000000000) {
        //         $tmp[5] = '不限';
        //         $tmp[6] = '不限';
        //     } else {
        //         $tmp[5] = money_fmt($item['limit'] - $hasUsedLimit);
        //         $tmp[6] = money_fmt($item['limit'] + $item['lmoney']);
        //     }
        //     $tmp[7] = $item['oper'];
        //     $tmp[8] = $item['memo'];
        //
        //     $data[] = $tmp;
        // }

        // return $this->returnData(200, '', $data);
    }
    /**
     * 获取交易记录统计汇总新版
     * <AUTHOR>
     * @date   2020-05-20
     * @param  int      $dtype      商户类型
     * @param  int         $type       获取用户类型
     * @param  integer     $memberId       供应商
     * @param  string      $beginTime 开始时间
     * @param  string      $endTime   结束时间
     * @param  int      $page   页数
     * @param  int      $size   大小
     * @return array
     */
    public function getTradeCountInfoService($beginTime, $endTime,$dtype = -1, $type = 0, $memberId = 0, $page = 1, $size = 3)
    {
        if (!$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }
        $resData     = [
            'total' => 0,
            'list'  => [],
        ];
        $memberQueryApi   = new MemberQuery();
        $fidArr = $memberId ? [$memberId] : [];
        $userListRes = $memberQueryApi->queryMemberNameByDtypeList($dtype,$type,$fidArr,$page,$size);
        if ($userListRes['code'] != 200){
            return $this->returnData(204, '获取用户失败');
        }
        $userList    = $userListRes['data']['list']?? [];
        $count       = $userListRes['data']['total'] ?? 0;
        if (empty($userList) || !$count){
            return $this->returnData(200, '', $resData);
        }

        try{
            $resList = $this->_handleUserTradeList($userList,$beginTime,$endTime);
        }catch (\Exception $e){
            $msg = $e->getMessage();
            return $this->returnData(500, $msg);
        }

        $resData['total'] = $count;
        $resData['list'] = $resList;
        $resData['trade_field'] = $this->tradeFieldChange();
        return $this->returnData(200, '', $resData);
    }
    /**
     * 获取交易记录统计汇总新版
     * <AUTHOR>
     * @date   2020-05-20
     * @param  string      $beginTime 开始时间
     * @param  string      $endTime   结束时间
     * @param  int      $fid   用户id
     * @return array
     */
    public function getTradeCountInfoDetailService($beginTime,$endTime,$fid){
        if (!$beginTime || !$endTime || !$fid){
            return $this->returnData(204, '参数错误');
        }
        $memberBiz = new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($fid,true);
        try{
            $result = $this->_handleOneUserTradeInfo($memberInfo,$beginTime,$endTime);
        }catch (\Exception $e){
            $msg = $e->getMessage();
            return $this->returnData(500, $msg);
        }
        $detail = [
            'detail' => $result,
            'trade_field' => $this->tradeFieldChange()
        ];
        return $this->returnData(200, '', $detail);
    }
    /**
     * 获取账本科目类型
     * <AUTHOR>
     * @date   2019-03-08
     * @return array
     */
    private function _getSubjectBooks($fid = 0, $filterUndefined = true) {
        if (intval($fid) == 0) {
            $subjectBooks = load_config('book_subject', 'account_book');
        } else {
            $subjectBooks = AccountBookSubject::getInstance()->getCommonBookSubject($fid, $filterUndefined);
        }
        $all = ['code' => 0, 'name' => '全部'];
        array_unshift($subjectBooks, $all);
        return array_values($subjectBooks);
    }

    /**
     * 获取费用项分类
     * <AUTHOR>
     * @date   2019-03-27
     * @return array
     */
    private function _getItemTypes() {
        $list = load_config('trade_item', 'trade_record');
        $itemTypes = [];
        foreach ($list as $code => $name) {
            $itemTypes[] = [
                'code' => $code,
                'name' => $name
            ];
        }
        $all = ['code' => 0, 'name' => '全部'];
        array_unshift($itemTypes, $all);
        return $itemTypes;
    }

    /**
     * 获取费用项列表
     * <AUTHOR>
     * @date   2019-03-08
     * @return array
     */
    private function _getItems() {
        $items = load_config('template_item', 'account_book');
        $all = ['code' => 0, 'name' => '全部'];
        array_unshift($items, $all);
        return array_values($items);
    }

    /**
     * 获取查询时间段
     * <AUTHOR>
     * @date   2019-03-08
     * @return array
     */
    private function _getDateRange() {
        $yearBegin = 2013;
        $yearEnd   = date('Y');

        $dateRange = [];
        for ($year = $yearBegin; $year < $yearEnd; $year++) {
            $dateRange[] = [$year.'-01-01', $year.'-06-30'];
            $dateRange[] = [$year.'-07-01', $year.'-12-31'];
        }

        //最近一年先手动指定的，不知道什么日期会进行归档
        $dateRange[] = [date('Y-01-01'), date('Y-m-d')];
        return $dateRange;
    }

    private function _getMonthLimit() {
        return 3;
    }

    private function _getMemberType($memberId) {
        //查询用户身份判断
        $memberInfo = $this->container['member_model']->getMemberInfo($memberId, 'id','dtype');
        return intval($memberInfo['dtype']);
    }
    /**
     * 处理财务费用项数据
     * <AUTHOR>
     * @param array $list  列表
     * @param string $beginTime  开始时间
     * @param string $endTime  结束时间
     * @date   2020-05-20
     * @return array
     */
    private function _handleUserTradeList($list, $beginTime, $endTime)
    {
        if (empty($list)) {
            throw new \Exception("列表为空");
        }
        //获取用户基本信息
        $accountIds  = array_column($list, 'accountId');
        $memberIdArr = array_column($list, 'id');
        //获取下用户当前余额
        $accountBookApi = new \Business\JavaApi\Account\AccountBook();
        $result         = $accountBookApi->queryPlatformBook($memberIdArr);
        if ($result['code'] != 200) {
            throw new \Exception("获取用户余额失败");
        }

        //获取用户信息

        $memberBiz       = new \Business\Member\Member();
        $memberList      = $memberBiz->getList($memberIdArr, true);
        $balanceUserList = array_column($result['data'], null, 'member_id');
        $beginMoneyTime  = date('ymd', strtotime('-1 Day', strtotime($beginTime)));
        $endMoneyTime    = date('ymd', strtotime('-1 Day', strtotime($endTime)));
        $beginTime       = date('ymd', strtotime($beginTime));
        $endTime         = date('ymd', strtotime($endTime));
        //获取用户的期初期末余额
        $tradeRecordMdl = new PftTransReport();
        $beginMoney     = $tradeRecordMdl->getUserTradeMoneyByPlatformMoney($accountIds, $beginMoneyTime);
        $beginMoney = array_column($beginMoney, null, 'account_id');
        $endMoney   = $tradeRecordMdl->getUserTradeMoneyByPlatformMoney($accountIds, $endMoneyTime);
        $endMoney = array_column($endMoney, null, 'account_id');
        //获取用户的账本
        $platformMoneyList = $tradeRecordMdl->getUserTradeTemplateCodeMoneyList($accountIds, $beginTime, $endTime,
            [BookSubject::PLATFORM_SUBJECT_CODE]);
        $platformMoneyList = array_column($platformMoneyList, null, 'account_id');

        //获取用户的费用项
        $itemCode   = [
            TemplateItem::PRESTORE_SMS_FEE_CODE,
            TemplateItem::PRESTORE_CERT_FEE_CODE,
            TemplateItem::SMS_DEDUCT_CODE,
            TemplateItem::ELE_CERT_DEDUCT_CODE,
        ];
        $chargeList = $tradeRecordMdl->getUserChargeMoneyList($accountIds, $beginTime, $endTime, $itemCode);
        $resList    = [];
        foreach ($list as $key => $value) {
            $list = [
                'id'                => $value['id'],
                'dname'             => $value['dname'],
                'balance_money'     => isset($balanceUserList[$value['id']]) ? $balanceUserList[$value['id']]['money'] : 0,
                'account'           => isset($memberList[$value['id']]) ? $memberList[$value['id']]['account'] : '',
                'com_name'          => isset($memberList[$value['id']]) ? $memberList[$value['id']]['com_name'] : '',
                'begin_money'       => isset($beginMoney[$value['accountId']]) ? $beginMoney[$value['accountId']]['end_money'] : 0,
                'end_money'         => isset($endMoney[$value['accountId']]) ? $endMoney[$value['accountId']]['end_money'] : 0,
                'expense'           => isset($platformMoneyList[$value['accountId']]) ? $platformMoneyList[$value['accountId']]['expense'] : 0,
                'income'            => isset($platformMoneyList[$value['accountId']]) ? $platformMoneyList[$value['accountId']]['income'] : 0,
                'sms_deduct'        => 0,
                'ele_cert_deduct'   => 0,
                'prestore_cert_fee' => 0,
                'prestore_sms_fee'  => 0,
            ];
            foreach ($chargeList as $k => $v) {
                switch ($v['template_item_code']) {
                    case TemplateItem::PRESTORE_SMS_FEE_CODE:
                        if ($v['account_id'] == $value['accountId']) {
                            $list['prestore_sms_fee'] = $v['income'];
                        }
                        break;
                    case TemplateItem::PRESTORE_CERT_FEE_CODE:
                        if ($v['account_id'] == $value['accountId']) {
                            $list['prestore_cert_fee'] = $v['income'];
                        }
                        break;
                    case TemplateItem::SMS_DEDUCT_CODE:
                        if ($v['account_id'] == $value['accountId']) {
                            $list['sms_deduct'] = $v['expense'];
                        }
                        break;
                    case TemplateItem::ELE_CERT_DEDUCT_CODE:
                        if ($v['account_id'] == $value['accountId']) {
                            $list['ele_cert_deduct'] = $v['expense'];
                        }
                        break;
                    default:
                        break;
                }
            }
            $resList[] = $list;
        }

        return $resList;
    }
    /**
     * 处理单个用户财务费用项数据
     * <AUTHOR>
     * @param array $userInfo  用户信息
     * @param string $beginTime  开始时间
     * @param string $endTime  结束时间
     * @date   2020-05-20
     * @return array
     */
    private function _handleOneUserTradeInfo($userInfo, $beginTime, $endTime)
    {
        if (empty($userInfo)) {
            throw new \Exception("列表为空");
        }
        //获取用户基本信息
        $accountId  = $userInfo['account_id'];
        $memberId =   $userInfo['id'];
        //获取下用户当前余额
        $accountBookApi = new \Business\JavaApi\Account\AccountBook();
        $result = $accountBookApi->queryOnePlatformBook($memberId);
        if ($result['code'] != 200) {
            throw new \Exception("获取用户余额失败");
        }

        //获取用户信息
        $balanceUser = $result['data'];
        $beginMoneyTime = date('ymd', strtotime('-1 Day',strtotime($beginTime)));
        $endMoneyTime   = date('ymd', strtotime('-1 Day',strtotime($endTime)));
        $beginTime      = date('ymd', strtotime($beginTime));
        $endTime        = date('ymd', strtotime($endTime));
        //获取用户的期初期末余额
        $tradeRecordMdl = new PftTransReport();
        $beginMoney     = $tradeRecordMdl->getUserTradeMoneyByPlatformMoney([$accountId], $beginMoneyTime);
        $endMoney       = $tradeRecordMdl->getUserTradeMoneyByPlatformMoney([$accountId], $endMoneyTime);

        //获取用户的账本
        $platformMoneyList = $tradeRecordMdl->getUserTradeTemplateCodeMoneyList([$accountId], $beginTime, $endTime,
            [BookSubject::PLATFORM_SUBJECT_CODE]);

        //获取短信和电子凭证费的余额
        $biz = new \Business\Finance\AccountMoney();
        $res = $biz->getAccountSmsAndCode($memberId);
        if ($res['code'] != 200){
            throw new \Exception("获取短信费凭证费余额错误");
        }
        $smsAndCodeMoney = $res['data'];
        //获取用户的费用项
        $itemCode   = [
            TemplateItem::PRESTORE_SMS_FEE_CODE,
            TemplateItem::PRESTORE_CERT_FEE_CODE,
            TemplateItem::SMS_DEDUCT_CODE,
            TemplateItem::ELE_CERT_DEDUCT_CODE,
        ];
        $chargeList = $tradeRecordMdl->getUserChargeMoneyList([$accountId], $beginTime, $endTime, $itemCode);
            $list = [
                'id'                => $userInfo['id'],
                'dname'             => $userInfo['dname'],
                'balance_money'     => $balanceUser['money'] ? $balanceUser['money'] : 0,
                'account'           => $userInfo['account'],
                'com_name'          => $userInfo['com_name'],
                'begin_money'       => $beginMoney[0]['end_money'] ?? 0,
                'end_money'         => $endMoney[0]['end_money'] ?? 0,
                'expense'           => $platformMoneyList[0]['expense'] ?? 0,
                'income'            => $platformMoneyList[0]['income'] ?? 0,
                'sms_deduct'        => 0,
                'ele_cert_deduct'   => 0,
                'prestore_cert_fee' => 0,
                'prestore_sms_fee'  => 0,
                'sms_money'         => $smsAndCodeMoney['sms']['lmoney'] ?? 0,
                'code_money'        => $smsAndCodeMoney['code']['lmoney'] ?? 0,
            ];
        foreach ($chargeList as $k => $v) {
            switch ($v['template_item_code']) {
                case TemplateItem::PRESTORE_SMS_FEE_CODE:
                    $list['prestore_sms_fee'] = $v['income'];
                    break;
                case TemplateItem::PRESTORE_CERT_FEE_CODE:
                    $list['prestore_cert_fee'] = $v['income'];
                    break;
                case TemplateItem::SMS_DEDUCT_CODE:
                    $list['sms_deduct'] = $v['expense'];
                    break;
                case TemplateItem::ELE_CERT_DEDUCT_CODE:
                    $list['ele_cert_deduct'] = $v['expense'];
                    break;
                default:
                    break;
            }
        }

        return $list;
    }
    /**
     * 导出用户账户统计
     * <AUTHOR>
     * @param int $dtype  账户类型
     * @param string $begin  开始时间
     * @param string $end  结束时间
     * @param int $ignoreType  类型
     * @param array $resellerArrId  用户id数组
     * @param int $page  页数
     * @param int $size  大小
     * @date   2020-05-20
     * @return array
     */
    public function exportTradeByAdmin($begin, $end, $dtype, $ignoreType, $resellerArrId, $page, $size)
    {
        $memberQueryApi = new MemberQuery();
        $userListRes    = $memberQueryApi->queryMemberNameByDtypeList($dtype, $ignoreType, $resellerArrId, $page, $size);
        if ($userListRes['code'] != 200) {
            return [];
        }
        try {
            $resList = $this->_handleUserTradeList($userListRes['data']['list'], $begin, $end);
        } catch (\Exception $e) {
            return [];
        }

        return $resList;
    }
    /**
     * 获取字段对应的账本编码
     * <AUTHOR>
     * @date   2020-05-20
     * @return array
     */
    public function tradeFieldChange(){
        $changeField = [
            'expense' => [
                'subject_code' => BookSubject::PLATFORM_SUBJECT_CODE,
            ],
            'income'  => [
                'subject_code'  => BookSubject::PLATFORM_SUBJECT_CODE,
            ],
            'sms_deduct' => [
                'item_code'     => TemplateItem::SMS_DEDUCT_CODE
            ],
            'ele_cert_deduct' => [
                'item_code'     => TemplateItem::ELE_CERT_DEDUCT_CODE
            ],
            'prestore_cert_fee' => [
                'item_code'    => TemplateItem::PRESTORE_CERT_FEE_CODE,
            ],
            'prestore_sms_fee'  => [
                'item_code'    => TemplateItem::PRESTORE_SMS_FEE_CODE,
            ],
            'sms_money'         => [
                'subject_code'  => BookSubject::SMS_SPECIAL_SUBJECT_CODE,
            ],
            'code_money'        => [
                'subject_code'  => BookSubject::ELE_CERT_SPECIAL_SUBJECT_CODE,
            ]
        ];
        return $changeField;
    }

    /**
     * 授信记录管理
     * <AUTHOR>
     * @date   2022/3/23
     *
     * @param  string  $type         授信操作类型
     * @param  int     $aid          供应商id
     * @param  string  $beginTime    开始时间
     * @param  string  $endTime      结束时间
     * @param  int     $fid          分销商id
     * @param  string  $orderId      订单号查询
     * @param  int     $page         页码
     * @param  int     $size         页数
     *
     * @return array
     */
    public function getCreditRecordManage(string $type, int $aid, string $beginTime, string $endTime, int $fid = 0, string $orderId = '', int $page = 1, int $size = 15)
    {
        if (!$type || !$page || !$size || !$aid || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        if (!strtotime($beginTime) || !strtotime($endTime)) {
            return $this->returnData(204, '时间参数错误');
        }

        $earlyTime = strtotime('2021-01-01 00:00:00');
        if ($earlyTime > strtotime($beginTime) || $earlyTime > strtotime($endTime)) {
            return $this->returnData(204, '仅支持搜索从2021年1月1日起操作的授信额度调整记录');
        }

        if (!in_array($type, ['preSave', 'adjust'])) {
            return $this->returnData(204, '查询的交易记录类型未定义');
        }
        $items = [];
        switch ($type) {
            case 'preSave':
                $items = [TemplateItem::OFFLINE_REFUND_CODE, TemplateItem::REFUND_CREDIT_RECHARGE_CODE];
                break;
            case 'adjust':
                $items = [TemplateItem::ADJUST_CREDIT_LINE];
                break;
        }

        $beginTime = date("Y-m-d 00:00:00", strtotime($beginTime));
        $endTime   = date("Y-m-d 23:50:59", strtotime($endTime));

        $queryRes = $this->container['trade_api']->creditListQueryPage($beginTime, $endTime, $items, $fid, $aid,
            $orderId, $page, $size);
        if ($queryRes['code'] == 200) {
            //获取操作人姓名
            $opArr     = array_unique(array_column($queryRes['data']['list'] ?? [], 'opid'));
            $fidArr    = array_unique(array_column($queryRes['data']['list'] ?? [], 'fid'));
            $uidArr    = array_unique(array_merge($opArr, $fidArr));
            $opNameMap = $this->container['member_model']->getMemberInfoByMulti($uidArr, 'id', 'id,dname', 1);
            //费用项
            $itemsMap = array_key($this->_getItems(), 'code');
            $list     = [];

            $total = $queryRes['data']['total'] ?? 0;

            foreach ($queryRes['data']['list'] ?? [] as $item) {
                $opName    = '';
                $opAccount = '';
                //是否有权限看
                if (in_array($item['opid'], [$item['fid'], $item['aid']])) {
                    $opName    = $opNameMap[$item['opid']]['dname'] ?? '';
                    $opAccount = $opNameMap[$item['opid']]['account'] ?? '';
                }
                $account   = $opNameMap[$item['fid']]['account'] ?? '';
                $dname     = $opNameMap[$item['fid']]['dname'] ?? '';
                $cname     = $opNameMap[$item['fid']]['cname'] ?? '';
                $ctel      = $opNameMap[$item['fid']]['ctel'] ?? '';
                $list[] = [
                    //操作类型
                    'fee_item'     => $itemsMap[$item['templateItemCode']]['name'],
                    //操作时间
                    'rectime'      => $item['rectime'],
                    //订单号
                    'orderid'      => $item['orderid'],
                    //交易金额
                    'dmoney'       => $item['dmoney'],
                    //余额
                    'lmoney'       => $item['lmoney'],
                    //交易限额
                    'limit'        => $item['tradeLimitMoney'],
                    //交易金额+或- 0表示余额增加1表示余额减少
                    'daction'      => $item['daction'],
                    //已用额度
                    'used_limit'   => $item['usedLimit'],
                    //可用余额
                    'usable_money' => $item['usableMoney'],
                    //剩余额度
                    'remain_limit' => $item['remainLimit'],
                    //操作人名称
                    'oper'         => $opName,
                    //操作人账号
                    'opac'         => $opAccount,
                    //分销商id
                    'fid'          => $item['fid'],
                    //分销商名称
                    'dname'        => $dname,
                    //联系人名称
                    'cname'        => $cname,
                    //分销商账号
                    'account'      => $account,
                    //联系人电话
                    'ctel'         => $ctel,
                    //备注
                    'memo'         => $item['memo'],
                ];
            }

            return $this->returnData(200, '', ['list' => $list, 'total' => $total]);
        } else {
            return $this->returnData(200, '', ['list' => [], 'total' => 0]);
        }
    }

    /**
     * 授信记录汇总接口
     * <AUTHOR>
     * @date   2022/5/12
     *
     * @param  string  $type         授信操作类型
     * @param  int     $aid          供应商id
     * @param  string  $beginTime    开始时间
     * @param  string  $endTime      结束时间
     * @param  int     $fid          分销商id
     *
     * @return array
     */
    public function getRecordSummary(string $type, int $aid, string $beginTime, string $endTime, int $fid)
    {
        if (!$type || !$aid || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        if (!strtotime($beginTime) || !strtotime($endTime)) {
            return $this->returnData(204, '时间参数错误');
        }

        $earlyTime = strtotime('2021-01-01 00:00:00');
        if ($earlyTime > strtotime($beginTime) || $earlyTime > strtotime($endTime)) {
            return $this->returnData(204, '仅支持汇总从2021年1月1日起操作的授信额度调整记录');
        }

        if (!in_array($type, ['preSave', 'adjust'])) {
            return $this->returnData(204, '查询的交易记录类型未定义');
        }
        $itemCode    = [];
        $subjectCode = [];
        switch ($type) {
            case 'preSave':
                $itemCode    = [TemplateItem::OFFLINE_REFUND_CODE, TemplateItem::REFUND_CREDIT_RECHARGE_CODE];
                $subjectCode = [BookSubject::CREDIT_SUBJECT_CODE, BookSubject::ADJUST_CREDIT_SUBJECT_CODE];
                break;
            case 'adjust':
                $itemCode    = [TemplateItem::ADJUST_CREDIT_LINE];
                $subjectCode = [BookSubject::CREDIT_SUBJECT_CODE, BookSubject::ADJUST_CREDIT_SUBJECT_CODE];
                break;
        }

        $beginTime = date("Y-m-d 00:00:00", strtotime($beginTime));
        $endTime   = date("Y-m-d 23:50:59", strtotime($endTime));

        $option = [
            'order_id' => '',
            'trade_no' => '',
            'aid'      => $aid,
        ];

        $data = $this->_summaryForMember($fid, $beginTime, $endTime, $subjectCode, $itemCode, -1, $option);

        return $this->returnData(200, '', $data);
    }

    private function _parseConditionForHotData($beginTime, $endTime, $subjectCode, $itemCode, $tradeType, $option)
    {
        $condition = [
            'beginDateStr' => $beginTime,
            'endDateStr'   => $endTime,
        ];
        if ($itemCode) {
            $condition['templateItemCodes'] = is_array($itemCode) ? $itemCode : [$itemCode];
        }
        if ($subjectCode) {
            $condition['subjectCodes'] = is_array($subjectCode) ? $subjectCode : [$subjectCode];
        }
        if ($option['order_id']) {
            $condition['orderIds'] = [$option['order_id']];
        }
        if ($option['trade_no']) {
            $condition['tradeNos'] = [$option['trade_no']];
        }
        if (!empty($option['fAccountId'])) {
            $condition['accountId'] = $option['fAccountId'];
        }
        if (!empty($option['aAccountId'])) {
            $condition['tradeAccountId'] = $option['aAccountId'];
        }
        if (!empty($option['tradeScope'])) {
            $condition['tradeScope'] = $option['tradeScope'];
        }
        if ($tradeType != -1) {
            $condition['daction'] = $tradeType;
        }

        return $condition;
    }

    /**
     * 交易记录热数据返回参数格式兼容
     * <AUTHOR>
     * @date   2024/11/20
     *
     * @param $data
     *
     * @return array|mixed
     */
    private function _hotDataFormatCompatible($data)
    {
        //格式兼容下
        if (!empty($data) && is_array($data)) {
            $data = array_map(function ($item) {
                //兼容下之前的时间戳格式
                isset($item['rectime']) && $item['rectime'] = strtotime($item['rectime']);
                isset($item['tradeTime']) && $item['tradeTime'] = strtotime($item['tradeTime']);
                return $item;
            }, $data);
        }

        return $data;
    }
}
