<?php
/**
 * 报表查询基础
 * <AUTHOR>
 * @date   2023/10/16
 */

namespace Business\Report;

use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\AuthContext as AuthContextBiz;
use Model\Report\ReportSummary as ReportSummaryModel;
use Business\Base;

class ReportBase extends Base
{

    //报表模型
    private $_statisModel = null;
    //归档模型
    private $_archiverModel = null;
    //归档数据标识
    private $_archiverDb = false;
    // 归档年份标识
    private $_yearMark = '';

    //每年归档操作日期(到每年的7月1号的时候，将去年一整年的数据进行归档)
    const ARCHIVER_ACTION_DATE = '0701';

    /**
     * 判断能否查询日期参数间的报表数据
     * <AUTHOR>
     * @date   2021/7/21
     *
     * @param $begin
     * @param $end
     *
     * @return bool
     * @throws
     */
    public function checkDateAllowQuery($begin, $end)
    {
        //被归档的年份不能进行跨年查询
        $isAllow = $this->dateAllowQuery($begin, $end);
        if (!$isAllow) {
            throw new \Exception("不支持该时间段内的查询");
        }

        return true;
    }

    /**
     * 判断能否查询日期参数间的报表数据
     * <AUTHOR>
     * @date   2019-01-28
     */
    public function dateAllowQuery($startDate, $endDate, $launchDateYear = '2016')
    {
        $currentYear = date('Y');
        $startYear   = date('Y', strtotime($startDate));

        //当前历史数据只有到2016年为止
        if ((int)$startYear < $launchDateYear) {
            return false;
        }

        $endYear = date('Y', strtotime($endDate));
        $isAllow = false;

        //查询几年前的数据
        $diffYear = $currentYear - $startYear;
        //去年数据是否归档
        $isArchiverLastYear = $this->_isArchiverLastYear();

        //查询的数据未跨年
        if ($endYear == $startYear) { //非今年数据

            //查询去年数据
            if (1 == $diffYear) {
                //去年数据是否归档
                $this->_archiverDb = $isArchiverLastYear;
            }

            //查询去年之前数据
            if (1 < $diffYear) {
                $this->_archiverDb = true;
            }

            $this->_yearMark = $startYear;
            $isAllow         = true;

        } else { //跨年查询:被归档的年份前端不能进行跨年查询

            if ($endYear == $currentYear && 1 == $diffYear) {//仅支持的跨年查询
                //去年数据是否归档
                $this->_archiverDb = $isArchiverLastYear;
                //去年报表数据已被归档则不支持跨年查询
                !$isArchiverLastYear && $isAllow = true;
            }
        }

        return $isAllow;
    }

    /**
     * 去年报表数据是否归档
     * <AUTHOR>
     * @date   2019-01-28
     */
    private function _isArchiverLastYear()
    {
        return date('md') >= self::ARCHIVER_ACTION_DATE ? true : false;
    }

    /**
     * 选择报表模型
     * <AUTHOR>
     * @date   2019-01-28
     */
    public function getModel()
    {
        return $this->_archiverDb ? $this->_getArchiverModel() : $this->_getStatisModel();
    }

    /**
     * 归档报表模型
     * <AUTHOR>
     * @date   2019-01-28
     */
    private function _getArchiverModel()
    {
        //归档库
        if (!$this->_archiverModel) {
            $this->_archiverModel = new \Model\Report\Statistics('summary_history', $this->_yearMark);
        }

        return $this->_archiverModel;
    }

    /**
     * 报表模型
     * <AUTHOR>
     * @date   2019-01-28
     */
    private function _getStatisModel()
    {
        if (!$this->_statisModel) {
            $this->_statisModel = new \Model\Report\Statistics();
        }

        return $this->_statisModel;
    }

    /**
     * 判断报表是否进行取票统计
     * <AUTHOR>
     * @date   2020/8/25
     *
     * @return bool
     */
    public function judgePrintNum()
    {
        //归档处理 ->归档移除 2020之前的移除取票统计
        if (empty($this->_yearMark) || $this->_yearMark >= 2020) {
            return true;
        }

        return false;
    }

    /**
     * 根据模板设置的产品和请求的产品范围参数获取查询的产品id  (参数 pid 和 notPid 至少有一个为空)
     *  返回值说明
     *  not_pid => [1,2,3]      查询的pid参数不包含值中的产品id
     *  pid     => [1,2,3]      查询的pid参数只包含值中的产品id
     *  not_pid 和 pid 只会返回一种类型
     *
     * <AUTHOR>
     * @date   2021/07/14
     *
     * @param  string  $productScope    产品范围 1=在售的产品 2=下架的产品 6=已删除的产品 -1=不限
     * @param  string  $pid             报表模板中特殊维度设置的查询特定产品id
     * @param  string  $notPid          报表模板中特殊维度设置的不查询的特定产品id
     *
     * @return  array
     */
    public function getQueryPidByScope(int $sid, string $productScope, string $pid, string $notPid)
    {
        if (!$productScope || $productScope == -1) {
            //搜索所有的产品 模板中是否有特殊维度设置的排除产品
            $return = empty($notPid) ? ['pid' => $pid] : ['not_pid' => $notPid];

            return $return;
        }

        $cacheKey   = "pft001:statis_report:pid_data_" . $sid . '_' . $productScope;
        $cache      = \Library\Cache\Cache::getInstance('redis');
        $cacheValue = $cache->get($cacheKey);
        if ($cacheValue) {
            $tmpData = json_decode($cacheValue, true);

        } else {
            $state   = explode(',', $productScope);
            $javaApi = new \Business\JavaApi\Product\EvoluteListQuery();
            $result  = $javaApi->queryEvoluteProductByFid($sid, $state, '', 1, 10000, ['Q']);
            if (isset($result['list'])) {
                $tmpData = array_column($result['list'], 'pid');
                $cache->set($cacheKey, json_encode($tmpData));
                $cache->expire($cacheKey, 60);
            }
        }

        $pidArr = [];
        if ($notPid) {
            //需要排除模板勾选的排除的产品
            $notPidArr = explode(',', $notPid);
            foreach ($tmpData as $tmpPid) {
                !in_array($tmpPid, $notPidArr) && $pidArr[] = $tmpPid;
            }

        } elseif ($pid) {
            //需要校验特殊维度设置的产品是否在勾选的产品范围内
            $pidList = explode(',', $pid);
            foreach ($pidList as $tmpPid) {
                in_array($tmpPid, $tmpData) && $pidArr[] = $tmpPid;
            }

        } else {
            $pidArr = $tmpData;
        }

        if (empty($pidArr)) {
            return ['pid' => -1];
        } else {
            return ['pid' => implode(',', $pidArr)];
        }
    }

    /**
     * 根据查询的报表模板和参数生成查询参数
     *
     * <AUTHOR>
     * @date   2021/07/14
     *
     * @param  array  $itemValue
     * @param  array  $itemNotValue
     *
     * @return  array
     * @throws
     */
    public function handleSearchCondition(array $params, array $loginInfo, array $itemValue, array $itemNotValue)
    {
        $resellerId = $resellerGroup = $pid = $lid = $tid = $operateId = $channel = $payWay = $siteId = $cardType = $cardPid = $terminalType = $oid = $notPid = $notTid = $venueId = '';

        if (!empty($itemValue) || !empty($itemNotValue)) {
            list($resellerId, $resellerGroup, $pid, $lid, $tid, $operateId, $channel, $payWay, $siteId, $cardType, $cardPid, $terminalType, $oid, $notPid, $notTid, $venueId) = $this->templateValueMap($itemValue,
                $itemNotValue);
        }

        if ($this->reportSearchCondition($params)) {

            $lidParam = $params['lid'] ?? '';
            //产品id
            $pidParam = $params['pid'] ?? '';
            //门票id
            $tidParam = $params['tid'] ?? '';
            //分销商id
            $resellerIdParam = $params['reseller_id'] ?? '';
            // 分销商组查看
            $resellerGroupParam = $params['reseller_group'] ?? '';
            //渠道
            $channelParam = strval(trim($params['channel'] ?? ''));
            //操作员id
            $operateIdParam = $params['operate_id'] ?? '';
            //支付方式
            $payWayParam = strval(trim($params['pay_way'] ?? ''));
            //卡类型
            $cardTypeParam = $params['card_type'] ?? '';
            //卡明细
            $cardPidParam = $params['card_pid'] ?? '';
            //终端类型
            $terminalTypeParam = strval(trim($params['terminal_type'] ?? ''));
            //分账对象
            $oidParam = $params['oid'] ?? '';
            //站点ID
            $siteIdParam = $params['site_id'] ?? '';
            //场馆id
            $venueId = $params['venue_id'] ?? '';

            // 通过分销商数组获取分销商id
            $resellerGroupParam = $this->getResellerByGroup($resellerGroupParam);

            ///数据权限范围
            $tmpOperateId    = $operateIdParam ?: $operateId;
            $returnOperateId = $this->_operateorFilter($tmpOperateId, $loginInfo);

            return [
                $resellerIdParam ?: $resellerId,
                $resellerGroupParam ?: $resellerGroup,
                $pidParam ?: $pid,
                $lidParam ?: $lid,
                $tidParam ?: $tid,
                $returnOperateId,
                $channelParam != '' ? $channelParam : $channel,
                $payWayParam != '' ? $payWayParam : $payWay,
                $siteIdParam ?: $siteId,
                $cardTypeParam ?: $cardType,
                $cardPidParam ?: $cardPid,
                $terminalTypeParam ?: $terminalType,
                $oidParam ?: $oid,
                $notPid,
                $notTid,
                $venueId,
            ];

        } else {
            $operateId = $this->_operateorFilter($operateId, $loginInfo);

            return [
                $resellerId,
                $resellerGroup,
                $pid,
                $lid,
                $tid,
                $operateId,
                $channel,
                $payWay,
                $siteId,
                $cardType,
                $cardPid,
                $terminalType,
                $oid,
                $notPid,
                $notTid,
                $venueId,
            ];
        }
    }

    /**
     * 映射模板默认值
     * <AUTHOR>
     * @date   2018-07-30
     *
     * @param  array  $itemValue       模板基础值
     * @param  array  $itemNotValue    特殊维度反向定制，查询的维度不包含该字段内的数据
     *
     * @return array
     * @throws
     */
    private function templateValueMap($itemValue, $itemNotValue = [])
    {
        $reseller      = '';
        $resellerGroup = '';
        $pid           = '';
        $lid           = '';
        $tid           = '';
        $oid           = '';
        $operateId     = '';
        $channel       = '';
        $payWay        = '';
        $cardType      = '';
        $terminalType  = '';
        $notPid        = '';
        $notTid        = '';
        $cardPid       = '';
        $siteId        = false;
        $venueId       = '';

        foreach ($itemNotValue as $key => $value) {
            switch ($key) {
                case 'production':
                    $notPid = implode(',', array_column($value, 'id'));
                    break;
                case 'ticket':
                    $notTid = implode(',', array_column($value, 'id'));
                    break;
            }
        }

        foreach ($itemValue as $key => $value) {
            switch ($key) {
                // 分销商
                case 'distor':
                    $reseller = implode(',', array_column($value, 'id'));
                    break;
                case 'distor_group':
                    $groupIdArr    = array_column($value, 'id');
                    $resellerGroup = $this->getResellerByGroup(implode(',', $groupIdArr));
                    break;
                case 'production':
                    $pid = implode(',', array_column($value, 'id'));
                    break;

                case 'scenic':
                    $lid = implode(',', array_column($value, 'id'));
                    break;
                case 'ticket':
                    $tid = implode(',', array_column($value, 'id'));
                    break;
                case 'conductor':
                    $operateId = implode(',', array_column($value, 'id'));
                    break;
                case 'channel':
                    $channel = implode(',', array_column($value, 'id'));
                    break;
                case 'pay_way':
                    $payWay = implode(',', array_column($value, 'id'));
                    break;
                case 'site_id':
                    $siteId = implode(',', array_column($value, 'id'));
                    break;
                case 'card_type':
                    $cardType = implode(',', array_column($value, 'id'));
                    break;
                case 'card_pid':
                    $cardPid = implode(',', array_column($value, 'id'));
                    break;
                case 'terminal_type':
                    $terminalType = implode(',', array_column($value, 'id'));
                    break;
                case 'oid':
                    $oid = implode(',', array_column($value, 'id'));
                    break;
                case 'venues':
                    $venueId = implode(',', array_column($value, 'id'));
                    break;
            }
        }

        return [
            $reseller,
            $resellerGroup,
            $pid,
            $lid,
            $tid,
            $operateId,
            $channel,
            $payWay,
            $siteId,
            $cardType,
            $cardPid,
            $terminalType,
            $oid,
            $notPid,
            $notTid,
            $venueId
        ];
    }

    /**
     * 报表判断是否存在搜索条件
     * <AUTHOR>
     * @date   2018-05-30
     */
    private function reportSearchCondition($params)
    {
        //景区id
        $lid = $params['lid'] ?? '';
        //产品id
        $pid = $params['pid'] ?? '';
        //门票id
        $tid = $params['tid'] ?? '';
        //分销商id
        $resellerId = $params['reseller_id'] ?? '';
        //渠道
        $channel = strval(trim($params['channel'] ?? ''));
        //操作员id
        $operateId = $params['operate_id'] ?? '';
        //支付方式
        $payWay = strval(trim($params['pay_way'] ?? ''));
        //卡类型
        $cardType = $params['card_type'] ?? '';
        //卡明细
        $cardPid = $params['card_pid'] ?? '';
        //终端类型
        $terminalType = strval(trim($params['terminal_type'] ?? ''));
        //分账对象
        $oid = $params['oid'] ?? '';
        //站点ID
        $siteId = $params['site_id'] ?? '';

        $resellerGroup = $params['reseller_group'] ?? '';
        //场馆id
        $venueId = $params['venue_id'] ?? '';

        if ($lid || $pid || $tid || $resellerGroup || $resellerId || $channel !== '' || $operateId || $payWay !== '' || $cardType || $cardPid || $terminalType !== '' || $oid || $siteId || $venueId) {
            return true;
        }

        return false;
    }

    /**
     * 数据权限的校验
     * <AUTHOR>
     * @date   2019-05-21
     *
     * @return object
     */
    private function _operateorFilter($operateor, $loginInfo)
    {
        $hasAllScope = (new AuthLogicBiz())->memberDataResource($loginInfo['sid'], $loginInfo['memberID'],
                $loginInfo['dtype']);
        if (!$hasAllScope) {
            //只有个人数据权限的只能看自己的数据
            $operateor = $loginInfo['memberID'];
        }

        return $operateor;
    }

    /**
     * 获取分销商分组
     * <AUTHOR>
     * @date   2023/10/16
     *
     * @param  string  $resellerGroup
     *
     * @return string
     * @throws
     */
    private function getResellerByGroup($resellerGroup)
    {
        if (empty($resellerGroup)) {
            return '';
        }

        $groupIdArr = explode(',', $resellerGroup);

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds($groupIdArr);
        if ($groupFidRes['code'] == 200 && !empty($groupFidRes['data'])) {
            $resellerGroup = implode(',', array_column($groupFidRes['data'], 'fid'));
        } else {
            throw new \Exception("暂无数据");
        }

        return $resellerGroup;
    }

    /**
     * 新版报表要求统计灵活，动态拼接excel title
     * <AUTHOR>
     *
     * @param $head
     * @param $ex
     * @param $offset
     *
     * @return mixed
     */
    public function reportNewHeadMerge($head, $ex, $offset)
    {
        array_splice($head, $offset, 0, $ex);
        return $head;
    }

    /**
     * 获取归档数据标识
     * <AUTHOR>
     * @date   2023/10/16
     *
     * @return bool
     */
    public function getArchiverDb()
    {
        return $this->_archiverDb;
    }

    /**
     * 获取归档年份标识
     * <AUTHOR>
     * @date   2023/10/16
     *
     * @return bool
     */
    public function getYearMark()
    {
        return $this->_yearMark;
    }

    /**
     * 获取报表模板详情
     * <AUTHOR>
     * @date   2023/10/31
     *
     * @param int $fid
     * @param int $configId
     *
     * @return array
     * @throws
     */
    public function getTemplateDetail($fid, $configId)
    {
        if (!$fid || !$configId) {
            throw new \Exception("模板配置未知");
        }

        $templateModel   = new \Model\Report\Template();

        $template = $templateModel->getTemplateById($fid, $configId);
        if (empty($template)) {
            throw new \Exception("模板配置未知");
        }

        //统计纬度
        $item = json_decode($template['item'], true);
        if (empty($item) || !is_array($item)) {
            throw new \Exception("配置有误, 统计纬度不能为空");
        }

        // 判断模板是否有设置默认参数，同时无搜索条件
        $itemValue    = json_decode($template['item_value'], true);
        $itemNotValue = json_decode($template['item_not_value'], true);

        empty($itemValue) && $itemValue = [];
        empty($itemNotValue) && $itemNotValue = [];

        //报表名称
        $name = $template['name'];

        //统计指标
        $target = json_decode($template['target'], true);
        if (empty($target) || !is_array($target)) {
            $target = [];
        }

        //统一格式化返回
        return [
            'name'           => $name,
            'item'           => $item,
            'target'         => $target,
            'item_value'     => $itemValue,
            'item_not_value' => $itemNotValue,
        ];
    }

    /**
     * 票类范围（门票tid）
     * <AUTHOR>
     * @date   2023/11/6
     *
     * @param  int     $sid
     * @param  string  $productScope
     * @param  string  $tid
     *
     * @return array|int[]|string[]
     */
    public function getQueryTidByScope(int $sid, string $productScope, string $tid = '', string $notTid = '')
    {
        if (!$productScope || $productScope == -1) {
            return ['tid' => $tid, 'notTid' => $notTid];
        }
        $cacheKey   = "pft001:statis_report:tid_data_" . $sid . '_' . $productScope;
        $cache      = \Library\Cache\Cache::getInstance('redis');
        $cacheValue = $cache->get($cacheKey);
        $tmpData = [];
        if ($cacheValue) {
            $tmpData = json_decode($cacheValue, true);
        } else {
            $state   = explode(',', $productScope);
            $javaApi = new \Business\JavaApi\Product\EvoluteListQuery();
            $result  = $javaApi->getAllEvoluteProduct($sid, $state, '', 1, 10000, ['Q']);
            if (isset($result['list'])) {
                $tmpData = array_column($result['list'], 'tid');
                $cache->set($cacheKey, json_encode($tmpData));
                $cache->expire($cacheKey, 60);
            }
        }
        if ($notTid) {
            $notTidList = explode(',', $notTid);
            $tidArr = array_diff($tmpData, $notTidList);
        } else if ($tid) {
            //需要校验特殊维度设置的产品是否在勾选的产品范围内
            $tidList = explode(',', $tid);
            $tidArr = array_intersect($tmpData, $tidList);
        } else {
            $tidArr = $tmpData;
        }
        if (empty($tidArr)) {
            return ['tid' => -1];
        } else {
            return ['tid' => implode(',', $tidArr), 'notTid' => ''];
        }
    }

    /**
     * 判断日期区间是否超过指定天数
     * <AUTHOR>
     * @date   2024/1/11
     *
     * @param  string  $beginDate
     * @param  string  $endDate
     * @param  int     $days
     *
     * @return bool
     */
    public static function isRangeOverDays(string $beginDate, string $endDate, int $days = 30)
    {
        $beginTime = strtotime($beginDate);
        $endTime   = strtotime($endDate);
        $diffTime   = (int)$endTime - (int)$beginTime;
        $diffDays   = floor($diffTime / (24 * 3600));

        return $diffDays > $days;
    }

    /**
     * 获取报表汇总模型
     * <AUTHOR>
     * @date   2024/05/04
     *
     * @return ReportSummaryModel
     */
    public function getReportSummaryModel(): ReportSummaryModel
    {
        if ($this->_archiverModel) {
            return new ReportSummaryModel('summary_history', $this->_yearMark);
        }

        return new ReportSummaryModel();
    }

    /**
     * 刪除报表查询模板
     * 原方法：\Controller\report\statistics::deleteTemplate
     * <AUTHOR>
     * @date   2024/07/05
     *
     * @param  int  $fid           主账号id
     * @param  int  $memberId      登录账号id
     * @param  int  $reportType    报表类型
     * @param  int  $templateId    模板id
     *
     * @return array
     */
    public function deleteTemplate($fid, $memberId, $reportType, $templateId)
    {
        if (!$fid || !$memberId || !$reportType || !$templateId) {
            return $this->returnDataV2(400, [], '删除失败，参数错误');
        }

        $templateModel = new \Model\Report\Template();
        $templateInfo  = $templateModel->getTemplateById(0, $templateId);
        if (empty($templateInfo)) {
            return $this->returnDataV2(400, [], '未找到该条记录');
        }
        if ($templateInfo['temp_type'] == $templateModel::TEMP_TYPE_SYS) {
            return $this->returnDataV2(400, [], '该模板为系统模板，不允许账号进行删除操作');
        }
        if ($templateInfo['is_share'] == $templateModel::IS_SHARE && $memberId != $fid) {
            return $this->returnDataV2(400, [], '该模板为共享模板，只允许主账号进行删除操作');
        }

        if ($fid != $templateInfo['fid']) {
            return $this->returnDataV2(400, [], '非法操作');
        }

        $res = $templateModel->deleteTemplateConfig($memberId, $templateId, $reportType);

        if (empty($res)) {
            return $this->returnDataV2(400, [], '删除失败');
        }

        return $this->returnDataV2(200, [], '删除成功');
    }

}