<?php
/**
 * 预订验证分钟报表
 * <AUTHOR>
 * @date   2023/10/17
 */

namespace Business\Report;

use Business\Authority\DataAuthLimit;
use Business\Authority\DataAuthLogic;
use Business\Base;
use Business\Report\ReportBase as ReportBaseBiz;
use Business\Statistics\StatisticsAuth as StatisticsAuthBiz;
use Business\Statistics\StatisticsHandle as StatisticsHandleBiz;

class MinuteReport extends Base
{
    const ALLOW_MEMBER_IDS = ENV == 'PRODUCTION' ? [
        //测试用户id
        28459860,
        27379841,
        27379854,
        27379871,
        33000294,
        33000356,
        13701405,
        12772761,
        //瘦西湖id
        29821585,
        //黄姚古镇
        32991419,
        //茶卡盐湖71025898
        37045537,
        //西矿智旅5916699
        6092214,
    ] : (ENV == 'TEST' ? [
        3263539,
        3263540,
        3263541,
        3263542,
        3265378,
        3265380,
        3250235,
        3262552,
        3265372,
        3266712,
        6970,
        3385,
    ] : [
        3385,
        6970,
    ]);

    /**
     * 预订报表（分钟）
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param  array  $params          请求参数集
     * @param  array  $getLoginInfo    登录信息
     * @param  DataAuthLimit $dataAuthLimit 数据权限
     * @return array
     */
    public function minuteOrderTwoList(array $params, array $getLoginInfo, DataAuthLimit $dataAuthLimit)
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            //开始时间
            $begin = $params['begin'] ?? '';
            //结束时间
            $end = $params['end'] ?? '';
            //模板id
            $configId = $params['search_config_id'] ?? '';
            // 只看转分销， 如果是用户则只看转分销， 如果是管理员 则包含所有level
            $onlyLevel = $params['only_level'] ?? '';
            //页数
            $page = intval($params['page'] ?? 1);
            //每页数量
            $pageSize = intval($params['page_size'] ?? 10);

            //产品范围 1=在售的产品 2=下架的产品 6=已删除的产品 -1=不限
            $productScope = strval($params['product_scope'] ?? '-1');
            //员工状态范围    0=正常 1=禁用 2=已删除 -1=不限
            $operatorScope = strval($params['operator_scope'] ?? '-1');

            //是否展示打包方的子票订单 0不展示 1展示
            $showSelfSonTicket = intval($params['show_self_son_ticket'] ?? 0);//套票子票
            $showBindSonTicket = intval($params['show_bind_son_ticket'] ?? 0);//演出捆绑票子票
            $extFilter = [
                'show_self_son_ticket' => $showSelfSonTicket,//套票子票
                'show_bind_son_ticket' => $showBindSonTicket,//捆绑票子票
            ];

            if (strtotime($end)) {
                //查询结束时间右边不包含
                $end = date('Y-m-d H:i:s', strtotime($end) - 1);
            }

            $reportBaseBiz = new ReportBaseBiz();

            //被归档的年份不能进行跨年查询
            $isAllow = $reportBaseBiz->dateAllowQuery($begin, $end, '2023');
            if (!$isAllow) {
                throw new \Exception("不支持该时间段内的查询");
            }

            $fid   = $getLoginInfo['sid'];
            $level = $onlyLevel ? ['NOT IN', [0, 1]] : false;

            //验证是否开通分账销售报表
            $judgeMinuteSalesReport = StatisticsAuthBiz::judgeMinuteSalesReport($fid);
            if (!$judgeMinuteSalesReport) {
                throw new \Exception("当前账号不支持分钟报表查询");
            }

            $templateModel = new \Model\Report\Template();
            $template      = $templateModel->getTemplateById(0, $configId);
            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            // 判断模板是否有设置默认参数，同时无搜索条件
            $itemValue    = json_decode($template['item_value'], true);
            $itemNotValue = json_decode($template['item_not_value'], true);

            empty($itemValue) && $itemValue = [];
            empty($itemNotValue) && $itemNotValue = [];

            list($resellerId, $resellerGroup, $pid, $lid, $tid, $operateId, $channel, $payWay, $siteId, $cardType, $cardPid, $terminalType, $oid, $notPid, $notTid) = $reportBaseBiz->handleSearchCondition($params,
                $getLoginInfo, $itemValue, $itemNotValue);

            //处理下查询的产品参数
            $queryPidData = $reportBaseBiz->getQueryPidByScope($fid, $productScope, $pid, $notPid);
            $pidStr       = $queryPidData['pid'] ?? '';
            $notPid       = $queryPidData['not_pid'] ?? '';

            if ($pidStr == -1) {
                return $this->returnData(200, ['total' => 0, 'list' => []]);
            }

            $queryOperatorData = (new \Business\Statistics\statistics())->getQueryOperatorByScope($fid, $operatorScope,
                $operateId);
            $operateId         = $queryOperatorData['operator'] ?? '';
            if ($operateId == -1) {
                return $this->returnData(200, ['total' => 0, 'list' => []]);
            }

            $item = array_values($item);
            //报表名称
            $name = $template['name'];

            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }

            //不统计取票时移除
            if (!$reportBaseBiz->judgePrintNum()) {
                //移除取票状态
                $rmPrintNum = 'print_num';
                foreach ($target as $k => $v) {
                    if ($v == $rmPrintNum) {
                        unset($target[$k]);
                    }
                }
            }

            $group = '';
            if (!empty($item)) {
                $group = implode(",", $item);
            }

            $notInArr = [
                'not_tid' => $notTid,
                'not_pid' => $notPid,
            ];

            $res = [];
            $lid = is_array($lid) ? $lid : ($lid ? explode(',', $lid) : []);
            //数据权限过滤
            $dataAuthCondition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lid]);
            if ($dataAuthCondition !== false) {
                if (isset($dataAuthCondition['lidList'])) {
                    $lid = $dataAuthCondition['lidList'];
                }
                if (isset($dataAuthCondition['notLidList'])) {
                    $notInArr['not_lid'] = $dataAuthCondition['notLidList'];
                }
                $statisModel = $reportBaseBiz->getModel();

                $res = $statisModel->getMinuteOrderV2List($begin, $end, $fid, $lid, $pidStr, $tid, $resellerGroup,
                    $resellerId, $channel, $operateId, $payWay, $group, $level, $page, $pageSize, $siteId, $notInArr,
                    $extFilter);
            }

            if (empty($res)) {
                return $this->returnData(200, ['total' => 0, 'list' => []]);
            }

            //输出格式要出处理下，部分返回[]
            $list  = $res['list'] ?? [];
            $total = $res['total'] ?? 0;
            $sum   = $res['sum'] ?? [];

            //展示日期格式处理下
            foreach ($list as &$value) {
                if (!isset($value['date'])) {
                    continue;
                }

                $value['date'] = $this->_dateToRange($begin, $end, $value['date']);
            }
            unset($value);

            $statisticsHandleBiz = new StatisticsHandleBiz();

            //列表抬头字段 key->val
            $confName = 'minuteOrderTwo';
            //获取配置
            $reportConf = load_config($confName, 'report_list');
            $targetItem = $reportConf['target_item'] ?? [];
            $unit       = array_keys($reportConf['unit'] ?? []);

            //需要多返回的字段
            $otherField = [];

            //数据结构处理
            $newList = $statisticsHandleBiz->handleReportList($list, $item, $target, $fid,
                StatisticsHandleBiz::ORDER_TWO_RULE, $otherField);

            //统计维度字段名称转换
            $itemArr = $statisticsHandleBiz->changeFieldName($item);

            //处理总和
            $sumNew['order_ticket']  = $sum['order_ticket'];
            $sumNew['cancel_ticket'] = $sum['cancel_ticket'];
            $sumNew['revoke_ticket'] = $sum['revoke_ticket'];
            $sumNew['service_money'] = $sum['service_money'] / 100;

            //售后字段汇总
            $sumNew['after_sale_ticket_num']   = intval($sum['after_sale_ticket_num']);
            $sumNew['after_sale_refund_money'] = round($sum['after_sale_refund_money'] / 100, 2);
            $sumNew['after_sale_income_money'] = round($sum['after_sale_income_money'] / 100, 2);

            //是否展示取票统计
            if ($reportBaseBiz->judgePrintNum()) {
                //取票数总和
                $sumNew['print_num'] = $sum['print_num'];
            }

            $sumNew['real_ticket']       = $sum['order_ticket'] - $sum['cancel_ticket'] - $sum['revoke_ticket'] - $sum['after_sale_ticket_num'];
            $sumNew['real_settle_money'] = round(($sum['cost_money'] - $sum['cancel_cost_money'] - $sum['revoke_cost_money'] - $sum['after_sale_income_money']) / 100,
                2);
            $sumNew['settle_money']      = round(($sum['cost_money']) / 100, 2);

            $sumNew['real_money'] = round(($sum['sale_money'] - $sum['cancel_sale_money'] - $sum['revoke_sale_money'] - $sum['after_sale_refund_money']) / 100,
                2);
            $sumNew['sale_money'] = $sum['sale_money'] / 100;

            $sumNew['profit'] = round($sumNew['real_money'] - $sumNew['real_settle_money'] + $sumNew['service_money'],
                2);

            //需要展示的全部字段需要按用户选择排序下
            $fieldTitle = $statisticsHandleBiz->handleListHeadFieldSort($targetItem, array_values($itemArr), $target);

            //过滤非必要展示的汇总字段
            list($sumNew) = $statisticsHandleBiz->handleBackDataFormat([$sumNew], $target);

            //按统计维度重新排序下 子票维度 需要递增排序（主票子票为0优先展示）， 其他则递减排序
            $newList = $statisticsHandleBiz->handleDataMultiSort($newList, $item, SORT_ASC);

            //格式化返回格式
            $data = $statisticsHandleBiz->handleListDataFormat($newList, $total, $sumNew, $fieldTitle, $itemArr,
                $target, $unit, $name);

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 验证报表（分钟）
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param  array  $params          请求参数集
     * @param  array  $getLoginInfo    登录信息
     *
     * @return array
     */
    public function minuteCheckTwoList(array $params, array $getLoginInfo, DataAuthLimit $dataAuthLimit)
    {
        $code = 200;
        $data = [];
        $msg  = '';

        try {
            //开始时间
            $begin = $params['begin'] ?? '';
            //结束时间
            $end = $params['end'] ?? '';
            //模板id
            $configId = $params['search_config_id'] ?? '';
            // 只看转分销， 如果是用户则只看转分销， 如果是管理员 则包含所有level
            $onlyLevel = $params['only_level'] ?? '';
            //页数
            $page = intval($params['page'] ?? 1);
            //每页数量
            $pageSize = intval($params['page_size'] ?? 10);

            //产品范围 1=在售的产品 2=下架的产品 6=已删除的产品 -1=不限
            $productScope = strval($params['product_scope'] ?? '-1');
            //员工状态范围    0=正常 1=禁用 2=已删除 -1=不限
            $operatorScope = strval($params['operator_scope'] ?? '-1');

            //是否展示打包方的子票订单 0不展示 1展示
            $showSelfSonTicket = intval($params['show_self_son_ticket'] ?? 0);//套票子票
            $showBindSonTicket = intval($params['show_bind_son_ticket'] ?? 0);//演出捆绑票子票
            $extFilter = [
                'show_self_son_ticket' => $showSelfSonTicket,//套票子票
                'show_bind_son_ticket' => $showBindSonTicket,//捆绑票子票
            ];

            if (strtotime($end)) {
                //查询结束时间右边不包含
                $end = date('Y-m-d H:i:s', strtotime($end) - 1);
            }

            $reportBaseBiz = new ReportBaseBiz();

            //被归档的年份不能进行跨年查询
            $isAllow = $reportBaseBiz->dateAllowQuery($begin, $end, '2023');
            if (!$isAllow) {
                throw new \Exception("不支持该时间段内的查询");
            }

            $fid   = $getLoginInfo['sid'];
            $level = $onlyLevel ? ['NOT IN', [0, 1]] : false;

            //验证是否开通分账销售报表
            $judgeMinuteSalesReport = StatisticsAuthBiz::judgeMinuteSalesReport($fid);
            if (!$judgeMinuteSalesReport) {
                throw new \Exception("当前账号不支持分钟报表查询");
            }

            //报表模板数据模型
            $templateModel = new \Model\Report\Template();
            $template      = $templateModel->getTemplateById(0, $configId);
            if (empty($template)) {
                throw new \Exception("模板配置未知");
            }

            //统计纬度
            $item = json_decode($template['item'], true);
            if (empty($item) || !is_array($item)) {
                throw new \Exception("配置有误, 统计纬度不能为空");
            }

            // 判断模板是否有设置默认参数，同时无搜索条件
            $itemValue    = json_decode($template['item_value'], true);
            $itemNotValue = json_decode($template['item_not_value'], true);

            empty($itemValue) && $itemValue = [];
            empty($itemNotValue) && $itemNotValue = [];

            list($resellerId, $resellerGroup, $pid, $lid, $tid, $operateId, $channel, $payWay, $siteId, $cardType, $cardPid, $terminalType, $oid, $notPid, $notTid) = $reportBaseBiz->handleSearchCondition($params,
                $getLoginInfo, $itemValue, $itemNotValue);

            //处理下查询的产品参数
            $queryPidData = $reportBaseBiz->getQueryPidByScope($fid, $productScope, $pid, $notPid);
            $pidStr       = $queryPidData['pid'] ?? [];
            $notPid       = $queryPidData['not_pid'] ?? [];

            if ($pidStr == -1) {
                return $this->returnData(200, ['total' => 0, 'list' => []]);
            }

            $queryOperatorData = (new \Business\Statistics\statistics())->getQueryOperatorByScope($fid, $operatorScope,
                $operateId);
            $operateId         = $queryOperatorData['operator'] ?? '';
            if ($operateId == -1) {
                return $this->returnData(200, ['total' => 0, 'list' => []]);
            }

            $item = array_values($item);
            //报表名称
            $name = $template['name'];

            //统计指标
            $target = json_decode($template['target'], true);
            if (empty($target) || !is_array($target)) {
                $target = [];
            }

            $group = '';
            if (!empty($item)) {
                $group = implode(",", $item);
            }

            if (in_array('today_ticket', $target) || in_array('not_today_ticket', $target)) {
                $group .= ',today_check';
            }

            $notInArr = [
                'not_tid' => $notTid,
                'not_pid' => $notPid,
            ];

            $res = [];
            $lid = is_array($lid) ? $lid : ($lid ? explode(',', $lid) : []);
            //数据权限过滤
            $dataAuthCondition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lid]);
            if ($dataAuthCondition !== false) {
                if (isset($dataAuthCondition['lidList'])) {
                    $lid = $dataAuthCondition['lidList'];
                }
                if (isset($dataAuthCondition['notLidList'])) {
                    $notInArr['not_lid'] = $dataAuthCondition['notLidList'];
                }
                $statisModel = $reportBaseBiz->getModel();
                // 日报表数据
                $res = $statisModel->getMinuteCheckV2List($begin, $end, $fid, $lid, $pidStr, $tid, $resellerGroup,
                    $resellerId, $channel, $operateId, $payWay, $group, $level, $page, $pageSize, $siteId, $notInArr,
                    $extFilter);
            }

            $list  = $res['list'] ?? [];
            $total = $res['total'] ?? 0;
            $sum   = $res['sum'] ?? 0;

            //展示日期格式处理下
            foreach ($list as &$value) {
                if (!isset($value['date'])) {
                    continue;
                }

                $value['date'] = $this->_dateToRange($begin, $end, $value['date']);
            }
            unset($value);

            $statisticsHandleBiz = new StatisticsHandleBiz();

            //列表抬头字段 key->val
            $confName = 'minuteCheckTwo';
            //获取配置
            $reportConf = load_config($confName, 'report_list');
            $targetItem = $reportConf['target_item'] ?? [];
            $unit       = array_keys($reportConf['unit'] ?? []);

            //需要多返回的字段
            $otherField = [];

            //数据结构处理
            $newList = $statisticsHandleBiz->handleReportList($list, $item, $target, $fid,
                StatisticsHandleBiz::CHECK_TWO_RULE, $otherField);

            //统计维度字段名称转换
            $itemArr = $statisticsHandleBiz->changeFieldName($item);

            //处理总和
            //验证总数
            $sumNew['total_ticket']     = intval($sum['order_ticket']);
            $sumNew['finish_ticket']    = intval($sum['finish_ticket']);
            $sumNew['not_today_ticket'] = array_sum(array_column($newList, 'not_today_ticket')) . "(当前页)";
            $sumNew['today_ticket']     = array_sum(array_column($newList, 'today_ticket')) . "(当前页)";
            $sumNew['revoke_ticket']    = intval($sum['revoke_ticket']);
            $sumNew['service_money']    = round($sum['service_money'] / 100, 2);

            //售后字段汇总
            $sumNew['after_sale_ticket_num']    = intval($sum['after_sale_ticket_num']);
            $sumNew['after_sale_refund_money']    = round($sum['after_sale_refund_money'] / 100, 2);
            $sumNew['after_sale_income_money']    = round($sum['after_sale_income_money'] / 100, 2);

            $sumNew['settle_money']      = round(($sum['cost_money'] + $sum['finish_cost_money']) / 100, 2);
            $sumNew['real_settle_money'] = round(($sum['cost_money'] + $sum['finish_cost_money'] - $sum['revoke_cost_money'] - $sum['after_sale_income_money']) / 100,
                2);

            $sumNew['total_ticket'] = intval($sum['order_ticket']);
            $sumNew['all_ticket']   = intval($sum['order_ticket'] + $sum['finish_ticket'] - $sum['revoke_ticket'] - $sum['after_sale_ticket_num']);

            $sumNew['sale_money'] = round(($sum['sale_money'] + $sum['finish_sale_money']) / 100, 2);
            $sumNew['real_money'] = round(($sum['sale_money'] + $sum['finish_sale_money'] - $sum['revoke_sale_money'] - $sum['after_sale_refund_money']) / 100,
                2);

            $sumNew['profit'] = round($sumNew['real_money'] - $sumNew['real_settle_money'] + $sumNew['service_money'],
                2);

            //需要展示的全部字段需要按用户选择排序下
            $fieldTitle = $statisticsHandleBiz->handleListHeadFieldSort($targetItem, array_values($itemArr), $target);

            //过滤非必要展示的汇总字段
            list($sumNew) = $statisticsHandleBiz->handleBackDataFormat([$sumNew], $target);

            //按统计维度重新排序下 子票维度 需要递增排序（主票子票为0优先展示）， 其他则递减排序
            $newList = $statisticsHandleBiz->handleDataMultiSort($newList, $item, SORT_ASC);

            //格式化返回格式
            $data = $statisticsHandleBiz->handleListDataFormat($newList, $total, $sumNew, $fieldTitle, $itemArr,
                $target, $unit, $name);

        } catch (\Exception $e) {
            $code = 400;
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 日期结合查询条件转为时间范围
     * <AUTHOR>
     * @date   2023/10/16
     *
     * @param  string  $begin
     * @param  string  $end
     * @param  string  $dt
     *
     * @return string
     */
    private function _dateToRange($begin, $end, $dt)
    {
        $searchBeginDay = date("Ymd", strtotime($begin));
        $searchEndDay   = date("Ymd", strtotime($end));

        $dtRange = $dt;

        if ($dt == $searchBeginDay && $dt == $searchEndDay) {
            $dtRange = date("Y-m-d H:i:00", strtotime($begin));
            $dtRange .= "~" . date("H:i:59", strtotime($end));

            return $dtRange;
        }

        if ($dt == $searchBeginDay && $dt != $searchEndDay) {
            $dtRange = date("Y-m-d H:i:00", strtotime($begin));
            $dtRange .= "~23:59:59";

            return $dtRange;
        }

        if ($dt != $searchBeginDay && $dt == $searchEndDay) {
            $dtRange = date("Y-m-d 00:00:00", strtotime($end));
            $dtRange .= "~" . date("H:i:59", strtotime($end));

            return $dtRange;
        }

        if ($dt != $searchBeginDay && $dt != $searchEndDay) {
            $dtRange = date("Y-m-d 00:00:00", strtotime($dtRange . '000000'));
            $dtRange .= "~23:59:59";

            return $dtRange;
        }

        return $dtRange;
    }
}