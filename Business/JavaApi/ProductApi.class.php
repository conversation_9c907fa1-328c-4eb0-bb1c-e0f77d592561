<?php

namespace Business\JavaApi;

use Business\Authority\DataAuthLimit;
use Business\MultiDist\Product;

/**
 * java产品相关接口
 *
 * <AUTHOR>
 */
class ProductApi extends BaseApi{



    //价格接口方法
    const __REFRESH_PRICE_FUNC__               = '/api/v1/pft.ticket.price/actualtime';
    //日历价格接口方法
    const __CALENDAR_PRICE_FUNC__              = '/api/v1/productprice/calendarprice';
    //预定列表页数据接口方法
    const __PRODUCT_LIST_FUNC__                = '/api/v1/pft.products.order/list';
    //站点用的产品列表
    const __PRODUCT_SITE_LIST_FUNC__           = '/api/v1/pft.product/site/reserve/tickets';
    //站点用的门票列表
    const __TICKET_SITE_LIST_FUNC__            = '/api/v1/pft.product/site/filter/list';
    //站点可捡的产品列表
    const __PRODUCT_SITE_CHECK_LIST_FUNC__     = '/api/v1/pft.product/site/check/list';
    //云票务分销商站点获取可售产品
    const __PRODUCT_SITE_CLOUND_LIST_FUNC__    = '/api/v1/pft.product/site/cloud/land/list';
    //预定列表页数据接口新方法
    const __PRODUCT_LIST_FUNC_NEW__            = '/api/v1/pft.product/reserve/list';
    //微平台小程序产品预订列表
    const __PRODUCT_LIST_WPT_SMALL__           = '/api/v1/pft.product/small/routine/list';
    //获取计调下单列表接口方法
    const __PLAN_ORDER_PRODUCT_LIST_FUNC__     = '/api/v1/pft.product/operator/reserve/list';
    //独立域名预定列表页数据接口方法
    const __DOMAIN_PRODUCT_LIST_FUNC__         = '/api/v1/pft.domain/product/reserve/list';
    //会员卡预定列表页数据接口方法
    const __MEMBERCARD_PRODUCT_LIST_FUNC__     = '/api/v1/pft.member.card/product/reserve/list';
    //独立域名预定计调下单列表页数据接口方法
    const __TRANSFERORDERS_PRODUCT_LIST_FUNC__ = '/api/v1/pft.domain/product/operator/reserve/list';
    //零售预定列表
    const _RETAIL_PRODUCT_LIST_                = '/api/v1/pft.products.order/retail/list';
    //窗口价预定列表
    const _WINDOW_PRODUCT_LIST_                = '/api/v1/pft.products.order/window/list';
    //pc店铺零售预定列表
    const _PC_RETAIL_PRODUCT_LIST_             = '/api/v1/pft.product/pc/retail/list';
    //salerid零售预定列表
    const _SALER_RETAIL_PRODUCT_LIST_          = '/api/v1/pft.products.order/saler/retail/ticket/list';
    //推荐列表
    const _RECOMMEND_PRODUCT_LIST_             = '/api/v1/pft.products.order/retail/recommend/ticket/list';
    //销售渠道产品列表页数据方法
    const __CHANNEL_LIST_FUNC__                = '/api/v1/pft.channel/config/list';
    //销售渠道产品列表获取更多渠道
    const __CHANNEL_LIST_FUNC_MORE__           = '/api/v1/pft.channel/config/list/more/data';
    //获取会员可售的产品列表信息
    const __AVAILABLE_SALE_LIST__              = '/api/v1/pft.product/available/sale/list';
    //获取转分销产品列表
    const __DIS_PRO_LIST__                     = '/api/v1/pft.ticket.distribution/transferlist';
    //新获取装分销产品列表
    const __DIS_PRO_LIST_NEW__                 = '/api/v1/pft.distribution.product/list';

    const __CLOUD_PRO_LIST__                   = '/api/v1/pft.products.order/cloud/list';
    //分销权限配置接口
    const __CONF_EVOLUTE__                     = '/api/v1/pft.ticket.distribution/modify';
    //库存接口方法
    const __STORAGE_FUNC__                     = '/api/v1/storage/daystorage';
    //日历库存接口方法
    const __CALENDAR_STORAGE_FUNC__            = '/api/v1/storage/calendarstorage';
    //新开启转分销接口方法
    const __OPEN_EVOLUTE_NEW__                 = '/api/v1/pft.optimize.distribution/open';
    //新关闭转分销接口方法
    const __CLOSE_EVOLUTE_NEW__                = '/api/v1/pft.optimize.distribution/close';
    //删除分销商的分销链
    const __BREAK_EVOLUTE__                    = '/api/v1/pft.distribution/break';
    //批量设置渠道
    const __BATCH_SET_CHANNEL__                = '/api/v1/pft.channel/all/config';
    //设置产品渠道推荐值
    const __SET_PRODUCT_SORT__                 = '/api/v1/pft.channel/item/sort/config';
    //设置票类的渠道推荐值
    const __SET_TICKET_SORT__                  = '/api/v1/pft.channel/ticket/sort/config';
    //设置票类的销售渠道
    const __SET_TICKET_CHANNEL__               = '/api/v1/pft.channel/batch/config';
    //设置默认销售渠道
    const __SET_DEFAULT_CHANNEL__              = '/api/v1/pft.member.channel.default/modify';
    //获取默认销售渠道
    const __GET_DEFAULT_CHANNEL__              = '/api/v1/pft.member.channel.default/get';
    //获取非特殊票的出售中的自供应产品
    const __GET_UNSPECIAL_SALE_PRODUCT_LIST__  = '/api/v1/pft.product.self/filter/special/listing/list';
    //获取出售中自供应产品列表
    const __GET_SALE_PRODUCT_LIST__            = '/api/v1/pft.product.self/listing/list';
    //获取出售中自供应产品列表(#管理员)
    const __GET_ADMIN_SALE_PRODUCT_LIST__      = '/api/v1/pft.product.self/admin/listing/list';
    //获取下架的自供应产品列表
    const __GET_EXPIRED_PRODUCT_LIST__         = '/api/v1/pft.product.self/delisting/list';
    //获取下架的自供应产品列表(#管理员)
    const __GET_ADMIN_EXPIRED_PRODUCT_LIST__   = '/api/v1/pft.product.self/admin/delisting/list';
    //获取未发布的自供应产品列表
    const __GET_UNPUBLISH_PRODUCT_LIST__       = '/api/v1/pft.product.self/unpublish/list';
    //获取条件查询产品下拉选择列表信息-按产品供应商区分
    const __GET_SELECTION_BY_SUPPLIERID_LIST__ = '/api/v1/pft.product/selection/by/supplierId/list';
    //获取未发布的自供应产品列表(#管理员)

    const __GET_ADMIN_UNPUBLISH_PRODUCT_LIST__  = '/api/v1/pft.product.self/admin/unpublish/list';
    //获取积分商城产品预定列表
    const __GET_INTEGRAL_MALL_RESERVE_LIST__  = '/api/v1/pft.product/integral/mall/reserve/list';
    //获取年卡的产品列表
    const __GET_ANNUAL_PRODUCT_LIST__           = '/api/v1/pft.annual.card/list';
    //云票务获取年卡的产品列表
    const __GET_ANNUAL_PRODUCT_LIST_V1__           = '/api/v1/pft.annual.card/orderBy/list';
    //获取分销价格自供应产品列表
    const __GET_PRODUCT_PRICE_ALLOCATION_SELF_LIST__     = '/api/v1/pft.product.price.allocation/self/supply';
    //获取分销价格转分销产品列表
    const __GET_PRODUCT_PRICE_ALLOCATION_TRANSFRE_LIST__ = '/api/v1/pft.product.price.allocation/transfre/distribution';
    //获取自供应分销价格配置产品列表--景区列表展示
    const __GET_PRODUCT_SELF_LAND__ = '/api/v1/pft.product.price.allocation/self/supply/land';
    //获取转分销分销价格配置产品列表--景区列表展示
    const __GET_PRODUCT_DIS_LAND__ = '/api/v1/pft.product.price.allocation/transfre/distribution/land';
    //获取产品价格配置产品列表
    const __GET_PRODUCT_PRICE_LIST__ = '/api/v1/pft.product.price.allocation/product/price/list';


    //发布产品
    const __PUBLISH_PRODUCT__                  = '/api/v1/pft.item/create';
    //更新产品
    const __UPDATE_PRODUCT__                   = '/api/v1/pft.item/update';
    //上架产品
    const __PRO_PUTAWAY__                      = '/api/v1/pft.item/listing';
    //下架产品
    const __PRO_SOLDOUT__                      = '/api/v1/pft.item/delisting';
    //删除产品
    const __PRO_DELETE__                       = '/api/v1/pft.item/delete';
    //恢复产品
    const __PRO_RESTORE__                      = '/api/v1/pft.item/recovery';
    //绑定子景点
    const __PRO_BIND__                         = '/api/v1/pft.item/scenic/bind';
    //添加子景点
    const __SIC_CREATE__                       = '/api/v1/pft.scenic/create';
    //更新子景点
    const __SIC_UPDATE__                       = '/api/v1/pft.scenic/update';
    //获取子景点
    const __SIC_SELECT__                       = '/api/v1/pft.scenic/query';
    //产品信息
    const __LAND_INFO__                        = '/api/v1/pft.item/query';
    //微平台小程序,产品详情页
    const __WPT_LAND_INFO__                    = '/api/v1/pft.product/small/routine/land';
    //景区列表
    const __LAND_LIST__                        = '/api/v1/pft.item/ota/list';
    //出售自供应产品列表
    const __PRO_SALE_LIST__                    = '/api/v1/pft.product.self/listing/list';
    //仓库自供应产品列表
    const __PRO_OUT_LIST__                     = '/api/v1/pft.product.self/delisting/list';

    //产品关联资源
    const __RELEATION_RESOURCE__               = '/api/v1/pft.item/resource/bind';
    //打包套票需要的产品列表
    const __PACKAGE_LIST__                     = '/api/v1/pft.ticket.package/package/list';


    const __FORBID_ACCOUNT__                   = '/api/v1/pft.item/all/delisting';


    const __LAND_LETTERS__                     = '/api/v1/pft.item/updateLandLetters';

    //导出转分销产品列表
    const __EXPORT_DIS_PRO_LIST__              = '/api/v1/pft.ticket.distribution/export/transferlist';

    //获取资源页的产品列表
    const __GET_RESOURCE_PRODUCT__             = '/api/v1/pft.product/resource/library/list';

    //获取用户的可销售的全部产品（ota去哪儿页面目前在使用）
    const __GET_QUNAER_LIST__                  = '/api/v1/pft.ota.product/ticket/all/list';

    //获取用户的可销售的全部产品 - 只返回已经绑定产品（ota去哪儿页面目前在使用）
    const __GET_QUNAER_BIND_LIST__             = '/api/v1/pft.ota.product/ticket/binding/list';

    //获取用户的可销售的全部产品 - 过滤已经绑定的产品（ota去哪儿页面目前在使用）
    const __GET_QUNAER_UNBIND_LIST__           = '/api/v1/pft.ota.product/ticket/unbinding/list';

    /**
     * 禁止账号后下架产品
     *
     * @date   2018-05-24
     * <AUTHOR> Lan
     *
     * @param
     *
     * @return array
     * @deprecated
     * @see \Business\CommodityCenter\Land::deListingAllLand()
     */
    public static function forbidAccount($memberId) {
        if (!$memberId) {
            return false;
        }

        $data = [
            'account_id'   => (int)$memberId,
        ];

        $result = self::_customCurlPost(self::__FORBID_ACCOUNT__, $data, 'POST', $data);

        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取产品预定列表-需要调用的，请到business下面调用
     *
     * @date   2017-09-20
     * <AUTHOR> Lan
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getPacketProducts($params) {
        $result = self::_customCurlPost(self::__PACKAGE_LIST__, [], 'GET', $params);

        return $result;
    }

    /**
     * 获取产品预定列
     * 表-需要调用的，请到business下面调用
     *
     * @date   2017-09-20
     * <AUTHOR> Lan
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getProductList($params) {
        $result = self::_customCurlPost(self::__PRODUCT_LIST_FUNC__, $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取计调下单列表
     * 需要调用的，请到business下面调用
     *
     * @date   2018-07-23
     * <AUTHOR>
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getPlanOrderProductList($params) {
        $result = self::_customCurlPost(self::__PLAN_ORDER_PRODUCT_LIST_FUNC__, [], 'GET', $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取产品预定列 新开接口  平台微平台调用
     *
     * @date   2018-08-08
     * <AUTHOR> Li
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getProductListV2($params) {
        $result = self::_customCurlPost(self::__PRODUCT_LIST_FUNC_NEW__, [], 'GET', $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取产品预订列表，微平台小程序新开接口
     * Create by zhangyangzhen
     * Date: 2018/12/27
     * Time: 18:32
     * @param $params
     */
    public static function getProductListV3($params)
    {
        $result = self::_customCurlPost(self::__PRODUCT_LIST_WPT_SMALL__, [], 'GET', $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取团购导码产品列表 
     *
     * @date   2018-08-14
     * <AUTHOR>
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getListSupplierId($params) {
        $result = self::_customCurlPost(self::__GET_SELECTION_BY_SUPPLIERID_LIST__, [], 'GET', $params);

        if ($result['code'] == 200) {
            return $result;
        } else {
            return [];
        }
    }

    /**
     * 获取站点可售的产品
     * @date   2018-07-27
     * <AUTHOR>
     * @param  array   $account_id 供应商id
     * @param  array   $shop       渠道
     * @param  array   $strTicket  门票字符串
     * @return array
     */
    public function getSiteProductList($memberId,$channel,$strTicket) {
        if(!$memberId || ! $channel || !$strTicket){
            return $this->returnData(204, '参数错误');
        }
        $params  = [
            'account_id'    => $memberId,
            'shop'          => $channel,
            'ticket_ids'    => $strTicket
        ];
        $result = self::_customCurlPost(self::__PRODUCT_SITE_LIST_FUNC__, $params,'GET',$params);
        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }
    /**
     * 获取站点可售的产品
     * @date   2018-07-27
     * <AUTHOR>
     * @param  array   $account_id 供应商id
     * @param  array   $shop       渠道
     * @param  array   $strTicket  门票字符串
     * @param  array   $did         分销商id
     * @param  array   $lid        景区id
     * @return array
     */
    public function getSiteTicketList($memberId,$channel,$strTicket,$lid,$did=0) {
        if(!$memberId || ! $channel || !$strTicket || !$lid){
            return $this->returnData(204, '参数错误');
        }
        if($channel ==9){
             $params  = [
                 'supplier_id'   => '',
                 'item_id'       => $lid,
                 'account_id'    => $memberId,
                 'shop'          => $channel,
                 'ticket_ids'    => $strTicket,
                 'distributor_id'=> $did
        ];}else{
            $params  = [
                'supplier_id'   => $memberId,
                'item_id'       => $lid,
                'account_id'    => $did,
                'shop'          => $channel,
                'ticket_ids'    => $strTicket,
                'distributor_id'=> ''
                ];
        }

        $result = self::_customCurlPost(self::__TICKET_SITE_LIST_FUNC__, $params,'GET',$params);
        if ($result['code'] == 200) {
            return $this->returnData(200,$result['msg'],$result['data']);
        } else {
            return [];
        }
    }

    /**
     * 获取站点可售的产品(云票务分销商)
     * @date   2018-08-02
     * <AUTHOR>
     * @param  array   $account_id 账户id
     * @param  array   $supplier_id 供应商id
     * @param  array   $strTicket  门票字符串
     * @return array
     */
    public function getCloudSiteProductList($memberId,$aid,$strTicket) {
        if(!$memberId || ! $aid || !$strTicket){
            return $this->returnData(204, '参数错误');
        }
        $params  = [
            'account_id'    => $memberId,
            'supplier_id'   => $aid,
            'ticket_ids'   => $strTicket
        ];
        $result = self::_customCurlPost(self::__PRODUCT_SITE_CLOUND_LIST_FUNC__, $params,'GET',$params);
        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }



    /**
     * 获取站点可捡的产品
     * @date   2018-07-27
     * <AUTHOR>
     * @param  array   $strItem  景区字符串
     * @return array
     */
    public function getSiteCheckProductList($strItem) {
        if(!$strItem ){
            return $this->returnData(204, '参数错误');
        }
        $params  = [
            'item_ids'    => $strItem,
        ];
        $result = self::_customCurlPost(self::__PRODUCT_SITE_CHECK_LIST_FUNC__, $params,'GET',$params);
        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }
    /**
     * 获取独立域名产品预定列
     *
     * @date   2018-06-15
     * <AUTHOR>
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getDomainProductList($params) {
        $result = self::_customCurlPost(self::__DOMAIN_PRODUCT_LIST_FUNC__, $params, 'GET', $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }
    /**
     * 获取年卡产品列表
     *
     * @date   2018-08-5
     * <AUTHOR>
     *
     * @param  int  $aid     供应商id
     * @param  int  $did     分销商id
     * @param  string  $lid     进去id
     *
     * @return array
     */
    public  function getAnnualProductList($aid, $did=0, $lid = '') {
        if(!$aid){
            return $this->returnData(204,'参数错误');
        }
        $params =[
            'account_id'    => $aid,
            'distributor_id'=> $did,
            'item_id'       => $lid,
        ];
        $result = self::_customCurlPost(self::__GET_ANNUAL_PRODUCT_LIST__, $params, 'GET', $params);
        if ($result['code'] == 200) {
            return $this->returnData(200,'',$result['data']);
        }
    }

    /**
     * 云票务获取年卡产品列表  (云票务用的这边废弃吧)
     *
     * @date   2019-05-15
     * <AUTHOR>
     *
     * @param  int  $aid     供应商id
     * @param  int  $did     分销商id
     * @param  string  $lid     进去id
     * @param  string  $shop    销售渠道
     * @deprecated
     * @return array
     */
    public  function getAnnualProductListV1($aid, $did=0, $lid = '', $shop) {
        if(!$aid){
            return $this->returnData(204,'参数错误');
        }
        $params =[
            'account_id'     => $aid,
            'distributor_id' => $did,
            'item_id'        => $lid,
            'shop'           => $shop
        ];
        $result = self::_customCurlPost(self::__GET_ANNUAL_PRODUCT_LIST_V1__, $params, 'GET', $params);
        if ($result['code'] == 200) {
            return $this->returnData(200,'',$result['data']);
        }
    }
    /**
     * 获取独立域名计调下单产品预定列
     *
     * @date   2018-07-3
     * <AUTHOR>
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getTransferOrdersList($params) {
        $result = self::_customCurlPost(self::__TRANSFERORDERS_PRODUCT_LIST_FUNC__, $params, 'GET', $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取会员卡产品预定列
     *
     * @date   2018-07-3
     * <AUTHOR>
     *
     * @param  array   $params     请求参数
     *
     * @return array
     */
    public static function getMemberCardProductList($params) {
        $result = self::_customCurlPost(self::__MEMBERCARD_PRODUCT_LIST_FUNC__, $params, 'GET', $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }


    /**
     * 获取转分销产品列表
     *
     * @param  int      $memberId 会员id
     * @param  int      $page     当前页数
     * @param  int      $pageSize 每页条数
     * @param  string   $title    景区名称
     * @param  string   $supplier 供应商名称
     * @param  int      $province 省份code
     * @param  int      $city     城市code
     * @param  bool     $showOnsite   现场支付票类是否允许开启转分销
     * @param  int      $disStatus     开启转分销状态 -1=全部 0=关闭 1=开启
     * @param  int      $subType     计时产品子类型
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     * @return array
     */
    public static function getDisProList(
        $memberId,
        $page,
        $pageSize,
        $title = '',
        $supplier = '',
        $province = 0,
        $city = 0,
        $showOnsite = false,
        $type ='',
        $oversea = -1,
        $disStatus = -1,
        $subType = 0,
        array $extCondition = []) {

        $data = [
            'account_id'          => (int)$memberId,
            'oversea'             => $oversea,
            'province'            => $province,
            'city'                => $city,
            'type'                => $type,
            'title'               => $title,
            'supplier'            => $supplier,
            'show_onsite'         => $showOnsite,
            'distribution_status' => $disStatus,
            'page_num'            => (int)$page,
            'page_size'           => (int)$pageSize,
            'subType'             => (int)$subType,
        ];

        //预售券需产品类型为了区分景区和预售券，游船计时会影响这边做下兼容
        if (($data['type'] == 'K' && !$subType) || empty($type)) {
            unset($data['subType']);
        }

        if (isset($extCondition['lidList'])) {
            $data['lidList'] = implode(',', $extCondition['lidList']);
        }
        if (isset($extCondition['notLidList'])) {
            $data['notLidList'] = implode(',', $extCondition['notLidList']);
        }
        if (isset($extCondition['tags'])) {
            $data['tags'] = $extCondition['tags'];
        }

        $result = self::_customCurlPost(self::__DIS_PRO_LIST_NEW__, [], 'GET', $data);

        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 导出转分销产品列表
     *
     * @date   2018-09-18
     * <AUTHOR>
     *
     * @param  int      $memberId 会员id
     * @param  int      $page     当前页数
     * @param  int      $pageSize 每页条数
     * @param  string   $title    景区名称
     * @param  string   $supplier 供应商名称
     * @param  int      $province 省份code
     * @param  int      $city     城市code
     * @param  bool     $showOnsite   现场支付票类是否允许开启转分销
     *
     * @return array
     */
    public static function exportDisProList(
        $memberId,
        $page = 1,
        $pageSize = 500,
        $title = '',
        $supplier = '',
        $province = 0,
        $city = 0,
        $showOnsite = false,
        $type ='',
        $oversea ='',
        $subType = 0
    ) {

        $params = [
            'account_id'      => (int)$memberId,
            'page_num'        => (int)$page,
            'page_size'       => (int)$pageSize,
            'item_name'       => $title,
            'supplier_name'   => $supplier,
            'province'        => $province,
            'city'            => $city,
            'show_onsite'     => $showOnsite,
            'type'            => $type,
            'oversea'         => $oversea,
            'subType'         => $subType,

        ];

        $result = self::_customCurlPost(self::__EXPORT_DIS_PRO_LIST__, $params, 'GET', $params);

        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 销售渠道产品列表
     *
     * 维护时间
     * @date   2018-08-10
     * <AUTHOR>
     *
     * @param  int      $memberId  会员id     必须
     * @param  int      $page      当前页码   1
     * @param  int      $pageSize  每页条数   8
     * @param  string   $title     景区名称
     * @param  string   $supplier  供应商名称
     * @param  string   $province  省份
     * @param  string   $city      城市
     * @param  string   $oversea   境内外
     * @param  int      $channelId 销售渠道
     * @param  bool      $showPrice 是否显示结算价 true=显示 false=不显示
     * @return array | bool
     */
    public static function getProListForChannel
    (
        $memberId,
        $page       = 1,
        $pageSize   = 8,
        $title      = '',
        $supplier   = '',
        $province   = '',
        $city       = '',
        $oversea    = '',
        $channelId  = 0,
        $tid        = 0,
        $type       = '',
        $showPrice  = false
    ) {

        if(empty($memberId)){
            return false;
        }

        $data = [
            'member_id'     => (int)$memberId,   //会员id
            'land_name'     => $title,           //景区名称
            'supplier_name' => $supplier,        //供应商名称
            'page_num'      => (int)$page,       //当前页码
            'page_size'     => (int)$pageSize,   //每页条数 8
            'province'      => $province,        //省份
            'city'          => $city,            //城市
            'oversea'       => $oversea,         //城市
            'channel_id'    => (int)$channelId,  //销售渠道
            'ticket_id'     => (int)$tid,        //门票ID
            'showPrice'     => $showPrice        //是否显示结算价 true=显示 false=不显示
        ];

        if (!empty($type)) {
            $data['type'] = $type;
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__CHANNEL_LIST_FUNC__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $result['msg'], $data);
    }

    /**
     * 销售渠道获取更多的渠道
     * <AUTHOR>
     * @date 2020/9/8
     *
     * @param  int  $memberId 用户id
     * @param  array  $ids 渠道ID
     * @param  bool  $showPrice 是否显示结算价 true=显示 false=不显示
     *
     * @return array
     */
    public static function getMoreListForChannel(int $memberId, array $ids, bool $showPrice = false)
    {
        if (empty($memberId) || empty($ids)) {
            return self::returnDataStatic(self::CODE_PARAM_ERROR, '参数缺失', []);
        }

        $data = [
            'memberId'   => $memberId,   //用户ID
            'ids'        => $ids,        //渠道ID数组
            'showPrice'  => $showPrice,  //是否显示结算价 true=显示 false=不显示
        ];

        $result = self::_customCurlPost(self::__CHANNEL_LIST_FUNC_MORE__, $data);

        if (!isset($result['code'])) {
            return self::returnDataStatic(self::CODE_INVALID_REQUEST, '接口异常', []);
        }
        $data = isset($result['data']) ? $result['data'] : [];

        return self::returnDataStatic($result['code'], $result['msg'], $data);
    }


    /**
     * 配置价格/分销权限
     *
     * @param   int     $sid      配置用户id
     * @param   array   $confList 配置数据列表
     *
     * @return  bool
     */
    public static function confEvolutes($sid, $confList, $isDiff = 1) {

        if (!is_array($confList)) {
            return false;
        }

        if (!$confList) {
            return true;
        }

        $lastList = [];
        foreach($confList as $item) {
            $lastList[] = [
                'priceset' => $item['priceset'],
                'pid'      => intval($item['pid']),
                'aid'      => intval($item['aid']),
            ];
        } 

        $data = [
            'fid'           => (int)$sid,
            'list'          => $lastList,
            'isdprice'      => (int)$isDiff
        ];

        $result = self::_customCurlPost(self::__CONF_EVOLUTE__, $data);
        
        if ($result && $result['code'] != 200) {
            @pft_log('price/config_error', json_encode(debug_backtrace()));
            @pft_log('price/config_error', json_encode(func_get_args()));
            @pft_log('price/config_error', json_encode($result));
        }

        if ($result && $result['code'] == 200) {
            //分销专员转分销产品更新通知
            Product::pushUpdataDis($data);

            pft_log('evolute_3/confEvolutes', json_encode(['param' => $data, 'res' => $result]));
            return true;
        } else {
            return false;
        }

    }


    /**
     * 获取实时结算价(可同时获取过多个产品，但必须属于同个供应商)
     *
     * @param  int      $memberId 会员id
     * @param  string   $pid      产品id  6214,6215
     * @param  int      $aid      上级供应商id
     * @param  string   $date     日期
     *
     * @return false:接口调用失败 | 价格 = -1 ：未配置价格 or 未建立分销链
     */
    public static function getSettlePrice($memberId, $pid, $aid, $date = '') {

        if ($date) {
            $date = date('Y-m-d', strtotime($date));
        }

        $data = [
            'memberId'  => (int)$memberId,
            'pid'       => $pid,
            'aid'       => (int)$aid,
            'date'      => $date
        ];

        $result = self::_customCurlPost(self::__REFRESH_PRICE_FUNC__, $data);

        if ($result && $result['code'] == 200) {

            return $result['data'];

        } else {
            return false;
        }
    }

    /**
     * 获取实时结算价
     *
     * @param  int      $memberId 会员id
     * @param  string   $tid      产品id  6214
     * @param  int      $aid      上级供应商id
     * @param  string   $date     日期
     * @param  bool     $retail   是否获取零售价
     *
     * @return false:接口调用失败 | 价格 = -1 ：未配置价格 or 未建立分销链
     */
    public static function getOneSettlePrice($memberId, $tid, $aid, $date = '', $retail = false) {

        if (!($memberId && $tid && $aid)) {
            return false;
        }

        if ($date) {
            $date = date('Y-m-d', strtotime($date));
        }

        $data = [
            'account_id'  => (int)$memberId,
            'ticket_id'   => (int)$tid,
            'superior_id' => (int)$aid,
            'date'        => $date
        ];

        $result = self::_customCurlPost(self::__REFRESH_PRICE_FUNC__, [], 'GET', $data);

        if ($result && $result['code'] == 200) {
            if ($retail) {
                return [
                    'js' => $result['data']['settlement'],
                    'ls' => $result['data']['retail']
                ];
            } else {
                return $result['data']['settlement'];
            }
        } else {
            return false;
        }
    }


    /**
     * 获取日历价格，时间段价格
     *
     * @param  int      $memberId  会员id
     * @param  int      $pid       产品id
     * @param  int      $aid       上级供应商id
     * @param  string   $startDate 开始日期, Y-m-d
     * @param  string   $endDate   结束日期
     *
     * @return false:接口调用失败 | array
     */
    public static function getCalendarPrice($memberId, $pid, $aid, $startDate, $endDate) {

        if (!$memberId || !$pid || !$aid) {
            return false;
        }

        if(!chk_date($startDate) || !chk_date($endDate)) {
            return false;
        }

        $startDate = date('Y-m-d', strtotime($startDate));
        $endDate   = date('Y-m-d', strtotime($endDate));

        $data = [
            'memberId'  => (int)$memberId,
            'pid'       => (int)$pid,
            'aid'       => (int)$aid,
            'sdate'     => $startDate,
            'edate'     => $endDate
        ];

        $result = self::_customCurlPost(self::__CALENDAR_PRICE_FUNC__, $data);

        if ($result && $result['code'] == 200) {

            return $result['data'];

        } else {
            return false;
        }

    }


    /**
     * 获取产品库存
     *
     * @param  srting   $memberId   会员id
     * @param  string   $pids       产品id,多个用逗号分隔
     * @param  int      $aid        上级供应商id
     * @param  string   $date       日期
     *
     * @return false | 库存信息
     */
    public static function getStorage($memberId, $pids, $aid, $date = '') {

        $date = chk_date($date) ? $date : date('Y-m-d');

        $data = [
            'memberId'  => $memberId,
            'pids'      => $pids,
            'aid'       => (int)$aid,
            'date'      => $date
        ];

        $result = self::_customCurlPost(self::__STORAGE_FUNC__, $data);

        if ($result && $result['code'] == 200) {

            return $result['data'];

        } else {
            return false;
        }
    }

    /**
     * 获取日历库存，时间段库存
     *
     * @param  int      $memberId  会员id
     * @param  int      $pid       产品id
     * @param  int      $aid       上级供应商id
     * @param  string   $startDate 开始日期, Y-m-d
     * @param  string   $endDate   结束日期
     *
     * @return false:接口调用失败 | array
     */
    public static function getCalendarStorage($memberId, $pid, $aid, $startDate, $endDate) {

        if (!$memberId || !$pid || !$aid) {
            return false;
        }

        if(!chk_date($startDate) || !chk_date($endDate)) {
            return false;
        }

        $data = [
            'memberId'  => (int)$memberId,
            'pid'       => (int)$pid,
            'aid'       => (int)$aid,
            'sdate'     => $startDate,
            'edate'     => $endDate
        ];

        $result = self::_customCurlPost(self::__CALENDAR_STORAGE_FUNC__, $data);

        if ($result && $result['code'] == 200) {

            return $result['data'];

        } else {
            return false;
        }
    }


    /**
     * 开启转分销 active => 1
     *
     * @param  int      $memberId 会员id
     * @param  int      $sid      上级供应商id
     * @param  int      $pid      产品id
     *
     * @return bool
     */
    public static function openEvolute($memberId, $sid, $pid) {

        if (!$memberId || !$sid || !$pid) {
            return false;
        }

        $data = [
            'account_id'   => (int)$memberId,
            'product_id'   => (int)$pid,
            'supplier_id'  => (int)$sid,
            'operator_id'  => (int)$memberId
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__OPEN_EVOLUTE_NEW__, $data, 'POST', $httpQuery);

        if ($result && $result['code'] == 200) {
            pft_log('evolute_3/openEvolute', json_encode(['pid' => $pid , 'param' => $data, 'res' => $result]));
            return true;
        } else {
            return false;
        }
    }


    /**
     * 关闭转分销
     *
     * @param  int      $memberId 会员id
     * @param  int      $sid      上级供应商id
     * @param  int      $pid      产品id
     * @param  int      $closeDown  关闭向下分销 0.关闭 1.不关闭 [默认：0]
     *
     * @return bool
     */
    public static function closeEvolute($memberId, $sid, $pid, $closeDown = 0)
    {

        if (!$memberId || !$sid || !$pid) {
            return false;
        }

        $data = [
            'product_id' => (int)$pid,
            'supplier_id' => (int)$sid,
            'account_id' => (int)$memberId,
            'operator_id' => (int)$memberId,
            'closeDown' => (int)$closeDown,
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__CLOSE_EVOLUTE_NEW__, [], 'POST', $httpQuery);

        if ($result && $result['code'] == 200) {
            //分销专员转分销产品更新通知
            if (Product::EVOLUTE_CLOSE_SWITCH) {
                Product::pushUpdataDisByOffAll($pid, $memberId);
            }
            pft_log('evolute_3/closeEvolute', json_encode(['pid' => $pid , 'param' => $data, 'res' => $result]));
            return true;
        } else {
            return false;
        }

    }


    /**
     * 删除分销商的所有分销链
     *
     * <AUTHOR>
     * @date   2017-06-15
     *
     * @param  int     $memberId 分销商id
     * @param  int     $sid      供应商id
     *
     * @return bool
     */
    public static function breakEvolute($memberId, $sid) {

        if (!$memberId || !$sid) {
            return false;
        }

        $data = [
            'account_id'  => (int)$memberId,
            'superior_id' => (int)$sid,
            'operator_id' => (int)$memberId
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__BREAK_EVOLUTE__, [], 'POST', $httpQuery);

        if ($result && $result['code'] == 200) {
            pft_log('evolute_3/breakEvolute', json_encode(['param' => $data, 'res' => $result]));
            return true;
        } else {
            pft_log('evolute_3/breakEvolute', json_encode(['param' => $data, 'res' => $result]));
            return false;
        }
    }


    /**
     * 设置票类渠道推荐值
     * <AUTHOR>
     * @date   2017-08-11
     * @param  int     $fid         会员id
     * @param  array   $sortMapping [['id' => 1, 'ticket_sort' => 2],]
     * @param  integer $opId       操作员id
     */
    public static function setTicketSort($fid, $sortMapping, $opId = 0) {

        if (!$fid || !$sortMapping) {
            return false;
        }

        $data = [
            'distributor_id'=> (int)$fid,
            'operator_id'   => (int)$opId,
        ];

        $query = http_build_query($data);
        $result = self::_customCurlPost(self::__SET_TICKET_SORT__, $sortMapping, 'POST', $query);

        if ($result && $result['code'] == 200) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }


    /**
     * 设置票类的渠道
     * <AUTHOR>
     * @date   2017-08-11
     * @param  int      $fid     会员id
     * @param  array    $idArr   evoluteid数组
     * @param  string   $channel 渠道(1,2,3)
     * @param  int      $opId    操作员id
     */
    public static function setChannel($fid, $idArr, $channel = '', $opId = 0) {

        if (!$fid) {
            return false;
        }

        $data = [
            'ids'               => implode(',', $idArr),
            'distributor_id'    => (int)$fid,
            'channel_ids'       => $channel,
            'operator_id'       => (int)$opId
        ];

        $query = http_build_query($data);
        $result = self::_customCurlPost(self::__SET_TICKET_CHANNEL__, [], 'POST', $query);

        if ($result && $result['code'] == 200) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }


    /**
     * 设置产品推荐值
     * <AUTHOR>
     * @date   2017-08-11
     * @param  int     $fid  会员id
     * @param  int     $lid  景区id
     * @param  int     $px   推荐值
     * @param  int    $opId 操作员id
     *
     * @return false || array
     */
    public static function setProductSort($fid, $lid, $px, $opId = 0) {

        if (!($fid && $lid)) {
            return false;
        }

        $data = [
            'item_id'       => (int)$lid,
            'distributor_id'=> (int)$fid,
            'item_sort'     => (int)$px,
            'operator_id'   => (int)$opId,
        ];

        $httpQuery = http_build_query($data);

        $result = self::_customCurlPost(self::__SET_PRODUCT_SORT__, [], 'POST', $httpQuery);

        if ($result && $result['code'] == 200) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }


    /**
     * 批量设置销售渠道
     *
     * <AUTHOR>
     * @date   2017-06-07
     *
     * @param  int      $fid        会员id
     * @param  string   $channelVal 要设置的渠道值
     * @param  int      $opId       操作人员id
     * @return bool
     */
    public static function batchSetChannels($fid, $channelVal = '', $opId = 0) {

        if (!(int)$fid) {
            return false;
        }

        if ($channelVal) {
            $valid = [1,2,3,4,5,6,7,8];
            $channelSet = explode(',', $channelVal);
            if (array_diff($channelSet, $valid)) {
                return false;
            }
        }

        $data = [
            'distributor_id' => $fid,
            'shop_ids'       => $channelVal,
            'operator_id'    => (int)$opId
        ];

        $httpQuery = http_build_query($data);

        $result = self::_customCurlPost(self::__BATCH_SET_CHANNEL__, [], 'POST', $httpQuery);

        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }

    }

    /**
     * 获取默认渠道的值
     * <AUTHOR>
     * @date   2017-10-24
     * @param  int     $fid 会员id
     * @return array
     */
    public static function getDefaultChannel($fid) {

        if (!$fid) {
            return ['code' => '204', 'msg' => '参数错误'];
        }

        $data = [
            'account_id' => (int)$fid
        ];

        $result = self::_customCurlPost(self::__GET_DEFAULT_CHANNEL__, [], 'GET', $data);

        if ($result && $result['code'] == 200) {
            return $result;
        } else {
            return ['code' => '500', 'msg' => '服务器错误'];
        }
    }


    /**
     * 设置默认渠道
     * <AUTHOR>
     * @date   2017-10-24
     * @param  int     $fid         会员id
     * @param  string  $channelVal  渠道值
     */
    public static function setDefaultChannel($fid, $channelVal = '', $opId = 0) {

        if (!$fid) {
            return ['code' => '204', 'msg' => '参数错误'];
        }

        $data = [
            'account_id'    => (int)$fid,
            'shop'          => $channelVal,
            'operater_id'   => (int)$opId
        ];

        $result = self::_customCurlPost(self::__SET_DEFAULT_CHANNEL__, [], 'POST', $data);

        if ($result && $result['code'] == 200) {
            return $result;
        } else {
            return ['code' => '500', 'msg' => '服务器错误'];
        }
    }



    /*****************************************************产品发布/编辑相关接口********************************************************************/


    /**
     * 发布新产品
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php createLand
     * <AUTHOR>
     * @date   2017-07-03
     * @param  array      $proData 景区信息数组
     * @return false | 产品id
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::createLand()
     */
    public static function publish(array $proData) {
        $logData = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        pft_log('debug/land_move', 'createLand:' . $logData);
        if (!$proData) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $result = self::_customCurlPost(self::__PUBLISH_PRODUCT__, $proData);

        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }

    /**
     * 更新产品
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php updateLand
     * <AUTHOR>
     * @date   2017-07-03
     * @param  array      $proData 景区信息数组
     * @return false | 产品id
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::updateLand()
     */
    public static function update(array $proData) {

        if (!$proData || !$proData['id']) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $result = self::_customCurlPost(self::__UPDATE_PRODUCT__, $proData);

        if (isset($result['code'])) {
            return $result;
        } else {
            @pft_log("debug/wengbin/product", json_encode($proData));
            return ['code' => 500, 'msg' => '服务器错误'];
        }

    }


    /**
     * 上架产品
     *
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php listingLand
     *
     * <AUTHOR>
     * @date   2017-07-06
     * @param  int      $sid 供应商id
     * @param  int      $mid 操作员id
     * @param  int      $lid 景区id
     * @return bool
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::listingLand()
     */
    public static function putaway($sid, $mid, $lid) {
        $logData = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        pft_log('debug/land_move', 'listingLand:' . $logData);
        if (!($sid && $mid && $lid)) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $data = [
            'item_id'       => (int)$lid,
            'account_id'    => (int)$sid,
            'operater_id'   => (int)$mid,
        ];

        $httpQuery = http_build_query($data);

        $result = self::_customCurlPost(self::__PRO_PUTAWAY__, [], 'POST', $httpQuery);

        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

    }

    /**
     * 下架产品
     *
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php deListingLand
     *
     * <AUTHOR>
     * @date   2017-07-06
     * @param  int      $sid 供应商id
     * @param  int      $mid 操作员id
     * @param  int      $lid 景区id
     * @return bool
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::deListingLand()
     */
    public static function soldOut($sid, $mid, $lid) {
        $logData = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        pft_log('debug/land_move', 'deListingLand:' . $logData);
        if (!($sid && $mid && $lid)) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $data = [
            'item_id'       => (int)$lid,
            'account_id'    => (int)$sid,
            'operater_id'   => (int)$mid,
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__PRO_SOLDOUT__, [], 'POST', $httpQuery);

        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

    }

    /**
     * 删除产品
     *
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php deleteLand
     *
     * <AUTHOR>
     * @date   2017-07-06
     * @param  int      $sid 供应商id
     * @param  int      $mid 操作员id
     * @param  int      $lid 景区id
     * @return bool
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::deleteLand()
     */
    public static function delete($sid, $mid, $lid) {

        $logData = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        pft_log('debug/land_move', 'deleteLand:' . $logData);
        if (!($sid && $mid && $lid)) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $data = [
            'item_id'       => (int)$lid,
            'account_id'    => (int)$sid,
            'operater_id'   => (int)$mid,
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__PRO_DELETE__, [], 'POST', $httpQuery);

        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

    }

    /**
     * 恢复产品
     *
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php recoveryLand
     *
     * <AUTHOR>
     * @date   2017-07-06
     * @param  int      $sid 供应商id
     * @param  int      $mid 操作员id
     * @param  int      $lid 景区id
     * @return bool
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::recoveryLand()
     */
    public static function restore($sid, $mid, $lid, $subSid = 0) {
        $logData = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        pft_log('debug/land_move', 'recoveryLand:' . $logData);
        if (!($sid && $mid && $lid)) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $data = [
            'item_id'       => (int)$lid,
            'account_id'    => (int)$sid,
            'operater_id'   => (int)$mid,
        ];

        if ($subSid) {
            $data['subSid'] = $subSid;
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__PRO_RESTORE__, [], 'POST', $httpQuery);

        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

    }

    /**
     * 绑定子景点
     *
     * @date   2017-09-06
     * <AUTHOR> Lan
     *
     * @param  array  $params  请求参数
     *
     * @return array
     * @deprecated
     * @see \Business\CommodityCenter\Land::bindSecnicList()
     */
    public static function bindChildLand($params) {

        $httpQuery = http_build_query($params);
        $result = self::_customCurlPost(self::__PRO_BIND__, [], 'POST', $httpQuery);
        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }

    /**
     * 设置子景点
     *
     * @date   2017-09-07
     * <AUTHOR> Lan
     *
     * @param  array  $params  请求参数
     *
     * @return array
     */
    public static function setScenic($params) {
        if (isset($params['id'])) {
            $url = self::__SIC_UPDATE__;
        } else {
            $url = self::__SIC_CREATE__;
        }

        $result = self::_customCurlPost($url, $params, 'POST', []);

        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }


    /**
     * 获取子景点
     *
     * @date   2017-09-06
     * <AUTHOR> Lan
     *
     * @param  array  $params  请求参数
     *
     * @return array
     */
    public static function getScenic($params) {
        $result = self::_customCurlPost(self::__SIC_SELECT__, [], 'GET', $params);
        if (isset($result['code'])) {
            return $result;
        } else {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
    }

    /**
     * 获取产品信息
     *
     * <AUTHOR>
     * @date   2017-09-05
     * @param  int $lid  景区id
     * @return array
     *
     * @deprecated
     * @see \Business\CommodityCenter\Land::queryLandById()
     */
    public function getLandInfo($lid) {

        if (!$lid) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $data = [
            'item_id' => (int)$lid
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__LAND_INFO__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

        $msg  = $this->javaReturnCode($result['code']);

        $data = isset($result['data']) ? $result['data'] : '';

        // 返回java的错误码 和中文错误提示
        return ['code' => $result['code'], 'msg' => $msg, 'data' => $data];
    }

    /**
     * Create by zhangyangzhen
     * Date: 2018/12/28
     * Time: 16:44
     * @param int $sid      用户ID
     * @param int $aid      上级供应商ID
     * @param int $lid      产品ID
     * @param int $channel  销售渠道
     * @return array
     */
    public function getWPTLandInfo($sid, $aid, $lid, $channel)
    {
        $data = [
            'account_id'    => (int)$sid,
            'supplier_id'   => (int)$aid,
            'land_id'       => (int)$lid,
            'shop'          => (string)$channel
        ];

        $httpQuery = http_build_query($data);
        $result    = self::_customCurlPost(self::__WPT_LAND_INFO__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

        $msg  = $this->javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return ['code' => $result['code'], 'msg' => $msg, 'data' => $data];
    }

    /**
     * 获取景区列表
     *
     * <AUTHOR> Lin
     * @date   2017-10-11
     *
     * @param  int $member  会员id
     * @param  int $pageSize 每页显示记录数
     * @param  int $startPage 起始页,默认为0
     *
     * @return mixed|bool
     * @deprecated
     * @see \Business\CommodityCenter\Land::otaList()
     *
     */
    public function getLandList($member, $pageSize, $startPage=0, $pTypes = [])
    {

        $params = [
            'account_id'   => (int)$member,
            'page_size'    => (int)$pageSize,
            'start_page'   => (int)$startPage,
        ];
        if (!empty($pTypes)) {
            $params['pTypes'] = $pTypes;
        }

        $result = self::_customCurlPost(self::__LAND_LIST__, [], 'GET', $params);
        if ($result && $result['code'] == 200) {
            return $result;
        }

        return false;
    }

    /**
     * 获取出售自供应产品列表, 参数在biz层处理
     *
     * <AUTHOR>
     * @date 2018-06-11
     *
     * @param int $memberId 会员id
     * @param int $page    起始页 默认1
     * @param int $pageSize 页显示数量 默认15
     * @param string $itemId 产品id 默认空
     * @param string $itemName 产品名称 默认空
     * @param string $itemCode 6位数产品编码
     * @param string $terminalNum 终端号 默认空
     * @param string $province 省份， 默认空
     * @param string $city 城市，默认空
     * @param string $type 产品类别,多个类别逗号分割;(A:景点门票;B:旅游线路;C:酒店客房;F:套票产品;H:剧场演出;I:年卡产品;G:餐饮产品;J:旅游特产;)
     * @param string $startDate 发布产品的时间-开始时间【yyyy-MM-dd】
     * @param string $endDate   发布产品的时间-结束时间【yyyy-MM-dd】
     * @param bool $showExpire  过期：true是;false否;
     * @param bool $onlyShowItem 仅列出产品:true是;false;
     *
     * @return array
     */
    public function getSelfProductSaleList($params)
    {
        $result = self::_customCurlPost(self::__PRO_SALE_LIST__, [], 'GET', $params);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

        $data = isset($result['data']) ? $result['data'] : '';

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $data];
    }


    /**
     * 获取仓库自供应产品列表, 参数在biz层处理
     *
     * <AUTHOR>
     * @date 2018-06-11
     *
     * @param int $memberId 会员id
     * @param int $page    起始页 默认1
     * @param int $pageSize 页显示数量 默认15
     * @param string $itemId 产品id 默认空
     * @param string $itemName 产品名称 默认空
     * @param string $itemCode 6位数产品编码
     * @param string $terminalNum 终端号 默认空
     * @param string $province 省份， 默认空
     * @param string $city 城市，默认空
     * @param string $type 产品类别,多个类别逗号分割;(A:景点门票;B:旅游线路;C:酒店客房;F:套票产品;H:剧场演出;I:年卡产品;G:餐饮产品;J:旅游特产;)
     * @param string $startDate 发布产品的时间-开始时间【yyyy-MM-dd】
     * @param string $endDate   发布产品的时间-结束时间【yyyy-MM-dd】
     * @param bool $showExpire  过期：true是;false否;
     * @param bool $onlyShowItem 仅列出产品:true是;false;
     *
     * @return array
     */
    public function getSelfProductOutList($params)
    {
        $result = self::_customCurlPost(self::__PRO_OUT_LIST__, [], 'GET', $params);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

        $data = isset($result['data']) ? $result['data'] : '';

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $data];
    }


    /*****************************************************资源创建/关联相关接口********************************************************************/


    /**
     * 产品关联资源操作
     * <AUTHOR>
     * @date   2017-09-01
     * @param  int $lid      景区id
     * @param  int $resourceId  资源id
     * @return array
     *
     * @deprecated
     * @see \Business\CommodityCenter\Land::resourceBind()
     */
    public function updateResouceId($lid, $resourceId) {

        if (!$lid || !$resourceId) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $data = [
            'item_id'     => (int)$lid,
            'resource_id' => (int)$resourceId
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__RELEATION_RESOURCE__, [], 'POST', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }

        $msg = $this->javaReturnCode($result['code']);

        // 返回java的错误码 和中文错误提示
        return ['code' => $result['code'], 'msg' => $msg];
    }

    /**
     * 获取转分销产品列表
     *
     * @param  int      $memberId 会员id
     * @param  int      $page     当前页数
     * @param  int      $pageSize 每页条数
     * @param  int      $supplyId 供应商Id
     * @param  int      $shop     渠道
     *
     * @return array
     */
    public static function getProListForDistributor(
        $memberId,
        $page,
        $pageSize,
        $supplyId,
        $shop   ) {

        $data = [
            'distributor_id'=> (int)$memberId,
            'pagerno'       => (int)$page,
            'pagerrows'     => (int)$pageSize,
            'superior_id'   => (int)$supplyId,
            'shop'          => $shop,
        ];
        $result = self::_customCurlPost(self::__CLOUD_PRO_LIST__, $data, 'GET', $data);
        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取零售的产品列表数据格式
     * --- 微商城和二级店铺目前使用
     * <AUTHOR>
     * @date
     *
     * @param string $memberId  多个用户用 ，分割
     * @param int $page
     * @param int $pageSize
     * @param array $option 数组
     * @param string $channel           产品渠道
     * @param string $title             产品名字
     * @param string $type              产品类型
     * @param string $rating            等级
     * @param string $topic             话题
     * @param string $province          省份code
     * @param string $city              城市code
     * @param int    $operId            操作人id
     * @param int    $isShowDistribute  是否显示自供应+转分销
     * @param int    $online 是否只显示在线产品
     *
     * @return array
     *
     */
    public static function getRetailProductList($memberId, $page, $pageSize, $option = [])
    {
        if (!$memberId || !$page || !$pageSize) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $channel          = isset($option['channel']) ? $option['channel'] : '';
        $title            = isset($option['title']) ? $option['title'] : '';
        $type             = isset($option['type']) ? $option['type'] : '';
        $rating           = isset($option['rating']) ? $option['rating'] : '';
        $topic            = isset($option['topic']) ? $option['topic'] : '';
        $province         = isset($option['province']) ? $option['province'] : '';
        $city             = isset($option['city']) ? $option['city'] : '';
        $operId           = isset($option['operId']) ? $option['operId'] : '';
        $isShowDistribute = isset($option['isShowDistribute']) ? $option['isShowDistribute'] : true;
        $online           = isset($option['online']) ? $option['online'] : false;
        $lids             = isset($option['lids']) ? $option['lids'] : '';

        $data = [
            'account_ids'   => (string)$memberId,
            'pagerno'       => (int)$page,
            'pagerrows'     => (int)$pageSize,
            'shop'          => $channel,
            'name'          => $title,
            'type'          => $type,
            'rating'        => $rating,
            'topic'         => $topic,
            'province'      => $province,
            'city'          => $city,
            'operator_id'   => $operId,
            'distribute'    => $isShowDistribute,
            'online'        => $online,
            'lids'          => $lids
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::_RETAIL_PRODUCT_LIST_, [], 'POST', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取窗口价产品列表数据格式
     * --- 微票房
     * <AUTHOR>
     * @date
     *
     * @param string $memberId  多个用户用 ，分割
     * @param int $page
     * @param int $pageSize
     * @param array $option 数组
     * @param string $channel           产品渠道
     * @param string $title             产品名字
     * @param string $type              产品类型
     * @param string $rating            等级
     * @param string $topic             话题
     * @param string $province          省份code
     * @param string $city              城市code
     * @param int    $operId            操作人id
     * @param int    $isShowDistribute  是否显示自供应+转分销
     * @param int    $online 是否只显示在线产品
     *
     * @return array
     *
     */
    public static function getWindowProductList($memberId, $page, $pageSize, $option = [])
    {
        if (!$memberId || !$page || !$pageSize) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $channel          = isset($option['channel']) ? $option['channel'] : '';
        $title            = isset($option['title']) ? $option['title'] : '';
        $type             = isset($option['type']) ? $option['type'] : '';
        $rating           = isset($option['rating']) ? $option['rating'] : '';
        $topic            = isset($option['topic']) ? $option['topic'] : '';
        $province         = isset($option['province']) ? $option['province'] : '';
        $city             = isset($option['city']) ? $option['city'] : '';
        $operId           = isset($option['operId']) ? $option['operId'] : '';
        $isShowDistribute = isset($option['isShowDistribute']) ? $option['isShowDistribute'] : true;
        $online           = isset($option['online']) ? $option['online'] : false;
        $lids             = isset($option['lids']) ? $option['lids'] : '';

        $data = [
            'account_ids'   => (string)$memberId,
            'pagerno'       => (int)$page,
            'pagerrows'     => (int)$pageSize,
            'shop'          => $channel,
            'name'          => $title,
            'type'          => $type,
            'rating'        => $rating,
            'topic'         => $topic,
            'province'      => $province,
            'city'          => $city,
            'operator_id'   => $operId,
            'distribute'    => $isShowDistribute,
            'online'        => $online,
            'lids'          => $lids
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::_WINDOW_PRODUCT_LIST_, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 二级店铺产品列表
     * <AUTHOR>
     * @date
     *
     * @param string $memberId  多个用户用 ，分割
     * @param int    $page
     * @param int    $pageSize
     * @param array  $option 数组
     * @param string $channel           产品渠道
     * @param string $title             产品名字
     * @param string $type              产品类型
     * @param string $rating            等级
     * @param string $topic             话题
     * @param string $province          省份code
     * @param string $city              城市code
     * @param int    $operId            操作人id
     * @param int    $isShowDistribute  是否显示自供应+转分销
     * @param int    $online 是否只显示在线产品
     *
     * @return array
     *
     */
    public static function getPCShopProductList($memberId, $page, $pageSize, $option = [])
    {
        if (!$memberId || !$page || !$pageSize) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $channel          = isset($option['channel']) ? $option['channel'] : '';
        $title            = isset($option['title']) ? $option['title'] : '';
        $type             = isset($option['type']) ? $option['type'] : '';
        $rating           = isset($option['rating']) ? $option['rating'] : '';
        $topic            = isset($option['topic']) ? $option['topic'] : '';
        $province         = isset($option['province']) ? $option['province'] : '';
        $city             = isset($option['city']) ? $option['city'] : '';
        $operId           = isset($option['operId']) ? $option['operId'] : '';
        $isShowDistribute = isset($option['isShowDistribute']) ? $option['isShowDistribute'] : true;
        $online           = isset($option['online']) ? $option['online'] : false;

        $data = [
            'account_ids'   => (string)$memberId,
            'page_size'     => (int)$pageSize,
            'page_num'      => (int)$page,
            'shop'          => $channel,
            'title'         => $title,
            'type'          => $type,
            'hotel_star'    => $rating,
            'topic'         => $topic,
            'province'      => $province,
            'city'          => $city,
            'operator_id'   => $operId,
            'distribute'    => $isShowDistribute,
            'pay_way_online'=> $online
        ];


        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::_PC_RETAIL_PRODUCT_LIST_, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取景区的产品列表数据格式
     * --- 微商城和二级店铺目前使用
     * @param int   $salerId  景区id
     * @param array $option 数组
     * @param string $channel           产品渠道
     * @param string $title             产品名字
     * @param string $type              产品类型
     * @param string $rating            等级
     * @param string $topic             话题
     * @param string $province          省份code
     * @param string $city              城市code
     * @param int    $operId            操作人id
     * @param int    $isShowDistribute  是否显示自供应+转分销
     * @param int    $online 是否只显示在线产品
     *
     * @return array
     *
     */
    public static function getSalerRetailProductList($salerId, $option = [])
    {
        if (!$salerId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $channel          = isset($option['channel']) ? $option['channel'] : '';
        $operId           = isset($option['operId']) ? (int)$option['operId'] : '';

        $data = [
            'saler_id'    => (int)$salerId,
            'shop'         => $channel,
            'operator_id' => $operId
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::_SALER_RETAIL_PRODUCT_LIST_, [], 'POST', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 根据lid获取底下tid被打包的相关套票
     * --- 微商城在使用 -- 预定页面-相关套票功能
     * @param int $memberId
     * @param int $page
     * @param int $pageSize
     * @param array $option 数组
     * @param string $channel           产品渠道
     * @param string $title             产品名字
     * @param string $type              产品类型
     * @param string $rating            等级
     * @param string $topic             话题
     * @param string $province          省份code
     * @param string $city              城市code
     * @param int    $operId            操作人id
     * @param int    $isShowDistribute  是否显示自供应+转分销
     * @param int    $online 是否只显示在线产品
     *
     * @return array
     *
     */
    public static function getRecommendProductList($memberId, $landId, $option = [])
    {
        if (!$memberId || !$landId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $channel          = isset($option['channel']) ? (string)$option['channel'] : '';
        $operId           = isset($option['operId']) ? (int)$option['operId'] : '';

        $data = [
            'account_id'  => (int)$memberId,
            'item_id'     => (int)$landId,
            'shop'        => $channel,
            'operator_id' => $operId
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::_RECOMMEND_PRODUCT_LIST_, [], 'POST', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取会员可售的产品列表信息
     * <AUTHOR>
     * @date   2018-07-20
     * 
     * @param  int      $memberId 会员id
     * @param  int      $page     当前页数
     * @param  int      $size     每页条数
     * @param  int      $type     类型
     * @param  int      $channel  渠道
     * @param  int      $Distribution  是否显示转分销
     * @return array
     */
    public static function getVendibilityProductList(
        $memberId,
        $page,
        $size,
        $type,
        $channel,
        $Distribution   ) {

        $data = [
            'account_id'        => (int)$memberId,
            'page_num'          => (int)$page,
            'page_size'         => (int)$size,
            'type'              => $type,
            'shop'              => $channel,
            'show_distribution' => $Distribution
        ];

        $result = self::_customCurlPost(self::__AVAILABLE_SALE_LIST__, [], 'GET', $data);
        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return [];
        }
    }

    /**
     * 获取出售中的自供应的非特殊票
     * Create by zhangyangzhen
     * Date: 2018/8/14
     * Time: 18:00
     * @param $account_id
     * @param int $page
     * @param int $pageSize
     * @param array $option
     * @param DataAuthLimit|null $dataAuthLimit 产品数据权限
     *
     * @return array
     */
    public static function getUnspecialSaleProList($account_id, $page = 1, $pageSize = 10, $option = [], ?DataAuthLimit $dataAuthLimit = null)
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province         = isset($option['province']) ? $option['province'] : '';         //省份code
        $city             = isset($option['city']) ? $option['city'] : '';                 //城市code
        $oversea          = isset($option['oversea']) ? $option['oversea'] : '';                 //境内外code
        $type             = isset($option['type']) ? $option['type'] : '';                 //景区类型，多个类型使用逗号分隔
        $terminal_num     = isset($option['terminal_num']) ? $option['terminal_num'] : ''; //终端号
        $start_date       = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date         = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name        = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id          = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code        = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $only_show_item   = isset($option['only_show_item']) ? $option['only_show_item'] : false; //是否仅列出产品
        $show_expire      = isset($option['show_expire']) ? $option['show_expire'] : false;       //是否显示过期产品

        //java需要字符串的 true false
        $only_show_item   = $only_show_item === true ? "true" : "false";
        $show_expire      = $show_expire === true ? "true" : "false";

        $data = [
            "account_id"       => (int)$account_id ,
            "province"         => $province ,
            "city"             => $city ,
            "oversea"          => $oversea ,
            "type"             => $type ,
            "terminal_num"     => $terminal_num ,
            "start_date"       => $start_date ,
            "end_date"         => $end_date ,
            "item_name"        => $item_name ,
            "item_id"          => $item_id ,
            "item_code"        => $item_code ,
            "only_show_item"   => $only_show_item ,
            "show_expire"      => $show_expire ,
            "page_num"         => (int)$page ,
            "page_size"        => (int)$pageSize
        ];

        if (isset($option['lidList'])) {
            $data['lidList'] = implode(',', $option['lidList']);
        }
        if (isset($option['notLidList'])) {
            $data['notLidList'] = implode(',', $option['notLidList']);
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_UNSPECIAL_SALE_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }


    /**
     * 获取自供应产品列表(出售中)  -- 参数核对完成
     * @date   2018-06-12
     * <AUTHOR>
     *
     * @param  int      $account_id    账号Id           必须
     * @param  int      $page          当前页数        默认值：1
     * @param  int      $pageSize      每页条数        默认值：10
     *
     * @param  array    $option 数组
     * @param  int      $province       省份code
     * @param  int      $city           城市code
     * @param  string   $type           景区类型，多个类型使用逗号分隔
     * @param  int      $terminal_num   终端号
     * @param  string   $start_date     发布产品开始时间
     * @param  string   $end_date       发布产品结束的时间
     * @param  string   $item_name      产品名称
     * @param  int      $item_id        产品id
     * @param  int      $item_code      6位数产品编号
     * @param  bool     $only_show_item 仅列出产品:true是;false;	 默认值:false
     * @param  bool     $expire         过期：true是;false否      默认值:false
     *
     * @return array
     */
    public static function getSaleProList($account_id, $page = 1, $pageSize = 10, $option = [])
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province         = isset($option['province']) ? $option['province'] : '';         //省份code
        $city             = isset($option['city']) ? $option['city'] : '';                 //城市code
        $oversea          = isset($option['oversea']) ? $option['oversea'] : '';                 //境内外code
        $type             = isset($option['type']) ? $option['type'] : '';                 //景区类型，多个类型使用逗号分隔
        $terminal_num     = isset($option['terminal_num']) ? $option['terminal_num'] : ''; //终端号
        $start_date       = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date         = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name        = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id          = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code        = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $only_show_item   = isset($option['only_show_item']) ? $option['only_show_item'] : false; //是否仅列出产品
        $show_expire      = isset($option['show_expire']) ? $option['show_expire'] : false;       //是否显示过期产品
        $show_ticket_num  = isset($option['show_ticket_num']) ? $option['show_ticket_num'] : 3;    //展示票的数量
        $about_expire     = isset($option['about_expire']) ? $option['about_expire'] : false;       //是否显示即将过期产品
        $subtype          = isset($option['sub_type']) ? $option['sub_type'] : 0;       //计时产品子类型
        $labels           = $option['labels'] ?? ''; //产品来源
        $subSid           = $option['subSid'] ?? ''; //子商户id
        //java需要字符串的 true false
        $only_show_item   = $only_show_item === true ? "true" : "false";
        $show_expire      = $show_expire === true ? "true" : "false";
        $about_expire     = $about_expire === true ? "true" : "false";

        $data = [
            "account_id"       => (int)$account_id ,
            "province"         => $province ,
            "city"             => $city ,
            "oversea"          => $oversea ,
            "type"             => $type ,
            "terminal_num"     => $terminal_num ,
            "start_date"       => $start_date ,
            "end_date"         => $end_date ,
            "item_name"        => $item_name ,
            "item_id"          => $item_id ,
            "item_code"        => $item_code ,
            "only_show_item"   => $only_show_item ,
            "show_expire"      => $show_expire ,
            "page_num"         => (int)$page ,
            "page_size"        => (int)$pageSize,
            "show_ticket_num"  => (int)$show_ticket_num,
            "about_expire"     => $about_expire,
            "subType"          => $subtype,
            'labels'           => $labels,
            'subSid'           => $subSid,
        ];

        if (isset($option['lidList'])) {
            $data['lidList'] = implode(',', $option['lidList']);
        }
        if (isset($option['notLidList'])) {
            $data['notLidList'] = implode(',', $option['notLidList']);
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_SALE_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取自供应产品列表(出售中) #管理员 -- 参数核对完成
     * @date   2018-07-13
     * <AUTHOR>
     *
     * @param  int      $account_id    账号Id           必须
     * @param  int      $page          当前页数        默认值：1
     * @param  int      $pageSize      每页条数        默认值：10
     *
     * @param  array    $option 数组
     * @param  int      $province       省份code
     * @param  int      $city           城市code
     * @param  string   $type           景区类型，多个类型使用逗号分隔
     * @param  int      $terminal_num   终端号
     * @param  string   $start_date     发布产品开始时间
     * @param  string   $end_date       发布产品结束的时间
     * @param  string   $item_name      产品名称
     * @param  int      $item_id        产品id
     * @param  int      $item_code      6位数产品编号
     * @param  bool     $only_show_item 仅列出产品:true是;false;	 默认值:false
     * @param  bool     $expire         过期：true是;false否      默认值:false
     * @param  int      $supplier_id      供应商id    # 管理员有
     * @param  bool     $only_show_filter 是否过滤测试产品  默认值:false  # 管理员有
     *
     * @return array
     */
    public static function getAdminSaleProList($account_id, $page = 1, $pageSize = 10, $option = [])
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province         = isset($option['province']) ? $option['province'] : '';         //省份code
        $city             = isset($option['city']) ? $option['city'] : '';                 //城市code
        $oversea          = isset($option['oversea']) ? $option['oversea'] : '';                 //城市code
        $type             = isset($option['type']) ? $option['type'] : '';                 //景区类型，多个类型使用逗号分隔
        $terminal_num     = isset($option['terminal_num']) ? $option['terminal_num'] : ''; //终端号
        $start_date       = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date         = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name        = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id          = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code        = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $only_show_item   = isset($option['only_show_item']) ? $option['only_show_item'] : false;      //是否仅列出产品
        $show_expire      = isset($option['show_expire']) ? $option['show_expire'] : false;            //是否显示过期产品
        $supplier_id      = isset($option['supplier_id']) ? $option['supplier_id'] : "";               //供应商id
        $only_show_filter = isset($option['only_show_filter']) ? $option['only_show_filter'] : false;  //是否过滤测试产品
        $only_show_relevance = isset($option['is_relevance']) ? $option['is_relevance'] : false;     //是否过滤关联资源
        $show_ticket_num     = isset($option['show_ticket_num']) ? $option['show_ticket_num'] : 3;    //展示票的数量
        //java需要字符串的 true false
        $only_show_item   = $only_show_item === true ? "true" : "false";
        $show_expire      = $show_expire === true ? "true" : "false";
        $only_show_filter = $only_show_filter === true ? "true" : "false";
        $only_show_relevance = $only_show_relevance === true ? "true" : "false";
        $filterNotRelationLand = $option['is_management'] == 1 ? "true" : "false";

        $data = [
            "account_id"          => (int)$account_id ,
            "province"            => $province ,
            "city"                => $city ,
            "oversea"             => $oversea ,
            "type"                => $type ,
            "terminal_num"        => $terminal_num ,
            "start_date"          => $start_date ,
            "end_date"            => $end_date ,
            "item_name"           => $item_name ,
            "item_id"             => $item_id ,
            "item_code"           => $item_code ,
            "only_show_item"      => $only_show_item ,
            "show_expire"         => $show_expire ,
            "page_num"            => (int)$page ,
            "page_size"           => (int)$pageSize ,
            "supplier_id"         => $supplier_id ,
            "filter_test_product" => $only_show_filter,
            "filte_resource_id"   => $only_show_relevance,
            'show_ticket_num'     => (int)$show_ticket_num,
            'filterNotRelationLand'     => $filterNotRelationLand
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_ADMIN_SALE_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取自供应产品列表（仓库中）  -- 参数核完成
     * @date   2018-06-12
     * <AUTHOR>
     *
     * @param  int      $account_id    账号Id           必须
     * @param  int      $page          当前页数        默认值：1
     * @param  int      $pageSize      每页条数        默认值：10
     *
     * @param  array    $option 数组
     * @param  int      $province       省份code
     * @param  int      $city           城市code
     * @param  string   $type           景区类型，多个类型使用逗号分隔
     * @param  int      $terminal_num   终端号
     * @param  string   $start_date     发布产品开始时间
     * @param  string   $end_date       发布产品结束的时间
     * @param  string   $item_name      产品名称
     * @param  int      $item_id        产品id
     * @param  int      $item_code      6位数产品编号
     * @param  bool     $only_show_item 仅列出产品:true是;false;	   默认值:false
     * @param  bool     $show_delete    是否显示删除的产品：true是;false否    默认值:false
     *
     * @return array
     */
    public static function getExpiredProList($account_id, $page = 1, $pageSize = 10, $option = [])
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province        = isset($option['province']) ? $option['province'] : '';        //省份code
        $city            = isset($option['city']) ? $option['city'] : '';                //城市code
        $oversea         = isset($option['oversea']) ? $option['oversea'] : '';                 //境内外code
        $type            = isset($option['type']) ? $option['type'] : '';                //景区类型，多个类型使用逗号分隔
        $terminal_num    = isset($option['terminal_num']) ? $option['terminal_num'] : '';//终端号
        $start_date      = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date        = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name       = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id         = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code       = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $only_show_item  = isset($option['only_show_item']) ? $option['only_show_item'] : false; //是否仅列出产品
        $show_delete     = isset($option['show_delete']) ? $option['show_delete'] : false;       //是否显示删除的产品
        $show_ticket_num = isset($option['show_ticket_num']) ? $option['show_ticket_num'] : 3;    //展示票的数量
        $subtype         = isset($option['sub_type']) ? $option['sub_type'] : 0;       //计时产品子类型
        $subSid           = $option['subSid'] ?? ''; //子商户id
        //java需要字符串的 true false
        $only_show_item   = $only_show_item === true ? "true" : "false";
        $show_delete      = $show_delete === true ? "true" : "false";


        $data = [
            "account_id"       => (int)$account_id ,
            "province"         => $province ,
            "oversea"          => $oversea ,
            "city"             => $city ,
            "type"             => $type ,
            "terminal_num"     => $terminal_num ,
            "start_date"       => $start_date ,
            "end_date"         => $end_date ,
            "item_name"        => $item_name ,
            "item_id"          => $item_id ,
            "item_code"        => $item_code ,
            "only_show_item"   => $only_show_item ,
            "show_delete"      => $show_delete ,
            "page_num"         => (int)$page ,
            "page_size"        => (int)$pageSize,
            'show_ticket_num'  => (int)$show_ticket_num,
            "subType"          => $subtype,
            'subSid'           => $subSid,
        ];

        if (isset($option['lidList'])) {
            $data['lidList'] = implode(',', $option['lidList']);
        }
        if (isset($option['notLidList'])) {
            $data['notLidList'] = implode(',', $option['notLidList']);
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_EXPIRED_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取自供应产品列表（仓库中） #管理员  -- 参数核对完成
     * @date   2018-06-12
     * <AUTHOR>
     *
     * @param  int      $account_id    账号Id           必须
     * @param  int      $page          当前页数        默认值：1
     * @param  int      $pageSize      每页条数        默认值：10
     *
     * @param  array    $option 数组
     * @param  int      $province       省份code
     * @param  int      $city           城市code
     * @param  string   $type           景区类型，多个类型使用逗号分隔
     * @param  int      $terminal_num   终端号
     * @param  string   $start_date     发布产品开始时间
     * @param  string   $end_date       发布产品结束的时间
     * @param  string   $item_name      产品名称
     * @param  int      $item_id        产品id
     * @param  int      $item_code      6位数产品编号
     * @param  bool     $only_show_item 仅列出产品:true是;false;	   默认值:false
     * @param  bool     $show_delete    是否显示删除的产品：true是;false否    默认值:false
     * @param  int      $supplier_id      供应商id    # 管理员有
     * @param  bool     $only_show_filter 是否过滤测试产品  默认值:false  # 管理员有
     *
     * @return array
     */
    public static function getAdminExpiredProList($account_id, $page = 1, $pageSize = 10, $option = [])
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province         = isset($option['province']) ? $option['province'] : '';        //省份code
        $city             = isset($option['city']) ? $option['city'] : '';                //城市code
        $oversea          = isset($option['oversea']) ? $option['oversea'] : '';                 //境内外code
        $type             = isset($option['type']) ? $option['type'] : '';                //景区类型，多个类型使用逗号分隔
        $terminal_num     = isset($option['terminal_num']) ? $option['terminal_num'] : '';//终端号
        $start_date       = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date         = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name        = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id          = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code        = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $only_show_item   = isset($option['only_show_item']) ? $option['only_show_item'] : false; //是否仅列出产品
        $show_delete      = isset($option['show_delete']) ? $option['show_delete'] : false;       //是否显示删除的产品
        $supplier_id      = isset($option['supplier_id']) ? $option['supplier_id'] : "";             //供应商id
        $only_show_filter = isset($option['only_show_filter']) ? $option['only_show_filter'] : false;  //是否过滤测试产品
        $show_ticket_num     = isset($option['show_ticket_num']) ? $option['show_ticket_num'] : 3;    //展示票的数量
        //java需要字符串的 true false
        $only_show_item   = $only_show_item === true ? "true" : "false";
        $show_delete      = $show_delete === true ? "true" : "false";
        $only_show_filter = $only_show_filter === true ? "true" : "false";

        $data = [
            "account_id"          => (int)$account_id ,
            "province"            => $province ,
            "oversea"             => $oversea ,
            "city"                => $city ,
            "type"                => $type ,
            "terminal_num"        => $terminal_num ,
            "start_date"          => $start_date ,
            "end_date"            => $end_date ,
            "item_name"           => $item_name ,
            "item_id"             => $item_id ,
            "item_code"           => $item_code ,
            "only_show_item"      => $only_show_item ,
            "show_delete"         => $show_delete ,
            "page_num"            => (int)$page ,
            "page_size"           => (int)$pageSize ,
            "supplier_id"         => $supplier_id ,
            "filter_test_product" => $only_show_filter,
            'show_ticket_num'     => (int)$show_ticket_num
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_ADMIN_EXPIRED_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取自供应产品列表（未发布的）  --
     * @date   2018-06-12
     * <AUTHOR>
     *
     * @param  int      $account_id    账号Id           必须 1
     * @param  int      $page          当前页数        默认值：1 1
     * @param  int      $pageSize      每页条数        默认值：10 1
     *
     * @param  array    $option 数组
     * @param  int      $province       省份code 1
     * @param  int      $city           城市code 1
     * @param  string   $type           景区类型，多个类型使用逗号分隔 1
     * @param  int      $terminal_num   终端号 1
     * @param  string   $start_date     发布产品开始时间 1
     * @param  string   $end_date       发布产品结束的时间 1
     * @param  string   $item_name      产品名称 1
     * @param  int      $item_id        产品id 1
     * @param  int      $item_code      6位数产品编号 1
     * @param DataAuthLimit|null $dataAuthLimit 产品数据权限
     *
     * @return array
     */
    public static function getUnpublishProList($account_id, $page = 1, $pageSize = 10, $option = [], ?DataAuthLimit $dataAuthLimit = null)
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province         = isset($option['province']) ? $option['province'] : '';        //省份code
        $city             = isset($option['city']) ? $option['city'] : '';                //城市code
        $oversea          = isset($option['oversea']) ? $option['oversea'] : '';                 //境内外code
        $type             = isset($option['type']) ? $option['type'] : '';                //景区类型，多个类型使用逗号分隔
        $terminal_num     = isset($option['terminal_num']) ? $option['terminal_num'] : '';//终端号
        $start_date       = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date         = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name        = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id          = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code        = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $subtype          = isset($option['sub_type']) ? $option['sub_type'] : 0;       //计时产品子类型
        $subSid           = $option['subSid'] ?? ''; //子商户id

        $data = [
            "account_id"       => (int)$account_id ,
            "province"         => $province ,
            "city"             => $city ,
            "oversea"          => $oversea ,
            "type"             => $type ,
            "terminal_num"     => $terminal_num ,
            "start_date"       => $start_date ,
            "end_date"         => $end_date ,
            "item_name"        => $item_name ,
            "item_id"          => $item_id ,
            "item_code"        => $item_code ,
            "page_num"         => (int)$page ,
            "page_size"        => (int)$pageSize,
            "subType"          => $subtype,
            'subSid'           => $subSid,
        ];

        if (isset($option['lidList'])) {
            $data['lidList'] = implode(',', $option['lidList']);
        }
        if (isset($option['notLidList'])) {
            $data['notLidList'] = implode(',', $option['notLidList']);
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_UNPUBLISH_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }


    /**
     * 获取自供应产品列表（未发布的） #管理员
     * @date   2018-06-12
     * <AUTHOR>
     *
     * @param  int      $account_id    账号Id           必须 1
     * @param  int      $page          当前页数        默认值：1 1
     * @param  int      $pageSize      每页条数        默认值：10 1
     *
     * @param  array    $option 数组
     * @param  int      $province       省份code 1
     * @param  int      $city           城市code 1
     * @param  string   $type           景区类型，多个类型使用逗号分隔 1
     * @param  int      $terminal_num   终端号 1
     * @param  string   $start_date     发布产品开始时间 1
     * @param  string   $end_date       发布产品结束的时间 1
     * @param  string   $item_name      产品名称 1
     * @param  int      $item_id        产品id 1
     * @param  int      $item_code      6位数产品编号 1
     * @param  int      $supplier_id      供应商id    # 管理员有 1
     * @param  bool     $only_show_filter 是否过滤测试产品  默认值:false
     *
     * @return array
     */
    public static function getAdminUnpublishProList($account_id, $page = 1, $pageSize = 10, $option = [])
    {
        if (!$account_id) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $province         = isset($option['province']) ? $option['province'] : '';        //省份code
        $city             = isset($option['city']) ? $option['city'] : '';                //城市code
        $oversea          = isset($option['oversea']) ? $option['oversea'] : '';                 //境内外code
        $type             = isset($option['type']) ? $option['type'] : '';                //景区类型，多个类型使用逗号分隔
        $terminal_num     = isset($option['terminal_num']) ? $option['terminal_num'] : '';//终端号
        $start_date       = isset($option['start_date']) ? $option['start_date'] : '';     //起始日期
        $end_date         = isset($option['end_date']) ? $option['end_date'] : '';         //结束日期
        $item_name        = isset($option['item_name']) ? $option['item_name'] : '';       //产品名称
        $item_id          = isset($option['item_id']) ? $option['item_id'] : '';           //产品Id
        $item_code        = isset($option['item_code']) ? $option['item_code'] : '';       //产品编号
        $supplier_id      = isset($option['supplier_id']) ? $option['supplier_id'] : "";             //供应商id
        $only_show_filter = isset($option['only_show_filter']) ? $option['only_show_filter'] : false;  //是否过滤测试产品

        //java需要字符串的 true false
        $only_show_filter = $only_show_filter === true ? "true" : "false";

        $data = [
            "account_id"           => (int)$account_id ,
            "province"             => $province ,
            "city"                 => $city ,
            "oversea"              => $oversea ,
            "type"                 => $type ,
            "terminal_num"         => $terminal_num ,
            "start_date"           => $start_date ,
            "end_date"             => $end_date ,
            "item_name"            => $item_name ,
            "item_id"              => $item_id ,
            "item_code"            => $item_code ,
            "page_num"             => (int)$page ,
            "page_size"            => (int)$pageSize ,
            "supplier_id"          => $supplier_id ,
            "filter_test_product"  => $only_show_filter
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_ADMIN_UNPUBLISH_PRODUCT_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取积分商城产品预定列表
     * <AUTHOR>
     * @date   2018-08-03
     *
     * @param int     $sid          供应商id
     * @param int     $page_size    每页显示条数，默认5
     * @param int     $page_num     当前页数，默认1
     * 
     * @param  array  $option       数组
     * @param string  $title        产品名称
     * @param int     $type         产品类型
     * @param string  $topic        旅游主题
     * @param string  $province     省份code
     * @param string  $city         城市code
     * @param string  $hotel_star   酒店星级
     *
     * @return array
     *
     */
    public static function getIntegralMallReserveList($sid, $page = 1, $pageSize = 5, $option)
    {
        if (!$sid || !$page || !$pageSize) {
            return self::returnDataStatic(203, '参数缺失');
        }

        $title            = isset($option['title']) ? $option['title'] : '';
        $type             = isset($option['type']) ? $option['type'] : '';
        $topic            = isset($option['topic']) ? $option['topic'] : '';
        $province         = isset($option['province']) ? $option['province'] : '';
        $city             = isset($option['city']) ? $option['city'] : '';
        $hotelStar        = isset($option['hotelStar']) ? $option['hotelStar'] : '';

        $data = [
            'account_id'    => (int)$sid,
            'page_num'      => (int)$page,
            'page_size'     => (int)$pageSize,
            'title'         => $title,
            'type'          => $type,
            'topic'         => $topic,
            'province'      => $province,
            'city'          => $city,
            'hotel_star'    => $hotelStar
        ];

        $result = self::_customCurlPost(self::__GET_INTEGRAL_MALL_RESERVE_LIST__, [], 'GET', $data);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $list = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $list);
    }

    /**
     * 修改Land表的letters字段
     *
     * ps:已迁移到Business/JavaApi/CommodityCenter/Land.class.php updateLandLetters
     *
     * <AUTHOR>
     * @date   2018-10-08
     * @param  array      $data     id和letter的数组
     *
     * @return array
     *
     * @deprecated
     * @see \Business\JavaApi\CommodityCenter\Land::updateLandLetters()
     */
    public static function updateLandLetters($data)
    {
        $logData = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        pft_log('debug/land_move', 'updateLandLetters:' . $logData);
        $result = self::_customCurlPost(self::__LAND_LETTERS__, $data, 'POST');
        if (isset($result['code'])) {
            return $result;
        } else {
            return self::returnDataStatic(500, '接口异常');
        }
    }

    /**
     * 获取资源页可关联的产品列表
     * <AUTHOR>
     * @date   2018-10-08
     * @param  int     $province   省
     * @param  int     $city       市
     * @param  string  $title      产品名称
     * @param  int     $page       页码
     * @param  int     $size       条目
     * @param  string  $ptype      产品类型
     * @param  int     $resourceId 资源id
     * @return array
     */
    public static function getResourceProductList($province, $city ,$title, $page, $size, $ptype = 'A', $resourceId = 0)
    {
        if (!$title || !$page || !$size) {
            return self::returnDataStatic(203, '参数缺失', []);
        }
        $data   = [
            'titles'    => $title,
            'page_num'  => (int)$page,
            'page_size' => (int)$size,
            'province'  => $province,
            'city'      => $city,
            'ptype'     => $ptype,
            'resourceId'=> (int)$resourceId
        ];

        $result = self::_customCurlPost(self::__GET_RESOURCE_PRODUCT__, [], 'GET', $data);
        if (isset($result['code'])) {
            return $result;
        } else {
            return self::returnDataStatic(500, '接口异常', []);
        }
    }

    /**
     * 获取去哪儿美团ota所有在售产品
     * <AUTHOR>
     * @date   2018-10-19
     * @param  int      $fid     登录的id
     * @param  int      $page    当前页
     * @param  int      $size    一页个数
     * @param  string   $search  搜索条件
     * @param  array   $arrTids  票id （可传空数组，如需要获取下架的票可以传票id过去）
     * @return array
     */

    public static function getOtaAllTicket($fid,$page,$size,$search = '',$arrTids = [],$lid = '',$ticketId = '') {
        if (!$fid || !$page || !$size) {
            return self::returnDataStatic(203, '参数缺失');
        }
        $data = [
            'accountId'     => (int)$fid,
            'pageNum'       => (int)$page,
            'pageSize'      => (int)$size,
            'ticketIds'     => $arrTids,
            'title'         => $search,
        ];
        if (!empty($lid) && is_numeric($lid)) {
            $data['lid'] = $lid;
        }
        if (!empty($ticketId) && is_numeric($ticketId)) {
            $data['ticketId'] = $ticketId;
        }
        $result = self::_customCurlPost(self::__GET_QUNAER_LIST__, $data, 'POST');
        if (isset($result['code'])) {
            return $result;
        } else {
            return self::returnDataStatic(500, '接口异常');
        }
    }
    /**
     * 获取去哪儿美团ota绑定的产品
     * <AUTHOR>
     * @date   2018-10-19
     * @param  int      $fid     登录的id
     * @param  int      $page    当前页
     * @param  int      $size    一页个数
     * @param  string   $search  搜索条件
     * @param  array   $arrTids  票id （可传空数组，如需要获取下架的票可以传票id过去）
     * @return array
     */
    public static function getOtaBindingTicket($fid,$page,$size,$search = '',$arrTids = [],$lid = '',$ticketId = '') {
        if (!$fid || !$page || !$size) {
            return self::returnDataStatic(203, '参数缺失');
        }
        $data = [
            'accountId'     => (int)$fid,
            'pageNum'       => (int)$page,
            'pageSize'      => (int)$size,
            'ticketIds'     => $arrTids,
            'title'         => $search
        ];
        if (!empty($lid) && is_numeric($lid)) {
            $data['lid'] = $lid;
        }
        if (!empty($ticketId) && is_numeric($ticketId)) {
            $data['ticketId'] = $ticketId;
        }
        $result = self::_customCurlPost(self::__GET_QUNAER_BIND_LIST__, $data, 'POST');
        if (isset($result['code'])) {
            return $result;
        } else {
            return self::returnDataStatic(500, '接口异常');
        }
    }
    /**
     * 获取去哪儿美团ota未绑定的产品
     * <AUTHOR>
     * @date   2018-10-19
     * @param  int      $fid     登录的id
     * @param  int      $page    当前页
     * @param  int      $size    一页个数
     * @param  string   $search  搜索条件
     * @param  array   $arrTids  票id （可传空数组，如需要获取下架的票可以传票id过去）
     * @param  int      $sid    供应商id
     *
     * @return array
     */
    public static function getOtaUnBindingTicket($fid,$page,$size,$search = '',$arrTids = [],$lid = '',$ticketId = '', $sid = 0, $pTypes = []) {
        if (!$fid || !$page || !$size) {
            return self::returnDataStatic(203, '参数缺失');
        }
        $data = [
            'accountId'     => (int)$fid,
            'pageNum'       => (int)$page,
            'pageSize'      => (int)$size,
            'ticketIds'     => $arrTids,
            'title'         => $search
        ];

        if (!empty($sid)) {
            $data['sid'] = $sid;
        }

        if (!empty($lid) && is_numeric($lid)) {
            $data['lid'] = $lid;
        }
        if (!empty($ticketId) && is_numeric($ticketId)) {
            $data['ticketId'] = $ticketId;
        }
        if (!empty($pTypes)) {
            $data['pTypes'] = $pTypes;
        }
        $result = self::_customCurlPost(self::__GET_QUNAER_UNBIND_LIST__, $data, 'POST');
        if (isset($result['code'])) {
            return $result;
        } else {
            return self::returnDataStatic(500, '接口异常');
        }
    }

    /**
     * 获取自供应分销价格配置产品列表
     * @date   2018-01-21
     * <AUTHOR>
     * @param int $accountId  会员Id  是
     * @param int $landId     景区Id 否
     * @param int $groupId    分组Id 否,但分组Id和分销商Id不能同时不填
     * @param int $distributorId   分销商Id	否，但分组Id和分销商Id不能同时不填
     * @param int $pageNum    当前页 否，默认值为1
     * @param int $pageSize   每页显示大小 否，默认值为20
     * @param string $productType 产品类型
     * @param int $ticketId 票类Id
     * @param int $distributionActive 分销开启状态 1：开启，0：未开启  2;全部
     * @return array   /api/v1/pft.product.price.allocation/self/supply
     */
    public static function getSelfProPriceList(
        $accountId,
        $landId             = 0,
        $groupId            = 0,
        $distributorId      = 0,
        $pageNum            = 1,
        $pageSize           = 20,
        $productType        = '',
        $ticketId           = 0,
        $distributionActive = 2
    ){
        if (!$accountId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        //分组Id和分销商Id不能同时不填
        if(!$groupId && !$distributorId){
            return self::returnDataStatic(203, '分组Id和分销商Id不能同时为空');
        }

        $data = [
            "accountId"          => (int)$accountId,       //int 会员Id 是
            "distributorId"      => (int)$distributorId,   //int 分销商Id 否
            "type"               => $productType,          //产品类型
            "ticketId"           => (int)$ticketId,        //int 门票Id
            "landId"             => (int)$landId,          //int 景区Id 否
            "groupId"            => (int)$groupId,         //int 分组Id 否
            "pageSize"           => (int)$pageSize,        //int 每页显示大小 否，默认值为20
            "pageNum"            => (int)$pageNum,         //int 当前页 否，默认值为1
            "active"             => (int)$distributionActive    //分销开启状态 1：开启，0：未开启 2;全部
        ];

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_PRODUCT_PRICE_ALLOCATION_SELF_LIST__, [], 'GET', $httpQuery);

        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取自供应分销价格配置产品列表
     * @date   2019-05-13
     * <AUTHOR>
     * @param int $accountId  会员Id  是
     * @param int $landId     景区Id 否
     * @param int $groupId    分组Id 否,但分组Id和分销商Id不能同时不填
     * @param int $distributorId   分销商Id	否，但分组Id和分销商Id不能同时不填
     * @param int $pageNum    当前页 否，默认值为1
     * @param int $pageSize   每页显示大小 否，默认值为20
     * @param string $productType 产品类型
     * @param int $ticketId 票类Id
     * @param int $distributionActive 分销开启状态 1：开启，0：未开启  2;全部
     * @return array   /api/v1/pft.product.price.allocation/self/supply/land
     */
    public static function getSelfProPriceListTwo(
        $accountId,
        $landId             = 0,
        $groupId            = 0,
        $distributorId      = 0,
        $pageNum            = 1,
        $pageSize           = 20,
        $productType        = '',
        $ticketId           = 0,
        $distributionActive = 2
    ){
        if (!$accountId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        //分组Id和分销商Id不能同时不填
        if(!$groupId && !$distributorId){
            return self::returnDataStatic(203, '分组Id和分销商Id不能同时为空');
        }

        $data = [
            "accountId"          => (int)$accountId,       //int 会员Id 是
            "distributorId"      => (int)$distributorId,   //int 分销商Id 否
            "type"               => $productType,          //产品类型
            "ticketId"           => (int)$ticketId,        //int 门票Id
            "landId"             => (int)$landId,          //int 景区Id 否
            "groupId"            => (int)$groupId,         //int 分组Id 否
            "pageSize"           => (int)$pageSize,        //int 每页显示大小 否，默认值为20
            "pageNum"            => (int)$pageNum,         //int 当前页 否，默认值为1
            "active"             => (int)$distributionActive    //分销开启状态 1：开启，0：未开启 2;全部
        ];
        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_PRODUCT_SELF_LAND__, [], 'GET', $httpQuery);
        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取转分销分销价格配置产品列表
     * @date   2018-01-21
     * <AUTHOR>
     * @param int $accountId  会员Id 是
     * @param int $landId     景区Id 否
     * @param int $groupId    分组Id 否,但分组Id和分销商Id不能同时不填
     * @param int $distributorId   分销商id	否，但分组Id和分销商Id不能同时不填
     * @param int $supplierId   供应商id	否，但分组Id和分销商Id不能同时不填
     * @param int $pageNum    当前页 否，默认值为1
     * @param int $pageSize   每页显示大小 否，默认值为20
     * @param string $productType 产品类型
     * @param int $ticketId 票类Id
     * @param int $distributionActive 分销开启状态 1：开启，0：未开启  2;全部
     * @return array
     */
    public static function getTransfreProPriceList(
        $accountId,
        $landId             = 0,
        $groupId            = 0,
        $distributorId      = 0,
        $supplierId         = 0,
        $pageNum            = 1,
        $pageSize           = 20,
        $productType        = '',
        $ticketId           = 0,
        $distributionActive = 2
    ){
        if (!$accountId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        //分组Id和分销商Id不能同时不填
        if(!$groupId && !$distributorId){
            return self::returnDataStatic(203, '分组Id和分销商Id不能同时为空');
        }

        $data = [
            "accountId"          => (int)$accountId,       //int 会员Id 是
            "landId"             => (int)$landId,          //int 景区Id 否
            "groupId"            => (int)$groupId,         //int 分组Id 否
            "distributorId"      => (int)$distributorId,   //int 分销商Id 否
            "supplierId"         => (int)$supplierId,      //供应商Id
            "pageSize"           => (int)$pageSize,        //int 每页显示大小 否，默认值为20
            "pageNum"            => (int)$pageNum,         //int 当前页 否，默认值为1
            "type"               => $productType,          //产品类型
            "ticketId"           => (int)$ticketId,        //int 门票Id
            "active"             => (int)$distributionActive    //分销开启状态 1：开启，0：未开启 2;全部
        ];
        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_PRODUCT_PRICE_ALLOCATION_TRANSFRE_LIST__, [], 'GET', $httpQuery);
        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 获取转分销分销价格配置产品列表
     * @date   2018-01-21
     * <AUTHOR>
     * @param int $accountId  会员Id 是
     * @param int $landId     景区Id 否
     * @param int $groupId    分组Id 否,但分组Id和分销商Id不能同时不填
     * @param int $distributorId   分销商id	否，但分组Id和分销商Id不能同时不填
     * @param int $supplierId   供应商id	否，但分组Id和分销商Id不能同时不填
     * @param int $pageNum    当前页 否，默认值为1
     * @param int $pageSize   每页显示大小 否，默认值为20
     * @param string $productType 产品类型
     * @param int $ticketId 票类Id
     * @param int $distributionActive 分销开启状态 1：开启，0：未开启  2;全部
     * @return array
     */
    public static function getTransfreProPriceListTwo(
        $accountId,
        $landId             = 0,
        $groupId            = 0,
        $distributorId      = 0,
        $supplierId         = 0,
        $pageNum            = 1,
        $pageSize           = 20,
        $productType        = '',
        $ticketId           = 0
    ){
        if (!$accountId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        //分组Id和分销商Id不能同时不填
        if(!$groupId && !$distributorId){
            return self::returnDataStatic(203, '分组Id和分销商Id不能同时为空');
        }

        $data = [
            "accountId"          => (int)$accountId,       //int 会员Id 是
            "landId"             => (int)$landId,          //int 景区Id 否
            "groupId"            => (int)$groupId,         //int 分组Id 否
            "distributorId"      => (int)$distributorId,   //int 分销商Id 否
            "supplierId"         => (int)$supplierId,      //供应商Id
            "pageSize"           => (int)$pageSize,        //int 每页显示大小 否，默认值为20
            "pageNum"            => (int)$pageNum,         //int 当前页 否，默认值为1
            "type"               => $productType,          //产品类型
            "ticketId"           => (int)$ticketId,        //int 门票Id
        ];
        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_PRODUCT_DIS_LAND__, [], 'GET', $httpQuery);
        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        // 返回java的错误码 和中文错误提示
        return self::returnDataStatic($result['code'], $msg, $data);
    }

    /**
     * 旧版价格配置产品列表
     * @param $memberId 会员Id
     * @param $distributionType 转分销类型:0 自供应, 1 转分销
     * @param $ticketName 票名称
     * @param $pageNum 当前页
     * @param $pageSize 每页显示大小
     * Author: yangwx
     * Date: 2019/9/18 0018
     */
    public function getOldPriceProduct($memberId, $distributionType, $ticketName = '', $pageNum = 1, $pageSize = 20)
    {

        if (!$memberId) {
            return self::returnDataStatic(203, '参数缺失');
        }

        if (!in_array($distributionType, [0, 1])) {
            return self::returnDataStatic(203, '参数错误');
        }

        $data = [
            "memberId"          => (int)$memberId,
            "distributionType"  => (int)$distributionType,
            "pageSize"          => (int)$pageSize,
            "pageNum"           => (int)$pageNum
        ];

        if (!empty($ticketName)) {
            $data['ticketName'] = $ticketName;
        }

        $httpQuery = http_build_query($data);
        $result = self::_customCurlPost(self::__GET_PRODUCT_PRICE_LIST__, [], 'GET', $httpQuery);
        if (!isset($result['code'])) {
            return ['code' => 500, 'msg' => '服务器错误'];
        }
        $msg  = self::javaReturnCode($result['code']);
        $data = isset($result['data']) ? $result['data'] : [];

        return self::returnDataStatic($result['code'], $msg, $data);
    }
}
