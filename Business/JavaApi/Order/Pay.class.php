<?php
/**
 * 支付相关接口
 *
 * <AUTHOR>
 * @date    2019-06-27
 */
namespace Business\JavaApi\Order;

use \Business\JavaApi\CallBase;

class Pay extends CallBase
{

    public function __construct()
    {
        $tmpList = load_config('order', 'javaApiUrl');
        $apiInfo = $tmpList['pay'];

        if (!$apiInfo) {
            return false;
        }
        parent::__construct($apiInfo, 'pay', 2);
    }


    /**
     * 订单支付接口
     *
     * @param array     $orderArr[{"orderId" : "12312312"}, ]
     * @param string    $tradeNo 第三方交易流水号
     * @param integer   $totalMoney 总金额
     * @param integer   $paymode 支付方式
     * @param integer   $subjectCode 支付账本
     * @param integer   $oper 操作人
     * @param string    $paytime 支付时间 datetime
     * @param string    $remark 备注
     * @param array     $options 更多参数选项
     * @return array
     * <AUTHOR>
     */
    public function pay(array $orderArr, $tradeNo, $totalMoney, $paymode, $subjectCode, $oper = 0, $paytime = '', $channel = 0, $remark = '', $options = []) {
        if (!$orderArr) {
            return $this->returnData(204, '参数错误');
        }

        $javaParams = [
            'orders'      => $orderArr,
            'tradeNo'     => strval($tradeNo),
            'payMoney'    => intval($totalMoney),
            'payMode'     => intval($paymode),
            'subjectCode' => intval($subjectCode),
            'operMember'  => intval($oper),
            'channel'     => intval($channel),
            'source'      => intval($channel),
            'payDate'     => $paytime ?: date('Y-m-d H:i:s'),
            'remark'      => strval($remark),
        ];

        //支付终端号
        if (isset($options['terminal'])) {
            $javaParams['terminal'] = (int)$options['terminal'];
        }
        //支付分终端号
        if (isset($options['branchTerminal'])) {
            $javaParams['branchTerminal'] = (int)$options['branchTerminal'];
        }
        //是否票付通收款
        if (isset($options['pftReceipt'])) {
            $javaParams['pftReceipt'] = (int)$options['pftReceipt'];
        }
        //功能编码
        if (isset($options['functionCode'])) {
            $javaParams['functionCode'] = (int)$options['functionCode'];
        }
        //追踪表的渠道
        if (isset($options['source'])) {
            $javaParams['source'] = (int) $options['source'];
        }
	
        // 支付来源。0=终端机,1=软终端,2=自助机,3=外部通知更新,4=云票务...
	    if (isset($options['paySource'])) {
		    $javaParams['paySource'] = (int) $options['paySource'];
	    }
        if (isset($options['tryBizCode']) && is_array($options['tryBizCode'])){
            $this->_tryCode = $options['tryBizCode'];
        }

        //分账处理的收银台订单号和是否分账
        if (isset($options['payId'])) {
            $javaParams['profitSharingTradeStrategy'] = [
                'payId'               => $options['payId'],
                'enableProfitSharing' => $options['enableProfitSharing'] ? true : false,
            ];
        }

        //增加子商户操作人
        if (isset($options['subOpId'])) {
            $javaParams['subOpId'] = (int) $options['subOpId'];
        }

        //发起请求
        $res = $this->call('payment', $javaParams);

        if ($res['code'] == 200) {
            return $this->returnData(200, '成功', $res['data']);
        } else {
            return $this->returnData($res['code'], $res['msg'], []);
        }
    }



}
