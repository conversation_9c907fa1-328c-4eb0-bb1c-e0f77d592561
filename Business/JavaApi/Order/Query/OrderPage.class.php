<?php
/**
 * 分页查询订单列表数据
 * User: 谢小勇
 * Date: 2020-11-02
 * Time: 19:36
 */

namespace Business\JavaApi\Order\Query;

use \Business\JavaApi\CallBase;
use \Exception;

class OrderPage extends CallBase
{
    const _ALLOW_CHECK_CODE_FIELEDS = ['checkCode','page','size','dbStart','dbEnd','forceMainDb',
        'minOrdertime','maxOrdertime','minPlayTime','maxPlayTime', 'minDtime','maxDtime','minBegintime','maxBegintime'
        ,'maxFirstDtime','minFirstDtime', 'cancelTimeStart', 'cancelTimeEnd', 'members'];
    public function __construct()
    {
        $tmpList = load_config('order_query', 'javaApiUrl');
        $apiInfo = $tmpList['orderPage'];

        if (!$apiInfo) {
            return false;
        }
        $this->setOpenLog(false);
        parent::__construct($apiInfo, 'orderPageQueryService', 2);
    }

    /**
     * 分页获取未支付的订单
     *
     * @param  string  $beginTime  下单起始时间
     * @param  string  $endTime  下单结束时间
     * @param  string  $page  起始位置
     * @param  string  $pageSize  页条数
     *
     * <AUTHOR>
     * @date   2020-11-02
     *
     * @return array
     */
    public function getUnpaidOrder(string $beginTime, string $endTime, int $page = 1, int $pageSize = 20)
    {
        if (!$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'page'         => $page,
            'size'         => $pageSize,
            'minOrdertime' => $beginTime,
            'maxOrdertime' => $endTime,
        ];

        //发起请求
        return $this->call('unpaidOrder', $javaParamArr);
    }

    /**
     * 获取上次消费时间
     *
     * @param  integer  $userId  用户id
     * @param  integer  $lid  景区id
     * @param  string  $page  起始位置
     * @param  string  $pageSize  页条数
     *
     * <AUTHOR>
     * @date   2020-11-02
     *
     * @return array
     */
    public function getlastConsume(int $userId, int $lid, int $page = 1, int $pageSize = 20)
    {
        if (!$userId || !$lid) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'page'   => $page,
            'size'   => $pageSize,
            'member' => $userId,
            'lid'    => $lid,
        ];

        //发起请求
        return $this->call('lastConsume', $javaParamArr);
    }

    /**
     * 获取已验证的订单信息
     *
     * @param  integer  $salerId  商户ID
     * @param  string  $beginTime  验证时间起始
     * @param  string  $endTime  验证时间截止
     * @param  string  $orderNum  订单号
     * @param  string  $orderTel  手机号
     * @param  string  $page  起始位置
     * @param  string  $pageSize  页条数
     *
     * <AUTHOR>
     * @date   2020-11-02
     *
     * @return array
     */
    public function getVerifyedOrder(int $salerId, string $beginTime, string $endTime, string $orderNum, string $orderTel, int $page = 1, int $pageSize = 20)
    {
        if (!$salerId || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'page'     => $page,
            'size'     => $pageSize,
            'salerid'  => $salerId,
            'minDtime' => $beginTime,
            'maxDtime' => $endTime,
        ];

        if ($orderNum) {
            $javaParamArr['ordernum'] = $orderNum;
        }

        if ($orderTel) {
            $javaParamArr['ordertel'] = $orderTel;
        }

        //发起请求
        return $this->call('verifyedOrder', $javaParamArr);
    }

    /**
     * 获取已验证的订单信息
     *
     * @param  integer  $userId  商户ID
     * @param  array  $orderMode  下单方式
     * @param  array  $orderStatus  验证时间截止
     * @param  array  $payStatus  支付状态
     * @param  string  $minPlaytime  预计游玩时间起始
     * @param  string  $maxPlaytime  预计游玩时间截止
     * @param  array  $ordernumsNotIn  排除的订单列表
     *
     * <AUTHOR>
     * @date   2020-11-02
     *
     * @return array
     */
    public function findOpenTicket(int $userId, array $orderMode, array $orderStatus, array $payStatus, string $minPlaytime,
        string $maxPlaytime, int $page, int $pageSize, array $ordernumsNotIn = [])
    {
        if (!$userId || !$minPlaytime || !$maxPlaytime) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'page'        => $page,
            'size'        => $pageSize,
            'member'      => $userId,
            'ordermode'   => $orderMode,
            'status'      => $orderStatus,
            'payStatus'   => $payStatus,
            'minPlaytime' => $minPlaytime,
            'maxPlaytime' => $maxPlaytime,
        ];

        if ($ordernumsNotIn) {
            //$ordernumsNotIn = array_values($ordernumsNotIn);
            //foreach ($ordernumsNotIn as &$num) {
            //    $num = (string)$num;
            //}
            $ordernumsNotIn = $this->_handleStrArr($ordernumsNotIn);
            $javaParamArr['ordernumsNotIn'] = $ordernumsNotIn;
        }

        //发起请求
        return $this->call('findOpenTicket', $javaParamArr);
    }

    /**
     * 通过门票获取订单列表
     *
     * @param  integer  $ticketId  门票id
     * @param  string  $beginTime  下单开始时间
     * @param  string  $endTime  下单结束时间
     * @param  string  $page  页数
     * @param  string  $pageSize  每页数据
     *
     * <AUTHOR>
     * @date   2020-11-03
     *
     * @return array
     */
    public function findByTidAndOrderTimeBetween(int $ticketId, string $beginTime, string $endTime, int $page = 1, int $pageSize = 20)
    {
        if (!$ticketId || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'page'         => $page,
            'size'         => $pageSize,
            'tid'          => $ticketId,
            'minOrdertime' => $beginTime,
            'maxOrdertime' => $endTime,
        ];

        //发起请求
        return $this->call('findByTidAndOrderTimeBetween', $javaParamArr);
    }

    /**
     * 根据aid和其他参数查询订单
     * 支持历史库查询
     * <AUTHOR>
     * @date 2020/11/9
     *
     * @param  int  $aid  供应商ID
     * @param  int|1  $page 页码
     * @param  int|10  $size 页大小
     * @param  int|null  $dbStart  历史库时间范围起始,满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用
     * @param  int|null  $dbEnd  历史库时间范围起始,满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用
     * @param  int|null  $memberId  购买者ID
     * @param  int|null  $code  凭证码
     * @param  int|null  $status  订单状态
     * @param  int|null  $payStatus  支付方式
     *
     * @return array
     */
    public function findByAidAndParams
    (
        int $aid,
        int $page = null,
        int $size = null,
        bool $verify = null,
        string $dbStart = null,
        string $dbEnd = null,
        int $memberId = null,
        int $code = null,
        int $status = null,
        int $payStatus = null,
        string $orderBy = null
    )
    {
        if (!$aid) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        if (empty($page)) {
            $page = 1;
        }

        if (empty($size)) {
            $size = 10;
        }

        if (empty($orderBy)) {
            $orderBy = 'id';
        }

        //请求参数
        $javaParamArr = [
            'page'    => $page,
            'size'    => $size,
            'aid'     => $aid,
            'desc'    => true,
            'orderBy' => $orderBy,
        ];

        if ($dbStart !== null) {
            $javaParamArr['dbStart'] = $dbStart;
        }

        if ($dbEnd !== null) {
            $javaParamArr['dbEnd'] = $dbEnd;
        }

        if ($memberId !== null) {
            $javaParamArr['member'] = $memberId;
        }

        if ($code !== null) {
            $javaParamArr['code'] = $code;
        }

        if ($status !== null) {
            $javaParamArr['status'] = $status;
        }

        if ($payStatus !== null) {
            $javaParamArr['payStatus'] = $payStatus;
        }

        if ($verify !== null) {
            $javaParamArr['verify'] = $verify;
        }

        //发起请求
        return $this->call('findByAidAndParams', $javaParamArr);
    }

    /**
     * 根据member和其他参数查询订单信息
     * 支持历史库查询
     * <AUTHOR>
     * @date 2020/11/9
     *
     * @param  int  $memberId  购买者ID
     * @param  int|1  $page 页码
     * @param  int|10  $size 页大小
     * @param  string|null  $dbStart  历史库时间范围起始,满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用
     * @param  string|null  $dbEnd  历史库时间范围起始,满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用
     * @param  string|null  $minOrdertime  下单时间起始
     * @param  string|null  $maxOrdertime  下单时间截止
     * @param  int|null  $status  订单状态
     * @param  string|null  $ordertel  取票人电话
     * @param  int|null  $ordermode  下单方式
     * @param  int|null  $payStatus  支付方式
     *
     * @return array
     */
    public function findByMemberAndParams
    (
        int $memberId,
        int $page = null,
        int $size = null,
        string $dbStart = null,
        string $dbEnd = null,
        string $minOrdertime = null,
        string $maxOrdertime = null,
        array $status = null,
        string $ordertel = null,
        int $ordermode = null,
        array $payStatus = null
    )
    {
        if (!$memberId) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        if (empty($page)) {
            $page = 1;
        }

        if (empty($size)) {
            $size = 10;
        }

        //请求参数
        $javaParamArr = [
            'page'   => $page,
            'size'   => $size,
            'member' => $memberId,
        ];

        if ($dbStart !== null) {
            $javaParamArr['dbStart'] = $dbStart;
        }

        if ($dbEnd !== null) {
            $javaParamArr['dbEnd'] = $dbEnd;
        }

        if ($minOrdertime !== null) {
            $javaParamArr['minOrdertime'] = $minOrdertime;
        }

        if ($maxOrdertime !== null) {
            $javaParamArr['maxOrdertime'] = $maxOrdertime;
        }

        if ($status !== null) {
            $javaParamArr['statusIn'] = $status;
        }

        if ($ordertel !== null) {
            $javaParamArr['ordertel'] = $ordertel;
        }

        if ($ordermode !== null) {
            $javaParamArr['ordermode'] = $ordermode;
        }

        if ($payStatus !== null) {
            $javaParamArr['payStatusIn'] = $payStatus;
        }

        //发起请求
        return $this->call('findByMemberAndParams', $javaParamArr);
    }

    /**
     * 景区获取订单列表 - P9.13
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "salerid": "int //景区账号",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *      "ptypeIn": "array //产品类型数组"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function findSalerOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }
        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['payStatus'])) {
            if (is_array($queryParams['payStatus'])) {
                foreach ($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (!$queryParams['pType'] && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['salerid'])) {
            $formatParams['salerid'] = intval($queryParams['salerid']);
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        $res = $this->call('findSalerOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 供应商/分销商/集团账户获取列表 - P9.15
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "statusIn": array //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "members": "int[] //会员id",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *      "ptypeIn": "int //页大小"
     *      "cmbId": "string 合并订单号"
     *      "tels": "array 取票人或者游客手机号"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function findBusinessOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        } else if (isset($queryParams['statusIn']) && is_array($queryParams['statusIn'])) {
            $formatParams['statusIn'] = array_values($queryParams['statusIn']);
        }

        if (isset($queryParams['payStatus'])) {
            if (is_array($queryParams['payStatus'])) {
                foreach ($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $pid) {
                $lidArr[] = intval($pid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (!$queryParams['pType'] && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['buyeridIn'])) {
            $buyeridArr = [];
            foreach ($queryParams['buyeridIn'] as $buyid) {
                $buyeridArr[] = intval($buyid);
            }

            if ($buyeridArr) {
                $formatParams['buyeridIn'] = $buyeridArr;
            }
        }

        if (isset($queryParams['selleridIn'])) {
            $selleridArr = [];
            foreach ($queryParams['selleridIn'] as $sellerid) {
                $selleridArr[] = intval($sellerid);
            }

            if ($selleridArr) {
                $formatParams['selleridIn'] = $selleridArr;
            }
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['members']) && is_array($queryParams['members'])) {
            $memberArr = [];
            foreach ($queryParams['members'] as $memberId) {
                $memberArr[] = intval($memberId);
            }

            if ($memberArr) {
                $formatParams['members'] = $memberArr;
            }
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (isset($queryParams['cmbId'])){
            $formatParams['cmbId'] = strval($queryParams['cmbId']);
        }

        if (isset($queryParams['tels']) && is_array($queryParams['tels'])) {
            $formatParams['tels'] = array_values($queryParams['tels']);
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        $res = $this->call('findBusinessOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 管理员平台端订单查询 - P9.11
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lidIn": "int //景区id",
     *      "pidIn": "int //产品ID",
     *      "tidIn": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyeridIn": "int //购买者id",
     *      "selleridIn": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "operateId": "int //操作人员ID",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "color": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *  }
     *
     * @return array
     */
    public function findAdminOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $pid) {
                $lidArr[] = intval($pid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['payStatus'])) {
            if (is_array($queryParams['payStatus'])) {
                foreach ($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (!$queryParams['pType'] && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['buyeridIn'])) {
            $buyeridArr = [];
            foreach ($queryParams['buyeridIn'] as $buyid) {
                $buyeridArr[] = intval($buyid);
            }

            if ($buyeridArr) {
                $formatParams['buyeridIn'] = $buyeridArr;
            }
        }

        if (isset($queryParams['selleridIn'])) {
            $selleridArr = [];
            foreach ($queryParams['selleridIn'] as $sellerid) {
                $selleridArr[] = intval($sellerid);
            }

            if ($selleridArr) {
                $formatParams['selleridIn'] = $selleridArr;
            }
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        $res = $this->call('findAdminOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 根据lid和其他参数查询订单信息
     * <AUTHOR>
     * @date 2020/12/24
     *
     * @param  array  $lid  景区id
     * @param  int|null  $page  页码
     * @param  int|null  $size  页大小
     * @param  string|null  $minDtime  验证时间起始
     * @param  string|null  $maxDtime  验证时间截止
     * @param  array|null  $status  订单状态
     * @param  array|null  $code  凭证码
     * @param  array|null  $member  购买者ID
     * @param  array|null  $payStatus  支付方式
     * @param  string|null  $dbStart  历史库时间范围起始
     * @param  string|null  $dbEnd  历史库时间范围起始
     * @param  bool|null  $forceMainDb  是否查询主库
     *
     * @return array
     */
    public function findByLidAndParams
    (
        array $lid,
        int $page = null,
        int $size = null,
        string $minDtime = null,
        string $maxDtime = null,
        array $status = null,
        array $code = null,
        array $member = null,
        array $payStatus = null,
        array $sortBy = null,
        bool $desc = null,
        string $dbStart = null,
        string $dbEnd = null,
        bool $forceMainDb = null
    )
    {
        if (!$lid) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        if (empty($page)) {
            $page = 1;
        }

        if (empty($size)) {
            $size = 10;
        }

        //请求参数
        $javaParamArr = [
            'page'  => $page,
            'size'  => $size,
            'lidIn' => $lid,
        ];

        if (!is_null($dbStart)) {
            $javaParamArr['dbStart'] = $dbStart;
        }

        if (!is_null($dbEnd)) {
            $javaParamArr['dbEnd'] = $dbEnd;
        }

        if (!is_null($minDtime)) {
            $javaParamArr['minOrdertime'] = $minDtime;
        }

        if (!is_null($maxDtime)) {
            $javaParamArr['maxOrdertime'] = $maxDtime;
            $javaParamArr['minDtime'] = $minDtime;
        }

        if (!is_null($maxDtime)) {
            $javaParamArr['maxDtime'] = $maxDtime;
        }

        if (!is_null($status)) {
            $javaParamArr['statusIn'] = $status;
        }

        if (!is_null($code)) {
            $javaParamArr['codeIn'] = $code;
        }

        if (!is_null($member)) {
            $javaParamArr['memberIn'] = $member;
        }

        if (!is_null($payStatus)) {
            $javaParamArr['payStatusIn'] = $payStatus;
        }

        if (!is_null($sortBy)) {
            $javaParamArr['sortBy'] = $sortBy;
        }

        if (!is_null($desc)) {
            $javaParamArr['desc'] = $desc;
        }

        if (!is_null($forceMainDb)) {
            $javaParamArr['forceMainDb'] = $forceMainDb;
        }

        //发起请求
        return $this->call('findByLidAndParams', $javaParamArr);
    }

    /**
     * 根据ordernum和其他参数查询订单信息
     * <AUTHOR>
     * @date 2020/12/25
     *
     * @param  array  $lid  景区id
     * @param  int|null  $page  页码
     * @param  int|null  $size  页大小
     * @param  string|null  $minDtime  验证时间起始
     * @param  string|null  $maxDtime  验证时间截止
     * @param  array|null  $status  订单状态
     * @param  array|null  $code  凭证码
     * @param  array|null  $member  购买者ID
     * @param  array|null  $payStatus  支付方式
     * @param  string|null  $dbStart  历史库时间范围起始
     * @param  string|null  $dbEnd  历史库时间范围起始
     * @param  bool|null  $forceMainDb  是否查询主库
     *
     * @return array
     */
    public function findByOrdernumsAndParams
    (
        array $ordernumsIn,
        int $page = null,
        int $size = null,
        array $status = null,
        array $tid = null,
        int $member = null,
        array $lid = null,
        array $payStatus = null,
        array $sortBy = null,
        bool $desc = null,
        string $dbStart = null,
        string $dbEnd = null,
        bool $forceMainDb = null
    )
    {
        if (!$ordernumsIn) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        if (empty($page)) {
            $page = 1;
        }

        if (empty($size)) {
            $size = 10;
        }

        //请求参数
        $javaParamArr = [
            'page'        => $page,
            'size'        => $size,
            'ordernumsIn' => $ordernumsIn,
        ];

        if (!is_null($dbStart)) {
            $javaParamArr['dbStart'] = $dbStart;
        }

        if (!is_null($dbEnd)) {
            $javaParamArr['dbEnd'] = $dbEnd;
        }

        if (!is_null($status)) {
            $javaParamArr['statusIn'] = $status;
        }

        if (!is_null($tid)) {
            $javaParamArr['tidIn'] = $tid;
        }

        if (!is_null($member)) {
            $javaParamArr['member'] = $member;
        }

        if (!is_null($payStatus)) {
            $javaParamArr['payStatusIn'] = $payStatus;
        }

        if (!is_null($lid)) {
            $javaParamArr['lidIn'] = $lid;
        }

        if (!is_null($sortBy)) {
            $javaParamArr['sortBy'] = $sortBy;
        }

        if (!is_null($desc)) {
            $javaParamArr['desc'] = $desc;
        }

        if (!is_null($forceMainDb)) {
            $javaParamArr['forceMainDb'] = $forceMainDb;
        }

        //发起请求
        return $this->call('findByOrdernumsAndParams', $javaParamArr);
    }

    /**
     * 微平台查询景区账号下的订单 - P9.9
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     * {
     *     "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *     "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *     "forceMainDb": "boolean //是否查询主库",
     *     "lidIn": "int[] //景区id",
     *     "saleridIn": "int[] 非空//商家id",
     *     "minOrdertime": "date //下单时间起始,必须和maxOrdertime一起使用",
     *     "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *     "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *     "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *     "statusIn": "int[] //订单状态",
     *     "ordertel": "string //订单手机号",
     *     "ordernumIn": "string[] //订单号",
     *     "ordername": "string //订单名字",
     *     "page": "int //页码",
     *     "size": "int //页大小"
     * }
     *
     * @return array
     */
    public function findLidOrders($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['statusIn'])) {
            $statusArr = [];
            foreach ($queryParams['statusIn'] as $status) {
                $statusArr[] = intval($status);
            }

            if ($statusArr) {
                $formatParams['statusIn'] = $statusArr;
            }
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $lid) {
                $lidArr[] = intval($lid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['ordernumIn']) && is_array($queryParams['ordernumIn'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernumIn'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}

            $ordernumArr = $this->_handleStrArr($queryParams['ordernumIn']);

            if ($ordernumArr) {
                $formatParams['ordernumIn'] = $ordernumArr;
            }
        }

        if (isset($queryParams['saleridIn']) && is_array($queryParams['saleridIn'])) {
            $saleridArr = [];
            foreach ($queryParams['saleridIn'] as $ordernum) {
                $saleridArr[] = intval($ordernum);
            }

            if ($saleridArr) {
                $formatParams['saleridIn'] = $saleridArr;
            }
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (isset($queryParams['ordername'])) {
            $formatParams['ordername'] = strval($queryParams['ordername']);
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = strval($queryParams['memberRelationship']);
        }

        $res = $this->call('findLidOrders', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }





    /******************************************************------**************************************************************/



    /**
     * 景区获取订单列表 - P9.13
     * <AUTHOR>
     * @date 2022/04/01
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "salerid": "int //景区账号",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *      "ptypeIn": "array //产品类型数组"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function findCompleteSalerOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = $this->_handleCompleteSalerOrderParams($queryParams);

        $res = $this->call('findCompleteSalerOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 景区获取订单列表  (带同一请求标识)
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $queryParams
     *
     * @return array|void
     * @throws Exception
     * @deprecated
     */
    public function pageCompleteSalerOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = $this->_handleCompleteSalerOrderParams($queryParams);

        $res = $this->call('pageCompleteSalerOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 供应商/分销商/集团账户获取列表 - P9.15
     * <AUTHOR>
     * @date 2022/04/01
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "members": "int[] //会员id",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *      "ptypeIn": "int //页大小"
     *  }
     *
     * @return array
     * @deprecated 已废弃，请使用 Business\NewJavaApi\Order\ProductOrder\BizProductOrder::pageInfoBest
     */
    public function findCompleteBusinessOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = $this->_handleCompleteBusinessOrderParams($queryParams);

        $res = $this->call('findCompleteBusinessOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 供应商/分销商/集团账户获取列表 (带同一请求标识)
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $queryParams
     *
     * @return array|void
     * @throws Exception
     * @deprecated
     */
    public function pageCompleteBusinessOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = $this->_handleCompleteBusinessOrderParams($queryParams);

        $res = $this->call('pageCompleteBusinessOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 管理员平台端订单查询 - P9.11
     * <AUTHOR>
     * @date 2022/04/01
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lidIn": "int //景区id",
     *      "pidIn": "int //产品ID",
     *      "tidIn": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyeridIn": "int //购买者id",
     *      "selleridIn": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "operateId": "int //操作人员ID",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "color": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function findCompleteAdminOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = $this->_handleCompleteAdminOrderParams($queryParams);

        $res = $this->call('findCompleteAdminOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 管理员平台端订单查询 (带同一请求标识)
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $queryParams
     *
     * @return array|void
     * @throws Exception
     * @deprecated
     */
    public function pageCompleteAdminOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = $this->_handleCompleteAdminOrderParams($queryParams);

        $res = $this->call('pageCompleteAdminOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 微平台查询景区账号下的订单 - P9.9
     * <AUTHOR>
     * @date 2022/04/01
     *
     * @param  array  $queryParams  查询参数数组
     * {
     *     "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *     "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *     "forceMainDb": "boolean //是否查询主库",
     *     "lidIn": "int[] //景区id",
     *     "saleridIn": "int[] 非空//商家id",
     *     "minOrdertime": "date //下单时间起始,必须和maxOrdertime一起使用",
     *     "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *     "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *     "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *     "statusIn": "int[] //订单状态",
     *     "ordertel": "string //订单手机号",
     *     "ordernumIn": "string[] //订单号",
     *     "ordername": "string //订单名字",
     *     "page": "int //页码",
     *     "size": "int //页大小"
     * }
     *
     * @return array
     * @deprecated
     */
    public function findCompleteLidOrders($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['statusIn'])) {
            $statusArr = [];
            foreach ($queryParams['statusIn'] as $status) {
                $statusArr[] = intval($status);
            }

            if ($statusArr) {
                $formatParams['statusIn'] = $statusArr;
            }
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $lid) {
                $lidArr[] = intval($lid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['ordernumIn']) && is_array($queryParams['ordernumIn'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernumIn'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}

            $ordernumArr = $this->_handleStrArr($queryParams['ordernumIn']);

            if ($ordernumArr) {
                $formatParams['ordernumIn'] = $ordernumArr;
            }
        }

        if (isset($queryParams['saleridIn']) && is_array($queryParams['saleridIn'])) {
            $saleridArr = [];
            foreach ($queryParams['saleridIn'] as $ordernum) {
                $saleridArr[] = intval($ordernum);
            }

            if ($saleridArr) {
                $formatParams['saleridIn'] = $saleridArr;
            }
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (isset($queryParams['ordername'])) {
            $formatParams['ordername'] = strval($queryParams['ordername']);
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = strval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['pTypeIn'])) {
            $formatParams['pTypeIn'] = $queryParams['pTypeIn'];
        }

        $res = $this->call('findCompleteLidOrders', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 生成请求id
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $prefix
     *
     * @return string
     */
    public static function getRequestId($prefix = '')
    {
        return uniqid($prefix) . rand(1000, 9999);
    }

    /**
     * 供应商/分销商/集团账户获取列表 参数处理
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $queryParams
     *
     * @return array
     */
    private function _handleCompleteBusinessOrderParams($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['payStatus'])) {
            if (is_array($queryParams['payStatus'])) {
                foreach ($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $pid) {
                $lidArr[] = intval($pid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (isset($queryParams['buyeridIn'])) {
            $buyeridArr = [];
            foreach ($queryParams['buyeridIn'] as $buyid) {
                $buyeridArr[] = intval($buyid);
            }

            if ($buyeridArr) {
                $formatParams['buyeridIn'] = $buyeridArr;
            }
        }

        if (isset($queryParams['selleridIn'])) {
            $selleridArr = [];
            foreach ($queryParams['selleridIn'] as $sellerid) {
                $selleridArr[] = intval($sellerid);
            }

            if ($selleridArr) {
                $formatParams['selleridIn'] = $selleridArr;
            }
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['members']) && is_array($queryParams['members'])) {
            $memberArr = [];
            foreach ($queryParams['members'] as $memberId) {
                $memberArr[] = intval($memberId);
            }

            if ($memberArr) {
                $formatParams['members'] = $memberArr;
            }
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (!$queryParams['pType'] && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['isCombine'])){
            $formatParams['isCombine'] = intval($queryParams['isCombine']);
        }

        if (isset($queryParams['upstreamOrderId'])){
            $formatParams['upstreamOrderId'] = $queryParams['upstreamOrderId'];
        }

        if (isset($queryParams['subSid'])){
            $formatParams['subSid'] = $queryParams['subSid'];
        }

        if (isset($queryParams['afterSaleState'])){
            $formatParams['afterSaleState'] = $queryParams['afterSaleState'];
        }
        if (isset($queryParams['roundId'])){
            $formatParams['roundId'] = intval($queryParams['roundId']);
        }

        //取消开始时间
        if (isset($queryParams['cancelTimeStart'])){
            $formatParams['cancelTimeStart'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeStart']));
        }

        //取消结束时间
        if (isset($queryParams['cancelTimeEnd'])){
            $formatParams['cancelTimeEnd'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeEnd']));
        }

        //这个是特殊逻辑放最后
        if (isset($queryParams['checkCode'])){
            $formatParams['checkCode'] = $queryParams['checkCode'];
            $formatParamKeys = array_keys($formatParams);
            foreach ($formatParamKeys as $key){
                if(in_array($key,$this::_ALLOW_CHECK_CODE_FIELEDS) === false){
                    unset($formatParams[$key]);
                }
            }
        }

        if (isset($queryParams['notLidList'])) {
            $formatParams['notLidList'] = $queryParams['notLidList'];
        }

        //子产品类型
        if (isset($queryParams['subType']) && isset($queryParams['pType'])) {
            $formatParams['productSubTypes'] = [$queryParams['subType']];
            $formatParams['ptypeIn']         = [$queryParams['pType']];
        }

        if (isset($queryParams['cmbId'])) {
            $formatParams['cmbId'] = $queryParams['cmbId'];
        }

        if (isset($queryParams['requestId'])){
            $formatParams['requestId'] = $queryParams['requestId'];
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        return $formatParams;
    }

    /**
     * 管理员平台端订单查询 参数处理
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $queryParams
     *
     * @return array
     */
    private function _handleCompleteAdminOrderParams($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $pid) {
                $lidArr[] = intval($pid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['payStatus'])) {
            if (is_array($queryParams['payStatus'])) {
                foreach ($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (isset($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['buyeridIn'])) {
            $buyeridArr = [];
            foreach ($queryParams['buyeridIn'] as $buyid) {
                $buyeridArr[] = intval($buyid);
            }

            if ($buyeridArr) {
                $formatParams['buyeridIn'] = $buyeridArr;
            }
        }

        if (isset($queryParams['selleridIn'])) {
            $selleridArr = [];
            foreach ($queryParams['selleridIn'] as $sellerid) {
                $selleridArr[] = intval($sellerid);
            }

            if ($selleridArr) {
                $formatParams['selleridIn'] = $selleridArr;
            }
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (isset($queryParams['isCombine'])){
            $formatParams['isCombine'] = intval($queryParams['isCombine']);
        }

        if (isset($queryParams['upstreamOrderId'])){
            $formatParams['upstreamOrderId'] = $queryParams['upstreamOrderId'];
        }

        if (isset($queryParams['afterSaleState'])){
            $formatParams['afterSaleState'] = $queryParams['afterSaleState'];
        }
        if (isset($queryParams['roundId'])){
            $formatParams['roundId'] = intval($queryParams['roundId']);
        }

        //取消开始时间
        if (isset($queryParams['cancelTimeStart'])){
            $formatParams['cancelTimeStart'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeStart']));
        }

        //取消结束时间
        if (isset($queryParams['cancelTimeEnd'])){
            $formatParams['cancelTimeEnd'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeEnd']));
        }

        if (isset($queryParams['checkCode'])){
            $formatParams['checkCode'] = $queryParams['checkCode'];
            $formatParamKeys = array_keys($formatParams);
            foreach ($formatParamKeys as $key){
                if(in_array($key,$this::_ALLOW_CHECK_CODE_FIELEDS) === false){
                    unset($formatParams[$key]);
                }
            }
        }

        //子产品类型
        if (isset($queryParams['subType']) && isset($queryParams['pType'])) {
            $formatParams['productSubTypes'] = [$queryParams['subType']];
            $formatParams['ptypeIn']         = [$queryParams['pType']];
        }

        if (isset($queryParams['requestId'])){
            $formatParams['requestId'] = $queryParams['requestId'];
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        return $formatParams;
    }

    /**
     * 资源账号订单查询 参数处理
     * <AUTHOR>
     * @date   2023/12/18
     *
     * @param $queryParams
     *
     * @return array
     */
    private function _handleCompleteSalerOrderParams($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }
        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['payStatus'])) {
            if (is_array($queryParams['payStatus'])) {
                foreach ($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['salerid'])) {
            $formatParams['salerid'] = intval($queryParams['salerid']);
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (!$queryParams['pType'] && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['upstreamOrderId'])){
            $formatParams['upstreamOrderId'] = $queryParams['upstreamOrderId'];
        }

        if (isset($queryParams['afterSaleState'])){
            $formatParams['afterSaleState'] = $queryParams['afterSaleState'];
        }
        if (isset($queryParams['roundId'])){
            $formatParams['roundId'] = intval($queryParams['roundId']);
        }

        //取消开始时间
        if (isset($queryParams['cancelTimeStart'])){
            $formatParams['cancelTimeStart'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeStart']));
        }

        //取消结束时间
        if (isset($queryParams['cancelTimeEnd'])){
            $formatParams['cancelTimeEnd'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeEnd']));
        }

        if (isset($queryParams['checkCode'])){
            $formatParams['checkCode'] = $queryParams['checkCode'];
            $formatParamKeys = array_keys($formatParams);
            foreach ($formatParamKeys as $key){
                if(in_array($key,$this::_ALLOW_CHECK_CODE_FIELEDS) === false){
                    unset($formatParams[$key]);
                }
            }
        }

        //子产品类型
        if (isset($queryParams['subType']) && isset($queryParams['pType'])) {
            $formatParams['productSubTypes'] = [$queryParams['subType']];
            $formatParams['ptypeIn']         = [$queryParams['pType']];
        }

        if (isset($queryParams['requestId'])){
            $formatParams['requestId'] = $queryParams['requestId'];
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        return $formatParams;
    }
}