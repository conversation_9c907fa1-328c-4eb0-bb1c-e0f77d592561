<?php
/**
 * Created by PhpStorm.
 * User: 谢小勇
 * Date: 2020-11-03
 * Time: 10:12
 */

namespace Business\JavaApi\Order\Query;

use \Business\JavaApi\CallBase;

class OrderQuery extends CallBase
{

    public function __construct()
    {
        $tmpList = load_config('order_query', 'javaApiUrl');
        $apiInfo = $tmpList['orderQuery'];

        if (!$apiInfo) {
            return false;
        }
        $this->setOpenLog(false);
        parent::__construct($apiInfo, 'orderQueryService', 2);
    }

    const _ALLOW_CHECK_CODE_FIELEDS = ['checkCode','page','size','dbStart','dbEnd','forceMainDb',
        'minOrdertime','maxOrdertime','minPlayTime','maxPlayTime', 'minDtime','maxDtime','minBegintime','maxBegintime'
        ,'maxFirstDtime','minFirstDtime', 'cancelTimeStart', 'cancelTimeEnd'];
    /**
     * 统计指定会员购买指定票的次数
     *
     * @param  integer  $userId  购买者id
     * @param  integer  $lid  景区id
     * @param  integer  $beginTime  下单时间起始
     * @param  integer  $endTime  单时间截止
     * @param  integer  $aid  供应商id
     * @param  integer  $orderMode  下单方式:0=正常分销商下单,1=普通用户支付,2=用户手机支付,3=会员卡购票,10=云票务,
     *                                  11=微信商城,12=自助机,13=二级店铺,14=闸机购票,15=智能终端,16=计调下单,17=淘宝码商,18=年卡,19=微信端,20=外部接口OTA下单,22=一卡通
     *
     * <AUTHOR>
     * @date   2020-11-02
     *
     * @return array
     */
    public function distintCodeByMemeber(int $userId, int $lid, string $beginTime, string $endTime, int $aid, int $orderMode = -1)
    {
        if (!$userId || !$lid || !$beginTime || $endTime) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'member'       => $userId,
            'lid'          => $lid,
            'minOrdertime' => $beginTime,
            'maxOrdertime' => $endTime,
        ];

        if ($aid) {
            $javaParamArr['aid'] = $aid;
        }

        if ($orderMode != -1) {
            $javaParamArr['ordermode'] = $orderMode;
        }

        //发起请求
        return $this->call('distintCodeByMemeberAndParams', $javaParamArr);
    }

    /**
     * 根据订单号获取最详细的订单数据
     *
     * @param  string|array  $orderNum  订单号
     *
     * <AUTHOR>
     * @date   2020-11-02
     *
     * @return array
     */
    public function completeOrder($orderNum,$forceMainDb = false)
    {
        if (!$orderNum) {
            return $this->returnData(204, '参数错误');
        }

        if (!is_array($orderNum)) {
            $orderNum = [$orderNum];
        }

        $orderNum = $this->_handleStrArr($orderNum);

        //请求参数
        $javaParamArr = [
            'orderNum' => $orderNum,
        ];
        if($forceMainDb){
            $javaParamArr['forceMainDb'] = $forceMainDb;
        }
        //发起请求
        return $this->_unCamelize($this->call('completeOrder', $javaParamArr));
    }

    /**
     * 关联订单分销表获取数据 - 微平台查单详情
     *
     * @param  array  $orderNumArr  订单号数组
     *
     * <AUTHOR>  Li
     * @date   2020-11-08
     *
     * @return array
     */
    public function completeOrderWithAids(array $orderNumArr, bool $desc = false)
    {
        if (!$orderNumArr) {
            return $this->returnData(204, '参数错误');
        }

        //$orderNumArr = array_values($orderNumArr);
        //foreach ($orderNumArr as &$num) {
        //    $num = (string)$num;
        //}
        $orderNumArr = $this->_handleStrArr($orderNumArr);

        //请求参数
        $javaParamArr = [
            'ordernums' => $orderNumArr,
            'desc'      => $desc,
        ];

        //发起请求
        return $this->_unCamelize($this->call('completeOrderWithAids', $javaParamArr));
    }

    /**
     * 统计散客用户还有多少订单未使用
     * <AUTHOR>
     * @date 2020/11/10
     *
     * @param  int  $memberId  会员id
     * @param  array  $status  订单状态
     * @param  array  $payStatus  支付状态
     *
     * @return array
     */
    public function countByMemberAndStatusAndPayStatus(int $memberId, array $status, array $payStatus)
    {
        if (!$memberId || !$status || !$payStatus) {
            return $this->returnData(204, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'memberId'    => $memberId,
            'statusIn'    => $status,
            'payStatusIn' => $payStatus,
        ];

        //发起请求
        return $this->call('countByMemberAndStatusAndPayStatus', $javaParamArr);
    }

    /**
     * 根据aid和其他参数查询单条订单
     * <AUTHOR>
     * @date 2020/11/11
     *
     * @param  int  $aid  供应商ID
     * @param  int|null  $memberId  购买者ID
     *
     * @return array
     */
    public function findByAidAndParams(int $aid, int $memberId = null)
    {
        if (!$aid) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'aid' => $aid,
        ];

        if ($memberId !== null) {
            $javaParamArr['member'] = $memberId;
        }

        //发起请求
        return $this->call('findByAidAndParams', $javaParamArr);
    }

    /**
     * 根据member查询单条订单信息
     * 不支持历史库查询
     * <AUTHOR>
     * @date 2020/11/11
     *
     * @param  int  $memberId  购买者ID
     *
     * @return array
     */
    public function findByMemberAndParams(int $memberId)
    {
        if (!$memberId) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'member' => $memberId,
        ];

        //发起请求
        return $this->call('findByMemberAndParams', $javaParamArr);
    }

    /**
     * 根据凭证码/手机号/身份证查询订单
     * <AUTHOR>
     * @date 2020/11/13
     *
     * @param  array  $lid  景区id
     * @param  int  $code  code
     * @param  string  $ordertel  取票人电话
     * @param  string  $personid  身份证
     * @param  array  $ordernum  订单号
     * @param  string  $minDtime  验证时间
     * @param  array  $statusIn  订单状态
     * @param  int  $page  页码
     * @param  int  $size  每页大小
     *
     * @return array
     */
    public function findOrderWithStatusWithCodeTriple
    (
        array $lid,
        string $minDtime,
        array $statusIn,
        int $page,
        int $size,
        int $code = null,
        string $ordertel = null,
        string $personid = null,
        array $ordernum = null
    )
    {
        if (!$lid || !$minDtime || !$statusIn || !$page || !$size) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'lidIn'    => $lid,
            'minDtime' => $minDtime,
            'statusIn' => $statusIn,
            'page'     => $page,
            'size'     => $size,
        ];

        if ($code !== null) {
            $javaParamArr['code'] = $code;
        }

        if ($ordertel !== null) {
            $javaParamArr['ordertel'] = $ordertel;
        }

        if ($personid !== null) {
            $javaParamArr['personid'] = $personid;
        }

        if ($ordernum !== null) {
            $javaParamArr['ordernumIn'] = $ordernum;
        }

        //发起请求
        return $this->call('findOrderWithStatusWithCodeTriple', $javaParamArr);
    }

    /**
     * 根据订单号获取最详细的订单数据
     *
     * @param  array  $remoteOrdernumArr  远端订单号数组
     *
     * <AUTHOR>  Li
     * @date   2020-11-08
     *
     * @return array
     */
    public function completeOrderByRemoteOrdernum(array $remoteOrdernumArr)
    {
        if (!$remoteOrdernumArr) {
            return $this->returnData(204, '参数错误');
        }

        //$remoteOrdernumArr = array_values($remoteOrdernumArr);
        //foreach ($remoteOrdernumArr as &$num) {
        //    $num = (string)$num;
        //}
        $remoteOrdernumArr = $this->_handleStrArr($remoteOrdernumArr);

        //请求参数
        $javaParamArr = [
            'remoteOrdernum' => $remoteOrdernumArr,
        ];

        //发起请求
        return $this->call('completeOrderByRemoteOrdernum', $javaParamArr);
    }

    /**
     * 单表数组处理
     * <AUTHOR>
     * @date 2020-02-03
     *
     * @param  array  $res  列表
     * @param  bool  $isList  是否是有list的分页处理
     * @param  bool  $isListval  分页处理是否有数组
     *
     * @return array
     */
    public function _unCamelize($res, $isList = false)
    {
        if ($res['code'] == 200) {
            $speKey = [
                'minNum' => 'min_number',
                'maxNum' => 'max_number',
                'unioncheck'=> 'unionCheck'
            ];
            $arrSpe = array_keys($speKey);

            $unCamelizeData = [];
            if ($isList) {
                $data = $res['data']['list'] ? $res['data']['list'] : [];
            } else {
                $data = $res['data'];
            }

            foreach ($data as $key => &$val) {
                if (is_int($key)) {
                    foreach ($val as $key2 => &$val2) {
                        if (is_array($val2) && !empty($val2)) {
                            $flag = [];
                            foreach ($val2 as $key3 => $val3) {
                                if (is_array($val3)) {
                                    foreach ($val3 as $key4 => $val4) {
                                        if (in_array($key4, $arrSpe)) {
                                            $flag[$key3][$speKey[$key4]] = $val4;
                                        } else {
                                            $flag[$key3][uncamelize($key4)] = $val4;
                                        }

                                    }
                                } else {
                                    $flag[uncamelize($key3)] = $val3;
                                }
                            }
                            $val2 = $flag;
                        }
                        if (in_array($key2, $arrSpe)) {
                            $unCamelizeData[$key][$speKey[$key2]] = $val2;
                        } else {
                            $unCamelizeData[$key][uncamelize($key2)] = $val2;
                        }
                    }
                } else {
                    if (is_array($val) && !empty($val)) {
                        $flag = [];
                        foreach ($val as $key3 => $val3) {
                            foreach ($val3 as $key4 => $val4) {
                                if (in_array($key4, $arrSpe)) {
                                    $flag[$key3][$speKey[$key4]] = $val4;
                                } else {
                                    $flag[$key3][uncamelize($key4)] = $val4;
                                }
                            }
                        }
                        $val = $flag;
                    }
                    if (in_array($key, $arrSpe)) {
                        $unCamelizeData[$speKey[$key]] = $val;
                    } else {
                        $unCamelizeData[uncamelize($key)] = $val;
                    }
                }
            }
            if ($isList) {
                $res['data']['list'] = $unCamelizeData;
            } else {
                $res['data'] = $unCamelizeData;
            }
        }

        return $res;
    }

    /**
     * 根据手机号及订单状态查询订单
     *
     * @param  string  $ordertel  订单号
     * @param  int  $lid  景区id
     * @param  string  $dateTime  订单日期
     * @param  int  $payStatus  支付状态
     * @param  array  $statusArr  订单状态
     *
     * <AUTHOR>
     * @date   2020-11-08
     *
     * @return array
     */
    public function findByOrdertelAndParams(string $ordertel, int $lid, string $dateTime, int $payStatus, array $statusArr)
    {
        if (!$ordertel || !$lid || !$payStatus || !$statusArr || !$dateTime) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'ordertel'  => $ordertel,
            'lid'       => $lid,
            'payStatus' => $payStatus,
            'statusIn'  => $statusArr,
            'time'      => $dateTime,
        ];

        //发起请求
        return $this->call('findByOrdertelAndParams', $javaParamArr);
    }

    /**
     * 景区获取订单总数 - P9.14
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "salerid": "int //景区账号",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *      "ptypeIn": "array //产品类型数组"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function countSalerOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['payStatus'])) {
            if(is_array($queryParams['payStatus'])) {
                foreach($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['salerid'])) {
            $formatParams['salerid'] = intval($queryParams['salerid']);
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (isset($queryParams['ifPrint']) && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['upstreamOrderId'])){
            $formatParams['upstreamOrderId'] = $queryParams['upstreamOrderId'];
        }
        if (isset($queryParams['afterSaleState'])){
            $formatParams['afterSaleState'] = $queryParams['afterSaleState'];
        }
        if (isset($queryParams['roundId'])){
            $formatParams['roundId'] = intval($queryParams['roundId']);
        }

        //取消开始时间
        if (isset($queryParams['cancelTimeStart'])){
            $formatParams['cancelTimeStart'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeStart']));
        }

        //取消结束时间
        if (isset($queryParams['cancelTimeEnd'])){
            $formatParams['cancelTimeEnd'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeEnd']));
        }

        //这个是特殊逻辑放最后
        if (isset($queryParams['checkCode'])){
            $formatParams['checkCode'] = $queryParams['checkCode'];
            $formatParamKeys = array_keys($formatParams);
            foreach ($formatParamKeys as $key){
                if(in_array($key,$this::_ALLOW_CHECK_CODE_FIELEDS) === false){
                    unset($formatParams[$key]);
                }
            }
        }

        //子产品类型
        if (isset($queryParams['subType']) && isset($queryParams['pType'])) {
            $formatParams['productSubTypes'] = [$queryParams['subType']];
            $formatParams['ptypeIn']         = [$queryParams['pType']];
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        $res = $this->call('countSalerOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 供应商/分销商/集团账户获取订单总数 - P9.16
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "members": "int[] //会员id",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function countBussinessOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['maxFirstDtime'])) {
            $formatParams['maxFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxFirstDtime']));
        }

        if (isset($queryParams['minFirstDtime'])) {
            $formatParams['minFirstDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minFirstDtime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['payStatus'])) {
            if(is_array($queryParams['payStatus'])) {
                foreach($queryParams['payStatus'] as $payStatus) {
                    $formatParams['payStatusIn'][] = intval($payStatus);
                }
            } else {
                $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
            }
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }


        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $pid) {
                $lidArr[] = intval($pid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (isset($queryParams['buyeridIn'])) {
            $buyeridArr = [];
            foreach ($queryParams['buyeridIn'] as $buyid) {
                $buyeridArr[] = intval($buyid);
            }

            if ($buyeridArr) {
                $formatParams['buyeridIn'] = $buyeridArr;
            }
        }

        if (isset($queryParams['selleridIn'])) {
            $selleridArr = [];
            foreach ($queryParams['selleridIn'] as $sellerid) {
                $selleridArr[] = intval($sellerid);
            }

            if ($selleridArr) {
                $formatParams['selleridIn'] = $selleridArr;
            }
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['members']) && is_array($queryParams['members'])) {
            $memberArr = [];
            foreach ($queryParams['members'] as $memberId) {
                $memberArr[] = intval($memberId);
            }

            if ($memberArr) {
                $formatParams['members'] = $memberArr;
            }
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }

        if (isset($queryParams['ifPrint']) && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['ptypeIn'] = array_values($queryParams['ptypeIn']);
        }

        if (isset($queryParams['upstreamOrderId'])){
            $formatParams['upstreamOrderId'] = $queryParams['upstreamOrderId'];
        }

        if (isset($queryParams['subSid'])){
            $formatParams['subSid'] = $queryParams['subSid'];
        }
        if (isset($queryParams['afterSaleState'])){
            $formatParams['afterSaleState'] = $queryParams['afterSaleState'];
        }
        if (isset($queryParams['roundId'])){
            $formatParams['roundId'] = intval($queryParams['roundId']);
        }

        //取消开始时间
        if (isset($queryParams['cancelTimeStart'])){
            $formatParams['cancelTimeStart'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeStart']));
        }

        //取消结束时间
        if (isset($queryParams['cancelTimeEnd'])){
            $formatParams['cancelTimeEnd'] = date('Y-m-d H:i:s', strtotime($queryParams['cancelTimeEnd']));
        }

        //这个是特殊逻辑放最后
        if (isset($queryParams['checkCode'])){
            $formatParams['checkCode'] = $queryParams['checkCode'];
            $formatParamKeys = array_keys($formatParams);
            foreach ($formatParamKeys as $key){
                if(in_array($key,$this::_ALLOW_CHECK_CODE_FIELEDS) === false){
                    unset($formatParams[$key]);
                }
            }
        }

        if (isset($queryParams['notLidList'])) {
            $formatParams['notLidList'] = $queryParams['notLidList'];
        }

        //子产品类型
        if (isset($queryParams['subType']) && isset($queryParams['pType'])) {
            $formatParams['productSubTypes'] = [$queryParams['subType']];
            $formatParams['ptypeIn']         = [$queryParams['pType']];
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        $res = $this->call('countBussinessOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 管理员平台端订单查询总数 - P9.12
     * <AUTHOR>
     * @date 2020/12/16
     *
     * @param  array  $queryParams  查询参数数组
     *  {
     *      "dbStart": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbEnd一起使用;",
     *      "dbEnd": "date //历史库时间范围起始;非\u003cbr/\u003e\t  满足时间范围条件时,会自动切换到对应的历史库,不传或者不满足条件则在当前库查询;不支持同时查询多个库;必须和dbStart一起使用;",
     *      "forceMainDb": "boolean //是否查询主库",
     *      "minOrdertime": "date //下单时间起始,必须和 maxOrdertime 一起使用",
     *      "maxOrdertime": "date //下单时间截止,必须和minOrdertime一起使用",
     *      "minPlayTime": "int //游玩时间起始,必须和maxPayTime一起使用",
     *      "maxPlayTime": "int //游玩时间截止,必须和minPayTime一起使用",
     *      "minDtime": "date //验证时间起始,必须和maxDtime一起使用",
     *      "maxDtime": "date //验证时间截止,必须和minDtime一起使用",
     *      "minBegintime": "date //开始时间起始,必须和maxBegintime一起使用",
     *      "maxBegintime": "date //开始时间截止,必须和minBegintime一起使用",
     *      "status": "int //订单状态",
     *      "ordermode": "int //下单渠道",
     *      "lid": "int //景区id",
     *      "pid": "int //产品ID",
     *      "tid": "int //门票id",
     *      "pmode": "int //该级别的支付方式",
     *      "ordernums": "string[] //订单号",
     *      "personid": "string //取票人证件号码",
     *      "ordertel": "string //手机号",
     *      "pType": "string //0：通用 A\u003d景点，B\u003d线路，C\u003d酒店，F\u003d套票，G\u003d餐饮，H\u003d演出，I\u003d年卡套餐，J\u003d特产，K\u003d计时，L\u003d保险",
     *      "buyerid": "int //购买者id",
     *      "sellerid": "int //分销商id",
     *      "memberRelationship": "int //订单会员关系 0:平台关系,1:资源中心关系",
     *      "checkSource": "int //验证方式",
     *      "operateId": "int //操作人员ID",
     *      "ifPrint": "int //是否是取票状态搜索 0未取票，1取票, 2部分取票",
     *      "mark": "int //订单标记:1=红，2=绿，3=蓝",
     *      "page": "int //页码",
     *      "size": "int //页大小"
     *  }
     *
     * @return array
     * @deprecated
     */
    public function countAdminOrder($queryParams = [])
    {
        //格式化后的查询参数
        $formatParams = [];

        if (isset($queryParams['dbStart'])) {
            $formatParams['dbStart'] = date('Y-m-d', strtotime($queryParams['dbStart']));
        }

        if (isset($queryParams['dbEnd'])) {
            $formatParams['dbEnd'] = date('Y-m-d', strtotime($queryParams['dbEnd']));
        }

        if (isset($queryParams['forceMainDb'])) {
            $formatParams['forceMainDb'] = boolval($queryParams['forceMainDb']);
        }

        if (isset($queryParams['minOrdertime'])) {
            $formatParams['minOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['minOrdertime']));
        }

        if (isset($queryParams['maxOrdertime'])) {
            $formatParams['maxOrdertime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxOrdertime']));
        }

        if (isset($queryParams['minPlayTime'])) {
            $formatParams['minPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['minPlayTime']));
        }

        if (isset($queryParams['maxPlayTime'])) {
            $formatParams['maxPlayTime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxPlayTime']));
        }

        if (isset($queryParams['minDtime'])) {
            $formatParams['minDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['minDtime']));
        }

        if (isset($queryParams['maxDtime'])) {
            $formatParams['maxDtime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxDtime']));
        }

        if (isset($queryParams['minBegintime'])) {
            $formatParams['minBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['minBegintime']));
        }

        if (isset($queryParams['maxBegintime'])) {
            $formatParams['maxBegintime'] = date('Y-m-d H:i:s', strtotime($queryParams['maxBegintime']));
        }

        if (isset($queryParams['status'])) {
            $formatParams['status'] = intval($queryParams['status']);
        }

        if (isset($queryParams['payStatus'])) {
            $formatParams['payStatusIn'] = [intval($queryParams['payStatus'])];
        }

        if (isset($queryParams['ordermodeIn'])) {
            $ordermodeArr = [];
            foreach ($queryParams['ordermodeIn'] as $ordermode) {
                $ordermodeArr[] = intval($ordermode);
            }

            if ($ordermodeArr) {
                $formatParams['ordermodeIn'] = $ordermodeArr;
            }
        }

        if (isset($queryParams['orderName'])) {
            $formatParams['orderName'] = strval($queryParams['orderName']);
        }

        if (isset($queryParams['lidIn'])) {
            $lidArr = [];
            foreach ($queryParams['lidIn'] as $pid) {
                $lidArr[] = intval($pid);
            }

            if ($lidArr) {
                $formatParams['lidIn'] = $lidArr;
            }
        }

        if (isset($queryParams['pidIn'])) {
            $pidArr = [];
            foreach ($queryParams['pidIn'] as $pid) {
                $pidArr[] = intval($pid);
            }

            if ($pidArr) {
                $formatParams['pidIn'] = $pidArr;
            }
        }

        if (isset($queryParams['tidIn'])) {
            $tidArr = [];
            foreach ($queryParams['tidIn'] as $tid) {
                $tidArr[] = intval($tid);
            }

            if ($tidArr) {
                $formatParams['tidIn'] = $tidArr;
            }
        }

        if (isset($queryParams['pmodeIn'])) {
            $pmodeArr = [];
            foreach ($queryParams['pmodeIn'] as $pmode) {
                $pmodeArr[] = intval($pmode);
            }

            if ($pmodeArr) {
                $formatParams['pmodeIn'] = $pmodeArr;
            }
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            //$ordernumArr = [];
            //foreach ($queryParams['ordernums'] as $ordernum) {
            //    $ordernumArr[] = strval($ordernum);
            //}
            $ordernumArr = $this->_handleStrArr($queryParams['ordernums']);

            if ($ordernumArr) {
                $formatParams['ordernums'] = $ordernumArr;
            }
        }

        if (isset($queryParams['personid'])) {
            $formatParams['personid'] = strval($queryParams['personid']);
        }

        if (isset($queryParams['ordertel'])) {
            $formatParams['ordertel'] = strval($queryParams['ordertel']);
        }

        if (isset($queryParams['pType'])) {
            $formatParams['pType'] = strval($queryParams['pType']);
        }

        if (isset($queryParams['buyeridIn'])) {
            $buyeridArr = [];
            foreach ($queryParams['buyeridIn'] as $buyid) {
                $buyeridArr[] = intval($buyid);
            }

            if ($buyeridArr) {
                $formatParams['buyeridIn'] = $buyeridArr;
            }
        }

        if (isset($queryParams['selleridIn'])) {
            $selleridArr = [];
            foreach ($queryParams['selleridIn'] as $sellerid) {
                $selleridArr[] = intval($sellerid);
            }

            if ($selleridArr) {
                $formatParams['selleridIn'] = $selleridArr;
            }
        }

        if (isset($queryParams['memberRelationship'])) {
            $formatParams['memberRelationship'] = intval($queryParams['memberRelationship']);
        }

        if (isset($queryParams['checkSource'])) {
            $checkSourceArr = [];
            foreach ($queryParams['checkSource'] as $checkSource) {
                $checkSourceArr[] = intval($checkSource);
            }

            if ($checkSourceArr) {
                $formatParams['checkSourceIn'] = $checkSourceArr;
            }
        }

        if (isset($queryParams['operateId'])) {
            $formatParams['operateId'] = intval($queryParams['operateId']);
        }

        if (isset($queryParams['ifPrint'])) {
            $formatParams['ifPrint'] = intval($queryParams['ifPrint']);
        }

        if (isset($queryParams['color'])) {
            $formatParams['color'] = strval($queryParams['color']);
        }

        if (isset($queryParams['page'])) {
            $formatParams['page'] = intval($queryParams['page']);
        }

        if (isset($queryParams['size'])) {
            $formatParams['size'] = intval($queryParams['size']);
        }
        if (isset($queryParams['roundId'])){
            $formatParams['roundId'] = intval($queryParams['roundId']);
        }

        if (isset($queryParams['touristMobile'])){
            $formatParams['touristMobile'] = $queryParams['touristMobile'];
        }

        if (isset($queryParams['touristIdentificationCode'])){
            $formatParams['touristIdentificationCode'] = $queryParams['touristIdentificationCode'];
        }

        //这个是特殊逻辑放最后
        if (isset($queryParams['checkCode'])){
            $formatParams['checkCode'] = $queryParams['checkCode'];
            $formatParamKeys = array_keys($formatParams);
            foreach ($formatParamKeys as $key){
                if(in_array($key,$this::_ALLOW_CHECK_CODE_FIELEDS) === false){
                    unset($formatParams[$key]);
                }
            }
        }
        $res = $this->call('countAdminOrder', $formatParams);

        if ($res['code'] == 500) {
            throw new \Exception("系统异常[" . $res['msg'] . "]", 500);
        } elseif ($res['code'] == 200) {
            return $res;
        }
    }

    /**
     * 景区销量统计
     * <AUTHOR>
     * @date 2020/12/22
     *
     * @param  int  $lid  景区id
     * @param  int  $minOrdertime  下单时间起始
     * @param  int  $maxOrdertime  下单时间截止
     * @param  int  $groupByTid  是否根据tid group by查询
     *
     * @return array
     */
    public function countByParams(string $minOrdertime, string $maxOrdertime, int $groupByTid = null, int $lid = null)
    {
        if (!$minOrdertime || !$maxOrdertime) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'lid'          => $lid,
            'minOrdertime' => $minOrdertime,
            'maxOrdertime' => $maxOrdertime,
        ];

        if (!is_null($groupByTid)) {
            $javaParamArr['groupByTid'] = $groupByTid;
        }

        if (!is_null($lid)) {
            $javaParamArr['lid'] = $lid;
        }

        //发起请求
        return $this->call('countByParams', $javaParamArr);
    }

    /**
     * 根据远端订单号查询平台订单信息,返回最新1条
     * <AUTHOR>  Li
     * @date 2020/12/22
     *
     * @param  string  $remotenum  景区id
     *
     * @return array
     */
    public function findNewestOrderByRemoteOrdernum(string $remotenum)
    {
        if (!$remotenum) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'remotenum' => $remotenum,
        ];

        //发起请求
        return $this->_unCamelize($this->call('findNewestOrderByRemoteOrdernum', $javaParamArr));
    }

    /**
     * 根据远端订单号查询平台订单信息,返回最新1条（先从当前库查询，如果查询不到走es查询）
     * @deprecated
     */
    public function findNewestOrderByRemoteOrdernumFromEs($remotenum)
    {
        if (!$remotenum) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }
        //请求参数
        $javaParamArr = [
            'remotenum' => $remotenum,
        ];
        //发起请求
        return $this->_unCamelize($this->call('findNewestOrderByRemoteOrdernumFromEs', $javaParamArr));
    }

    /**
     * 获取活跃的景区,不会重复,返回最新1条
     * <AUTHOR>  Li
     * @date 2020/12/22
     *
     * @param  string  $minOrdertime  下单时间起始
     * @param  string  $maxOrdertime  下单时间截止
     *
     * @return array
     */
    public function findLidsByOrderTimeBetween(string $minOrdertime, string $maxOrdertime)
    {
        if (!$minOrdertime || !$maxOrdertime) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = [
            'minOrdertime' => $minOrdertime,
            'maxOrdertime' => $maxOrdertime,
        ];

        //发起请求
        return $this->call('findLidsByOrderTimeBetween', $javaParamArr);
    }

    /**
     * 获取时间段内指定条件的订单
     * <AUTHOR>  Li
     * @date 2022-08-30
     *
     * @param  string  $dbStart  下单时间起始
     * @param  string  $dbEnd  下单时间截止
     * @param  int  $applyId  供应商id
     * @param  string  $idCard  身份证号
     * @param  array  $ticketIds  门票集合
     * @param  array  $orderStatus  订单状态集合
     *
     * @return array
     */
    public function findByIdCardCond(string $dbStart, string $dbEnd, int $applyId, string $idCard, array $ticketIds, array $orderStatus = [1, 8])
    {
        if (!$dbStart || !$dbEnd || !$applyId || !$idCard || !$ticketIds || !$orderStatus) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        //请求参数
        $javaParamArr = compact('dbStart', 'dbEnd', 'applyId', 'idCard', 'ticketIds', 'orderStatus');

        //发起请求
        return $this->call('findByIdCardCond', $javaParamArr);
    }

}