<?php
/**
 * 门票产品线支付后回调业务处理
 *
 * <AUTHOR>
 * @date    2022-08-05
 */

namespace Business\JavaApi\Order;

use \Business\JavaApi\CallBase;
use Library\Util\DebugUtil;

class ScenicPayCallback extends CallBase
{

    private $_orderServiceApi = '';

    public function __construct()
    {
        $tmpList = load_config('scenic_line', 'javaApiUrl');
        $apiInfo = $tmpList['transactionOrderService'];

        if (!$apiInfo) {
            return false;
        }

        $this->setOpenLog(true);

        parent::__construct($apiInfo, 'transactionOrderService', 2);
    }

    /**
     * 景区门票产品线支付回调业务接口 - 外部支付，包括在线支付和线下支付
     * <AUTHOR>
     * @date 2022/08/26
     *
     * @param $ordernum
     * @param $paymentSerialNumber
     * @param $tradeNo
     * @param $sourceT
     * @param $totalMoney
     * @param $isPayToPft
     * @param $payChannel
     * @param $payTerminal
     * @param $paySource
     * @param $isNeedVerify
     * @param $buyerInfo
     * @param $sellInfo
     * @param $opId
     * @param $isIgnorePayRecord bool 是否忽略校验已支付记录
     * @param array $extData 扩展信息 ['subOpId' => '子商户操作人']
     *
     * @return array
     */
    public function payCallBack($ordernum, $paymentSerialNumber, $tradeNo, $sourceT, $totalMoney, $isPayToPft, $payChannel, $payTerminal, $paySource, $isNeedVerify = false, $payId = '', $enableProfitSharing = false, $buyerInfo = '', $sellInfo = '', $opId = 0, $isIgnorePayRecord = false, $extData = [])
    {
        $javaParamArr = [
            'ordernum'            => $ordernum,
            'paymentSerialNumber' => $paymentSerialNumber,
            'tradeNo'             => $tradeNo,
            'sourceT'             => $sourceT,
            'totalFee'            => $totalMoney,
            'payToPft'            => $isPayToPft,
            'payChannel'          => $payChannel,
            'payTermianl'         => $payTerminal,
            'buyerInfo'           => $buyerInfo,
            'sellInfo'            => $sellInfo,
            'oper'                => $opId,
            'verify'              => $isNeedVerify,
            'payId'               => $payId,
            'enableProfitSharing' => $enableProfitSharing,
            'isIgnorePayRecord'   => $isIgnorePayRecord,
        ];

        if (!is_null($paySource)) {
            $javaParamArr['paySource'] = $paySource;
        }
        if (isset($extData['subOpId'])) {
            $javaParamArr['subOpId'] = $extData['subOpId'];
        }

        $this->switchDomainNameConfig($this->_orderServiceApi);

        //发起请求
        $res = $this->call('payCallBack', $javaParamArr);

        if ($res['code'] == 200) {
            //支付成功
            return $this->orderReturn(200, '支付成功');
        } elseif ($res['code'] == 1000) {
            return $this->orderReturn(1000, '业务正在处理中');
        } elseif ($res['code'] == 50510) {
            //采购上游失败，这个特殊处理，这种情况下不进行后续的原路退回退款
            //现在是上游那边直接发起退票，由退票那边去发起退款，这边就先不处理
            return $this->orderReturn(50510, '第三方下单失败');
        }  elseif ($res['code'] == 500) {
            throw new \Exception('系统异常，请重试', 500);
        } else {
            #/alidata/log/site/log_system/ddd/debug/
            DebugUtil::info([
                'tag' => 'payCallBack',
                'method' => __METHOD__,
                'res' => $res,
                'javaParams' => $javaParamArr
            ]);
            //支付失败
            $errorMsg = "支付失败：{$res['msg']}【code={$res['code']}】003";

            return $this->orderReturn(0, $errorMsg, ['err_code' => $res['code']]);
        }
    }

    /**
     * 景区门票产品线支付回调业务接口 - 内部订单支付
     *
     * <AUTHOR>
     * @date 2022/08/26
     *
     * @param $ordernum string 订单号
     * @param $payChannel int 支付渠道
     * @param $payOpId int 支付人员
     *
     * @return array
     */
    public function insidePay($ordernum = '', $payChannel = 0, $payOpId = 0)
    {
        $javaParamArr = [
            'ordernum'   => $ordernum,
            'payChannel' => $payChannel,
            'payOpId'    => $payOpId,
        ];

        $this->switchDomainNameConfig($this->_orderServiceApi);

        //发起请求
        $res = $this->call('insidePay', $javaParamArr);

        if ($res['code'] == 200) {
            //支付成功
            return $this->orderReturn(200, '支付成功');
        } elseif ($res['code'] == 1000) {
            return $this->orderReturn(1000, '业务正在处理中');
        } elseif ($res['code'] == 500) {
            throw new \Exception('系统异常，请重试', 500);
        } else {
            //支付失败
            $errorMsg = "支付失败：{$res['msg']}【code={$res['code']}】";

            return $this->orderReturn(0, $errorMsg, ['err_code' => $res['code']]);
        }
    }

    /**
     * 切换下单服务api
     * <AUTHOR>
     * @date   2024/04/12
     *
     * @param string $orderServiceApi
     */
    public function switchOrderServiceApi($orderServiceApi = '')
    {
        if (!empty($orderServiceApi)) {
            $this->_orderServiceApi = $orderServiceApi;
        }
    }
}
