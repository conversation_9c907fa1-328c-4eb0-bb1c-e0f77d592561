<?php
/**
 * 交易记录查询相关操作接口
 * @date 2019-03-06
 * <AUTHOR>
 */
namespace Business\JavaApi\Trade;

use \Model\Member\Member;
use \Business\JavaApi\CallBase;
use \Library\Constants\Account\BookSubject;
use \Library\Constants\Account\TemplateItem;

class Query extends CallBase {

    //用户查询
    private $_memebrQuery = 'memberQuery';
    //用户查询并返回总数
    private $_memebrQueryWithTotal = 'memberQueryWithTotal';
    //管理员查询
    private $_adminQuery  = 'adminQuery';
    //集团账号查询
    private $_groupQuery = 'groupQuery';
    //id查询一条记录
    private $_queryById   = 'queryTradejournalWithId';
    //用户查询汇总
    private $_memberSummary = 'memberSummary';
    //管理员查询汇总
    private $_adminSummary  = 'adminSummary';
    //集团账号查询汇总
    private $_groupSummary = 'groupSummary';
    //交易记录查询统计
    private $_tradeCollect = 'tradeCollect';
    //根据itemcode汇总
    private $_summaryForItemcode = 'summaryForItemcode';
    //生成交易记录实时数据
    const __CREATE_TRADE_REPORT__ = '/web/v1/report/accountTransRecordService/summaryIntradayTransactionReportByFid';

    //交易范围-总金额
    const TRADE_SCOPE_TOTAL     = 'total';
    //交易范围-可用金额
    const TRADE_SCOPE_AVAILABLE = 'available';
    //交易范围-冻结金额
    const TRADE_SCOPE_FROZEN    = 'frozen';

    public function __construct() {
        $tmpList = load_config('trade', 'javaApiUrl');
        $apiInfo = $tmpList['query'];

        if (!$apiInfo) {
            return false;
        }
        parent::__construct($apiInfo, 'query');
    }


    /**
     * 根据用户ID和订单ID批量获取用户的交易记录(兼容旧接口)
     * <AUTHOR>
     * @date   2019-03-06
     * @param  array     $memberArr 会员id数组
     * @param  array     $orderArr  订单号数组
     * @return array
     */
    public function getMembersJournalMoney($memberArr, $orderArr) {

        if (!$memberArr || !$orderArr || !is_array($memberArr) || !is_array($orderArr)) {
            return [];
        }

        $condition = [
            'fid_arr'   => $memberArr,
            'order_arr' => $orderArr
        ];

        $queryRes = $this->_adminQuery($condition);
        if (isset($queryRes['code'])) {
            return $queryRes;
        } else {
            return $this->returnData(500, '接口错误');
        }
    }

    /**
     * 退款业务获取ptype
     * <AUTHOR>
     * @date   2019-03-06
     * @param  int     $fid     会员id
     * @param  string  $orderId 订单号id
     * @return array
     */
    public function getPtypeForRefundBiz($fid, $orderId) {

        if (!$fid || !$orderId) {
            return false;
        }

        $condition = [
            'fid'       => $fid,
            'order_arr' => [$orderId],
            'daction'   => 1
        ];

        $queryRes = $this->_memebrQuery($condition);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes['data'][0]['ptype'];
        } else {
            return false;
        }
    }

    /**
     * 会员记录查询（不要调用，这里只做一些旧业务的兼容用）
     * <AUTHOR>
     * @date   2019-03-07
     * @param  array      $condition 查询 条件
     * @return array
     */
    public function memberQuery(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $queryRes = $this->_memebrQuery($condition);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    public function memberQueryByStartId(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $queryRes = $this->_memebrQueryByStartId($condition);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    public function memberQueryByPageQuery(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $finalCondition = $this->_combineCommonConditionWithPageQuery($condition);

        $queryRes = $this->_call('pageQuery', $finalCondition);

        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    /**
     * 授信交易记录列表查询
     * <AUTHOR>
     * @date   2020-03-05
     * @param  string     $beginDate      开始日期
     * @param  string     $endDate        结束日期
     * @param  array      $items          费用项数组
     * @param  int        $fid            己方id
     * @param  int        $aid            对方id
     * @param  int        $page           当前页码
     * @param  int        $size           每页条数
     * @return array
     */
    public function creditListQuery(string $beginDate, string $endDate, array $items, int $fid, int $aid, int $page = 1, int $size = 15) {

        if (!$beginDate || !$endDate || !$fid || !$aid || !$page || !$size) {
            return $this->returnData(204, '参数错误');
        }

        //获取对用的account_id
        $memberModel = new Member('slave');
        $mapping = $memberModel->getAccountIdByMidArr([$fid, $aid]);

        $javaParamArr = [
            'beginDateStr'       => $beginDate,
            'endDateStr'         => $endDate,
            'templateItemCodes'  => $items,
            'tradeAccountId'     => $mapping[$aid],
            'accountId'          => $mapping[$fid],
            'pageNum'            => $page,
            'pageSize'           => $size
        ];

        return $this->call('creditListQuery', $javaParamArr);
    }

    /**
     * 授信交易记录列表查询
     * <AUTHOR>
     * @date   2020-03-05
     *
     * @param  string  $beginDate    开始日期
     * @param  string  $endDate      结束日期
     * @param  array   $items        费用项数组
     * @param  string  $orderId      订单号列表
     * @param  int     $fid          己方id
     * @param  int     $aid          对方id
     * @param  int     $page         当前页码
     * @param  int     $size         每页条数
     *
     * @return array
     */
    public function creditListQueryPage(string $beginDate, string $endDate, array $items, int $fid, int $aid, string $orderId, int $page = 1, int $size = 15)
    {

        if (!$beginDate || !$endDate || !$items || !$aid || !$page || !$size) {
            return $this->returnData(204, '参数错误');
        }

        //获取对用的account_id
        $memberModel = new Member('slave');
        $mapping     = $memberModel->getAccountIdByMidArr([$fid, $aid]);

        $javaParamArr = [
            'beginDateStr'      => $beginDate,
            'endDateStr'        => $endDate,
            'templateItemCodes' => $items,
            'tradeAccountId'    => $mapping[$aid],
            'accountId'         => $mapping[$fid],
            'orderId'           => trim($orderId),
            'pageNum'           => $page,
            'pageSize'          => $size,
        ];

        return $this->call('creditListQueryPage', $javaParamArr);
    }

    /**
     * 会员交易记录查询（同时返回总条数）
     * <AUTHOR>
     * @date   2019-11-07
     * @param  array      $condition 查询条件
     * @return array
     */
    public function memberQueryWithTotal(array $condition) 
    {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        return $this->_memebrQueryWithTotal($condition);
    }

    public function memberSummary($condition) {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $queryRes = $this->_memebrQuery($condition, true);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    /**
     * 管理员交易记录查询
     * <AUTHOR>
     * @date   2019-03-07
     * @param  array      $condition 查询 条件
     * @return array
     */
    public function adminQuery(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $queryRes = $this->_adminQuery($condition);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    public function adminQueryByStartId($condition)
    {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }
        $queryRes = $this->_adminQueryByStartId($condition);

        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    public function adminQueryByPageQuery($condition)
    {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $finalCondition = $this->_combineCommonConditionWithPageQuery($condition);

        $queryRes = $this->_call('pageQuery', $finalCondition);

        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    /**
     * 管理员交易记录查询
     * <AUTHOR>
     * @date   2019-03-07
     * @param  array      $condition 查询 条件
     * @return array
     */
    public function adminSummary(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        $queryRes = $this->_adminQuery($condition, true);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    /**
     * 集团账号交易记录查询
     * <AUTHOR>
     * @date   2019-03-07
     * @param  array      $condition 查询 条件
     * @return array
     */
    public function groupQuery(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        if (!isset($condition['group_fid'])) {
            return $this->returnData(204, '集团账号id缺失');
        }

        $queryRes = $this->_groupQuery($condition);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    public function groupQueryByStartId($condition)
    {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        if (!isset($condition['group_fid'])) {
            return $this->returnData(204, '集团账号id缺失');
        }

        $queryRes = $this->_groupQueryByStartId($condition);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }

    public function groupQueryByPageQuery($condition)
    {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        if (!isset($condition['group_fid'])) {
            return $this->returnData(204, '集团账号id缺失');
        }

        $finalCondition = $this->_combineCommonConditionWithPageQuery($condition);
        $finalCondition['groupMemberId'] = (int)$condition['group_fid'];

        $queryRes = $this->_call('pageQuery', $finalCondition);

        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }


    /**
     * 集团账号交易记录查询
     * <AUTHOR>
     * @date   2019-03-07
     * @param  array      $condition 查询 条件
     * @return array
     */
    public function groupSummary(array $condition) {

        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }

        if (!isset($condition['group_fid'])) {
            return $this->returnData(204, '集团账号id缺失');
        }

        $queryRes = $this->_groupQuery($condition, true);
        if (isset($queryRes['code']) && $queryRes['code'] == 200) {
            return $queryRes;
        } else {
            return false;
        }
    }


    /**
     * 根据id查询一条交易记录
     * <AUTHOR>
     * @date   2019-03-11
     * @param  int     $id id
     * @param  string  $beginTime 开始时间
     * @param  string  $endTime   结束时间
     * @return array
     */
    public function queryById($id, $beginTime, $endTime) {

        if (!$id || !$beginTime || !$endTime) {
            return $this->returnData(204, 'id缺失');
        }

        $javaParamArr = [
            'id'        => (int)$id,
            'beginDate' => date('Y-m-d H:i:s', strtotime($beginTime)),
            'endDate'   => date('Y-m-d H:i:s', strtotime($endTime))
        ];

        return $this->call($this->_queryById, $javaParamArr);
    }


    /**
     * 交易记录统计
     * <AUTHOR>
     * @date   2019-03-07
     * @param  array      $condition 查询 条件
     * @return array
     */
    public function tradeCollect(array $condition) {
        if (!$condition) {
            return $this->returnData(204, '参数错误');
        }
        if (!isset($condition['group']) || !in_array($condition['group'], ['subjectCode', 'tradeSubjectCode'])) {
            return $this->returnData(204, '参数错误');
        }

        $finalCondition = $this->_combineCommonCondition($condition);
        $finalCondition['statisticalParam'] = $condition['group'];

        $memberArr = [];
        if (isset($condition['fid_arr'])) {
            $memberArr = array_merge($condition['fid_arr'], $memberArr);
        }
        if (isset($condition['aid_arr'])) {
            $memberArr = array_merge($condition['aid_arr'], $memberArr);
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid_arr'])) {
                foreach ($condition['fid_arr'] as $fid) {
                    $finalCondition['accountIds'][] = (int)$mapping[$fid];
                }
            }
            if (isset($condition['aid_arr'])) {
                foreach ($condition['aid_arr'] as $aid) {
                    $finalCondition['tradeAccountIds'][] = (int)$mapping[$aid];
                }
            }
        }

        return $this->call($this->_tradeCollect, $finalCondition);
    }


    private function _memebrQuery($condition, $isSummary = false) 
    {
        $finalCondition = $this->_combineCommonCondition($condition);

        $memberArr = [];
        if (isset($condition['fid'])) {
            $memberArr[] = $condition['fid'];
        }
        if (isset($condition['aid'])) {
            $memberArr[] = $condition['aid'];
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid'])) {
                $finalCondition['accountId'] = (int)$mapping[$condition['fid']];
            }
            if (isset($condition['aid'])) {
                $finalCondition['tradeAccountId'] = (int)$mapping[$condition['aid']];
            }
        }

        $callUrl = $isSummary ? $this->_memberSummary : $this->_memebrQuery;
        return $this->_call($callUrl, $finalCondition);
    }

    private function _memebrQueryByStartId($condition)
    {
        $finalCondition = $this->_combineCommonConditionWithStartId($condition);

        $memberArr = [];
        if (isset($condition['fid'])) {
            $memberArr[] = $condition['fid'];
        }
        if (isset($condition['aid'])) {
            $memberArr[] = $condition['aid'];
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid'])) {
                $finalCondition['accountId'] = (int)$mapping[$condition['fid']];
            }
            if (isset($condition['aid'])) {
                $finalCondition['tradeAccountId'] = (int)$mapping[$condition['aid']];
            }
        }

        return $this->_call('memberQueryByStartId', $finalCondition);
    }


    private function _memebrQueryWithTotal($condition) 
    {
        $finalCondition = $this->_combineCommonCondition($condition);

        $memberArr = [];
        if (isset($condition['fid'])) {
            $memberArr[] = $condition['fid'];
        }
        if (isset($condition['aid'])) {
            $memberArr[] = $condition['aid'];
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid'])) {
                $finalCondition['accountId'] = (int)$mapping[$condition['fid']];
            }
            if (isset($condition['aid'])) {
                $finalCondition['tradeAccountId'] = (int)$mapping[$condition['aid']];
            }
        }

        return $this->_call($this->_memebrQueryWithTotal, $finalCondition);
    }


    private function _adminQuery($condition, $isSummary = false) {

        $finalCondition = $this->_combineCommonCondition($condition);

        $memberArr = [];
        if (isset($condition['fid_arr'])) {
            $memberArr = array_merge($condition['fid_arr'], $memberArr);
        }
        if (isset($condition['aid_arr'])) {
            $memberArr = array_merge($condition['aid_arr'], $memberArr);
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid_arr'])) {
                foreach ($condition['fid_arr'] as $fid) {
                    $finalCondition['accountIds'][] = (int)$mapping[$fid];
                }
            }
            if (isset($condition['aid_arr'])) {
                foreach ($condition['aid_arr'] as $aid) {
                    $finalCondition['tradeAccountIds'][] = (int)$mapping[$aid];
                }
            }
        }

        $callUrl = $isSummary ? $this->_adminSummary : $this->_adminQuery;
        return $this->_call($callUrl, $finalCondition);
    }

    private function _adminQueryByStartId($condition)
    {
        $finalCondition = $this->_combineCommonConditionWithStartId($condition);
        $memberArr = [];
        if (isset($condition['fid_arr'])) {
            $memberArr = array_merge($condition['fid_arr'], $memberArr);
        }
        if (isset($condition['aid_arr'])) {
            $memberArr = array_merge($condition['aid_arr'], $memberArr);
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid_arr'])) {
                foreach ($condition['fid_arr'] as $fid) {
                    $finalCondition['accountIds'][] = (int)$mapping[$fid];
                }
            }
            if (isset($condition['aid_arr'])) {
                foreach ($condition['aid_arr'] as $aid) {
                    $finalCondition['tradeAccountIds'][] = (int)$mapping[$aid];
                }
            }
        }

        return $this->_call('adminQueryByStartId', $finalCondition);
    }


    private function _groupQuery($condition, $isSummary =  false) {

        $finalCondition = $this->_combineCommonCondition($condition);

        $finalCondition['groupMemberId'] = (int)$condition['group_fid'];

        $memberArr = [];
        if (isset($condition['fid_arr'])) {
            $memberArr = array_merge($condition['fid_arr'], $memberArr);
        }
        if (isset($condition['aid_arr'])) {
            $memberArr = array_merge($condition['aid_arr'], $memberArr);
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid_arr'])) {
                foreach ($condition['fid_arr'] as $fid) {
                    $finalCondition['accountIds'][] = (int)$mapping[$fid];
                }
            }
            if (isset($condition['aid_arr'])) {
                foreach ($condition['aid_arr'] as $aid) {
                    $finalCondition['tradeAccountIds'][] = (int)$mapping[$aid];
                }
            }
        }

        $callUrl = $isSummary ? $this->_groupSummary : $this->_groupQuery;
        return $this->_call($callUrl, $finalCondition);
    }

    private function _groupQueryByStartId($condition)
    {
        $finalCondition = $this->_combineCommonConditionWithStartId($condition);

        $finalCondition['groupMemberId'] = (int)$condition['group_fid'];

        $memberArr = [];
        if (isset($condition['fid_arr'])) {
            $memberArr = array_merge($condition['fid_arr'], $memberArr);
        }
        if (isset($condition['aid_arr'])) {
            $memberArr = array_merge($condition['aid_arr'], $memberArr);
        }
        if ($memberArr) {
            //获取对用的account_id
            $memberModel = new Member('slave');
            $mapping = $memberModel->getAccountIdByMidArr($memberArr);
            if (!$mapping) {
                return $this->returnData(203, '未匹配到账户信息');
            }
            if (isset($condition['fid_arr'])) {
                foreach ($condition['fid_arr'] as $fid) {
                    $finalCondition['accountIds'][] = (int)$mapping[$fid];
                }
            }
            if (isset($condition['aid_arr'])) {
                foreach ($condition['aid_arr'] as $aid) {
                    $finalCondition['tradeAccountIds'][] = (int)$mapping[$aid];
                }
            }
        }

        return $this->_call('groupQueryByStartId', $finalCondition);
    }


    /**
     * 通用的请求参数转换
     * <AUTHOR>
     * @date   2019-03-06
     * @param  array     $condition 请求参数
     * @return array
     */
    private function _combineCommonCondition($condition) {

        $finalCondition = [];
        if (isset($condition['begin_date'], $condition['end_date'])) {
            //开始日期
            $finalCondition['beginDateStr'] = date('Y-m-d H:i:s', strtotime($condition['begin_date']));
            //结束日期
            $finalCondition['endDateStr'] = date('Y-m-d H:i:s', strtotime($condition['end_date']));
        }

        if (isset($condition['order_arr'])) {
            $finalCondition['orderIds'] = array_map('strval', $condition['order_arr']);
        }
        
        if (isset($condition['tradeno_arr'])) {
            $finalCondition['tradeNos'] = array_map('strval', $condition['tradeno_arr']);
        }

        $ptype2SubjectCode = load_config('ptype_2_subject_code', 'trade_record');
        $dtype2SubjectCode = load_config('dtype_2_item_code', 'trade_record');
        if (isset($condition['ptype_arr'])) {
            foreach ($condition['ptype_arr'] as $ptype) {
                $finalCondition['tradeSubjectCodes'][] = (int)$ptype2SubjectCode[$ptype];
            }
        }
        if (isset($condition['trade_subject_codes'])) {
            $finalCondition['tradeSubjectCodes'] = $condition['trade_subject_codes'];
        }

        if (isset($condition['account_type_arr'])) {
            foreach ($condition['account_type_arr'] as $ptype) {
                $finalCondition['subjectCodes'][] = (int)$ptype2SubjectCode[$ptype];
            }
        }
        if (isset($condition['subject_codes'])) {
            $finalCondition['subjectCodes'] = $condition['subject_codes'];
        }

        if (isset($condition['dtype_arr'])) {
            foreach ($condition['dtype_arr'] as $dtype) {
                $finalCondition['templateItemCodes'][] = (int)$dtype2SubjectCode[$dtype];
            }
        }
        if (isset($condition['item_codes'])) {
            $finalCondition['templateItemCodes'] = $condition['item_codes'];
        }

        if (isset($condition['daction'])) {
            $finalCondition['daction'] = (int)$condition['daction'];
        }

        if (isset($condition['page'], $condition['size'])) {
            $finalCondition['pageNum']  = (int)$condition['page'];
            $finalCondition['pageSize'] = (int)$condition['size'];
        }

        if (isset($condition['tradeScope'])) {
            $finalCondition['tradeScope']  = (string)$condition['tradeScope'];
        }

        return $finalCondition;
    }

    private function _combineCommonConditionWithStartId($condition)
    {
        $finalCondition = [];
        if (isset($condition['begin_date'], $condition['end_date'])) {
            //开始日期
            $finalCondition['beginDateStr'] = date('Y-m-d H:i:s', strtotime($condition['begin_date']));
            //结束日期
            $finalCondition['endDateStr'] = date('Y-m-d H:i:s', strtotime($condition['end_date']));
        }

        if (isset($condition['order_arr'])) {
            $finalCondition['orderIds'] = array_map('strval', $condition['order_arr']);
        }

        if (isset($condition['tradeno_arr'])) {
            $finalCondition['tradeNos'] = array_map('strval', $condition['tradeno_arr']);
        }

        $ptype2SubjectCode = load_config('ptype_2_subject_code', 'trade_record');
        $dtype2SubjectCode = load_config('dtype_2_item_code', 'trade_record');
        if (isset($condition['ptype_arr'])) {
            foreach ($condition['ptype_arr'] as $ptype) {
                $finalCondition['tradeSubjectCodes'][] = (int)$ptype2SubjectCode[$ptype];
            }
        }
        if (isset($condition['trade_subject_codes'])) {
            $finalCondition['tradeSubjectCodes'] = $condition['trade_subject_codes'];
        }

        if (isset($condition['account_type_arr'])) {
            foreach ($condition['account_type_arr'] as $ptype) {
                $finalCondition['subjectCodes'][] = (int)$ptype2SubjectCode[$ptype];
            }
        }
        if (isset($condition['subject_codes'])) {
            $finalCondition['subjectCodes'] = $condition['subject_codes'];
        }

        if (isset($condition['dtype_arr'])) {
            foreach ($condition['dtype_arr'] as $dtype) {
                $finalCondition['templateItemCodes'][] = (int)$dtype2SubjectCode[$dtype];
            }
        }
        if (isset($condition['item_codes'])) {
            $finalCondition['templateItemCodes'] = $condition['item_codes'];
        }

        if (isset($condition['daction'])) {
            $finalCondition['daction'] = (int)$condition['daction'];
        }

        if (isset($condition['startId'])) {
            $finalCondition['startId']  = (int)$condition['startId'];
        }

        if (isset($condition['size'])){
            $finalCondition['pageSize'] = (int)$condition['size'];
        }

        return $finalCondition;
    }

    private function _combineCommonConditionWithPageQuery($condition)
    {
        $finalCondition = [
            'requestId' => $condition['requestId'] ?? uniqid() . rand(1000, 9000)
        ];
        if (isset($condition['begin_date'], $condition['end_date'])) {
            //开始日期
            $finalCondition['beginDate'] = date('Y-m-d H:i:s', strtotime($condition['begin_date']));
            //结束日期
            $finalCondition['endDate'] = date('Y-m-d H:i:s', strtotime($condition['end_date']));
        }

        if (isset($condition['order_arr'])) {
            $finalCondition['orderIds'] = array_map('strval', $condition['order_arr']);
        }

        if (isset($condition['tradeno_arr'])) {
            $finalCondition['tradeNos'] = array_map('strval', $condition['tradeno_arr']);
        }

        $ptype2SubjectCode = load_config('ptype_2_subject_code', 'trade_record');
        $dtype2SubjectCode = load_config('dtype_2_item_code', 'trade_record');
        if (isset($condition['ptype_arr'])) {
            foreach ($condition['ptype_arr'] as $ptype) {
                $finalCondition['tradeSubjectCodes'][] = (int)$ptype2SubjectCode[$ptype];
            }
        }
        if (isset($condition['trade_subject_codes'])) {
            $finalCondition['tradeSubjectCodes'] = $condition['trade_subject_codes'];
        }

        if (isset($condition['account_type_arr'])) {
            foreach ($condition['account_type_arr'] as $ptype) {
                $finalCondition['subjectCodes'][] = (int)$ptype2SubjectCode[$ptype];
            }
        }
        if (isset($condition['subject_codes'])) {
            $finalCondition['subjectCodes'] = $condition['subject_codes'];
        }

        if (isset($condition['dtype_arr'])) {
            foreach ($condition['dtype_arr'] as $dtype) {
                $finalCondition['templateItemCodes'][] = (int)$dtype2SubjectCode[$dtype];
            }
        }
        if (isset($condition['item_codes'])) {
            $finalCondition['templateItemCodes'] = $condition['item_codes'];
        }

        if (isset($condition['daction'])) {
            $finalCondition['daction'] = (int)$condition['daction'];
        }

        if (isset($condition['accountIds'])) {
            $finalCondition['accountIds'] = $condition['accountIds'];
        }

        if (isset($condition['tradeAccountIds'])) {
            $finalCondition['tradeAccountIds'] = $condition['tradeAccountIds'];
        }

        if (isset($condition['page'], $condition['size'])) {
            $finalCondition['pageNum']  = (int)$condition['page'];
            $finalCondition['pageSize'] = (int)$condition['size'];
        }

        if (isset($condition['tradeScope'])) {
            $finalCondition['tradeScope'] = $condition['tradeScope'];
        }

        return $finalCondition;
    }


    /**
     * 根据itemcode汇总
     * <AUTHOR>
     * @date   2019-03-27
     * @param  integer    $memberId  会员id
     * @param  string     $beginTime 开始时间
     * @param  string     $endTime   结束时间
     * @param  integer    $daction   0收入1支出
     * @return array
     */
    public function summaryForItemcode($memberId, $beginTime, $endTime, $daction = 0) {

        if (!$memberId || !$beginTime || !$endTime) {
            return $this->returnData(203, '参数错误');
        }

        //获取对用的account_id
        $memberModel = new Member('slave');
        $mapping = $memberModel->getAccountIdByMidArr([$memberId]);
        if (!$mapping) {
            return $this->returnData(203, '未匹配到账户信息');
        }
        $accountId = $mapping[$memberId];

        $beginTime = date('Y-m-d H:i:s', strtotime($beginTime));
        $endTime   = date('Y-m-d H:i:s', strtotime($endTime));
        $daction   = intval($daction);

        $javaParamArr = [
            'beginDateStr' => $beginTime,
            'endDateStr'   => $endTime,
            'accountId'    => $accountId,
            'daction'      => $daction
        ];

        return $this->call($this->_summaryForItemcode, $javaParamArr);
    }

    private function _call($url, $condition) {
        $result = $this->call($url, $condition);
        if ($result['code'] == 200 && $result['data'] && isset($result['data'][0])) {
            //字段兼容旧接口
            foreach ($result['data'] as &$item) {
                $item['trade_no'] = $item['tradeNo'];
                $item['account_type'] = $item['accountType'];
            }
        }
        return $result;
    }

    /**
     * 用户请求后生成实时的交易报表数据
     * @author: zhangyz
     * @date: 2020/3/3
     *
     * @param int $fid 用户ID
     *
     * @return bool
     */
    public static function createTradeReport($fid)
    {
        if (!$fid) {
            return false;
        }

        $params = [
            'transaction_id' => rand(100000, 999999) . time(),
            'requestTime'    => date('Y-m-d H:i:s'),
            'content'        => [
                'fid' => $fid
            ]
        ];

        $result = self::_customCurlPost(self::__CREATE_TRADE_REPORT__, $params);

        if ($result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }
}