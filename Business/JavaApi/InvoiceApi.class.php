<?php
/**
 * 发票对接JAVA接口
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/5/28
 * Time: 10:10
 */

namespace Business\JavaApi;

class InvoiceApi extends BaseApi {
    private $_setInvoiceUrl            = '/invoice/v1/business/bind/baiwang';
    private $_getInvoiceInfoUrl       = '/invoice/v1/business/baiwang';
    private $_setSpecialInvoiceUrl    = '/invoice/v1/business/sepcial/info';
    private $_setBusinessInvoiceUrl   = '/invoice/v1/business/config';
    private $_addSpecialInvoiceUrl    = '/invoice/v1/record/pft/special/apply';
    private $_addCommonInvoiceUrl     = '/invoice/v1/pft/issue';
    private $_syncInvoiceStatusUrl    = '/invoice/v1/pft/issue/queryBatchYongYouInvoiceStatusByTradeNo';
    private $_updateSpecialInvoiceUrl = '/invoice/v1/business/sepcial/info';
    private $_getPftInvoiceListUrl    = '/invoice/v1/record/pft';
    private $_getPftInvoiceByIdUrl    = '/invoice/v1/record/pft';
    private $_getInvoiceContentUrl    = '/invoice/v1/commodity/tax/rate';
    private $_getSpecialConfigUrl     = '/invoice/v1/business/sepcial/info';
    private $_setAddressUrl            = '/invoice/v1/business/special/express/info';
    private $_getAddressUrl            = '/invoice/v1/business/special/express/info';
    private $_getInvoiceCheckUrl      = '/invoice/v1/record/pft';
    private $_invoiceCheckExcelUrl    = '/invoice/v1/record/pft/export';
    private $_addCourierNumberUrl     = '/invoice/v1/record/pft/special/express/';
    private $_addOfflineRecordUrl     = '/invoice/v1/record/pft/business/add';


    /**
     * 商家电子发票设置
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return bool
     */
    public function bindInvoice($data) {
        $result = self::_customCurlPost($this->_setInvoiceUrl, $data, 'POST');

        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 商家电子发票设置
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return bool
     */
    public function setBusinessInvoice($data) {
        $result = self::_customCurlPost($this->_setBusinessInvoiceUrl, $data, 'POST');

        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据Id获取票付通开给商家开票记录详细信息
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  int  $businessId   商家ID
     *
     * @return array|bool
     */
    public function getInvoiceInfo($businessId) {
        $this->_getInvoiceInfoUrl .= '/'.$businessId;

        $result = self::_customCurlPost($this->_getInvoiceInfoUrl, [], 'GET');

        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }

    /**
     * 根据Id获取票付通开给商家开票记录详细信息
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  int  $businessId   商家ID
     *
     * @return array|bool
     */
    public function getSpecialConfig($businessId) {
        $this->_getSpecialConfigUrl .= '/'.$businessId;

        $result = self::_customCurlPost($this->_getSpecialConfigUrl, [], 'GET');

        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }

    /**
     * 配置收件地址
     *
     * @date   2017-06-09
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return bool
     */
    public function setAddress($data) {
        if (isset($data['id'])) {
            $method = 'PUT';
            unset($data['id']);
        } else {
            $method = 'POST';
        }

        $result = self::_customCurlPost($this->_setAddressUrl, [], $method, $data);
        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取收件地址
     *
     * @date   2017-06-09
     * <AUTHOR> Lan
     *
     * @param  int  $businessId  商家ID
     *
     * @return bool
     */
    public function getAddress($businessId) {
        $result = self::_customCurlPost($this->_getAddressUrl."/$businessId", [], 'GET');
        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }

    /**
     * 专票申请-配置
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return bool
     */
    public function setSpecialInvoice($data) {
        $result = self::_customCurlPost($this->_setSpecialInvoiceUrl, $data, 'POST');
        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 修改专票
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return bool
     */
    public function updateSpecialInvoice($data) {
        $result = self::_customCurlPost($this->_updateSpecialInvoiceUrl, $data, 'PUT');
        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 分页条件商家获取票付通开票记录列表信息
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return array
     */
    public function getPftInvoiceList($data) {
        $result = self::_customCurlPost($this->_getPftInvoiceListUrl, [], 'GET', $data);
        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }


    /**
     * 获取审核显示数据
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return array
     */
    public function getInvoiceCheck($data) {
        $result = self::_customCurlPost($this->_getInvoiceCheckUrl, [], 'GET', $data);
        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }

    /**
     * 获取审核导出数据
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return array
     */
    public function invoiceCheckExcel($data) {
        $result = self::_customCurlPost($this->_invoiceCheckExcelUrl, [], 'GET', $data);
        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }

    /**
     * 根据Id获取票付通开给商家开票记录详细信息
     *
     * @date   2017-05-28
     * <AUTHOR> Lan
     *
     * @param  int  $invoiceId   发票ID
     *
     * @return array|bool
     */
    public function getPftInvoiceById($invoiceId) {
        $this->_getPftInvoiceByIdUrl .= '/' . $invoiceId;
        $result = self::_customCurlPost($this->_getPftInvoiceByIdUrl, [], 'GET');

        if ($result && $result['code'] == 200) {
            return $result['data'];
        } else {
            return false;
        }
    }



    /**
     * 专票申请
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     *
     * @return bool
     */
    public function addSpecialInvoice($data) {
        $result = self::_customCurlPost($this->_addSpecialInvoiceUrl, [], 'POST', $data);
        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 普票申请
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @param  array  $data  请求参数
     * @param  array  $body  请求参数
     *
     * @return array
     */
    public function addCommonInvoice($data, $body) {
        $result = self::_customCurlPost($this->_addCommonInvoiceUrl, $body, 'POST', $data);
        if ($result && $result['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 同步发票状态
     * @param int $memberId
     * @param array $tradeNos
     * @return bool
     */
    public function syncInvoiceStatus($memberId, array $tradeNos)
    {
        $query = [
            'memberId' => $memberId,
            'tradeNoStr' => implode(',', $tradeNos)
        ];
        $result = self::_customCurlPost($this->_syncInvoiceStatusUrl, [], 'GET', $query);
        return $result && $result['code'] == 200;
    }

    /**
     * 获取开票内容
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @param  int   $memberId  请求参数
     *
     * @return array|bool
     */
    public function getInvoiceContent($memberId) {
        $this->_getInvoiceContentUrl .= '/' . $memberId;
        $result = self::_customCurlPost($this->_getInvoiceContentUrl, [], 'GET');

        if ($result && $result['code'] == 200) {
            return $result['data'];
        }

        return false;
    }

    /**
     * 设置税率
     *
     * @date   2017-06-01
     * <AUTHOR> Lan
     *
     * @param  int     $mid   商家ID
     * @param  array   $data  请求参数
     *
     * @return bool
     */
    public function setRate($mid, $data) {
        $result = self::_customCurlPost($this->_getInvoiceContentUrl."/$mid", $data, 'POST');

        if ($result && $result['code'] == 200) {
            return true;
        }
        return false;
    }

    /**
     * 填写快递单号
     *
     * @date   2017-07-19
     * <AUTHOR> Lan
     *
     * @param  int    $tradeNo  流水号
     * @param  array  $number   快递单号
     *
     * @return bool
     */
    public function addCourierNumber($tradeNo, $number) {
        $result = self::_customCurlPost($this->_addCourierNumberUrl.$tradeNo, [], 'PUT', $number);

        if ($result && $result['code'] == 200) {
            return true;
        }
        return false;
    }

    /**
     * 添加线下开票记录
     *
     * @date   2017-08-04
     * <AUTHOR> Lan
     *
     * @param  array  $data  待开数据
     *
     * @return bool
     */
    public function addOfflineRecord($data) {
        $result = self::_customCurlPost($this->_addOfflineRecordUrl, [], 'POST', $data);

        if ($result && $result['code'] == 200) {
            return true;
        }
        return false;
    }
}