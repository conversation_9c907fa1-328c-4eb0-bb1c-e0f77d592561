<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2017/8/4
 * Time: 11:00
 */
namespace Business\Member;

use Business\JavaApi\InvoiceApi;
use Model\Member\Invoice as model;
use Model\AppCenter\ModuleList;

class Invoice{
    /**
     * 线下开票数据保存操作
     *
     * @date   2017-08-04
     * <AUTHOR>
     *
     * @param  int      $fid              会员ID
     * @param  int      $transTime        交易时间
     * @param  int      $tradeType        交易类型
     * @param  int      $money            交易金额
     * @param  int      $invoiceType      发票类型
     * @param  string   $invoiceNumber    发票号码
     *
     * @return array
     */
    public function offlineInvoice($fid, $transTime, $tradeType, $money, $invoiceType, $invoiceNumber) {
        if (!$fid || !$transTime || !$tradeType || (!$money && $money != 0)) {
            return false;
        }

        $invoiceModel = new model();

        $serialNum = $invoiceModel->getSerial();

        $data = [
            'fid'        => $fid,
            'trans_time' => $transTime,
            'trade_type' => $tradeType,
            'money'      => $money,
            'time'       => time(),
            'apply_time' => time(),
            'trade_no'   => $serialNum,
            'invoice_no' => $invoiceNumber,
            'invoice_type' => $invoiceType,
            'status'     => 1,
        ];

        $res = $invoiceModel->insertDayData([$data]);
        if ($res) {
            $javaApi = new InvoiceApi();
            if ($invoiceType == 1) {
                $invoiceType = 2;
            } else {
                $invoiceType = 1;
            }
            $javaData = [
                'transactionType' => $tradeType,
                'price'            => $money,
                'serialNumber'    => $serialNum,
                'invoiceType'     => $invoiceType,
                'invoiceNumber'   => $invoiceNumber,
                'businessId'      => $fid,
            ];

            $res = $javaApi->addOfflineRecord($javaData);

            if (!$res) {
                pft_log('invoice/offline', print_r($javaData, true));
            }
            return true;
        }

        pft_log('invoice/offline', $invoiceModel->_sql());

        return false;
    }

    /**
     * 获取线下开票记录
     *
     * @date   2017-09-01
     * <AUTHOR> Lan
     *
     * @param  int      $fid              会员ID
     * @param  int      $transTime        交易时间
     * @param  int      $tradeType        交易类型
     * @param  int      $money            交易金额
     *
     * @return int
     */
    public function getOfflineInvoice($fid, $transTime, $tradeType, $money) {
        if (!$fid || !$transTime || !$tradeType || (!$money && $money != 0)) {
            return false;
        }

        $invoiceModel = new model();
        $res = $invoiceModel->checkRecord($fid, $transTime, $tradeType, $money);

        return $res;
    }
    /**
     * 获取发票审核套餐详情
     *
     * @date   2019-06-17
     * <AUTHOR>
     *
     * @param  int      $fid              会员ID
     * @param  int      $transTime        交易时间
     * @param  int      $tradeType        交易类型
     * @param  int      $money            交易金额
     *
     * @return int
     */
    //public function getInvoicePack($data){
    //    $invoiceSerialNumberIds = array_column($data,'invoiceSerialNumber');
    //    $modelInvoice = new model();
    //    $invoiceList  = $modelInvoice->getInvoiceRecordByTradeNo($invoiceSerialNumberIds);
    //    $moduleMdl = new ModuleList();
    //    foreach ($data as $key => $value){
    //        $data[$key]['pack'] = [];
    //        foreach ($invoiceList as $k => $v){
    //            if ($v['trade_no'] == $value['invoiceSerialNumber']){
    //                $dateStr = strtotime(date('Y-m-d', $v['time']));
    //                $begin = strtotime('-1 days',$dateStr);
    //                $end   = $dateStr - 1;
    //                $result = $moduleMdl->getMemberByAddTime($v['fid'],$begin,$end);
    //                if (!empty($result)){
    //                    $data[$key]['pack'] = $result;
    //                }
    //            }
    //        }
    //    }
    //    return $data;
    //}

    /**
     * 生成模块费和套餐费开票记录
     *
     * @date   2020-11-11
     * <AUTHOR>
     *
     * @param  int      $fid    会员ID
     * @param  int      $type   交易类型
     * @param  int      $money  交易金额
     *
     * @return array
     */
    public function insertInvoiceModelAndComboData(int $fid, int $type, int $money, string $orderId = '')
    {
        $logParam = [
            'key'       => '生成模块费和套餐费开票记录',
            'type'      => $type,
            'money'     => $money,
            'fid'       => $fid,
            'order_id'  => $orderId,
        ];
        pft_log('invoice_debug', json_encode($logParam, JSON_UNESCAPED_UNICODE), "month");
        if (!$fid) {
            return ['code' => 203, 'msg' => '会员ID参数错误', 'data' => []];
        }

        //120=购买码费
        if (!in_array($type,[26, 32, 65, 66, 120])) {
            return ['code' => 203, 'msg' => '交易类型参数错误', 'data' => []];
        }

        if ($money < 0) {
            return ['code' => 203, 'msg' => '交易金额参数错误', 'data' => []];
        }

        $tempArr[0]['fid']        = $fid;
        $tempArr[0]['trade_type'] = $type;
        $tempArr[0]['money']      = $money;
        $tempArr[0]['time']       = time();
        $tempArr[0]['order_id']   = $orderId;

        $invoiceModel = new model();
        $res = $invoiceModel->insertDayData($tempArr);

        if ($res) {
            return ['code' => 200, 'msg' => '成功', 'data' => ['id'=>$res]];
        }else{
            return ['code' => 400, 'msg' => '生成开票记录失败', 'data' => ['id'=>$res]];
        }


    }
}