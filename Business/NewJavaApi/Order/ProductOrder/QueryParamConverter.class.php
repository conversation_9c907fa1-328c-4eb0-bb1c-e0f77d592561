<?php

namespace Business\NewJavaApi\Order\ProductOrder;

use Business\JavaApi\Order\Query\OrderPage;
use Exception;
use Library\Controller;

class QueryParamConverter
{
    /**
     * @throws Exception
     */
    public static function convert($queryParams = []): array
    {
        // 不符合条件的字段 unset 掉
        $rangeConfig = [
            'isCombine' => [0, 1],
        ];
        foreach ($rangeConfig as $key => $range) {
            if (isset($queryParams[$key]) && !in_array($queryParams[$key], $range)) {
                unset($queryParams[$key]);
            }
        }

        // 历史逻辑：如果传入了 checkCode，则限制了可查询的字段
        if (isset($queryParams['checkCode'])) {
            foreach (array_keys($queryParams) as $key) {
                if (!in_array($key, OrderPage::_ALLOW_CHECK_CODE_FIELEDS)) {
                    unset($queryParams[$key]);
                }
            }
        }

        // 结束时间不能小于开始时间
        $timeRangeKeys = [
            ['minOrdertime', 'maxOrdertime'],
            ['minPlayTime', 'maxPlayTime'],
            ['minDtime', 'maxDtime'],
            ['minBegintime', 'maxBegintime'],
            ['cancelTimeStart', 'cancelTimeEnd'],
        ];
        foreach ($timeRangeKeys as $timeRangeKey) {
            if (empty($queryParams[$timeRangeKey[0]]) || empty($queryParams[$timeRangeKey[1]])) {
                continue;
            }
            $startTime = strtotime($queryParams[$timeRangeKey[0]]);
            $endTime = strtotime($queryParams[$timeRangeKey[1]]);
            if ($startTime > $endTime) {
                throw new Exception('开始时间不能大于结束时间');
            }
            // 如果结束时间没有传入时分秒，将结束时间改为 23:59:59
            if (preg_match('/\d{2}:\d{2}:\d{2}/', $queryParams[$timeRangeKey[1]]) === 0) {
                $queryParams[$timeRangeKey[1]] = date('Y-m-d 23:59:59', $endTime);
            }
        }

        $formatParams = [];

        $fields = [
            'minOrdertime' => ['type' => 'datetime', 'target' => 'beginOrderTime'],
            'maxOrdertime' => ['type' => 'datetime', 'target' => 'endOrderTime'],
            'minPlayTime' => ['type' => 'datetime', 'target' => 'beginPlayTime'],
            'maxPlayTime' => ['type' => 'datetime', 'target' => 'endPlayTime'],
            'minDtime' => ['type' => 'datetime', 'target' => 'beginVerifyTime'],
            'maxDtime' => ['type' => 'datetime', 'target' => 'endVerifyTime'],
            'minBegintime' => ['type' => 'datetime', 'target' => 'beginValidTime'],
            'maxBegintime' => ['type' => 'datetime', 'target' => 'endValidTime'],
            'cancelTimeStart' => ['type' => 'datetime', 'target' => 'beginCancelTime'],
            'cancelTimeEnd' => ['type' => 'datetime', 'target' => 'endCancelTime'],
            'status' => ['type' => 'array_int', 'target' => 'bizOrderStatus'],
            'tel' => ['type' => 'string', 'target' => 'mobilePhone'],
            'statusIn' => ['type' => 'array_int', 'target' => 'bizOrderStatus'],
            'ordermodeIn' => ['type' => 'array_int', 'target' => 'orderMethods'],
            'lid' => ['type' => 'array_int', 'target' => 'itemSpuIds'],
            'lidIn' => ['type' => 'array_int', 'target' => 'itemSpuIds'],
            'tidIn' => ['type' => 'array_int', 'target' => 'itemSkuIds'],
            'pmodeIn' => ['type' => 'array_int', 'target' => 'payChannels'],
            'pTypeIn' => ['type' => 'array_string', 'target' => 'productTypes'],
            'pType' => ['type' => 'array_string', 'target' => 'productTypes'],
            'sellerId' => ['type' => 'array_int', 'target' => 'sellerIds'],
            'selleridIn' => ['type' => 'array_int', 'target' => 'sellerIds'],
            'salerid' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'salerid', 'value_type' => 'int', 'operator' => 'EQ'],
            'memberRelationship' => ['type' => 'int', 'target' => 'memberRelationship'],
            'operateId' => ['type' => 'int', 'target' => 'operatorId'],
            'color' => ['type' => 'string', 'target' => 'color'],
            'cmbId' => ['type' => 'string', 'target' => 'tradeOrderId'],
            'buyerId' => ['type' => 'array_int', 'target' => 'buyerIds'],
            'buyeridIn' => ['type' => 'array_int', 'target' => 'buyerIds'],
            'remotenum' => ['type' => 'string', 'target' => 'downstreamOrderId'],
            'subType' => ['type' => 'int', 'target' => 'productSubType'],
            'isCombine' => ['type' => 'int', 'target' => 'isCombinePay'],
            'afterSaleState' => ['type' => 'int', 'target' => 'isAfterSale'],
            'checkSource' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'check_source', 'value_type' => 'array_int', 'operator' => 'IN'],
            'payStatus' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'pay_status', 'value_type' => 'array_int', 'operator' => 'IN'],
            'checkCode' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'tourists.chk_code', 'value_type' => 'string', 'operator' => 'EQ'],
            'orderName' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'ordername', 'value_type' => 'string', 'operator' => 'EQ'],
            'pidIn' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'pid', 'value_type' => 'array_int', 'operator' => 'IN'],
            'personid' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'personid', 'value_type' => 'string', 'operator' => 'EQ'],
            'ordertel' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'ordertel', 'value_type' => 'string', 'operator' => 'EQ'],
            'ifPrint' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'ifprint', 'value_type' => 'int', 'operator' => 'EQ'],
            'apiOrder' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'upstreamOrderId', 'value_type' => 'string', 'operator' => 'EQ'],
            'thirdOrder' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'upstreamOrderId', 'value_type' => 'string', 'operator' => 'EQ'],
            'upstreamOrderId' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'upstreamOrderId', 'value_type' => 'string', 'operator' => 'EQ'],
            'roundId' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'round_id', 'value_type' => 'int', 'operator' => 'EQ'],
            'touristMobile' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'tourists.mobile', 'value_type' => 'string', 'operator' => 'EQ'],
            'touristIdentificationCode' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'tourists.idcard', 'value_type' => 'string', 'operator' => 'EQ'],
            'subSid' => ['type' => 'ext', 'target' => 'extQuery', 'property' => 'subSid', 'value_type' => 'int', 'operator' => 'EQ'],
            'notLidList' => ['type' => 'ext', 'target' => 'notQuery', 'property' => 'lid', 'value_type' => 'array_int', 'operator' => 'IN'],
        ];
        foreach ($fields as $paramKey => $field) {
            if (empty($queryParams[$paramKey])) {
                // empty 对 0、false、null 都返回 true，所以这里要特殊处理
                if (in_array($paramKey, ['status', 'ifPrint', 'isCombine', 'payStatus']) && $queryParams[$paramKey] == 0 && !is_null($queryParams[$paramKey])) {
                } else if (in_array($paramKey, ['afterSaleState'])  && $queryParams[$paramKey] === false) {
                } else {
                    continue;
                }
            }
            if ($field['type'] == 'ext') {
                $formatParams[$field['target']][] = [
                    'property' => $field['property'],
                    'value' => format($field['value_type'], $queryParams[$paramKey]),
                    'type' => $field['operator']
                ];
                continue;
            }
            $formatParams[$field['target']] = format($field['type'], $queryParams[$paramKey]);
        }

        // 扩展 TIME_RANGE 类型的查询，特殊处理
        if (!empty($queryParams['minFirstDtime']) && !empty($queryParams['maxFirstDtime'])) {
            $formatParams['extQuery'][] = [
                'property' => 'firstDtime',
                'value' => [
                    format('datetime', $queryParams['minFirstDtime']),
                    format('datetime', $queryParams['maxFirstDtime']),
                ],
                'type' => 'TIME_RANGE'
            ];
        }


        if (!$queryParams['pType'] && isset($queryParams['ptypeIn']) && is_array($queryParams['ptypeIn'])) {
            $formatParams['productTypes'] = toArrayString($queryParams['ptypeIn']);
        }
        // 查询单个产品类型时，子类型默认传 0，避免将其他子类型的订单查出来，例如预售券；查询多个的话，目前就没法限制了，只能都查出来；
        if (count($formatParams['productTypes']) == 1 && empty($formatParams['productSubType'])) {
            $formatParams['productSubType'] = 0;
        }

        if (isset($queryParams['ordernums']) && is_array($queryParams['ordernums'])) {
            $queryParams['ordernums'] = array_unique($queryParams['ordernums']);
            if (count($queryParams['ordernums']) > 1) {
                $formatParams['extQuery'][] = [
                    'property' => 'ordernum',
                    'value' => array_values($queryParams['ordernums']),
                    'type' => 'IN'
                ];
            } else {
                $formatParams['productOrderId'] = $queryParams['ordernums'][0];
            }
        }

        if (isset($queryParams['members']) && is_array($queryParams['members'])) {
            $queryParams['members'] = array_unique($queryParams['members']);
            if (count($queryParams['members']) > 1) {
                throw new Exception('暂不支持多会员查询');
            }
            if ($queryParams['members']) {
                $formatParams['memberId'] = intval($queryParams['members'][0]);
            }
        }

        if (isset($queryParams['identity_photo']) && !empty($queryParams['identity_photo'])) {
            $formatParams['extQuery'][] = [
                'property' => 'exists_identity_photo',
                'value' => format('int', $queryParams['identity_photo']),
                'type' => 'EQ'
            ];
            //身份证照片选了是或否都要传顶级供应商ID：apply_did
            $formatParams['extQuery'][] = [
                'property' => 'apply_did',
                'value'    => format('int', $queryParams['sid'] ?? 0),
                'type'     => 'EQ'
            ];
        }

        // beginOrderTime、endOrderTime 为必填字段，如果没传，则自动传入
        if (!isset($formatParams['beginOrderTime'])) {
            // 13个月前的1号
            $formatParams['beginOrderTime'] = date('Y-m-01 00:00:00', strtotime('-13 month'));
        }
        if (!isset($formatParams['endOrderTime'])) {
            $formatParams['endOrderTime'] = date('Y-m-d 23:59:59', time());
        }

        if (isset($queryParams['mobileSubject'])) {
            $formatParams['mobileSubject'] = $queryParams['mobileSubject'];
        }

        if (isset($queryParams['mobileQuery'])) {
            $formatParams['mobileQuery'] = $queryParams['mobileQuery'];
        }

        return [
            'param' => $formatParams,
            'scrollKey' => $queryParams['scrollKey'] ?? '',
            'page' => isset($queryParams['page']) ? intval($queryParams['page']) : 1,
            'size' => isset($queryParams['size']) ? intval($queryParams['size']) : 10
        ];
    }
}
