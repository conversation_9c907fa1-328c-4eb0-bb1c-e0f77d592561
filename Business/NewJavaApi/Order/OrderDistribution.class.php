<?php
/**
 * 旅游券订单分销链信息查询
 * <AUTHOR>  Li
 * @date  2022-03-10
 */
namespace Business\NewJavaApi\Order;

use \Business\NewJavaApi\CallBase;
use Business\NewJavaApi\Order\ProductOrder\AdminProductOrder;

class OrderDistribution extends CallBase
{

    public function __construct()
    {
        $tmpList = load_config('order_center', 'newJavaApiUrl');
        $apiInfo = $tmpList['order'];

        if (!$apiInfo) {
            return false;
        }
        parent::__construct($apiInfo, 'order', 2);
    }

    /**
     * 批量查询新订单中心订单信息
     * @params array $orderArr [[orderNum, orderProductType]]
     * @return array
     */
    public function batchAggregationIds($orderArr)
    {
        $javaParamArr = [];

        foreach ($orderArr as $item) {
            list($orderNum, $productType) = $item;

            $javaParamArr[] = [
                'productType' => $productType,
                'productOrderId' => $orderNum
            ];
        }

        return $this->call('aggregationIds', $javaParamArr);
    }

    /**
     * 批量查询订单分销商信息
     * <AUTHOR>  Li
     * @date 2022-03-10
     *
     * @param  array $orderNumArr 订单号数组
     *
     * @return array
     */
    public function batchQueryDistribution(array $orderNumArr)
    {
        if (!$orderNumArr) {
            return $this->returnData(203, '参数错误');
        }

        //请求参数
        $javaParamArr = $this->_handleStrArr($orderNumArr);

        //发起请求
        return $this->call('batchQueryDistribution', $javaParamArr);
    }

    /**
     * 通过订单号获取产品类型
     * <AUTHOR>  Li
     * @date 2022-05-16
     *
     * @param  array $orderNumArr 订单号数组
     *
     * @return array
     * @deprecated
     */
    public function getPtypeByOrderNumArr(array $orderNumArr)
    {
        // if (!$orderNumArr) {
        //     return $this->returnData(203, '参数错误');
        // }

        // //请求参数
        // $javaParamArr = $this->_handleStrArr($orderNumArr);

        // //发起请求
        // return $this->call('complete', $javaParamArr);
        
        // 废弃之前的接口调用逻辑，迁移为新接口，适配以前的字段
        try {
            $orderList = (new AdminProductOrder())->batchBasicInfo(AdminProductOrder::MEMBER_ID, $orderNumArr);
        } catch (\Exception $e) {
            return $this->returnData(500, $e->getMessage());
        }
        $res = [];
        foreach ($orderList as $order) {
            $res[] = [
                'orderId' => $order['productOrderId'],
                'itemType' => $order['itemType'],
                'status' => $order['orderStatus'],
                // 映射不上，估计也没业务在用，不做处理
                // 'validTime' => $order['businessInfo']['firstDtime'],
                // 'invalidTime' => $order['invalidTime'],
                'ticketId' => $order['itemSku'],
                'landId' => $order['itemSpu'],
            ];
        }
        return $this->returnData(200, 'success', $res);
    }
}