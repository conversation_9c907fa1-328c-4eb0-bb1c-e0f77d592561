<?php
/**
 * Created by PhpStorm.
 * User: linchen
 * Date: 2022/01/13
 * Time: 15:06
 */

namespace Business\Pay;

use Bean\Request\Pay\MiniJsApiPayRequestBean;
use Bean\Request\Pay\OrderPayCommonRequestBean;
use Bean\Request\Pay\PayNotifyRequestBean;
use Bean\Request\Pay\QrPayRequestBean;
use Bean\Response\Pay\MicroPayResponseBean;
use Business\Base;
use Business\JsonRpcApi\PayService\CommonHandle;
use Business\JsonRpcApi\PayService\UnifiedPay;
use Business\JsonRpcApi\PayService\UnifiedQuery;
use Business\JsonRpcApi\ScenicLocalService\LeaseDeposit;
use Business\Order\MergeOrder;
use Library\Cache\Cache;
use Library\Cache\CacheRedis;
use Library\Constants\OnlineTradeStatus;
use Library\Util\DebugUtil;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\TradeRecord\OnlineTrade;
use Process\Order\Validate\MergeOrderPreCheck\OrderPreCheckException;
use Bean\Request\Pay\SettleNotifyContentRequestBean;

class UnifiedPayment extends Base
{
    private $appkey = '775481e2556e4564985f5439a5e6a27g';
    //支付中心回调通知到Service项目地址 回复非SUCCESS会多次通知 直到成功或达到最大通知次数
    private $callBackUrl = PAY_DOMAIN.'r/pay_CommonPayNotify/payNotify';
    private $settleCallbackUrl = PAY_DOMAIN.'r/pay_CommonPayNotify/sellteNotify';

    public $jsonRpcClient = null;

    /**
     * 统一付款码支付收款业务
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $microPayBean \Bean\Request\Pay\MicroPayRequestBean 付款码请求bean
     *
     * @return mixed
     */
    public function unifiedMicroPay($microPayBean)
    {
        //根据不同渠道进行参数处理校验
        $checkPayBaseRes = $this->microPayBaseCheck($microPayBean);
        if ($checkPayBaseRes['code'] != 200) {
            return $checkPayBaseRes;
        }
        //支付前业务处理 (预留方法)
        $this->microPayBeforeHandle();
        //todo 支付日志金额计算等业务处理
        $bizRes = $this->microPayBizHandle($microPayBean);
        if ($bizRes['code'] != 200) {
            return $bizRes;
        }
        $bizData = $bizRes['data'];

        //调用支付收银台业务处理
        $requestRes = $this->microPayRequestHandle($microPayBean, $bizData);
        if ($requestRes['code'] != 200) {
            return $requestRes;
        }
        $responseData = $requestRes['data'];
        //支付成功后的业务处理
        $payRes = $this->microPayAfterHandle($microPayBean, $responseData, $bizData);
        if (!is_null($this->jsonRpcClient)) {
            $this->jsonRpcClient->call('Order/PaymentLock/unlock', [$microPayBean->getOrderNum()], 'scenic');
        }
        if ($payRes['code'] != 200) {  //内部修改支付失败了
            return $payRes;
        }
        $payResultData = $payRes['data'];
        //统一返回或者特殊处理的返回处理
        return $this->microPayReturnHandle($microPayBean->getNeedVerify(), $payResultData);
    }

    /**
     * 统一付款码支付收款业务
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $microPayBean \Bean\Request\Pay\MicroPayRequestBean 付款码请求bean
     *
     * @return mixed
     */
    public function microPayBaseCheck($microPayBean)
    {
        //todo 有特殊的判断可以获取$microPayBean->getChannelId();
        $codeLength = strlen($microPayBean->getAuthCode());
        if ($codeLength < 16 || $codeLength > 24 || !is_numeric($microPayBean->getAuthCode())) {
            return self::returnData(204, "非法的支付条码[{$microPayBean->getAuthCode()}]，请重新刷新支付码");
        }
        return self::returnData(200, 'success');
    }

    public function microPayBeforeHandle()
    {

    }

    /**
     * 统一付款码支付收款业务
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $microPayBean \Bean\Request\Pay\MicroPayRequestBean 付款码请求bean
     * @param $checkBaseData array 基础校验后数据
     *
     * @return array
     */
    public function microPayBizHandle($microPayBean)
    {
        $mergePayFlag = false;
        $logIdList    = [];
        $paymentObj   = new \Business\Order\MergeOrder();
        if ($paymentObj->isCombineOrder($microPayBean->getOrderNum())) {
            $mergePayFlag = true;
            //合并付款支付日志创建
            $rs = $paymentObj->handlerCombineTradeLog($microPayBean->getOrderNum(), $microPayBean->getSubject(),$microPayBean->getMerchantId(),
                $microPayBean->getPayBiz());
            if ($rs['code'] != 200) {
                return self::returnData(204, $rs['msg']);
            }
            $logIdList = $rs['data'] ?: [];
        }
        $timeOrderDeposit = 0;
        $leaseOrderDeposit = 0;
        //获取下金额
        $orderQuery = new OrderQuery('localhost');
        $totalFee   = $orderQuery->get_order_total_fee($microPayBean->getOrderNum());
        if ($microPayBean->getPayBiz()) {
            $payBaseBiz       = new PayBase();
            $payOrderOtherRes = $payBaseBiz->orderPayOtherBizHandle($microPayBean->getOrderNum(),
                $microPayBean->getPayBiz());
            if ($payOrderOtherRes['code'] == 200) {
                $timeOrderDeposit = $payOrderOtherRes['data']['timeOrderDeposit'];
                $leaseOrderDeposit = $payOrderOtherRes['data']['leaseOrderDeposit'];
                if ($timeOrderDeposit > 0) {
                    $microPayBean->setSubject($microPayBean->getSubject() . '/订单押金');
                }
                if ($leaseOrderDeposit > 0) {
                    $microPayBean->setSubject($microPayBean->getSubject() . '[租赁业务]');
                }
            }
            $totalFee += ($timeOrderDeposit + $leaseOrderDeposit);
        }
        if (!$totalFee) {
            return self::returnData(204, '金额有误(请检查门票价格是否为0元，0元票无需支付，'.$microPayBean->getOrderNum().':'.(int)$totalFee.')');
        }

        //插入支付日志
        $onlineTrade = new OnlineTrade();
        $logId      = $onlineTrade->addLogPayCenter(
            $microPayBean->getOrderNum(), $totalFee / 100, $microPayBean->getSubject(), $microPayBean->getSubject(),
            OnlineTrade::PAY_METHOD_ORDER, $microPayBean->getMerchantId()
        );
        if (!$logId) {
            return self::returnData(204, '支付失败,生成交易记录失败001');
        }
        $logIdList[] = $logId;

        $returnData = [
            'totalFee'         => $totalFee,
            'timeOrderDeposit' => $timeOrderDeposit,
            'leaseOrderDeposit' => $leaseOrderDeposit,
            'mergePayFlag'     => $mergePayFlag,
            'tradeIdList'      => $logIdList,
        ];

        return self::returnData(200, 'success', $returnData);
    }

    /**
     * 统一付款码支付收款业务
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $microPayBean \Bean\Request\Pay\MicroPayRequestBean 付款码请求bean
     *
     * @return array
     */
    public function microPayRequestHandle($microPayBean, $bizData)
    {
        $unifiedPayRpcApi = new UnifiedPay();
        //请求到支付中心
        $requestRes       = $unifiedPayRpcApi->microPayRpcService(
            $microPayBean->getOrderNum(), $microPayBean->getAuthCode(), $bizData['totalFee'],
            $microPayBean->getSubject(), $microPayBean->getMerchantId(), $microPayBean->getPayType(),
            $microPayBean->getClientId(), $microPayBean->getRequestIp(), $this->settleCallbackUrl,
            $microPayBean->getTerminalType(), $microPayBean->getReqId() ?: ''
        );
        if ($requestRes['code'] == 200) {  //判断下是否需要查询一轮
            $returnData = $requestRes['data'];
            if ($returnData['trade_status'] == OnlineTradeStatus::TRADE_WAITING) {  //这边需要查询接口
                $unifiedQueryRpcApi = new UnifiedQuery();
                $maxTime            = 120;
                $begin              = 1;
                while (1) {
                    $queryRes = $unifiedQueryRpcApi->microPayQueryRpcService($microPayBean->getOrderNum(),
                        $returnData['pay_id'], $microPayBean->getMerchantId());
                    if ($queryRes['code'] != 200) {
                        return self::returnData($queryRes['code'], $queryRes['msg']);
                    } elseif ($queryRes['data']['status'] == OnlineTradeStatus::TRADE_SUCCESS) { //查询返回成功
                        $requestRes['data']['trade_no'] = $queryRes['data']['trade_no'];  //三方流水重新赋值
                        break;
                    } elseif ($queryRes['data']['status'] == OnlineTradeStatus::TRADE_FAIL) { //如果查询明确返回失败直接返回失败
                        return self::returnData(204, '请求支付接口交易返回失败');
                    }
                    if ($begin >= $maxTime) {
                        return self::returnData(204, '订单未支付');
                    }
                    $begin++;
                    $sleepTime = $begin < 30 ? 1 : 3;
                    sleep($sleepTime); //3秒一查
                }
            }
            elseif ($returnData['trade_status'] == OnlineTradeStatus::TRADE_SUCCESS) {
                return $requestRes;
            }
            elseif ($returnData['trade_status'] == OnlineTradeStatus::TRADE_FAIL) {  //交易失败直接返回失败
                return self::returnData(400, '请求支付接口交易失败');
            }
            else {
                return self::returnData(400, '支付状态返回异常');
            }
        }

        return $requestRes;
    }

    /**
     * 统一付款码支付后业务
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $microPayBean \Bean\Request\Pay\MicroPayRequestBean 付款码请求bean
     *
     * @return array
     */
    public function microPayAfterHandle($microPayBean, $responseData, $bizData)
    {
        $totalFee      = $bizData['totalFee'];
        $payToPft      = $responseData['pay_to_pft'] ?? true;
        $transactionId = $responseData['trade_no'];
        //$jsonSell      = json_encode($responseData['seller_info'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        //$jsonBuy       = json_encode($responseData['buyer_info'], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        $payId         = $responseData['pay_id'] ?? '';
        $payMode       = $responseData['pay_mode'] ?? '';
        $sourceToPaymode = load_config('sourceT_pay_mode_map');
        $sourceT       =  array_search($payMode,$sourceToPaymode);
        $logIdList     = $bizData['tradeIdList'] ?: [];

        if ($sourceT === false) {
            pft_log('pay/error', __METHOD__ . 'payMode=' . $payMode);
            return self::returnData(204, '订单支付成功,渠道配置错误,请联系客服');
        }

        $onlineTrade = new OnlineTrade();
        $saveRs = $onlineTrade->saveSourceT($logIdList,$sourceT,$payId);
        if (!$saveRs) {
            return self::returnData(204, '订单支付成功,保存配置错误,请联系客服');
        }

        $sourceToTrackSource = load_config('sourceT_track_source_map');
        $trackSource = $sourceToTrackSource[$sourceT] ?? 0;

        if ($bizData['timeOrderDeposit'] > 0) {
            $totalFee -= $bizData['timeOrderDeposit'];
        }
        if ($bizData['leaseOrderDeposit'] > 0) {
            $totalFee -= $bizData['leaseOrderDeposit'];
        }
        $options = [
            'buyer_info'          => '',
            'sell_info'           => '',
            'pay_channel'         => $trackSource,
            'pay_termianl'        => $microPayBean->getPayTerminal(),
            'oper'                => $microPayBean->getPayTrackOpId(),
            'pay_source'          => $microPayBean->getCheckSource(),
            'payId'               => $payId,//支付收银台订单号
            'enableProfitSharing' => $responseData['split_type'] ? true : false,//0不分账,1分账
            'subOpId'             => $microPayBean->getSubSid(),
            'leaseOrderDeposit'   => $bizData['leaseOrderDeposit'],//总支付押金(分)
            'payBiz'              => $microPayBean->getPayBiz(),//3-租赁押金业务
        ];
        $data    = [];
        $successOrders = [];
        if ($microPayBean->getPayBiz() == 3 && $totalFee == 0) {
            //0元订单按现金支付 认为都成功
            $paymentObj   = new \Business\Order\MergeOrder();
            if ($paymentObj->isCombineOrder($microPayBean->getOrderNum())) {
                $paymentModel = new \Model\Order\Payment();
                $orderList    = $paymentModel->getCombileLogByTradeId($microPayBean->getOrderNum());
                if (!empty($orderList)) {
                    $successOrders = array_column($orderList, 'ordernum');
                }
            } else {
                $successOrders = [$microPayBean->getOrderNum()];
            }
        } else {
            #/alidata/log/site/log_system/ddd/debug/
            DebugUtil::debug([__METHOD__, $microPayBean->getOrderNum(), $options, $totalFee]);
            $result  = \Library\Tools\Helpers::payComplete($microPayBean->getOrderNum(), $transactionId,
                $sourceT, $totalFee, (int)$payToPft, $options);
            if ($result['code'] != 200) {
                return $result;
            }
            $successOrders = $result['data']['success_orders'];
        }
        foreach ($successOrders as $item) {
            $data[] = [
                'code'     => 200,
                'msg'      => '支付成功',
                'order_id' => $item,
            ];
        }
        $checkData = [];
        if ($microPayBean->getNeedVerify()) {
            $paymentObj = new \Business\Order\MergeOrder();
            if ($bizData['mergePayFlag'] === true) {
                $verifyRes = $paymentObj->handleCombineOrderBuyVerifyByRpc($successOrders,
                    $microPayBean->getPayTerminal(), $microPayBean->getPayTrackOpId(), $microPayBean->getCheckSource(), true,
                    $microPayBean->getSubSid());
                if ($verifyRes) {
                    $verifyData = $verifyRes[0];  //默认就取第0个，因为手持机那边只能读取一个订单
                    $checkData  = $verifyData;
                }
            } else {
                $orderInfo      = $this->_getOrder($microPayBean->getOrderNum());
                $query          = new \Business\Order\Query();

                $checkData = $query->getOrderInfoForPrintByRpc($microPayBean->getOrderNum(), $orderInfo, $totalFee,
                    $payMode, $microPayBean->getTerminal(), $microPayBean->getCheckSource(),
                    $microPayBean->getPayTrackOpId(), $microPayBean->getSubSid());

            }
        }
        $payBaseBiz = new PayBase();
        $ext = [
            'leaseNo' => $microPayBean->getLeaseNo(),
            'sid' => $microPayBean->getMerchantId(),
            'payId' => $payId,
            'payMode' => $payMode,
            'authCode' => $microPayBean->getAuthCode(),//根据这个判断是什么支付方式
            'leaseOrderDeposit' => $bizData['leaseOrderDeposit'],//总支付押金(分)
        ];
        $payBaseBiz->afterOrderPayAction($microPayBean->getPayBiz(),$sourceT,$transactionId, (int)$payToPft,$successOrders, 0, $ext);

        $returnData = [
            'checkData' => $checkData,
            'data'      => $data,
        ];

        return self::returnData(200, 'success', $returnData);
    }

    /**
     * 统一付款码支付后返回处理
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $payResultData array 支付结果
     *
     * @return array
     */
    public function microPayReturnHandle($needVerify, $payResultData)
    {
        $microPayResponseBean = new MicroPayResponseBean($payResultData);
        if ($needVerify) {
            return self::returnData(200, 'success',
                $microPayResponseBean->getCheckData()->toArrayWithMapping(null,1));
        } else {
            return self::returnData(200, 'success',
                $microPayResponseBean->getData());
        }
    }

    /**
     * 获取订单信息
     * <AUTHOR>
     * @date   2018-04-11
     *
     * @param  string  $ordernum
     *
     * @return array
     */
    protected function _getOrder($ordernum)
    {
        $orderToolMdl = new OrderTools('localhost');
        $orderInfo    = $orderToolMdl->getOrderInfo(
            $ordernum,
            'member,ordername,ordertel,aid,code,lid,tid,tnum,tprice,ordermode,paymode,salerid,ss.pay_status,ss.status',
            'de.aids,de.series'
        );
        if ($orderInfo['aids']) {
            // 转分销获取第二级分销商
            $tmp                 = explode(',', $orderInfo['aids']);
            $orderInfo['member'] = $tmp[1];
        }

        return $orderInfo;
    }

    /**
     * 统一微信jsApi支付收款业务
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $jsApiPayRequestBean \Bean\Request\Pay\JsApiPayRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function unifiedJsApiPay($jsApiPayRequestBean)
    {
        $baseCheckRes = $this->jsApiBaseCheck($jsApiPayRequestBean);
        if ($baseCheckRes['code'] != 200) {
            return $baseCheckRes;
        }

        $bizRes    = $this->orderBizHandle($jsApiPayRequestBean);
        if ($bizRes['code'] != 200) {
            return $bizRes;
        }

        $payInfo    = $bizRes['data'];

        $requestRes = $this->jsApiRequestHandle($jsApiPayRequestBean, $payInfo);
        if ($requestRes['code'] != 200) {
            return $requestRes;
        }
        $resData  = $requestRes['data'];
        $afterRes = $this->orderAfterHandle($jsApiPayRequestBean);
        if ($afterRes['code'] != 200) {
            return $afterRes;
        }

        $savaRs = $this->saveSourceT($payInfo,$resData);
        if ($savaRs['code'] != 200) {
            return $savaRs;
        }

        return $this->jsApiResponseHandle($jsApiPayRequestBean, $resData);
    }

    /**
     * 统一微信jsApi支付基础校验
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $jsApiPayRequestBean \Bean\Request\Pay\JsApiPayRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function jsApiBaseCheck($jsApiPayRequestBean)
    {
        if (!$jsApiPayRequestBean->getOpenId()) {
            return self::returnData(204, '请用微信APP打开进行支付');
        }
        return self::returnData(200, 'success');
    }

    /**
     * 统一微信jsApi支付业务处理
     * @param $orderPayRequestBean OrderPayCommonRequestBean Jsapi请求bean
     *
     * @return array
     * @throws \Exception
     * <AUTHOR>
     * @date   2022-01-13
     *
     */
    public function orderBizHandle($orderPayRequestBean): array
    {
        $mergeOrder = new MergeOrder();
        $logIdList  = [];
        if ($mergeOrder->isCombineOrder($orderPayRequestBean->getOutTradeNo())) {
            $totalFee = $mergeOrder->getTradeIdTotleMoney($orderPayRequestBean->getOutTradeNo());
            $rs = $mergeOrder->handlerCombineTradeLog($orderPayRequestBean->getOutTradeNo(), '', $orderPayRequestBean->getMerchantId());
            if ($rs['code'] != 200) {
                return self::returnData(204, $rs['msg']);
            }
            $logIdList = $rs['data'] ?: [];
        } else {
            //获取订单总金额
            $OrderQeury = new OrderQuery('localhost');
            $totalFee   = $OrderQeury->get_order_total_fee((string)$orderPayRequestBean->getOutTradeNo());
        }
        $totalLeaseDeposit = 0;
        $leaseNo = '';
        //如果是租赁柜租赁业务 需要在这里加上租赁费用 rpc调用ScenicLocalService
        if ($orderPayRequestBean instanceof MiniJsApiPayRequestBean && $orderPayRequestBean->getBizMode() == 1) {
            $leaseNo = $orderPayRequestBean->getLeaseNo();
            $biz = new LeaseDeposit();
            $leaseRes = $biz->getUnpaidMachineOrderDeposit($orderPayRequestBean->getOutTradeNo());
            if (empty($leaseRes)) {
                return self::returnData(204, 'getUnpaidMachineOrderDeposit：未找到待支付的租赁订单');
            }
            foreach ($leaseRes as $lease) {
                $totalLeaseDeposit += $lease['total_lease_deposit'];
            }
            $totalFee += $totalLeaseDeposit;
            //更新租赁单的c端用户id
            if ($orderPayRequestBean->getMemberId() > 0) {
                $updateRes = $biz->updateLeaseOrderMember($orderPayRequestBean->getOutTradeNo(), $orderPayRequestBean->getMemberId());
                if (!$updateRes) {
                    pft_log('lease/miniAppPay', '更新租赁单的c端用户id失败: '.$orderPayRequestBean->getOutTradeNo().' mid='. $orderPayRequestBean->getMerchantId());
                }
            }
        }
        //存在订单金额为0 押金金额不为0的情况
        //还存在cmb订单里某个订单金额和押金都为0的情况 最好前端拦截下这种不要合并支付
        if (!$totalFee) {
            return self::returnData(204, '金额有误:请检查门票价格是否为0元，0元票无需支付');
        }
        //插入支付日志
        $onlineTrade = new OnlineTrade();
        $logId      = $onlineTrade->addLogPayCenter(
            $orderPayRequestBean->getOutTradeNo(), $totalFee / 100, '', '',
            OnlineTrade::PAY_METHOD_ORDER, $orderPayRequestBean->getMerchantId()
        );
        if (!$logId) {
            return self::returnData(204, '支付失败,生成交易记录失败002');
        }
        $logIdList[] = $logId;

        if ($orderPayRequestBean->getVerify()) {
            PayCache::setOrderVerfify($orderPayRequestBean->getOutTradeNo(), $orderPayRequestBean->getVerify());
        }
        $result = [
            'totalFee' => $totalFee,
            'leaseOrderDeposit' => $totalLeaseDeposit,
            'leaseNo' => $leaseNo,
            'tradeIdList'=> $logIdList,
        ];

        return self::returnData(200, 'success', $result);
    }

    /**
     * 统一微信jsApi支付业务请求
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $jsApiPayRequestBean \Bean\Request\Pay\JsApiPayRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function jsApiRequestHandle($jsApiPayRequestBean, $payInfo)
    {
        $unifiedPayRpc = new UnifiedPay();
        $response      = $unifiedPayRpc->jsApiRpcService($jsApiPayRequestBean->getOutTradeNo(),
            "订单支付[{$jsApiPayRequestBean->getOutTradeNo()}]",
            $payInfo['totalFee'], $jsApiPayRequestBean->getMerchantId(), $jsApiPayRequestBean->getPayType(),
            $jsApiPayRequestBean->getClientId(),$this->callBackUrl, get_client_ip(), $jsApiPayRequestBean->getAppid(),
            $jsApiPayRequestBean->getOpenId(), $this->settleCallbackUrl, $jsApiPayRequestBean->getFontUrl(),
            $jsApiPayRequestBean->getAttach());
        if ($response['code'] != 200) {
            return $response;
        }

        return $response;
    }

    /**
     * 统一微信jsApi支付业务请求
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $miniJsApiPayRequestBean \Bean\Request\Pay\MiniJsApiPayRequestBean  Jsapi请求bean
     *
     * @return mixed
     */
    public function miniJsApiRequestHandle($miniJsApiPayRequestBean, $payInfo)
    {
        $unifiedPayRpc = new UnifiedPay();
        $subject = "订单支付[{$miniJsApiPayRequestBean->getOutTradeNo()}]";
        $attach = '';
        if ($miniJsApiPayRequestBean->getBizMode() == 1) {
            $subject .= '[柜机租赁]';
            $attach = json_encode([
                'leaseOrderDeposit' => $payInfo['leaseOrderDeposit'],
                'bizMode' => $miniJsApiPayRequestBean->getBizMode(),
                'leaseNo' => $payInfo['leaseNo'],
                'sid' => $miniJsApiPayRequestBean->getMerchantId(),
                'mid' => $miniJsApiPayRequestBean->getMemberId(),
                'exp' => $miniJsApiPayRequestBean->getOrderExpire(),
            ], JSON_UNESCAPED_UNICODE);
        } elseif ($miniJsApiPayRequestBean->getBizMode() == 2) {
            $subject .= '[柜机售卖]';
            $attach = json_encode([
                'bizMode' => $miniJsApiPayRequestBean->getBizMode(),
                'sid' => $miniJsApiPayRequestBean->getMerchantId(),
                'mid' => $miniJsApiPayRequestBean->getMemberId(),
                'exp' => $miniJsApiPayRequestBean->getOrderExpire(),
            ], JSON_UNESCAPED_UNICODE);
        }
        $miniJsApiPayRequestBean->setAttach($attach);
        return $unifiedPayRpc->miniJsApiRpcService(
            $miniJsApiPayRequestBean->getOutTradeNo(),
            $subject,
            $payInfo['totalFee'],
            $miniJsApiPayRequestBean->getMerchantId(),
            $miniJsApiPayRequestBean->getPayType(),
            $miniJsApiPayRequestBean->getClientId(),
            $this->callBackUrl,
            get_client_ip(),
            $miniJsApiPayRequestBean->getAppletSubAppid(),
            $miniJsApiPayRequestBean->getOpenId(),
            $this->settleCallbackUrl,
            $miniJsApiPayRequestBean->getAlipayProviderId(),
            $miniJsApiPayRequestBean->getAttach(),
            $miniJsApiPayRequestBean->getOrderExpire()
        );
    }

    /**
     * 统一微信jsApi支付业务请求
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $orderPayRequestBean \Bean\Request\Pay\OrderPayCommonRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function orderAfterHandle($orderPayRequestBean)
    {
        if ($orderPayRequestBean->getFontUrl()){
            (new CommonHandle())->setRedirectUrlRpc($orderPayRequestBean->getOutTradeNo(), $orderPayRequestBean->getFontUrl());
        }
        return self::returnData(200, 'success');
    }

    /**
     * 统一微信jsApi支付业务返回
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $jsApiPayRequestBean \Bean\Request\Pay\JsApiPayRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function jsApiResponseHandle($jsApiPayRequestBean, $resData)
    {
        $channelId = $resData['channelId'] ?? 0;
        $channelId = $channelId ?  $channelId : ($resData['channel_id'] ?? 0);

        switch ($channelId) {
            case 3:
                $return = ['pay_data' => $resData['payUrl'] ?? '', 'type' => 2];
                break;
            case 13:
                $resData['payData']['type'] = 1;
                $return = $resData['payData'] ?? [];
                break;
            case 14:
                $return = ['pay_data' => $resData['qr_url'] ?? '', 'type' => 2];
                break;
            case 15:// 工商银行
            case 18:// 安徽农商行
            case 19:// 绿聚
                $return = $resData['payData'];//, true);
                break;
            default:
                $return = ['pay_data' => $resData['payData'] ?? [],'type' => 1];
                break;
        }

        return self::returnData(200, 'success', $return);
    }

    /**
     * 统一微信jsApi支付业务返回
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $miniJsApiPayRequestBean \Bean\Request\Pay\MiniJsApiPayRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function miniJsApiResponseHandle($miniJsApiPayRequestBean, $resData)
    {
        if ( $miniJsApiPayRequestBean->getPayType() == 1){
            $returnData = [
                'outTradeNo' =>$miniJsApiPayRequestBean->getOutTradeNo(),
                'tradeNo'    => $resData['tradeNo'],
            ];
        } elseif ($miniJsApiPayRequestBean->getPayType() == 2 || $miniJsApiPayRequestBean->getPayType() == 3) {
            $returnData = $resData['payData'];
        }
        return ['code' => 200, 'data' => $returnData, 'status' => 'ok', 'msg' => 'success'];
    }

    /**
     * 统一回调通知处理
     * <AUTHOR>
     * @date   2022-01-13
     * @param $payNotifyRequestBean PayNotifyRequestBean
     *
     * @return mixed
     */
    public function unifiedNotify($payNotifyRequestBean){
        if (!$this->decryptNotifyData($payNotifyRequestBean->getContent()->toArrayWithMapping(null,1,true))){
            return self::returnData(204,'签名错误');
        }
        $bizContentDto = $payNotifyRequestBean->getContent()->getBizContent();
        //微信点金计划
        if ($bizContentDto->getBankOrderNo()){
            $payServiceApi = new CommonHandle();
            $redirectRes   = $payServiceApi->getRedirectUrlRpc($bizContentDto->getOutTradeNo());
            if ($redirectRes['code'] == 200){
                $payServiceApi->setRedirectUrlRpc($bizContentDto->getBankOrderNo(), $redirectRes['data']['redirect_url']);
            }
        }
        $payMode = $bizContentDto->getPayMode();
        $sourceToPaymode = load_config('sourceT_pay_mode_map');
        $sourceT       =  array_search($payMode,$sourceToPaymode);
        if ($sourceT === false) {
            pft_log('unifiedNotify/error', '渠道配置错误,请联系客服：payMode=' . $payMode);
            return self::returnData(204, '渠道配置错误,请联系客服');
        }
        $sourceToTrackSource = load_config('sourceT_track_source_map');
        $trackSource = $sourceToTrackSource[$sourceT] ?? 0;
        $redis     = Cache::getInstance('redis');
        //$leaseBoxFlag: 1-从租赁柜租借 2-从租赁柜购买
        $leaseBoxFlag = $redis->get("wanju_pay_with_biz:{$bizContentDto->getOutTradeNo()}");
        $rentBusinessFlag = $leaseBoxFlag == 1;
        $leaseOrderDeposit = 0;//租金
        $leaseNo = '';//租赁单号
        $sid = 0;
        $attachArr = [];
        if ($rentBusinessFlag) {
            //租赁业务
            $attachArr = json_decode($bizContentDto->getAttach(), true);
            if (json_last_error() == JSON_ERROR_NONE) {
                $leaseOrderDeposit = $attachArr['leaseOrderDeposit'] ?? null;
                $sid = $attachArr['sid'] ?? 0;
                $leaseNo = $attachArr['leaseNo'] ?? '';
                if (!is_numeric($leaseOrderDeposit) || empty($leaseNo) || empty($sid)) {
                    pft_log('leaseAppletPay/error', '小程序支付回调获取租金异常1:' . $bizContentDto->getAttach());
                }
            } else {
                pft_log('leaseAppletPay/error', '小程序支付回调获取租金异常2:' . $bizContentDto->getAttach());
            }
        }
        $options = [
            'buyer_info'           => '',
            'sell_info'            => '',
            'pay_channel'          => $trackSource,
            'pay_termianl'         => 0,
            'payId'                => $bizContentDto->getPayId(),//支付收银台订单号
            'enableProfitSharing'  => (bool)$bizContentDto->getSplitType(),//0不分账,1分账
            'leaseOrderDeposit' => $leaseOrderDeposit,//总支付押金(分) 由于是异步通知 支付时候把押金金额写入到了attach中 这里从attach中取
            'payBiz' => $rentBusinessFlag ? 3 : 0,//0-无业务 3-租赁押金业务 这里和microPay传递的保持一致
        ];
        //调用payComplete方法传递的金额要减掉押金,不减掉账本接口金额会报不一致错误 参考$this->microPayAfterHandle里的实现
        $orderFee = $bizContentDto->getAmount();
        if ($leaseOrderDeposit) {
            $orderFee -= $leaseOrderDeposit;
        }
        $paymentObj   = new \Business\Order\MergeOrder();
        //对于租赁业务 如果中台订单金额为0 则不调用payComplete方法
        $successOrders = [];
        if ($rentBusinessFlag && $orderFee == 0) {
            //0元订单按现金支付 认为都成功
            if ($paymentObj->isCombineOrder($bizContentDto->getOutTradeNo())) {
                $paymentModel = new \Model\Order\Payment();
                $orderList    = $paymentModel->getCombileLogByTradeId($bizContentDto->getOutTradeNo());
                if (!empty($orderList)) {
                    $successOrders = array_column($orderList, 'ordernum');
                }
            } else {
                $successOrders = [$bizContentDto->getOutTradeNo()];
            }
            $result = [
                'code' => 200,
                'data' => ['success_orders' => $successOrders],
            ];
        } else {
            $result = \Library\Tools\Helpers::payComplete($bizContentDto->getOutTradeNo(), $bizContentDto->getTradeNo(),
                $sourceT, $orderFee, (int)$bizContentDto->getPay2pft(), $options);
        }
        if ($result['code'] == 200) {
            $successOrders = $result['data']['success_orders'];
            // 购即验逻辑
            $needVerify = PayCache::getOrderVerfify($bizContentDto->getOutTradeNo());
            if ($needVerify) {
                $paymentObj    = new \Business\Order\MergeOrder();
                $paymentObj->handleCombineOrderBuyVerify($successOrders, 0);
            }
        }
        if ($result['code'] == 200 || $result['code'] == 102 || $result['code'] == 101) {
            $wanjuFlag = $redis->get("wepay_wanju:{$bizContentDto->getOutTradeNo()}");
            if ( $wanjuFlag ) {
                $post2wanju = [
                    'code'=>200,
                    'data'=>[
                        'out_trade_no'  => $bizContentDto->getOutTradeNo(),
                        'success_orders'=> $result['data']['success_orders'],
                        'total_fee'     => $orderFee,
                        'trade_no'      => $bizContentDto->getTradeNo(),
                        'trade_time'    => date('Y-m-d H:i:s'),
                        'origin_trade_no' => $bizContentDto->getOriginTradeNo(),
                    ],
                    'msg'=>'success'
                ];
                $wanJuUrl = ENV == "PRODUCTION" ? "https://wanju.12301.cc/api/callback/pay-notify":"https://wanju.12301dev.com/api/callback/pay-notify";
                $content = \Library\Tools\Helpers::curl_post_json($wanJuUrl, json_encode($post2wanju, JSON_UNESCAPED_UNICODE));
                pft_log('wanjunotify', json_encode([$post2wanju, $wanJuUrl, $content], JSON_UNESCAPED_UNICODE));
            }
            //发起通知 如果返回失败 会重复通知 注意数据处理的幂等
            $biz = new LeaseDeposit();
            if ($leaseBoxFlag == 1) {
                //租赁柜购买业务后续处理
                $notifyData = [
                    'trade_id'  => $bizContentDto->getOutTradeNo(),
                    'success_orders' => $result['data']['success_orders'],
                    'total_fee' => $orderFee,
                    'pay_id' => $bizContentDto->getPayId(),
                    'trade_no' => $bizContentDto->getTradeNo(),
                    'origin_trade_no' => $bizContentDto->getOriginTradeNo(),
                    'attach' => $bizContentDto->getAttach(),
                    'leaseOrderDeposit' => $leaseOrderDeposit,//总支付押金(分)
                ];
                $notifyRes = $biz->notifyBoxOrderHasPaid($notifyData);
                DebugUtil::debug([
                    'leaseBox1NotifyData' => $notifyData,
                    'leaseBox1NotifyRes' => $notifyRes,
                ]);
                //租赁业务后续处理 参考$this->microPayAfterHandle里的实现
                $payBaseBiz = new PayBase();
                $ext = [
                    'leaseNo' => $leaseNo,
                    'sid' => $sid,
                    'mid' => $attachArr['mid'] ?: 0,
                    'payId' => $bizContentDto->getPayId(),
                    'payMode' => $payMode,
                    'leaseOrderDeposit' => $leaseOrderDeposit,//总支付押金(分)
                ];
                $payBaseBiz->afterOrderPayAction($options['payBiz'], $sourceT, $bizContentDto->getTradeNo(), (int)$bizContentDto->getPay2pft(), $successOrders, 0, $ext);
                //这里不做通知失败重试 afterOrderPayAction里面不幂等
                //return self::returnData($notifyRes['code'], $notifyRes['msg'], $notifyRes['data']);
            } elseif ($leaseBoxFlag == 2) {
                //租赁柜购买业务后续处理
                $notifyData = [
                    'trade_id'  => $bizContentDto->getOutTradeNo(),
                    'success_orders' => $result['data']['success_orders'],
                    'total_fee' => $orderFee,
                    'pay_id' => $bizContentDto->getPayId(),
                    'trade_no' => $bizContentDto->getTradeNo(),
                    'origin_trade_no' => $bizContentDto->getOriginTradeNo(),
                    'attach' => $bizContentDto->getAttach(),
                ];
                $notifyRes = $biz->notifyBoxOrderHasPaid($notifyData);
                DebugUtil::debug([
                    'leaseBox2NotifyData' => $notifyData,
                    'leaseBox2NotifyRes' => $notifyRes,
                ]);
                return self::returnData($notifyRes['code'], $notifyRes['msg'], $notifyRes['data']);
            }
            return self::returnData(200,'处理成功');
        } else {
            return self::returnData(204,$result['msg']);
        }
    }

    /**
     * 统一微信jsApi支付收款业务
     * @param $miniJsApiPayRequestBean MiniJsApiPayRequestBean Jsapi请求bean
     * @return mixed
     * <AUTHOR>
     * @date   2022-01-13
     *
     */
    public function unifiedMiniJsApiPay($miniJsApiPayRequestBean)
    {
        // check lock
        /**
         * @var $lock CacheRedis
         */
        $lock = Cache::getInstance('redis');
        $key = 'minijspay:'.$miniJsApiPayRequestBean->getOutTradeNo();
        if (!$lock->lock($key, $miniJsApiPayRequestBean->getOpenId(), 15)) {
            $openId = $lock->lock_get($key);
            if ($openId!=$miniJsApiPayRequestBean->getOpenId()) {
                return ['code'=>400, 'msg'=>'订单支付中，请勿重复支付或请等待 15 秒后重试'];
            }
        }
        try {
            $bizRes = $this->orderBizHandle($miniJsApiPayRequestBean);
            if ($bizRes['code'] != 200) {
                return $bizRes;
            }
            $payInfo    = $bizRes['data'];
            $requestRes = $this->miniJsApiRequestHandle($miniJsApiPayRequestBean, $payInfo);
            if ($requestRes['code'] != 200) {
                return $requestRes;
            }
            //支付成功的后续处理 参见
            $resData  = $requestRes['data'];

            $savaRs = $this->saveSourceT($payInfo,$resData);
            if ($savaRs['code'] != 200) {
                return $savaRs;
            }
            return $this->miniJsApiResponseHandle($miniJsApiPayRequestBean, $resData);
        } catch (\Throwable $e) {
            return ['code'=>400, 'msg'=> $e->getMessage()];
        }
    }


    /**
     * User: lanwanhui
     * Date: 2022/8/12
     *
     * @param array $payInfo      支付信息
     * @param array $responseData 支付返回信息
     *
     * @return array
     */
    public function saveSourceT($payInfo, $responseData)
    {
        $payId           = $responseData['pay_id'] ?? '';
        $payMode         = $responseData['pay_mode'] ?? '';
        $sourceToPaymode = load_config('sourceT_pay_mode_map');
        $sourceT         = array_search($payMode,$sourceToPaymode);
        $logIdList       = $payInfo['tradeIdList'] ?: [];

        if ($sourceT === false) {
            return self::returnData(204, '渠道配置错误,请联系客服'.$payMode);
        }
        $onlineTrade = new OnlineTrade();
        $saveRs = $onlineTrade->saveSourceT($logIdList,$sourceT,$payId);
        if (!$saveRs) {
            return self::returnData(204, '保存配置错误,请联系客服');
        }
        return self::returnData(200);
    }

    /**
     *  解密 sha256加密
     * @author: Guangpeng Chen
     * @date: 2020/11/12
     *
     * @param $params array 待加密的数据
     *
     * @return bool
     */
    public function decryptNotifyData(array $params)
    {
        $signature = $params['signature'];
        unset($params['signature']);
        $sign       = bin2hex(hash('sha256', json_encode(asort($params['biz_content'],2),JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES), true));
        $signStr    = $params['appId'] . $params['timestamp'] . $params['nonce'] . $sign;
        $signResult = base64_encode(hash_hmac('sha256', $signStr, $this->appkey, true));
        if ($signature == $signResult) {
            return true;
        } else {
            return false;
        }
    }
    /**
     * 统一扫码支付处理
     * <AUTHOR>
     * @date   2022-01-13
     * @param $qrPayRequestBean QrPayRequestBean
     *
     * @return mixed
     */
    public function unifiedQrPay($qrPayRequestBean){
        $baseCheckRes = $this->qrBaseCheck($qrPayRequestBean);
        if ($baseCheckRes['code'] != 200){
            return $baseCheckRes;
        }

        $bizRes = $this->orderBizHandle($qrPayRequestBean);
        if ($bizRes['code'] != 200) {
            return $bizRes;
        }
        $payInfo    = $bizRes['data'];
        $requestRes = $this->QrRequestHandle($qrPayRequestBean,$payInfo);
        if ($requestRes['code'] != 200) {
            return $requestRes;
        }

        $resData  = $requestRes['data'];
        $afterRes = $this->orderAfterHandle($qrPayRequestBean);
        if ($afterRes['code'] != 200) {
            return $afterRes;
        }

        $savaRs = $this->saveSourceT($payInfo,$resData);
        if ($savaRs['code'] != 200) {
            return $savaRs;
        }

        $returnData = [
            'outTradeNo' => $qrPayRequestBean->getOutTradeNo(),
            'qrUrl' => $resData['qr_url'],
        ];
        return self::returnData(200,'success',$returnData);
    }

    /**
     * 统一扫码支付处理
     * <AUTHOR>
     * @date   2022-01-13
     * @param $appPayRequestBean \Bean\Request\Pay\AppPayRequestBean
     *
     * @return mixed
     */
    public function unifiedAppPay($appPayRequestBean){

        $bizRes = $this->orderBizHandle($appPayRequestBean);
        if ($bizRes['code'] != 200) {
            return $bizRes;
        }
        $payInfo    = $bizRes['data'];
        $requestRes = $this->AppRequestHandle($appPayRequestBean,$payInfo);
        if ($requestRes['code'] != 200) {
            return $requestRes;
        }
        $resData  = $requestRes['data'];
        $afterRes = $this->orderAfterHandle($appPayRequestBean);
        if ($afterRes['code'] != 200) {
            return $afterRes;
        }

        $savaRs = $this->saveSourceT($payInfo,$resData);
        if ($savaRs['code'] != 200) {
            return $savaRs;
        }

        if ($appPayRequestBean->getPayType() == 1) {
            $returnData = [
                'outTradeNo' => $appPayRequestBean->getOutTradeNo(),
                'alipay_url' => $resData['alipay_url'],
            ];
            return ['code' => 200, 'data' => $returnData,'msg' => 'success'];
        } elseif ($appPayRequestBean->getPayType() == 2) {
            $returnData = [
                'parameter' => $resData['parameter'],
                'outTradeNo'=> $appPayRequestBean->getOutTradeNo(),
            ];
            return ['code' => 200, 'data' => $returnData, 'status' => 'ok', 'msg' => 'success'];
        }
    }

    /**
     * 统一微信jsApi支付基础校验
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $qrPayRequestBean QrPayRequestBean
     *
     * @return mixed
     */
    public function qrBaseCheck($qrPayRequestBean)
    {
        return self::returnData(200, 'success');
    }
    /**
     * 统一微信jsApi支付业务请求
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $qrPayRequestBean \Bean\Request\Pay\QrPayRequestBean Jsapi请求bean
     *
     * @return mixed
     */
    public function QrRequestHandle($qrPayRequestBean, $payInfo)
    {
        $unifiedPayRpc = new UnifiedPay();
        $response      = $unifiedPayRpc->qrRpcService($qrPayRequestBean->getOutTradeNo(),
            $qrPayRequestBean->getSubject(), $payInfo['totalFee'], $qrPayRequestBean->getMerchantId(),
            $qrPayRequestBean->getPayType(),  $qrPayRequestBean->getClientId(),$this->callBackUrl, get_client_ip(),
            $this->settleCallbackUrl, $qrPayRequestBean->getIsQr(), $qrPayRequestBean->getAppid());
        if ($response['code'] != 200) {
            return $response;
        }

        return $response;
    }

    /**
     * 统一微信jsApi支付业务请求
     * <AUTHOR>
     * @date   2022-01-13
     *
     * @param $qrPayRequestBean \Bean\Request\Pay\AppPayRequestBean
     *
     * @return mixed
     */
    public function AppRequestHandle($appPayRequestBean,$payInfo)
    {

        $unifiedPayRpc = new UnifiedPay();
        $response      = $unifiedPayRpc->appRpcService($appPayRequestBean->getOutTradeNo(),
            $appPayRequestBean->getSubject(),
            $payInfo['totalFee'], $appPayRequestBean->getMerchantId(), $appPayRequestBean->getPayType(),
            $appPayRequestBean->getClientId(),$this->callBackUrl, get_client_ip(), $this->settleCallbackUrl);
        if ($response['code'] != 200) {
            return $response;
        }

        return $response;
    }


    /**
     * 清算回调处理
     * <AUTHOR>
     * @date   2022-6-8
     * @param SettleNotifyContentRequestBean $settleNotifyRequestBean
     *
     * @return mixed
     */
    public function settleNotify(SettleNotifyContentRequestBean $settleNotifyRequestBean){

        if (empty($settleNotifyRequestBean->getAppId()) || empty($settleNotifyRequestBean->getSignature()) || empty($settleNotifyRequestBean->getNonce())) {
            return self::returnData(204,'数据为空');
        }

        if (!$this->decryptNotifyData($settleNotifyRequestBean->toArrayWithMapping(null,1,true))){
            return self::returnData(204,'签名错误');
        }

        if ($settleNotifyRequestBean->getBizContent()->getStatus() != 'SUCCESS') {
            return self::returnData(204,'清算状态错误');
        }

        if (empty($settleNotifyRequestBean->getBizContent()->getOutTradeNo())) {
            return self::returnData(204,'订单号不存在');
        }

        if (empty($settleNotifyRequestBean->getBizContent()->getPayId())) {
            return self::returnData(204,'收银台订单号不存在');
        }

        if ($settleNotifyRequestBean->getBizContent()->getSplitType() != 1) {
            return self::returnData(200,'无需分账的订单不需要处理清算');
        }

        $mergeOrder = new MergeOrder();
        $isCombine  = $mergeOrder->isCombineOrder($settleNotifyRequestBean->getBizContent()->getOutTradeNo());
        if ($isCombine) {
            $paymentModel = new \Model\Order\Payment();
            $orderList    = $paymentModel->getCombileLogByTradeId($settleNotifyRequestBean->getBizContent()->getOutTradeNo());
        } else {
            $orderList =  [$settleNotifyRequestBean->getBizContent()->getOutTradeNo()];
        }

        if (empty($orderList)) {
            return self::returnData(204,'订单列表为空');
        }

        $data = [
            'orderList'    => $orderList,
            'splitType'    => $settleNotifyRequestBean->getBizContent()->getSplitType(),
            'payId'        => $settleNotifyRequestBean->getBizContent()->getPayId(),
        ];

        //todo 调用java接口

        return self::returnData(200,'处理成功');
    }
}