<?php

namespace Business\Pay;

use Business\Base;

use Business\CommodityCenter\Ticket;
use Business\JsonRpcApi\ScenicLocalService\RedisHandle;
use Business\TeamOrder\TeamOrderReport;
use Library\Constants\Api\HttpTryCode;
use Library\Constants\FastCheckConst;
use Library\Constants\Team\TeamConst;
use Library\MessageNotify\OrderNotify;
use Library\Util\DebugUtil;
use Library\Util\Team\TeamUtil;
use Model\CardSolution\TimingOrderDeposit;
use Model\Order\SubOrderQuery;
use Model\TradeRecord\OnlineTrade;
use Model\Order\OrderQuery;
use Model\Order\OrderHandler;
use Model\Order\OrderTools;
use Model\Order\TeamOrderSearch;

use Library\Resque\Queue;
use Library\Cache\Cache;
use Library\Tools\Helpers;

use Business\Order\MergeOrder;
use Business\Order\TeamOrder;
use Business\JavaApi\Order\Pay as PayApi;

use Pimple\Container;
use Business\Order\ProductService\ServiceObject;

class PayComplete extends Base
{

    private $_sourceToPaymode;

    private $_sourceToPaytype;

    private $_offlinePay;

    //切换到新版的接口的sourceT
    private $_sourceToNew = [
        0, //原生支付宝
        1, //原始微信
        20, //易宝
        22, // 农行
        23, // 建行
        18,  //银联商务pos通
        17,//银联商务公众号
        19, //威富通
        21, //云闪付
        30, //抖音支付
        31,//招行
        32,//百度
        35,//中国银行
        36,//新版易宝:独立收款
        41,//新版易宝:平台收款
    ];

    //切换到新版的接口的sourceT
    private $_sourceToNewSMS = [
        0, //原生支付宝
        1, //原始微信
        20, //易宝
        22, // 农行
        23, // 建行
        18,  //银联商务pos通
        17,//银联商务公众号
        19, //威富通
        21, //云闪付
        30, //抖音支付
        31,//招行
        32,//百度
        35,//中国银行
        36,//新版易宝:独立收款
        41,//新版易宝:平台收款
        24,//丰收互联支付
        25,//江西农商行
        26,//银联会员卡
        27,//盛世通支付
//        28,//玩聚
        29,//青海银行
        37,//工商银行
        38, //预付卡付款
        39,//农行(云BMP)
        40,//安徽农商行
        42,//绿聚支付
        43,//福建农信
        44,//聚富通
        45,//农行创识
        46,//通联支付
        47,//富友支付
        48,//酷享支付
        49,//自贡银行
        50,//慧徕店支付
        51,//星沙农商
        52,//网联商务
        53,//金华银行
        54,//悦航支付
        55,//拉卡拉支付
        57,//TLinx
        68,//通联支付
        69,//小红书-担保支付
        70,//西安银行
        71,//富滇银行
        72,//浙江农信
    ];
    private $refundTimes     = 0;

    private $container;

    //接入收银台的下单渠道(跳过交易记录表的相关逻辑操作)
    private $_cashRegisterOrderMode = [
        56,//玩聚
    ];

    public function __construct()
    {
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        $this->container = new Container();
        //redis实例
        $this->container['redis'] = function () {
            return Cache::getInstance('redis');
        };
        //在线交易model
        $this->container['trade_model'] = function () {
            return new OnlineTrade();
        };
        // 订单查询model
        $this->container['order_query_model'] = function () {
            return new OrderQuery('localhost');
        };
        $this->container['merge_biz']         = function () {
            return new MergeOrder();
        };
        $this->container['team_biz']          = function () {
            return new TeamOrder();
        };

        $this->_sourceToPaymode = load_config('sourceT_pay_mode_map');
        $this->_sourceToPaytype = load_config('sourceT_pay_type_map');

        //线下支付渠道
        //42-玩聚 44-玩聚-抖音担保交易支付 47-线下手工补单=现金
        $this->_offlinePay = [
            9, 10, 11, 12, 33,
            36, 42, 44, 47,
            //76~85这10个是自定义线下支付方式预留
            76, 77, 78, 79, 80,
            81, 82, 83, 84, 85, 87,
        ];
    }

    /**
     * 兼容open下的 Change_Order_Pay 方法(后续接口不要调用)
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  int  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  int  $totalFee  支付总金额
     * @param  string  $sellInfo  卖家信息
     * @param  string  $buyerInfo  买家信息
     * @param  integer  $payStatus  支付状态(都是1)
     * @param  boolean  $payToPft  是否票付通收款
     * @param  integer  $payChannel  支付来源,订单追踪表的source字段
     * @param  string  $payOrderNum  原始订单号(已废弃)
     * @param  integer  $payTermianl  支付的终端号，终端购票汇总使用
     * @param  integer  $payTrackOpId  支付的操作人
     * @param  array   $extData 扩展信息 ['subOpId' => '子商户操作人']
     *
     * @return int|bool
     * <AUTHOR>
     */
    public function adjustChangeOrderPay(
        $ordernum,
        $tradeNo,
        $sourceT,
        $totalFee,
        $sellInfo = '',
        $buyerInfo = '',
        $payStatus = 1,
        $payToPft = true,
        $payChannel = 0,
        $payOrderNum = '',
        $payTermianl = 0,
        $payTrackOpId = 0,
        $extData = []
    )
    {
        //请求日志
        //@pft_log('order/pay', 'request:' . json_encode(func_get_args()));

        $requestId = md5(uniqid(mt_rand(), true));
        $reqParams = [
            'requestId' => $requestId,
            'req'       => func_get_args(),
        ];
        pft_log('product_line_paycallback_log', json_encode($reqParams, JSON_UNESCAPED_UNICODE), 3);

        $options = [
            'buyer_info'   => $buyerInfo,
            'sell_info'    => $sellInfo,
            'pay_channel'  => $payChannel,
            'pay_termianl' => $payTermianl,
            'oper'         => $payTrackOpId,
        ];

        if (isset($extData['subOpId'])) {
            $options['subOpId'] = $extData['subOpId'];
        }

        $result = $this->_payAction($ordernum, $tradeNo, $sourceT, $totalFee, (int)$payToPft, $options);

        //特定错误,在线退款
        if ($result['code'] == 102) {
            $failFlag           = $result['data']['is_trade_fail'] ?? 0;
            $options['err_msg'] = $result['msg'];
            $this->_onlineRefund($ordernum, $tradeNo, $sourceT, $payToPft, $totalFee, $failFlag, $options,
                $result['code']);
        }

        //结果日志
        //@pft_log('order/pay', 'result:' . json_encode($result, JSON_UNESCAPED_UNICODE));

        $resParams = [
            'requestId' => $requestId,
            'res'       => $result,
        ];
        pft_log('product_line_paycallback_log', json_encode($resParams, JSON_UNESCAPED_UNICODE), 3);

        if ($result['code'] != 204) {
            return $result['code'] == 200 ? 100 : $result['code'];
        } else {
            return false;
        }
    }

    /**
     * 支付完成后的动作（更新订单状态等等）
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  integer  $verify  是否立即验证
     * @param  array  $options  支付选项 ['buyer_info' => '买家信息',
     *                                     'sell_info' => '卖家信息',
     *                                     'pay_channel' => '支付来源,订单追踪表的source字段',
     *                                     'pay_termianl' => '支付的终端号',
     *                                     'oper' => '操作人id',
     *                                     'verify' => '是否进行验证',
     *                                     'ignore_alipay_rec' => '忽略校验alipay_rec',
     *                                     'subOpId' => '子商户操作人id'
     *                                    ]
     *
     * @return array
     * <AUTHOR>
     */
    public function payComplete(string $ordernum, string $tradeNo, int $sourceT, int $totalFee, $payToPft = 1, array $options = [])
    {
        //避免重复回调
        $locky = "lock:pay:{$ordernum}_{$sourceT}";

        if (!$this->container['redis']->lock($locky, 1, 120)) {
            return $this->returnData(5005, '订单正在处理中，请稍后再试');
        }

        //是否是合并付款
        $isCombine = $this->container['merge_biz']->isCombineOrder($ordernum);

        if ($isCombine) {
            //合并付款订单
            $result = $this->_mergeOrderPay($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options);
        } else {
            //单个订单
            $result = $this->_singleOrderPay($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options, $ordernum);
        }

        //释放锁
        $this->container['redis']->rm($locky);

        return $result;
    }

    /**
     * 合并付款支付完成后的动作
     *
     * @param  string  $ordernum  合并订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  array  $options  支付选项 ['buyer_info' => '买家信息',
     *                                     'sell_info' => '卖家信息',
     *                                     'pay_channel' => '支付来源,订单追踪表的source字段',
     *                                     'pay_termianl' => '支付的终端号',
     *                                     'oper' => '操作人id',
     *                                     'subOpId' => '子商户操作人id'
     *                                    ]
     *
     * @return array
     * <AUTHOR>
     */
    private function _mergeOrderPay($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options)
    {
        $paymentModel = new \Model\Order\Payment();
        $orderList    = $paymentModel->getCombileLogByTradeId($ordernum);
        $orderList    = $orderList ?: [];

        $totalmoneyAll = 0;
        $successOrders = [];
        $failOrders    = [];
        $allOrderMoneyArr = [];

        $oneMarkOrdernum = '';
        foreach ($orderList as $item) {
            //单票订单号
            $oneOrderNum = (string)$item['ordernum'];

            //只是为了兼容之前的原路退回接口
            $oneMarkOrdernum = $oneOrderNum;

            //单笔总金额
            $totalmoney = $this->container['order_query_model']->get_order_total_fee($oneOrderNum);

            $allOrderMoneyArr[$oneOrderNum] = $totalmoney;

            //合并付款订单总金额
            $totalmoneyAll += $totalmoney;
        }

        //判断实际支付金额和订单上面的实时金额是否相同
        $totalFee      = intval($totalFee);
        $totalmoneyAll = intval($totalmoneyAll);
        if (bccomp($totalFee, $totalmoneyAll) != 0) {
            //直接原路退回
            $failFlag           = 0;
            $options['err_msg'] = "合并付款支付失败：支付金额和订单实际金额不符001";

            $this->_mergeOnlineRefund($ordernum, $oneMarkOrdernum, $tradeNo, $sourceT, $payToPft, $totalFee, $failFlag, $options, 102,
                $ordernum);

            //临时添加一个支付金额不符的日志
            pft_log("PayComplete/totalFeeNotEqual",
                "订单号:{$ordernum},支付金额:{$totalFee},订单金额:{$totalmoneyAll}");

            return $this->returnData(204, '合并付款支付失败：支付金额和订单实际金额不符002');
        } else {
            foreach ($orderList as $item) {
                //单票订单号
                $oneOrderNum = (string)$item['ordernum'];
                //单笔总金额
                $totalmoney = $allOrderMoneyArr[$oneOrderNum];
                $res = $this->_singleOrderPay($oneOrderNum, $tradeNo, $sourceT, $totalmoney, $payToPft, $options, $ordernum);

                if (in_array($res['code'], [101, 102, 5005])) {
                    //这边次数+1标识已经退了一次的  第二次需要延时5秒执行
                    $this->refundTimes = $this->refundTimes + 1;
                    // 已支付、已取消、处理中
                    continue;
                }

                if ($res['code'] == 200) {
                    $successOrders[] = $oneOrderNum;
                } else {
                    $failOrders[] = $oneOrderNum;
                }
            }

            if ($successOrders) {
                $paymentModel->changeCombinePayStatus($ordernum, $successOrders, 1);
            }

            if (count($failOrders)) {
                $paymentModel->changeCombinePayStatus($ordernum, $failOrders, 2);

                return $this->returnData(204, '合并付款支付失败:' . implode('|', $failOrders));
            } else {
                $this->_updateOnlineLog($ordernum, $tradeNo, $sourceT, $payToPft, 1, $options);
            }

            $data = [
                'total_fee'      => $totalmoneyAll,
                'success_orders' => $successOrders,
            ];

            return $this->returnData(200, '', $data);
        }
    }

    /**
     * 支付完成后的动作
     *
     * @param  string  $ordernum  订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  array  $options  支付选项 ['buyer_info' => '买家信息',
     *                                     'sell_info' => '卖家信息',
     *                                     'pay_channel' => '支付来源,订单追踪表的source字段',
     *                                     'pay_termianl' => '支付的终端号',
     *                                     'ignore_alipay_rec' => '忽略校验alipay_rec'.
     *                                     'oper' => '操作人id',
     *                                     'subOpId' => '子商户操作人id']
     *
     * @return array
     * <AUTHOR>
     */
    private function _singleOrderPay($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options, $originPayNum = '')
    {
        $requestId = md5(uniqid(mt_rand(), true));
        $reqParams = [
            'requestId' => $requestId,
            'req'       => func_get_args(),
        ];
        pft_log('product_line_paycallback_log', json_encode($reqParams, JSON_UNESCAPED_UNICODE), 3);

        //请求参数
        $result = $this->_payAction($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options);

        $resParams = [
            'requestId' => $requestId,
            'res'       => $result,
        ];
        pft_log('product_line_paycallback_log', json_encode($resParams, JSON_UNESCAPED_UNICODE), 3);

        //特定错误,在线退款
        if ($result['code'] == 102) {
            $failFlag           = $result['data']['is_trade_fail'] ?? 0;
            $options['err_msg'] = $result['msg'];
            $this->_onlineRefund($ordernum, $tradeNo, $sourceT, $payToPft, $totalFee, $failFlag, $options, $result['code'], $originPayNum);
        }

        if ($result['code'] == 200) {
            //与合并付款保持格式一致
            $data = [
                'total_fee'      => $totalFee,
                'success_orders' => [$ordernum],
            ];

            return $this->returnData(200, '', $data);
        } elseif ($ordernum > ********) { // 微信和支付宝经常会推送几年前的支付订单过来，这里增加一个判断
            //发送钉钉预警
            helpers::sendDingTalkGroupRobotMessage($result['msg'], "在线支付失败", "订单号:{$ordernum}",
                \Library\Constants\DingTalkRobots::BANK_JOURNAL);

            return $result;
        }
    }

    /**
     * 支付完成后的动作（更新订单状态等等）
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  array  $options  支付选项 ['buyer_info' => '买家信息',
     *                                     'sell_info' => '卖家信息',
     *                                     'pay_channel' => '支付来源,订单追踪表的source字段',
     *                                     'pay_termianl' => '支付的终端号',
     *                                     'oper' => '操作人id',
     *                                     'ignore_alipay_rec' => '忽略校验alipay_rec',
     *                                     'subOpId' => '子商户操作人id'
     *                                    ]
     *
     * @return array
     * <AUTHOR>
     */
    public function _payAction($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options)
    {
        if (!$ordernum) {
            return $this->returnData(204, '订单号缺失');
        }

        //进行具体的产品线的处理调度
        return $this->_productLineDispatch($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options);
    }

    /**
     * 调用java支付接口（分润，支付记录，更改订单状态）
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  integer  $payMode  支付方式
     * @param  integer  $payType  支付账本
     * @param  array  $orderData  订单数据
     * @param  array  $options  支付选项
     *
     * <AUTHOR>
     */
    private function _orderPay($ordernum, $tradeNo, $totalFee, $payToPft, $payMode, $payType, $orderData, $options)
    {
        //支付渠道
        $payChannel = $options['pay_channel'] ?? 0;
        //操作人
        $oper = $options['oper'] ?? $orderData['mainOrder']['member'];

        $payOptions               = [];
        $payOptions['pftReceipt'] = $payToPft;
        $payOptions['source']     = $payChannel;
        if (isset($options['pay_termianl'])) {
            $payOptions['branchTerminal'] = $options['pay_termianl'];
        }

        if (isset($options['pay_source'])) {
            $payOptions['paySource'] = $options['pay_source'];
        }

        if (isset($options['payId'])) {
            $payOptions['payId']               = $options['payId'];
            $payOptions['enableProfitSharing'] = $options['enableProfitSharing'] ? true : false;
        }

        //增加子商户操作人
        if (isset($options['subOpId'])) {
            $payOptions['subOpId'] = intval($options['subOpId']);
        }

        $payOptions['tryBizCode'] = [HttpTryCode::JAVA_BIZ_ERROR];
        $orders                   = [['orderId' => $ordernum]];

        $subjectMap  = load_config('ptype_2_subject_code', 'trade_record');
        $subjectCode = $subjectMap[$payType];
        $payApi      = new PayApi();

        return $payApi->pay($orders, $tradeNo, $totalFee, $payMode, $subjectCode, $oper, date('Y-m-d H:i:s'),
            $payChannel, '', $payOptions);
    }

    /**
     * 数据校验
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  array  $options  支付额外信息
     *
     * @return array
     * <AUTHOR>
     */
    private function _validCheck($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options)
    {
        $payType = $this->_sourceToPaytype[$sourceT];
        $payMode = $this->_sourceToPaymode[$sourceT];

        //获取在线支付的发起记录
        $onlineLog = $this->container['trade_model']->getLog($ordernum, $sourceT);

        $insertOnlineLog = false;
        if (!$onlineLog && in_array($payMode, $this->_offlinePay)) {
            //是否需要重新插入pft_alipay_rec数据 - 拉卡拉支付之类
            $onlineLog       = [
                'status'    => 0,
                'total_fee' => $totalFee / 100,
            ];
            $insertOnlineLog = true;
        }

        //是否有退款记录（三方重复通知）
        $refundModel = new \Model\TradeRecord\OnlineRefund();
        $hasRefund   = $refundModel->getRefundJournalByOrderNum($ordernum);
        if ($hasRefund) {
            return $this->returnData(204, '订单已退款');
        }

        //获取订单信息
        $orderData = $this->container['order_query_model']->getSimpleOrderInfo($ordernum);
        if (!isset($orderData['mainOrder']['ordernum'])) {
            return $this->returnData(204,
                "[$ordernum]getSimpleOrderInfo返回空." . json_encode($orderData, JSON_UNESCAPED_UNICODE));
        }

        //接入收银台的下单渠道跳过
        $ignoreAlipayRec = $options['ignore_alipay_rec'] ?? false;
        if (!in_array($orderData['mainOrder']['ordermode'], $this->_cashRegisterOrderMode) && !$ignoreAlipayRec) {
            //支付已经完成
            if ($onlineLog['status'] == 1) {
                return $this->returnData(101, '支付已经完成');
            }

            if (!$onlineLog) {
                return $this->returnData(204, '支付记录不存在');
            }
        }

        //获取门票数据
        $mainTid         = $orderData['mainOrder']['tid'];
        $ticketCenter    = new \Business\CommodityCenter\Ticket();
        $mainProductInfo = $ticketCenter->queryTicketInfoById($mainTid, $tField = 'title', $pField = true,
            $lField = 'title,p_type',
            $lFField = true, $isGetField = false);

        if (!$mainProductInfo) {
            return $this->returnData(204, '商品记录不存在');
        }

        $mainTicketInfo = $mainProductInfo['ticket'] ?: [];
        $mainLandInfo   = $mainProductInfo['land'] ?: [];

        $orderData['mainOrder']['p_type'] = $mainLandInfo['p_type'];
        $orderData['mainOrder']['ltitle'] = $mainLandInfo['title'];
        $orderData['mainOrder']['ttitle'] = $mainTicketInfo['title'];
        //订单金额
        $orderTotalMoney = $orderData['mainOrder']['totalmoney'];
        $otherMoney = 0;
        if ($mainLandInfo['p_type'] == 'K') {
            //获取下押金
            $timingOrderDepositMdl = new TimingOrderDeposit();
            $timeOrderInfo         = $timingOrderDepositMdl->getTimeOrderDepositInfo($ordernum, 'money', 0);
            if ($timeOrderInfo) {
                $otherMoney      += $timeOrderInfo['money'];
                //$orderTotalMoney += $timeOrderInfo['money'];
            }
        }
        //租赁订单押金
        if (!empty($options['leaseOrderDeposit']) && $options['leaseOrderDeposit'] > 0) {
            $otherMoney += $options['leaseOrderDeposit'];
        }
        //接入收银台的下单渠道跳过
        if (!in_array($orderData['mainOrder']['ordermode'], $this->_cashRegisterOrderMode)) {
            //校验支付发起金额与实际支付金额
            $rightFee = $onlineLog['total_fee'] * 100;
            if (bccomp(($totalFee + $otherMoney), $rightFee) != 0 && $totalFee + $otherMoney < $rightFee) {
                $logMsg = "[$ordernum]支付金额不等。返回金额:{$totalFee},实际金额:{$rightFee} code=004";
                DebugUtil::info([
                    'tag' => 'oldPayCallbackHandle',
                    'method' => __METHOD__,
                    'options' => $options,
                    'totalFee' => $totalFee,
                    'otherMoney' => $otherMoney,
                    'onlineLog' => $onlineLog,
                    'logMsg' => $logMsg,
                ]);
                return $this->returnData(204, $logMsg);
            }
        }

        $productExt = json_decode($orderData['mainOrder']['product_ext'], true);
        if ($orderData['mainOrder']['p_type'] == 'J' && !empty($productExt['carriage'])) {
            $orderTotalMoney += intval($productExt['carriage']);
        }
        if (count($orderData['childOrder'])) {
            foreach ($orderData['childOrder'] as $item) {
                $orderTotalMoney += $item['totalmoney'];
            }
        }

        //订单实际金额与实际支付金额
        if (bccomp($totalFee, $orderTotalMoney) != 0) {
            //临时添加一个支付金额不符的日志
            pft_log("PayComplete/totalFeeNotEqual",
                "订单号:{$ordernum},支付金额:{$totalFee},订单金额:{$orderTotalMoney}");
            // 计时产品的逻辑特殊处理，不然手持机和云票务无法支付完成
            if ($mainLandInfo['p_type'] == 'K' ) {//&& $totalFee < $orderTotalMoney
                //$logMsg = "[$ordernum]支付金额错误。支付金额:{$totalFee},订单金额:{$orderTotalMoney}";
                //return $this->returnData(204, $logMsg);
            } else {
                //只要金额不一致的话就原路退回
                $logMsg = "[$ordernum]支付金额错误。支付金额:{$totalFee},订单金额:{$orderTotalMoney} code=005";
                return $this->returnData(204, $logMsg);
            }
        }

        if ($insertOnlineLog) {
            if (!in_array($orderData['mainOrder']['ordermode'], $this->_cashRegisterOrderMode)) {
                $this->_insertOnlineLog($orderData, $totalFee, $sourceT);
            } else {
                //接入收银台的下单渠道
                $this->_insertOnlineLog($orderData, $totalFee, $sourceT, $tradeNo);
            }
        }

        //订单是否取消或者已过期
        if ($orderData['mainOrder']['status'] == 2 || $orderData['mainOrder']['status'] == 3) {
            return $this->returnData(102, "[$ordernum]状态不对:{$orderData['mainOrder']['status']}",
                ['is_trade_fail' => 1]);
        }

        //0元现金订单
        $zeroCash = $orderData['mainOrder']['pmode'] == 9 && $orderData['mainOrder']['totalmoney'] == 0;

        //接入收银台的下单渠道跳过
        if (!in_array($orderData['mainOrder']['ordermode'], $this->_cashRegisterOrderMode)) {
            //是否重复支付
            if ($orderData['mainOrder']['paystatus'] != 2 && !$zeroCash) {
                $payedInfo = $this->container['trade_model']->getRepeatPayChannel($ordernum);
                if ($payedInfo['trade_no'] != '' && $payedInfo['sourceT'] != $sourceT) {
                    $logMsg = "订单重复支付自动退款, 订单号:{$ordernum},第一次支付渠道:{$payedInfo['sourceT']},第二次支付渠道:{$sourceT}";

                    return $this->returnData(102, $logMsg);
                }
            }
        }

        //0元订单和其他订单进行合并付款，选择在线支付会有问题
        if ($orderData['mainOrder']['totalmoney'] == 0 && $orderData['mainOrder']['paystatus'] == 1 && !$orderData['childOrder']) {
            return $this->returnData(200, '无须支付');
        }

        $data = [
            'order_data' => $orderData,
            'pay_type'   => $payType,
            'pay_mode'   => $payMode,
        ];

        return $this->returnData(200, '校验通过', $data);
    }

    /**
     * 支付完成后的一些动作
     *
     * @param  array  $orderData  订单信息
     * @param  string  $tradeNo  第三方订单号
     * @param  int  $sourceT  来源
     * @param  int  $payToPft  是否票付通收款
     * @param  array  $options  选项
     * @param  int  $payType  支付账本
     *
     * @return void
     * <AUTHOR>
     */
    private function _afterOnlinePayFinished($orderData, $tradeNo, $sourceT, $payToPft, $payMode, $options, $payType)
    {
        $mainOrder = $orderData['mainOrder'];
        $ordernum  = $mainOrder['ordernum'];

        //判断是否外部发码
        if (isset($mainOrder['product_ext']) && $mainOrder['product_ext']) {
            $extContent = json_decode($mainOrder['product_ext'], true) ?? [];
            if (isset($extContent['externalSendCode']) && $extContent['externalSendCode']) {
                //第三方发码的订单
                $externalCodeBiz = new \Business\ExternalCode\CodeManage();
                $externalCodeBiz->asyncPaySuccessTask($orderData);
            }
        }

        //计时卡押金状态处理
        if ($mainOrder['p_type'] == 'K' && isset($mainOrder['ext_content']) && $mainOrder['ext_content']) {
            $subjectMap  = load_config('ptype_2_subject_code', 'trade_record');
            $subjectCode = $subjectMap[$payType];
            $extContent  = json_decode($mainOrder['ext_content'], true) ?? [];
            if (isset($extContent['timeCardDeposit']) && $extContent['timeCardDeposit']) {
                $depositRes = (new \Business\JsonRpcApi\ScenicLocalService\TimeProduct())->updateTimeDeposit($ordernum,
                    $tradeNo, $subjectCode, $mainOrder['aid'], $mainOrder['aid'], $payToPft);
                if ($depositRes['code'] != 200) {
                    $logData = [
                        'ac'          => 'updateTimeDeposit',
                        'ordernum'    => $ordernum,
                        'tradeNo'     => $tradeNo,
                        'subjectCode' => $subjectCode,
                        'aid'         => $mainOrder['aid'],
                        'res'         => $depositRes,
                    ];
                    @pft_log('order/deposit', json_encode($logData, JSON_UNESCAPED_UNICODE));
                }
            }
        }

        //接入收银台的下单渠道跳过(抖音支付还是需要在这边更新下)
        if (!in_array($mainOrder['ordermode'], $this->_cashRegisterOrderMode) || $sourceT == 30) {
            //更新在线支付请求的记录
        }
        // 玩聚微信小程序没有接入收银台，这边的判断先放开。
        $this->_updateOnlineLog($ordernum, $tradeNo, $sourceT, $payToPft, 1, $options);

        //支付立即验证
        $this->_immediatelyVerify($orderData, $options);
        //有切换接口的支付渠道才执行以下的动作
        pft_log('notify/job', json_encode([$ordernum, $sourceT], JSON_UNESCAPED_UNICODE));
        if (in_array($sourceT, $this->_sourceToNewSMS)) {
            //发送短信
            $this->_sendSms($orderData);
        }
        if (in_array($sourceT, $this->_sourceToNew)) {
            //更新团单信息
            $this->_updateTeamOrderInfo($orderData, $payMode);
        }

        //支付方式
        $orderData['mainOrder']['real_paymode'] = $payMode;
        //第三方流水号
        $orderData['mainOrder']['third_pay_trade'] = $tradeNo;
        //支付成功后的一些列动作(异步)
        Queue::push('order', 'OnlinePayFinish_Job', $orderData);

        return true;
    }

    /**
     * 更新团单信息
     *
     * @param  array  $orderData  订单数据
     * @param  int  $payMode  支付方式
     *
     * @return bool
     * <AUTHOR>
     */
    private function _updateTeamOrderInfo($orderData, $payMode)
    {
        $mainOrder = $orderData['mainOrder'];
        $ordernum  = $mainOrder['ordernum'];

        if (!in_array($mainOrder['ordermode'],
            [TeamConst::_GROUP_RESERVATION_DISTRIBUTOR, TeamConst::_GROUP_RESERVATION_SUPPLER])) {
            return true;
        }

        $teamOrderModel = new TeamOrderSearch();
        $mainOrderInfo  = $teamOrderModel->getMainOrderInfoBySonOrder(strval($ordernum));
        if (empty($mainOrderInfo)) {
            return true;
        }
        $teamOrder = $mainOrderInfo['main_ordernum'];
        $sonOrder  = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrder);
        $orderArr  = array_column($sonOrder, 'son_ordernum');
        $orderTool = new OrderTools();
        $orderData = $orderTool->getOrderInfo($orderArr, 'status,pay_status,tnum');

        $needUpdatePay = true;
        if ($orderData) {
            foreach ($orderData as $value) {
                if ($value['pay_status'] != 1 && $value['status'] != 3) {
                    $needUpdatePay = false;
                }
            }
        }
        $payStatus = $needUpdatePay ? 1 : 2;
        //获取团单主单
        $teamOrderInfo = $teamOrderModel->getMainOrderInfoByOrderNum($teamOrder, 'fid,status,pay_status,ordertype');
        //更新团单最末级的支付方式
        $splitInfoRes = $teamOrderModel->updateLastSplitPayMode($ordernum, $teamOrderInfo['fid'], $payMode);
        if ($splitInfoRes === false) {
            TeamUtil::info([
                'tag' => '_updateTeamOrderInfo_payComplete',
                'msg' => '团单末级支付方式更新失败',
                'args' => func_get_args(),
                'err' => $teamOrderModel->getDbError(),
                'sql' => $teamOrderModel->getLastSql(),
            ]);
        }
        if ($teamOrderInfo['status'] != 1 || $teamOrderInfo['pay_status'] != $payStatus) {
            $data = [
                'updatetime' => time(),
                'status'     => 1,
                'pay_status' => $payStatus,
            ];
            $teamOrderModel->setTeamOrderByOrder($teamOrder, $data);
        }
        $action = TeamConst::_REAL_TASK_ADD_ORDER_;
        //获取支付的追踪记录
        $orderQueryModel = new SubOrderQuery();
        $trackInfo       = $orderQueryModel->getTrackInfoByOrder($ordernum, 'oper_member, source, ext_content', 1,
            'id desc', ['action' => 4]);
        $extContent      = empty($trackInfo['ext_content']) ? [] : json_decode($trackInfo['ext_content'], true);
        if ($extContent['pay_source'] == 4 || $trackInfo['source'] == 4) {
            $action = TeamConst::_REAL_TASK_ADD_ORDER_TERMINAL_;
        }
        $teamReportTask = new TeamOrderReport();
        $teamReportTask->addTeamRealTask($teamOrder, $ordernum, $mainOrder['tnum'], $action, $trackInfo['oper_member'],
            1);

        return true;
    }

    /**
     * 支付成功发送短信
     *
     * @param  array  $orderData  订单数据
     *
     * @return void
     * <AUTHOR>
     */
    private function _sendSms($orderData)
    {
        $main = $orderData['mainOrder'];

        $internationCall = OrderNotify::getInternationalCall($main['mobile_area'] ?? '', $main['ordertel'] ?? '');
        $args = [
            'ordernum' => $main['ordernum'],
            'buyerId'  => $main['member'],
            'mobile'   => $internationCall,
            'aid'      => $main['aid'],
        ];
        Queue::push('notify', 'OrderNotify_Job', $args);

        return true;
    }

    /**
     * 插入在线支付记录
     *
     * @param  array  $orderData  订单信息
     * @param  int  $totalFee  总金额
     * @param  int  $sourceT  来源
     *
     * @return void
     * <AUTHOR>
     */
    private function _insertOnlineLog($orderData, $totalFee, $sourceT, $tradeNo = '')
    {
        $subject     = $orderData['mainOrder']['ltitle'] . $orderData['mainOrder']['ttitle'];
        $description = $subject . '|非在线支付';
        $ordernum    = $orderData['mainOrder']['ordernum'];

        $merchantId = 0;
        //当下单渠道是玩聚，支付渠道是抖音担保的时候，需要存供应商ID，独立收款退款的时候有用到
        if ($orderData['mainOrder']['ordermode'] == 56 && $sourceT == 30) {
            $merchantId = $orderData['mainOrder']['aid'];
        }

        $this->container['trade_model']->addLog($ordernum, $totalFee / 100, $subject, $description, $sourceT, 0, '',
            $merchantId, $tradeNo);

        return true;
    }

    /**
     * 更新支付记录信息
     *
     * @param  string  $ordernum  订单号
     * @param  string  $tradeNo  第三方流水号
     * @param  int  $sourceT  来源
     * @param  int  $payToPft  是否票付通收款
     * @param  int  $status  状态 1 交易成功，2交易失败原路退回
     * @param  array  $options  额外信息
     *
     * @return void
     * <AUTHOR>
     */
    private function _updateOnlineLog($ordernum, $tradeNo, $sourceT, $payToPft, $status = 1, $options = [])
    {
        //卖家信息
        $sellInfo = $options['sell_info'] ?? '';
        //买家信息
        $buyerInfo = $options['buyer_info'] ?? '';

        if ($status == 1 && !empty($options['payId'])) {
            $payId = $options['payId'];
        } else {
            $payId = '';
        }

        $this->container['trade_model']->updateLog($ordernum, $tradeNo, $sourceT, $sellInfo, $buyerInfo, $status, '',
            $payToPft, $payId);

        return true;
    }

    /**
     * 支付失败，原路退款
     *
     * @param  string  $ordernum
     * @param  string  $tradeNo
     * @param  int  $sourceT
     * @param  int  $payToPft
     * @param  int  $totalFee
     * @param  array  $options
     *
     * @return void
     * <AUTHOR>
     */
    private function _onlineRefund($ordernum, $tradeNo, $sourceT, $payToPft, $totalFee, $failFlag = 0, $options = [], $payErrorCode = 0, $originPayNum = '')
    {
        //记录在线退款日志
        // pft_log('order/pay', json_encode([
        //     'key'      => '支付失败，原路退款',
        //     'ordernum' => $ordernum,
        //     'tradeNo'  => $tradeNo,
        //     'sourceT'  => $sourceT,
        //     'payToPft' => $payToPft,
        //     'totalFee' => $totalFee,
        //     'failFlag' => $failFlag,
        // ], JSON_UNESCAPED_UNICODE));

        $reqParams = [
            'key' => '支付失败，原路退款',
            'req' => func_get_args(),
        ];
        pft_log('product_line_paycallback_log', json_encode($reqParams, JSON_UNESCAPED_UNICODE), 3);

        $orderTool = new OrderTools();
        $orderInfo = $orderTool->getOrderInfo((string)$ordernum, 'ordernum,member,aid,tnum,status,tid,apply_did');
        $this->_updateOnlineLog($ordernum, $tradeNo, $sourceT, $payToPft, 2, $options);

        $commodityCenterTicketBiz = new Ticket();
        $ticketInfo               = $commodityCenterTicketBiz->queryTicketInfoById($ordernum);
        $pType                    = $ticketInfo['land']['p_type'];
        $paymode                  = $this->_sourceToPaymode[$sourceT];
        //TODO::生成原路退回的记录
        $refundModel = new \Model\TradeRecord\OnlineRefund();
        //退款类型 0 平台业务 1-计时一卡通业务 2-线下收款退款业务
        $refundBizType = 0;
        //是否是线下在线退款业务
        $isUnderLineOnlineRefund = false;
        if (in_array($paymode, load_config('underline_online_pay'))) {
            $isUnderLineOnlineRefund = true;
            $refundBizType           = 2;
        }
        $memo = "订单状态为【{$orderInfo['status']}】退款,原路退回" . ($failFlag ? ';内部交易失败退款' : '');

        if ($pType == 'K') {
            $timingOrderDepositMdl = new TimingOrderDeposit();
            $timeOrderDepositInfo  = $timingOrderDepositMdl->getTimeOrderDepositInfo($ordernum, '', 0);
            if ($timeOrderDepositInfo) {
                $totalFee += $timeOrderDepositInfo['money'];
                $memo     .= '+押金' . ($totalFee / 100);
            }
        }
        if (isset($options['payBiz']) && $options['payBiz'] == 3) {
            $leaseDeposit = $options['leaseOrderDeposit'] ?? 0;
            $totalFee += $leaseDeposit;
            $memo .= '+租赁押金' . ($leaseDeposit / 100);
        }
        //原始供应商扣款记录
        $logId = $refundModel->AddRefundLog($orderInfo['member'], $orderInfo['aid'], $ordernum, $orderInfo['tnum'],
            $totalFee, 0, $memo, 0, '', true, $sourceT, $refundBizType);

        //线下在线收款不走线上退款 sourT= 24 丰收互联 线下退款 回调地址  Api/Order/underLineRefundNotify
        if (in_array($paymode, load_config('online_pay_mode')) && !$isUnderLineOnlineRefund) {
            //TODO::原路退回操作，这里发送数据过去，不考虑处理结果。如果处理失败，将由财务手工处理
            if ($this->refundTimes < 1) {   //只是一次退款的
                $refundParam = [
                    'auth'          => md5(md5($ordernum) . md5(strrev($ordernum))),
                    'pay_mode'      => $paymode,
                    'ordernum'      => $ordernum,
                    'log_id'        => $logId,
                    'no_journal'    => 1,
                    'auto_status'   => 2,
                    'is_trade_fail' => $failFlag,
                    'pay_error_code' => $payErrorCode,
                    'origin_pay_num' => $originPayNum,
                ];
                curl_post(PAY_DOMAIN . "/r/OnlineRefund/index/", $refundParam, 80, 2);
            } else {
                $setDelayTime     = $this->refundTimes * 3;
                $delayTime        = "+{$setDelayTime} seconds";
                $jobData          = [
                    'ordernum'    => $ordernum,
                    'payMode'     => $paymode,
                    'autoStatus'  => 2,
                    'noJournal'   => 1,
                    'isTradeFail' => $failFlag,
                    'logId'       => $logId,
                    'payErrorCode' => $payErrorCode,
                    'originPayNum' => $originPayNum,
                ];
                $onlineRefundData = [
                    'job_type' => 'refund_online_request',
                    'job_data' => $jobData,
                ];
                Queue::delay(strtotime($delayTime), 'order', 'OnlineRefund_Job', $onlineRefundData);
            }

            //因为经常支付失败后，客户又重新在订单列表发起支付，导致第二次支付失败后的退款退不出去，这边直接给他取消掉
            $checkJobData = [
                'job_type' => 'online_pay_fail_refund',
                'job_data' => [
                    'ordernum' => $ordernum,
                    'opId'     => $orderInfo['apply_did'],
                    'remark'   => $options['err_msg'],
                ],
            ];
            Queue::push('order', 'Order_Job', $checkJobData);
        }
    }

    /**
     * 合并付款，支付失败，原路退款
     * 原先的原路退款只有单个订单的，临时添加一个合并付款的原路退款
     *
     * @param  string  $ordernum
     * @param  string  $tradeNo
     * @param  int  $sourceT
     * @param  int  $payToPft
     * @param  int  $totalFee
     * @param  array  $options
     *
     * @return array
     * <AUTHOR>
     * @date 2023/03/02
     */
    private function _mergeOnlineRefund($mergeOrdernum, $ordernum, $tradeNo, $sourceT, $payToPft, $totalFee, $failFlag = 0, $options = [], $payErrorCode = 0, $originPayNum = '')
    {
        $reqParams = [
            'key' => '合并付款：支付失败，原路退款',
            'req' => func_get_args(),
        ];
        pft_log('product_line_paycallback_log', json_encode($reqParams, JSON_UNESCAPED_UNICODE), 3);

        $orderTool = new OrderTools();
        $orderInfo = $orderTool->getOrderInfo((string)$ordernum, 'ordernum,member,aid,tnum,status,tid,apply_did');
        $this->_updateOnlineLog($mergeOrdernum, $tradeNo, $sourceT, $payToPft, 2, $options);

        $paymode     = $this->_sourceToPaymode[$sourceT];
        $refundModel = new \Model\TradeRecord\OnlineRefund();
        //退款类型 0 平台业务 1-计时一卡通业务 2-线下收款退款业务
        $refundBizType = 0;
        $memo          = "原路退回";

        //原始供应商扣款记录
        $logId = $refundModel->AddRefundLog($orderInfo['member'], $orderInfo['aid'], $mergeOrdernum, $orderInfo['tnum'],
            $totalFee, 0, $memo, 0, '', true, $sourceT, $refundBizType);

        $queryResArr = [];
        if (in_array($paymode, load_config('online_pay_mode'))) {
            $refundParam        = [
                'auth'           => md5(md5($mergeOrdernum) . md5(strrev($mergeOrdernum))),
                'pay_mode'       => $paymode,
                'ordernum'       => $mergeOrdernum,
                'log_id'         => $logId,
                'no_journal'     => 1,
                'auto_status'    => 2,
                'is_trade_fail'  => $failFlag,
                'pay_error_code' => $payErrorCode,
                'origin_pay_num' => $originPayNum,
            ];
            $queryRes           = curl_post(PAY_DOMAIN . "/r/OnlineRefund/index/", $refundParam, 80, 2);
            $queryResArr['res'] = $queryRes ? json_decode($queryRes, true) : null;
        }

        $reqParams = [
            'key'           => '合并付款：支付失败，原路退款',
            'mergeOrdernum' => $mergeOrdernum,
            'res'           => $queryResArr,
        ];
        pft_log('product_line_paycallback_log', json_encode($reqParams, JSON_UNESCAPED_UNICODE), 3);

        return $queryResArr;
    }

    /**
     * 立即验证
     *
     * @param  array  $orderData  订单数据
     * @param  array  $options  支付选项
     *
     * @return void
     * <AUTHOR>
     */
    private function _immediatelyVerify($orderData, $options)
    {
        $verify = $options['verify'] ?? false;
        if (!$verify) {
            return true;
        }

        $handerModel = new OrderHandler();

        $orders = [$orderData['mainOrder']];
        $orders = array_merge($orders, $orderData['childOrder']);

        //主票和子票一起验证
        foreach ($orders as $item) {
            // 投入延迟异步队列里面执行
            $res = $handerModel->CheckOrderSimply($item['ordernum'], 0, null, '', false, false, 0, 0, false, '', true,
                false, FastCheckConst::CHECK_TYPE_DELAY_ASYNC);
            pft_log('order/pay', "verify:{$item['ordernum']}" . json_encode($res));
        }

        return true;
    }

    /**
     * 产品线内部支付接口调度
     * @authoer dwer.cn
     * @date 2022/08/08
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $tradeNo  第三方订单号
     * @param  integer  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  integer  $totalFee  总金额
     * @param  integer  $payToPft  是否票付通收款
     * @param  array  $options  支付选项 ['buyer_info' => '买家信息',
     *                                     'sell_info' => '卖家信息',
     *                                     'pay_channel' => '支付来源,订单追踪表的source字段',
     *                                     'pay_termianl' => '支付的终端号',
     *                                     'oper' => '操作人id',
     *                                     'subOpId' => '子商户操作人',
     *                                    ]
     *
     * @return array
     */
    private function _productLineDispatch($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options)
    {
        //通过订单号获取产品/供应商等信息
        $lib      = new \Business\JavaApi\Order\Query\UusOrder();
        $queryRes = $lib->queryOrderInfoByOrdernum($ordernum);

        //服务对象
        $serviceObject = null;

        if ($queryRes['code'] == 200) {
            //获取到了订单数据
            $orderInfo = $queryRes['data'];

            $applyDid  = $orderInfo['apply_did'];
            $lid       = $orderInfo['lid'];
            $pType     = $orderInfo['product_type'];
            $ordermode = $orderInfo['ordermode'];
            $tid       = $orderInfo['tid'];
            $extParams = [
                'payBiz' => $options['payBiz']
            ];
            $serviceObject = ServiceObject::getPayCallbackServiceObject($pType, $sourceT, $lid, $applyDid, $ordermode, $extParams);
        }
        //手持购买的订单，$options中的pay_termianl为空的时候重新配置下
        if ($queryRes['data']['ordermode'] == 15 && empty($options['pay_termianl'])) {
            $scenicJsonApi           = new RedisHandle();
            $result                  = $scenicJsonApi->getShopCarTerminalCache($ordernum);
            $options['pay_termianl'] = $result['data'][0] ?? 0;
        }

        if ($serviceObject) {
            //走具体的产品线逻辑
            $payParamArr = [
                'ordernum' => $ordernum,
                'tradeNo'  => $tradeNo,
                'sourceT'  => $sourceT,
                'totalFee' => $totalFee,
                'payToPft' => $payToPft,
                'options'  => $options,
                'tid'      => $tid ?? 0,
                'applyDid' => $applyDid ?? 0,
            ];
            $res         = $serviceObject->payCallback($payParamArr);

            return $res;
        } else {
            //原先的逻辑
            return $this->_oldPayCallbackHandle($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options);
        }
    }

    /**
     * 原先的支付回调处理逻辑
     *
     * @param $ordernum
     * @param $tradeNo
     * @param $sourceT
     * @param $totalFee
     * @param $payToPft
     * @param $options
     *
     * @return array
     */
    private function _oldPayCallbackHandle($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options)
    {
        //合法性校验
        $checkRes = $this->_validCheck($ordernum, $tradeNo, $sourceT, $totalFee, $payToPft, $options);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        if (!$checkRes['data']) {
            //无须进行后续处理

            //之前上层的逻辑调整到这边
            //通知微商城全民营销订单
            $allDisBiz = new \Business\Cooperator\AllDis\AllDisOrder();
            $allDisRes = $allDisBiz->orderNotify($ordernum, 0);
            if ($allDisRes['code'] != 200) {
                pft_log("mall/allDis/orderNotify", json_encode($allDisRes));
            }

            return $this->returnData(200, '处理成功');
        }

        $checkData = $checkRes['data'];
        //实际支付方式
        $payMode = $checkData['pay_mode'];
        //支付账本
        $payType = $checkData['pay_type'];
        //订单信息
        $orderData = $checkData['order_data'];
        //调用java支付接口（分润，支付记录，更改订单状态，都由java处理）
        $tradeRes = $this->_orderPay($ordernum, $tradeNo, $totalFee, $payToPft, $payMode, $payType, $orderData,
            $options);
        if ($tradeRes['code'] != 200 && $tradeRes['code'] != 110) {
            return $this->returnData(102, "交易记录扣款异常:{$tradeRes['msg']}", ['is_trade_fail' => 1]);
        }

        //主线完成后的其他一系列动作
        $this->_afterOnlinePayFinished($orderData, $tradeNo, $sourceT, $payToPft, $payMode, $options, $payType);

        //之前上层的逻辑调整到这边
        //通知微商城全民营销订单
        $allDisBiz = new \Business\Cooperator\AllDis\AllDisOrder();
        $allDisRes = $allDisBiz->orderNotify($ordernum, 0);
        if ($allDisRes['code'] != 200) {
            pft_log("mall/allDis/orderNotify", json_encode($allDisRes));
        }

        return $this->returnData(200, '处理成功');
    }
}
