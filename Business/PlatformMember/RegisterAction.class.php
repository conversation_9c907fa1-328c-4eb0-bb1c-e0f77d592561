<?php

namespace Business\PlatformMember;

use Business\Base;
use Business\NewJavaApi\UserCenter\MemberDistributor;

/**
 * 平台用户注册
 */
class RegisterAction extends Base
{
    //角色类型
    private $_accountType;

    public function __construct() {
        //加载注册相关的配置
        $this->_accountType  = load_config('merchant_type', 'account');
    }

    /**
     * 注册分销商
     * <AUTHOR>
     * @date   2018-09-12
     * @param  \StdClass  $request 请求参数
     * @return array
     */
    public function registerDistributor(\StdClass $request) {
        //参数检测 rules: 注册请求参数验证规则， 字段名 => ['是否必须', '过滤/验证方法', 是否判断验证方法返回值, '默认值']
        $checkRes = $this->registerCheck($request, [
            //角色类型
            'type'        => ['required', 'intval'],
            //地址
            'address'     => ['', 'htmlentities', false, ''],
            //主营业务
            'business'    => ['', 'htmlentities', false, ''],
            //城市code
            'city'        => ['required', 'intval', false, 0],
            //别名
            'cname'       => ['required', 'htmlentities', false, ''],
            //公司类型
            'corpKind'    => ['required', 'intval', false, ''],
            //姓名
            'dname'       => ['required', 'htmlentities'],
            //业态
            'formatType'  => ['required', 'intval', false, 0],
            //账号、手机号、邮箱或第三方应用的唯一标识(与info_source参数对应)
            'mobile'      => ['required', 'htmlentities'],
            //密码是否经过md5(md5)加密
            'encrypt'     => ['', 'boolval', false, false],
            //密码
            'password'      => ['', 'strval', false, ''],
            //省份code
            'province'    => ['required', 'intval', false, 0],
            //类型/级别
            'types'        => ['required', 'intval', false, 0],
        ]);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        $request = $checkRes['data']['valid'];

        //注册数据
        $registerData = [
            'address' => (string)$request->address, //公司地址
            'business' => mb_substr($request->business, 0, 100), //经营范围
            'city' => (int)$request->city, //市
            'cname' => filter_utf8($request->cname), //个人/企业实名
            'corpKind' => (int)$request->corpKind, //公司类型
            'dname' => filter_utf8($request->dname), //账户名称
            'formatType' => (int)$request->formatType, //公司业态
            'mobile' => (string)$request->mobile, //手机号
            'province' => (int)$request->province, //省
            'encrypt' => (bool)$request->encrypt, //密码是否经过md5(md5)加密
            'password' => (string)$request->password, //密码
            'types' => (int)$request->types, //类型/级别
        ];

        $memberDistributorBiz = new MemberDistributor();
        $result = $memberDistributorBiz->register($registerData);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg']);
        }
        if (empty($result['data'])) {
            pft_log('register', json_encode([__METHOD__, $registerData, $result], JSON_UNESCAPED_UNICODE));
            return $this->returnData(500, '注册失败');
        }
        //后续处理下
        $this->registerAfter($request, $result['data']);

        return $this->returnData(200, '注册成功', $result['data']);
    }

    /**
     * 注册成功后操作
     *
     * @param \stdClass $request
     * @param array $result
     *
     * @return bool
     */
    protected function registerAfter($request, $result)
    {
        $dtype    = (int)$request->type;
        $memberId = $result['memberId'];

        //分销商默认开通合作审核 和 防骚扰
        if (in_array($dtype, [0, 1])) {
            $data     = [
                'distribution_check' => 1
            ];
            $memberBiz = new \Business\Member\Member();
            $res = $memberBiz->updateMemberExtInfo($memberId, $data);
            if (!$res) {
                pft_log('register/distribution_check/debug', "用户id: $memberId, 开启用户合作审失败");
            }

            $resPrivacy = (new \Business\Member\MemberPrivacy())->setNotHarassConfigByMemberId($memberId, 1, 1);
            if (!$resPrivacy) {
                pft_log('register/set_not_harass/debug', "用户id: $memberId, 开启用户防骚扰失败");
            }
        }

        return true;
    }

    /**
     * 注册参数检测
     * @param  object    $request 请求参数对象
     * @param  array     $fieldRules 字段检测规则
     * @return array
     */
    protected function registerCheck($request, $fieldRules = [])
    {
        //共有属性检测
        $commonCheckRes = $this->commonCheck($request, $fieldRules);
        if ($commonCheckRes['code'] != 200) {
            return $commonCheckRes;
        }
        $request = $commonCheckRes['data'];

        //根据来源渠道不同进行检测
        $sourceCheckRes = $this->sourceCheck($request);
        if ($sourceCheckRes['code'] != 200) {
            return $sourceCheckRes;
        }
        $request = $sourceCheckRes['data'];

        return $this->returnData(200, '', ['valid' => $request]);
    }

    /**
     * 共有属性检测
     * @param  object    $request 请求参数对象
     * @param  array     $fieldRules 字段检测规则
     * @return array
     */
    protected function commonCheck($request, $fieldRules = [])
    {
        foreach ($fieldRules as $field => $rule) {
            if (!isset($request->$field) && isset($rule[3])) {
                $request->$field = $rule[3];
            }
            if ($rule[0] == 'required') {
                if (!isset($request->$field) || $request->$field === '') {
                    return $this->returnData(204, "{$field}字段参数缺失");
                }
            }
            if ($request->$field && $rule[1] && function_exists($rule[1])) {
                $tmpRes = call_user_func($rule[1], $request->$field);
                if (isset($rule[2]) && $rule[2]) {
                    //检验返回值是否为true
                    if (!$tmpRes) {
                        return $this->returnData(204, "{$field}字段校验失败");
                    }
                } else {
                    $request->$field = $tmpRes;
                }
            }
        }

        return $this->returnData(200, '', $request);
    }

    protected function sourceCheck($request) {
        $accountTypeArr = array_keys($this->_accountType);
        if (!in_array($request->type, $accountTypeArr)) {
            return $this->returnData(204, '角色来源未定义');
        }
        return $this->returnData(200, '', $request);
    }
}