<?php

namespace Business\DistributionCenter;

use Business\AppCenter\Package;
use Business\Base;
use Business\NewJavaApi\DistributionCenter\groupDistributorQuery as DcGroupDistributorQueryApi;
use Business\Order\BatchOperate as BatchOperateBiz;
use Library\Cache\Cache;
use Library\Cache\CacheLock;
use Library\Cache\CacheReplication;
use Library\Constants\MemberConst;
use Library\Container;
use Library\Resque\Queue;
use Model\Order\BatchOperate as BatchOperateModel;
use Model\Ota\PftOtaSystem;

class DistributorOperation extends Base
{
    const CREATE_DISTRIBUTOR_QUERY_TASK = 'c:dis:renew:q:%d';
    const STORE_DISTRIBUTOR_QUERY_RESULT = 's:dis:renew:q:%d:%s';

    /**
     * 查询过期/即将过期分销商列表
     * @param $sid
     * @param $requestId
     * @param string $queryDname
     * @param string $queryAccount
     * @param int $pageNum
     * @param int $pageSize
     * @return array
     */
    public function queryDistributorResult($sid, $requestId, $queryDname = '', $queryAccount = '', $pageNum = 1, $pageSize = 10)
    {
        $returnData = ['count' => 0, 'list' => []];
        $cacheKey = sprintf(self::STORE_DISTRIBUTOR_QUERY_RESULT, $sid, $requestId);
        //把任务丢入到队列中去执行
        $redis = Container::pull(CacheReplication::class);
        if (!$redis->exists($cacheKey)) {
            if ($redis->exists('e:' . $cacheKey)) {
                return $this->returnDataV2(self::CODE_SUCCESS, $returnData);
            }
            return $this->returnDataV2(10011, [], '由于太久没有使用该页面，请求结果已失效，请返回上一级界面重新筛选');
        }
        // 计算翻页的start和end索引
        $start = max(($pageNum - 1) * $pageSize, 0);
        $end = $start + $pageSize - 1;
        $queryAccount = $queryAccount ? [$queryAccount] : [];
        if ($queryDname) {
            [$total, $list] = $this->queryValidDistributor($sid, [], $queryDname);
            if (!$total) {
                return $this->returnDataV2(self::CODE_SUCCESS, $returnData, '查询成功');
            }
            $_queryAccount = array_column($list, 'account');
            if ($queryAccount) {
                if (array_diff($queryAccount, $_queryAccount)) {
                    return $this->returnDataV2(self::CODE_SUCCESS, $returnData, '查询成功');
                }
                $queryAccount = array_intersect($queryAccount, $_queryAccount);
            } else {
                $queryAccount = $_queryAccount;
            }
        }
        if ($queryAccount) {
            $items = [];
            foreach ($queryAccount as $account) {
                $score = $redis->zScore($cacheKey, $account);
                if ($score !== false) {
                    $items[$account] = $redis->zScore($cacheKey, $account);
                }
            }
            if (!$items) {
                return $this->returnDataV2(self::CODE_SUCCESS, $returnData, '查询成功');
            }
            $returnData['count'] = count($items);
        } else {
            $returnData['count'] = $redis->zCard($cacheKey);
            //按照临近有效期天数递减
            $items = $redis->zRange($cacheKey, $start, $end, true);
        }
        //查询分销商账号信息并返回数据
        $accountList = array_keys($items);
        $api = new \Business\JavaApi\Member\MemberQuery();
        $res = $api->queryMemberByMemberQueryInfo(['accountList' => $accountList, 'dTypes' => [MemberConst::ROLE_DISTRIBUTOR]]);
        if ($res['code'] != 200 || !$res['data']) {
            return $this->returnDataV2(self::CODE_SUCCESS, $returnData, '查询成功');
        }
        foreach ($res['data'] as $item) {
            $memberInfo = $item['memberInfo'];
            $returnData['list'][] = [
                'account' => $memberInfo['account'],
                'dname' => $memberInfo['dname'],
                'expiring_days' => $items[$memberInfo['account']] ?? 0
            ];
        }
        return $this->returnDataV2(self::CODE_SUCCESS, $returnData, '查询成功');
    }

    /**
     * 批量续费分销商
     * @param $sid
     * @param $requestId
     * @param $endDate
     * @param $opId
     * @return array
     */
    public function createBatchDistributionRenewTask($sid, $requestId, $endDate, $opId)
    {
        //把任务丢入到队列中去执行
        $redis = Container::pull(CacheLock::class);
        $lockKey = "dis:op:batchDistributionRenew:{$sid}";
        if (!$redis->lock($lockKey, 1)) {
            return $this->returnDataV2(10012, [], '还有未完成续费操作，请等提交的请求完成后再提交');
        }
        try {
            return $this->createDistributionRenewTask($sid, $requestId, $endDate, $opId);
        } finally {
            $redis->unlock($lockKey);
        }
    }

    /**
     * 批量分销商续费
     * @param array $params
     * @return array
     */
    public function batchDistributorRenew(array $params)
    {
        if (!isset($params['serial_code']) || !isset($params['member_id'])) {
            return $this->returnData(203, '参数错误');
        }
        $serialCode = $params['serial_code'];
        $sid = $params['member_id'];
        $mainParams = json_decode($params['params'], true);
        $endTime = strtotime($mainParams['date']);
        $renewPackage = $this->getDistributorRenewPackageId();
        $openPackageId = $renewPackage[0] ?? 63;
        $currentTime = time();

        $api = new \Business\JavaApi\Member\MemberQuery();
        //查询供应商账号信息
        $res = $api->queryMemberByMemberQueryInfo(['idList' => [$sid]]);
        if ($res['code'] != 200 || !$res['data']) {
            return $this->returnData(203, '账号不存在');
        }
        $memberInfo = array_shift($res['data']);
        $memberInfo = $memberInfo['memberInfo'] ?? [];

        $model = new BatchOperateModel();
        $queryRes = $model->getRecordList(0, $serialCode, '', BatchOperateModel::RECORD_STATE_WAIT);

        while ($queryRes) {
            $record = array_shift($queryRes);
            $account = $record['ordernum'];
            $res = $api->queryMemberByMemberQueryInfo(['accountList' => [$account]]);
            if ($res['code'] != 200 || !$res['data']) {
                $model->updateRecord($record['id'], '', '', 0, ['state' => BatchOperateModel::RECORD_STATE_FAIL, 'err_remark' => '分销商不存在']);
                continue;
            }
            $distributorInfo = array_shift($res['data']);
            $distributorInfo = $distributorInfo['memberInfo'] ?? [];
            //创建分销商续费套餐
            $result = $this->distributorPackageRenew($distributorInfo, $openPackageId, $currentTime, $endTime, $memberInfo);
            if ($result['code'] != 200) {
                $model->updateRecord($record['id'], '', '', 0, ['state' => BatchOperateModel::RECORD_STATE_FAIL, 'err_remark' => $result['msg'] ?? '未知错误']);
                continue;
            }
            $model->updateRecord($record['id'], '', '', 0, ['state' => BatchOperateModel::RECORD_STATE_DONE]);
        }
        return $this->returnData(200, '操作成功');
    }

    protected function distributorPackageRenew($distributorInfo, $openPackageId, $currentTime, $endTime, $memberInfo) {
        if ($distributorInfo['dtype'] != 1) {
            return $this->returnDataV2(400, [], '不是有效的分销商账号');
        }
        $fid = $distributorInfo['id'];
        $lockKey = sprintf('dis:op:batchDistributionRenew:%d', intval($fid));
        $redis = Container::pull(CacheLock::class);
        if (!$redis->lock($lockKey, 1)) {
            return $this->returnDataV2(400, [], '还有未完成续费操作，请等提交的任务完成后再提交');
        }
        try {
            $packageBz = Container::pull(Package::class);
            //查询分销商套餐
            $queryRes = $packageBz->batchGetUserPackageList([$fid]);
            $queryRes['data'] = $queryRes['code'] == 200 ? $queryRes['data'] : [];
            $distributorPackageList = [];
            while ($queryRes['data']) {
                $item = array_shift($queryRes['data']);
                //取用户有效期最大的一段套餐
                if (!isset($distributorPackageList[$item['member_id']]) || $distributorPackageList[$item['member_id']]['end_time'] < $item['end_time']) {
                    $distributorPackageList[$item['member_id']] = $item;
                }
            }
            $distributorPackage = $distributorPackageList[$fid] ?? [];
            if ($distributorPackage) {
                if ($distributorPackage['end_time'] >= $endTime) {
                    return $this->returnDataV2(400, [], '不在支持续费的有效期范围内');
                }
                $beginTime = strtotime('+1 day', $distributorPackage['end_time']);
            } else {
                $beginTime = $currentTime;
            }
            $dateStr = date('Y年m月d日', $endTime);
            //分销商续费
            $signInfo = [
                'contract_num'      => 'PFT_ZEARO',
                'sign_date'         => date('Y-m-d', $currentTime),
                'receipt_money'     => 0,       //实收金额
                'agree_money'       => 0,              //协议金额
                'achievement_money' => 0,   //业绩金额
                'cmode'             => 1,   //合作模式 1=套餐
                'open_type'         => 3, //开通类型 3=免费开通
                'sub_open_type'     => 14, //开通子类型 14=赠送
                'records'           => [],
                'sales_id'          => 1, //管理员ID
                'remark'            => "由供应商代为续费，操作人：{$memberInfo['dname']}/{$memberInfo['account']}，批量操作开通到{$dateStr}",
            ];
            $res = $packageBz->openPackageByAdmin($openPackageId, $memberInfo['id'], $fid, MemberConst::ROLE_DISTRIBUTOR, $beginTime, $endTime, [], $signInfo);
            if ($res['code'] != 200) {
                return $this->returnDataV2(400, [], $res['msg'] ?? '未知错误');
            }
            $packageBz->handleAfterOpen($fid, $openPackageId, $res['data']['role'], $beginTime, $endTime, 0);
            return $this->returnDataV2(200, [], '操作成功');
        } finally {
            $redis->unlock($lockKey);
        }
    }

    /**
     * 创建分销商续费查询任务
     * @param $sid
     * @param $requestId
     * @param $endDate
     * @param $opId
     * @return array
     */
    protected function createDistributionRenewTask($sid, $requestId, $endDate, $opId)
    {
        $batchOperateModel = new BatchOperateModel();
        $workingTask = $batchOperateModel->getMainList('', 0, '', $sid, 0, 1,
            BatchOperateBiz::ACTION_TYPE_DISTRIBUTOR_RENEW, [], '', true);
        if ($workingTask) {
            return $this->returnDataV2(10012, [], '还有未完成续费操作，请等提交的请求完成后再提交');
        }
        $redis = Container::pull(CacheReplication::class);
        $cacheKey = sprintf(self::STORE_DISTRIBUTOR_QUERY_RESULT, $sid, $requestId);
        if (!$redis->exists($cacheKey)) {
            return $this->returnDataV2(10011, [], '由于太久没有使用该页面，请求结果已失效，请返回上一级界面重新筛选');
        }
        $accountList = $redis->zRange($cacheKey, 0, -1);
        $batchOperateBz = new BatchOperateBiz();
        $serialCode = $batchOperateBz->createSerialCode($sid);
        $total = count($accountList);

        $batchOperateModel->startTrans();
        try {
            //新增操作记录
            $mainRes = $batchOperateBz->createMain($serialCode, $sid, $opId, $total, BatchOperateBiz::ACTION_TYPE_DISTRIBUTOR_RENEW, ['date' => $endDate]);
            if ($mainRes['code'] != 200) {
                throw new \Exception($mainRes['msg'], 400);
            }
            while ($accountList) {
                $list = array_splice($accountList, 0, 2000);
                $list = array_map(function ($item) use ($endDate) {
                    return [
                        'ordernum' => $item
                    ];
                }, $list);
                $recordRes = $batchOperateBz->createRecord($serialCode, $list);
                if ($recordRes['code'] != 200) {
                    throw new \Exception($recordRes['msg'], 400);
                }
            }
            $batchOperateModel->commit();
        } catch (\Throwable $e) {
            $batchOperateModel->rollback();
            return $this->returnDataV2($e->getCode(), [], $e->getMessage());
        }
        //数据取完后把缓存删除掉，避免磁盘空间浪费
        $redis->del($cacheKey);
        return $this->returnDataV2(self::CODE_SUCCESS, ['serialCode' => $serialCode], '操作成功');
    }

    /**
     * 创建分销商查询任务
     * @param int $sid
     * @param $requestId
     * @param array $params
     * @return array
     */
    public function createDistributorQueryTask(int $sid, $requestId = '', array $params = [])
    {
        $redis = Container::pull(CacheReplication::class);
        $state = 'pending'; //pending=执行中，done=完成
        $progress = 0; //进度百分比
        $failData = []; //失败信息
        $renewPackageInfo = [];
        if ($requestId) {
            $cacheKey = sprintf(self::CREATE_DISTRIBUTOR_QUERY_TASK, $sid);
            $cacheData = $redis->get($cacheKey);
            if ($cacheData === false) {
                return $this->returnDataV2(self::CODE_INVALID_REQUEST, [], '查询任务超时，请返回上一级界面重新筛选');
            }
            $cacheData = json_decode($cacheData, true);
            if ($cacheData['rid'] != $requestId) {
                return $this->returnDataV2(self::CODE_INVALID_REQUEST, [], '查询任务已过期，请返回上一级界面重新筛选');
            }
            $progress = $cacheData['p'] ?? 0;
            if ($progress >= 100) {
                $progress = 100;
                $failData = $cacheData['f'] ?? [];
                $successCount = $cacheData['sc'] ?? 0;
                $state = $successCount ? 'done' : 'fail';
                //把进度缓存删除掉，避免磁盘空间浪费
                $redis->del($cacheKey);
                $distributionPackage = $this->getDistributorRenewPackageId();
                $distributionPackageRenewId = array_shift($distributionPackage);
                $packageBz = new Package();
                $renewPackageInfo = $packageBz->packageBasicInfo($distributionPackageRenewId);
                $renewPackageInfo = $renewPackageInfo['data'] ?? [];
                $renewPackageInfo = [
                    'id' => $distributionPackageRenewId,
                    'name' => $renewPackageInfo['name'] ?? '',
                    'introduce' => $renewPackageInfo['introduce'] ?? '',
                    'price' => 0
                ];
            } else {
                $currentTime = time();
                $expireAt = $cacheData['ex'] ?? 0;
                if ($expireAt > $currentTime) {
                    //模拟进度条进度
                    $progress = min($progress + rand(5, 10), 50);
                    $ex = $expireAt - $currentTime;
                    $redis->setex($cacheKey, $ex, json_encode(['p' => $progress, 'rid' => $requestId, 'ex' => $expireAt]));
                }
            }
        } else {
            $batchOperateModel = new BatchOperateModel();
            $workingTask = $batchOperateModel->getMainList('', 0, '', $sid, 0, 1,
                BatchOperateBiz::ACTION_TYPE_DISTRIBUTOR_RENEW, [], '', true);
            if ($workingTask) {
                return $this->returnDataV2(10012, [], '还有未完成续费操作，请等提交的请求完成后再提交');
            }
            $cacheKey = sprintf(self::CREATE_DISTRIBUTOR_QUERY_TASK, $sid);
            $cacheData = $redis->get($cacheKey);
            if ($cacheData !== false) {
                $cacheData = json_decode($cacheData, true);
                return $this->returnDataV2(10101, ['rid' => $cacheData['rid']], '还有未完成查询操作，需要继续上次查询操作吗?');
            }
            $requestId = uniqid() . rand(1000, 9999);
            $params['rid'] = $requestId;
            $data = [
                'action' => 'create_distributor_query_task',
                'data' => $params
            ];
            Queue::push('independent_system', 'BatchOperate_Job', $data);
            $ex = 2 * 60;
            $redis->setex($cacheKey, $ex, json_encode(['p' => 0, 'rid' => $requestId, 'ex' => time() + $ex]));
        }
        return $this->returnDataV2(self::CODE_SUCCESS, ['s' => $state, 'p' => $progress, 'f' => $failData, 'rid' => $requestId, 'rp' => $renewPackageInfo]);
    }

    /**
     * 存储分销商查询结果
     * @param $params
     * @return array
     */
    public function storeDistributorQueryResult($params)
    {
        if (empty($params['sid']) || empty($params['rid'])) {
            return $this->returnDataV2(self::CODE_SUCCESS);
        }
        $res = $this->updateDistributorQueryTaskProgress($params, 50, 30 * 60);
        if ($res['code'] != self::CODE_SUCCESS) {
            return $this->returnDataV2($res['code'], [], $res['msg']);
        }
        $failData = [];
        $queryDistributorList = [];
        if (!empty($params['query_account'])) {
            $queryDistributorList = $this->queryDistributorList($params['query_account'], $failData);
            //没有查到分销商信息，直接返回任务完成
            if (!$queryDistributorList) {
                return $this->storeDistributorQueryResultComplete($params, $queryDistributorList, $failData);
            }
        }
        $res = $this->updateDistributorQueryTaskProgress($params, 70);
        if ($res['code'] != self::CODE_SUCCESS) {
            return $this->returnDataV2($res['code'], [], $res['msg']);
        }
        //判断是否合作分销商
        $queryDistributorList = $this->queryCoDistributorList($params['sid'], $queryDistributorList, $failData);
        if (!$queryDistributorList) {
            return $this->storeDistributorQueryResultComplete($params, $queryDistributorList, $failData);
        }
        $res = $this->updateDistributorQueryTaskProgress($params, 80);
        if ($res['code'] != self::CODE_SUCCESS) {
            return $this->returnDataV2($res['code'], [], $res['msg']);
        }
        //判断是否OTA标识
        $queryDistributorList = $this->queryOtaDistributorList($queryDistributorList, $failData);
        if (!$queryDistributorList) {
            return $this->storeDistributorQueryResultComplete($params, $queryDistributorList, $failData);
        }
        $res = $this->updateDistributorQueryTaskProgress($params, 90);
        if ($res['code'] != self::CODE_SUCCESS) {
            return $this->returnDataV2($res['code'], [], $res['msg']);
        }
        //判断是否付费分销商且有效期<=90天
        $queryDistributorList = $this->queryDistributorPackageList($queryDistributorList, $params['expiration_type'] ?? [0], $failData);
        $successData = [];
        foreach ($queryDistributorList as $item) {
            $successData[$item['account']] = $item['score'] ?? 0;
        }
        return $this->storeDistributorQueryResultComplete($params, $successData, $failData);
    }

    protected function updateDistributorQueryTaskProgress($params, $progress, $taskDuration = 0)
    {
        $redis = Container::pull(CacheReplication::class);

        $cacheKey = sprintf(self::CREATE_DISTRIBUTOR_QUERY_TASK, $params['sid']);
        $cacheData = $redis->get($cacheKey);
        if ($cacheData === false) {
            return $this->returnDataV2(self::CODE_INVALID_REQUEST);
        }
        $cacheData = json_decode($cacheData, true);
        if ($cacheData['rid'] != $params['rid']) {
            return $this->returnDataV2(self::CODE_INVALID_REQUEST);
        }
        $currentTime = time();
        if ($taskDuration) {
            $expireAt = $currentTime + $taskDuration;
            $redis->setex($cacheKey, $taskDuration, json_encode(['p' => $progress, 'rid' => $params['rid'], 'ex' => $expireAt]));
            return $this->returnDataV2(self::CODE_SUCCESS);
        }
        $expireAt = $cacheData['ex'] ?? 0;
        if ($expireAt <= $currentTime) {
            return $this->returnDataV2(self::CODE_INVALID_REQUEST);
        }
        $ex = $expireAt - $currentTime;
        $redis->setex($cacheKey, $ex, json_encode(['p' => $progress, 'rid' => $params['rid'], 'ex' => $expireAt]));
        return $this->returnDataV2(self::CODE_SUCCESS);
    }

    protected function getDistributorRenewPackageId() {
        //获取续费套餐的id[续费套餐id,不可续费套餐id]，默认为[63,64]（63=分销商免费版，64=分销商付费版）
        $qconf = \Library\Container::pull(\Library\Util\QConfUtil::class);
        $config = $qconf->getQConf('/php/platform/renew_distributor_package_id');
        return $config ?: [63, 64];
    }

    protected function queryDistributorPackageList($queryDistributorList, $expirationType = [], &$failData = [])
    {
        $distributorIdList = array_column($queryDistributorList, 'fid');
        $renewPackage = $this->getDistributorRenewPackageId();
        $payPackageId = $renewPackage[1] ?? 64;
        $packageBz = new Package();
        $queryRes = $packageBz->batchGetUserPackageList($distributorIdList);
        $queryRes['data'] = $queryRes['code'] == 200 ? $queryRes['data'] : [];
        $distributorPackageList = [];
        while ($queryRes['data']) {
            $item = array_shift($queryRes['data']);
            //取用户有效期最大的一段套餐
            if (!isset($distributorPackageList[$item['member_id']]) || $distributorPackageList[$item['member_id']]['end_time'] < $item['end_time']) {
                $distributorPackageList[$item['member_id']] = $item;
            }
        }
        $currentTime = time();
        $maxDay = max($expirationType);
        $lastTime = strtotime("+{$maxDay} days", $currentTime);
        foreach ($queryDistributorList as $key => $item) {
            $account = $item['account'];
            //套餐已过期
            if (!isset($distributorPackageList[$item['fid']])) {
                //没有勾选已过期分销商
                if (!in_array(0, $expirationType)) {
                    $failData[] = ['account' => $account, 'msg' => '套餐已过期'];
                    unset($queryDistributorList[$key]);
                    continue;
                }
                $queryDistributorList[$key]['score'] = 0;
            } else {
                if ($distributorPackageList[$item['fid']]['package_id'] == $payPackageId) {
                    $failData[] = ['account' => $account, 'msg' => '付费用户，不支持代理续费'];
                    unset($queryDistributorList[$key]);
                    continue;
                }
                if ($lastTime <= $distributorPackageList[$item['fid']]['end_time']) {
                    $failData[] = ['account' => $account, 'msg' => '不在支持续费的有效期范围内'];
                    unset($queryDistributorList[$key]);
                    continue;
                }
                $queryDistributorList[$key]['score'] = $maxDay;
            }
        }
        return $queryDistributorList;
    }

    /**
     * 查询OTA账号列表
     * @param $queryDistributorList
     * @param $failData
     * @return array|void
     */
    protected function queryOtaDistributorList($queryDistributorList, &$failData = [])
    {
        $otaSystem = new PftOtaSystem();
        $list = $otaSystem->getList();
        if ($list) {
            $otaAccountList = array_column($list, null, 'account');
            $diffAccount = array_intersect_key($otaAccountList, $queryDistributorList);
            foreach ($diffAccount as $account => $item) {
                $failData[] = ['account' => $account, 'msg' => '特殊账号，不支持代理续费'];
            }
            $queryDistributorList = array_diff_key($queryDistributorList, $otaAccountList);
        }
        return $queryDistributorList;
    }

    /**
     * 查询合作分销商列表
     * @param $sid
     * @param $queryDistributorList
     * @param array $failData
     * @return array
     */
    protected function queryCoDistributorList($sid, $queryDistributorList, &$failData = [])
    {
        $successData = [];

        $pageNum = 1;
        $pageSize = 200;
        $sonIdArr = array_column($queryDistributorList, 'id');
        $dcGroupDistributorQueryApi = new DcGroupDistributorQueryApi();
        while (true) {
            $res = $dcGroupDistributorQueryApi->querySimpleGroupDistributorPage([
                'fidList' => $sonIdArr,
                'sid' => $sid,
                'pageNum' => $pageNum++,
                'pageSize' => $pageSize,
                'groupTypeList' => [0,2,4,5]
            ]);
            $res = $res['data'] ?? [];
            $res = $res['list'] ?? [];
            $successData = array_merge($successData, $res);
            if (count($res) < $pageSize) {
                break;
            }
        }
        //合作分销商账号列表
        $successData = array_column($successData, null, 'account');
        if ($queryDistributorList) {
            $diffAccount = array_diff_key($queryDistributorList, $successData);
            foreach ($diffAccount as $account => $item) {
                $failData[] = ['account' => $account, 'msg' => '不是该商家的分销商或在不活跃列表'];
            }
        }
        //查询分销商账号信息并返回数据
        $accountList = array_keys($successData);
        $api = new \Business\JavaApi\Member\MemberQuery();
        $res = $api->queryMemberByMemberQueryInfo(['accountList' => $accountList, 'dTypes' => [MemberConst::ROLE_DISTRIBUTOR]]);
        if ($res['code'] != 200) {
            $res['data'] = [];
        }
        $memberInfoList = array_column($res['data'], 'memberInfo');
        $memberInfoList = array_column($memberInfoList, null, 'account');
        $successData = array_intersect_key($successData, $memberInfoList);
        $diffAccount = array_diff_key($successData, $memberInfoList);
        foreach ($diffAccount as $account => $item) {
            $failData[] = ['account' => $account, 'msg' => '不是该商家的合作分销商'];
        }
        return $successData;
    }

    /**
     * 判断输入的账号是否分销商账号
     * @param $queryAccountList
     * @param array $failData
     * @return array
     */
    protected function queryDistributorList($queryAccountList, &$failData = [])
    {
        $successData = [];
        while ($queryAccountList) {
            $queryAccount = array_splice($queryAccountList, 0, 200);
            //判断需要查询的分销商账号信息是否分销商账号
            $api = new \Business\JavaApi\Member\MemberQuery();
            $res = $api->queryMemberByMemberQueryInfo(['accountList' => $queryAccount]);
            if ($res['code'] != 200 || !$res['data']) {
                foreach ($queryAccount as $item) {
                    $failData[] = ['account' => $item, 'msg' => '账号错误'];
                }
                continue;
            }
            $queryBusinessAccount = [];
            foreach ($res['data'] as $item) {
                $memberInfo = $item['memberInfo'];
                $queryAccount[] = $memberInfo['account'];
                if ($memberInfo['dtype'] == MemberConst::ROLE_DISTRIBUTOR) {
                    $successData[$memberInfo['account']] = $memberInfo;
                    $queryBusinessAccount[] = $memberInfo['account'];
                } else if ($memberInfo['dtype'] != MemberConst::ROLE_TOURIST) {
                    $failData[] = ['account' => $memberInfo['account'], 'msg' => '非分销商账号，不支持代理续费'];
                    $queryBusinessAccount[] = $memberInfo['account'];
                }
            }
            //排除非B端账号数据，返回“账号错误”
            $diffAccount = array_diff($queryAccount, $queryBusinessAccount);
            foreach ($diffAccount as $item) {
                $failData[] = ['account' => $item, 'msg' => '账号错误'];
            }
        }
        return $successData;
    }

    protected function queryValidDistributor($sid, $sonIds = [], $dname = '', $page = 1, $size = 100)
    {
        $queryParams = [
            'parentIds' => [$sid],
            'pageNum' => $page,
            'pageSize' => $size,
            'sonIds' => $sonIds,
            'sonIdTypes' => [],
            'shipTypes' => [0],
            'status' => [0],
            'account' => '',
            'mobile' => '',
            'dname' => $dname,
            'letters' => '',
            'cname' => '',
            'province' => '',
            'city' => '',
            'corpKinds' => [],
            'dTypes' => [1],
            'mStatus' => [0,3],
        ];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberRelationQuery',
            'queryDistributorBasisInfoListByParentId', $queryParams);

        $total = 0;
        $list = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $total = $queryRes['data']['total'];
            $list = $queryRes['data']['list'];
        }
        return [$total, $list];
    }

    protected function storeDistributorQueryResultComplete($params, $successData = [], $failData = [])
    {
        $redis = Container::pull(CacheReplication::class);
        $cacheKey = sprintf(self::CREATE_DISTRIBUTOR_QUERY_TASK, $params['sid']);
        $cacheData = $redis->get($cacheKey);
        if ($cacheData === false) {
            return $this->returnDataV2(self::CODE_INVALID_REQUEST);
        }
        $cacheData = json_decode($cacheData, true);
        if ($cacheData['rid'] != $params['rid']) {
            return $this->returnDataV2(self::CODE_INVALID_REQUEST);
        }
        $successCount = count($successData);
        //结束进度条，并把失败原因记录下来。前端会做轮训请求，要求在5分钟内取走数据
        $ex = 5 * 60;
        $redis->setex($cacheKey, 5 * 60, json_encode(['p' => 100, 'f' => $failData, 'sc' => $successCount, 'rid' => $params['rid'], 'ex' => time()+$ex]));
        $cacheResultKey = sprintf(self::STORE_DISTRIBUTOR_QUERY_RESULT, $params['sid'], $params['rid']);
        if ($successData) {
            $tmpData = [];
            foreach ($successData as $member => $score) {
                $tmpData[] = $score;
                $tmpData[] = $member;
            }
            if ($redis->exists($cacheResultKey)) {
                $redis->del($cacheResultKey);
            }
            $redis->zAdd($cacheResultKey, ...$tmpData);
            $redis->expire($cacheResultKey, 30 * 60);
        }
        $redis->setex('e:' . $cacheResultKey, 30 * 60, 1);
        return $this->returnDataV2(self::CODE_SUCCESS);
    }

    /**
     * 移除分销商查询结果
     * @param int $sid
     * @param $requestId
     * @param $account
     * @return array
     */
    public function removeDistributorQueryResult(int $sid, $requestId, $account)
    {
        $cacheKey = sprintf(self::STORE_DISTRIBUTOR_QUERY_RESULT, $sid, $requestId);
        //把任务丢入到队列中去执行
        $redis = Container::pull(CacheReplication::class);
        if (!$redis->exists($cacheKey)) {
            return $this->returnDataV2(10011, [], '由于太久没有使用该页面，请求结果已失效，请返回上一级界面重新筛选');
        }
        $redis->zRem($cacheKey, $account);
        return $this->returnDataV2(self::CODE_SUCCESS);
    }
}
