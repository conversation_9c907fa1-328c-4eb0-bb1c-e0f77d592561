<?php
/**
 * 团队订单预订相关接口
 * <AUTHOR>
 */

namespace Business\TeamOrder;

use Business\Base;
use Business\CommodityCenter\Ticket;
use Business\JavaApi\EvoluteApi;
use Business\JavaApi\Product\Listing as ProductLising;
use Business\JavaApi\Product\TouristGroupOrderService;
use Business\JavaApi\Ticket\Listing as TicketListing;
use Business\NewJavaApi\NewPms\StockQuery;
use Business\NewJavaApi\Product\ProductApi;
use Business\Product\Get\EvoluteGroupUser;
use Business\Product\Performance;
use Business\Product\Show;
use Library\Constants\Product\ProductConst;
use Library\Constants\Team\TeamConst;
use Library\Util\DebugUtil;
use Model\Member\Reseller;
use Model\Team\TeamRules as TeamRulesModel;

class Booking extends Base
{

    private $performanceBiz;
    public function __construct()
    {

    }

    /**
     * 生成报团订单的二维码url地址
     * <AUTHOR> Chen
     * @date 2019-02-17
     *
     * @param  string  $teamOrder  团单号
     * @param  int  $tnum
     * @param  string  $playTime  游玩时间
     *
     * @return string
     */
    public static function getTeamQrCode($teamOrder, $tnum, $playTime)
    {
        if (ENV == 'PRODUCTION') {
            $prefix = 'https://pay.12301.cc';
        } elseif ((ENV == 'TEST')) {
            $prefix = 'http://pay.12301dev.com';
        } else {
            $prefix = 'http://pay.12301.test';
        }
        $url = "{$prefix}/miniapp/teamorder_face.html?teamorder={$teamOrder}&tnum={$tnum}&playtime={$playTime}";

        return $url;
    }

    /**
     * 团队订单产品搜索
     * <AUTHOR>
     * @date   2018-05-08
     * @param  integer    $sid     供应商id
     * @param  integer    $aid     上级供应商id
     * @param  string     $date    日期
     * @param  string     $keyword 关键字搜索
     * @param  integer    $page    当前页码
     * @param  integer    $size    每页条数
     * @param  string    $lids    需要获取的景区id列表
     * @param  string    $excludeLids    不需要获取的景区id列表
     * @param  integer    $isQueryBeforeDay    1 需要限制提前预定, 0不判断提前限制
     * @return array
     */
    public function productSearch($sid, $aid, $date, $keyword = '', $page = 1, $size = 5, $lvlState = 2, $lids = "",
        $excludeLids = "", $isQueryBeforeDay = 1) {

        if (!$sid || !$aid || !$date) {
            return $this->returnData(204, '参数错误');
        }
        //获取报团限制中产品销售渠道限制的票类
        $teamLimitBz   = new TeamLimit();
        $exceptTidArr  = $teamLimitBz->getChannelExceptTid($aid, $sid);

        $listApi = new TouristGroupOrderService();
        $result  = $listApi->queryOrderProductList($sid, $aid, $date, $keyword, $page, $size, $lvlState, $lids, $excludeLids,
            implode(',', $exceptTidArr), $isQueryBeforeDay);

        if ($result['code'] != 200 || empty($result['data'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $ticketIdArr = [];
        foreach ($result['data'] as $value) {
            if (!empty($value['ticket_list'])) {
                foreach ($value['ticket_list'] as $v) {
                    $ticketIdArr[] = $v['ticket_id'];
                }
            }
        }
        $ticketIdArr = array_values(array_unique($ticketIdArr));
        //$result里有库存信息 这里用新接口替换
        $stockQueryApi = new StockQuery;
        $stockQueryRes = $stockQueryApi->compactQueryBatchSkuAvailableStock($sid, $aid, $ticketIdArr, $date);
        DebugUtil::debug([
            'tag' => 'compactQueryBatchSkuAvailableStockService',
            'fid' => $sid,
            'aid' => $aid,
            'date' => $date,
            'ticketIdArr' => $ticketIdArr,
            'stockQueryRes' => $stockQueryRes,
        ]);
        if ($stockQueryRes['code'] != self::CODE_SUCCESS) {
            return $this->returnData(203, $stockQueryRes['msg']);
        }
        $storageData = $stockQueryRes['data'] ?? [];
        $newStockMap = [];
        foreach ($storageData as $storageItem) {
            $newStockMap[$storageItem['skuId']] = $storageItem['availableStock'];
        }
        $ticketBz = new Ticket();
        // 处理数据返回给前端
        $data         = [];
        $packTid      = [];
        $timeShareBiz = new \Business\Order\TimeShareOrder();
        foreach ($result['data'] as $item) {
            if (empty($item['ticket_list'])) {
                continue;
            }
            switch ($item['p_type']) {
                case 'F':
                    $packTid = array_merge($packTid, array_column($item['ticket_list'], 'ticket_id'));
                    break;
            }
            // 返回团队订单预定的规则属性
            if (!empty($item['group_limit'])) {
                $groupLimitArr = json_decode($item['group_limit'], true);
            }
            $groupNumLimit['is_limit']     = 0;
            $groupNumLimit['limit_number'] = 0;
            if (isset($item['group_number_limit']) && $item['group_number_limit'] == 1) {
                $groupNumLimit['is_limit']     = 1;
                $groupNumLimit['limit_number'] = $groupLimitArr['group_number_limit']['limit_min_num'] ?? 0;
            }
            $groupTicketLimit['is_limit'] = 0;
            $groupTicketLimit['tickets']  = [];
            if (isset($item['group_ticket_limit']) && $item['group_ticket_limit'] == 1) {
                $groupTicketLimit['is_limit'] = 1;
                $groupTicketLimit['tickets']  = $groupLimitArr['group_ticket_limit'] ?? [];
            }

            $item['group_ticket_limit'] = $groupTicketLimit;
            $item['group_number_limit'] = $groupNumLimit;
            //获取票属性
            $ticketArr     = array_column($item['ticket_list'], 'ticket_id');
            $ticketInfo    = $ticketBz->queryTicketInfoByIds($ticketArr);
            $ticketInfoArr = [];
            foreach ($ticketInfo as $tmpTicketInfo) {
                $ticketInfoArr[$tmpTicketInfo['ticket']['id']] = array_merge($tmpTicketInfo['ticket'], $tmpTicketInfo['land_f']);
            }
            $ticketExtInfo = empty($ticketInfo) ? [] : array_key(array_column($ticketInfo, 'ext'), 'tid');
            foreach ($item['ticket_list'] as $key => $value) {
                $moreCredentials                                      = isset($ticketExtInfo[$value['ticket_id']]['more_credentials']) && $ticketExtInfo[$value['ticket_id']]['more_credentials'] ? json_decode($ticketExtInfo[$value['ticket_id']]['more_credentials'], true)['content'] : [];
                $item['ticket_list'][$key]['face_open']               = $ticketExtInfo[$value['ticket_id']]['face_open'];
                $item['ticket_list'][$key]['more_credentials']        = $moreCredentials ? 1 : 0;
                $item['ticket_list'][$key]['more_credential_content'] = $moreCredentials;
                $item['ticket_list'][$key]['zone_id']                 = $ticketInfoArr[$value['ticket_id']]['zone_id'] ?? 0;
                if ($item['p_type'] == 'H') {
                    //非套票子票的演出tid 用于追加票属性：is_show_seat 演出是否自由入座 1按号入座 2自由入座
                    //是否需要选游玩日期 pre_sale 0-需要（非期票） 1-不需要（需预约）
                    //座位分区 zone_id是否大于0  大于0-有分区 0-无分区
                    //是否有捆绑票 exist_bundle_ticket false-无捆绑票 true-有捆绑票
                    $getBundleTicket = $this->getPerformanceBiz()->getBindTickets($value['ticket_id']);
                    $item['ticket_list'][$key]['exist_bundle_ticket'] = !empty($getBundleTicket);
                    $item['ticket_list'][$key]['is_show_seat'] = (int)($ticketExtInfo[$value['ticket_id']]['is_show_seat'] ?? 0);
                }
                //覆盖旧版本查出来的库存数据
                if (isset($newStockMap[$value['ticket_id']])) {
                    $item['ticket_list'][$key]['ticket_storage'] = $newStockMap[$value['ticket_id']];
                }
            }
            //获取分时预约的库存信息
            $tidArr       = array_column($item['ticket_list'], 'ticket_id');
            $timeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($tidArr, $date, 9, true);
            foreach ($item['ticket_list'] as &$ticketItem) {
                $ticketItem['time_share_info'] = $timeShareRes['data'][$ticketItem['ticket_id']] ?? [];
            }
            $data[] = $item;
        }
        $packSonData = [];
        if ($packTid) {
            $packSonData = $this->handlePackProductSonTicket($packTid);
        }
        foreach ($data as $key => $value) {
            $ticketList = $value['ticket_list'];
            foreach ($ticketList as $k => $v) {
                if (isset($packSonData[$v['ticket_id']])) {
                    foreach ($packSonData[$v['ticket_id']] as &$packageTicketItem) {
                        $packageTicketItem['time_share_info'] = $v['time_share_info']['package_time_share_info'][$packageTicketItem['tid']] ?? [];
                    }
                    $data[$key]['ticket_list'][$k]['son_ticket'] = $packSonData[$v['ticket_id']];
                } else {
                    $data[$key]['ticket_list'][$k]['son_ticket'] = [];
                }
            }
        }

        return $this->returnData(200, "获取成功", $data);
    }

    public function WPTProductSearch($sid, $aid, $date, $keyword = '', $page = 1, $size = 5)
    {

        if (!$sid || !$aid || !$date) {
            return $this->returnData(204, '参数错误');
        }

        $listApi = new ProductLising();
        $result  = $listApi->groupWPTOrderList($sid, $aid, $date, $keyword, $page, $size);

        if (isset($result['code'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        } else {
            return $this->returnData(500, '接口出错');
        }
    }

    /**
     * 展示剩余的全部门票
     * <AUTHOR>
     * @date   2018-05-08
     *
     * @param  integer  $sid  供应商id
     * @param  integer  $productId  产品id
     * @param  integer  $aid  上级供应商id
     * @param  string  $date  日期
     *
     * @return array
     */
    public function showMoreTickets($sid, $productId, $aid, $date)
    {

        if (!$sid || !$productId || !$aid || !$date) {
            return $this->returnData(204, '参数错误');
        }

        $listApi = new TicketListing();
        $result  = $listApi->groupMoreTickets($sid, $productId, $aid, $date);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        $ticketArr    = array_column($result['data'], 'ticket_id');
        $timeShareBiz = new \Business\Order\TimeShareOrder();

        //获取下更多信息
        $ticketBz   = new Ticket();
        $ticketInfo = $ticketBz->queryTicketInfoByIds($ticketArr);
        if (empty($ticketInfo)) {
            return $this->returnData(204, "票信息有误");
        }
        $oneTicketInfo = $ticketInfo[0];
        if ($oneTicketInfo['land']['p_type'] == 'F') {
            $packSonData = $this->handlePackProductSonTicket($ticketArr);
        }
        $ticketInfoArr = [];
        foreach ($ticketInfo as $tmpTicketInfo) {
            $ticketInfoArr[$tmpTicketInfo['ticket']['id']] = array_merge($tmpTicketInfo['ticket'], $tmpTicketInfo['land_f'], $tmpTicketInfo['ext']);
        }
        $timeShareRes  = $timeShareBiz->getTimeSlicesWithTidArr($ticketArr, $date, 9, true);
        //获取票属性
        foreach ($result['data'] as $key => $value) {
            $moreCredentials                                 = !empty($ticketInfoArr[$value['ticket_id']]['more_credentials']) ? json_decode($ticketInfoArr[$value['ticket_id']]['more_credentials'], true)['content'] : [];
            $result['data'][$key]['face_open']               = $ticketInfoArr[$value['ticket_id']]['face_open'];
            $result['data'][$key]['more_credentials']        = $moreCredentials ? 1 : 0;
            $result['data'][$key]['more_credential_content'] = $moreCredentials;
            $result['data'][$key]['time_share_info']         = $timeShareRes['data'][$value['ticket_id']];
            $result['data'][$key]['zone_id']                 = $ticketInfoArr[$value['ticket_id']]['zone_id'];
            if ($ticketInfoArr[$value['ticket_id']]['p_type'] == 'H') {
                //非套票子票的演出tid 用于追加票属性：is_show_seat 演出是否自由入座 1按号入座 2自由入座
                //是否需要选游玩日期 pre_sale 0-需要（非期票） 1-不需要（需预约）
                //座位分区 zone_id是否大于0  大于0-有分区 0-无分区
                //是否有捆绑票 exist_bundle_ticket false-无捆绑票 true-有捆绑票
                $getBundleTicket = $this->getPerformanceBiz()->getBindTickets($value['ticket_id']);
                $result['data'][$key]['exist_bundle_ticket'] = !empty($getBundleTicket);
                $result['data'][$key]['is_show_seat'] = (int)($ticketInfoArr[$value['ticket_id']]['is_show_seat'] ?? 0);
            }
            $tmpPackSonData                                  = $packSonData[$value['ticket_id']] ?? [];
            foreach ($tmpPackSonData as &$packageTicketItem) {
                $packageTicketItem['time_share_info'] = $timeShareRes['data'][$value['ticket_id']][$packageTicketItem['tid']] ?? [];
            }
            unset($packageTicketItem);
            $result['data'][$key]['son_ticket'] = $tmpPackSonData;
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取team_order_num当日团队订单自增数
     * <AUTHOR>
     * @date 2018-07-17
     */
    public function getTeamNumIncr()
    {
        $cacheRedis = \Library\Cache\CacheRedis::getInstance('redis');

        $key          = 'team_order_num:' . date('Ymd');
        $teamOrderNum = $cacheRedis->incr($key);

        if ($teamOrderNum == 1) {
            $cacheRedis->setTimeout($key, 86400);
        }

        return $teamOrderNum;
    }

    /**
     * 生成团队订单
     * <AUTHOR>
     * @date 2018-07-17
     */
    public function createTeamOrderNum($memberId)
    {
        $incrNum  = $this->getTeamNumIncr();
        $padNum   = str_pad((string)$incrNum, 6, '0', STR_PAD_LEFT);
        $orderNum = 'team_' . date('ymd') . $memberId . $padNum;

        return $orderNum;
    }
    
    /**
     * 生成一个新的团单激活码(保证不重复)
     * @param $memberId
     * @return string
     */
    public function newTeamActiveCode($memberId) {
        $incrNum  = $this->getTeamNumIncr();
        $padNum   = str_pad((string)$incrNum, 6, '0', STR_PAD_LEFT);
        $orderNum = TeamConst::TEAM_ACTIVE_CODE_PREFIX . date('ymd') . $memberId . $padNum;
    
        return $orderNum;
    }

    /**
     * 团队订单限制判断
     * <AUTHOR>
     * @date 2018-12
     *
     * @param $lid
     * @param $optionsArr
     *
     * @return array
     *
     */
    public function checkTeamLimit($lid, $optionsArr)
    {
        $landIdArr   = [$lid];
        $javaAPi     = new \Business\CommodityCenter\Land();
        $landInfoArr = $javaAPi->queryLandMultiQueryById($landIdArr)[0];

        $totalBuyNum     = 0;
        $ticketBuyNumArr = [];
        if (!empty($optionsArr)) {
            $ticketModel = new \Model\Product\Ticket();
            $ticketArray = $ticketModel->getTidArrByPid(array_column($optionsArr, 'pid'));
            foreach ($optionsArr as $item) {
                $totalBuyNum                                         += $item['tnum'];
                $ticketBuyNumArr[$ticketArray[$item['pid']]]['tnum'] = $item['tnum'];
            }

            if (!empty($landInfoArr['group_limit'])) {
                $groupLimtArr = json_decode($landInfoArr['group_limit'], true);
            }

            if ($landInfoArr['group_number_limit'] == 1) {
                if ($totalBuyNum < $groupLimtArr['group_number_limit']['limit_min_num']) {
                    return $this->returnData(204,
                        $landInfoArr['title'] . '票数未达到最低' . $groupLimtArr['group_number_limit']['limit_min_num'] . '张数');
                }
            }
            if ($landInfoArr['group_ticket_limit'] == 1) {
                foreach ($groupLimtArr['group_ticket_limit'] as $ticketLimit) {
                    $tid      = $ticketLimit['tid'];
                    $type     = $ticketLimit['type'];
                    $score    = $ticketLimit['score'];
                    $limitNum = $score * $totalBuyNum / 100;

                    if ($type == 1) {
                        // 不高于
                        if (isset($ticketBuyNumArr[$tid]) && $ticketBuyNumArr[$tid]['tnum'] > $limitNum) {
                            return $this->returnData(204, $tid . '票数高于本产品的' . $score . '%');
                        }
                    } else {
                        // 不低于
                        if (!isset($ticketBuyNumArr[$tid]) || $ticketBuyNumArr[$tid]['tnum'] < $limitNum) {
                            return $this->returnData(204, $tid . '票数低于本产品的' . $score . '%');
                        }
                    }
                }
            }
        }

        return $this->returnData(200);
    }

    /**
     * 报团产品预定列表获取不限制票列表数
     * <AUTHOR>
     * @date   2018-03-22
     * @param  integer    $sid     供应商id
     * @param  integer    $aid     上级供应商id
     * @param  string     $date    日期
     * @param  string     $keyword 关键字搜索
     * @param  integer    $page    当前页码
     * @param  integer    $size    每页条数
     * @param  string     $type    产品类型
     * @return array
     */
    public function productSearchNoLimitTicketNum($sid, $aid, $date, $keyword = '', $page = 1, $size = 5, $type = '') {

        if (!$sid || !$aid || !$date) {
            return $this->returnData(204, '参数错误');
        }

        $listApi = new ProductLising();
        $result  = $listApi->groupWPTOrderList($sid, $aid, $date, $keyword, $page, $size, $type);

        if (isset($result['code'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        } else {
            return $this->returnData(500, '接错出错');
        }
    }
    /**
     * 报团产品预定列表获取 不限制票列表数
     * <AUTHOR>
     * @date   2021-01-07
     * @param  integer    $sid     供应商id
     * @param  integer    $aid     上级供应商id
     * @param  string     $date    日期
     * @param  string     $keyword 关键字搜索
     * @param  integer    $page    当前页码
     * @param  integer    $size    每页条数
     * @param  string     $type    产品类型
     * @param  integer    $isQueryBeforeDay  是否过滤提前预定天数 0：否 1：是
     *
     * @return array
     */
    public function productSearchNoLimitTicketNumNew($sid, $aid, $date, $keyword = '', $page = 1, $size = 5, $type = '', $isQueryBeforeDay = 1){
        if (!$sid || !$aid || !$date) {
            return $this->returnData(204, '参数错误');
        }
        $touristGroupJavaApi = new TouristGroupOrderService();
        $result = $touristGroupJavaApi->queryOrderProductTicketList($sid, $aid, $date, $keyword, $page, $size, $type, $isQueryBeforeDay);
        return $result;
    }
    /**
     * 报团产品预定列表获取套票下的子票信息
     * <AUTHOR>
     * @date   2021-11-24
     *
     * @param  array  $arrPackTid  套票子票id
     *
     * @return array
     */
    public function handlePackProductSonTicket($arrPackTid)
    {
        $packBiz   = new \Business\Product\PackTicket();
        $sonTicket = $packBiz->getTickets($arrPackTid);
        if (empty($sonTicket)) {
            return [];
        }

        //批量获取下子票的票属性
        $arrSonTicketId     = array_unique(array_column($sonTicket, 'tid'));
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $arrTicketData      = $commodityTicketBiz->queryTicketInfoByIds($arrSonTicketId);
        $resultData         = [];
        foreach ($sonTicket as $key => $value) {
            foreach ($arrTicketData as $k => $v) {
                if ($value['tid'] == $v['ticket']['id']) {
                    $moreCredentials = isset($v['ext']['more_credentials']) && $v['ext']['more_credentials'] ? json_decode($v['ext']['more_credentials'], true)['content'] : [];
                    $resultData[]    = [
                        'parent_tid'              => $value['parent_tid'],
                        'tid'                     => $value['tid'],
                        'num'                     => $value['num'],
                        'lid'                     => $v['land']['id'],
                        'more_credentials'        => $moreCredentials ? 1 : 0,
                        'more_credential_content' => $moreCredentials,
                        'tourist_info'            => $v['land_f']['tourist_info'],
                        'pre_sale'                => $v['ticket']['pre_sale'],
                        'p_type'                  => $v['land']['p_type'],
                        'zone_id'                 => $v['land_f']['zone_id'],
                        'title'                   => $v['land']['title'] . ' ' . $v['ticket']['title'],
                    ];
                    break;
                }
            }
        }
        $result = [];
        foreach ($resultData as $data) {
            $result[$data['parent_tid']][] = $data;
        }

        return $result;
    }

    /**
     * 获取报团产品价格跟库存
     *
     * @param string $date 日期
     * @param int $aid 商家id
     * @param int $fid 游玩日期
     * @param string $tids 票id字符串
     *
     * @date 2023/08/01
     * @auther yangjianhui
     * @return array
     */
    public function getTicketPriceAndStorage($date, $aid, $fid, $tids)
    {
        if (empty($date)  || empty($aid) || empty($fid) || empty($tids)) {
            return $this->returnData(204, '参数错误');
        }
        $tidArr = explode(',', $tids);
        $commodityTicketBiz = new Ticket();
        $ticketInfoRes      = $commodityTicketBiz->queryTicketInfoByIds($tidArr, 'id,pre_sale', '', '');
        foreach ($ticketInfoRes as $key => $value) {
            if ($value['ticket']['pre_sale'] == 1) {
                $promissoryNote[] = $value['ticket']['id'];
            } else {
                $normalTicket[] = $value['ticket']['id'];
            }
        }
        if (empty($normalTicket) && empty($promissoryNote)) {
            return $this->returnData(201, '票属性获取失败');
        }
        $tPriceBiz          = new \Business\Product\Price();
        $normalData         = [];
        $promissoryNoteData = [];
        if (!empty($normalTicket)){
            $normalDataRes = $tPriceBiz->buyBatchGet($fid, $aid, $date, implode(',',$normalTicket));
            $normalData    = $normalDataRes['data'];
            if (empty($normalData)) {
                return $this->returnData(203, '获取价格出错，请稍后重试');
            }
        }
        if (!empty($promissoryNote)) {
            $promissoryNoteDataRes = $tPriceBiz->buyBatchGet($fid, $aid, date('Y-m-d'), implode(',',$promissoryNote));
            $promissoryNoteData    = $promissoryNoteDataRes['data'];
            if (empty($promissoryNoteData)) {
                return $this->returnData(203,'获取价格出错，请稍后重试');
            }
        }
        $data = $normalData + $promissoryNoteData;
        //获取票类型,套票要返回子票的分时配置
        $timeShareBiz = new \Business\Order\TimeShareOrder();
        $timeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($tidArr, $date, 9, true);
        foreach ($data as $tid => &$item) {
            $item['time_share_info'] = $timeShareRes['data'][$tid];
        }

        return $this->returnData(200, 'success', $data);
    }

    /**
     * 获取报团景区列表
     *
     * @param int $aid 供应商id
     * @param int $fid 分销id
     * @param int $sid 当前登录供应商id
     * @param int $teamTypeId 团队类型
     * @param int $page 页码
     * @param string $keyWord 查询关键字
     * @param int $size 条数
     * @date 2023/08/03
     * @auther yangjianhui
     * @return array
     */
    public function getTeamLandList($aid, $fid, $keyWord, $teamTypeId, $sid, $page, $size)
    {
        if (empty($aid) || empty($fid)) {
            return $this->returnData(204, '参数错误');
        }

        if (empty($teamTypeId)) {
            $productApi = new ProductApi();
            $result     = $productApi->queryDistinctLandByPaging($fid, $aid, [], 1, $page, $size, [], $keyWord, 1);
            if ($result['code'] != 200) {
                return $this->returnData($result['code'], $result['msg']);
            }
            $returnData = [];
            foreach ($result['data']['list'] as $value) {
                $returnData[] = [
                    'landTitle' => $value['landTitle'],
                    'lid'       => $value['lid'],
                    'sourceId'  => $value['sourceId'],
                ];
            }

            return $this->returnData(200, "获取成功", $returnData);
        }
        $ruleModel = new TeamRulesModel();
        $field     = "limit_lid_config, limit_distributors_type, limit_distributors_id, limit_distributors_group, limit_tids";
        $ruleInfo  = $ruleModel->getOrderEditLimitInfoById($teamTypeId, $field);
        if (empty($ruleInfo) || empty($ruleInfo['limit_lid_config']) || empty($ruleInfo['limit_tids'])) {
            return $this->returnData(200, "获取成功");
        }
        $teamSupply = 0; //是否为报团计调 1：是 0：否
        if ($aid == $sid) {
            $teamSupply = 1;
        }
        //报团计调且现在类型是指定分销商时，过滤下
        if ($teamSupply && $ruleInfo['limit_distributors_type'] == 2) {
            $groupDids = [];
            if (!empty($ruleInfo['limit_distributors_group'])) {
                //获取分组信息
                $evoluteGroupApi = new EvoluteGroupUser();
                $groupInfo       = $evoluteGroupApi->queryGroupFidsByGroupIds(explode(',', $ruleInfo['limit_distributors_group']));
                $groupDids       = empty($groupInfo['data']) ? [] : array_column($groupInfo['data'], 'fid');
            }
            //报团计调的时候，分销商不在团单类型里，直接返回
            if ((empty($groupDids) && empty($ruleInfo['limit_distributors_id'])) ||
                !in_array($fid, array_merge(explode(',', $ruleInfo['limit_distributors_id']), $groupDids))) {

                return $this->returnData(200, "获取成功");
            }
        }
        $limitLidConfig = json_decode($ruleInfo['limit_lid_config'], true);
        $allTidArr      = [];
        foreach ($limitLidConfig as $value) {
            $allTidArr = array_merge($allTidArr, $value);
        }
        //获取报团限制中产品销售渠道限制的票类
        $teamLimitBz   = new TeamLimit();
        $exceptTidArr  = $teamLimitBz->getChannelExceptTid($aid, $fid);
        $allTidArr     = array_diff($allTidArr, $exceptTidArr);
        $ticketApi     = new Ticket();
        $ticketInfoRes = $ticketApi->queryTicketInfoByIds($allTidArr);
        if (empty($ticketInfoRes)) {
            return $this->returnData(204, "票类信息获取失败");
        }
        $ticketInfo = [];
        $pidArr     = [];
        foreach ($ticketInfoRes as $value) {
            $ticketInfo[$value['ticket']['id']] = array_merge($value['ticket'], $value['land_f'], $value['ext']);
            $pidArr[]                           = $value['product']['id'];
        }
        $pids       = empty($pidArr) ? "" : implode(',', $pidArr);
        $landIdArr  = array_keys($limitLidConfig);
        $evoluteApi = new EvoluteApi();
        $landList   = $evoluteApi->getProductRetailList($aid, $fid, implode(',', $landIdArr), $keyWord,'',
            '', 0, 0, '', '', $page, $size, $pids);
        if ($landList['code'] != 200) {
            return $this->returnData($landList['code'], $landList['msg']);
        }
        $returnData = [];
        foreach ($landList['data']['lists'] as $value) {
            $returnData[] = [
                'landTitle' => $value['name'],
                'lid'       => $value['id'],
                'sourceId'  => $value['supplier_id'],
            ];
        }

        return $this->returnData(200, "获取成功", $returnData);
    }

    /**
     * 获取演出库存信息
     *
     * @param int $aid 供应商id
     * @param int $sid 当前登录供应商id
     * @param int $lid 景区id
     * @param int $tid 票id
     * @param string $date 日期
     * @param int $zoneId 分区id
     *
     * @return array
     * @date 2023/12/05
     * @auther yangjianhui
     * @return array
     */
    public function getShowInfo($aid, $sid, $lid, $tid, $date)
    {
        if (empty($aid) || empty($sid) || empty($tid) || empty($date)) {
            return $this->returnData(203, "参数有误");
        }

        $landIdArr   = [$lid];
        $javaAPi     = new \Business\CommodityCenter\Land();
        $productInfo = $javaAPi->queryLandMultiQueryById($landIdArr)[0];
        if (!$productInfo) {
            return $this->returnData(203, "暂无演出产品信息");
        }

        $ticketModel   = new \Model\Product\Ticket();
        $ticketInfoArr = $ticketModel->getTicketInfoById($tid, 'pid');
        $pid           = $ticketInfoArr['pid'];
        if ($sid == $aid) {
            $topSellerId = $sid;
        } else {
            $sellerModel   = new Reseller();
            $topSellerId   = $sellerModel->getTopResellerId($sid, $aid, $pid);
            $topSellerId   = $topSellerId ? $topSellerId : $sid;
        }
        $condition = [
            'applyLimit'  => 1,
            'landFlag'    => 0,
            'productFlag' => 1,
            'landFFlag'   => 1,
        ];
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('apply_did,title,tprice,pay,id',
            'id', '', 'zone_id,tourist_info', false, null, [$lid], null, null, $condition);
        $data      = [];
        foreach ($ticketArr as $idx => $ticketInfo) {
            $data[$idx]['apply_did']                = $ticketInfo['ticket']['apply_did'];
            $data[$idx]['title']                    = $ticketInfo['ticket']['title'];
            $data[$idx]['price']                    = $ticketInfo['ticket']['tprice'];
            $data[$idx]['pay']                      = $ticketInfo['ticket']['pay'];
            $data[$idx]['tid']                      = $ticketInfo['ticket']['id'];
            $data[$idx]['pid']                      = $ticketInfo['product']['id'];
            $data[$idx]['zone_id']                  = $ticketInfo['land_f']['zone_id'];
            $data[$idx]['tourist_info']             = $ticketInfo['land_f']['tourist_info'];
        }


        $date   = date('Y-m-d', strtotime($date));
        $rounds = $this->_getRoundsInfo($aid, $sid, $productInfo['venus_id'], $date, $topSellerId, $data);

        if (count($rounds['roundList']) < 1) {
            return $this->returnData(203, "暂无演出场次信息");
        }
        $return = [
            'rounds'  => $rounds,
            'tickets' => $data,
        ];

        return $this->returnData(200, "获取成功", $return);
    }

    /**
     * 获取演出场次列表
     *
     * @param  int  $venueId  场馆ID
     * @param  string  $date  日期
     * @param  int  $topSellerId  一级分销商，用来获取分销库存
     *
     * @return array
     */
    private function _getRoundsInfo($aid, $sid, $venueId, $date, $topSellerId, $ticketInfo)
    {
        if (!$venueId || !$date || !$topSellerId) {
            return [];
        }
        $roundList = [];
        $showBiz   = new Show();
        $date      = date('Y-m-d', strtotime($date));
        $tPriceBiz = new \Business\Product\Price();
        //$javaApi   = new \Business\CommodityCenter\Ticket();
        $tmpList = $showBiz->getRoundList($venueId, $date, $topSellerId);
        if ($tmpList['code'] == 0) {
            return [];
        }
        foreach ($tmpList['data'] as $item) {
            $roundList[] = [
                'id'           => $item['id'],
                'round_name'   => $item['round_name'],
                'bt'           => $item['bt'],
                'et'           => $item['et'],
                'venus_id'     => $item['venus_id'],
                'use_date'     => $item['use_date'],
                'storage'      => $item['storage'],
                'area_storage' => $item['area_storage'],
            ];
        }
        $tidArr        = array_column($ticketInfo, 'tid');
        $normalDataRes = $tPriceBiz->buyBatchGet($sid, $aid, $date, implode(',', $tidArr));
        $normalData    = $normalDataRes['data'];
        if (empty($normalData)) {
            return $this->returnData(400, '获取价格出错，请稍后重试');
        }

        $calendar = [];
        foreach ($ticketInfo as $value) {
            //数据格式处理
            if (!empty($value['zone_id'])) {
               continue;
            }
            $calendar[$value['tid']] = $normalData[$value['tid']]['availableStorage'];
        }

        return [
            'roundList' => $roundList,
            'calendar'  => $calendar,
        ];
    }
    private function getPerformanceBiz(): Performance
    {
        if (!$this->performanceBiz) {
            $this->performanceBiz = new Performance();
        }
        return $this->performanceBiz;
    }
}
