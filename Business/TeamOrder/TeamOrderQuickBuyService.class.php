<?php
/**
 * 新版团单快捷购票
 */

namespace Business\TeamOrder;

use Business\Base;
use Business\CommodityCenter\Land;
use Business\CommodityCenter\Ticket;
use Business\CommonTrait\LandTrait;
use Business\JavaApi\Member\IdCard;
use Business\Product\Show;
use Library\component\Singleton;
use Library\Tools\Helpers;
use Library\Util\DebugUtil;
use Model\Member\Reseller;
use Tpl\userCenter;

class TeamOrderQuickBuyService extends Base
{
    use Singleton, LandTrait;

    //最大导入上限200条
    const MAX_IMPORT_NUM = 500;

    //获取适用于供应商+分销给供应商的快捷购票方案列表
    public function quickBuySolutionList(array $params): array
    {
        $javaParams = [
            'memberId' => $params['sid'],
            'sid' => $params['aid'],//筛选某个具体供应商分销给他的+他自己创建的
            'pageNum' => $params['page'],
            'pageSize' => $params['pageSize'],
        ];
        if (!empty($params['keyword'])) {
            $javaParams['solutionNameLike'] = $params['keyword'];
        }
        $return = [
            'total' => 0,
            'page' => $params['page'],
            'page_size' => $params['pageSize'],
            'total_page' => 0,
            'list' => [],
        ];
        $result = Helpers::DistributionCenterCall('queryQuickBuyMatchSolutionByPaging', $javaParams, 'quickBuyRule', false, 'distribution-center');
        if ($result['code'] != 200 || empty($result['rows'])) {
            return $return;
        }
        $return['total'] = $result['total'];
        $return['total_page'] = ceil($result['total'] / $params['pageSize']);
        $return['list'] = $result['rows'];
        return $return;
    }

    //根据下单信息匹配可用的门票
    public function queryQuickBuyMatchSolution(array $params): array
    {
        $javaParams = [
            'memberId' => (int)$params['aid'],
            'solutionId' => (int)$params['solutionId'],
            'peopleList' => $params['peopleList'],
        ];
        $result = Helpers::DistributionCenterCall('queryQuickBuyMatchSolution', $javaParams, 'quickBuyRule', false, 'distribution-center');
        if ($result['code'] != 200 || empty($result['rows'])) {
            return [];
        }
        return $result['rows'];
    }

    //方案详情
    public function querySolutionById($mid, $solutionId)
    {
        if (empty($solutionId)) {
            return [];
        }
        $solutionId = intval($solutionId);
        $mid = intval($mid);
        $javaParams = ['id' => $solutionId, 'memberId' => $mid];
        $result = Helpers::DistributionCenterCall('querySolutionById', $javaParams, 'quickBuySolution', false, 'distribution-center', false, [], 'GET');
        if ($result['code'] != 200 || empty($result['rows'])) {
            return [];
        }
        return $result['rows'];
    }

    /**
     * @throws \Exception
     */
    public function importExcel(array $params): array
    {
        if (empty($params['solution_id_arr'])) {
            throw new \Exception('请选择方案');
        }
        if (empty($params['aid'])) {
            throw new \Exception('请选择供应商');
        }
        //解析Excel
        $fileInfo = $this->parseExcelFile($params['file']);
        if (count($fileInfo) > self::MAX_IMPORT_NUM) {
            throw new \Exception('导入的游客数量不能超过' . self::MAX_IMPORT_NUM);
        }
        //匹配方案
        $matchParams = [
            'sid' => $params['sid'],//登录用户的sid
            'aid' => $params['aid'],
            'fid' => $params['fid'],
            'team_type_id' => $params['team_type_id'],
            'touristList' => $fileInfo,
            'solutionIdArr' => $params['solution_id_arr'],
            'date' => date('Y-m-d'),
        ];
        return $this->touristMatchSolution($matchParams);
    }

    /**
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Exception
     * @throws \Exception
     */
    public function parseExcelFile($file): array
    {
        //文件有效判断
        if ($file['size'] <= 0 && $file['error'] == 1) {
            throw new \Exception('文件无法识别，请重试', 203);
        } elseif ($file['size'] <= 0) {
            throw new \Exception('文件内容不能为空', 203);
        }
        //允许上传的文件类型
        $allowType = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
        ];
        //验证文件类型
        if (!in_array($file['type'], $allowType)) {
            throw new \Exception('上传的文件类型不对', 203);
        }
        //文件解析
        $fileInfo = $this->readExcelDetail($file['tmp_name']);
        //内容不得为空
        if (empty($fileInfo)) {
            throw new \Exception('内容异常，无法导入！', 203);
        }
        //解析处理
        return $this->handleExcelDetail($fileInfo);
    }

    /**
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     * @throws \Exception
     */
    public function readExcelDetail(string $filename): array
    {
        if (empty($filename)) {
            throw new \Exception('文件不存在', 203);
        }
        //读取文件类型
        $inputFileType = \PHPExcel_IOFactory::identify($filename);
        //允许读取的excel类型,低版本兼容
        $allowInputFileType = ['Excel5', 'Excel2007', 'Excel2003XML'];
        //验证读取类型
        if (!in_array($inputFileType, $allowInputFileType)) {
            return [];
        }
        $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($filename, $encode = 'utf-8');
        $objPHPExcel->setActiveSheetIndex(0);
        $objWorksheet = $objPHPExcel->getActiveSheet();
        $highestRow = $objWorksheet->getHighestRow();
        $highestColumn = $objWorksheet->getHighestColumn();
        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
        $excelData = array();
        for ($row = 1; $row <= $highestRow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $tmpValue = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
                if (empty($tmpValue)) {
                    continue;
                }
                //每行一个数组(一行存在多个列字段)
                $excelData[$row][] = $tmpValue;
            }
        }
        return $excelData;
    }
    //需要区分校验不同的证件类型
    //身份证要获取省市区 用于匹配省市区限制的方案
    public function handleExcelDetail(array $fileInfo): array
    {
        $tourists = [];
        $checkIdCard = [];
        $idCardArr = [];
        foreach ($fileInfo as $key => $info) {
            //空行不算，移除
            if (empty($info)) {
                unset($fileInfo[$key]);
                continue;
            }
            $item = [];
            $name = $info[0] ?? '';
            $cardType = $info[1] ?? '';
            $idCard = $info[2] ?? '';
            $mobile = $info[3] ?? '';
            //验证是否抬头行，不记录
            if (strstr($name, '必填') || strstr($cardType, '证件类型') || strstr($idCard, '证件号码')) {
                continue;
            }
            $item['name'] = trim($name, ' ');
            $item['cardType'] = trim($cardType, ' ');
            $item['idCard'] = trim($idCard, ' ');
            $item['mobile'] = trim($mobile, ' ');
            $checkRes = $this->checkCardValid($item['cardType'], $item['idCard']);
            //1:身份证 2:护照 3:军官证 4:回乡证 5:台胞证 6:外国人永久居留证
            //7:港澳通行证 8:台湾通行证 9:港澳居民居住证 10:台湾居民居住证
            $item['cardTypeInt'] = $checkRes['cardType'];//数字类型
            $checkNameMsg = strlen($item['name']) == 0 ? '游客姓名有误' : '';
            if (!$checkRes['valid']) {
                $item['valid'] = false;
                $item['errorMsg'] = $checkRes['msg'];
                $item['age'] = '';
                $item['gender'] = '';
                $item['area'] = [];
            } else {
                if (isset($checkIdCard[$item['idCard']])) {
                    $item['valid'] = false;
                    $item['errorMsg'] = $item['idCard'] . '证件号码重复';
                } else {
                    $item['valid'] = empty($checkNameMsg);//正常与否取决于游客姓名的校验 上面是校验了证件号
                    $item['errorMsg'] = $checkNameMsg;
                    $checkIdCard[$item['idCard']] = 1;
                }
                if (in_array($item['cardTypeInt'], [1, 9, 10])) {
                    list($age, $gender) = $this->getGenderAndAgeByIdCard($item['idCard']);
                } else {
                    list($age, $gender) = [0, ''];
                }
                $item['age'] = $age > 0 ? $age : '--';
                $item['gender'] = $gender;
                $item['area'] = [];
                if ($item['cardTypeInt'] == 1) {
                    $idCardArr[] = $item['idCard'];
                }
            }
            $tourists[] = $item;
        }
        $idCardMap = $this->getProvinceCityByCardId($idCardArr);
        foreach ($tourists as $key => $item) {
            $tourists[$key]['area'] = $idCardMap[$item['idCard']] ?? [];
        }
        return $tourists;
    }

    /**
     * 通过身身份证和性别获取年龄
     * @param string $idCard
     * @return array
     */
    public function getGenderAndAgeByIdCard(string $idCard): array
    {
        if (empty($idCard)) {
            return [0, 0];
        }
        if (strlen($idCard) == 15) {
            //15位身份证号取最后一位
            $sex = substr($idCard, -1, 1);
            $born = substr($idCard, 6, 2);
        }
        if (strlen($idCard) == 18) {
            //18位身份证号取最后第二位
            $sex = substr($idCard, -2, 1);
            $born = substr($idCard, 6, 4);
        }
        if (!isset($sex) || !isset($born)) {
            return [0, 0];
        }
        //奇数男性 偶数女性
        $sex = $sex % 2 == 0 ? 'female' : 'male';
        //年龄
        $nowYear = date('Y');
        $age = $nowYear - $born;
        return [$age, $sex];
    }

    //通过用户身份证获取客源地
    public function getProvinceCityByCardId(array $idCardArr): array
    {
        if (empty($idCardArr)) {
            return [];
        }
        $code = (new IdCard())->queryAreaByIDCardPrefixNew($idCardArr);
        $codeData = $code['data'] ?? [];
        //这里要剔除奇怪的格式 比如：110105返回的格式和其他请求返回的不一样 多了一个
        if (!empty($codeData)) {
            foreach ($codeData as $key => $item) {
                if (!isset($item['provinceCode'])) {
                    unset($codeData[$key]);
                }
            }
            $codeData = array_values($codeData);
        }
        $codeMap = array_column($codeData, null, 'idCard');
        $idCardMap = [];
        foreach ($idCardArr as $idCard) {
            //目前没有接口实现，这里还是调用配置文件
            $code = substr($idCard, 0, 6);
            $value = $codeMap[$code] ?? [];
            $province = $value['provinceName'] ?? '';
            $city = $value['cityName'] ?? '';
            $town = $value['countryName'] ?? '';
            $countyCode = $value['countryCode'] ?? '';
            $cityCode = $value['cityCode'] ?? '';
            $provinceCode = $value['provinceCode'] ?? '';
            $idCardMap[$idCard] = [
                'province' => $province,
                'city' => $city,
                'town' => $town,
                'provinceCode' => $provinceCode,
                'cityCode' => $cityCode,
                'countyCode' => $countyCode,
            ];
        }
        /*DebugUtil::debug([
            'tag' => 'getProvinceCityByCardId',
            'data' => $codeData,
            'codeMap' => $codeMap,
            'idCardArr' => $idCardArr,
            'idCardMap' => $idCardMap,
        ]);*/
        return $idCardMap;
    }

    //证件类型 1:身份证 2:护照 3:军官证 4:回乡证 5:台胞证 6:外国人永久居留证 7:港澳通行证 8:台湾通行证 9:港澳居民居住证 10:台湾居民居住证
    public function checkCardValid(string $cardType, string $idCard): array
    {
        if (strstr($cardType, '身份证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isIdCard($idCard),
                'msg' => '身份证不合法',
                'cardType' => 1,
            ];
        }
        if (strstr($cardType, '护照')) {
            return [
                'valid' => !empty($idCard),
                'msg' => '护照不能为空',
                'cardType' => 2,
            ];
        }
        if (strstr($cardType, '军官证')) {
            return [
                'valid' => !empty($idCard),
                'msg' => '军官证不能为空',
                'cardType' => 3,
            ];
        }
        if (strstr($cardType, '回乡证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isBackHomeCard($idCard),
                'msg' => '回乡证不合法',
                'cardType' => 4,
            ];
        }
        if (strstr($cardType, '台胞证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isTaiWanCompatriotCard($idCard),
                'msg' => '台胞证不合法',
                'cardType' => 5,
            ];
        }
        if (strstr($cardType, '外国人永久居留证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isForeignIdCard($idCard),
                'msg' => '外国人永久居留证不合法',
                'cardType' => 6,
            ];
        }
        if (strstr($cardType, '港澳通行证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isHKMPassportCard($idCard),
                'msg' => '台胞证不合法',
                'cardType' => 7,
            ];
        }
        if (strstr($cardType, '台湾通行证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isTaiWanPassportCard($idCard),
                'msg' => '台湾通行证不合法',
                'cardType' => 8,
            ];
        }
        if (strstr($cardType, '港澳居民居住证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isHKMTResidenceCard($idCard),
                'msg' => '港澳居民居住证不合法',
                'cardType' => 9,
            ];
        }
        if (strstr($cardType, '台湾居民居住证')) {
            return [
                'valid' => TeamRegexpService::getInstance()->isHKMTResidenceCard($idCard),
                'msg' => '台湾居民居住证不合法',
                'cardType' => 10,
            ];
        }
        return [
            'valid' => false,
            'msg' => '证件类型不支持',
            'cardType' => -1,
        ];
    }

    public function touristMatchSolution($matchParams): array
    {
        $solutionList = [];
        foreach ($matchParams['solutionIdArr'] as $item) {
            $solutionList[$item['solutionId']] = $this->querySolutionById($item['memberId'], $item['solutionId']);
        }
        //获取可预订产品范围
        $date = $matchParams['date'] ?: date('Y-m-d');
        $lidArr = [];
        $solutionAndPoiIdList = [];
        foreach ($solutionList as $id => $solutions) {
            foreach ($solutions as $solution) {
                if (empty($solution)) {
                    continue;
                }
                $solutionAndPoiIdList[$id] = $solution['poiIdList'];
                $lidArr = array_merge($lidArr, $solution['poiIdList']);
            }
        }
        $lidArr = array_values(array_unique($lidArr));
        $lidStr = implode(',', $lidArr);
        $page = 1;
        $size = count($lidArr);
        if ($matchParams['team_type_id']) {
            $teamSupply = 1; //是否为报团计调 1：是 0：否
            $fid = $matchParams['fid'];
            if (empty($fid)) {
                $fid = $matchParams['sid'];
                $teamSupply = 0;
            }
            $teamRuleBz = new TeamRules();
            $keyword = '';
            if ($size == 1) {
                $landInfo = $this->getLandById($lidArr[0]);
                $keyword = $landInfo['data']['title'];
                unset($landInfo);
            }
            $size += 500;
            $result = $teamRuleBz->getProductByTeamTypeId(
                $matchParams['aid'], $fid, $matchParams['team_type_id'], $date, $keyword,
                $page, $size, $teamSupply);
        } else {
            $bookingBiz = new Booking();
            if (!empty($matchParams['fid'])) {
                $result = $bookingBiz->productSearch($matchParams['fid'], $matchParams['aid'], $date, '', $page, $size, 1, $lidStr);
            } else {
                $result = $bookingBiz->productSearch($matchParams['sid'], $matchParams['aid'], $date, '', $page, $size, 2, $lidStr);
            }
        }
        //过滤掉套票 本期不处理套票的
        $list = $result['data'];
        if (!empty($list)) {
            $list = array_values(array_filter($list, function ($item) {
                return $item['p_type'] != 'F';
            }));
        }
        //获取每个方案的匹配结果
        //$matchResult = [];
        $peopleList = [];
        foreach ($matchParams['touristList'] as $item) {
            if ($item['valid'] === false) {
                continue;
            }
            $peopleList[] = [
                'certificateNum' => $item['idCard'],
                'certificateType' => $item['cardTypeInt'],
                'userSort' => 1,
            ];
        }
        $matchCardAndTidMap = [];
        foreach ($matchParams['solutionIdArr'] as $item) {
            $solutionId = intval($item['solutionId']);
            foreach (array_chunk($peopleList, 100) as $peopleListChunk) {
                $javaParams = [
                    'aid' => $matchParams['aid'],
                    'solutionId' => $solutionId,
                    'peopleList' => $peopleListChunk,
                ];
                $matchRes = $this->queryQuickBuyMatchSolution($javaParams);
                if (!empty($matchRes)) {
                    foreach ($matchRes as $row) {
                        if (isset($row['skuId'])) {
                            //一个证件可能匹配多个方案
                            $matchCardAndTidMap[$row['certificateNum']][] = [
                                'tid' => $row['skuId'],
                                'solutionId' => $solutionId,
                            ];
                        }
                    }
                }
                //$matchResult[$solutionId] = $matchRes;
            }
        }
        //结合匹配到的方案加工游客列表和票列表信息
        $newTicketList = [];
        $tidLidMap = [];
        //$ticketProcessStart = microtime(true);
        if (!empty($list)) {
            foreach ($list as $value) {
                foreach ($value['ticket_list'] as $ticket) {
                    $tidLidMap[$ticket['ticket_id']]['lid'] = $value['product_id'];
                    $storage = $ticket['ticket_storage'];
                    if ($ticket['time_share_info']['use_time_share'] == 1) {
                        $storage = $ticket['time_share_info']['total_storage'];
                    }
                    $tidLidMap[$ticket['ticket_id']]['storage'] = $storage == -1 ? 99999999 : $storage;
                }
            }
            foreach ($solutionAndPoiIdList as $solutionId => $lidList) {
                foreach ($list as $value) {
                    if (!empty($lidList) && in_array($value['product_id'], $lidList)) {
                        $newTicketList[$solutionId][] = $value;
                    } else {
                        $newTicketList[$solutionId][] = [];
                    }
                }
            }
        }
        unset($list);
        $retTicketList = [];
        $showLidArr = [];
        $showTidArr = [];
        //如果是演出类的还需要追加上演出的信息
        foreach ($newTicketList as $solutionId => $item) {
            if (empty($item)) {
                continue;
            }
            foreach ($item as $vv) {
                if ($vv['p_type'] == 'H') {
                    $showLidArr[] = $vv['product_id'];
                    foreach ($vv['ticket_list'] as $v) {
                        $showTidArr[] = $v['ticket_id'];
                    }
                }
            }
            $retTicketList[] = [
                'solutionId' => $solutionId,
                'solutionName' => $solutionList[$solutionId][0]['solutionName'],
                'list' => $this->filterEmptyTicketList($item),
            ];
        }
        //查询演出场次信息
        $showInfoMap = $this->getShowInfoMap($showLidArr, $showTidArr, $matchParams['aid'], $matchParams['sid'], $date);
        foreach ($retTicketList as &$item) {
            if (!empty($item['list'])) {
                foreach ($item['list'] as &$vv) {
                    foreach ($vv['ticket_list'] as &$vvv) {
                        $vvv['ticket_id'] = (int)$vvv['ticket_id'];
                        $vvv['showInfo'] = $showInfoMap[$vvv['ticket_id']] ?? [];
                    }
                }
            }
        }
        unset($item, $vv, $vvv, $newTicketList);
        //如果票列表为空 则只处理方案信息
        if (empty($retTicketList)) {
            foreach ($solutionList as $solutionId => $item) {
                $retTicketList[] = [
                    'solutionId' => $solutionId,
                    'solutionName' => $item[0]['solutionName'],
                    'list' => [],
                ];
            }
        }

        foreach ($matchParams['touristList'] as &$item) {
            //初始化为空
            $item['selectedInfo'] = [];
            if (isset($matchCardAndTidMap[$item['idCard']])) {
                foreach ($matchCardAndTidMap[$item['idCard']] as $v) {
                    if (!empty($tidLidMap[$v['tid']]) && $tidLidMap[$v['tid']]['storage'] > 0) {
                        $tid = (int)$v['tid'];
                        $solutionId = (int)$v['solutionId'];
                        $item['selectedInfo'][] = [
                            'selectedTid' => $tid,
                            'solutionId' => $solutionId,
                            'selectedLid' => (int)$tidLidMap[$v['tid']]['lid'],
                        ];
                    }
                }
            }
        }
        unset($item, $matchCardAndTidMap, $tidLidMap);
        return [
            'touristList' => $matchParams['touristList'],
            'ticketList' => $retTicketList,
            'matchResult' => [],
            'solutionIdArr' => $matchParams['solutionIdArr'],
        ];
    }

    public function downloadExcel()
    {
        $title = ['姓名(必填)', '证件类型(必填)', '证件号码(必填)', '手机号'];
        $subject = '团单快捷购票游客证件信息模板';
        $objPHPExcel = new \PHPExcel();
        $col = ['A', 'B', 'C', 'D'];
        try {
            $objPHPExcel->getActiveSheet()->setTitle($subject);
            //获取第一行
            $titleRow = [];
            foreach ($col as $l) {
                $titleRow[] = $l . '1';
                $objPHPExcel->getActiveSheet()->getColumnDimension($l)->setWidth(20);
            }
            //第一行赋值 抬头
            for ($a = 0; $a < count($title); $a++) {
                //超出处理了
                if (!isset($titleRow[$a])) {
                    continue;
                }
                $objPHPExcel->setActiveSheetIndex(0)->setCellValue($titleRow[$a], $title[$a]);
            }
            // 构建下拉列表的数据有效性数组
            for ($i = 2; $i <= 1000; $i++) {
                $cellCoordinate = 'B' . $i;
                $validation = $objPHPExcel->getActiveSheet()->getCell($cellCoordinate)->getDataValidation();
                $validation->setType(\PHPExcel_Cell_DataValidation::TYPE_LIST);
                //$validation->setErrorStyle(\PHPExcel_Cell_DataValidation::STYLE_STOP);
                //$validation->setAllowBlank(false);
                $validation->setShowInputMessage(true);
                $validation->setShowErrorMessage(true);
                $validation->setShowDropDown(true);
                //$validation->setErrorTitle('输入错误');
                //$validation->setError('请选择列表中的一个值。');
                //$validation->setPromptTitle('选择值');
                //$validation->setPrompt('请选择一个值。');
                $validation->setFormula1('"身份证,护照,军官证,回乡证,台胞证,外国人永久居留证,港澳通行证,台湾通行证,港澳居民居住证,台湾居民居住证"'); // 列表值用逗号分隔
            }
            //设置证件号为文本格式
            $objPHPExcel->setActiveSheetIndex(0)->getStyle('C')->getNumberFormat()->setFormatCode(\PHPExcel_Style_NumberFormat::FORMAT_TEXT);
            $objPHPExcel->setActiveSheetIndex(0);
            //输出表格
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename=' . $subject . '-' . date('Ymd') . '.xlsx');
            header('Cache-Control: max-age=0');
            $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
            $objWriter->save('php://output');
        } catch (\Exception $e) {
            pft_log('TeamOrderQuickBuyService', $e->getMessage());
        }
    }

    public function getShowInfoMap(array $showLidArr, array $showTidArr, int $aid, int $sid, string $date): array
    {
        $javaAPi = new Land();
        $productInfo = $javaAPi->queryLandMultiQueryById($showLidArr);
        $lidVenusIdMap = [];
        if (!empty($productInfo)) {
            foreach ($productInfo as $item) {
                $lidVenusIdMap[$item['id']] = $item['venus_id'];
            }
        }
        $condition = [
            'applyLimit' => 1,
            'landFlag' => 0,
            'productFlag' => 1,
            'landFFlag' => 1,
        ];
        $javaApi = new Ticket();
        $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('apply_did,landid,title,tprice,pay,id',
            'id', '', 'zone_id,tourist_info', false, null, $showLidArr, null, null, $condition);
        $data = [];
        foreach ($ticketArr as $ticketInfo) {
            $data[$ticketInfo['ticket']['landid']][] = [
                'apply_did' => $ticketInfo['ticket']['apply_did'],
                'landid' => $ticketInfo['ticket']['landid'],
                'title' => $ticketInfo['ticket']['title'],
                'tprice' => $ticketInfo['ticket']['tprice'],
                'pay' => $ticketInfo['ticket']['pay'],
                'tid' => $ticketInfo['ticket']['id'],
                'pid' => $ticketInfo['product']['id'],
                'zone_id' => $ticketInfo['land_f']['zone_id'],
                'tourist_info' => $ticketInfo['land_f']['tourist_info'],
            ];
        }
        $sellerModel = new Reseller();
        $ret = [];
        foreach ($data as $lid => $ticketList) {
            foreach ($ticketList as $item) {
                if (in_array($item['tid'], $showTidArr)) {
                    if ($sid == $aid) {
                        $topSellerId = $sid;
                    } else {
                        $topSellerId = $sellerModel->getTopResellerId($sid, $aid, $item['pid']);
                        $topSellerId = $topSellerId ?: $sid;
                    }
                    $ret[$item['tid']] = $this->getRoundList($lidVenusIdMap[$item['landid']] ?? 0, $date, $topSellerId);
                }
            }
        }
        return $ret;
    }

    //证件类型 1:身份证 2:护照 3:军官证 4:回乡证 5:台胞证 6:外国人永久居留证
    // 7:港澳通行证 8:台湾通行证 9:港澳居民居住证 10:台湾居民居住证 99:其他
    /**
     * @throws \Exception
     */
    public function cardVerify(string $idCard, int $cardType)
    {
        $cardTypeAndMethod = [
            1 => 'isIdCard',
            2 => 'isNotEmpty',
            3 => 'isNotEmpty',
            4 => 'isBackHomeCard',
            5 => 'isTaiWanCompatriotCard',
            6 => 'isForeignIdCard',
            7 => 'isHKMPassportCard',
            8 => 'isTaiWanPassportCard',
            9 => 'isHKMTResidenceCard',
            10 => 'isHKMTResidenceCard',
            99 => 'isNotEmpty',
        ];
        if (empty($idCard)) {
            throw new \Exception('证件号码不能为空');
        }
        if (!isset($cardTypeAndMethod[$cardType])) {
            throw new \Exception('不支持的证件类型请自行校验');
        }
        $method = $cardTypeAndMethod[$cardType];
        return TeamRegexpService::getInstance()->$method($idCard);
    }

    public function getRoundList($venueId, $date, $topSellerId): array
    {
        $roundList = [];
        $showBiz = new Show();
        $date = date('Y-m-d', strtotime($date));
        $tmpList = $showBiz->getRoundList($venueId, $date, $topSellerId);
        if ($tmpList['code'] == 0) {
            return [];
        }
        foreach ($tmpList['data'] as $item) {
            $roundList[] = [
                'id' => $item['id'],
                'round_name' => $item['round_name'],
                'bt' => $item['bt'],
                'et' => $item['et'],
                'venus_id' => $item['venus_id'],
                'use_date' => $item['use_date'],
                'storage' => $item['storage'],
                'area_storage' => $item['area_storage'],
            ];
        }
        return $roundList;
    }

    private function filterEmptyTicketList($array): array
    {
        if (empty($array)) {
            return [];
        }
        foreach ($array as $key => $item) {
            if (empty($item['ticket_list'])) {
                unset($array[$key]);
            }
        }
        return array_values($array);
    }
}