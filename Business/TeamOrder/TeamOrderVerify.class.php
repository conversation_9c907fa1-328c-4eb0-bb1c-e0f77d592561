<?php
/**
 * 报团审核模块
 */

namespace Business\TeamOrder;

use Business\Base;
use Business\JavaApi\Order\Query\OrderAidsSplitQuery;
use Business\JavaApi\Order\Query\OrderDetailQuery;
use Business\JavaApi\Product\EvoluteGroupSpecialityService;
use Business\JavaApi\Product\Ticket;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\TeamOrder\Booking as BookingBiz;
use Library\Constants\OrderConst;
use Library\Container;
use Library\MessageNotify\PFTSMSInterface;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Library\Util\Team\TeamUtil;
use Model\Member\Member;
use Model\Order\OrderTools;
use Model\Order\TeamOrderSearch;
use Model\TeamOrder\Verify as VerifyModel;
use Model\Product\Area;
use Process\Resource\AreaProcess;
use Business\Member\Member as MemberBiz;

class TeamOrderVerify extends Base
{

    private $_verifyModel;
    private $_status = [
        0 => '未审核',
        1 => '同意',
        2 => '拒绝',
        3 => '取消',
    ];

    public function __construct()
    {
        $this->_verifyModel = new VerifyModel();
    }

    /**
     * 检测分销商下单是否需要审核
     * @param integer $sid 原始供应商id
     * @param integer $fid 分销商id(上级供应商id)
     * @param integer $memberId 购买者id
     * @param array $orderData 下单参数
     *
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-22
     *
     */
    public function checkOrderReview($sid, $fid, $memberId, $orderData)
    {
        if (!$sid || !$fid || !$orderData || !$memberId) {
            return $this->returnData(204, '参数错误');
        }
        //加子单的会有团单号
        $teamOrderId = $orderData['teamOrderId'] ?? 0;
        if ($teamOrderId) {
            $stashInfo = $this->_verifyModel->getTeamorderStashByOrderNum($teamOrderId);
            if ($stashInfo) {
                return $this->returnData(204, '该团单有在审核中的申请，请勿重新申请');
            }
        }
        //获取出修改的票信息
        $tickets = [];
        $tidArr = [];
        $opOrderNumArr = [];
        foreach ($orderData['orderData'] as $orderNum => $ticketInfo) {
            $opOrderNumArr[] = $orderNum;
            foreach ($ticketInfo as $tid => $item) {
                $tidArr[] = $tid;
                $tickets[$tid] = $item;
            }
        }
        //通过tid获取pid
        $ticketApi = new Ticket();
        $ticketInfo = $ticketApi->queryTicketInfoByIds($tidArr);
        if ($ticketInfo['code'] != 200 || empty($ticketInfo['data'])) {
            return $this->returnData(202, '下单产品票信息有误');
        }
        $firstLevelFid = []; //一级分销商数组
        $evoluteInfoData = []; //下单完整分销链信息
        $isAudit = false; //是否需要报团审核
        //调用java接口获取对于的分销层级关系
        $evoluteApi = new EvoluteGroupSpecialityService();
        $hasBrokenChain = false;//是否存在断开分销链的情况
        foreach ($ticketInfo['data'] as $value) {
            //调用java接口获取对于的分销层级关系
            $evoluteInfo = $evoluteApi->queryEvoluteLvl($memberId, $fid, $value['uuJqTicketDTO']['pid']);
            if ($evoluteInfo['code'] != 200 || empty($evoluteInfo['data'])) {
                $hasBrokenChain = true;
                continue;
            }
            $evoluteInfoData[$value['uuJqTicketDTO']['id']] = $evoluteInfo['data'];
            //根据等级作为key值
            $evoluteInfo = array_key($evoluteInfo['data'], 'lvl');
            $firstLevelFid[] = $evoluteInfo[1]['fid']; //一级分销商id
        }
        if ($hasBrokenChain) {
            //由于下完单上面的接口存在分销链断开的情况，需要兼容下查询一级分销商id和顶级供应商id
            $orderSplitApi = new OrderAidsSplitQuery();
            $splitInfo = $orderSplitApi->getOrderDistributionChain($opOrderNumArr);
            if ($splitInfo['code'] != 200 || empty($splitInfo['data'])) {
                TeamUtil::info([
                    'tag' => 'modifyOrderReview',
                    'args' => func_get_args(),
                    'splitInfo' => $splitInfo,
                ]);
            } else {
                foreach ($splitInfo['data'] as $splitItem) {
                    if ($splitItem['applyDid'] == $splitItem['sellerid']) {
                        $firstLevelFid[] = $splitItem['buyerid'];
                        //$applyDid = $splitItem['applyDid'];
                        $tid = key($orderData['orderData'][$splitItem['orderid']]);
                        $evoluteInfoData[$tid][] = [
                            'fid' => $splitItem['buyerid'],
                            'lvl' => $splitItem['level'],
                            'sid' => $splitItem['sellerid'],
                        ];
                    }
                }
            }

        }
        //判断下单是否需要审核
        foreach ($firstLevelFid as $value) {
            $config = $this->getConfig($sid, $value, 1, $tidArr);
            if ($config && ($config['fids'] || in_array('all',
                        $config['fids']) || $config['group_ids'] || in_array('all',
                        $config['group_ids'])) && ($config['tids'] || in_array('all', $config['tids']))) {
                $isAudit = true;
                break;
            }
        }
        if (!$isAudit) {
            return ['code' => 200, []];
        }
        $teamDiscountInfo = empty($orderData['teamDiscountInfo']) ? [] : json_decode(html_entity_decode($orderData['teamDiscountInfo']), true);
        $orderData['fid'] = $fid;
        $driverInfo = empty($orderData['driverInfo']) ? [] : $orderData['driverInfo']; //司机信息
        $synchronous = $orderData['synchronous'];
        $voucherType = $orderData['voucherType'] ?? 1;
        $moreCredentials = $orderData['moreCredentials'] ?: [];
        $moreTouristCredentials = $orderData['moreTouristCredentials'] ?: [];
        $orderIdCardTickets = $orderData['orderIdCardTickets'] ?: [];
        $timeShareInfo = $orderData['timeShareInfo'] ?: [];
        $extContent = [
            'driver' => $driverInfo,
            'synchronous' => $synchronous,
            'voucherType' => $voucherType,  //取票人身份证类型
            'moreCredentials' => $moreCredentials,  //取票人更多信息
            'moreTouristCredentials' => $moreTouristCredentials, //实名制人更多信息
            'orderIdCardTickets' => $orderIdCardTickets,   //取票人身份证tid过滤数组
            'teamDiscountInfo' => json_encode($teamDiscountInfo, JSON_UNESCAPED_UNICODE), //团单优惠金额
            'timeShareInfo' => json_encode(json_decode(html_entity_decode($timeShareInfo), true)), //分时预约信息
            'playTimeArr' => json_encode($orderData['playTimeArr']), //游玩日期
        ];
        $extContent = json_encode($extContent, JSON_UNESCAPED_UNICODE);
        $saveData = $this->_paramsConversion($orderData);
        $lastId = $this->_verifyModel->addTeamOrderStash($saveData['sid'], $saveData['fid'],
            $saveData['group_products'], $saveData['play_time'], $saveData['team_num'], $saveData['guest_source'], $saveData['name'],
            $saveData['mobile'], $saveData['paymode'], $saveData['mome'], $saveData['guide'], $saveData['id_card'], $saveData['book_type'],
            $saveData['show_data'], $saveData['order_data'], $teamOrderId, 1, $saveData['is_lodging'], $saveData['hotel'], $memberId,
            json_encode($evoluteInfoData, JSON_UNESCAPED_UNICODE), $orderData['teamTypeId'], $extContent, $orderData['orderIdCard']);
        if (!$lastId) {
            return $this->returnData(204, '申请失败', []);
        }
        //如果有团单附件则写入
        if (!empty($orderData['teamAttachInfo'])) {
            $insAttach = [
                'team_stash_id' => $lastId,
                'team_order_id' => $teamOrderId,
                'attach_info' => json_encode($orderData['teamAttachInfo'], JSON_UNESCAPED_UNICODE),
            ];
            TeamAttachService::getInstance()->addTeamAttach($insAttach);
        }
        $memberBiz = new MemberBiz();
        if (1 & $config['buyer_notice']) {
            //分销商申请下单成功后通知供应商
            $memberMobile = $memberBiz->getInfo($saveData['sid']);
            $this->sendSMS($saveData['sid'], '尊敬的客户，您有一笔新的分销商报团下单申请（申请单号）' . $lastId . '，请及时处理。',
                $memberMobile['mobile'], '分销商下单成功报团申请单');
        }
        $title = "尊敬的客户，您有一笔新的分销商报团下单申请（申请单号）' . $lastId . '，请及时处理。";
        //判断下是多级分销下单还是一级分销下单 1:多级分销 0：一级分销
        $orderLevelType = $sid == $fid ? 0 : 1;
        //发送微信通知
        $app = new Application();
        $order = $app->order;
        $order->sendTeamOrderWxMessage($sid, $title, $memberId, $saveData['team_num'], $orderData['teamOrderId'] ?? '', $saveData['paymode'], "",
            $orderLevelType);
        return ['code' => 201, 'data' => ['audit_id' => $lastId]];
    }

    /**
     * 报团订单修改申请审核
     * @param integer $sid 供应商id
     * @param integer $fid 分销商id
     * @param array $ticketArr 修改门票的信息  订单下门票的增加数量及原有票数    ['ordernum' => ['179973'=>['add'=> 2, 'old' =>1]],'ordernum' => ['179974'=>['add'=> 2, 'old' =>1]]]
     * @param integer $type 处理类型 0 修改订单 1 取消订单
     * @param integer $mid 当前登录用户id
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-22
     *
     */
    public function modifyOrderReview($sid, $fid, $ticketArr, $teamOrderNum, $type, $mid)
    {
        if (!$sid || !$fid || !$ticketArr || !$teamOrderNum) {
            return $this->returnData(204, '参数错误');
        }

        $stashInfo = $this->_verifyModel->getTeamorderStashByOrderNum($teamOrderNum);
        if ($stashInfo) {
            return $this->returnData(204, '该团单有在审核中的申请，请勿重新申请');
        }
        //获取出修改的票信息
        $idCardArr = [];
        $tmpIdCardArr = [];
        $orderType = 2; //修改类型  1 下单 2 修改门票审核(加票) 3 退票 4 通知配置 5:取消订单
        $numMapping = [];
        //通过团单号获取订单信息 存入申请表中
        $tidArr = [];
        $tickets = [];
        $ticketEditType = [];
        $opOrderNumArr = [];
        if (!$type) {
            foreach ($ticketArr as $orderNum => $ticketInfo) {
                $opOrderNumArr[] = $orderNum;
                $tidArr[] = key($ticketInfo);
                foreach ($ticketInfo as $tid => $item) {
                    if (!empty($item['tourist_info'])) {
                        $tmpIdCardArr[$tid] = $item['tourist_info'];
                        unset($item['tourist_info']);
                    }
                    if ($item['add'] == 0) {
                        continue;
                    }
                    $ticketEditType[$tid] = 1; //修改类型 1加票 2:减票
                    if ($item['add'] < 0) {
                        $orderType = 3;
                        $ticketEditType[$tid] = 2;
                    }
                    $tickets[$tid] = $item;
                    //$numMapping[$orderNum] = $item['old'] + $item['add'];
                }
            }
        } else {
            foreach ($ticketArr as $orderNum => $ticketInfo) {
                $tmpTid = key($ticketInfo);
                $opOrderNumArr[] = $orderNum;
                $tidArr[] = $tmpTid;
                $ticketEditType[$tmpTid] = 2;
                foreach ($ticketInfo as $tid => $item) {
                    $tickets[$tid] = $item;
                }
                //$numMapping[$sonOrderData[$tmpTid]['son_ordernum']] = $ticketInfo['old'] + $ticketInfo['add'];
            }
            $orderType = 5;
            //$tickets = $ticketArr;
        }
        if (empty($tidArr)) {
            return $this->returnData(204, '修改数据有误');
        }
        $teamOrderModel = new \Model\Order\TeamOrderSearch();
        $sonOrderData = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamOrderNum, false, false, 'son_ordernum, lid, tid, playtime');
        $sonOrderData = array_filter($sonOrderData, function ($item) use ($tidArr, $opOrderNumArr) {
            return !in_array($item['tid'], $tidArr) || in_array($item['son_ordernum'], $opOrderNumArr);
        });
        //如果用tid作为key 要剔除掉 同笔团单下相同tid且非要操作的订单的子单（可以通过加子票的方式产生）
        $sonOrderData = array_column($sonOrderData, null, 'tid');
        //初始化numMapping
        foreach ($ticketArr as $orderNum => $ticketInfo) {
            foreach ($ticketInfo as $tid => $item) {
                $numMapping[$orderNum] = $item['old'] + $item['add'];
            }
        }
        //通过tid获取pid
        $ticketApi = new Ticket();
        $ticketInfo = $ticketApi->queryTicketInfoByIds($tidArr);
        if ($ticketInfo['code'] != 200 || empty($ticketInfo['data'])) {
            return $this->returnData(202, '下单产品票信息有误');
        }
        $evoluteInfoData = [];
        //调用java接口获取对于的分销层级关系
        $evoluteApi = new EvoluteGroupSpecialityService();
        $applyDid = 0;
        $firstLevelFid = [];
        $hasBrokenChain = false;//是否存在断开分销链的情况
        foreach ($ticketInfo['data'] as $value) {
            //调用java接口获取对于的分销层级关系
            $evoluteInfo = $evoluteApi->queryEvoluteLvl($fid, $sid, $value['uuJqTicketDTO']['pid']);
            if ($evoluteInfo['code'] != 200 || empty($evoluteInfo['data'])) {
                //如果分销链断开会查询不到结果 使用其他接口兼容下
                $hasBrokenChain = true;
                continue;
            }
            $evoluteInfoData[$value['uuJqTicketDTO']['id']] = $evoluteInfo['data'];
            //根据等级作为key值
            $evoluteInfo = array_key($evoluteInfo['data'], 'lvl');
            $firstLevelFid[] = $evoluteInfo[1]['fid']; //一级分销商id
            $applyDid = $evoluteInfo[0]['fid']; //一级分销商id
        }
        if ($hasBrokenChain) {
            $evoluteInfoData = [];
            //由于下完单上面的接口存在分销链断开的情况，需要兼容下查询一级分销商id和顶级供应商id
            $orderSplitApi = new OrderAidsSplitQuery();
            $splitInfo = $orderSplitApi->getOrderDistributionChain($opOrderNumArr);
            if ($splitInfo['code'] != 200 || empty($splitInfo['data'])) {
                TeamUtil::info([
                    'tag' => 'modifyOrderReview',
                    'args' => func_get_args(),
                    'splitInfo' => $splitInfo,
                ]);
            } else {
                foreach ($splitInfo['data'] as $splitItem) {
                    if ($splitItem['applyDid'] == $splitItem['sellerid']) {
                        $firstLevelFid[] = $splitItem['buyerid'];
                        $applyDid = $splitItem['applyDid'];
                        $tid = key($ticketArr[$splitItem['orderid']]);
                        $evoluteInfoData[$tid][] = [
                            'fid' => $splitItem['buyerid'],
                            'lvl' => $splitItem['level'],
                            'sid' => $splitItem['sellerid'],
                        ];
                    }
                }
            }
        }
        $teamRulesBz = new TeamRules();
        foreach ($tidArr as $item) {
            $checkRes = $teamRulesBz->teamOrderChangeJudge($applyDid, $item, $sonOrderData[$item]['playtime'], $ticketEditType[$item], $teamOrderNum, $numMapping);
            if ($checkRes['code'] != 200) {
                return $this->returnData(204, $checkRes['msg']);
            }
            //allow  数量是否允许修改 1：允许  2：不允许
            if ($checkRes['data']['allow'] != 1) {
                return $this->returnData(204, $checkRes['msg']);
            }
        }
        $firstLevelFid = array_unique($firstLevelFid);
        //判断下单是否需要审核
        $isAudit = false;
        foreach ($firstLevelFid as $value) {
            //获取出分销商的审核配置
            $config = $this->getConfig($applyDid, $value, $orderType, $tidArr);
            if (
                ($config['fids']
                    || in_array('all', $config['fids'])
                    || $config['group_ids']
                    || in_array('all', $config['group_ids'])
                )
                && ($config['tids'] || in_array('all', $config['tids']))
            ) {
                $isAudit = true;
                break;
            }
        }
        //判断订单是否能通过审核
        $orderData = $teamOrderModel->getMainOrderInfoByOrderNum($teamOrderNum);
        if ($isAudit) {
            $tmpArr = [];
            $tmpArr1 = [];
            $tmpTicketIdArr = [];
            $numMapping = [];
            $model = false;  //操作模式 true ：取消模式 false 修改模式
            foreach ($sonOrderData as $item) {
                if (!isset($tickets[$item['tid']]['add'])) {
                    continue;
                }
                if (in_array($orderType, [3, 5]) && $tickets[$item['tid']]['add'] < 0
                    && abs($tickets[$item['tid']]['add']) == $tickets[$item['tid']]['old']) {
                    //取消订单记录
                    $tmpTicketIdArr[] = $item['tid'];
                    $tmpArr[$item['tid']]['tnum'] = 0;
                    $tmpArr[$item['tid']]['reduce_num'] = $tickets[$item['tid']]['old'];
                    $tmpArr[$item['tid']]['old_num'] = $tickets[$item['tid']]['old'];
                    $tmpArr1[$item['lid']][$item['tid']] = 0;
                    $numMapping[$item['son_ordernum']] = 0;
                    $model = true;
                } elseif (in_array($orderType, [3, 5]) && $tickets[$item['tid']]['add'] < 0
                    && abs($tickets[$item['tid']]['add']) != $tickets[$item['tid']]['old']) {
                    //减少票数
                    $tmpTicketIdArr[] = $item['tid'];
                    $tmpArr[$item['tid']]['tnum'] = $tickets[$item['tid']]['add'] + $tickets[$item['tid']]['old'];
                    $tmpArr[$item['tid']]['reduce_num'] = abs($tickets[$item['tid']]['add']);
                    $tmpArr[$item['tid']]['old_num'] = $tickets[$item['tid']]['old'];
                    $tmpArr1[$item['lid']][$item['tid']] = $tmpArr[$item['tid']]['tnum'];
                    if (!empty($tmpIdCardArr[$item['tid']])) {
                        $idCardArr[$item['tid']] = $tmpIdCardArr[$item['tid']];
                    }
                    $numMapping[$item['son_ordernum']] = $tmpArr[$item['tid']]['tnum'];
                } elseif ($orderType == 2 && $tickets[$item['tid']]['add'] > 0) {
                    //增加门票记录
                    $tmpArr[$item['tid']]['tnum'] = $tickets[$item['tid']]['add'] + $tickets[$item['tid']]['old'];
                    $tmpArr[$item['tid']]['add_num'] = $tickets[$item['tid']]['add'];
                    $tmpArr[$item['tid']]['old_num'] = $tickets[$item['tid']]['old'];
                    $tmpArr1[$item['lid']][$item['tid']] = $tmpArr[$item['tid']]['tnum'];
                    if (!empty($tmpIdCardArr[$item['tid']])) {
                        $idCardArr[$item['tid']] = $tmpIdCardArr[$item['tid']];
                    }
                    $numMapping[$item['son_ordernum']] = $tmpArr[$item['tid']]['tnum'];
                }
            }
            if (!$tmpArr1) {
                return $this->returnData(204, '您未做任何修改！请重新操作');
            }
            $TeamLimitBiz = new \Business\TeamOrder\TeamLimit();
            //团队限制逻辑
            $checkTeamLimit = $TeamLimitBiz->checkTeamOrderEditLimit($teamOrderNum, $sid, $fid, $numMapping, $model);
            if ($checkTeamLimit['is_error']) {
                return $this->returnData(205, $checkTeamLimit['error_info']);
            }
            $orderData['orderData'] = $tmpArr1;
            $orderData['showData'] = [];
            $orderData['fid'] = $sid; //上级供应商
            $orderData['sourceId'] = $applyDid; //原始供应商
            $orderData['idCard'] = !empty($idCardArr) ? $idCardArr : [];
            //对于通过加子单方式 虽然走的是修改审核判断 但是后续审核通过需要走下单模式
            $orderData['type'] = $orderType;
            $saveData = $this->_paramsConversion($orderData);
            $saveData['group_products'] = json_encode($tmpArr);

            $lastId = $this->_verifyModel->addTeamOrderStash(
                $saveData['sid'], $saveData['fid'], $saveData['group_products'], $saveData['play_time'], $orderData['tnum'],
                $saveData['guest_source'], $saveData['name'], $saveData['mobile'], $saveData['paymode'], $saveData['mome'],
                $saveData['guide'], $saveData['id_card'], $saveData['book_type'], $saveData['show_data'], $saveData['order_data'],
                $teamOrderNum, $orderData['type'], $orderData['is_lodging'], $orderData['hotel'], $fid,
                json_encode($evoluteInfoData, JSON_UNESCAPED_UNICODE), $orderData['team_type_id']
            );

            if (!$lastId) {
                return $this->returnData(204, '申请失败', []);
            }

            $memberBiz = new MemberBiz();
            if ($type == 0 && (2 & $config['buyer_notice'])) {
                //分销商申请增加门票
                $memberMobile = $memberBiz->getInfo($applyDid);
                $title = $orderType == 2 ? '增加' : '减少';
                $this->sendSMS($saveData['sid'],
                    '尊敬的客户，您有一笔新的分销商报团订单修改申请（申请单号）' . $lastId . '，请及时处理。',
                    $memberMobile['mobile'], '申请团单' . $title . '门票');
            }

            if ((4 & $config['sell_notice']) && $mid == $saveData['sid']) {
                //供应商修改报团申请
                $memberMobile = $memberBiz->getInfo($fid);
                $this->sendSMS($saveData['sid'], '尊敬的客户，供应商修改了您的报团下单申请（申请单号）' . $lastId . '，请及时确认。',
                    $memberMobile['mobile'], '修改报团申请单');
            }

            if ($type == 1 && (4 & $config['buyer_notice'])) {
                //分销商申请退票
                $memberMobile = $memberBiz->getInfo($applyDid);
                $this->sendSMS($saveData['sid'],
                    '尊敬的客户，您有一笔新的分销商报团退票申请（申请单号）' . $lastId . '，请及时处理。',
                    $memberMobile['mobile'], '申请团单退票');
            }

            return ['code' => 201, 'data' => ['audit_id' => $lastId]];
        }

        return ['code' => 200, []];
    }

    /**
     * 报团审核申请
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-22
     *
     */
    private function _paramsConversion($orderData)
    {
        $provinceName = '';
        $cityName = '';
        $countryName = '';

        $area = new Area();
        $result = $area->getAreaCache("area:allArea");
        if (!$result) {
            $result = AreaProcess::childerCity($area);
            $area->setAreaCache("area:allArea", $result);
        }
        foreach ($result as $province) {
            if ($province['area_id'] == $orderData['province']) {
                foreach ($province['_child'] as $city) {
                    if ($city['area_id'] == $orderData['city']) {
                        $provinceName = $province['area_name'];
                        $cityName = $city['area_name'];
                    }
                }
            }
        }
        $cityInfo = $area->getTownList($orderData['city']);
        if ($cityInfo) {
            foreach ($cityInfo as $country) {
                if ($country['id'] == $orderData['country'] || $country['id'] == $orderData['county']) {
                    $countryName = $country['town_name'];
                }
            }
        }
        $params = [];
        $orderDatas = json_encode($orderData['orderData']);
        $showData = json_encode($orderData['showData']);
        $idCardData = json_encode($orderData['idCard']);

        $params['type'] = $orderData['type'] ?: 1;           // 申请类型 1下单 2修改 3退票
        $params['fid'] = $orderData['fid'];  //上级供应商id
        $params['sid'] = $orderData['sourceId']; //原始供应商id
        $params['play_time'] = $params['type'] == 1 ? $orderData['playTime'] : $orderData['playtime'];
        $params['guest_source'] = json_encode([
            $orderData['province'] => $provinceName,
            $orderData['city'] => $cityName,
            $orderData['country'] => $countryName,
        ]);
        $params['name'] = $params['type'] == 1 ? $orderData['orderName'] : $orderData['ordername'];
        $params['mobile'] = $params['type'] == 1 ? $orderData['orderTel'] : $orderData['contacttel'];
        $params['paymode'] = $params['type'] == 1 ? $orderData['payType'] : $orderData['ordertype'];
        $params['mome'] = $orderData['orderMemo'] ?: '';
        $params['guide'] = $orderData['guide'];
        $params['book_type'] = $orderData['bookType'] ?: 1;
        $params['team_num'] = $params['type'] == 1 ? $orderData['number'] : $orderData['tnum'];
        $params['show_data'] = $showData;
        $params['order_data'] = $orderDatas;
        $params['id_card'] = $idCardData;
        $params['is_lodging'] = $orderData['is_lodging'] ?: 0;
        $params['hotel'] = $orderData['hotel'] ?: '';

        //处理门票信息
        if ($orderData['orderData']) {
            foreach ($orderData['orderData'] as $item) {
                if ($params['type'] == 1) {
                    foreach ($item as $tid => $tnum) {
                        $params['group_products'][$tid] = $tnum;
                    }
                }
            }
        }
        $params['group_products'] = json_encode($params['group_products']);

        return $params;
    }

    /**
     * 获取分销商审核配置
     * @param int $sid 供应商id
     * @param int $fid 分销商id
     * @param int $type 获取类型  1 下单审核 2 修改门票审核(加票) 3 退票审核 4 通知配置 5 取消订单
     * @param array $tidArr 门票id数组
     *
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-22
     *
     */
    public function getConfig($sid, $fid, $type = 1, $tidArr = [])
    {
        $result = $this->_verifyModel->getFidConfig($sid, true);

        if (!$result) {
            return [];
        }

        //检测分组
        $groupId = 0;
        //判断分销商是否在新分组中
        $evoluteGroupApi = new \Business\JavaApi\Product\EvoluteGroupUser();
        $userGroupInfoArr = $evoluteGroupApi->queryDistributor($sid, $fid);
        if ($userGroupInfoArr['code'] == 200 && !empty($userGroupInfoArr['data'])) {
            $groupId = $userGroupInfoArr['data']['groupId'];
        }

        $config = [];
        foreach ($result as $key => $value) {
            $config[$key] = $value;
            $config[$key]['fids'] = [];
            $config[$key]['group_ids'] = [];
            $config[$key]['tids'] = [];
            $config[$key]['group_id'] = $groupId;
            //特殊处理判断配置中是否选择全部还是指定分销商
            if ($value['fids'] != '["all"]' && $value['group_ids'] != '["all"]') {
                if (in_array($fid, json_decode($value['fids'], true))) {
                    $config[$key]['fids'] = json_decode($value['fids'], true);
                }
                if (in_array($groupId, json_decode($value['group_ids'], true))) {
                    $config[$key]['group_ids'] = json_decode($value['group_ids'], true);
                }
            } elseif ($value['fids'] != '["all"]' && $value['group_ids'] == '["all"]') {
                if (in_array($fid, json_decode($value['fids'], true))) {
                    $config[$key]['fids'] = json_decode($value['fids'], true);
                }
            } elseif ($value['group_ids'] != '["all"]' && $value['fids'] == '["all"]') {
                if (in_array($groupId, json_decode($value['group_ids'], true))) {
                    $config[$key]['group_ids'] = json_decode($value['group_ids'], true);
                }
            } else {
                $config[$key]['fids'] = ['all'];
                $config[$key]['group_ids'] = ['all'];
            }

            if ($value['tids'] != '["all"]') {
                $config[$key]['tids'] = json_decode($value['tids'], true);
            } else {
                $config[$key]['tids'] = ['all'];
            }

            //只取出下单门票对应的下单配置
            if ((($type == 1 && (1 & $value['audit_conf'])) || ($type == 2 && (2 & $value['audit_conf'])) || ($type == 3 && (4 & $value['audit_conf'])) || ($type == 5 && (8 & $value['audit_conf']))) && $tidArr) {
                foreach ($tidArr as $tid) {
                    if (in_array($tid, json_decode($value['tids'], true)) || $value['tids'] == '["all"]') {
                        $config[$key]['tids'] = [$tid];
                        //还要获取出公共的配置项
                        $config[$key]['sell_notice'] = max(array_column($result, 'sell_notice'));
                        $config[$key]['buyer_notice'] = max(array_column($result, 'buyer_notice'));
                        return $config[$key];
                    }
                }
            } elseif ($type == 4 && (1 & $value['sell_notice'] || 2 & $value['sell_notice'] || 4 & $value['sell_notice'] || 1 & $value['buyer_notice'] || 2 & $value['buyer_notice'] || 4 & $value['buyer_notice']) && $tidArr) {
                foreach ($tidArr as $tid) {
                    if (in_array($tid, json_decode($value['tids'], true)) || $value['tids'] == '["all"]') {
                        //取出当前门票配置 通知审核的最大值
                        $config[$key]['sell_notice'] = max(array_column($result, 'sell_notice'));
                        $config[$key]['buyer_notice'] = max(array_column($result, 'buyer_notice'));
                        $config[$key]['tids'] = [$tid];

                        return $config[$key];
                    }
                }
            }
        }

        return [];
    }

    /**
     * 审核统一封装发送短信
     * @param int $memberId 平台用户ID
     * @param string $msg 短信内容
     * @param string $mobile 手机号
     * @param string $smsSign 短信抬头
     * @param string $remark 备注
     *
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-19
     *
     */
    public function sendSMS($memberId, $msg, $mobile, $remark = '', $smsSign = '票付通')
    {
        //$smsLib = new FzZwxSms($mobile);
        if (Helpers::isMobile($mobile)) {
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $result] = $messageServiceApi->dispatchMessageSend(MessageService::V2, PFTSMSInterface::ISDIY, $mobile,
                [$msg], $memberId, '', $remark, $smsSign, '', 1, true);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode([$remark, __METHOD__, [$mobile, [$msg], $memberId, $smsSign], $result], JSON_UNESCAPED_UNICODE));
                }
            } else {
                /** @depreciate 放量结束后删除 */
                $smsLib = SmsFactory::getFactory($mobile);
                $result = $smsLib->customMsg($memberId, $smsSign, $msg, $mobile, '', '', true, $remark);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode([$remark . '.old', __METHOD__, [$mobile, [$msg], $memberId, $smsSign], $result], JSON_UNESCAPED_UNICODE));
                }
            }
            if (isset($result['code']) && $result['code'] == 200) {
                return ['code' => 200, 'data' => [], 'msg' => '短信发送成功'];
            }
        }

        return ['code' => 204, 'data' => [], 'msg' => '短信发送失败'];
    }

    /**
     * 获取报团审核申请列表
     *
     * @param int $aid 登录的用户id
     * @param int $dtype 查询类型 1供应商 2分销商
     * @param string $playTime 游玩日期
     * @param string $identify 搜索条件
     * @param int $sid 搜索的供应商id
     * @param int $fid 搜索的分销商id
     * @param string $applyTime 申请时间
     * @param string $verifyTime 审核时间
     * @param int $page 当前页数
     * @param int $pageSize 每页条数
     * @param int $type 申请类型 1下单 2增加门票 3退票
     * @param int $status 审核状态 0未审核 1同意 2拒绝 3取消 4全部
     *
     * @date 2023/12/29
     * @auther yangjianhui
     * @return array
     */
    public function getTeamorderStashList($sid, $dtype, $playTime, $identify, $aid, $fid, $applyTime,
                                          $verifyTime, $page, $pageSize, $type, $status)
    {
        if (empty($sid)) {
            return $this->returnData(203, "供应商信息有误");
        }

        $result = $this->_verifyModel->getTeamOrderStashList($sid, $dtype, $playTime, $identify, $aid,
            $fid, $applyTime, $verifyTime, $page, $pageSize, $type, $status);
        //空直接返回
        if (empty($result['list'])) {
            $return = [
                'list' => [],
                'page' => $page,
                'page_size' => $pageSize,
                'total_page' => 0,
                'total' => 0,
            ];

            return $this->returnData(200, '', $return);
        }

        $list = $result['list'];
        $total = $result['total'];

        //新版支持转分销多级，这里得做下数据兼容
        $firstLevelArr = [];  //一级分销商id，根据审核记录做分类
        $firstArr = [];  //一级分销商id数组
        foreach ($list as $value) {
            if (!empty($value['evolute_info']) && $value['evolute_info'] != 'null') {
                $evoluteInfo = json_decode($value['evolute_info'], true);
                foreach ($evoluteInfo as $key => $item) {
                    $tmpData = array_key($item, 'lvl');
                    $firstLevelArr[$value['id']][] = $tmpData[1]['fid'];
                    $firstArr[] = $tmpData[1]['fid'];
                }
                $firstLevelArr[$value['id']] = array_unique($firstLevelArr[$value['id']]); //去重一下
            }
        }
        $memModel = new Member('slave');
        $nameMap = $memModel->getMemberInfoByMulti(array_merge(array_column($list, 'sid'),
            array_column($list, 'fid'), $firstArr), 'id', 'id,dname', 1);
        foreach ($list as &$item) {
            if ($item['create_time']) {
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            }
            if ($item['update_time']) {
                $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
            }
            $item['guest_source'] = implode(' ', array_values(json_decode($item['guest_source'], true)));
            $item['dname'] = $nameMap[$item['sid']]['dname']; //分销商报团申请管理展示的审核方
            $item['fname'] = $nameMap[$item['fid']]['dname']; //供应商报团审核管理页面申请方名称
            //存在这个值，说明是新的报团分销，需要做下参数调整
            if (!empty($item['evolute_info']) && $item['evolute_info'] != 'null') {
                $item['dname'] = $nameMap[$item['fid']]['dname']; //分销商报团申请管理展示的审核方
                $item['fname'] = "转分销";//供应商报团审核管理页面申请方名称
                //审核数据来源多个供应商下单的，这边不做提交审核者名称展示（供应商报团审核管理页面申请方名字不展示，直接展示转分销）
                if (count($firstLevelArr[$item['id']]) == 1 && $firstLevelArr[$item['id']][0] == $item['order_mid']) {
                    $item['fname'] = $nameMap[$firstLevelArr[$item['id']][0]]['dname'];
                }
            }
        }
        unset($item);

        $return = [
            'list' => array_values($list),
            'page' => $page,
            'page_size' => $pageSize,
            'total_page' => ceil($total / $pageSize),
            'total' => $total,
        ];

        return $this->returnData(200, '', $return);
    }

    /**
     * 获取报团审核申请详情
     *
     * @param int $sid 供应商id
     * @param int $id 审核id
     * @param int $type 查看类型 1：供应商产看 2：分销商查看
     *
     * @return array
     * @date 2023/12/29
     * @auther yangjianhui
     * @return array
     */
    public function getTeamorderStashById($sid, $id, $type)
    {
        if (!$id) {
            return $this->returnData(203, '缺少必要参数');
        }

        $field = 'id,sid,fid,group_products,play_time,team_ordernum,guest_source,name,
        mobile,paymode,mome,guide,FROM_UNIXTIME(create_time) as create_time,type,status,
        book_type,id_card,show_data,order_data,team_num,is_lodging,hotel,order_mid,evolute_info,
        team_type_id,ext_content,order_id_card,audit_remark,audit_fail_remark';
        $result = $this->_verifyModel->getTeamorderStashById($id, $field);
        if (!$result) {
            return $this->returnData(203, '暂无数据');
        }
        $extContent = $result['ext_content'] ? json_decode($result['ext_content'], true) : [];
        unset($result['ext_content']);
        $result['voucherType']            = $extContent['voucherType'] ?? 1;
        $result['moreCredentials']        = $extContent['moreCredentials'] ?? [];
        $result['orderIdCardTickets']     = $extContent['orderIdCardTickets'] ?? [];
        $result['moreTouristCredentials'] = $extContent['moreTouristCredentials'] ?? [];
        $result['team_discount_info']     = empty($extContent['teamDiscountInfo']) ? [] : json_decode($extContent['teamDiscountInfo'], true);
        $result['time_share_info']        = $extContent['timeShareInfo'] ?? "";
        $playTimeArr                      = $result['play_time_arr'] = empty($extContent['playTimeArr']) ? [] : json_decode($extContent['playTimeArr'], true);
        //重组门票信息
        $groupProducts = json_decode($result['group_products'], true);
        $tidArr = [];       //门票id数组
        $tnumArr = [];       //门票对应的数量
        $idCardArr = json_decode($result['id_card'], true);       //被修改的游客身份证信息

        foreach ($groupProducts as $tid => $num) {
            $tidArr[] = $tid;
            //下单
            if ($result['type'] == 1) {
                $tnumArr[$tid] = $num;
            } else {
                $tnumArr[$tid]['tnum'] = $num['tnum'];
                $tnumArr[$tid]['add_num'] = $num['add_num'] ?: 0;
                $tnumArr[$tid]['reduce_num'] = $num['reduce_num'] ?: 0;
                $tnumArr[$tid]['old_num'] = $num['old_num'];
            }
        }

        //获取供应商及分销商名称
        $ticketModel = new \Model\Product\Ticket();
        $memModel = new Member('slave');
        $nameMap = $memModel->getMemberInfoByMulti([$result['fid'], $result['sid']], 'id', 'id,dname', 1);
        $result['sid_name'] = $nameMap[$result['sid']]['dname'];
        $result['fid_name'] = $nameMap[$result['fid']]['dname'];

        $payTypesArr = load_config('order_pay_mode');
        //下单成功的获取子订单号
        $sonOrderNumArr = [];
        $sonOrderTNumArr = [];
        $tmpOrderNumArr = [];
        //$isAddSubOrder = false;//是否加单 true-是 false-否
        if ($result['team_ordernum']) {
            $teamOrderModel = new TeamOrderSearch();
            $teamOrderList = $teamOrderModel->getSonOrderInfoByMainOrderNum($result['team_ordernum']);
            if ($teamOrderList) {
                $orderModel = new OrderTools();
                //通过子订单号获取出门票对应的票数
                $tmpSonOrderArr = array_column($teamOrderList, 'son_ordernum');
                $sonOrderInfo = $orderModel->getOrderInfo($tmpSonOrderArr, 'tid,tnum');
                foreach ($sonOrderInfo as $item) {
                    if (!isset($sonOrderTNumArr[$item['tid']])) {
                        $sonOrderTNumArr[$item['tid']] = [
                            'tnum' => $item['tnum'],
                            'playtime' => $item['playtime'],
                        ];
                    }
                }

                foreach ($teamOrderList as $item) {
                    $sonOrderNumArr[$item['tid']] = $item['son_ordernum'];
                }
            }
            //获取团单主单信息
            $mainOrderInfo = $teamOrderModel->getMainOrderInfoByOrderNum($result['team_ordernum'], 'ext_content');
            $mainExtContent = empty($mainOrderInfo['ext_content']) ? [] : json_decode($mainOrderInfo['ext_content'], true);
            //获取订单原始身份证信息
            $tmpOrderNumArr = array_values($sonOrderNumArr);
            //获取原始订单门票ID
            $tmpOrderTidArr = array_keys($sonOrderNumArr);
            if (!in_array($tidArr[0], $tmpOrderTidArr)) {
                //待审核的票和原始团单子单里的不一样 证明是加单逻辑
                //$isAddSubOrder = true;
                $tmpOrderTidArr = $tidArr;
                $sonOrderNumArr = [];
            }
        } else {
            $tmpOrderTidArr = $tidArr;
        }
        //获取门票价格
        //$priceAndStorage = TicketApi::getPriceAndStorageByPlayDate(implode(',', $tmpOrderTidArr), $result['fid'],
        //    $result['sid'], $result['play_time']);
        $tPriceBiz = new \Business\Product\Price();
        //这里要做个新旧版本兼容（新：报团产品支持多级分销 旧：报团产品仅一级分销）,evolute_info不为空为新版
        if (!empty($result['evolute_info']) && $result['evolute_info'] != "null") {
            $result['fid_name'] = "转分销";
            //evolute_info数据格式{"1039239": [{"fid": 3267458, "lvl": 1, "pid": 373430, "sid": 6970, "aids": "6970", "sourceid": 6970}, {"fid": 6970, "lvl": 0, "pid": 373430, "sid": 6970, "aids": "", "sourceid": 6970}]}
            $priceAndStorage = [];
            $evoluteInfo = json_decode($result['evolute_info'], true);
            if (empty($evoluteInfo)) {
                return $this->returnData(203, '分销链数据为空，请检查是否断开了分销链');
            }
            foreach ($evoluteInfo as $tid => $value) {
                $tmpEvoluteInfo = array_key($value, 'lvl');
                $sid = $result['sid'];
                $fid = $tmpEvoluteInfo[1]['fid'];
                $fidArr[] = $tmpEvoluteInfo[1]['fid'];
                if ($type == 2) {
                    $sid = $result['fid'];
                    $fid = $result['order_mid'];
                }
                $priceAndStorageRes = $tPriceBiz->buyBatchGet($fid, $sid, $result['play_time'], $tid);
                if ($priceAndStorageRes['code'] == 200 && !empty($priceAndStorageRes['data'])) {
                    $priceAndStorage[$tid] = $priceAndStorageRes['data'][$tid];
                }
            }
            $fidArr = array_unique($fidArr);
            //上级供应商只有一个人，这个时候展示名称
            if (count($fidArr) == 1 && $fidArr[0] == $result['order_mid']) {
                if ($type == 2) {
                    $nameMap = $memModel->getMemberInfoByMulti([$sid], 'id', 'id,dname', 1);
                    $result['fid_name'] = $nameMap[$sid]['dname'];
                } else {
                    $nameMap = $memModel->getMemberInfoByMulti($fidArr, 'id', 'id,dname', 1);
                    $result['fid_name'] = $nameMap[$fidArr[0]]['dname'];
                }
            }
        } else {
            $priceAndStorageRes = $tPriceBiz->buyBatchGet($result['fid'], $result['sid'], $result['play_time'], implode(',', $tmpOrderTidArr));
            if ($priceAndStorageRes['code'] == 200 && !empty($priceAndStorageRes['data'])) {
                $priceAndStorage = $priceAndStorageRes['data'];
            }
        }

        if (empty($priceAndStorage)) {
            return $this->returnData(203, '价格获取错误，请稍后重试');
        }

        $javaApi    = new \Business\CommodityCenter\Ticket();
        $ticketArr  = $javaApi->queryTicketInfoByIds($tmpOrderTidArr, 'title,id,pid,pre_sale', 'id', 'title,imgpath,id,p_type', 'tourist_info');
        $ticketInfo = [];
        $packTid    = [];
        $scenicTid  = [];
        $landToTid  = [];
        foreach ($ticketArr as $ticketInfos) {
            switch ($ticketInfos['land']['p_type']) {
                case 'F':
                    $packTid[] = $ticketInfos['ticket']['id'];
                    break;
                case 'A':
                    $scenicTid[] = $ticketInfos['ticket']['id'];
                    break;
            }
            $landToTid[$ticketInfos['land']['id']][] = $ticketInfos['ticket']['id'];
            $moreCredentials = isset($ticketInfos['ext']['more_credentials']) && $ticketInfos['ext']['more_credentials'] ? json_decode($ticketInfos['ext']['more_credentials'],
                true)['content'] : [];
            $ticketInfo[$ticketInfos['ticket']['id']] = [
                'id' => $ticketInfos['ticket']['id'],
                'ttitle' => $ticketInfos['ticket']['title'],
                'tid' => $ticketInfos['ticket']['id'],
                'pid' => $ticketInfos['ticket']['pid'],
                'title' => $ticketInfos['land']['title'],
                'imgpath' => $ticketInfos['land']['imgpath'],
                'landid' => $ticketInfos['land']['id'],
                'p_type' => $ticketInfos['land']['p_type'],
                'pre_sale' => $ticketInfos['ticket']['pre_sale'],
                'more_credentials' => $moreCredentials ? 1 : 0,
                'more_credential_content' => $moreCredentials,
                'tourist_info' => $ticketInfos['land_f']['tourist_info'],
            ];
        }
        //获取门票信息
        $ticketsLimit = $ticketModel->getTicketInfoMulti($tmpOrderTidArr, 'id,refund_rule,num_modify');

        $oldIdCardArr = [];
        if ($tmpOrderNumArr) {
            $orderToolModel = new OrderTools();
            $touristInfo = $orderToolModel->getOrderTouristInfo($tmpOrderNumArr);
            if (!empty($touristInfo)) {
                foreach ($touristInfo as $key => $value) {
                    if ($value['check_state'] == OrderConst::ORDER_TOURIST_NO_VERIFIED) {
                        $oldIdCardArr[$value['orderid']][] = $value;  //原始的游客身份证信息
                    }
                }
            }
        }

        $ticketInfoArr = [];
        foreach ($tidArr as $tid) {
            $ticketInfoArr[$tid]['orderNum'] = $sonOrderNumArr[$tid] ?: '';
            $ticketInfoArr[$tid]['title'] = $ticketInfo[$tid]['title'];
            $ticketInfoArr[$tid]['ttitle'] = $ticketInfo[$tid]['ttitle'];
            $ticketInfoArr[$tid]['pid'] = $ticketInfo[$tid]['pid'];
            $ticketInfoArr[$tid]['tid'] = $ticketInfo[$tid]['tid'];
            $ticketInfoArr[$tid]['landid'] = $ticketInfo[$tid]['landid'];
            $ticketInfoArr[$tid]['storage'] = $priceAndStorage[$tid]['availableStorage'];
            $ticketInfoArr[$tid]['settlement'] = sprintf("%.2f", $priceAndStorage[$tid]['settlement'] / 100);
            $ticketInfoArr[$tid]['refund_rule'] = $ticketsLimit[$tid]['refund_rule'] == '-1' || $ticketsLimit[$tid]['refund_rule'] == '2' ? false : true; //门票退票属性
            $ticketInfoArr[$tid]['num_modify'] = $ticketsLimit[$tid]['num_modify'];                                     //门票更改数量属性 0只可减少，大于0为增减或减少的上限比例
            if (in_array($result['type'], [2, 3, 5])) {
                $ticketInfoArr[$tid]['tnum'] = $tnumArr[$tid]['tnum'];                                           //增加后订单票数
                $ticketInfoArr[$tid]['add_num'] = $tnumArr[$tid]['add_num'];                                        //申请票数
                $ticketInfoArr[$tid]['old_num'] = $tnumArr[$tid]['old_num'];                                        //订单票数
                $ticketInfoArr[$tid]['reduce_num'] = $tnumArr[$tid]['reduce_num'];                                     //减少票数
                $ticketInfoArr[$tid]['totalMoney'] = $tnumArr[$tid]['add_num'] * $ticketInfoArr[$tid]['settlement'];   //订单增加金额
                if ($tnumArr[$tid]['reduce_num']) {
                    $ticketInfoArr[$tid]['totalMoney'] = '-' . $tnumArr[$tid]['reduce_num'] * $ticketInfoArr[$tid]['settlement'];//订单减少金额
                }
            } else {
                $ticketInfoArr[$tid]['tnum'] = $tnumArr[$tid];
                $ticketInfoArr[$tid]['totalMoney'] = sprintf("%.2f",
                    (int)$priceAndStorage[$tid]['settlement'] * (int)$ticketInfoArr[$tid]['tnum'] / 100);
            }

            if (isset($idCardArr[$tid]) && $idCardArr[$tid]) {
                if ($result['type'] == 1) {
                    foreach ($idCardArr[$tid] as $k => $v) {
                        $ticketInfoArr[$tid]['id_card'][$k]['tourist'] = $v['name'];
                        $ticketInfoArr[$tid]['id_card'][$k]['idcard'] = $v['idcard'];
                        //前端说要字符串
                        $ticketInfoArr[$tid]['id_card'][$k]['voucher_type'] = $v['voucher_type'] ? strval($v['voucher_type']) : '1';
                        //证件照
                        $ticketInfoArr[$tid]['id_card'][$k]['cert_url'] = $v['cert_url'] ?? '';
                    }
                } else {
                    $ticketInfoArr[$tid]['id_card'] = $idCardArr[$tid];
                }
            }

            if (isset($oldIdCardArr[$sonOrderNumArr[$tid]])) {
                $ticketInfoArr[$tid]['old_idcard'] = $oldIdCardArr[$sonOrderNumArr[$tid]];
            }
        }

        $oldTicketInfo = [];
        $packSonData = [];
        if ($packTid) {
            $bookingBiz = new BookingBiz();
            $packSonData = $bookingBiz->handlePackProductSonTicket($packTid);
        }
        $timeShareBiz = new \Business\Order\TimeShareOrder();
        $timeShareRes = [];
        foreach ($landToTid as $key => $value) {
            $tmpPlayTime     = empty($playTimeArr[$key]) ? $result['play_time'] : $playTimeArr[$key];
            $tmpTimeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($value, $tmpPlayTime, 9, true, true);
            $timeShareRes    = $timeShareRes + $tmpTimeShareRes['data'];
        }
        //获取订单扩展信息
        $orderDetailApi = new OrderDetailQuery();
        $detailInfo = $orderDetailApi->getOrderWithDetail($sonOrderNumArr);
        $detailInfo = empty($detailInfo['data']) ? [] : array_column($detailInfo['data'], null, 'tid');
        //处理原始门票信息
        if ($type == 1) {
            $tmpOrderTidArr = $tidArr;
        }
        foreach ($tmpOrderTidArr as $ticketId) {
            $tmpPlayTime = empty($playTimeArr[$ticketInfo[$ticketId]['landid']]) ? $result['play_time'] : $playTimeArr[$ticketInfo[$ticketId]['landid']];
            $oldTicketInfo[$ticketId]['orderNum'] = $sonOrderNumArr[$ticketId] ?: '';
            $oldTicketInfo[$ticketId]['title'] = $ticketInfo[$ticketId]['title'];
            $oldTicketInfo[$ticketId]['ttitle'] = $ticketInfo[$ticketId]['ttitle'];
            $oldTicketInfo[$ticketId]['pid'] = $ticketInfo[$ticketId]['pid'];
            $oldTicketInfo[$ticketId]['tid'] = $ticketInfo[$ticketId]['tid'];
            $oldTicketInfo[$ticketId]['pre_sale'] = $ticketInfo[$ticketId]['pre_sale'];
            $oldTicketInfo[$ticketId]['landid'] = $ticketInfo[$ticketId]['landid'];
            $oldTicketInfo[$ticketId]['storage'] = $priceAndStorage[$ticketId]['availableStorage'];
            $oldTicketInfo[$ticketId]['settlement'] = sprintf("%.2f", $priceAndStorage[$ticketId]['settlement'] / 100);
            $oldTicketInfo[$ticketId]['refund_rule'] = $ticketsLimit[$ticketId]['refund_rule'] == '-1' || $ticketsLimit[$ticketId]['refund_rule'] == '2' ? false : true; //门票退票属性
            $oldTicketInfo[$ticketId]['num_modify'] = $ticketsLimit[$ticketId]['num_modify'];                                     //门票更改数量属性 0只可减少，大于0为增减或减少的上限比例
            $oldTicketInfo[$ticketId]['num'] = $sonOrderTNumArr[$ticketId]['tnum'];
            $oldTicketInfo[$ticketId]['more_credentials'] = $ticketInfo[$ticketId]['more_credentials'];
            $oldTicketInfo[$ticketId]['more_credential_content'] = $ticketInfo[$ticketId]['more_credential_content'];
            $oldTicketInfo[$ticketId]['ext_content'] = empty($detailInfo[$ticketId]['fxDetails']['extContent']) ? "" : json_decode($detailInfo[$ticketId]['fxDetails']['extContent'], true);
            $tmpPackSonData = $packSonData[$ticketId] ?? [];
            $sonTidArr = array_column($tmpPackSonData, 'tid');
            $sonTimeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($sonTidArr, $tmpPlayTime, 9, true);
            foreach ($tmpPackSonData as &$packageTicketItem) {
                $packageTicketItem['time_share_info'] = $sonTimeShareRes['data'][$packageTicketItem['tid']] ?? [];
                $packageTicketItem['ext_content'] = empty($detailInfo[$ticketId]['fxDetails']['extContent']) ? "" : json_decode($detailInfo[$ticketId]['fxDetails']['extContent'], true);
            }
            unset($packageTicketItem);
            $oldTicketInfo[$ticketId]['son_ticket'] = $tmpPackSonData;
            $oldTicketInfo[$ticketId]['p_type'] = $ticketInfo[$ticketId]['p_type'];
            $oldTicketInfo[$ticketId]['tourist_info'] = $ticketInfo[$ticketId]['tourist_info'];
            $oldTicketInfo[$ticketId]['time_share_info'] = $timeShareRes[$ticketId] ?? [];
            if (isset($oldIdCardArr[$sonOrderNumArr[$ticketId]])) {
                $oldTicketInfo[$ticketId]['id_card'] = $oldIdCardArr[$sonOrderNumArr[$ticketId]];
            }
        }

        $orderInfo = [];
        if (!empty($sonOrderNumArr)) {
            foreach ($sonOrderNumArr as $tmpTid => $tmpOrderNum) {
                $tmpPlayTime = empty($playTimeArr[$ticketInfo[$tmpTid]['landid']]) ? $result['play_time'] : $playTimeArr[$ticketInfo[$tmpTid]['landid']];
                $orderInfo[$tmpTid]['orderNum'] = $tmpOrderNum;
                $orderInfo[$tmpTid]['title'] = $ticketInfo[$tmpTid]['title'];
                $orderInfo[$tmpTid]['ttitle'] = $ticketInfo[$tmpTid]['ttitle'];
                $orderInfo[$tmpTid]['pid'] = $ticketInfo[$tmpTid]['pid'];
                $orderInfo[$tmpTid]['tid'] = $ticketInfo[$tmpTid]['tid'];
                $orderInfo[$tmpTid]['pre_sale'] = $ticketInfo[$tmpTid]['pre_sale'];
                $orderInfo[$tmpTid]['landid'] = $ticketInfo[$tmpTid]['landid'];
                $orderInfo[$tmpTid]['playtime'] = $sonOrderTNumArr[$tmpTid]['playtime'];
                $orderInfo[$tmpTid]['storage'] = $priceAndStorage[$tmpTid]['availableStorage'];
                $orderInfo[$tmpTid]['settlement'] = sprintf("%.2f", $priceAndStorage[$tmpTid]['settlement'] / 100);
                $orderInfo[$tmpTid]['refund_rule'] = $ticketsLimit[$tmpTid]['refund_rule'] == '-1' || $ticketsLimit[$tmpTid]['refund_rule'] == '2' ? false : true; //门票退票属性
                $orderInfo[$tmpTid]['num_modify'] = $ticketsLimit[$tmpTid]['num_modify'];                                     //门票更改数量属性 0只可减少，大于0为增减或减少的上限比例
                $orderInfo[$tmpTid]['num'] = $sonOrderTNumArr[$tmpTid]['tnum'];
                $orderInfo[$tmpTid]['more_credentials'] = $ticketInfo[$tmpTid]['more_credentials'];
                $orderInfo[$tmpTid]['more_credential_content'] = $ticketInfo[$tmpTid]['more_credential_content'];
                if ($ticketInfo[$tmpTid]['pre_sale'] == 'H' && !empty($detailInfo[$tmpTid]['fxDetails']['series'])) {
                    $series = unserialize($detailInfo[$tmpTid]['fxDetails']['series']);
                    $orderInfo[$tmpTid]['show_info'] = [
                        'venusid' => $series[0],
                        'roundid' => $series[1],
                        'zoneid' => $series[2],
                    ];
                }
                $orderInfo[$tmpTid]['ext_content'] = empty($detailInfo[$tmpTid]['fxDetails']['extContent']) ? "" : json_decode($detailInfo[$tmpTid]['fxDetails']['extContent'], true);
                $tmpPackSonData = $packSonData[$tmpTid] ?? [];
                $sonTidArr = array_column($tmpPackSonData, 'tid');
                $sonTimeShareRes = $timeShareBiz->getTimeSlicesWithTidArr($sonTidArr, $tmpPlayTime, 9, true);
                foreach ($tmpPackSonData as &$packageTicketItem) {
                    $packageTicketItem['time_share_info'] = $sonTimeShareRes['data'][$packageTicketItem['tid']] ?? [];
                    $packageTicketItem['ext_content'] = empty($detailInfo[$tmpTid]['fxDetails']['extContent']) ? "" : json_decode($detailInfo[$tmpTid]['fxDetails']['extContent'], true);
                }
                unset($packageTicketItem);
                $orderInfo[$tmpTid]['son_ticket'] = $tmpPackSonData;
                $orderInfo[$tmpTid]['p_type'] = $ticketInfo[$tmpTid]['p_type'];
                $orderInfo[$tmpTid]['tourist_info'] = $ticketInfo[$tmpTid]['tourist_info'];
                $orderInfo[$tmpTid]['time_share_info'] = $timeShareRes[$tmpTid] ?? [];
                if (isset($oldIdCardArr[$tmpOrderNum])) {
                    $orderInfo[$tmpTid]['id_card'] = $oldIdCardArr[$tmpOrderNum];
                }
            }
        }
        $result['group_products'] = $ticketInfoArr;
        //处理客源地信息
        $result['guest_source'] = implode(' ', array_values(json_decode($result['guest_source'], true)));
        //获取变更记录
        $changeRecord = $this->_verifyModel->getChangeRecordsByStashId($id);

        //获取修改记录的用户名称及票名称
        $recordMidArr = array_column($changeRecord, 'operator_id');
        $nameMap = $memModel->getMemberInfoByMulti($recordMidArr, 'id', 'id,dname', 1);
        $ticketArr = $javaApi->queryTicketInfoByIds($tmpOrderTidArr, 'title,id,pid', 'id', 'title,imgpath,id');
        $ticketInfo = [];
        foreach ($ticketArr as $ticketInfos) {
            $ticketInfo[$ticketInfos['ticket']['id']] = [
                'id' => $ticketInfos['product']['id'],
                'ttitle' => $ticketInfos['ticket']['title'],
                'tid' => $ticketInfos['ticket']['id'],
                'pid' => $ticketInfos['ticket']['pid'],
                'title' => $ticketInfos['land']['title'],
                'imgpath' => $ticketInfos['land']['imgpath'],
                'landid' => $ticketInfos['land']['id'],
            ];
        }

        foreach ($changeRecord as &$item) {
            $item['dname'] = $nameMap[$item['operator_id']]['dname'];
            $item['title'] = $ticketInfo[$item['tid']]['title'] . ' - ' . $ticketInfo[$item['tid']]['ttitle'];
        }
        //modify_auth 是否允许修改 1：是  0：否
        $result['modify_auth'] = 1;
        //针对新版支持多级转分销报团，这边得加下判断，原始供应商也不能修改申请产品，并且不展示申请方名称
        if (!empty($result['order_mid']) && $result['sid'] != $result['fid']) {
            $result['modify_auth'] = 0;
        }
        if ($result['team_type_id']) {
            //根据团单类型获取团单类型信息
            $teamRulesBz = new \Business\TeamOrder\TeamRules();
            $ruleInfo = $teamRulesBz->getTeamOrderEditLimitById($result['team_type_id']);
        }
        //追加下团单附件
        $attach = TeamAttachService::getInstance()->getAttachInfoByTeamStashId($id);
        $result['team_attach_info'] = $attach['attach_info'] ?? [];
        $result['team_type_name'] = $ruleInfo['data']['limit_name'] ?? "";
        $result['change_records'] = $changeRecord;
        $result['paymode'] = in_array($result['paymode'], [1, 3]) ? '到付' : $payTypesArr[$result['paymode']];
        $result['status'] = $this->_status[$result['status']];
        $result['type_label'] = $result['type'] == 1 ? '下单' : ($result['type'] == 2 ? '增加门票' : '退票');
        $result['old_ticket_info'] = $oldTicketInfo;
        $result['order_info'] = $orderInfo;
        $result['ext_content'] = $mainExtContent ?? [];

        return $this->returnData(200, '获取成功', $result);
    }

    /**
     * 审核申请
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-19
     * @params $type 审核状态 1通过 2拒绝  3取消
     */
    public function teamOrderAudit($sid, $id, $type, $remark)
    {
        if (!$id) {
            return $this->returnData(203, '缺少必要参数');
        }
        $field = "id,sid,fid,group_products,play_time,team_ordernum,guest_source,name,mobile,paymode,mome,guide,
        FROM_UNIXTIME(create_time) as create_time,type,status,book_type,id_card,show_data,order_data,team_num,is_lodging,
        hotel,order_mid, team_type_id, ext_content, order_id_card";
        //获取审核申请
        $orderArray = $this->_verifyModel->getTeamorderStashById($id, $field);
        if ($orderArray['status'] != 0) {
            return $this->returnData(203, '申请已处理，无需重复操作');
        }
        //获取出修改的票信息
        $tidArr = [];
        foreach (json_decode($orderArray['order_data'], true) as $lid => $ticketInfo) {
            $tidArr[] = array_keys($ticketInfo)[0];
        }

        $verifyBiz = new TeamOrderVerify();
        $config = $verifyBiz->getConfig($orderArray['sid'], $orderArray['fid'], 4, $tidArr);

        $teamOrderNum = 0;
        $msg          = '';
        $resultMsg    = '';
        if ($type !== 3) {
            $groupProducts = array_keys(json_decode($orderArray['guest_source'], true));
            $extContent = json_decode($orderArray['ext_content'], true);
            $orderData = [
                'playTime' => $orderArray['play_time'],
                'province' => $groupProducts[0],
                'city' => $groupProducts[1],
                'country' => $groupProducts[2],
                'orderName' => $orderArray['name'],
                'orderTel' => $orderArray['mobile'],
                'orderMemo' => $orderArray['mome'],
                'guide' => $orderArray['guide'],
                'payType' => $orderArray['paymode'],
                'bookType' => $orderArray['book_type'],
                'aid' => $orderArray['fid'], //上级供应商
                'sourceId' => $orderArray['sid'], //原始供应商id
                'orderIdCard' => $orderArray['order_id_card'], //取票人身份证信息
                'idCard' => json_decode($orderArray['id_card'], true),
                'orderData' => json_decode($orderArray['order_data'], true),
                'showData' => json_decode($orderArray['show_data'], true),
                'number' => $orderArray['team_num'],
                'memberId' => !empty($orderArray['order_mid']) ? $orderArray['order_mid'] : $orderArray['fid'], //下单人id
                'is_lodging' => $orderArray['is_lodging'],
                'hotel' => $orderArray['hotel'],
                'teamTypeId' => $orderArray['team_type_id'],
                'driverInfo' => empty($extContent['driver']) ? [] : $extContent['driver'],
                'synchronous' => $extContent['synchronous'] ?? 0,
                'voucherType' => $extContent['voucherType'] ?? 1,
                'moreCredentials' => $extContent['moreCredentials'] ?? [],
                'moreTouristCredentials' => $extContent['moreTouristCredentials'] ?? [],
                'orderIdCardTickets' => $extContent['orderIdCardTickets'] ?? [],
                'teamDiscountInfo' => $extContent['teamDiscountInfo'] ?? [],  //团单优惠信息
                'timeShareInfo' => $extContent['timeShareInfo'] ?? [],  //分时信息
                'playTimeArr' => empty($extContent['playTimeArr']) ? [] : json_decode($extContent['playTimeArr'], true),
                'teamOrderId' => $orderArray['team_ordernum'] ?: 0,
            ];
            if ($type == 1 && $orderArray['type'] == 1) {
                //审核通过将订单下单
                $result = $this->_submitOrder($orderData);
                pft_log('debug/lee', json_encode(['result: ', json_encode(['_submitOrder', json_encode($result)])]));
                $resultMsg = $result['msg'];
                $teamOrderNum = $result['data']['team_order'];
            } elseif ($type == 1 && in_array($orderArray['type'], [2, 3, 5])) {
                $oldArray = json_decode($orderArray['group_products'], true);
                //审核通过将票数修改
                $result = $this->_numModify($sid, $orderArray['team_ordernum'], $oldArray, $orderData['idCard'], $orderArray['type']);
                $resultMsg = $result['msg'];
                pft_log('debug/lee', json_encode(['_numModify', json_encode($result)]));
            } else {
                $result['code'] = 200;
            }
            $memberBiz = new MemberBiz();
            if ($type == 1 && $orderArray['type'] == 1 && (1 & $config['sell_notice'])) {
                //发送短信通知分销商（通过）
                //分销商申请修改团单通知供应商
                $memberMobile = $memberBiz->getInfo($orderArray['fid']);
                $res = $verifyBiz->sendSMS($orderArray['sid'],
                    '尊敬的客户，您的报团申请单' . $id . '审核通过，团单下单成功。订单号：' . $result['data']['team_order'] . '，详情及二维码12301.cc/',
                    $memberMobile['mobile'], '报团申请单审核通过');
                pft_log('debug/lee', json_encode(['sendMSG: ', $res]));
            } elseif ($type == 2 && $orderArray['type'] == 1 && (2 & $config['sell_notice'])) {
                //发送短信通知分销商（拒绝）
                //分销商申请修改团单通知供应商
                $memberMobile = $memberBiz->getInfo($orderArray['fid']);
                $res = $verifyBiz->sendSMS($orderArray['sid'], '尊敬的客户，您的报团申请单' . $id . '审核拒绝，团单下单失败，具体原因请登录平台查看审核备。',
                    $memberMobile['mobile'], '报团申请单审核拒绝');
            } elseif (($orderArray['type'] == 2 || $orderArray['type'] == 3) && (4 & $config['buyer_notice'])) {
                //退票审核短信通知
                if ($type == 1) {
                    $msg = '尊敬的客户，您的报团申请单' . $id . '审核通过，团单门票退票成功。订单号：' . $result['data']['team_order'] . '，详情及二维码12301.cc/';
                } else {
                    $msg = '尊敬的客户，您的报团申请单' . $id . '审核拒绝，团单门票退票失败，具体原因请登录平台查看审核备。';
                }
                //分销商申请团单退票通知供应商
                $memberMobile = $memberBiz->getInfo($orderArray['fid']);
                $res = $verifyBiz->sendSMS($orderArray['sid'], $msg, $memberMobile['mobile'], '团单门票退票');
                pft_log('debug/lee', json_encode(['sendMSG: ', $res]));
            }
            //假如下单失败|修改票数失败  将审核拒绝掉
            if (!in_array($result['code'], [200, 1095]) && in_array($type, [1, 2])) {
                $type = 2;
            }
        } else {
            $result['code'] = 200;
        }
        $errorMsg = $result['code'] != 200 || $result['code'] == 1095 ? $resultMsg : "";


        $saveRes = $this->_verifyModel->saveTeamOrderStasStatus($id, $type, $resultMsg, $teamOrderNum, $remark, $errorMsg);
        //回写一下团单附件表 这里不判断返回结果
        TeamAttachService::getInstance()->updateTeamOrderIdByStashId($id, $teamOrderNum);
        pft_log('debug/lee', json_encode(['saveRes: ', json_encode($saveRes)]));

        if (!$saveRes || $result['code'] != 200) {
            return $this->returnData(203, $msg ?: '审核失败');
        }
        $msg = '审核完成';
        if ($type == 2) {
            $msg = '取消申请成功';
        }

        return $this->returnData(200, $msg);
    }

    /**
     * 团单修改申请
     *
     * @param array $orderData 下单参数
     *
     * @return array
     */
    private function _submitOrder($orderData)
    {
        try {
            $app = new Application();

            $order = $app->order;

            $prepare = $app->prepare;
            $subData = $prepare->getOrderData($orderData);
            $subData['verify'] = 1; //报团审核通下单
            $res = $order->submit($subData);
            return $this->returnData(200, $res[1], [
                'team_order' => $res[0],
                'qrUrl' => \Business\TeamOrder\Booking::getTeamQrCode(
                    $subData['team_data']['ordernum'],
                    $subData['team_data']['tnum'],
                    $subData['team_data']['playtime']),
            ]);
        } catch (\Exception $e) {
            if ($e->getCode() == 1001) {
                $return = json_decode($e->getMessage(), true);
                $msg = '';
                foreach ($return['data'] as $value) {
                    $msg .= "证件号{$value['idCard']}不满足{$value['msg']}条件,";
                }
                $msg = rtrim($msg, ',');
                return $this->returnData($e->getCode(), $msg);
            }

            return $this->returnData($e->getCode(), $e->getMessage());
        }
    }

    /**
     * 团单票数修改
     * @return array
     * <AUTHOR> Li
     * @date   2019-03-25
     *
     */
    private function _numModify($sid, $teamOrder, $ticketInfo, $touristInfoArray = [], $type = 0)
    {
        $business = new \Business\Order\Modify();

        $app = new Application();
        $order = $app->order;
        $orderData = $app->order_class->getTeamModel()->getMainOrderInfoByOrderNum($teamOrder);

        if (!in_array($orderData['status'], [1, 4]) && $orderData['aid'] == $sid) {
            return $this->returnData(205, '团队订单未确认，无法修改');
        }

        $sonOrderData = $app->order_class->getTeamModel()->getSonOrderInfoByMainOrderNum($teamOrder, false, false,
            'son_ordernum, lid, tid');
        $numMapping = [];
        if (empty($sonOrderData)) {
            return $this->returnData(205, '团单信息有误');
        }
        $tidArr = array_keys($ticketInfo);
        foreach ($sonOrderData as $sonOrder) {
            if (in_array($sonOrder['tid'], $tidArr)) {
                $numMapping[$sonOrder['son_ordernum']] = $ticketInfo[$sonOrder['tid']]['tnum'];
            }
        }
        $delTicket = [];
        $errorMsg = '';
        $changeNum = 0;
        $cancelList = $business->handleCancelList($numMapping, $sid, $sid, $type);
        foreach ($cancelList as $cancelKey => $cancelValue) {
            if (!empty($cancelValue['errorMsg'])) {
                $errorMsg .= '订单号' . $cancelValue['ordernum'] . $cancelValue['errorMsg'];
                $result = ['code' => 204];
                continue;
            }
            if ($cancelValue['type'] == 'add') {    //加票
                $touristInfo = $touristInfoArray[$cancelValue['tid']] ?? [];
                $result = $business->orderAddNum($cancelValue['ordernum'],
                    [$cancelValue['ordernum'] => $cancelValue['cancelNum']], $sid, $sid, $touristInfo);

                if ($result['code'] != 200) {
                    $errorMsg .= '订单号' . $cancelValue['ordernum'] . '增加:' . $result['msg'];
                }
            } elseif ($cancelValue['type'] == 'del') {  //减少票
                $delTicket[$cancelValue['ordernum']] = [
                    'num' => $cancelValue['cancelNum'],
                    'tid' => $cancelValue['tid'],
                ];
            } else {
                $errorMsg .= '订单号' . $cancelValue['ordernum'] . "未做任何修改，请重新操作";
                $result = ['code' => 204];
            }
        }

        if (!empty($delTicket)) {
            foreach ($delTicket as $key => $value) {
                //https://12301-cc.feishu.cn/wiki/NhPYwj0uaiWUSgkPYACcE8WgnOe?from=from_copylink
                //{"person":{"person_id_list":[],"person_index":0,"ticket_code_list":[],"person_info_list":[{"person_index":"取消第几个游客的数据","idcard":"证件号","idcard_type":"证件类型","ticket_code":"对应的取消门票码"}]}}
                //这里需要支持其他证件类型的退票
                //$touristInfoArray={"2440348": [{"idcard": "H05460896", "tourist": "葉秀珍", "voucher_type": "4"}]}
                //$touristInfo['person_id_list'] = isset($touristInfoArray[$value['tid']]) ? array_column($touristInfoArray[$value['tid']], 'idcard') : [];
                $touristInfo = [];
                if (isset($touristInfoArray[$value['tid']])) {
                    foreach ($touristInfoArray[$value['tid']] as $touristInfoItem) {
                        $touristInfo['person_info_list'][] = [
                            //person_index未支付订单时必填 ticket_code 非必填
                            'idcard' => $touristInfoItem['idcard'],//必填
                            'idcard_type' => (int)$touristInfoItem['voucher_type'],//必填
                        ];
                    }
                }
                $reqSerialNumber = $business->getAuditInfoByOrder($key);
                $result = $business->cancelParamsCommonFormat(
                    $key, $sid, OrderConst::TEAM_VERIFY_CANCEL, $reqSerialNumber, $value['num'],
                    'common', [], [], $touristInfo
                );
                if ($result['code'] == 200) {
                    $changeNum += $value['cancelNum'];
                } else {
                    $errorMsg .= '订单号' . $key . '减少' . $result['msg'];
                }
            }
        }
        $errorMsg = $errorMsg ? $errorMsg : '审核通过';
        if ($changeNum != 0) {
            $res = $order->modifyTeamTicketNum($teamOrder, $changeNum);

            if (!$res) {
                $result = [
                    'code' => 400,
                    'data' => [],
                ];
                $errorMsg .= '更新团队订单人数失败';
            }
        }

        return $this->returnData($result['code'], $errorMsg, ['team_order' => $teamOrder]);
    }
}