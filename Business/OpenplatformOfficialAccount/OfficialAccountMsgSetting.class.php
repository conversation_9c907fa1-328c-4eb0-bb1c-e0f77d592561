<?php

namespace Business\OpenplatformOfficialAccount;

use Business\Base;
use Library\Cache\Cache;
use Library\wechat\core\Media;
use Library\wechat\OpenplatformOfficialAccount;
use Model\AppCenter\ModuleList;
use \Model\Wechat\OfficialAccountMsgSetting as OfficialAccountMsgSettingMode;
use Model\Wechat\open;
use Model\Wechat\WxMemberBehavior;
use Model\Wechat\WxOpen;

class OfficialAccountMsgSetting extends Base
{
    private $templateIdShortList = [
        //新订单通知  互联网|电子商务
        1 => [
            'template_id_short' => "TM00351",
            'template_type'     => 1,
            'template_name'     => "新订单通知",
        ],
        //拼团成功     互联网|电子商务
        2 => [
            'template_id_short' => "OPENTM401153728",
            'template_type'     => 2,
            'template_name'     => "拼团成功通知",
        ],
        //拼团失败     互联网|电子商务
        3 => [
            'template_id_short' => "OPENTM401113750",
            'template_type'     => 3,
            'template_name'     => "拼团失败通知",
        ],
    ];

    /**
     * 获取商家微信公众号信息
     *
     * @param int  $sid  商家id
     *
     * <AUTHOR>
     * @date 2020/4/23
     *
     * @return array
     */
    public function getOfficialAccountInfo($sid)
    {
        if (empty($sid)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //查询商家托管的微信公众号
        $openMode = new WxOpen();
        $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
        if (empty($openInfo)) {
            return $this->returnData(self::CODE_INVALID_REQUEST,"公众号未托管");
        }
        $sameBusiness = false;
        //判断行家是否在符合所属行业
        $result      = $this->_getIndustry($openInfo['appid']);
        if ($result['code'] == self::CODE_SUCCESS) {
            $sameBusiness = true;
        }
        $returnData = [
            'service_type_info' => $openInfo['service_type_info'],
            'verify_type_info'  => $openInfo['verify_type_info'],
            'same_business'     => $sameBusiness
        ];
        return $this->returnData(self::CODE_SUCCESS,"获取成功", $returnData);
    }
    /**
     * 获取用户开通模板信息
     *
     * @param int  $sid  商家id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/24
     *
     * */
    public function getMsgSetting($sid)
    {
        if (empty($sid)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //查询商家托管的微信公众号
        $openMode = new WxOpen();
        $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
        if (empty($openInfo)) {
            return $this->returnData(self::CODE_SUCCESS,"公众号未托管");
        }
        $officialAccountMsgSettingMode = new OfficialAccountMsgSettingMode();
        //获取微信模板消息配置表
        $field  = 'id, template_type, template_status';
        $result = $officialAccountMsgSettingMode->getOfficialAccountMsgSettingListBySid($sid, $openInfo['appid'], $field);
        if (!empty($result)) {
            //给开通过模板消息的赋值数据
            foreach ($result as $value) {
                $this->templateIdShortList[$value['template_type']]['id']              = $value['id'];
                $this->templateIdShortList[$value['template_type']]['template_type']   = $value['template_type'];
                $this->templateIdShortList[$value['template_type']]['template_status'] = $value['template_status'];
            }
        }
        $returnData = array_values($this->templateIdShortList);
        return $this->returnData(self::CODE_SUCCESS, "获取成功", $returnData);
    }

    /**
     * 为用户开启/开通行业模板
     *
     * @param int  $sid 商家id
     * @param int  $id  配置的主键id
     * @param string  $templateIdShort  模板库中模板的编号
     * @param string  $templateStatus  状态  1:开启  2:关闭
     *
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/24
     *
     *
     */
    public function addOrUpdateMsgSetting($sid, $id, $templateIdShort, $templateStatus)
    {
        if (empty($id) && empty($templateIdShort)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //查询商家托管的微信公众号
        $openMode = new WxOpen();
        $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
        if (empty($openInfo)) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "公众号未托管");
        }
        $officialAccountMsgSettingMode = new OfficialAccountMsgSettingMode();
        //如果已开启过，就更新开关状态，未开启过，给商家注册
        if ($id) {
            $result = $officialAccountMsgSettingMode->updateMsgSettingStatusBySid($sid, $id, $templateStatus);
            self::deleteTplInfoRedis($sid);
        } else {
            //获取商家公众号所在行业
            $industry = $this->_getIndustry($openInfo['appid']);
            if ($industry['code'] != self::CODE_SUCCESS) {
                return $this->returnData($industry['code'], $industry['msg']);
            }
            $moduleListMode = new ModuleList();
            $type = 1;  //默认添加的模板类型为新订单通知
            switch ($templateIdShort){
                //拼团的情况,检测商家是否开通拼团模块
                case 'OPENTM401153728':
                    $type = 2;
                    $res = $moduleListMode->checkMemberUseModuleAuth($sid, 46);
                    if ($res['code'] != 200) {
                        return $this->returnData($res['code'], $res['msg']);
                    }
                    break;
                case 'OPENTM401113750':
                    $type = 3;
                    $res = $moduleListMode->checkMemberUseModuleAuth($sid, 46);
                    if ($res['code'] != 200) {
                        return $this->returnData($res['code'], $res['msg']);
                    }
                    break;
                default:
                    break;
            }
            //调用微信接口，为公众号添加对应的行业模板
            $addTemplateResult = OpenplatformOfficialAccount::apiAddTemplate($openInfo['appid'], $templateIdShort);
            if ($addTemplateResult['errcode'] != 0) {
                return $this->returnData($addTemplateResult['errcode'], $addTemplateResult['errmsg']);
            }
            $templateId = $addTemplateResult['template_id'];
            $result     = $officialAccountMsgSettingMode->addMsgSetting($sid, $openInfo['appid'], $templateId, $type);
        }
        if ($result === false) {
            return $this->returnData(self::CODE_INVALID_REQUEST,"操作失败");
        }
        return $this->returnData(self::CODE_SUCCESS,"操作成功");
    }


    /**
     * 获取客服消息开关状态
     *
     * @param int  $sid  商家id
     * @param int  $type  服务类型 1:定时托送  2:粉丝关注推送 3:粉丝对话推送 4:操作菜单推送
     * 
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     *
     */
    public function getCustomerStatusSetting($sid, $type)
    {
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        $field               = "id,push_type,state,interval_time";
        $result              = $officialAccountMode->getCustomerStatusSetting($sid, $type, $field);
        $result              = $result ?? [];

        return $this->returnData(self::CODE_SUCCESS,"获取成功", $result);
    }

    /**
     * 开启/关闭客服消息推送功能
     *
     * @param int  $id   主键id
     * @param int  $sid  供应商id
     * @param string  $type 操作的类型
     * @param int  $status  开启关闭的类型1:开启  2:关闭
     *
     * <AUTHOR>
     * @date 2020/4/27
     *
     * @return array
     */
    public function updateOrAddCustomerStatusSetting($id, $sid, $type, $status)
    {
        if (empty($sid) || empty($status) || empty($type)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //当商家操作开启推送功能或者新增推送状态时查询商家托管的微信公众号
        if ($status == 1 || empty($id)) {
            $openMode = new WxOpen();
            $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
            if (empty($openInfo)) {
                return $this->returnData(self::CODE_INVALID_REQUEST, "公众号未托管");
            }
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        if ($id) {
            $result = $officialAccountMode->openOrCloseCustomerStatusSetting($id, $sid, $type, $status);
        } else {
            //查询用户配置是否已存在，防止一个用户在表被有多条数据，此时的特殊返回码为401
            $res    = $officialAccountMode->getCustomerStatusSetting($sid, $type);
            if ($res) {
                return $this->returnData(self::CODE_AUTH_ERROR, "操作失败");
            }
            $result = $officialAccountMode->addCustomerStatusSetting($sid, $type, $status);
        }
        if ($result === false) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "操作失败");
        }

        return $this->returnData(self::CODE_SUCCESS, "操作成功");
    }

    /**
     * 设置客服消息的间隔时间
     *
     * @param int  $id  配置的主键id
     * @param int  $intervalTime  间隔时间
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/30
     *
     *
     */
    public function setIntervalTime($id, $intervalTime)
    {
        if (empty($id)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        $result              = $officialAccountMode->setIntervalTime($id, $intervalTime);
        if ($result === false) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "操作失败");
        }

        return $this->returnData(self::CODE_SUCCESS, "操作成功");
    }

    /**
     * 更新或者新增客服消息
     * @param int       $sid                    商家id
     * @param int       $id                     数据主键id
     * @param string    $title                  标题
     * @param int       $pushType               推送类型 1：粉丝活跃定时推送   2：粉丝关注推送 3：粉丝对话推送  4：操作菜单推送
     * @param int       $msgType                信息类型 1：文字  2：图片  3：图文消息  4：微信文字  5：小程序
     * @param int       $pushMode               推送方式 1：每日推送  2：单次推送 3：延迟推送
     * @param int       $sendTime               发送时间(当为延迟推送是为延迟推送时间)
     * @param string    $config                 推送配置数据
     * @param string    $activeFans             活跃粉丝范围1：关注  2：对话  3：扫码  4：操作菜单（多个都好隔开）
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/27
     *
     * */
    public function addOrUpdateCustomerServiceMsgSetting($sid, $id, $title, $pushType, $msgType, $pushMode, $sendTime, $config, $activeFans)
    {
        if (empty($pushType) || empty($msgType) || empty($pushMode) || empty($config) || empty($title) || empty($sid) || empty($sendTime)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //查询商家托管的微信公众号
        $openMode = new WxOpen();
        $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
        if (empty($openInfo)) {
            return $this->returnData(self::CODE_INVALID_REQUEST,"公众号未托管");
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        $msg = "操作成功";
        if ($id) {
            $result = $officialAccountMode->updateCustomerServiceMsgSetting($id, $sid, $title, $pushType, $msgType, $pushMode, $sendTime, $config,
                $activeFans);
        } else {
            $msgStatus = 1;
            $total  = $officialAccountMode->countCustomerServiceMsgByPushType($sid, $pushType);
            if ($total == 2 && in_array($pushType, [2,3,4])) {
                $msgStatus = 2;
                $msg       = "操作成功，生效的配置仅允许同时生效两条，有需要请手动调整";
            } elseif ($total == 3) {
                $msgStatus = 2;
                $msg       = "操作成功，生效的配置仅允许同时生效三条，有需要请手动调整";
            }
            $result = $officialAccountMode->addCustomerServiceMsgSetting($sid, $openInfo['appid'], $title, $pushType, $msgType, $pushMode, $sendTime,
                $config, $activeFans, $msgStatus);
        }
        if ($result === false) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "操作失败");
        }

        return $this->returnData(self::CODE_SUCCESS, $msg);
    }

    /**
     * 获取配置信息列表
     *
     * @param int  $sid         商家id
     * @param int  $type        获取的列表类型 1：粉丝活跃定时推送   2：粉丝关注推送 3：粉丝对话推送  4：操作菜单推送
     * @param int  $pageNum     第几页
     * @param int  $pageSize    每页几条
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/27
     *
     * */
    public function getCustomerServiceMsgSettingList($sid, $type = 1, $pageNum = 1, $pageSize = 10)
    {
        if (empty($sid)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //获取商家托管的公众号
        $openMode = new WxOpen();
        $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
        if (empty($openInfo)) {
            return $this->returnData(self::CODE_SUCCESS, "商家公众号未托管");
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        //获取列表数据
        $field  = 'id, push_mode, send_time, title, push_times, sum_push_times, msg_status';
        $result = $officialAccountMode->getCustomerServiceMsgSettingList($sid, $openInfo['appid'], $type, $field, $pageNum, $pageSize);

        $result['list'] = $result['list'] ?? [];
        if (!empty($result['list'])) {
            foreach ($result['list'] as &$value) {
                $value['in_beginning'] = false;
                if ($type != 1) {
                    continue;
                }
                if ($value['push_mode'] == 1) {
                    $value['send_time'] = date('Y-m-d') . " ". substr_replace(str_pad($value['send_time'], 4,
                            0,STR_PAD_LEFT), ":", 2, 0);
                } elseif ($value['push_mode'] == 2) {
                    if ($value['send_time'] < time()) {
                        $value['in_beginning'] = true;
                    }
                    $value['send_time'] = date('Y-m-d H:i', $value['send_time']);
                }
            }
        }

        return $this->returnData(self::CODE_SUCCESS, "获取成功", $result);
    }

    /**
     * 获取客服消息配置详情
     *
     * @param int  $id  主键id
     * @param int  $sid 商家id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     *
     *
     */
    public function getCustomerServiceMsgSettingDetail($id, $sid)
    {
        if (empty($id) || empty($sid)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        //获取列表数据
        $field  = 'id, push_mode, send_time, title, msg_type, config, active_fans';
        $result = $officialAccountMode->getCustomerServiceMsgSettingDetail($id, $sid, $field);
        if (!empty($result)) {
            if ($result['push_mode'] == 1) {
                $result['send_time'] = substr_replace(str_pad($result['send_time'], 4,
                        0,STR_PAD_LEFT), ":", 2, 0) . ":" . "00";
            } elseif ($result['push_mode'] == 2) {
                $result['send_time'] = date('Y-m-d H:i:s', $result['send_time']);
            }
            $result['config']              = json_decode($result['config'], true);
        }

        return $this->returnData(self::CODE_SUCCESS, "获取成功", $result);
    }

    /**
     * 删除客服消息配置
     *
     * @param int  $id  主键id
     * @param int  $sid 商家id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     *
     *
     */
    public function deleteCustomerServiceMsgSetting($id, $sid)
    {
        if (empty($id) || empty($sid)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        $result              = $officialAccountMode->deleteCustomerServiceMsgSetting($id, $sid);
        if ($result === false) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "删除失败");
        }

        return $this->returnData(self::CODE_SUCCESS, "删除成功");
    }

    /**
     * 开启/关闭客服消息配置
     *
     * @param int  $id  主键id
     * @param int  $sid 商家id
     * @param int  $msgStatus 状态  1：开启  2：关闭
     *
     * <AUTHOR>
     * @date 2020/4/28
     *
     * @return array
     */
    public function openOrCloseCustomerServiceMsgSetting($id, $sid, $msgStatus)
    {
        if (empty($id) || empty($sid) || empty($msgStatus)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        //如果是开启某个配置,统计相同模块开启数量
        if ($msgStatus == 1) {
            $pushTypeInfo = $officialAccountMode->getCustomerServiceMsgSettingDetail($id, $sid, 'push_type');
            $pushType     = $pushTypeInfo['push_type'];
            $total        = $officialAccountMode->countCustomerServiceMsgByPushType($sid, $pushType);
            if ($total == 2 && in_array($pushType, [2,3,4])) {
                return $this->returnData(self::CODE_INVALID_REQUEST, "该类型消息最多允许同时生效两条");
            } elseif ($total == 3) {
                return $this->returnData(self::CODE_INVALID_REQUEST, "该类型消息最多允许同时生效三条");
            }
        }
        $result = $officialAccountMode->openOrCloseCustomerServiceMsgSetting($id, $sid, $msgStatus);
        if ($result === false) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "操作失败");
        }

        return $this->returnData(self::CODE_SUCCESS, "操作成功");

    }

    /**
     * 上传永久图片素材到微信
     *
     * @param string  $src  文件路径
     * @param int     $sid  商家id
     * @param string  $type  商家id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/28
     */
    public function AddMaterial($sid, $src, $type = 'image')
    {
        if (empty($sid) ) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        //查询商家托管的微信公众号
        $openMode = new WxOpen();
        $openInfo = $openMode->getWechatOffiAccInfo($sid, 'fid');
        if (empty($openInfo)) {
            return $this->returnData(self::CODE_INVALID_REQUEST,"公众号未托管");
        }
        $res = Media::addMaterial($openInfo['appid'], $src, $type);
        if (isset($res['errcode']) || $res['errcode'] != 0) {
            return $this->returnData($res['errcode'], $res['errmsg']);
        }

        return $this->returnData(self::CODE_SUCCESS, "上传成功", $res);
    }

    /**
     * 删除模板消息缓存数据
     * @param int  $sid  商家id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/5/13
     *
     * */
    public static function deleteTplInfoRedis($sid)
    {
        /**
         * @var  $redis \Library\Cache\CacheRedis
         */
        $redis = Cache::getInstance('redis');
        $key   = "wx_tpl:" . $sid;
        $redis->hdel($key, '');
    }

    /**
     * 添加模板消息缓存数据
     * @param int  $sid  商家id
     * @param array  $data  添加的数据
     *
     * @return void
     * <AUTHOR>
     * @date 2020/5/13
     *
     * */
    public static function addTplInfoRedis($sid, $data)
    {
        /**
         * @var  $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        $key   = "wx_tpl:" . $sid;
        $redis->hMSet($key,$data);
        $redis->expire($key, 86400);
    }

    /**
     * 获取模板消息缓存数据
     * @param int  $sid  商家id
     *
     * @return array
     * <AUTHOR>
     * @date 2020/5/13
     *
     * */
    public static function getTplInfoRedis($sid)
    {
        /**
         * @var  $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        $key   = "wx_tpl:" . $sid;
        $result = $redis->hGetAll($key);
        if ($result) {
            return $result;
        }
        return [];
    }

    /**
     * 根据供应商ID判断是否需要发送模板消息
     * @author: Guangpeng Chen
     * @date: 2020/5/13
     * @param $sid
     *
     * @return array
     */
    public static function getWxTemplateInfo($sid)
    {
        $sendInfo = self::getTplInfoRedis($sid);
        if (!empty($sendInfo)) {
            $returnData = [
                'appid'         => $sendInfo['appid'],
                'template_id'   => $sendInfo['template_id'],
                'send_to_third' => $sendInfo['send_to_third'] == 1 ? true : false,
            ];
            return $returnData;
        }
        //查看商家众号是否托管给我们
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $weChatInfo  = $wxOpenModel->getWechatOffiAccInfo($sid, 'fid');
        $appId       = false;
        $tplId       = "";
        $sendToThird = false; //通知给游客的新订单消息是否通过商家的公众号发送  false:否  true:是
        //如果商家公众号有托管，获取商家是否有自己开通新订单通知模板
        if (!empty($weChatInfo)) {
            $officialMsgSettingMode = new \Model\Wechat\OfficialAccountMsgSetting();
            $result                 = $officialMsgSettingMode->getMsgSettingBySidAndType($sid, $weChatInfo['appid'], 1, 'template_id');
            if (!empty($result)) {
                $appId       = $weChatInfo['appid'];
                $tplId       = $result['template_id'];
                $sendToThird = true;
            }
        }
        $redisData = [
            'appid'         => $appId,
            'template_id'   => $tplId,
            'send_to_third' => $sendToThird,
        ];
        self::addTplInfoRedis($sid, $redisData);
        return $redisData;
    }


    /**
     * 更新微信用户操作记录
     *
     * @param array  $request 回调参数
     *
     * @return array|bool
     * <AUTHOR>
     * @date 2020/4/26
     *
     * */
    public static function recordWxMemberBehaviour($request)
    {
        //根据appid获取商家id
        $wxOpenMode = new WxOpen();
        $wxInfo     = $wxOpenMode->getWechatOffiAccInfoByUserName($request['tousername'], 'fid, appid');
        $moduleListMode = new ModuleList();
        //查询商家是否开通公众号营销模块
        $res = $moduleListMode->checkMemberUseModuleAuth($wxInfo['fid'], 62);
        if ($res['code'] != 200) {
            return false;
        }
        $requestEvent = [
            'subscribe',
            'scan',
            'click',
            'view',
            'scancode_push',
            'pic_sysphoto',
            'pic_photo_or_album',
            'pic_weixin',
            'location_select',
        ];
        $msgType = ['text', 'image', 'voice', 'video', 'location', 'link'];

        //回调的行为事件在这些行为中，记录下改用户在这个公众号下关注的行为
        if (!((in_array(strtolower($request['event']), $requestEvent) && $request['msgtype'] == 'event') || in_array($request['msgtype'], $msgType))) {
            return false;
        }
        pft_log('wx_member_behaviour', "用户操作记录参数为：" . json_encode($request, JSON_UNESCAPED_UNICODE));
        //扫码的要多个判断排除扫码添加分销商的情况
        if (in_array(strtolower($request['event']), ['subscribe', 'scan']) && $request['msgtype'] == 'event') {
            //存在事件KEY值，qrscene_为前缀，后面为二维码的参数值，需要排除扫码添加分销商的情况不记录为用户行为记录
            if (isset($request['eventkey'])) {
                $eventkey = $request['eventkey'];
                if (stripos($eventkey, 'qrscene_') !== false) {
                    $eventkey = str_replace('qrscene_', '', $eventkey);
                }
                if (!empty($eventkey)) {
                    $sceneModel = new \Model\Wechat\WxScene();
                    //获取扫码场景信息
                    $scene = $sceneModel->getWechatScene($eventkey);
                    $extra = !empty($scene) ? json_decode($scene['extra'], true) : [];
                    if (isset($extra['identify']) || $extra['identify'] == 'addreseller') {
                        return false;
                    }
                }
            }
        }

        $click    = [
            'click',
            'view',
            'scancode_push',
            'pic_sysphoto',
            'pic_photo_or_album',
            'pic_weixin',
            'location_select',
        ];
        $dialogue = ['text', 'image', 'voice', 'video', 'location', 'link'];
        // interactionType 操作类型   1：关注  2：对话  3：扫码  4：操作菜单
        // serviceType 服务类型  2:粉丝关注推送 3:粉丝对话推送 4:操作菜单推送
        $interactionType        = 0;
        $interactionTypeAgainst = 0;
        $serviceType            = 0;
        if ($request['msgtype'] == 'event' && $request['event'] == 'subscribe') {
            //这种情况又是扫码又是关注
            $interactionType        = 1;
            $interactionTypeAgainst = 3;
            $serviceType            = 2;
        }
        if (in_array($request['msgtype'], $dialogue)) {
            $interactionType = 2;
            $serviceType     = 3;
        }
        if ($request['msgtype'] == 'event' && $request['event'] == 'scan') {
            $interactionType = 3;
        }
        if ((in_array(strtolower($request['event']), $click) && $request['msgtype'] == 'event')) {
            $interactionType = 4;
            $serviceType     = 4;
        }

        //为关注事件，记录下改用户在这个公众号下关注的行为
        $wxMemberBehaviourMode = new WxMemberBehavior();
        //插入用户表
        $result = $wxMemberBehaviourMode->addOrUpdateBehaviorLog($wxInfo['appid'], $request['fromusername'],
            $interactionType);

        if ($result === false) {
            pft_log('wx_member_behaviour',
                "用户{$interactionType}操作记录更新失败，参数为：" . json_encode($request) . "原因" .
                $wxMemberBehaviourMode->getDbError());
        }
        if (!empty($interactionTypeAgainst)) {
            $res = $wxMemberBehaviourMode->addOrUpdateBehaviorLog($wxInfo['appid'], $request['fromusername'],
                $interactionTypeAgainst);
            if ($res === false) {
                pft_log('wx_member_behaviour',
                    "用户{$interactionTypeAgainst}操作录更新失败，参数为：" . json_encode($request) . "原因" .
                    $wxMemberBehaviourMode->getDbError());
            }
        }

        //记录用户对应的消息推送记录
        if (!empty($serviceType)) {
            self::_insertOfficialMsgPush($request, $serviceType);
        }
        return true;
    }

    /**
     * 记录用户对应的消息推送记录
     *
     * @param array  $request 回调参数
     * @param int    $serviceType  用户行为   2:粉丝关注 3:粉丝对话 4:操作菜单
     *
     * <AUTHOR>
     * @date 2020/4/29
     *
     * @return array|bool
     * */
    private static function _insertOfficialMsgPush($request, $serviceType)
    {
        //根据appid获取对应商家id
        $wxOpenMode = new \Model\Wechat\WxOpen();
        $openInfo   = $wxOpenMode->getWechatOffiAccInfoByUserName($request['tousername'], 'fid, appid');
        //查询商家是否有开启和配置对应的客服消息
        $officialAccountMode = new OfficialAccountMsgSettingMode();
        $officialStatusInfo  = $officialAccountMode->getCustomerStatusSetting($openInfo['fid'], $serviceType, 'id, interval_time, state');
        if (empty($officialStatusInfo) || $officialStatusInfo['state'] == 2) {
            pft_log('insert_msg_log_error', "商家{$openInfo['fid']}未配置或者对应的微信客服消息开启状态配置是关闭的" . json_encode($officialStatusInfo));
            return false;
        }
        //查询在间隔时间内该用户是否已推送过
        $time = time() - $officialStatusInfo['interval_time'] * 3600;
        $result = $officialAccountMode->getOfficialMsgPushLogByOpenId($request['fromusername'], $time, $serviceType, 'id');

        if (!empty($result)) {
            pft_log('insert_msg_log_error', "用户{$request['fromusername']}商家{$openInfo['fid']}不允许在间隔时间反复插入：" . json_encode($result));
            return false;
        }
        //获取生效中的对应的配置消息
        $field          = 'id, send_time, config, msg_type';
        $serviceMsgInfo = $officialAccountMode->getServiceMsgSettingListBySidAndPushType($openInfo['fid'], $serviceType, $field);
        if (empty($serviceMsgInfo)) {
            pft_log('insert_msg_log_error', "商家{$openInfo['fid']}没有生效的客服消息可推送");
            return false;
        }
        $serviceIdArr = array_column($serviceMsgInfo, 'id');
        $increaseRes  = $officialAccountMode->increaseSendNums($serviceIdArr);
        if ($increaseRes === false) {
            pft_log('insert_msg_log_error', "增加累计推送人数失败" . $officialAccountMode->getDbError()
                                            . "id数组为" . json_encode($serviceIdArr, JSON_UNESCAPED_UNICODE)
            );
            return false;
        }
        foreach ($serviceMsgInfo as $value) {
            $sendTime = time() + $value['send_time'] * 60;
            $data[] = [
                'openid'      => $request['fromusername'],
                'appid'       => $openInfo['appid'],
                'msg_type'    => $value['msg_type'],
                'service_id'  => $value['id'],
                'create_time' => time(),
                'send_conf'   => $value['config'],
                'send_time'   => $sendTime,
                'push_type'   => $serviceType
            ];
        }
        //满足以上的条件，对用户的推送消息记录到表中
        $res = $officialAccountMode->batchAddLog($data);
        if ($res === false) {
            pft_log('insert_msg_log_error', "推送记录插入失败" . $officialAccountMode->getDbError());
            return false;
        }
        return true;
    }

    /**
     * 获取公众号设置的行业信息
     *
     * @param string  $appId  商家appid
     *
     * @return array
     * <AUTHOR>
     * @date 2020/4/29
     */
    private function _getIndustry($appId)
    {
        if (empty($appId)) {
            return $this->returnData(self::CODE_PARAM_ERROR, "参数错误");
        }
        $result = OpenplatformOfficialAccount::getIndustry($appId);
        if ($result['errcode'] != 0) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "获取所在行业失败");
        }
        $primaryIndustry   = $result['primary_industry'];        //公众号主营行业
        $secondaryIndustry = $result['secondary_industry'];    //公众号副营行业
        if ((strcmp($primaryIndustry['first_class'], "IT科技") != 0 || strcmp($primaryIndustry['second_class'], "互联网|电子商务") != 0)
        && (strcmp($secondaryIndustry['first_class'], "IT科技") != 0 || strcmp($secondaryIndustry['second_class'], "互联网|电子商务") != 0)) {
                return $this->returnData(self::CODE_INVALID_REQUEST, "公众号所在行业至少要设置一个为IT科技/互联网|电子商务");

        }
        return $this->returnData(self::CODE_SUCCESS, "可以开通");
    }
}