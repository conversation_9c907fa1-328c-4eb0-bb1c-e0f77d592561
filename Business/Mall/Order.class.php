<?php

namespace Business\Mall;

use Business\Base;
use Business\JavaApi\Express\PickPoint;
use Business\JavaApi\LogisticsCenter\Carriage;
use Business\JavaApi\Ticket\Price;
use Business\JavaApi\Ticket\SpecialtyTicket;
use Business\JavaApi\TicketApi;
use Business\Order\MergeOrder;
use Business\Order\OrderAidsSplitQuery;
use Business\Order\OrderOta;
use Business\Order\OrderUnity;
use Business\Product\Specialty;
use Model\Order\Coupon;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Product\AnnualCard;
use Model\Product\Land;
use Model\Product\Ticket;
use Model\Subdomain\SubdomainInfo;

/**
 * 微商城订单接口
 *
 * <AUTHOR>
 *
 */
class Order extends Base
{

    //cookie解密key
    const COOKIE_DECODE_KEY = 'weather_is_bad';
    //COOKIE key
    const COOKIE_MID_KEY = 'weather_is_good';

    /**
     * 存储下单人的id
     * <AUTHOR>
     * @date   2018-12-05
     *
     * @param  array  $orderinfo  订单详情
     */
    public function setcookieForMid($orderinfo)
    {

        if (!isset($orderinfo['order_member'])) {
            return true;
        }
        //是否存在cookie
        $midArr = $this->getCookieForMid();

        $midArr[]     = $orderinfo['order_member'];
        $midArr       = array_unique($midArr);
        $setMidString = pft_authcode(implode(',', $midArr), 'ENCODE', self::COOKIE_DECODE_KEY);
        //天荒地老
        setcookie(self::COOKIE_MID_KEY, $setMidString, time() + 3600 * 24 * 3650, '/');
    }

    /**
     * 获取cookie中的下单人id
     * <AUTHOR>
     * @date   2018-12-05
     * @return array
     */
    public function getCookieForMid()
    {
        $midArr = [];
        if (isset($_COOKIE[self::COOKIE_MID_KEY])) {
            $midstring = pft_authcode($_COOKIE[self::COOKIE_MID_KEY], 'DECODE', self::COOKIE_DECODE_KEY);
            if ($midstring) {
                $midArr = explode(',', $midstring);
                $midArr = array_map(function ($val) {
                    return intval($val);
                }, $midArr);
            }
        }

        return $midArr;
    }

    /**
     * 支付成功订单详情页信息.
     * User: xujinyao
     * Date: 2019/7/28
     *
     * @param string  $ordernum 订单号
     * @param int  $sid  供应商id
     * @param int $passCheck    是否检测
     * @param string  $reFrom  调用终端类型，微信小程序xcx,微商城h5
     *
     * @return array
     * @throws \Exception
     */
    public function paySuccessForMall($ordernum, $sid, $passCheck, $reFrom)
    {

        if (!$ordernum) {
            return $this->returnData(203, "参数错误", []);
        }
        if (strpos((string)$ordernum, 'CMB-') === false) {
            $res = $this->_getOrderPaySuccessDetail($ordernum, $sid, $reFrom);

            return $this->returnData($res['code'], $res['msg'], $res['data']);
        } else {
            $mergeOrder = new MergeOrder();
            $orderNums  = $mergeOrder->getMainOrderNumByTradeId($ordernum);
            $result     = null;
            foreach ($orderNums as $value) {
                $res = $this->_getOrderPaySuccessDetail($value, $sid, $reFrom);
                if ($res['code'] != 200) {
                    return $this->returnData(204, $res['msg']);
                }
                $res      = $res['data'];
                $result[] = $res;
            }

            return $this->returnData(200, "获取订单详情成功", $result);
        }
    }

    /**
     * 获取订单支付结果.
     * User: xujinyao
     * Date: 2019/8/23
     *
     * @param string  $ordernum 订单好
     * @param int  $sid  供应商id
     * @param string  $reFrom  调用终端类型，微信小程序xcx,微商城h5
     *
     * @return array
     */
    private function _getOrderPaySuccessDetail($ordernum, $sid, $reFrom = 'h5')
    {
        if (!$ordernum) {
            return $this->returnData(204, '参数错误', []);
        }

        $timeShareBiz      = new \Business\Product\TimeShare();
        $return            = $this->_getOrderDetail($ordernum, $reFrom);
        $return['is_face'] = 0;
        if ($return['ptype'] == 'F') {
            $orderModel = new \Model\Order\OrderTools('slave');
            $childOrder = $orderModel->getPackChildOrders($ordernum, 'orderid');

            if ($childOrder) {
                $childOrderArr = array_column($childOrder, 'orderid');
                $orderInfo     = $orderModel->getOrderInfo($childOrderArr, 'lid, status');
                foreach ($orderInfo as $value) {
                    if ($return['package_time_share_info']) {
                        $extContent                                                   = json_decode($value['ext_content'], true);
                        $return['package_time_share_info'][$value['tid']]['time_str'] = $extContent['sectionTimeStr'] ? $extContent['sectionTimeStr'].= $timeShareBiz->getTimeShareDelayTime($extContent['sectionTimeStr'], $extContent['sectionDelayCheckInTime'] ?? 0, $extContent['sectionAheadCheckInTime'] ?? 0, $reFrom): '';
                    }
                    if ($value['status'] == 10) {
                        $return['status'] = 10;
                        break;
                    }
                }
                $lid           = $orderInfo[0]['lid'];

            }
        } else {
            $lid = $return['lid'];
        }

        $landExtDraft  = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($return['tid']);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                $landExtDraft[$attr['key']] = $attr['val'];
            }
        }
        if (!isset($landExtDraft['face_open'])) {
            $return['is_face'] = 0;
        } else {
            $return['is_face'] = (int)$landExtDraft['face_open'];
            if ($return['is_face'] == 1 && isset($landExtDraft['no_record_face_channel']) && in_array(1, explode(',', $landExtDraft['no_record_face_channel']))) {
                $return['is_face'] = 0;
            }
        }

        //$ticketModel   = new \Model\Product\Ticket();
        //$ticketExtInfo = $ticketModel->getFaceConfig([$return['tid']], 'face_open');
        //if ($ticketExtInfo === false) {
        //    $return['is_face'] = 0;
        //} else {
        //    $return['is_face'] = (int)$ticketExtInfo[0];
        //}

        // $faceModel    =  new \Model\Terminal\FaceCompare();
        // $platformData = $faceModel -> getFacePlatform($lid);
        // if($platformData['face_platform']) {
        //     $return['is_face']  = 1;
        // }
        $toolModel = new \Model\Order\OrderTools('slave');
        $extra     = $toolModel->getOrderDetailInfo($ordernum);
        if (!$extra['aids']) {
            $aid = $sid;
        } else {
            $tmp = explode(',', $extra['aids']);
            $aid = array_pop($tmp);
        }

        $return['aid'] = $aid;
        $return['product_ext'] = $extra['product_ext'];

        // 关注公众号
        $wxOpenModel = new \Model\Wechat\WxOpen();
        $info        = $wxOpenModel->getWechatOffiAccInfo((int)$sid, 'fid');
        if ($info) {
            $return['alert']      = 1;
            $return['qrcode_url'] = $info['qrcode_url'];
            $return['nick_name']  = $info['nick_name'];
        } else {
            $return['alert'] = 0;
        }

        //二维码类型 门票码判断
//        if ($return['tickets'][0]['print_mode'] == 4 && ($return['tickets'][0]['code_show_type'] == '' || $return['tickets'][0]['code_show_type'] == 1)) {
        if ($return['tickets'][0]['code_show_type'] == 1) { //新需求【订单游客信息升级一人一票后】，只根据二维码类型判断是否展示门票码
            $temidxArr = $toolModel->getTouristOrederIdxs($ordernum, 'idx, check_state, orderid, chk_code');
            $idxArr    = array_column($temidxArr, 'idx');
            if ($idxArr) {
                foreach ($temidxArr as $temVal) {
                    if ($temVal['check_state'] != 2) {
                        if (empty($temVal['chk_code'])) {
                            // 没有门票码用OD#
                            $temMultiCode                               = OrderUnity::getCodeByOrdernumTnum($ordernum,
                                $return['tickets'][0]['num'], $idxArr, $temVal['idx'])[0];
                            $return['multi_code'][]                     = $temMultiCode;
                            $return['multi_code_status'][$temMultiCode] = $temVal['check_state'];
                        } else {
                            $return['multi_code'][]                           = $temVal['chk_code'];
                            $return['multi_code_status'][$temVal['chk_code']] = $temVal['check_state'];
                        }
                    }
                }
            }
        }

        //如果是第三方订单且存在第三方订单凭证码， 则获取第三方订单凭证码
        $orderOtaBus           = new OrderOta();
        $handleCodeRes         = $orderOtaBus->handleCode($ordernum, $return['qrcode']);
        $noticeType            = $orderOtaBus->getNoticeCode($return['lid']);
        $return['notice_type'] = $noticeType;
        $return['third_api_qr_code'] = $handleCodeRes['api_qr_code'];
        //第三方订单情况处理
        if ($noticeType != -1) {
            $return['qrcode'] = $handleCodeRes['code'];//赋值为第三方的凭证码
        }
        //第三方订单凭证码生成类型为默认类型0时候的处理
        if ($noticeType == 0) {
            if (!empty($handleCodeRes['code'])) {
                $return['notice_type'] = 1;//类型为同步发码
            } else {
                $return['notice_type'] = 3;//类型为不发码
                if ($handleCodeRes['handle_status'] == 2) {
                    //超时情况下
                    $return['notice_type'] = 2;//类型为异步发码
                }
            }
            //all_api_order表没有记录情况为平台订单
            if ($handleCodeRes['handle_status'] == -1) {
                $return['notice_type'] = 0;//类型为非第三方订单
            }
        }
        //同步发码超时情况处理
        if ($noticeType == 1 && empty($handleCodeRes['code']) && $handleCodeRes['handle_status'] == 2) {
            $return['notice_type'] = 2;//类型为异步发码
        }
        //非第三方订单情况处理
        if ($noticeType == -1) {
            $return['notice_type'] = 0;//类型为非第三方订单
        }

        return $this->returnData(200, "成功", $return);
    }

    /**
     * 获取订单详细信息
     *
     * @param  int  $ordernum  订单号
     * @param  string  $reFrom  调用终端类型，微信小程序xcx,微商城h5
     *
     * @return array
     */
    private function _getOrderDetail($ordernum, $reFrom = 'h5')
    {
        $Order      = new OrderTools();
        $OrderQuery = new OrderQuery();
        $Land       = new Land();

        $orderExtra = $Order->getOrderDetailInfo($ordernum);

        $orderInfo = $Order->getOrderInfo($ordernum, '*,ss.pay_status', 'de.ext_content');

        //景区类型
        $land  = $Land->getLandInfo($orderInfo['lid'], false, 'p_type,title');
        $pType = $land['p_type'];

        //获取包含的门票信息
        $tickets = $this->_getOrderTickets(
            $orderInfo,
            $orderInfo['tid'],
            $orderInfo['tnum'],
            //是否是联票
            $orderExtra['concat_id'] ? true : false,
            $reFrom
        );

        //根据景区类型不同，获取一些额外的展示信息
        $extra = $this->_getExtraInfo(
            $pType,
            $orderInfo,
            $orderExtra
        );

        $totalmoney = $OrderQuery->get_order_total_fee($ordernum);

        //获取订单的优惠金额
        $orderCouponModel = new Coupon('slave');
        $couponInfo       = $orderCouponModel->getOrderCouponInfo($ordernum, 'emoney');
        //分时预约订单数据
        $extContent           = json_decode($orderInfo['ext_content'], true);
        $packageTimeShareInfo = $extContent['packageTimeShareInfo'];  //分时预约订单为套票是的订单扩展信息
        //存在套票分时预约数据时，根据主票id获取子票信息
        if ($packageTimeShareInfo && $pType == 'F') {
            //$javaApi = new \Business\JavaApi\Product\PackageTicket();
            //$result  = $javaApi->queryPageTicketInfoListByParentId($orderInfo['tid']);
            //$ticketApi = new TicketApi();
            //$result    = $ticketApi->getSonTicketList($orderInfo['tid']);
            $packApi   = new \Business\PackTicket\PackRelation();
            $result    = $packApi->queryPageTicketInfoListByParentId($orderInfo['tid']);
            $childList = $result['data'];
            $childList = array_key($childList, 'ticket_id');
            foreach ($packageTimeShareInfo as $key => $value) {
                if (isset($childList[$key])) {
                    $packageTimeShareInfo[$key]['ticket_name'] = $childList[$key]['ticket_name'];
                    $packageTimeShareInfo[$key]['item_name']   = $childList[$key]['item_name'];
                }
            }
        }

        return [
            'lid'                     => $orderInfo['lid'],
            'pid'                     => $orderInfo['pid'],
            'aid'                     => $orderInfo['aid'],
            'order_aid'               => $orderInfo['aid'], // aid后续会被修改 定义一个订单aid数据
            'tid'                     => $orderInfo['tid'],
            'status'                  => $orderInfo['status'],
            'ptype'                   => $land['p_type'],
            'landTitle'               => $land['title'],
            'totalmoney'              => $totalmoney / 100,
            'couponMoney'             => !empty($couponInfo) ? ($couponInfo / 100) : 0,
            'ordername'               => $orderInfo['ordername'],
            'ordertel'                => $orderInfo['ordertel'],
            'personid'                => $orderInfo['personid'],
            'qrcode'                  => (string)$orderInfo['code'],
            'tickets'                 => $tickets,
            'paymode'                 => $orderInfo['paymode'],
            'ordernum'                => $ordernum,
            'extra'                   => $extra,
            'pay_status'              => $orderInfo['pay_status'],
            'time_share_order'        => isset($extContent['sectionTimeStr']) ? $extContent['sectionTimeStr'] .= (new \Business\Product\TimeShare())->getTimeShareDelayTime($extContent['sectionTimeStr'], $extContent['sectionDelayCheckInTime'] ?? 0, $extContent['sectionAheadCheckInTime'] ?? 0, $reFrom) : '',
            'package_time_share_info' => $packageTimeShareInfo ?: "",
            'ordertime'               => $orderInfo['ordertime'],
            'playtime'                => $orderInfo['playtime'],  //微商城需要的
            'member_id'               => $orderInfo['member'],
            'ordermode'               => $orderInfo['ordermode'],
        ];
    }

    /**
     * 获取订单的门票信息
     *
     * @param  int  $orderInfo  订单信息
     * @param  int  $tid  主票tid
     * @param  int  $tnum  主票票数
     * @param  boolean  $link  是否是联票
     * @param  string  $reFrom  调用终端类型，微信小程序xcx,微商城h5
     *
     * @return [type]            [description]
     */
    private function _getOrderTickets($orderInfo, $tid, $tnum, $link = false, $reFrom = 'h5')
    {

        if ($link) {
            $field           = 'ss.tid,ss.tnum';
            $orderToolsModel = new OrderTools();
            $tickets         = $orderToolsModel->getLinkOrdersInfo($orderInfo['ordernum'], $field);
        } else {
            $tickets[] = [
                'tid'  => $tid,
                'tnum' => $tnum,
            ];
        }
        // print_r($tickets);exit;

        $priceApi   = new Price();
        //$ticketApi  = new \Business\JavaApi\TicketApi();
        $tidArray   = array_column($tickets, 'tid');
        //$tidStr     = implode(',', array_unique($tidArray));
        //$tidData    = $ticketApi->getTicketArr($tidStr);
        $javaApi  = new \Business\CommodityCenter\Ticket();
        $queryInfoArr = [];
        foreach ($tidArray as $key => $value) {
            $queryInfoArr[$key]['ticketId'] = $value;
        }
        $tidData = [];
        $ticketInfoArr = $javaApi->queryTicketInfoByQueryInfos($queryInfoArr);
        if ($ticketInfoArr) {
            foreach ($ticketInfoArr as $item) {
                $tidData[] = $javaApi->fieldConversion($item);
            }
        }
        $ticketData = [];
        foreach ($tidData as $ticket) {
            $print_mode = isset($ticket['ext']['print_mode']) ? $ticket['ext']['print_mode'] : 0;
            $ticketData[$ticket['id']] = [
                'apply_did'         => $ticket['account_id'],
                'title'             => $ticket['name'],
                'getaddr'           => $ticket['get_ticket_info'],
                'pid'               => $ticket['product_id'],
                'entry_method'      => isset($ticket['ext']['entry_method']) ? $ticket['ext']['entry_method'] : 0,
                'is_show_ordernum'  => isset($ticket['ext']['is_show_ordernum']) ? $ticket['ext']['is_show_ordernum'] : 0,
                'print_mode'        => $print_mode,
                'code_show_type'    => isset($ticket['ext']['code_show_type']) ? $ticket['ext']['code_show_type'] : '',
                'is_online_reserve' => $ticket['ext']['is_online_reserve'] ?? 0,  //期票是否是要线上预约字段
                'annual_order_type' => $ticket['annual_order_type'] ?? 0,  //年卡购买 0 填购卡人  1 填持卡人
            ];
        }
        $return = [];
        foreach ($tickets as $item) {
            //$priceRes = $priceApi->getActualtimePrice(($orderInfo['visitor'] == 1 ? $orderInfo['aid'] : $ticketData[$item['tid']]['apply_did']), $ticketData[$item['tid']]['apply_did'], $tid, $orderInfo['playtime']);
            //if ($priceRes['code'] != 200) {
            //    $price = 0; //正常都会获取到
            //} else {
            //    if ($reFrom == 'h5') {
            //        $price = $priceRes['data']['retail_price'];
            //    } else {
            //        $price = $priceRes['data']['window_price'];
            //    }
            //    // $price = $this->inWechatSmallApp() || $this->inAlipaySmallApp() ? $priceRes['data']['window_price'] / 100 : $priceRes['data']['retail_price'] / 100;
            //}
            $return[] = [
                'title'             => $ticketData[$item['tid']]['title'],
                'num'               => $item['tnum'],
                'price'             => $orderInfo['tprice'],
                'getaddr'           => $ticketData[$item['tid']]['getaddr'],
                'entry_method'      => $ticketData[$item['tid']]['entry_method'],
                'is_show_ordernum'  => $ticketData[$item['tid']]['is_show_ordernum'],
                'print_mode'        => isset($ticketData[$item['tid']]['print_mode']) ? $ticketData[$item['tid']]['print_mode'] : 0,
                'code_show_type'    => isset($ticketData[$item['tid']]['code_show_type']) ? $ticketData[$item['tid']]['code_show_type'] : '',
                'is_online_reserve' => $ticketData[$item['tid']]['is_online_reserve'] ?? 0,  //期票是否是要线上预约字段
            ];
        }

        return $return;
    }

    /**
     * 根据票种获取不同的订单信息
     *
     * @param  string  $type  类型
     * @param  array  $orderInfo  订单信息
     * @param  array  $orderExtra  订单额信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfo($type, $orderInfo, $orderExtra)
    {

        switch ($type) {
            case 'A':
                return $this->_getExtraInfoForLand($orderInfo);
                break;

            case 'F':
                return $this->_getExtraInfoForLand($orderInfo);
                break;

            case 'B':
                return $this->_getExtraInfoForLoad($orderInfo, $orderExtra);
                break;

            case 'C':
                return $this->_getExtraInfoForHotel($orderInfo);
                break;

            case 'H':
                return $this->_getExtraInfoForShow($orderExtra, $orderInfo);
                break;

            case 'I':
                return $this->_getExtraInfoForAnnual($orderInfo);
                break;

            case 'J':
                return $this->_getExtraInfoForSpecial($orderInfo, $orderExtra);
                break;

            default:
                # code...
                break;
        }
    }

    /**
     * 获取景区订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLand($orderInfo)
    {

        //有效时间
        $date = $orderInfo['begintime'] . '~' . $orderInfo['endtime'];

        return [
            'date' => $date,
        ];
    }

    /**
     * 获取线路订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForLoad($orderInfo, $orderExt = [])
    {

        //集合时间
        $date        = $orderInfo['begintime'];
        $ticketModel = new Ticket();
        $ticket      = $ticketModel->getTicketExtInfoByTid($orderInfo['tid'], 'ass_station');

        //集合地点
        $station = $ticket['ass_station'] ? json_decode($ticket['ass_station'], true) : [];

        return [
            'date'    => $date,
            'station' => $station[$orderExt['assembly']],
        ];
    }

    /**
     * 获取酒店订单的信息
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return [type]            [description]
     */
    private function _getExtraInfoForHotel($orderInfo)
    {

        $orderToolsModel = new OrderTools();
        $link            = $orderToolsModel->getLinkOrdersInfo($orderInfo['ordernum'], 'endtime');

        //$last = array_pop($link);
        $beginTimeStr = min(array_column($link, 'begintime'));
        $endTimeStr = max(array_column($link, 'endtime'));

        if (empty($beginTimeStr)) {
            $beginTimeStr = $orderInfo['begintime'];
        }
        if (empty($endTimeStr)) {
            $endTimeStr = $orderInfo['endtime'];
        }

        $begintime = strtotime($beginTimeStr);
        $endtime   = strtotime($endTimeStr);

        //住店时间
        $date = date('Y-m-d', $begintime) . '~' . date('Y-m-d', $endtime);

        //住店天数
        $days = count($link) ?: 1;

        return [
            'date' => $date,
            'days' => $days,
        ];

    }

    /**
     * 获取演出订单的信息
     *
     * @param  [type] $orderExtra 订单额外信息
     *
     * @return [type]             [description]
     */
    private function _getExtraInfoForShow($orderExtra, $orderInfo = '')
    {
        $series = unserialize($orderExtra['series']);

        //演出日期
        $date = $series[11] ?? $series[4];

        //座位
        $seat = explode(',', $series[6])[2];
        //是否展示座位号 0不展示 1展示
        $isShowSeat = 1;
        if (!empty($orderInfo)) {
            $tid           = $orderInfo['tid'];
            $ticketExtInfo = [];
            $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
            $getTicketInfo = $ticketBiz->queryTicketAttrsById($tid);
            if ($getTicketInfo['code'] == 200) {
                foreach ($getTicketInfo['data'] as $attr) {
                    if ($attr['key'] == 'is_show_seat') {
                        $ticketExtInfo[$attr['ticket_id']] = $attr;
                    }
                }
            }
            //$ticketModel   = new Ticket();
            //$ticketExtInfo = $ticketModel->getTicketExtConfig([$orderInfo['tid']], 'ticket_id,key,val',
            //    ['is_show_seat']);
            //自由入座不展示座位号
            if (!empty($ticketExtInfo[$tid]['val']) && $ticketExtInfo[$tid]['val'] == 2) {
                $isShowSeat = 0;
                $seat       = '';
            }
        }
        //处理区-座位
        $seatZone = '';
        $seatInfo = explode(',', $series[6]);
        if (!empty($seatInfo)) {
            $seatStr  = '';
            $zoneName = explode(':', $seatInfo[1])[1];
            $seatArr  = explode('_', $seatInfo[2]);
            foreach ($seatArr as $item) {
                if (empty($item)) {
                    continue;
                }
                $tmp     = explode('-', $item);
                $tmp[0]  = str_replace("座位号:", '', $tmp[0]);
                $seatStr .= $tmp[2] .$tmp[0] . '排' . $tmp[1] . '座，';
            }
            $seatStr = rtrim($seatStr, "，");
            //有座位号且是按号入座的 展示分区 + 座位号
            if (!empty($seatStr) && $isShowSeat) {
                $seatZone = "({$zoneName}) - " . $seatStr;
            } else {
                $seatZone = "(自由入座)";
            }
        }

        return [
            'date'     => $date,
            'seat'     => $seat,
            'seatZone' => $seatZone,
        ];
    }

    /**
     * 获取年卡订单信息
     * <AUTHOR>
     * @date   2017-11-22
     *
     * @param  array  $orderInfo  订单详情
     *
     * @return array
     */
    private function _getExtraInfoForAnnual($orderInfo)
    {

        $ordernum = (string)$orderInfo['ordernum'];

        //获取虚拟卡号
        $annualModel = new AnnualCard('slave');

        $virtualNo = $annualModel->getVirtualNoByOrdernum($ordernum);

        if (!empty($virtualNo)) {
            $cardInfo = $annualModel->getAnnualCardBycardNoAndsid($virtualNo, $orderInfo['aid'], 1);
            if (!empty($cardInfo)) {
                $ret= [
                    'active_date' => $orderInfo['begintime'] . '~' . $orderInfo['endtime'],
                    'avalid_begin' => $cardInfo['avalid_begin'] ? date('Y-m-d', $cardInfo['avalid_begin']) : '',
                    'avalid_end'   => $cardInfo['avalid_end'] ? date('Y-m-d', $cardInfo['avalid_end']) : '',
                    'virtual_no'   => $virtualNo ?: '',
                    'physics_no'   => '',
                ];
				$is_active = 0;
	            $valid_date = '';
				if($cardInfo['status'] == 1) {  // 表示已经激活
					$is_active = 1;
					$valid_date = date('Y-m-d',$cardInfo['avalid_begin']).'~'.date('Y-m-d',$cardInfo['avalid_end']);
				}
				$ret['is_active'] = $is_active;
				$ret['valid_date'] = $valid_date;
				return $ret;
            }
        }

        return [
            'virtual_no' => $virtualNo ?: '',
            'physics_no' => '',
        ];
    }

    /**
     * 获取特产订单信息
     * <AUTHOR>
     * @date   2017-12-13
     *
     * @param  array  $orderInfo  订单详情
     * @param  array  $orderExtra  订单额外信息
     *
     * @return array
     */
    private function _getExtraInfoForSpecial($orderInfo, $orderExtra)
    {
        $return     = [];
        $extArr     = json_decode($orderExtra['product_ext'], true);
        $tid        = $orderInfo['tid'];
        //获取特产配置信息
        $specialGoodServiceApi = new SpecialtyTicket();
        $goodsInfo             = $specialGoodServiceApi->querySpecialPriceByGoodsIdAndTicketIdsAndEvolute($orderInfo['aid'],
            0, 0, $orderInfo['lid'], [$orderInfo['tid']]);
        //获取特产景区信息
        $return['ttitle']  = "";
        if (!empty($goodsInfo['data'][0]['specialValue'])) {
            foreach ($goodsInfo['data'][0]['specialValue'] as $value) {
                $return['ttitle'] .= $value['itemName'] . ":" . $value['valueName'];
            }
        }
        //计算订单有效期
        $landInfo      = $specialGoodServiceApi->getSpecialGoodsByGidOrLid('', $orderInfo['lid']);
        $orderValueDay = date('Y-m-d H:i:s', strtotime("+ {$landInfo[0]['goodsDeliveryAttribute']['no_pick_auto_cancel']} day", strtotime($orderInfo['ordertime'])));
        //deliveryType  0:快递  1:自取
        $return['delivery_way'] = $extArr['deliveryType'];
        if ($extArr['deliveryType'] == 1) {
            //请求自提点信息
            $pickPointApi = new PickPoint();
            $pickData     = $pickPointApi->queryExpressPickPointByTid($orderInfo['tid']);
            $areaCodeArr = explode('|', $pickData['areaCode']);
            $areaCodeArr = array_filter($areaCodeArr);
            if ($areaCodeArr) {
                $areaModel = new \Model\Product\Area();
                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
            } else {
                $codeMap   = [];
            }
            //自提点信息
            $return['take_delivery'] = [
                'area'               => implode('', array_values($codeMap)),
                'address'            => $pickData['address'],
                'linkman'            => $pickData['linkman'] . '/' . $pickData['phone'],
                'reception_time'     => $pickData['acceptTime'],
                'cancel_auto_on_min' => $orderValueDay,
            ];
            $return['delivery_price'] = 0;
        } else {
            //快递
            //收货信息
//            $orderUserModel = new \Model\Order\OrderUser();
//            $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($orderInfo['ordernum'], 'personid,voucher_type,province_code,city_code,town_code,address');

            $queryParams = [[$orderInfo['ordernum']]];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo','getOrderUserInfoByOrderNum', $queryParams);
            $orderUserInfo = [];
            if ($queryRes['code'] == 200) {
                $orderUserInfo = $queryRes['data'][0];
            }

            $areaCodeArr = [$orderUserInfo['province_code'], $orderUserInfo['city_code'], $orderUserInfo['town_code']];
            $areaCodeArr = array_filter($areaCodeArr);
            if ($areaCodeArr) {
                $areaModel = new \Model\Product\Area();
                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
            } else {
                $codeMap   = [];
            }
            $return['take_delivery'] = [
                'province'    => $codeMap[$orderUserInfo['province_code']] ?? '',
                'city'        => $codeMap[$orderUserInfo['city_code']] ?? '',
                'town'        => $codeMap[$orderUserInfo['town_code']] ?? '',
                'address'     => $orderUserInfo['address'],
                'exp_company' => $extArr['expCompany'] ?? '',
                'exp_no'      => $extArr['expNo'] ?? '',
            ];
            $return['delivery_price'] = $extArr['carriage'] ?? 0;
        }
        $return['take_delivery']['code']       = $orderInfo['code'];
        $return['take_delivery']['valid_date'] = $orderValueDay;

        return $return;
    }

    /**
     *  获取订单详情供微信查单使用
     * <AUTHOR>
     * @date 2020/10/10
     *
     * @param  string  $orderNum    订单号
     *
     * @return array
     */
    public function getOrderInfoForWxByOrdernum(string $orderNum)
    {

        $orderTools = new OrderTools();
        $orderInfo  = $orderTools->getOrderInfo($orderNum, 'member,aid,visitors,salerid');

        $memberInfo = [];
        try {
            $memberModel = new \Model\Member\Member('slave');
            $memberInfo  = $memberModel->getMemberInfo($orderInfo['member'], 'id', 'dtype,account,id');

        } catch (\Exception $exception) {
            return $this->returnDataV2(203, [], "查询失败");
        }

        //获取
        $sid        = $orderInfo['aid'];
        $memberType = $memberInfo['dtype'];
        $account    = $memberInfo['account'];
        $currentFid = $orderInfo['member'];

        //判断身份 取当前符合要求的会员id集合
        $salerId = false;
        if ($sid == 1) {
            //管理员
            $currentFid = 1;
        } elseif ($memberType == 7) {
            //集团账号
            $orderSearchBusiness = new \Business\Order\OrderSearch();
            $currentFid          = $orderSearchBusiness->getRelationMember($sid);
        } elseif (in_array($memberType, [2, 3])) {
            //景区账号
            $salerId    = $account;
            $orderTools = new OrderTools();
            $orderInfo  = $orderTools->getOrderInfo($orderNum, 'salerid');

            if ($salerId != $orderInfo['salerid']) {
                return $this->returnDataV2(203, [], "无权查看此订单");
            }
        } else {
            //对当前查询订单的用户做权限校验
            //$chainModel = new \Model\Order\SubOrderQuery\SubOrderSplit();
            //$orderChain = $chainModel->getListByOrderSingle($orderNum, 'id,buyerid,sellerid');

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $orderChain = $orderAidsSplitQueryLib->getListByOrderSingleNew(strval($orderNum));

            if (empty($orderChain)) {
                return $this->returnDataV2(203, [], "订单分销链数据不存在");
            }

            $buyerid  = array_column($orderChain, 'buyerid');
            $sellerid = array_column($orderChain, 'sellerid');
            $members  = array_unique(array_merge($buyerid, $sellerid));

            if ($currentFid != 1 && !in_array($currentFid, $members)) {
                return $this->returnDataV2(203, [], "无权查看此订单");
            }
        }

        $orderListBiz = new \Business\Order\OrderList();
        $orderDetail  = $orderListBiz->handleOrderDetailForWx($orderNum, $currentFid, $memberType, $sid);

        return $this->returnDataV2(200, $orderDetail, "获取订单详情成功");
    }

    /**
     *  订单列表查询供微信查单使用（其他项目不允许使用）
     *  页面：https://wx.12301.cc/c/wechat_query.html，不知道是哪个景区在使用
     * <AUTHOR>
     * @date 2020/10/12
     *
     * @param  string  $sid  商户ID
     * @param  string  $mid  分销ID，批量：|分隔
     * @param  string  $lid  景区ID
     * @param  string  $tid  门票ID
     * @param  string  $btime1  下单时间1
     * @param  string  $etime1  下单时间2
     * @param  string  $btime2  预计游玩时间1 9
     * @param  string  $etime2  预计游玩时间2 10
     * @param  string  $btime3  订单完成时间1 11
     * @param  string  $etime3  订单完成时间2 12
     * @param  string  $ordernum  订单号     13
     * @param  string  $mobile  15:取票人手机
     * @param  string  $status  16状态（0未使用|1已使用|2已过期|3被取消|4凭证码被替代|5被终端修改|6被终端撤销）
     * @param  string  $pays  17支付状态（0景区到付|1成功|2未支付）
     * @param  int  $orderby  19排序(1下单时间|2游玩时间|3实际使用时间|4商户ID|5景区标题|6取消时间)
     * @param  int  $sort  升序或降序（0升序|1降序)
     * @param  int  $pageStart  记录起始指针
     * @param  int  $pageSize  返回条数
     * @param  int  $c  返回类型（0详细1返回总数2逗号隔开字符串#订单数,票数,总数）
     * @param  int  $ordermode  下单方式（0正常分销商下单1普通用户下单2手机下单 注：1、2下支付方式只能是账户余额或支付宝,取票人手机当作登录号）
     * @param  string  $payinfo  支付（为空不做筛选0帐号余额支付1支付宝2使用供应商可用金额支付)
     * @param  string  $remotenum  27/远端订单号,
     * @param  int  $origin  28/客源地
     * @param  string  $order_confirm  30/确认订单状态(0无需确认 1待确认 2已确认未验证 3已确认已验证 4确认后取消 5确认后修改)
     * @param  string  $aid  31/供应商ID（批量：|分隔）
     * @param  int  $concat  32/关联订单(0显示所有订单 1显示关联订单【订单号不能为空】）
     * @param  int  $ifpack  33/套票（默认0正常 1套票 2子票）
     * @param  null  $contacttel  36联系人手机
     * @param  string  $code  38
     * @param  array  $dbLinkConfig  39 链接历史数据库配置
     *
     * @return array
     */
    public function orderGlobalSearch($sid, $mid, $lid, $tid, $btime1, $etime1, $btime2, $etime2, $btime3, $etime3,
        $ordernum, $mobile = '', $status = '', $pays = '', $orderby = 0, $sort = 0, $pageStart = 0, $pageSize = 40,
        $c = 0, $ordermode = 0, $payinfo = "", $remotenum = "", $origin = 0, $order_confirm = '', $aid = '',
        $concat = 0, $ifpack = 0, $contacttel = null, $code = '', $dbLinkConfig = [])
    {
        if (empty($btime1) || empty($etime1) || empty($mobile) || empty($pageSize)) {
            return $this->returnDataV2(103, [], "参数缺失");
        }

        $orderListBiz = new \Business\Order\OrderSearch();
        $list         = $orderListBiz->Order_Globle_Search($sid, $mid, $lid, $tid, $btime1, $etime1, $btime2, $etime2,
            $btime3, $etime3, $ordernum, $mobile, $status, $pays, $orderby, $sort, $pageStart, $pageSize, $c,
            $ordermode, $payinfo, $remotenum, $origin, $order_confirm, $aid, $concat, $ifpack, $contacttel, $code,
            $dbLinkConfig);

        return $this->returnDataV2(200, $list, "查询订单列表成功");
    }

}

