<?php
/**
 * 外部码配置接口
 */

namespace Business\ExternalCode;

use Business\JavaApi\CommodityCenter\Ticket as TicketApi;
use Business\JavaApi\ProductApi;
use Business\JavaApi\CommodityCenter\Ticket;
use Business\ExternalCode\ECBase;
use \Library\Cache\Cache;
use Business\Product\ExchangeCouponProduct as ExchangeCouponProductBiz;

class CodeManage extends ECBase
{

    //日志目录
    private $_debugLogDir = 'external_code/code_manage/';
    //错误记录日志
    private $_errorLogDir = 'external_code/error';

    //票属性外部码状态：1启用  0禁用
    const TICKET_EXT_CODE_STATE_YES = 1;
    const TICKET_EXT_CODE_STATE_NO  = 0;

    //回收站查询
    public $recycle = false;

    //应用唯一标识
    const TAG_NAME = 'external_code';

    //外部码应用开通信息缓存
    const USER_EXTERNAL_CODE_MODULE_CACHE = 'external_code:used_module_info:%u';

    public function __construct()
    {

    }


    //===============发码管理=========================

    /**
     * 获取外部码配置列表
     * <AUTHOR>
     * @date 2020/7/21
     *
     * @param  int $memberSid 供应商id
     * @param  string $extTitle 名称查询
     * @param  int $landId 景区id
     * @param  int $ticketId 门票id
     * @param  int $searchSId 搜索的供应商id （管理员用）
     * @param  int $sendType 发码方式 1一单一码；2一单多码 （管理员用）
     * @param  int $state 状态：0停用;1启用
     * @param  int $page 分页
     * @param  int $size 页数
     *
     * @return array
     */
    public function queryExtListByMemberId(int $memberSid, int $searchSId, int $sendType, string $extTitle = '', int $landId = 0, int $ticketId = 0, int $state = 999, int $page = 1, int $size = 10)
    {
        if (!$memberSid) {
            return $this->returnData(203, '参数错误');
        }

        $data = [];
        if (!empty($extTitle)) {
            $data['title'] = $extTitle;
        }

        if (!empty($landId)) {
            $data['land_id'] = $landId;
        }

        if (!empty($ticketId)) {
            $data['ticket_id'] = $ticketId;
        }

        if (!empty($sendType)) {
            $data['send_type'] = $sendType;
        }

        if ($memberSid == 1 && !empty($searchSId)) {
            $data['member_id'] = $searchSId;
        } else {
            $data['member_id'] = $memberSid;
        }

        if (in_array($state, [0, 1])) {
            $data['state'] = $state;
        }

        $data['size'] = $size;
        $data['page'] = $page;

        //回收站访问
        if ($this->recycle) {
            $data['recycle'] = 1;
        }

        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_TICKET_LIST_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200 || !is_array($result['data'])) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        $result_data = $result['data']['list'];

        $list = [];

        if ($result_data) {
            //查询所有票名称
            $ticket_ids   = array_column($result_data, 'ticket_id');
            $batchQuery   = new TicketApi();
            $tickets      = $batchQuery->batchQueryTicketByTicketIds($ticket_ids);
            $tickets_data = [];
            $ticketsStatusdata = [];
            if (!empty($tickets['data'])) {
                foreach ($tickets['data'] as $k => $v) {
                    $tickets_data[$v['id']]      = $v['title'];
                    $ticketsStatusdata[$v['id']] = $v['status'] == 1 ? true : false; //1有效 其他无效
                    //已过期需要单独验证下
                    if ($ticketsStatusdata[$v['id']]) {
                        //对比日期
                        if (date("Ymd", strtotime($v['max_expiration_date'])) < date("Ymd")) {
                            $ticketsStatusdata[$v['id']] = false; //已过期，无效
                        }
                    }
                }
            }

            //获取景区名称
            $landIdArr = array_column($result_data, 'land_id');
            $javaAPi  = new \Business\CommodityCenter\Land();
            $landInfo = $javaAPi->queryLandMultiQueryById($landIdArr);
            $landMap  = array_column($landInfo, 'title', 'id');

            //获取商户信息
            $memberIdArr = array_column($result_data, 'member_id');
            $memberBiz   = new \Business\Member\Member();
            $memberMap   = $memberBiz->getMemberInfoByMulti($memberIdArr, 'id', true);

            foreach ($result_data as $key => $item) {
                //获取景区和票名称
                $item['land_name']   = $landMap[$item['land_id']] ?? '';
                $item['ticket_name'] = $tickets_data[$item['ticket_id']] ?? '';
                //是否无效 1是2不是
                $isNotInvalid = $landMap[$item['land_id']] ? 2 : 1;
                if ($isNotInvalid == 2) {//景区有效再验证票是否有效
                    $isNotInvalid = $tickets_data[$item['ticket_id']] ? 2 : 1;
                }
                //无剩余可用的
                if (0 == $item['surplus']) {
                    $item['state_msg'] = '无可用码，已暂停发码';
                }
                //标记票的有效性，如果无可用码和失效同时存在，展示失效提示
                if ($isNotInvalid == 1 || (isset($ticketsStatusdata[$item['ticket_id']]) && !$ticketsStatusdata[$item['ticket_id']])) {
                    $item['state_msg'] = '关联票失效，已暂停发码';
                }

                if (isset($memberMap[$item['member_id']])) {
                    $item['account'] = $memberMap[$item['member_id']]['account'];
                    $item['dname']   = $memberMap[$item['member_id']]['dname'];
                }

                $list[] = $item;

            }
        }

        $return = [
            'list'         => $list,
            'total'        => $result['data']['total'] ?: 0,
            'current_page' => $result['data']['current_page'] ?: 1,
            'last_page'    => $result['data']['last_page'] ?: 1,
        ];

        return $this->returnData(200, $result['msg'], $return);

    }

    /**
     * 外部码关联票，配置表单提交
     *
     * <AUTHOR>
     * @date 2020-07-20
     *
     * @param  int $memberSid 供应商id
     * @param  int $memberId 当前用户id
     * @param  string $title 外部码关联票配置名称
     * @param  int $landId 景区id
     * @param  int $ticketId 门票id
     * @param  int $state 状态：1启用；0禁用
     * @param  int $sendType 发码方式：1一单一码；2一单多码
     * @param  int $expireType 游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
     * @param  string $startDt 当expire_type=3时必传属性，例如2020/09/01
     * @param  string $endDt 当expire_type=3时必传属性，例如2020/09/31
     * @param  int $showCode 短信是否显示外部码: 1展示；2不展示
     * @param  int $showDetails 短信是否显示订单详情: 1展示；2不展示
     * @param  string $remark 备注
     *
     * 注：需要日志记录操作人员id
     *
     * @return array
     */
    public function addConfigBySubmit(int $memberSid, int $memberId, string $title, int $landId, int $ticketId, int $state = 0, int $sendType = 2, int $expireType = 1, string $startDt = '', string $endDt = '', int $showCode = 1, int $showDetails = 1, string $remark = '')
    {
        if (!$memberSid || !$memberId || !$title || !$landId || !$ticketId) {
            return $this->returnData(203, '参数错误');
        }

        if (mb_strlen($title, 'UTF-8') > 10) {
            return $this->returnData(203, '名称不能超过10个字符');
        }

        if (!in_array($state, [1, 0])) {
            return $this->returnData(203, '状态设置出错');
        }

        if (!in_array($sendType, [1, 2])) {
            return $this->returnData(203, '发码方式设置出错');
        }

        //有效期验证
        if (!in_array($expireType, [1, 2, 3])) {
            return $this->returnData(203, '有效期设置异常');
        }

        //有效期管理验证必填
        if ($expireType == 3 && (empty($startDt) || empty($endDt))) {
            return $this->returnData(203, '设置固定有效期，日期选择异常');
        }

        //验证短信是否显示外部码
        if (!in_array($showCode, [1, 2])) {
            return $this->returnData(203, '短信是否展示外部码设置异常');
        }

        //验证短信是否显示订单详情
        if (!in_array($showDetails, [1, 2])) {
            return $this->returnData(203, '短信是否展示订单详情设置异常');
        }

        if (mb_strlen($remark, 'UTF-8') > 100) {
            return $this->returnData(203, '备注不能超过100个字');
        }

        //验证景区参数
        $batchQuery = new Ticket();
        $tickets    = $batchQuery->batchQueryTicketByTicketIds([$ticketId]);
        if (empty($tickets['data'])) {
            return $this->returnData(203, '票信息无效');
        }
        $ticket = $tickets['data'][0];
        //比对供应商和景区id
        if ($ticket['apply_did'] != $memberSid || $ticket['landid'] != $landId) {
            return $this->returnData(203, '票信息不匹配', $ticket);
        }

        //判断票是否绑定上游
        $isNotExist = $this->_checkThridTicket([$ticketId]);
        if (!in_array($ticketId, $isNotExist)) {
            //绑定了上游的票，不支持
            return $this->returnData(400, '该票绑定第三方，不支持配置外部码');
        }

        //判断是不是预售券
        if ((new ExchangeCouponProductBiz())->isExchangeCouponByLandId($landId)) {
            return $this->returnData(400, '预售券产品，不支持配置外部码');
        }

        $data   = [
            'member_id'    => $memberSid,
            'title'        => $title,
            'land_id'      => $landId,
            'ticket_id'    => $ticketId,
            'state'        => $state,
            'send_type'    => $sendType,
            'expire_type'  => $expireType,
            'start_dt'     => $startDt,
            'end_dt'       => $endDt,
            'show_code'    => $showCode,
            'show_details' => $showDetails,
            'remark'       => $remark,
        ];
        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CONFIG_SUBMIT_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        //编辑门票是否发码属性
        $extRes = $this->saveExtInfo($memberSid, $memberId, [$ticketId], $state);
        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        //成功 记录日志 记录操作人员
        pft_log('external_code/debug', json_encode(['_addConfigBySubmit', [$memberId, $data]]));

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 外部码关联票配置 禁用或者启用
     * <AUTHOR>
     * @date 2020-07-20
     *
     * @param  int $memberSid 供应商id
     * @param  int $memberId 当前操作人员id
     * @param  int $extId 外部码关联票配置id
     * @param  int $state 状态：1启用；0禁用
     * @param  int $ticketId 门票id
     *
     * @return array
     */
    public function updateConfigByExtId(int $memberSid, int $memberId, int $extId, int $state, int $ticketId)
    {
        if (!$memberSid || !$memberId || !$extId) {
            return $this->returnData(203, '参数错误');
        }

        if (!in_array($state, [1, 0])) {
            return $this->returnData(203, '状态设置出错');
        }

        //启用时要判断是否绑定上游
        if ($state == 1) {
            $isNotExist = $this->_checkThridTicket([$ticketId]);
            if (!in_array($ticketId, $isNotExist)) {
                return $this->returnData(203, '该票绑定第三方，不支持启用外部码');
            }
        }

        $data = [
            'member_id' => $memberSid,
            'config_id' => $extId,
            'state'     => $state,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CONFIG_DISABLE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        //编辑门票是否发码属性
        $extRes = $this->saveExtInfo($memberSid, $memberId, [$ticketId], $state);

        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        //成功 记录日志 记录操作人员
        pft_log('external_code/debug', json_encode(['_updateConfigByExtId', [$memberId, $data]]));

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 批量禁启用
     * <AUTHOR>
     * @date 2021/6/25
     *
     * @param  int  $sid  商户id
     * @param  int  $memberId  当前账号id
     * @param  array  $extIds  配置id
     * @param  int  $state  状态 1启用 0禁用
     * @param  array  $tids 票id
     *
     * @return array
     */
    public function updateConfigByExtIds(int $sid, int $memberId, array $extIds, int $state, array $tids)
    {
        if (!$sid || !$memberId || empty($extIds) || empty($tids)) {
            return $this->returnData(203, '参数错误');
        }

        if (!in_array($state, [1, 0])) {
            return $this->returnData(203, '状态设置出错');
        }

        $data = [
            'member_id' => $sid,
            'config_id' => $extIds,
            'state'     => $state,
        ];

        $result = $this->_handelRequest($memberId, self::__EXTERNALCODE_CONFIG_BATCH_DISABLE_FUN__, $data);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], []);
        }

        //编辑门票是否发码属性
        $extRes = $this->saveExtInfo($sid, $memberId, $tids, $state);
        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 删除外部码配置到回收站
     * <AUTHOR>
     * @date 2021/6/26
     *
     * @param  int  $sid
     * @param  int  $memberId
     * @param  array  $extIds
     *
     * @return array
     */
    public function delConfigByExtIds(int $sid, int $memberId, array $extIds)
    {
        if (!$sid || !$memberId || empty($extIds)) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id' => $sid,
            'config_id' => $extIds,
        ];

        $result = $this->_handelRequest($memberId, self::__EXTERNALCODE_MOVE_RECYCLE_BIN_FUN__, $data);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], []);
        }

        if (empty($result['data']) || !is_array($result['data'])) {
            return $this->returnData(204, '外部码信息更新成功，门票信息获取失败', []);
        }

        $tids = $result['data'];

        //关闭票属性外部码配置
        $extRes = $this->saveExtInfo($sid, $memberId, $tids, self::TICKET_EXT_CODE_STATE_NO);
        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 应用失效后，关闭最后状态
     * <AUTHOR>
     * @date 2021/7/9
     *
     * @param  int  $sid  上级账号
     * @param  int  $memberId 当前操作人
     * @param  int  $tid  票id
     *
     * @return array
     */
    public function closeLastStateByTid(int $sid, int $memberId, int $tid)
    {
        if (!$sid || !$memberId || !$tid) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'  => $sid,
            'ticket_id' => $tid,
        ];

        $result = $this->_handelRequest($sid, self::__EXTERNALCODE_EDIT_CONFIG_LASTSTATE_FUN__, $data);

        //记录操作人员
        pft_log('external_code/debug', json_encode(['_closeLastStateByTid', [$memberId, $data, $result]],JSON_UNESCAPED_UNICODE));

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 回收站恢复
     * <AUTHOR>
     * @date 2021/6/26
     *
     * @param  int  $sid 商户id
     * @param  int  $memberId  当前账号id
     * @param  array  $extIds 配置id
     *
     * @return array
     */
    public function discardRecoveryConfigByExtIds(int $sid, int $memberId, array $extIds)
    {
        if (!$sid || !$memberId || empty($extIds)) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id' => $sid,
            'config_id' => $extIds,
        ];

        $result = $this->_handelRequest($memberId, self::__EXTERNALCODE_DISCARD_RECOVARY_FUN__, $data);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], []);
        }

        if (empty($result['data']) || !is_array($result['data'])) {
            return $this->returnData(204, '外部码信息更新成功，门票信息获取失败', []);
        }

        $tids = $result['data'];

        //恢复票属性外部码配置
        $extRes = $this->saveExtInfo($sid, $memberId, $tids, self::TICKET_EXT_CODE_STATE_YES);
        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        return $this->returnData(200, $result['msg'], []);
    }

    /**
     * 回收站-永久删除
     * <AUTHOR>
     * @date 2021/6/28
     *
     * @param  int  $sid  商户id
     * @param  int  $memberId  当前账号id
     * @param  array  $extIds 配置id
     *
     * @return array
     */
    public function delForeverConfigByExtIds(int $sid, int $memberId, array $extIds)
    {
        if (!$sid || !$memberId || empty($extIds)) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id' => $sid,
            'config_id' => $extIds,
        ];

        $result = $this->_handelRequest($memberId, self::__EXTERNALCODE_DEL_FOREVER_FUN__, $data);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], []);
        }

        if (empty($result['data']) || !is_array($result['data'])) {
            return $this->returnData(204, '外部码信息更新成功，门票信息获取失败', []);
        }

        $tids = $result['data'];

        //关闭票属性外部码配置
        $extRes = $this->saveExtInfo($sid, $memberId, $tids, self::TICKET_EXT_CODE_STATE_NO);
        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        return $this->returnData(200, $result['msg'], []);
    }

    /**
     * 获取外部码配置
     * <AUTHOR> Li
     * @date 2020-07-28
     *
     * @param  int $memberSid 供应商id
     * @param  int $memberId 当前操作人员id
     * @param  int $extId 外部码关联票配置id
     *
     * @return array
     */
    public function getConfigInfoByExtId(int $memberSid, int $memberId, int $extId)
    {
        if (!$memberSid || !$memberId || !$extId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id' => $memberSid,
            'config_id' => $extId,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_TICKET_INFO_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存门票是否发码扩展信息
     * <AUTHOR> Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $memberId 操作员id
     * @param  array $ticketIdArr 门票id数组
     * @param  int $state 状态：1启用；0禁用
     *
     * @return  bool
     */

    public function saveExtInfo(int $sid, int $memberId, array $ticketIdArr, int $state)
    {
        //保存扩展数据属性，目前支持单票，多属性，所以多票的时候要处理下
        $params = [];
        $ticketIdArr = array_unique($ticketIdArr);
        foreach ($ticketIdArr as $ticketId) {
            $params[$ticketId][] = [
                'ticketId' => $ticketId,
                'key'      => 'is_external_send_code',
                'val'      => $state,
            ];
        }

        if (empty($params)) {
            return false;
        }

        //多票需要循环处理下，后续有多票的口，可以在调整下
        foreach ($params as $item) {
            //获取票类扩展属性字典
            $ticketExtConf = new \Business\JavaApi\Ticket\TicketExtendAttr();
            $extSaveRes    = $ticketExtConf->save($sid, $memberId, $item);

            if ($extSaveRes['code'] != 200) {
                $ticketInfo = implode(',', $ticketIdArr);
                pft_log('ticket_ext/debug' . 'config/take_off', "门票:{$ticketInfo}保存扩展属性失败, 操作员{$memberId}");

                return false;
            }
        }

        return true;
    }

    /**
     * 外部码关联票配置 编辑提交接口
     * <AUTHOR>
     * @date 2020-07-20
     *
     * @param  int $memberSid 供应商id
     * @param  int $memberId 当前用户id
     * @param  int $extId 外部码关联票配置id
     * @param  string $title 外部码关联票配置名称
     * @param  int $state 状态：1启用；0禁用
     * @param  int $sendType 发码方式：1一单一码；2一单多码
     * @param  int $ticketId 门票id
     * @param  int $expireType 游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
     * @param  string $startDt 当expire_type=3时必传属性，例如2020/09/01
     * @param  string $endDt 当expire_type=3时必传属性，例如2020/09/31
     * @param  int $showCode 短信是否显示外部码: 1展示；2不展示
     * @param  int $showDetails 短信是否显示订单详情: 1展示；2不展示
     * @param  string $remark 备注
     *
     * @return array
     */
    public function editConfigByExtId(int $memberSid, int $memberId, int $extId, string $title, int $state, int $sendType, int $ticketId, int $expireType, string $startDt, string $endDt, int $showCode, int $showDetails, string $remark)
    {
        if (!$memberSid || !$memberId || !$title || !$extId || !$ticketId || !$expireType || !$showCode || !$showDetails) {
            return $this->returnData(203, '参数错误');
        }

        if (mb_strlen($title, 'UTF-8') > 10) {
            return $this->returnData(203, '名称不能超过10个字符');
        }

        if (!in_array($state, [1, 0])) {
            return $this->returnData(203, '状态设置出错');
        }

        if (!in_array($sendType, [1, 2])) {
            return $this->returnData(203, '发码方式设置出错');
        }

        //有效期验证
        if (!in_array($expireType, [1, 2, 3])) {
            return $this->returnData(203, '有效期设置异常');
        }

        //有效期管理验证必填
        if ($expireType == 3 && (empty($startDt) || empty($endDt))) {
            return $this->returnData(203, '设置固定有效期，日期选择异常');
        }

        //验证短信是否显示外部码
        if (!in_array($showCode, [1, 2])) {
            return $this->returnData(203, '短信是否展示外部码设置异常');
        }

        //验证短信是否显示订单详情
        if (!in_array($showDetails, [1, 2])) {
            return $this->returnData(203, '短信是否展示订单详情设置异常');
        }

        if (mb_strlen($remark, 'UTF-8') > 100) {
            return $this->returnData(203, '备注不能超过100个字');
        }

        $data = [
            'member_id'    => $memberSid,
            'config_id'    => $extId,
            'title'        => $title,
            'state'        => $state,
            'send_type'    => $sendType,
            'expire_type'  => $expireType,
            'start_dt'     => $startDt,
            'end_dt'       => $endDt,
            'show_code'    => $showCode,
            'show_details' => $showDetails,
            'remark'       => $remark,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CONFIG_COMPILE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        //编辑门票是否发码属性
        $extRes = $this->saveExtInfo($memberSid, $memberId, [$ticketId], $state);

        if (!$extRes) {
            return $this->returnData(204, '外部码信息更新成功，门票信息更新失败', []);
        }

        //成功 记录日志 记录操作人员
        pft_log('external_code/debug', json_encode(['_editConfigByExtId', [$memberId, $data]]));

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    //===============批次管理=========================

    /**
     * 获取批次管理列表
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 外部码关联票配置id
     * @param  string $orderNum 订单号
     * @param  string $code 三方码
     * @param  int $page 当前页数
     * @param  int $size 每页条数
     *
     * @return array
     */
    public function getCodeBatchList(int $sid, int $operatorId, int $confId, string $orderNum, string $code, int $page = 1, int $size = 10)
    {
        if (!$sid || !$operatorId || !$confId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'ordernum'    => $orderNum,
            'code'        => $code,
            'page'        => $page,
            'size'        => $size,
        ];

        //回收站访问
        if ($this->recycle) {
            $data['recycle'] = 1;
        }

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_LIST_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 上移下移批次
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     * @param  int $status 上下移状态 1上移 2下移
     *
     * @return array
     */
    public function sortCodeBatch(int $sid, int $operatorId, int $confId, int $batchId, int $status)
    {
        if (!$sid || !$operatorId || !$batchId || !$confId || !$status) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'batch_id'    => $batchId,
            'direction'   => $status,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_SORT_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 置顶批次
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $batchId 批次id
     * @param  int $confId 发码配置id
     *
     * @return array
     */
    public function topCodeBatch(int $sid, int $operatorId, int $batchId, int $confId)
    {
        if (!$sid || !$operatorId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'batch_id'    => $batchId,
            'config_id'   => $confId,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_TOP_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 作废或恢复批次
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     * @param  int $status 操作状态 1作废 0恢复
     *
     * @return array
     */
    public function invalidOrRestoreCodeBatch(int $sid, int $operatorId, int $confId, int $batchId, int $status)
    {
        if (!$sid || !$operatorId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'batch_id'    => $batchId,
            'action'      => $status,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_EDITVOID_FUN__,
            $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 修改批次备注
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $batchId 批次id
     * @param  int $confId 配置id
     * @param  string $remark 备注信息
     *
     * @return array
     */
    public function remarkCodeBatch(int $sid, int $operatorId, int $batchId, int $confId, string $remark)
    {
        if (!$sid || !$operatorId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'batch_id'    => $batchId,
            'config_id'   => $confId,
            'remark'      => $remark,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_REMARK_SUBMIT_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 导入票码
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 发码配置idid
     * @param  array $codeArr 导入码数组
     * @param  int $validTime
     * @param  string $remark
     *
     * @return array
     */
    public function importCode(int $sid, int $operatorId, int $confId, array $codeArr, int $validTime, string $remark)
    {
        if (!$sid || !$operatorId || !$codeArr || !$confId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'code_arr'    => $codeArr,
            'expire_time' => $validTime,
            'remark'      => $remark,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CODE_IMPORT_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(empty($result['code']) ? 500 : $result['code'],
                empty($result['msg']) ? '接口异常' : $result['msg'], empty($result['data']) ? 500 : $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 导入票码二次确认
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 发码配置id
     * @param  int $isImport 是否导入 1不导入 2导入并作废之前的码
     * @param  string $importKey 备注信息
     *
     * @return array
     */
    public function importCodeConfirm(int $sid, int $operatorId, int $confId, string $importKey, int $isImport)
    {
        if (!$sid || !$operatorId || !$importKey || !$isImport) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'import_key'  => $importKey,
            'is_import'   => $isImport,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CODE_IMPORT_CONFIRM_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取发码配置信息
     * <AUTHOR> Li
     * @date  2020-07-27
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 有效期时间戳
     *
     * @return array
     */
    public function getConfInfo(int $sid, int $operatorId, int $confId)
    {
        if (!$sid || !$operatorId || !$confId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_INFO_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        if (!empty($result['data'])) {
            //查询所有票名称
            $ticketId   = $result['data']['ticket_id'];
            $batchQuery = new TicketApi();
            $ticket     = $batchQuery->queryTicketById($ticketId);

            //获取景区名称
            $landModel = new \Model\Product\Land();
            $landId    = $result['data']['land_id'];
            $landMap   = $landModel->getLandInfo($landId, false, 'id, title');

            //获取景区名称
            if (!empty($landMap['title'])) {
                $result['data']['land_name']  = $landMap['title'];
                $result['data']['is_invalid'] = 2; //是否无效 1是2不是
            } else {
                $result['data']['land_name']  = "";
                $result['data']['is_invalid'] = 1;
            }

            //获取门票名称
            if (!empty($ticket['data']['title'])) {
                $result['data']['ticket_name'] = $ticket['data']['title'];
                $result['data']['is_invalid']  = 2; //是否无效 1是2不是
            } else {
                $result['data']['ticket_name'] = "";
                $result['data']['is_invalid']  = 1;
            }
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取发码配置信息
     * <AUTHOR> Li
     * @date  2020-07-27
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     *
     * @return array
     */
    public function getBatchInfo(int $sid, int $operatorId, int $confId, int $batchId)
    {
        if (!$sid || !$operatorId || !$confId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'batch_id'    => $batchId,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_BATCH_QUERY_INFO_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    //====================码列表======================

    /**
     * 获取码列表
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     * @param  string $code 三方码
     * @param  string $ordernum 订单号
     * @param  int $status 码状态
     * @param  string $remark 备注信息
     * @param  int $page 当前页数
     * @param  int $size 每页条数
     *
     * @return array
     */
    public function getCodeInfoList(int $sid, int $operatorId, int $confId, int $batchId, string $code, string $ordernum, int $status, string $remark, int $page = 1, int $size = 10)
    {
        if (!$sid || !$operatorId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'config_id'   => $confId,
            'batch_id'    => $batchId,
            'code'        => $code,
            'ordernum'    => $ordernum,
            'state'       => $status,
            'remark'      => $remark,
            'page'        => $page,
            'size'        => $size,
        ];

        //回收站访问
        if ($this->recycle) {
            $data['recycle'] = 1;
        }

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CODE_LIST_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 编辑码备注
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $codeId 三方码id
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     * @param  string $remark 备注信息
     *
     * @return array
     */
    public function remarkCode(int $sid, int $operatorId, int $codeId, int $confId, int $batchId, string $remark)
    {
        if (!$sid || !$operatorId || !$codeId || !$confId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'code_id'     => $codeId,
            'config_id'   => $confId,
            'batch_id'    => $batchId,
            'remark'      => $remark,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CODE_REMARK_SUBMIT_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 作废或恢复码
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  int $codeId 三方码id
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     * @param  int $status 状态码 1作废 0恢复
     *
     * @return array
     */
    public function invalidOrRestoreCode(int $sid, int $operatorId, int $codeId, int $confId, int $batchId, int $status)
    {
        if (!$sid || !$operatorId || !$codeId || !$confId || !$batchId) {

            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'   => $sid,
            'operator_id' => $operatorId,
            'code_id'     => $codeId,
            'config_id'   => $confId,
            'batch_id'    => $batchId,
            'action'      => $status,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CODE_EDITVOID_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 批量作废或恢复码
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  int $operatorId 当前用户id
     * @param  array $codeIdArr 三方码id数组
     * @param  int $confId 发码配置id
     * @param  int $batchId 批次id
     * @param  int $status 状态码 1作废 0恢复
     *
     * @return array
     */
    public function invalidOrRestoreCodeMore(int $sid, int $operatorId, array $codeIdArr, int $confId, int $batchId, int $status)
    {
        if (!$sid || !$operatorId || !$codeIdArr || !$confId || !$batchId) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id' => $sid,
            'code_ids'  => $codeIdArr,
            'config_id' => $confId,
            'batch_id'  => $batchId,
            'action'    => $status,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_MOEE_CODE_EDITVOID_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    //======================外部码查询===================

    /**
     * 通过订单号获取订单对应的外部码
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  string $orderNum 订单号
     *
     * @return array
     */
    public function getExternalCodeInfoByOrdernum(int $sid, string $orderNum)
    {
        if (!$sid || !$orderNum) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id' => $sid,
            'ordernum'  => $orderNum,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_ORDER_QUERYCODE_FUN__, $data);
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 外部码有效期处理展示
     * <AUTHOR>
     * @date 2020/9/14
     *
     * @param  int $expireType 发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
     * @param  int $preSale 是否需要选择游玩日期
     * @param  string $orderTime 订单下单时间
     * @param  string $playTime 游客游玩时间
     * @param  int $expireTime 过期时间
     * @param  string $startDt 固定日期开始时间
     * @param  string $endDt 固定日期结束时间
     * @param  bool $year 是否展示年份，固定展示
     *
     * @return array
     */
    public function handleExternalCodeOrderTime(int $expireType, int $preSale, string $orderTime, string $playTime, int $expireTime = 0, string $startDt = "", string $endDt = "", bool $year = true)
    {
        if (!$expireType) {
            return $this->returnData(203, '参数错误');
        }
        if ($expireType == 1) { //导码设置的有效期
            $beginTime = date('Y-m-d', strtotime($orderTime));
            $endTime   = $expireTime != 0 ? date('Y-m-d', $expireTime) : 0;
        } elseif ($expireType == 2) { //下单选择的游玩日期,验证是否开启配置
            $beginTime = date('Y-m-d', strtotime($playTime));
            $endTime   = date('Y-m-d', strtotime($playTime));
            //验证是否开启游玩日期选择 1否0是
            if ($preSale == 1) {
                //导码设置的有效期
                $beginTime = date('Y-m-d', strtotime($orderTime));
                $endTime   = $expireTime != 0 ? date('Y-m-d', $expireTime) : 0;
            }
        } else { //固定有效期
            $beginTime = $startDt;
            $endTime   = $endDt;
        }
        //处理：有效期跨年，显示yyyy-mm-dd  有效期未跨年，显示mm-dd
        if (!$year) {
            $beginY = date('Y', strtotime($beginTime));
            $endY   = date('Y', strtotime($endTime));
            if ($beginY == $endY) {
                //只显示月日
                $beginTime = date('m-d', strtotime($beginTime));
                $endTime   = date('m-d', strtotime($endTime));
            }
        }
        $externalCode                = [];
        $externalCode['begin_time']  = $beginTime;
        $externalCode['end_time']    = $endTime;
        $externalCode['expire_type'] = $expireType;

        return $this->returnData(200, '成功', $externalCode);
    }

    //======================通过门票id判断是否要发码及码信息==================

    /**
     * 通过门票id判断门票是否有对应可用码
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  int $sid 供应商id
     * @param  array $tidArr 门票id
     *
     * @return array
     */
    public function checkIsExternalCodeByTid(int $sid, array $tidArr)
    {
        if (!$tidArr) {
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'member_id'  => $sid,
            'ticket_ids' => $tidArr,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_TICKET_JUDGE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 根据ticket ids获取外部代码信息
     *
     * @param string $tids ticket ids
     *
     * @return array 返回数据
     */
    public function getExternalCodeInfoByTids(string $tids)
    {
        if (!$tids) { // 如果没有传入ticket ids参数
            return $this->returnData(203, '参数错误');
        }

        $data = [
            'ticket_ids' => $tids,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_CONFIG_INFO_FUN__, $data); // 调用外部代码接口

        if (!isset($result['code']) || $result['code'] != 200) { // 如果返回结果中没有code字段或者code不等于200
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, $result['msg'], $result['data']); // 返回结果数据
    }

    /**
     * 根据用户的票ID查询配置信息
     * <AUTHOR>
     * @date 2021/6/29
     *
     * @param  int  $sid
     * @param  array  $tidArr
     *
     * @return array
     */
    public function checkExternalCodeModuleExpireByTids(int $sid, array $tidArr)
    {
        if (!$sid || empty($tidArr)) {
            return $this->returnData(203, '参数错误!', func_get_args());
        }

        if (!$this->checkModuleExpire($sid)) {
            return $this->returnData(200, '', []);
        }

        $data = [
            'member_id'  => $sid,
            'ticket_ids' => $tidArr,
        ];

        $result = $this->_handelRequest($sid, self::__EXTERNALCODE_USER_CONFIG_TICKETS_FUN__, $data);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    //======================套餐应用关闭时关闭供应商下外部码导码配置===================

    /**
     * 关闭供应商下代码配置
     * <AUTHOR>  Li
     * @date  2020-07-24
     *
     * @param  int $sid 供应商id
     *
     * @return array
     */
    public function closeCodeManageInfoBySid(int $sid)
    {
        pft_log($this->_debugLogDir . 'debug', "关闭供应商下代码配置， 用户：$sid");
        if (!$sid) {
            return $this->returnData(203, '缺少供应商信息');
        }

        $data = [
            'member_id' => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__EXTERNALCODE_DISABLE_CONFIG_FUN__, $data);
        pft_log($this->_debugLogDir . 'debug', "关闭供应商下代码配置， 结果：" . json_encode($result));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        if (!empty($result['data'])) {

            $resultData = is_array($result['data']) ? $result['data'] : [];

            $tids = array_column($resultData, 'ticket_id');

            //关闭成功后将对应门票发码属性关闭
            $res = $this->saveExtInfo($sid, $sid, $tids, 0);
            if (!$res) {
                return $this->returnData(400, '关闭发码应用成功，但是票属性更新失败，请联系技术人员处理');
            }

            //下架外部码票
            $deTickets = [];
            foreach ($resultData as $val) {
                $lastState = $val['last_state'] ?? 0;
                //最后为启用状态的，需要下架
                if ($lastState == 2) {
                    $deTickets[] = $val['ticket_id'];
                }
            }

            if (!empty($deTickets)) {
                //下架票之前，为了防止在开通，这边推入延迟队列，15分钟后在判断下，如果没有生效的再出发下架
                $data = [
                    'action' => 'close_delay_de_ticket',
                    'data'   => ['deTickets' => $deTickets, 'tag' => self::TAG_NAME, 'sid' => $sid],
                ];
                $time = strtotime("+900 seconds"); //延迟15分钟执行
                \Library\Resque\Queue::delay($time, 'independent_system', 'ExternalCode_Job', $data);
            }
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 关闭应用下架门票
     * <AUTHOR>
     * @date 2021/6/29
     *
     * @param  array  $tidArr
     *
     * @return bool
     */
    public function closeModuleDeTickets(array $tidArr)
    {
        if (empty($tidArr)) {
            return true;
        }
        //获取供应商信息
        $tickets = (new \Business\CommodityCenter\Ticket())->batchQueryTicketByTicketIds($tidArr);
        if (empty($tickets)) {
            pft_log($this->_debugLogDir . 'debug', json_encode(["关闭应用下架门票,失败", $tidArr], JSON_UNESCAPED_UNICODE));
            return true;
        }
        $tickets = is_array($tickets) ? $tickets : [];
        $didArr  = [];
        foreach ($tickets as $val) {
            $did    = $val['apply_did'];
            $status = $val['status'];
            //正在上架外部码票，才能下架
            if ($status != 2) {
                $didArr[$did][] = $val['id'];
            }
        }

        $codeMsg = [];
        if (!empty($didArr)) {
            foreach ($didArr as $op => $tids) {
                //当前用户id和操作人员id 默认供应商id
                $res       = (new \Business\Product\HandleTicket($op, $op))->setTicketStatus(implode(',', $tids), $op,
                    'deListTicket');
                $codeMsg[] = $res;
            }
        }

        pft_log($this->_debugLogDir . 'debug', json_encode(["关闭应用下架门票", $didArr, $codeMsg], JSON_UNESCAPED_UNICODE));

        return true;
    }


    //======================文件流处理=================

    /**
     * 文件检测及处理
     * <AUTHOR>  Li
     * @date 2020-07-22
     *
     * @param  array $file $_FILE 文件
     *
     * @return array
     */
    public function fileCheck(array $file)
    {
        //文件有效判断
        if ($file['size'] <= 0 && $file['error'] == 1) {
            return $this->returnData(203, '文件无法识别，请重试');
        } elseif ($file['size'] <= 0) {
            return $this->returnData(203, '文件内容不能为空');
        }

        //文件解析
        $fileInfo = [];
        if (in_array($file['type'], [
            'text/plain',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
        ])) {
            switch ($file['type']) {
                case 'text/plain':
                    if (file_exists($file['tmp_name'])) {
                        $fileInfo = file_get_contents($file['tmp_name']);//将整个文件内容读入到一个字符串中
                        $fileInfo = str_replace("\r\n", ",", $fileInfo);
                        $fileInfo = explode(',', $fileInfo);
                    }
                    break;
                case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                    $fileInfo = $this->readExcelDetail($file['tmp_name'], 'xlsx');
                    break;
                case 'application/vnd.ms-excel':
                    $fileInfo = $this->readExcelDetail($file['tmp_name'], 'xls');
                    break;
                default:
                    break;
            }
        } else {
            return $this->returnData(203, '上传的文件类型不对');
        }

        if (empty($fileInfo)) {
            return $this->returnData(203, '文件内容获取有误');
        }

        //码内容有效判断
        foreach ($fileInfo as $key => &$code) {
            //去除空格
            $code = str_replace(' ', '', $code);

            if (empty($code)) {
                unset($fileInfo[$key]);
                continue;
            }

            //./:@!*-_#%?+=&
            if (!preg_match("/^[A-Za-z0-9\/\.\:\@\!\*\-_\#\%\?\+\=\&]+$/", $code)) {
                return $this->returnData(203, '外部码仅支持数字、字母、英文符号，请确认后再导入');
            }

            if (strlen($code) > 100) {
                return $this->returnData(203, '单个码超过100个字符，请确认后再导入');
            }
        }

        if (empty($fileInfo)) {
            return $this->returnData(203, '文件内容获取有误');
        }

        if (count($fileInfo) > 1000) {
            return $this->returnData(203, '每次最多只能导入1000个码');
        }

        return $this->returnData(200, '文件解析成功', $fileInfo);
    }

    /**
     * 读取excel
     * <AUTHOR> Li
     * @date 2020-07-22
     *
     * @param  string $filename 文件
     * @param  string $fileType 文件类型
     *
     * @return array
     */
    private function readExcelDetail(string $filename, string $fileType = 'xls')
    {
        if ($fileType == 'xls') {
            $objReader = \PHPExcel_IOFactory::createReader('Excel5');
        } else {
            $objReader = \PHPExcel_IOFactory::createReader('Excel2007');
        }

        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($filename, $encode = 'utf-8');
        $objPHPExcel->setActiveSheetIndex(0);
        $objWorksheet       = $objPHPExcel->getActiveSheet();
        $hightestrow        = $objWorksheet->getHighestRow();
        $highestColumn      = $objWorksheet->getHighestColumn();
        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
        $excelData          = array();

        for ($row = 1; $row <= $hightestrow; $row++) {
            for ($col = 0; $col < $highestColumnIndex; $col++) {
                $tmpValue = (string)$objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
                if (empty($tmpValue)) {
                    continue;
                }
                $excelData[] = $tmpValue;
            }
        }

        return $excelData;

    }



    //============下单相关的==================

    /**
     * 锁定可使用的码，订单下单时锁定
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  integer $tid 门票id
     * @param  integer $num 需要的码的数量
     * @param  string $playDate 游玩日期
     *
     * @return  array
     */
    public function lockCode(int $tid, int $ticketNum, string $playDate)
    {

        if (!$tid || !$ticketNum || !$playDate) {
            return $this->returnData(201, '参数错误');
        }

        $data = [
            'tid'       => $tid,
            'tnum'      => $ticketNum,
            'play_date' => $playDate,
        ];

        $result = $this->ExternalCodeCall(self::__ORDER_GET_CODE__, $data);
        pft_log($this->_debugLogDir . 'lock_code/', json_encode([$tid, $ticketNum, $playDate, $result]));
        if (!isset($result['code'])) {
            return $this->returnData(500, '接口异常');
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 释放被锁定的码，下单失败时释放
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  integer $codeData 第三方码数据
     *
     * @return  array
     */
    public function orderFailReleaseCode(array $codeData)
    {
        if (!$codeData) {
            return $this->returnData(201, '参数错误');
        }

        $data   = ['code_data' => $codeData,];
        $result = $this->ExternalCodeCall(self::__ORDER_FAIL_RELEASE_CODE__, $data);
        pft_log($this->_debugLogDir . 'fail_release/', json_encode([$codeData, $result]));

        if (!isset($result['code'])) {
            return $this->returnData(500, '接口异常');
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 绑定码与订单的关联关系，下单完成后调用
     * <AUTHOR>
     * @date    2020-07-22
     *
     * @param  string $ordernum 订单号
     * @param  array $codeData 第三方消费码数据
     *
     * @return  array
     */
    public function codeBindOrderId(string $orderId, array $codeData)
    {
        if (!$orderId || !$codeData) {
            return $this->returnData(201, '参数错误');
        }

        $data = [
            'orderid'   => $orderId,
            'code_data' => $codeData,
        ];

        $result = $this->ExternalCodeCall(self::__ORDER_SUCCESS_BIND_CODE__, $data);
        pft_log($this->_debugLogDir . 'bind_code/', json_encode([$orderId, $codeData, $result]));

        if (!isset($result['code'])) {
            return $this->returnData(500, '接口异常');
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 修改码状态为已使用（订单支付完成后）
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  string $orderId 订单号
     *
     * @return  array
     */
    public function usedCode(string $orderId)
    {
        if (!$orderId) {
            return $this->returnData(201, '参数错误');
        }

        $data   = ['orderid' => $orderId];
        $result = $this->ExternalCodeCall(self::__ORDER_PAY_USE_CODE__, $data);
        pft_log($this->_debugLogDir . 'use_code/', json_encode([$orderId, $result]));

        if (!isset($result['code'])) {
            return $this->returnData(500, '接口异常');
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 释放码，锁定的码才可释放（下单完成，订单未支付）
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  array $orderId 订单号
     *
     * @return  array
     */
    public function releaseCodeByOrderId(string $orderId)
    {
        if (!$orderId) {
            return $this->returnData(201, '参数错误');
        }

        $isExternalOrder = $this->isExternalCodeOrder($orderId);
        if (!$isExternalOrder) {
            return $this->returnData(200, '非第三方发码订单');
        }

        $data   = ['orderid' => $orderId];
        $result = $this->ExternalCodeCall(self::__ORDER_CANCEL_RELEASE_CODE__, $data);

        pft_log($this->_debugLogDir . 'release_code_orderid/', json_encode([$orderId, $result]));

        if (!isset($result['code'])) {
            return $this->returnData(500, '接口异常');
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }


    //============异步处理相关的==================

    /**
     * 订单支付成功后的(在线支付)
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  array $orderId 订单号
     *
     * @return  boolean
     */
    public function asyncPaySuccessTask(array $orderData)
    {
        $orderId = $orderData['mainOrder']['ordernum'];
        if (!$orderId) {
            return false;
        }

        $isExternalOrder = $this->isExternalCodeOrder($orderId);
        if (!$isExternalOrder) {
            return true;
        }

        pft_log($this->_debugLogDir . 'pay_success/', json_encode([$orderData, $isExternalOrder]));
        $result = $this->usedCode($orderId);

        if (!isset($result['code']) || $result['code'] != 200) {
            return false;
        }

        return true;
    }

    /**
     * 订单取消成功后的异步任务
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  array $orderId 订单号
     *
     * @return  boolean
     */
    public function asyncOrderCancelSuccessTask(array $orderData)
    {

        $orderId = $orderData['mainOrder']['ordernum'];
        if (!$orderId) {
            return false;
        }

        $isExternalOrder = $this->isExternalCodeOrder($orderId);
        if (!$isExternalOrder) {
            return true;
        }

        pft_log($this->_debugLogDir . 'cancel_success/', json_encode([$orderData, $isExternalOrder]));
        $result = $this->releaseCodeByOrderId($orderId);

        if (!isset($result['code']) || $result['code'] != 200) {
            return false;
        }

        return true;

    }

    /**
     * 是否是外部发码订单
     * <AUTHOR>
     * @date    2020-07-23
     *
     * @param  array $orderId 订单号
     *
     * @return  boolean
     */
    public function isExternalCodeOrder(string $orderId)
    {
        $isExternalOrder = false;
        //订单详细信息获取
        $orderDetailInfo = (new \Model\Order\OrderTools('slave'))->getOrderDetailInfo($orderId, 'product_ext');

        $extProduct = json_decode($orderDetailInfo['product_ext'], true);
        //是否是第三方码的订单
        if (isset($extProduct['externalSendCode']) && $extProduct['externalSendCode'] == 1) {
            $isExternalOrder = true;
        }

        return $isExternalOrder;
    }

    /**
     * 通过订单号获取门票码信息(OTA用)
     * <AUTHOR>  Li
     * @date    2020-07-31
     *
     * @param  string $orderNum 订单号
     * @param  string $orderNum 订单号
     *
     * @return  array
     */
    public function getCodeByOrder(string $orderNum, $isGetOrderCode = true)
    {
        if (!$orderNum) {
            return $this->returnData(203, '订单数据缺失');
        }

        //校验一次是否需要发码
        $isExternalOrder = $this->isExternalCodeOrder($orderNum);

        if ($isExternalOrder) {
            $data = [
                'orderid' => $orderNum,
            ];

            $result = $this->ExternalCodeCall(self::__EXTERNALCODE_GETORDERCODE_FUN__, $data);
            if (!isset($result['code']) || $result['code'] != 200) {
                return $this->returnData(500, '接口异常');
            } else {
                if ($result['data']['code'] == 200) {
                    $codeArr = $result['data']['data'];
                } else {
                    $codeArr = [];
                }
            }
        } elseif ($isGetOrderCode) {
            //订单详细信息获取
            $orderDetailInfo = (new \Model\Order\SubOrderQuery('slave'))->getInfoByOrder($orderNum, 'code');
            $codeArr         = [$orderDetailInfo['code']];
        } else {
            $codeArr = [];
        }

        return $this->returnData(200, '码获取成功', $codeArr);
    }

    //============ 辅助处理的方法 ==================

    /**
     * 检测外部码应用是否过期
     * <AUTHOR>
     * @date 2021/6/29
     *
     * @param  int  $sid
     *
     * @return bool true 已过期  false 未过期或者未开通
     */
    public function checkModuleExpire(int $sid)
    {
        $redisLib = Cache::getInstance('redis');
        $cacheKey = sprintf(self::USER_EXTERNAL_CODE_MODULE_CACHE, $sid);
        $useRes   = $redisLib->get($cacheKey);
        //$useRes = false;
        if (empty($useRes)) {
            $useRes = [
                'module_id' => 0,
                'info'      => [],
            ];
            //后续这边可以优化下
            $moduleCommonBiz = new \Business\AppCenter\ModuleCommon();
            $moduleRes       = $moduleCommonBiz->getModuleIdByMenu([self::TAG_NAME]);
            $menuMap         = array_column($moduleRes, 'module_id', 'menu_id');
            $moduleId        = isset($menuMap[self::TAG_NAME]) ? $menuMap[self::TAG_NAME] : 0;
            if (!$moduleId) {
                //如果未找到应用id 默认未过期
                $redisLib->set($cacheKey, json_encode($useRes), '', 120);

                return false;
            }
            $userModule          = $moduleCommonBiz->getModuleUsedByUid($sid, 'module_id, status', $moduleId);
            $useRes['module_id'] = $moduleId;
            $useRes['info']      = $userModule;
            $redisLib->set($cacheKey, json_encode($useRes), '', 120);
        } else {
            $useRes = json_decode($useRes, true);
            $userModule = $useRes['info'];
            $moduleId   = $useRes['module_id'];
        }

        $userModule = array_column($userModule, 'status', 'module_id');
        if (isset($userModule[$moduleId]) && $userModule[$moduleId] != 1) {
            //已过期
            return true;
        }

        return false;
    }

    /**
     * 请求处理
     * <AUTHOR>
     * @date 2021/6/28
     *
     * @param  int  $memberId 当前用户
     * @param  string  $method 请求方法
     * @param  array  $data 请求数据
     *
     * @return array
     */
    private function _handelRequest(int $memberId, string $method, array $data)
    {
        $result = $this->ExternalCodeCall($method, $data);

        //成功 记录日志 记录操作人员
        $opeMsg = json_encode(['_updateConfigByExtId', [$memberId, $data]], JSON_UNESCAPED_UNICODE);
        pft_log($this->_debugLogDir . "/oper", $opeMsg);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], []);
        }

        return $this->returnData(200, '', $result['data']);
    }

    //============ 其他查询 ==================

    /**
     * 外部码景区查询票 移除绑定上游门票
     * <AUTHOR>
     * @date 2022/1/21
     *
     * @param  int  $memberId  用户id
     * @param  int  $landId  景区id
     *
     * @return array
     */
    public function getSelfTicketByLandId(int $memberId, int $landId)
    {
        $code = 200;
        $data = [];
        $msg = '';
        try {
            if (!$memberId || !$landId) {
                throw new \Exception('参数错误', 203);
            }
            $javaApi = new \Business\CommodityCenter\Ticket();
            $status  = 1; //上架
            $ticketList  = $javaApi->queryTicketListByLandId($landId, 'id,title,apply_did,max_expiration_date,pid', false, $status);
            if (empty($ticketList)) {
                throw new \Exception('无数据', 200);
            }
            $ticketsData = [];
            foreach ($ticketList as $ticket) {
                $applyDid = $ticket['apply_did'] ?? 0;
                $title    = $ticket['title'] ?? '';
                $ticketId = $ticket['id'] ?? 0;
                $pid      = $ticket['pid'] ?? 0;
                $maxTime  = $ticket['max_expiration_date'] ?? 0;
                if (!$applyDid || $applyDid != $memberId) {
                    //供应商不对，直接返回空
                    throw new \Exception('无数据', 200);
                }
                if (date("Ymd") > date("Ymd", $maxTime)) {
                    continue;
                }
                if (!$ticketId) {
                    continue;
                }
                //返回字段限制
                $ticketsData[] = [
                    'name' => $title,
                    'id' => $ticketId,
                    'pid' => $pid,
                    'lid' => $landId,
                ];
            }

            //判断票是否绑定上游
            $tidArr     = array_column($ticketsData, 'id');
            $isNotExist = $this->_checkThridTicket($tidArr);
            foreach ($ticketsData as $key => $value) {
                if (!in_array($value['id'], $isNotExist)) {
                    //移除
                    unset($ticketsData[$key]);
                }
            }

            $data = array_values($ticketsData);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 检测票是否绑定第三方
     * <AUTHOR>
     * @date 2022/1/24
     *
     * @param  array  $tidArr
     *
     * @return array
     */
    private function _checkThridTicket(array $tidArr)
    {
        if (empty($tidArr)) {
            return [];
        }
        $ticketAttrList = (new \Business\JavaApi\Product\Ticket())->queryTicketInfoByIds($tidArr);

        $isNotExist     = [];
        if ($ticketAttrList['code'] == 200) {
            foreach ($tidArr as $ticketId) {
                foreach ($ticketAttrList['data'] as $ticketAttr) {
                    if ($ticketId == $ticketAttr['uuJqTicketDTO']['id'] &&
                        $ticketAttr['thridTicketAttributesDTO']['uuid'] == '') {
                        //不存在
                        $isNotExist[] = $ticketId;
                    }
                }
            }
        }

        return $isNotExist;
    }
}