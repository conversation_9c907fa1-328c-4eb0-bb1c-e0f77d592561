<?php
/**
 * 旅游券订单查询
 */

namespace Business\JsonRpcApi\TravelVoucherService;

use Business\NewJavaApi\Order\FeignClient;

class OrderQuery extends TravelVoucherBase
{
    //旅游券订单状态转换
    private $orderStatusMap = [
        10  => 0,   //未使用
        20  => 7,   //部分使用
        30  => 3,   //已取消
        40  => 2,   //已过期
        50  => 5,   //撤改
        60  => 6,   //撤销
        100 => 1,   //已使用
    ];

    private $orderStatusName = [
        10  => '未使用',
        20  => '部分使用',
        30  => '已取消',
        40  => '已过期',
        50  => '撤改',
        60  => '撤销',
        100 => '已使用',
    ];

    private $orderChannel = [
        5  => 0,
        12 => 16,
    ];

    public function getRpcServiceMethod()
    {
        return [
            '/order/list' => [
                'method' => '/order/list',
                'desc'   => '发票中心获取获取订单详情',
            ],
            '/order/getOrderListByExcelFields' => [
                'method' => '/order/getOrderListByExcelFields',
                'desc'   => '订单列表（订单列表导出Excel）',
            ],
            '/order/total' => [
                'method' => '/order/total',
                'desc'   => '订单列表查看汇总',
            ],
        ];
    }

    /**
     * 订单列表（订单列表导出Excel）
     * <AUTHOR>
     * @date 2022/3/11
     *
     * @param  array  $centreOrderNos 订单编号集合（最多一千条）
     * @param  int  $sid  供应商id
     *
     * @return array
     */
    public function orders(array $centreOrderNos, int $sid)
    {
        if (!$centreOrderNos) {
            return $this->returnData(204, '参数错误');
        }
        $params = [
            'centreOrderNos' => $centreOrderNos,
            'sid'            => $sid,
        ];

        return $this->jsonRpcClient('/order/getOrderListByExcelFields', $params);
    }

    /**
     * 订单列表查看汇总
     * <AUTHOR>
     * @date 2022/3/11
     *
     * @param  array  $centreOrderNos 订单编号集合（最多一千条）
     * @param  int  $sid  供应商id
     *
     * @return array
     */
    public function ordersTotal(array $centreOrderNos, int $sid)
    {
        if (!$centreOrderNos) {
            return $this->returnData(204, '参数错误');
        }
        $params = [
            'centreOrderNos' => $centreOrderNos,
            'sid'            => $sid,
        ];

        return $this->jsonRpcClient('/order/total', $params);
    }

    /**
     * 发票中心获取获取订单详情
     * <AUTHOR>  Li
     * @date  2022-03-10
     *
     * @param  array  $orderNumArr  订单号
     *
     * @return array
     */
    public function getOrderInfo($orderNumArr)
    {
        if (!$orderNumArr) {
            return $this->returnData(204, '参数错误');
        }
        $params = [
            'params' => [
                'centre_order_no' => $orderNumArr,
            ],
        ];

        return $this->jsonRpcClient('/order/list', $params);
    }

    /**
     * 通过订单号查询旅游券订单信息
     * <AUTHOR> Li
     * @date 2022-03-10
     *
     * @param  array  $orderNumArr  订单号
     * @param  bool  $getSplitAids  是否获取分销链信息
     *
     * @return  array
     */
    public function getOrderInfoDetail(array $orderNumArr, bool $getSplitAids = false)
    {
        if (!$orderNumArr) {
            return [];
        }
        $orderRes   = $this->getOrderInfo($orderNumArr);
        if ($orderRes['code'] != 200 || empty($orderRes['data'])) {
            pft_log('jsonRpcApi/TravelVoucherBase/OrderInfo', 'infoS:' . json_encode($orderRes));
            return [];
        }

        $orderInfo = [];
        $splitAids = [];
        if ($getSplitAids) {
            $newJavaApi = new \Business\NewJavaApi\Order\OrderDistribution();
            $result     = $newJavaApi->batchQueryDistribution($orderNumArr);
            if ($result['code'] == 200  && !empty($result['data'])) {
                //按照分销层级排序
                $levelArr = array_column($result['data'],'level');
                array_multisort($levelArr,SORT_ASC, $result['data']);
                //分销链
                while($result['data']) {
                    $orderDetail = array_shift($result['data']);
                    $splitAids[$orderDetail['orderId']][] = $orderDetail;
                }
            }
        }
        while ($orderRes['data']) {
            $order = array_shift($orderRes['data']);
            $currentSplitAids = $splitAids[$order['centre_order_no']] ?? [];
            $aids = $aidsMoney = $aidsPrice = [];
            foreach ($currentSplitAids as $item) {
                $aids[] = $item['sellerId'];
                $aidsPrice[] = $item['saleMoney'];
                $aidsMoney[] = [
                    'aid' => $item['sellerId'],
                    'fid' => $item['buyerId'],
                    'money' => $item['saleMoney'] * $order['amount'],
                    'payway' => $item['payMode']
                ];
            }
            $orderInfo[] = [
                'ordernum'     => $order['centre_order_no'],
                'tid'          => $order['tid'],
                'member'       => $order['member_id'],
                'ordertime'    => $order['order_time'],
                'tprice'       => $order['unit_price'],
                'tnum'         => $order['amount'],
                'dtime'        => $order['verify_time'],
                'status'       => $this->orderStatusMap[intval($order['status'])],
                'order_status' => $this->orderStatusName[intval($order['status'])],
                'paymode'      => $order['pay_type'],
                'ordermode'    => $this->orderChannel[$order['sale_channel']] ?? 0,
                'totalmoney'   => $order['total_price'],
                'aid'          => $order['sid'],
                'apply_did'    => $order['sid'],
                'contacttel'   => $order['user_mobile'],
                'aids'         => implode(',', $aids),
                'aids_money'   => json_encode($aidsMoney),
                'aids_price'   => implode(',', $aidsPrice),
                'order_type'   => 2,
                'p_type'       => 'Q',
                'lid'          => $order['lid'],
                'pid'          => $order['pid'],
                'buyer'        => $order['user_name'],
                'voucher_name' => $order['spu_name'],
                'title'        => $order['sku_name'],
                'pname'        => $order['product_name'],
                'order_detail_url'=> $order['order_detail_url']
            ];
        }

        return $orderInfo;
    }

    /**
     * 批量查询订单分销商信息
     * <AUTHOR>  Li
     * @date 2022-03-10
     *
     * @param  array $orderNumArr  订单号数组
     *
     * @return array
     */
    public function batchQueryDistribution(array $orderNumArr)
    {
        if (!$orderNumArr) {
            return [];
        }
        $newJavaApi = new \Business\NewJavaApi\Order\OrderDistribution();
        $result     = $newJavaApi->batchQueryDistribution($orderNumArr);
        if ($result['code'] != 200  || empty($result['data'])) {
            return [];
        }

        //按照分销层级排序
        $levelArr = array_column($result['data'],'level');
        array_multisort($levelArr,SORT_ASC, $result['data']);

        //分销链
        $splitAids = [];
        foreach ($result['data'] as $orderDetail) {
            $splitAids[$orderDetail['orderId']][] = $orderDetail['sellerId'];
        }
        return $splitAids;
    }

    public function bathJudgeTravelVoucher($orderNumArr)
    {
        return (new FeignClient())->mapOrdersPType($orderNumArr);
    }
}