<?php

namespace Business\JsonRpcApi\PayService;

class UnifiedRefund extends PayServiceBase
{

    public function getRpcServiceMethod()
    {
        return [
            'refundRpcService' => [
                'method' => 'Refund/RefundCenter/onlineRefundService',
                'desc'   => '收银台统一退款接口',
            ],
        ];
    }

    /**
     * 收银台统一退款
     * <AUTHOR>
     * @date 2021/12/22
     *
     * @param  string  $orderNum  订单号
     * @param  string  $refundBatchId  退款批次号
     * @param  int  $refundMoney  钱（分）
     * @param  string  $tradeNo  三方流水号
     * @param  int  $merchantId  供应商id
     * @param  string  $clientId  业务方
     * @param  string  $refundReason  退款理由
     *
     * @return array
     */

    public function refundRpcService($orderNum, $merchantId, $refundBatchId, $tradeNo, $refundMoney, $payId, $originOrderNum = '', $clientId = 'platform_system',$refundReason = '订单退款', $payErrorCode = 0)
    {
        pft_log('refund_test_lan',json_encode(func_get_args()));
        if (!$orderNum || !$refundMoney || !$refundBatchId || !$clientId || (!$tradeNo && !$payId)) {
            return $this->returnData(204, '退款参数错误');
        }
        $params = [
            'merchant_id'     => $merchantId,
            'refund_order_id' => $refundBatchId,
            'trade_no'        => $tradeNo,
            'refund_money'    => $refundMoney,
            'order_id'        => $orderNum,
            'client_id'       => $clientId,
            'subject'         => $refundReason,
            'pay_id'          => $payId,
            'origin_order_num' => $originOrderNum,
            'pay_error_code'  => $payErrorCode,
        ];

        return $this->scenicRpcClient('refundRpcService', [$params]);
    }
}