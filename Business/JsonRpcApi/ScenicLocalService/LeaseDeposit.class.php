<?php
/**
 * 租赁订单押金业务
 */

namespace Business\JsonRpcApi\ScenicLocalService;
class LeaseDeposit extends ScenicLocalBase
{
    public function getRpcServiceMethod(): array
    {
        return [
            'getUnpaidInfoByOrderIds' => [
                'method' => 'Pay/LeaseDeposit/getUnpaidInfoByOrderIds',
                'desc' => '根据订单号获取应支付的租赁订单押金信息',
            ],
            'afterLeaseOrderOnlinePay' => [
                'method' => 'Pay/LeaseDeposit/afterLeaseOrderOnlinePay',
                'desc' => '租赁订单押金支付成功后处理逻辑',
            ],
            'leaseChangeLogExport' => [
                'method' => 'DownloadCenter/Lease/leaseChangeLogExport',
                'desc' => '设备租赁操作明细表导出',
            ],
            'getUnpaidMachineOrderDeposit' => [
                'method' => 'Pay/LeaseDeposit/getUnpaidMachineOrderDeposit',
                'desc' => '获取租赁柜的应支付的押金信息',
            ],
            'updateLeaseOrderMember' => [
                'method' => 'Pay/LeaseDeposit/updateLeaseOrderMember',
                'desc' => '更新租赁订单游客信息',
            ],
            'notifyBoxOrderHasPaid' => [
                'method' => 'AutoSale/AutoSaleApi/paySuccess',
                'desc' => '租赁柜购买业务小程序支付回调通知',
            ],
            'rentReportExport' => [
                'method' => 'DownloadCenter/Lease/rentReportExport',
                'desc' => '导出租赁报表',
            ],
            'rentReportDetailExport' => [
                'method' => 'DownloadCenter/Lease/rentReportDetailExport',
                'desc' => '导出租赁报表明细',
            ],
            'getItemList' => [
                'method' => 'Lease/LeaseBiz/getItemList',
                'desc' => '获取商家租赁物品列表',
            ],
            'getLidList' => [
                'method' => 'Lease/LeaseBiz/getLidList',
                'desc' => '获取商家租赁商品列表',
            ],
            'getPayTypeDepositList' => [
                'method' => 'Lease/LeaseBiz/getPayTypeDepositList',
                'desc' => '获取押金支付方式列表',
            ],
        ];
    }

    public function getUnpaidInfoByOrderIds(array $orderInfo): array
    {
        $params = [
            'orderInfo' => $orderInfo,
        ];
        return $this->scenicRpcClient('getUnpaidInfoByOrderIds', $params);
    }

    //租赁订单押金支付成功后处理逻辑
    public function afterLeaseOrderOnlinePay(array $params): array
    {
        $req = [
            'params' => $params,
        ];
        return $this->scenicRpcClient('afterLeaseOrderOnlinePay', $req);
    }

    public function leaseChangeLogExport($params, $fid, $pageNum, $pageSize): array
    {
        $req = [
            'params' => $params,
            'fid' => $fid,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize
        ];
        return $this->scenicRpcClient('leaseChangeLogExport', $req);
    }

    //获取租赁柜的押金信息
    public function getUnpaidMachineOrderDeposit(string $orderId): array
    {
        $params = [
            'orderId' => $orderId,
        ];
        return $this->scenicRpcClient('getUnpaidMachineOrderDeposit', $params);
    }

    public function updateLeaseOrderMember(string $orderId, int $memberId)
    {
        $params = [
            'orderId' => $orderId,
            'memberId' => $memberId,
        ];
        return $this->scenicRpcClient('updateLeaseOrderMember', $params);
    }

    public function notifyBoxOrderHasPaid(array $notifyData): array
    {
        return $this->scenicRpcClient('notifyBoxOrderHasPaid', [$notifyData]);
    }

    public function rentReportExport($params, int $fid, $opId, $pageNum, int $pageSize)
    {
        $req = [
            'params' => $params,
            'fid' => $fid,
            'opId' => $opId,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize
        ];
        return $this->scenicRpcClient('rentReportExport', $req);
    }

    public function rentReportDetailExport($params, int $fid, $opId, $pageNum, int $pageSize)
    {
        $req = [
            'params' => $params,
            'fid' => $fid,
            'opId' => $opId,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize
        ];
        return $this->scenicRpcClient('rentReportDetailExport', $req);
    }

    public function getItemList($sid, $detailLid, $pageNum, $pageSize): array
    {
        $req = [
            'sid' => $sid,
            'lid' => $detailLid,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize
        ];
        return $this->scenicRpcClient('getItemList', $req);
    }
    public function getLidList($sid, $keyword, $pageNum, $pageSize): array
    {
        $req = [
            'sid' => $sid,
            'keyword' => $keyword,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize
        ];
        return $this->scenicRpcClient('getLidList', $req);
    }


    public function getPayTypeDepositList($sid)
    {
        $req = [
            'sid' => $sid,
        ];
        return $this->scenicRpcClient('getPayTypeDepositList', $req);
    }
}