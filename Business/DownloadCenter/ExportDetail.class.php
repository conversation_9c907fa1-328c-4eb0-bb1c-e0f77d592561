<?php
/**
 * 导出明细
 * <AUTHOR>
 * @date   2023/11/28
 */

namespace Business\DownloadCenter;

use Business\Base;
use Business\DownloadCenter\CustomField\Factory;

class ExportDetail extends Base
{
    //订单查询导出明细自定义字段
    const EXPORT_DETAIL_ORDER_SEARCH = 'export_detail_order_search';
    //交易记录导出明细自定义字段
    const EXPORT_DETAIL_TRADE_RECORD = 'export_detail_trade_record';
    //预约报表导出明细自定义字段
    const EXPORT_DETAIL_ORDER_RESERVE = 'export_detail_order_reserve';
    //预订报表导出明细自定义字段
    const EXPORT_DETAIL_ORDER_TWO = 'export_detail_order_two';
    //验证报表导出明细自定义字段
    const EXPORT_DETAIL_CHECKED_TWO = 'export_detail_checked_two';

    /**
     * 获取导出字段
     * <AUTHOR>
     * @date   2023/11/28
     *
     * @param  string  $tag
     *
     * @return array
     */
    public function getExportField(string $tag)
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            if (empty($tag)) {
                throw new \Exception('参数错误', 203);
            }

            $factory = Factory::getDriverFromSession($tag);
            $result  = $factory::getDefaultCustomField();
            $data    = $result['list'] ?? [];
            $data    = $factory->fieldLimit($data);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取导出字段配置
     * <AUTHOR>
     * @date   2023/11/28
     *
     * @param  string  $tag
     *
     * @return array
     */
    public function getUserConfig(string $tag)
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            if (empty($tag)) {
                throw new \Exception('参数错误', 203);
            }
            $data = Factory::getDriverFromSession($tag)->getUserSelectData();
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 导出字段配置
     * <AUTHOR>
     * @date   2023/11/28
     *
     * @param  string  $tag
     * @param  array   $val
     *
     * @return array
     */
    public function setUserConfig(string $tag, array $val)
    {
        $data = [];
        try {
            if (empty($tag) || empty($val)) {
                throw new \Exception('参数错误', 203);
            }
            $factory  = Factory::getDriverFromSession($tag);
            $default  = $factory::getDefaultCustomField();
            $required = $factory::getDefaultRequiredField($default);

            $select = $factory->getUserSelectData();

            //必选判断
            if (!empty(array_diff($required, $val))) {
                throw new \Exception('缺失必选参数', 203);
            }

            //异常字段限制
            if (!empty(array_diff($val, $default['select'] ?? []))) {
                throw new \Exception('参数存在异常', 203);
            }

            //判断是否未更改,因为有排序，所以需要按序号比较
            $isEdit = false;
            if (count($val) == count($select)) {
                foreach ($select as $key => $item) {
                    if (($val[$key] ?? '') != $item) {
                        $isEdit = true;
                    }
                }
            } else {
                $isEdit = true;
            }

            if (!$isEdit) {
                throw new \Exception('没有存在变更，无需修改', 200);
            }

            $res  = Factory::getDriverFromSession($tag)->setUserCustomField($val);
            $code = $res['code'] ?? 400;
            $data = $res['data'] ?? [];
            $msg  = $res['msg'] ?? '异常';

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }
}