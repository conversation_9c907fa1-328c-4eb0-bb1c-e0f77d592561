<?php

namespace Business\DownloadCenter\CustomField;

use Business\Common\CommonConfig;
use Business\Member\Member;
use Library\component\Singleton;
use Library\Util\ArrayUtil;
use Model\CommonConfig\Config;

/**
 * 自定义字段导出
 */
abstract class Server
{
    protected static $defaultConfig = [];
    protected static $defaultConfigListMap = [];
    protected $sid;
    protected $sdType;
    protected $memberId;
    protected $userSelect = [];

    public function __construct($sid, $memberId, $sdType = null) {
        $this->sid = intval($sid);
        $this->memberId = intval($memberId);
        if (is_null($sdType) && $this->sid > 1) {
            $this->sdType = $this->getMemberDType($this->sid);
        } else {
            $this->sdType = $sdType;
        }
        if ($this->isAllowed()) {
            $this->userSelect = $this->getUserCustomField();
        }
    }

    /**
     * 判断当前用户是否可使用自定义导出
     * @return bool
     */
    public function isAllowed() {
        return $this->sid > 1 && in_array($this->sdType, [0, 1]);
    }

    public function getMemberDType($sid) {
        $memberBiz = new Member();
        $memberInfos = $memberBiz->getMemberInfoByMulti([$sid]);
        $memberInfo = array_pop($memberInfos);
        return $memberInfo['dtype'] ?? 0;
    }

    /**
     * 获取默认的配置
     * @return array
     */
    public static function getDefaultCustomField()
    {
        $tagKey = uncamelize(self::getShortClassname());
        if (!isset(self::$defaultConfig[$tagKey])) {
            $configModel = new Config();
            $result      = $configModel->getConfigDict($tagKey . '_list');
            $tagDefault  = $result['tag_default'] ?? null;
            $list        = $tagDefault ? json_decode($tagDefault, true) : [];
            $result      = $configModel->getConfigDict($tagKey);
            $tagDefault  = $result['tag_default'] ?? null;
            $select      = $tagDefault ? json_decode($tagDefault, true) : [];

            self::$defaultConfig[$tagKey] = ['list' => $list, 'select' => $select];
        }

        return self::$defaultConfig[$tagKey] ?? [];
    }

    /**
     * 获取自定义字段配置
     * @return array
     */
    protected function getUserCustomField() {
        $tagKey = uncamelize(self::getShortClassname());
        $userConfigModel = new CommonConfig();
        $result = $userConfigModel->getUserConfig($this->sid, $this->memberId, $tagKey);
        return $result['data'] ?? [];
    }

    /**
     * 报表自定义字段配置
     * @param $tagVal
     * @return array
     */
    public function setUserCustomField($tagVal) {
        $tagKey = uncamelize(self::getShortClassname());
        $userConfigModel = new CommonConfig();
        $result = $userConfigModel->setUserConfig($this->sid, $this->memberId, $tagKey, $tagVal);
        if ($result['code'] == 200) {
            $this->userSelect = $tagVal;
        }
        return $result;
    }

    protected static function getShortClassname()
    {
        $classArr = explode('\\', static::class);
        return array_pop($classArr);
    }

    /**
     * 获取所有的选中tag
     * @return array
     */
    public function getUserSelectData()
    {
        return $this->userSelect;
    }

    /**
     * 是否tag被选中
     * @param string|array $tag
     * @return bool
     */
    public function isTagSelected($tag)
    {
        if (!$this->isAllowed()) {
            return true;
        }

        if (!is_array($tag)) {
            $tag = [$tag];
        }
        return !!array_intersect($tag, $this->getUserSelectData());
    }

    /**
     * 排序数组
     * @param array $data list或者map
     * @param array $keys
     * @param array $defaultValue 默认值：null表示字段不在列表内过滤，否则用默认值填充
     * @return array
     */
    public function sortBy($data, $keys, $defaultValue = '')
    {
        if (!$this->isAllowed() || !$data || !is_array($data)) {
            return $data;
        }
        if (is_array(reset($data))) {
            //二维数组
            foreach ($data as $key => $item) {
                $data[$key] = ArrayUtil::intersectMerge($keys, $item, $defaultValue);
            }
            return $data;
        } else {
            return ArrayUtil::intersectMerge($keys, $data, $defaultValue);
        }
    }

    /**
     * 排序数组
     * @param array $data
     * @param array $defaultValue 默认值：null表示字段不在列表内过滤，否则用默认值填充
     * @return array
     */
    public function sort($data, $defaultValue = '')
    {
        return $this->sortBy($data, $this->getUserSelectData(), $defaultValue);
    }

    /**
     * @param $method
     * @param $arguments
     * @return bool
     * @throws \Exception
     */
    public function __call($method, $arguments)
    {
        if (strpos($method, 'isCustom') === 0) {
            $tag = strtoupper(uncamelize(substr($method, 2)));
            if (!defined(static::class . '::' . $tag) || !$this->isAllowed()) {
                return true;
            }
            $tagDefinition = constant(static::class . '::' . $tag);

            return $this->isTagSelected($tagDefinition);
        }
        throw new \Exception('method is undefined');
    }

    /**
     * 导出字段
     * <AUTHOR>
     * @date   2023/11/29
     *
     * @param  array  $data
     *
     * @return array
     */
    public function fieldLimit(array $data)
    {
        return $data;
    }

    /**
     * 获取默认必选字段
     * <AUTHOR>
     * @date   2023/11/29
     *
     * @return array
     */
    public static function getDefaultRequiredField($default)
    {
        $required = [];
        if ($default !== null) {

            $list = $default['list'] ?? [];
            foreach ($list as $item) {
                if (!is_array($item['list'])) {
                    continue;
                }
                foreach ($item['list'] as $val) {
                    if ($val['required']) {
                        $required[] = $val['key'];
                    }
                }
            }
        }

        return $required;
    }

    /**
     * 获取自定义字段配置key->val
     * @return array
     */
    public static function getDefaultCustomFieldListMap()
    {
        $tagKey = uncamelize(self::getShortClassname());
        if (!isset(self::$defaultConfigListMap[$tagKey])) {
            $configModel = new Config();
            $result      = $configModel->getConfigDict($tagKey . '_list');
            $tagDefault  = $result['tag_default'] ?? null;
            $data        = $tagDefault ? json_decode($tagDefault, true) : [];
            $list        = array_column($data, 'list');

            $result = [];
            foreach ($list as $item) {
                foreach ($item as $tmp) {
                    $result[$tmp['key']] = $tmp['title'];
                }
            }

            self::$defaultConfigListMap[$tagKey] = $result;
        }

        return self::$defaultConfigListMap[$tagKey] ?? [];
    }

    public function setExternalCustomField($fields)
    {
        if (!empty($fields)) {
            $this->userSelect = $fields;
        }
        return true;
    }
}