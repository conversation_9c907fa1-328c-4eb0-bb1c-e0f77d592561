<?php

namespace Business\DownloadCenter\CustomField;

use Business\MemberLogin\MemberLoginHelper;

class Factory
{
    protected static $instances = [];

    /**
     * 从session中获取用户信息并实例化类
     * @param string $driverName
     * @return Server
     * @throws \Exception
     */
    public static function getDriverFromSession($driverName)
    {
        $loginMember = MemberLoginHelper::getLoginBusinessMember();
        if (!$loginMember->getSId() && !$loginMember->getMemberId()) {
            throw new \Exception('请先登陆');
        }
        return self::getDriver($driverName, $loginMember->getSId(), $loginMember->getMemberId(), $loginMember->getSDtype());
    }

    /**
     * 获取驱动
     * @param string $driverName
     * @param int $sid
     * @param int $memberId
     * @param int $sdType
     * @return Server
     * @throws \Exception
     */
    public static function getDriver($driverName, $sid, $memberId, $sdType = null) {
        if (strpos($driverName, '\\') === false) {
            $driverName = ucfirst(camelize($driverName));
            $driverName = __NAMESPACE__ . '\\Driver\\' . $driverName;
        }
        $cacheKey = str_replace('\\', '_', $driverName);
        $cacheKey = $cacheKey . '_' . $memberId;
        if (!isset(self::$instances[$cacheKey])) {
            if (!class_exists($driverName)) {
                throw new \Exception('不支持该操作');
            }
            self::$instances[$cacheKey] = new $driverName($sid, $memberId, $sdType);
        }
        return self::$instances[$cacheKey];
    }

    /**
     * 外部传入已选定字段，实例化类
     * <AUTHOR>
     * @date   2025/03/03
     *
     * @param $driverName
     * @param $sid
     * @param $memberId
     * @param $sdType
     * @param $selectFields
     *
     * @return Server
     * @throws
     */
    public static function getDriverFromExternalSelect($driverName, $sid, $memberId, $sdType = null, $selectFields = [])
    {
        $factory = self::getDriver($driverName, $sid, $memberId, $sdType);
        if (empty($selectFields)) {
            return $factory;
        }
        $factory->setExternalCustomField($selectFields);

        return $factory;
    }
}