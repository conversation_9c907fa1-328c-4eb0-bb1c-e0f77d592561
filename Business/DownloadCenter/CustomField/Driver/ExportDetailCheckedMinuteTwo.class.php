<?php

namespace Business\DownloadCenter\CustomField\Driver;

use Business\DownloadCenter\CustomField\Server;

/**
 * 验证分钟报表
 * @method bool isCustomOrdernum()
 * @method bool isCustomProductName()
 * @method bool isCustomOrderTime()
 * @method bool isCustomPlayTime()
 * @method bool isCustomFirstTime()
 * @method bool isCustomBeginTime()
 * @method bool isCustomEndTime()
 * @method bool isCustomFinishTime()
 * @method bool isCustomPayTime()
 * @method bool isCustomRevokeTime()
 * @method bool isCustomOrdersStatus()
 * @method bool isCustomTnum()
 * @method bool isCustomCheckNum()
 * @method bool isCustomFinishNum()
 * @method bool isCustomRevokeNum()
 * @method bool isCustomSalePrice()
 * @method bool isCustomCostPrice()
 * @method bool isCustomCheckMoney()
 * @method bool isCustomFinishMoney()
 * @method bool isCustomCostMoney()
 * @method bool isCustomRevokeSaleMoney()
 * @method bool isCustomRevokeCostMoney()
 * @method bool isCustomCancelService()
 * @method bool isCustomTodayCheckNum()
 * @method bool isCustomOffdayCheckNum()
 * @method bool isCustomCode()
 * @method bool isCustomOrdername()
 * @method bool isCustomOrdertel()
 * @method bool isCustomPersonid()
 * @method bool isCustomComName()
 * @method bool isCustomResellerName()
 * @method bool isCustomGroup()
 * @method bool isCustomUpApplyName()
 * @method bool isCustomSellPayMode()
 * @method bool isCustomBuyPayMode()
 * @method bool isCustomRemotenum()
 * @method bool isCustomThirdOrdernum()
 * @method bool isCustomOpName()
 * @method bool isCustomCheckOpName()
 * @method bool isCustomReportChannel()
 * @method bool isCustomSiteName()
 * @method bool isCustomMemo()
 * @method bool isCustomPayCenterOrdernum()
 * @method bool isCustomMergeOrdernum()
 * @method bool isCustomAfterSaleTnum()
 * @method bool isCustomAfterCancelMoney()
 * @method bool isCustomAfterRealMoney()
 * @method bool isCustomAfterSaleNum()
 * @method bool isCustomVerifyDiscountMoney()
 * @method bool isCustomCostDiscountMoney()
 * @method bool isCustomCancelVerifyDiscountMoney()
 * @method bool isCustomCancelCostDiscountMoney()
 * @method bool isCustomSectionTime()
 */
class ExportDetailCheckedMinuteTwo extends Server
{
    // 订单编号
    const CUSTOM_ORDERNUM = 'ordernum';
    // 产品名称
    const CUSTOM_PRODUCT_NAME = 'product_name';
    // 下单时间
    const CUSTOM_ORDER_TIME = 'order_time';
    // 预计游玩日期
    const CUSTOM_PLAY_TIME = 'play_time';
    // 首次入园时间
    const CUSTOM_FIRST_TIME = 'first_time';
    // 开始有效日期
    const CUSTOM_BEGIN_TIME = 'begin_time';
    // 截止有效时间
    const CUSTOM_END_TIME = 'end_time';
    // 完成时间
    const CUSTOM_FINISH_TIME = 'finish_time';
    // 支付时间
    const CUSTOM_PAY_TIME = 'pay_time';
    // 撤销/撤改时间
    const CUSTOM_REVOKE_TIME = 'revoke_time';
    // 订单状态
    const CUSTOM_ORDERS_STATUS = 'orders_status';
    // 总票数
    const CUSTOM_TNUM = 'tnum';
    // 验证总数
    const CUSTOM_CHECK_NUM = 'check_num';
    // 完结票数
    const CUSTOM_FINISH_NUM = 'finish_num';
    // 撤销/撤改票数
    const CUSTOM_REVOKE_NUM = 'revoke_num';
    // 销售单价
    const CUSTOM_SALE_PRICE = 'sale_price';
    // 采购单价
    const CUSTOM_COST_PRICE = 'cost_price';
    // 验证金额
    const CUSTOM_CHECK_MONEY = 'check_money';
    // 完结金额
    const CUSTOM_FINISH_MONEY = 'finish_money';
    // 采购金额
    const CUSTOM_COST_MONEY = 'cost_money';
    // 撤销/撤改验证金额
    const CUSTOM_REVOKE_SALE_MONEY = 'revoke_sale_money';
    // 撤销/撤改采购金额
    const CUSTOM_REVOKE_COST_MONEY = 'revoke_cost_money';
    // 凭证码
    const CUSTOM_CODE = 'code';
    // 卖出支付方式
    const CUSTOM_SELL_PAY_MODE = 'sell_pay_mode';
    // 买入支付方式
    const CUSTOM_BUY_PAY_MODE = 'buy_pay_mode';
    // 渠道
    const CUSTOM_REPORT_CHANNEL = 'report_channel';
    // 站点
    const CUSTOM_SITE_NAME = 'site_name';
    // 订单备注
    const CUSTOM_MEMO = 'memo';
    // 取票人
    const CUSTOM_ORDERNAME = 'ordername';
    // 手机
    const CUSTOM_ORDERTEL = 'ordertel';
    // 身份证
    const CUSTOM_PERSONID = 'personid';
    // 分销商企业名称
    const CUSTOM_COM_NAME = 'com_name';
    // 分销商账户名称
    const CUSTOM_RESELLER_NAME = 'reseller_name';
    // 分组
    const CUSTOM_GROUP = 'group';
    // 上级供应商
    const CUSTOM_UP_APPLY_NAME = 'up_apply_name';
    // 下单员工
    const CUSTOM_OP_NAME = 'op_name';
    // 检票员
    const CUSTOM_CHECK_OP_NAME = 'check_op_name';
    // 远程订单号
    const CUSTOM_REMOTENUM = 'remotenum';
    // 第三方订单号
    const CUSTOM_THIRD_ORDERNUM = 'third_ordernum';
    // 售后数量
    const CUSTOM_AFTER_SALE_TNUM = 'after_sale_tnum';
    // 售后退回金额
    const CUSTOM_AFTER_CANCEL_MONEY = 'after_cancel_money';
    // 售后收入金额
    const CUSTOM_AFTER_REAL_MONEY = 'after_real_money';
    // 售后编号
    const CUSTOM_AFTER_SALE_NUM = 'after_sale_num';
    // 支付中心订单号
    const CUSTOM_PAY_CENTER_ORDERNUM = 'pay_center_ordernum';
    // 合并订单号
    const CUSTOM_MERGE_ORDERNUM = 'merge_ordernum';
    // 退票手续费
    const CUSTOM_CANCEL_SERVICE = 'cancel_service';
    // 当日下单验证票数
    const CUSTOM_TODAY_CHECK_NUM = 'today_check_num';
    // 非当日下单验证票数
    const CUSTOM_OFFDAY_CHECK_NUM = 'offday_check_num';
    // 验证优惠金额
    const CUSTOM_VERIFY_DISCOUNT_MONEY = 'verify_discount_money';
    // 采购优惠金额
    const CUSTOM_COST_DISCOUNT_MONEY = 'cost_discount_money';
    // 取消验证优惠金额
    const CUSTOM_CANCEL_VERIFY_DISCOUNT_MONEY = 'cancel_verify_discount_money';
    // 取消采购优惠金额
    const CUSTOM_CANCEL_COST_DISCOUNT_MONEY = 'cancel_cost_discount_money';
    // 分时时间段
    const CUSTOM_SECTION_TIME = 'section_time';
}