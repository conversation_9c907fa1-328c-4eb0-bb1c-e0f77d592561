<?php
/**
 * 客源地报表导出明细
 * <AUTHOR>
 * @date   2024/07/11
 */

namespace Business\DownloadCenter\CustomField\Driver;

use Business\DownloadCenter\CustomField\Server;

/**
 * @method bool isCustomChkCode()
 * @method bool isCustomSerialNumber()
 * @method bool isCustomOpTime()
 * @method bool isCustomOpType()
 * @method bool isCustomOpMember()
 * @method bool isCustomOpSite()
 * @method bool isCustomCount()
 * @method bool isCustomOrderNum()
 * @method bool isCustomProductName()
 * @method bool isCustomTicketName()
 * @method bool isCustomOrderChannel()
 * @method bool isCustomResellerGroup()
 * @method bool isCustomResellerName()
 * @method bool isCustomIdType()
 * @method bool isCustomIdNumber()
 * @method bool isCustomRegionName()
 * @method bool isCustomProvinceName()
 * @method bool isCustomCityName()
 * @method bool isCustomDistrictName()
 *
 */
class ExportDetailTouristSourceArea extends Server
{
    //门票码
    const CHK_CODE = 'chk_code';
    //序号
    const SERIAL_NUMBER = 'serial_number';
    //操作时间
    const OP_TIME = 'op_time';
    //操作类型
    const OP_TYPE = 'op_type';
    //操作人员
    const OP_MEMBER = 'op_member';
    //操作站点
    const OP_SITE = 'op_site';
    //计数
    const COUNT = 'count';
    //订单号
    const ORDER_NUM = 'ordernum';
    //产品
    const PRODUCT_NAME = 'product_name';
    //票种
    const TICKET_NAME = 'ticket_name';
    //下单渠道
    const ORDER_CHANNEL = 'order_channel';
    //分销商分组
    const RESELLER_GROUP = 'reseller_group';
    //分销商
    const RESELLER_NAME = 'reseller_name';
    //证件类型
    const ID_TYPE = 'id_type';
    //证件号
    const ID_NUMBER = 'id_number';
    //地域
    const REGION_NAME = 'region_name';
    //省
    const PROVINCE_NAME = 'province_name';
    //市
    const CITY_NAME = 'city_name';
    //区
    const DISTRICT_NAME = 'district_name';
}