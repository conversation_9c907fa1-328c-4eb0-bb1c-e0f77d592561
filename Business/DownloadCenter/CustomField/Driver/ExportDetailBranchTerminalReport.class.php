<?php

namespace Business\DownloadCenter\CustomField\Driver;

use Business\DownloadCenter\CustomField\Server;

/**
 *
 * @method bool isCustomOrderNum()
 * @method bool isCustomStatus()
 * @method bool isCustomVerifyNum()
 * @method bool isCustomOpName()
 * @method bool isCustomLandName()
 * @method bool isCustomTerminal()
 * @method bool isCustomTicketName()
 * @method bool isCustomTnum()
 */
class ExportDetailBranchTerminalReport extends Server
{
    //订单号
    const CUSTOM_ORDER_NUM = 'order_num';
    //订单状态
    const CUSTOM_STATUS = 'status';
    //已验证数量
    const CUSTOM_VERIFY_NUM = 'verify_num';
    //员工
    const CUSTOM_OP_NAME = 'op_name';
    //产品
    const CUSTOM_LAND_NAME = 'land_name';
    //分终端
    const CUSTOM_TERMINAL = 'terminal';
    //票类
    const CUSTOM_TICKET_NAME = 'ticket_name';
    //终端检票数
    const CUSTOM_TNUM = 'tnum';
}