<?php

namespace Business\DownloadCenter\CustomField\Driver;

use Business\DownloadCenter\CustomField\Server;
use Business\SubMerchant\SubMerchant;
use Business\AppCenter\Module as ModuleBiz;

/**
 * @method bool isCustomOrdernum()
 * @method bool isCustomOrdertime()
 * @method bool isCustomPlaytime()
 * @method bool isCustomBegintime()
 * @method bool isCustomEndtime()
 * @method bool isCustomDtime()
 * @method bool isCustomPaystatus()
 * @method bool isCustomCanceltime()
 * @method bool isCustomPtype()
 * @method bool isCustomPname()
 * @method bool isCustomStations()
 * @method bool isCustomSellmoney()
 * @method bool isCustomTnum()
 * @method bool isCustomSelltotalmoney()
 * @method bool isCustomSellpaymode()
 * @method bool isCustomStatus()
 * @method bool isCustomAuditStatus()
 * @method bool isCustomOrderSerial()
 * @method bool isCustomCode()
 * @method bool isCustomExternalcode()
 * @method bool isCustomOriginnum()
 * @method bool isCustomOriginmoney()
 * @method bool isCustomActualpaymoney()
 * @method bool isCustomRefundnum()
 * @method bool isCustomBuymode()
 * @method bool isCustomBuymoney()
 * @method bool isCustomBuytotalmoney()
 * @method bool isCustomSource()
 * @method bool isCustomChecked()
 * @method bool isCustomIsprint()
 * @method bool isCustomMemo()
 * @method bool isCustomReseller()
 * @method bool isCustomGroup()
 * @method bool isCustomOrderman()
 * @method bool isCustomOrdername()
 * @method bool isCustomContacttel()
 * @method bool isCustomPersonid()
 * @method bool isCustomBuyname()
 * @method bool isCustomPrintName()
 * @method bool isCustomRemotenum()
 * @method bool isCustomApiorder()
 * @method bool isCustomApicode()
 * @method bool isCustomCouponName()
 * @method bool isCustomCouponValue()
 * @method bool isCustomCouponNum()
 * @method bool isCustomPlay()
 * @method bool isCustomZone()
 * @method bool isCustomSeat()
 * @method bool isCustomAnnualCard()
 * @method bool isCustomUserResoure()
 * @method bool isCustomTeamUserResoure()
 * @method bool isCustomOrderType()
 * @method bool isCustomIsLodging()
 * @method bool isCustomHotel()
 * @method bool isCustomTeamTypeName()
 * @method bool isCustomDriverName()
 * @method bool isCustomDriverTelephone()
 * @method bool isCustomCarNumber()
 */
class ExportDetailTeamOrderSearch extends Server
{
    // 订单编号
    const CUSTOM_ORDERNUM = 'ordernum';
    // 下单时间
    const CUSTOM_ORDERTIME = 'ordertime';
    // 预计游玩时间
    const CUSTOM_PLAYTIME = 'playtime';
    // 开始有效日期
    const CUSTOM_BEGINTIME = 'begintime';
    // 截止有效日期
    const CUSTOM_ENDTIME = 'endtime';
    // 完成时间
    const CUSTOM_DTIME = 'dtime';
    // 支付状态
    const CUSTOM_PAYSTATUS = 'paystatus';
    // 取消时间
    const CUSTOM_CANCELTIME = 'canceltime';
    // 类别
    const CUSTOM_PTYPE = 'ptype';
    // 产品名称
    const CUSTOM_PNAME = 'pname';
    // 产品名称
    const CUSTOM_STATIONS = 'stations';
    // 销售单价
    const CUSTOM_SELLMONEY = 'sellmoney';
    // 实售票数
    const CUSTOM_TNUM = 'tnum';
    // 实售金额
    const CUSTOM_SELLTOTALMONEY = 'selltotalmoney';
    // 卖出支付方式
    const CUSTOM_SELLPAYMODE = 'sellpaymode';
    // 订单状态
    const CUSTOM_STATUS = 'status';
    // 退票状态
    const CUSTOM_AUDITSTATUS = 'auditStatus';
    // 票面流水号
    const CUSTOM_ORDERSERIAL = 'orderSerial';
    // 凭证码
    const CUSTOM_CODE = 'code';
    // 外部码
    const CUSTOM_EXTERNALCODE = 'externalcode';
    // 下单票数
    const CUSTOM_ORIGINNUM = 'originnum';
    // 下单金额
    const CUSTOM_ORIGINMONEY = 'originmoney';
    // 销售实付金额(扣除优惠金额)
    const CUSTOM_ACTUALPAYMONEY = 'actualpaymoney';
    // 买入支付方式
    const CUSTOM_BUYMODE = 'buymode';
    // 采购单价
    const CUSTOM_BUYMONEY = 'buymoney';
    // 采购金额
    const CUSTOM_BUYTOTALMONEY = 'buytotalmoney';
    // 销售渠道
    const CUSTOM_SOURCE = 'source';
    // 已验证数
    const CUSTOM_CHECKED = 'checked';
    // 取票状态
    const CUSTOM_ISPRINT = 'isprint';
    // 游客备注
    const CUSTOM_MEMO = 'memo';
    // 分销商
    const CUSTOM_RESELLER = 'reseller';
    // 分组
    const CUSTOM_GROUP = 'group';
    // 下单员工
    const CUSTOM_ORDERMAN = 'orderman';
    // 取票人/收货人/联系人
    const CUSTOM_ORDERNAME = 'ordername';
    // 手机
    const CUSTOM_CONTACTTEL = 'contacttel';
    // 游客身份证
    const CUSTOM_PERSONID = 'personid';
    // 上级供应商
    const CUSTOM_BUYNAME = 'buyname';
    // 取票员工
    const CUSTOM_PRINTNAME = 'printName';
    // 远端订单号
    const CUSTOM_REMOTENUM = 'remotenum';
    // 第三方系统订单
    const CUSTOM_APIORDER = 'apiorder';
    // 第三方系统凭证码
    const CUSTOM_APICODE = 'apicode';
    // 优惠券名称
    const CUSTOM_COUPON_NAME = 'coupon_name';
    // 优惠券数量
    const CUSTOM_COUPON_VALUE = 'coupon_value';
    // 优惠券数量
    const CUSTOM_COUPON_NUM = 'coupon_num';
    // 演出场次
    const CUSTOM_PLAY = 'play';
    // 座位分区
    const CUSTOM_ZONE = 'zone';
    // 座位号
    const CUSTOM_SEAT = 'seat';
    // 年卡套餐
    const CUSTOM_ANNUALCARD = 'annualCard';
    // 客源地
    const CUSTOM_USER_RESOURE = 'user_resoure';

    // 团队订单客源地
    const CUSTOM_TEAM_USER_RESOURE = 'team_user_resoure';

    // 报团渠道
    const CUSTOM_ORDER_TYPE = 'order_type';

    // 住宿
    const CUSTOM_IS_LOAGING = 'is_lodging';

    // 入住酒店
    const CUSTOM_HOTEL = 'hotel';

    // 团队类型
    const CUSTOM_TYPE_NAME = 'team_type_name';

    // 司机名称
    const CUSTOM_DRIVER_NAME = 'driver_name';

    // 司机手机号
    const CUSTOM_DRIVER_TELEPHONE = 'driver_telephone';

    // 司机车牌号
    const CUSTOM_CAR_NUMBER = 'car_number';

    /**
     * 字段限制
     * @date   2023/11/29
     *
     * @param  array  $data
     *
     * @return array
     */
    public function fieldLimit(array $data)
    {
        foreach ($data as $k => $item) {
            if (!is_array($item['list'])) {
                continue;
            }
            foreach ($item['list'] as $key => $val) {
                //商户字段处理
                if ($this->_merchantManagementLimit($val['key'])) {
                    unset($item['list'][$key]);
                }
            }

            $data[$k]['list'] = array_values($item['list']);
            if (empty($data[$k]['list'])) {
                unset($data[$k]);
            }
        }

        return array_values($data);
    }

    /**
     * 获取所有的选中tag
     * @return array
     */
    public function getUserSelectData()
    {
        foreach ($this->userSelect as $key => $tag) {
            //商户字段处理
            if ($this->_merchantManagementLimit($tag)) {
                unset($this->userSelect[$key]);
            }
        }

        return array_values($this->userSelect);
    }


    /**
     * 商户管理应用限制商户字段
     *
     * @return bool
     */
    private function _merchantManagementLimit($tag)
    {
        //如果不是商户字段 返回false 不移除
        if (!in_array($tag, $this->_merchantManagementField)) {
            return false;
        }

        //是商户字段，账号类型和id不符，就移除商户字段
        if (!in_array($this->sdType, [0]) || $this->sid == 1){
            return true;
        }

        //获取子商户应用记录
        $userModuleUsedRes = (new ModuleBiz())->getUserModuleUsed([$this->sid], [SubMerchant::SUB_MERCHANT_APP_CENTER_TAG], false);

        //存在子商户应用记录，则excel会有商户列，不存在则返回空
        if (empty($userModuleUsedRes[$this->sid][SubMerchant::SUB_MERCHANT_APP_CENTER_TAG])){
            return true;
        }

        return false;
    }
}