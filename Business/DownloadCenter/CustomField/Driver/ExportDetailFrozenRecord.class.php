<?php

namespace Business\DownloadCenter\CustomField\Driver;

use Business\DownloadCenter\CustomField\Server;

/**
 * 交易记录-冻结金额
 * @method bool isCustomTradeId()
 * @method bool isCustomRectime()
 * @method bool isCustomOrderid()
 * @method bool isCustomOrderstatus()
 * @method bool isCustomTradeNo()
 * @method bool isCustomCmbId()
 * @method bool isCustomToMerchant()
 * @method bool isCustomToAccount()
 * @method bool isCustomFeeType()
 * @method bool isCustomProName()
 * @method bool isCustomSubjectBook()
 * @method bool isCustomMoney()
 * @method bool isCustomBalance()
 * @method bool isCustomMemo()
 */
class ExportDetailFrozenRecord extends Server
{
    // 交易流水号
    const CUSTOM_TRADE_ID = 'trade_id';
    // 交易时间
    const CUSTOM_RECTIME = 'rectime';
    // 对方商户
    const CUSTOM_TO_MERCHANT = 'to_merchant';
    // 对方账户
    const CUSTOM_TO_ACCOUNT = 'to_account';
    // 费用类型
    const CUSTOM_FEE_TYPE = 'fee_type';
    // 账本类型
    const CUSTOM_SUBJECT_BOOK = 'subject_book';
    // 冻结收支金额
    const CUSTOM_MONEY = 'money';
    // 冻结余额
    const CUSTOM_BALANCE = 'balance';
    // 备注
    const CUSTOM_MEMO = 'memo';
    // 订单状态
    const CUSTOM_ORDERSTATUS = 'orderstatus';
    // 业务订单号
    const CUSTOM_ORDERID = 'orderid';
    // 建议订单号
    const CUSTOM_CMB_ID = 'cmb_id';
    // 产品名称
    const CUSTOM_PRO_NAME = 'pro_name';
    // 第三方流水号
    const CUSTOM_TRADE_NO = 'trade_no';
}