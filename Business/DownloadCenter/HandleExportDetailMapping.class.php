<?php
/**
 * 下载中心导出明细字段映射处理
 * <AUTHOR>
 * @date   2023/12/5
 */

namespace Business\DownloadCenter;

use Business\Base;
use Business\DownloadCenter\CustomField\Server;

class HandleExportDetailMapping extends Base
{
    /**
     * 处理数组idx和key的关系
     * <AUTHOR>
     * @date   2023/12/5
     *
     * @param  array  $head
     * @param  array  $idxToKeyMap
     *
     * @return array
     */
    public static function handleHeadKeyIdxMap($head, $idxToKeyMap)
    {
        if (empty($head)) {
            return [];
        }

        $headKeyIdxMap = [];

        foreach (array_values($head) as $k => $title) {
            foreach ($idxToKeyMap as $key => $val) {
                if ($title != $val) {
                    continue;
                }
                $headKeyIdxMap[$k] = $key;
            }
        }

        return $headKeyIdxMap;
    }

    /**
     * 导出自定义字段处理抬头文字
     * <AUTHOR>
     * @date   2023/12/5
     *
     * @param  array   $head
     * @param  server  $customField
     *
     * @return array
     */
    public static function handleNewHeadByIdx($head, $customField = null)
    {
        if (!$customField->isAllowed() || empty($head) || empty($customField)) {
            return $head;
        }

        $selectField = $customField->getUserSelectData();

        //获取全部字段key->val
        $idxToKeyMap = $customField::getDefaultCustomFieldListMap();

        $newHead = [];

        foreach ($selectField as $key) {
            foreach (array_values($head) as $title) {
                foreach ($idxToKeyMap as $k => $val) {
                    if ($title != $val) {
                        continue;
                    }
                    if ($k != $key) {
                        continue;
                    }

                    $newHead[] = $title;
                }
            }
        }

        return $newHead;
    }

    /**
     * 处理下数据字段
     * <AUTHOR>
     * @date   2023/12/5
     *
     * @param  array   $item
     * @param  array   $head
     * @param  Server  $customField
     *
     * @return mixed
     */
    public static function handleDataFiledByIdx($item, $head, $customField = null)
    {
        if (!$customField->isAllowed() || empty($head) || empty($item) || empty($customField)) {
            return $item;
        }

        //获取全部字段key->val
        $idxToKeyMap = $customField::getDefaultCustomFieldListMap();

        //获取用户配置的字段key
        $userSelect = $customField->getUserSelectData();

        //处理idx和key关系
        $headKeyIdxMap = self::handleHeadKeyIdxMap($head, $idxToKeyMap);

        $tmp = [];
        foreach ($item as $k => $v) {
            if (!isset($headKeyIdxMap[$k])) {
                continue;
            }

            $tmp[$headKeyIdxMap[$k]] = $v;
        }

        //导出需要的key
        $newKey = [];
        //现有的key字段
        $keyArr = array_keys($tmp);
        foreach ($userSelect as $key) {
            //配置导出的字段，不在现有字段里，导出不展示
            if (!in_array($key, $keyArr)) {
                continue;
            }

            $newKey[] = $key;
        }

        //按照导出配置重新排序下
        return $customField->sortBy($tmp, $newKey);
    }
}