<?php

namespace Business\PackTicket;

use Business\Base;
use Business\JavaApi\Order\Order;
use Business\JavaApi\Order\Query\UuOrderAddon;
use Business\JavaApi\Product\Ticket;
use Business\JsonRpcApi\MessageService\MessageService;
use Library\Container;
use Library\MessageNotify\OrderNotify;
use Library\Util\EnvUtil;
use Library\wechat\core\TemplateMessage;
use Model\PackTicket\PackageTicketsModel;
use Model\Wechat\WxMember;

/**
 * 套票订单业务
 */
class PackOrder extends Base
{

    /**
     * 批量出套票子票订单信息集合
     *
     * author queyourong
     * date 2022/8/24
     *
     * @param array $parentOrderNumArr 主票id
     */
    public function batchQueryOrderAddonBypackOrders(array $parentOrderNumArr)
    {
        if (!$parentOrderNumArr) {
            return $this->returnData(204, '参数错误');
        }

        $queryParams = [$parentOrderNumArr];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuOrderAddon',
            'batchQueryOrderAddonBypackOrders', $queryParams);

        return $queryRes;
    }

    /**
     * 获取套票所有子票的信息
     * author queyourong
     * date 2022/8/24
     * @param string $orderNum
     */
    public function getOrderWithAddon($orderNum)
    {
        if (!$orderNum) {
            return $this->returnData(204, '参数错误');
        }

        $queryParams = [$orderNum];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderAddon',
            'getOrderWithAddon', $queryParams);

        return $queryRes;
    }

    /**
     * 通过订单号获取 检测套票是否生成成功
     *
     * author queyourong
     * date 2022/8/24
     * @param string $orderNum
     */
    public function packageTicketGenComplete($orderNum)
    {
        if (!$orderNum) {
            return $this->returnData(204, '参数错误');
        }

        $queryParams = [$orderNum];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderAddon',
            'packageTicketGenComplete', $queryParams);

        return $queryRes;
    }

    /**
     * 套票报表，将套票的主票和子票筛选出来
     * @param array $ordernums
     * @param bool $packOrderNotEmpty
     *
     * @return array
     * <AUTHOR> lingfeng
     * 2022/8/25 16:54
     */
    public function findByOrderIdAndPackOrderNotEmpty($ordernums, $packOrderNotEmpty = null)
    {
        if (empty($ordernums)) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        $queryParams = [$ordernums, $packOrderNotEmpty];

        $queryRes = \Business\JavaApi\Order\Query\Container::query('orderAddon',
            'findByOrderIdAndPackOrderNotEmpty', $queryParams);

        return $queryRes;
    }

    /**
     * 获取套票所有子票的信息
     * @param array $orderNumArr
     *
     * @return array
     * <AUTHOR> lingfeng
     * 2022/8/25 17:25
     */
    public function batchGetOrderWithAddon($orderNumArr)
    {
        if (!$orderNumArr) {
            return $this->returnData(204, '参数错误');
        }

        $queryParams = [$orderNumArr];

        $queryRes = \Business\JavaApi\Order\Query\Container::query('orderAddon',
            'batchGetOrderWithAddon', $queryParams);

        return $queryRes;
    }

    /**
     * 下单前置校验
     * @return array
     */
    public function preOrderCheck(array $paramArr)
    {
        $order = new Order();
        return $order->preOrderCheckForJavaApi($paramArr);
    }

    /**
     * 验证子票属性是否冲突
     * @param $subTicketIds
     * @return array
     */
    public function verifyPackageTicketAttrRuleConflict($subTicketIds){
//        $subTicketIds = ['110025780','110025781'];
        //至少需要两个子票id
        if(empty($subTicketIds) || count($subTicketIds)<=1){
            return $this->returnData(204, '传入的有效门票id数量小于2');
        }
        //查询所有子票数据
        $ticketApi  = new Ticket();
        $ticketList= $ticketApi->queryTicketInfoByIds($subTicketIds);
        if ($ticketList['code'] != 200 || empty($ticketList['data'])) {
            return $this->returnData(202, '下单产品票信息有误');
        }
        $ticketList = $ticketList['data'];
        // 下单人数验证
        // 获取购买下限数组
        $buyLimitLowList = [];
        // 获取购买上限数组
        $buyLimitUpList = [];
        //购票限制
        // 购买限制：0不限1手机号-张票2身份证-张票3手机号-笔订单4身份证-笔订单5同时-张票6同时-笔订单
        // 只需要限制类型为门票的数据
        $buyLimit = [1,2,5];
        //购票限制--限购张数数组
        $buyLimitNumList = [];

        // 年龄限制
        // 获取年龄限制下限数组
        $ageLimitMinList = [];
        // 获取年龄限制上限数组
        $ageLimitMaxList = [];

        foreach ($ticketList as $ticketInfo){
            $buyLimitLowList[] = (int) $ticketInfo['uuJqTicketDTO']['buyLimitLow'];
            $buyLimitUpList[] = (int) $ticketInfo['uuJqTicketDTO']['buyLimitUp'];
            if(in_array($ticketInfo['uuLandFDTO']['buyLimit'],$buyLimit)){
                $buyLimitNumList[] = (int) $ticketInfo['uuLandFDTO']['buyLimitNum'];
            }
            $ageLimitMinList[] = (int) $ticketInfo['uuLandFDTO']['ageLimitMin'];
            $ageLimitMaxList[] = (int) $ticketInfo['uuLandFDTO']['ageLimitMax'];
        }

        // 判断下单数量是否有交集
        // 获取购买下限的最大值
        $buyLimitLow = max($buyLimitLowList);
        // 获取购买上限的最小值
        $buyLimitUp = min($buyLimitUpList);
        if ($buyLimitUp>0 && $buyLimitLow > $buyLimitUp){
            $verifyMessage = '票属性：购买上限和购买下限冲突';
            return $this->returnData(205, $verifyMessage,$ticketList);
        }

        // 判断下单数量和限购是否冲突
        //购票限制--限购张数最小值
        if(!empty($buyLimitNumList)){
            $buyLimitNum = min($buyLimitNumList);
            if ($buyLimitNum < $buyLimitLow || $buyLimitNum > $buyLimitUp){
                $verifyMessage = '票属性：购买数量与限购数量冲突';
                return $this->returnData(205, $verifyMessage,$ticketList);
            }
        }

        // 年龄限制判断
        // 获取年龄限制下限的最大值
        $ageLimitMin = max($ageLimitMinList);
        // 获取年龄限制上限的最小值
        $ageLimitMax = min($ageLimitMaxList);
        if($ageLimitMin > $ageLimitMax){
            $verifyMessage = '票属性：年龄限制冲突';
            return $this->returnData(205, $verifyMessage,$ticketList);
        }
        // TODO 获取实名制信息 0不需要填写 1需要填写 2需要填写所有游客身份证
        return $this->returnData(200);
//        var_dump($ticketList);
//        var_dump($buyLimitLowList,$buyLimitUpList,$buyLimitNumList,$ageLimitMaxList,$ageLimitMaxList);die();
//        return $ticketInfo;
    }

    public function submitOrder($javaParamArr){
        //判断是否为主票
        pft_log('platformOrder/PackOrder', '$javaParamArr:'.json_encode($javaParamArr,JSON_UNESCAPED_UNICODE));
        $orderBaseInfo = $javaParamArr['orderBaseInfo'];
        $orderTicketInfos = reset($javaParamArr['orderTicketInfos']);
        if($orderBaseInfo['linkOrderType'] == 'package'){
            //查询所属子票以及票信息

            //通过主票id查询出所有子票
            $packModel     = new PackageTicketsModel();
            $sonTicketInfo = $packModel->getSonPackageTicketListByParentId($orderTicketInfos['ticketId']);
            if (!$sonTicketInfo) {
                return $this->returnData(204, '未查询到子票信息');
            }

            $sonTicketIdArr = array_column($sonTicketInfo, 'tid');
            $ticketApi      = new Ticket();
            $sonTicketRes   = $ticketApi->queryTicketInfoByIds($sonTicketIdArr);
//            pft_log('platformOrder/PackOrder', 'debug:'.json_encode($sonTicketRes,JSON_UNESCAPED_UNICODE));
            //查询子票列表，不能为空
            if($sonTicketRes['code'] != 200 ||  empty($sonTicketRes['data'])){
                return $this->returnData(204, '未查询到子票信息',$sonTicketIdArr);
            }

            //需要校验下限时售卖的属性
            $limitInfo     = [];
            $limitCheckRes = (new \Business\PftShow\Order())->ticketOrderLimitCheck($sonTicketIdArr);
            if ($limitCheckRes['code'] == 200 && $limitCheckRes['data']) {
                $limitInfo = $limitCheckRes['data'];
            }

            $javaApi = new \Business\JavaApi\Ticket\Price();
            foreach ($sonTicketRes['data'] as $sonTicket){
                //判断子票是否存在且下架
                if(in_array($sonTicket['uuJqTicketDTO']['status'],[2,6]) ){//状态: 1上架, 2下架, 6删除"
                    return $this->returnData(204, $sonTicket['uuJqTicketDTO']['title']."_子票状态[{$sonTicket['uuJqTicketDTO']['status']}]_不满足下单条件");
                }
                //对应产品是否存在且下架
                if($sonTicket['uuProductDTO']['applyLimit'] != 1){ //0未审核1已审核2下架3被拒绝6删除
                    return $this->returnData(204, $sonTicket['uuProductDTO']['pName']."_产品状态[{$sonTicket['uuProductDTO']['applyLimit']}]_不满足下单条件");
                }
                //对应景区是否存在且下架
                if($sonTicket['uuLandDTO']['status'] != 1){ //状态: 1上架, 2下架
                    return $this->returnData(204, $sonTicket['uuLandDTO']['title'].'_景区已下架不满足下单条件');
                }
                //子票对应的游玩日期必须存在可售价格信息
                $result  = $javaApi->queryDatePrice($sonTicket['confs']['tid'],$orderTicketInfos['playTime']);
                if(empty($result)) {
                    return $this->returnData(204, $sonTicket['uuLandDTO']['title'].'_选择的游玩日期不存在可售价格信息');
                }
                //演出子票下单预定校验
                if ($sonTicket['uuLandDTO']['pType'] == "H" && isset($limitInfo[$sonTicket['uuJqTicketDTO']['id']]) && !$limitInfo[$sonTicket['uuJqTicketDTO']['id']]['can_order'] && $limitInfo[$sonTicket['uuJqTicketDTO']['id']]['text']) {
                    return $this->returnData(204, '子票：' . $sonTicket['uuJqTicketDTO']['title'] . "_{$limitInfo[$sonTicket['uuJqTicketDTO']['id']]['text']}");
                }
            }

            pft_log('platformOrder/PackOrder', 'tips:验证通过');

            //计算有效期
            //主票 - 直接获取 票时间

            $ticketInfoRes   = $ticketApi->queryTicketInfoById($orderTicketInfos['ticketId']);
            if ($ticketInfoRes['code'] != 200) {
                return $this->returnData(204, '未查询到主票信息',$ticketInfoRes);
            }
            $ticketInfo = $ticketInfoRes['data'];
            $playTime = $orderTicketInfos['playTime'];
            $delayType = $ticketInfo['uuJqTicketDTO']['delaytype'];
            $delayDays = $ticketInfo['uuJqTicketDTO']['delaydays'];
            $useEarlyDays = $ticketInfo['uuJqTicketDTO']['useEarlyDays'];

            $orderStart =  $ticketInfo['uuJqTicketDTO']['orderStart'] ?? '';
            $orderEnd =  $ticketInfo['uuJqTicketDTO']['orderEnd'] ?? '';
//            pft_log('platformOrder/PackOrder', 'debug:'.json_encode($ticketInfo,JSON_UNESCAPED_UNICODE));
            //游玩当天有效
            if ($delayType == 0 && $delayDays == 0) {
                $javaParamArr['orderTicketInfos']['beginTime'] =  date("Y-m-d H:i:s", strtotime($playTime));
                $javaParamArr['orderTicketInfos']['endTime'] = date("Y-m-d 23:59:59", strtotime($playTime));
                return $this->returnData(200,'游玩当天有效',$javaParamArr);
            }
            elseif($delayType == 0 && ($delayDays > 0 || $useEarlyDays>0)){
                //游玩日期 提前x天有效
                if($useEarlyDays){
                    $beginTime = date("Y-m-d H:i:s", strtotime($playTime) - ($useEarlyDays*24*3600));
                }
                else{
                    $beginTime = date("Y-m-d H:i:s", strtotime($playTime));
                }
                //游玩日期 延后x天有效
                if($delayDays){
                    $endTime = date("Y-m-d H:i:s", strtotime($playTime) + ($delayDays*24*3600));
                }
                else{
                    $endTime = date("Y-m-d 23:59:59", strtotime($playTime));
                }
                $javaParamArr['orderTicketInfos']['beginTime'] = $beginTime;
                $javaParamArr['orderTicketInfos']['endTime'] = $endTime;
                return $this->returnData(200,"游玩日期（含）前{$useEarlyDays}天有效，后{$delayDays}天有效",$javaParamArr);
            }
            elseif($delayType == 1){
                $beginTime = date("Y-m-d H:i:s", time());
                $endTime = date("Y-m-d H:i:s", time()+($delayDays*24*3600));
                $javaParamArr['orderTicketInfos']['beginTime'] = $beginTime;
                $javaParamArr['orderTicketInfos']['endTime'] = $endTime;
                return $this->returnData(200,"下单日期(含){$delayDays}天内有效",$javaParamArr);
            }
            elseif($delayType == 2 && !empty($orderStart)  && !empty($orderEnd)){
                $javaParamArr['orderTicketInfos']['beginTime'] = $orderStart;
                $javaParamArr['orderTicketInfos']['endTime'] = $orderEnd;
                return $this->returnData(200,"固定日期有效:{$orderStart}-{$orderEnd}",$javaParamArr);
            }
            else{
                return $this->returnData(204,'票类有效期错误',$javaParamArr);
            }
        }
        else{
            return $this->returnData(200);
        }
    }


    public function packageOrderFail($mainOrderNum,$visibleArr,$orderInfo,$errInfo,$sellerID,$sellerMobile){
        $sms = new \Library\MessageNotify\Platform\FzZwxSms($orderInfo['ordertel']);
        //有传入手牌号 发送短信至供应商手机号中
        if ($visibleArr) {
            $visibleNo = implode(',', $visibleArr);
            if(empty($sellerMobile) || !ismobile($sellerMobile)){
                pft_log('order/package', "{$mainOrderNum}|子票下单失败-供应商手机号为空");
                return $this->returnData(204);
            }
            $sendData = [strval($mainOrderNum), ";错误信息:{$errInfo},", $visibleNo];
            $messageServiceApi = Container::pull(MessageService::class);
            [$approval, $res2] = $messageServiceApi->dispatchMessageSend(MessageService::V1, 'Presuming_package_order_cancel',
                $sellerMobile, $sendData, 0, $mainOrderNum, '子票下单失败', '', 0);
            if ($approval) {
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['子票下单失败', __METHOD__, [$sellerMobile, $sendData, $mainOrderNum], $res2], JSON_UNESCAPED_UNICODE));
                }
            } else {
                $res2 = $sms->presumingPackageOrderFail($mainOrderNum, $visibleNo, ";错误信息:{$errInfo},",
                    $sellerMobile);
                if (!EnvUtil::isRelease()) {
                    pft_log('message_service', json_encode(['子票下单失败.old', __METHOD__, [$sellerMobile, [$mainOrderNum, $visibleNo, ";错误信息:{$errInfo},"], $mainOrderNum], $res2], JSON_UNESCAPED_UNICODE));
                }
                pft_log('order/package',
                    "{$mainOrderNum}|发送短信通知,;供应商结果:" . json_encode([$mainOrderNum,$sellerMobile,$visibleNo,$res2], JSON_UNESCAPED_UNICODE));
            }
            return $this->returnData(200);
        } else {

            //获取主票票属性
            $ticketApi      = new Ticket();
            $ticketData = $ticketApi->queryTicketInfoById($orderInfo['tid']);
            pft_log('order/package', "{$mainOrderNum}|子票下单失败-主票回滚参数:" . json_encode([$ticketData], JSON_UNESCAPED_UNICODE));
            if($ticketData['code']!=200 || empty($ticketData['data'])){
                pft_log('order/package', "{$mainOrderNum}|子票下单失败-票属性获取失败");
                return $this->returnData(204);
            }
            $ticketData = $ticketData['data'];
            //配置发送下单失败短信-游客
            $orderMsgFalse = $ticketData['confs']['order_msg_guest_false'] ?? true;
            $ordertel = OrderNotify::getInternationalCall($orderInfo['mobile_area'], $orderInfo['ordertel']);
            if($orderMsgFalse && $ordertel && ismobile($ordertel)){
                $sendData = [strval($mainOrderNum), ''];
                $messageServiceApi = Container::pull(MessageService::class);
                [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V1, 'package_order_cancel', $ordertel,
                    $sendData, $sellerID, $mainOrderNum, '套票下单失败短信', '', '', 1, false, true);
                if ($approval) {
                    if (!EnvUtil::isRelease()) {
                        pft_log('message_service', json_encode(['套票下单失败短信.游客', __METHOD__, [$ordertel, $sendData, $sellerID, $mainOrderNum], $res], JSON_UNESCAPED_UNICODE));
                    }
                } else {
                    $res  = $sms->packageOrderFail($mainOrderNum,$sellerID); //您的套票订单{1}由于子票订单提交失败{2}已被系统自动取消。
                    if (!EnvUtil::isRelease()) {
                        pft_log('message_service', json_encode(['套票下单失败短信.游客.old', __METHOD__, [$ordertel, $sendData, $sellerID, $mainOrderNum], $res], JSON_UNESCAPED_UNICODE));
                    }
                }
                pft_log('order/package', "{$mainOrderNum}|子票下单失败-主票回滚发送短信通知,游客结果:" . json_encode([$res,$orderInfo['ordertel']], JSON_UNESCAPED_UNICODE));
            }

            //配置发送下单失败短信-供应商
            $orderMsgGuestFalse = $ticketData['confs']['order_msg_false'] ?? true;
            if($orderMsgGuestFalse){
                if(empty($sellerMobile) || !ismobile($sellerMobile)){
                    pft_log('order/package', "{$mainOrderNum}|子票下单失败-供应商手机号为空");
                    return $this->returnData(204);
                }
                $sendData = [strval($mainOrderNum), ";错误信息:{$errInfo},"];
                $messageServiceApi = Container::pull(MessageService::class);
                [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V1, 'package_order_cancel', $sellerMobile,
                    $sendData, $sellerID, $mainOrderNum, '套票下单失败短信', '', '', 1, false, true);
                if ($approval) {
                    if (!EnvUtil::isRelease()) {
                        pft_log('message_service', json_encode(['套票下单失败短信.供应商', __METHOD__, [$sellerMobile, $sendData, $sellerID, $mainOrderNum], $res], JSON_UNESCAPED_UNICODE));
                    }
                } else {
                    $res = $sms->packageOrderFail($mainOrderNum, $sellerID, ";错误信息:{$errInfo},", $sellerMobile);
                    if (!EnvUtil::isRelease()) {
                        pft_log('message_service', json_encode(['套票下单失败短信.供应商.old', __METHOD__, [$sellerMobile, $sendData, $sellerID, $mainOrderNum], $res], JSON_UNESCAPED_UNICODE));
                    }
                }
                pft_log('order/package', "{$mainOrderNum}|子票下单失败-主票回滚发送短信通知,供应商结果:" . json_encode([$res,$errInfo,$sellerMobile], JSON_UNESCAPED_UNICODE));
            }

            //配置发送下单失败微信-供应商
            $orderWechatFalse = $ticketData['confs']['order_wechat_false'] ?? false;
            if($orderWechatFalse){
                //获取openID
                $modelWechat = new WxMember('slave');
                $wxInfoList  = $modelWechat->getWxInfo($sellerID, PFT_WECHAT_APPID, true);
                if (!$wxInfoList) {
                    pft_log('order/package', "{$mainOrderNum}|子票下单失败-主票回滚发送微信通知,供应商错误:获取openId失败");
                    return $this->returnData(204);
                }
                $refundMoney = $orderInfo['totalmoney']/100;
                //构建模板参数
                $data  = [
                    'first'  => ['value' => "您的套票订单[{$mainOrderNum}]由于子票提交失败已取消", 'color' => '#0A0A0A'],
                    'reason' => ['value' => "错误原因:{$errInfo}", 'color' => '#173177'],
                    'refund' => ['value' => "{$refundMoney} 元", 'color' => '#173177'],
                    'remark' => ['value' => "请检查相关子票设置或者联系相应供应商", 'color' => ''],
                ];
                $url   = $color = '';
                $tplId = 'REFUND';
                foreach ($wxInfoList as $item) {
                    $res = TemplateMessage::openSendTemplateMessage(
                        $data,
                        $item['fromusername'],
                        $tplId,
                        $url,
                        $color,
                        PFT_WECHAT_APPID
                    );
                    pft_log('order/package', "{$mainOrderNum}|子票下单失败-主票回滚发送微信通知,供应商结果:"
                        . json_encode([$res,$data, $item['fromusername'], $tplId, $url, $color, PFT_WECHAT_APPID], JSON_UNESCAPED_UNICODE));
                }
            }

            return $this->returnData(200);
        }
    }
}
