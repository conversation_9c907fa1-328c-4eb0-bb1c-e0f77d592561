<?php

namespace Business\ResourceCenter;

use Business\JavaApi\BaseApi;
use Business\JavaApi\CallBase;
use \Exception;
use Business\Base;
use Library\Traits\CacheTrait;
use Model\Tools\ResourceForward;
use Business\ResourceCenter\Bond as BondBiz;

class Forward extends Base
{
    use CacheTrait;

    private $automaticInterFaceForwardingKey = 'rs:if:forward:%s';

    private $resourceForwardModel;

    private function getResourForwardModel()
    {
        if (!$this->resourceForwardModel) {
            $this->resourceForwardModel = new ResourceForward();
        }

        return $this->resourceForwardModel;
    }

    /**
     * 转发接口
     *
     * @param  int  $memberId
     * @param  array  $params
     * @param  int  $sid
     * @param  int  $dtype  类型
     *
     * @return array
     */
    public function forward(int $memberId, int $sid, array $params, int $dtype): array
    {
        $data = [];

        try {

            $tag = $params['tag'];
            if (empty($tag)) {
                throw new Exception('请求标识不存在', 400);
            }

            $forwardConfig = $this->getForwardConfig($tag);
            if (empty($forwardConfig)) {
                throw new Exception('不存在对应的自动请求配置', 400);
            }

            //验证分销商权限 条件是: 开通了资源中心、分销商账号类型、需要验证接口
            if ($forwardConfig['allow_access'] == 1) {
                $bondBiz   = new BondBiz();
                $bondState = $bondBiz->operateVerify($sid, $dtype);
                if (isset($bondState['code'])  && $bondState['code'] !== 200) {
                    throw new Exception($bondState['msg'], 400);
                }
            }

            $params['memberId'] = $sid;
            $params['opId']     = $memberId;

            if ($forwardConfig['request_version'] == 1) {
                // 旧版接口请求方式
                if ($forwardConfig['request_method'] == 1) {
                    // GET请求
                    $postRes = BaseApi::customCurlPostForForward($forwardConfig['url'], [], 'GET', $params);
                } else {
                    // POST请求
                    $postRes = BaseApi::customCurlPostForForward($forwardConfig['url'], $params);
                }
            } else {
                $callBase = new CallBase();
                // 新版接口请求方式
                if ($forwardConfig['request_method'] == 1) {
                    // GET请求
                    $postRes = $callBase->callForForward($forwardConfig['url'], [], 'GET', $params);
                } else {
                    // POST请求
                    $postRes = $callBase->callForForward($forwardConfig['url'], $params);
                }
            }

            $code = $postRes['code'];
            $msg  = $postRes['msg'];
            $data = $postRes['data'];

        } catch (Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 清除标签缓存
     * 由于新增字段导致之前的数据可能存在缓存
     * 这个接口单独用于清除缓存，其他地方勿调
     * <AUTHOR>
     * @date 2020/11/25
     *
     * @param  string  $tag  标志
     *product_ResourceCenter/addPurchaseRule
     * @return bool
     */
    public function clearForwardConfigCache(string $tag): bool
    {
        $forwardConfig = $this->getForwardConfig($tag);
        if (empty($forwardConfig)) {
            return false;
        }
        $redisInstance = $this->getRedisInstance();
        $key           = $this->getCacheKey($this->automaticInterFaceForwardingKey, [$tag]);

        $redisInstance->rm($key);

        return true;
    }

    /**
     * 获取接口转发配置
     *
     * @param  string  $tag
     *
     * @return array
     */
    private function getForwardConfig(string $tag): array
    {
        $redisInstance = $this->getRedisInstance();
        $key           = $this->getCacheKey($this->automaticInterFaceForwardingKey, [$tag]);

        $data = $redisInstance->get($key);

        if ($data === false) {
            $resourceForwardModel = $this->getResourForwardModel();
            $data                 = $resourceForwardModel->getAutomaticInterfaceForwardConfigByTag($tag);

            if (empty($data)) {
                return [];
            }

            $redisInstance->setex($key, 86400, json_encode($data));
        }

        return is_array($data) ? $data : json_decode($data, true);
    }

}