<?php
/**
 * 分销商保证相关权限控制接口
 *
 * <AUTHOR>
 * @date 2020-11-24
 *
 */

namespace Business\ResourceCenter;

use Business\Base;
use Business\JavaApi\Resource\ResourceBondConfig;
use Business\Member\Member as MemberBiz;
use Business\JavaApi\Member\ResourceDistributorBond as BondBiz;

class Bond extends Base
{
    private static $checkState = [1, 2]; //状态：1可用2不可用
    private static $checkDtype = 1; //分销商类型
    private static $logPath    = 'jsonrpc/resource_center'; //日志
    private static $reidsKey   = 'resource_center:bond:%u:state'; //日志
    private static $cacheTime  = 3600 * 24 * 31; //缓存时间31天
    private static $rechargeType = [0, 1]; //交易类型 0:微信,1:支付宝

    /**
     * 设置状态
     * 提供给jsonrpc调用的
     * 其他地方不要调用
     *
     * 请求地址示例：ResourceCenter/Bond/setState
     *
     * <AUTHOR>
     * @date 2020/11/24
     *
     * @param  int  $fid  分销商id
     * @param  int  $state  状态：1可用2不可用
     *
     * @return array
     */
    public function setState(int $fid, int $state)
    {
        $code = 200;
        $msg  = '';
        $data = true;
        pft_log(self::$logPath, "资源中心请求, 分销商: $fid, 状态: $state");
        try {
            if (!$fid || !$state) {
                throw new \Exception('参数错误', 203);
            }
            if (!in_array($state, self::$checkState)) {
                throw new \Exception('状态参数错误', 203);
            }
            //验证商户信息
            $memberBiz = new MemberBiz();
            $memberMap = $memberBiz->getMemberInfoByMulti([$fid], 'id', true);
            if (!isset($memberMap[$fid]) || $memberMap[$fid]['dtype'] != self::$checkDtype) {
                throw new \Exception('分销商id错误', 400);
            }

            //redis缓存该字段
            $key = sprintf(self::$reidsKey, $fid);
            (self::getCache())->set($key, $state, '', self::$cacheTime);

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            $data = false;
            pft_log(self::$logPath, "资源中心请求, 分销商: $fid, 状态: $state, 错误码: $code, 信息: $msg");
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取分销商保证金权限状态
     * <AUTHOR>
     * @date 2020/11/24
     *
     * @param  int  $fid  分销商id
     *
     * @return bool
     */
    public function getBondState(int $fid)
    {
        if (!$fid) {
            return true;
        }

        //redis缓存该字段
        $key   = sprintf(self::$reidsKey, $fid);
        $state = (self::getCache())->get($key);
        if (!$state) {
            $state = (new BondBiz())->getBondState($fid);
            if (is_array($state)) {
                return true;
            }
            (self::getCache())->set($key, $state, '', self::$cacheTime);
        }

        return ($state == 1) ? true : false;
    }

    /**
     * 更新tag缓存
     * 给jsonrpc调用的，其他地方不要调
     * <AUTHOR>
     * @date 2020/11/25
     *
     * @param  string  $tag  标签
     *
     * @return bool|string
     */
    public function updateTagCache(string $tag)
    {
        if (!$tag) {
            return "参数不能为空";
        }

        return (new Forward())->clearForwardConfigCache($tag);
    }

    /**
     * 资源中心分销商验证是否可以继续
     * <AUTHOR>
     * @date 2020/11/25
     *
     * @param  int  $sid  用户id
     * @param  int  $dtype  用户类型 1是分销商
     *
     * @return array
     */
    public function operateVerify(int $sid, int $dtype = -1)
    {
        if (!$sid) {
            return $this->returnData(203, "用户参数异常");
        }
        if ($dtype == -1) {
            $memberBiz  = new \Business\Member\Member();
            $memberInfo = $memberBiz->getMemberInfoByMulti([$sid], 'id', true);
            if (empty($memberInfo) || !isset($memberInfo[$sid]['dtype'])) {
                return $this->returnData(203, "用户角色信息获取异常");
            }
            $dtype = $memberInfo[$sid]['dtype'];
        }
        if ($dtype < 0) {
            return $this->returnData(203, "用户类型异常");
        }
        if ($dtype != 1) {
            return $this->returnData(200, "");
        }

        $bondState = $this->getBondState($sid);
        if (!$bondState) {
            return $this->returnData(203, "当前账号保证金不足，请及时缴纳！");
        }

        return $this->returnData(200, "");
    }

    /**
     * 获取缓存对象
     * <AUTHOR>
     * @date 2020/11/25
     *
     * @return object
     */
    private static function getCache()
    {
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');

        return $cacheRedis;
    }
    /**
     * 资源中心保证金订单充值生成订单获取微信支付信息
     * <AUTHOR>
     * @date 2021/03/10
     *
     * @return object
     */
    public function resourceBondRecharge(int $memberId, string $openid, string $returnUrl)
    {
        $code = 200;
        $msg  = '';
        $data = true;
        pft_log(self::$logPath, "微平台资源中心保证金充值请求, 分销商: $memberId");
        try {
            if (!$memberId) {
                throw new \Exception('参数错误', 203);
            }

            if (!$openid) {
                throw new \Exception('openid参数错误', 203);
            }

            //资源中心保证金充值订单生成
            $orderInfo = (new ResourceBondConfig())->resourceBondRechargeOrderNumGenerate($memberId);
            if ($orderInfo['code'] != 200) {
                pft_log(self::$logPath, "微平台资源中心保证金充值请求, 分销商: $memberId".'资源中心保证金充值订单生成错误,msg:' . $orderInfo['msg']);
                throw new \Exception('资源中心保证金充值订单生成错误', $orderInfo['code']);
            }

            $notifyIp = load_config('java_api','api');
            $apiurl   = $notifyIp['v1']['api_base_url'];
            $apiport  = $notifyIp['v1']['api_port'];
            $notifyIp = $apiurl. ':' .$apiport;
            pft_log(self::$logPath, "微平台资源中心保证金充值请求, 分销商: $memberId".'资源中心保证金充值订单生成号'.$orderInfo['orderNum']);
            $orderInfo = $orderInfo['data'];
            $payUrl = PAY_DOMAIN . 'r/pay_MobilePay/jsApiPay';
            $payData['pay_type']   = 2;
            $payData['order_id']   = (string)$orderInfo['orderNum'];//业务方订单号
            $payData['money']      = (int)$orderInfo['rechargeAmount'];
            $payData['notify_url'] = $notifyIp .'/web/v1/resource/resourceBondConfigService/resourceBondRecharge';
            $payData['open_id']    = $openid;
            $payData['subject']    = '微平台资源中心保证金充值';
            $payData['success_url']  = $returnUrl;
            pft_log(self::$logPath, "微平台资源中心保证金充值请求, 分销商: $memberId".'请求支付'.json_encode($payData));
            $result = curl_post($payUrl, $payData, 80, 40);
            $result = @json_decode($result, true);
            $msg  = $result['msg'];
            $data = $result['data'];
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            $data = false;
            pft_log('resource/resource_bond_recharge',
                "资源中心保证金充值订单生成错误请求, 分销商: $memberId, 错误码: $code, 信息: $msg");
        }

        return $this->returnData($code, $msg, $data);
    }

}