<?php

namespace Business\ResourceCenter;

use Business\Base;
use Business\JavaApi\Resource\UserBusinessVerify;
use Library\Constants\OrderConst;

class Order extends Base
{

    /**
     * 资源中心校验用户业务权限
     * <AUTHOR>
     * @date 2021/1/14
     *
     * @param  int  $sid  上级id
     * @param  int  $fid  下级id
     * @param  int  $pid  产品id
     * @param  string  $aids 对应分销链
     *
     * @return array
     * @throws
     */
    public function orderAppModuleEffectCheck(int $sid, int $fid, int $pid, string $aids = ''): array
    {
        if (!$sid || !$fid || !$pid) {
            return $this->returnData(400, '资源中心验证参数错误');
        }

        $authorityBatchCheck = (new UserBusinessVerify())->authorityBatchCheck($sid, $fid, $pid, $aids);
        if (isset($authorityBatchCheck['code'])  && $authorityBatchCheck['code'] !== 200) {
            return $this->returnData(400, $authorityBatchCheck['msg'], ['err_code' => OrderConst::err6004]);
        }

        return $this->returnData(200, '');
    }

}