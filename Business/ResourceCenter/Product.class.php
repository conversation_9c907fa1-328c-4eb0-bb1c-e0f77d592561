<?php

namespace Business\ResourceCenter;

use Business\Base;
use Business\JavaApi\ResourceCenter\ResourceRecommendProduct;
use Business\JavaApi\ResourceCenter\ResourceRecommendUserConfig;
use Business\JavaApi\Resource\ResourceOtaConfig;
use Library\Traits\CacheTrait;
use phpDocumentor\Reflection\Types\Boolean;

class Product extends Base
{
    use CacheTrait;

    private $productHistoryListCacheKey = 'rs:pro:his:list:mid:%u:t:%u';

    /**
     * 删除搜索缓存
     * <AUTHOR>
     *
     * @param  int  $memberId
     * @param  int $type 1，供应商，2产品
     */
    public function delHistoryRecord(int $memberId, int $type)
    {
        if (empty($memberId)) {
            return;
        }

        $redisInstance = $this->getRedisInstance();
        $key           = $this->getCacheKey($this->productHistoryListCacheKey, [$memberId, $type]);

        $redisInstance->del($key);
    }

    /**
     * 记录搜索缓存
     * <AUTHOR>
     *
     * @param  int  $memberId
     * @param  string  $productName
     * @param  int $type 1，供应商，2产品
     */
    public function productHistoryRecord(int $memberId, string $productName, int $type)
    {
        if (empty($memberId) || empty($productName)) {
            return;
        }

        $redisInstance = $this->getRedisInstance();
        $key           = $this->getCacheKey($this->productHistoryListCacheKey, [$memberId, $type]);

        if($redisInstance->exists($key)) {
            $data = $redisInstance->lRange($key, 0, 10);
            if (in_array($productName, $data)) {
                return ;
            }

            // 获取list长度
            $length = $redisInstance->lLen($key);
            if ($length >= 10) {
                $redisInstance->lPop($key);
            }

            $redisInstance->rPushList($key, [$productName]);
        } else {
            $redisInstance->rPushList($key, [$productName]);
            $redisInstance->expire($key, 3600*24*30);
        }
    }

    /**
     * 获取搜索缓存列表
     * <AUTHOR>
     *
     * @param  int  $memberId
     * @param  int $type 1，供应商，2产品
     *
     * @return array
     */
    public function getProductHistoryList(int $memberId, int $type): array
    {
        if (empty($memberId)) {
            return [];
        }

        $redisInstance = $this->getRedisInstance();
        $key           = $this->getCacheKey($this->productHistoryListCacheKey, [$memberId, $type]);

        $data = $redisInstance->lRange($key, 0, 10);

        return array_reverse($data);
    }

    /**
     * 获取用户旅游小店专题列表
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @param  int $memberId 用户id
     * @param  int $evoluteState 分销状态:-1:全部, 0=停止分销,1=分销中
     * @param  int $channel 渠道
     * @param  int $pageNum
     * @param  int $pageSize
     *
     * @return array
     */
    public function queryResourceRecommendUserConfigListByMemberId(int $memberId, int $channel, int $evoluteState = -1, int $pageNum = 1, int $pageSize = 10): array
    {
        if (!$memberId) {
            return ['code' => 203, 'msg' => '参数错误', 'data' => []];
        }
        if (!in_array($evoluteState, [-1, 0, 1])) {
            return $this->returnData(203, '分销状态参数错误');
        }

        $result = (new ResourceRecommendUserConfig())->queryResourceRecommendUserConfigListByMemberId($memberId,$channel,
            $evoluteState, $pageNum, $pageSize);

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $result['data']];
    }

    /**
     * 获取资源中心推荐景区列表 可返回门票列表
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @param  int $memberId 用户id
     * @param  int $recommendId 推荐id
     * @param  int $ticketListState 门票列表返回状态: 0:默认不返回,1:返回门票3条
     * @param  string $supplierName 供应商名称
     * @param  string $landName 景区名称
     * @param  int $provinceCode 省份编码
     * @param  int $cityCode 城市编码
     * @param  string $landType 景区类型
     * @param  string $topic 景区主题
     * @param  int $channel  渠道
     * @param  int $pageNum
     * @param  int $pageSize
     * @param  int $salesStatus 销售状态: 1=出售中,0=停售
     *
     * @return array
     */
    public function queryResourceRecommendLandList(int $memberId, int $recommendId , int $channel, int $salesStatus, int $ticketListState = 0, string $supplierName = '', string $landName = '', int $provinceCode = 0, int $cityCode = 0, string $landType = '', string $topic = '', int $pageNum = 1, int $pageSize = 10): array
    {
        if (!$memberId || !$recommendId) {
            return ['code' => 203, 'msg' => '参数错误', 'data' => []];
        }

        $result = (new ResourceRecommendProduct())->queryResourceRecommendLandList($memberId, $recommendId,$channel, $salesStatus,
            $ticketListState, $supplierName, $landName, $provinceCode, $cityCode, $landType, $topic, $pageNum,
            $pageSize);

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $result['data']];
    }

    /**
     * 获取资源中心推荐专题下景区内门票列表
     * 详细参数见文档,如下:
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @param  int $memberId 用户id
     * @param  int $recommendId 推荐id
     * @param  int $sid 供应商id
     * @param  int $lid 景区id
     * @param  int $channel 渠道
     * @param  string $filterTids 过滤门票ids (1,2,3)
     * @param  int $pageNum
     * @param  int $pageSize
     *
     * @return array
     */
    public function queryResourceRecommendTicketList(int $memberId, int $recommendId, int $sid, int $lid, int $channel, int $salesStatus, string $filterTids = '', int $pageNum = 1, int $pageSize = 10): array
    {
        if (!$memberId || !$recommendId || !$lid || !in_array($salesStatus, [1, 0])) {
            return ['code' => 203, 'msg' => '参数错误', 'data' => []];
        }

        $result = (new ResourceRecommendProduct())->queryResourceRecommendTicketList($memberId, $recommendId, $channel,
            $sid, $lid, $salesStatus,
            $filterTids, $pageNum, $pageSize);

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $result['data']];
    }


    /**
     * 修改用户专题分销状态
     * 详细参数见文档,如下:
     * <AUTHOR>
     * @date   2020-12-15
     *
     * @param  int $memberId 用户id
     * @param  int $evoluteState 分销状态: 0=停止分销,1=分销中
     * @param  int $recommendId 推荐位Id
     *
     * @return array
     */
    public function updateResourceRecommendUserConfigEvoluteSatet(int $memberId,int $evoluteState, int $recommendId, int $opid): array
    {
        if (!$memberId || !$recommendId) {
            return $this->returnData(203, '参数错误');
        }
        if (!in_array($evoluteState,[0,1])) {
            return $this->returnData(203, '分销状态参数错误');
        }

        $result = (new ResourceRecommendUserConfig())->updateResourceRecommendUserConfigEvoluteSatet($memberId, $evoluteState, $recommendId,$opid);

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $result['data']];

    }

    /**
     * 产品预订增加禁售标签
     * <AUTHOR>
     * @date 2021/11/10
     *
     * @param  array  $params 预订列表数据
     *
     * @return array
     */
    public function handleBookListParams(array $params)
    {
        if (empty($params)) {
            return $params;
        }

        $lidArr = array_column($params, 'lid');
        if (empty($lidArr)) {
            return $params;
        }

        $otaConfigMap = [];

        $javaApi = new ResourceOtaConfig();
        $result  = $javaApi->queryResourceOtaUserConfigInfoByLid($lidArr);
        if ($result['code'] != 200 || empty($result['data'])) {
            return $params;
        } else {
            $otaConfigMap = array_column($result['data'], null, 'lid');
        }

        foreach ($params as &$item) {
            $lid                      = $item['lid'];
            $disableOTATag            = $otaConfigMap[$lid]['otaStatus'] ?? 0; //不存在，是否开启禁售 0否 1是
            $disableOTADesc           = $otaConfigMap[$lid]['otaTag'] ?? ''; //禁售渠道内容，逗号隔开 如：“去哪儿，美团直连”
            $item['disable_ota_tag']  = $disableOTATag;
            $item['disable_ota_desc'] = $disableOTADesc;
        }

        return $params;
    }
}