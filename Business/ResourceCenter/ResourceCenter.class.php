<?php

/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON>
 * Date: 2019/1/8
 * Time: 16:38
 */

namespace Business\ResourceCenter;

use Business\AppCenter\Module;
use Business\Base;
use Business\JavaApi\Product\ResourceCenterApi;
use Business\Member\MemberRelation;
use Model\AppCenter\ModuleAuthor;
use Model\Member\MemberRelationship;
use Model\Product\Evolute;

class ResourceCenter extends Base
{
    private $_memberShipModel;

    private $_moduleId = 49;

    /***
     * 添加资源中心合作关系
     * @author: <PERSON><PERSON>
     * @date: 2019/1/8
     * @param $sid
     * @param $fid
     * @return bool
     */
    public function saveResourceCenterShip($sid, $fid) {
        $shipModel = $this->_getMemberShipModel();
        //检查关系是否存在
        //$shipsRes = $shipModel->getOneRelation($fid, $sid, '0,4', true);
        $memberRelationBiz = new MemberRelation();
        $shipsRes = $memberRelationBiz->getMemberRelationListToJava([$sid],[$fid],[],[0],[0,4],'*');
        if ($shipsRes) {
            $ships = [];
           foreach ($shipsRes as $ship) {
               $ships[$ship['ship_type']] = $ship;
           }

           if (count($ships) > 1) {
               if (isset($ships[4])) {
                   if ($ships[4]['status'] == 1) {
                       return $shipModel->saveResourceCenterShip($sid, $fid, 4);
                   }

                   return true;
               } elseif (isset($ships[0])) {
                   if ($ships[0]['status'] == 1) {
                       return $shipModel->saveResourceCenterShip($sid, $fid, 0);
                   }

                   return false;
               }
               return false;
           } else {
               $ship = array_shift($ships);
               if ($ship['status'] == 0) {
                   return $ship['ship_type'] == 4 ? true : false;//原有的其他关系还正常 建立资源合作关系失败
               }
               //原关系已经断联 直接覆盖为资源中心合作关系
               return $shipModel->saveResourceCenterShip($sid, $fid, $ship['ship_type']);
           }

        } else {
            $api = new \Business\JavaApi\Member\MemberRelation();
            $res = $api->createMemberRelation($sid, $fid, 4, 0);
            return $res['code'] == 200;
        }
    }

    /***
     * 查询是否开通了资源中心
     * @author: Cai Yiqiang
     * @date: 2019/1/21
     * @param $sid
     * @return mixed
     */
    public function hasOpenedResourceCenter($sid, $dtype, $qx = '')
    {
        //应用中心是否开通
        $result = (new \Business\AppCenter\Module())->checkUserIsCanUseApp($sid, 'resource_center_app');

        //如果有开通，再判断员工权限
        if ($result && $dtype == 6) {
            $result = in_array('resource_center_app', explode(',', $qx)) ? true : false;
        }

        return $result;
    }

    /***
     * 删除资源合作关系
     * @author: Cai Yiqiang
     * @date: 2019/1/21
     * @param $sid
     * @param $fid
     * @return bool
     */
    public function delDisShip($sid, $fid) {
        $rcApi = new ResourceCenterApi();

        $query = [
            'fid' => $fid
        ];

        $queryRe = $rcApi->getDistributorList($sid,1,1, $query);

        $result = true;
        if ($queryRe['resultList']) {
            $info = array_shift($queryRe['resultList']);
            $id   = $info['id'];
            $result = $rcApi->deleteDistributor($id);
        }

        return $result;
    }

    /***
     * 删除分销商的时候清理该分销商基于该供应商有的资源中心关系
     * @author: Cai Yiqiang
     * @date: 2019/3/14
     * @param $sid
     * @param $fid
     * @return bool
     */
    public function delDisRCData($sid, $fid) {
        if (!($sid && $fid)) {
            return false;
        }

        $getEvoluteBiz = new \Business\Product\Get\Evolute();
        $res           = $getEvoluteBiz->getEvoluteBySidFidStatus([$sid], [$fid], 0);

        $return = true;
        if ($res) {
            $pids = array_column($res, 'pid');
            $return = ResourceCenterApi::batchSoldOutProducts([$fid], $pids, $sid);
        }

        return $return;
    }

    private function _getMemberShipModel() {
        if (!$this->_memberShipModel) {
            $this->_memberShipModel = new MemberRelationship();
        }
        return $this->_memberShipModel;
    }
    /**
     *
     * 资源中心用户信息导出
     *
     * <AUTHOR>
     * @date   2021-07-09
     *
     * @param  string  $account  账号
     * @param  int  $userId 用户id
     * @param  int  $settledState 供应商入驻状态 （0:未入驻，1:已入驻）
     * @param  int  $purchaseSettledState 采购商入驻状态 （0:未入驻，1:已入驻）
     * @param  int  $settledStartTime 供应商入驻开始时间（时间戳秒）
     * @param  int  $settledEndTime  供应商入驻结束时间（时间戳秒）
     * @param  int  $purchaseSettledStartTime 采购商入驻开始时间（时间戳秒）
     * @param  int  $purchaseSettledEndTime  采购商入驻结束时间（时间戳秒）
     * @param  int  $cityCode  城市编码
     * @param  int  $provinceCode  城市编码
     * @param  int  $userState  用户状态 0 全部，1有效，2未生效，3过期
     * @param  int  $userType  角色类型 -1:全部,0:供应商,1:分销商
     * @param  int  $accountType  账号类型: 全部:-1,测试账号:0,客户账号:1
     * @param  int  $examineType  审核类型：全部= -1,未认证 = 0,待审核 = 1,已认证 = 2,被拒绝 = 3
     * @param  int  $openType  开通类型：0=全部；1=套餐开通；2=签单开通；3=应用中心开通；4=推广页开通
     * @param  int  $openTime  开通  开始时间
     * @param  int  $endTime  开通 结束时间
     * @param  int  $pageSize
     * @param  int  $pageNum
     *
     * @return array
     */
    public function queryResourceUserAdminListExport(
        string $account = null,
        int $userId = null,
        int $settledState = null,
        int $purchaseSettledState = null,
        int $settledStartTime = null,
        int $settledEndTime = null,
        int $purchaseSettledEndTime = null,
        int $purchaseSettledStartTime = null,
        int $cityCode = null,
        int $provinceCode = null,
        int $userState = 0,
        int $userType = -1,
        int $accountType = -1,
        int $examineType = -1,
        int $openType = 0,
        int $openTime = null,
        int $endTime = null,
        int $pageNum = 1,
        int $pageSize = 10
    )
    {
        if (!is_null($settledState) && !in_array($settledState, [0, 1])) {
            return $this->returnDataV2('203', '供应商入驻状态参数错误');
        }

        if (!is_null($purchaseSettledState) && !in_array($purchaseSettledState, [0, 1])) {
            return $this->returnDataV2('203', '采购商入驻状态参数错误');
        }

        if (!is_null($userState) && !in_array($userState, [0, 1, 2, 3])) {
            return $this->returnDataV2('203', '用户状态参数错误');
        }

        if (!is_null($userType) && !in_array($userType, [-1, 0, 1])) {
            return $this->returnDataV2('203', '角色类型参数错误');
        }

        if (!is_null($accountType) && !in_array($accountType, [-1, 0, 1])) {
            return $this->returnDataV2('203', '角色类型参数错误');
        }
        if (!is_null($examineType) && !in_array($examineType, [-1, 0, 1, 2, 3])) {
            return $this->returnDataV2('203', '角色类型参数错误');
        }

        if (!in_array($openType, [0, 4, 1, 2, 3 ,5])) {
            return $this->returnDataV2('203', '开通类型参数错误');
        }
        if ($settledStartTime && (!date('ymd', $settledStartTime) || !is_numeric($settledStartTime))) {
            return $this->returnDataV2('203', '供应商入驻开始时间参数错误');
        }

        if ($settledEndTime && (!date('ymd', $settledEndTime) || !is_numeric($settledEndTime))) {
            return $this->returnDataV2('203', '供应商入驻结束时间参数错误');
        }

        if ($purchaseSettledStartTime && (!date('ymd',
                    $purchaseSettledStartTime) || !is_numeric($purchaseSettledStartTime))) {
            return $this->returnDataV2('203', '采购商入驻开始时间参数错误');
        }

        if ($purchaseSettledEndTime && (!date('ymd',
                    $purchaseSettledEndTime) || !is_numeric($purchaseSettledEndTime))) {
            return $this->returnDataV2('203', '采购商入驻结束时间参数错误');
        }

        if ($openTime && (!date('ymd', $openTime) || !is_numeric($openTime))) {
            return $this->returnDataV2('203', '开通开始时间参数错误');
        }

        if ($endTime && (!date('ymd', $endTime) || !is_numeric($endTime))) {
            return $this->returnDataV2('203', '开通结束时间参数错误');
        }

        $re = (new \Business\JavaApi\ResourceCenter\ResourceAdminUser())->queryResourceUserAdminListExport($account,
            $userId, $settledState, $purchaseSettledState, $settledStartTime, $settledEndTime, $purchaseSettledEndTime,
            $purchaseSettledStartTime, $cityCode, $provinceCode, $userState, $userType, $accountType, $examineType,
            $openType, $openTime, $endTime, $pageNum,$pageSize);

        return $this->returnDataV2($re['code'], $re['data'], $re['msg']);
    }
}