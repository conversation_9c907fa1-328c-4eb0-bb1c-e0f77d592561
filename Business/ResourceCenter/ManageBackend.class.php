<?php

namespace Business\ResourceCenter;

use Business\Base;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Product\ResourceCenterBackend;
use Model\SystemLog\PftLog;
use Model\AppCenter\ModuleList;
use Business\JavaApi\Member\MemberQuery;
use Library\Traits\CacheTrait;
use \Exception;
use Business\JavaApi\Product\ResourceCenterApi;

class ManageBackend extends Base
{

    use CacheTrait;

    private $orderStatisticsCacheKey = 'resource:backend:order:s:mid:%u';

    /**
     * 新增banner
     * <AUTHOR>
     *
     * @param  int  $opId
     * @param  string  $urlPath
     * @param  string  $title
     * @param  string  $imgPath
     * @param  int  $startTime
     * @param  int  $endTime
     * @param  int  $px
     * @param  int  $place
     *
     * @return array
     */
    public function saveBanner(int $opId, string $urlPath, string $title, string $imgPath,
        string $startTime, string $endTime, int $px, int $place): array
    {
        $data = [];
        try {
            if (empty($opId)) {
                throw new Exception('操作人不能为空', 400);
            }

            //if (empty($title)) {
            //    throw new Exception('图片标题不能为空', 400);
            //}

            //if (empty($urlPath)) {
            //    throw new Exception('网址路径不能为空', 400);
            //}

            if (empty($startTime) || !strtotime($startTime)) {
                throw new Exception('开始时间不能为空', 400);
            }

            if (empty($endTime) || !strtotime($endTime)) {
                throw new Exception('结束时间不能为空', 400);
            }

            if (empty($px)) {
                throw new Exception('排序时间不能为空', 400);
            }

            //if (empty($place)) {
            //    throw new Exception('位置编码不能为空', 400);
            //}

            if (empty($imgPath)) {
                throw new Exception('图片地址不能为空', 400);
            }

            $biz  = new TicketApi();
            $res  = $biz->resourceSaveBanner($opId, $urlPath, $title, $imgPath, strtotime($startTime),
                strtotime($endTime), $px, $place);
            $code = $res['code'];
            $msg  = $res['msg'];
            $data = $res['data'];

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 编辑banner
     * <AUTHOR>
     *
     * @param  int  $opId
     * @param  string  $urlPath
     * @param  string  $title
     * @param  string  $imgPath
     * @param  int  $startTime
     * @param  int  $endTime
     * @param  int  $px
     * @param  int  $place
     *
     * @return array
     */
    public function updateBanner(int $opId, int $id, string $urlPath, string $title, string $imgPath,
        string $startTime, string $endTime, int $px, int $place): array
    {
        $data = [];
        try {
            if (empty($opId)) {
                throw new Exception('操作人不能为空', 400);
            }

            if (empty($id)) {
                throw new Exception('banner id不能为空');
            }

            //if (empty($title)) {
            //    throw new Exception('图片标题不能为空', 400);
            //}

            //if (empty($urlPath)) {
            //    throw new Exception('网址路径不能为空', 400);
            //}

            if (empty($startTime) || !strtotime($startTime)) {
                throw new Exception('开始时间不能为空', 400);
            }

            if (empty($endTime) || !strtotime($endTime)) {
                throw new Exception('结束时间不能为空', 400);
            }

            if (empty($px)) {
                throw new Exception('排序时间不能为空', 400);
            }

            //if (empty($place)) {
            //    throw new Exception('位置编码不能为空', 400);
            //}

            if (empty($imgPath)) {
                throw new Exception('图片地址不能为空', 400);
            }

            $biz  = new TicketApi();
            $res  = $biz->resourceUpdateBanner($opId, $id, $urlPath, $title, $imgPath, strtotime($startTime),
                strtotime($endTime), $px, $place);
            $code = $res['code'];
            $msg  = $res['msg'];
            $data = $res['data'];

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 查询banner list
     * <AUTHOR>
     *
     * @param  int  $opid
     * @param  int  $enabledState
     * @param  int  $deleteType
     * @param  int  $place
     * @param  int  $pageNum
     * @param  int  $pageSize
     *
     * @return array
     */
    public function queryBannerList(int $opid, int $enabledState, int $deleteType, int $place, int $pageNum, int $pageSize): array
    {
        $biz = new TicketApi();
        $res = $biz->resourceQueryBannerList($opid, $enabledState, $deleteType,
            $place, $pageNum, $pageSize);

        foreach ($res['data']['list'] as &$item) {
            $item['addTime']    = date('Y-m-d H:i:s', $item['addTime']);
            $item['endTime']    = date('Y-m-d H:i:s', $item['endTime']);
            $item['startTime']  = date('Y-m-d H:i:s', $item['startTime']);
            $item['updateTime'] = date('Y-m-d H:i:s', $item['updateTime']);
        }

        return $res;
    }

    /**
     * 用户详情
     * <AUTHOR>
     *
     * @param  int  $opId
     * @param  int  $memberId
     *
     * @return array
     */
    public function memberDetail(int $opId, int $memberId): array
    {
        return $this->returnDataV2(500, [], '接口废弃');
    }

    /**
     * 用户列表查看汇总
     *
     * @param  int  $opId
     * @param  int  $memberId
     * @param  string  $dname
     * @param  string  $account
     * @param  string  $openStartTime
     * @param  string  $openEndTime
     *
     * @return array
     */
    public function memberStatistics(int $opId, int $memberId, string $account, string $dname,
        string $openStartTime, string $openEndTime): array
    {
        $code = 200;
        $data = [];
        $msg  = 'success';

        try {

            if (!empty($account) || !empty($dname)) {
                // 获取用户信息
                $memberQueryBiz = new MemberQuery();
                $memberInfoRes  = $memberQueryBiz->queryMemberInfoByAccountOrMobileOrName($account, '', $dname);
                if ($memberInfoRes['code'] != 200) {
                    throw new Exception($memberInfoRes['msg'], $memberInfoRes['code']);
                }

                if (empty($memberInfoRes['data'])) {
                    throw new Exception('用户信息不存在', 400);
                }

                $memberId = array_column($memberInfoRes['data'], 'id');
            }

            // 如果存在memberId 直接查询用户详情接口
            $javaApi = new ResourceCenterBackend();
            if (!empty($memberId)) {
                $openMemberNum = 1;

                $memberId    = is_array($memberId) ? $memberId : [$memberId];
                $userInfoRes = $javaApi->queryResourceUserInfo($opId, $memberId, $openStartTime, $openEndTime);
                if ($userInfoRes['code' != 200]) {
                    throw new Exception($userInfoRes['msg'], $userInfoRes['code']);
                }

                if (empty($userInfoRes['data'])) {
                    throw new Exception('汇总数据为空', 400);
                }

                $statisticsRes = [
                    'data' => [
                        'supplyProductNum'  => 0,
                        'purchaseTicketNum' => 0,
                    ],
                ];

                foreach ($userInfoRes['data'] as $item) {
                    $statisticsRes['data']['supplyProductNum']  += $item['supplyProductNum'];
                    $statisticsRes['data']['purchaseTicketNum'] += $item['purchaseTicketNum'];
                }

            } else {
                $statisticsRes = $javaApi->productStatistics($memberId, $openStartTime, $openEndTime,'');
                if ($statisticsRes['code'] != 200) {
                    throw new Exception($statisticsRes['msg'], $statisticsRes['code']);
                }

                $moduleUsedModel = new ModuleList();
                $moduleInfoRes   = $moduleUsedModel->getModuleListForResource($memberId, $openStartTime, $openEndTime,
                    1, 1);
                $openMemberNum   = $moduleInfoRes['total'];
            }

            $data = array_merge([
                'openMemberNum' => $openMemberNum,
            ], $statisticsRes['data']);

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 获取用户管理列表
     * <AUTHOR>
     *
     * @param  int  $opId
     * @param  int  $memberId
     * @param  string  $account
     * @param  string  $dname
     * @param  string  $openStartTime
     * @param  string  $openEndTime
     * @param  int  $pageSize
     * @param  int  $page
     *
     * @return array
     */
    public function memberList(int $opId, int $memberId, string $account, string $dname,
        string $openStartTime, string $openEndTime, int $page, int $pageSize): array
    {

        $code = 200;
        $data = [];
        $msg  = 'success';

        try {

            if (empty($opId)) {
                throw new Exception('操作人为空', 400);
            }

            $moduleUsedModel = new ModuleList();

            if (!empty($account) || !empty($dname)) {
                // 获取用户信息
                $memberQueryBiz = new MemberQuery();
                $memberInfoRes  = $memberQueryBiz->queryMemberInfoByAccountOrMobileOrName($account, '', $dname);
                if ($memberInfoRes['code'] != 200) {
                    throw new Exception($memberInfoRes['msg'], $memberInfoRes['code']);
                }

                if (empty($memberInfoRes['data'])) {
                    throw new Exception('用户信息不存在', 200);
                }

                $memberId = array_column($memberInfoRes['data'], 'id');

            }
            $moduleInfoRes = $moduleUsedModel->getModuleListForResource($memberId, $openStartTime, $openEndTime, $page,
                $pageSize);

            if (!empty($moduleInfoRes['list'])) {


                $memberIdArr = array_column($moduleInfoRes['list'], 'member_id');

                // 获取用户列表数据
                $javaApi             = new ResourceCenterBackend();
                $resourceUserInfoRes = $javaApi->queryResourceUserInfo($opId, $memberIdArr, $openStartTime,
                    $openEndTime);

                if ($resourceUserInfoRes['code'] != 200) {
                    throw new Exception($resourceUserInfoRes['msg'], $resourceUserInfoRes['code']);
                }
                if (empty($resourceUserInfoRes['data'])) {
                    throw new Exception('资源中心用户数据为空', 400);
                }

                $resourceUserInfoMap = [];
                foreach ($resourceUserInfoRes['data'] as $item) {
                    $resourceUserInfoMap[$item['memberId']] = $item;
                }

                $list = [];
                foreach ($moduleInfoRes['list'] as $item) {
                    $list[] = array_merge([
                        'openTime'   => date('Y-m-d H:i:s', $item['begin_time']),
                        'expireTime' => date('Y-m-d H:i:s', $item['expire_time']),
                        'isActive'   => $resourceUserInfoMap[$item['member_id']]['settledState'],
                        'activeTime' => date('Y-m-d H:i:s', $resourceUserInfoMap[$item['member_id']]['settledTime']),
                    ], $resourceUserInfoMap[$item['member_id']]);
                }
                $moduleInfoRes['list'] = $list;
            }

            $data = $moduleInfoRes;

        } catch (Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }
        return $this->returnDataV2($code, $data, $msg);
    }

    private function getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList(0);
        }

        return $this->_orderBusiness;
    }

    private function getOrderModel()
    {
        if (!isset($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer(0);
        }

        return $this->_orderReferModel;
    }
}