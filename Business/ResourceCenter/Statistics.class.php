<?php

namespace Business\ResourceCenter;

use Business\Base;
use Business\Member\Member as MemberBusiness;
use Library\Traits\CacheTrait;
use Model\AppCenter\ModuleList;
use Model\Report\StatisticsV2;
use Model\SystemLog\PftLog;
use Business\JavaApi\Product\ResourceCenterBackend;
use Business\CommodityCenter\Ticket as TicketBusiness;
use Business\JavaApi\Product\ResourceCenterApi;

class Statistics extends Base
{
    use CacheTrait;

    private $moduleListModel;
    private $statisticsModel;
    private $memberBiz;
    private $pftLogModel;
    // 当月月活跃供应商key
    private $activeSupplierMemberCurrentMonthKey = 'rs:ac:sp:sc:cm:member:type:%u';
    // 当月月活跃分销商key
    private $activeDistriMemberCurrentMonthKey = 'rs:ac:dt:sc:cm:member:type:%u';
    // 当月开通商户
    private $openModuleMemberCurrentMonthKey = 'rs:om:cm:member:type:%u';
    // 当月入住商户
    private $settleMemberCurrentMonthKey = 'rs:st:cm:member:type:%u';
    // 累计订单
    private $totalOrderKey = 'rs:total:order:type:%u';
    // 产品订单商家供应商
    private $totalSupplierOrderKey = 'rs:total:order:sp:type:%u';
    // 产品订单商家分销商
    private $totalDistriOrderKey = 'rs:total:order:dt:type:%u';
    // 激活产品key
    private $activeProductKey = 'rs:ac:product:type:%u';
    // 产品top100 key
    private $productTopKey = 'rs:product:top:type:%u';
    // 商家top100 key
    private $memberTopKey = 'rs:member:top:type:%u';

    private $pageSize = 100;

    private function getPftLogModel()
    {
        if (!$this->pftLogModel) {
            $this->pftLogModel = new PftLog();
        }

        return $this->pftLogModel;
    }

    private function getMemberBiz()
    {
        if (!$this->memberBiz) {
            $this->memberBiz = new MemberBusiness();
        }

        return $this->memberBiz;
    }

    private function getModuleListModel()
    {
        if (!$this->moduleListModel) {
            $this->moduleListModel = new ModuleList();
        }

        return $this->moduleListModel;
    }

    private function getStatisticsV2Model()
    {
        if (!$this->statisticsModel) {
            $this->statisticsModel = new StatisticsV2();
        }

        return $this->statisticsModel;
    }

    /**
     * 商家Top 100
     * <AUTHOR>
     *
     * @param  int  $type
     * @param  int  $page
     * @param  int  $pageSize
     *
     * @return array
     */
    public function memberTop($beginTime, $endTime, int $page, int $pageSize): array
    {
        if (empty($page) || empty($pageSize)) {
            return [];
        }
        $type            = 0;
        $redisIns        = $this->getRedisInstance();
        $statisticsModel = $this->getStatisticsV2Model();
        $memberBiz       = $this->getMemberBiz();
        $memberTopKey    = $this->getCacheKey($this->memberTopKey, [$type]);

        $cacheTime = $this->getTodayRemainingCacheTime();

        $start = ($page - 1) * $pageSize;
        $end   = $start + $pageSize - 1;

        if (!$redisIns->exists($memberTopKey)) {
            // 返回数据
            $data         = [];
            $needPushData = [];  // 需要推送的数据
            $list         = $statisticsModel->getResourceMemberTop(date('Ymd', $beginTime), date('Ymd', $endTime));
            $total        = count($list);
            $fidArr       = array_column($list, 'fid');
            if (empty($fidArr)) {
                return [];
            }

            // 获取用户数据
            $memberList = $memberBiz->getMemberInfoByMulti($fidArr);
            $memberMap  = [];
            foreach ($memberList as $item) {
                $memberMap[$item['id']] = $item['dname'];
            }

            // 获取返回数据
            foreach ($list as $item) {
                $tmpData        = [
                    'member_name' => $memberMap[$item['fid']] ?? '',
                    'order_num'   => $item['order_num'],
                ];
                $data[]         = $tmpData;
                $needPushData[] = json_encode($tmpData);
            }

            $iterator = new \ArrayIterator($data);
            $limits   = new \LimitIterator($iterator, ($page - 1) * $pageSize, $pageSize);
            try {
                $data = iterator_to_array($limits, false);
            } catch (\Exception $e) {
                $data = [];
            }

            if (empty($data)) {
                return [];
            }

            $res = [
                'list'  => $data,
                'total' => $total,
            ];

            // 记录缓存
            //$redisIns->rPush($memberTopKey, ...$needPushData);
            //$redisIns->expire($memberTopKey, $cacheTime);
        } else {
            $data = $redisIns->lRange($memberTopKey, $start, $end);
            $data = array_map(function ($value) {
                return json_decode($value, true);
            }, $data);

            $res = [
                'list'  => $data,
                'total' => $redisIns->lLen($memberTopKey),
            ];
        }

        return $res;

    }

    /**
     * 产品top100
     * <AUTHOR>
     *
     * @param  int  $type
     * @param  int  $page
     * @param  int  $pageSize
     *
     * @return array
     */
    public function productTop($beginTime, $endTime, int $page, int $pageSize): array
    {
        if (empty($page) || empty($pageSize)) {
            return [];
        }
        $type            = 0;
        $redisIns        = $this->getRedisInstance();
        $statisticsModel = $this->getStatisticsV2Model();
        $productTopKey   = $this->getCacheKey($this->productTopKey, [$type]);

        $cacheTime = $this->getTodayRemainingCacheTime();

        $start = ($page - 1) * $pageSize;
        $end   = $start + $pageSize - 1;

        if (!$redisIns->exists($productTopKey)) {
            // 返回数据
            $data         = [];
            $needPushData = [];  // 需要推送的数据
            $list         = $statisticsModel->getResourceProductTop(date('Ymd', $beginTime), date('Ymd', $endTime));
            $total        = count($list);
            $tidArr       = array_column($list, 'tid');

            if (empty($tidArr)) {
                return [];
            }

            // 获取门票数据
            $ticketBiz = new TicketBusiness();
            $javaRes   = $ticketBiz->queryTicketInfoByIds($tidArr);
            if (empty($javaRes)) {
                return [];
            }

            $javaResMap = [];
            foreach ($javaRes as $item) {
                $javaResMap[$item['ticket']['id']] = $item;
            }

            // 获取返回数据
            foreach ($list as $item) {
                $tmpData        = [
                    'land_name'   => $javaResMap[$item['tid']]['land']['title'] ?? '',
                    'order_num'   => $item['order_num'],
                    'ticket_name' => $javaResMap[$item['tid']]['ticket']['title'] ?? '',
                ];
                $data[]         = $tmpData;
                $needPushData[] = json_encode($tmpData);
            }

            $iterator = new \ArrayIterator($data);
            $limits   = new \LimitIterator($iterator, ($page - 1) * $pageSize, $pageSize);
            try {
                $data = iterator_to_array($limits, false);
            } catch (\Exception $e) {
                $data = [];
            }

            if (empty($data)) {
                return [];
            }

            $res = [
                'list'  => $data,
                'total' => $total,
            ];

            // 记录缓存
            //$redisIns->rPush($productTopKey, ...$needPushData);
            //$redisIns->expire($productTopKey, $cacheTime);
        } else {
            $data = $redisIns->lRange($productTopKey, $start, $end);
            $data = array_map(function ($value) {
                return json_decode($value, true);
            }, $data);

            $res = [
                'list'  => $data,
                'total' => $redisIns->lLen($productTopKey),
            ];
        }

        return $res;
    }

    /**
     * 产品统计
     * <AUTHOR>
     *
     * @param  int  $type
     *
     * @return array
     */
    public function productStatistics($beginTime, $endTime, $region): array
    {
        $redisIns                 = $this->getRedisInstance();
        $statisticsModel          = $this->getStatisticsV2Model();
        $resourceCenterBackendBiz = new ResourceCenterBackend();
        $type                     = 0;
        // 激活产品key
        $activeProductKey = $this->getCacheKey($this->activeProductKey, [$type]);
        // 激活产品
        $activeProduct = $redisIns->get($activeProductKey);
        // 缓存key时间
        $cacheTime = $this->getTodayRemainingCacheTime();

        // 获取激活产品
        if ($activeProduct === false) {
            $activeProduct = $statisticsModel->getActiveProductCountFromResourceOrder(date('Ymd', $beginTime),
                date('Ymd', $endTime),$region);
            //$redisIns->setex($activeProductKey, $cacheTime, $activeProduct);
        }

        // 获取供应产品以及采购产品
        // 供应产品
        $supplyProductNum = 0;
        // 采购票类
        $purchaseTicketNum = 0;
        $javaRes           = $resourceCenterBackendBiz->productStatistics(0, date('Y-m-d H:i:s', $beginTime),
            date('Y-m-d H:i:s', $endTime), $region);
        if ($javaRes['code'] == 200 && isset($javaRes['data'])) {
            $supplyProductNum  = $javaRes['data']['supplyProductNum'];
            $purchaseTicketNum = $javaRes['data']['purchaseTicketNum'];
        }

        // 今日激活产品
        $activeProductToday = 0;
        // 今日新增供应产品
        $supplyProductNumToday = 0;
        // 今日新增采购票类
        $purchaseTicketNumToday = 0;
        $lastday = $this->getlastday();

        if ($beginTime == strtotime(date("Y-m-01"))) {
            $activeProductToday = $statisticsModel->getActiveProductCountFromResourceOrder(date('Ymd',
                mktime(0, 0, 0, date('m'),
                    date('d'), date('Y'))),
                date('Ymd', mktime(23, 59, 59, date('m'), date('d'), date('Y'))),$region);
            $javaRes            = $resourceCenterBackendBiz->productStatistics(0, date('Y-m-d 00:00:00'),
                date('Y-m-d 23:59:59'), $region);
            if ($javaRes['code'] == 200 && isset($javaRes['data'])) {
                $supplyProductNumToday  = $javaRes['data']['supplyProductNum'];
                $purchaseTicketNumToday = $javaRes['data']['purchaseTicketNum'];
            }
        }

        return compact('activeProduct', 'activeProductToday', 'supplyProductNum', 'supplyProductNumToday',
            'purchaseTicketNum', 'purchaseTicketNumToday');
    }

    /**
     * 订单统计
     * <AUTHOR>
     *
     * @param  int  $type  1本月，2上月， 3累计
     *
     * @return array
     */
    public function orderStatistics($beginTime, $endTime, $region): array
    {
        $redisIns = $this->getRedisInstance();
        $type     = 0;
        // 累计订单key
        $totalOrderKey = $this->getCacheKey($this->totalOrderKey, [$type]);
        // 产品订单商家供应商key
        $totalSupplierOrderKey = $this->getCacheKey($this->totalSupplierOrderKey, [$type]);
        // 产品订单商家分销商Key
        $totalDistriOrderKey = $this->getCacheKey($this->totalDistriOrderKey, [$type]);
        // 累计订单
        $totalOrder = $redisIns->get($totalOrderKey);
        //// 产品订单商家供应商
        $totalSupplierOrder = $redisIns->get($totalSupplierOrderKey);
        //// 产品订单商家分销商
        $totalDistriOrder = $redisIns->get($totalDistriOrderKey);
        // 缓存key时间
        $cacheTime = $this->getTodayRemainingCacheTime();

        if (empty($totalOrder) || empty($totalSupplierOrder) || empty($totalDistriOrder)) {
            list($totalOrder, $totalSupplierOrder, $totalDistriOrder) = $this->orderStatisticsByTime($beginTime,
                $endTime,$region);

            // 将汇总数据丢入缓存
            //$redisIns->setex($totalOrderKey, $cacheTime, $totalOrder);
            //$redisIns->setex($totalSupplierOrderKey, $cacheTime, $totalSupplierOrder);
            //$redisIns->setex($totalDistriOrderKey, $cacheTime, $totalDistriOrder);
        }
        // 累计订单今日新增
        $totalOrderToday = 0;
        // 产品订单商家供应商
        $totalSupplierOrderToday = 0;
        // 产品订单商家分销商
        $totalDistriOrderToday = 0;
        $lastday = $this->getlastday();
        if ($beginTime == strtotime(date("Y-m-01"))) {
            list($totalOrderToday, $totalSupplierOrderToday, $totalDistriOrderToday) = $this->orderStatisticsByTime(mktime(0,
                0, 0, date('m'), date('d'), date('Y')),
                mktime(23, 59, 59, date('m'), date('d'), date('Y')),$region);
        }

        return compact('totalOrder', 'totalSupplierOrder', 'totalDistriOrder', 'totalOrderToday',
            'totalSupplierOrderToday', 'totalDistriOrderToday');
    }

    /**
     * 获取开始跟结束时间
     * <AUTHOR>
     *
     * @param  int  $type  1本月，2上月， 3累计
     *
     * @return array
     */
    private function getTime(int $type): array
    {
        // 获取需要的时间
        switch ($type) {
            case 1:
            default:
                // 本月
                $beginTime = mktime(0, 0, 0, date('m'), 1, date('Y'));
                $endTime   = mktime(23, 59, 59, date('m'), date('t'), date('Y'));
                break;
            case 2:
                // 上月
                $lastMonth = strtotime('-1 month');
                $beginTime = strtotime(date('Y-m-01 00:00:00', $lastMonth));
                $endTime   = strtotime(date('Y-m-t 23:59:59', $lastMonth));
                break;
            case 3:
                $beginTime = mktime(0, 0, 0, date('m', strtotime('-2 months')), 1, date('Y', strtotime('-2 months')));
                $endTime   = mktime(23, 59, 59, date('m'), date('t'), date('Y'));
                break;
        }

        return [$beginTime, $endTime];
    }

    /**
     * 封装的订单统计通过时间
     * <AUTHOR>
     *
     * @param  int  $beginTime
     * @param  int  $endTime
     *
     * @return array
     */
    private function orderStatisticsByTime(int $beginTime, int $endTime, $region): array
    {
        // 累计订单
        $totalOrder = [];
        // 产品订单商家供应商
        $totalSupplierOrder = [];
        // 产品订单商家分销商
        $totalDistriOrder = [];

        // 获取本月开通计划
        $statisticsModel = $this->getStatisticsV2Model();
        $memberBiz       = $this->getMemberBiz();

        $page = 1;
        while (true) {
            // 获取日报表列表
            $list = $statisticsModel->getMemberFromResourceOrderList(date('Ymd', $beginTime), date('Ymd', $endTime),$region,
                $page, $this->pageSize);

            if (empty($list)) {
                break;
            }

            $page++;

            // 汇总分销和供应商数据
            $memberIdArr = array_column($list, 'fid');

            // 判断客户账号类型
            $memberList = $memberBiz->getMemberInfoByMulti($memberIdArr);
            $memberMap  = [];
            foreach ($memberList as $item) {
                $memberMap[$item['id']] = $item['dtype'];
            }

            // 汇总累计订单
            foreach ($list as $item) {
                $ordersInfoArr = json_decode($item['orders_info'], true);
                if (empty($ordersInfoArr)) {
                    continue;
                }

                foreach ($ordersInfoArr as $orderInfoItem) {
                    // 计算总订单数
                    if (!in_array($orderInfoItem[0], $totalOrder)) {
                        $totalOrder[] = $orderInfoItem[0];
                    }

                    // 计算分销商供应商统计数量
                    if (isset($memberMap[$item['fid']]) && $memberMap[$item['fid']] == 0
                        && !in_array($item['fid'], $totalSupplierOrder)) {
                        $totalSupplierOrder[] = $item['fid'];
                    } else if (isset($memberMap[$item['fid']]) && $memberMap[$item['fid']] == 1
                               && !in_array($item['fid'], $totalDistriOrder)) {
                        $totalDistriOrder[] = $item['fid'];
                    }
                }
            }
        }

        return [count($totalOrder), count($totalSupplierOrder), count($totalDistriOrder)];
    }

    /**
     * 活跃用户汇总按月
     * <AUTHOR>
     *
     * @param  int  $type  1本月，2上月， 3累计
     *
     * @return array
     */
    public function activeMemberStatistics($beginTime,$endTime,$region): array
    {
        $redisIns = $this->getRedisInstance();
        // 获取本月开通计划
        $moduleListModel = $this->getModuleListModel();
        $statisticsModel = $this->getStatisticsV2Model();
        $memberBiz       = $this->getMemberBiz();
        $pftLogModel     = $this->getPftLogModel();
        $type            = 0;
        // 活跃供应商key
        $activeSupplierMemberCurrentMonthKey = $this->getCacheKey($this->activeSupplierMemberCurrentMonthKey, [$type]);
        // 活跃分销商key
        $activeDistriMemberCurrentMonthKey = $this->getCacheKey($this->activeDistriMemberCurrentMonthKey, [$type]);
        // 开通商户key
        $openModuleMemberCurrentMonthKey = $this->getCacheKey($this->openModuleMemberCurrentMonthKey, [$type]);
        // 入住商户key
        $settleMemberCurrentMonthKey = $this->getCacheKey($this->settleMemberCurrentMonthKey, [$type]);
        // 活跃供应商
        $activeSupplierMember = $redisIns->get($activeSupplierMemberCurrentMonthKey);
        // 活跃分销商
        $activeDistriMember = $redisIns->get($activeDistriMemberCurrentMonthKey);
        // 开通商户
        $openMember = $redisIns->get($openModuleMemberCurrentMonthKey);
        // 入住商户
        $settleMember = $redisIns->get($settleMemberCurrentMonthKey);
        // 缓存key时间
        $cacheTime = $this->getTodayRemainingCacheTime();

        // 计算开通商户以及入住商户
        if ($settleMember === false) {
            $resouce      = new ResourceCenterApi();
            $settleMember = $resouce->queryResourceSettledTotal($beginTime,$endTime,$region);
            //$settleMember = $pftLogModel->getResourceCenterLogCountByTimeAndBehavior(2,
            //    $beginTime, $endTime);
            //$redisIns->setex($settleMemberCurrentMonthKey, $cacheTime, $settleMember);
        }

        // 获取开通用户
        if ($openMember === false) {
            $openMember = $moduleListModel->getCountForResource($beginTime, $endTime);
            //$redisIns->setex($openModuleMemberCurrentMonthKey, $cacheTime, $openMember);
        }

        // 计算活跃供应商以及活跃分销商
        if ($activeSupplierMember === false || $activeDistriMember === false) {
            $activeSupplierMember = 0;
            $activeDistriMember = 0;

            $page = 1;

            while(true) {
                // 通过报表判断是否达到活跃条件
                $activeList  = $statisticsModel->getActiveMemberFromResourceOrder(date('Ymd', $beginTime),
                    date('Ymd', $endTime), $region, $page, $this->pageSize);

                if (empty($activeList)) {
                    break;
                }

                $page++;

                $memberIdArr = array_column($activeList, 'fid');
                if (!empty($memberIdArr)) {
                    // 判断客户账号类型
                    $memberList = $memberBiz->getMemberInfoByMulti($memberIdArr);
                    foreach ($memberList as $item) {
                        if ($item['dtype'] == 0) {
                            $activeSupplierMember++;
                        } else if ($item['dtype'] == 1) {
                            $activeDistriMember++;
                        }
                    }
                }
            }

            // 将汇总数据丢入缓存
            //$redisIns->setex($activeSupplierMemberCurrentMonthKey, $cacheTime, $activeSupplierMember);
            //$redisIns->setex($activeDistriMemberCurrentMonthKey, $cacheTime, $activeDistriMember);
        }
        //获取本月最后一天
        $lastday  = $this->getlastday();
        // 今日新增开通商户
        $openMemberToday = 0;
        // 今日新增入住商户
        $settleMemberToday = 0;
        if ($beginTime == strtotime(date("Y-m-01"))) {
            // 如果是本月需要计算今日新增
            $openMemberToday = $moduleListModel->getCountForResource(mktime(0, 0, 0, date('m'), date('d'), date('Y')),
                mktime(23, 59, 59, date('m'), date('d'), date('Y')));
            // 新增入住
            $settleMemberToday = $resouce->queryResourceSettledTotal(mktime(0, 0, 0, date('m'), date('d'), date('Y')),
                mktime(23, 59, 59, date('m'), date('d'), date('Y')), $region);
            //$settleMemberToday = $pftLogModel->getResourceCenterLogCountByTimeAndBehavior(2,
            //    mktime(0, 0, 0, date('m'), date('d'), date('Y')),
            //    mktime(23, 59, 59, date('m'), date('d'), date('Y')));
        }

        return compact('activeSupplierMember', 'activeDistriMember', 'openMember', 'settleMember',
            'openMemberToday', 'settleMemberToday');
    }

    /**
     * 获取本月最后一天
     * <AUTHOR>
     *
     */
    public function getlastday()
    {
        $firstday = date('Y-m-01', strtotime(date("Y-m-d")));
        $lastday  = strtotime(date('Y-m-d', strtotime("$firstday +1 month -1 day "))) + (24 * 60 * 60 - 1);

        return $lastday;
    }

}