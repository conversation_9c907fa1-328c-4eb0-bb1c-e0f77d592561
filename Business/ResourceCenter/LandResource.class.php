<?php
/**
 * Created by PhpStorm.
 * User: xwh
 * Date: 2020-11-18
 * Time: 15:20
 */

namespace Business\ResourceCenter;

use Business\Base;
use Library\Cache\Cache;

class LandResource extends Base {

    private $_selectType = false;
    private $_orderBusiness;
    private $_orderReferModel;
    /**
     * 根据用户id和时间类型获取资源中心概况
     *
     * @date   2020-11-19
     * <AUTHOR>
     *
     * @param  int      $fid   会员ID
     * @param  int      $type  类型， 1今天, 2昨天, 3本周, 4上周, 5本月， 6上月, 7 近7日，8 近30天 9 近90
     *
     * @return array
     */
    public function orderStatistics($fid, $type){
        if (!$fid) {
            return ['code' => 203, 'msg' => '参数错误', 'data' => []];
        }
        $time = $this->_disposeTime($type);
        $beginTime = $time['beginTime'];
        $endTime   = $time['endTime'];
        $redis = Cache::getInstance('redis');

        $key = 'rs:order:fid:' . $fid . ':t:' . $type;

        $data = $redis->get($key);
        $data = ($data === false) ? false : json_decode($data, true);

        // 这里汇总逻辑同订单查询汇总逻辑，额外添加缓存
        if ($data === false) {
            ////DB交互获取数据
            //$list = $this->getOrderModel()->getTotal($fid, $params['ordernum'], $params['ordername'],
            //    $params['person_id'], $params['tel'],
            //    $params['order_time_start'], $params['order_time_end'], $params['play_time_start'],
            //    $params['play_time_end'], $params['dtime_start'],
            //    $params['dtime_end'], $params['begin_time_start'], $params['begin_time_end'], $params['status'],
            //    $params['pay_status'], $params['pay_mode'],
            //    $params['order_mode'], $params['p_type'], $params['operate_id'], $params['check_resource'],
            //    $params['lid'], $params['pid'], $params['aid'],
            //    $params['reseller_id'], $params['salerid'], $params['tid'], $params['order_source']);

            $memberArr = [$fid];
            $queryRes  = $this->getOrderBusiness()->getBusinessTotalByOrderService($fid, $fid, 0,
                $memberArr, false,
                false, 1, 100, false, false,
                false, false, $beginTime, $endTime,
                false, false, false, false,
                false, false, false, false,
                false, false, false, false,
                false, false, false, false, false, false,
                2);

            $res = $queryRes['data'];

            if (empty($res)) {
                $data = [
                    'profit' => 0,
                    'money'  => 0,
                    'total'  => 0,
                    'tnum'   => 0,
                ];
            } else {
                //$orderIdArr = array_column($list, 'ordernum');
                //$res        = $this->getOrderBusiness()->handleOrderTotal($fid, $orderIdArr);

                //利润=总销售额-总成本价
                $profit = $res['total_sale_money'] - $res['total_cost_money'];
                $data   = [
                    'profit' => $profit,
                    'money'  => $res['total_sale_money'],
                    'total'  => $res['total_order_num'],
                    'tnum'   => $res['total_ticket_num'],
                ];
            }

            if ($type == 1) {
                // 今天的数据 缓存记录30分钟，
                $redis->setex($key, 1800, json_encode($data));
            } else {
                $redis->setex($key, strtotime(date('Y-m-d 23:59:59')) - time(), json_encode($data));
            }
        }

        return ['code' => 200, 'msg' => 'success', 'data' => $data];

    }

    private function getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        }

        return $this->_orderBusiness;
    }

    private function getOrderModel()
    {
        if (!isset($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer($this->_selectType);
        }

        return $this->_orderReferModel;
    }


    /**
     * 获取某个供应商下时间段内产生订单的分销商
     *
     * @date   2020-11-19
     * <AUTHOR>
     *
     * @param  int      $uid   会员ID
     * @param  int      $type  类型， 1今天, 2昨天, 3本周, 4上周, 5本月， 6上月
     * @param  array    $fids  分销商
     *
     * @return array
     */
    public function getDistributionOrderNum(int $uid, int $type, array $fids)
    {
        if (!$uid || !$type || !$fids) {
            return ['code' => 203, 'msg' => '参数错误', 'data' => []];
        }
        $time          = $this->_disposeTime($type, '', false);
        $beginDateTime = $time['beginTime'];
        $endDateTime   = $time['endTime'];

        $statisticsModel = new \Model\Report\Statistics();
        $result          = $statisticsModel->getDistributionOrderNum($uid, $fids, $beginDateTime, $endDateTime);
        $result && $result = array_column($result, 'reseller_id');

        $result = array_unique($result);

        return ['code' => 200, 'msg' => 'success', 'data' => $result];

    }

    private function _disposeTime($type, $fill = '-', $sfm = true)
    {
        $layout = "Y" . $fill . "m" . $fill . "d";
        switch ($type) {
            case 1:
                $beginTime = date($layout);
                $endTime   = date($layout);
                break;
            case 2:
                $beginTime = date($layout, strtotime('-1 days'));
                $endTime   = date($layout, strtotime('-1 days'));
                break;
            case 3:
                $beginTime = date($layout, strtotime("this week Monday", time()));
                $endTime   = date($layout, strtotime("this week Sunday", time()));
                break;
            case 4:
                $beginTime = date($layout, strtotime("last week Monday", time()));
                $endTime   = date($layout, strtotime("last week Sunday", time()));
                break;
            case 5:
                $beginTime = date("Y" . $fill . "m" . $fill . "01");
                $endTime   = date("Y" . $fill . "m" . $fill . "t");
                break;
            case 6:
                $lastMonth = strtotime('-1 month');
                $beginTime = date("Y" . $fill . "m" . $fill . "01", $lastMonth);
                $endTime   = date("Y" . $fill . "m" . $fill . "t", $lastMonth);
                break;
            case 7:
                $beginTime = date($layout, strtotime('-6 days'));
                $endTime   = date($layout);
                break;
            case 8:
                $beginTime = date($layout, strtotime('-29 days'));
                $endTime   = date($layout);
                break;
            case 9:
                $beginTime = date($layout, strtotime('-89 days'));
                $endTime   = date($layout);
                break;
            default:
                $beginTime = date($layout);
                $endTime   = date($layout);
                break;
        }
        if ($sfm) {
            $beginTime .= ' 00:00:00';
            $endTime   .= ' 23:59:59';
        }

        return ['beginTime' => $beginTime, 'endTime' => $endTime];
    }
}