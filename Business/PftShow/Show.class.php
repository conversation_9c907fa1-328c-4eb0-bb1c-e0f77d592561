<?php
/**
 * 演出相关
 */

namespace Business\PftShow;

class Show extends ECBase
{
    //日志目录
    private $_debugLogDir = 'pft_show/show/';

    public function __construct()
    {

    }

    /**
     * 获取演出场馆列表
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return  array
     */
    public function getVenuesList(int $applyDid, int $page = 1, int $size = 10, string $venueName = ''): array
    {
        if (!$applyDid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'apply_did'  => $applyDid,
            'venue_name' => $venueName,
            'page'       => $page,
            'size'       => $size,
        ];

        $result = $this->ExternalCodeCall(self::__GET_VENUES_LIST__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取演出场馆信息
     *
     * @param  array  $venueIdArr  场馆id数组
     *
     * @return  array
     */
    public function getVenueInfoByIdArr(array $venueIdArr): array
    {
        if (!$venueIdArr) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'venue_id_arr' => $venueIdArr,
        ];

        $result = $this->ExternalCodeCall(self::__GET_VENUES_INFO_BY_ID_ARR__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取演出分区信息
     *
     * @param  array  $zoneIdArr  分区id数组
     *
     * @return  array
     */
    public function getZoneInfoByIdArr(array $zoneIdArr): array
    {
        if (!$zoneIdArr) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'zone_id_arr' => $zoneIdArr,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ZONE_INFO_BY_ID_ARR__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取演出场次信息
     *
     * @param  array  $roundIdArr  场次id数组
     *
     * @return  array
     */
    public function getRoundInfoByIdArr(array $roundIdArr): array
    {
        if (!$roundIdArr) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'round_id_arr' => $roundIdArr,
        ];

        //$result = $this->ExternalCodeCall(self::__GET_ROUND_INFO_BY_ID_ARR__, $data);

        $result = $this->ExternalCodeCall('Merchant/RoundManage/getRoundInfoByRoundIdArr', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取场馆演出列表
     * <AUTHOR>  Li
     * @date  2021-08-18
     *
     * @param  int  $venueId  场馆id
     * @param  string  $date  游玩日期
     *
     * @return  array
     */
    public function getRoundList(int $venueId, string $date): array
    {
        if (!$venueId || !$date) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'venue_id' => $venueId,
            'date'     => $date,
        ];

        //$result = $this->ExternalCodeCall(self::__GET_ROUND_LIST__, $data);
        $result = $this->ExternalCodeCall('Merchant/RoundManage/getRoundListByVenueId', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 下单获取演出信息
     * <AUTHOR>  Li
     * @date  2021-08-18
     *
     * @param  int  $sid  供应商id
     * @param  int  $venueId  场馆id
     * @param  string  $date  游玩日期
     *
     * @return  array
     */
    public function getShowInfoList(int $sid, int $venueId, string $date): array
    {
        if (!$sid || !$venueId || !$date) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'      => $sid,
            'venue_id' => $venueId,
            'date'     => $date,
        ];

        $result = $this->ExternalCodeCall(self::__GET_SHOW_INFO_LIST__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取场馆演出列表
     * <AUTHOR>  Li
     * @date  2021-08-18
     *
     * @param  int  $sid  供应商id
     * @param  int  $aid  上级供应商id
     * @param  int  $tid  门票id
     * @param  int  $venueId  场馆id
     * @param  string  $date  游玩日期 2021-08-12
     * @param  int  $zoneId  分区id
     *
     * @return  array
     */
    public function getShowStorage(int $sid, int $aid, int $tid, int $venueId, string $date, int $zoneId = 0, $merchantId = 0, $channel = 0): array
    {
        if (!$sid || !$aid || !$tid || !$venueId || !$date || !strtotime($date)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'        => $sid,
            'aid'        => $aid,
            'tid'        => $tid,
            'venue_id'   => $venueId,
            'zone_id'    => $zoneId,
            'date'       => $date,
            'merchantId' => $merchantId,
            'fid'        => $merchantId,
            'channel'    => $channel,
        ];

        //通过白名单判断走新服务还是老服务
        $showHeavyVolumeAccountConfig = load_config('config', 'tmp_show_heavy_volume_account');
        if ($showHeavyVolumeAccountConfig['is_all_open'] == 1 || in_array($aid, $showHeavyVolumeAccountConfig['list'])) {
            $result = $this->ExternalCodeCall('Merchant/RoundManage/getShowStorage', $data, 'show_manage');
        } else {
            $result = $this->ExternalCodeCall(self::__GET_SHOW_STORAGE__, $data);
        }

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    ///**
    // * 演出改签具体操作
    // * <AUTHOR>  Li
    // * @date   2021-08-24
    // *
    // * @param  string  $ordernum  订单号
    // * @param  string  $changeDate  改签日期
    // * @param  array  $venueArr  演出信息
    // * @param  int  $sid  供应商id
    // * @param  int  $source  来源
    // * @param  int  $opId  操作员id
    // * @param  bool  $isSms  是否短信改签
    // *
    // * @return  array
    // */
    //public function showChange(string $ordernum, string $changeDate, array $venueArr, int $sid, int $source, int $opId = 0, bool $isSms = false): array
    //{
    //    if (!$ordernum || !$changeDate || !$source || !$sid || !$venueArr) {
    //        return $this->returnData(203, '参数异常', []);
    //    }
    //
    //    $data = [
    //        'ordernum'    => $ordernum,
    //        'date'        => $changeDate,
    //        'venue_arr'   => $venueArr,
    //        'sid'         => $sid,
    //        'source'      => $source,
    //        'operator_id' => $opId,
    //        'is_sms'      => $isSms,
    //    ];
    //
    //    $result = $this->ExternalCodeCall(self::__SHOW_CHANGE__, $data);
    //
    //    if (!isset($result['code']) || $result['code'] != 200) {
    //        return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
    //    }
    //
    //    return $this->returnData(200, $result['msg'], $result['data']);
    //}

    ///**
    // * 演出预约（占座，订单绑定）
    // * <AUTHOR>  Li
    // * @date  2022-04-13
    // *
    // * @param  string  $ordernum  订单号
    // * @param  string  $changeDate  预约日期
    // * @param  array  $venueArr  演出信息
    // * @param  int  $sid  供应商id
    // * @param  int  $source  来源
    // * @param  int  $opId  操作员id
    // * @param  bool  $isSms  是否短信预约
    // *
    // * @return  array
    // */
    //public function showReserve(string $ordernum, string $changeDate, array $venueArr, int $sid, int $source, int $opId = 0, bool $isSms = false): array
    //{
    //    if (!$ordernum || !$changeDate || !$source || !$sid || !$venueArr) {
    //        return $this->returnData(203, '参数异常', []);
    //    }
    //
    //    $data = [
    //        'ordernum'    => $ordernum,
    //        'date'        => $changeDate,
    //        'venue_arr'   => $venueArr,
    //        'sid'         => $sid,
    //        'source'      => $source,
    //        'operator_id' => $opId,
    //        'is_sms'      => $isSms,
    //    ];
    //
    //    $result = $this->ExternalCodeCall(self::__SHOW_RESERVE__, $data);
    //
    //    if (!isset($result['code']) || $result['code'] != 200) {
    //        return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
    //    }
    //
    //    return $this->returnData(200, $result['msg'], $result['data']);
    //}

    /**
     * 获取时间段分区的库存
     * <AUTHOR>  Li
     * @date  2022-04-16
     *
     * @param  int  $venueId  场馆id
     * @param  string  $startDate  开始日期
     * @param  string  $endDate  结束日期
     * @param  int  $sid  供应商id
     * @param  int  $aid  门票上级供应商id
     * @param  int  $tid  门票id
     * @param  int  $zoneId  分区id
     *
     * @return  array
     */
    public function getZoneStorageList(int $venueId, string $startDate, string $endDate, int $sid, int $aid, int $tid, int $zoneId, $merchantId = 0, $channel = 0): array
    {
        if (!$venueId || !$startDate || !$endDate || !$sid || !$aid || !$tid || !strtotime($startDate) || !strtotime($endDate)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'venue_id'   => $venueId,
            'start_date' => $startDate,
            'end_date'   => $endDate,
            'sid'        => $sid,
            'aid'        => $aid,
            'tid'        => $tid,
            'zone_id'    => $zoneId,
            'merchantId' => $merchantId,
            'fid'        => $merchantId,
            'channel'    => $channel,
        ];

        $showHeavyVolumeAccountConfig = load_config('config', 'tmp_show_heavy_volume_account');
        if ($showHeavyVolumeAccountConfig['is_all_open'] == 1 || in_array($aid, $showHeavyVolumeAccountConfig['list'])) {
            $result = $this->ExternalCodeCall('Merchant/RoundManage/getZoneStorageList', $data, 'show_manage');
        } else {
            $result = $this->ExternalCodeCall(self::__GET_SHOW_STORAGE_LIST__, $data);
        }

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 接口预判演出信息
     * 
     */
    public function orderPreCheck($sid, $tid, $tnum, $totalPrice, $playTime, $orderName, $orderTel,
                                $payMode, $channel, $linkType, $memberId, $roundSortId, $personInfoArr = [], $roundId = 0, $series = '')
    {
        if (!$sid || !$tid || !is_numeric($tnum) || !$tnum || !$playTime || !is_numeric($payMode) 
            || !is_numeric($channel) || !in_array($linkType, ['common', 'package', 'packageSon', 'link', 'linkSon']) 
            || !$memberId || (!$roundId && !$roundSortId)) {
            return $this->returnData(203, '参数异常');
        }

        $data = [
            'sid'           => $sid,
            'tid'           => $tid,
            'tnum'          => $tnum,
            'total_price'   => $totalPrice,
            'playtime'      => $playTime,
            'order_name'    => $orderName,
            'ordertel'      => $orderTel,
            'paymode'       => $payMode,
            'channel'       => $channel,
            'link_type'     => $linkType,
            'member_id'     => $memberId,
            'round_sort_id' => $roundSortId,
            'person_info'   => $personInfoArr,
            'round_id'      => $roundId,
            'series'        => $series,
        ];

        $showHeavyVolumeAccountConfig = load_config('config', 'tmp_show_heavy_volume_account');
        if ($showHeavyVolumeAccountConfig['is_all_open'] == 1 || in_array($sid, $showHeavyVolumeAccountConfig['list'])) {
            $result = $this->ExternalCodeCall('Merchant/Order/orderPreCheck', $data, 'show_manage');
        } else {
            $result = $this->ExternalCodeCall(self::__ORDER_PRE_CHECK__, $data);
        }

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 更新场馆关联产品数量
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $venueId  配置后的场馆ID
     * @param  int  $oldVenueId  配置前的场馆ID
     * @param  int  $lid  操作的景区id
     * @param  int  $opId  操作人id
     *
     * @return  array
     */
    public function updateVenueProductNum($applyDid, $venueId, $oldVenueId = 0, $lid = 0, $opId = 0): array
    {
        if (!$venueId || !$applyDid || !$lid || !$opId) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'apply_did'    => $applyDid,
            'venue_id'     => $venueId,
            'old_venue_id' => $oldVenueId,
            'lid'          => $lid,
            'op_id'        => $opId,
        ];

        $result = $this->ExternalCodeCall('Merchant/VenueManage/updateVenueProductNum', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取场次时间
     *
     * @param  int  $roundMode  场次模式 0：时段模式 1：时点模式
     * @param  string  $startTime  开始时间
     * @param  string  $endTime  结束时间
     *
     * @return  array
     */
    public function getRoundTime($roundMode, $startTime, $endTime): array
    {
        if (!in_array($roundMode, [0, 1]) || !$startTime || !$endTime) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'round_mode' => $roundMode,
            'bt'         => $startTime,
            'et'         => $endTime,
        ];

        $result = $this->ExternalCodeCall('Merchant/RoundManage/getRoundTime', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取舞台信息
     * @param  int  $roundId  场次id
     *
     * @return array
     */
    public static function getStageInfo($venueId = 0, $roundId = 0)
    {
        if (!$roundId && !$venueId) {
            return [];
        }

        if ($roundId) {
            //获取舞台信息
            $stageSeat = (new \Model\Product\Show())->getZoneSeats($roundId, \Business\PftShow\ShowManage::getStageConstants()['stage_id']);
        } else {
            $field = 'id,col_num,row_num,custom_num as seat,custom_pos,seat_status as status,col_id,row_id,priority';
            //获取舞台信息
            $stageSeat = (new \Model\Product\Show())->getRoundSeatById(\Business\PftShow\ShowManage::getStageConstants()['stage_id'], $field, [0, 5], $venueId);
        }

        //组装信息
        $stageSeat = $stageSeat ?: [];
        $stageInfo = [
            'stage_seats' => $stageSeat,
            'zone_id'     => \Business\PftShow\ShowManage::getStageConstants()['stage_id'],
            'zone_name'   => \Business\PftShow\ShowManage::getStageConstants()['stage_name'],
            'zone_color'  => \Business\PftShow\ShowManage::getStageConstants()['stage_color'],
        ];

        return $stageInfo;
    }

    /**
     * 获取舞台常量信息
     *
     * @return  array
     */
    public function getStageConstants(): array
    {
        $result = $this->ExternalCodeCall('Merchant/RoundSeat/getStageConstants', [], 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 预留记录处理【座位从预留设置为可售时调用】 将预留记录取消
     *
     * @param $reserveSeatIdArr
     *
     * @return array
     */
    public function reserveRecordHandle($roundId, $reserveSeatIdArr)
    {
        if (!$roundId || !$reserveSeatIdArr) {
            return $this->returnData(203, '缺少必要参数');
        }

        $seatIdArr = [];
        foreach ($reserveSeatIdArr as $seatArr) {
            $seatIdArr = array_merge($seatIdArr, $seatArr);
        }

        $showModel = new \Model\Product\Show();

        //先获取到预留记录对应的座位id
        $recordList = $showModel->getSeatReserveList($roundId, $seatIdArr);
        //没拿到信息就当做不需要处理了
        if (!$recordList) {
            return $this->returnData(200, '未获取到预留信息');
        }

        //组装下数据
        $zoneSeatIdArr  = [];
        $recordSeatList = [];
        foreach ($recordList as $item) {
            //需要过滤掉已使用的座位
            if ($item['reserve_status'] != 2) {
                //分区座位信息
                $zoneSeatIdArr[intval($item['zone_id'])][] = intval($item['seat_id']);
                //预留记录信息
                $recordSeatList[intval($item['record_id'])][] = intval($item['seat_id']);
            }
        }

        //如果存在预留记录的座位 需要将对应座位id全部释放成可售
        $zoneSeatStatus = [];
        foreach ($reserveSeatIdArr as $zoneId => $seatList) {
            //取出$zoneSeatIdArr[$zoneId] 数组中与$seatList 数组的差集
            $releaseIdArr = isset($zoneSeatIdArr[$zoneId]) && $zoneSeatIdArr[$zoneId] ? array_filter(array_unique($zoneSeatIdArr[$zoneId])) : [];
            if ($releaseIdArr) {
                $diffSeatIdArr = array_diff($releaseIdArr, $seatList);
                //再将差值组装到需要更新的座位信息中
                foreach ($diffSeatIdArr as $seatId) {
                    $zoneSeatStatus[] = [
                        'zone_id' => $zoneId,
                        'seat_id' => $seatId,
                        'status'  => 4,
                    ];
                }
            }
        }

        //$errorNum   = 0;
        //$successNum = 0;
        //将所有待出票的记录全部取消
        //foreach ($recordSeatList as $recordId => $seatList) {
        //    $releaseRes = $showModel->releaseReserveDetail($recordId, $roundId, $seatList, $remark);
        //    if (!$releaseRes) {
        //        pft_log($this->_debugLogDir, json_encode([
        //            'recordId' => $recordId,
        //            'roundId'  => $roundId,
        //            'seatList' => $seatList,
        //        ], JSON_UNESCAPED_UNICODE));
        //        $errorNum +=1;
        //    } else {
        //        $successNum +=1;
        //    }
        //}

        return $this->returnData(200, '操作成功', ['zoneSeatStatus' => $zoneSeatStatus, 'recordSeatList' => $recordSeatList]);
    }

    /**
     * 演出期票下单 选座座位id恢复可售
     *
     * @param  int  $venueId  场馆id
     * @param  int  $roundId  场次id
     * @param  int  $zoneId  分区id
     * @param  array  $seatIdArr  座位id数组
     *
     * @return array
     */
    public function showPreSaleSeatReductionForOrder($venueId, $roundId, $zoneId, $seatIdArr)
    {
        if (!$venueId || !$roundId || !$zoneId || !$seatIdArr) {
            return $this->returnData(203, '缺少必要参数');
        }

        $showModel = new \Model\Product\Show();
        //先查询下当前座位是否是预留的
        $roundInfo = $showModel->getRoundInfo($roundId);
        if (!$roundInfo) {
            return $this->returnData(203, '场次信息不存在');
        }

        if ($roundInfo['venus_id'] != $venueId) {
            return $this->returnData(203, '场次信息异常');
        }

        //需要校验下座位状态
        $seatInfo = $showModel->getZoneSeats($roundId, $zoneId, false, $seatIdArr);

        //组装下座位更新的数据
        $zoneSeatStatus = [];
        foreach ($seatInfo as $item) {
            if (in_array($item['status'], [0, 1, 4])) {
                $zoneSeatStatus[] = [
                    'zone_id' => $item['zone_id'],
                    'seat_id' => $item['id'],
                    'status'  => 4,
                ];
            }
        }

        //先获取到预留记录对应的座位id
        $recordList = $showModel->getSeatReserveList($roundId, $seatIdArr);
        //没拿到信息就当做不需要处理了
        $releaseRecordInfo = [];
        if ($recordList) {
            //组装下数据
            $recordSeatList = [];
            foreach ($recordList as $item) {
                //需要过滤掉已使用的座位
                if ($item['reserve_status'] != 2) {
                    //预留记录信息
                    $recordSeatList[intval($item['record_id'])][] = intval($item['seat_id']);
                }
            }

            if ($recordSeatList) {
                $releaseRecordInfo = [
                    'remark'         => '取消的原因为：演出期票下单，预留记录失效',
                    'recordSeatList' => $recordSeatList,
                ];
            }
        }

        $result = (new \Business\Product\Show())->updateRoundSeat($roundId, $zoneSeatStatus, $roundInfo['opid'], $roundInfo['is_new'], $releaseRecordInfo);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取场次库存
     *
     * @param  int  $sid  供应商id
     * @param  int  $aid  上级供应商id
     * @param  int  $tid  门票id
     * @param  int  $venueId  场馆id
     * @param  string  $useDate  适用日期
     * @param  int  $zoneId  分区id
     * @param  int  $merchantId  登录商户id
     * @param  int  $channel  渠道来源
     * @param  bool  $isRemoveExpire  是否过滤过期场次
     *
     * @return  array
     */
    public function getRoundStorage($sid, $aid, $tid, $venueId, $useDate, $zoneId = 0, $merchantId = 0, $channel = 0, $isRemoveExpire = true): array
    {
        if (!$sid || !$aid || !$tid || !$venueId || !$useDate || !strtotime($useDate)) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'sid'              => $sid,
            'aid'              => $aid,
            'tid'              => $tid,
            'zone_id'          => $zoneId,
            'venue_id'         => $venueId,
            'use_date'         => $useDate,
            'merchant_id'      => $merchantId,
            'channel'          => $channel,
            'is_remove_expire' => $isRemoveExpire,
        ];

        $result = $this->ExternalCodeCall('Merchant/RoundSeat/getRoundStorage', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 判断当前门票是否可操作通知
     *
     * @param  int  $sid  供应商id
     * @param  int  $tid  门票id
     * @param  int  $fid  分销商id 没传默认为美团直连用户id
     * @param  int  $dockingMode  对接模式 1美团直连
     *
     * @return  array
     */
    public function refreshNoticeCheck($sid, $tid, $fid = 0, $dockingMode = 1): array
    {
        if (!$tid || !$sid) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'sid'          => $sid,
            'fid'          => $fid,
            'tid'          => $tid,
            'docking_mode' => $dockingMode,
        ];

        $result = $this->ExternalCodeCall('Merchant/Ota/refreshNoticeCheck', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取当前场馆是否被复制中
     *
     * @param  int  $venueId  场馆id
     *
     * @return  array
     */
    public function venueCopyLockCheck($venueId): array
    {
        if (!$venueId) {
            return $this->returnData(203, '场馆id不能为空');
        }

        $data = [
            'venue_id' => $venueId,
        ];

        $result = $this->ExternalCodeCall('Merchant/VenueManage/venueCopyLockCheck', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 推送演出订单出票结果【仅未绑定上游的情况调用】
     *
     * @param  string  $orderNum  订单号
     * @param  int  $status  出票状态 0出票失败 1出票成功
     * @param  string  $desc  原因
     *
     * @return  array
     */
    public function pushOrderIssuing($orderNum, $status, $desc = ''): array
    {
        if (!$orderNum) {
            return $this->returnData(203, '订单号不能为空');
        }
        if (!is_numeric($status) || !in_array($status,[0, 1])) {
            return $this->returnData(203, '出票状态异常');
        }

        $data = [
            'order_num' => $orderNum,
            'status'    => $status,
            'desc'      => $desc,
        ];

        $result = $this->ExternalCodeCall('Merchant/Order/pushOrderIssuing', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 演出库存检测通知
     *
     * @param  int  $lid  景区id
     * @param  int  $tid  门票id
     * @param  string  $playTime  游玩日期
     * @param  int  $venueId  场馆id
     * @param  int  $roundId  场次
     * @param  int  $zoneId  分区id
     *
     * @return  array
     */
    public function showStorageNotice($lid, $tid, $playTime, $venueId = 0, $roundId = 0, $zoneId = 0): array
    {
        if (!$tid || !$lid) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'lid'       => $lid,
            'tid'       => $tid,
            'play_time' => $playTime,
            'venue_id'  => $venueId,
            'round_id'  => $roundId,
            'zone_id'   => $zoneId,
        ];

        $result = $this->ExternalCodeCall('Merchant/RoundSeat/showStorageNotice', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 演出预约
     *
     * @param  string  $orderNum  平台订单号
     * @param  string  $playDate  预约日期
     * @param  int  $sid  供应商id
     * @param  int  $venueId  场馆id
     * @param  int  $roundId  场次id
     * @param  int  $opId  操作人员
     * @param  int  $source  来源
     * @param  string  $seatIds  选座的座位id 多个以逗号隔开
     * @param  int  $isSms  是否短信预约 0否1是
     *
     * @return  array
     */
    public function showReserve($orderNum, $playDate, $sid, $venueId, $roundId, $opId, $source, $seatIds = '', $isSms = 0): array
    {
        if (!$orderNum || !$playDate || !$venueId || !$roundId) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'order_num' => $orderNum,
            'play_date' => $playDate,
            'venue_id'  => $venueId,
            'round_id'  => $roundId,
            'seat_ids'  => $seatIds,
            'source'    => $source,
            'sid'       => $sid,
            'op_id'     => $opId,
            'is_sms'    => $isSms,
        ];

        $result = $this->ExternalCodeCall('Merchant/Order/showReserve', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 演出改签
     *
     * @param  string  $orderNum  平台订单号
     * @param  string  $changeDate  预约日期
     * @param  int  $sid  供应商id
     * @param  int  $aid  上级供应商id
     * @param  int  $venueId  场馆id
     * @param  int  $roundId  场次id
     * @param  int  $opId  操作人员
     * @param  int  $source  来源
     * @param  int  $isSms  是否短信预约 0否1是
     * @param  int  $isAudit  是否改签审核 0否1是
     * @param  array  $seatIdArr  选座的座位id数组
     *
     * @return  array
     */
    public function showChange($orderNum, $changeDate, $sid, $aid, $venueId, $roundId, $opId, $source, $isSms, $isAudit, $seatIdArr = []): array
    {
        if (!$orderNum || !$changeDate || !$venueId || !$roundId || !$aid) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'order_num'   => $orderNum,
            'change_date' => $changeDate,
            'venue_id'    => $venueId,
            'aid'         => $aid,
            'round_id'    => $roundId,
            'seat_ids'    => implode(',', $seatIdArr),
            'is_sms'      => $isSms,
            'is_audit'    => $isAudit,
            'source'      => $source,
            'sid'         => $sid,
            'op_id'       => $opId,
        ];

        $result = $this->ExternalCodeCall('Merchant/Order/showChange', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取场馆全景座位图
     *
     * @param  int  $applyDid  供应商id
     * @param  int  $venueId  场馆id
     * @param  int  $roundId  场次id
     * @param  int  $needSummary  是否获取汇总数据
     *
     * @return  array
     */
    public function getPanoramicSeatsInfo($applyDid, $venueId, $roundId, $needSummary = 0): array
    {
        if (!$venueId || !$applyDid || !$roundId) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'apply_did'    => $applyDid,
            'venue_id'     => $venueId,
            'round_id'     => $roundId,
            'need_summary' => $needSummary,
        ];

        $result = $this->ExternalCodeCall('Merchant/RoundSeat/getPanoramicSeatsInfo', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 场次库存变更通知
     *
     * @param  int  $memberId  商户id
     * @param  int  $opId  操作人id
     * @param  int  $roundId  场次id
     * @param  array  $noticeInfo  通知内容
     *
     * @return  array
     */
    public function showRoundStorageChangeNotice($memberId, $opId, $roundId, $noticeInfo): array
    {
        if (!$memberId || !$opId || !$roundId || !$noticeInfo) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'member_id'   => $memberId,
            'op_id'       => $opId,
            'round_id'    => $roundId,
            'notice_info' => $noticeInfo,
        ];

        $result = $this->ExternalCodeCall('Merchant/RoundSeat/showRoundStorageChangeNotice', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 演出下单成功后通知
     *
     * @param  string  $orderNum  平台订单号
     * @param  int  $venueId  场馆id
     *
     * @return  array
     */
    public function orderNotice($orderNum, $venueId): array
    {
        if (!$orderNum || !$venueId) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'order_num' => $orderNum,
            'venue_id'  => $venueId,
        ];

        $result = $this->ExternalCodeCall('Merchant/Order/orderNotice', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 演出下单成功后通知【批量 - 刷数据用】
     *
     * @param  array  $batchNoticeInfo  需要通知的场馆订单信息数组 场馆id为key 订单号数组为value
     *
     * @return  array
     */
    public function orderNoticeBatch($batchNoticeInfo): array
    {
        if (!$batchNoticeInfo) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'batch_notice_info' => $batchNoticeInfo,
        ];

        $result = $this->ExternalCodeCall('Merchant/Order/orderNoticeBatch', $data, 'show_manage');

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 处理演出时间
     * @param  string  $showTime  演出时间  2024-12-31 18:00-23:59
     *
     * @return array|string[]
     */
    public static function showTimeHandle($showTime)
    {
        if (!$showTime) {
            return [];
        }
        //取到演出的具体时分
        $showDate = explode(' ', $showTime)[0];
        $showHour = explode(' ', $showTime)[1];
        //再解析下场次的结束时间
        $showEndTime  = explode('-', $showHour);
        if (!isset($showTime[1])) {
            $showEndTime = explode('~', $showHour);
        }

        $beginHour = $showEndTime[0];
        $endHour   = $showEndTime[1] ?? $showEndTime[0];

        return ['round_date' => $showDate, 'begin_time' => $beginHour, 'end_time' => $endHour];
    }

    /**
     * 查询用户多个日期的可用库存
     *
     * @param  int  $tid  门票id
     * @param  int  $sid  供应商id
     * @param  int  $fid  分销商id
     * @param  int  $venueId  场馆id
     * @param  int  $zoneId  分区id
     * @param  int  $sortId  场次排序id
     * @param  array  $roundIdArr  场次id数组
     * @param  string  $startDate  库存开始日期
     * @param  string  $endDate  库存结束日期
     *
     * @return  array
     */
    public function getShowDistributionStorageBatch($tid, $sid, $fid, $venueId, $zoneId = 0, $sortId = 0, $roundIdArr = [], $startDate = '', $endDate = ''): array
    {
        if (!$tid || !$fid || !$sid || !$startDate || !$endDate || !strtotime($startDate) || !strtotime($endDate) || $endDate < $startDate) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'tid'          => $tid,
            'sid'          => $sid,
            'fid'          => $fid,
            'venue_id'     => $venueId,
            'zone_id'      => $zoneId,
            'sort_id'      => $sortId,
            'round_id_arr' => $roundIdArr,
            'start_date'   => $startDate,
            'end_date'     => $endDate,
        ];

        $result = $this->ExternalCodeCall('Merchant/DistributionStorage/getShowDistributionStorageBatch', $data, 'show_manage');
        
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 判断商户是否有开通选座上限至2w
     *
     * @param  int  $applyDid  商家id
     *
     * @return bool
     */
    public function isOpenLimitTwoMillion(int $applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $resultRes = (new \Business\Admin\ModuleConfig())->getOpenModuleBySidAndAppKey([$applyDid], 'show_seat_limit_two_million');
        if($resultRes['code'] != 200) {
            return false;
        }
        return !empty($resultRes['data']);
    }
}