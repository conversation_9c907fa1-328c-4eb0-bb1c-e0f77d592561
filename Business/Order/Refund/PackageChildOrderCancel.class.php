<?php
/**
 * 套票子票取消逻辑拆分
 */
namespace Business\Order\Refund;

use Business\Admin\ModuleConfig;
use Business\Order\RefundMoney;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\OrderConst;
use Model\Order\OrderTools;
use Business\Order\RefundApprovalCenterService\RefundApprovalService;
use Model\Order\OrderHandler;
use Business\Product\Ticket as TicketBiz;
use Model\TradeRecord\OrderRefund;

class PackageChildOrderCancel extends AbstractBaseController
{
    /**
     * 取消子票
     * @param $orderNum
     * @param $subOrderList
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function cancelPackageChildOrder($orderNum, $subOrderList, $params)
    {
        pft_log('order_refund/debug', json_encode(['cancelPackageChildOrder_new', $subOrderList, $params, $orderNum]));
        $cancelNum = $params['cancelNum'];
        $cancelType = $params['cancelType'];
        $totalNum = $params['totalNum'];
        $opId = $params['opId'];
        $personIndex = $params['personIndex'];
        $isForceCancel = $params['isForceCancel'];
        $packageOriginCancelChannel = $params['cancelChannel'];
        $personIdList = $params['personIdList'];
        $isRollBack = $params['isRollBack'];
        $approvalParams = $params['approvalParams'] ?? [];
        $pftSerialNumber = $params['pftSerialNumber'];
        $personInfoList = $params['personInfoList'];
        $isSyntaxRollBack = $params['isSyntaxRollback'];
        //审批流特有参数$reqSerialNumber、$moreData
        $reqSerialNumber = $params['reqSerialNumber'] ?? ''; //非审批流默认值
        $moreData = [];
        if(isset($approvalParams['approvalNodeInfo']['processInstanceId'])){
            $moreData['mainApprovalCode'] = $approvalParams['approvalNodeInfo']['processInstanceId'];
        }
        //无子票
        if (!$subOrderList) {
            return $this->orderReturn(200, '子票还没有生成，也直接取消掉去');
        }
        //检查子票余额是否够可用退票
        $moreMoneyCanUse = $this->checkRefundSubOrderMoney($subOrderList, $cancelNum, $cancelType);
        if (!$moreMoneyCanUse) {
            return $this->orderReturn(0, '套票中的子票退票的时候，上级余额不足', ['err_code' => OrderConst::err222]);
        }
        //子票状态判断
        $isAllowPartUsedRefund = $this->checkSubOrderStatusUsedPart($subOrderList, $isForceCancel);
        if (!$isAllowPartUsedRefund) {
            return $this->orderReturn(0, '部分子票已经验证，不能退票', ['err_code' => OrderConst::err2023]);
        }
        //将已经取消的订单返回到外层
        $cancelOrderList = [];
        foreach ($subOrderList as $subInfo) {
            //未使用和过期的订单进行取消
            $allowStatus = [0, 2, CommonOrderStatus::WAIT_APPOINTMENT_CODE, CommonOrderStatus::WAIT_PRINTTICKET_CODE];
            if ($this->isAllowPartUsedRefund($subInfo['apply_did'])) {
                $allowStatus[] = 7;
            }
            if (in_array($subInfo['status'], $allowStatus)) {
                $subOrdernum = $subInfo['ordernum'];
                $subTotalNum = $subInfo['tnum'];

                //取消的子票数 - 按比例计算子票的取消数量
                /**
                 * @var $cancelNum int 本次取消的主票数量
                 * @var $subTotalNum int 当前子票剩余总数量
                 * @var $totalNum int 主票的总剩余数量
                 * fixme 这个算法，一旦主票子票数量有提前取消导致数量不对等，就永远会计算错误
                 * 另外 $subCancelNum的初次计算和$realityNum的计算，没有区别，乘除法中因子不变谁先谁后带来的结果是一致的
                 */
                $subCancelNum = ceil(($cancelNum * $subTotalNum) / $totalNum );
                $subCancelNum = $subCancelNum > $subTotalNum ? $subTotalNum : $subCancelNum;
                //实际的退票数
                $realityNum = ($cancelNum / $totalNum) * $subTotalNum;

                $cancelRemarkArr  = ['remark' => '主票取消/修改，子票自动取消/修改'];
                $cancelSiteArr    = [];
                $cancelPersonArr  = [];
                $cancelSpecialArr = [
                    'is_cancel_sub' => false,
                    //和之前退票逻辑一致，退子票的时候不需要进行退票审核判断
                    'is_need_audit' => false,
                    'is_rollback'   => $isRollBack,
                    'is_syntax_rollback'   => $isSyntaxRollBack, //是否系统原因导致的回滚，需要退回码费等特殊款项
                    'packageOriginCancelChannel' => $packageOriginCancelChannel,
                    'packageOriginIsForceCancel' => $isForceCancel,
                ];
                if (bccomp($realityNum,$cancelNum,2) === 0) {   //判断下如果子票数量等于主票的时候就按主票要取消的idx去取消
                    $cancelPersonArr['person_index'] = $personIndex;
                } elseif ($personIndex && bccomp(ceil($realityNum),$cancelNum,2) === 0){
                    $cancelPersonArr['person_index'] = $personIndex;
                }
                //比例为1：n的时候，需要重新获取子票的person_info_list,使用上面重新计算的person_id_list去获取
                $cancelPersonArr['person_id_list'] = $personIdList;
                $cancelPersonArr['person_info_list'] = $personInfoList;
                /**
                 * 当退票中的子票需实名（不管是一人一票还是一票种一票），游客实名制数量小于门票数量时需要去置换实名信息，这个时候会置换不出来，需要转换成子票按数量退
                 * 套票的主票和子票无绑定关系，转化成按数量时，因为又存在实名制身份证退，所以统一转化成按idx退，避免查一次idcard再查一次无idcard
                 * 套票可包含覆盖场景
                 * 1.子票A需游客实名 主票数量：子票数量=1：1
                 * 2.子票B无需游客实名 主票数量：子票数量=1(n)：2(m)
                 *
                 * 置换实名信息时，如果证件类型非身份证，需要额外增加person_info_list数组
                 */
                if (count($personIdList)!=$subCancelNum) {
                    $personRes = self::sonTicketRealRefundData($subOrdernum, $pftSerialNumber, $personIdList, $subCancelNum);
                    pft_log('order_refund/debug',
                        json_encode(['sonTicketRealRefundData', $personRes, $subOrdernum, $pftSerialNumber, $personIdList, $subCancelNum, $realityNum, $cancelNum, $subTotalNum, $totalNum, $cancelPersonArr]));
                    if ($personRes['code'] != 200) {
                        return $this->returnData($personRes['code'], $personRes['msg']);
                    }
                    //判断主票的按idx退的idx，在子票中是否不存在，不存在则子票转换为数量退，一般出现为子票是一票种一票
                    //因为判断的是person_index键，不是判断的$personIndex值，所以会受
                    //主票按idx退的时候，$personIdList肯定是为空的，这个参数是原始的最外层接口发起的值
                    if (isset($personRes['data']['idxRes']) && isset($cancelPersonArr['person_index'])) {
                        $sonTicketIndex = [];
                        foreach ($personRes['data']['idxRes'] as $personItem) {
                            if (is_array($cancelPersonArr['person_index'])) {
                                if (in_array($personItem['idx'], $cancelPersonArr['person_index'])) {
                                    $sonTicketIndex[] = $personItem['idx'];
                                }
                            } else {
                                if ($personItem['idx'] == $cancelPersonArr['person_index']) {
                                    $sonTicketIndex[] = $personItem['idx'];
                                }
                            }
                        }
                        $cancelPersonArr['person_index'] = $sonTicketIndex;
                        if (empty($sonTicketIndex)) {
                            $cancelPersonArr['person_info_list'] = [];
                        }
                    }
                    //如果实名制数量小于退票数量，则转换为按数量退的按idx退
                    if (isset($personRes['data']['personIdList']) && !empty($personRes['data']['personIdxList']) && count($personRes['data']['personIdList']) < $subCancelNum) {
                        $cancelPersonArr = [
                            'person_index'=>$personRes['data']['personIdxList']
                        ];
                    } else {
                        $cancelPersonArr['person_id_list'] = $personRes['data']['personIdList'];
                        if (!empty($personRes['data']['personInfoList'])) {
                            $cancelPersonArr['person_info_list'] = $personRes['data']['personInfoList'];
                        }
                    }
                }
                //调用退票
                $subRefundResult = $this->cancelPackageChildOrderCommon($subOrdernum, $subCancelNum, [
                    'opId'  =>  $opId,
                    'cancelType'  =>  $cancelType,
                    'reqSerialNumber'  =>  $reqSerialNumber,
                    'cancelRemarkArr'  =>  $cancelRemarkArr,
                    'cancelSiteArr'  =>  $cancelSiteArr,
                    'cancelPersonArr'  =>  $cancelPersonArr,
                    'cancelSpecialArr'  =>  $cancelSpecialArr,
                    'moreData'  =>  $moreData,
                ]);
                if ($subRefundResult['code'] == 200) {
                    $cancelOrderList[] = $subOrdernum;
                } else if ($subRefundResult['code'] == 1095) {
                    //退票审核提交成功
                    if ($isForceCancel || $isRollBack) {
                        //无需处理：下单回滚退和强制退；需要跳过拦截，跳过卡住退票
                        continue;
                    } else {
                        return $this->orderReturn(1095, '子票提交退票审核', ['err_code' => $subRefundResult['code'], 'cancel_order' => $cancelOrderList]);
                    }
                } else {
                    //如果是强制取消和下单回滚的话，就不去管他是否取消成功
                    //其他情况下，直接就不让取消子票了，顺便把前面已经取消的订单返回
                    if ($isForceCancel || $isRollBack) {
                        continue;
                    } else {
                        $errMsg = $subRefundResult['msg'] ?? '';
                        if (empty($errMsg)) {
                            $errMsg = $subRefundResult['data']['err_msg'] ?? '';
                        }
                        $errCode = $subRefundResult['data']['err_code'] ?? $subRefundResult['code'];
                        $logErrorMsgContent = $subRefundResult['data']['log_err_msg'] ?? '';
                        if (empty($logErrorMsgContent)) {
                            $logErrorMsgContent = $subRefundResult['data']['inner_msg'] ?? '';
                        }
                        $logErrorMsg = "子票【{$subOrdernum}】退票失败，套票不能退票-msg:{$errMsg} ;". $logErrorMsgContent;
                        $errorMsg = "子票【{$subOrdernum}】退票失败，套票不能退票".$errMsg;
                        if ($errCode == OrderConst::err917) {
                            $errorMsg = '套票子票部分退票目前仅支持操作一个门票码,子票订单号:'.$subOrdernum;
                        }
                        return $this->orderReturn(0, $errorMsg,
                            ['err_code' => $errCode, 'cancel_order' => $cancelOrderList, 'log_err_msg'=>$logErrorMsg]);
                    }
                }
            }
        }

        //可以直接取消
        return $this->orderReturn(200, '所有子票已全部取消成功', $cancelOrderList);

    }

    /**
     * 调用common取消方法取消子票
     * @param $subOrderNum
     * @param $subCancelNum
     * @param $params
     * @return array
     */
    public function cancelPackageChildOrderCommon($subOrderNum, $subCancelNum, $params)
    {
        $cancelType = $params['cancelType'];
        $opId = $params['opId'];
        $cancelChannel    = 23;
        $reqSerialNumber  = $params['reqSerialNumber'] ?? '';
        $cancelRemarkArr  = ['remark' => '主票取消/修改，子票自动取消/修改'];
        $cancelSiteArr    = [];
        $cancelPersonArr  = $params['cancelPersonArr'];
        $cancelSpecialArr = $params['cancelSpecialArr'];
        $moreData = $params['moreData'] ?? [];
        $refundBiz = new \Business\Order\BaseRefund();
        $subRefundRes = $refundBiz->commonRefund($subOrderNum, $subCancelNum, $opId, $cancelChannel, $cancelType,
            $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);

        //记录日志
        $request      = [
            'ordernum'        => $subOrderNum,
            'cancelNum'       => $subCancelNum,
            'opId'            => $opId,
            'cancelChannel'   => $cancelChannel,
            'cancelType'      => $cancelType,
            'reqSerialNumber' => $reqSerialNumber,
            'remark'          => $cancelRemarkArr,
            'site'            => $cancelSiteArr,
            'person'          => $cancelPersonArr,
            'special'         => $cancelSpecialArr,
        ];
        $word = json_encode([
            'key' => 'cancelPackageChildOrderCommon',
            'req' => $request,
            'methodParams' => $params,
            'remark' => '取消套票子票',
            'res' => $subRefundRes,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('order_refund/debug', $word);

        return $subRefundRes;
    }

    /**
     * 获取子票订单detail
     * @param $subOrderList
     * @return array
     */
    public function getSubOrderDetailList($subOrderList)
    {
        $result = [];
        $orderArr     = array_column($subOrderList, 'ordernum');
        $checkRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail','getLinkOrdersInfoAndRemoveOrders', [$orderArr]);
        if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
            $orderDetails = $checkRes['data'];
            $result = array_key($orderDetails, 'ordernum');
        }

        return $result;
    }

    /**
     * 处理数据，将订单详情中的aids_money合入subOrderList中
     * @param $subOrderList
     * @return mixed
     */
    public function orderDetailsToSubOrderList($subOrderList)
    {
        $subOrderDetailList = $this->getSubOrderDetailList($subOrderList);
        foreach ($subOrderList as $key => $item) {
            $tmpOrdernum  = $item['ordernum'];
            $tmpAidsMoney = isset($subOrderDetailList[$tmpOrdernum]) ? $subOrderDetailList[$tmpOrdernum]['aids_money'] : '';
            $subOrderList[$key]['aids_money'] = $tmpAidsMoney;
            //这里可以扩展后续需要加其他属性
        }
        return $subOrderList;
    }

    /**
     * 校验子票的退票余额，和账户中可用余额是否足够
     * @param $subOrderList
     * @param $cancelNum
     * @param $cancelType
     * @return bool
     */
    public function checkRefundSubOrderMoney($subOrderList, $cancelNum, $cancelType)
    {
        $subOrderDetailList = $this->orderDetailsToSubOrderList($subOrderList);
        $refundMoneyBiz = new RefundMoney();
        $isSelfReceive  = false; //子票默认都不是独立收款
        foreach ($subOrderDetailList as $subInfo) {
            $subOrdernum   = $subInfo['ordernum'];
            $isMoneyEnough = $refundMoneyBiz->checkRefundMoney($subOrdernum, $subInfo, $cancelNum,
                $isSelfReceive, true, false, $cancelType);

            //如果子票的余额不够
            if (!$isMoneyEnough) {
                //日志记录
                pft_log('order_refund/debug', json_encode([
                    'checkRefundMoney',
                    'package_sub',
                    $isMoneyEnough,
                    $subInfo,
                    $cancelNum,
                    $isSelfReceive,
                ]));
                return false;
            }
        }
        return true;
    }

    /**
     * 子票部分使用是否可退过滤
     * @param $subOrderList
     * @param $isForceCancel
     * @return bool
     */
    public function checkSubOrderStatusUsedPart($subOrderList, $isForceCancel)
    {
        if ($isForceCancel) {
            return true;
        }
        foreach ($subOrderList as $subOrder) {
            //部分账号允许子票部分使用时还可退
            if ($this->isAllowPartUsedRefund($subOrder['apply_did']) && $subOrder['status'] == 7) {
                continue;
            }

            if (in_array($subOrder['status'], [1, 7])) {
                //如果已经有被验证的订单话，直接返回
                return false;
            }
        }
        return true;
    }

    /**
     * 获取允许子票部分使用退的供应商fid
     * @param int $applyDid
     * @return bool
     */
    private function isAllowPartUsedRefund(int $applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'special_pack_check', 60);
        if($resultRes['code'] != 200) {
            return false;
        }
        return !empty($resultRes['data']);
    }

    /**
     * 获取子票退票所使用的游客数据
     * 要置换实名制数量小于退票数量时，置换为按数量退(直接指定idx退即可)
     * @param $orderNum
     * @param $pftSerialNumber
     * @param $personIdList
     * @param $modifyNum
     * @return array
     * @throws \Exception
     */
    public function sonTicketRealRefundData($orderNum,$pftSerialNumber,$personIdList,$modifyNum)
    {
        $orderModel   = new OrderTools('localhost');
        $tmpOrderInfo = $orderModel->getInfoForCancel($orderNum);
        $orderInfo = $tmpOrderInfo['order_info'];
        if (empty($orderInfo)) {
            return $this->orderReturn(0, "订单数据不存在", ['err_code' => OrderConst::err11]);
        }
        $ticketBiz = new TicketBiz();
        $tid = $orderInfo['tid'];
        $ticketRes = $ticketBiz->getListForOrderNew($tid);
        if (!$ticketRes['ticket_info']) {
            return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
        }

        pft_log('order_refund/debug',json_encode(['sonTicketRealNameForThird', $orderNum, $pftSerialNumber, $personIdList, $modifyNum]));
        $idxRes = [];
        if(empty($personIdList) && $pftSerialNumber){
            $refundModel = new OrderRefund();
            $arrPftSerialNumber = explode('_',$pftSerialNumber);
            $refundId = reset($arrPftSerialNumber);
            $refundInfo  = $refundModel->getRefundJournalById($refundId);
            if(empty($refundInfo)){
                return $this->orderReturn(0, '主订单退票数据不存在', ['err_code' => OrderConst::err1051]);
            }
            $auditDetails = $refundInfo['details']??[];
            if(empty($auditDetails)){
                return $this->orderReturn(0, '主订单数据退票数据组缺失', ['err_code' => OrderConst::err1051]);
            }
            $auditDetailsArr = json_decode($auditDetails,true);
            $refundIdxArr = isset($auditDetailsArr['refundIdx']) ? $auditDetailsArr['refundIdx'] : [$auditDetailsArr['personIndex']];
            if(empty($refundIdxArr)){
                return $this->orderReturn(0, '主订单数据退票数据缺失', ['err_code' => OrderConst::err1051]);
            }
            list($personIdList,$idxRes) = self::getSonRealRefundDataWithOrderNumFilterMainRefundIdx($orderNum,$refundIdxArr, $modifyNum);
            if(count($personIdList)==0){
                return $this->returnData(200 ,'不需要置换',['personIdList'=>$personIdList, 'personInfoList'=>[], 'idxRes'=>$idxRes]);
            }
        }
        pft_log('order_refund/debug',json_encode(['sonTicketRealNameForThird', 'getSonRealRefundDataWithOrderNumFilterMainRefundIdx', $orderNum, $pftSerialNumber, $personIdList, $modifyNum]));
        if($modifyNum>count($personIdList)){
            //获取未使用票数据，根据身份优先匹配【套票主票不支持撤销撤改，即便部分情况下支持也不联动子票】
            //如果是一票一证的门票，就需要获取未使用的码
            $fixObj = new HandleTourist();
            $leftTouristArr = $fixObj->getOrderLeftTourist($orderNum, $orderInfo);
            $availableIdCardArr = $fixObj->getAvailableIdNumberArr($leftTouristArr);
            pft_log('order_refund/debug',json_encode(['sonTicketRealNameForThird_getAvailableIdNumberArr', $availableIdCardArr, $personIdList, $orderNum, $modifyNum, $personIdList]));
            //校验传入进来的personId合法性
            $handlerModel = new OrderHandler();
            foreach ($personIdList as $personId) {
                //如果传入的证件号id不在可退门票列表中，则拦截报错
                if(!in_array($personId,$availableIdCardArr)){
                    if ( in_array($orderInfo['status'], [2,11]) ) {  //过期和待出票的订单状态，要获取状态为4的
                        $checkStateArr = [0, 4];
                    } else {
                        $checkStateArr = [0];
                    }
                    $allAvailableTouristArr = $handlerModel->getOrderTouristInfo($orderNum, 0, $personId, $checkStateArr);
                    if(empty($allAvailableTouristArr)){
                        return $this->returnData(257 ,"三方系统申请退票失败:该证件不可退{$personId}",$personIdList);
                    }
                }
            }
            //区分已经指定的idCard数据和未指定的idCard数据
            $refundIdCardArr = $otherIdCardArr = [];
            foreach ($leftTouristArr as $touristItem) {
                if (in_array($touristItem['idcard'], $personIdList)) {
                    $refundIdCardArr[] = $touristItem;
                } else {
                    $otherIdCardArr[] = $touristItem;
                }
            }
            $resultIdCardArr = $refundIdCardArr;
            $diffNum = $modifyNum - count($personIdList);
            $diffArr = array_slice($otherIdCardArr,0, $diffNum);
            $resultIdCardArr = array_merge($resultIdCardArr, $diffArr);

            $personIdxList = [];
            $personInfoList = [];
            $personIdCardList = [];
            foreach ($resultIdCardArr as $itemTourist) {
                //无实名，按idx数量退
                $personIdxList[] = $itemTourist['idx'];
                //如果证件号为空，则直接跳过
                if (empty($itemTourist['idcard'])) {
                    continue;
                }
                //这里过滤出来的证件号不会区分证件类型
                $personIdCardList[] = $itemTourist['idcard'];
                $personInfoList[] = [
                    "person_index"=>$itemTourist['idx'],
                    "idcard"=>$itemTourist['idcard'],
                    "idcard_type"=>$itemTourist['voucher_type'],
                    "ticket_code"=>$itemTourist['chk_code'],
                ];
            }
            return $this->returnData(200 ,'完成置换',['personIdList'=>$personIdCardList, 'personInfoList'=>$personInfoList, 'personIdxList'=>$personIdxList]);
        } elseif ($modifyNum==count($personIdList)) {
            return $this->returnData(200 ,'不需要置换',['personIdList'=>$personIdList, 'personInfoList'=>[], 'personIdxList'=>[]]);
        } else {
            return $this->returnData(257, "三方系统申请退票失败:【{$orderNum}】修改数量与身份证不一致(修改数量小于传入的证件数量)");
        }
    }

    /**
     * 获取子票退票的游客表数据，
     * 以主票idx相同的子票idx为取消的门票码数据为第一优先级，剩下的使用默认填充
     * 同时返回idcard不为空数据
     * @param $orderNum
     * @param $refundIdxArr
     * @param $cancelNum
     * @return array
     */
    public function getSonRealRefundDataWithOrderNumFilterMainRefundIdx($orderNum,$refundIdxArr, $cancelNum)
    {
        //针对发起主票审核时没有填写身份证的情况 此时子票应与主票取消身份证一致
        $subOrderModel = new \Model\Order\SubOrderQuery();
        $field = 'orderid,id,tourist,idcard,mobile,print_state,voucher_type,check_state,idx, chk_code';
        $idxRes = $subOrderModel->getInfoInTouristByOrder($orderNum, $field, 2, [], '', true);
        if (empty($idxRes)) {
            pft_log('order_refund/debug',
                json_encode(['SonRealRefundData', $idxRes, $refundIdxArr,$orderNum]));
            return $this->orderReturn(0, "退票参数错误，无游客信息", ['err_code' => OrderConst::err1]);
        }
        //子票对应主票的idx如果已经使用，则置换可用的idx
        $refundIdxArrIdxRes = [];
        $otherIdxRes = [];
        foreach ($idxRes as $item){
            if (in_array($item['idx'], $refundIdxArr)){
                if ($item['check_state'] != 2) {
                    $refundIdxArrIdxRes[]= $item;
                }
            } else {
                $otherIdxRes[] = $item;
            }
        }
        $result = $refundIdxArrIdxRes;
        if (count($refundIdxArrIdxRes) != count($refundIdxArr)) {
            array_push($result, array_slice($otherIdxRes, 0, $cancelNum - count($refundIdxArrIdxRes)));
        }
        $personIdList = array_filter(array_column($result, 'idcard'));
        return [$personIdList,$idxRes];
    }
}
