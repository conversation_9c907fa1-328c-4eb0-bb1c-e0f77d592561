<?php

namespace Business\Order\Refund;

use Library\Constants\Order\PrintStatus\OrderIfPrintStatus;
use Library\Constants\OrderConst;
use Library\Constants\Product\LandProductRefundRuleEnum;
use Library\Constants\Ticket\TicketIfPrintRefundRule;

class CommonRefundStrategy
{
    public function ValidateChangeCheckCancel($cancelChannel, $realCancelChannel, $opId, $applyDid, $saleId): array
    {
        $isChangeCheck = static::getIsChangeCheckCancel($cancelChannel, $realCancelChannel);
        if ($isChangeCheck) {
            $memberInfo = static::getMemberInfoById($opId);
            if (in_array($opId, [$applyDid, $saleId]) || ($memberInfo['dtype'] == 2 && in_array($memberInfo['account'], [$applyDid, $saleId]))) {
                return ['code' => 200, 'msg' => '取票退票规则-改单验证'];
            }
            return ['code' => OrderConst::err4007, 'msg' => '只有供应商和资源账号才可以操作改单验证', 'log_err_msg'=>'只有供应商和资源账号才可以操作改单验证'];
        }
        return [];
    }

    /**
     * 随时退
     * @param $paramDto CheckPrintRefundRuleParamDTO
     * @return array
     */
    public function anyWayRefund(CheckPrintRefundRuleParamDTO $paramDto)
    {
        $validateRes = $this->ValidateChangeCheckCancel($paramDto->cancel_channel, $paramDto->real_cancel_channel, $paramDto->opId, $paramDto->apply_did, $paramDto->saler_id);
        if (!empty($validateRes['code'])) {
            return $validateRes;
        }
        $applyDid = $paramDto->apply_did;
        $cancelOpId = static::getCancelMemberOpId($paramDto->cancel_member_id);

        //端午期间去除这个日志
        $filterLogStartTime = strtotime('2025-05-31 00:00:00');
        $filterLogEndTime   = strtotime('2025-06-02 23:59:59');
        if (time() < $filterLogStartTime || time() > $filterLogEndTime) {
            pft_log('order_refund/debug', json_encode(['anyWayRefund_new', $cancelOpId, $paramDto]));
        }

        //套票子票以打包方的身份退票
        if ($paramDto->origin_operator_id == $cancelOpId && $cancelOpId == $applyDid && $cancelOpId == $paramDto->buy_id && $paramDto->is_pack ==2) {
            return ['code' => 200, 'msg' => '随时退-取票退票规则[取票后不可退]-自供自销允许退[套票子票]'];
        }
        //判断自供自销，还会包含两级计调下单，当大于3级计调下单时，不会进来
        if ($cancelOpId == $applyDid && $cancelOpId == $paramDto->buy_id && $paramDto->is_pack != 2) {
            return ['code' => 200, 'msg' => '随时退-取票退票规则[不限]-自供自销允许退'];
        }
        //取票退票规则为不限
        if (empty($paramDto->print_refund_rule)) {
            return $this->checkOrderPrintRuleNoLimitAnyWayRefund($paramDto);
        }
        /******  因为需求核心诉求要区分角色相反计算，保证供应商和真实购买者有一个可退，所以需要以用户角度为第一视觉，默认都计算末级购买者；【购买者身份正向cancel值计算】 start *****/
        $orderMemberCancel = true;
        $orderMemberCancelCode = 200;
        $orderMemberCancelMsg = $logUserTip = '随时退-取票后不可退规则校验通过';

        //取票退票规则配置为：部分取票可退，仅已取票不可退
        if ($paramDto->print_refund_rule == TicketIfPrintRefundRule::ORDER_PART_PRINT_CAN_REFUND) {
            if (in_array($paramDto->order_status, [0, 2, 7])
                && $paramDto->order_ifprint == OrderIfPrintStatus::ALL_PRINTED
            ) {
                $orderMemberCancel = false;
                $orderMemberCancelMsg = '随时退-未使用/部分使用/已过期-全部取票-部分取票可退，仅已取票不可退';
                $logUserTip = '已取票的订单不允许退票';
                $orderMemberCancelCode = OrderConst::err1413;
            }
        }
        //取票退票规则配置为：部分取票、已取票都不可退
        if ($paramDto->print_refund_rule == TicketIfPrintRefundRule::ORDER_PRINT_NOT_CAN_REFUND) {
            if (in_array($paramDto->order_status, [0, 2, 7])
                && in_array($paramDto->order_ifprint, [OrderIfPrintStatus::ALL_PRINTED, OrderIfPrintStatus::PART_PRINTED])
            ) {
                $orderMemberCancel = false;
                $orderMemberCancelMsg = '随时退-未使用/已使用/已过期-全部取票-部分取、已取票都不可退';
                $logUserTip = '已取票的订单不允许退票';
                $orderMemberCancelCode = OrderConst::err1413;
            }
        }
        /******  【购买者身份正向cancel值计算】 end *****/
        $realCancelMsg = $orderMemberCancelMsg;
        $realCancelCode = $orderMemberCancelCode;
        $realCancel = $orderMemberCancel;
        /*****       当前操作人==末级分销商==真实购买者       *****/
        if ($cancelOpId == $paramDto->buy_id && $cancelOpId ==$paramDto->final_aid) {
            return ['code' => $realCancelCode, 'msg' => $logUserTip, 'log_err_msg' => $realCancelMsg];
        }
        /*****      中间级分销商： 当前操作人!=顶级供应商 && 当前操作人!=真实购买者  &&  当前操作人!= 末级分销商 &&  当前操作人!=景区资源账号 *****/
        if ($cancelOpId != $paramDto->first_apply_did && $cancelOpId != $paramDto->buy_id && $cancelOpId !=$paramDto->final_aid && $cancelOpId != $paramDto->saler_id) {
            $orderMemberCancel = false;
        }
        /*****       当前操作人==顶级供应商       *****/
        //当前操作人==顶级供应商  【供应商和购买者取反逻辑】
        if ($cancelOpId == $paramDto->first_apply_did && $cancelOpId !=$paramDto->final_aid) {
            $realCancel = !$orderMemberCancel;
        }
        /*****       当前操作人=景区资源账号       *****/
        if ($cancelOpId == $paramDto->saler_id) {
            $realCancel = !$orderMemberCancel;
        }
        //供应商和下单人都可退
        if ($paramDto->order_status == 2 &&
            ( (in_array($paramDto->order_ifprint, [OrderIfPrintStatus::UN_PRINTED, OrderIfPrintStatus::PART_PRINTED])) && $paramDto->print_refund_rule == TicketIfPrintRefundRule::ORDER_PART_PRINT_CAN_REFUND)
            || ($paramDto->order_ifprint == OrderIfPrintStatus::UN_PRINTED && $paramDto->print_refund_rule == TicketIfPrintRefundRule::ORDER_PRINT_NOT_CAN_REFUND)
        ) {
            $realCancel = true;
        }
        /*****       计调下单单独判断       *****/
        //计调下单-大于2级分销(即最少ABC的链路)，B给C下单，此处判断定位为角色B
        if ($cancelOpId == $paramDto->buy_id && $cancelOpId !=$paramDto->final_aid && in_array(intval($paramDto->real_order_mode), [16, 10])) {
            $realCancel = $orderMemberCancel;
        }
        //计调下单-大于2级分销(即最少ABC的链路)，C给B下单，此处判断定位为角色C
        if (in_array($paramDto->real_order_mode, ['16', '10']) && $cancelOpId ==$paramDto->final_aid && $cancelOpId != $paramDto->buy_id) {
            //计调下单-末级都不可退(非实际下单人)
            $realCancel = false;
        }
        if (empty($realCancel)) {
            $realCancelCode = $orderMemberCancelCode;
            if ($orderMemberCancelCode == 200) {
                $realCancelCode = OrderConst::err4006;
            }
        } else {
            $realCancelCode = 200;
            $realCancelMsg = '随时退-取票后不可退规则校验通过';
            return ['code' => $realCancelCode, 'msg' => $realCancelMsg, 'log_err_msg' => $realCancelMsg];
        }

        return ['code' => $realCancelCode, 'msg' => '下单人才可操作', 'log_err_msg' => $realCancelMsg];
    }

    /**
     * 随时退-取票退票规则为不限
     * @param $paramDto CheckPrintRefundRuleParamDTO
     * @return array
     */
    protected function checkOrderPrintRuleNoLimitAnyWayRefund(CheckPrintRefundRuleParamDTO $paramDto)
    {
        $tools = new Tools();
        $realAttr = $tools->getRealAttribute($paramDto->order_info, $paramDto->apply_did);
        $cancelOpId = $tools->getCancelMemberOpId($paramDto->cancel_member_id);
        pft_log('order_refund/debug', json_encode(['anyWayRefund_checkOrderPrintRuleNoLimitAnyWayRefund', $cancelOpId, $realAttr, $paramDto]));
        /******  因为需求核心诉求要区分角色相反计算，保证供应商和真实购买者有一个可退，所以需要以用户角度为第一视觉，默认都计算末级购买者；【购买者身份正向cancel值计算】 start *****/
        $orderMemberCancel = true;
        $orderMemberCancelMsg = $logUserTip = '随时退-取票退票规则[不限]-规则校验通过';
        $orderMemberCancelCode = 200;
        //订单状态为未使用/部分使用，已取票或部分取票都只限制ota购买渠道，末级购买者放行,最新的规则，不限的时候，末级购买者和ota都可以退
        /******  【购买者身份正向cancel值计算】 end *****/

        $realCancelMsg = $orderMemberCancelMsg;
        $realCancelCode = $orderMemberCancelCode;
        /*****       当前操作人==末级分销商==真实购买者       *****/
        if ($cancelOpId == $paramDto->buy_id && $cancelOpId == $paramDto->final_aid) {
            return ['code' => $realCancelCode, 'msg' => $logUserTip, 'log_err_msg' => $realCancelMsg];
        }

        /*****      中间级分销商： 当前操作人!=顶级供应商 && 当前操作人!=真实购买者  &&  当前操作人!= 末级分销商  &&  当前操作人!=景区资源账号 *****/
        if ($cancelOpId != $paramDto->first_apply_did && $cancelOpId != $paramDto->buy_id && $cancelOpId != $paramDto->final_aid && $cancelOpId != $paramDto->saler_id) {
            $orderMemberCancel = false;
        }

        /*****       当前操作人==顶级供应商       *****/
        //当前操作人==顶级供应商  【供应商和购买者取反逻辑】
        if ($cancelOpId == $paramDto->first_apply_did && $cancelOpId != $paramDto->final_aid) {
            $realCancel = !$orderMemberCancel;
            //末级购买者渠道是ota，供应商权限跟随ota下单权限
            if ($paramDto->real_order_mode == 20) {
                if (in_array($paramDto->order_status, [0, 7]) && $paramDto->order_ifprint >= 0) {
                    $realCancel = false;
                }
            }
        }
        //顶级供应商和末级购买者都可以退的场景；顶级供应商和末级购买者都不可退的场景在函数入口已经判断了
        if (in_array($cancelOpId, [$paramDto->buy_id, $paramDto->first_apply_did]) && $paramDto->order_status == 2) {
            $realCancel = true;
        }
        /*****       当前操作人=景区资源账号       *****/
        if ($cancelOpId == $paramDto->saler_id) {
            $realCancel = !$orderMemberCancel;
            //末级购买者渠道是ota，景区资源账号权限跟随ota下单权限
            if ($paramDto->real_order_mode == 20) {
                if (in_array($paramDto->order_status, [0, 7]) && $paramDto->order_ifprint >= 0) {
                    $realCancel = false;
                }
            }
        }
        //景区资源账号和末级购买者都可退
        if (in_array($cancelOpId, [$paramDto->buy_id, $paramDto->saler_id]) && $paramDto->order_status == 2) {
            $realCancel = true;
        }
        /*****       计调下单单独判断       *****/
        //计调下单-大于2级分销(即最少ABC的链路)，B给C下单，此处判断定位为角色B
        if ($cancelOpId == $paramDto->buy_id && $cancelOpId != $paramDto->final_aid && in_array($paramDto->real_order_mode, ['16', '10'])) {
            //计调下单都可退-实际付款人-大于2级分销
            $realCancel = $orderMemberCancel;
        }
        //计调下单-2级分销(即最少BC的链路)，B给C下单，此处判断定位为角色C
        if (in_array($paramDto->real_order_mode, ['16', '10']) && $cancelOpId == $paramDto->final_aid && $cancelOpId != $paramDto->buy_id) {
            //计调下单-末级都不可退(非实际下单人)
            $realCancel = false;
        }
        if (empty($realCancel)) {
            $orderMemberCancelCode = OrderConst::err4006;
            $realCancelMsg = '随时退-取票退票规则[不限]-下单人才可操作';
        } else {
            $orderMemberCancelCode = 200;
            $realCancelMsg = '随时退-取票后不可退规则校验通过';
        }
        return ['code' => $orderMemberCancelCode, 'msg' => '下单人才可操作', 'log_err_msg' => $realCancelMsg];
    }

    /**
     * 有条件退-取票退票规则为【不限】-有效期前X天内可退
     * @param $paramDto CheckPrintRefundRuleParamDTO
     * @return array
     */
    protected function commonPrintRuleNoLimitWithValidity(CheckPrintRefundRuleParamDTO $paramDto, callable $validityCallable)
    {
        $applyDid = $paramDto->apply_did;
        $tools = new Tools();
        $cancelOpId = $tools->getCancelMemberOpId($paramDto->cancel_member_id);
        $realAttr = $tools->getRealAttribute($paramDto->order_info, $applyDid);
        //套票子票以打包方的身份退票
        if ($paramDto->origin_operator_id == $cancelOpId && $cancelOpId == $applyDid && $cancelOpId == $paramDto->buy_id && $paramDto->is_pack ==2) {
            return ['code' => 200, 'msg' => '有条件退-有效期前x天内可退-取票退票规则[取票后不可退]-自供自销允许退[套票子票]'];
        }
        //判断自供自销，还会包含两级计调下单，当大于3级计调下单时，不会进来
        if ($cancelOpId == $applyDid && $cancelOpId == $paramDto->buy_id && $paramDto->is_pack != 2) {
            return ['code' => 200, 'msg' => '自供自销允许退'];
        }

        /******  因为需求核心诉求要区分角色相反计算，保证供应商和真实购买者有一个可退，所以需要以用户角度为第一视觉，默认都计算末级购买者；【购买者身份正向cancel值计算】 start *****/
        $validityCallableData = $validityCallable($paramDto);
        $orderMemberCancel = $validityCallableData['order_member_cancel'];
        $orderMemberCancelMsg = $validityCallableData['order_member_cancel_msg'];
        $logUserTip = $validityCallableData['log_user_tip'];
        $orderMemberCancelCode = $validityCallableData['order_member_cancel_code'];

        /******  【购买者身份正向cancel值计算】 end *****/
        $realCancelMsg = $orderMemberCancelMsg;
        $realCancelCode = $orderMemberCancelCode;
        /*****       当前操作人==末级分销商==真实购买者       *****/
        if ($cancelOpId == $paramDto->buy_id && $cancelOpId == $realAttr['finalAid']) {
            return ['code' => $realCancelCode, 'msg' => $logUserTip, 'log_err_msg' => $realCancelMsg];
        }
        /*****      中间级分销商： 当前操作人!=顶级供应商 && 当前操作人!=真实购买者  &&  当前操作人!= 末级分销商 &&  当前操作人!=景区资源账号 *****/
        if ($cancelOpId != $realAttr['firstApplyDid'] && $cancelOpId != $paramDto->buy_id && $cancelOpId != $realAttr['finalAid'] && $cancelOpId != $paramDto->saler_id) {
            $orderMemberCancel = false;
        }
        /*****       当前操作人==顶级供应商       *****/
        //当前操作人==顶级供应商  【供应商和购买者取反逻辑】
        if ($cancelOpId == $realAttr['firstApplyDid'] && $cancelOpId != $realAttr['finalAid']) {
            $realCancel = !$orderMemberCancel;
        }
        /*****       当前操作人=景区资源账号       *****/
        if ($cancelOpId == $paramDto->saler_id) {
            $realCancel = !$orderMemberCancel;
        }
        /*****       计调下单单独判断       *****/
        //计调下单-大于2级分销(即最少ABC的链路)，B给C下单，此处判断定位为角色B
        if ($cancelOpId == $paramDto->buy_id && $cancelOpId != $realAttr['finalAid'] && in_array($paramDto->real_order_mode, ['16', '10'])) {
            //计调下单都可退-实际付款人-大于2级分销
            $realCancel = $orderMemberCancel;
        }
        //计调下单-2级分销(即最少BC的链路)，B给C下单，此处判断定位为角色C
        if (in_array($paramDto->real_order_mode, ['16', '10']) && $cancelOpId == $realAttr['finalAid'] && $cancelOpId != $paramDto->buy_id) {
            //计调下单-末级都不可退(非实际下单人)
            $realCancel = false;
        }
        if (empty($realCancel)) {
            $orderMemberCancelCode = OrderConst::err4006;
            $realCancelMsg = '有条件退-取票退票规则[不限]-下单人才可操作';
        } else {
            $orderMemberCancelCode = 200;
            $realCancelMsg = '随时退-取票后不可退规则校验通过';
        }
        return ['code' => $orderMemberCancelCode, 'msg' => '下单人才可操作', 'log_err_msg' => $realCancelMsg];
    }

    /**
     * 有条件退-取票后不可退[部分取票/全部取票]-有效期前x天内可退
     * @param $paramDto CheckPrintRefundRuleParamDTO
     * @return array
     */
    protected function commonHasConditionWithValidity(CheckPrintRefundRuleParamDTO $paramDto, callable $validityCallable)
    {
        $applyDid = $paramDto->apply_did;
        $tools = new Tools();
        $cancelOpId = $tools->getCancelMemberOpId($paramDto->cancel_member_id);
        $realAttr = $tools->getRealAttribute($paramDto->order_info, $applyDid);
        //套票子票以打包方的身份退票
        if ($paramDto->origin_operator_id == $cancelOpId && $cancelOpId == $applyDid && $cancelOpId == $paramDto->buy_id && $paramDto->is_pack ==2) {
            return ['code' => 200, 'msg' => '有条件退-有效期前x天内可退-取票退票规则[取票后不可退]-自供自销允许退[套票子票]'];
        }
        if ($cancelOpId == $applyDid && $cancelOpId == $paramDto->buy_id  && $paramDto->is_pack != 2) {
            return ['code' => 200, 'msg' => '有条件退-有效期前x天内可退-取票退票规则[取票后不可退]-自供自销允许退'];
        }
        pft_log('order_refund/debug', json_encode(['hasConditionRefund_hasConditionWithBeforeTime', $paramDto, $cancelOpId, $realAttr]));

        /******  因为需求核心诉求要区分角色相反计算，保证供应商和真实购买者有一个可退，所以需要以用户角度为第一视觉，默认都计算末级购买者；【购买者身份正向cancel值计算】 start *****/
        $validityCallableData = $validityCallable($paramDto);
        $orderMemberCancelMsg = $validityCallableData['order_member_cancel_msg'];
        $logUserTip = $validityCallableData['log_user_tip'];
        $orderMemberCancelCode = $validityCallableData['order_member_cancel_code'];

        return $this->operatorValidate($orderMemberCancelCode, $orderMemberCancelMsg, $logUserTip, $cancelOpId, $realAttr, $paramDto->saler_id, $paramDto->order_status);
    }

    public function operatorValidate($orderMemberCancelCode, $orderMemberCancelMsg, $logUserTip, $cancelOpId, $realAttr, $saleId, $orderStatus)
    {
        /******  【购买者身份正向cancel值计算】 end *****/
        $orderMemberCancel = $orderMemberCancelCode == 200 ? true : false;
        $realCancelMsg = $orderMemberCancelMsg;
        $realCancelCode = $orderMemberCancelCode;

        /*****       当前操作人==末级分销商==真实购买者       *****/
        if ($cancelOpId == $realAttr['buyId'] && $cancelOpId == $realAttr['finalAid']) {
            return ['code' => $realCancelCode, 'msg' => $logUserTip, 'log_err_msg' => $realCancelMsg];
        }
        /*****      中间级分销商： 当前操作人!=顶级供应商 && 当前操作人!=真实购买者  &&  当前操作人!= 末级分销商 &&  当前操作人!=景区资源账号 *****/
        if ($cancelOpId != $realAttr['firstApplyDid'] && $cancelOpId != $realAttr['buyId'] && $cancelOpId != $realAttr['finalAid'] && $cancelOpId != $saleId) {
            $orderMemberCancel = false;
        }
        /*****       当前操作人==顶级供应商       *****/
        //当前操作人==顶级供应商  【供应商和购买者取反逻辑】
        if ($cancelOpId == $realAttr['firstApplyDid'] && $cancelOpId != $realAttr['finalAid']) {
            $realCancel = !$orderMemberCancel;
        }
        //顶级供应商都可退场景
        if ($cancelOpId == $realAttr['firstApplyDid'] && $orderStatus == 2) {
            $realCancel = true;
        }

        /*****       计调下单单独判断       *****/
        //计调下单-大于2级分销(即最少ABC的链路)，B给C下单，此处判断定位为角色B
        if ($cancelOpId == $realAttr['buyId'] && $cancelOpId != $realAttr['finalAid'] && in_array($realAttr['orderMode'], ['16', '10'])) {
            //计调下单都可退-实际付款人-大于2级分销
            $realCancel = $orderMemberCancel;
        }
        //计调下单-2级分销(即最少BC的链路)，B给C下单，此处判断定位为角色C
        if (in_array($realAttr['orderMode'], ['16', '10']) && $cancelOpId == $realAttr['finalAid'] && $cancelOpId != $realAttr['buyId']) {
            //计调下单-末级都不可退(非实际下单人)
            $realCancel = false;
        }
        if (empty($realCancel)) {
            $orderMemberCancelCode = OrderConst::err4006;
            $realCancelMsg = "下单人才可操作";
        } else {
            $orderMemberCancelCode = 200;
            $realCancelMsg = '校验通过';
        }
        return ['code' => $orderMemberCancelCode, 'msg' => '下单人才可操作', 'log_err_msg' => $realCancelMsg];
    }
}
