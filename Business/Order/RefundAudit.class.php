<?php
/**
 * 退票审核统一封装，不要和退票的逻辑混在一起
 * 暂时只是迁移了是否需要审核和添加审核记录两个逻辑
 *
 * <AUTHOR>
 * @date 2019-06-25
 *
 */
namespace Business\Order;

use Business\Admin\ModuleConfig;
use Business\Base;
use Business\JavaApi\Order\OrderTicketDiscounts;
use Business\JavaApi\Product\ChannelInspectConvert;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\MemberLogin\MemberLoginHelper;
use Business\Notice\Buyer;
use Business\Order\ExchangeBaseTicket\KafkaNotificationService;
use Library\Constants\Order\OrderChannel;
use Business\Order\Refund\QConfigSwitchController;
use Business\Order\Refund\RefundRedisManage;
use Business\Order\RefundApprovalCenterService\AbstractApprovalCenterService;
use Business\Order\RefundApprovalCenterService\PackRefundApprovalService;
use Business\Order\RefundApprovalCenterService\RefundApprovalService;
use Business\PackTicket\PackConst;
use Business\PackTicket\Traits\PackageAbnormalOrderTrait;
use Business\PftSystem\SysConfig;
use Library\Cache\Cache;
use Library\Constants\OrderConst;
use Library\Container;
use Library\Kafka\KafkaProducer;
use Library\MessageNotify\PFTSMSInterface;
use Library\Model;
use Library\Resque\Queue;
use Library\Util\EnvUtil;
use Model\Member\Member;
use Model\Member\RefundNotice;
use Model\Order\OrderTerminal;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery;
use Model\Product\Ticket;
use Model\SystemLog\OptLog;
use Model\Tools\EsSyncTask;
use Model\TradeRecord\OrderRefund;
use Model\Wechat\WxMember;
use Business\Product\Ticket as TicketBiz;

class RefundAudit extends Base
{
    use PackageAbnormalOrderTrait;
    const MODIFY_CODE               = 2; //退款审核表中修改申请的stype值
    const CANCEL_CODE               = 3; //退款审核表中取消申请的stype值
    const APPLY_AUDIT_CODE          = 11; //订单追踪表中表示发起退款审核
    const OPERATE_AUDIT_CODE        = 10; //订单追踪表中表示退款审核已处理
    const AGREE_AUDIT_CODE          = 13;
    const REFUSE_AUDIT_CODE         = 14;
    const SPECIAL_AUDIT_CODE        = 29; //特产退货审核
    const SPECIAL_REFUSE_AUDIT_CODE = 30; //拒绝特产退货审核
    const SPECIAL_AGREE_AUDIT_CODE  = 31; //同意特产退货审核


    private $refundAuditModel;
    private $orderModel;
    private $loginInfo;
    private $orderNum;
    private $refundRecordId;
    public function setLoginInfo($arr)
    {
        $this->loginInfo = $arr;
    }

    public function getLoginInfo()
    {
        return $this->loginInfo;
    }
    /**
     * 判断订单是否需要退票审核
     *
     * @param string $orderNum 订单ID
     * @param int   $targetTnum 修改后的票数
     * @param int   $operatorID 操作人ID
     * @param null  $modifyType 类型
     * @param array $orderInfo 订单信息
     *      *      [
     *                  'status'       => 1,
     *                  'paymode'      => 2,
     *                  'pay_status'   => 1,
     *                  'tnum'         => 1,
     *                  'tid'          => 200031,
     *                  'ifpack'       => 2,
     *                  'pack_order'   => 39991,
     *                  'aids'         => '961,4,3779',
     *                  'refund_audit' => '1', //退票审核设置
     *              ]
     * @param int   $refundQuantity 修改的票数
     *
     * @return array
     *         [
     *             'code' => 100, //100|200|xxx 100不需要审核，200需要审核，999系统级别退票审核，其它数字表示错误
     *             'msg'  => '说明',
     *             'data' => ['cancel_list' => ['111193', '111194']] //已经被取消的对接三方系统的订单
     *         ]
     */
    public function checkRefundAudit($orderNum, $targetTnum = 0, $operatorID = 1, $modifyType = null, $orderInfo = [], $refundQuantity = 0, $pftSerialNumber = '', $personIdList = [],$isThirdOrder = false, $refundAuditExt = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::checkRefundAuditForApproval($orderNum, $targetTnum, $operatorID, $modifyType, $orderInfo, $refundQuantity ,
            $pftSerialNumber, $personIdList ,$isThirdOrder);
        }
        $orderNum   = strval($orderNum);
        $operatorID = intval($operatorID);

        if (!$orderNum) {
            return $this->returnData(202, '参数错误');
        }

        // 系统全局启用了退票审核
        $globalRefundAudit = SysConfig::getGlobalRefundAduit();
        if ($globalRefundAudit === true) {
            return $this->returnData(999, '开启了系统级别退票审核');
        }

        //退票类型
        if ($modifyType === null) {
            $modifyType = $targetTnum == 0 ? 3 : 2;
        }

        //获取订单信息
        $orderModel = new OrderTools();
        $auditModel = $this->getRefundAdultModel();

        //如果外部没有传数据进来
        if (!$orderInfo) {
            $orderInfo = $auditModel->getInfoForAuditCheck($orderNum);
        }

        if (!$orderInfo || !is_array($orderInfo)) {
            return $this->returnData(204, '订单号不存在');
        }

        //未修改门票数量无需审核 2016-4-2 解决联票的判断bug
        if ($orderInfo['ifpack'] == 0) {
            //非套票的的情况下
            if ($orderInfo['tnum'] == $targetTnum) {
                return $this->returnData(100, '不需要退票审核');
            }
        }

        //顶级供应商
        if ($orderInfo['aids']) {
            $tmp          = explode(',', $orderInfo['aids']);
            $mainApplyDid = $tmp[0];
        } else {
            $mainApplyDid = $orderInfo['aid'];
        }

        //判断是否有退票审核参数
        if (isset($orderInfo['refund_audit'])) {
            $mainRefundAudit = $orderInfo['refund_audit'];
        } else {
            // 退款参数需要从java接口获取  -- jinmin
            $channelInspectConvertBiz = new ChannelInspectConvert();
            $ticketInfoRes = $channelInspectConvertBiz->batchConvert([[
                'ticketId'  => $orderInfo['tid'],
                'fid'       => $orderInfo['member'],
                'sid'       => $orderInfo['aid'],
                'visitors'  => $orderInfo['visitors'],
                'ordermode' => $orderInfo['ordermode'],
            ]]);

            $ticketInfoRes = $ticketInfoRes['data'];
            $mainRefundAudit = $ticketInfoRes[0]['uuJqTicketDTO']['refundAudit'];

            //$mainRefundAudit = $auditModel->getTicketInfoById($orderInfo['tid'], 'refund_audit');
        }

        if ($orderInfo['ifpack'] == 1) {
            //套票的主票
            $auditRes = $this->_packageOrderRefund($orderNum, $orderInfo['tnum'], $targetTnum, $refundQuantity, $modifyType, $operatorID,$pftSerialNumber,$personIdList, $refundAuditExt);

            //记录套票退票审核的情况
            $logData = json_encode([
                'key'      => 'package_check_audit',
                'ordernum' => $orderNum,
                'res'      => $auditRes,
            ]);

            pft_log('order_refund/debug', $logData);

        } else {
            //普通票或是套票的子票是否需要退票审核
            $packOrder = $orderInfo['ifpack'] == 2 ? $orderInfo['pack_order'] : false;
            if ($mainRefundAudit == 1 || $isThirdOrder){  //需要检查下审核数量够不够
                $BeforeTargetNum = $auditModel->getAllAuditTargetTnum($orderNum, [0]);
                if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                    $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                    $targetTicketNum  = $targetTnum - $haveAuditNum;
                    if ($targetTicketNum < 0){
                        return $this->returnData(206, '订单审核数量有误');
                    }
                }
            }
            $auditRes = $this->_isNeedRefundChecked($orderNum, $orderInfo['status'], $orderInfo['paymode'],
                $orderInfo['pay_status'], $mainApplyDid, $mainRefundAudit, $modifyType, $operatorID, $packOrder);
        }

        return $auditRes;
    }

    public function checkRefundAuditForApproval($orderNum, $targetTnum = 0, $operatorID = 1, $modifyType = null, $orderInfo = [], $refundQuantity = 0, $pftSerialNumber = '', $personIdList = [],$isThirdOrder = false)
    {
        $orderNum   = strval($orderNum);
        $operatorID = intval($operatorID);

        if (!$orderNum) {
            return $this->returnData(202, '参数错误');
        }

        // 系统全局启用了退票审核
        $globalRefundAudit = SysConfig::getGlobalRefundAduit();
        if ($globalRefundAudit === true) {
            return $this->returnData(999, '开启了系统级别退票审核');
        }

        //退票类型
        if ($modifyType === null) {
            $modifyType = $targetTnum == 0 ? 3 : 2;
        }

        //获取订单信息
        $orderModel = new OrderTools();
        $auditModel = $this->getRefundAdultModel();

        //如果外部没有传数据进来
        if (!$orderInfo) {
            $orderInfo = $auditModel->getInfoForAuditCheck($orderNum);
        }

        if (!$orderInfo || !is_array($orderInfo)) {
            return $this->returnData(204, '订单号不存在');
        }

        //未修改门票数量无需审核 2016-4-2 解决联票的判断bug
        if ($orderInfo['ifpack'] == 0) {
            //非套票的的情况下
            if ($orderInfo['tnum'] == $targetTnum) {
                return $this->returnData(100, '不需要退票审核');
            }
        }

        //顶级供应商
        if ($orderInfo['aids']) {
            $tmp          = explode(',', $orderInfo['aids']);
            $mainApplyDid = $tmp[0];
        } else {
            $mainApplyDid = $orderInfo['aid'];
        }

        //判断是否有退票审核参数
        if (isset($orderInfo['refund_audit'])) {
            $mainRefundAudit = $orderInfo['refund_audit'];
        } else {
            // 退款参数需要从java接口获取  -- jinmin
            $channelInspectConvertBiz = new ChannelInspectConvert();
            $ticketInfoRes = $channelInspectConvertBiz->batchConvert([[
                'ticketId'  => $orderInfo['tid'],
                'fid'       => $orderInfo['member'],
                'sid'       => $orderInfo['aid'],
                'visitors'  => $orderInfo['visitors'],
                'ordermode' => $orderInfo['ordermode'],
            ]]);

            $ticketInfoRes = $ticketInfoRes['data'];
            $mainRefundAudit = $ticketInfoRes[0]['uuJqTicketDTO']['refundAudit'];

            //$mainRefundAudit = $auditModel->getTicketInfoById($orderInfo['tid'], 'refund_audit');
        }

        if ($orderInfo['ifpack'] == 1) {
            //套票的主票
            $auditRes = $this->_packageOrderRefund($orderNum, $orderInfo['tnum'], $targetTnum, $refundQuantity, $modifyType, $operatorID,$pftSerialNumber,$personIdList);
            if($auditRes['code'] == 100){
                $auditRes  = $this->_isNeedRefundChecked($orderNum, $orderInfo['status'], $orderInfo['paymode'],
                    $orderInfo['pay_status'], $mainApplyDid, $mainRefundAudit, $modifyType, $operatorID);
            }
            if($auditRes['code'] == 200){
                $auditRes['data']['is_main_audit'] = $mainRefundAudit;
            }
            //记录套票退票审核的情况
            $logData = json_encode([
                'key'      => 'package_check_audit',
                'ordernum' => $orderNum,
                'res'      => $auditRes,
                'orderInfoStatus'=>$orderInfo['status'],
                'checkRefundAuditForApproval'
            ]);

            pft_log('order_refund/debug', $logData);

        } else {
            //普通票或是套票的子票是否需要退票审核
            $packOrder = $orderInfo['ifpack'] == 2 ? $orderInfo['pack_order'] : false;
            if ($mainRefundAudit == 1 || $isThirdOrder){  //需要检查下审核数量够不够
                $BeforeTargetNum = $auditModel->getAllAuditTargetTnum($orderNum, [0]);
                if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                    $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                    $targetTicketNum  = $targetTnum - $haveAuditNum;
                    if ($targetTicketNum < 0){
                        return $this->returnData(206, '订单审核数量有误');
                    }
                }
            }
            $auditRes = $this->_isNeedRefundChecked($orderNum, $orderInfo['status'], $orderInfo['paymode'],
                $orderInfo['pay_status'], $mainApplyDid, $mainRefundAudit, $modifyType, $operatorID, $packOrder);
        }

        return $auditRes;
    }
    /**
     * 添加退票审核记录
     * 只支持取消和修改，不支持撤销撤改
     *
     * @param string $orderNum 订单ID
     * @param int $targetTicketNum 剩余的票数
     * @param int $operatorID 操作ID
     * @param int $source 来源
     * @param int $requestTime 时间
     * @param array $orderInfo 订单信息
     *         [
     *             'status'     => 1,
     *             'salerid'    => 1,
     *             'tnum'       => 1,
     *             'tid'        => 200031,
     *             'lid'        => 111,
     *             'apply_did'  => '4800',
     *             'ifpack'     => '2',
     *         ]
     * @param int|bool   $mainTicketNum 如果是套票子票，需要带这个套票的原来票数
     * @param string $remoteSn 第三方流水号
     * @param string $pftSn 平台流水号
     * @param int $refundQuantity 退票数
     * @param bool $sysException 是否系统异常
     * @param  array  $timeOrderRefund 计时订单退票审核特殊参数
     * @param  array $refundAuditExt 退票审核拓展属性
     *      [
     *          'cancel_audit_remark' => '退票申请的备注'
     *      ]
     * @return mixed
     */
    public function addRefundAudit($orderNum, $targetTicketNum, $operatorID, $source = 18, $requestTime = 0,
        $orderInfo = [], $mainTicketNum = false, $remoteSn = '', $pftSn = '', $refundQuantity = 0, $sysException = false, $personIdList = [],
        $timeOrderRefund = [], $refundAuditExt = [], $moreData = [], $mainRefundAuditExt = []) {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::addRefundAuditForApproval($orderNum, $targetTicketNum, $operatorID, $source, $requestTime,
                $orderInfo, $mainTicketNum, $remoteSn, $pftSn, $refundQuantity, $sysException, $personIdList,
                $timeOrderRefund, $refundAuditExt, $moreData);
        }

        //如果没有传第三方流水号，就是用平台订单号
        $remoteSn = empty($remoteSn) ? $orderNum : $remoteSn;

        //查询是否存在审核记录
        $refundModel = self::getRefundAdultModel();
        $underAudit  = $refundModel->isUnderAudit($orderNum, $remoteSn);
        if ($underAudit) {
            return $this->returnData(240, '订单正在审核');
        }

        //参数初始化
        $auditStatus = 0; //所有未审核记录的的dstatus都为0
        $trackAction = self::APPLY_AUDIT_CODE;
        $requestTime = ($requestTime) ? $requestTime : date('Y-m-d H:i:s');
        $modifyType  = $targetTicketNum == 0 ? self::CANCEL_CODE : self::MODIFY_CODE;
        if (count($orderInfo) <= 0) {
            //正常票或是套票主票，只需要简单的信息
            $orderInfo = $refundModel->getOrderInfoForAudit($orderNum);
        }

        if (!$orderInfo || !is_array($orderInfo)) {
            return $this->returnData(205, '订单信息不全');
        }
        $qConfigSwitch = new QConfigSwitchController();
        $usedRefundFix = false;
        if ($qConfigSwitch::getRefundFixUsedWithTenant($orderNum)) {
            $usedRefundFix = true;
        }

        //获取原始供应商
        $auditorID = $orderInfo['apply_did'] ? $orderInfo['apply_did'] : false;
        $haveAuditNum = 0;
        //如果是主票的数量有变化，那子票的数量也要按比例变化
        //如果是套票的子票，需要判断类型，如果是系统自动审核需要去请求第三方系统
        if ($mainTicketNum !== false) {
//            if ($mainTicketNum >= 0) {
//                $targetTicketNum = floor($orderInfo['tnum'] * ($targetTicketNum / $mainTicketNum));
//            }
            if ($mainTicketNum >= 0){
                $refundQuantity   = ceil($refundQuantity * $orderInfo['tnum'] / $mainTicketNum);
                $refundQuantity = $refundQuantity > $orderInfo['tnum'] ? $orderInfo['tnum'] : $refundQuantity;
            }else{
                $refundQuantity   = 0;
            }
            $targetTicketNum = $orderInfo['tnum'] - $refundQuantity;
            //判断当前票 系统自动审核/供应商人工审核
            $tid         = $orderInfo['tid'];
            $ticketModel = new Ticket();
            $ticketInfo  = $ticketModel->getTicketInfoById($tid, ['refund_audit', 'apply_did']);
            $refundAudit = $ticketInfo['refund_audit'];
            $auditorID   = $auditorID ?: $ticketInfo['apply_did'];

            // 切换门票获取第三方系统信息
            $otaProductBiz    = new \Business\Ota\Product();
            $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($tid);
            if (empty($thirdBindInfoArr)) {
                //订单信息不全
                return $this->returnData(205, '订单信息不全');
            }

            $Mdetails = $thirdBindInfoArr['Mdetails'];
            $Mpath    = $thirdBindInfoArr['Mpath'];
            $sourceT  = $thirdBindInfoArr['sourceT'];
            $BeforeTargetNum = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                $targetTicketNum  = $targetTicketNum - $haveAuditNum;
                if ($targetTicketNum < 0){
                    return $this->returnData(206, '订单审核数量有误');
                }
            }
            $pftSn  = $pftSn.'_'.$orderNum;
            if ($usedRefundFix) {
                $subRes = $this->_refundHandler($refundAudit, $refundQuantity, $targetTicketNum, $orderInfo, $Mdetails,
                    $sourceT, $Mpath, $auditorID, $modifyType, $operatorID, '', 0, true, $pftSn, $personIdList, $moreData, $mainRefundAuditExt);

            } else {
                $subRes = $this->_refundHandler($refundAudit, $refundQuantity, $targetTicketNum, $orderInfo, $Mdetails, $sourceT, $Mpath, $auditorID, $modifyType, $operatorID, '', 0, true, $pftSn, $personIdList);
            }

            if ($subRes['code'] == 100) {
                return $this->returnData(100, '不需要退票审核');
            }
        }else{
            $BeforeTargetNum = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                $targetTicketNum  = $targetTicketNum - $haveAuditNum;
                if ($targetTicketNum < 0){
                    return $this->returnData(206, '订单审核数量有误');
                }
            }
        }

        //这边获取一些冗余字段插入到审核表
        $ModifyBiz = new Modify();
        $othenData = $ModifyBiz->getAuditOtherData($orderNum);
        switch ($othenData['p_type']){
            case 'J':
                $trackAction = self::SPECIAL_AUDIT_CODE;
                break;
            case 'K':
                $othenData['audit_data']['is_refund_deposit']  = $timeOrderRefund['is_refund_deposit'];  //是否退押金 0：不退押金 1:退押金
                $othenData['audit_data']['is_refund_over_pay'] = $timeOrderRefund['is_refund_over_pay'];  //是否否退超时补费金额 0：不退 1:退
            default:
                break;
        }
        $cancelMsg = $refundAuditExt['cancel_audit_remark'] ?? '';
        //申请表-取消数据， 增加优惠明细 用以审核通过后，通知中台
        if(isset($refundAuditExt['discount']) && !empty($refundAuditExt['discount'])){
            $othenData['audit_data']['discount'] = $refundAuditExt['discount'];
        }
        if($moreData['after_sale_num']){
            $othenData['audit_data']['after_sale_num'] = $moreData['after_sale_num'];
        }
        if($moreData['is_retry']){
            $othenData['audit_data']['is_retry'] = $moreData['is_retry'];
        }
        if($moreData['batch_refund_more_idx']){
            $othenData['audit_data']['batch_refund_more_idx'] = $moreData['batch_refund_more_idx'];
        }
        if($moreData['tourist_idx']){
            $othenData['audit_data']['tourist_idx'] = $moreData['tourist_idx'];
        }
        if($moreData['tourist_list'] && $usedRefundFix){
            $othenData['audit_data']['tourist_list'] = $moreData['tourist_list'];
        }
        if (isset($moreData['track_source'])) {
            $othenData['audit_data']['track_source'] = $moreData['track_source'];
        }
        if (isset($moreData['op_id'])) {
            $othenData['audit_data']['op_id'] = $moreData['op_id'];
        }
        //添加订单追踪记录
        $trackRes = $this->_addRefundAuditOrderTrack($orderNum, $source, $operatorID, $trackAction, $auditStatus, $targetTicketNum, $orderInfo,$cancelMsg,$haveAuditNum, $moreData);
        if ($trackRes['code'] != 200) {
            //添加错误
            return $trackRes;
        }
        $othenData['audit_data']['cancel_audit_remark'] = $cancelMsg;
        if ($moreData['subSid']){
            $othenData['sub_merchant_id'] = $moreData['subSid'];
        }
        if (isset($moreData['tourist_idx']) && $usedRefundFix){
            $othenData['tourist_idx'] = $moreData['tourist_idx'];
        }

        //添加审核记录
        $addAudit = $refundModel->addRefundAudit($orderNum, $refundQuantity, 0, $orderInfo['salerid'], $orderInfo['lid'], $orderInfo['tid'],
            $modifyType, $targetTicketNum, $operatorID, $auditStatus, $auditorID, $requestTime, '', 0, $remoteSn, $pftSn, $sysException, $othenData);

        if (!$addAudit) {
            //数据添加失败
            return $this->returnData(241, '退票审核信息写入出错');
        }

        // 为预售券兑换订单发送审核中 Kafka 通知
        if ($orderInfo['ordermode'] == OrderChannel::EXCHANGE_COUPON_REDEEM_CHANNEL) {
            KafkaNotificationService::sendAuditPendingNotification($orderNum, $remoteSn, $modifyType, $operatorID, '退票审核中');
        }

        //套票主票，需要将所有的子票添加审核记录
        if ($orderInfo['ifpack'] == 1) {
            $orderModel = $this->getOrderModel();
            $subOrders  = $orderModel->getPackSubOrder($orderNum);
            if (!$subOrders || !is_array($subOrders)) {
                //套票信息出错
                return $this->returnData(207, '套票子票获取失败');
            }
            $mainRefundAuditExt = $refundAuditExt;
            foreach ($subOrders as $subOrder) {
                $subOrderInfo = $refundModel->getOrderInfoForAudit($subOrder['orderid']);
//                if ($orderInfo['tnum'] == 0 || $subOrderInfo['tnum'] == 0) {
//                    $subRefundQuantity = 0;
//                } else {
//                    $subRefundQuantity = $refundQuantity / ($orderInfo['tnum'] / $subOrderInfo['tnum']);
//                }
                //子票添加审核记录
                $addSubOrder = $this->addRefundAudit($subOrder['orderid'], $targetTicketNum, $operatorID, $source, $requestTime,
                    $subOrderInfo, $orderInfo['tnum'], $remoteSn, $pftSn, $refundQuantity, $sysException, $personIdList, [], [], $moreData, $mainRefundAuditExt);

                if (in_array($addSubOrder['code'], [100, 200, 240])) {
                    //如果是在审核中、不需审核、需要退票推审核，继续审核其他的子票
                    continue;
                } else {
                    return $addSubOrder;
                }
            }
        }

        //数据添加成功
        return $this->returnData(200, '退票审核信息写入成功');
    }

    public function addRefundAuditForApproval($orderNum, $targetTicketNum, $operatorID, $source = 18, $requestTime = 0,
                                   $orderInfo = [], $mainTicketNum = false, $remoteSn = '', $pftSn = '', $refundQuantity = 0, $sysException = false, $personIdList = [],
                                   $timeOrderRefund = [], $refundAuditExt = [], $moreData = []) {

        if($orderInfo['ifpack'] == 1 && !isset($moreData['is_son_audit'])){
            $moreData['audit_data']['main_request'] = [
                'orderNum'=>$orderNum, 'targetTicketNum'=>$targetTicketNum, 'operatorID'=>$operatorID, 'source'=>$source, 'requestTime'=>$requestTime,
                'orderInfo'=>$orderInfo, 'mainTicketNum'=>$mainTicketNum, 'remoteSn'=>$remoteSn, 'pftSn'=>$pftSn, 'refundQuantity'=>$refundQuantity,
                'sysException'=>$sysException, 'personIdList'=>$personIdList, 'timeOrderRefund'=>$timeOrderRefund, 'refundAuditExt'=>$refundAuditExt,
                'moreData'=>$moreData
            ];
        }

        //如果没有传第三方流水号，就是用平台订单号
        $remoteSn = empty($remoteSn) ? $orderNum : $remoteSn;

        //查询是否存在审核记录
        $refundModel = self::getRefundAdultModel();
        $underAudit  = $refundModel->isUnderAudit($orderNum, $remoteSn);
        if ($underAudit && !isset($moreData['is_son_audit'])) {
            return $this->returnData(240, '订单正在审核');
        }

        //参数初始化
        $auditStatus = 0; //所有未审核记录的的dstatus都为0
        $trackAction = self::APPLY_AUDIT_CODE;
        $requestTime = ($requestTime) ? $requestTime : date('Y-m-d H:i:s');
        $modifyType  = $targetTicketNum == 0 ? self::CANCEL_CODE : self::MODIFY_CODE;
        if (count($orderInfo) <= 0) {
            //正常票或是套票主票，只需要简单的信息
            $orderInfo = $refundModel->getOrderInfoForAudit($orderNum);
        }

        if (!$orderInfo || !is_array($orderInfo)) {
            return $this->returnData(205, '订单信息不全');
        }

        //获取原始供应商
        $auditorID = $orderInfo['apply_did'] ? $orderInfo['apply_did'] : false;
        $haveAuditNum = 0;
        //如果是主票的数量有变化，那子票的数量也要按比例变化
        //如果是套票的子票，需要判断类型，如果是系统自动审核需要去请求第三方系统
        if ($mainTicketNum !== false) {
//            if ($mainTicketNum >= 0) {
//                $targetTicketNum = floor($orderInfo['tnum'] * ($targetTicketNum / $mainTicketNum));
//            }
            if(!isset($moreData['is_son_audit'])){
                if ($mainTicketNum >= 0){
                    $refundQuantity   = ceil($refundQuantity * $orderInfo['tnum'] / $mainTicketNum);
                    $refundQuantity = $refundQuantity > $orderInfo['tnum'] ? $orderInfo['tnum'] : $refundQuantity;
                }else{
                    $refundQuantity   = 0;
                }
                $targetTicketNum = $orderInfo['tnum'] - $refundQuantity;
            }

            //判断当前票 系统自动审核/供应商人工审核
            $tid         = $orderInfo['tid'];
            $ticketModel = new Ticket();
            $ticketInfo  = $ticketModel->getTicketInfoById($tid, ['refund_audit', 'apply_did']);
            $refundAudit = $ticketInfo['refund_audit'];
            $auditorID   = $auditorID ?: $ticketInfo['apply_did'];

            // 切换门票获取第三方系统信息
            $otaProductBiz    = new \Business\Ota\Product();
            $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($tid);
            if (empty($thirdBindInfoArr)) {
                //订单信息不全
                return $this->returnData(205, '订单信息不全');
            }

            $Mdetails = $thirdBindInfoArr['Mdetails'];
            $Mpath    = $thirdBindInfoArr['Mpath'];
            $sourceT  = $thirdBindInfoArr['sourceT'];
            if ($Mdetails == 1 && $sourceT > 0) {
                $moreData['isThirdOrder'] = true;
                $moreData['isCancelThirdSystem'] =true;
            }
            if(!isset($moreData['is_son_audit'])){
                $BeforeTargetNum = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
                if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                    $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                    $targetTicketNum  = $targetTicketNum - $haveAuditNum;
                    if ($targetTicketNum < 0){
                        return $this->returnData(206, '订单审核数量有误');
                    }
                }
                $pftSn  = $pftSn.'_'.$orderNum;
                $subRes = $this->_refundHandler($refundAudit, $refundQuantity, $targetTicketNum, $orderInfo, $Mdetails,
                    $sourceT, $Mpath, $auditorID, $modifyType, $operatorID, '', 0, true,
                    $pftSn, $personIdList,$moreData);

                if ($subRes['code'] == 100) {
                    return $this->returnData(100, '不需要退票审核');
                }
                elseif($subRes['code'] != 200){
                    $subRes['data']['orderNum']=$orderNum;
                    return $subRes;
                }
            }
        }else{
            $BeforeTargetNum = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                $targetTicketNum  = $targetTicketNum - $haveAuditNum;
                if ($targetTicketNum < 0){
                    return $this->returnData(206, '订单审核数量有误');
                }
            }
        }

        //这边获取一些冗余字段插入到审核表
        $ModifyBiz = new Modify();
        $othenData = $ModifyBiz->getAuditOtherData($orderNum);
        switch ($othenData['p_type']){
            case 'J':
                $trackAction = self::SPECIAL_AUDIT_CODE;
                break;
            case 'K':
                $othenData['audit_data']['is_refund_deposit']  = $timeOrderRefund['is_refund_deposit'];  //是否退押金 0：不退押金 1:退押金
                $othenData['audit_data']['is_refund_over_pay'] = $timeOrderRefund['is_refund_over_pay'];  //是否否退超时补费金额 0：不退 1:退
            default:
                break;
        }
        $cancelMsg = $refundAuditExt['cancel_audit_remark'] ?? '';
        //申请表-取消数据， 增加优惠明细 用以审核通过后，通知中台
        if(isset($refundAuditExt['discount']) && !empty($refundAuditExt['discount'])){
            $othenData['audit_data']['discount'] = $refundAuditExt['discount'];
        }
        if($moreData['after_sale_num']){
            $othenData['audit_data']['after_sale_num'] = $moreData['after_sale_num'];
        }
        if($moreData['is_retry']){
            $othenData['audit_data']['is_retry'] = $moreData['is_retry'];
        }
        if($moreData['batch_refund_more_idx']){
            $othenData['audit_data']['batch_refund_more_idx'] = $moreData['batch_refund_more_idx'];
        }
        if($moreData['tourist_idx']){
            $othenData['audit_data']['tourist_idx'] =  $moreData['tourist_idx'];
        }
        if($moreData['mainApprovalCode']){
            $othenData['audit_data']['mainApprovalCode'] =  $moreData['mainApprovalCode'];
        }
        if(!isset($moreData['is_son_audit'])){
            //添加订单追踪记录
            $trackRes = $this->_addRefundAuditOrderTrack($orderNum, $source, $operatorID, $trackAction, $auditStatus, $targetTicketNum, $orderInfo,$cancelMsg,$haveAuditNum, $moreData);
            if ($trackRes['code'] != 200) {
                //添加错误
                return $trackRes;
            }
        }
        $othenData['audit_data']['cancel_audit_remark'] = $cancelMsg;
        if ($moreData['subSid']){
            $othenData['sub_merchant_id'] = $moreData['subSid'];
        }
        if ($moreData['isCancelSub']){
            $othenData['isCancelSub'] = $moreData['isCancelSub'];
        }
        if ($moreData['isThirdOrder']){
            $othenData['isThirdOrder'] = $moreData['isThirdOrder'];
        }
        if ($moreData['isCancelThirdSystem']){
            $othenData['isCancelThirdSystem'] = $moreData['isCancelThirdSystem'];
        }
        if($moreData['is_main_audit']){
            $othenData['audit_data']['is_main_audit'] =  $moreData['is_main_audit'];
        }
        if($moreData['audit_data']['main_request']){
            $othenData['audit_data']['main_request'] = $moreData['audit_data']['main_request'];
        }
        if($moreData['is_need_approval']){
            $othenData['is_need_approval'] = $moreData['is_need_approval'];
        }

        if(!isset($moreData['is_son_audit'])){
            //添加审核记录
            $addAudit = $refundModel->addRefundAudit($orderNum, $refundQuantity, 0, $orderInfo['salerid'], $orderInfo['lid'], $orderInfo['tid'],
                $modifyType, $targetTicketNum, $operatorID, $auditStatus, $auditorID, $requestTime, '', 0, $remoteSn, $pftSn, $sysException, $othenData);

            if (!$addAudit) {
                //数据添加失败
                return $this->returnData(241, '退票审核信息写入出错');
            }
        }
        else{
            //套票主票，需要将所有的子票添加审核记录
            if ($orderInfo['ifpack'] == 1) {
                $orderModel = $this->getOrderModel();
                $subOrders  = $orderModel->getPackSubOrder($orderNum);
                if (!$subOrders || !is_array($subOrders)) {
                    //套票信息出错
                    return $this->returnData(207, '套票子票获取失败');
                }
                $hasSubOrderInApproval = false;
                foreach ($subOrders as $subOrder) {
                    $subOrderInfo = $refundModel->getOrderInfoForAudit($subOrder['orderid']);
//                if ($orderInfo['tnum'] == 0 || $subOrderInfo['tnum'] == 0) {
//                    $subRefundQuantity = 0;
//                } else {
//                    $subRefundQuantity = $refundQuantity / ($orderInfo['tnum'] / $subOrderInfo['tnum']);
//                }
                    $sonRequestTime = date('Y-m-d H:i:s');
                    //子票添加审核记录
                    $sonOtherData = [];
                    if($othenData['audit_data']['mainApprovalCode']){
                        $sonOtherData['mainApprovalCode'] = $othenData['audit_data']['mainApprovalCode'];
                    }
                    if($othenData['isThirdOrder']){
                        $sonOtherData['isThirdOrder'] = $othenData['isThirdOrder'];
                    }
                    if($othenData['isCancelThirdSystem']){
                        $sonOtherData['isCancelThirdSystem'] = $othenData['isCancelThirdSystem'];
                    }
                    if ($mainTicketNum >= 0){
                        $refundQuantity   = ceil($refundQuantity * $subOrderInfo['tnum'] / $mainTicketNum);
                        $refundQuantity = $refundQuantity > $subOrderInfo['tnum'] ? $subOrderInfo['tnum'] : $refundQuantity;
                    }else{
                        $refundQuantity   = 0;
                    }
                    $targetTicketNum = $subOrderInfo['tnum'] - $refundQuantity;
                    $addSubOrder = $this->addRefundAudit($subOrder['orderid'], $targetTicketNum, $operatorID, $source,
                        $sonRequestTime, $subOrderInfo, $subOrderInfo['tnum'], $remoteSn, $pftSn, $refundQuantity, $sysException,
                        $personIdList,[],[],$sonOtherData);
                    if(in_array($addSubOrder['code'], [200, 240])){
                        $hasSubOrderInApproval = true;
                    }
                    if (in_array($addSubOrder['code'], [100, 200, 240])) {
                        //如果是在审核中、不需审核、需要退票推审核，继续审核其他的子票
                        continue;
                    } else {
                        return $addSubOrder;
                    }
                }

            }
        }


        /*
        //套票主票，需要将所有的子票添加审核记录
        if ($orderInfo['ifpack'] == 1) {
            $orderModel = $this->getOrderModel();
            $subOrders  = $orderModel->getPackSubOrder($orderNum);
            if (!$subOrders || !is_array($subOrders)) {
                //套票信息出错
                return $this->returnData(207, '套票子票获取失败');
            }
            foreach ($subOrders as $subOrder) {
                $subOrderInfo = $refundModel->getOrderInfoForAudit($subOrder['orderid']);
                $sonRequestTime = date('Y-m-d H:i:s');
                //子票添加审核记录
                $sonOtherData = [];
                 if($othenData['audit_data']['mainApprovalCode']){
                     $sonOtherData['mainApprovalCode'] = $othenData['audit_data']['mainApprovalCode'];
                 }
                if ($Mdetails == 1 && $sourceT > 0) {
                    $sonOtherData['isThirdOrder'] = true;
                    $sonOtherData['isCancelThirdSystem'] =true;
                }
                $addSubOrder = $this->addRefundAudit($subOrder['orderid'], $targetTicketNum, $operatorID, $source,
                    $sonRequestTime, $subOrderInfo, $orderInfo['tnum'], $remoteSn, $pftSn, $refundQuantity, $sysException,
                    $personIdList,[],[],$sonOtherData);

                if (in_array($addSubOrder['code'], [100, 200, 240])) {
                    //如果是在审核中、不需审核、需要退票推审核，继续审核其他的子票
                    continue;
                } else {
                    return $addSubOrder;
                }
            }
        }
        */

        //数据添加成功
        return $this->returnData(200, '退票审核信息写入成功',['hasSubOrderInApproval'=>$hasSubOrderInApproval ?? true]);
    }

    /**
     * 普通票或是套票的子票是否需要退票审核
     * <AUTHOR>
     * @date   2016-09-13
     *
     * @param string $remoteSn 第三方平台流水号
     * @param int $status 订单状态
     * @param int  $paymode 订单支付方式
     * @param int  $payStatus 订单支付状态
     * @param int  $applyId 直接供应商
     * @param int $refundAudit 退票审核设置 0=系统自动审核，1=供应商审核
     * @param int $modifyType 修改类型
     * @param int $operatorID 操作人ID
     * @param  string|bool $packOrder 套票中主票的ID - 套票的子票才会有
     * @return array 100/200/205 100:不需要审核,200:需要退票审核
     */
    private function _isNeedRefundChecked($remoteSn, $status, $paymode, $payStatus, $applyId, $refundAudit, $modifyType, $operatorID, $packOrder = false)
    {
        $remoteSn  = strval($remoteSn);
        $status    = intval($status);
        $payStatus = intval($payStatus);
        $paymode   = intval($paymode);

        if ($refundAudit == 1) {
            //供应商审核
            //检查订单使用状态 // 过期的供应商自己取消也要审核  by jackchb 2020 7月细节优化
            $statusRes = $this->_checkUseStatus($status, $applyId, $operatorID);
            if ($statusRes['code'] != 200) {
                return $statusRes;
            }

            //检查订单支付状态
            $payRes = $this->_checkPayStatus($paymode, $payStatus);
            if ($payRes['code'] != 200) {
                return $payRes;
            }

            //自供自销的订单可自行取消
            // if ($applyId == $operatorID) {
            //     return 100;
            // }

            //不需要退票审核
            return $this->returnData(200, '需要供应商审核');

        } else {
            //系统自动审核
            if ($packOrder) {
                //是套票的子票，如果主票是在退票审核中，那这个订单也是需要退票审核的
                $mainOrderIsUnderAudit = $this->getRefundAdultModel()->isUnderAudit($packOrder, $remoteSn);
                if ($mainOrderIsUnderAudit) {
                    return $this->returnData(200, '主票是在退票审核中，需要退票审核');
                } else {
                    return $this->returnData(100, '系统自动审核');
                }
            } else {
                //不需要退票审核
                return $this->returnData(100, '系统自动审核');
            }
        }
    }
    /**
     * 检测套票子票是否需要退票审核
     * 只需要判断下面的子票，因为对套票主票设置审核是无效的
     *
     * @param strin $orderNum 退票订单号
     * @param int $mainTicketNum 订单票数
     * @param int $targetTnum 修改后的票数
     * @param int $refundQuantity 修改的票数
     * @param int $modifyType 修改类型
     * @param int $operatorID 操作人ID
     * @return array
     */
    private function _packageOrderRefund($orderNum, $mainTicketNum, $targetTnum, $refundQuantity, $modifyType, $operatorID, $pftSerialNumber = '', $personIdList = [], $refundAuditExt = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::_packageOrderRefundForApproval($orderNum, $mainTicketNum, $targetTnum, $refundQuantity, $modifyType,
                $operatorID, $pftSerialNumber,$personIdList);
        }
        //实例化
        $ticketModel = new Ticket();
        $auditModel  = $this->getRefundAdultModel();
        $orderModel  = $this->getOrderModel();

        //获取子票订单数据
        $subOrders = $orderModel->getPackSubOrder($orderNum);
        if ($subOrders === false) {
            //子票查询出错了
            return $this->returnData(207, '子票查询出错');
        }

        //这种情况是套票的主票生成了，但是子票没有生成，也要能够取消主票
        if (is_array($subOrders) && count($subOrders) == 0) {
            //套票信息出错
            return $this->returnData(100, '子票没有生成，取消主票');
        }

        $subArr = [];
        foreach ($subOrders as $subOrder) {
            $subArr[] = strval($subOrder['orderid']);
        }

        //批量获取子单信息
        $subInfoArr = $auditModel->getInfoForAuditCheck($subArr, false);
        $tidList    = array_column($subInfoArr, 'tid');

        // 获取子票的门票  refund_audit: 0=系统自动审核，1=供应商审核
        $ticketsInfo     = $ticketModel->getTicketList($tidList, ['id', 'refund_audit', 'apply_did']);
        $refundAuditList = array_column($ticketsInfo, 'refund_audit');

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTidArr($tidList);

        if (in_array(1, $refundAuditList)) {
            //有子票设置了需要供应商审核
            $isNeedAudit = true;
        } else {
            //子票都配置了不需要退票审核
            $isNeedAudit = false;
            foreach ($thirdBindInfoArr as $thirdInfo) {
                if ($thirdInfo['Mdetails'] == 1 && $thirdInfo['sourceT'] > 0) {
                    //如果有一个子票有对接三方系统的话，就需要往三方系统退票审核
                    $isNeedAudit = true;
                    break;
                }
            }
        }

        if ($isNeedAudit == false) {
            return $this->returnData(100, '子票都不需要退票审核，而且没有对接三方系统');
        }
        //默认是不需要审核
        $defaultAuditRes = $this->returnData(100, '默认不需要审核');

        //已经被取消的子票订单
        $cancelOrderArr = [];
        $cancelSubNum   = [];
        $refundModel = self::getRefundAdultModel();
        foreach ($subInfoArr as $key => $value){  //先计算好之前审核多少了
            if (in_array($value['status'],[1,7])){  //TODO 这个逻辑后期可以放到前置判断
                return $this->returnData(205, '子票状态已使用无法提交审核');
            }
            if ($mainTicketNum >= 0){
                $cancelNum = ceil($refundQuantity * $value['tnum'] / $mainTicketNum);  //算出取消数量
            }else{
                $cancelNum = 0;
            }

            $cancelNum = $cancelNum > $value['tnum'] ? $value['tnum'] : $cancelNum;
            $targetTicketNum = $value['tnum'] - $cancelNum;
            $BeforeTargetNum = $refundModel->getAllAuditTargetTnum($value['ordernum'], [0]);
            if ($BeforeTargetNum){  //代表之前有提交过退票审核了
                $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
                $targetTicketNum  = $targetTicketNum - $haveAuditNum;  //计算下剩余数量
                if ($targetTicketNum < 0){
                    return $this->returnData(205, '订单审核数量有误');
                }
            }
            $cancelSubNum[$value['ordernum']] = ['leftNum' => $targetTicketNum,'cancelNum' => $cancelNum];
        }

        //三方系统退票审核
        foreach ($subInfoArr as $subOrderId => $subInfo) {
            if ($subInfo['aids']) {
                $tmp      = explode(',', $subInfo['aids']);
                $applyDid = $tmp[0];
            } else {
                $applyDid = $subInfo['aid'];
            }

            $ticketInfo  = $ticketsInfo[$subInfo['tid']];
            $thirdInfo   = $thirdBindInfoArr[$subInfo['tid']];
            $refundAudit = $ticketInfo['refund_audit'];
            $leftNum     = $cancelSubNum[$subInfo['ordernum']]['leftNum'];
            $cancelNum   = $cancelSubNum[$subInfo['ordernum']]['cancelNum'];
            //子票的退票
            $auditRes = $this->_refundHandler($refundAudit, $cancelNum, $leftNum, $subInfo, $thirdInfo['Mdetails'], $thirdInfo['sourceT'], $thirdInfo['Mpath'],
                $applyDid, $modifyType, $operatorID, $orderNum, $mainTicketNum, false, $pftSerialNumber.'_'.$subInfo['ordernum'], $personIdList, [], $refundAuditExt);

            if ($auditRes['code'] == 100 || $auditRes['code'] == 200) {
                //记录已经被取消的子票订单
                if (isset($auditRes['data']['cancel_order'])) {
                    $cancelOrderArr[] = $auditRes['data']['cancel_order'];
                }

                //如果是需要退票审核的，那整个票都是需要退票审核的
                if ($auditRes['code'] == 200) {
                    $defaultAuditRes = $auditRes;
                }

                //如果是退票申请通过或是不需要审核的话，继续判断其他子票
                continue;
            } else {
                //如果有其中一个子票退票审核异常，直接返回异常
                $defaultAuditRes = $auditRes;
                pft_log('order_refund/debug',json_encode(['package_check_audit_children_ticket', $subInfo]));
                break;
            }
        }

        //如果有已经被取消的子票订单，将这些订单返回
        if ($cancelOrderArr) {
            $defaultAuditRes['data']['cancel_list'] = $cancelOrderArr;
        }

        return $defaultAuditRes;
    }

    private function _packageOrderRefundForApproval($orderNum, $mainTicketNum, $targetTnum, $refundQuantity, $modifyType, $operatorID, $pftSerialNumber = '', $personIdList = [])
    {
        //实例化
        $ticketModel = new Ticket();
        $auditModel  = $this->getRefundAdultModel();
        $orderModel  = $this->getOrderModel();

        //获取子票订单数据
        $subOrders = $orderModel->getPackSubOrder($orderNum);
        if ($subOrders === false) {
            //子票查询出错了
            return $this->returnData(207, '子票查询出错');
        }

        //这种情况是套票的主票生成了，但是子票没有生成，也要能够取消主票
        if (is_array($subOrders) && count($subOrders) == 0) {
            //套票信息出错
            return $this->returnData(100, '子票没有生成，取消主票');
        }

        $subArr = [];
        foreach ($subOrders as $subOrder) {
            $subArr[] = strval($subOrder['orderid']);
        }

        //批量获取子单信息
        $subInfoArr = $auditModel->getInfoForAuditCheck($subArr, false);
        $tidList    = array_column($subInfoArr, 'tid');

        // 获取子票的门票  refund_audit: 0=系统自动审核，1=供应商审核
        $ticketsInfo     = $ticketModel->getTicketList($tidList, ['id', 'refund_audit', 'apply_did']);
        $refundAuditList = array_column($ticketsInfo, 'refund_audit');

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTidArr($tidList);

        if (in_array(1, $refundAuditList)) {
            //有子票设置了需要供应商审核
            $isNeedAudit = true;
        } else {
            //子票都配置了不需要退票审核
            $isNeedAudit = false;
            foreach ($thirdBindInfoArr as $thirdInfo) {
                if ($thirdInfo['Mdetails'] == 1 && $thirdInfo['sourceT'] > 0) {
                    //如果有一个子票有对接三方系统的话，就需要往三方系统退票审核
                    $isNeedAudit = true;
                    break;
                }
            }
        }

        //如果子票也都没有对接三方系统的话，直接就不需要退票审核了
        if ($isNeedAudit == false) {
            return $this->returnData(100, '子票都不需要退票审核，而且没有对接三方系统');
        }
        else{
            return $this->returnData(200, '需要审核');
        }
        //对接审批中心后，以下逻辑取消
        /**
        //默认是不需要审核
        $defaultAuditRes = $this->returnData(100, '默认不需要审核');

        //已经被取消的子票订单
        $cancelOrderArr = [];
        $cancelSubNum   = [];
        $refundModel = self::getRefundAdultModel();
        foreach ($subInfoArr as $key => $value){  //先计算好之前审核多少了
        if (in_array($value['status'],[1,7])){  //TODO 这个逻辑后期可以放到前置判断
        return $this->returnData(205, '子票状态已使用无法提交审核');
        }
        if ($mainTicketNum >= 0){
        $cancelNum = ceil($refundQuantity * $value['tnum'] / $mainTicketNum);  //算出取消数量
        }else{
        $cancelNum = 0;
        }

        $cancelNum = $cancelNum > $value['tnum'] ? $value['tnum'] : $cancelNum;
        $targetTicketNum = $value['tnum'] - $cancelNum;
        $BeforeTargetNum = $refundModel->getAllAuditTargetTnum($value['ordernum'], [0]);
        if ($BeforeTargetNum){  //代表之前有提交过退票审核了
        $haveAuditNum    = array_sum(array_column($BeforeTargetNum,'modify_tnum'));
        $targetTicketNum  = $targetTicketNum - $haveAuditNum;  //计算下剩余数量
        if ($targetTicketNum < 0){
        return $this->returnData(205, '订单审核数量有误');
        }
        }
        $cancelSubNum[$value['ordernum']] = ['leftNum' => $targetTicketNum,'cancelNum' => $cancelNum];
        }

        //三方系统退票审核
        foreach ($subInfoArr as $subOrderId => $subInfo) {
        if ($subInfo['aids']) {
        $tmp      = explode(',', $subInfo['aids']);
        $applyDid = $tmp[0];
        } else {
        $applyDid = $subInfo['aid'];
        }

        $ticketInfo  = $ticketsInfo[$subInfo['tid']];
        $thirdInfo   = $thirdBindInfoArr[$subInfo['tid']];
        $refundAudit = $ticketInfo['refund_audit'];
        $leftNum     = $cancelSubNum[$subInfo['ordernum']]['leftNum'];
        $cancelNum   = $cancelSubNum[$subInfo['ordernum']]['cancelNum'];
        //子票的退票
        $auditRes = $this->_refundHandler($refundAudit, $cancelNum, $leftNum, $subInfo, $thirdInfo['Mdetails'], $thirdInfo['sourceT'], $thirdInfo['Mpath'],
        $applyDid, $modifyType, $operatorID, $orderNum, $mainTicketNum, false, $pftSerialNumber.'_'.$subInfo['ordernum'], $personIdList);

        if ($auditRes['code'] == 100 || $auditRes['code'] == 200) {
        //记录已经被取消的子票订单
        if (isset($auditRes['data']['cancel_order'])) {
        $cancelOrderArr[] = $auditRes['data']['cancel_order'];
        }

        //如果是需要退票审核的，那整个票都是需要退票审核的
        if ($auditRes['code'] == 200) {
        $defaultAuditRes = $auditRes;
        }

        //如果是退票申请通过或是不需要审核的话，继续判断其他子票
        continue;
        } else {
        //如果有其中一个子票退票审核异常，直接返回异常
        $defaultAuditRes = $auditRes;
        break;
        }
        }

        //如果有已经被取消的子票订单，将这些订单返回
        if ($cancelOrderArr) {
        $defaultAuditRes['data']['cancel_list'] = $cancelOrderArr;
        }

        return $defaultAuditRes;
         */
    }


    /**
     * 检查支付方式和支付状态
     *
     * @param int $payMode   支付方式：1在线支付|2授信支付|3自供自销|4到付|5微信支付|7银联支付|8环迅支付
     * @param int $payStatus 0景区到付|1已成功|2未支付
     *
     * @return array
     */
    private function _checkPayStatus($payMode, $payStatus)
    {
        if ($payMode == 4 || $payStatus == 2) {
            //到付订单/未支付订单:无需退票审核
            return $this->returnData(100, '到付或是未支付订单无需退票审核');
        } else {
            return $this->returnData(200);
        }
    }

    /**
     * 检查订单使用状态
     * 当开启取消要审核时，过期的供应商自己取消也要审核  by jackchb 2020 7月细节优化
     *
     * @param $useStatus  0未使用|1已使用|2已过期|3被取消|4待确认|5被终端修改|6被终端撤销|7部分使用
     * @param $ticketAid
     * @param $operatorId
     *
     * @return array
     */
    private function _checkUseStatus($useStatus, $ticketAid, $operatorId)
    {
        if (in_array($useStatus, [1, 3, 5, 6])) {
            //核销、取消、撤改订单不能取消了
            return $this->returnData(210, '存在已经使用的订单不能取消');
        } else if ($useStatus == 2) {
            //过期订单需要特殊处理
            //if ($operatorId == $ticketAid) {
            //   //订单已过期：只有供应商可以取消
            //    return $this->returnData(100, '供应商取消过期订单');
            //} else {
            //    return $this->returnData(200);
            //}
            return $this->returnData(200);
        } else {
            //其他情况需要审核
            return $this->returnData(200);
        }
    }

    /**
     * 添加订单追踪记录
     *
     * @param string $orderNum 订单ID
     * @param int $source 来源
     * @param int $operatorID 操作ID
     * @param int $action 10-提交退票请求 11-操作退票审核
     * @param int $auditStatus 审核状态
     * @param int $targetTicketNum 操作的票数
     * @param int $havaAuditNum 已经审核的数量
     * @param array $orderInfo 订单信息
     *         [
     *             'status'     => 1,
     *             'salerid'    => 1,
     *             'tnum'       => 1,
     *             'tid'        => 200031,
     *             'lid'        => 111
     *         ]
     * @param string|null $msg 备注信息
     *
     * @return mixed
     */
    private function _addRefundAuditOrderTrack($orderNum, $source, $operatorID, $action, $auditStatus, $targetTicketNum, $orderInfo, $msg = null,$haveAuditNum = 0, $moreData = [])
    {
        if (!in_array($auditStatus, [0, 1, 2])) {
            return $this->returnData(208, '审核状态不正确');
        }

        $orderModel = $this->getOrderModel();
        $orderRealTnum = $orderInfo['tnum'] - $haveAuditNum;
        $tNumOperate = $orderRealTnum - $targetTicketNum;
        if ($orderInfo['status'] == 7 || $orderInfo['status'] == 2) {
            $verifyNum = $orderModel->getVerifiedNum($orderNum);
            if ($verifyNum) {
                $tNumCanBeModified = $orderRealTnum - $verifyNum;
            } else {
                $tNumCanBeModified = $orderRealTnum;
            }
        } else {
            $tNumCanBeModified = $orderRealTnum;
        }

        switch ($auditStatus) {
            case 0:
            case 1:
                $remainTicketNum = $tNumCanBeModified - $tNumOperate;
                break;
            case 2:
                $remainTicketNum = $tNumCanBeModified;
                break;
        }
        $extContent = [];
        if ($moreData['subSid'] && $moreData['subOpId']){
            $extContent['subSid'] = $moreData['subSid'];
            $extContent['subOpId'] = $moreData['subOpId'];
        }
        if($moreData['after_sale_num']){
            $extContent['after_sale_num'] = $moreData['after_sale_num'];
        }
        if($moreData['batch_refund_more_idx']){
            $extContent['batch_refund_more_idx'] = $moreData['batch_refund_more_idx'];
        }
        $trackModel = new OrderTrack();
        $trackRes   = $trackModel->addTrack($orderNum, $action, $orderInfo['tid'], $tNumOperate, $remainTicketNum,
            $source, 0, 0, 0, $operatorID, $orderInfo['salerid'], $create_time = '', $msg, 0, false, $extContent);

        if ($trackRes) {
            return $this->returnData(200);
        } else {
            return $this->returnData(244, '退票审核追踪记录写入失败');
        }
    }

    /**
     * 套票的子票退票审核
     * <AUTHOR>
     * @date   2019-06-29
     *
     * @param  int     $refundAudit
     * @param  int     $modifyNum
     * @param  int     $targetTicketNum
     * @param  array     $orderInfo
     * @param  int     $Mdetails
     * @param  int     $Mpath
     * @param  int     $applyDid
     * @param  int     $modifyType
     * @param  int     $operatorID
     * @param  string     $masterOrderNum
     * @param  int    $mainTicketNum
     * @param  bool $checkStatus 对接三方系统，判断是否需要退票审核的逻辑中，如果检测的时候已经处理了，就不再二次请求
     * @return array
     */
    private function _refundHandler($refundAudit, $modifyNum, $targetTicketNum, $orderInfo, $Mdetails, $sourceT, $Mpath,
                                    $applyDid, $modifyType, $operatorID, $masterOrderNum = '', $mainTicketNum = 0,
                                    $checkStatus = false, $pftSerialNumber = '', $personIdList = [],$moreData =[], $refundAuditExt = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderInfo['ordernum']);
        if($isWhite){
            return self::_refundHandlerForApproval($refundAudit, $modifyNum, $targetTicketNum, $orderInfo, $Mdetails, $sourceT, $Mpath,
                $applyDid, $modifyType, $operatorID, $masterOrderNum, $mainTicketNum, $checkStatus, $pftSerialNumber, $personIdList,$moreData);
        }
        if ($refundAudit == 1) {
            //需要供应商审核
            $auditRes = $this->_isNeedRefundChecked($orderInfo['ordernum'], $orderInfo['status'], $orderInfo['paymode'],
                $orderInfo['pay_status'], $applyDid, $refundAudit, $modifyType, $operatorID, $masterOrderNum);
        } elseif ($Mdetails == 1){
            //对接三方系统的子票由子票自己处理
            $auditRes = $this->returnData(200, '需要三方系统申请审核');
            //是对接三方系统的订单才往三方系统去退票审核
            if ($Mdetails == 1 && $sourceT > 0) {
                //如果是主票的数量有变化，那子票的数量也要按比例变化
//                if (!empty($masterOrderNum)) {
//                    $targetTicketNum = floor($orderInfo['tnum'] * ($targetTicketNum / $mainTicketNum));
//                }

                $interfaceMode = 'old';
                if ($sourceT == 3) {
                    $interfaceMode = 'new';
                }
                //置换子票退票实名制数据 根据订单号，以及身份证 优先匹配身份证
                $baseRefund = new BaseRefund;
                $personRes = $baseRefund->sonTicketRealNameForThird($orderInfo['ordernum'],$pftSerialNumber,$personIdList,$modifyNum);
                pft_log('order_refund/debug',
                    json_encode(['ThirdRefundIdCard_sonRefund:', $personRes, $orderInfo['ordernum'],$pftSerialNumber,$personIdList,$modifyNum, $refundAuditExt]));
                if($personRes['code']!=200){
                    $this->returnData($personRes['code'], $personRes['msg']);
                }
                $personIdList = $personRes['data']['personIdList'];
                //实名制退票时，当实名制数量小于票数量时,转换为按数量退 || 实名制数量小于游客数量
                $qConfigSwitch = new QConfigSwitchController();
                if ($qConfigSwitch::getRefundFixUsedWithTenant($orderInfo['ordernum'])) {
                    if ($modifyNum > count($personIdList) || $refundAuditExt['card_num_left_tourist_num']) {
                        $personIdList = [];
                    }
                }
                $tmpRes = $this->_callThirdSystem($orderInfo['ordernum'], $orderInfo['lid'], $targetTicketNum, $orderInfo['member'],
                    $applyDid, $Mdetails, $Mpath, $checkStatus, $interfaceMode, $pftSerialNumber, $personIdList,$modifyNum);
                $refundCode = $tmpRes['code'];

                if ($refundCode == 200) {
                    //直接退票成功,后面的票还需要继续判断
                    $auditRes = $this->returnData(100, '三方系统直接退票成功', ['cancel_order' => $orderInfo['ordernum']]);
                } else if ($refundCode == 1095) {
                    //申请退票成功,待第三方审核通知
                    $auditRes = $this->returnData(200, '三方系统申请退票成功');
                } else {
                    //申请退票失败，第三方不给于退款
                    $auditRes = $this->returnData(257, '三方系统申请退票失败');
                }
            } else {
                $auditRes = $this->returnData(100, '不需要退票审核');
            }
        } else {
            //不需要退票审核
            $auditRes = $this->returnData(100, '不需要退票审核');
        }

        return $auditRes;
    }



    private function _refundHandlerForApproval($refundAudit, $modifyNum, $targetTicketNum, $orderInfo, $Mdetails, $sourceT, $Mpath,
                                    $applyDid, $modifyType, $operatorID, $masterOrderNum = '', $mainTicketNum = 0, $checkStatus = false,
                                    $pftSerialNumber = '', $personIdList = [],$moreData=[])
    {

        $BaseRefund = new BaseRefund();
        $checkBiz     = new BaseRefundCheck();
        $ticketBiz = new TicketBiz();
        $ticketRes = $ticketBiz->getListForOrderNew($orderInfo['tid']);
        if (!$ticketRes['ticket_info']) {
            return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
        }

        $ordernum = $orderInfo['ordernum'];
        $tmpTicketInfo = $ticketRes['ticket_info'];
        $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
        $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);

        $orderModel   = new OrderTools('localhost');
        $tmpOrderInfo = $orderModel->getInfoForCancel($ordernum);
        if (!$tmpOrderInfo) {
            return $this->orderReturn(0, "订单数据不存在", ['err_code' => OrderConst::err11]);
        }
        //如果是已经支付的套票的主票，sub_list返回子票的取票信息
        $orderInfo    = $tmpOrderInfo['order_info'];
        $isRefund = $BaseRefund->_checkMustRefundAttribute($ordernum, $ticketInfo, $orderInfo, []);
        if ($isRefund['code'] != 200) {
            //记录个日志观察几天
            $jsonData = [
                'ordernum' => $ordernum,
                'res'      => $isRefund,
            ];
            pft_log('order_refund/noRefund', json_encode($jsonData));

            return $isRefund;
        }
        //预约不可退  强制退要调过该限制
        $checkAppointmentRes = $checkBiz->checkAppointmentRefund($applyDid,$operatorID,$orderInfo,$ticketInfo);
        if(!$checkAppointmentRes['res'] && !($moreData['isForceCancel'] ?? false) ){
            return $this->orderReturn(0,"预约退票规则：预约成功后不可退",['err_code'=>OrderConst::err1425,'err_msg'=>"预约退票规则：预约成功后不可退"]);
        }

        $isNeedCheckRefundRule = $checkBiz->isNeedCheckRefundRule($ordernum, 1, $operatorID, $applyDid,
            true,false);

        if ($isNeedCheckRefundRule) {
            $memberModel = new Member();
            $memberInfo  = $memberModel->getMemberInfo($operatorID);

            if (!$memberInfo) {
                //用户不存在
                return $this->orderReturn(0, '退票用户不存在', ['err_code' => OrderConst::err11]);
            }
            $memberType = $memberInfo['dtype'];
            //如果是员工账号，获取主账号信息
            if ($memberType == 6) {
                $parentInfo = $memberModel->getStaffBySonId($operatorID);
                if (!$parentInfo) {
                    //获取不到主账号信息
                    return $this->orderReturn(0, '获取不到主账号信息', ['err_code' => OrderConst::err11]);
                }
                $cancelMemberId = $parentInfo['parent_id'];
            } else {
                $cancelMemberId = $operatorID;
            }

            $cancelType = in_array($modifyType,[0,1]) ? 'revoke' : 'common';

            $checkAuthRes = $BaseRefund->_isHaveCancelAuth($ordernum, $orderInfo['tid'], $orderInfo, $ticketInfo, $cancelMemberId,
                $memberType, 23, $cancelType, false, [],false,'');
            if ($checkAuthRes['code'] != 200) {
                return $checkAuthRes;
            }
        }

        if ($refundAudit == 1 && $Mdetails!=1) {
            //需要供应商审核
            $auditRes = $this->_isNeedRefundChecked($orderInfo['ordernum'], $orderInfo['status'], $orderInfo['paymode'],
                $orderInfo['pay_status'], $applyDid, $refundAudit, $modifyType, $operatorID, $masterOrderNum);
        } elseif ($Mdetails == 1){
            //对接三方系统的子票由子票自己处理
            $auditRes = $this->returnData(200, '需要三方系统申请审核');
            /*
            //是对接三方系统的订单才往三方系统去退票审核
            if ($Mdetails == 1 && $sourceT > 0) {
                //如果是主票的数量有变化，那子票的数量也要按比例变化
//                if (!empty($masterOrderNum)) {
//                    $targetTicketNum = floor($orderInfo['tnum'] * ($targetTicketNum / $mainTicketNum));
//                }

                $interfaceMode = 'old';
                if ($sourceT == 3) {
                    $interfaceMode = 'new';
                }

                $tmpRes = $this->_callThirdSystem($orderInfo['ordernum'], $orderInfo['lid'], $targetTicketNum, $orderInfo['member'],
                    $applyDid, $Mdetails, $Mpath, $checkStatus, $interfaceMode, $pftSerialNumber, $personIdList,$modifyNum);
                $refundCode = $tmpRes['code'];

                if ($refundCode == 200) {
                    //直接退票成功,后面的票还需要继续判断
                    $auditRes = $this->returnData(100, '三方系统直接退票成功', ['cancel_order' => $orderInfo['ordernum']]);
                } else if ($refundCode == 1095) {
                    //申请退票成功,待第三方审核通知
                    $auditRes = $this->returnData(200, '三方系统申请退票成功');
                } else {
                    //申请退票失败，第三方不给于退款
                    $auditRes = $this->returnData(257, '三方系统申请退票失败');
                }
            } else {
                $auditRes = $this->returnData(100, '不需要退票审核');
            }
             */
        } else {
            //不需要退票审核
            $auditRes = $this->returnData(100, '不需要退票审核');
        }

        return $auditRes;
    }

    /**
     * 请求第三方退票接口
     * <AUTHOR>
     * @date   2016-10-23
     *
     * @param string  $orderNum 订单ID
     * @param int $lid 景点ID
     * @param int  $tnum 数量
     * @param int $member 购买用户
     * @param int $applyDid 原始供应商
     * @param string $Mdetails 是否需要请求第三方
     * @param string $Mpath 之前的第三方地址
     * @param bool $checkStatus 对接三方系统，判断是否需要退票审核的逻辑中，如果检测的时候已经处理了，就不再二次请求
     * @param string $interfaceMode 'old' 我们开发对方的，  'new'  开放接口
     *
     * @return int 1=直接退票成功，2=申请退票成功,待第三方审核通知，3=申请退票失败，第三方不给于退款
     */
    private function _callThirdSystem($orderNum, $lid, $tnum, $member, $applyDid, $Mdetails, $Mpath, $checkStatus = false, $interfaceMode = 'old', $pftSerialNumber = '', $personIdList = [],$cancelNum = 0)
    {

        $params = [
            'Tnum'        => $tnum,
            'Fid'         => $member,
            'Ordern'      => $orderNum,
            'LandId'      => $lid,
            'ApplyId'     => $applyDid,
            'CheckStatus' => $checkStatus,
            'PftSerialNum'=> $pftSerialNumber,
        ];

        if (!empty($personIdList)){
            $params['IdCardList']   = implode(',',$personIdList); // 身份证
        }else{
            $params['IdCardList']   = '';
        }
        if ($cancelNum > 0){
            $params['cancelNum']  = $cancelNum;
        }
        $thirdSystem = new ThirdSystem();
        $refundRes   = $thirdSystem->commonRefund($params, $interfaceMode);
        return $refundRes;
    }

    /**
     * 获取退票审核模型
     * <AUTHOR>
     * @date 2019-06-30
     *
     * @return Object
     */
    private function getRefundAdultModel()
    {
        if (is_null($this->refundAuditModel)) {
            $this->refundAuditModel = new RefundAuditModel();
        }
        return $this->refundAuditModel;
    }

    /**
     * 获取订单模型
     * <AUTHOR>
     * @date 2019-06-30
     *
     * @return Object
     */
    private function getOrderModel()
    {
        if (is_null($this->orderModel)) {
            $this->orderModel = new OrderTools();
        }
        return $this->orderModel;
    }
    private static $revockeMemberKey = 'config:revoke_members';

    /**
     * 添加允许审核验证7天后的订单的供应商ID
     * @author: Guangpeng Chen
     * @date: 2019/10/31
     * @param int $mid 供应商id
     *
     * @return int
     */
    public static function setRevokeMemberId($mid)
    {
        /**
         * @var $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        return $redis->sAdd(self::$revockeMemberKey, $mid);
    }

    /**
     * 校验是否允许多次撤改用户列表
     * author queyourong
     * date 2022/9/16
     * @applyDid int 供应商id
     * @return boolean
     */
    public static function isAllowMoreRevoke($applyDid)
    {
        //售后通用版本 - 产品要求取消该开放功能
//        if(empty($applyDid)) {
//            return false;
//        }
//        $moduleConfigBiz = new ModuleConfig();
//        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'allow_more_revoke', 60);
//        if($resultRes['code'] != 200) {
//            return false;
//        }
//        if(empty($resultRes['data'])) {
//            return false;
//        }

        return true;
    }

    /**
     * 废弃
     * 校验是否允许分销商发起退票申请
     * @param $applyDid
     * @return bool
     */
    public static function isAllowDistributorRefund($applyDid){
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'allow_distributor_refund', 60);
        if($resultRes['code'] != 200) {
            return false;
        }
        if(empty($resultRes['data'])) {
            return false;
        }
        return true;
    }

    /**
     * 校验是否售后服务
     * 后期作为通用功能
     * @param $applyDid
     * @return bool
     */
    public static function isAllowAfterSaleService($applyDid){
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'after_sale_service', 60);
        if($resultRes['code'] != 200) {
            return false;
        }
        if(empty($resultRes['data'])) {
            return false;
        }
        return true;
    }

    /**
     * 校验是否允许超过七天撤改
     * author queyourong
     * date 2022/9/16
     * @param $applyDid int 供应商id
     * @return boolean
     */
    public static function isAllowExpireRevoke($applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'allow_exipre_revoke', 60);

        if($resultRes['code'] != 200) {
            return false;
        }
        if(empty($resultRes['data'])) {
            return false;
        }

        return true;
    }

    /**
     * 校验是否允许待预约退票规则
     * @param $applyDid
     * @return bool
     */
    public static function isAllowRefundAppointment($applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'refund_appointment_rule', 60);

        if($resultRes['code'] != 200) {
            return false;
        }
        if(empty($resultRes['data'])) {
            return false;
        }

        return true;
    }


    /**
     * 校验是否开通套票价格校验
     * @param $applyDid
     * @return bool
     */
    public static function isAllowPackPriceCheck($applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'pack_price_check', 60);
        if($resultRes['code'] != 200) {
            return false;
        }
        if(empty($resultRes['data'])) {
            return false;
        }
        return true;
    }


    /**
     * 获取供应商配置列表
     * @author: Guangpeng Chen
     * @date: 2019/10/31
     * @return array
     */
    public static function getRevokeMemberList()
    {
        /**
         * @var $redis \Redis
         */
        $redis = Cache::getInstance('redis');
        $list  = $redis->sMembers(self::$revockeMemberKey);
        return $list;
    }
    private function getOrderToolsModel()
    {
        if (empty($this->_orderToolsModel)) {
            $this->_orderToolsModel = new OrderTools();
        }

        return $this->_orderToolsModel;
    }

    /**
     * 更新审核记录 / ota也有在使用
     *
     * @param  string  $orderNum  订单ID
     * @param  int  $auditResult  审核结果 1=通过，2=不通过
     * @param  string  $auditNote  审核备注
     * @param  int  $operatorID  操作人员
     * @param  int  $auditID  审核记录ID
     *
     * @return mixed
     *          200 : 正常票获取套票的子票都审核过了
     *          243 : 套票的子票审核过了，但是还有其他的子票没有审核过
     *          xxx : 其他错误，统一参考order.conf.php -> refund_erro
     */
    public function updateRefundAudit($orderNum, $auditResult, $auditNote, $operatorID, $auditID, $moreData = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::updateRefundAuditForApproval($orderNum, $auditResult, $auditNote, $operatorID, $auditID, $moreData);
        }
        pft_log('order/refund_audit', json_encode(['updateRefundAudit_after_isWhite',$orderNum, $auditResult, $auditNote, $operatorID, $auditID, $moreData],JSON_UNESCAPED_UNICODE));
        $refundModel = self::getRefundAdultModel();
        if ($auditID == 0) {
            $refundRecord = $refundModel->getTerminalChangeInfo($orderNum, 0);
        } else {
            $refundRecord = $refundModel->getRefundAuditDetailById($auditID);
        }
        //处理向上游第三方发起退票，需审批时，审批写入延时，但是三方又立马自动回调审批通过了的问题
        if (!$refundRecord) {
            $returnCode = 4004;
            $cacheValue = '';
            if (!empty($moreData['system_sn'])) {
                $cacheValue = RefundRedisManage::getRefundAuditRecordRedisLock($moreData['system_sn']);
                $returnCode = empty($cacheValue) ? 4004 : 4003;
            }
            pft_log('order/refund_audit', json_encode(['refund_audit_refundRecord_empty',$orderNum, $auditResult, $auditNote, $operatorID, $auditID, $moreData, $refundRecord, $returnCode, $cacheValue],JSON_UNESCAPED_UNICODE));
            return $returnCode;
        }

        //如果该订单已经审核处理过了，就不再重复处理
        if ($refundRecord['dstatus'] != 0) {
            //dstatus=0 未审核(默认值)，1=已通过，2=已拒绝
            //审核数据已经处理过，不需要处理了
            $logData = json_encode([
                'status'   => 256,
                'ordernum' => $orderNum,
                'tnum'     => $refundRecord['tnum'],
                'refundRecord'     => $refundRecord,
                'key'     => 'refund_audit_refundRecord_dstatus',
            ]);
            pft_log('order/refund_audit', $logData);
            return 256;
        }
        // 参数验证
        if (!$orderNum) {
            return 201;
        }

        //检查传入参数，1=同意；2=拒绝
        if (!in_array($auditResult, [1, 2])) {
            //审核结果只能是同意或拒绝
            return 250;
        }

        //如果传入的修改票数有问题
        $targetTnum = intval($refundRecord['tnum']);
        if ($targetTnum < 0) {
            return 208;
        }

        //参数初始化
        $result    = 0;
        $orderInfo = $refundModel->getPackOrderInfo($orderNum);
        if (!$orderInfo) {
            //订单信息不全
            return 205;
        }
        $orderModel = new OrderTools();

        //套票需特殊处理 ifpack : 0=正常票, 1=套票主票, 2=套票子票
        $ifpack = $orderInfo['ifpack'];
        if ($ifpack == 1) {
            //套票主票无人工审核权限
            return 255;
        }

        // 预售券权益订单正在审核中，不能操作
        $auditData = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
        if (isset($auditData['exchange_base_ticket_equity_order_audit_status'])) { // 有这个属性就不能操作，等预售券回调
            return 260;
        }

        self::refundAuditLock($orderNum,$refundRecord['id']);
        if (!$ifpack) {
            //ifpack=0 正常票
            $result = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, null, true, false, 0, $moreData);
            pft_log('order/refund_audit', json_encode(["updateAudit", $orderNum, $result, $ifpack, $targetTnum, $auditResult, $operatorID]));
            if ($result['code'] == 1095) {
                return 200; // 1095 可能是联动了套票子票、预售券权益订单的审核，这里返回审核完成即可，后续审核状态机由关联的审核单触发流转
            }
            if ($result['code'] != 200) {
                return 241;
            }
        } else {
            // ifpack=2 套票子票
            //先更新审核记录,不具体操作订单取消或修改
            $updateResult = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, null, false);
            pft_log('order/refund_audit', json_encode(["updateAudit", $orderNum, $updateResult, $ifpack, $targetTnum, $auditResult, $operatorID]));
            if ($updateResult['code'] == 200) {
                $result = 200;
            }
        }
        $qConfigSwitch = new QConfigSwitchController();
        $usedRefundFix = false;
        if ($qConfigSwitch::getRefundFixUsedWithTenant($orderNum)) {
            $usedRefundFix = true;
        }
        //套票子票处理 - 还需要处理其他的子票
        // 子票流水号与主票一样
        if ($ifpack == 2 && $orderInfo['pack_order']) {
            $mainOrder = $orderInfo['pack_order'];
            // 获取子票订单号
            $ordersAutoUpdate = $orderModel->getPackSubOrder($mainOrder);
            pft_log('order/refund_audit', json_encode(["getPackSubOrder", $orderNum, $ordersAutoUpdate, $mainOrder]));
            $ordersAutoUpdate = array_column($ordersAutoUpdate, 'orderid');
            $desc             = '系统:部分子票的退票申请被拒绝';
            if ($auditResult == 1) {
                $desc = '系统:全部子票通过退票审核';
                // 检测是否所有子票都已经审核通过，如果审核通过，将主票也审核通过
                if ($refundModel->hasUnAuditSubOrder($ordersAutoUpdate, $refundRecord['remote_sn'])) {
                    $result = 243;

                    return $result;
                }
            }
            // 检测主票的审核记录是否存在
            $masterRefundRecord = $refundModel->isUnderAudit($mainOrder, $refundRecord['remote_sn']);
            if ($masterRefundRecord) {
                //这边主票在获取下订单信息看看是不是和子票是一个供应商
                $mainOrderData = $orderModel->getOrderInfo($mainOrder);
                $mainOperate   = 0;
                if ($mainOrderData['apply_did'] != $orderInfo['apply_did']) {
                    $mainOperate = $mainOrderData['apply_did'];  //主票取消的操作员覆盖成主票供应商
                }
                //更改主票uu_order_terminal_change审核记录状态
                $result = $this->updateAudit($masterRefundRecord, 1, $mainOrder, $masterRefundRecord['tnum'],
                    $auditResult, $desc, $operatorID, $masterRefundRecord['id'], null, true, false, $mainOperate);
                if ($usedRefundFix) {
                    //更改主票对应游客门票码状态
                    $mainAuditData = empty($masterRefundRecord['audit_data']) ? [] : json_decode($masterRefundRecord['audit_data'], true);
                    pft_log('order/refund_audit', json_encode(["mainAuditData", $orderNum, $mainAuditData, $mainOrder, $auditResult]));
                    //仅更改拒绝状态的主票审批流程
                    if ($auditResult != 1) {
                        if (isset($mainAuditData['tourist_list']) && !empty($mainAuditData['tourist_list'])) {
                            foreach ($mainAuditData['tourist_list'] as $itemTourist) {
                                pft_log('order/refund_audit', json_encode(["mainAuditData_params", $orderNum, $itemTourist, $mainOrder, $auditResult]));
                                //单个遍历修改，可能存在old_status不一致的情况
                                $data = [
                                    'checkState' => $auditResult == 1 ? 2 : $itemTourist['old_status'],
                                ];
                                $map  = [
                                    'checkState' => 3,
                                    'idxs'=>[$itemTourist['idx']]
                                ];
                                $api = new \Business\JavaApi\Order\OrderTouristUpdate();
                                $resTouristInfoUpdate = $api->touristInfoUpdate($mainOrder, $data, $map);
                                pft_log('order/refund_audit', json_encode(["mainAuditData_params", $orderNum, $itemTourist, $mainOrder, $resTouristInfoUpdate]));
                            }
                        }
                    }
                }
            } else {
                // 如果主票不存在审核记录，那么是因为单独取消子票
                $result = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                    $operatorID, $auditID, null, true, true);
            }
        }

        //update set的值，即更改后的最终状态，这里直接使用2和0也不对，
        //应该取进入审核中的上一个状态,因为会有自动过期定时调度更改值为4，造成差异
        $data = [
            'checkState' => $auditResult == 1 ? 2 : 0,
        ];
        if ($usedRefundFix) {
            /**
             * 这下面的更改，只能更改子票的对应游客门票码状态
             * 更改时使用的where条件
             * fixme 实际此处的$api->touristInfoUpdate调用应该移除，因为上面的updateAudit会已经调用了baseRefund，在baseRefund中会执行touristInfoUpdate
             * 这里暂时仅增加乐观锁的where条件，保持幂等性一致，暂时不做改变，等重构再统一修复
             * 此处checkState=3条件会将所有的多个审批状态只要是==3的更改，实际应该只更改当前的审批流
             */
            $map  = [
                'checkState' => 3,
            ];
            if (!empty($auditData) && !empty($auditData['tourist_idx'])){
                $idxs = [];
                //idx 强转为int类型
                foreach ($auditData['tourist_idx'] as $idx) {
                    $idxs[] = intval($idx);
                }
                $map  = [
                    'checkState' => 3,
                    'idxs'=>$idxs
                ];
            }
        } else {
            $map  = [
                'checkState' => 3,
            ];
        }
        pft_log('order/refund_audit', json_encode(['updateRefundAudit_touristInfoUpdate', $refundRecord, $result, $data, $map],JSON_UNESCAPED_UNICODE));

        //更新订单游客信息
        $api = new \Business\JavaApi\Order\OrderTouristUpdate();
        $api->touristInfoUpdate($orderNum, $data, $map);

        //如果是退票拒绝，需要通知下游
        if (is_array($result)) {
            $isSuccess = $result['code'] == 200 ? true : false;
        } else {
            $isSuccess = $result == 200 ? true : false;
        }
        //审核类型为
        $sType = $refundRecord['stype'];
        if ($auditResult == 2 && $isSuccess && in_array($sType,[self::MODIFY_CODE,self::CANCEL_CODE])) {
            $this->_notifyDownSystem($orderNum, $refundRecord,$operatorID);
            if($ifpack == 2 && $orderInfo['pack_order']){
                $mainOrder = $orderInfo['pack_order'];
                if(isset($masterRefundRecord)  && !empty($masterRefundRecord)){
                    $this->_notifyDownSystem($mainOrder, $masterRefundRecord,$operatorID);
                }
            }

        }
        $result = $result ? $result : 252;
        self::releaseRefundAuditLock($orderNum,$refundRecord['id']);
        return $result;
    }

    public function updateRefundAuditForApproval($orderNum, $auditResult, $auditNote, $operatorID, $auditID, $moreData = [])
    {
        pft_log('order/refund_audit', json_encode([$orderNum, $auditResult, $auditNote, $operatorID, $auditID, $moreData],JSON_UNESCAPED_UNICODE));
        $isApprovalFund = $moreData['isApprovalFund'] ?? false;
        $refundModel = self::getRefundAdultModel();
        if ($auditID == 0) {
            $refundRecord = $refundModel->getTerminalChangeInfo($orderNum, 0);
        } else {
            $refundRecord = $refundModel->getRefundAuditDetailById($auditID);
        }

        //如果该订单已经审核处理过了，就不再重复处理
        if (!$refundRecord || $refundRecord['dstatus'] != 0) {
            //审核数据已经处理过，不需要处理了
            $logData = json_encode([
                'status'   => 256,
                'ordernum' => $orderNum,
                'tnum'     => $refundRecord['tnum'],
                'refundRecord' => $refundRecord,
            ],JSON_UNESCAPED_UNICODE);
            pft_log('order/refund_audit', $logData);
            if($refundRecord['dstatus']==1 &&  $auditResult==2){
               //允许二次关闭拒绝
            }
            else{
                return 256;
            }
        }
        // 参数验证
        if (!$orderNum) {
            return 201;
        }

        //检查传入参数
        if (!in_array($auditResult, [1, 2])) {
            //审核结果只能是同意或拒绝
            return 250;
        }

        //如果传入的修改票数有问题
        $targetTnum = intval($refundRecord['tnum']);
        if ($targetTnum < 0) {
            return 208;
        }

        //参数初始化
        $result    = 0;
        $orderInfo = $refundModel->getPackOrderInfo($orderNum);
        if (!$orderInfo) {
            //订单信息不全
            return 205;
        }
        $orderModel = new OrderTools();


        //套票需特殊处理 ifpack : 0=正常票, 1=套票主票, 2=套票子票
        $ifpack = $orderInfo['ifpack'];
        /**
        审批中心支持套票审核
        if ($ifpack == 1) {
        //套票主票无人工审核权限
        return 255;
        }
         */
        //非审核中心进入且其存在流程节点视为三方取消，审核结果需要直接通知审批流程
        //ota的一定会先接入审批中心
        if($ifpack!=1 && !$isApprovalFund){
            $moreData['isThirdAuditRes'] = true;
        }

        self::refundAuditLock($orderNum,$refundRecord['id']);
        if (!$ifpack) {
            //ifpack=0 正常票
            $result = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, null, true, false, 0, $moreData);
            pft_log('order/refund', "$orderNum :" . json_encode($result));
            if( $result['code'] == 1095){
                //视为三方待审核
                $refundApprovalService = new RefundApprovalService();
                $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                $objectParams['orderNum'] = $orderNum;
                $nodeInfo = $refundApprovalService->getApprovalNodeInfo($objectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_BUSINESS);
                if(!$nodeInfo['exist_node']){
                    $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, 2, $auditNote,
                        $operatorID, $auditID, null, true, false, 0, $result['msg']);
                }
                return 240;
            }
            if ($result['code'] == 200) {
                if(!$isApprovalFund && $auditResult ==1){
                    $RefundApprovalService = new RefundApprovalService();
                    $RefundApprovalService->approvalNotifyForOTA($orderNum,$refundRecord['remote_sn'],$operatorID,$auditResult,$auditNote);
                }
                if(!$isApprovalFund && $auditResult ==2){
                    $refundApprovalService = new RefundApprovalService();
                    $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                    $objectParams['orderNum'] = $orderNum;
                    $nodeInfo = $refundApprovalService->getApprovalNodeInfo($objectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_PASS);
                    $processInstanceId = $nodeInfo['processInstanceId']?? '';
                    $refundApprovalService->cancelProcess($processInstanceId,$operatorID,$auditNote);
                    $refundApprovalService->updateProcessExt($processInstanceId,-1,6);
                }
            }
            else{
                if(in_array($result['code'] ,[1109,1096])){
                    $result = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, 2, $auditNote,
                        $operatorID, $auditID, null, true, false, 0, $result['msg']);
                    $refundApprovalService = new RefundApprovalService();
                    $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                    $objectParams['orderNum'] = $orderNum;
                    $nodeInfo = $refundApprovalService->getApprovalNodeInfo($objectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_PASS);
                    $processInstanceId = $nodeInfo['processInstanceId']?? '';
                    $refundApprovalService->cancelProcess($processInstanceId,$operatorID,$result['msg']);
                    $result = 257;
                    $this->_notifyDownSystem($orderNum, $refundRecord,$operatorID);
                    $refundApprovalService->updateProcessExt($processInstanceId,-1,6);
                }
                else{
                    return $result;
                }
            }

        }
        elseif($ifpack == 1){
            $isMainRefund  = $moreData['isMainRefund'];
            $lastCheck = $isMainRefund ? true : false;
            $onlyModify = false;
//            if($isMainRefund && ($auditResult == 1)){
//                $onlyModify = true;
//            }
            $updateResult = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, null, $lastCheck,$onlyModify,$moreData['operatorID'], $moreData);
            pft_log('order/refund', json_encode([$updateResult,$refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, null, $lastCheck,$onlyModify,$moreData['operatorID'], $moreData],JSON_UNESCAPED_UNICODE));
            if ($updateResult['code'] == 200 && $auditResult == 2) {
                $result = 200;
                self::rejectMainOrder($orderNum,$auditNote,$operatorID,$moreData);
                if(!$isApprovalFund){
                    $refundApprovalService = new RefundApprovalService();
                    $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                    $objectParams['orderNum'] = $orderNum;
                    $nodeInfo = $refundApprovalService->getApprovalNodeInfo($objectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_PASS);
                    $processInstanceId = $nodeInfo['processInstanceId']?? '';
                    $refundApprovalService->cancelProcess($processInstanceId,$operatorID,$auditNote);
                }
            }
        } else {
            // ifpack=2 套票子票
            //先更新审核记录,不具体操作订单取消或修改
            if($auditResult == 1){
                $BaseRefund = new BaseRefund();
                $isThirdAuditRes = $moreData['isThirdAuditRes'] ?? false;
                $thirdRes = $BaseRefund->thirdSystemRefund($orderNum,$operatorID,$moreData['refundParams'],$isThirdAuditRes);
                pft_log('order/refund',json_encode([$orderNum,$operatorID,$moreData['refundParams'],$thirdRes],JSON_UNESCAPED_UNICODE));
                if($thirdRes['code'] == 1095){
                    //视为三方待审核
                    return 240;
                }
                elseif($thirdRes['code'] == 200){
                    $updateResult = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                        $operatorID, $auditID, null, false,false,0,$moreData);
                    if ($updateResult['code'] == 200) {
                        $result = 200;
                        if(!$isApprovalFund){
                            //子票非审批流程审核 跳过前审节点
                            $refundApprovalService = new RefundApprovalService();
                            $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                            $objectParams['orderNum'] = $orderNum;
                            list($skipRes,$processInstanceId,$des) = $refundApprovalService->skipApprovalBeforeAudit($objectParams,$operatorID,$orderInfo['apply_did']);
                            if(!$skipRes){
                                pft_log('order/refund',json_encode(['跳过前审节点异常',$des,$processInstanceId,$orderNum,$operatorID,$objectParams,$orderInfo],JSON_UNESCAPED_UNICODE));
                            }
                        }
//                        if(isset($moreData['isThirdAuditRes']) && $moreData['isThirdAuditRes']){
//                            $RefundApprovalService = new RefundApprovalService();
//                            $RefundApprovalService->approvalNotifyForOTA($orderNum,$refundRecord['remote_sn'],$operatorID,$auditResult,$auditNote);
//                        }
                    }
                    else{
                        return $updateResult;
                    }
                }
                else{
                    //视为三方审核拒绝
                    $mainOrder = $orderInfo['pack_order'];
                    $auditNote = "系统:部分子票的退票申请被三方拒绝:{$orderNum}_{$thirdRes['code']}_{$thirdRes['msg']}";
                    $masterRefundRecord = $refundModel->isUnderAudit($mainOrder, $refundRecord['remote_sn']);
                    $result = $this->updateAudit($masterRefundRecord, 1, $mainOrder, $masterRefundRecord['tnum'],
                        2, $auditNote, 1, $masterRefundRecord['id'], null, true, false);
                    if($result['code'] == 200){
                        $packRefundApprovalService = new PackRefundApprovalService();
                        $objectParams['reqSerialNumber'] = $masterRefundRecord['remote_sn'];
                        $objectParams['orderNum'] = $mainOrder;
                        $judgeResult = $packRefundApprovalService->judgePackApproval($objectParams,$operatorID);
                        $refundApprovalService = new RefundApprovalService();
                        $processInstanceId = $judgeResult['main_approval_node_info']['processInstanceId'];
                        if($processInstanceId){
                            $refundApprovalService->cancelProcess($processInstanceId,$orderInfo['apply_did'],$auditNote);
                        }
//                        if(!$isApprovalFund){
                            //非审核调用
                            $refundApprovalService = new RefundApprovalService();
                            $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                            $objectParams['orderNum'] = $orderNum;
                            $nodeInfo = $refundApprovalService->getApprovalNodeInfo($objectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_PASS);
                            $processInstanceId = $nodeInfo['processInstanceId']?? '';
                            $refundApprovalService->cancelProcess($processInstanceId,$operatorID,$auditNote);
//                        }
                    }
                    self::rejectMainOrder($mainOrder,$auditNote,$operatorID,$moreData);
                    $result =  257;
                    $this->_notifyDownSystem($orderNum, $refundRecord,$operatorID);
                }
            }
            else{
                $updateResult = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                    $operatorID, $auditID, null, false,false,0,$moreData);
                if ($updateResult['code'] == 200) {
                    $result = 200;
                    if(!$isApprovalFund && $auditResult ==2){
                        $refundApprovalService = new RefundApprovalService();
                        $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
                        $objectParams['orderNum'] = $orderNum;
                        $nodeInfo = $refundApprovalService->getApprovalNodeInfo($objectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_PASS);
                        $processInstanceId = $nodeInfo['processInstanceId']?? '';
                        $refundApprovalService->cancelProcess($processInstanceId,$operatorID,$auditNote);
                        $mainOrder = $orderInfo['pack_order'];
                        self::rejectMainOrder($mainOrder,$auditNote,$operatorID,$moreData);
                        $result =  257;
                        $this->_notifyDownSystem($orderNum, $refundRecord,$operatorID);
                        $refundApprovalService->updateProcessExt($processInstanceId,-1,6);
                    }

                }
            }
        }
        //套票子票处理 - 还需要处理其他的子票
        // 子票流水号与主票一样

        if ($ifpack == 2 && $orderInfo['pack_order']) {
            $mainOrder = $orderInfo['pack_order'];
            $desc             = '系统:部分子票的退票申请被拒绝';
            $packRefundApprovalService = new PackRefundApprovalService();
            $objectParams['reqSerialNumber'] = $refundRecord['remote_sn'];
            $objectParams['orderNum'] = $mainOrder;
            $judgeResult = $packRefundApprovalService->judgePackApproval($objectParams,$operatorID);
            pft_log('order/refund',json_encode([$judgeResult,$mainOrder,$orderNum],JSON_UNESCAPED_UNICODE));
            if(!$judgeResult['main_approval_exist']){
                // 如果主票不存在审核记录，那么是因为单独取消子票
                $result = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                    $operatorID, $auditID, null, true, true,0,$moreData);
            }
            elseif($judgeResult['main_approval_audit_wait']){
                // 检测主票的审核记录是否存在
                $masterRefundRecord = $refundModel->isUnderAudit($mainOrder, $refundRecord['remote_sn']);
                //子票都已经审核通过，如果审核通过，将主票也审核通过
                $mainOrderData = $orderModel->getOrderInfo($mainOrder);
                $mainOperate   = $mainOrderData['apply_did'];
                //筛选过滤 未完结子票

                $allSubApprovalFinish = $judgeResult['all_sub_approval_finish'];
                if(!$allSubApprovalFinish){
                    $allSubApprovalArray = [];
                    foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                        if($subInfo['nodeInfo']['has_approval'] && $subInfo['nodeInfo']['exist_node']
                            && !$subInfo['nodeInfo']['son_wait_node'] && $orderNum!= $subInfo['orderNum']){
                            $allSubApprovalArray[$subInfo['orderNum']] = $processInstanceId;
                        }
                    }
                    if(count($allSubApprovalArray)<1){
                        $allSubApprovalFinish = true;
                    }
                }
                pft_log('order/refund',json_encode([$allSubApprovalArray, $allSubApprovalFinish, $mainOrder, $masterRefundRecord['tnum']],JSON_UNESCAPED_UNICODE));
                if($allSubApprovalFinish && $auditResult == 1){
                    $desc = '系统:全部子票通过退票审核';
                    $result = $this->updateAudit($masterRefundRecord, 1, $mainOrder, $masterRefundRecord['tnum'],
                        $auditResult, $desc, $mainOperate, $masterRefundRecord['id'], null, true,
                        false,$mainOperate);
                    if($result['code'] == 200){
                        $refundApprovalService = new RefundApprovalService();
                        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
                        $params = [
                            'approvalNoticeType' => $noticeType,
                            'processInstanceId' => $judgeResult['main_approval_node_info']['processInstanceId'],
                            'operatorID' => $mainOperate
                        ];
                        $refundApprovalService->approvalNotify($params,['code'=>200,'msg'=>$desc]);
                    }
                }
                elseif($auditResult == 1){
                    //视为单独通过某个子票审核通过
                    if(!$isApprovalFund){
                        $subOrderProcessInstanceId = '';
                        foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                            if($orderNum == $subInfo['orderNum']){
                                $subOrderProcessInstanceId = $processInstanceId;
                                break;
                            }
                        }
                        $RefundApprovalService = new RefundApprovalService();
                        $params = [
                            'approvalNoticeType' => $RefundApprovalService::NOTIFY_TYPE_BUSINESS,
                            'processInstanceId' => $subOrderProcessInstanceId,
                            'operatorID' => $operatorID
                        ];
                        $RefundApprovalService->approvalNotify($params,['code'=>243,'msg'=>(new Refund())->getMsg(243)]);
                    }
                    return 243;
                }
                elseif($auditResult == 2){
                    //视为查询整笔票拒绝
                    $refundApprovalService = new RefundApprovalService();
                    $desc = "{$desc}:{$orderNum}_{$auditNote}";
                    $result = $this->updateAudit($masterRefundRecord, 1, $mainOrder, $masterRefundRecord['tnum'],
                        $auditResult, $desc, $mainOperate, $masterRefundRecord['id'], null, true, false,$mainOperate);
                    pft_log('order/refund',json_encode(['rejectSonOrder',$masterRefundRecord, 1, $mainOrder, $masterRefundRecord['tnum'],
                        $auditResult, $desc, $mainOperate, $masterRefundRecord['id'], null, true, false,$mainOperate, $mainOperate,$result],JSON_UNESCAPED_UNICODE));
                    if($result['code'] == 200){
                        //主票审核直接关闭
                        $refundApprovalService->cancelProcess($judgeResult['main_approval_node_info']['processInstanceId'],$mainOperate,$desc);
//                        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
//                        $params = [
//                            'approvalNoticeType' => $noticeType,
//                            'processInstanceId' => $judgeResult['main_approval_node_info']['processInstanceId'],
//                            'operatorID' => $mainOperate
//                        ];
//                        $refundApprovalService->approvalNotify($params,['code'=>254,'msg'=>$desc]);
                        // 联动子票拒绝
                        foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                            $subOrderNum = $subInfo['orderNum'];
                            if($orderNum == $subOrderNum){
                                continue;
                            }
                            $subRefundRecord = $refundModel->getTerminalChangeInfo($subOrderNum, 0);
                            if (!$subRefundRecord) {
                                $subRefundRecord = $refundModel->getTerminalChangeInfo($subOrderNum, 1);
                                if (!$subRefundRecord) {
                                    pft_log('order/refund',json_encode(['rejectSonOrder111',$subOrderNum,$subRefundRecord],JSON_UNESCAPED_UNICODE));
                                    continue;
                                }
                            }
                            $orderInfo = $refundModel->getPackOrderInfo($subOrderNum);
                            if (!$orderInfo) {
                                //订单信息不全
                                continue;
                            }
                            $subIfPack = $orderInfo['ifpack'];
                            $subTargetTnum = intval($subRefundRecord['tnum']);
                            $subAuditID = $subRefundRecord['id'];
                            $updateResult = $this->updateAudit($subRefundRecord, $subIfPack, $subOrderNum, $subTargetTnum, 2, $desc,
                                1, $subAuditID, null, true, false,0,$moreData);
                            pft_log('order/refund',json_encode(['rejectSonOrder',$updateResult,$subOrderNum,$subRefundRecord],JSON_UNESCAPED_UNICODE));
                            if($updateResult['code']==200){
                                $refundApprovalService->cancelProcess($processInstanceId,$mainOperate,$desc);
                            }
                        }
                    }
                }
            }
            elseif(!$judgeResult['main_approval_audit_wait']){
                return 250;
            }
            else{
                if(!$isApprovalFund){
                    $subOrderProcessInstanceId = '';
                    foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                        if($orderNum == $subInfo['orderNum']){
                            $subOrderProcessInstanceId = $processInstanceId;
                            break;
                        }
                    }
                    $RefundApprovalService = new RefundApprovalService();
                    $params = [
                        'approvalNoticeType' => $RefundApprovalService::NOTIFY_TYPE_BUSINESS,
                        'processInstanceId' => $subOrderProcessInstanceId,
                        'operatorID' => $operatorID
                    ];
                    $RefundApprovalService->approvalNotify($params,['code'=>243,'msg'=>(new Refund())->getMsg(243)]);
                }
                return 243;
            }
            /**
            // 获取子票订单号
            $ordersAutoUpdate = $orderModel->getPackSubOrder($mainOrder);
            $ordersAutoUpdate = array_column($ordersAutoUpdate, 'orderid');
            $desc             = '系统:部分子票的退票申请被拒绝';
            if ($auditResult == 1) {
            $desc = '系统:全部子票通过退票审核';
            // 检测是否所有子票都已经审核通过，如果审核通过，将主票也审核通过
            if ($refundModel->hasUnAuditSubOrder($ordersAutoUpdate, $refundRecord['remote_sn'])) {
            $result = 243;

            return $result;
            }
            }
            // 检测主票的审核记录是否存在
            $masterRefundRecord = $refundModel->isUnderAudit($mainOrder, $refundRecord['remote_sn']);
            if ($masterRefundRecord) {
            //这边主票在获取下订单信息看看是不是和子票是一个供应商
            $mainOrderData = $orderModel->getOrderInfo($mainOrder);
            $mainOperate   = 0;
            if ($mainOrderData['apply_did'] != $orderInfo['apply_did']) {
            $mainOperate = $mainOrderData['apply_did'];  //主票取消的操作员覆盖成主票供应商
            }
            $result = $this->updateAudit($masterRefundRecord, 1, $mainOrder, $masterRefundRecord['tnum'],
            $auditResult, $desc, $operatorID, $masterRefundRecord['id'], null, true, false, $mainOperate);
            } else {
            // 如果主票不存在审核记录，那么是因为单独取消子票
            $result = $this->updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
            $operatorID, $auditID, null, true, true);
            }
             */
        }

        $data = [
            'checkState' => $auditResult == 1 ? 2 : 0,
        ];
        $map  = [
            'checkState' => 3,
        ];

        //更新订单游客信息
        $api = new \Business\JavaApi\Order\OrderTouristUpdate();
        $api->touristInfoUpdate($orderNum, $data, $map);
        pft_log('order_refund/debug', json_encode(['touristInfoUpdate_params_audit', $orderNum, $data, $map]));

        //如果是退票拒绝，需要通知下游
        if (is_array($result)) {
            $isSuccess = $result['code'] == 200 ? true : false;
        } else {
            $isSuccess = $result == 200 ? true : false;
        }
        //审核类型
        $sType = $refundRecord['stype'];
        if ($auditResult == 2 && $isSuccess && in_array($sType,[self::MODIFY_CODE,self::CANCEL_CODE])) {
            $this->_notifyDownSystem($orderNum, $refundRecord,$operatorID);
            if($ifpack == 2 && $orderInfo['pack_order']){
                $mainOrder = $orderInfo['pack_order'];
                if(isset($masterRefundRecord)  && !empty($masterRefundRecord)){
                    $this->_notifyDownSystem($mainOrder, $masterRefundRecord,$operatorID);
                }
            }

        }
        $result = $result ? $result : 252;
        self::releaseRefundAuditLock($orderNum,$refundRecord['id']);
        return $result;
    }

    public function rejectMainOrder($mainOrderNum,$auditNote,$mainOperate,$moreData){
        //视为查询整笔票拒绝
        $desc             = '系统:部分子票的退票申请被拒绝';
        $refundModel = self::getRefundAdultModel();
        $refundApprovalService = new RefundApprovalService();
        $packRefundApprovalService = new PackRefundApprovalService();
        $desc = "{$desc}:{$mainOrderNum}_{$auditNote}";
        $masterRefundRecord = $refundModel->getTerminalChangeInfo($mainOrderNum, 2);
        if(empty($masterRefundRecord)){
            $masterRefundRecord = $refundModel->getTerminalChangeInfo($mainOrderNum, 1);
        }
        if(empty($masterRefundRecord)){
            $masterRefundRecord = $refundModel->getTerminalChangeInfo($mainOrderNum, 0);
        }
        $objectParams['reqSerialNumber'] = $masterRefundRecord['remote_sn'];
        $objectParams['orderNum'] = $mainOrderNum;
        $judgeResult = $packRefundApprovalService->judgePackApproval($objectParams,$mainOperate);
        pft_log('order/refund',json_encode(['rejectMainOrder',$mainOrderNum,$objectParams,$judgeResult],JSON_UNESCAPED_UNICODE));
        if(!empty($judgeResult['sub_approval_node_info'])){
            // 联动子票拒绝
            foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                $subOrderNum = $subInfo['orderNum'];
                $subRefundRecord = $refundModel->getTerminalChangeInfo($subOrderNum, 0);
                if (!$subRefundRecord) {
                    $subRefundRecord = $refundModel->getTerminalChangeInfo($subOrderNum, 1);
                    if (!$subRefundRecord) {
                        continue;
                    }
                }
                $orderInfo = $refundModel->getPackOrderInfo($subOrderNum);
                if (!$orderInfo) {
                    //订单信息不全
                    continue;
                }
                $subIfPack = $orderInfo['ifpack'];
                $subTargetTnum = intval($subRefundRecord['tnum']);
                $subAuditID = $subRefundRecord['id'];
                $updateResult = $this->updateAudit($subRefundRecord, $subIfPack, $subOrderNum, $subTargetTnum, 2, $desc,
                    1, $subAuditID, null, true, false,0,$moreData);
                pft_log('order/refund',json_encode([$updateResult,$subOrderNum,$subRefundRecord],JSON_UNESCAPED_UNICODE));
                if($updateResult['code']==200){
                    $refundApprovalService->cancelProcess($processInstanceId,$subRefundRecord['apply_did'],$desc);
                }
            }
        }
    }

    /**
     * 更新退票审核结果
     *
     * @param  array  $refundRecord  审核数据详情
     * @param  int  $ifpack
     * @param  string  $orderNum
     * @param  int  $targetTnum
     * @param  int  $auditResult  审核结果:1=通过，2=不通过
     * @param  string  $auditNote  审核备注
     * @param  int  $operatorID  操作人员
     * @param  int  $auditID  审核记录ID
     * @param  null  $sTime
     * @param  bool  $lastCheck  控制是否需要执行具体的取消、修改程序
     * @param  bool  $onlyModify  是否只执行退票或取消的操作，不做记录更新、追踪记录
     * @param  int  $mainOperator  主票操作人（如果是分销的子票最后一个子票点击同意使用这个字段的操作人）
     *
     * @return array
     */
    private function updateAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
        $operatorID = 1, $auditID = 0, $sTime = null, $lastCheck = true, $onlyModify = false, $mainOperator = 0, $moreData = [])
    {
        $subOrderName = $ifpack != 0 ? '子票' : '权益订单';
        if ($auditResult == 1) {
            $res = $this->baseAgreeAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, $sTime, $lastCheck, $onlyModify, $mainOperator, $moreData);
            if(!in_array($res['code'],[200,1095])){
                $msg = $subOrderName . "供应商审核退票失败，原因：{$res['msg']}";
                goto AFTER_SALE_NOTICE;

            }
        } else {
            $res = $this->baseRejectAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, $sTime, $lastCheck, $onlyModify, $mainOperator, $moreData);
            if($res['code'] == 200){
                $msg = $subOrderName. "供应商拒绝，拒绝原因：{$auditNote}";
            }
            else{
                $msg = $subOrderName. "供应商审核退票失败，原因：{$res['msg']}";
            }
            if($ifpack==1){
                //旧版主票没有审核，子票拒绝联动主票拒绝。 新版有主票审核，亦视为审批流触发的异常
                //记录异常日志
                $logParams = [
                    'ac'     => 'updateAudit_main',
                    'params' => [$refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                        $operatorID, $auditID, $sTime, $lastCheck, $onlyModify, $mainOperator, $moreData],
                ];
                $logResponse = [
                    'ac'        => 'updateAudit_main',
                    'handleRes' => $res,
                    'response'   => $msg,
                ];

                $this->recordAnAbnormal($orderNum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                    PackConst::ABNORMAL_REASON_AUDIT_ERROR,PackConst::ABNORMAL_LINKAGE_YES,$msg,$mainOperator);
            }
            AFTER_SALE_NOTICE:
            if($lastCheck){
                $auditData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
                if(isset($auditData['after_sale_num']) && !empty($auditData['after_sale_num'])){
                    $linkTouristIdx = reset($auditData['tourist_idx']);
                    $idx = is_array($linkTouristIdx) ? $linkTouristIdx['idx'] :$linkTouristIdx;
                    $cancelType = in_array($refundRecord['stype'] ,[0,1]) ? 'revoke':'common';
                    $dataKafka = [
                        'op_id' => $operatorID,
                        'refund_status'  => 2,// 0审核中 1退票成功  2失败
                        'after_sale_num' => $auditData['after_sale_num'],
                        'req_serial_number' => strval($refundRecord['remote_sn'] ?? $refundRecord['system_sn']),
                        'order_num' => $orderNum,
                        'refund_idx' => empty($idx) ? ["1"]:[strval($idx)],
                        'cancel_type' => $cancelType,
                        'order_status' => '',
                        'cancel_num' => $refundRecord['modify_tnum'] ?? 0,
                        'left_num' =>  $refundRecord['tnum'] ?? 0,
                        'is_retry' => $auditData['is_retry'] ?? false,
                        'msg' => $msg
                    ];
                    pft_log('debug/refundKafka', json_encode(['售后-退票审核', $dataKafka,$res,$refundRecord],JSON_UNESCAPED_UNICODE));
                    \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
                }
            }
        }
        return $res;
    }

    /**
     * 审核通过
     * <AUTHOR>
     * @date 2022/11/14
     *
     * @return array
     */
    private function baseAgreeAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
        $operatorID = 1, $auditID = 0, $sTime = null, $lastCheck = true, $onlyModify = false, $mainOperator = 0, $moreData = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::baseAgreeAuditForApproval($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, $sTime, $lastCheck, $onlyModify, $mainOperator, $moreData);
        }
        //参数初始化
        $refundModel = self::getRefundAdultModel();
        $source      = 16;
        if ($mainOperator) {
            $operatorID = $mainOperator;
        }
        $return = $refundModel->updateAudit($orderNum, $auditResult, $auditNote, $operatorID, $auditTime = 0, $auditID,
            $refundRecord['remote_sn']);
        if (!$return && !$onlyModify) {
            return ['code' => 201, 'msg' => '更新审批操作失败'];
        }

        //获取订单数据
        $orderInfo  = $refundModel->getOrderInfoForAudit($orderNum);
        $targetTnum = $orderInfo['tnum'] - $refundRecord['modify_tnum'];

        if (!$onlyModify) {
            $haveAuditNum = 0;
            $auditData    = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            foreach ($auditData as $key => $value) {
                if ($value['id'] != $refundRecord['id'] && isset($refundRecord['id'])) {
                    $haveAuditNum += $value['modify_tnum'];
                }
            }

            $action = $refundRecord['p_type'] == 'J' ? self::SPECIAL_AGREE_AUDIT_CODE : self::AGREE_AUDIT_CODE;
            $extContent = [];
            $authData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
            if(isset($authData['discount']['serialNumber'])){
                $serialNumber = $authData['discount']['serialNumber'];
                $extContent['serial_number'] = $serialNumber;
            }
            if (isset($moreData['subSid']) && isset($moreData['subOpId'])){
                $extContent['subSid'] = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            $addAuditTrackArr = $this->addRefundAuditOrderTrack($orderNum, $source, $operatorID, $action, $auditResult,
                $targetTnum, $orderInfo, $auditNote, $haveAuditNum,$extContent);
        }

        if ($lastCheck === false) {
            return ['code' => 200, 'msg' => '操作成功'];
        }

        $isNeedAuth  = $ifpack == 1 ? true : false;
        $authData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
        $otherParams = [
            'audit_data' => $authData,
        ];
        if (isset($moreData['subSid']) && isset($moreData['subOpId'])){
            $otherParams['subSid'] = $moreData['subSid'];
            $otherParams['subOpId'] = $moreData['subOpId'];
        }

        if ($targetTnum == 0) {
            $result = $this->_cancelOrder($orderNum, $refundRecord['remote_sn'], true, $isNeedAuth,
                $mainOperator, $otherParams);
        } else {
            //lc切换取消  彬哥说不存在增加票数审核的业务 所以票数 = 本次修改的票数
            $result = $this->_modifyOrder($orderNum, $refundRecord['modify_tnum'], $refundRecord['remote_sn'],
                true, $isNeedAuth, $mainOperator, $otherParams);
        }
        // 审核失败 更新审核记录字段未审核 -- 删除同意记录
        if ($result['code'] != 200) {
            $upNoAudit = $refundModel->updateAudit($orderNum, 0, $auditNote, $operatorID, $auditTime = 0, $auditID,
                $refundRecord['remote_sn'], 1);
            if (!$onlyModify && $upNoAudit && $addAuditTrackArr['code'] == 200) {
                //模型
                $trackModel  = new OrderTrack();
                $esSyncModel = new EsSyncTask();

                $trackId     = $addAuditTrackArr['data'];
                $delTrackRes = $trackModel->delTrack($trackId);

                if ($delTrackRes) {
                    // 删除es数据 更新es_sync_status flag = 1的待删除状态
                    $esSyncModel->delEsTrack($trackId);
                }
            }
            $msg = isset($result['msg']) ? $result['msg'] : '审核取消失败';
            if($ifpack==2){
                //记录异常日志
                $logParams = [
                    'ac'     => 'baseAgreeAudit',
                    'params' => [$orderNum,$refundRecord,$isNeedAuth,$targetTnum,$mainOperator,$otherParams],
                ];
                $logResponse = [
                    'ac'        => 'baseAgreeAudit_response',
                    'handleRes' => $result,
                    'respone'   => $msg,
                ];

                $this->recordAnAbnormal($orderNum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                    PackConst::ABNORMAL_REASON_AUDIT_ERROR,PackConst::ABNORMAL_LINKAGE_YES,$msg,$mainOperator);
            }

            return ['code' => $result['code'] == 1095 ?: 201, 'msg' => $msg];
        } else {
            return $result;
        }
    }

    private function baseAgreeAuditForApproval($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                                    $operatorID = 1, $auditID = 0, $sTime = null, $lastCheck = true, $onlyModify = false, $mainOperator = 0, $moreData = [])
    {
        //参数初始化
        $refundModel = self::getRefundAdultModel();
        $source      = 16;
        if ($mainOperator) {
            $operatorID = $mainOperator;
        }
        $return = $refundModel->updateAudit($orderNum, $auditResult, $auditNote, $operatorID, $auditTime = 0, $auditID,
            $refundRecord['remote_sn']);
        if (!$return && !$onlyModify) {
            return ['code' => 201, 'msg' => '更新审批操作失败'];
        }

        //获取订单数据
        $orderInfo  = $refundModel->getOrderInfoForAudit($orderNum);
        $targetTnum = $orderInfo['tnum'] - $refundRecord['modify_tnum'];

        if (!$onlyModify) {
            $haveAuditNum = 0;
            $auditData    = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            foreach ($auditData as $key => $value) {
                if ($value['id'] != $refundRecord['id'] && isset($refundRecord['id'])) {
                    $haveAuditNum += $value['modify_tnum'];
                }
            }

            $action = $refundRecord['p_type'] == 'J' ? self::SPECIAL_AGREE_AUDIT_CODE : self::AGREE_AUDIT_CODE;
            $extContent = [];
            $authData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
            if(isset($authData['discount']['serialNumber'])){
                $serialNumber = $authData['discount']['serialNumber'];
                $extContent['serial_number'] = $serialNumber;
            }
            if (isset($moreData['subSid']) && isset($moreData['subOpId'])){
                $extContent['subSid'] = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            $addAuditTrackArr = $this->addRefundAuditOrderTrack($orderNum, $source, $operatorID, $action, $auditResult,
                $targetTnum, $orderInfo, $auditNote, $haveAuditNum,$extContent);
        }

        if ($lastCheck === false) {
            return ['code' => 200, 'msg' => '操作成功'];
        }

        $isNeedAuth  = $ifpack == 1 ? true : false;
        $authData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
        $otherParams = $moreData;
        if(!empty($authData)){
            $otherParams['audit_data'] = $authData;
        }
        if (isset($moreData['subSid']) && isset($moreData['subOpId'])){
            $otherParams['subSid'] = $moreData['subSid'];
            $otherParams['subOpId'] = $moreData['subOpId'];
        }

        if ($targetTnum == 0) {
            $result = $this->_cancelOrder($orderNum, $refundRecord['remote_sn'], true, $isNeedAuth,
                $mainOperator, $otherParams);
        } else {
            //lc切换取消  彬哥说不存在增加票数审核的业务 所以票数 = 本次修改的票数
            $result = $this->_modifyOrder($orderNum, $refundRecord['modify_tnum'], $refundRecord['remote_sn'],
                true, $isNeedAuth, $mainOperator, $otherParams);
        }
        // 审核失败 更新审核记录字段未审核 -- 删除同意记录
        if ($result['code'] != 200) {
            $auditResult  = 0 ;
            $upNoAudit = $refundModel->updateAudit($orderNum, $auditResult, $auditNote, $operatorID, $auditTime = 0, $auditID,
                $refundRecord['remote_sn'], 1);
            if (!$onlyModify && $upNoAudit && $addAuditTrackArr['code'] == 200) {
                //模型
                $trackModel  = new OrderTrack();
                $esSyncModel = new EsSyncTask();

                $trackId     = $addAuditTrackArr['data'];
                $delTrackRes = $trackModel->delTrack($trackId);

                if ($delTrackRes) {
                    // 删除es数据 更新es_sync_status flag = 1的待删除状态
                    $esSyncModel->delEsTrack($trackId);
                }
            }
            if(in_array($result['data']['err_code'] ,[1109,1096])){
                return ['code' => $result['data']['err_code'], 'msg' => $result['data']['err_msg']];
            }
            if($result['code'] == 1095){
                return ['code' => $result['code'], 'msg' => $result['msg']];
            }
            else{
                // 返回前端审核失败
                $msg = isset($result['msg']) ? $result['msg'] : '审核取消失败';
                if($ifpack){
                    //记录异常日志
                    $logParams = [
                        'ac'     => 'baseAgreeAudit_approval',
                        'params' => [$orderNum,$refundRecord,$isNeedAuth,$targetTnum,$mainOperator,$otherParams],
                    ];
                    $logResponse = [
                        'ac'        => 'baseAgreeAudit_approval_response',
                        'handleRes' => $result,
                        'respone'   => $msg,
                    ];

                    $this->recordAnAbnormal($orderNum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                        PackConst::ABNORMAL_REASON_AUDIT_ERROR,PackConst::ABNORMAL_LINKAGE_YES,$msg,$mainOperator);
                }
                return ['code' => 201, 'msg' => $msg];
            }

        } else {
            return $result;
        }
    }

    /**
     * 审核拒绝
     * <AUTHOR>
     * @date 2022/11/14
     *
     * @return array
     */
    private function baseRejectAudit($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
        $operatorID = 1, $auditID = 0, $sTime = null, $lastCheck = true, $onlyModify = false, $mainOperator = 0, $moreData = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::baseRejectAuditForApproval($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                $operatorID, $auditID, $sTime, $lastCheck, $onlyModify, $mainOperator, $moreData);
        }
        //参数初始化
        $refundModel = self::getRefundAdultModel();
        $source      = 16;
        if ($mainOperator) {
            $operatorID = $mainOperator;
        }
        $return = $refundModel->updateAudit($orderNum, $auditResult, $auditNote, $operatorID, $auditTime = 0, $auditID,
            $refundRecord['remote_sn']);
        if (!$return && !$onlyModify) {
            return ['code' => 201, 'msg' => '更新审批操作失败'];
        }

        //获取订单数据
        $orderInfo  = $refundModel->getOrderInfoForAudit($orderNum);
        $targetTnum = $orderInfo['tnum'] - $refundRecord['modify_tnum'];

        if (!$onlyModify) {
            $haveAuditNum = 0;
            $auditData    = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            foreach ($auditData as $key => $value) {
                if ($value['id'] != $refundRecord['id'] && isset($refundRecord['id'])) {
                    $haveAuditNum += $value['modify_tnum'];
                }
            }

            $extContent =[];
            $action = $refundRecord['p_type'] == 'J' ? self::SPECIAL_REFUSE_AUDIT_CODE : self::REFUSE_AUDIT_CODE;
            $authData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
            if(isset($authData['discount']['serialNumber'])){
                $serialNumber = $authData['discount']['serialNumber'];
                $extContent['serial_number'] = $serialNumber;
                //释放优惠 - 中台锁
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $arrSerial = $orderTicketDiscounts->_recoverDiscountSerialNumber($serialNumber);
                $orderTicketDiscounts->releaseLock($orderNum, $arrSerial);
            }
            if (isset($moreData['subSid']) && isset($moreData['subOpId'])){
                $extContent['subSid'] = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            $addAuditTrackArr = $this->addRefundAuditOrderTrack($orderNum, $source, $operatorID, $action, $auditResult,
                $targetTnum, $orderInfo, $auditNote, $haveAuditNum,$extContent);
        }
        if ($lastCheck === false) {
            return ['code' => 200, 'msg' => '操作成功'];
        }

        if ($orderInfo['callback'] == 1 && $ifpack != 2) {
            //如果拒绝订单数量要以原有数量通知 by-pzp 20160816
            $notifyNum = $orderInfo['tnum'];
            $this->noticeAuditResult('reject', $orderNum, $notifyNum, $auditResult, $auditNote,
                $refundRecord['remote_sn']);
        }
        $orderRefundModel = new OrderRefund();
        $orderRefundRes = $orderRefundModel->getRefundJournal($orderNum,$refundRecord['remote_sn']);
        if(!empty($orderRefundRes)){
            $result = $orderRefundModel->updateRefundJournal($orderRefundRes['id'], $orderRefundModel::REFUND_REFUSE,
                '退票审核拒绝');
            if (!$result) {
                return $this->returnData(401, '服务器异常，拒绝失败');
            }
        }
        
        // ============ 新增：发送审核驳回 Kafka 通知（仅预售券兑换订单） ============
        if ($orderInfo['ordermode'] == OrderChannel::EXCHANGE_COUPON_REDEEM_CHANNEL) {
            $details = json_decode($orderRefundRes['details'], true);
            $cancelChannel = $details['channel'];
            \Business\Order\ExchangeBaseTicket\KafkaNotificationService::sendAuditRejectNotification($cancelChannel,
                $orderNum, $refundRecord['remote_sn'], $orderRefundRes['dtype'], $operatorID, $auditNote);
        }
        // ==================================================================
        
        return ['code' => 200, 'msg' => '操作成功'];
    }

    private function baseRejectAuditForApproval($refundRecord, $ifpack, $orderNum, $targetTnum, $auditResult, $auditNote,
                                     $operatorID = 1, $auditID = 0, $sTime = null, $lastCheck = true, $onlyModify = false, $mainOperator = 0, $moreData = [])
    {
        //参数初始化
        $refundModel = self::getRefundAdultModel();
        $source      = 16;
        if ($mainOperator) {
            $operatorID = $mainOperator;
        }
        $return = $refundModel->updateAudit($orderNum, $auditResult, $auditNote, $operatorID, 0, $auditID,
            $refundRecord['remote_sn'],0,[0,1]);
        if (!$return && !$onlyModify) {
            return ['code' => 201, 'msg' => '更新审批操作失败'];
        }

        //获取订单数据
        $orderInfo  = $refundModel->getOrderInfoForAudit($orderNum);
        $targetTnum = $orderInfo['tnum'] - $refundRecord['modify_tnum'];

        if (!$onlyModify) {
            $haveAuditNum = 0;
            $auditData    = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
            foreach ($auditData as $key => $value) {
                if ($value['id'] != $refundRecord['id'] && isset($refundRecord['id'])) {
                    $haveAuditNum += $value['modify_tnum'];
                }
            }

            $extContent =[];
            $action = $refundRecord['p_type'] == 'J' ? self::SPECIAL_REFUSE_AUDIT_CODE : self::REFUSE_AUDIT_CODE;
            $authData    = empty($refundRecord['audit_data']) ? [] : json_decode($refundRecord['audit_data'], true);
            if(isset($authData['discount']['serialNumber'])){
                $serialNumber = $authData['discount']['serialNumber'];
                $extContent['serial_number'] = $serialNumber;
                //释放优惠 - 中台锁
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $arrSerial = $orderTicketDiscounts->_recoverDiscountSerialNumber($serialNumber);
                $orderTicketDiscounts->releaseLock($orderNum, $arrSerial);
            }
            if (isset($moreData['subSid']) && isset($moreData['subOpId'])){
                $extContent['subSid'] = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            $addAuditTrackArr = $this->addRefundAuditOrderTrack($orderNum, $source, $operatorID, $action, $auditResult,
                $targetTnum, $orderInfo, $auditNote, $haveAuditNum,$extContent);
        }
        if ($lastCheck === false) {
            return ['code' => 200, 'msg' => '操作成功'];
        }

        if ($orderInfo['callback'] == 1 && $ifpack != 2) {
            //如果拒绝订单数量要以原有数量通知 by-pzp 20160816
            $notifyNum = $orderInfo['tnum'];
            $this->noticeAuditResult('reject', $orderNum, $notifyNum, $auditResult, $auditNote,
                $refundRecord['remote_sn']);
        }
        $orderRefundModel = new OrderRefund();
        $orderRefundRes = $orderRefundModel->getRefundJournal($orderNum,$refundRecord['remote_sn']);
        if(!empty($orderRefundRes)){
            $result = $orderRefundModel->updateRefundJournal($orderRefundRes['id'], $orderRefundModel::REFUND_REFUSE,
                '退票审核拒绝');
            if (!$result) {
                return $this->returnData(401, '服务器异常，拒绝失败');
            }
        }

        return ['code' => 200, 'msg' => '操作成功'];
    }

    /**
     * 拒绝退款审核通知相应的供应商
     *
     * @param  int  $action
     * @param  int  $ordernum
     * @param  int  $targetTicketNum
     * @param  string  $auditResult  审核结果
     * @param  string  $remoteSn  第三方退款流水号
     *
     * @return array
     */
    private function noticeAuditResult($action, $ordernum, $targetTicketNum, $auditResult = null, $auditNote = '', $remoteSn = '')
    {
        $refundResult = 2;
        if ($action == 'reject') {
            $refundResult = 2;
        } elseif ($action == 'repush') {
            if ($auditResult == 1) {
                $refundResult = 1;
            }
        }

        $noticeBiz = new Buyer();
        $res       = $noticeBiz->refund($ordernum, $targetTicketNum, $refundResult, $refundMoney = 0, $refundFee = 0,
            $auditNote, $remoteSn);
        $code      = $res['code'];
        $msg       = $res['msg'];
        if ($code == 200) {
            return ['code' => 200];
        } else {
            pft_log('order/refund_audit', json_encode([$ordernum, $targetTicketNum, $auditResult, $auditNote, $res]));
            //return ['code' => 252];
            //这边都是订单通知下单OTA商家，失败了记日志
            return ['code' => 200];
        }
    }
    /**
     * 添加订单追踪记录
     *
     * @param  string  $orderNum  订单ID
     * @param  int  $source  来源
     * @param  int  $operatorID  操作ID
     * @param  int  $action  10-提交退票请求 11-操作退票审核
     * @param  int  $auditStatus  审核状态
     * @param  int  $targetTicketNum  操作的票数
     * @param  array  $orderInfo  订单信息
     *         [
     *             'status'     => 1,
     *             'salerid'    => 1,
     *             'tnum'       => 1,
     *             'tid'        => 200031,
     *             'lid'        => 111
     *         ]
     * @param  string|null  $msg  备注
     *
     * @return mixed
     */
    private function addRefundAuditOrderTrack($orderNum, $source, $operatorID, $action, $auditStatus, $targetTicketNum, $orderInfo, $msg = null,$haveAuditNum = 0,$extContent =[])
    {
        if (!in_array($auditStatus, [0, 1, 2])) {
            return ['code' => 208];
        }
        $orderModel = new OrderTools();
        try {
            $tNumOperate = $orderInfo['tnum'] - $targetTicketNum;
            if ($orderInfo['status'] == 7 || $orderInfo['status'] == 2) {
                $verifyNum = $orderModel->getVerifiedNum($orderNum);
                if ($verifyNum) {
                    $tNumCanBeModified = $orderInfo['tnum'] - $verifyNum;
                } else {
                    $tNumCanBeModified = $orderInfo['tnum'];
                }
            } else {
                $tNumCanBeModified = $orderInfo['tnum'];
            }

            switch ($auditStatus) {
                case 0:
                case 1:
                    $remainTicketNum = $tNumCanBeModified - $tNumOperate;
                    break;
                case 2:
                    $remainTicketNum = $tNumCanBeModified - $haveAuditNum;
                    break;
            }
            $trackModel = new OrderTrack();
            //if ($this->isLogin('ajax', false) !== false && in_array($action, [13, 14])) {
            //    $loginInfoArr = $this->getLoginInfo();
            //    $operatorID   = $loginInfoArr['memberID'];
            //}
            $addTrack = $trackModel->addTrack($orderNum, $action, $orderInfo['tid'], $tNumOperate, $remainTicketNum,
                $source, 0, 0, 0, $operatorID, $orderInfo['salerid'], $create_time = '', $msg ,0,false,$extContent);
            $result   = $addTrack ? ['code' => 200, 'data' => $addTrack] : ['code' => 244];

            return $result;
        } catch (\Exception $e) {
            return ['code' => 244];
        }
    }
    private function _cancelOrder($orderNum, $serialNumber, $auditPass = false, $isNeedAuth = false,$mainOperator = 0, $otherParams = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::_cancelOrderForApproval($orderNum, $serialNumber, $auditPass, $isNeedAuth,$mainOperator, $otherParams);
        }
        $biz = new Modify();
        //初始化两个操作人员id跟主账号id
        $operatorID    = 0;
        $parentId      = 0;
        $cancelChannel = OrderConst::VERIFY_CANCEL;//审核取消渠道

        //取审核记录表中的数据
        $trackSource = $otherParams['audit_data']['track_source'] ?? 0;
        if ($trackSource) {
            $ordermodeArr = load_config('channel_track_cancel_map', 'business');
            $cancelChannel = $ordermodeArr[$trackSource] ?? $cancelChannel;
        }

        //之前是是否通过审核，现在是是否需要审核
        $auditPass = $auditPass ? false : true;
        //获取订单详情
        $orderOrderToolsMode = new OrderTools();
        $info                = $orderOrderToolsMode->getOrderInfo($orderNum, 'ordermode');
        //如果订票的渠道来自云票务
        if ($info['ordermode'] == 10 || $trackSource == 4) { //加上$trackSource，否则云票务计调下单ordermode==44走不进这里，导致最终操作人不对
            $operatorID = $otherParams['audit_data']['op_id'] ?? 0; //正常来说，云票务走到这里的时候，审核记录里一定有op_id，防止有未考虑到场景，做一次旧逻辑兼容吧
            if (!$operatorID) {
                //订单查询迁移二期
                $orderTrackQueryLib = new OrderTrackQuery();
                $trackInfo  = $orderTrackQueryLib->getTrackInfoByOrderAction([strval($orderNum)], [11]);
                $operatorID = $trackInfo['oper_member'];
            }

            //获取下单人员信息
            $memberMode = new Member();
            $memberInfo = $memberMode->getMemberInfo($operatorID, 'id', 'dtype');
            //如果下单人员为员工账号，则获取主账号id
            if ($memberInfo['dtype'] == 6) {
                //获取主账号id
                $parentInfo = $memberMode->getStaffBySonId($operatorID);
                $parentId   = $parentInfo['parent_id'];
                //lc修改切换
                $ret = $biz->auditPassCancel($orderNum, $isNeedAuth, $operatorID, $parentId, $cancelChannel, '',
                    $serialNumber, $auditPass,false,true, $otherParams);
            } else {
                $ret = $biz->auditPassCancel($orderNum, $isNeedAuth, $operatorID, $operatorID, $cancelChannel, '',
                    $serialNumber, $auditPass,false,true, $otherParams);
            }
        } else {
            $opId = $mainOperator ?: $this->loginInfo['memberID'];
            $parentId = $mainOperator ?:  $this->loginInfo['sid'];
            if (MemberLoginHelper::isSubMerchantLogin()){
                $opId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $parentId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }
            $ret = $biz->auditPassCancel($orderNum, $isNeedAuth, $opId, $parentId,
                $cancelChannel, '', $serialNumber, $auditPass,false,true, $otherParams);
            //$ret        = $biz->cancelBaseFormat($orderNum,$this->loginInfo['memberID'],$this->loginInfo['sid'],$cancelChannel,'',$serialNumber,$auditPass);
            //   $ret = $biz->cancel($orderNum, $this->loginInfo['sid'], $this->loginInfo['memberID'], $this->loginInfo['dtype'], $auditPass, $serialNumber);
        }
        if ($ret['code'] == 200 && $this->loginInfo['dtype'] == 6) {
            $outlogModel = new OptLog();
            $daction     = "取消订单($orderNum)";
            //此时判断员工id跟主账号id情况，云票务下单操作日志行为人要记录为提交退票申请的操作人员
            if (!empty($operatorID)) {
                if (!empty($parentId)) {
                    $outlogModel->StuffOptLog($operatorID, $parentId, $daction);
                } else {
                    $outlogModel->StuffOptLog($operatorID, $operatorID, $daction);
                }
            } else {
                $outlogModel->StuffOptLog($this->loginInfo['memberID'], $this->loginInfo['sid'], $daction);
            }
        }

        return $ret;
    }

    private function _cancelOrderForApproval($orderNum, $serialNumber, $auditPass = false, $isNeedAuth = false,$mainOperator = 0, $otherParams = [])
    {
        $biz = new Modify();
        //初始化两个操作人员id跟主账号id
        $operatorID    = 0;
        $parentId      = 0;
        $cancelChannel = OrderConst::VERIFY_CANCEL;//审核取消渠道
        //之前是是否通过审核，现在是是否需要审核
        $auditPass = $auditPass ? false : true;
        //获取订单详情
        $orderOrderToolsMode = new OrderTools();
        $info                = $orderOrderToolsMode->getOrderInfo($orderNum, 'ordermode');
        //如果订票的渠道来自云票务
        if ($info['ordermode'] == 10) {
            //去订单追踪表取出提交退票申请操作人员id
            //$orderTrackMode = new OrderTrack();
            //$trackInfo      = $orderTrackMode->getTrackInfoByOrderAction($orderNum, 'oper_member', 11);

            //订单查询迁移二期
            $orderTrackQueryLib = new OrderTrackQuery();
            $trackInfo          = $orderTrackQueryLib->getTrackInfoByOrderAction([strval($orderNum)], [11]);

            $operatorID     = $trackInfo['oper_member'];
            //获取下单人员信息
            $memberMode = new Member();
            $memberInfo = $memberMode->getMemberInfo($operatorID, 'id', 'dtype');
            //如果下单人员为员工账号，则获取主账号id
            if ($memberInfo['dtype'] == 6) {
                //获取主账号id
                $parentInfo = $memberMode->getStaffBySonId($operatorID);
                $parentId   = $parentInfo['parent_id'];
                //lc修改切换
                $ret = $biz->auditPassCancel($orderNum, $isNeedAuth, $operatorID, $parentId, $cancelChannel, '',
                    $serialNumber, $auditPass,false,true, $otherParams);
                //$ret        = $biz->cancelBaseFormat($orderNum,$operatorID,$parentId,$cancelChannel,'',$serialNumber,$auditPass);
                //$ret        = $biz->cancel($orderNum, $parentId, $operatorID, $memberInfo['dtype'], $auditPass, $serialNumber);
            } else {
                $ret = $biz->auditPassCancel($orderNum, $isNeedAuth, $operatorID, $operatorID, $cancelChannel, '',
                    $serialNumber, $auditPass,false,true, $otherParams);
                //$ret        = $biz->cancelBaseFormat($orderNum,$operatorID,$operatorID,$cancelChannel,'',$serialNumber,$auditPass);
                //   $ret = $biz->cancel($orderNum, $operatorID, $operatorID, $memberInfo['dtype'], $auditPass, $serialNumber);
            }
        } else {
            $opId = $mainOperator ?: $otherParams['mainAccountId'];
            $parentId = $mainOperator ?: $otherParams['mainAccountId'];
            if(empty($opId)){
                $opId = $this->loginInfo['memberID'];
            }
            if(empty($parentId)){
                $parentId = $this->loginInfo['sid'] ;
            }
            if (MemberLoginHelper::isSubMerchantLogin()){
                $opId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $parentId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }
            $ret = $biz->auditPassCancel($orderNum, $isNeedAuth, $opId, $parentId,
                $cancelChannel, '', $serialNumber, $auditPass,false,true, $otherParams);
            //$ret        = $biz->cancelBaseFormat($orderNum,$this->loginInfo['memberID'],$this->loginInfo['sid'],$cancelChannel,'',$serialNumber,$auditPass);
            //   $ret = $biz->cancel($orderNum, $this->loginInfo['sid'], $this->loginInfo['memberID'], $this->loginInfo['dtype'], $auditPass, $serialNumber);
        }
        if ($ret['code'] == 200 && $this->loginInfo['dtype'] == 6) {
            $outlogModel = new OptLog();
            $daction     = "取消订单($orderNum)";
            //此时判断员工id跟主账号id情况，云票务下单操作日志行为人要记录为提交退票申请的操作人员
            if (!empty($operatorID)) {
                if (!empty($parentId)) {
                    $outlogModel->StuffOptLog($operatorID, $parentId, $daction);
                } else {
                    $outlogModel->StuffOptLog($operatorID, $operatorID, $daction);
                }
            } else {
                $outlogModel->StuffOptLog($this->loginInfo['memberID'], $this->loginInfo['sid'], $daction);
            }
        }

        return $ret;
    }

    private function _modifyOrder($orderNum, $targetNum, $serialNumber, $auditPass = false, $isNeedAuth = false,$mainOperator = 0, $otherParams = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if($isWhite){
            return self::_modifyOrderForApproval($orderNum, $targetNum, $serialNumber, $auditPass,$isNeedAuth, $mainOperator,$otherParams);
        }
        $biz = new Modify();
        //获取订单详情
        $orderOrderToolsMode = new OrderTools();
        $touristList         = [];
        $refundChannel       = OrderConst::VERIFY_CANCEL;

        //取审核记录表中的数据
        $trackSource = $otherParams['audit_data']['track_source'] ?? 0;
        if ($trackSource) {
            $ordermodeArr = load_config('channel_track_cancel_map', 'business');
            $refundChannel = $ordermodeArr[$trackSource] ?? $refundChannel;
        }

        $auditPass           = $auditPass ? false : true;
        $info                = $orderOrderToolsMode->getOrderInfo($orderNum, 'ordermode');
        //如果订票的渠道来自云票务
        //lc留 下面只执行取消操作
        if ($info['ordermode'] == 10 || $trackSource == 4) { //加上$trackSource，否则云票务计调下单ordermode==44走不进这里，导致最终操作人不对
            $operatorID = $otherParams['audit_data']['op_id'] ?? 0; //正常来说，云票务走到这里的时候，审核记录里一定有op_id，防止有未考虑到场景，做一次旧逻辑兼容吧
            if (!$operatorID) {
                //订单查询迁移二期
                $orderTrackQueryLib = new OrderTrackQuery();
                $trackInfo  = $orderTrackQueryLib->getTrackInfoByOrderAction([strval($orderNum)], [11]);
                $operatorID = $trackInfo['oper_member'];
            }

            //获取下单人员信息
            $memberMode = new Member();
            $memberInfo = $memberMode->getMemberInfo($operatorID, 'id', 'dtype');
            //如果下单人员为员工账号，则获取主账号id
            if ($memberInfo['dtype'] == 6) {
                //获取主账号id
                $parentInfo = $memberMode->getStaffBySonId($operatorID);
                $parentId   = $parentInfo['parent_id'];
                $ret        = $biz->refundBaseFormat($orderNum, [$orderNum => $targetNum], $operatorID, $parentId,
                    $refundChannel, $touristList, '', $serialNumber, $auditPass, $isNeedAuth,true, $otherParams);
            } else {
                $ret = $biz->refundBaseFormat($orderNum, [$orderNum => $targetNum], $operatorID, $operatorID,
                    $refundChannel, $touristList, '', $serialNumber, $auditPass, $isNeedAuth,true, $otherParams);
            }
        } else {
            $opId = $mainOperator ?: $this->loginInfo['memberID'];
            $parentId = $mainOperator ?: $this->loginInfo['sid'];
            if (MemberLoginHelper::isSubMerchantLogin()){
                $opId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $parentId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }
            $ret = $biz->refundBaseFormat($orderNum, [$orderNum => $targetNum],$opId,
                $parentId, $refundChannel, $touristList, '', $serialNumber, $auditPass, $isNeedAuth,true, $otherParams);
        }

        return $ret;
    }

    private function _modifyOrderForApproval($orderNum, $targetNum, $serialNumber, $auditPass = false, $isNeedAuth = false,$mainOperator = 0, $otherParams = [])
    {
        $biz = new Modify();
        //获取订单详情
        $orderOrderToolsMode = new OrderTools();
        $touristList         = [];
        $refundChannel       = OrderConst::VERIFY_CANCEL;
        $auditPass           = $auditPass ? false : true;
        $info                = $orderOrderToolsMode->getOrderInfo($orderNum, 'ordermode');
        //如果订票的渠道来自云票务
        //lc留 下面只执行取消操作
        if ($info['ordermode'] == 10) {
            //去订单追踪表取出提交退票申请操作人员id
            //$orderTrackMode = new OrderTrack();
            //$trackInfo      = $orderTrackMode->getTrackInfoByOrderAction($orderNum, 'oper_member', 11);

            //订单查询迁移二期
            $orderTrackQueryLib = new OrderTrackQuery();
            $trackInfo          = $orderTrackQueryLib->getTrackInfoByOrderAction([strval($orderNum)], [11]);

            $operatorID     = $trackInfo['oper_member'];
            //获取下单人员信息
            $memberMode = new Member();
            $memberInfo = $memberMode->getMemberInfo($operatorID, 'id', 'dtype');
            //如果下单人员为员工账号，则获取主账号id
            if ($memberInfo['dtype'] == 6) {
                //获取主账号id
                $parentInfo = $memberMode->getStaffBySonId($operatorID);
                $parentId   = $parentInfo['parent_id'];
                $ret        = $biz->refundBaseFormat($orderNum, [$orderNum => $targetNum], $operatorID, $parentId,
                    $refundChannel, $touristList, '', $serialNumber, $auditPass, $isNeedAuth,true, $otherParams);
            } else {
                $ret = $biz->refundBaseFormat($orderNum, [$orderNum => $targetNum], $operatorID, $operatorID,
                    $refundChannel, $touristList, '', $serialNumber, $auditPass, $isNeedAuth,true, $otherParams);
            }
        } else {
            $opId = $mainOperator ?: $otherParams['mainAccountId'];
            $parentId = $mainOperator ?: $otherParams['mainAccountId'];
            if(empty($opId)){
                $opId = $this->loginInfo['memberID'];
            }
            if(empty($parentId)){
                $parentId = $this->loginInfo['sid'];
            }
            if (MemberLoginHelper::isSubMerchantLogin()){
                $opId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
                $parentId = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            }

            $ret = $biz->refundBaseFormat($orderNum, [$orderNum => $targetNum],$opId,
                $parentId, $refundChannel, $touristList, '', $serialNumber, $auditPass, $isNeedAuth,true, $otherParams);
        }

        return $ret;
    }

    /**
     * 获取特产审核记录详情
     * <AUTHOR>
     * @date 2020-04-16
     * @param  int  $auditId  审核表的id
     *
     * @return array
     */
    public function getSpecialVerifyDetailService($auditId)
    {
        if (!$auditId) {
            return $this->returnData(203, '参数错误');
        }
        $res = $this->_getOrderCarriageMoneyInfo($auditId);
        if ($res['code'] != 200) {
            return $this->returnData($res['code'], $res['msg']);
        }
        $orderInfo = $res['data']['orderInfo'];
        $carriage  = $res['data']['carriage'];
        $resData   = [
            'carriage' => $carriage,
            'ordernum' => $orderInfo['ordernum'],
        ];

        return $this->returnData(200, '', $resData);
    }
    /**
     * 更新特产的运费
     * <AUTHOR>
     * @date 2020-04-16
     * @param  int  $auditId  审核表的id
     * @param  int  $modifyMoney  修改金额
     * @param  int  $opId  操作员
     *
     * @return array
     */
    public function updateSpecialCarriageService($auditId, $modifyMoney, $opId)
    {
        if (!$auditId || intval($modifyMoney) < 0 || !$opId) {
            return $this->returnData(203, '参数错误');
        }
        $specialRes = $this->_getOrderCarriageMoneyInfo($auditId);
        if ($specialRes['code'] != 200) {
            return $this->returnData($specialRes['code'], $specialRes['msg']);
        }
        $orderInfo = $specialRes['data']['orderInfo'];
        $carriage  = $specialRes['data']['carriage'];
        $auditInfo = $specialRes['data']['auditInfo'];
        if ($modifyMoney > $carriage) {
            return $this->returnData(203, '超过最大运费');
        }
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $ticketRes          = $commodityTicketBiz->queryTicketInfoById($orderInfo['tid'], 'id,apply_did');
        $ticketInfo         = $ticketRes['ticket'];
        if (empty($ticketInfo)) {
            return $this->returnData(203, '票属性获取失败');
        }
        if ($opId != $ticketInfo['apply_did']) {
            return $this->returnData(203, '无权操作');
        }
        //获取下这笔订单的退款流水进行比对运费金额
        $orderRefundMdl = new OrderRefund();
        $orderRefundLog = $orderRefundMdl->getRefundJournal($auditInfo['ordernum'], $auditInfo['remote_sn']);
        if (empty($orderRefundLog)) {
            return $this->returnData(203, '退款记录不存在');
        }
        $refundDetail = $orderRefundLog['details'] ? json_decode($orderRefundLog['details'], true) : [];
        if (!isset($refundDetail['carriage'])) {
            return $this->returnData(200, '无需修改');
        }
        //退款记录里面的运费
        $logCarriage = $refundDetail['carriage'];
        if ($logCarriage != $modifyMoney) {
            //这边去修改下之前审核时候的运费
            $refundDetail['carriage'] = $modifyMoney;
            $detailJson               = json_encode($refundDetail);
            $updateRes                = $orderRefundMdl->updateRefundJournalDetail($orderRefundLog['id'], $detailJson);
            if (!$updateRes) {
                return $this->returnData(204, '修改运费失败');
            }
        }
        return $this->returnData(200, '修改成功');
    }
    /**
     * 获取订单的运费金额和订单详情
     * <AUTHOR>
     * @date 2020-04-16
     * @param  int  $auditId  审核表的id
     *
     * @return array
     */
    private function _getOrderCarriageMoneyInfo($auditId)
    {
        if (!$auditId) {
            return $this->returnData(203, '参数错误');
        }
        $refundModel  = new RefundAuditModel();
        $refundRecord = $refundModel->getRefundAuditDetailById($auditId);
        if (empty($refundRecord)) {
            return $this->returnData(203, '记录不存在');
        } elseif ($refundRecord['p_type'] != 'J') {
            return $this->returnData(203, '不是特产审核订单');
        }
        $orderNum     = $refundRecord['ordernum'];
        $orderToolMdl = new OrderTools();
        $field        = 'ordernum,tnum,tid,totalmoney';
        $detailField  = 'product_ext';
        $orderInfo    = $orderToolMdl->getOrderInfo($orderNum, $field, $detailField);
        if (empty($orderInfo)) {
            return $this->returnData(203, '订单不存在');
        }
        $carriage       = 0;
        $productExtInfo = json_decode($orderInfo['product_ext'], true);
        if (!empty($productExtInfo)) {
            $carriageAllMoney    = isset($productExtInfo['carriage']) ? $productExtInfo['carriage'] : 0;
            $carriageRefundMoney = isset($productExtInfo['refundCarriage']) ? $productExtInfo['refundCarriage'] : 0;
            $carriage            = $carriageAllMoney - $carriageRefundMoney;
        }
        $res = [
            'carriage'  => $carriage,
            'orderInfo' => $orderInfo,
            'auditInfo' => $refundRecord,
        ];

        return $this->returnData(200, '', $res);
    }

    /**
     * 获取退票通知配置
     * <AUTHOR>
     * @date   2020-9-15
     * @param  int  $sid  用户sid
     *
     * @return array
     */
    public function getRefundNoticeConfig($sid)
    {
        if (!$sid) {
            return $this->returnData(203,'参数错误');
        }
        $refundModel = new RefundNotice();
        $config      = $refundModel->getNoticeConfig($sid);

        $result['wxappid']     = PFT_WECHAT_APPID;
        $result['mobile']      = $config['mobile'] ?? null;
        $result['state']       = $config['state'] ?? null;
        $result['notice_type'] = $config['notice_type'] ?? null;

        if (is_null($result['notice_type']) && is_null($result['mobile']) && is_null($result['state'])) {
            //默认开启短信通知 写入数据
            $member = new \Model\Member\Member();
            $data = $member->getMemberInfo($sid);

            $addData = [
                'fid'         => $sid,
                'mobile'      => $data['mobile'],
                'state'       => 1,
                'notice_type' => 1,
                'update_time' => time()
            ];
            $refundModel->addNoticeConfig($sid,$addData);
            $result['mobile'] = $data['mobile'];
            $result['state'] = 1;
            $result['notice_type'] = 1;
        }
        return $this->returnData(200,'成功',$result);
    }

    /**
     * 退票审核推送
     *
     * @date   2020-9-15
     * <AUTHOR>
     *
     * @param  int $sid 供应商ID
     * @param  string $noticeCentent 通知内容
     *
     * @return array
     */
    public function refundOrderNotice($sid, $noticeCentent, $ordernum = '')
    {
        if (!$noticeCentent || !$sid) {
            return ['code' => 203, 'msg' => '参数错误', 'data' => []];
        }

        $refundNoticeConfig = $this->getRefundNoticeConfig($sid);
        if ($refundNoticeConfig['code'] != 200) {
            return ['code' => 400, 'msg' => '获取退票通知配置失败', 'data' => []];
        }
        if (!$refundNoticeConfig['data']['state']) {
            return ['code' => 200, 'msg' => '未开启退票审核通知配置通知', 'data' => []];
        }
        $noticeType = $refundNoticeConfig['data']['notice_type'];
        switch ($noticeType) {
            case 0:
                $wxModel            = new WxMember();
                $cacheRedis         = Cache::getInstance('redis');
                $time               = $wxModel->wxBindListTime;//缓存时间
                $cacheKey           = sprintf($wxModel->wxBindListKey, $sid);
                $balanceWarningList = $cacheRedis->get($cacheKey);
                $balanceWarningList = json_decode($balanceWarningList, true);
                if (!$balanceWarningList) {
                    $balanceWarningList = $wxModel->getWxInfo($sid, PFT_WECHAT_APPID, 1, '', true);
                    if ($balanceWarningList) {
                        $cacheRedis->set($cacheKey, json_encode($balanceWarningList), '', $time);
                    }
                }
                if ($balanceWarningList) {
                    foreach ($balanceWarningList as $_idx => $item) {
                        if ($item['notice_info']) {
                            $noticeInfo = isset($item['notice_info']) ? json_decode($item['notice_info'], true) : null;
                            if (!empty($noticeInfo['reimburse'])) {
                                //微信通知处理
                                $data       = array(
                                    'first'    => array('value' => '收到退票申请，请您及时登录平台处理审核', 'color' => '#ff3300'),
                                    'keyword1' => array('value' => date('Y-m-d H:i:s'), 'color' => '#ff9900'),
                                    'keyword2' => array('value' => $noticeCentent, 'color' => '#ff9900'),
                                    'remark'   => array('value' => '', 'color' => '#0000ff'),
                                );
                                $paramsJson = json_encode($noticeCentent, JSON_UNESCAPED_UNICODE);
                                if (!$item['fromusername']) {
                                    pft_log('queue/refundOrderNotice/error',
                                        "发送退票通知失败,openid为空。data:" . json_encode($data, JSON_UNESCAPED_UNICODE),
                                        "month");
                                    continue;
                                }
                                pft_log('queue/refundOrderNotice/success',
                                    'fromusername:' . $item['fromusername'] . ';params=' . $paramsJson, "month");

                                $job_id = Queue::push('notify', 'WxNotify_Job',
                                    array(
                                        'data'   => $data,
                                        'openid' => $item['fromusername'],
                                        'tplid'  => 'WARNING',
                                        'url'    => "",
                                        'color'  => '#FF0000',
                                    )
                                );
                                pft_log('wx/refundOrderNotice/ok', 'jobId:' . $job_id, "month");
                            }
                        }
                    }
                }
                break;
            case 1:
                //短信通知处理
                if (empty($refundNoticeConfig['data']['mobile'])) {
                    $logParam = [
                        'key'  => '发送退票通知失败,手机号为空',
                        'data' => json_encode($refundNoticeConfig, JSON_UNESCAPED_UNICODE),
                    ];
                    pft_log('queue/refundOrderNotice/error', json_encode($logParam, JSON_UNESCAPED_UNICODE),
                        "month");
                    continue;
                }

                $params = [
                    'mobile' => $refundNoticeConfig['data']['mobile'],
                    'action' => 'customMsg',
                    'params' => [$sid, '票付通', $noticeCentent, $refundNoticeConfig['data']['mobile'],'',$ordernum,true,'发送退票通知'],
                ];
                pft_log('queue/refundOrderNotice/success',
                    'mobile:' . $refundNoticeConfig['data']['mobile'] . ';params=' . json_encode($params,
                        JSON_UNESCAPED_UNICODE), "month");

                $paramsJson = json_encode($params, JSON_UNESCAPED_UNICODE);
                $job_id     = Queue::push('notify', 'SmsNotify_Job', $params);
                pft_log('queue/refundOrderNotice/success', 'jobId:' . $job_id . ';params=' . $paramsJson, "month");

                break;
            default:
                return ['code' => 204, 'msg' => '参数错误', 'data' => []];
        }

        return ['code' => 200, 'msg' => '成功', 'data' => []];
    }

    /**
     * 返回是否允许撤改
     * author queyourong
     * date 2022/7/26
     *
     * @param $ordernum string 订单号
     * @param $orderStatus int 订单状态
     * @param $tnum int 剩余有效票数
     * @param $applyDid int 供应商id
     * @return bool
     */
    public function isCanRevoke($ordernum, $orderStatus, $tnum, $applyDid)
    {
        //允许撤销场景: 1=已使用, 5=撤改, 7=部分使用, 2=已过期(部分使用过期)
        if(!in_array($orderStatus, [1, 2, 5, 7])){
            return false;
        }
        if($tnum <= 0) {
            return false;
        }

        //$allowMoreTimeRevoke = load_config('more_time_revoke');
//        $orderTerminalModel = new \Model\Order\OrderTerminal();
//        //查询出通过的撤改记录
//        $revokeInfo = $orderTerminalModel->GetTerminalChange($ordernum, [1]);
//        if($revokeInfo) {
//            //已有撤改记录
//            return false;
//        }

        return true;
    }

    /**
     * 暂时在这边通知下游
     * @param $orderNum
     * @param $refundRecord
     *
     * @return mixed
     */
    private function _notifyDownSystem($orderNum, $refundRecord,$operatorID)
    {
        $orderData = self::getRejectRefundAuditOrderData($orderNum,$refundRecord);
        //订单退票审核通知（推送kafka）
        $dataKafka = [
            'orderNum' => $orderNum,
            'status'   => 0,
            'data'     => [
                'type'   => 2,
                'params' => [
                    'pftSerialNumber'     => $refundRecord['system_sn'] ?? '',//平台退款流水号
                    'reqSerialNumber'     => $refundRecord['remote_sn'] ?? '',//请求退款流水号
                    'orderStatus'         => $orderData['status'],//退票前订单的状态
                    'cancelNum'           => $orderData['cancelNum'],//退票数量
                    'leftNum'             => $orderData['leftNum'],//剩余的可用票数
                    'validNum'            => $orderData['validNum'],//剩余的有效票数
                    'validMoney'          => 0,//剩余的有效金额
                    'trackId'             => 0,//退票追踪ID
                    'opId'                => $operatorID,//退票操作人
                    'refundIdx'           => [],//已取消的订单序号
                    'idCardCodeParams'    => [],//实名制退票参数
                    'refundAmount'        => 0,//退票金额
                    'refundFee'           => 0,//退票手续费
                    'cancelChannel'       => OrderConst::VERIFY_CANCEL,//取消渠道
                    'isCancelNoticeThird' => true,//是否需要取消了通知三方系统
                    'isRollBack'          => false,//是否是回滚订单
                ],
            ],
        ];
        pft_log('debug/refundKafka', json_encode($dataKafka));
        KafkaProducer::push('platform', 'refund_examine_topic', $dataKafka, strval($orderNum));
    }

    private function getRejectRefundAuditOrderData($orderNum,$refundRecord){
        $refundModel = self::getRefundAdultModel();
        $orderInfo  = $refundModel->getOrderInfoForAudit($orderNum);
        $haveAuditNum = 0;
        $auditData    = $refundModel->getAllAuditTargetTnum($orderNum, [0]);
        foreach ($auditData as $key => $value) {
            if ($value['id'] != $refundRecord['id'] && isset($refundRecord['id'])) {
                $haveAuditNum += $value['modify_tnum'];
            }
        }
        $orderModel = new OrderTools();
        $verifyNum = 0 ;
        //允许撤销场景: 1=已使用, 5=撤改, 7=部分使用, 2=已过期(部分使用过期)
        if ($orderInfo['status'] == 7 || $orderInfo['status'] == 2) {
            $verifyNum = $orderModel->getVerifiedNum($orderNum);
            if ($verifyNum) {
                $tNumCanBeModified = $orderInfo['tnum'] - $verifyNum;
            } else {
                $tNumCanBeModified = $orderInfo['tnum'];
            }
        } else {
            $tNumCanBeModified = $orderInfo['tnum'];
        }
        $remainTicketNum = $tNumCanBeModified - $haveAuditNum;

        //'变更类型:0=撤改,1=撤销,2=修改,3=取消,4=改签
        if($refundRecord['stype'] < self::MODIFY_CODE){
            $validNum   = $remainTicketNum;
        }
        else{
            $validNum = $verifyNum > 0 ? $orderInfo['tnum'] - $verifyNum : $remainTicketNum;
        }
        return ['status'=>$orderInfo['status'],'cancelNum' =>$refundRecord['modify_tnum'], 'leftNum'=>$remainTicketNum,'validNum'=>$validNum];
    }

    private function refundAuditLock($orderNum,$refundRecordId){
        $lockObj  = \Library\Lock\LockFactory::factory('redis');
        $lockKey = "lock:cancel_audit:{$orderNum}:{$refundRecordId}";
        $lockObj->acquire($lockKey, 10, 10);
    }

    private function releaseRefundAuditLock($orderNum,$refundRecordId){
        $lockObj  = \Library\Lock\LockFactory::factory('redis');
        $lockKey ="lock:cancel_audit:{$orderNum}:{$refundRecordId}";
        $lockObj->release($lockKey);
    }

    /**
     * 获取审核列表数据
     * @param $memberID
     * @param null $landTitle
     * @param null $noticeType
     * @param null $timeType
     * @param null $beginTime
     * @param null $endTime
     * @param null $auditStatus
     * @param null $orderNum
     * @param int $page
     * @param int $limit
     * @param false $auditType
     * @param int $landId
     * @param int $tid
     * @param int $subSid
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     * @return array|false|mixed|string|string[]|null
     */
    public function getCompactAuditList($operatorID, $landTitle = null, $noticeType = null, $timeType = null,
        $beginTime = null, $endTime = null, $auditStatus = null, $orderNum = null, $page = 1, $limit = 20,
        $auditType = false, $landId = 0, $tid = 0, $subSid = 0, array $extCondition = [], $memberId = 0)
    {
        //切换新接口
        $approvalSearchParam = [
            'functionTypes' => [6,7],
            'memberId' => $memberId,
        ];
        if ($operatorID != 1) {
            $approvalSearchParam['extraSearchMap']['aids'] = [
                'value' => [$operatorID],
                'searchType' => 'in'
            ];
            $approvalSearchParam['mainSid'] = $operatorID;
            if (isset($extCondition['lidList'])) {
                $approvalSearchParam['extraSearchMap']['lid'] = [
                    'value' => $extCondition['lidList'],
                    'searchType' => 'in'
                ];
            }
            if (isset($extCondition['notLidList'])) {
                $approvalSearchParam['extraSearchMap']['lid'] = [
                    'value' => $extCondition['notLidList'],
                    'searchType' => 'not_in'
                ];
            }
        }
        $approvalSearchParam['extraSearchMap']['businessStatus'] = [
            'value' => OrderTerminal::APPROVAL_STATUS_MAP[$auditStatus],
            'searchType' => 'equal'
        ];
        if ($auditType == 1){
            $approvalSearchParam['extraSearchMap']['pType'] = [
                'value' => 'J',
                'searchType' => 'equal'
            ];
        }elseif ($auditType == 0){
            $approvalSearchParam['extraSearchMap']['pType'] = [
                'value' => 'J',
                'searchType' => 'not_equal'
            ];
        }
        if($landId){
            $approvalSearchParam['extraSearchMap']['lid'] = [
                'value' => $landId,
                'searchType' => 'equal'
            ];
        }
        if($tid){
            $approvalSearchParam['extraSearchMap']['tid'] = [
                'value' => $tid,
                'searchType' => 'equal'
            ];
        }

        if ($orderNum) {
            $approvalSearchParam['extraSearchMap']['orderNum'] = [
                'value' => $orderNum,
                'searchType' => 'equal'
            ];
        } else {
            if($subSid){
                $approvalSearchParam['extraSearchMap']['subMerchantId'] = [
                    'value' => $subSid,
                    'searchType' => 'equal'
                ];
            }
            if($noticeType !== null){
                $approvalSearchParam['extraSearchMap']['sType'] = [
                    'value' => $noticeType,
                    'searchType' => 'equal'
                ];
            }
            if($timeType>0){
                $beginTodayDefault = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
                $endTodayDefault = mktime(0, 0, 0, date('m'), date('d')+1, date('Y'))-1;
                $bTime = date('Y-m-d H:i:s', (empty($beginTime) ? $beginTodayDefault :$beginTime));
                $eTime = date('Y-m-d H:i:s',  (empty($endTime) ? $endTodayDefault :$endTime));
                if($timeType == 2){
                    $approvalSearchParam['beginFinishedDate'] = $bTime;
                    $approvalSearchParam['endFinishedDate'] = $eTime;
                }
                else{
                    $approvalSearchParam['beginDate'] = $bTime;
                    $approvalSearchParam['endDate'] = $eTime;
                }
            }
        }
        $refundApprovalService = new RefundApprovalService();
        list($res,$msg,$data) = $refundApprovalService->queryApprovalFormList($approvalSearchParam,$page,$limit);
        if (!$res) {
            $data = ['total' => 0, 'list' => []];
        }
        $businessStatusMap = array_flip(OrderTerminal::APPROVAL_STATUS_MAP);
        $data['list'] = array_map(function($value) use ($businessStatusMap) {
            return [
                'id'=>$value['extraContent']['id'],
                'ordernum'=>$value['extraContent']['orderNum'],
                'lid'=>$value['extraContent']['lid'],
                'tid'=>$value['extraContent']['tid'],
                'stype'=>$value['extraContent']['sType'],
                'tnum'=>$value['extraContent']['tNum'],
                'dstatus'=>$businessStatusMap[$value['extraContent']['businessStatus']],
                'reason'=>$value['extraContent']['reason'],
                'dadmin'=>$value['extraContent']['tenantId'],
                'stime' =>$value['createTime'],
                'dtime' =>$value['extraContent']['dtime'],
                'fxid' => $value['extraContent']['startUserId'],
                'system_sn' => $value['extraContent']['systemSn'],
                'remote_sn' => $value['extraContent']['remoteSn'],
                'modify_tnum' => $value['extraContent']['modifyTnum'],
                'aids' => $value['extraContent']['aids'],
                'ltitle' => $value['extraContent']['lTitle'] ?? '',
                'ticketTitle' => $value['extraContent']['tTitle'] ?? '',
                'concat_id' => $value['extraContent']['concatId'],
                'ifpack' => $value['extraContent']['ifPack'],
                'apply_did' => $value['extraContent']['applyDid'],
                'sourceT' => $value['extraContent']['sourceT'] ?? 0,
                'dcodeURL' => $value['extraContent']['dCodeURL'] ?? '',
                'pack_order' => $value['extraContent']['packOrder'] ,
                'system_auto_check' => $value['extraContent']['systemAutoCheck'] ?? 0,
                'p_type' => $value['extraContent']['pType'],
                'audit_data' => $value['extraContent']['auditData'] ?? '',
                'sub_merchant_id' => $value['extraContent']['subMerchantId'] ?? '',
                'can_audit' => $value['canAudit'],
                'task_id' => $value['taskId'] ?? "",
                'retry_node_id' => $value['retryNodeId'],
                'approval_code' => $value['procInsId'],
                'process_status' => $value['processStatus'],
                'comment_list' => $value['commentList'] ?? [],
            ];
        }, $data['list'] ?? []);

        $returnRows = count($data['list']);
        $pages = ceil($data['total'] / $limit) ?: 1;
        $overflowRows = $data['total'] % $limit;

        $extCondition['approval_code'] = 0;
        $refundModel = new RefundAuditModel('slave');
        $auditListTotal = $refundModel->getAuditListNew($operatorID, $landTitle, $noticeType, $timeType, $beginTime, $endTime, $auditStatus,
            $orderNum, true, $page, $limit, $auditType, $landId,  $tid, $subSid, $extCondition);
        $auditListTotal = $auditListTotal ? array_values($auditListTotal)[0] : 0;
        $data['total'] += $auditListTotal;
        //返回条数不足期望的分页数，则需要从历史数据中获取（排除接口问题导致的数据量不足）
        if ($auditListTotal && $returnRows < $limit && $page >= $pages) {
            $concatLimit = $limit - $returnRows;
            $extCondition['use_offset'] = max(($page - $pages) * $limit - $overflowRows, 0);
            $oldRefundRecords = $refundModel->getAuditListNew($operatorID, $landTitle, $noticeType, $timeType, $beginTime, $endTime, $auditStatus,
                $orderNum, false, $page, $concatLimit, $auditType, $landId,  $tid, $subSid, $extCondition);
            $oldRefundRecords = $oldRefundRecords ?: [];
            //当页数=1时返回一维数组，否则二位数组
            $oldRefundRecords = ($concatLimit == 1 && $oldRefundRecords) ? [$oldRefundRecords] : $oldRefundRecords;
            $data['list'] = array_merge($data['list'], $oldRefundRecords);
        }
        return $data;
    }

    /**
     * 获取撤改审核列表数据
     *
     * @param null $operatorID
     * @param null $landTitle
     * @param null $noticeType
     * @param null $applyTime
     * @param null $auditStatus
     * @param null $auditTime
     * @param null $orderNum
     * @param int  $page
     * @param int  $limit
     * @param int  $auditType  审核类型 1-特产
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     */
    public function getAuditList($operatorID = null, $landTitle = null, $noticeType = null, $timeType = null, $beginTime = null, $endTime = null,
                                 $auditStatus = null, $orderNum = null, $page = 1, $limit = 20, $auditType = 0, $landId = 0, $tid = 0,$subSid = 0,
                                 array $extCondition = [],$memberId='')
    {
        //参数初始化
        $limit       = intval($limit);
        $page        = intval($page);

        $limit < 1 && $limit = 20;
        $page < 1 && $page = 1;

        //获取记录详情
        $result = $this->getCompactAuditList($operatorID, $landTitle, $noticeType, $timeType, $beginTime, $endTime, $auditStatus,
            $orderNum, $page, $limit, $auditType, $landId,  $tid, $subSid, $extCondition, $memberId);

        $data = array(
            'page'       => $page,
            'limit'      => $limit,
            'total'      => $result['total'],
            'audit_list' => [],
        );
        $refundRecords = $result['list'];

        if (!$data['total'] || !$refundRecords) {
            return $this->returnData(200,'成功',$data);
        }

        $orderNumSubSidMap  = [];
        $orderNumArr = array_column($refundRecords, 'ordernum');
        $orderNumArr = array_unique($orderNumArr);

        // 根据审核结果类型去 order_track 去相关的操作-- 理论同意,拒绝只有一次
        if (in_array($auditStatus, [1, 2]) && $orderNumArr) {
            if ($auditStatus == 1) {
                //变更状态:1=0=下单,1=修改,2=取消,3=出票,4=支付,5=验证,6=撤销,7=撤改,8=重打印,9=离线订单下载,10=处理退票申请,
                //11=提交退票申请,12=过期,13=同意退票申请,14=拒绝退票申请,15=核销,16=订单加票,17=完结
                $action = [13,6,7];
            } else {
                $action = [14];
            }

            //订单查询迁移二期
            $orderTrackQueryLib = new OrderTrackQuery();
            $orderTrackArr      = $orderTrackQueryLib->getOrderSourceByOrderId($orderNumArr, $action);
            foreach ($orderTrackArr as $orderInfo){
                $extContent = json_decode($orderInfo['ext_content'], true);
                if (isset($extContent['subSid'])){
                    $orderNumSubSidMap[$orderInfo['ordernum']] = $extContent['subSid'];
                }
            }
            $opMemArr = array_column($orderTrackArr, 'oper_member');
            $opMemArr = array_unique($opMemArr);
            $subSidArr = array_unique(array_values($orderNumSubSidMap));
            $opMemArr = array_merge($opMemArr, $subSidArr);
            $memberModel  = new Member();
            $opMemInfoArr = $memberModel->getMemberInfoByMulti($opMemArr, 'id', 'dname,id', true);
        }
        $subOrderModel   = new SubOrderQuery();
        $orderArray      = $subOrderModel->getDetailByOrderJoin($orderNumArr, 'ordernum,tid,tnum,totalmoney', false,
            false);
        $ticketIds       = array_column($orderArray, 'tid');
        $ticketIds       = array_unique($ticketIds);
        $orderHandleData = array_column($orderArray, null, 'ordernum');
        $tidStr          = implode(',', $ticketIds);
        $thirdAttrApiBiz = new \Business\JavaApi\Ticket\ThirdAttr();
        $uuidListArr     = $thirdAttrApiBiz->thirdBindByTickets($tidStr);
        $uuidList        = $uuidListArr['data'];
        foreach ($uuidList as $item) {
            $tmpTicketList[$item['ticketId']] = $item['uuid'];
        }

        //旅游券操作人名称
        $dadminInfoArrMap = [];
        if (!empty($refundRecords)) {
            $dadminId = array_column($refundRecords, 'dadmin');
            $dadminIdNew = [];
            foreach ($dadminId as $dadminValue) {
                if (!empty($dadminValue)) {
                    $dadminIdNew[] = $dadminValue;
                }
                $dadminIdNew = array_unique($dadminIdNew);
            }
            $memberModel  = new Member();
            $dadminInfoArrMap = $memberModel->getMemberInfoByMulti($dadminIdNew, 'id', 'dname,id', true);
        }

        $orderRefundMdl = new OrderRefund();
        while ($refundRecords) {
            $row = array_shift($refundRecords);
            foreach ($orderArray as $value) {
                if ($row['ordernum'] == $value['ordernum']) {
                    $row['uuid'] = $tmpTicketList[$value['tid']];
                    break;
                }
            }
            if($row['apply_did'] != $operatorID){
                $row['is_self_supply'] = false;
            }
            else{
                $row['is_self_supply'] = true;
            }
            $auditData = json_decode($row['audit_data'], true);
            unset($row['audit_data']);
            $row['action'] = 0;
            $row['repush'] = false;
            if ($row['approval_code']) {
                $row['is_approval'] = true;
                if (isset($row['can_audit']) && $row['can_audit']) {
                    $row['action'] = $row['dstatus'] == 0 ? 1 : 2;
                    unset($row['can_audit']);
                } else {
                    $row['action'] = $row['dstatus'] == 0 ? 0 : 2;
                }
                $row['process_status'] = $row['process_status'] ?? 0;
                $row['task_id'] = $row['task_id'] ?? 0;
                $row['retry_node_id'] = $row['retry_node_id'] ?? 0;
                if (($row['apply_did'] == $operatorID || $operatorID == 1) && $row['ifpack'] != 1 && !empty($row['uuid'])) {
                    $row['repush'] = $row['dstatus'] != 0 && $row['dcodeURL'];
                }
                // 子商户需要额外处理，流程编码不可跳转、不展示操作按钮
                if (!empty($extCondition['is_sub_merchant'])) {
                    $row['action'] = $row['dstatus'] == 0 ? 0 : 2;
                    $row['repush'] = false;
                    $row['is_self_supply'] = false;
                }
                $lastComment = array_pop($row['comment_list']);
                $lastCommentText = '';
                foreach ($row['comment_list'] as $commentValue){
                    if(isset($commentValue['comment'])&& !empty($commentValue['comment'])){
                        $lastCommentText = $commentValue['comment'];
                    }
                }
                $row['opMem'] = $lastComment['assignee'] ?? '';
                $row['dtime'] = $lastComment['commentTime'] ?? '';
                $row['reason'] = $lastCommentText;
            } else {
                $row['approval_code'] = '';
                // action -0 等待处理 -1 同意|拒绝 -2 已处理
                if (($row['apply_did'] == $operatorID || $operatorID == 1) && $row['ifpack'] != 1) {
                    if (empty($row['uuid'])) {
                        $row['action'] = $row['dstatus'] == 0 ? 1 : 2;
                    } else {
                        $row['repush'] = $row['dstatus'] != 0 && $row['dcodeURL'];
                    }
                    if (!empty($row['uuid']) && in_array($row['stype'], [0, 1])) {
                        $row['action'] = $row['dstatus'] == 0 ? 1 : 2;
                    }
                    // 预售券权益单待审核时，预售券订单不可操作
                    if (isset($auditData['exchange_base_ticket_equity_order_audit_status']) && $row['action'] == 1) {
                        $row['action'] = 0;
                    }
                } else {
                    $row['action'] = $row['dstatus'] == 0 ? 0 : 2;
                    if($row['ifpack'] == 1 && $row['dstatus']==0){
                        $orderQuery = new Query();
                        $isBindTicket = $orderQuery->isBindTicketForMainOrderNum($row['ordernum']);
                        if($isBindTicket){
                            $row['action'] = 1;
                        }
                    }
                }
                $orderTrackArr = $orderTrackArr ?? [];
                $opMemId = 0;
                $opMem   = '';
                foreach ($orderTrackArr as $orderKey => $orderVal) {
                    if (($row['ordernum'] == $orderVal['ordernum']) && ($row['dtime'] == $orderVal['insertTime'])) {
                        $opMemId = $orderNumSubSidMap[$orderVal['ordernum']] ?: $orderVal['oper_member'];
                    }
                }
                if ($opMemId !== 0) {
                    if ($opMemId == 1) {
                        $opMem = '系统';
                    } else {
                        $opMem = $opMemInfoArr[$opMemId]['dname'] ?? '';
                    }
                }
                $row['opMem'] = $opMem ?: '';
            }
            $row['cancel_audit_remark'] = $auditData['cancel_audit_remark'] ?? '';

            //处理下特产需要的值
            if ($row['p_type'] == 'J') {
                $refundInfo = $orderRefundMdl->getRefundJournal($row['ordernum'], $row['remote_sn']);

                $row['carriage'] = 0;
                if ($refundInfo) {
                    $refundDetails        = $refundInfo['details'] ? json_decode($refundInfo['details'], true) : [];
                    $carriage             = $refundDetails['carriage'] ?? 0;
                    $row['carriage']      = $carriage;
                    $row['special_money'] = 0;
                    if ($refundInfo['origin_num']) {  //假设数据库存了0就报错了，这边先这样处理吧
                        $row['special_money'] = $refundInfo['origin_money'] / $refundInfo['origin_num'] * $row['modify_tnum'];
                    }
                } else {
                    $row['special_money'] = 0;
                }
            }

            if ($row['p_type'] == 'Q') {
                $opMemLv = '';
                if ($row['dadmin'] !== 0) {
                    if ($row['dadmin'] == 1) {
                        $opMemLv = '系统';
                    } else {
                        $opMemLv = $dadminInfoArrMap[$row['dadmin']]['dname'] ?: '';
                    }
                }
                $row['opMem'] = $opMemLv;
            }
            $row['apply_member_id'] = $row['fxid'];
            if(!empty($auditData['after_sale_num']) && !empty($auditData['batch_refund_more_idx']) ){
                $originalModifyTNum = $row['modify_tnum'];
                $row['modify_tnum'] = count($auditData['batch_refund_more_idx']);
                $row['tnum'] = $row['tnum'] - ($row['modify_tnum']-$originalModifyTNum);
            }

            unset($row['fxid']);
            unset($row['dcodeURL']);
            unset($row['sourceT']);
            unset($row['uuid']);
            $data['audit_list'][] = $row;
        }
        if($data['audit_list']){
            $memberIds = array_column($data['audit_list'],'apply_member_id');
            if(!empty($memberIds)){
                $memberBiz   = new \Business\Member\Member();
                $memberRes = $memberBiz->getMemberInfoByMulti($memberIds, 'id');
                $memberRes = array_column($memberRes,null,'id');
                $data['audit_list'] = array_map(function ($auditInfo) use ($memberRes) {
                    if(array_key_exists($auditInfo['apply_member_id'], $memberRes)){
                        $auditInfo['apply_member_name'] = $memberRes[$auditInfo['apply_member_id']]['dname'];
                    }
                    return $auditInfo;
                }, $data['audit_list']);
            }
        }
        return $this->returnData(200,'成功',$data);
    }

    /**
     * 通用下游改签审核结果通知
     * @param $orderInfo array 订单信息
     * @param $tChangingRecord array 审核记录
     * @param $auditResult int 审核结果1=同意，2=拒绝
     * @param $auditNote string 审核说明
     * @param $newTime string 改签后日期
     * @param $shareTimeStr string 改签后分时时段
     *
     * @return mixed
     */
    public function _ticketChangeNotifyDownstream($orderInfo, $tChangingRecord, $auditResult, $auditNote, $newTime, $shareTimeStr)
    {
        //订单改签审核通知（推送kafka）
        $orderNum = $orderInfo['ordernum'];
        $auditData = json_decode($tChangingRecord['audit_data'], true);
        $remoteSn  = $auditData['downstreamSn'] ?? '';

        $dataKafka = [
            'orderNum' => $orderNum,
            'status'    => $orderInfo['status'], //订单状态
            'data'      => [
                'type'   => 4, //1退单 2核销 3出票 4改签
                'params' => [
                    'order_num'      => $orderNum,//平台订单号
                    'remote_num'     => $orderInfo['remotenum'],//远端订单号
                    'pft_serial_num' => $tChangingRecord['system_sn'] ?? '',//平台改签流水号
                    'req_serial_num' => $remoteSn,//请求改签流水号
                    'audit_result'   => (int)$auditResult,//审核结果1=同意，2=拒绝
                    'audit_time'     => date('Y-m-d H:i:s'),//审核时间，2025-04-22 18:00:00
                    'audit_note'     => $auditNote,//审核说明
                    'new_time'       => $newTime,//改签后日期，2025-05-01
                    'time_str'       => $shareTimeStr,//改签后分时时段，12:01-18:00
                    'tnum'           => $orderInfo['tnum'],//改签后剩余可使用数量
                ],
            ],
        ];
        pft_log('debug/refundKafka', json_encode($dataKafka));
        KafkaProducer::push('platform', 'changed_examine_topic', $dataKafka, strval($orderNum));
    }

}
