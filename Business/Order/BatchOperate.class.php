<?php
/**
 * 订单批量操作
 * <AUTHOR>
 * @date   2022/5/7
 */

namespace Business\Order;

use Business\Authority\DataAuthLimit;
use Business\Authority\DataAuthLogic;
use Business\Base;
use Library\Tools\Helpers;
use Model\Order\OrderTools;
use Library\Constants\OrderConst;
use Model\Order\BatchOperate as BatchOperateModel;
use Business\JavaApi\Member\MemberQuery;
use Business\Authority\AuthLogic as AuthLogicBiz;

class BatchOperate extends Base
{

    const ACTION_TYPE           = [1, 2, 3, 4]; //操作类型 1.变更有效期 2.取消 3.验证 4.完结
    const ACTION_TYPE_VAIL_DATE = 1; //变更有效期
    const ACTION_TYPE_CANCEL    = 2; //取消
    const ACTION_TYPE_CHECKED   = 3; //验证
    const ACTION_TYPE_FINISH    = 4; //完结
    const ACTION_TYPE_DISTRIBUTOR_RENEW     = 5; //分销商续费

    public function __construct()
    {

    }

    public function getServerInside()
    {
        return Helpers::GetSoapInside();
    }

    /**
     * 批量有效期变更
     * <AUTHOR>
     * @date   2022/5/11
     *
     * @param  array  $params    参数集
     *
     * @return array
     */
    public function batchValidDate(array $params)
    {
        if (!isset($params['serial_code']) || !isset($params['member_id'])) {
            return $this->returnData(203, '参数错误');
        }
        $serialCode    = $params['serial_code'];
        $mainParams    = json_decode($params['params'], true);
        $isOrderSearch = $mainParams['is_order_search'] ?? false;
        $isShowOrderSearch = $mainParams['is_show_order_search'] ?? false;
        $handleType    = $mainParams['handle_type'] ?? 0;
        $operator      = $params['operator'] ?? 0;
        $memberId      = $params['member_id'];
        $isChange      = $mainParams['is_change'] ?? 0;

        //订单查询 触发取消
        if ($isOrderSearch) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($operator);
            return $this->orderSearchOperate($mainParams, $handleType, $serialCode, $dataAuthLimit);
        }
        //演出订单查询
        if ($isShowOrderSearch) {
            return $this->handleBatchOperateRecord($mainParams, $handleType, $serialCode);
        }

        $model      = new BatchOperateModel();
        $recordList = $model->getRecordList(0, $serialCode);
        $orderIds   = array_column($recordList, 'ordernum');
        if (empty($orderIds)) {
            return $this->returnData(400, '没有可处理的订单号');
        }

        //重新组合数据
        $fileInfo     = [];
        $orderError   = [];
        $orderSuccess = [];
        foreach ($recordList as $item) {
            $params   = json_decode($item['params'] ?? '', true);
            $type     = $params['type'] ?? '';
            $ordernum = $item['ordernum'] ?? '';

            if (empty($ordernum)) {
                continue;
            }

            if (empty($type)) {
                $orderError[$ordernum] = '订单：' . $ordernum . '延期失败，类型错误';
                continue;
            }
            if (in_array($type, ['all', 'only_start']) && !isset($params['start_time'])) {
                $orderError[$ordernum] = '订单：' . $ordernum . '延期失败，开始时间缺失';
                continue;
            }
            if (in_array($type, ['all', 'only_end']) && !isset($params['end_time'])) {
                $orderError[$ordernum] = '订单：' . $ordernum . '延期失败，结束时间缺失';
                continue;
            }

            switch ($type) {
                case 'only_start':
                    if (!isset($fileInfo[$type][$params['start_time']])) {
                        $fileInfo[$type][$params['start_time']] = [];
                    }
                    $fileInfo[$type][$params['start_time']][] = $ordernum;
                    break;
                case 'only_end':
                    if (!isset($fileInfo[$type][$params['end_time']])) {
                        $fileInfo[$type][$params['end_time']] = [];
                    }
                    $fileInfo[$type][$params['end_time']][] = $ordernum;
                    break;
                default:
                    if (!isset($fileInfo[$type][$params['start_time']][$params['end_time']])) {
                        $fileInfo[$type][$params['start_time']][$params['end_time']] = [];
                    }
                    $fileInfo[$type][$params['start_time']][$params['end_time']][] = $ordernum;
            }
        }

        //没有需要延期，直接错误
        if (!empty($fileInfo)) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($operator);
            $orderDelayBiz = new \Business\Order\OrderDelay();
            $res           = $orderDelayBiz->batchOrOneOrderDelay($memberId, $operator, $orderIds, $fileInfo, $dataAuthLimit, $isChange);
            if ($res['code'] != 200) {
                //全部订单处理成失败
                foreach ($orderIds as $orderId) {
                    $orderError[$orderId] = $res['msg'] ?? '处理失败';
                }
            }
            $orderSuccess = $orderSuccess + ($res['data']['order_success'] ?? []);
            $orderError   = $orderError + ($res['data']['order_error'] ?? []);
        }

        //队列更新
        $this->_handleUpdateRecord($orderSuccess, $orderError, $serialCode);

        return $this->returnData(200, '处理成功');
    }

    /**
     * 批量取消
     * <AUTHOR>
     * @date   2022/5/11
     *
     * @param  array  $params    参数集
     *
     * @return array
     */
    public function batchCancel(array $params)
    {
        if (!isset($params['serial_code']) || !isset($params['member_id'])) {
            return $this->returnData(203, '参数错误');
        }
        $serialCode    = $params['serial_code'];
        $memberId      = $params['member_id'];
        $mainParams    = json_decode($params['params'], true);
        $isOrderSearch = $mainParams['is_order_search'] ?? false;
        $handleType    = $mainParams['handle_type'] ?? 0;
        $operator      = $params['operator'] ?? 0;
        $isShowOrderSearch = $mainParams['is_show_order_search'] ?? false;

        //订单查询 触发取消
        if ($isOrderSearch) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($operator);
            return $this->orderSearchOperate($mainParams, $handleType, $serialCode, $dataAuthLimit);
        }
        if ($isShowOrderSearch) {
            return $this->handleBatchOperateRecord($mainParams, $handleType, $serialCode);
        }

        $model      = new BatchOperateModel();
        $recordList = $model->getRecordList(0, $serialCode);
        $orders     = array_column($recordList, 'ordernum');
        if (empty($orders)) {
            return $this->returnData(400, '没有可处理的订单号');
        }

        define('IN_PFT', true);
        //获取内部接口类
        $serverInside = $this->getServerInside();
        //获取订单模型接口
        $orderModel   = new OrderTools();
        $modifyBiz    = new Modify();
        $orderSuccess = [];
        $orderError   = [];

        foreach ($orders as $key => $value) {
            //获取订单信息
            $orderInfo = $orderModel->getOrderInfo($value, 'member,status');
            if (!empty($orderInfo) && $orderInfo['member'] == $memberId) {
                if ($orderInfo['status'] == 3) {
                    $orderError[$value] = '订单：' . $value . '订单已是取消的状态，请勿重复操作';
                } else {
                    $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($value);
                    //取消订单动作
                    $res = Helpers::platformRefund($value, -1, $memberId, OrderConst::PURCHASE_CANCEL, 'common',
                        $reqSerialNumber);

                    if ($res['code'] == 200) {
                        $orderSuccess[] = $value;//取消成功
                    } else {
                        $errorMsg           = isset($res['err_msg']) ? $res['err_msg'] : $res['msg'];
                        $orderError[$value] = '订单：' . $value . '取消失败!' . "\n" . '错误信息：' . $errorMsg; //取消失败
                    }
                }

            } else {
                $orderError[$value] = '订单：' . $value . '不属于购买者的导码订单，无法执行取消操作';
            }
        }

        //队列更新
        $this->_handleUpdateRecord($orderSuccess, $orderError, $serialCode);

        return $this->returnData(200, '处理成功');
    }

    /**
     * 批量验证
     * <AUTHOR>
     * @date   2022/5/11
     *
     * @param  array  $params    参数集
     *
     * @return array
     */
    public function batchCheck(array $params)
    {
        if (!isset($params['serial_code']) || !isset($params['member_id'])) {
            return $this->returnData(203, '参数错误');
        }
        $serialCode    = $params['serial_code'];
        $mainParams    = json_decode($params['params'], true);
        $isOrderSearch = $mainParams['is_order_search'] ?? false;
        $handleType    = $mainParams['handle_type'] ?? 0;
        $operator      = $params['operator'] ?? 0;
        $isShowOrderSearch = $mainParams['is_show_order_search'] ?? false;

        //订单查询 触发取消
        if ($isOrderSearch) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($operator);
            return $this->orderSearchOperate($mainParams, $handleType, $serialCode, $dataAuthLimit);
        }
        if ($isShowOrderSearch) {
            return $this->handleBatchOperateRecord($mainParams, $handleType, $serialCode);
        }
        return $this->returnData(204, '没有订单需要处理');
    }

    /**
     * 批量完结
     * <AUTHOR>
     * @date   2022/5/11
     *
     * @param  array  $params    参数集
     *
     * @return array
     */
    public function batchFinish(array $params)
    {
        if (!isset($params['serial_code']) || !isset($params['member_id'])) {
            return $this->returnData(203, '参数错误');
        }
        $serialCode    = $params['serial_code'];
        $mainParams    = json_decode($params['params'], true);
        $isOrderSearch = $mainParams['is_order_search'] ?? false;
        $handleType    = $mainParams['handle_type'] ?? 0;
        $operator      = $params['operator'] ?? 0;
        $isShowOrderSearch = $mainParams['is_show_order_search'] ?? false;

        //订单查询 触发取消
        if ($isOrderSearch) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($operator);
            return $this->orderSearchOperate($mainParams, $handleType, $serialCode, $dataAuthLimit);
        }
        if ($isShowOrderSearch) {
            return $this->handleBatchOperateRecord($mainParams, $handleType, $serialCode);
        }
        return $this->returnData(204, '没有订单需要处理');
    }

    /**
     * 订单查询触发批量操作，方法迁移到这里
     * TODO::批量操作上线前，需要确认下new工程下代码有没有变更
     * <AUTHOR>
     * @date   2022/5/10
     *
     * @param  array   $mainParams    参数集
     * @param  int     $handleType    操作方式
     * @param  string  $serialCode    记录编号
     *
     * @return array
     * @throws
     */
    public function orderSearchOperate(array $mainParams, int $handleType = 1, string $serialCode = '', ?DataAuthLimit $dataAuthLimit = null)
    {
        if (empty($mainParams) || !$handleType || empty($serialCode)) {
            return $this->returnData(400, '处理操作参数错误');
        }

        if (!isset($mainParams['dtype']) || !isset($mainParams['oper']) || !isset($mainParams['sid'])) {
            return $this->returnData(400, '参数缺失');
        }

        $delayBegin = $mainParams['delay_begin'] ?? ''; //延期开始
        $delayEnd   = $mainParams['delay_end'] ?? ''; //延期结束
        $params    = $mainParams['params'] ?? [];
        $loginInfo = $mainParams['login_info'] ?? [];

        //分页获取订单号 后续可以优化下
        $orders = $this->getSearchOrderList($params, $loginInfo, $delayBegin, $delayEnd, $dataAuthLimit);
        if (empty($orders)) {
            return $this->returnData(400, '没有查询到需要批量处理的订单');
        }

        //创建订单记录
        $createRes = $this->createRecord($serialCode, $orders);
        if ($createRes['code'] != 200) {
            return $this->returnData(400, '新增记录失败');
        }
        return $this->handleBatchOperateRecord($mainParams, $handleType, $serialCode);
    }

    /**
     * 订单批量操作主逻辑
     * @param array $mainParams
     * @param int $handleType
     * @param string $serialCode
     * @return array
     */
    public function handleBatchOperateRecord(array $mainParams, int $handleType = 1, string $serialCode = '') {
        if (empty($mainParams) || !$handleType || empty($serialCode)) {
            return $this->returnData(400, '处理操作参数错误');
        }

        if (!isset($mainParams['dtype']) || !isset($mainParams['oper']) || !isset($mainParams['sid'])) {
            return $this->returnData(400, '参数缺失');
        }

        $memberID   = $mainParams['sid'];
        $opId       = $mainParams['oper'];
        $dtype      = $mainParams['dtype'];
        $delayBegin = $mainParams['delay_begin'] ?? ''; //延期开始
        $delayEnd   = $mainParams['delay_end'] ?? ''; //延期结束
        $isChange   = $mainParams['is_change'] ?? 0; //是否变更
        $isShowOrderSearch = $mainParams['is_show_order_search'] ?? false; //是否来自演出订单查询
        $isSkipUpstreamOrderValidate = $mainParams['is_skip_upstream_order_validate'] ?? 0;

        $model      = new BatchOperateModel();
        $recordList = $model->getRecordList(0, $serialCode);

        $handleTypeArr = [
            1, //批量验证
            2, //批量完结
            3, //批量取消
            4, //批量延期
        ];

        if (!in_array($handleType, $handleTypeArr)) {
            return $this->returnData(400, '操作类型错误');
        }

        //记录一下请求日志
        pft_log('batch_handle', json_encode($mainParams));
        //员工批量延期权限校验
        if ($handleType == 4 && $dtype == 6) {
            //如果是新版权限体验用户，可以直接使用新版权限中心鉴权
            $res = (new AuthLogicBiz())->resource($memberID, $opId, 'special_order', $dtype);
            if (!in_array('orderDelay', $res)) {
                return $this->returnData(204, '当前员工无权限订单批量延期');
            }
        }

        $orderList = array_column($recordList, 'params');
        if (empty($orderList)) {
            return $this->returnData(204, '没有订单需要处理');
        }

        //实例化模型
        $orderTool   = new \Model\Order\OrderTools();
        $ticketModel = new \Model\Product\Ticket();
        $landModel   = new \Model\Product\Land();

        //需要获取票ID的订单
        $orderTidInfos = [];

        //已经获取过子票的联票的订单 - 防止联票处理多次
        $haveQuerySumOrders = [];

        //如果订单是联票的，要将所有的订单找出
        $handleOrderArr = [];

        //需要获取验证票数的订单
        $queryCheckedArr = [];

        //验证票数数组
        $checkedNumArr = [];

        $orderSuccess = [];
        $orderIdxSuccess = [];
        $orderError   = [];

        foreach ($orderList as $item) {
            $val      = @json_decode($item, true);
            $ordernum = strval($val['ordernum']);
            $tid      = $val['tid'];
            $lid      = $val['lid'];
            $concatId = $val['concat_id'];
            $status   = $val['status'];

            //将非过期状态的订单过滤
            if ($status != 2 && $status != '已过期') {
                if ($isShowOrderSearch && in_array($handleType, [1, 3])) {
                    //演出订单查询页面，允许批量验证和取消非已过期订单
                } else {
                    $orderError[$ordernum] = "订单[{$ordernum}]状态不允许操作 ";
                    continue;
                }
            }

            if ($concatId) {
                //联票之前已经处理过
                if (in_array($concatId, $haveQuerySumOrders)) {
                    $orderError[$ordernum] = "订单[{$ordernum}]不允许操作 ";
                    continue;
                }

                $subOrders = $orderTool->getLinkSubOrder($ordernum);
                if ($subOrders) {
                    $orders = $subOrders; //array_column($subOrders, 'orderid');

                    $handleOrderArr[$concatId] = $orders;
                    $haveQuerySumOrders[]      = $concatId;

                    $queryCheckedArr = array_merge($queryCheckedArr, $orders);
                } else {
                    //查询不到联票信息
                    $orderTidInfos[$ordernum]  = ['tid' => $tid, 'lid' => $lid];
                    $handleOrderArr[$ordernum] = [$ordernum];

                    $queryCheckedArr = array_merge($queryCheckedArr, [$ordernum]);
                }
            } else {
                $orderTidInfos[$ordernum]  = ['tid' => $tid, 'lid' => $lid];
                $handleOrderArr[$ordernum] = [$ordernum];

                $queryCheckedArr = array_merge($queryCheckedArr, [$ordernum]);
            }
        }

        //删除不需要的数据
        unset($orderList);

        //根据订单号找到相应的票类ID和验证数据
        $tmp = $orderTool->getOrderListNew($queryCheckedArr, $field = 'ordernum,tid,lid,status', false, false,
            'refund_num,verified_num,origin_num');
        if ($tmp) {
            foreach ($tmp as $item) {
                $orderTidInfos[$item['ordernum']] = [
                    'tid'      => $item['tid'],
                    'lid'      => $item['lid'],
                    'status'   => $item['status'],
                    'ordernum' => $item['ordernum'],
                ];

                //如果是批量取消订单的话，需要获取订单的验证票数传入到取消接口中
                //TODO:这个是取消接口写得太奇葩了，后期会进行修改
                $checkedNumArr[$item['ordernum']] = isset($item['verified_num']) ? intval($item['verified_num']) : 0;
            }
        }

        //相应订单的票类信息
        $tidArr = array_column($orderTidInfos, 'tid');
        foreach ($orderTidInfos as $ordernum => $val) {
            $tid = $val['tid'];
            $lid = $val['lid'];

            //获取景区信息
            $landInfo = [];
            if ($lid) {
                $landInfo = $landModel->getLandInfo($lid, false, 'p_type,terminal,salerid');
            }
            //获取门票信息
            $ticketInfo = [];
            if ($tid) {
                $ticketInfo = $ticketModel->getTicketInfoById($tid, 'id,apply_did,expire_action,expire_action_fee');
            }

            if (!$landInfo || !$ticketInfo) {
                $orderTidInfos[$ordernum]['ticket'] = false;
            } else {
                $ticketInfo['p_type']               = $landInfo['p_type'];
                $ticketInfo['terminal']             = $landInfo['terminal'];
                $ticketInfo['salerid']              = $landInfo['salerid'];
                $orderTidInfos[$ordernum]['ticket'] = $ticketInfo;
            }
        }

        //根据不同的操作实例化模型
        if ($handleType == 3) {
            $insideSoap = \Library\Tools\Helpers::GetSoapInside();
        } else {
            $handleModel = new \Model\Order\OrderHandler();
        }

        //返回数据
        $resData = [];

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTidArr($tidArr);

        $modifyBiz     = new \Business\Order\Modify();
        $orderQueryBiz = new \Business\Order\OrderSearch();

        foreach ($handleOrderArr as $ordernum => $orders) {
            foreach ($orders as $subOrder) {

                //如果联票里面的其他票是已经验证，取消，完结的状态，就不进行处理了
                if (isset($orderTidInfos[$subOrder]) && isset($orderTidInfos[$subOrder]['status'])) {
                    $tmpStatus = $orderTidInfos[$subOrder]['status'];
                    if (in_array($tmpStatus, [1, 3, 8])) {
                        $orderError[$subOrder] = "订单[{$subOrder}]状态不允许操作";
                        continue;
                    }
                }

                $resData[$subOrder] = ['status' => 1, 'msg' => ''];

                //订单的权限判断
                $ticketInfo = isset($orderTidInfos[$subOrder]['ticket']) ? $orderTidInfos[$subOrder]['ticket'] : false;
                if (!$ticketInfo) {
                    $resData[$subOrder]['status'] = 0;
                    $resData[$subOrder]['msg']    = "订单[{$subOrder}]票类数据缺失 ";
                    $orderError[$subOrder]        = "订单[{$subOrder}]票类数据缺失 ";
                    continue;
                }

                $ptype    = $ticketInfo['p_type'];
                $terminal = $ticketInfo['terminal'];
                $salerid  = $ticketInfo['salerid'];

                //旅游券订单不支持批量操作按失败返回
                if ($ptype == 'Q') {
                    $resData[$subOrder]['status'] = 0;
                    $resData[$subOrder]['msg']    = "旅游券订单[{$subOrder}]暂不支持批量操作";
                    $orderError[$subOrder]        = "旅游券订单[{$subOrder}]暂不支持批量操作";
                    continue;
                }

                $Mdetails = empty($thirdBindInfoArr[$ticketInfo['id']]['Mdetails']) ? 0 : $thirdBindInfoArr[$ticketInfo['id']]['Mdetails'];
                $applyDid = $ticketInfo['apply_did'];

                //判断是不是直接供应商
                if ($applyDid != $memberID) {
                    if (!$isShowOrderSearch || (isset($orderTidInfos[$subOrder]['status']) && $orderTidInfos[$subOrder]['status'] == 2)) {
                        //演出订单批量操作支持非过期状态订单核销取消，需要走常规核销逻辑不能走快速验证
                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]产品发布方才有过期操作的权限 ";
                        $orderError[$subOrder]        = "订单[{$subOrder}]产品发布方才有过期操作的权限 ";
                        continue;
                    }
                }

                if ($handleType == 2) {
                    pft_log('handle/batch_check', json_encode([
                        'name' => 'third_order_batch_finish',
                        'p_type' => $ptype,
                        'm_details' => $Mdetails,
                        'is_skip_upstream_order_validate' => $isSkipUpstreamOrderValidate,
                    ]));
                    //完结操作，对接第三方系统的订单，不能做自动处理
                    if ($ptype != 'H' && $Mdetails == 1 && $isSkipUpstreamOrderValidate == 0) {
                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]对接第三方系统，不做处理 ";
                        $orderError[$subOrder]        = "订单[{$subOrder}]对接第三方系统，不做处理 ";
                        continue;
                    }
                }

                if ($handleType == 4) {
                    //延期操作，对接第三方系统的订单，不能做自动处理
                    if ($ptype == "H" || $ptype == "J" || $ptype == "I" || $ptype == "C") {
                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]演出 特产 年卡 酒店 不支持延期，不做处理 ";
                        $orderError[$subOrder]        = "订单[{$subOrder}]演出 特产 年卡 酒店 不支持延期，不做处理 ";
                        continue;
                    }
                }

                //针对不同类型进行处理
                if ($handleType == 1) {

                    //判断订单是否是已经归档了，如果是已经归档了，就暂时走归档验证规则
                    $archiveInfo = $orderQueryBiz->isArchive($subOrder);

                    if ($archiveInfo) {
                        $cfg      = [
                            'vCmd'       => 602,
                            'vMode'      => 5,
                            'vCheckDate' => date('Y-m-d H:i:s'),
                            'ext_info'   => [
                                'op' => $memberID,
                            ],
                        ];
                        $tSock    = \Library\Business\TerminalCheck::connect(IP_TERMINAL);
                        $chResult = $tSock->Terminal_Check_In_Order($terminal, $salerid, $subOrder, $cfg);

                        if ($chResult['state'] == 'success') {
                            $res = true;
                        } else {
                            $res = ['code' => 0, 'msg' => $chResult['msg']];
                        }
                    } else if ($isShowOrderSearch && isset($orderTidInfos[$subOrder]['status']) && $orderTidInfos[$subOrder]['status'] != 2) {
                        //演出订单批量操作支持非过期状态订单核销，需要走常规核销逻辑不能走快速验证
                        $cfg      = [
                            'vCmd'       => 601,
                            'vMode'      => 5,
                            'vCheckDate' => date('Y-m-d H:i:s'),
                            'ext_info'   => [
                                'op' => $memberID,
                            ],
                        ];
                        $tSock    = \Library\Business\TerminalCheck::connect(IP_TERMINAL);
                        $chResult = $tSock->Terminal_Check_In_Order($terminal, $salerid, $subOrder, $cfg);

                        if ($chResult['state'] == 'success') {
                            $res = true;
                        } else {
                            $res = ['code' => 0, 'msg' => $chResult['msg']];
                        }
                    } else {
                        //验证
                        $res = $handleModel->CheckOrderSimply($subOrder, $memberID, $order_info = null, $memo = '批量验证',
                            $source = 1);
                    }

                    if ($res === true) {
                        $orderSuccess[]               = $subOrder;
                        $resData[$subOrder]['status'] = 1;
                    } else {
                        $msg = $res['msg'];

                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]验证失败 - {$msg} ";
                        $orderError[$subOrder]        = $resData[$subOrder]['msg'];

                        pft_log('handle/batch_check/', $resData[$subOrder]['msg']);
                    }
                } else if ($handleType == 2) {
                    //完结
                    $modifyBiz = new \Business\Order\Modify();
                    $res       = $modifyBiz->finishOrder($subOrder, $memberID, $source = 16, '批量完结');

                    if ($res['code'] == 200) {
                        $orderSuccess[]               = $subOrder;
                        $resData[$subOrder]['status'] = 1;
                    } else {
                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]完结失败 ";
                        $orderError[$subOrder]        = $resData[$subOrder]['msg'];
                    }
                } else if ($handleType == 4) {
                    //延期
                    $modifyBiz = new \Business\JavaApi\Order\OrderInfoUpdate();
                    $res       = $modifyBiz->batchOrderDelayIdx([$subOrder], $delayBegin, $delayEnd, $opId, $isChange);

                    if ($res['code'] == 200 && !empty($res['data'])) {
                        foreach ($res['data'] as $resItem) {
                            $orderIdxSuccess[$resItem['orderId']] = $resItem['idxList'];
                        }
                        $orderSuccess[]               = $subOrder;
                        $resData[$subOrder]['status'] = 1;
                    } else {
                        $errorMsg = $res['code'] == 200 ? '请检查订单是否满足延期条件/延期日期是否正确' : $res['msg'];
                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]延期失败，" . $errorMsg;
                        $orderError[$subOrder]        = $resData[$subOrder]['msg'];
                    }
                } else {
                    //取消
                    try {
                        $cancelNum       = -1;
                        $cancelRemarkArr = [
                            'remark' => '批量取消',
                        ];
                        $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($subOrder);
                        $res             = \Library\Tools\Helpers::platformRefund($subOrder, $cancelNum, $memberID,
                            \Library\Constants\OrderConst::PC_CANCEL, 'common', $reqSerialNumber, $cancelRemarkArr);

                        $resErrorMsg = isset($res['data']['err_msg']) ? $res['data']['err_msg'] : $res['msg'];
                        if ($isShowOrderSearch && $res['code'] == 1095) {
                            //演出订单批量操作，添加退票审核成功属于操作成功
                            $orderSuccess[]               = $subOrder;
                            $resData[$subOrder]['status'] = 1;
                        } else if ($res['code'] == 200) {
                            $orderSuccess[]               = $subOrder;
                            $resData[$subOrder]['status'] = 1;
                        } else {
                            $resData[$subOrder]['status'] = 0;
                            $resData[$subOrder]['msg']    = "订单[{$subOrder}]取消失败 - {$resErrorMsg} ";
                            $orderError[$subOrder]        = $resData[$subOrder]['msg'];
                        }
                    } catch (\Exception $e) {
                        $resData[$subOrder]['status'] = 0;
                        $resData[$subOrder]['msg']    = "订单[{$subOrder}]取消失败 ";
                        $orderError[$subOrder]        = $resData[$subOrder]['msg'];
                    }

                }
            }
        }

        //最后统一操作一下
        if($handleType == 4) { //延期
            if(!empty($delayEnd) && $orderIdxSuccess) {
                $syncFaceResult = (new OrderDelay())->syncOrderIdxFaceValid($orderIdxSuccess, strtotime($delayEnd), $orderTidInfos);
                pft_log('order_delay/sync_face_result', json_encode([$orderIdxSuccess, $delayEnd, $syncFaceResult], JSON_UNESCAPED_UNICODE));
            }
        }

        //队列更新
        $this->_handleUpdateRecord($orderSuccess, $orderError, $serialCode);

        return $this->returnData(200, '批量操作成功', $resData);
    }

    /**
     * 更新状态
     * <AUTHOR>
     * @date   2022/5/6
     *
     * @param  array  $mainId      记录id
     * @param  int    $state       处理状态:0=未处理,1=处理中,2=处理完成
     * @param  int    $oldState    更新前状态  处理状态:0=未处理,1=处理中,2=处理完成
     *
     * @return array
     */
    public function updateMainSate(array $mainId, int $state, int $oldState)
    {
        if (!in_array($state, BatchOperateModel::MAIN_STATE)) {
            return $this->returnData(203, '状态错误');
        }

        $model = new BatchOperateModel();

        $saveData = [
            'state' => $state,
        ];

        //获取成功总数
        if ($state == BatchOperateModel::MAIN_STATE_DONE) {
            $mainRes  = $model->getMainList("serial_code", $mainId);
            $mainInfo = $mainRes[0] ?? [];
            if (!empty($mainInfo) && !empty($mainInfo['serial_code'])) {
                $recordState = BatchOperateModel::RECORD_STATE_DONE;
                $successNum  = $model->getRecordList(0, $mainInfo['serial_code'], '', $recordState, [], true);
                $totalNum    = $model->getRecordList(0, $mainInfo['serial_code'], '', 0, [], true);
                //更新成功数
                $saveData['success_num'] = $successNum;
                $saveData['total_num']   = $totalNum;
            }
        }

        $res = $model->updateMain($mainId, '', '', $oldState, $saveData);
        if (!$res) {
            return $this->returnData(203, '更新失败');
        }

        return $this->returnData(200, '更新成功');
    }

    /**
     * 订单操作记录更新
     * <AUTHOR>
     * @date   2022/5/7
     *
     * @param  array   $ordernum      订单号
     * @param  string  $serialCode    记录编码
     * @param  string  $msg           错误信息
     * @param  int     $state         状态
     * @param  int     $oldState      之前状态
     *
     * @return array
     */
    public function updateRecordSate(string $serialCode, array $ordernum, int $state, int $oldState, string $msg = '')
    {
        if (!in_array($state, BatchOperateModel::RECORD_STATE) || empty($serialCode) || empty($ordernum)) {
            return $this->returnData(203, '状态错误');
        }

        $model    = new BatchOperateModel();
        $saveData = [
            'state'      => $state,
            'err_remark' => $msg,
        ];

        $res = $model->updateRecord(0, $serialCode, $ordernum, $oldState, $saveData);
        if (!$res) {
            return $this->returnData(203, '更新失败');
        }

        return $this->returnData(200, '更新成功');
    }

    /**
     * 新增操作记录
     * <AUTHOR>
     * @date   2022/5/7
     *
     * @param  int    $memberId    用户id
     * @param  int    $oper        操作人
     * @param  array  $orders      订单列表
     * @param  int    $type        操作类型
     * @param  array  $params      参数
     *
     * @return array
     */
    public function createOperate(int $memberId, int $oper, array $orders, int $type, array $params = [])
    {
        $code = 200;
        $data = [];
        $msg  = '添加批量操作成功';
        try {
            if (!$memberId || !$oper || !$type) {
                throw new \Exception('参数错误', 203);
            }

            $model = new BatchOperateModel();

            $model->startTrans();

            $serialCode = $this->createSerialCode($memberId);
            $total      = count($orders);

            //新增操作记录
            $mainRes = $this->createMain($serialCode, $memberId, $oper, $total, $type, $params);
            if ($mainRes['code'] != 200) {
                $model->rollback();
                throw new \Exception($mainRes['msg'], 400);
            }

            //新增操作订单记录
            if (!empty($orders)) {
                $recordRes = $this->createRecord($serialCode, $orders);
                if ($recordRes['code'] != 200) {
                    $model->rollback();
                    throw new \Exception($recordRes['msg'], 400);
                }
            }

            $model->commit();
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 获取批量导出列表
     * <AUTHOR>
     * @date   2022/5/12
     *
     * @param  int     $sid
     * @param  int     $type
     * @param  string  $begin
     * @param  string  $end
     * @param  int     $status
     * @param  string  $ptype
     * @param  int     $oper
     * @param  int     $page
     * @param  int     $size
     *
     * @return array
     */
    public function batchOrderList(int $sid, int $type = 99, string $begin = '', string $end = '', int $status = -1, string $ptype = '', int $oper = 0, int $page = 1, int $size = 10)
    {
        if (!$sid) {
            return $this->returnData(204, '参数错误');
        }

        switch ($type) {
            case 99:
                $res   = (new \Business\Order\BatchOrder())->batchOrderList($sid, $begin, $end, $status, $ptype, $oper,
                    $page, $size);
                break;
            default:
                $res =  $this->getBatchList($sid, $type, $begin, $end, $status, $oper, $page, $size);
        }

        return $this->returnData($res['code'], $res['msg'], $res['data']);
    }

    /**
     * 获取批量列表
     * <AUTHOR>
     * @date   2022/5/12
     *
     * @param  int     $sid
     * @param  int     $type
     * @param  string  $begin
     * @param  string  $end
     * @param  int     $status
     * @param  int     $oper
     * @param  int     $page
     * @param  int     $size
     *
     * @return array
     */
    public function getBatchList(int $sid, int $type = 0, string $begin = '', string $end = '', int $status = -1, int $oper = 0, int $page = 1, int $size = 10)
    {
        if (!$sid) {
            return $this->returnData(204, '参数错误');
        }

        $model = new BatchOperateModel();

        $whereRaw = [];
        if ($begin && $end) {
            $whereRaw['create_time'] = ['between', [strtotime($begin), strtotime($end)]];
        }
        $total = $model->getMainList('', 0, '', $sid, $oper, $status, $type, $whereRaw, 'id desc', true);
        $list  = [];
        if ($total) {
            $list      = $model->getMainList('', 0, '', $sid, $oper, $status, $type, $whereRaw, 'id desc', false, $page,
                $size);
            $operArr   = array_column($list, 'operator');
            $memberApi = new MemberQuery();
            $memberRes = $memberApi->queryMemberByMemberQueryInfo(['idList' => $operArr]);
            if ($memberRes['code'] == 200) {
                $nameMap = array_key(array_column($memberRes['data'], 'memberInfo'), 'id');
            } else {
                $nameMap = [];
            }
            foreach ($list as &$item) {
                $item['oper']         = $nameMap[$item['operator']]['dname'] ?? '';
                $item['oper_account'] = $nameMap[$item['operator']]['account'] ?? '';
                $item['update_date'] = date("Y-m-d H:i:s", $item['update_time']);
                $item['create_date'] = date("Y-m-d H:i:s", $item['create_time']);
                unset($item['params']);
                unset($item['create_time']);
                unset($item['update_time']);
                unset($item['member_id']);
            }
        }

        return $this->returnData(200, '', ['list' => $list, 'total' => $total]);
    }

    /**
     * 导出处理结果
     * <AUTHOR>
     * @date   2022/5/12
     *
     * @param  int  $sid       用户id
     * @param  int  $type      操作类型
     * @param  int  $mainId    操作id
     * @param  int  $oper      操作人
     *
     * @return array
     */
    public function exportBatchRecord(int $sid, int $type, int $mainId, int $oper = 0)
    {
        if (!$sid || !$type || !$mainId) {
            return $this->returnData(204, '参数错误');
        }
        switch ($type) {
            case 99:
                $res = (new \Business\Order\BatchOrder())->exportBatchOrderRecord($sid, $mainId);
                break;
            default:
                $res = $this->handleExportRecord($sid, $type, $mainId);
        }

        return $this->returnData($res['code'] ?? 500, $res['msg'] ?? '', $res['data'] ?? []);
    }

    /**
     * 处理导出记录
     * <AUTHOR>
     * @date   2022/5/12
     *
     * @param  int  $sid       用户id
     * @param  int  $type      操作类型
     * @param  int  $mainId    操作id
     *
     * @return array
     */
    public function handleExportRecord(int $sid, int $type, int $mainId)
    {
        if (!$sid || !$type || !$mainId) {
            return $this->returnData(204, '参数错误');
        }

        $model    = new BatchOperateModel();
        $mainRes  = $model->getMainList("serial_code", $mainId);
        $mainInfo = $mainRes[0] ?? [];
        if (empty($mainInfo) || empty($mainInfo['serial_code'])) {
            return $this->returnData(400, '操作不存在');
        }

        $serialCode = $mainInfo['serial_code'];
        $recordList = $model->getRecordList(0, $serialCode);
        if (empty($recordList)) {
            return $this->returnData(400, '没有可处理的订单号');
        }

        $titleMap = [
            0 => '异常',
            1 => '处理异常',
            2 => '处理成功',
            3 => '处理失败',
        ];

        $data = [];
        switch ($type) {
            case self::ACTION_TYPE_VAIL_DATE:
                $data[] = ['订单号', '延期开始', '延期结束', '状态', '备注'];
                foreach ($recordList as $item) {
                    $params = json_decode($item['params'] ?? '', true);
                    $data[] = [
                        ($item['ordernum'] ?? '') . "\t",
                        $params['start_time'] ?? '',
                        $params['end_time'] ?? '',
                        $titleMap[$item['state'] ?? 0] ?? '状态异常',
                        $item['err_remark'] ?? '',
                    ];
                }
                break;
            case self::ACTION_TYPE_CANCEL:
            case self::ACTION_TYPE_CHECKED:
            case self::ACTION_TYPE_FINISH:
                $data[] = ['订单号', '状态', '备注'];
                foreach ($recordList as $item) {
                    $data[] = [
                        ($item['ordernum'] ?? '') . "\t",
                        $titleMap[$item['state'] ?? 0] ?? '状态异常',
                        $item['err_remark'] ?? '',
                    ];
                }
                break;
            case self::ACTION_TYPE_DISTRIBUTOR_RENEW:
                $data[] = ['分销商账号', '状态', '备注'];
                foreach ($recordList as $item) {
                    $data[] = [
                        ($item['ordernum'] ?? '') . "\t",
                        $titleMap[$item['state'] ?? 0] ?? '状态异常',
                        $item['err_remark'] ?? '',
                    ];
                }
                break;
            default:
                return $this->returnData(400, '操作类型错误');
        }

        return $this->returnData(200, '', $data);
    }

    /**
     * 终止批次操作
     * <AUTHOR>
     * @date   2022/5/12
     *
     * @param  int  $sid       用户id
     * @param  int  $type      操作类型
     * @param  int  $mainId    操作id
     * @param  int  $oper      操作人
     *
     * @return array
     */
    public function stopBatch(int $sid, int $type, int $mainId, int $oper = 0)
    {
        if (!$sid || !$type || !$mainId) {
            return $this->returnData(204, '参数错误');
        }

        switch ($type) {
            case 99:
                $res = (new \Business\Order\BatchOrder())->stopMain($sid, $mainId);
                break;
            default:
                $res = $this->stopMain($sid, $mainId);
        }

        if ($res['code'] != 200) {
            return $this->returnData(400, $res['msg'] ?? '终止失败');
        }

        return $this->returnData(200, '终止成功');
    }

    /**
     * 终止操作
     * <AUTHOR>
     * @date   2022/5/13
     *
     * @param  int  $sid       用户id
     * @param  int  $mainId    操作id
     *
     * @return array
     */
    public function stopMain(int $sid, int $mainId)
    {
        if (!$sid || !$mainId) {
            return $this->returnData(400, '参数缺失');
        }

        $model = new BatchOperateModel();
        $state = $model::MAIN_STATE_WAIT;

        $mainRes = $model->getMainList('', $mainId);
        $info    = $mainRes[0] ?? [];
        if (empty($info) || (isset($info['state']) && $info['state'] != $state)) {
            return $this->returnData(400, '操作不存在，或者状态不允许终止');
        }

        $saveData = ['state' => $model::MAIN_STATE_STOP];
        $res      = $model->updateMain($mainId, '', $sid, $state, $saveData);
        if (!$res) {
            return $this->returnData(400, '终止失败');
        }

        return $this->returnData(200, '终止成功');
    }

    /**
     * 新增操作记录
     * <AUTHOR>
     * @date   2022/5/7
     *
     * @param  string  $serialCode    记录编号
     * @param  int     $memberId      用户id
     * @param  int     $oper          操作人
     * @param  int     $total         总数
     * @param  int     $type          操作类型
     * @param  array   $params        参数
     *
     * @return array
     */
    public function createMain(string $serialCode, int $memberId, int $oper, int $total, int $type, array $params)
    {
        if (empty($serialCode) || !$memberId || !$oper || !$type) {
            return $this->returnData(203, '参数错误');
        }

        $model = new BatchOperateModel();

        $res = $model->adddMain($memberId, $oper, $total, $serialCode, $params, $type);
        if (!$res) {
            return $this->returnData(400, '新增失败');
        }

        return $this->returnData(200, '新增成功');
    }

    /**
     * 新增操作订单记录
     * <AUTHOR>
     * @date   2022/5/7
     *
     * @param  string  $serialCode    记录编码
     * @param  array   $orders        订单相关列表
     *
     * @return array
     */
    public function createRecord(string $serialCode, array $orders)
    {
        if (empty($serialCode) || empty($orders)) {
            return $this->returnData(203, '参数错误');
        }

        $inster = [];
        foreach ($orders as $item) {
            $ordernum = $item['ordernum'] ?? '';
            if (empty($ordernum)) {
                continue;
            }
            $params   = $item['params'] ?? [];
            $inster[] = [
                'serial_code' => $serialCode,
                'params'      => json_encode($params),
                'ordernum'    => $ordernum,
            ];
        }

        if (empty($inster)) {
            return $this->returnData(400, '参数缺失');
        }

        $model = new BatchOperateModel();
        $res   = $model->adddRecord($inster);
        if (!$res) {
            return $this->returnData(400, '记录新增失败');
        }

        return $this->returnData(200, '记录新增成功');
    }

    /**
     * 处理订单查询 批量取消订单操作
     * 注：基本之前new/d/call/handle.php:batch_handle_orders 处理来的
     * <AUTHOR>
     * @date   2022/5/10
     *
     * @param  array  $params       操作参数
     * @param  array  $loginInfo    登录信息
     *
     * @return array
     */
    public function handleSearchOrder(array $params, array $loginInfo)
    {
        $code = 200;
        $data = [];
        $msg  = '';
        try {
            $handleTypeArr = [
                1 => self::ACTION_TYPE_CHECKED, //批量验证
                2 => self::ACTION_TYPE_FINISH, //批量完结
                3 => self::ACTION_TYPE_CANCEL, //批量取消
                4 => self::ACTION_TYPE_VAIL_DATE, //批量延期
            ];

            $memberType = $loginInfo['dtype'] ?? -1;
            if (empty($params) || $memberType == -1) {
                throw new \Exception('参数错误', 203);
            }

            if (!isset($params['handle_type']) || !in_array($params['handle_type'], $handleTypeArr)) {
                throw new \Exception('操作类型错误', 203);
            }

            //多票查询，逗号分隔处理
            $params['tid'] = (new \Business\Order\OrderList())->paramParseCommaSeparated($params['tid']);
            $tidArr        = $params['tid'] ?: false;
            if (is_array($tidArr) && count($tidArr) > 20) {
                throw new \Exception('一次最多可选20个票种，已超过，请重试');
            }

            $orders     = [];
            $mainParams = [
                'is_order_search' => true, //标记下是订单查询来源
                'dtype'           => $loginInfo['dtype'], //用户类型
                'oper'            => $loginInfo['memberID'], //操作人
                'sid'             => $loginInfo['sid'], //商户id
                'handle_type'     => $params['handle_type'], //操作类型
                'params'          => $params, //请求参数
                'login_info'      => $loginInfo, //登录信息
                'delay_begin'     => $params['delay_begin'] ?? '', //延期开始
                'delay_end'       => $params['delay_end'] ?? '', //延期结束
                'is_change'       => $params['is_change'] ?? 0,//
            ];

            $sid      = $loginInfo['sid'];
            $memberId = $loginInfo['memberID'];
            $type     = $handleTypeArr[$params['handle_type']] ?? 0;
            //批量记录写入
            $result = $this->createOperate($sid, $memberId, $orders, $type, $mainParams);

            $code = $result['code'];
            $data = $result['data'];
            $msg  = $result['msg'];

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 生产序列号
     * <AUTHOR>
     * @date   2022/4/27
     *
     * @param  int  $sid    商户id
     *
     * @return string
     */
    public function createSerialCode(int $sid)
    {
        $serialCode = $sid . date('YmdHis') . rand(1, 10000);
        $serialCode = str_pad($serialCode, 30, '0');

        return $serialCode;
    }

    /**
     * 分页获取订单查询数据
     * 注：基本之前new/d/call/handle.php:batch_handle_orders 处理来的
     * <AUTHOR>
     * @date   2022/5/9
     *
     * @param  array  $params
     * @param  array  $loginInfo
     * @param  string  $delayBegin
     * @param  string  $delayEnd
     *
     * @return array
     * @throws
     */
    public function getSearchOrderList(array $params, array $loginInfo, string $delayBegin = '', string $delayEnd = '', ?DataAuthLimit $dataAuthLimit = null)
    {
        $memberType = $loginInfo['dtype'] ?? -1;
        if (empty($params) || $memberType == -1) {
            throw new \Exception('参数错误', 203);
        }

        $orders = [];

        // 使用游标翻页，避免触发总数限制
        $scrollKey = '';
        while (true) {
            if ($memberType == 2) {
                $res = $this->_getScenicListScroll($loginInfo, $params, $scrollKey);
            } else {
                $res = $this->_getBusinessListScroll($loginInfo, $params, $dataAuthLimit, $scrollKey);
            }

            $list = $res['list'] ?? [];
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                if ($item['ptype'] == 'Q') {
                    continue;
                }
                $tmpOrdernum = $item['ordernum'] ?? ($item['uniq_key'] ?? '');
                if (!$tmpOrdernum) {
                    continue;
                }
                $tid = 0;
                if (isset($item['tid'])) {
                    $tid = is_array($item['tid']) ? ($item['tid']['id'] ?? 0) : $item['tid'];
                }
                $lid = 0;
                if (isset($item['lid'])) {
                    $lid = is_array($item['lid']) ? ($item['lid']['id'] ?? 0) : $item['lid'];
                }
                $status = $item['status'] ?? 0;

                $orders[] = [
                    'ordernum' => $tmpOrdernum,
                    'params'   => [
                        'ordernum'   => $tmpOrdernum,
                        'tid'        => $tid,
                        'lid'        => $lid,
                        'concat_id'  => '',
                        'status'     => $status,
                        'start_time' => $delayBegin,
                        'end_time'   => $delayEnd,
                    ],
                ];
            }

            $scrollKey = $res['scroll_key'] ?? '';
            if (empty($scrollKey)) {
                break;
            }
        }

        return $orders;
    }

    /**
     * 景区账号查询订单列表（游标翻页）
     * @param array $loginInfo
     * @param array $paramArr
     * @param string $scrollKey
     * @return array [list, scroll_key]
     * @throws \Exception
     */
    private function _getScenicListScroll(array $loginInfo, array $paramArr, string $scrollKey = '')
    {
        $sid        = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $account    = $loginInfo['account'];
        $memberId   = $loginInfo['memberID'];

        // 景区数据查询账号
        $paramArr['salerid'] = $account;

        // 参数处理（与页面查询一致）
        $orderLib = new \Business\Order\OrderList();
        $res      = $orderLib->handleOrderParam($paramArr, $sid);
        if (empty($res['code'])) {
            throw new \Exception($res['msg']);
        }

        // 时间范围
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        // 预留数组
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;

        // 多票查询，逗号分隔
        $paramArr['tid'] = (new \Business\Order\OrderList())->paramParseCommaSeparated($paramArr['tid']);
        $tidArr          = $paramArr['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            throw new \Exception('一次最多可选20个票种，已超过，请重试');
        }

        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        $ordermodeIn = $paramArr['order_mode'] !== false ? (is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']]) : false;
        $pmodeIn     = $paramArr['pay_mode'] !== false ? (is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']]) : false;

        // 游标翻页导出接口（不依赖 total）
        $page = 1;
        $size = 1000;
        $queryRes = $orderLib->getScenicExportByOrderService(
            $memberId,
            $sid,
            $memberType,
            $paramArr['salerid'],
            $dbStart,
            $dbEnd,
            $page,
            $size,
            $ordernumArr,
            $paramArr['ordername'],
            $paramArr['person_id'],
            $paramArr['userMobileSubject'],
            $paramArr['order_time_start'],
            $paramArr['order_time_end'],
            $paramArr['play_time_start'],
            $paramArr['play_time_end'],
            $paramArr['dtime_start'],
            $paramArr['dtime_end'],
            $paramArr['begin_time_start'],
            $paramArr['begin_time_end'],
            $paramArr['status'],
            $paramArr['pay_status'],
            $pmodeIn,
            $ordermodeIn,
            $paramArr['p_type'],
            $paramArr['operate_id'],
            $paramArr['check_resource'],
            $pidArr,
            $tidArr,
            $paramArr['order_source'],
            $paramArr['if_print'],
            $paramArr['mark'],
            $paramArr['begin_first_time_start'] ?? false,
            $paramArr['begin_first_time_end'] ?? false,
            $paramArr['upstreamOrderId'] ?? false,
            $paramArr['afterSaleState'] ?? '',
            $paramArr['check_code'] ?? '',
            $paramArr['sub_type'] ?? false,
            $paramArr['requestId'] ?? '',
            $paramArr['round_id'] ?? 0,
            $paramArr['cancel_time_start'] ?? false,
            $paramArr['cancel_time_end'] ?? false,
            $paramArr['touristMobileSubject'] ?? false,
            $paramArr['touristIdentificationCode'] ?? false,
            $paramArr['cmbId'] ?? '',
            $paramArr['personid'] ?? '',
            $paramArr['remotenum'] ?? '',
            $paramArr['apiOrder'] ?? '',
            $scrollKey
        );

        if (($queryRes['code'] ?? 500) != 200) {
            throw new \Exception($queryRes['msg'] ?? '查询失败');
        }

        $list      = $queryRes['data']['list'] ?? [];
        $nextKey   = $queryRes['data']['scrollKey'] ?? '';
        return ['list' => $list, 'scroll_key' => $nextKey];
    }

    /**
     * 普通账号查询订单列表（游标翻页）
     * @param array $loginInfo
     * @param array $paramArr
     * @param DataAuthLimit|null $dataAuthLimit
     * @param string $scrollKey
     * @return array [list, scroll_key]
     * @throws \Exception
     */
    private function _getBusinessListScroll(array $loginInfo, array $paramArr, ?DataAuthLimit $dataAuthLimit = null, string $scrollKey = '')
    {
        $sid        = $loginInfo['sid'];
        $memberType = $loginInfo['dtype'];
        $memberId   = $loginInfo['memberID'];

        // 参数处理
        $orderLib = new \Business\Order\OrderList();
        $res      = $orderLib->handleOrderParam($paramArr, $sid);
        if (empty($res['code'])) {
            throw new \Exception($res['msg']);
        }

        // 时间范围
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        // 资源过滤
        $lidArr = [];
        if ($paramArr['lid']) {
            $lidArr = [$paramArr['lid']];
        } elseif ($paramArr['source_id']) {
            $landApi      = new \Business\CommodityCenter\Land();
            $resourceList = $landApi->queryLandMultiQueryByResourceId([$paramArr['source_id']]);
            $lidArr       = array_column($resourceList, 'id');
        }

        if ($memberType == 7) {
            $orderSearchBusiness = new \Business\Order\OrderSearch();
            $memberArr           = $orderSearchBusiness->getRelationMember($sid);
        } else {
            $memberArr = [$sid];
        }

        // 预留数组
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;

        // 多票查询
        $paramArr['tid'] = (new \Business\Order\OrderList())->paramParseCommaSeparated($paramArr['tid']);
        $tidArr          = $paramArr['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            throw new \Exception('一次最多可选20个票种，已超过，请重试');
        }

        // 供应商/分销商
        $sellerIdArr = $paramArr['aid'] ? [$paramArr['aid']] : false;
        if ($paramArr['reseller_id']) {
            $buyerIdArr = is_array($paramArr['reseller_id']) ? $paramArr['reseller_id'] : [$paramArr['reseller_id']];
        } else {
            $buyerIdArr = false;
        }

        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        $ordermodeIn = $paramArr['order_mode'] !== false ? (is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']]) : false;
        $pmodeIn     = $paramArr['pay_mode'] !== false ? (is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']]) : false;

        // 数据权限
        $notLidArr = [];
        if ($dataAuthLimit) {
            $condition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lidArr]);
            if ($condition === false) {
                return ['list' => [], 'scroll_key' => ''];
            }
            $lidArr   = $condition['lidList'] ?? [];
            $notLidArr = $condition['notLidList'] ?? false;
        }

        // 游标翻页导出接口
        $page = 1;
        $size = 1000;
        $queryRes = $orderLib->getBusinessExportByOrderService(
            $memberId,
            $sid,
            $memberType,
            $memberArr,
            $dbStart,
            $dbEnd,
            $page,
            $size,
            $ordernumArr,
            $paramArr['ordername'],
            $paramArr['person_id'],
            $paramArr['userMobileSubject'],
            $paramArr['order_time_start'],
            $paramArr['order_time_end'],
            $paramArr['play_time_start'],
            $paramArr['play_time_end'],
            $paramArr['dtime_start'],
            $paramArr['dtime_end'],
            $paramArr['begin_time_start'],
            $paramArr['begin_time_end'],
            $paramArr['status'],
            $paramArr['pay_status'],
            $pmodeIn,
            $ordermodeIn,
            $paramArr['p_type'],
            $paramArr['operate_id'],
            $paramArr['check_resource'],
            $lidArr,
            $pidArr,
            $sellerIdArr,
            $buyerIdArr,
            $tidArr,
            $paramArr['order_source'],
            $paramArr['if_print'],
            $paramArr['mark'],
            $paramArr['begin_first_time_start'] ?? false,
            $paramArr['begin_first_time_end'] ?? false,
            $paramArr['is_combine'] ?? -1,
            $paramArr['upstreamOrderId'] ?? false,
            $paramArr['sub_sid'] ?? 0,
            $paramArr['afterSaleState'] ?? '',
            $paramArr['check_code'] ?? '',
            $notLidArr,
            null,
            $paramArr['sub_type'] ?? false,
            $paramArr['requestId'] ?? '',
            $paramArr['round_id'] ?? 0,
            $paramArr['cancel_time_start'] ?? false,
            $paramArr['cancel_time_end'] ?? false,
            $paramArr['touristMobileSubject'] ?? false,
            $paramArr['touristIdentificationCode'] ?? false,
            $paramArr['cmbId'] ?? '',
            $paramArr['personid'] ?? '',
            $paramArr['remotenum'] ?? '',
            $paramArr['apiOrder'] ?? '',
            $scrollKey,
            intval($paramArr['identity_photo'] ?? 0)
        );

        if (($queryRes['code'] ?? 500) != 200) {
            \pft_log('batch_search_order_fail', json_encode($queryRes, JSON_UNESCAPED_UNICODE));
            throw new \Exception($queryRes['msg'] ?? '查询失败');
        }

        $list      = $queryRes['data']['list'] ?? [];
        $nextKey   = $queryRes['data']['scrollKey'] ?? '';
        return ['list' => $list, 'scroll_key' => $nextKey];
    }

    /**
     * 批量处理操作记录更新
     * <AUTHOR>
     * @date   2022/5/11
     *
     * @param  array   $orderSuccess    成功的订单号数据
     * @param  array   $orderError      失败的订单号和错误信息
     * @param  string  $serialCode      记录编号
     *
     * @return  bool
     */
    private function _handleUpdateRecord(array $orderSuccess, array $orderError, string $serialCode)
    {
        if (!empty($orderSuccess) || !empty($orderError)) {
            $args = [
                'action' => 'batch_operate_update_record_state',
                'data'   => [
                    'error'       => $orderError,
                    'success'     => $orderSuccess,
                    'serial_code' => $serialCode,
                ],
            ];

            \Library\Resque\Queue::push('independent_system', 'BatchOperate_Job', $args);
        }

        return true;
    }

    /**
     * 批量处理订单-演出
     * @param array $loginInfo
     * @param array $orderIdArr
     * @param array $params
     * @return array
     */
    public function batchHandleOrderForShow($loginInfo, $orderIdArr, $params) {
        $actionTypeArr = [
            1 => BatchOperate::ACTION_TYPE_CHECKED, //批量验证
            2 => BatchOperate::ACTION_TYPE_FINISH, //批量完结
            3 => BatchOperate::ACTION_TYPE_CANCEL, //批量取消
            4 => BatchOperate::ACTION_TYPE_VAIL_DATE, //批量延期
        ];
        $sid = $loginInfo['sid'];
        $dtype = $loginInfo['dtype'];
        $memberId = $loginInfo['memberID'];
        $delayBegin = $params['delay_begin'] ?? ''; //延期开始
        $delayEnd   = $params['delay_end'] ?? ''; //延期结束

        $mainParams = [
            'is_show_order_search' => true, //标记下是演出订单查询来源
            'dtype'           => $dtype, //用户类型
            'oper'            => $memberId, //操作人
            'sid'             => $sid, //商户id
            'handle_type'     => $params['handle_type'], //操作类型
            'params'          => [], //请求参数
            'login_info'      => $loginInfo, //登录信息
            'delay_begin'     => $delayBegin, //延期开始
            'delay_end'       => $delayEnd, //延期结束
            'is_change'       => $params['is_change'] ?? 0,//
            'is_skip_upstream_order_validate' => $params['is_skip_upstream_order_validate'] ?? 0,
        ];

        $orderTools = new OrderTools();
        $orderInfoArr = $orderTools->getOrderInfo($orderIdArr);
        $orders = [];
        foreach ($orderInfoArr as $orderInfo) {
            if ($orderInfo['product_type'] == 'Q') {
                continue;
            }
            $orders[] = [
                'ordernum' => $orderInfo['ordernum'],
                'params' => [
                    'ordernum'   => $orderInfo['ordernum'],
                    'tid'        => $orderInfo['tid'],
                    'lid'        => $orderInfo['lid'],
                    'concat_id'  => $orderInfo['concat_id'],
                    'status'     => $orderInfo['status'],
                    'start_time' => $delayBegin,
                    'end_time'   => $delayEnd,
                ]
            ];
        }
        if (!$orders) {
            return $this->returnDataV2(200);
        }
        $actionType = $actionTypeArr[$params['handle_type']];
        //批量记录写入
        return $this->createOperate($sid, $memberId, $orders, $actionType, $mainParams);
    }
}
