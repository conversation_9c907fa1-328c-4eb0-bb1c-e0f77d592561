<?php
/**
 * 原先的平台端统一下单业务封装层
 *
 * <AUTHOR>
 * @date   2019-02-15
 *
 */

namespace Business\Order;

use Business\Base;
use Business\CommodityCenter\Product;
use Business\JavaApi\Order\Query\OrderAidsSplitQuery;
use Business\Order\MergeOrder;
use Business\JavaApi\Product\Ticket as NewTicketApi;
use Business\PackTicket\PackConst;
use Business\TeamOrder\TeamOrderReport;
use Library\Constants\OrderConst;
use Library\Constants\Team\TeamConst;
use Library\Tools\Helpers;
use Library\Util\Team\TeamUtil;
use Model\Order\TeamOrderSearch;
use Model\Product\Ticket;
use Model\Product\Ticket as TicketModel;
use Model\Team\TeamReportRealTask;
use Process\Order\OrderParams;
use Library\Constants\Discount\DiscountType;
use Business\JsonRpcApi\MarketCenterService\ActivityApi;

class PlatformSubmit extends Base
{
    use \Business\PackTicket\Traits\PackageAbnormalOrderTrait;
    //下单必填的参数
    private $_orderMustParams = [
        'pid'       => '产品ID', //产品ID
        'begintime' => '开始游玩时间', //开始游玩时间
        'channel'   => '购买渠道', //购买渠道
        'tnum'      => '购买数量', //购买数量
        'paymode'   => '支付方式', //支付方式
        'member_id' => '下单用户ID', //下单用户ID - 散客默认112
        'aid'       => '产品供应商ID', //产品供应商ID
        'ordername' => '游玩用户姓名', //游玩用户姓名
        'ordertel'  => '游玩用户手机号', //游玩用户手机号
        'op_id'     => '操作用户', //操作用户
        'is_sale'   => '是否散客下单', //是否散客下单
    ];

    //合并付款必须参数
    private $_combineMustParams = [
        'combine_pids' => '购买产品数据', //购买产品数据
        'begintime'    => '开始游玩时间', //开始游玩时间
        'channel'      => '购买渠道', //购买渠道
        'paymode'      => '支付方式', //支付方式
        'member_id'    => '下单用户ID', //下单用户ID - 散客默认112
        //'ordername'    => '游玩用户姓名', //游玩用户姓名
        //'ordertel'     => '游玩用户手机号', //游玩用户手机号
        'op_id'        => '操作用户', //操作用户
        'is_sale'      => '是否散客下单', //是否散客下单
    ];

    public function __construct()
    {

    }

    /**
     * 订单提交
     * <AUTHOR>
     * @data   2019-01-29
     *
     * @param  array  $orderParams
     * {
     *      'pid'              //产品ID
     *      'begintime'        //开始游玩时间
     *      'leavetime'        //酒店离店时间 - 2019-02-13:这个参数在终端这边下单暂时没有使用到
     *      'channel'          //销售渠道:0=pc,1=wx,2=接口,3=自助机,4=云票务,5=智能终端
     *      'tnum'             //购买数量
     *      'paymode'          //支付方式 0 = 余额支付,1 = 支付宝,2 = 授信支付,3 = 产品自销,4 = 现场支付,9 = 现金支付
     *      'member_id'        //下单用户ID - 散客默认112
     *      'aid'              //产品供应商ID
     *      'upper_supplier'   //散客购买时，更上一级的分销商
     *      'ordername'        //游玩用户姓名
     *      'ordertel'         //游玩用户手机号
     *      'op_id'            //操作用户
     *      'memo'             //下单备注
     *      'venue_id'         //演出场馆ID
     *      'round_id'         //演出场次ID
     *      'area_id'          //演出分区ID
     *      'seat_ids'         //演出选座座位
     *      'LinkSeatMark'     //演出连座标识
     *      'team_order'       //团单的订单号
     *      'cut_price'        //砍价后的单价
     *      'group_price'      //团购后的单价
     *      'jasmine_discount' //茉莉分积分折扣 - 9.5折
     *      'site_id'          //站点ID
     *      'memo'             //下单备注
     *      'idcard_arr'       //游玩人员身份证 - 有带具体的哪个票对于哪些身份证信息
     *      'link_pids'        //联票产品ID
     *      'is_sale'          //是否是散客下单
     *      'distributePrices'  // 下单提供的结算方式
     * }
     *
     * @return array
     */
    public function submit(array $orderParams)
    {
        //判断必须参数是否存在
        $leftMustParam = array_diff_key($this->_orderMustParams, $orderParams);

        if ($leftMustParam) {
            $leftKey = array_keys($leftMustParam)[0];
            $leftVal = $leftMustParam[$leftKey];
            $leftMsg = "缺少下单参数[{$leftKey}-{$leftVal}]";

            return [400, $leftMsg];
        }

        //下单频率控制
        $frequencyRes = $this->_controlFrequency($orderParams);
        if ($frequencyRes[0] != 200) {
            return [400, $frequencyRes[1]];
        }

        //下单参数基础检测
        //包括产品是否存在，产品类型，演出场次，是否能下联票判断
        $baseCheckRes = $this->_baseCheck($orderParams);
        if ($baseCheckRes[0] != 200) {
            return [400, $baseCheckRes[1]];
        }
        //这里下单逻辑特殊处理下，统一下单接口不需要填写手机号，需要传12301（票属性有可以无需填写联系人的属性）
        $orderParams['ordertel'] = empty($orderParams['ordertel']) ? "12301" : $orderParams['ordertel'];

        //门票的一些基础信息一起返回
        $ticketInfo = $baseCheckRes[2];
        $ptype      = $ticketInfo['p_type']; //产品类型
        $tid        = $ticketInfo['tid']; //产品可以延迟验证时间
        //$discountScale = $ticketInfo['discount_scale'] && $orderParams['paymode'] == 17 ? $ticketInfo['discount_scale'] : false; // 会员卡支付 折扣比例
        $discountScale = $ticketInfo['discount_scale']; // 会员卡支付 折扣比例

        //下单参数处理
        $member       = $orderParams['member_id'];
        $pid          = $orderParams['pid'];
        $aid          = $orderParams['aid'];
        $opId         = $orderParams['op_id'];
        $orderChannel = $orderParams['channel'];
        $playdate     = $orderParams['begintime'];
        $orderName    = $orderParams['ordername'];
        $orderTel     = $orderParams['ordertel'];
        $contactTel   = $orderParams['contacttel'];
        $tnum         = $orderParams['tnum'];

        $paymode         = $orderParams['paymode'];
        $memo            = $orderParams['memo'];
        $isSale          = $orderParams['is_sale'];
        $moreCredentials = $orderParams['moreCredential'];
        $personId        = $orderParams['personid'];
        $voucherType     = $orderParams['voucher_type'];

        $origin        = $orderParams['origin'] ?? ''; //集合地点
        $terminal      = $orderParams['terminal'] ?? 0; //集合地点
        $siteId        = $orderParams['site_id'] ?? 0; //集合地点
        $clientIp      = $orderParams['client_ip'] ?? ''; //客户端IP
        $assembly      = $orderParams['assembly'] ?? 0; //集合地点
        $leavetime     = $orderParams['leavetime'] ?? ''; //散客购买时，更上一级的分销商
        $upperSupplier = $orderParams['upper_supplier'] ?? ''; //散客购买时，更上一级的分销商
        $teamOrder     = $orderParams['team_order'] ?? ''; //团单附加订单
        $mail          = $orderParams['mail'] ?? ''; //是否需要发现邮件通知
        $linkPidArr    = $orderParams['link_pids'] ?? [];
        $idcardArr     = $orderParams['idcard_arr'] ?? [];

        //是否发短信
        $isSendSms = isset($orderParams['is_send_sms']) ? boolval($orderParams['is_send_sms']) : true;

        //自定义流水号
        $serialNumber = $orderParams['serial_number'] ?? '';

        //价格优惠方案
        $cutPrice         = isset($orderParams['cut_price']) ? intval($orderParams['cut_price']) : false;
        $groupPrice       = isset($orderParams['group_price']) ? intval($orderParams['group_price']) : false;
        $seckillPrice     = isset($orderParams['seckill_price']) ? intval($orderParams['seckill_price']) : false;
        $jasmineDiscount  = isset($orderParams['jasmine_discount']) ? floatval($orderParams['jasmine_discount'] / 10) : false;
        $linkSeatMark     = $orderParams['link_seat_mark'] ?? '';
        $couponInfo       = isset($orderParams['useCouponValue']) ? floatval($orderParams['useCouponValue']) : false;
        $teamDiscountInfo = $orderParams['team_discount_info'] ?? [];

        //远端订单号处理
        $remotenum = $orderParams['remotenum'] ?? '';

        //积分兑换商品比例
        $pointRate = $orderParams['point_rate'] ?? 0;

        //套票子票下单，临时处理下
        $packageParentOrdernum = $orderParams['package_parent_ordernum'] ?? '';

        //分时预约时间段id['time_id' => 1, 'time_str' => '11:00-12:00']
        $timeShareInfo = $orderParams['time_share_info'] ?? [];
        //套票子票分时预约时间段id(['子票tid' => ['time_id' => 1, 'time_str' => '11:00-12:00'], ...]
        $packageTimeShareInfo = $orderParams['package_time_share_info'] ?? [];
        //套票子票是演出票时字段
        $packageShowIinfo = $orderParams['package_show_info'] ?? [];

        // 下单提供的结算方式
        $distributePrices = $orderParams['distributePrices'] ?? [];
        $orderExtensionParams['book_advance']   = $orderParams['book_advance'] ?? 2; //是否跳过购票需要提前预定前置校验 1：跳过 2：不跳过(仅团单)
        $orderExtensionParams['discountDetail'] = $teamDiscountInfo['discountDetail'] ?? [];
        //演出数据处理
        $showInfoArr = [];
        if (in_array($ptype, ['H', 'F'])) {
            $showInfoArr = [
                'venue_id'       => $orderParams['venue_id'],
                'round_id'       => $orderParams['round_id'],
                'area_id'        => $orderParams['area_id'],
                'seat_ids'       => $orderParams['seat_ids'],
                'link_seat_mark' => $linkSeatMark,
            ];

            //判断演出套票是否是主票生成连座标识
            //演出子票还是演出的话对连坐标识进行修改 --- 根据对应的主票选择的演出场次进行子票的showInfoArr组合
            if (empty($linkSeatMark)) {
                $linkShowMarkSonTidArr = [];
                $sonTicketShowInfo     = $this->_createPackageShowTicketSeries($tid, $showInfoArr, $playdate,
                    $packageShowIinfo);
                if (!empty($sonTicketShowInfo)) {
                    // 子票超出演出开始时间
                    if (isset($sonTicketShowInfo['code']) && $sonTicketShowInfo['code'] === false) {
                        return [400, $sonTicketShowInfo['msg']];
                    }
                    foreach ($sonTicketShowInfo as $sonItem) {
                        // 选择下单的连坐标识
                        $linkShowMarkSonTidArr[$sonItem['tid']]['showInfoArr'] = $sonItem;
                        $linkShowMarkSonTidArr[$sonItem['tid']]['tnum']        = $sonItem['bind_num'] * $tnum;
                        $linkShowMarkSonTidArr[$sonItem['tid']]['mainTid']     = $sonItem['main_tid'];
                        $linkShowMarkSonTidArr[$sonItem['tid']]['tid']         = $sonItem['tid'];
                    }

                    // 对子票进行标识占位
                    $sonTicketShowInfoArr = [];
                    if (!empty($linkShowMarkSonTidArr)) {
                        //如果有相同分区，添加上连座的标识
                        $mainTidShowInfoArr                      = [];
                        $mainTidShowInfoArr[$tid]['showInfoArr'] = $showInfoArr;
                        $needLinkShowArr                         = array_merge($mainTidShowInfoArr,
                            $linkShowMarkSonTidArr);
                        $orderInfoArr                            = $this->_fillLinkShowMark($needLinkShowArr,
                            $orderTel);
                        foreach ($orderInfoArr as $sonTicketKey => $sonTicketShowItem) {
                            if (isset($sonTicketShowItem['mainTid'])) {
                                $sonTicketShowInfoArr[$sonTicketShowItem['mainTid']][$sonTicketShowItem['tid']] = $sonTicketShowItem;
                                // 清楚子票下单数组
                                unset($orderInfoArr[$sonTicketKey]);
                            } else {
                                // 重写单票主票连座标识
                                $showInfoArr['link_seat_mark'] = $sonTicketShowItem['showInfoArr']['link_seat_mark'];
                            }
                        }
                    }

                    // 演出子票座位信息
                    if (!empty($sonTicketShowInfoArr[$tid])) {
                        $showInfoArr['childTicketShowInfoArr'] = $sonTicketShowInfoArr[$tid];
                    }
                }
            }
        }

        //处理优惠信息
        $saleSetting = $this->_handleSaleInfo($isSale, $upperSupplier, $discountScale, $jasmineDiscount, $cutPrice,
            $groupPrice, $seckillPrice, $couponInfo, $teamDiscountInfo);

        //是否需要直接进行支付，如果是平台支付的话，会直接进行支付
        $isNeedInsidePay = true;

        //特产：0快递1自提
        if ($ptype == 'J') {
            $deliveryWay = intval($orderParams['delivery_way'] ?? 0);
        } else {
            $deliveryWay = -1;
        }
        //联系人(收货人)地址信息
        $contactArea = $orderParams['contact_area'] ?? [];
        //只有针对特殊渠道(微商城/小程序)生效
        $useWindowPrice = $orderParams['useWindowPrice'] ?? true; //下单价格方式：1窗口价 2零售价

        $contactArr = [
            'ordername'     => $orderName,
            'ordertel'      => $orderTel,
            'contacttel'    => $contactTel,
            'is_sms'        => $isSendSms,
            'personid'      => $personId, // 身份信息
            'voucher_type'  => $voucherType, //身份信息类型
            //省份编码
            'province_code' => intval($contactArea['province_code'] ?? 0),
            //城市编码
            'city_code'     => intval($contactArea['city_code'] ?? 0),
            //城镇编码
            'town_code'     => $contactArea['town_code'] ?? 0,
            //详细地址
            'address'       => $contactArea['address'] ?? 0,
            //取票人填写更多证件
            'moreList'      => $moreCredentials['contacts'] ?? [],
        ];

        if (!empty($orderParams['personid'])) {
            $contactArr['personid']     = $orderParams['personid'];
            $contactArr['voucher_type'] = $orderParams['voucher_type'];
        }

        //特产订单快递配送，计算运费
        if ($ptype == 'J' && $deliveryWay == 0) {
            //计算运费
            $tidNumMap       = [];
            $tidNumMap[$tid] = $tnum;
            $carriageApi     = new \Business\JavaApi\LogisticsCenter\Carriage();
            $fidArr          = [];
            $aidArr          = [];
            //订单价格计算方式 0=结算价 1=窗口价 2=零售价 默认结算价
            $tidFidPriceTypeMap = [];
            if ($isSale) {//游客下单处理
                $fidArr[$tid] = $aid;
                $aidArr[$tid] = $upperSupplier;
                $tidFidPriceTypeMap[$tid] = 2;
                //如果订单渠道为 小程序 且配置了强制使用窗口价 那么取窗口价
                if (in_array($orderChannel, [43, 56]) && $useWindowPrice) {
                    $tidFidPriceTypeMap[$tid] = 1;
                }
                if (!$aid || !$upperSupplier) {
                    return [400, "缺少运费计算参数"];
                }
            } else {
                $fidArr[$tid] = $member;
                $aidArr[$tid] = $aid;
            }
            $carriageRes = $carriageApi->countBatchTicket($tidNumMap, $contactArr['province_code'],
                $contactArr['city_code'], $fidArr, $aidArr, $tidFidPriceTypeMap); //订单提交
            if ($carriageRes['code'] != 200) {
                return [400, '运费获取失败'];
            }
            $carriage = $carriageRes['data'][$tid];
        }

        $remarkArr = [
            'memo'                    => $memo,
            'origin'                  => $origin,
            'assembly'                => $assembly,
            'serial_number'           => $serialNumber, //自定义线上流水号
            'time_share_info'         => $timeShareInfo,
            'package_time_share_info' => $packageTimeShareInfo,
            'package_show_info'       => $packageShowIinfo,
        ];

        $saleSiteArr = [
            'site_id'   => $siteId,
            'terminal'  => $terminal,
            'stuff_id'  => $opId,
            'client_ip' => $clientIp,
        ];

        //特殊信息数组
        $specialSettingArr['delivery_way'] = $deliveryWay;

        if ($pointRate) {
            $specialSettingArr['point_rate'] = $pointRate;
        }

        if ($ptype == 'C') {
            $specialSettingArr['leavetime'] = $leavetime;
        }

        if ($ptype == 'J') {
            $specialSettingArr['carriage'] = $carriage ?? 0;
        }

        if (!empty($distributePrices)) {
            $specialSettingArr['distributePrices'] = $distributePrices;
        }

        //只有针对特殊渠道(微商城/小程序)生效
        $specialSettingArr['useWindowPrice'] = $orderParams['useWindowPrice'] ?? true; //下单价格方式：1窗口价 2零售价

        //处理酒店多张票的数据
        //这种情况下下单方式和普通的联票是一样的方式
        $linkProductArr = [];
        if ($ptype == 'C') {
            $linkProductArr = $this->_handleHotlLinkParam($member, $aid, $upperSupplier, $tid, $tnum, $playdate,
                $leavetime, $orderChannel, $paymode, $isNeedInsidePay, $isSale, $contactArr, $idcardArr, $saleSiteArr,
                $remarkArr, $specialSettingArr);
        } else {
            if ($linkPidArr) {
                $linkProductArr = $this->_handleLinkParam($linkPidArr, $member, $aid, $upperSupplier, $playdate,
                    $orderChannel, $paymode, $isNeedInsidePay, $isSale, $contactArr, $idcardArr, $saleSiteArr,
                    $remarkArr, $specialSettingArr);
            }
        }

        //是否联票订单
        $isLinkOrder = $linkProductArr ? true : false;

        if ($isLinkOrder) {
            $linkOrderInfoArr = ['link_type' => 'link'];
        } else {
            $linkOrderInfoArr = ['link_type' => 'common'];
        }

        if ($ptype == 'F') {
            $linkOrderInfoArr = [
                'link_type' => 'package',
            ];
        }

        if ($packageParentOrdernum) {
            $linkOrderInfoArr = ['link_type' => 'package_son', 'parent_ordernum' => $packageParentOrdernum];
        }

        //身份证信息
        $mainIdcardArr = isset($idcardArr[$tid]) ? $idcardArr[$tid] : (is_array($idcardArr) ? $idcardArr : []);

        //下单类型
        $orderType = 'ShowOrder.class';
        //统一下单
        $orderRes = Helpers::platformOrder($member, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode,
            $isNeedInsidePay, $saleSetting, $contactArr, $remotenum, $mainIdcardArr, $linkOrderInfoArr,
            $saleSiteArr, $remarkArr, $showInfoArr, $specialSettingArr, $orderExtensionParams);

        if ($orderRes['code'] == 200) {
            //下单成功
            $orderData = $orderRes['data'];
            $ordernum  = $orderData['ordernum'];

            //进行联票子票的下单逻辑
            $subOrderArr = [];
            if ($isLinkOrder) {
                $orderType   = 'linkOrder';
                $subOrderArr = $this->_subOrder($ordernum, $linkProductArr);
            }

            //接下去处理剩余的一些数据写入
            $this->_followOrderTask($orderData, $member, $paymode, $subOrderArr, $mail, $teamOrder, [], $orderParams);

            return [200, '', ['orderNum' => $ordernum, 'orderType' => $orderType, 'originRes' => $orderRes]];
        } else {
            //下单失败
            $errCode = $orderRes['data']['err_code'];
            $msg = $orderRes['msg'];
            $errMsg  = empty($orderRes['data']['err_msg']) ? $msg : $orderRes['data']['err_msg'];
            return [500, "下单接口出错{$errCode}, 错误描述：{$errMsg}"];
        }
    }

    /**
     * 合并付款下单
     * <AUTHOR>
     * @data   2019-03-28
     *
     * @param  array  $orderParams
     *    {
     *      'pid'              //产品ID
     *      'begintime'        //开始游玩时间
     *      'channel'          //销售渠道:0=pc,1=wx,2=接口,3=自助机,4=云票务,5=智能终端
     *      'paymode'          //支付方式 0 = 余额支付,1 = 支付宝,2 = 授信支付,3 = 产品自销,4 = 现场支付,9 = 现金支付
     *      'member_id'        //下单用户ID - 散客默认112
     *      'upper_supplier'   //散客购买时，更上一级的分销商
     *      'ordername'        //游玩用户姓名
     *      'ordertel'         //游玩用户手机号
     *      'op_id'            //操作用户
     *      'memo'             //下单备注
     *      'team_order'       //团单的订单号
     *      'terminal'         //终端号
     *      'site_id'          //站点ID
     *      'memo'             //下单备注
     *      'idcard_arr'       //游玩人员身份证 - 有带具体的哪个票对于哪些身份证信息
     *      'is_sale'          //是否是散客下单
     *      'saleChannel'      //非必须,销售二级渠道: 101.小红书
     *      'equityEncryptionCode'      //非必须,预售券权益加密串
     *      'firstSaleChannel' //一级分销渠道
     *      'secondSaleChannel' //二级分销渠道
     *   }
     *
     * @return [type]
     */
    public function combineSubmit(array $orderParams)
    {
        //判断必须参数是否存在
        $leftMustParam = array_diff_key($this->_combineMustParams, $orderParams);

        if ($leftMustParam) {
            $leftKey = array_keys($leftMustParam)[0];
            $leftVal = $leftMustParam[$leftKey];
            $leftMsg = "缺少下单参数[{$leftKey}-{$leftVal}]";

            return [400, $leftMsg];
        }

        //下单参数基础检测
        //包括产品是否存在，产品类型等数据
        $baseCheckRes = $this->_combineCheck($orderParams);
        if ($baseCheckRes[0] != 200) {
            return [400, $baseCheckRes[1]];
        }

        //门票数据
        $ticketInfoArr = $baseCheckRes[2];

        //下单参数处理
        $member          = $orderParams['member_id'];
        $opId            = $orderParams['op_id'];
        $orderChannel    = $orderParams['channel'];
        $playdate        = $orderParams['begintime'];
        $ordername       = $orderParams['ordername'] ?: '游客';
        $ordertel        = $orderParams['ordertel'] ?: '12301';
        $personId        = $orderParams['personid'];
        $voucherType     = $orderParams['voucher_type'];
        $contacttel      = $orderParams['contacttel'];
        $mobileArea      = $orderParams['mobile_area'] ?? '';
        $mobileRegion    = $orderParams['mobile_region'] ?? '';
        $paymode         = $orderParams['paymode'];
        $memo            = $orderParams['memo'];
        $isSale          = $orderParams['is_sale'];
        $terminal        = $orderParams['terminal'];
        $siteId          = $orderParams['site_id'];
        $clientIp        = $orderParams['client_ip'] ?? '';
        $moreCredentials = $orderParams['moreCredential'];

        $assembly           = isset($orderParams['assembly']) ? $orderParams['assembly'] : 0; //集合地点
        $teamOrder          = $orderParams['team_order'] ?? ''; //团单附加订单
        $mail               = isset($orderParams['mail']) ? $orderParams['mail'] : ''; //是否需要发现邮件通知
        $idcardArr          = isset($orderParams['idcard_arr']) ? $orderParams['idcard_arr'] : [];
        $combineProductList = isset($orderParams['combine_pids']) ? $orderParams['combine_pids'] : [];

        $usePoint    = isset($orderParams['use_point']) ? $orderParams['use_point'] : false;        //是否使用积分
        $useCoupon   = isset($orderParams['use_coupon']) ? $orderParams['use_coupon'] : false;      //是否使用优惠券
        $useDiscount = isset($orderParams['use_discount']) ? $orderParams['use_discount'] : false;  //是否使用会员折扣
        $useMarketingDiscount = $orderParams['useMarketingDiscount'] ?? false; //营销折扣

        //价格优惠方案
        $activityArr                    = [];
        $activityArr['cutPrice']        = isset($orderParams['cut_price']) ? intval($orderParams['cut_price']) : false;
        $activityArr['groupPrice']      = isset($orderParams['group_price']) ? intval($orderParams['group_price']) : false;
        $activityArr['seckillPrice']    = isset($orderParams['seckill_price']) ? intval($orderParams['seckill_price']) : false;
        $activityArr['jasmineDiscount'] = isset($orderParams['jasmine_discount']) ? floatval($orderParams['jasmine_discount'] / 10) : false;
        $oneTicket = current($ticketInfoArr);

        //特产：0快递1自提
        if ($oneTicket['p_type'] == 'J') {
            $deliveryWay = intval($orderParams['delivery_way'] ?? 0);
        } else {
            $deliveryWay = -1;
        }
        //联系人(收货人)地址信息
        $contactArea = $orderParams['contact_area'] ?? [];

        $contactArr = [
            'ordername'     => $ordername,
            'ordertel'      => $ordertel,
            'personid'      => $personId, // 身份信息
            'voucher_type'  => $voucherType, //身份信息类型
            'contacttel'    => $contacttel,
            'mobile_area'   => $mobileArea,
            'mobile_region' => $mobileRegion,
            'is_sms'        => true,
            //省份编码
            'province_code' => intval($contactArea['province_code'] ?? 0),
            //城市编码
            'city_code'     => intval($contactArea['city_code'] ?? 0),
            //城镇编码
            'town_code'     => intval($contactArea['town_code'] ?? 0),
            //详细地址
            'address'       => $contactArea['address'] ?? '',
            //取票人填写更多证件
            'moreList'      => $moreCredentials['contacts'] ?? [],
        ];

        //默认内部支付自动付款
        $isNeedInsidePay = true;

        $linkOrderInfoArr = [
            'link_type' => 'common',
        ];

        $saleSiteArr = [
            'site_id'   => $siteId,
            'terminal'  => $terminal,
            'stuff_id'  => $opId,
            'client_ip' => $clientIp,
        ];

        $remarkArr = [
            'memo'     => $memo,
            'assembly' => $assembly,
        ];

        //特性信息数组
        $specialSettingArr                   = [];
        $specialSettingArr['delivery_way']   = $deliveryWay;
        $specialSettingArr['shop']           = $orderParams['shop'] ?? 0;
        $oneTicket                           = current($ticketInfoArr);
        $specialSettingArr['useWindowPrice'] = $orderParams['useWindowPrice'] ?? true; //下单价格方式：1窗口价 2零售价

        if ($oneTicket['p_type'] == 'C') {
            //特性信息数组
            $specialSettingArr['leavetime'] = $orderParams['leavetime'];
        }

        //特产订单快递配送，计算运费
        if ($oneTicket['p_type'] == 'J' && $deliveryWay == 0) {
            //计算运费
            $tidNumMap = [];
            $tidAidMap = [];
            $tidFidMap = [];
            //订单价格计算方式 0=结算价 1=窗口价 2=零售价 默认结算价
            $tidFidPriceTypeMap = [];
            foreach ($combineProductList as $item) {
                $tidNumMap[$ticketInfoArr[$item['pid']]['tid']] = $item['tnum'];
                if ($isSale) {
                    $tidAidMap[$ticketInfoArr[$item['pid']]['tid']] = $item['upper_supplier'];
                    $tidFidMap[$ticketInfoArr[$item['pid']]['tid']] = $item['aid'];
                    $tidFidPriceTypeMap[$ticketInfoArr[$item['pid']]['tid']] = 2;
                    //如果订单渠道为 小程序 且配置了强制使用窗口价 那么取窗口价
                    if (in_array($orderChannel, [43, 56]) && $specialSettingArr['useWindowPrice']) {
                        $tidFidPriceTypeMap[$ticketInfoArr[$item['pid']]['tid']] = 1;
                    }
                } else {
                    $tidAidMap[$ticketInfoArr[$item['pid']]['tid']] = $item['aid'];
                    $tidFidMap[$ticketInfoArr[$item['pid']]['tid']] = $member;
                }
            }
            $carriageApi = new \Business\JavaApi\LogisticsCenter\Carriage();
            $carriageRes = $carriageApi->countBatchTicket($tidNumMap, $contactArr['province_code'],
                $contactArr['city_code'], $tidFidMap, $tidAidMap, $tidFidPriceTypeMap); //合并付款下单
            if ($carriageRes['code'] != 200) {
                return [400, json_encode([$tidFidMap, $tidAidMap])];
            }
            $carriageMap = $carriageRes['data'];
            foreach ($combineProductList as &$item) {
                $item['carriage'] = $carriageMap[$ticketInfoArr[$item['pid']]['tid']];
            }
        }

        //分销专员推广参数,联票子票下单时记录
        $specialSettingArr['dis_id'] = isset($orderParams['dis_id']) ? $orderParams['dis_id'] : ''; //分销专员推广
        //处理下单的数据
        $orderInfoArr = $this->_handleCombineList($combineProductList, $member, $playdate, $paymode, $orderChannel,
            $ticketInfoArr, $idcardArr, $isSale, $isNeedInsidePay, $contactArr,
            $saleSiteArr, $remarkArr, $specialSettingArr, $activityArr);
        //合并下单，前置校验优惠信息有效性
        //本来应该下沉的通用下单接口common_order中检验，但是有传的参，不知道优惠券分布，所以只能前置并且透下去
        $discountDetail = [];
        if ($usePoint || $useCoupon || $useDiscount || $useMarketingDiscount) {
            $ticketList = [];
            //构建校验数据
            foreach ($orderInfoArr as $value){
                $ticketList[] = [
                    'play_date'                    => $playdate,
                    'pid'                          => $value['pid'],
                    'tid'                          => $value['tid'],
                    'aid'                          => $value['aid'],
                    'tnum'                         => $value['tnum'],
                    'upper_supplier'               => $value['saleSetting']['upper_supplier'],
                    'points'                       => $value['saleSetting']['points'],
                    'pointsAmount'                 => $value['saleSetting']['pointsAmount'],
                    'couponAmount'                 => $value['saleSetting']['couponAmount'],
                    'use_point'                    => $value['saleSetting']['use_point'],
                    'use_coupon'                   => $value['saleSetting']['use_coupon'],
                    'use_discount'                 => $value['saleSetting']['use_discount'],
                    'ticket_price'                 => $value['saleSetting']['ticket_price'],
                    'ticket_member_discount_price' => $value['saleSetting']['ticket_member_discount_price'],
                    'useMarketingDiscount'         => $value['saleSetting']['useMarketingDiscount'],
                    'discountInfo'                 => $value['saleSetting']['discountInfo'],
                ];
            }
            $pointsApi = new \Business\JsonRpcApi\MarketCenterService\pointsApi();
            $checkRes  = $pointsApi->integralOrderPreCheck($member, $opId, $isSale, $orderChannel, $paymode,
                $orderParams['use_point'], $orderParams['use_coupon'], $orderParams['use_discount'], $ticketList,
                $orderParams['coupon_list'], $useMarketingDiscount);
            if ($checkRes['code'] != 200) {
                return [$checkRes['code'], $checkRes['msg']];
            }
            else{
                $discountList = array_column($checkRes['data']['ticketList'],'discountDetail','ticketId');
                $checkTicketList = array_column($checkRes['data']['ticketList'],null,'ticketId');

                foreach ($orderInfoArr as $key => $value){
                    if(array_key_exists($value['tid'],$discountList)){
                        $orderInfoArr[$key]['discountDetail'] = $discountList[$value['tid']];
                    }
                    foreach ($value['saleSetting'] as $keyField => $valueField){
                        if(in_array($keyField,DiscountType::DISCOUNT_ORDER_MAP) && !array_key_exists($keyField,$checkTicketList[$value['tid']])){
                            unset($orderInfoArr[$key]['saleSetting'][$keyField]);
                        }
                        if(isset($checkTicketList[$value['tid']]['useMarketingDiscount'])){
                            $orderInfoArr[$key]['saleSetting']['useMarketingDiscount']= $checkTicketList[$value['tid']]['useMarketingDiscount'];
                        }
                    }
                }
            }
        }


        //演出子票还是演出的话对连坐标识进行修改 --- 根据对应的主票选择的演出场次进行子票的showInfoArr组合
        $linkShowMarkSonTidArr = [];
        foreach ($orderInfoArr as $orderFirst) {
            $sonTicketShowInfo = $this->_createPackageShowTicketSeries($orderFirst['tid'], $orderFirst['showInfoArr'],
                $orderFirst['playdate'], $orderFirst['remarkArr']['package_show_info']);
            if (!empty($sonTicketShowInfo)) {
                // 子票超出演出开始时间
                if (isset($sonTicketShowInfo['code']) && $sonTicketShowInfo['code'] === false) {
                    return [400, $sonTicketShowInfo['msg']];
                }
                foreach ($sonTicketShowInfo as $sonItem) {
                    // 选择下单的连坐标识
                    $linkShowMarkSonTidArr[] = [
                        'showInfoArr' => $sonItem,
                        'tnum'        => $sonItem['bind_num'] * $orderFirst['tnum'],
                        'mainTid'     => $sonItem['main_tid'],
                        'tid'         => $sonItem['tid'],
                    ];
                }
            }
        }

        // 对子票进行标识占位
        $sonTicketShowInfoArr = [];
        if (!empty($linkShowMarkSonTidArr)) {
            //如果有相同分区，添加上连座的标识
            $needLinkShowArr = array_merge($orderInfoArr, $linkShowMarkSonTidArr);
            $orderInfoArr    = $this->_fillLinkShowMark($needLinkShowArr, $ordertel);
            foreach ($orderInfoArr as $sonTicketKey => $sonTicketShowItem) {
                if (isset($sonTicketShowItem['mainTid'])) {
                    $sonTicketShowInfoArr[$sonTicketShowItem['mainTid']][$sonTicketShowItem['tid']] = $sonTicketShowItem;
                    // 清楚子票下单数组
                    unset($orderInfoArr[$sonTicketKey]);
                }
            }
        } else {
            //如果有相同分区，添加上连座的标识
            $orderInfoArr = $this->_fillLinkShowMark($orderInfoArr, $ordertel);
        }

        //逐步下单
        $orderDataArr  = [];
        $firstOrderNum = ''; //标记合并付款第一笔订单号
        $orderIndex    = 0; //标记是否是第一单
        $mergeOrderBiz = new MergeOrder();
        //生成合并付款ID
        $tradeId = count($orderInfoArr) > 1 ? $mergeOrderBiz->getMergeOrderNum() : null;
        foreach ($orderInfoArr as $key => $item) {
            $item['remarkArr']['first_ordernum'] = $firstOrderNum;
            //下单参数
            $member            = $item['member'];
            $aid               = $item['aid'];
            $tid               = $item['tid'];
            $pid               = $item['pid'];
            $tnum              = $item['tnum'];
            $remotenum         = $item['remotenum'] ?? '';
            $playdate          = $item['playdate'];
            $orderChannel      = $item['orderChannel'];
            $paymode           = $item['paymode'];
            $isNeedInsidePay   = $item['isNeedInsidePay'];
            $subSaleSetting    = $item['saleSetting'];
            $contactArr        = $item['contactArr'];
            $idCardList        = $item['idCardList'];
            $saleSiteArr       = $item['saleSiteArr'];
            $remarkArr         = $item['remarkArr'];
            $showInfoArr       = $item['showInfoArr'];
            $ptype             = $ticketInfoArr[$pid]['p_type'];
            $specialSettingArr = $item['specialSettingArr'];
            $orderExtensionParams['op_id'] = $opId ;
            $orderExtensionParams['discountDetail'] = $item['discountDetail'] ?? [];
            if (isset($orderParams['saleChannel'])) { //  //销售二级渠道: 101.小红书
                $orderExtensionParams['saleChannel'] = $orderParams['saleChannel'];
            }
            if (isset($orderParams['firstSaleChannel'])) { //一级销售渠道
                $orderExtensionParams['firstSaleChannel'] = $orderParams['firstSaleChannel'];
            }
            if (isset($orderParams['secondSaleChannel'])) { //二级销售渠道
                $orderExtensionParams['secondSaleChannel'] = $orderParams['secondSaleChannel'];
            }
            if (!empty($sonTicketShowInfoArr[$item['tid']])) {
                $showInfoArr['childTicketShowInfoArr'] = $sonTicketShowInfoArr[$item['tid']];
            }
            //预售券权益加密串透传
            if (isset($orderParams['equityEncryptionCode'])) {
                $orderExtensionParams['equityEncryptionCode'] = $orderParams['equityEncryptionCode'];
            }
            //营销活动标识
            if (!empty($orderParams['mkActivityCode'])) {
                $orderExtensionParams['mkActivityCode'] = $orderParams['mkActivityCode'];
            }
            //增加交易订单号【购物车下单必传】
            $orderExtensionParams['tradeOrderId'] = $orderParams['tradeOrderId'] ?? $tradeId;
            $orderExtensionParams['useMarketingDiscount'] = $item['useMarketingDiscount'] ?? false;
            //都是普通票
            $linkOrderInfoArr = [
                'link_type' => 'common',
            ];

            if ($ptype == 'F') {
                $linkOrderInfoArr = [
                    'link_type' => 'package',
                ];
            }

            //演出捆绑,也当做套票处理
            if ($ptype == 'H') {
                $packBiz   = new \Business\Product\PackTicket();
                $sunTicket = $packBiz->getTickets($tid);

                if ($sunTicket) {
                    $linkOrderInfoArr = [
                        'link_type' => 'package',
                    ];
                }
            }

            //酒店如果多天要下联票，既合付款+联票的模式
            $linkOrder = false;
            if ($ptype == 'C' && (strtotime($item['specialSettingArr']['leavetime']) - strtotime($item['playdate'])) > 24 * 3600) {
                $subOrderRes = $this->_hotelLinkSubmit($item);
                $linkOrder   = true;
            } else {
                $subOrderRes = Helpers::platformOrder($member, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode,
                    $isNeedInsidePay,
                    $subSaleSetting, $contactArr, $remotenum, $idCardList, $linkOrderInfoArr, $saleSiteArr,
                    $remarkArr, $showInfoArr, $specialSettingArr,$orderExtensionParams);
            }

            if ($subOrderRes['code'] != 200) {
                //如果下单失败，将之前的订单取消
                $errMsg = $subOrderRes['data']['err_msg'];
                $AbnormalMsg = "票{$tid}_合并付款后取消产生的异常:{$errMsg}";
                $cancelRemark = '';
                if (in_array($subOrderRes['data']['err_code'], [228, 229])) {
                    $cancelRemark = $AbnormalMsg;
                }
                $cancelOrders = $this->_cancelSuccessOrder($member, $orderDataArr ,$AbnormalMsg, $cancelRemark);
                $resultData   = [
                    'errorPid'    => ['mainProduct' => $pid],
                    "cancelOrder" => $cancelOrders,
                ];
                $errMsg = $subOrderRes['data']['err_msg'];
                return [402, $errMsg, $resultData];
            } else {
                //循环时获取第一笔
                if (empty($firstOrderNum) && $orderIndex == 0) {
                    $firstOrderNum = isset($subOrderRes['data']['ordernum']) ? $subOrderRes['data']['ordernum'] : '';
                }
                $subOrderData                  = $subOrderRes['data'];
                $subOrderData['is_link_order'] = $linkOrder;
                // 增加收货人ID
                $subOrderData['consignee'] = $orderParams['consignee'];
                $orderDataArr[]            = $subOrderData;
                $orderIndex++;
            }
        }

        //接下去处理剩余的一些数据写入
        foreach ($orderDataArr as $subOrderInfo) {
            $this->_followOrderTask($subOrderInfo, $member, $paymode, $subOrderArr = [], $mail, $teamOrder, [],
                $orderParams);
        }

        if (count($orderInfoArr) == 1) {
            //只有一张票的情况下，不进行合并处理
            $tradeId = $orderDataArr[0]['ordernum'];
        } else {
            //将订单信息写入合并付款表
            $mergeRes      = $mergeOrderBiz->submitOrders($orderDataArr, $tradeId);

            //数据写入失败
            if (!$mergeRes) {
                $AbnormalMsg = "票id_{$tid}_合并付款后取消产生的异常";
                $cancelOrders = $this->_cancelSuccessOrder($member, $orderDataArr,$AbnormalMsg);
                $resultData   = [
                    'errorPid'    => ['mainProduct' => $pid],
                    "cancelOrder" => $cancelOrders,
                ];

                return [402, '合并下单出错', $resultData];
            } else {
                $tradeId = $mergeRes['tradeid'];
            }
        }

        //用来确认是不是第三方产品，调用Java接口, sourcet= [2,3]就是第三方
        $ticketIds = array_column($orderInfoArr, 'tid'); //票ids
        $ticketIds = implode(',', $ticketIds);
        $isThird   = (new \Business\JavaApi\Ticket\ThirdAttr())->isThirdTickets($ticketIds);

        //返回数据
        $submitRes = [
            'tradeId'  => $tradeId,
            'is_third' => $isThird,
            'list'     => $orderDataArr,
        ];

        return [200, '', $submitRes];
    }

    /**
     * <AUTHOR>
     * @date   2019-05-20
     *
     * @param  int  $tid  门票id
     * @param  string  $series  门票座位信息
     *
     * @return array
     */
    private function _createPackageShowTicketSeries($manTid, $showInfoArr, $playdate, $packageShowInfo = [])
    {
        if (empty($manTid) || (empty($showInfoArr) && empty($packageShowInfo))) {
            return [];
        }

        // 获取主票下子票信息
        $ticketModel  = new \Model\Product\Ticket();
        $packBiz      = new \Business\Product\PackTicket();
        $sonTicketArr = $packBiz->getSonTickets($manTid);

        // 获取演出类型子票
        if (!empty($sonTicketArr)) {
            $showTicketArr = [];
            foreach ($sonTicketArr as $item) {
                if ($item['p_type'] == 'H') {
                    $showTicketArr[$item['tid']] = [
                        'lid'      => $item['lid'],
                        'venus_id' => $item['venus_id'],
                        'bind_num' => $item['num'],
                    ];
                }
            }

            // 存在演出子票的时候,子票根据对应的场馆,场次生成对应的下单信息
            if (!empty($showTicketArr)) {
                $showModel  = new \Model\Product\Show();
                $roundIdMap = [];
                if (!$packageShowInfo) {
                    // 通过主id 的演出信息获取当前排序
                    $roundInfoArr = $showModel->getRoundInfoById($showInfoArr['round_id']);
                } else {
                    foreach ($packageShowInfo as $tid => $item) {
                        $roundInfoArr     = $showModel->getRoundInfoById($item['roundid']);
                        $roundIdMap[$tid] = $roundInfoArr['round_sort_id'];
                    }
                }

                if (empty($roundInfoArr)) {
                    return [];
                }

                $mainRoundSortId = $roundInfoArr['round_sort_id'];

                $javaApi             = new \Business\CommodityCenter\LandF();
                $sonticketExtInfoArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId(array_keys($showTicketArr),
                    [], [], 'tid, zone_id, p_type', true);

                $showBiz = new \Business\Product\Show();

                // 通过子票id获取子票的的产品设置的场馆
                $sonTicketSeriesArr = [];

                foreach ($showTicketArr as $sonTicket => $sonTicketInfo) {
                    if ($sonticketExtInfoArr[$sonTicket]['p_type'] != 'H') {
                        continue;
                    }

                    $tmpRoundSortId = isset($roundIdMap[$sonTicket]) ? $roundIdMap[$sonTicket] : $mainRoundSortId;

                    unset($roundInfoArr);

                    $roundInfoArr = $showBiz->getRoundInfoBySortId($sonTicketInfo['venus_id'], $playdate,
                        $tmpRoundSortId);

                    if (empty($roundInfoArr)) {
                        return ['code' => false, 'msg' => '子票无演出场次'];
                    }

                    $roundId       = 0;
                    $showBeginTime = '';
                    if ($roundInfoArr) {
                        $roundId       = $roundInfoArr['id'];
                        $showBeginTime = $roundInfoArr['use_date'] . ' ' . $roundInfoArr['bt'];
                        $showEndTime   = $roundInfoArr['use_date'] . ' ' . $roundInfoArr['et'];

                        if (time() > strtotime($showEndTime)) {
                            return ['code' => false, 'msg' => '子票超出演出结束时间'];
                        }
                    }

                    $zoneId               = $sonticketExtInfoArr[$sonTicket]['zone_id']; // 分区id
                    $sonTicketSeriesArr[] = [
                        'tid'             => $sonTicket,
                        'venue_id'        => $sonTicketInfo['venus_id'],
                        'round_id'        => $roundId,
                        'area_id'         => $zoneId,
                        'show_begin_time' => $showBeginTime,
                        'bind_num'        => $sonTicketInfo['bind_num'],
                        'main_tid'        => $manTid,
                    ];
                }

                return $sonTicketSeriesArr;
            }

            return [];
        }

        return [];
    }

    /**
     * 酒店下联票
     * <AUTHOR>
     * @date   2019-04-15
     *
     * @param  array  $orderParams  下单参数
     *
     * @return array
     */
    private function _hotelLinkSubmit($orderParams)
    {
        $orderParams = OrderParams::transParamForHotel($orderParams);
        $result      = $this->submit($orderParams);
        if ($result[0] == 200) {
            return $result[2]['originRes'];
        } else {
            return $this->returnData(500, '', ['err_msg' => $result[1]]);
        }
    }

    /**
     * 下单失败就直接中断下单 取消前面完成的订单
     * <AUTHOR>
     * @date 2018-11-26
     */
    private function _cancelSuccessOrder($member, $orderDataArr,$AbnormalMsg= '', $cancelRemark = '')
    {
        // $mergeOrder  = new MergeOrder();
        $modifyBiz   = new Modify();
        $cancelOrder = [
            'success' => [],
            'fail'    => [],
        ];
        foreach ($orderDataArr as $item) {
            $ordernum = $item['ordernum'];
            $res      = $modifyBiz->cancelBaseFormat($ordernum, $member, $member, OrderConst::ORDER_FAIL_CANCEL, $cancelRemark, '',
                true, true, true);
            $this->recordAnAbnormal($ordernum,[$member,$item],$res,PackConst::ABNORMAL_PROCESS_CANCEL,
                PackConst::ABNORMAL_REASON_UPSTREAM,PackConst::ABNORMAL_LINKAGE_YES,$AbnormalMsg,$member);
            if ($res['code'] == 200) {
                $cancelOrder['success'][] = $ordernum;
            } else {
                $cancelOrder['fail'][] = $ordernum;
            }
        }

        return $cancelOrder;
    }

    /**
     * 处理合并付款的参数
     * <AUTHOR>
     * @data   2019-03-29
     *
     * @param  array  $combineProductList
     * @param  int  $member
     * @param  date  $playdate
     * @param  array  $paymode
     * @param  int  $orderChannel
     * @param  array  $ticketInfoArr
     * @param  array  $idCardList
     * @param  bool  $isSale
     * @param  bool  $isNeedInsidePay
     * @param  array  $contactArr
     * @param  array  $saleSiteArr
     * @param  array  $remarkArr
     * @param  array  $specialSettingArr
     * @param  array  $activityArr
     *
     * @return array
     */
    private function _handleCombineList($combineProductList, $member, $playdate, $paymode, $orderChannel, $ticketInfoArr, $idCardList, $isSale, $isNeedInsidePay, $contactArr, $saleSiteArr, $remarkArr, $specialSettingArr, $activityArr = [])
    {
        //下单参数
        $orderInfoArr = [];
        foreach ($combineProductList as $item) {
            $pid           = $item['pid'];
            $aid           = $item['aid'];
            $tnum          = $item['tnum'];
            $remotenum     = $item['remotenum'] ?? '';
            $upperSupplier = $item['upper_supplier'] ?: 0;
            $ticketInfo    = $ticketInfoArr[$pid];
            $tid           = $ticketInfo['tid'];
            $ptype         = $ticketInfo['p_type'];
            $payType       = $ticketInfo['pay'];
            //优惠详情
            //使用积分数
            $points = isset($item['points']) ? $item['points'] : 0;
            //积分优惠金额
            $pointsAmount = isset($item['pointsAmount']) ? $item['pointsAmount'] : 0;
            //优惠券实际优惠金额
            $couponAmount = isset($item['couponAmount']) ? $item['couponAmount'] : 0;
            //是否使用积分
            $usePoint = isset($item['use_point']) ? $item['use_point'] : false;
            //是否使用优惠券
            $useCoupon = isset($item['use_coupon']) ? $item['use_coupon'] : false;
            //是否使用会员折扣
            $useDiscount = isset($item['use_discount']) ? $item['use_discount'] : false;
            //营销活动
            $useMarketingDiscount = $item['useMarketingDiscount'] ?? false;
            //票原价
            $ticketPrice = isset($item['ticket_price']) ? $item['ticket_price'] : 0;
            //票会员折后价
            $ticketDiscountPrice = isset($item['ticket_member_discount_price']) ? $item['ticket_member_discount_price'] : 0;
            //分时预约时间段id
            $remarkArr['time_share_info'] = $item['time_share_info'] ?? [];
            //套票子票分时预约时间段id(['子票tid' => ['time_id' => 1, 'time_str' => '11:00-12:00'], ...]
            $remarkArr['package_time_share_info'] = $item['package_time_share_info'] ?? [];
            //套票子票是演出票时字段
            $remarkArr['package_show_info'] = $item['package_show_info'] ?? [];
            //新版营销中心的优惠信息
            $useMarketingDiscount = $item['useMarketingDiscount'] ?? false;
            $discountDetail = $item['discount_detail'] ?? [];

            //销售分销链，价格，优惠之类的数据
            $subSaleSetting = [
                'is_sale'                      => $isSale,
                'upper_supplier'               => $upperSupplier,
                'points'                       => $points,
                'pointsAmount'                 => $pointsAmount,
                'couponAmount'                 => $couponAmount,
                'use_point'                    => $usePoint,
                'use_coupon'                   => $useCoupon,
                'use_discount'                 => $useDiscount,
                'ticket_price'                 => $ticketPrice,
                'ticket_member_discount_price' => $ticketDiscountPrice,
                'discount_list'                => [],
                'member_discount_code'         => $item['member_discount_code'] ? $item['member_discount_code'] : 0,
                'useMarketingDiscount'         => $useMarketingDiscount,
                'discountInfo'                 => $item['discountInfo'] ?? [],
            ];

            //如果是用会员卡支付购票且渠道来自终端
            if ($paymode == 17) {
                //会员卡支付 折扣比例
                $subDiscountScale = $ticketInfo['discount_scale'];
                if ($subDiscountScale > 0 && $subDiscountScale < 100) {
                    $subSaleSetting['discount_list'][] = [
                        'type'      => 'member_card',
                        'name'      => '会员卡支付折扣',
                        'calc_type' => 2, //优惠折扣
                        'calc_num'  => $subDiscountScale / 100, //折扣百分比
                    ];
                }
            }

            // 营销活动处理
            if (!empty($activityArr)) {
                if (isset($activityArr['cutPrice']) && $activityArr['cutPrice'] !== false) {
                //if (!empty($activityArr['cutPrice'])) {
                    // 砍价
                    $subSaleSetting['discount_list'][] = [
                        'type'      => 'cut_discount',
                        'name'      => '砍价单价优惠',
                        'calc_type' => 'tprice',
                        'calc_num'  => $activityArr['cutPrice'],
                    ];
                } elseif (isset($activityArr['groupPrice']) && $activityArr['groupPrice'] !== false) {
                    // 拼团
                    $subSaleSetting['discount_list'][] = [
                        'type'      => 'group_discount',
                        'name'      => '拼团单价优惠',
                        'calc_type' => 'tprice',
                        'calc_num'  => $activityArr['groupPrice'],
                    ];
                } elseif (isset($activityArr['seckillPrice']) && $activityArr['seckillPrice'] !== false) {
                //} elseif (!empty($activityArr['seckillPrice'])) {
                    // 抢购
                    $subSaleSetting['discount_list'][] = [
                        'type'      => 'seckill_discount',
                        'name'      => '抢购单价',
                        'calc_type' => 'tprice',
                        'calc_num'  => $activityArr['seckillPrice'],
                    ];
                } elseif (isset($activityArr['jasmineDiscount']) && $activityArr['jasmineDiscount'] !== false) {
                //} elseif (!empty($activityArr['jasmineDiscount'])) {
                    // 茉莉分
                    $subSaleSetting['discount_list'][] = [
                        'type'      => 'jasmine_discount',
                        'name'      => '茉莉分积分折扣',
                        'calc_type' => 'tprice',
                        'calc_num'  => $activityArr['jasmineDiscount'],
                    ];
                }
            }

            //优惠券，调整顺序，保证优惠券在最后叠加优惠，（java接口要求）
            if (isset($item['coupon'])) {
                $subSaleSetting['discount_list'][] = [
                    'type'      => 'deduction',
                    'name'      => '优惠券折扣',
                    'calc_type' => 'deduction', //优惠折扣
                    'calc_num'  => $item['coupon']['use_coupon_value'], //折扣百分比
                ];
            }

            //身份证数据
            $subIdCardList = isset($idCardList[$tid]) ? $idCardList[$tid] : [];

            isset($idCardList['moreList']) && $subIdCardList['moreList'] = $idCardList['moreList'];

            //处理演出信息
            $showInfoArr = [];
            if ($ptype == 'H' && $ticketInfo['preSale'] == 0) {
                $showInfoArr = [
                    'venue_id' => $item['venue_id'],
                    'round_id' => $item['round_id'],
                    'area_id'  => $item['area_id'],
                    'seat_ids' => $item['seat_ids'],
                ];
            }

            //现场支付处理
            $tmpPaymode = $paymode;
            if ($payType == 0) {
                $tmpPaymode = 4;
            }

            //运费
            if ($ptype == 'J' && $item['carriage']) {
                $specialSettingArr['carriage'] = $item['carriage'];
            }

            //计时卡押金
            if ($ptype == 'K' && $item['time_card_deposit']) {
                $specialSettingArr['time_card_deposit'] = $item['time_card_deposit'];
            }

	        if ($ptype == 'K' && !empty($item['timing_device_id'])) {
		        $specialSettingArr['timing_device_id'] = $item['timing_device_id'];
	        }
	        $orderInfoArr[] = [
                'member'            => $member,
                'aid'               => $aid,
                'tid'               => $tid,
                'pid'               => $pid,
                'tnum'              => $tnum,
                'remotenum'         => $remotenum,
                'playdate'          => $playdate,
                'orderChannel'      => $orderChannel,
                'paymode'           => $tmpPaymode,
                'isNeedInsidePay'   => $isNeedInsidePay,
                'saleSetting'       => $subSaleSetting,
                'contactArr'        => $contactArr,
                'idCardList'        => $subIdCardList,
                'saleSiteArr'       => $saleSiteArr,
                'remarkArr'         => $remarkArr,
                'showInfoArr'       => $showInfoArr,
                'specialSettingArr' => $specialSettingArr,
                'useMarketingDiscount' => $useMarketingDiscount,
                'discountDetail'    => $discountDetail,
            ];
        }

        return $orderInfoArr;
    }

    /**
     * 合并付款提交订单参数处理
     * <AUTHOR>
     * @data   2019-03-29
     *
     * @param  array  $orderParams
     *
     * @return array
     */
    private function _combineCheck($orderParams)
    {
        //如果没有传的话默认是PC后台下单
        $channel   = isset($orderParams['channel']) ? $orderParams['channel'] : 0;
        $paymode   = isset($orderParams['paymode']) ? $orderParams['paymode'] : 1;
        $teamOrder = isset($orderParams['team_order']) ? $orderParams['team_order'] : 0;
        $isSale    = isset($orderParams['is_sale']) ? $orderParams['is_sale'] : false;
        $begintime = $orderParams['begintime'];
        $ordername = $orderParams['ordername'];
        $ordertel  = $orderParams['ordertel'];
        $teamData  = $orderParams['team'];
        // 用户id
        $memberId = $orderParams['member_id'] ?? 0;
        // 销售渠道
        $saleChannel = $orderParams['shop'] ?? 0;

        //营销活动标识校验->营销中心
        if (!empty($orderParams['mkActivityCode'])) {
            $checkActivityCodeRes = (new ActivityApi())->checkActivityCodeValid($orderParams['mkActivityCode'], $memberId, $orderParams['op_id']);
            //营销活动标识校验错误，则返回错误信息
            if ($checkActivityCodeRes['code'] != 200) {
                return [$checkActivityCodeRes['code'], $checkActivityCodeRes['msg']];
            }
        }

        if ($teamData['team_order']) {
            //团单号是否存在
            $teamOrderModel = new TeamOrderSearch();
            $teamOrderInfo  = $teamOrderModel->getSonOrderInfoByMainOrderNum($teamData['team_order']);
            if (empty($teamOrderInfo)) {
                return [400, "没有查到该团单"];
            }
        }

        if (empty($teamData['team_order']) && $teamData['is_team']) {
            //如果没传团队订单号 且是团队单
            $bookingBiz                        = new \Business\TeamOrder\Booking();
            $teamOrderNum                      = $bookingBiz->createTeamOrderNum($orderParams['member']);
            $orderParams['team']['team_order'] = $teamOrderNum;
        }

        if (!chk_date($begintime)) {
            return [400, "游玩日期{$begintime}格式错误"];
        }

        //if (!$ordername) {
        //    return [400, "联系人不能为空"];
        //}
        //
        //if (!$ordertel) {
        //    return [400, "联系电话不能为空"];
        //}
        if (!$orderParams['op_id']) {
            return [400, '缺失操作人员'];
        }
        //下单渠道需要进行判断
        $ordermodeArr = load_config('order_mode_track_map', 'business');
        if (!array_key_exists($channel, $ordermodeArr)) {
            return [400, "下单渠道[{$channel}]不存在"];
        }

        if (!is_numeric($paymode) && !in_array($paymode, [0, 1, 2, 3, 4, 5, 7, 9])) {
            return [400, '支付方式错误'];
        }

        $combineProductList = isset($orderParams['combine_pids']) ? $orderParams['combine_pids'] : [];
        if (!$combineProductList) {
            return [400, '合并付款参数错误'];
        }

        //门票数据
        $ticketInfoArr = [];
        //$ticketModel   = new TicketModel();
        $newTicketApi = new NewTicketApi();

        //是否需要效验手机号和联系人 true=要效验 false=不用效验
        $hasTel = $hasName = false;

        //合并付款的参数判断
        foreach ($combineProductList as $item) {
            if (!$item['pid'] || !$item['tnum'] || !$item['aid']) {
                return [400, '合并付款参数错误'];
            }

            $pid  = $item['pid'];
            $tnum = intval($item['tnum']);
            $aid  = $item['aid'];

            if ($tnum <= 0) {
                return [400, '合并付款票数错误'];
            }

            // 票信息通过java接口获取，新增渠道规则 -- jinmin
            $ticketInfoRes = $newTicketApi->replaceProductInfoTao($pid, $memberId, $aid, $saleChannel);
            if ($ticketInfoRes['code'] != 200) {
                return [400, '下单产品不存在'];
            }

            $ticketInfo = $ticketInfoRes['data'];
            //是否是会员体系那边配置的产品  0 = 不使用会员用折扣 1= 使用会员折扣
            $memberShipDiscountCode = isset($item['member_discount_code']) ? $item['member_discount_code'] : 0;
            if (!in_array($memberShipDiscountCode, [0, 1])) {
                return [400, "会员体系折扣参数错误"];
            }
            if(isset($item['discountDetail'])){
                $ticketInfo['discountDetail'] = $item['discountDetail'];
            }
            //时间段内有效 (期票的话不判断这个时间了，默认都是只能单天的)
            if (strtotime($ticketInfo['order_start']) > 0 && strtotime($ticketInfo['order_end']) > 0 && $ticketInfo['preSale'] != 1) {
                $tmpDate = date('Y-m-d H:i:s', strtotime($begintime));
                if ($tmpDate < $ticketInfo['order_start'] || $tmpDate > $ticketInfo['order_end']) {
                    return [400, '超出票类有效期'];
                }
                if ($ticketInfo['p_type'] == 'C') {
                    $tmpDate = date('Y-m-d H:i:s', strtotime($orderParams['leavetime']));
                    if ($tmpDate < $ticketInfo['order_start'] || $tmpDate > $ticketInfo['order_end']) {
                        return [400, '超出票类有效期'];
                    }
                }
            }

            $ticketInfoArr[$pid] = $ticketInfo;

            //演出产品校验
            if ($ticketInfo['p_type'] == 'H') {
                if ($ticketInfo['preSale'] == 0) {
                    if (!isset($item['round_id']) || !isset($item['venue_id'])) {
                        return [400, '场次信息缺失'];
                    }

                    if ($item['venue_id'] != $ticketInfo['venus_id'] || $item['area_id'] != $ticketInfo['zone_id']) {
                        return [400, '场次信息错误'];
                    }
                }
            }

            //如果是非散客下单不能带 upper_supplier参数
            if (!$isSale) {
                if ($item['upper_supplier']) {
                    return [400, '非散客下单，不能传upper_supplier参数'];
                }
            }

            //下单特殊配置判断-联系人
            if (isset($ticketInfo['need_name'])) {
                if ($ticketInfo['need_name'] == 1) {
                    $hasName = true;
                }
            } else {
                $hasName = true;
            }

            //下单特殊配置判断-手机号
            if (isset($ticketInfo['need_mobile'])) {
                if ($ticketInfo['need_mobile'] == 1) {
                    $hasTel = true;
                }
            } else {
                $hasTel = true;
            }
        }

        if ($hasName && !$ordername) {
            return [400, "联系人不能为空"];
        }
        if ($hasTel && !$ordertel) {
            return [400, "联系电话不能为空"];
        }

        return [200, '', $ticketInfoArr];
    }

    /**
     * 下单参数基础检测
     * 包括产品是否存在，产品类型，演出场次，一卡通数据，是否能下联票判断
     *
     * <AUTHOR>
     * @data   2019-02-01
     *
     * @param  array  $orderParams
     * {
     *      'pid'              //产品ID
     *      'begintime'        //开始游玩时间
     *      'leavetime'        //酒店离店时间 - 2019-02-13:这个参数在终端这边下单暂时没有使用到
     *      'channel'          //销售渠道:0=pc,1=wx,2=接口,3=自助机,4=云票务,5=智能终端
     *      'tnum'             //购买数量
     *      'paymode'          //支付方式 0 = 余额支付,1 = 支付宝,2 = 授信支付,3 = 产品自销,4 = 现场支付,9 = 现金支付
     *      'member'           //下单用户ID - 散客默认112
     *      'aid'              //产品供应商ID
     *      'upper_supplier'   //散客购买时，更上一级的分销商
     *      'ordername'        //游玩用户姓名
     *      'ordertel'         //游玩用户手机号
     *      'op_id'            //操作用户
     *      'memo'             //下单备注
     *      'venue_id'         //演出场馆ID
     *      'round_id'         //演出场次ID
     *      'area_id'          //演出分区ID
     *      'seat_ids'         //演出选座座位
     *      'team_order'       //团单的订单号
     *      'cut_price'        //砍价后的单价
     *      'group_price'      //团购后的单价
     *      'jasmine_discount' //茉莉分积分折扣 - 9.5折
     *      'site_id'          //站点ID
     *      'memo'             //下单备注
     *      'idcard_arr'       //游玩人员身份证 - 有带具体的哪个票对于哪些身份证信息
     *      'link_pids'        //联票产品ID
     *      'is_sale'          //是否是散客下单
     * }
     *
     * @return []
     */
    private function _baseCheck($orderParams)
    {
        $pid     = $orderParams['pid'];
        $roundId = isset($orderParams['round_id']) ? $orderParams['round_id'] : 0;
        $venusId = isset($orderParams['venue_id']) ? $orderParams['venue_id'] : 0;
        $areaId  = isset($orderParams['area_id']) ? $orderParams['area_id'] : 0;

        $linkPids = isset($orderParams['link_pids']) ? $orderParams['link_pids'] : [];

        //如果没有传的话默认是PC后台下单
        $channel   = isset($orderParams['channel']) ? $orderParams['channel'] : 0;
        $teamOrder = isset($orderParams['team_order']) ? $orderParams['team_order'] : 0;
        $begintime = $orderParams['begintime'];
        $ordername = $orderParams['ordername'];
        $ordertel  = $orderParams['ordertel'];

        if (!chk_date($begintime)) {
            return [400, "游玩日期{$begintime}格式错误"];
        }
        if (!$orderParams['op_id']) {
            return [400, '缺失操作人员'];
        }

        $ticketModel         = new TicketModel();
        $commodityProductBiz = new Product();
        $ticketInfo          = $commodityProductBiz->getProductAllInfoByPidToJava($pid);
        if (!$ticketInfo) {
            //记录日志
            $logData = json_encode([
                'pid' => $pid,
                'sql' => '找java',
            ]);
            pft_log('platform_order/debug', $logData);

            return [400, '下单产品不存在'];
        }

        //下单渠道需要进行判断
        $ordermodeArr = load_config('order_mode_track_map', 'business');
        if (!array_key_exists($channel, $ordermodeArr)) {
            return [400, "下单渠道[{$channel}]不存在"];
        }

        //演出产品校验
        if ($ticketInfo['p_type'] == 'H') {
            if (!isset($roundId, $venusId, $areaId)) {
                return [400, '场次信息缺失'];
            }

            if ($venusId != $ticketInfo['venus_id'] || $areaId != $ticketInfo['zone_id']) {
                return [400, '场次信息错误'];
            }
        } elseif ($ticketInfo['p_type'] == 'C') {
            if (!$orderParams['leavetime']) {
                return [400, '酒店产品缺失离店时间'];
            }
        }

        //如果不是团单产品 又使用团单计调下单 返回错误
        if ($ticketInfo['order_flag'] != 1 && $teamOrder) {
            return [400, '非团单产品, 不能计调下单'];
        }

        //联票数据判断
        if ($linkPids) {
            $pidArr = array_keys($linkPids);

            if (in_array($ticketInfo['p_type'], ['C', 'H', 'F'])) {
                return [400, '该产品类型不支持下联票'];
            }

            //新增加的渠道判断
            if ($channel == 5) {
                //终端
                $source = 'client';
            } else if ($channel == 4) {
                //云票务
                $source = 'cloud';
            } elseif ($channel == 3) {
                $source = 'ssmachine';
            } else {
                //暂定pc
                $source = 'pc';
            }

            $orderUnityBusizess = new \Business\Order\OrderUnity();
            array_unshift($pidArr, $pid);

            $ticketArr    = $ticketModel->getTidArrByPid($pidArr);
            $mainTicketId = $ticketArr[$pid];
            $ticketIdArr  = array_values($ticketArr);
            $beginFormat  = date('Y-m-d', strtotime($begintime));

            $idArrInfo = $orderUnityBusizess->linkProductFilterCommon($mainTicketId, $ticketIdArr, $source,
                $beginFormat);
            if ($idArrInfo['code'] != 200) {
                return [400, $idArrInfo['msg']];
            }
        }

        $needName   = $ticketInfo['confs']['need_name'] ?? 1; //下单是否需要填写姓名 1：需要 0：不需要
        $needMobile = $ticketInfo['confs']['need_mobile'] ?? 1; //下单是否需要填写手机号 1：需要 0：不需要
        if ($needName && !$ordername) {
            return [400, "联系人不能为空"];
        }

        if ($needMobile && !$ordertel) {
            return [400, "联系电话不能为空"];
        }

        //如果是非散客下单不能带 upper_supplier参数
        if (!$orderParams['is_sale']) {
            if ($orderParams['upper_supplier']) {
                return [400, '非散客下单，不能传upper_supplier参数'];
            }
        }

        return [200, '', $ticketInfo];
    }

    /**
     * 售价信息相关数组处理
     * <AUTHOR>
     * @data   2019-03-29
     *
     * @param  bool  $isSale
     * @param  int  $upperSupplier
     * @param  float  $discountScale
     * @param  float  $jasmineDiscount
     * @param  int  $cutPrice
     * @param  int  $groupPrice
     * @param  array  $teamDiscountInfo
     *
     * @return array
     */
    private function _handleSaleInfo($isSale, $upperSupplier, $discountScale = false, $jasmineDiscount = false, $cutPrice = false, $groupPrice = false,
        $seckillPrice = false, $couponInfo = false, $teamDiscountInfo = [])
    {
        $saleSetting = [
            'is_sale'        => $isSale, //是否散客购买
            'upper_supplier' => $upperSupplier, //更上一级的供应商，如果末级散客没有设置分销链的时候就有这个参数
            'discount_list'  => [], //各种优惠策略数组
        ];

        if ($discountScale) {
            $saleSetting['discount_list'][] = [
                'type'      => 'member_card',
                'name'      => '会员卡支付折扣',
                'calc_type' => 'discount', //优惠折扣
                'calc_num'  => $discountScale / 100, //折扣百分比
            ];
        } elseif ($jasmineDiscount) {
            $saleSetting['discount_list'][] = [
                'type'      => 'jasmine_discount',
                'name'      => '茉莉分积分折扣',
                'calc_type' => 'discount', //优惠折扣
                'calc_num'  => $jasmineDiscount, //折扣百分比
            ];
        } elseif ($cutPrice) {
            $saleSetting['discount_list'][] = [
                'type'      => 'cut_discount',
                'name'      => '砍价单价优惠',
                'calc_type' => 'tprice', //直接指定销售单价
                'calc_num'  => $cutPrice, //折扣百分比
            ];
        } elseif ($groupPrice) {
            $saleSetting['discount_list'][] = [
                'type'      => 'group_discount',
                'name'      => '拼团单价优惠',
                'calc_type' => 'tprice', //直接指定销售单价
                'calc_num'  => $groupPrice, //折扣百分比
            ];
        } elseif ($seckillPrice !== false) {
            $saleSetting['discount_list'][] = [
                'type'      => 'seckill_discount',
                'name'      => '抢购单价',
                'calc_type' => 'tprice', //直接指定销售单价
                'calc_num'  => $seckillPrice,
            ];
        }
        //优惠券需要独立出来，java接口下单的时候，优惠券需要在最后叠加
        if ($couponInfo) {
            //TODO: 优惠券信息
            $saleSetting['discount_list'][] = [
                'type'      => 'deduction',
                'name'      => '优惠券折扣',
                'calc_type' => 'deduction', //优惠折扣
                'calc_num'  => $couponInfo, //折扣百分比
            ];
        }

        if ($teamDiscountInfo) {
            $saleSetting['specifyReducedPolicyId'] = $teamDiscountInfo['specifyReducedPolicyId'] ?? 0;
            $saleSetting['bargainPricePolicyId']   = $teamDiscountInfo['bargainPricePolicyId'] ?? 0;
        }

        return $saleSetting;
    }

    /**
     * 处理和判断联票参数数据
     * <AUTHOR>
     * @data   2019-03-29
     *
     * @param  array  $linkPidArr
     * @param  int  $member
     * @param  int  $aid
     * @param  int  $upperSupplier
     * @param  date  $playdate
     * @param  date  $leavetime
     * @param  int  $orderChannel
     * @param  int  $paymode
     * @param  bool  $isNeedInsidePay
     * @param  array  $isSale
     * @param  array  $contactArr
     * @param  array  $idCardList
     * @param  array  $saleSiteArr
     * @param  array  $remarkArr
     * @param  array  $specialSettingArr
     *
     * @return array
     */
    private function _handleLinkParam($linkPidArr, $member, $aid, $upperSupplier, $playdate, $orderChannel, $paymode, $isNeedInsidePay, $isSale, $contactArr, $idCardList, $saleSiteArr, $remarkArr, $specialSettingArr)
    {
        $linkProductArr = [];
        //$ticketModel    = new TicketModel();
        $commodityProductBiz = new Product();
        //数据整理
        foreach ($linkPidArr as $subPid => $subTnum) {
            $subTnum = intval($subTnum);
            $subPid  = intval($subPid);

            if ($subTnum <= 0 || !$subPid) {
                continue;
            }
            $subProduct = $commodityProductBiz->getProductAllInfoByPidToJava($subPid);
            $subTid     = $subProduct['tid'];

            //销售分销链，价格，优惠之类的数据
            $subSaleSetting = [
                'is_sale'        => $isSale, //是否散客购买
                'upper_supplier' => $upperSupplier, //更上一级的供应商，如果末级散客没有设置分销链的时候就有这个参数
                'discount_list'  => [], //各种优惠策略数组
            ];

            //如果是用会员卡支付购票且渠道来自终端
            if ($paymode == 17) {
                //会员卡支付 折扣比例
                $subDiscountScale = $subProduct['discount_scale'];
                if ($subDiscountScale > 0 && $subDiscountScale < 100) {
                    $subSaleSetting['discount_list'][] = [
                        'type'      => 'member_card',
                        'name'      => '会员卡支付折扣',
                        'calc_type' => 2, //优惠折扣
                        'calc_num'  => $subDiscountScale / 100, //折扣百分比
                    ];
                }
            }

            //身份证数据
            $subIdCardList = isset($idCardList[$subTid]) ? $idCardList[$subTid] : [];

            $linkProductArr[] = [
                'member'            => $member,
                'aid'               => $aid,
                'tid'               => $subTid,
                'tnum'              => $subTnum,
                'playdate'          => $playdate,
                'orderChannel'      => $orderChannel,
                'paymode'           => $paymode,
                'isNeedInsidePay'   => $isNeedInsidePay,
                'saleSetting'       => $subSaleSetting,
                'contactArr'        => $contactArr,
                'idCardList'        => $subIdCardList,
                'saleSiteArr'       => $saleSiteArr,
                'remarkArr'         => $remarkArr,
                'specialSettingArr' => $specialSettingArr,
            ];
        }

        return $linkProductArr;
    }

    /**
     * 酒店的联票信息处理
     * <AUTHOR>
     * @data   2019-03-29
     *
     * @param  int  $member
     * @param  int  $aid
     * @param  date  $playdate
     * @param  date  $leavetime
     * @param  int  $orderChannel
     * @param  int  $paymode
     * @param  bool  $isNeedInsidePay
     * @param  array  $isSale
     * @param  array  $contactArr
     * @param  array  $idCardList
     * @param  array  $saleSiteArr
     * @param  array  $remarkArr
     * @param  array  $specialSettingArr
     *
     * @return array
     */
    private function _handleHotlLinkParam($member, $aid, $upperSupplier, $tid, $tnum, $playdate, $leavetime, $orderChannel, $paymode, $isNeedInsidePay, $isSale, $contactArr, $idCardList, $saleSiteArr, $remarkArr, $specialSettingArr)
    {
        $linkProductArr = [];
        //住店天数
        $days = ceil((strtotime($leavetime) - strtotime($playdate)) / 24 / 3600);

        if ($days <= 1) {
            //只住一天，不需要下联票了
        } else {
            for ($n = 1; $n < $days; $n++) {
                $subPlaydate = date("Y-m-d", strtotime($playdate) + 3600 * 24 * $n);

                $subSaleSetting = [
                    'is_sale'        => $isSale, //是否散客购买
                    'upper_supplier' => $upperSupplier, //更上一级的供应商，如果末级散客没有设置分销链的时候就有这个参数
                    'discount_list'  => [], //各种优惠策略数组
                ];

                $subIdCardList = $idCardList ?: [];

                $linkProductArr[] = [
                    'member'            => $member,
                    'aid'               => $aid,
                    'tid'               => $tid,
                    'tnum'              => $tnum,
                    'playdate'          => $subPlaydate,
                    'orderChannel'      => $orderChannel,
                    'paymode'           => $paymode,
                    'isNeedInsidePay'   => $isNeedInsidePay,
                    'saleSetting'       => $subSaleSetting,
                    'contactArr'        => $contactArr,
                    'idCardList'        => $subIdCardList,
                    'saleSiteArr'       => $saleSiteArr,
                    'remarkArr'         => $remarkArr,
                    'specialSettingArr' => $specialSettingArr,
                ];
            }
        }

        return $linkProductArr;
    }

    /**
     * 联票中子票下单
     * <AUTHOR>
     * @data   2019-03-27
     *
     * @param  string  $ordernum
     * @param  array  $linkProductArr
     *
     * @return array
     */
    private function _subOrder($ordernum, $linkProductArr)
    {
        $subOrderArr = [];

        foreach ($linkProductArr as $item) {
            //参数处理
            $member            = $item['member'];
            $aid               = $item['aid'];
            $tid               = $item['tid'];
            $tnum              = $item['tnum'];
            $playdate          = $item['playdate'];
            $orderChannel      = $item['orderChannel'];
            $paymode           = $item['paymode'];
            $isNeedInsidePay   = $item['isNeedInsidePay'];
            $subSaleSetting    = $item['saleSetting'];
            $contactArr        = $item['contactArr'];
            $idCardList        = $item['idCardList'][$tid] ?? (is_array($item['idCardList']) ? $item['idCardList'] : []);
            $saleSiteArr       = $item['saleSiteArr'];
            $remarkArr         = $item['remarkArr'];
            $specialSettingArr = $item['specialSettingArr'];

            //下联票
            $linkOrderInfoArr = [
                'link_type'       => 'link_son',
                'parent_ordernum' => $ordernum,
            ];

            $subOrderRes = Helpers::platformOrder($member, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode,
                $isNeedInsidePay, $subSaleSetting, $contactArr, '', $idCardList, $linkOrderInfoArr, $saleSiteArr,
                $remarkArr, [], $specialSettingArr);

            if ($subOrderRes['code'] != 200) {
                //下单失败写日志

            } else {
                $subOrderData  = $subOrderRes['data'];
                $subOrderArr[] = $subOrderData;
            }
        }

        return $subOrderArr;
    }

    /**
     * 下单完成后需要完成的后续任务
     * TODO:后期这些工作统一到队列去完成
     *
     * <AUTHOR>
     * @data   2019-02-22
     * @return []
     */
    private function _followOrderTask($orderData, $member, $paymode, $subOrderData = [], $mail = '', $teamOrder = '', $couponInfo = [], $orderParams = [])
    {
        //发送邮件
        $sendMailRes = -1;
        if ($mail && Helpers::isEmail($mail)) {
            $submitModel = new \Model\Order\OrderSubmit();
            $sendMailRes = $submitModel->SaveEmail($orderData['ordernum'], $mail);
        }

        //处理团单添加订单的数据
        $teamRes = -1;
        if ($teamOrder) {
            //如果填写了团队订单 需要关联
            $teamOrderModel = new \Model\Order\TeamOrderSearch();

            //需要修改团单张票
            $teamRes = $teamOrderModel->addMainAndSonOrderLink($teamOrder, $orderData['ordernum'], $orderData['lid'],
                $orderData['tid']);
            $teamOrderModel->setTicketNum($teamOrder, $orderData['tnum'], '+');
            //团单有新增下单的子订单，写入团单定时任务
            $teamReportTask = new TeamOrderReport();
            $teamReportTask->addTeamRealTask($teamOrder, $orderData['ordernum'], $orderData['tnum'],
                TeamConst::_REAL_TASK_ADD_ORDER_, $orderParams['op_id'], 1);
            //写入层级关系
            //订单生成成功，同步订单层级关系
            $orderSplitApi = new OrderAidsSplitQuery();
            $splitInfo     = $orderSplitApi->getOrderDistributionChain([$orderData['ordernum']]);
            $splitInfo     = empty($splitInfo['data']) ? [] : $splitInfo['data'];
            $levelInfo     = [];
            foreach ($splitInfo as $value) {
                $levelInfo[] = [
                    'buyerid'       => $value['buyerid'],
                    'sellerid'      => $value['sellerid'],
                    'order_level'   => $value['level'],
                    'pmode'         => $value['pmode'],
                    'cost_money'    => $value['costMoney'],
                    'sale_money'    => $value['saleMoney'],
                    'main_ordernum' => $teamOrder,
                    'son_ordernum'  => $value['orderid'],
                    'order_time'    => $value['orderTime'],
                    'apply_did'     => $value['applyDid'],
                ];
            }
            $splitRes = $teamOrderModel->addTeamOrderSplit($levelInfo);
            if ($splitRes === false) {
                TeamUtil::info([
                    'tag' => '_followOrderTask',
                    'msg' => '团单子订单层级关系新增失败',
                    'levelInfo' => $levelInfo,
                    'err' => $teamOrderModel->getDbError(),
                    'sql' => $teamOrderModel->getLastSql(),
                ]);
            }
        }

        //下单送优惠券活动
        $marketRes = -1;
        if (in_array($paymode, [0, 2, 3])) {
            $pid      = $orderData['pid'];
            $ordernum = $orderData['ordernum'];
            $tnum     = $orderData['tnum'];
            $orderPid = [
                $pid => [$tnum, $ordernum],
            ];

            //对应产品是否有做下单送优惠券活动
            $marketingModel = new \Model\Product\Marketing();
            $resMarketing   = $marketingModel->getMarketing(array_keys($orderPid));

            //数据
            $marketRes = $resMarketing;

            if ($resMarketing) {
                $markingData = [];
                foreach ($resMarketing as $key => $row) {
                    $markingData[$key]            = $row;
                    $markingData[$key]['orderid'] = $orderPid[$row['relation_pid']][1];
                    $markingData[$key]['tnum']    = $orderPid[$row['relation_pid']][0];
                }

                \Library\Resque\Queue::push('marketing_system', 'CouponSend_Job',
                    [
                        'couponData' => $markingData,
                        'memberID'   => $member,
                    ]
                );
            }
        }

        //分销专员下单记录下单数据
        $paramRes = -1;
        if (!empty($orderParams['dis_id'])) {
            $source       = 0;
            $disOrderList = [];
            //微商城有记录主订单号的下单参数
            if ($orderParams['channel'] == 0) {
                $disOrderList[] = $orderData['ordernum'];
            }

            if ($orderParams['channel'] == 19) {
                $disOrderList[] = $orderData['ordernum'];
                $source         = intval($orderParams['channel']);
            }

            foreach ($subOrderData as $tmpOrder) {
                $disOrderList[] = $tmpOrder['ordernum'];
            }

            $paramRes = (new \Model\Order\OrderLog())->saveOrderAndParamsMap($disOrderList, $orderParams, $source);
        }

        //TODO:将优惠券使用掉

        //记录日志
        $logData = json_encode([
            'ordernum'    => $orderData['ordernum'],
            'sendMailRes' => $sendMailRes,
            'teamRes'     => $teamRes,
            'marketRes'   => $marketRes,
            'paramRes'    => $paramRes,
        ]);
        pft_log('platform_order/debug', $logData);

        return true;
    }

    /**
     * 合并付款所需要的预留座位信息
     * @date   2019-03-09
     * <AUTHOR>
     *
     * @param  array  $orderInfoArr  合并付款参数数组
     * @param  string  $visitorMobile  购票手机号
     *
     * @return array 添加 series 标识|动作|总的需要几个连续的座位
     */
    private function _fillLinkShowMark(array $orderInfoArr = [], $visitorMobile = '')
    {
        if (empty($orderInfoArr)) {
            return [];
        }

        $count = count($orderInfoArr);
        for ($i = 0; $i < $count; $i++) {

            //演出的产品都有设置showInfoArr
            if (!isset($orderInfoArr[$i]['showInfoArr']) || empty($orderInfoArr[$i]['showInfoArr'])) {
                continue;
            }

            $tmpSeries = $orderInfoArr[$i]['showInfoArr'];

            //如果已经有选座直接返回
            if (isset($tmpSeries['seat_ids']) && $tmpSeries['seat_ids']) {
                continue;
            }

            //如果已经打上标记也直接返回
            if (isset($tmpSeries['link_seat_mark'])) {
                continue;
            }

            //标记值 场次号+分区号+订单手机号+随机数字
            $roundId   = $tmpSeries['round_id'];
            $areaId    = $tmpSeries['area_id'];
            $microtime = str_replace('.', '', microtime(true));
            $mark      = "show:{$roundId}:{$areaId}:{$visitorMobile}:{$microtime}";

            $total = 0; //附加的连座票数
            for ($j = $i + 1; $j < $count; $j++) {
                if (!isset($orderInfoArr[$j]['showInfoArr']) || empty($orderInfoArr[$j]['showInfoArr'])) {
                    continue;
                }

                //场馆-场次-分区-座位号为空（字符串应该是一致的）
                $lowerNode   = $orderInfoArr[$j];
                $lowerSeries = $orderInfoArr[$j]['showInfoArr'];

                //如果已经有选座直接返回
                if (isset($lowerSeries['seat_ids']) && $lowerSeries['seat_ids']) {
                    continue;
                }

                //如果已经打上标记也直接返回
                if (isset($lowerSeries['link_seat_mark'])) {
                    continue;
                }

                if (
                    $tmpSeries['round_id'] == $lowerSeries['round_id'] &&
                    $tmpSeries['area_id'] == $lowerSeries['area_id'] &&
                    $tmpSeries['venue_id'] == $lowerSeries['venue_id']
                ) {
                    $tnum  = (int)$lowerNode['tnum']; //票数
                    $total += $tnum; //附加的连座票，如果改成总票的话，判断一下$tmpSeries[4]

                    $tmpSeries['link_seat_mark']   = "$mark|create|$total";
                    $lowerSeries['link_seat_mark'] = "$mark|select|$tnum";

                    $orderInfoArr[$i]['showInfoArr'] = $tmpSeries;
                    $orderInfoArr[$j]['showInfoArr'] = $lowerSeries;
                }
            }
        }

        return $orderInfoArr;
    }

    /**
     * 平台下单频率控制
     * <AUTHOR>
     * @date 2019-03-30
     *
     * @param  array  $orderParams
     *
     * @return []
     */
    private function _controlFrequency($orderParams)
    {
        $lockKey = 'platform_order_' . md5(json_encode($orderParams));
        $cache   = \Library\Cache\Cache::getInstance('redis');
        $lockRes = $cache->lock($lockKey, 1, 10);
        if (!$lockRes) {
            //记录日志
            $logData = json_encode(['重复下单', $orderParams], JSON_UNESCAPED_UNICODE);
            pft_log('platform_order/debug', $logData);

            return [400, '订单正在处理中，请下单成功后再次操作'];
        } else {
            return [200, ''];
        }
    }
}
