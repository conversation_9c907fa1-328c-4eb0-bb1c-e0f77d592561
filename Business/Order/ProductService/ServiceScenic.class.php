<?php
/**
 * 门票产品业务（下单退款）
 * User: dwer.cn
 * Date: 2022-07-24
 */

namespace Business\Order\ProductService;

use Business\Base;
use Library\Constants\OrderConst;

class ServiceScenic extends Base implements ServiceInterface
{
    private $_linkTypeArr = [
        'common'      => 'common',
        'package_son' => 'packageSon',
        'link_son'    => 'linkSon',
        'link'        => 'link',
        'package'     => 'package',
    ];

    /**
     * 景区门票产品线下单 - 对接到门票产线线去
     * <AUTHOR>
     * @date 2022-07-24
     *
     * @param  array  $orderParams  下单参数
     * [
     * 'memberId'          => $memberId,
     * 'aid'               => $aid,
     * 'tid'               => $tid,
     * 'tnum'              => $tnum,
     * 'playdate'          => $playdate,
     * 'orderChannel'      => $orderChannel,
     * 'paymode'           => $paymode,
     * 'isNeedInsidePay'   => $isNeedInsidePay,
     * 'saleSettingArr'    => $saleSettingArr,
     * 'contactArr'        => $contactArr,
     * 'remotenum'         => $remotenum,
     * 'idcardArr'         => $idcardArr,
     * 'linkOrderInfoArr'  => $linkOrderInfoArr,
     * 'saleSiteArr'       => $saleSiteArr,
     * 'remarkArr'         => $remarkArr,
     * 'specialSettingArr' => $specialSettingArr,
     * 'orderExtensionParams' => $orderExtensionParams,
     * ];
     *
     * @return  array
     */
    public function commonOrder(array $orderParams)
    {
        $insidePayMode = $orderParams['paymode'];
        $qconf = \Library\Container::pull(\Library\Util\QConfUtil::class);
        if ($qconf->abTest('/php/platform/common_order_online_paymode_transfer') && $insidePayMode == 1) {
            //在线支付传入中台的值需要变更为255=未知
            $insidePayMode = 255;
        }

        //合并订单号（预留），后续这个合并订单号由购物车去订单中心获取
        $mergeOrderNum = '';

        $saleSettingArr         = $orderParams['saleSettingArr'] ?? [];
        $linkOrderInfoArr       = $orderParams['linkOrderInfoArr'] ?? [];
        $contactArrTmp          = $orderParams['contactArr'] ?? [];
        $saleSiteArrParam       = $orderParams['saleSiteArr'] ?? [];
        $remarkArrParam         = $orderParams['remarkArr'] ?? [];
        $specialSettingArrParam = $orderParams['specialSettingArr'] ?? [];
        $orderExtension         = $orderParams['orderExtensionParams'] ?? [];
        $idcardArrTmp           = $orderParams['idcardArr'] ?? [];

        //临时针对毕棚沟项目做下处理
        $allowTidArr = load_config('allow_tid_arr', 'tmp_bpg');
        $allowTidArr = $allowTidArr ?? [];
        if (in_array('all', $allowTidArr) || in_array($orderParams['tid'], $allowTidArr)) {
            //毕棚沟的tid走新的逻辑
            //联系人信息和实名制信息，需要兼容下旧的格式
            $tmpRes = $this->_handleIdCardDataNew($contactArrTmp, $idcardArrTmp);
        } else {
            //联系人信息和实名制信息，需要兼容下旧的格式
            $tmpRes = $this->_handleIdCardData($contactArrTmp, $idcardArrTmp);
        }

        //兼容后的实名制信息
        $contactArrParam = $tmpRes['contact_arr'];
        $idcardArrParam  = $tmpRes['idcard_arr'];

        $contactMoreList = [];
        if (isset($contactArrParam['moreList']) && $contactArrParam['moreList']) {
            foreach ($contactArrParam['moreList'] as $item) {
                //临时先直接这样处理 - 后面处理下
                $contactMoreList[] = $item;
            }
        }

        $contactArr = [
            'orderName'    => strval($contactArrParam['ordername'] ?? ''),
            'orderTel'     => strval($contactArrParam['ordertel'] ?? ''),
            'mobileArea'   => strval($contactArrParam['mobile_area'] ?? ''),
            'mobileRegion' => strval($contactArrParam['mobile_region'] ?? ''),
            'isSms'        => boolval($contactArrParam['is_sms'] ?? true),
            'contactTel'   => strval($contactArrParam['contacttel'] ?? ''),
            'callbackUrl'  => strval($contactArrParam['callback_url'] ?? ''),
            'voucherType'  => intval($contactArrParam['voucher_type'] ?? 0),
            'personId'     => strval($contactArrParam['personid'] ?? ''),
            'isNotSendSms' => $contactArrParam['is_not_send_sms'] == 1 ? true : false,
            'provinceCode' => intval($contactArrParam['province_code'] ?? 0),
            'cityCode'     => intval($contactArrParam['city_code'] ?? 0),
            'townCode'     => intval($contactArrParam['town_code'] ?? 0),
            'address'      => strval($contactArrParam['address'] ?? ''),
            'moreList'     => $contactMoreList,
        ];

        $saleSiteArr = [
            'siteId'   => intval($saleSiteArrParam['site_id'] ?? 0),
            'terminal' => intval($saleSiteArrParam['terminal'] ?? 0),
            'stuffId'  => intval($saleSiteArrParam['stuff_id'] ?? 0),
            'clientIp' => strval($saleSiteArrParam['client_ip'] ?? ''),
        ];

        //添加子商户操作人
        if (isset($saleSiteArrParam['subStuffId'])) {
            $saleSiteArr['subStuffId'] = intval($saleSiteArrParam['subStuffId']);
        }

        $remarkArr = [
            'memo'         => strval($remarkArrParam['memo'] ?? ''),
            'origin'       => strval($remarkArrParam['origin'] ?? ''),
            'serialNumber' => strval($remarkArrParam['serial_number'] ?? ''),
            'prePoseIds'   => $remarkArrParam['pre_pose_ids'] ?? [],
        ];

        //下单提供的额外结算方式
        $distributePrices = [];
        if (isset($specialSettingArrParam['distributePrices'])) {
            foreach ($specialSettingArrParam['distributePrices'] as $item) {
                $distributePrices[] = [
                    'tid'           => intval($item['tid']),
                    'fid'           => intval($item['memberId']),
                    'sid'           => intval($item['sid']),
                    'price'         => $item['price'],
                    'discountState' => $item['discountState'],
                ];
            }
        }

        $specialSettingArr = [
            'isVerifySpecial'  => $specialSettingArrParam['is_verify_special'] ?? true,
            'isNeedCallback'   => $specialSettingArrParam['is_need_callback'] ?? false,
            'distributePrices' => $distributePrices,
            'useWindowPrice'   => $specialSettingArrParam['useWindowPrice'] ?? true,
            'moreStorage'      => $specialSettingArrParam['moreStorage'] ?? 2,
        ];

        $orderExtensionParam = [
            'checkIdCard'         => $orderExtension['checkIdCard'] ?? 0,
            'skipDayAndHourCheck' => $orderExtension['book_advance'] == 1 ? 1 : 0,
        ];

        $orderExtra = [];
        if (isset($orderExtension['saleChannel'])) {
            $orderExtra['saleChannel'] = $orderExtension['saleChannel']; //  销售二级渠道: 101.小红书
        }

        if (isset($orderExtension['equityEncryptionCode'])) {
            $orderExtra['equityEncryptionCode'] = $orderExtension['equityEncryptionCode']; //预售券权益加密串
        }

        if (isset($orderExtension['firstSaleChannel'])) {
            $orderExtra['firstSaleChannel'] = $orderExtension['firstSaleChannel']; //  新销售一级渠道
        }

        if (isset($orderExtension['secondSaleChannel'])) {
            $orderExtra['secondSaleChannel'] = $orderExtension['secondSaleChannel']; // 新销售二级渠道:
        }

        if (isset($orderExtension['mkActivityCode'])) {
            $orderExtra['mkActivityCode'] = $orderExtension['mkActivityCode']; //营销活动标识
        }

        if (isset($orderExtension['enable_real_name'])) {
            $orderExtra['enableRealName'] = $orderExtension['enable_real_name'] ?? false; //true为开启实名，会校验实名规则，false不开启：跳过，只有终端该值生效，默认为false不开启
        }

        //增加交易订单号（CMB合并订单号）
        if (!empty($orderExtension['tradeOrderId'])) {
            $orderExtra['tradeOrderId'] = $orderExtension['tradeOrderId'];
        }

        $idCardList          = [];
        if ($idcardArrParam) {
            foreach ($idcardArrParam as $item) {
                $moreList      = $item['moreList'] ?? [];
                $idCardUrlList = $item['id_card_url'] ?? [];
                $certUrlList   = $item['cert_url'] ?? [];

                $idCardList[] = [
                    'name'          => strval($item['name'] ?? ''),
                    'idCard'        => strval($item['idcard'] ?? ''),
                    'voucherType'   => intval($item['voucher_type'] ?? 1),
                    'mobile'        => strval($item['mobile'] ?? ''),
                    'mobileArea'    => strval($item['mobile_area'] ?? ''),
                    'mobileRegion'  => strval($item['mobile_region'] ?? ''),
                    'moreList'      => $moreList,
                    'idCardUrlList' => $idCardUrlList,
                    'certUrlList'   => $certUrlList,
                    'age' => $item['age'] ?? null,
                    'sex' => $item['sex'] ?? null,
                ];
            }
        }

        //分时预约数据
        $timeShareInfo = [];
        if ($remarkArrParam['time_share_info']) {
            $timeShareInfo = [
                'timeId'  => intval($remarkArrParam['time_share_info']['time_id'] ?? 0),
                'timeStr' => strval($remarkArrParam['time_share_info']['time_str'] ?? ''),
            ];
        }

        //优惠折扣数据
        $discountList = [];
        if (isset($saleSettingArr['discount_list'])) {
            foreach ($saleSettingArr['discount_list'] as $item) {
                $discountList[] = [
                    'type'     => $item['type'] ?? '',
                    'name'     => $item['name'] ?? '',
                    'calcType' => $item['calc_type'] ?? '',
                    'calcNum'  => $item['calc_num'] ?? 0,
                ];
            }
        }

        //临时只有一张票下单的情况，后续会支持多票种的情况
        $ticketList = [];
        $ticketInfo = [
            'tid'           => intval($orderParams['tid']),
            'aid'           => intval($orderParams['aid']),
            'upperSupplier' => intval($saleSettingArr['upper_supplier'] ?? 0),
            'tnum'          => intval($orderParams['tnum']),
            'playdate'      => $orderParams['playdate'],
            'timeShareInfo' => $timeShareInfo,
            'discountList'  => $discountList,
        ];
        //优惠相关参数
        if (isset($orderExtension['discountDetail']) && !empty($orderExtension['discountDetail'])) {
            $ticketInfo['extra']['discountDetail']         = $orderExtension['discountDetail'];
            if(isset($orderExtension['discountDetail']['usePoint'])){
                $ticketInfo['extra']['usePoint']               = $saleSettingArr['use_point'];
            }
            if(isset($orderExtension['discountDetail']['useCoupon'])){
                $ticketInfo['extra']['useCoupon']              = $saleSettingArr['use_coupon'];
            }
            if(isset($orderExtension['discountDetail']['use_discount'])){
                $ticketInfo['extra']['use_discount']           = $saleSettingArr['use_discount'];
                $orderExtensionParam['useMemberDiscount']      = $saleSettingArr['use_discount'] ?? false;
            }
            if(isset($orderExtension['discountDetail']['bargainPricePolicyId'])){
                $ticketInfo['extra']['bargainPricePolicyId']   = $saleSettingArr['bargainPricePolicyId'];    //增加报团优惠特价快照ID
            }
            if(isset($orderExtension['discountDetail']['specifyReducedPolicyId'])){
                $ticketInfo['extra']['specifyReducedPolicyId'] = $saleSettingArr['specifyReducedPolicyId'];    //增加报团优惠减免快照ID
            }
            if(isset($saleSettingArr['useMarketingDiscount'])){
                $ticketInfo['extra']['useMarketingDiscount'] = $saleSettingArr['useMarketingDiscount'];    //是否使用通用优惠参数
            }
        }

        if (isset($orderExtension['tags'])) {
            $ticketInfo['extra']['tags'] = $orderExtension['tags']; //适用人群标签信息:
        }


        $ticketList[] = $ticketInfo;

        $isSale         = $saleSettingArr['is_sale'] ? true : false;
        $parentOrderNum = $linkOrderInfoArr['parent_ordernum'] ?? '';
        $linkType       = $this->_linkTypeArr[$linkOrderInfoArr['link_type']] ?? 'common';

        try {
            //如果绑定上游，则使用上游的下单服务，需要切换api域名
            $orderServiceApi = $this->getOrderServiceApi($orderParams);

            $lib = new \Business\JavaApi\Order\ScenicOrder();
            $lib->switchOrderServiceApi($orderServiceApi);//切换api域名
            $res = $lib->submitOrder($mergeOrderNum, $orderParams['memberId'], $orderParams['remotenum'],
                $orderParams['orderChannel'], $insidePayMode, $orderParams['isNeedInsidePay'],
                $isSale, $linkType, $parentOrderNum, $contactArr, $idCardList, $saleSiteArr, $remarkArr,
                $specialSettingArr,
                $orderExtensionParam, $ticketList, $orderExtra);

            //返回数据处理成原先的格式
            if ($res['code'] == 200) {
                $orderData = $res['data'];

                $remoteNum  = $orderData['remoteNum'];
                $isOrdered  = $orderData['isOrdered'];
                $isPay      = $orderData['isPay'];
                //paymode和其他产品线一致，返回下单时传参的paymode
                $payMode    = $orderParams['paymode'];
                //这里接收中台返回的实际paymode
                $realPayMode = $orderData['payMode'];
                $ticketList = $orderData['ticketList'];

                //因为暂时只有单产品下单，所以直接获取第一个
                $orderTicketInfo = $ticketList[0];

                $resData = [
                    'ordernum'      => $orderTicketInfo['orderNum'],
                    'remotenum'     => $remoteNum,
                    'tid'           => $orderTicketInfo['tid'],
                    'lid'           => $orderTicketInfo['lid'],
                    'pid'           => $orderTicketInfo['pid'],
                    'tnum'          => $orderTicketInfo['tnum'],
                    'tprice'        => $orderTicketInfo['tprice'],
                    'totalmoney'    => $orderTicketInfo['totalMoney'],
                    'cost_price'    => $orderTicketInfo['costPrice'],
                    'counter_price' => $orderTicketInfo['counterPrice'],
                    'retail_price'  => $orderTicketInfo['retailPrice'],
                    'code'          => $orderTicketInfo['code'],
                    'paymode'       => $payMode,
                    'is_pay'        => $isPay,
                    'is_sms'        => $orderTicketInfo['isSms'],
                    'is_ordered'    => $isOrdered, //是否通过远程订单号已经下过订单了
                    'mdetails'      => $orderTicketInfo['mdetails'],
                    'sourcet'       => $orderTicketInfo['sourcet'],
                    'member'        => $orderTicketInfo['member'],
                    'aid'           => $orderTicketInfo['aid'],
                    'apply_did'     => $orderTicketInfo['applyDid'],
                    'real_paymode'  => $realPayMode,
                ];

                return $this->orderReturn(200, '下单成功', $resData);
            } else {
                //产品线返回的错误码和内部错误信息
                $errorCode = $res['data']['err_code'] ?? 0;
                $innerMsg  = $res['data']['inner_msg'] ?? '';

                return $this->orderReturn(0, $res['msg'], ['err_code' => $errorCode, 'inner_msg' => $innerMsg]);
            }
        } catch (\Exception $e) {
            //如果有抛出异常，统一返回系统异常
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            return $this->orderReturn(0, '系统异常，请稍后再试', ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * 正常退票
     * <AUTHOR>
     * @date 2022-11-11
     *
     * @param  array  $refundParamArr  退票参数
     *
     * @return array
     */
    public function normalRefund(array $refundParamArr)
    {
        $cancelRemarkArr  = $refundParamArr['cancelRemarkArr'] ?? [];
        $cancelSiteArr    = $refundParamArr['cancelSiteArr'] ?? [];
        $cancelPersonArr  = $refundParamArr['cancelPersonArr'] ?? [];
        $cancelSpecialArr = $refundParamArr['cancelSpecialArr'] ?? [];

        $ordernum        = strval($refundParamArr['ordernum']);
        $cancelNum       = intval($refundParamArr['cancelNum']);
        $opId            = intval($refundParamArr['opId']);
        $cancelChannel   = intval($refundParamArr['cancelChannel']);
        $cancelType      = strval($refundParamArr['cancelType']);
        $reqSerialNumber = strval($refundParamArr['reqSerialNumber']);

        $cancelRemark = strval($cancelRemarkArr['remark'] ?? '');
        $terminal     = intval($cancelSiteArr['terminal'] ?? 0);
        $personIndex  = isset($cancelPersonArr['person_index']) ? intval($cancelPersonArr['person_index']) : null;

        $personIdListTmp = $cancelPersonArr['person_id_list'] ?? [];
        $personIdList    = [];
        foreach ($personIdListTmp as $item) {
            $personIdList[] = strval($item);
        }
        $refundPersonInfoList = $cancelPersonArr['person_info_list'] ?? [];
        if (empty($personIdList) && $refundPersonInfoList) {
            foreach ($refundPersonInfoList as $personInfo) {
                if (!empty($personInfo['idcard'])) {
                    $personIdList[] = strval($personInfo['idcard']);
                }
                if (!empty($personInfo['idx'])) {
                    $personIndex[] = $personInfo['idx'];
                }
            }
        }

        $ticketCodeListTmp = $cancelPersonArr['ticket_code_list'] ?? [];
        $ticketCodeList    = [];
        foreach ($ticketCodeListTmp as $item) {
            $ticketCodeList[] = strval($item);
        }

        $cancelSpecial = [];
        if (isset($cancelSpecialArr['is_need_audit'])) {
            $cancelSpecial['isNeedAudit'] = boolval($cancelSpecialArr['is_need_audit']);
        }
        if (isset($cancelSpecialArr['auto_cancel_fee'])) {
            $cancelSpecial['autoCancelFee'] = intval($cancelSpecialArr['auto_cancel_fee']);
        }
        if (isset($cancelSpecialArr['cancel_time'])) {
            $cancelSpecial['cancelTime'] = strval($cancelSpecialArr['cancel_time']);
        }
        if (isset($cancelSpecialArr['is_force_cancel'])) {
            $cancelSpecial['isForceCancel'] = boolval($cancelSpecialArr['is_force_cancel']);
        }
        if (isset($cancelSpecialArr['is_cancel_third_system'])) {
            $cancelSpecial['isCancelThirdSystem'] = boolval($cancelSpecialArr['is_cancel_third_system']);
        }
        if (isset($cancelSpecialArr['is_cancel_notice_third'])) {
            $cancelSpecial['isCancelNoticeThird'] = boolval($cancelSpecialArr['is_cancel_notice_third']);
        }
        if (isset($cancelSpecialArr['is_allow_more_revoke'])) {
            $cancelSpecial['isAllowMoreRevoke'] = boolval($cancelSpecialArr['is_allow_more_revoke']);
        }
        if (isset($cancelSpecialArr['is_rollback'])) {
            $cancelSpecial['isRollBack'] = boolval($cancelSpecialArr['is_rollback']);
        }
        if (isset($cancelSpecialArr['is_audit_pass'])) {
            $cancelSpecial['isAuditPass'] = boolval($cancelSpecialArr['is_audit_pass']);
        }
        if (isset($cancelSpecialArr['cancel_audit_remark'])) {
            $cancelSpecial['cancelAuditRemark'] = strval($cancelSpecialArr['cancel_audit_remark']);
        }
        if (isset($cancelSpecialArr['is_package_sub'])) {
            $cancelSpecial['isPackageSub'] = boolval($cancelSpecialArr['is_package_sub']);
        }
        if (isset($cancelSpecialArr['is_group_order'])) {
            $cancelSpecial['isOrderGroup'] = boolval($cancelSpecialArr['is_group_order']);
        }
        if (!empty($cancelSpecialArr['is_syntax_rollback'])) {
            $cancelSpecial['actionReason'] = 'rollback';
        }
        pft_log('order_refund/debug',json_encode(['nopayRefund_refundParams','ServiceScenic', $ordernum, $cancelNum, $reqSerialNumber, $personIndex, $personIdList, $cancelPersonArr]));

        try {
            $lib       = new \Business\JavaApi\Order\ScenicRefund();
            $handleRes = $lib->normalRefund($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber,
                $cancelRemark, $terminal, $personIndex, $personIdList, $ticketCodeList, $cancelSpecial);

            if ($handleRes['code'] == 200) {
                //数据处理后返回
                $resData = $handleRes['data'];
                $data    = [
                    'pft_serial_number' => $resData['pftSerialNumber'],
                    'req_serial_number' => $resData['reqSerialNumber'],
                    'order_status'      => $resData['orderStatus'],
                    'cancel_num'        => $resData['cancelNum'],
                    'left_num'          => $resData['leftNum'],
                    'valid_num'         => $resData['validNum'],
                    'valid_money'       => $resData['validMoney'],
                    'op_id'             => $resData['opId'],
                    'is_audit'          => $resData['isAudit'],
                    'refund_amount'     => $resData['refundAmount'] ?? null,
                    'refund_fee'        => $resData['refundFee'] ?? null,
                ];

                return $this->returnData(200, '处理成功', $data);
            } else {
                //之前代码里面需要的参数
                $errorMsg = $handleRes['msg'];
                $errCode  = $handleRes['code'];
                $innerMsg = $handleRes['inner_msg'] ?? $errorMsg;

                return $this->returnData(0, $errorMsg, ['err_code' => $errCode, 'inner_msg' => $innerMsg]);
            }
        } catch (\Exception $e) {
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            return $this->returnData(0, '系统异常，请稍后再试', ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * 回滚退(套票子票回滚退）
     * <AUTHOR>
     * @date 2022-11-11
     *
     * @param  array  $refundParamArr  退票参数
     *
     * @return array
     */
    public function rollbackPackageRefund(array $refundParamArr)
    {
        $cancelRemarkArr  = $refundParamArr['cancelRemarkArr'] ?? [];
        $cancelSiteArr    = $refundParamArr['cancelSiteArr'] ?? [];
        $cancelPersonArr  = $refundParamArr['cancelPersonArr'] ?? [];
        $cancelSpecialArr = $refundParamArr['cancelSpecialArr'] ?? [];

        $ordernum        = strval($refundParamArr['ordernum']);
        $cancelNum       = intval($refundParamArr['cancelNum']);
        $opId            = intval($refundParamArr['opId']);
        $cancelChannel   = intval($refundParamArr['cancelChannel']);
        $cancelType      = strval($refundParamArr['cancelType']);
        $reqSerialNumber = strval($refundParamArr['reqSerialNumber']);

        $cancelRemark = strval($cancelRemarkArr['remark'] ?? '');
        $terminal     = intval($cancelSiteArr['terminal'] ?? 0);
        $personIndex  = isset($cancelPersonArr['person_index']) ? intval($cancelPersonArr['person_index']) : null;

        $personIdListTmp = $cancelPersonArr['person_id_list'] ?? [];
        $personIdList    = [];
        foreach ($personIdListTmp as $item) {
            $personIdList[] = strval($item);
        }

        $ticketCodeListTmp = $cancelPersonArr['ticket_code_list'] ?? [];
        $ticketCodeList    = [];
        foreach ($ticketCodeListTmp as $item) {
            $ticketCodeList[] = strval($item);
        }

        $cancelSpecial = [];
        if (isset($cancelSpecialArr['is_need_audit'])) {
            $cancelSpecial['isNeedAudit'] = boolval($cancelSpecialArr['is_need_audit']);
        }
        if (isset($cancelSpecialArr['auto_cancel_fee'])) {
            $cancelSpecial['autoCancelFee'] = intval($cancelSpecialArr['auto_cancel_fee']);
        }
        if (isset($cancelSpecialArr['cancel_time'])) {
            $cancelSpecial['cancelTime'] = strval($cancelSpecialArr['cancel_time']);
        }
        if (isset($cancelSpecialArr['is_force_cancel'])) {
            $cancelSpecial['isForceCancel'] = boolval($cancelSpecialArr['is_force_cancel']);
        }
        if (isset($cancelSpecialArr['is_cancel_third_system'])) {
            $cancelSpecial['isCancelThirdSystem'] = boolval($cancelSpecialArr['is_cancel_third_system']);
        }
        if (isset($cancelSpecialArr['is_cancel_notice_third'])) {
            $cancelSpecial['isCancelNoticeThird'] = boolval($cancelSpecialArr['is_cancel_notice_third']);
        }
        if (isset($cancelSpecialArr['is_allow_more_revoke'])) {
            $cancelSpecial['isAllowMoreRevoke'] = boolval($cancelSpecialArr['is_allow_more_revoke']);
        }
        if (isset($cancelSpecialArr['is_rollback'])) {
            $cancelSpecial['isRollBack'] = boolval($cancelSpecialArr['is_rollback']);
        }
        if (isset($cancelSpecialArr['is_audit_pass'])) {
            $cancelSpecial['isAuditPass'] = boolval($cancelSpecialArr['is_audit_pass']);
        }
        if (isset($cancelSpecialArr['cancel_audit_remark'])) {
            $cancelSpecial['cancelAuditRemark'] = strval($cancelSpecialArr['cancel_audit_remark']);
        }
        if (isset($cancelSpecialArr['is_package_sub'])) {
            $cancelSpecial['isPackageSub'] = boolval($cancelSpecialArr['is_package_sub']);
        }
        if (isset($cancelSpecialArr['is_group_order'])) {
            $cancelSpecial['isOrderGroup'] = boolval($cancelSpecialArr['is_group_order']);
        }
        if (isset($cancelSpecialArr['is_syntax_rollback'])) {
            $cancelSpecial['isSyntaxRollback'] = boolval($cancelSpecialArr['is_syntax_rollback']);
        }

        try {
            $lib       = new \Business\JavaApi\Order\ScenicRefund();
            $handleRes = $lib->rollbackPackageRefund($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber,
                $cancelRemark, $terminal, $personIndex, $personIdList, $ticketCodeList, $cancelSpecial);

            if ($handleRes['code'] == 200) {
                //数据处理后返回
                $resData = $handleRes['data'];
                $data    = [
                    'pft_serial_number' => $resData['pftSerialNumber'],
                    'req_serial_number' => $resData['reqSerialNumber'],
                    'order_status'      => $resData['orderStatus'],
                    'cancel_num'        => $resData['cancelNum'],
                    'left_num'          => $resData['leftNum'],
                    'valid_num'         => $resData['validNum'],
                    'valid_money'       => $resData['validMoney'],
                    'op_id'             => $resData['opId'],
                    'is_audit'          => $resData['isAudit'],
                    'refund_amount'     => $resData['refundAmount'] ?? null,
                    'refund_fee'        => $resData['refundFee'] ?? null,
                ];

                return $this->returnData(200, '处理成功', $data);
            } else {
                //之前代码里面需要的参数
                $errorMsg = $handleRes['msg'];
                $errCode  = $handleRes['code'];
                $innerMsg = $handleRes['inner_msg'] ?? $errorMsg;

                return $this->returnData(0, $errorMsg, ['err_code' => $errCode, 'inner_msg' => $innerMsg]);
            }
        } catch (\Exception $e) {
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            return $this->returnData(0, '系统异常，请稍后再试', ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * 审核通过退(上游审核通过后退/平台供应商审核通过退)
     * <AUTHOR>
     * @date 2022-11-11
     *
     * @param  array  $refundParamArr  审核参数
     *
     * @return array
     */
    public function auditRefund(array $refundParamArr)
    {
        $auditRecord   = $refundParamArr['auditRecord'] ?? [];
        $auditSource   = intval($refundParamArr['auditSource'] ?? 1);
        $auditMemberId = intval($refundParamArr['auditMemberId'] ?? 0);

        $auditId       = $auditRecord['id'];
        $ordernum      = $auditRecord['ordernum'];
        $lid           = $auditRecord['lid'];
        $tid           = $auditRecord['tid'];
        $stype         = $auditRecord['stype'];
        $dstatus       = $auditRecord['dstatus'];
        $reason        = $auditRecord['reason'];
        $applyMemberId = $auditRecord['fxid'];
        $applyTime     = $auditRecord['stime'];
        $auditTime     = $auditRecord['dtime'];
        $systemSn      = $auditRecord['system_sn'];
        $pType         = $auditRecord['p_type'];
        $auditData     = $auditRecord['audit_data'];

        try {
            $lib       = new \Business\JavaApi\Order\ScenicRefund();
            $handleRes = $lib->auditRefund($auditId, $ordernum, $lid, $tid, $stype, $dstatus, $reason, $auditMemberId,
                $applyMemberId, $auditSource, $applyTime, $auditTime, $systemSn, $pType, $auditData);

            if ($handleRes['code'] == 200) {
                //数据处理后返回
                $resData = $handleRes['data'];
                $data    = [
                    'pft_serial_number' => $resData['pftSerialNumber'],
                    'req_serial_number' => $resData['reqSerialNumber'],
                    'order_status'      => $resData['orderStatus'],
                    'cancel_num'        => $resData['cancelNum'],
                    'left_num'          => $resData['leftNum'],
                    'valid_num'         => $resData['validNum'],
                    'valid_money'       => $resData['validMoney'],
                    'op_id'             => $resData['opId'],
                    'is_audit'          => $resData['isAudit'],
                    'refund_amount'     => $resData['refundAmount'] ?? null,
                    'refund_fee'        => $resData['refundFee'] ?? null,
                ];

                return $this->returnData(200, '处理成功', $data);
            } else {
                //之前代码里面需要的参数
                $errorMsg = $handleRes['msg'];
                $errCode  = $handleRes['code'];
                $innerMsg = $handleRes['inner_msg'] ?? $errorMsg;

                return $this->returnData($errCode, $errorMsg, ['err_code' => $errCode, 'inner_msg' => $innerMsg]);
            }
        } catch (\Exception $e) {
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            return $this->returnData(OrderConst::err15, '系统异常，请稍后再试',
                ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * 撤销/撤改退
     * <AUTHOR>
     * @date 2022-11-11
     *
     * @param  array  $refundParamArr  退票参数
     *
     * @return array
     */
    public function revokeRefund(array $refundParamArr)
    {
        $cancelRemarkArr  = $refundParamArr['cancelRemarkArr'] ?? [];
        $cancelSiteArr    = $refundParamArr['cancelSiteArr'] ?? [];
        $cancelPersonArr  = $refundParamArr['cancelPersonArr'] ?? [];
        $cancelSpecialArr = $refundParamArr['cancelSpecialArr'] ?? [];

        $ordernum        = strval($refundParamArr['ordernum']);
        $cancelNum       = intval($refundParamArr['cancelNum']);
        $opId            = intval($refundParamArr['opId']);
        $cancelChannel   = intval($refundParamArr['cancelChannel']);
        $cancelType      = strval($refundParamArr['cancelType']);
        $reqSerialNumber = strval($refundParamArr['reqSerialNumber']);

        $cancelRemark = strval($cancelRemarkArr['remark'] ?? '');
        $terminal     = intval($cancelSiteArr['terminal'] ?? 0);
        $personIndex  = isset($cancelPersonArr['person_index']) ? intval($cancelPersonArr['person_index']) : null;

        $personIdListTmp = $cancelPersonArr['person_id_list'] ?? [];
        $personIdList    = [];
        foreach ($personIdListTmp as $item) {
            $personIdList[] = strval($item);
        }

        $ticketCodeListTmp = $cancelPersonArr['ticket_code_list'] ?? [];
        $ticketCodeList    = [];
        foreach ($ticketCodeListTmp as $item) {
            $ticketCodeList[] = strval($item);
        }

        $cancelSpecial = [];
        if (isset($cancelSpecialArr['is_need_audit'])) {
            $cancelSpecial['isNeedAudit'] = boolval($cancelSpecialArr['is_need_audit']);
        }
        if (isset($cancelSpecialArr['auto_cancel_fee'])) {
            $cancelSpecial['autoCancelFee'] = intval($cancelSpecialArr['auto_cancel_fee']);
        }
        if (isset($cancelSpecialArr['cancel_time'])) {
            $cancelSpecial['cancelTime'] = strval($cancelSpecialArr['cancel_time']);
        }
        if (isset($cancelSpecialArr['is_force_cancel'])) {
            $cancelSpecial['isForceCancel'] = boolval($cancelSpecialArr['is_force_cancel']);
        }
        if (isset($cancelSpecialArr['is_cancel_third_system'])) {
            $cancelSpecial['isCancelThirdSystem'] = boolval($cancelSpecialArr['is_cancel_third_system']);
        }
        if (isset($cancelSpecialArr['is_cancel_notice_third'])) {
            $cancelSpecial['isCancelNoticeThird'] = boolval($cancelSpecialArr['is_cancel_notice_third']);
        }
        if (isset($cancelSpecialArr['is_allow_more_revoke'])) {
            $cancelSpecial['isAllowMoreRevoke'] = boolval($cancelSpecialArr['is_allow_more_revoke']);
        }
        if (isset($cancelSpecialArr['is_rollback'])) {
            $cancelSpecial['isRollBack'] = boolval($cancelSpecialArr['is_rollback']);
        }
        if (isset($cancelSpecialArr['is_audit_pass'])) {
            $cancelSpecial['isAuditPass'] = boolval($cancelSpecialArr['is_audit_pass']);
        }
        if (isset($cancelSpecialArr['cancel_audit_remark'])) {
            $cancelSpecial['cancelAuditRemark'] = strval($cancelSpecialArr['cancel_audit_remark']);
        }
        if (isset($cancelSpecialArr['is_package_sub'])) {
            $cancelSpecial['isPackageSub'] = boolval($cancelSpecialArr['is_package_sub']);
        }
        if (isset($cancelSpecialArr['is_group_order'])) {
            $cancelSpecial['isOrderGroup'] = boolval($cancelSpecialArr['is_group_order']);
        }

        try {
            $lib       = new \Business\JavaApi\Order\ScenicRefund();
            $handleRes = $lib->revokeRefund($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber,
                $cancelRemark, $terminal, $personIndex, $personIdList, $ticketCodeList, $cancelSpecial);

            if ($handleRes['code'] == 200) {
                //数据处理后返回
                $resData = $handleRes['data'];
                $data    = [
                    'pft_serial_number' => $resData['pftSerialNumber'],
                    'req_serial_number' => $resData['reqSerialNumber'],
                    'order_status'      => $resData['orderStatus'],
                    'cancel_num'        => $resData['cancelNum'],
                    'left_num'          => $resData['leftNum'],
                    'valid_num'         => $resData['validNum'],
                    'valid_money'       => $resData['validMoney'],
                    'op_id'             => $resData['opId'],
                    'is_audit'          => $resData['isAudit'],
                    'refund_amount'     => $resData['refundAmount'] ?? null,
                    'refund_fee'        => $resData['refundFee'] ?? null,
                ];

                return $this->returnData(200, '处理成功', $data);
            } else {
                //之前代码里面需要的参数
                $errorMsg = $handleRes['msg'];
                $errCode  = $handleRes['code'];
                $innerMsg = $handleRes['inner_msg'] ?? $errorMsg;

                return $this->returnData(0, $errorMsg, ['err_code' => $errCode, 'inner_msg' => $innerMsg]);
            }
        } catch (\Exception $e) {
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            return $this->returnData(0, '系统异常，请稍后再试', ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * 支付回调 -外部支付，包括在线支付和线下支付
     * <AUTHOR>
     * @date 2022/08/05
     *
     * @param  array  $payParamArr
     *  {
     *      ordernum => '',//平台订单号
     *      tradeNo => '',//第三方订单号
     *      sourceT => '',//支付渠道,pft_alipay_rec.sourceT
     *      totalFee => '',//总金额 - 单位分
     *      payToPft => '',//是否票付通收款
     *      options => { //支付选项
     *          buyer_info => ''//买家信息
     *          sell_info => ''//卖家信息
     *          pay_channel => ''//支付来源,订单追踪表的source字段
     *          pay_termianl => ''//支付的终端号
     *          oper => ''//操作人id,
     *          subOpId => '子商户操作人id'
     *      }
     *  }
     *
     * @return mixed|void
     */
    public function payCallback(array $payParamArr)
    {
        $ordernum   = strval($payParamArr['ordernum']);
        $tradeNo    = strval($payParamArr['tradeNo']);
        $sourceT    = intval($payParamArr['sourceT']);
        $totalMoney = intval($payParamArr['totalFee']);
        $isPayToPft = boolval($payParamArr['payToPft']);
        $optionsArr = $payParamArr['options'] ?? [];

        $paymentSerialNumber = '';
        $payChannel          = intval($optionsArr['pay_channel'] ?? 0);
        $buyerInfo           = strval($optionsArr['buyer_info'] ?? '');
        $sellerInfo          = strval($optionsArr['sell_info'] ?? '');
        $payTermianl         = intval($optionsArr['pay_termianl'] ?? 0);
        $opId                = intval($optionsArr['oper'] ?? 0);

        $isNeedVerify        = boolval($optionsArr['verify'] ?? false);
        $payId               = strval($optionsArr['payId'] ?? '');
        $enableProfitSharing = boolval($optionsArr['enableProfitSharing'] ?? false);

        //pay_source有些有传，有些没有传
        $paySource = isset($optionsArr['pay_source']) ? intval($optionsArr['pay_source']) : null;

        //接入收银台的下单渠道跳过
        $isIgnorePayRecord =  boolval($optionsArr['ignore_alipay_rec'] ?? false);

        //增加子商户操作人
        $extData = [];
        if (isset($optionsArr['subOpId'])) {
            $extData['subOpId'] = intval($optionsArr['subOpId']);
        }

        //如果绑定上游，则使用上游的下单服务，需要切换api域名
        $orderServiceApi = $this->getOrderServiceApi($payParamArr);

        $lib       = new \Business\JavaApi\Order\ScenicPayCallback();
        $lib->switchOrderServiceApi($orderServiceApi);//切换api域名
        $handleRes = $lib->payCallBack($ordernum, $paymentSerialNumber, $tradeNo, $sourceT, $totalMoney, $isPayToPft,
            $payChannel, $payTermianl, $paySource, $isNeedVerify, $payId, $enableProfitSharing, $buyerInfo, $sellerInfo,
            $opId, $isIgnorePayRecord, $extData);

        if ($handleRes['code'] == 200) {
            //数据处理后返回
            return $this->returnData(200, '处理成功');
        } else if ($handleRes['code'] == 1000) {
            //业务正在处理中
            return $this->returnData(205, '业务正在处理中');

        } else if($handleRes['code'] == 50510) {
            //采购上游失败，这个特殊处理，这种情况下不进行后续的原路退回退款
            //现在是上游那边直接发起退票，由退票那边去发起退款，这边就先不处理

            return $this->returnData(206, '第三方下单失败');
        } else {
            //之前代码里面需要的参数
            //TODO 可以和产品确认下，什么错误的情况下需要进行原路退回
            //TODO 可能是支付错误，可能是订单状态修改失败，可能是联票中的一个子票订单支付失败
            $isTradeFail = 1;
            $errorMsg    = $handleRes['msg'];

            return $this->returnData(102, $errorMsg, ['is_trade_fail' => $isTradeFail]);
        }
    }

    /**
     * 景区门票产品线支付回调业务接口 - 内部订单支付
     * @auther dwer.cn
     * @date 2022/08/26
     *
     * @param $payParamArr
     *
     * @return array
     */
    public function insidePayCallback($payParamArr)
    {
        $ordernum   = strval($payParamArr['ordernum']);
        $payChannel = intval($payParamArr['payChannel'] ?? 0);
        $payOpId    = intval($payParamArr['payOpId'] ?? 0);

        //如果绑定上游，则使用上游的下单服务，需要切换api域名
        $orderServiceApi = $this->getOrderServiceApi($payParamArr);

        $lib       = new \Business\JavaApi\Order\ScenicPayCallback();
        $lib->switchOrderServiceApi($orderServiceApi);//切换api域名
        $handleRes = $lib->insidePay($ordernum, $payChannel, $payOpId);

        if ($handleRes['code'] == 200) {
            //数据处理后返回
            return $this->returnData(200, '处理成功');
        } else if ($handleRes['code'] == 1000) {
            //业务正在处理中
            return $this->returnData(123, '业务正在处理中');
        } else {
            //之前代码里面需要的参数
            $errorMsg = $handleRes['msg'];

            return $this->returnData(102, $errorMsg);
        }
    }

    /**
     * 处理身份证新旧客户端兼容问题
     * @Author: dwer.cn
     * 2022-06-27
     *
     * @param $contactArr
     * @param $idcardArr
     *
     * @return array
     */
    private function _handleIdCardData($contactArr, $idcardArr)
    {
        // 兼容旧版客户端身份证填写方式  (没有身份类型，但是有身份证数组，并且不是从平台或新版微商城下单的  这种就认为他是旧版)
        //取票人没有类型,身份信息也没有类型都默认是身份证
        if (empty($contactArr['voucher_type']) && !empty($contactArr) && !empty($idcardArr) && empty($idcardArr[0]['voucher_type'])) {
            if (empty($idcardArr[0]['idcard'])) {
                return ['contact_arr' => $contactArr, 'idcard_arr' => $idcardArr];
            }
            $contactArr['personid']     = $idcardArr[0]['idcard'];
            $contactArr['voucher_type'] = 1;
            // 旧的客户端，游客身份信息类型都默认是身份证
            foreach ($idcardArr as &$item) {
                $item['voucher_type'] = 1;
            }

            //取票人没有类型,身份信息有类型,默认取身份信息第一位类型和信息
        } elseif (empty($contactArr['voucher_type']) && !empty($contactArr) && !empty($idcardArr) && !empty($idcardArr[0]['voucher_type'])) {
            $contactArr['personid']     = $idcardArr[0]['idcard'];
            $contactArr['voucher_type'] = $idcardArr[0]['voucher_type'];
        }

        return ['contact_arr' => $contactArr, 'idcard_arr' => $idcardArr];
    }

    /**
     * 处理身份证新旧客户端兼容问题
     *  联系人实名制： 如果联系人没有传证件类型字段就按原来的逻辑，如果是有传了vouch_type字段就不走原来赋值的逻辑
     * @Author: dwer.cn
     * 2022-06-27
     *
     * @param $contactArr
     * @param $idcardArr
     *
     * @return array
     */
    private function _handleIdCardDataNew($contactArr, $idcardArr)
    {
        // 兼容旧版客户端身份证填写方式  (没有身份类型，但是有身份证数组，并且不是从平台或新版微商城下单的  这种就认为他是旧版)
        //取票人没有类型,身份信息也没有类型都默认是身份证
        $isVoucherTypeFieldExist = array_key_exists('voucher_type', $contactArr);

        if (!$isVoucherTypeFieldExist && !empty($contactArr) && !empty($idcardArr) && !isset($idcardArr[0]['voucher_type'])) {
            if (empty($idcardArr[0]['idcard'])) {
                return ['contact_arr' => $contactArr, 'idcard_arr' => $idcardArr];
            }
            $contactArr['personid']     = $idcardArr[0]['idcard'];
            $contactArr['voucher_type'] = 1;
            // 旧的客户端，游客身份信息类型都默认是身份证
            foreach ($idcardArr as &$item) {
                $item['voucher_type'] = 1;
            }

            //取票人没有类型,身份信息有类型,默认取身份信息第一位类型和信息
        } elseif (!$isVoucherTypeFieldExist && !empty($contactArr) && !empty($idcardArr) && isset($idcardArr[0]['voucher_type'])) {
            $contactArr['personid']     = $idcardArr[0]['idcard'];
            $contactArr['voucher_type'] = $idcardArr[0]['voucher_type'];
        }

        return ['contact_arr' => $contactArr, 'idcard_arr' => $idcardArr];
    }

    /**
     * 获取下单服务
     * <AUTHOR>
     * @date   2024/04/10
     *
     * @param array $orderParams
     *
     * @return string
     */
    private function getOrderServiceApi($orderParams)
    {
        //判断是门票是否绑定上游
        $isBindThird = false;

        //临时对供应商id做下判断，白名单或者是否全部开启
        $upstreamAccountConfig = load_config('config', 'tmp_upstream_account');
        if ($upstreamAccountConfig['is_all_open'] == 1 || in_array($orderParams['applyDid'] ?? 0, $upstreamAccountConfig['list'])) {
            $checkBindThird = (new \Business\JavaApi\Ticket\ThirdAttr())->isBindThird($orderParams['tid'] ?? 0);
            if (isset($checkBindThird['code']) && $checkBindThird['code'] == 200) {
                $isBindThird = is_bool($checkBindThird['data']) && $checkBindThird['data'];
            }
        }

        //为空默认走原先的下单服务
        $serviceApi = '';
        if ($isBindThird) {
            //上游下单服务
            $serviceApi = 'upstream_api';
        }

        return $serviceApi;
    }
}
