<?php
/**
 * 产品业务对象获取
 * User: xiexy
 * Date: 2021-04-07
 */

namespace Business\Order\ProductService;

use Business\Order\OrderHelper;

class ServiceObject
{
    /**
     * 获取业务对象
     * <AUTHOR>
     * @data   2021-04-07
     *
     * @param  string  $pType
     * @param  int  $tid
     * @param  int  $lid
     * @param  int  $applyDid
     * @param  array  $contactArr
     * @param $orderChannel
     *
     * @return ServiceScenic|ServiceShow|null
     */
    public static function getServiceObject(string $pType = '', int $tid = 0, int $lid = 0, int $applyDid = 0, array $contactArr = [], $orderChannel = 0)
    {
        $object = null;
        //根据产品类型获取不同的产品业务对象

        if ($pType) {
            switch ($pType) {
                //暂时先只处理演出的，  其他产品类型的后期再做扩展
                case 'H':
                    $object = new \Business\Order\ProductService\ServiceShow();
                    break;
                case 'A':
                    //现在默认都开启
                    $object = new \Business\Order\ProductService\ServiceScenic();
                    break;
                default:
                    break;
            }
        }

        return $object;
    }

    /**
     * 获取退票业务对象
     * <AUTHOR>
     * @date 2022/07/18
     *
     * @param  string  $pType  产品类型
     *
     * @return object
     */
    public static function getRefundServiceObject($pType, $cancelChannel, $refundCategory, $lid, $applyDid, $orderInfo, $refundParamArr)
    {
        $object = null;
        //根据产品类型获取不同的产品业务对象


        // $orderInfo 该数据块数据是小驼峰，小心别写错

        // ---- 已支付 ----
        if ($orderInfo['payStatus'] != 2) {
            // 购物车产生的订单，且不是购物车入口请求退单，调用购物车接口类
            if ((in_array($pType, ['A','F']))
                && OrderHelper::checkIsShoppingCarOrder($orderInfo)
                && ($refundParamArr['cancelSpecialArr']['is_scenic_line_refund'] != true)
            ) {
                $object = new \Business\Order\ProductService\ServiceShoppingCar();
            }

            return $object;
        }
        // ---- 已支付 ----


        // ---- 未支付 ----
        if ($pType) {
            switch ($pType) {
                case 'A':
                    //现在只处理未支付的逻辑，未支付的统一走新接口
                    $object = new \Business\Order\ProductService\ServiceScenic();

                    //$isEnableNew = self::_isEnableNewScenicRefundInterface($cancelChannel, $lid, $refundCategory,
                    //    $applyDid);
                    //if ($isEnableNew) {
                    //    $object = new \Business\Order\ProductService\ServiceScenic();
                    //}
                    break;
                default:
                    break;
            }
        }
        // ---- /未支付 ----

        return $object;
    }

    /**
     * 获取支付回调业务对象
     * <AUTHOR>
     * @date 2022/08/05
     *
     * @param $pType string 产品类型
     * @param $sourceT int
     * @param $lid int
     * @param $applyDid int
     *
     * @return ServiceScenic|null
     */
    public static function getPayCallbackServiceObject($pType, $sourceT, $lid, $applyDid, $ordermode, $extParams = [])
    {
        $object = null;
        //根据产品类型获取不同的产品业务对象

        if ($pType) {
            switch ($pType) {
                case 'A':
                    $isEnableNew = self::_isEnableNewScenicPayCallbackInterface($sourceT, $lid, $applyDid, $ordermode, $extParams);
                    if ($isEnableNew) {
                        $object = new \Business\Order\ProductService\ServiceScenic();
                    }
                    break;
                default:
                    break;
            }
        }

        return $object;
    }

    /**
     * 临时添加部分门票走新的支付接口
     *
     * @return bool
     */
    private static function _isEnableNewScenicPayCallbackInterface($sourceT, $lid, $applyDid, $ordermode, $extParams = [])
    {
        // //所有门票产品线的商家都放开了
        // return true;

        //临时针对一卡通支付，调用原来的接口
        if (38 == $sourceT) {
            return false;
        }
        //租赁押金业务 暂时不走中台接口
        if (isset($extParams['payBiz']) && $extParams['payBiz'] == 3) {
            return false;
        }
        //所有门票产品线的商家都放开了
        return true;
    }

    /**
     * 临时添加部分门票走新的退票接口
     *
     * @param  int  $tid
     *
     * @return bool
     */
    private static function _isEnableNewScenicRefundInterface($cancelChanel, $lid, $refundCategory, $applyDid)
    {
        //只有生产/灰度环境走配置，其他环境都直接放开
        if (defined('ENV') && ENV == 'PRODUCTION') {
            $lidArrConf         = load_config('refund_lid_arr', 'enable_new_scenic_interface');
            $applyDidArrConf    = load_config('refund_apply_did_arr', 'enable_new_scenic_interface');
            $openChannelConf    = load_config('refund_open_channel', 'enable_new_scenic_interface');
            $refundCategoryConf = load_config('refund_category', 'enable_new_scenic_interface');

            if ($refundCategoryConf && in_array($refundCategory, $refundCategoryConf)) {
                if ($openChannelConf && in_array($cancelChanel, $openChannelConf)) {
                    if (is_array($lidArrConf) && in_array($lid, $lidArrConf)) {
                        return true;
                    } elseif (is_array($applyDidArrConf) && in_array($applyDid, $applyDidArrConf)) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            //其他环境默认开启
            return true;
        }
    }
}
