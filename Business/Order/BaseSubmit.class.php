<?php
/**
 * 平台下单方法基础封装层
 *
 * <AUTHOR>
 * @date 2019-02-15
 *
 */

namespace Business\Order;

use Business\Admin\ModuleConfig;
use Business\AgreementTicket\Storage;
use Business\Base;
use Business\JavaApi\Order\Order as OrderApi;
use Business\JavaApi\StorageApi;
use Business\Member\Member;
use Business\Order\ProductService\ServiceShow;
use Business\PackTicket\PackConst;
use Business\PackTicket\Traits\PackageAbnormalOrderTrait;
use Business\Product\Ticket as TicektBiz;
use Business\SmsNotice\OrderExpireNotice;
use Exception;
use Library\Constants\Discount\DiscountType;
use Library\Constants\Order\OrderChannel;
use Library\Constants\OrderConst;
use Library\Container;
use Library\JsonRpc\PftRpcClient;
use Library\Resque\Queue;
use Library\Tools\YarClient;
use Library\UnionCode\UnionCodeUtil;
use Library\Util\QConfUtil;
use Model\Order\OrderSubmit as OrderSubmitModel;
use Model\Ota\OtaQueryModel;
use Business\ResourceCenter\Order as ResourceCenterOrder;
use Business\Order\ProductService\ServiceObject;
use Business\Order\ProductService\ServiceContext;

class BaseSubmit extends Base
{
    use PackageAbnormalOrderTrait;
    //禁止下单的渠道
    private $_banChannel = [];

    //外部码下单
    private $_externalCodeOrder = false;
    //外部码业务层
    private $_externalCodeBiz;

    //协议票下单标识
    private $_agreementTicketOrder = 0;

    //协议票业务层
    private $_agreementTicketBiz;

    //订单过期前通知游客标识 0不通知 >0提前天数
    private $_noticeBeforeExpireDays = 0;

    /**
     * 通用的下单接口，包含了内部下单、支付、和往第三方系统下单
     * <AUTHOR>
     * @data   2019-03-21
     *
     * @param  int  $memberId  购买用户ID
     * @param  int  $aid  供应商ID
     * @param  int  $tid  门票ID
     * @param  int  $tnum  购买数量
     * @param  date  $playdate  开始游玩时间：2019-02-26
     * @param  int  $orderChannel  购买渠道:
     * @param  int  $paymode  支付方式：0=账户余额，1=在线支付，2=授信支付, 3=自供自销, 4=现场支付
     * @param  bool  $isNeedInsidePay  是否需要进行内部支付（美团订单是采用授信或是余额支付的，但是支付和下单的流程是分开的）
     * @param  array  $saleSettingArr  售价信息相关数组
     *                  {
     *                      'is_sale' : true, //是否散客购买
     *                      'upper_supplier' : 0, //更上一级的供应商，如果末级散客没有设置分销链的时候就有这个参数
     *                      'discount_list' ： [] //各种优惠策略数组，包括优惠券、茉莉积分、会员卡优惠之类的
     *                  }
     * @param  array  $contactArr  联系人信息数组
     *                  {
     *                      'ordername' ： '张三', //取票人姓名
     *                      'ordertel' ： '15039802110', //取票人手机
     *                      'is_sms' ： true, //是否需要发短信
     *                      'contacttel' : '15039802112', //联系人手机
     *                      'callback_url' ： "url地址" // OTA下单的时候会带核销和取消的回调地址
     *                      'personid' : 'xxxxxxx',   // 身份信息
     *                      'voucher_type' : 1,    // 身份信息类型  1,身份证 2,护照 3,军官证 4,回乡证 5,台胞证 99其他
     *                      'is_not_send_sms': 1,  //是否不发送短信 0=否 1=是
     *                  }
     * @param  string  $remotenum  远程订单号
     * @param  array  $idcardArr  购买用户身份证数组
     *                  [{
     *                      'name' : '张三丰', //游客姓名
     *                      'idcard' ： '350421187790238765' //身份信息号码
     *                      'voucher_type' : 1 // 身份信息类型  1,身份证 2,护照 3,军官证 4,回乡证 5,台胞证 99其他
     *                      'mobile' : '18159425412'
     *                  },{}]
     * @param  array  $linkOrderInfoArr  关联订单信息数组，比如联票主票，套票主票，统一凭证码等
     *                  {
     *                      'link_type' : 'common' //关联订单类型  common=正常订单, package_son=套票子票, link_son=联票子票, link=联票主票, package=套票主票
     *                      'parent_ordernum' : '54567623', //主票订单号
     *                      'valid_period_arr' : [ //以套票主票有效期为主数组
     *                          'is_use_main' : true, //是否使用主票有效期
     *                          'begin_time' : '2019-07-23',  //有效期开始时间
     *                          'end_time' : '2019-07-24',    //有效期结束时间
     *                      ]
     *                  }
     * @param  array  $saleSiteArr  购票网点的信息数组
     *                  {
     *                      'site_id' : 111344, //站点Id
     *                      'terminal' : 18877, //购票时候的终端号
     *                      'stuff_id' : 1222221, //购票网点售票员ID
     *                      'client_ip' : '**************', //购票端的ip
     *                  }
     * @param  array  $remarkArr  购票详情等信息数组
     *                  {
     *                      'memo' : '西大门进入', //订单备注信息
     *                      'origin' : '14|362',//客源地数据
     *                      'serial_number' : 'SAA20189023', //线下自填的流水号
     *                  }
     * @param  array  $showInfoArr  演出相关数据数组
     *                  {
     *                      'venue_id' : 112, //演出场馆ID
     *                      'round_id' : 9998, //演出场次ID
     *                      'area_id' : 221, //演出分区ID
     *                      'seat_ids': '9998,9997,9996', //所选座位 - 自助选座的情况
     *                      'link_seat_mark' : '', //合并付款订单连座标识
     *                      'sub_area_id' :  3334,// 小分区ID
     *                      "link_seat_mark":"show:13947:15591275126965|create|1"
     *                      'childTicketShowInfoArr' : {
     *                          {"180750":{
     *                              "showInfoArr":{"tid":180750,"venue_id":"42","round_id":"13947","area_id":"197",
     *                                      "show_begin_time":"2019-05-29 20:00","bind_num":"1","main_tid":"180749",
     *                                      "link_seat_mark":"show:13947:15591275126965|select|1"
     *                          }
     *                      }
     *                  }
     * @param  array  $specialSettingArr  特性票类的信息数组
     *                  {
     *                      'is_verify_special' : true, //true=通过,不再进行特殊票种判断，false=未通过，继续特殊票种判断
     *                  }
     * @param  array  $orderExtensionParams  下单扩展字段
     *                  {
     *                      'checkIdCard' : 0, //是否检测下单身份证 0：否 1：是
     *                      'book_advance' : 1, //是否跳过购票需要提前预定前置校验 1：跳过 2：不跳过(仅团单)
     *                  }
     * @return [
     *    'code' : 200,
     *    'msg' : '下单成功',
     *    'data' : [
     *        'ordernum' : '24377875', //订单号
     *        'remotenum' : 'remote_24377875', //远程订单号
     *        'tid' ： 1111,//门票ID
     *        'lid' ： 1111,//景区ID
     *        'pid' ： 1111,//产品ID
     *        'tnum' ： 1111,//购买数量
     *        'tprice' ： 1111,//门票单价
     *        'totalmoney' ： 25000, //总金额 - 分
     *        'counter_price' : 50000,//门市价 - 分
     *        'retail_price' : 50000,//零售价 - 分
     *        'code' : '222398',//凭证码
     *        'paymode' : 1,//支付方式
     *        'round_info' : '座位分区:B区,座位号:3-5_3-4_3-3',//演出座位信息
     *        'is_pay' : false,//是否已经支付
     *        'is_sms' : false,//是否票付通发短信
     *        'is_ordered' : false,//是否已经通过远端订单号下过订单进来了
     *    ],
     * ]
     */
    public function commonSubmit($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode,
        $isNeedInsidePay = true, array $saleSettingArr = [], array $contactArr = [], $remotenum = '',
        array $idcardArr = [], $linkOrderInfoArr = [], array $saleSiteArr = [], array $remarkArr = [],
        array $showInfoArr = [], array $specialSettingArr = [], array $orderExtensionParams = [])
    {
        // return $this->orderReturn(0, '系统升级,服务暂停', ['err_code' => 12301, 'inner_msg' => '系统升级,服务暂停']);

        if (in_array($orderChannel, $this->_banChannel)) {
            $innerMsg = '当前系统负载过高,请稍后再试';

            return $this->orderReturn(0, $innerMsg, ['err_code' => OrderConst::err20, 'inner_msg' => $innerMsg]);
        }

        try {
            //下单依赖队列
            Queue::ping();
        } catch (\Exception $e) {
            $innerMsg = 'redis队列服务ping异常';

            return $this->orderReturn(0, $innerMsg, ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }

        //通过门票id获取下产品类型
        //$pType = $this->getPtypeByTid($tid);
        $landInfo = $this->getLandInfoByTid($tid);

        if (!$landInfo) {
            $innerMsg = '产品类型获取失败';

            return $this->orderReturn(0, $innerMsg, ['err_code' => OrderConst::err6, 'inner_msg' => $innerMsg]);
        }

        $pType    = $landInfo['p_type'];
        $lid      = $landInfo['lid'];
        $applyDid = $landInfo['apply_did'];
        //判断是否使用优惠，以及拓展字段是否有优惠信息 discountDetail，若无走校验接口获取
        if(!isset($orderExtensionParams['discountDetail']) && ($saleSettingArr['use_point'] || $saleSettingArr['use_coupon'] || $saleSettingArr['use_discount'] || $saleSettingArr['useMarketingDiscount'])) {
            $opId         = $saleSettingArr['op_id'] ?? 0;
            $ticketList[] = [
                'play_date'                    => $playdate,
                'pid'                          => $opId,
                'tid'                          => $tid,
                'aid'                          => $aid,
                'tnum'                         => $tnum,
                'upper_supplier'               => $saleSettingArr['upper_supplier'],
                'points'                       => $saleSettingArr['points'],
                'pointsAmount'                 => $saleSettingArr['pointsAmount'],
                'couponAmount'                 => $saleSettingArr['couponAmount'],
                'use_point'                    => $saleSettingArr['use_point'],
                'use_coupon'                   => $saleSettingArr['use_coupon'],
                'use_discount'                 => $saleSettingArr['use_discount'],
                'ticket_price'                 => $saleSettingArr['ticket_price'],
                'ticket_member_discount_price' => $saleSettingArr['ticket_member_discount_price'],
                'useMarketingDiscount'         => $saleSettingArr['useMarketingDiscount'] ?? false,
                'discountInfo'                 => $saleSettingArr['discountInfo'] ?? new \stdClass(),
            ];

            $pointsApi = new \Business\JsonRpcApi\MarketCenterService\pointsApi();
            $checkRes  = $pointsApi->integralOrderPreCheck($memberId, $opId, $saleSettingArr['is_sale'], $orderChannel,
                $paymode, $saleSettingArr['use_point'], $saleSettingArr['use_coupon'], $saleSettingArr['use_discount'],
                $ticketList, $saleSettingArr['coupon_list'], $saleSettingArr['useMarketingDiscount'] ?? false);
            if ($checkRes['code'] != 200) {
                return $checkRes;
            }
            else{
                $orderExtensionParams['op_id'] = $opId;
                $res = reset($checkRes['data']['ticketList']);
                $orderExtensionParams['discountDetail'] = $res['discountDetail'];
                if(isset($res['useMarketingDiscount'])){
                    $saleSettingArr['useMarketingDiscount'] = $res['useMarketingDiscount'];
                }
                foreach ($saleSettingArr as $keyField => $valueField){
                    if(in_array($keyField,DiscountType::DISCOUNT_ORDER_MAP) && !array_key_exists($keyField,$res)){
                        unset($saleSettingArr[$keyField]);
                    }
                }
            }
        }

        //获取业务对象
        $orderObject = ServiceObject::getServiceObject($pType, $tid, $lid, $applyDid, $contactArr, $orderChannel);
        if (empty($orderObject)) {
            //JAVA版本下单
            $res = $this->_newCommonSubmit($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode,
                $isNeedInsidePay, $saleSettingArr, $contactArr, $remotenum, $idcardArr, $linkOrderInfoArr,
                $saleSiteArr, $remarkArr, $showInfoArr, $specialSettingArr, $orderExtensionParams);

        } else {
            $orderParams = [
                'memberId'             => $memberId,
                'aid'                  => $aid,
                'tid'                  => $tid,
                'tnum'                 => $tnum,
                'playdate'             => $playdate,
                'orderChannel'         => $orderChannel,
                'paymode'              => $paymode,
                'isNeedInsidePay'      => $isNeedInsidePay,
                'saleSettingArr'       => $saleSettingArr,
                'contactArr'           => $contactArr,
                'remotenum'            => $remotenum,
                'idcardArr'            => $idcardArr,
                'linkOrderInfoArr'     => $linkOrderInfoArr,
                'saleSiteArr'          => $saleSiteArr,
                'remarkArr'            => $remarkArr,
                'showInfoArr'          => $showInfoArr,
                'specialSettingArr'    => $specialSettingArr,
                'orderExtensionParams' => $orderExtensionParams,
                'applyDid'             => $applyDid
            ];
            $res         = (new ServiceContext($orderObject))->commonOrder($orderParams);
        }
        if($res['code']!=200){
            // 子下单异常进入异常订单记录
            if(isset($linkOrderInfoArr['parent_ordernum']) && !empty($linkOrderInfoArr['parent_ordernum'])){
                $mainOrderNum = $linkOrderInfoArr['parent_ordernum'];
                $infoMsg =  "子票tid:{$tid}_下单失败:".$res['msg']."，进入异常订单" ;
                $this->recordAnAbnormal($mainOrderNum,json_encode(func_get_args()),json_encode($res,JSON_UNESCAPED_UNICODE),
                    PackConst::ABNORMAL_PROCESS_ORDER,PackConst::ABNORMAL_REASON_OTHER,PackConst::ABNORMAL_LINKAGE_YES,$infoMsg);
            }
        }
        return $res;
    }

    /**
     * 切换到JAVA版本
     * <AUTHOR>
     * @date   2019-05-15
     *
     * @return []
     */
    private function _newCommonSubmit($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode, $isNeedInsidePay = true, array $saleSettingArr = [], array $contactArr = [], $remotenum = '', array $idcardArr = [], $linkOrderInfoArr = [], array $saleSiteArr = [], array $remarkArr = [], array $showInfoArr = [], array $specialSettingArr = [], array $orderExtensionParams = [])
    {
        try {
            //开始时间
            $orderStartTime = microtime(true);

            //基础的支付参数处理
            $orderCheckBiz = $this->_getCheckBiz();
            $paymodeRes    = $orderCheckBiz->handleInsidePay($paymode, $isNeedInsidePay);
            if ($paymodeRes['code'] != 200) {
                return $paymodeRes;
            }

            // 处理下身份证兼容
            $handleRes  = $this->_handleIdCardData($contactArr, $idcardArr, $orderChannel);
            $contactArr = $handleRes['contact_arr'];
            $idcardArr  = $handleRes['idcard_arr'];

            //内部的支付方式和是否需要进行内部支付
            $paymodeData         = $paymodeRes['data'];
            $insidePaymode       = $paymodeData['insidePaymode'];
            $isLastNeedInsidePay = $paymodeData['isNeedInsidePay'];

            ////下单之前先确认下第三方系统是否存活，如果系统已经下线了直接就不进入下单环节了
            //$offlineRes = $this->_preHandleThirdOrder($memberId, $aid, $tid);
            //if ($offlineRes['code'] != 200) {
            //    return $offlineRes;
            //}

            //内部下单
            $orderRes = $this->newInsideOrder($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $insidePaymode,
                $isLastNeedInsidePay, $saleSettingArr, $contactArr, $remotenum, $idcardArr, $linkOrderInfoArr,
                $saleSiteArr, $remarkArr, $showInfoArr, $specialSettingArr, $orderExtensionParams);

            //内部下单结束时间
            $orderEndtime = microtime(true);

            $this->_orderTimeDeal($orderRes, $orderStartTime, $orderEndtime);

            //内部下单失败，直接返回
            if ($orderRes['code'] != 200) {
                //如果是演出订单的话，需要回滚演出的座位
                if (isset($orderRes['data']['show_info'])) {
                    $this->_asyncRollbackOrder('rollback_' . time(), $memberId, 'showed',
                        $orderRes['data']['show_info']);
                }

                //下单失败需要释放之前获取的第三方消费码
                if (isset($orderRes['data']['external_code_data'])) {
                    $this->_orderFailReleaseExternalCode($orderRes['data']['external_code_data']);
                }

                //下单失败后释放协议票冻结库存
                if (isset($orderRes['data']['agreement_ticket_data'])) {
                    $agreementStoreBiz = new Storage();
                    $agreementStoreBiz->unfreezeStorage($orderRes['data']['agreement_ticket_data'],
                        $orderRes['data']['agreement_ticket_tnum']);
                }

                return $orderRes;
            }

            //平台订单号
            $orderResData   = $orderRes['data'];
            $orderType      = $orderResData['order_type'];
            $ordernum       = $orderResData['ordernum'];
            $oneLevelSeller = $orderResData['one_level_seller'];
            $remotenum      = $orderResData['remotenum'];
            $tid            = $orderResData['tid'];
            $lid            = $orderResData['lid'];
            $pid            = $orderResData['pid'];
            $ptype          = $orderResData['ptype'];
            $tnum           = $orderResData['tnum'];
            $tprice         = $orderResData['tprice'];
            $totalmoney     = $orderResData['totalmoney'];
            $counterPrice   = $orderResData['counter_price'];
            $costPrice      = $orderResData['cost_price'];
            $retailPrice    = $orderResData['retail_price'];
            $windowPrice    = $orderResData['window_price'];
            $code           = $orderResData['code'];
            $vcode          = $orderResData['vcode'];
            $showInfo       = $orderResData['show_info'];
            $resTicketInfo  = $orderResData['res_ticket_info'];
            $resTouristList = $orderResData['res_tourist_list'];
            $resTrackList   = $orderResData['res_track_list'];

            //如果是有远端订单，获取已经下的订单信息，就有可能是已经支付的
            $isPay           = $orderResData['is_pay'];
            $isNeedSendSms   = $this->_externalCodeOrder ? true : $orderResData['is_need_send_sms']; //是否需要入短信队列
            $smsSendTimes    = $orderResData['sms_send_times']; //实际的的短信发送次数
            $ordername       = $orderResData['ordername'];
            $ordertel        = $orderResData['ordertel'];
            $mobileArea      = $orderResData['mobile_area'];
            $mobileRegion    = $orderResData['mobile_region'];
            $personId        = $orderResData['personid'];
            $voucherType     = $orderResData['voucher_type'];
            $payStatus       = $orderResData['pay_status'];
            $begintime       = $orderResData['begintime'];
            $endtime         = $orderResData['endtime'];
            $orderMember     = $orderResData['member'];
            $orderAid        = $orderResData['aid'];
            $orderApplyDid   = $orderResData['apply_did'];
            $timeCardDeposit = $orderResData['time_card_deposit'];
            $realPaymode     = $orderResData['paymode'];

            //为了兼容之前的逻辑，这边如果是0元订单也直接进行支付
            //TODO: 对于在线支付的，后面需要通过单独的支付请求来完成支付的动作，而不应该糅合在下单接口里面
            if ($isNeedInsidePay) {
                //外部需要进行判断的情况下，如果0元的订单的话，需要进行支付
                if ($totalmoney == 0) {
                    if (in_array($orderChannel, [10, 12, 14, 15])) {
                        //终端渠道的0元先改成现金支付
                        $paymode = 9;
                    }
                    $isLastNeedInsidePay = true;
                }
            } else {
                //如果是外部传不需要进行支付，就不需要进行0元订单自动支付了
            }
            //套票主票下单是否有勾选 特殊库存(库存为0时可预约) ：1是 2否
            $childrenSpecialSettingArr = [
                'parent_contact_info_more' => [
                    'mobileArea'             => $mobileArea,
                    'mobileRegion'           => $mobileRegion,
                ]
            ];
            if (isset($specialSettingArr['moreStorage'])) {
                $childrenSpecialSettingArr['moreStorage'] = $specialSettingArr['moreStorage'] ?? 2;
            }
            //是新订单还是已经下进来过的订单
            $isOrdered = $orderType == 'old' ? true : false;

            //支付需要用的的数据
            $siteId   = $orderResData['site_id'];
            $terminal = $orderResData['terminal'];
            $stuffId  = $orderResData['stuff_id'];

            //默认返回平台的凭证码
            $resCode = $code;

            if (!$isOrdered) {
                //非重复下单的情况
                if ($isPay) {
                    //已经支付的情况下
                    if ($resTicketInfo['mdetails'] == 1 && $resTicketInfo['pType'] != 'H') {
                        //如果是第一次下单，还需要判断是否需要往三方系统去下单

                        //演出的产品的Mdetails也是=1，所以需要进行排除掉
                        //这边因为程序存在sourceT被情况的bug，暂时这样去判断
                        $thirdOrderRes = $this->_handleThirdSystemOrderV2($orderResData, $resTicketInfo,
                            $resTouristList, $personId, $voucherType, $ordername, $orderChannel);
                        // code=200 下单成功， code=598 三方出票中
                        if ($thirdOrderRes['code'] != 200 && $thirdOrderRes['code'] != OrderConst::err598) {
                            //如果下单失败的话, 直接返回错误信息
                            //然后通过队列将下单数据和支付数据回滚
                            $this->_asyncRollbackOrder($ordernum, $memberId, 'payed', $showInfo, $remotenum,
                                $thirdOrderRes['msg']);

                            return $thirdOrderRes;
                        }
                    }

                    //第三方消费码订单支付后的处理
                    if ($orderResData['is_external_code']) {
                        $codeOrderData['mainOrder']['ordernum'] = $ordernum;
                        $this->_externalCodeBiz->asyncPaySuccessTask($codeOrderData);
                        //$this->_addExternalCodeOrderPaySuccessTask($ordernum);
                    }

                }

                //演出产品下单成功后，将订单和座位关联
                $this->_relatedShow($ordernum, $tnum, $isPay, $ptype, $oneLevelSeller, $memberId, $showInfo);

                //子票演出信息
                $childTicketShowInfoArr = empty($showInfoArr['childTicketShowInfoArr']) ? [] : $showInfoArr['childTicketShowInfoArr'];

                //支付成功之后发送短信
                $this->_addQueueTask($ordernum, $isNeedSendSms, $playdate, $tnum, $isPay, $payStatus, $memberId, $aid,
                    $tid, $pid, $ptype, $ordername, $ordertel, $childTicketShowInfoArr, $resTrackList,
                    $childrenSpecialSettingArr);

            } else {
                //如果是再次下单的话
                if (in_array($resTicketInfo['sourcet'], [2, 3])) {
                    //有同步返回消费码, UUcode = 第三方返回的消费码； 如果没有返回消费码，UUcode=‘’ ，为空，不能传0
                    if ($vcode) {
                        $resCode = strval($vcode);
                    } else {
                        $resCode = '';
                    }
                }
            }

            //返回标准化的订单数据
            $orderData = [
                'ordernum'          => $ordernum,
                'remotenum'         => $remotenum,
                'tid'               => $tid,
                'lid'               => $lid,
                'pid'               => $pid,
                'tnum'              => $tnum,
                'tprice'            => $tprice,
                'totalmoney'        => $totalmoney,
                'cost_price'        => $costPrice,
                'counter_price'     => $counterPrice,
                'retail_price'      => $retailPrice,
                'code'              => $resCode,
                'paymode'           => $paymode,
                'round_info'        => isset($showInfo['round_info']) ? $showInfo['round_info'] : '',
                'is_pay'            => $isPay,
                'is_sms'            => $smsSendTimes > 0 ? true : false,
                'is_ordered'        => $isOrdered, //是否通过远程订单号已经下过订单了
                'mdetails'          => $resTicketInfo['mdetails'] ?? '',
                'sourcet'           => $resTicketInfo['sourcet'] ?? '',
                'member'            => $orderMember,
                'aid'               => $orderAid,
                'apply_did'         => $orderApplyDid,
                'time_card_deposit' => $timeCardDeposit,
                'real_paymode'      => $realPaymode,
            ];

            return $this->orderReturn(200, '下单成功', $orderData);
        } catch (Exception $e) {
            //如果有抛出异常，统一返回系统异常
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            return $this->orderReturn(0, '系统异常，请稍后再试', ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * JAVA版本平台内部下单接口
     * <AUTHOR>
     * @data   2019-02-26
     *
     */
    public function newInsideOrder($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $insidePaymode, $isLastNeedInsidePay, array $saleSettingArr, array $contactArr, $remotenum = '', array $idcardArr = [], $linkOrderInfoArr = [], array $saleSiteArr = [], array $remarkArr = [], array $showInfoArr = [], array $specialSettingArr = [], array $orderExtensionParams = [])
    {
        //开始下单的时间戳
        $orderTimestamp = time();
        //参数基础校验
        $orderCheckBiz = $this->_getCheckBiz();
        $baseCheckRes  = $orderCheckBiz->baseCheck($memberId, $aid, $tid, $tnum, $playdate, $orderTimestamp,
            $orderChannel, $insidePaymode, $isLastNeedInsidePay, $saleSettingArr, $contactArr, $remotenum, $idcardArr,
            $linkOrderInfoArr, $saleSiteArr, $remarkArr, $showInfoArr, $specialSettingArr);

        //基础校验错误返回
        if ($baseCheckRes['code'] != 200) {
            return $baseCheckRes;
        }

        //校验过的下单参数
        $orderParams = $baseCheckRes['data'];

        //初步校验后的下单参数
        $memberId      = $orderParams['memberId'];
        $aid           = $orderParams['aid'];
        $tid           = $orderParams['tid'];
        $tnum          = $orderParams['tnum'];
        $playdate      = $orderParams['playdate'];
        $orderChannel  = $orderParams['orderChannel'];
        $shop          = $orderParams['shop'];
        $isSale        = $orderParams['isSale'];
        $upperSupplier = $orderParams['upperSupplier'];
        $saleChannel = $orderExtensionParams['saleChannel'];

        $paymode     = $orderParams['paymode'];
        $remotenum   = $orderParams['remotenum'];
        $ordername   = $orderParams['ordername'];
        $ordertel    = $orderParams['ordertel'];
        $personId    = $orderParams['personid'];
        $voucherType = $orderParams['voucher_type'];
        $idcardArr   = $orderParams['idcardArr'];
        $siteId      = $orderParams['siteId'];
        $terminal    = $orderParams['terminal'];
        $stuffId     = $orderParams['stuffId'];
        $clientIp    = $orderParams['clientIp'];
        $showArr     = $orderParams['showInfo'];
        // $isVerifySpecial = $orderParams['isVerifySpecial'];
        $isNeedSendSms   = $orderParams['isNeedSendSms'];
        $outLandId       = $orderParams['outLandId'];
        $linkType        = $orderParams['linkType'];
        $parentOrdernum  = $orderParams['parentOrdernum'];
        $timeCardDeposit = $orderParams['timeCardDeposit'];
		// $timingDeviceId = $orderParams['timing_device_id'];

        //获取产品信息
        $ticketBiz = new TicektBiz();
        $ticketRes = $ticketBiz->getListForOrderSimple($tid);

        if (!$ticketRes['ticket_info'] || !$ticketRes['ticket_ext_info'] || !$ticketRes['land_info'] || !$ticketRes['third_attr_info']) {
            return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
        }
        if (!$ticketRes['product_info']) {
            return $this->orderReturn(0, '门票已经下架', ['err_code' => OrderConst::err1066]);
        }

        // 注意：之前ticket里面的Mpath,sourceT等参数已经废弃了
        // 这些对接三方的参数现在都在thirdInfo里面
        $ticketInfo    = $ticketRes['ticket_info'];
        $ticketExtInfo = $ticketRes['ticket_ext_info'];
        $productInfo   = $ticketRes['product_info'];
        $landInfo      = $ticketRes['land_info'];
        $thirdInfo     = $ticketRes['third_attr_info'];

        //将扩展信息统一到$ticketInfo，便于后面的下单属性判断
        $ticketInfo = array_merge($ticketInfo, $ticketExtInfo);

        //产品相关数据
        $ptype          = $landInfo['p_type'];
        $applyDid       = $landInfo['apply_did'];
        $lid            = $landInfo['id'];
        $resourceId     = $landInfo['resourceID'];
        $salerId        = $landInfo['salerid'];
        $terminalType   = $landInfo['terminal_type'];
        $pid            = $productInfo['id'];
        $dstatus        = $productInfo['d_status'];
        $batchCheckType = $ticketInfo['batch_check'];

        //订单过期前通知游客标识 0不通知 >0提前天数
        $this->_noticeBeforeExpireDays = (int)$ticketInfo['notice_before_expire_days'] ?? 0;

        //下单之前先确认下第三方系统是否存活，如果系统已经下线了直接就不进入下单环节了
        $offlineRes = $this->_preHandleThirdOrder($memberId, $aid, $tid, $thirdInfo, $landInfo);
        if ($offlineRes['code'] != 200) {
            return $offlineRes;
        }

        //判断是否在自动起售停售时间内
        $ticketAutoSaleRule = !empty($ticketInfo['auto_sale_rule']) ? json_decode($ticketInfo['auto_sale_rule'],
            true) : [];
        if (!empty($ticketAutoSaleRule)) {
            $time = time();
            if (strtotime($ticketAutoSaleRule['start']) > $time || strtotime($ticketAutoSaleRule['end']) < $time) {
                return $this->orderReturn(0, '门票不在销售时间', ['err_code' => OrderConst::err1081]);
            }
            unset($time);
        }
        unset($ticketAutoSaleRule);

        //判断套票是否在自动起售停售时间内
        if ($ptype == 'F' && $ticketInfo['ticket_shelf_rule'] == 3) {
            $time = time();
            if (strtotime($ticketInfo['start_sale_datetime']) > $time || strtotime($ticketInfo['stop_sale_datetime']) < $time) {
                return $this->orderReturn(0, '门票不在销售时间', ['err_code' => OrderConst::err1081]);
            }
            unset($time);
        }

        //是否设置了发外部码
        if ($ticketInfo['is_external_send_code']) {
            $this->_externalCodeOrder = true;
            $this->_externalCodeBiz   = new \Business\ExternalCode\CodeManage();
            //下单参数增加外部发码标识
            $orderParams['externalSendCode'] = 1;
        }

        //如果外部有传这个lid过来，需要判断是不是和门票的lid匹配
        if ($outLandId) {
            if ($lid != $outLandId) {
                return $this->orderReturn(0, '',
                    ['err_code' => OrderConst::err1051, 'inner_msg' => "外部传进来的lid={$outLandId}和门票的landid不匹配"]);
            }
        }

        //获取分销链数据
        $distributeCheckRes = $orderCheckBiz->newDistributeCheck($memberId, $aid, $applyDid, $tid, $pid, $playdate,
            $isSale, $orderChannel, $upperSupplier);

        if ($distributeCheckRes['code'] != 200) {
            return $distributeCheckRes;
        }

        //分销信息
        $distributeData = $distributeCheckRes['data'];
        $aidsArr        = $distributeData['evo_arr']; //上级分销链
        $memberRelation = $distributeData['memberRelation']; //关系类型:0供销关系1从属关系2平级关系3推荐关系4资源中心关系

        //套餐和转分销应用权限限制
        $appcenterServiceAuth = $this->_appCenterServiceAuth($aidsArr, $memberId, $aid, $applyDid, $isSale,
            $orderChannel);
        if ($appcenterServiceAuth['code'] != 200) {
            return $this->orderReturn(0, $appcenterServiceAuth['msg'], ['err_code' => OrderConst::err9001]);
        }

        //判断是否是资源中心关系订单
        $isResourceOrder = $orderCheckBiz->isResourceOrder($memberRelation, $pid, $aidsArr);

        // 判断是否资源中心订单并是否存在资源中心模块
        if ($isResourceOrder) {
            $evoArr                 = implode(',', $aidsArr);
            $resourceCenterOrderBiz = new ResourceCenterOrder();
            $effectCheck            = $resourceCenterOrderBiz->orderAppModuleEffectCheck($distributeData['sid'],
                $distributeData['fid'], $pid, $evoArr);
            if (isset($effectCheck['code']) && $effectCheck['code'] != 200) {
                $tmpMsg = empty($effectCheck['data']) ? ['err_code' => ''] : $effectCheck['data'];
                $msg    = empty($effectCheck['msg']) ? '' : $effectCheck['msg'];

                return $this->orderReturn(0, $msg, $tmpMsg);
            }
        }

        //获取一级分销商
        if ($aidsArr && isset($aidsArr[1])) {
            //转分销
            $oneLevelSeller = $aidsArr[1];
        } else {
            //直销
            $oneLevelSeller = $memberId == 112 ? $aid : $memberId;
        }

        //如果是演出订单，需要先往演出系统去下单
        $showOrderRes = $this->_handlerShowOrder($memberId, $tid, $tnum, $playdate, $oneLevelSeller, $ptype, $showArr);
        if ($showOrderRes['code'] != 200) {
            return $showOrderRes;
        }

        //如果是演出的话会返回演出的数据
        //如果不是的话，直接返回空
        $showData    = $showOrderRes['data'];
        $showInfoStr = $showOrderRes['data']['series'];

        //将演出数据返回到外层
        if ($showArr) {
            $showArr = array_merge($showArr, $showData);
        }

        //如果是第三方发码的，先获取第三方码
        $externalCodeChkRes = $this->_handleExternalCodeOrder($tid, $tnum, $playdate);
        if ($externalCodeChkRes['code'] != 200) {
            return $externalCodeChkRes;
        }
        //第三方消费码数据
        $externalCodeData = $externalCodeChkRes['data'];

        //判断是否设置了协议票(景区,套票)
        $agreementTicketRes = $this->_handleAgreementTicketOrder($ticketInfo['is_agreement_ticket'], $orderChannel,
            $applyDid, $memberId, $tid, $tnum, $aidsArr);
        if ($agreementTicketRes['code'] != 200) {
            if ($this->_externalCodeOrder) {
                //如果是第三方发码订单，需要将获取到的第三方码进行释放
                $agreementDataRes['data']['external_code_data'] = $externalCodeData;
            }

            return $agreementTicketRes;
        }

        //订单凭证码暂时还由PHP这边生产
        if (in_array($linkType, ['link_son', 'package_son'])) {
            //套票和联票的子票不需要生成凭证码
            $vcode = '';
        } else {
            //正常票类需要生成凭证码
            $TstartTime = microtime(true);
            $vcode      = $this->_getVcode($lid, $ptype, $resourceId, $terminalType);
            $TendTime   = microtime(true);
            $costTime   = intval($TendTime - $TstartTime);

            pft_log('order/vcode_create', json_encode([
                'vcode'        => $vcode,
                'cost_time'    => $costTime,
                'lid'          => $lid,
                'ptype'        => $ptype,
                'resourceId'   => $resourceId,
                'terminalType' => $terminalType,
            ]));

            if (!$vcode) {
                $tmpData = ['err_code' => OrderConst::err15, 'inner_msg' => '凭证码生成失败'];

                //如果是演出订单，需要将演出座位返回进行回滚
                if ($showArr) {
                    $tmpData['show_info'] = $showArr;
                }

                //如果是协议票订单，需要将冻结库存释放
                if ($this->_agreementTicketOrder) {
                    $tmpData['agreement_ticket_data'] = $this->_agreementTicketOrder;
                    $tmpData['agreement_ticket_tnum'] = intval($tnum);
                }

                return $this->orderReturn(0, '', $tmpData);
            }
        }

        //景旅纵横动态码 0=未启用 1=身份证 2=学生证
        if ($ptype == 'F' && isset($ticketInfo['trip_card_code'])) {
            $orderExtensionParams['tripCardCode'] = $ticketInfo['trip_card_code'];
        }

        //一次独立请求的流水号
        //TODO：后期需要在调用方添加请求流水号
        $reqTransaction = '';
        $transactionId  = $this->_genTransactionId($memberId, $aid, $tid, $remotenum, $reqTransaction, $parentOrdernum,
            $linkType);

        //内部下单写数据
        $orderApi  = new OrderApi();
        $submitRes = $orderApi->insideOrder($transactionId, $orderTimestamp, $orderParams, $vcode, $showInfoStr, $orderExtensionParams);

        //下单报错
        if ($submitRes['code'] != 200) {
            //如果是演出订单，需要将演出座位返回进行回滚
            if ($showArr) {
                $submitRes['data']['show_info'] = $showArr;
            }

            //如果是第三方发码订单，需要将获取到的第三方码进行释放
            if ($this->_externalCodeOrder) {
                $submitRes['data']['external_code_data'] = $externalCodeData;
            }

            //如果是协议票订单，需要将冻结库存释放
            if ($this->_agreementTicketOrder) {
                $submitRes['data']['agreement_ticket_data'] = $this->_agreementTicketOrder;
                $submitRes['data']['agreement_ticket_tnum'] = intval($tnum);
            }

            return $submitRes;
        }

        //下单成功后的订单号和凭证码
        $orderData      = $submitRes['data']['tickets'][0];
        $orderExt       = $orderData['orderExt'];
        $resTicketInfo  = $orderData['ticketInfo'];
        $resTouristList = $orderData['touristList'] ?: [];

        //实际订单的有效期
        $begintime = $orderExt['begintime'];
        $endtime   = $orderExt['endtime'];

        //这个比较简陋，主要是为了往ES那边写数据
        //TODO:后面这些数据统一由JAVA那边写入
        $resTrackList = $orderData['trackList'] ?: [];

        $isPay = in_array($orderData['payStatus'], [0, 1]) ? true : false;

        //为了兼容之前的一些逻辑，有些数据还是需要通过队列去写入
        $this->_afterOrderTask($orderData['ordernum'], $tnum, $tid, $pid, $lid, $ptype, $playdate, $endtime,
            $orderData['tprice']
            , $ticketInfo, $orderParams['delivery_way'], $isPay, $orderParams['orderChannel'], $orderTimestamp, $saleChannel);

        //下单成功第三方消费码业务处理
        $this->_orderSuccessHandleExternalCode($orderData['ordernum'], $externalCodeData);

        //下单成功后协议票库存处理
        if ($this->_agreementTicketOrder) {
            $this->_agreementTicketBiz->deductStorage($this->_agreementTicketOrder, $orderData['ordernum'],
                (int)$orderExt['tnum'], $orderTimestamp);
        }

        //计时卡下单成功 且需要收取押金时 完善订单押金字段
        if ($ptype == 'K' && $timeCardDeposit) {
            $this->_timeCardDepositTrack($orderData['ordernum'], $aid, $aid);
        }

        //返回数据
        $orderInfo = [
            'order_type'        => $orderData['repeatedRequest'] == 1 ? 'old' : 'new', //新下的订单
            'ordernum'          => $orderData['ordernum'],
            'member'            => $memberId,
            'ordername'         => $orderExt['ordername'],
            'ordertel'          => $orderExt['ordertel'],
            'mobile_area'       => $orderParams['mobile_area'],
            'mobile_region'     => $orderParams['mobile_region'],
            'personid'          => $orderExt['personid'],
            'voucher_type'      => $orderExt['voucherType'],
            'aid'               => $orderExt['aid'],
            'one_level_seller'  => $oneLevelSeller,
            'remotenum'         => $orderExt['remotenum'],
            'tid'               => $orderData['ticketId'],
            'ticket_name'       => $ticketInfo['title'],
            'lid'               => $orderExt['lid'],
            'land_name'         => $landInfo['title'],
            'pid'               => $orderExt['pid'],
            'ptype'             => $resTicketInfo['pType'],
            'apply_did'         => $orderExt['applyDid'],
            'tnum'              => $orderExt['tnum'],
            'tprice'            => $orderData['tprice'],
            'playtime'          => $orderData['playTime'],
            'begintime'         => $orderExt['begintime'],
            'endtime'           => $orderExt['endtime'],
            'totalmoney'        => $orderData['totalmoney'],
            'cost_price'        => $orderData['costPrice'],
            'counter_price'     => $orderData['counterPrice'],
            'retail_price'      => $orderData['retailPrice'],
            'window_price'      => 0,
            'code'              => $orderData['code'],
            'vcode'             => $orderData['vcode'],
            'show_info'         => $showArr,
            'site_id'           => $siteId,
            'terminal'          => $terminal,
            'stuff_id'          => $stuffId,
            'is_pay'            => $isPay, //默认是未支付的
            'pay_status'        => $orderData['payStatus'], //未支付或是现场支付
            'is_need_send_sms'  => $isNeedSendSms, //是否需要进行发送短信队列
            'sms_send_times'    => $orderData['remsg'], //短信时间的发送次数
            'res_ticket_info'   => $resTicketInfo,
            'res_tourist_list'  => $resTouristList,
            'res_track_list'    => $resTrackList,
            'time_str'          => $orderParams['timeShareInfo']['time_str'] ?? '',
            'is_external_code'  => $this->_externalCodeOrder,   //下单是否发外部码
            'apply_did'         => $applyDid, // 原始供应商ID
            'time_card_deposit' => $timeCardDeposit, // 计时产品押金金额
            'paymode'           => $orderData['paymode'], //支付方式
        ];

        return $this->orderReturn(200, "", $orderInfo);
    }

    /**
     * 为了兼容之前的一些逻辑，有些数据还是需要通过队列去写入
     * <AUTHOR>
     * @data   2019-04-02
     *
     * @param  string  $ordernum
     * @param  int  $tnum
     * @param  int  $tid
     * @param  int  $lid
     * @param  int  $ptype
     * @param  int  $totalMoney
     * @param  array  $ticketInfo
     * @param  array  $childTicketShowInfoArr  子票演出座位号信息
     *
     * @return bool
     */
    private function _afterOrderTask($ordernum, $tnum, $tid, $pid, $lid, $ptype, $playtime, $endtime, $tprice, $ticketInfo, $deliveryWay = -1, $isPay = true, $ordermode = 0, $orderTimestamp = 0, $saleChannel = 0)
    {
        $orderParam = [
            'actions'                     => [],
            'parentOrdernum'              => $ordernum,
            'parentPid'                   => $pid,
            'lid'                         => $lid,
            'tid'                         => $tid,
            'ptype'                       => $ptype,
            'tnum'                        => $tnum,
            'playtime'                    => $playtime,
            'endtime'                     => $endtime,
            'auto_checked_after_playdate' => $ticketInfo['auto_checked_at'],
            'childTicketShowInfoArr'      => [],
            'gid'                         => $ticketInfo['gid'],
            'delivery_way'                => $deliveryWay,
            'tprice'                      => $tprice,
            '_ordermode_'                 => $ordermode,
            'orderTimestamp'              => $orderTimestamp,
            'auto_check_time_type'        => $ticketInfo['auto_check_time_type'] ?? 0,
            'saleChannel'=>$saleChannel,
        ];
        Queue::push('package_order', 'PackageOrder_Job', $orderParam);

        if (!$isPay) {
            //102为小程序-购物车渠道下单的订单，未支付超时自动取消，都由购物车处理，这里不写枚举常量，我也清楚有哪些枚举
            if ($saleChannel == 102) {
                return true;
            }
            // 报团模式未支付的订单超过一周未支付则取消---叶星提的,为报团计调下单（ordermode==44）时，超过一周后未支付自动取消
            if (in_array($ordermode, [24, 44])) {
                $at = time() + 604800; // 最新的产品经理（chenlu）要求改7天。
            } else {
                //最大只支持一周
                $setMins = $ticketInfo['cancel_auto_onmin'] > 10080 ? 1440 : $ticketInfo['cancel_auto_onmin'];
                $at      = time() + $setMins * 60;
            }
            //延迟3三分钟，以防服务器时间差
            $at += 3 * 60;
            $queueParams = [
                'ordernum'  => $ordernum,
                'tnum'      => $tnum,
                'ordermode' => $ordermode,
            ];
            Queue::delay($at, 'order', 'NonPaymentAutoCancel_Job',$queueParams);
            //pft_log('order/auto_cancel', "{$ordernum}|" . json_encode(['push到Queue::delay->NonPaymentAutoCancel_Job', 'queueParams'=>$queueParams, 'orderParams'=>$orderParam], JSON_UNESCAPED_UNICODE));
        }

        return true;
    }

    /**
     * 回滚演出座位
     * <AUTHOR>
     * @date 2019-07-20
     *
     * @param  array  $showInfo
     *
     * @return bool
     */
    public function rollbackShowSeat($showInfo)
    {
        $showRes = -1;
        if ($showInfo) {
            //如果是演出订单，需要回滚座位
            $roundId   = $showInfo['round_id'];
            $venueId   = $showInfo['venue_id'];
            $areaId    = $showInfo['area_id'];
            $jitIdList = explode(',', $showInfo['seat_id_list']);
            //扩展属性
            $storageChange = $showInfo['storage_change'] ?? [];
            $lockSeatIdArr = $showInfo['lock_id_list'] ?? [];

            $otherInfo = [
                'storage_change' => $storageChange,
                'lock_id_arr'    => $lockSeatIdArr,
            ];

            $thirdSystemBiz = new ThirdSystem();
            $showRes        = $thirdSystemBiz->rollbackShowOrder(time(), $roundId, $areaId, $jitIdList, $otherInfo);
        }

        //写回退日志
        $logData = json_encode([
            'key'      => 'rollbackShowSeat',
            'showInfo' => $showInfo,
            'showRes'  => $showRes,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('order/debug', $logData);

        return true;
    }

    /**
     * 获取一个未使用的凭证码
     * 循环获取五次，如果都没有获取到可用的就直接返回false
     *
     * <AUTHOR>
     * @date 2019-03-18
     *
     * @return
     */
    private function _getVcode($lid, $ptype, $resourceId, $terminalType)
    {
        for ($i = 0; $i < 5; $i++) {
            $code = UnionCodeUtil::getCode($lid, $ptype, false, $resourceId, $terminalType);
            if ($code) {
                return strval($code);
            }

            /* $cert = \Library\OrderCodePool::GetCode($lid, $ptype, false, $resourceId, $terminalType);
            if ($cert) {
                return strval($cert);
            } */
        }

        return false;
    }

    /**
     * 生成独立的请求流水号，如果是相同的一次下单的话，那这个流水号要保持一直
     * <AUTHOR>
     * @date   2019-05-16
     *
     * @param  int  $memberId  下单用户ID
     * @param  int  $aid  供应商ID
     * @param  int  $tid  门票ID
     * @param  string  $remotenum  远端订单号
     * @param  string  $transactionId  独立的请求流水号，后期会在调用方去加这个参数
     * @param  string  $parentOrdernum  主票订单号
     *
     * @return string
     */
    private function _genTransactionId($memberId, $aid, $tid, $remotenum = '', $transactionId = '', $parentOrdernum = '', $linkType = '')
    {
        if ($remotenum) {
            //如果是有传远端订单号的话
            return strval("rt_{$memberId}_{$aid}_{$remotenum}_") . time();
        } elseif ($transactionId) {
            //如果有传请求流水号的话
            return strval("tc_{$memberId}_{$transactionId}");
        } elseif ($linkType == 'package_son' && $parentOrdernum) {
            //套票子票
            return "son_{$memberId}_{$tid}_{$parentOrdernum}";
        } else {
            //如果都没有的话，使用产品加上毫秒
            $microtime = str_pad(str_replace('.', '', microtime(true)), 14, '0') . rand(10000, 99999);
            if ($parentOrdernum) {
                $parentFlag = $parentOrdernum . '_';
            } else {
                $parentFlag = '';
            }

            return strval("co_{$memberId}_{$tid}_{$parentFlag}{$microtime}");
        }
    }

    /**
     * 如果是演出产品，需要往演出系统下单
     * <AUTHOR>
     * @data   2019-03-25
     *
     * @param  string  $ptype
     *
     * @return array
     */
    private function _handlerShowOrder($member, $tid, $tnum, $playdate, $OneLevelSeller, $ptype, $showArr = [])
    {
        $resArr = [
            'series'       => '',
            'round_info'   => '',
            'seat_id_list' => '',
        ];

        if ($ptype == 'H' && $showArr) {
            $reqData = [
                'TicketID'       => $tid,
                'PftPlayTime'    => $playdate . ' 00:00:00',
                'Tnum'           => $tnum,
                'Fid'            => $member,
                'OneLevelSeller' => $OneLevelSeller,
                'VenueID'        => $showArr['venue_id'],
                'RoundID'        => $showArr['round_id'],
                'AreaID'         => $showArr['area_id'],
                'SeatIds'        => $showArr['seat_ids'],
                'LinkSeatMark'   => $showArr['link_seat_mark'],
                'SubAreaID'      => $showArr['sub_area_id'],
            ];

            $thirdSystemBiz = new ThirdSystem();
            $thirdRes       = $thirdSystemBiz->showOrder($reqData);

            if ($thirdRes['code'] != 200) {
                return $this->orderReturn(0, $thirdRes['msg'], ['err_code' => OrderConst::err5001]);
            } else {
                $resArr = $thirdRes['data'];
            }
        }

        return $this->orderReturn(200, "", $resArr);
    }

    /**
     * 处理身份证新旧客户端兼容问题
     * @Author: zhujb
     * 2019/8/12
     *
     * @param $contactArr
     * @param $idcardArr
     *
     * @return array
     */
    private function _handleIdCardData($contactArr, $idcardArr, $orderChannel)
    {
        // 兼容旧版客户端身份证填写方式  (没有身份类型，但是有身份证数组，并且不是从平台或新版微商城下单的  这种就认为他是旧版)
        //取票人没有类型,身份信息也没有类型都默认是身份证
        if (empty($contactArr['voucher_type']) && !empty($contactArr) && !empty($idcardArr) && empty($idcardArr[0]['voucher_type'])) {
            if (empty($idcardArr[0]['idcard'])) {
                return ['contact_arr' => $contactArr, 'idcard_arr' => $idcardArr];
            }
            $contactArr['personid']     = $idcardArr[0]['idcard'];
            $contactArr['voucher_type'] = 1;
            // 旧的客户端，游客身份信息类型都默认是身份证
            foreach ($idcardArr as &$item) {
                $item['voucher_type'] = 1;
            }

            //取票人没有类型,身份信息有类型,默认取身份信息第一位类型和信息
        } elseif (empty($contactArr['voucher_type']) && !empty($contactArr) && !empty($idcardArr) && !empty($idcardArr[0]['voucher_type'])) {
            $contactArr['personid']     = $idcardArr[0]['idcard'];
            $contactArr['voucher_type'] = $idcardArr[0]['voucher_type'];
        }

        return ['contact_arr' => $contactArr, 'idcard_arr' => $idcardArr];
    }

    /**
     * 下单完成之后，将座位和订单关联
     * <AUTHOR>
     * @data   2019-04-08
     *
     * @param  string  $ordernum
     * @param  int  $tnum
     * @param  int  $payStatus
     * @param  int  $oneLevelSeller
     * @param  int  $memberId
     * @param  array  $showInfo
     *
     * @return array
     */
    private function _relatedShow($ordernum, $tnum, $isPay, $ptype, $oneLevelSeller, $memberId, $showInfo = [])
    {
        if ($ptype == 'H' && $showInfo) {
            //演出类的需要处理
            $reqData = [
                'RoundID'        => $showInfo['round_id'],
                'AreaID'         => $showInfo['area_id'],
                'Ordern'         => $ordernum,
                'Fid'            => $memberId,
                'Tnum'           => $tnum,
                'PayStatus'      => $isPay ? 1 : 2,
                'OneLevelSeller' => $oneLevelSeller,
                'SeatsId'        => $showInfo['seat_id_list'],
            ];

            $thirdSystemBiz = new ThirdSystem();
            $thirdRes       = $thirdSystemBiz->showRelated($reqData);

            if ($thirdRes['code'] == -1) {
                usleep(300000);
                $thirdRes = $thirdSystemBiz->showRelated($reqData);
            }

            if ($thirdRes['code'] == 200) {
                //成功
                return $this->orderReturn(200, "关联成功");
            } else {
                //失败
                return $this->orderReturn(0, "关联失败");
            }
        } else {
            return $this->orderReturn(200, "非演出订单");
        }
    }

    /**
     * 处理往第三方系统下单逻辑
     * <AUTHOR>
     * @date 2019-03-24
     *
     * @param  array  $ticketInfo
     *
     * @return array
     */
    private function _handleThirdSystermOrderNew($orderResData, $resTicketInfo, $resTouristList, $personId = '', $voucherType = 0, $ordername = '', $orderChannel = -1)
    {
        //身份证信息处理
        $idCardList      = [];
        $touristNameList = [];
        $mobileList      = [];
        foreach ($resTouristList as $item) {
            if ($item['idcard']) {
                $idCardList[] = strval($item['idcard']);
            }
            if ($item['tourist']) {
                $touristNameList[] = strval($item['tourist']);
            }
            if ($item['mobile']) {
                $mobileList[] = strval($item['mobile']);
            }
        }

        //如果有传身份证信息都往三方系统传
        $idcard = $idCardList ? $idCardList[0] : '';
        if (!$idcard) {
            $idcard         = $personId ?: '';
            $idcardStr      = $personId ?: '';
            $touristNameStr = $ordername ?: '';
            $mobileStr      = $orderResData['ordertel'] ?: '';
        } else {
            $idcardStr      = $idCardList ? implode(',', $idCardList) : '';
            $touristNameStr = $touristNameList ? implode(',', $touristNameList) : '';
            $mobileStr      = $mobileList ? implode(',', $mobileList) : '';
        }

        //请求参数
        $reqData = [
            'PftOrderSn'      => $orderResData['ordernum'],
            //票付通订单号
            'Ordern'          => $orderResData['ordernum'],
            //票付通订单号，冗余的
            'Ltitle'          => '',
            //景区名称
            'Ttitle'          => '',
            //门票名称
            'PftUUid'         => $resTicketInfo['uuid'],
            //uuid
            'PftPriceJs'      => $orderResData['tprice'],
            //结算价
            'PftPriceGh'      => $orderResData['cost_price'],
            //供货价
            'PftPriceLs'      => $orderResData['retail_price'],
            //零售价
            'PftPlayTime'     => $orderResData['playtime'],
            //游玩时间
            'Tnum'            => $orderResData['tnum'],
            'Fid'             => $orderResData['member'],
            'PayStatus'       => $orderResData['pay_status'],
            'TicketID'        => $orderResData['tid'],
            'TouristName'     => $orderResData['ordername'],
            'TouristTel'      => $orderResData['ordertel'],
            'PlayDate'        => $orderResData['begintime'],
            //这个参数有些奇怪,原来逻辑是游玩有效期
            'EndDate'         => $orderResData['endtime'],
            'IdCard'          => $idcard,
            'remotenum'       => $orderResData['remotenum'],
            //ota请求订单号作为第三方系统临时订单号请求
            'LandId'          => $orderResData['lid'],
            //景点ID
            'ApplyId'         => $orderResData['apply_did'],
            //原始供应商
            'OneLevelSeller'  => $orderResData['one_level_seller'],
            //一级分销商
            'LastLevelSeller' => $orderResData['aid'],
            //一级分销商
            'TouristNameList' => $touristNameStr,
            //所有身份证姓名
            'IdCardList'      => $idcardStr,
            //所有身份证号码
            'PftCode'         => base64_encode(strrev($orderResData['code'])),
            //凭证码-> strrev(base64_decode($code))
            'UUcode'          => $orderResData['code'],
            //凭证码
            'sourceT'         => $resTicketInfo['sourcet'],
            //票设置的对接属性
            'MobileList'      => $mobileStr,
            //所有手机号
            'ContactIdCard'   => $orderChannel == 9 ? ($personId ?: '') : '',
            //取票人身份证(会员卡改版后身份证都有)（会员卡的情况会优先取取票人身份证，然后昀威那边会配合特殊三方那边用这个字段优先）
        ];

        //分时预约时间
        if ($orderResData['time_str']) {
            [$reqData['PftTimeBegin'], $reqData['PftTimeEnd']] = explode('-', $orderResData['time_str']);
        } else {
            $reqData['PftTimeBegin'] = $reqData['PftTimeEnd'] = '';
        }

        $thirdSystemBiz = new ThirdSystem();
        $thirdRes       = $thirdSystemBiz->commonOrder($reqData);

        if ($thirdRes['code'] != 200) {
            $errCode = $thirdRes['data']['err_code'] == OrderConst::err5004 ? OrderConst::err5004 : OrderConst::err5001;

            return $this->orderReturn(0, $thirdRes['msg'], ['err_code' => $errCode]);
        } else {
            return $this->orderReturn(200, '', $thirdRes['data']);
        }
    }

    /**
     * 处理往第三方系统下单逻辑, 开放2.0版本
     * https://apifox.com/apidoc/shared-85ba6a28-382e-4a88-99a1-825c6c683748/api-24559962
     * @param $orderResData
     * @param $resTicketInfo
     * @param $resTouristList
     * @param $personId
     * @param $voucherType
     * @param $ordername
     * @param $orderChannel
     * @return array
     * @throws
     */
    private function _handleThirdSystemOrderV2($orderResData, $resTicketInfo, $resTouristList, $personId = '', $voucherType = 0, $ordername = '', $orderChannel = -1) {
        $memberIdList = array_values(array_unique([$orderResData['one_level_seller'], $orderResData['aid'], $orderResData['member']]));
        $memberBiz = Container::pull(Member::class);
        $memberList = $memberBiz->getMemberInfoByMulti($memberIdList, 'id', true);
        $oneLevelSeller = $memberList[$orderResData['one_level_seller']] ?? [];
        $lastLevelSeller = $memberList[$orderResData['aid']] ?? [];
        $buyUser = $memberList[$orderResData['member']] ?? [];
        // 订单信息
        $orderInfo = [
            'ticket_num' => $orderResData['tnum'], // 购买数量 必需
            'trave_date' => $orderResData['playtime'], // 预计游玩日期 必需
            'valid_start_date' => $orderResData['begintime'], // 订单有效开始日期 必需
            'valid_end_date' => $orderResData['endtime'], // 订单有效结束日期 必需
            'fid' => $oneLevelSeller && $oneLevelSeller['dtype'] != 5 ? $orderResData['one_level_seller'] : $orderResData['aid'], // 一级分销商id 必需
            'fid_name' => $oneLevelSeller && $oneLevelSeller['dtype'] != 5 ? $oneLevelSeller['dname'] : ($lastLevelSeller['dname'] ?? ''), // 一级分销商用户名 必需
            'last_fid' => $orderResData['aid'], // 最后一级分销商，uu_ss_order.aid 必需
            'last_fid_name' => $lastLevelSeller['dname'] ?? '', // 最后一级分销商，uu_ss_order.aid 用户名 必需
            'buy_user_id' => $orderResData['member'], // 末级购买用户id ， uu_ss_order.member 必需
            // 如果是散客则使用联系人姓名
            'buy_user_name' => $buyUser && $buyUser['dtype'] != 5 ? $buyUser['dname'] : $ordername, // 末级购买用户名 必需
            'order_status' => 0, // 订单状态 必需
            'pay_status' => $orderResData['pay_status'], // 支付状态 必需
            'share_start_time' => '', // 分时开始时间 可选
            'share_end_time' => '', // 分时结束时间 可选
            'pft_code' => $orderResData['code'], // 票付通凭证码 必需
            'remote_ota_order' => $orderResData['remotenum'], // ota 远端订单号 必需
            'level_sellers' => '', // 订单分销链用户id 表的aids 必需
        ];
        //分时预约时间
        if ($orderResData['time_str']) {
            [$orderInfo['share_start_time'], $orderInfo['share_end_time']] = explode('-', $orderResData['time_str']);
        }
        // 价格信息
        $priceInfo = [
            'settle_price' => $orderResData['tprice'], // 和一级分销商的结算价 必需
            'retail_price' => $orderResData['retail_price'], // 零售价 必需
            'cost_price' => $orderResData['cost_price'], // 成本价 必需
        ];
        // 商品信息
        $productInfo = [
            'section_id' => '', // 场次id 必需
            'product_name' => $orderResData['land_name'], // 景点名 必需
            'ticket_name' => $orderResData['ticket_name'], // 门票名 必需
            'ticket_id' => $orderResData['tid'], // 门票id 必需
            'product_id' => $orderResData['lid'], // 景点id 必需
            'apply_id' => $orderResData['apply_did'], // 供应商id 必需
        ];
        // 联系人信息
        $contactCredentialsInfo = [];
        if ($personId && $voucherType) {
            $contactCredentialsInfo[] = [
                'certificate_id' => $personId, // 证件值 可选
                'certificate_type' => $voucherType, // 证件类型 可选
            ];
        }

        // 联系人信息
        $contactInfo = [
            'contact_name' => $ordername ?: '', // 联系人姓名 必需
            'contact_phone' => $orderResData['ordertel'] ?: '', // 联系人手机号 必需
            'credentials_info' => $contactCredentialsInfo, // 证件信息 必须
        ];
        // 游客信息
        $touristInfo = [];
        foreach ($resTouristList as $tourist) {
            if (!$tourist['idcard'] && !$tourist['voucherType'] && !$tourist['mobile'] && !$tourist['tourist']) {
                continue;
            }
            $touristCredentialsInfo = [];
            if ($tourist['idcard'] && $tourist['voucherType']) {
                $touristCredentialsInfo[] = [
                    'certificate_id' => $tourist['idcard'], // 证件值 可选
                    'certificate_type' => $tourist['voucherType'], // 证件类型 可选
                ];
            }
            $touristInfo[] = [
                'tourist_name' => $tourist['tourist'], // 游客姓名 可选
                'tourist_phone' => $tourist['mobile'], // 游客手机号 可选
                'credentials_info' => $touristCredentialsInfo
            ];
        }
        $params = [
            'order_num' => $orderResData['ordernum'],
            'order_info' => $orderInfo ?: new \stdClass,
            'price_info' => $priceInfo ?: new \stdClass,
            'product_info' => $productInfo ?: new \stdClass,
            'contact_info' => $contactInfo ?: new \stdClass,
            'tourist_info' => $touristInfo ?: new \stdClass,
            'request_id' => uniqid('', true),
        ];
        $thirdSystemBiz = Container::pull(ThirdSystem::class);
        return $thirdSystemBiz->commonOrderV2($params);
    }

    /**
     * 将第三方系统的订单号或是凭证码更新到订单表
     * <AUTHOR>
     * @data   2019-04-03
     *
     * @param  string  $ordernum
     * @param  string  $thirdCode
     * @param  string  $thirdOrdernum
     *
     * @return bool
     */
    private function _updateThirdSystermOrderNew($ordernum, $thirdCode = '', $thirdOrdernum = '')
    {
        if (!$thirdCode && !$thirdOrdernum) {
            return false;
        }

        $updateData = [
            'vcode'     => $thirdCode,
            'tordernum' => $thirdOrdernum,
        ];

        $orderApi = new OrderApi();
        $res      = $orderApi->updateOrder($ordernum, $updateData);

        //写下日志
        $logData = json_encode([
            'type'          => 'update_third_info',
            'ordernum'      => $ordernum,
            'thirdCode'     => $thirdCode,
            'thirdOrdernum' => $thirdOrdernum,
            'res'           => $res,
        ]);
        pft_log('order/debug', $logData);

        return true;
    }

    /**
     * 异步回滚订单数据，将数据写入队列，通过异步任务区回滚这些数据
     * <AUTHOR>
     * @data   2019-03-25
     *
     * @param  string  $ordernum  订单号
     * @param  string  $rollbackType  回滚类型 ordered:下单后回滚, payed:支付后回滚
     * @param  array  $showInfo  演出相关的数据
     * @param  string  $remotenum  远程订单号
     * @param  string  $errMsg  订单错误原因
     *
     * @return array
     */
    private function _asyncRollbackOrder($ordernum, $memberId, $rollbackType = 'ordered', $showInfo = [], $remotenum = '', $errMsg = '')
    {
        //将数据写入队列
        $queueData = [
            'ordernum'     => $ordernum,
            'rollbackType' => $rollbackType,
            'showInfo'     => $showInfo,
            'memberId'     => $memberId,
            'remotenum'    => $remotenum,
            'errMsg'       => $errMsg,
        ];

        $cancelJobData = [
            'job_type' => 'rollback_order',
            'job_data' => $queueData,
        ];
        $queueId       = Queue::push('order', 'Order_Job', $cancelJobData);

        //将数据写入日志
        $logData = json_encode([
            'key'          => 'asyncRollbackOrder',
            'ordernum'     => $ordernum,
            'rollbackType' => $rollbackType,
            'showInfo'     => $showInfo,
            'memberId'     => $memberId,
            'remotenum'    => $remotenum,
            'errMsg'       => $errMsg,
            'queueId'      => $queueId,
        ]);
        pft_log('order/debug', $logData);

        return true;
    }

    /**
     * 添加队列任务
     * <AUTHOR>
     * @data   2019-04-15
     *
     * @param  string  $ordernum  订单号
     * @param  bool  $isNeedSendSms  是否需要发送短信
     * @param  string  $playtime  游玩日期
     * @param  string  $tnum  购买票数
     * @param  string  $isPay  是否已经支付
     * @param  string  $payStatus  支付状态
     * @param  string  $member  购买用户ID
     * @param  string  $aid  供应商ID
     * @param  string  $tid  门票ID
     * @param  string  $pid  产品ID
     * @param  string  $ptype  产品类型
     * @param  string  $ordername  订票用户名称
     * @param  string  $ordertel  手机号
     * @param  string  $childTicketShowInfoArr  演出子票座位信息
     * @param  array  $trackIdList  订单追踪记录数据是JAVA写入，ES数据暂时还由php这边处理
     * @param  array  $specialSettingArr  子票遵循主票下单的一些更多信息
     *
     * @return bool
     */
    protected function _addQueueTask($ordernum, $isNeedSendSms, $playtime, $tnum, $isPay, $payStatus, $member, $aid, $tid, $pid, $ptype, $ordername, $ordertel, $childTicketShowInfoArr = [], $trackIdList = [], array $specialSettingArr = [])
    {
        $parentContactInfoMore = $specialSettingArr['parent_contact_info_more'] ?? [];
        //国际号支持
        $internationalCall = \Library\MessageNotify\OrderNotify::getInternationalCall($parentContactInfoMore['mobileArea'] ?? '', $ordertel);
        //写入短信队列
        $notifyJobId = -1;

        //是否需要发送短信给游客，有些外部接口或是云票务有配置不传不发送短信给游客的参数
        $notifyType = $isNeedSendSms ? 0 : 1;

        $tmp_log_data = [
            'logTitle'               => '套票新通知机制日志打印',
            'ordernum'               => $ordernum,
            'isNeedSendSms'          => $isNeedSendSms,
            'playtime'               => $playtime,
            'tnum'                   => $tnum,
            'isPay'                  => $isPay,
            'payStatus'              => $payStatus,
            'member'                 => $member,
            'aid'                    => $aid,
            'tid'                    => $tid,
            'pid'                    => $pid,
            'ptype'                  => $ptype,
            'ordername'              => $ordername,
            'ordertel'               => $ordertel,
            'childTicketShowInfoArr' => $childTicketShowInfoArr,
            'trackIdList'            => $trackIdList,
            'specialSettingArr'      => $specialSettingArr,
        ];
        pft_log('order/debug', json_encode($tmp_log_data, JSON_UNESCAPED_UNICODE));

        //如果发送短信
        if ($isPay == true || $payStatus == 0) {
            $isNotice = true;
            //部分套票改到子票全部生成后发送短信
            if($ptype == 'F'){
                //开放功能
                $landInfo = $this->getLandInfoByTid($tid);
                if (!$landInfo) {
                    $innerMsg = '产品类型获取失败';
                    pft_log('order/debug', json_encode([$ordernum,$innerMsg],JSON_UNESCAPED_UNICODE));
                }
                $applyDid = $landInfo['apply_did'];
                $isAllowPack = self::isAllowPackNotice($applyDid);
                pft_log('order/debug', json_encode(['新套票通知开放功能',$ordernum,$isAllowPack],JSON_UNESCAPED_UNICODE));
                $isNotice = $isAllowPack ? false : true;
            }
            if ($isNotice) {
                //如果是已经支付的或是现场支付的订单，需要进入发送短信逻辑
                $notifyJobId = Queue::push('notify', 'OrderNotify_Job',
                    [
                        'ordernum' => $ordernum,
                        'buyerId'  => $member, //购买人的ID
                        'mobile'   => $internationalCall,
                        'aid'      => $aid,
                        'notify'   => $notifyType, //是否发送短信给游客：0=发送，1=不发送
                    ]
                );
            }
        }

        //写入套票队列
        $packgeJobId = -1;
        if (in_array($ptype, ['F', 'H'])) {
            if ($isPay == true || $payStatus == 0) {
                //已经支付的或是现场支付的情况下才去生成子票
                $specialSettingArr['sms_info'] = [
                    'ordernum' => $ordernum,
                    'buyerId'  => $member, //购买人的ID
                    'mobile'   => $internationalCall,
                    'aid'      => $aid,
                    'notify'   => $notifyType, //是否发送短信给游客：0=发送，1=不发送
                ];
                $packgeJobId = Queue::push('package_order', 'PackageOrder_Job',
                    [
                        'orderName'              => $ordername,
                        'orderTel'               => $ordertel,
                        'parentPid'              => $pid,
                        'parentOrdernum'         => $ordernum,
                        'playtime'               => $playtime,
                        'tnum'                   => $tnum,
                        'ptype'                  => $ptype,
                        'actions'                => ['submitChildrenOrder'],
                        'childTicketShowInfoArr' => $childTicketShowInfoArr,
                        'childSpecialSettingArr' => $specialSettingArr,
                    ]
                );
            } else {
                // 未支付的添加对应的订单号下子票的演出数据
                if (in_array($ptype,
                        ['F', 'H']) && !empty($childTicketShowInfoArr) && is_array($childTicketShowInfoArr)) {
                    $redis = \Library\Cache\Cache::getInstance('redis');
                    $redis->set('childShowInfoJson:' . $ordernum, json_encode($childTicketShowInfoArr), '',
                        7210); // 两个小时多些
                }
            }
        }

        $esTrackJobId = [];
        $createTime   = time();
        if ($trackIdList && is_array($trackIdList)) {
            //$trackModel = new \Model\Order\OrderTrack();
            foreach ($trackIdList as $item) {
                $trackId = intval($item['trackId']);

                $queryParams = [$trackId];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTrack', 'queryOrderTrackById',
                    $queryParams);
                $trackInfo   = [];
                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                    $trackInfo = $queryRes['data'];
                }
                //$trackInfo = $trackModel->getTrackInfoById($trackId);
                if ($trackInfo) {
                    $esJobId = 0;
                    //Queue::push('order', 'SyncEsOrder_Job',['method' => 'insert', 'params' => json_encode($trackInfo)]);
                    $esTrackJobId[] = $esJobId;
                }
            }
        }

        //订单过期前通知游客 景区门票相关业务已经迁走
        if (in_array($ptype, ['B', 'C', 'F', 'G'])) {
            OrderExpireNotice::pushJob($ordernum, $this->_noticeBeforeExpireDays);
        }
        //写入日志
        $logData = json_encode([
            'ordernum'     => $ordernum,
            'notifyJobId'  => $notifyJobId,
            'packgeJobId'  => $packgeJobId,
            'esTrackJobId' => $esTrackJobId,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('order/debug', $logData);

        return $notifyJobId;
    }

    /**
     * 是否开通套票新通知机制
     * @param $applyDid
     * @return bool
     */
    public static function isAllowPackNotice($applyDid)
    {
        if (empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
//        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'pack_notice', 60);
        //改为黑名单的方式，默认全平台开放，有问题的商家则配置为黑名单沿用原来就有逻辑，主票下单成功即出票通知下游
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'pack_notice_blacklist', 60);
        if ($resultRes['code'] == 200 && (!empty($resultRes['data']))) { //代表命中黑名单，取反，查到了返回false
            return false;
        }

        return true;
    }

    /**
     * 提前进行三方系统下线的判断处理
     * <AUTHOR>
     * @date   2019-05-21
     *
     * @param  int  $memberId
     * @param  int  $aid
     * @param  int  $tid
     * @param  array  $thirdInfo
     * @param  array  $landInfo
     *
     * @return array
     */
    private function _preHandleThirdOrder($memberId, $aid, $tid, $thirdInfo = [], $landInfo = [])
    {
        //$ticketBiz = new TicektBiz();
        //$ticketRes = $ticketBiz->getListForOrderNew($tid);
        //if (!isset($ticketRes['third_attr_info'])) {
        //    return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
        //}
        //$thirdInfo = $ticketRes['third_attr_info'];
        //$landInfo  = $ticketRes['land_info'];

        if ($thirdInfo['mdetails'] == 1 && $landInfo['p_type'] != 'H') {
            //检测是否有对接三方的权限（套餐模块）
            $moduleBiz = new \Business\AppCenter\Module();
            $moduleRes = $moduleBiz->checkUserIsCanUseApp($landInfo['apply_did'], 'mart_connect');
            if (!$moduleRes) {
                return $this->orderReturn(0, '没有对接三方系统的权限', ['err_code' => OrderConst::err1111]);
            }
        }

        //TODO:现在是把往第三方系统下单移到支付完成之后
        //所以后面需要在下单之前先确认下第三方系统是否存活，如果系统已经下线了直接就不进入下单环节了
        $offlineTidArr = [];
        if ($offlineTidArr && in_array($tid, $offlineTidArr)) {
            //暂时先手动处理，把已经对接下线系统的tid放入到$offlineTidArr中
            return $this->orderReturn(0, "", ['err_code' => 5004]);
        } else {
            return $this->orderReturn(200, "");
        }
    }

    private function _orderTimeDeal($orderRes, $startTime, $endTime)
    {

        $costTime = intval($endTime - $startTime);
        if ($costTime >= 5) {
            //下单时间超过5秒，钉钉告警
            if ($orderRes['code'] == 200) {
                $ordernum  = $orderRes['data']['ordernum'];
                $notifyMsg = "订单号:{$ordernum},下单花费时间:{$costTime}秒";
            } else {
                $errMsg    = $orderRes['data']['err_msg'];
                $notifyMsg = "下单失败,失败信息:{$errMsg},花费时间:{$costTime}秒";
            }
            \Library\Tools\Helpers::sendDingTalkGroupRobotMessageRaw($notifyMsg,
                \Library\Constants\DingTalkRobots::ORDER_EXCEPTION);
        }
    }

    private function _getCheckBiz()
    {
        if (!$this->_ordercheckBiz) {
            $this->_ordercheckBiz = new BaseCheck();
        }

        return $this->_ordercheckBiz;
    }

    /**
     * 获取支持其他身份类型的第三方供应商系统
     * @deprecated 建议使用qconf配置，参考BaseSubmit::isOtaSupportMultiIdentity
     * <AUTHOR>
     * @date 2019/12/19 0019
     *
     * @return array
     */
    private function getSupportIdenList()
    {
        return [
            6,//华侨城
            211, //华夏票联
        ];
    }

    /**
     * 如果是第三方发码的订单，需要往第三方发码的应用服务下单
     * <AUTHOR>
     * @data   2020-07-23
     *
     * @param  string  $ptype
     *
     * @return array
     */
    private function _handleExternalCodeOrder(int $tid, int $tnum, string $playDate)
    {
        $resArr = [];

        if ($this->_externalCodeOrder) {
            $getCodeRes = $this->_externalCodeBiz->lockCode($tid, $tnum, $playDate);
            if ($getCodeRes['code'] != 200) {

                if ($getCodeRes['code'] == 500) {
                    return $this->orderReturn(0, $getCodeRes['msg'], ['err_code' => OrderConst::err7001]);
                } else {
                    return $this->orderReturn(0, $getCodeRes['msg'], ['err_code' => OrderConst::err7002]);
                }

            } else {
                $resArr = $getCodeRes['data'];
            }
        }

        return $this->orderReturn(200, "", $resArr);
    }

    /**
     * 下单失败释放之前被锁定的第三方码
     * <AUTHOR>
     * @data   2020-07-23
     *
     * @param  array  $codeData  第三方码数据
     *
     * @return array
     */
    private function _orderFailReleaseExternalCode(array $codeData)
    {
        $resArr = [];

        if ($this->_externalCodeOrder) {
            $eCodeRes = $this->_externalCodeBiz->orderFailReleaseCode($codeData);
            // pft_Log('external_code/order_fail_release/', json_encode([$codeData, $eCodeRes]));
            if ($eCodeRes['code'] != 200) {
                return $this->orderReturn(0, $eCodeRes['msg']);
            } else {
                $resArr = $eCodeRes['data'];
            }
        }

        return $this->orderReturn(200, "", $resArr);
    }

    /**
     * 如果是第三方发码的订单 下单成功后需要处理的逻辑
     * <AUTHOR>
     * @data   2020-07-23
     *
     * @param  string  $ptype
     *
     * @return array
     */
    private function _orderSuccessHandleExternalCode(string $orderId, array $codeData)
    {
        $resArr = [];

        if ($this->_externalCodeOrder) {
            $eCodeRes = $this->_externalCodeBiz->codeBindOrderId($orderId, $codeData);
            //pft_Log('external_code/order_bind/', json_encode([$orderId, $codeData, $eCodeRes]));
            if ($eCodeRes['code'] != 200) {
                return $this->orderReturn(0, $eCodeRes['msg']);
            } else {
                $resArr = $eCodeRes['data'];
            }
        }

        return $this->orderReturn(200, "", $resArr);
    }

    /**
     * 外部发码订单非线上支付成功增加异步任务
     * <AUTHOR>
     * @data   2020-07-23
     *
     * @param  string  $orderId  订单号
     *
     * @return array
     */
    private function _addExternalCodeOrderPaySuccessTask(string $orderId)
    {
        $externalCodeData = [
            'action' => 'pay_use_code',
            'data'   => ['ordernum' => $orderId],
        ];

        $externalCodeJobId = Queue::push('independent_system', 'ExternalCode_Job', $externalCodeData);
        pft_Log('external_code/order_pay_success/', json_encode([$orderId, $externalCodeJobId]));
    }

    /**
     * 协议票配置库存判断逻辑
     * <AUTHOR>
     * @date 2021/1/13
     *
     * @param $isAgreementTicket 是否协议票票属性
     * @param $orderChannel 下单渠道
     * @param $aid 供应商ID
     * @param $memberId 用户ID
     * @param $tid 票ID
     * @param $tnum 下单数量
     *
     * @return array
     */
    public function _handleAgreementTicketOrder($isAgreementTicket, $orderChannel, $aid, $memberId, $tid, $tnum, $aidsArr)
    {

        $uid = $memberId;//实际下单人ID

        if (!empty($aidsArr[1])) {
            $memberId = $aidsArr[1];
        }

        $limitChannel = [
            OrderChannel::OPERATOR_CHANNEL,//计调下单
            OrderChannel::CLOUDTICKET_CHANNEL,//云票务下单
            OrderChannel::PLATFORM_CHANNEL,//平台正常下单
            OrderChannel::MICROPLAT_CHANNEL,//微平台下单
            OrderChannel::APP_CHANNEL,//APP下单
        ];
        if ($isAgreementTicket && in_array($orderChannel, $limitChannel)) {
            $this->_agreementTicketBiz = new \Business\AgreementTicket\Storage();
            $agreementStorageRes       = $this->_agreementTicketBiz->orderJudgeStorage(intval($aid), intval($memberId),
                intval($tid), intval($tnum), intval($uid));
            if ($agreementStorageRes['code'] != 200) {
                //服务异常
                $agreementDataRes = ['err_code' => OrderConst::err8001];

                return $this->orderReturn(0, $agreementStorageRes['msg'], $agreementDataRes);
            }
            //协议配置判断
            if ($agreementStorageRes['data']['config_id']) {
                $this->_agreementTicketOrder = $agreementStorageRes['data']['config_id']; //是否是协议票订单
                //协议库存判断
                if (!$agreementStorageRes['data']['is_storage']) {
                    $agreementDataRes = ['err_code' => OrderConst::err8002];

                    return $this->orderReturn(0, $agreementStorageRes['msg'], $agreementDataRes);
                }
            }
        }

        return $this->orderReturn(200, '', []);
    }

    /**
     * 通过门票id获取产品类型
     * <AUTHOR>  Li
     * @date  2021-04-12
     *
     * @param  int  $tid  门票id
     *
     * @return string
     */
    private function getPtypeByTid(int $tid)
    {
        $pType = '';
        $key   = "get_ptype_by_tid:{$tid}";
        //先缓存一个月
        $expTime   = 60 * 60 * 24 * 30;
        $cache     = \Library\Cache\Cache::getInstance('redis');
        $cacheData = $cache->get($key);

        if ($cacheData) {
            return $cacheData;
        } else {
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketInfo = $javaApi->getTicketAndLandInfoByArrTid([$tid], '', 'p_type');
            if ($ticketInfo) {
                $pType = $ticketInfo[0]['p_type'];
                $cache->set($key, $pType, '', $expTime);
            }
        }

        return $pType;
    }

    /**
     * 通过门票id获取产品类型等不会变更的数据
     * <AUTHOR>
     * @date  2022-07-15
     *
     * @param  int  $tid  门票id
     *
     * @return string
     */
    private function getLandInfoByTid(int $tid)
    {
        $key = "scenics:get_lanfinfo_by_tid:{$tid}";
        //先缓存一个月
        $expTime   = 60 * 60 * 24 * 30;
        $cache     = \Library\Cache\Cache::getInstance('redis');
        $cacheData = $cache->get($key);

        if ($cacheData) {
            $landInfo = json_decode($cacheData, true);
        } else {
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketInfo = $javaApi->getTicketAndLandInfoByArrTid([$tid], '', 'p_type,apply_did,id');

            if ($ticketInfo) {
                $landInfo = [
                    'lid'       => intval($ticketInfo[0]['id']),
                    'p_type'    => strval($ticketInfo[0]['p_type']),
                    'apply_did' => intval($ticketInfo[0]['apply_did']),
                ];

                $ticketInfoStr = json_encode($landInfo);
                $cache->set($key, $ticketInfoStr, '', $expTime);
            } else {
                $landInfo = [];
            }
        }

        return $landInfo;
    }

    /**
     * 应用中心服务下单限制
     * <AUTHOR>
     * @date    2021-11-17
     *
     * @param  int  $aidsArr  分销用户id
     * @param  int  $memberId  下单的用户id
     * @param  int  $applyDid  供应商id
     * @param  int  $isSale  是否散客购买
     *
     *
     * @return array
     */
    private function _appCenterServiceAuth(array $aidsArr, int $memberId, int $aid, int $applyDid, $isSale, int $orderChannel)
    {
        $startTime = microtime(true);

        if (empty($aidsArr)) {
            //加上供应商
            $aidsArr[] = $applyDid;
        }

        $isSale = (bool)$isSale;
        //散客购买
        if ($isSale) {
            $memberId = $aid;
        } else {
            $memberId != $applyDid && $aidsArr[] = $memberId;
        }

        $aidsArr              = array_unique($aidsArr);
        $appcenterServiceAuth = \Business\Order\OrderAuth::serviceLimit($aidsArr, $memberId, $applyDid);

        $endTime  = microtime(true);
        $costTime = ($endTime - $startTime) * 1000;

        if ($costTime >= 50) {
            //记录日志
            $word = json_encode([
                'cost_time' => $costTime . ' ms',
                'res'       => $appcenterServiceAuth,
                'params'    => func_get_args(),
            ], JSON_UNESCAPED_UNICODE);

            pft_log('order/appcenter_service_auth/cost_time', $word);
        }

        if ($appcenterServiceAuth['code'] != 200) {
            $logData = json_encode([
                'params'    => func_get_args(),
                'error_msg' => $appcenterServiceAuth['data']['error_msg'],
            ], JSON_UNESCAPED_UNICODE);
            pft_log('order/appcenter_service_auth/limit', $logData);
        }

        //外部下单提示
        if ($appcenterServiceAuth['code'] != 200 && in_array($orderChannel, [17, 20, 58])) {
            $appcenterServiceAuth['msg'] = \Business\Order\OrderAuth::__OTA_LIMIT_DESC__;
        }

        return $appcenterServiceAuth;
    }

    /**
     * 计时卡押金数据处理
     * <AUTHOR>  Li
     * @date  2022-08-10
     *
     * @param  string  $orderNum  订单号
     * @param  int  $sid  供应商id
     * @param  int  $opId  操作人id
     *
     * @return bool
     */
    private function _timeCardDepositTrack(string $orderNum, int $sid, int $opId)
    {
        $timeCardApi = new \Business\JsonRpcApi\ScenicLocalService\TimeProduct();
        //写入押金记录
        $trackRes    = $timeCardApi->addTimeDeposit($orderNum, $sid, $opId);
        if ($trackRes['code'] != 200) {
            $logData = json_encode([
                'ac'        => 'calculateTimeDepositMoney',
                'orderNum'  => $orderNum,
                'sid'       => $sid,
                'opId'      => $opId,
                'error_msg' => $trackRes['msg'],
            ], JSON_UNESCAPED_UNICODE);
            pft_log('timeCard/addTimeDeposit', $logData);
            return false;
        }

        return true;
    }
}
