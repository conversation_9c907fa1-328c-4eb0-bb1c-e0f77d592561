<?php
/**
 * 平台退票数据统一校验
 *
 * <AUTHOR>
 * @date 2019-06-24
 *
 */

namespace Business\Order;

use Business\Base;
use Business\LegacyService\LegacyServiceTrafficLog;
use Business\Order\Refund\QConfigSwitchController;
use Business\Order\Refund\RefundParamsCheck;
use Business\Order\Refund\TicketRefund;
use Business\Ota\OtaSystemService;
use Library\Cache\Cache;
use Library\Cache\RedisCache;
use Library\Constants\Card\TouristCardEnum;
use Library\Constants\Order\CancelChannelEnum;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\Order\OrderStatus\SpecialOrderStatus;
use Library\Constants\Order\OrderChannel;
use Library\Constants\Order\PrintStatus\OrderIfPrintStatus;
use Library\Constants\OrderConst;
use Library\Constants\Ticket\CancelTypeEnum;
use Library\Constants\Ticket\TicketIfPrintRefundRule;
use Library\Constants\Ticket\TicketTypEnum;
use Library\ServiceContainer;
use Library\Tools;
use Library\Tools\Validate;
use Model\Member\Member;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\OrderTourist;
use Model\Order\OrderTrack;
use Model\Order\SubOrderQuery;
use Model\Order\SubOrderQuery\SubOrderAddon;
use Model\Order\SubOrderQuery\SubOrderDetail;
use Model\Order\SubOrderQuery\SubOrderMain;
use Model\Order\TeamOrderSearch;
use Model\Order\TerminalDbHandler;
use Model\Order\TerminalOrderHandler;
use Model\Product\Presuming;
use Model\Product\Ticket;
use Model\Terminal\FaceCompare;

class BaseRefundCheck extends Base
{

    const TEAM_ORDER_MODE = [
        '44',//团单报团计调
        '24'//团队订单
    ];
    //订单取消类型
    private $_cancelTypeArr = [
        'common', //正常取消
        'revoke', //终端撤改
    ];

    // 门票模型
    private $_ticketModel;
    // 订单查询模型
    private $_orderQueryModel;
    // 会员模型
    private $_memberModel;
    // 订单追踪记录模型
    private $_orderTrackModel;
    // packTicekt模型
    private $_packTicketModel;
    // 团队订单模型
    private $_teamOrderModel;
    private $_orderToolsModel;
    private $_subOrderMainModel;

    //频率控制锁的key数组
    private $_lockKeyArr = [
        //'订单号' => '锁的key'
    ];

    /**
     * 退票基础参数校验
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum
     * @param  int  $cancelNum
     * @param  int  $opId
     * @param  int  $cancelChannel
     * @param  int  $cancelType
     * @param  string  $reqSerialNumber
     * @param  array  $cancelRemarkArr
     * @param  array  $cancelSiteArr
     * @param  array  $cancelPersonArr
     * @param  array  $cancelSpecialArr
     *
     * @return array
     */
    public function baseCheck($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber = '', $cancelRemarkArr = [], $cancelSiteArr = [], $cancelPersonArr = [], $cancelSpecialArr = [], $isCanRefundMoreTicketCode = false)
    {
        $ordernum        = strval($ordernum);
        $cancelNum       = intval($cancelNum);
        $opId            = intval($opId);
        $cancelChannel   = intval($cancelChannel);
        $cancelType      = strval($cancelType);
        $reqSerialNumber = trim(strval($reqSerialNumber));

        if (!$ordernum) {
            return $this->orderReturn(0, "订单号不能为空", ['err_code' => OrderConst::err11]);
        }

        if ($cancelNum == -1) {
            //如果是-1，表示需要取消订单
        } else {
            if ($cancelNum <= 0) {
                //其他情况下
                return $this->orderReturn(0, "退票数量错误", ['err_code' => OrderConst::err11]);
            }
        }

        if (!$opId) {
            return $this->orderReturn(0, "取消用户不能为空", ['err_code' => OrderConst::err11]);
        }

        //取消备注
        $cancelRemark = isset($cancelRemarkArr['remark']) ? strval($cancelRemarkArr['remark']) : '';

        //取消终端
        $cancelTerminal = isset($cancelSiteArr['terminal']) ? intval($cancelSiteArr['terminal']) : 0;

        //取消第几个游客的数据
        if ($isCanRefundMoreTicketCode ) {
            $personIndex = isset($cancelPersonArr['person_index']) ? intval($cancelPersonArr['person_index']) : 0;
            //如果有传取消第几个游客的数据，退票数量只能为1
            if ($personIndex) {
                if ($cancelNum > 1) {
                    return $this->orderReturn(0, "有传游客序号的时候，取消票数只能为1", ['err_code' => OrderConst::err917]);
                }
            }
        } else {
            $personIndex = 0;
            if (isset($cancelPersonArr['person_index'])) {
                $personIndex = intval($cancelPersonArr['person_index']);
                if (is_array($cancelPersonArr['person_index'])) {
                    $personIndex = $cancelPersonArr['person_index'];
                }
            }
        }

        //门票码取消对应的
        $ticketCodeList = isset($cancelPersonArr['ticket_code_list']) ? $cancelPersonArr['ticket_code_list'] : [];
        if ($ticketCodeList){
            if (count($ticketCodeList) != $cancelNum && $cancelNum != -1){
                return $this->orderReturn(0, "取消门票码和取消数量不匹配", ['err_code' => OrderConst::err11]);
            }
        }
        //取消游客身份证类型校验，其他证件类型不做校验
        $personIdList = [];
        $personIdTmp  = isset($cancelPersonArr['person_id_list']) ? $cancelPersonArr['person_id_list'] : [];
        $personInfoList = isset($cancelPersonArr['person_info_list']) ? $cancelPersonArr['person_info_list'] : [];
        //放量改造
        if (!empty($personInfoList)) {
            foreach ($personInfoList as $person) {
                $voucherTypeObj = TouristCardEnum::find(intval($person['idcard_type']));
                if (empty($voucherTypeObj)) {
                    continue;
                }
                if ($voucherTypeObj->key == TouristCardEnum::ID_CARD) {
                    $idcard = strval($person['idcard']);
                    if (strlen($idcard) == 15) {
                        $idcard = Validate::idcard_15to18($idcard);
                    }
                    if (!Validate::isIDCard($idcard)) {
                        return $this->orderReturn(0, "身份证【{$idcard}】不合法", ['err_code' => OrderConst::err11]);
                    }
                }
                if (!in_array($person['idcard'], $personIdList)) {
                    $personIdList[] = $person['idcard'];
                }
            }
        } else {
            //fixme 这段elseif的case待后续所有端都对接完后，需要删除,不会再存在person_id_list，而是都使用person_info_list
            foreach ($personIdTmp as $idcard) {
                $tempIdCard = $idcard = trim(strval($idcard));
                //加上15位身份证校验，即将15位转换为18位
                if (strlen($idcard) == 15) {
                    $tempIdCard = Validate::idcard_15to18($idcard);
                }
                //身份证校验
                if (!Validate::isIDCard($tempIdCard)) {
                    return $this->orderReturn(0, "身份证【{$idcard}】不合法", ['err_code' => OrderConst::err11]);
                }

                if (!in_array($idcard, $personIdList)) {
                    $personIdList[] = $idcard;
                }
            }
        }
        //TODO:看下后期退票数量和退票身份证数是否需要保持一致

        //是否需要进行平台退票审核处理
        $isNeedAudit = isset($cancelSpecialArr['is_need_audit']) ? boolval($cancelSpecialArr['is_need_audit']) : true;
        //是否审核同意
        $isAuditAgreed = isset($cancelSpecialArr['is_audit_agreed']) ? boolval($cancelSpecialArr['is_audit_agreed']) : false;

        //供应商主动取消的手续费费率百分之几
        $autoCancelFee = isset($cancelSpecialArr['auto_cancel_fee']) ? intval($cancelSpecialArr['auto_cancel_fee']) : false;

        //套票取消的时候，是否主动取消子票，默认是都会取消的
        $isCancelSub = isset($cancelSpecialArr['is_cancel_sub']) ? boolval($cancelSpecialArr['is_cancel_sub']) : true;

        //是否强制退票
        $isForceCancel = isset($cancelSpecialArr['is_force_cancel']) ? boolval($cancelSpecialArr['is_force_cancel']) : false;

        //是否是回滚订单
        $isRollBack = isset($cancelSpecialArr['is_rollback']) ? boolval($cancelSpecialArr['is_rollback']) : false;

        //是否是售后订单
        $afterSaleNum = isset($cancelSpecialArr['after_sale_num']) ? strval($cancelSpecialArr['after_sale_num']) : '';

        //是否是重试退票
        $isRetry= isset($cancelSpecialArr['is_retry']) ? intval($cancelSpecialArr['is_retry']) : '';

        //基于idx 批量退票
        $batchRefundMoreIdx = isset($cancelSpecialArr['batch_refund_more_idx']) ? $cancelSpecialArr['batch_refund_more_idx'] : [];

        //是否供应商开启酒店预定确定拒绝的订单
        $isScheduledRefuseBack = isset($cancelSpecialArr['hotel_scheduled_refuse']) ? boolval($cancelSpecialArr['hotel_scheduled_refuse']) : false;

        //是否允许多次撤销
        $isAllowMoreRevoke = isset($cancelSpecialArr['is_allow_more_revoke']) ? boolval($cancelSpecialArr['is_allow_more_revoke']) : false;

        //撤销是否通知购票人
        $isRevokeSmsNotice = isset($cancelSpecialArr['is_revoke_sms_notice']) ? boolval($cancelSpecialArr['is_revoke_sms_notice']) : false;
        //是否往三方系统取消订单，特殊情况下使用
        //如果对接三方系统，默认都是需要往三方系统去取消的
        $isCancelThirdSystem = isset($cancelSpecialArr['is_cancel_third_system']) ? boolval($cancelSpecialArr['is_cancel_third_system']) : true;
        //是否系统原因导致的回滚，需要退回码费等特殊款项
        $isSyntaxRollback = $cancelSpecialArr['is_syntax_rollback'] ?? false;

        //是否需要进行平台退票审核处理
        $cancelTime = isset($cancelSpecialArr['cancel_time']) ? trim($cancelSpecialArr['cancel_time']) : false;
        if ($tmpTime = strtotime($cancelTime)) {
            $cancelTime = date('Y-m-d H:i:s', $tmpTime);
        } else {
            $cancelTime = false;
        }

        //是否需要取消了通知三方系统
        $isCancelNoticeThird = isset($cancelSpecialArr['is_cancel_notice_third']) ? boolval($cancelSpecialArr['is_cancel_notice_third']) : true;
        if (!in_array($cancelType, $this->_cancelTypeArr)) {
            return $this->orderReturn(0, "取消类型[{$cancelType}]不存在", ['err_code' => OrderConst::err11]);
        }

        $ordermodeArr = load_config('cancel_modify_track_map', 'business');
        if (!array_key_exists($cancelChannel, $ordermodeArr)) {
            return $this->orderReturn(0, "退票渠道[{$cancelChannel}]不存在", ['err_code' => OrderConst::err11]);
        }

        //判断下是不是本地订单 本地订单占定只能本地渠道发起的才能退
        if ((Tools::is_local_order($ordernum) || Tools::is_mtts_order($ordernum)) && $cancelChannel != OrderConst::LOCAL_MANAGE_CANCEL){   //39代表本地取消
            return $this->orderReturn(0, "线下本地订单请在本地系统发起退票", ['err_code' => OrderConst::err11]);
        }

        //判断下是否跳过预售券校验规则
        $isSkipExchangeTicket =  isset($cancelSpecialArr['is_skip_exchange_ticket']) ? boolval($cancelSpecialArr['is_skip_exchange_ticket']) : false;

        //判断下是不是审核同意的取消
        $isAuditPass = isset($cancelSpecialArr['is_audit_pass']) ? $cancelSpecialArr['is_audit_pass'] : false;
        $trackSource = isset($ordermodeArr[$cancelChannel]) ? $ordermodeArr[$cancelChannel] : \Model\Order\OrderTrack::SOURCE_INSIDE_SOAP;

        //临时新增是否景区产品线退票
        $isScenicLineRefund = isset($cancelSpecialArr['is_scenic_line_refund']) ? boolval($cancelSpecialArr['is_scenic_line_refund']) : false;

        //请求的参数
        $reqParams = [
            'ordernum'              => $ordernum,
            'cancelNum'             => $cancelNum,
            'opId'                  => $opId,
            'cancelChannel'         => $trackSource,
            'cancelType'            => $cancelType,
            'reqSerialNumber'       => $reqSerialNumber,
            'cancelRemark'          => $cancelRemark,
            'cancelTerminal'        => $cancelTerminal,
            'personIndex'           => $personIndex,
            'personIdList'          => $personIdList,
            'personInfoList'        => $personInfoList,
            'isNeedAudit'           => $isNeedAudit,
            'isAuditAgreed'         => $isAuditAgreed,
            'autoCancelFee'         => $autoCancelFee,
            'isForceCancel'         => $isForceCancel,
            'isCancelThirdSystem'   => $isCancelThirdSystem,
            'isCancelSub'           => $isCancelSub,
            'cancelTime'            => $cancelTime,
            'realCancelChannel'     => $cancelChannel,
            'isRollBack'            => $isRollBack,
            'ticketCodeList'        => $ticketCodeList,
            'isCancelNoticeThird'   => $isCancelNoticeThird,
            'isAuditPass'           => $isAuditPass,
            'isScheduledRefuseBack' => $isScheduledRefuseBack,
            'isAllowMoreRevoke'     => $isAllowMoreRevoke,
            'isRefundDeposit'       => $cancelSpecialArr['is_refund_deposit'] ?? 0,   //计时订单是否退押金 0：不退押金 1:退押金
            'isRefundOverPay'       => $cancelSpecialArr['is_refund_over_pay'] ?? 0,  //计时订单是否退超时补费金额 0：不退 1:退
            'cancelAuditRemark'     => $cancelSpecialArr['cancel_audit_remark'] ?? 0,  //提交退票审核时候的备注
            'discount'              => $cancelSpecialArr['discount'] ?? '',
            'refundOpenCode'        => $cancelSpecialArr['refund_open_code'] ?? [],//下游透传券码至上游供应商
            'isRevokeSmsNotice'     => $isRevokeSmsNotice,
            'afterSaleNum'          => $afterSaleNum,
            'batchRefundMoreIdx'    => $batchRefundMoreIdx,
            'isRetry'               => $isRetry,
            'isScenicLineRefund'    => $isScenicLineRefund,
            'isSkipExchangeTicket'  => $isSkipExchangeTicket,
            'isSyntaxRollback'      => $isSyntaxRollback,
        ];

        return $this->orderReturn(200, '', $reqParams);
    }

    /**
     * 退票锁处理
     * <AUTHOR>
     * @date 2019-07-15
     *
     * @param  int  $ordernum  订单号
     * @param  string  $reqSerialNumber  退票流水号
     * @param  int  $cancelType  退票类型
     * @param  int  $leftNum  剩余有效票数
     * @param  int  $opId  退票用户
     * @param  int  $isNeedAudit  是否需要审核
     * @param  int  $personIndex  指定退票的下标
     *
     * @return array
     */
    public function refundLock($ordernum, $reqSerialNumber, $cancelType, $leftNum, $opId, $isNeedAudit, $personIndex = 0 ,$isThirdAuditRes ='')
    {
        //退票频率控制
        $frequencyRes = $this->_frequencyCheck($ordernum, $reqSerialNumber, $cancelType, $leftNum, $opId, $isNeedAudit, $personIndex);
        if ($frequencyRes['code'] != 200 && !$isThirdAuditRes) {
            return $frequencyRes;
        }

        //验证协调控制
        $verifyRes = $this->_verifyCheck($ordernum, $reqSerialNumber, $cancelType, $leftNum, $opId);
        if ($verifyRes['code'] != 200) {
            return $verifyRes;
        }

        //继续执行
        return $this->orderReturn(200, '');
    }

    /**
     * 删除频率控制锁
     * <AUTHOR>
     * @date 2019-07-15
     *
     * @param  int  $ordernum  订单号
     *
     * @return array
     */
    public function removeRefundLock($ordernum)
    {
        $ordernum = strval($ordernum);

        //如果是开发环境，直接跳过
        if (ENV == 'LOCAL') {
            return $this->orderReturn(200, '测试环境不处理');
        }

        if (!isset($this->_lockKeyArr[$ordernum])) {
            return $this->orderReturn(200, '没有找到key');
        }

        $cacheDriver = Cache::getInstance('redis');
        $lockKey     = $this->_lockKeyArr[$ordernum];
        $res         = $cacheDriver->rm($lockKey);

        if ($res) {
            return $this->orderReturn(200, '删除成功');
        } else {
            return $this->orderReturn(0, '删除失败');
        }
    }

    /**
     * 退票时需要问下其他的一些系统，该订单是否可以进行退票操作，比如优惠订单、营销订单
     * TODO:正常情况应该是要业务方主动将不可退的消息通知的订单这边
     *
     * <AUTHOR>
     * @date   2019-07-15
     *
     * @param  string  $ordernum  订单号
     * @param  array  $ticketInfo  门票信息
     * @param  int  $cancelNum  取消数量
     * @param  string  $cancelType  取消类型
     * @param  array  $orderInfo  订单信息
     * @param  int  $cancelChannel  外部取消渠道
     * @param  bool  $isPresumingOrder  是否是温泉系统订单
     *
     * @return []
     */
    public function otherSystemCheck($ordernum, $ticketInfo, $cancelNum, $cancelType, $orderInfo,$realCancelChannel,$isPresumingOrder = false,$isRollBack = false)
    {
        $tnum        = intval($orderInfo['tnum']);
        $verifiedNum = intval($orderInfo['verified_num']);
        $canUseNum   = intval($tnum - $verifiedNum);
        $isRefund    = $cancelNum == $canUseNum ? true : false;

        $isPay  = true;
        if ($orderInfo['pay_status'] == 2){
            $isPay = false;
        }
        if ($isPay){  //支付后才需要判断的
            //前面已有年卡订单状态判断、年卡是否过期判断 这里放开 --年卡退已激活
            /*if ($ticketInfo['p_type'] == 'I')
                //如果是年卡订单，如果已经绑定了就不能取消
                $annualModel = new \Model\Product\AnnualCard();
                $annualMap   = $annualModel->getStatusByOrdernum($ordernum);

                //还需要再查一次虚拟卡对应的状态
                $annualInfo = $annualModel->getCardInfoByVirtual([$annualMap[$ordernum]['virtual_no']], 'memberid,status')[0];
                //年卡状态不等于待激活 或者有绑定用户的  或者订单状态不是未使用或者已过期的
                if ($annualMap[$ordernum]['status'] == 1 && ($annualInfo['memberid'] > 0 || ($annualInfo['annual_status'] !=0 && !in_array($orderInfo['status'], [0, 2])))) {
                    return $this->orderReturn(0, "非待激活的年卡订单不能取消", ['err_code' => OrderConst::err1]);
                }

                //订单状态非未使用或已过期的  不可退
                if ($annualMap[$ordernum]['status'] == 1 && !in_array($orderInfo['status'], [0, 2])) {
                    //这个错误码有些问题，之前就是使用 OrderConst::err1
                    return $this->orderReturn(0, "年卡订单不能取消", ['err_code' => OrderConst::err1]);
                }
            }*/

            //如果是拼团订单，需要判断是否可退
            $groupBookBiz = new \Business\Wechat\GroupBooking();
            $groupRes     = $groupBookBiz->orderCancelInterceptorNew($ordernum);
            if ($groupRes['code'] != 200) {
                return $this->orderReturn(0, "拼团订单不可退", ['err_code' => OrderConst::err1]);
            }
        }
        //温泉系统订单判断
        if ($isPresumingOrder && $realCancelChannel != OrderConst::PRESUMING_CANCEL && $cancelType == 'common' && !$isRollBack) {   //代表取消的是温泉的订单了
            $presumingMdl = new Presuming();
            //计算下绑定数量
            $bindOrder = [];
            if ($orderInfo['ifpack'] == 2) {  //套票子票的绑定数量是依赖门票码，所以要获取所有门票码
                $orderQueryModel = new OrderQuery();
                $touristInfos    = $orderQueryModel->getOrderTicketCodes($ordernum);
                if ($touristInfos) {
                    $bindOrder = array_column($touristInfos, 'chk_code');
                }
            } else {
                $bindOrder = [$ordernum];
            }
            if ($bindOrder) {
                $presumingBindInfo = $presumingMdl->getOrderBindPresumingInfo($bindOrder);
                if ($presumingBindInfo) {
                    if ($presumingBindInfo['type'] == 1) {  //1次销售的要判断下绑定数量
                        $bindNum = $presumingMdl->getOrderBindPresumingNum($bindOrder);
                        if ($orderInfo['tnum'] - $bindNum < $cancelNum) {
                            return $this->orderReturn(0, "取消数量超过温泉订单绑定数量", ['err_code' => OrderConst::err1423]);
                        }
                    } else {    //二销的只要判断如果完全取消有绑定就不然他取消了
                        if ($isRefund) {
                            return $this->orderReturn(0, "请先解除温泉二销产品绑定关系", ['err_code' => OrderConst::err1424]);
                        }
                    }

                }
            }

        }
        //可以退
        return $this->orderReturn(200, '');
    }

    /**
     * 是否需要进行退票规则权限的判断
     * 未支付或者是原始供应商在退票审核通过的情况下不需要判断
     * 其他情况都需要进行判断
     *
     * <AUTHOR>
     * @date   2019-07-01
     *
     * @param  int  $ordernum  订单号
     * @param  int  $payStatus  支付状态
     * @param  int  $cancelMemberId  取消用户ID
     * @param  int  $appliDid  原始供应商ID
     * @param  bool  $isNeedAudit  是否需要退票审核
     *
     * @return bool
     */
    public function isNeedCheckRefundRule($ordernum, $payStatus, $cancelMemberId, $appliDid, $isNeedAudit = true,$isRollBack = false)
    {
        if ($payStatus != 1 || ($isNeedAudit == false && $cancelMemberId == $appliDid) || $isRollBack) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 基础订单状态检测，判断是否可以退票
     * <AUTHOR>
     * @date   2019-07-02
     *
     * @param  string  $ordernum  订单号
     * @param  int  $cancelNum  退票数
     * @param  array  $cancelType  取消类型
     * @param  array  $orderInfo  订单数据
     * @param  array  $ticketInfo  票类数据
     * @param  array  $personIdList  取消用户身份证
     * @param  bool  $isThirdOrder  是否对接三方系统订单
     * @param  int  $carriage  运费
     * @param  bool  $isPresumingOrder  是否是温泉订单
     *
     * @return []
     */
    public function statusCheck($ordernum, $cancelNum, $cancelType, $orderInfo, $ticketInfo, $personIdList, $isThirdOrder,$carriage = 0,$isPresumingOrder = false,$isAllowMoreRevoke = false, $isForceCancel = false)
    {
        //积分商城订单不可退
        if ($orderInfo['ordermode'] == 35) {
            return $this->orderReturn(0, '', ['err_code' => OrderConst::err1420]);
        }

        //拉卡拉支付订单不允许取消
        if ($orderInfo['paymode'] == 11 || $orderInfo['paymode'] == 12) {
            return $this->orderReturn(0, '', ['err_code' => OrderConst::err1087]);
        }

        //如果是未支付的订单不能修改
        if (!in_array($orderInfo['pay_status'],[0,1]) && $orderInfo['ordermode'] != 24 && $orderInfo['ordermode'] != 44 && !$isPresumingOrder) {
            if ($cancelNum < $orderInfo['tnum']) {
                return $this->orderReturn(0, '未支付的订单只能整笔取消', ['err_code' => OrderConst::err11]);
            }
        }

        $numModify   = intval($ticketInfo['num_modify']);
        $tnum        = intval($orderInfo['tnum']);
        $verifiedNum = intval($orderInfo['verified_num']);
        $originNum   = intval($orderInfo['origin_num']);
        $refundNum   = intval($orderInfo['refund_num']);
        $canUseNum   = intval($tnum - $verifiedNum);
        $refundNumScale   = $ticketInfo['refund_num'] ?? -1;
        $refundLeastNum   = $ticketInfo['refund_least_num'] ?? 0;

        //状态和退票数的判断
        if ($cancelType == 'revoke') {
            //撤改
            //220726需求要求去除部分使用下的撤改

            //if ($orderInfo['status'] != 1 && !($orderInfo['status'] == 5 && $isAllowMoreRevoke)) {
            //    //订单需要验证状态下，才可以进行撤改
            //    return $this->orderReturn(0, '订单不是使用状态，不允许撤改', ['err_code' => OrderConst::err19]);
            //}

            //撤改票数不能超过已经验证的票数
            if ($cancelNum > $verifiedNum) {
                return $this->orderReturn(0, '撤改票数不能大于验证票数', ['err_code' => OrderConst::err41]);
            }

            //判断是否取消整张票
            $isTotalCancel = $cancelNum == $verifiedNum ? true : false;
        } else {
            //订单不是未使用状态，不允许退款   9=待预约
            if (!in_array($orderInfo['status'], [0, 2, 7, 4,SpecialOrderStatus::SEND_GOODS_CODE,SpecialOrderStatus::TO_SEND_GOODS_CODE,CommonOrderStatus::WAIT_APPOINTMENT_CODE,CommonOrderStatus::WAIT_PRINTTICKET_CODE])) {

                return $this->orderReturn(0, '', ['err_code' => OrderConst::err19]);
            }

            //取消的票数不能超过剩余的票数
            if ($cancelNum > $canUseNum) {
                return $this->orderReturn(0, '退票数不能大于剩余票数', ['err_code' => OrderConst::err41]);
            }

            //判断是否取消整张票
            $isTotalCancel = $cancelNum == $canUseNum ? true : false;

            if (\Business\Product\Ticket::judgeNewChangeRule($ticketInfo['p_type'])) {
                //根据产品类型判断是否走新的退改参数规则
                //如果还有配置了 - 增加减少人数为初始订单的百分比
                if (!$isForceCancel) {
                    //判断是否是强制取消，强制取消不走退票数量规则
                    if ($refundNumScale > 0) {
                        //可退的票数
                        $canCancelNum = floor($originNum * ($refundNumScale / 100));

                        //加上这次总的退票数
                        $totalRefundNum = $refundNum + $cancelNum;
                        if ($totalRefundNum > $canCancelNum && $orderInfo['pay_status'] == 1 && !$isTotalCancel) {
                            return $this->orderReturn(0, "退票比例超过上限【" . $refundNumScale . "%】", ['err_code' => OrderConst::err41]);
                        }
                    }

                    //最少退票数 0:不选 大于0:退票数  订单里面退完最少要剩几张
                    if ($refundLeastNum > 0) {
                        //剩余的票数减去退票数,要大于（最少不低于x张的票属性）
                        $residueNum = $tnum - $cancelNum;
                        if ($refundLeastNum > $residueNum && $orderInfo['pay_status'] == 1 && !$isTotalCancel) {
                            return $this->orderReturn(0, "最少不低于{$refundLeastNum}张", ['err_code' => OrderConst::err41]);
                        }
                    }
                }
            } else {
                //旧的退改参数规则
                //如果还有配置了 - 增加减少人数为初始订单的百分比
                if ($numModify > 0) {
                    //可退的票数
                    $canCancelNum = floor($originNum * ($numModify / 100));

                    //加上这次总的退票数
                    $totalRefundNum = $refundNum + $cancelNum;
                    if ($totalRefundNum > $canCancelNum && $orderInfo['pay_status'] == 1 && !$isTotalCancel) {
                        return $this->orderReturn(0, "退票比例超过上限【" . $numModify . "%】", ['err_code' => OrderConst::err41]);
                    }
                }

            }

            //可能有同步原因导致终端库那边没同步到平台，这边加个判断防止没同步的情况
            $terminalOrderMdl  = new TerminalOrderHandler();
            $terminalOrderInfo = $terminalOrderMdl->getTerminalOrderInfo($ordernum);
            if ($terminalOrderInfo && $terminalOrderInfo['status'] == 1){
                return $this->orderReturn(0, "订单已使用无法取消", ['err_code' => OrderConst::err19]);
            }
            //判断下是否使用过分终端
            $terminalDbMdl = new TerminalDbHandler();
            $checkSubUsedRes  = $terminalDbMdl->getOneBranchTerminalInfo($ordernum);
            if ($checkSubUsedRes){  //代表使用过了，可是他的可退数量实际是少于tnum的
                $canRefundNum = $orderInfo['can_refund'];
                if ($canRefundNum == -1) {  //todo 这种旧数据没办法了,不能退就不能退吧
                    $canRefundNum = $orderInfo['origin_num'] - $orderInfo['verified_num'] - $orderInfo['refund_num'];
                }
                if ($cancelNum > $canRefundNum) {
                    return $this->returnData(0, '超过分终端可取消数量', ['err_code' => OrderConst::err1501,'err_msg' => '超过分终端可取消数量']);
                }
            }
        }
        //判断下取消的特产金额
        if ($ticketInfo['p_type'] == 'J' && $carriage > 0){
            //要判断下运费了
            $productExtInfo = json_decode($orderInfo['product_ext'],true);
            if ($productExtInfo && isset($productExtInfo['carriage'])){
                $refundCarriage    = isset($productExtInfo['refundCarriage']) ? $productExtInfo['refundCarriage'] : 0;
                $canRefundCarriage = $productExtInfo['carriage'] - $refundCarriage;
                if ($carriage > $canRefundCarriage || $canRefundCarriage < 0){
                    return $this->orderReturn(0, '退票运费有误', ['err_code' => OrderConst::err1422]);
                }
            }else{    //传入的特产金额都大于0了，如果还不存在这个数组说明有问题
                return $this->orderReturn(0, '退票运费有误', ['err_code' => OrderConst::err1422]);
            }
        }
        //if ($isThirdOrder) {
        //    //三方系统需要做身份证数量判断
        //    if ($ticketInfo['batch_check'] == 3 && !$isTotalCancel) {
        //        //一票一证属性的门票，只取消部分票的情况下，必须传身份证给第三方系统
        //        if (empty($personIdList)) {
        //            return $this->orderReturn(0, '', ['err_code' => OrderConst::err1103]);
        //        }
        //
        //        $personIdCnt = count($personIdList);
        //        if ($personIdCnt != $cancelNum) {
        //            return $this->orderReturn(0, '', ['err_code' => OrderConst::err1104]);
        //        }
        //    }
        //}

        return $this->orderReturn(200, '');
    }

    /**
     * 判断订单是否而已可以执行退款
     * @param  array  $orderNumArr  订单数组
     * @param  int  $orderOpId  操作人id - 员工的话就是员工上级id
     * @param  bool  $isAuditPass  是否是审核通过的 默认不是
     * @param  array  $orderInfo  订单信息
     *      {
     *           "4028945" : {
     *              "tid" : "31993", "ordernum" : "4028945", "tprice": 200, "paymode" : 2, "ordermode" : 0,
     *               "pay_status" : 1, "status" : 0, "member" : 3385, "playtime" :  "2018-01-15"
     *           }
     *      }
     * @param  array  $orderAddon  订单扩展信息
     *      {
     *          "4028945" : {
     *              "orderid" : "4028945", "ifprint" : 0, "id": 7671737, "ifpack" : 0, "pack_order" : ""
     *          }
     *      }
     * @param  array  $orderDetail  订单详情
     *      {
     *          "4028945" : {
     *              "orderid" : "4028945", "series" : "", "aids_money": "", "concat_id" : 0, "aids" : ""
     *          }
     *      }
     * @param  array  $baseTicketInfo  门票信息
     *     {
     *          "31993" : {
     *              "id" : "31993", "title" : "成人票", "refund_rule": 3, "apply_did" : 6970, "refund_early_time" : 0
     *          }
     *     }
     * @param  array  $ticketExt  门票扩展信息
     *       {
     *           "31993" : {
     *               "tid" : "31993", "id" : "28971", "v_time_limit": 0
     *           }
     *       }
     * @param  int  $trackChannel  追踪记录的渠道 : 21验证服务器改单验证过来的权限判断
     * @param  array  $packOrderRes  套票子票关系
     *                              [
     *                                  '24433102' => [ //主票订单号
     *                                      '24433103', //子票订单号
     *                                      '24433104'
     *                                  ]
     *                              ]
     *
     * @param  array  $refundParams  退票参数
     *@return array 返回有权限退款的订单
     *     {
     *          "canRefund" : {
     *             "4027950" : {
     *                   "code" => 200, "msg" => "下单人操作"
     * }
     *          },
     *          "refuseRefund" : {
     *               "4027948" : {
     *                  "code" => 1414, "msg" => "已过退票有效期"
     *              }
     *          }
     *     }
     *
     * <AUTHOR>
     * @date   2017-11-13
     * @modified dwer.cn 2019-06-27
     *
     *   //1、 未出票未使用 2、未出票部分使用，部分验证 3、已出票未使用
     *   //4、已出票部分验证，部分取消  5、未出票已过期 6、已出票已过期
     *   //1、微平台&平台 2、云票务to旅行社&计调下单
     *   //3、终端、云票务to散客/自助机/闸机/微商城
     *
     *   //退票字段 - refund_rule说明如下
     *   //0=有效期内可退 ： 未使用订单末级分销商可退；过期订单供应商可退，
     *   //1=游玩日期内可退，阶梯退：未使用订单末级分销商可退；过期订单供应商可退
     *   //2=不可退：分销商供应商都不可退
     *   //3=随时退：未使用订单末级分销商可退；过期订单分销商可退，供应商可退。'
     *   //-1=不可退且是可提现
     *
     */
    public function checkCanRefund($orderNumArr, $originOpId, $orderInfo = [], $orderAddon = [], $orderDetail = [], $baseTicketInfo = [], $ticketExt = [], $trackChannel = 0,
                                   $packOrderRes = [], $isAuditPass = false, $isForceCancel = false, $refundParams = [])
    {
        // 埋点
        (new LegacyServiceTrafficLog())->create();

        $resArr = ['canRefund' => [], 'refuseRefund' => []];
        if (empty($orderNumArr) || empty($originOpId)) {
            return $resArr;
        }

        // 员工id 找到上级id
        $memberModel     = $this->_instanceMemberModel();
        $findParentIdRes = $memberModel->getStaffBySonId($originOpId);
        if ($findParentIdRes) {
            $originOpId = $findParentIdRes['parent_id'];
        }

        //获取进行订单和门票信息
        $orderAndTicketInfo = $this->_orderAndTicketInfo($orderNumArr, $orderInfo, $orderAddon, $orderDetail,
            $baseTicketInfo, $ticketExt);
        if ($orderAndTicketInfo === false) {
            return $resArr;
        }
        $orderInfoArr       = $orderAndTicketInfo['order'];
        $orderAddonArr      = $orderAndTicketInfo['orderaddon'];
        $ticketInfoArr      = $orderAndTicketInfo['ticket'];
        $orderDetailInfoArr = $orderAndTicketInfo['orderdetail'];
        $ticketExtArr       = $orderAndTicketInfo['ticketExt'];

        $canRefundArr = [];
        $refuseArr    = [];

        foreach ($orderInfoArr as $orderVal) {
            //订单信息
            $ordernum     = strval($orderVal['ordernum']);
            $tid          = $orderVal['tid'];
            $ordermode    = (string)$orderVal['ordermode'];
            $salerAccount = $orderVal['salerid']; // 销售的景区id
            $orderOwnId   = $orderVal['member'];
            $buyId        = $orderVal['member'];
            $orderStatus  = $orderVal['status'];
            $payStatus    = $orderVal['pay_status'];
            $endTime      = $orderVal['endtime'] ?? '';
            $ticketInfo    = $ticketInfoArr[$tid];
            $ticketExtInfo = $ticketExtArr[$tid];
            $applyDid = $ticketInfo['apply_did'];

            // 为针对特殊订单的处理，订单的操作人分离出来
            $opId = $originOpId;

            //退票规则：添加了这个值 -1=不可退且是可提现
            //所以=-1的时候也是不可退
            if ($ticketInfo['refund_rule'] == -1) {
                $ticketInfo['refund_rule'] = 2;
            }

            //年卡未激活 订单状态为未使用的情况  退票属性设置成随时退
            if ($ticketInfo['p_type'] == 'I' && $orderStatus == 0) {
                $ticketInfo['refund_rule'] = 3;
            }

            // 变量赋值
            $refundEarlyMinu = $ticketInfo['refund_early_time'];
            $modifyLimitTime = isset($ticketInfo['modify_limit_time']) ? $ticketInfo['modify_limit_time'] : 0;
            $refundAfterTime = $ticketInfo['refund_after_time'] ?? 0;

            // 获取景点的直接供应方id
            $salerInfoArr = $memberModel->getInfoByAccount($salerAccount);
            $salerId      = $salerInfoArr['id'];

            // 预售券兑换订单特殊处理，特定场景下将操作人改为末级买家
            if (
                $ordermode == OrderChannel::EXCHANGE_COUPON_REDEEM_CHANNEL && // 预售券兑换订单
                !in_array($opId, [1, $buyId]) // 末级买家、系统走自己的逻辑，不需要特殊处理
            ) {
                // 预售券权益取消（联动取消）和售后取消
                if (in_array($refundParams['realCancelChannel'], [CancelChannelEnum::EXCHANGE_BASE_TICKET_CANCEL, CancelChannelEnum::AFTER_SALE_REFUND])) {
                    $opId = $buyId;
                }

                // 白名单&次末级
                if ($opId != $applyDid) { // 供应商不走该逻辑
                    $distributionChain = (new OrderAidsSplitQuery())->getSplitListByOrderIdSubNew([$ordernum]);
                    if (empty($distributionChain)) {
                        return $this->orderReturn(0, '获取分销链失败');
                    }
                    // 找到level为0或-1的分销链
                    $chain = array_filter($distributionChain, function($item) {
                        return in_array($item['level'], [0, -1]);
                    });
                    if (empty($chain)) {
                        return $this->orderReturn(0, '获取分销链失败', ['inner_msg' => '获取分销链失败']);
                    }
                    $lastBuyerId = $chain[0]['buyerid'];
                    $lastSellerId = $chain[0]['sellerid'];
                    if ($opId == $lastSellerId) {
                        $otaSystemMerchantIds = (new OtaSystemService())->getAllMemberIds();
                        if (in_array($lastBuyerId, $otaSystemMerchantIds)) {
                            $opId = $buyId;
                        }
                    }
                }
            }

            // 团单 -- 不兼容的修改取消规则 先过滤
            if (in_array($ordermode, ['24','44'], true)) {
                // 管理员 以及 cli 模式下不进行判断
                if ($opId == 1 || PHP_SAPI == 'cli') {
                    $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '管理员或cli模式团单订单修改'];
                    continue;
                }
                $numMapping = [];
                if (!empty($refundParams)) {
                    $numMapping = [
                        $ordernum => $orderVal['tnum'] - $refundParams['cancelNum'],
                    ];
                }

                // 调用modify中修改判断的逻辑
                $modifyBiz    = new \Business\Order\Modify();
                $checkTeamRes = $modifyBiz->checkTeamOrdeModifyRule($orderVal, $opId, $orderAddonArr, 0, $numMapping);

                if ($checkTeamRes['status'] == 1) {
                    $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '团单订单修改'];
                    continue;
                }

                $code = $checkTeamRes['status'];
                if ($checkTeamRes['status'] == 0) {
                    $code = 124;
                }
                $msg = $checkTeamRes['msg'] ?? '订单退票权限验证失败';

                $refuseArr[$ordernum] = ['code' => $code, 'msg' => $msg];
                continue;
            }

            // 未支付订单不走以下判断
            if ($payStatus == 2 && $orderStatus != 3) {
                // order_track 找到登陆下单人
                $buyId    = $this->_findOriginBuyId($ordernum);
                if (in_array($opId, [$buyId, $orderOwnId, $applyDid])) {
                    $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '未支付订单取消'];
                } else {
                    $refuseArr[$ordernum] = ['code' => 124, 'msg' => '权限不够，订单不可退'];
                }

                continue;
            }

            $addonInfoArr    = $orderAddonArr[$ordernum];
            $orderJoinStatus = '';

            switch ($addonInfoArr['ifprint']) {
                case '1':
                    // 已出票
                    if ($orderStatus == 0) {
                        // 未使用
                        $orderJoinStatus = 3;
                    } elseif ($orderStatus == 7) {
                        // 部分使用
                        $orderJoinStatus = 4;
                    } elseif ($orderStatus == 2) {
                        // 已过期
                        $orderJoinStatus = 6;
                    }elseif ($orderStatus == CommonOrderStatus::WAIT_APPOINTMENT_CODE){
                        //待预约 正常不应该会出现取票的
                        $orderJoinStatus = 9;
                    }
                    break;
                case '2':
                    // 部分出票  新增取票状态会出现取消按钮被隐藏，产品这边直接兼容成未取票
                    if ($orderStatus == 0) {
                        // 未使用
                        $orderJoinStatus = 1;
                    } elseif ($orderStatus == 7) {
                        // 部分使用
                        $orderJoinStatus = 2;
                    } elseif ($orderStatus == 2) {
                        // 已过期
                        $orderJoinStatus = 5;
                    }elseif ($orderStatus == CommonOrderStatus::WAIT_APPOINTMENT_CODE){
                        //待预约 正常不应该会出现取票的
                        $orderJoinStatus = 9;
                    }
                    break;
                case '0':
                    // 未出票
                    if ($orderStatus == 0) {
                        // 未使用
                        $orderJoinStatus = 1;
                    } elseif ($orderStatus == 7) {
                        // 部分使用
                        $orderJoinStatus = 2;
                    } elseif ($orderStatus == 2) {
                        // 已过期
                        $orderJoinStatus = 5;
                    }elseif ($orderStatus == CommonOrderStatus::WAIT_APPOINTMENT_CODE){
                        //待预约
                        $orderJoinStatus = 9;
                    }elseif ($orderStatus == SpecialOrderStatus::TO_SEND_GOODS_CODE){  //特产的待发货
                        $orderJoinStatus = 7;
                    }elseif ($orderStatus == SpecialOrderStatus::SEND_GOODS_CODE){   //特产已发货
                        $orderJoinStatus = 8;
                    }elseif ($orderStatus == CommonOrderStatus::WAIT_PRINTTICKET_CODE){  //等待出票状态 三方为返回的情况
                        $orderJoinStatus = 10;
                    }
                    break;
            }
            // 在这些之外的订单 因为不在返回的数据里面--在列表中会不显示按钮（不在可取消订单中）&&在取消接口中是可以取消（不在拒绝取消数据中）
            if (!in_array($orderJoinStatus, [1, 2, 3, 4, 5, 6, 7, 8, 9,10], true)) {
                pft_log('order_refund/debug', json_encode(['orderJoinStatus', $ordernum, $orderJoinStatus, $orderNumArr, $addonInfoArr['ifprint'], $orderStatus]));
                continue;
            }

            // 如果是套票子票下单模式 则需要找到主票的下单来源 进行判断--主订单拥有者
            if ($ordermode == 23) {
                $queryParams = [$addonInfoArr['pack_order']];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernum', $queryParams);
                if ($queryRes['code'] != 200) {
                    $log = [
                        'parame'   => $addonInfoArr['pack_order'],
                        'apiData'  => $queryRes,
                        'function' => 'queryOrderInfoByOrdernum',
                    ];
                    pft_log('checkCanRefund/Api/error', json_encode($log));
                }
                $packOrderInfoArr = $queryRes['data'];
                //将ordermode修改为主票的值
                $lastOrdermode = (string)$packOrderInfoArr['ordermode'];
                // 修改订单的拥有者
                $orderOwnId = $packOrderInfoArr['member'];
            } else {
                $lastOrdermode = (string)$ordermode;
            }

            if (in_array($lastOrdermode, ['0', '19', '20', '58', '3', '17', '33', '9','50','55', '59', (string)OrderChannel::EXCHANGE_COUPON_REDEEM_CHANNEL], true)) {
                // 微平台+平台+外部接口+会员卡购票+淘宝码商+e福州订单+团购导码 + 通兑换兑换
                $joinMode = 1;
            }

            if (in_array($lastOrdermode, ['16'], true)) {
                // 计调下单+云票务to旅行社
                $joinMode = 2;

                // order_track 找到登陆下单人
                $buyId = $this->_findOriginBuyId($ordernum);
            }

            if (in_array($lastOrdermode, ['11','45'], true)) {
                // 微商城
                $joinMode = 3;

                if ($orderOwnId == 112) {
                    // order_track 找到登陆下单人
                    $buyId = $this->_findOriginBuyId($ordernum);
                }
            }

            if (in_array($lastOrdermode, ['15', '12', '14', '13', '18', '22', '34', '35', '38', '43','49', '56', '57'], true)) {
                // 终端+云票务to散客+自助机+闸机+微商城+二级店铺+年卡+一卡通+云闪付
                $joinMode = 4;

                if ($orderOwnId == 112 || $lastOrdermode == 15) {
                    // order_track 找到登陆下单人
                    $buyId = $this->_findOriginBuyId($ordernum);
                }
            }

            if (in_array($lastOrdermode, ['10'], true)) {
                // order_track 找到登陆下单人
                $buyId = $this->_findOriginBuyId($ordernum);
                if ($orderOwnId == 112) {
                    // 云票务散客下单
                    $joinMode = 3;
                } else {
                    // 云票务给分销商下单
                    $joinMode = 2;
                }
            }

            //待预约退票规则 校验
            $checkAppointmentRes = self::checkAppointmentRefund($applyDid,$opId,$orderDetailInfoArr[$ordernum],$ticketInfo);
            if(!$checkAppointmentRes['res'] && !$isForceCancel && $trackChannel != 21){
                $refuseArr[$ordernum] = [
                    'code' => '438',
                    'msg'  => '票属性配置：预约成功后不可退',
                ];
                continue;
            }
            $ticketTypeEnum = TicketTypEnum::find($ticketInfo['p_type']);
            //只有拆分支持的产品类型才进行权限判断
            if (
                !empty($ticketTypeEnum->refundWithProductProviderDirectory) && // 目前只拆分了门票、演出、套票
                (empty($isAuditPass) || (
                    $ordermode == '10' || // 下单方式为云票务
                    $refundParams['realCancelChannel'] == CancelChannelEnum::AFTER_SALE_REFUND // 取消渠道为售后退票
                ))
            ) {
                $ticketRefund = new TicketRefund();
                $ticketRefund->setProductType($ticketTypeEnum->refundWithProductProviderDirectory);
                //取消类型,当时按钮展示的时候，默认使用common，实际取消接口取消的时候，还是走类型判断
                $cancelType = $refundParams['cancelType'] ?  : 'common';
                $realIsForceCancel = $refundParams['packageOriginIsForceCancel'] ?? $isForceCancel;
                if ($isForceCancel) {
                    $realIsForceCancel = $isForceCancel;
                }
                $cancelEnumType = CancelTypeEnum::formatConstants([
                    'cancelType'=>$cancelType, 'isForceCancel'=>$realIsForceCancel, 'isRollBack'=>$refundParams['isRollBack']]);
                $ticketRefund->setProvider($cancelEnumType);
                $memberInfo = (new CommonTools)->getOperatorInfo($opId);
                if ($memberInfo['code'] != 200) {
                    $refuseArr[$ordernum] = ['code' => $memberInfo['code'], 'msg' => $memberInfo['msg']];
                    continue;
                }
                $orderInfoToCheck = $orderVal;
                $orderInfoToCheck['pack_order'] = $addonInfoArr['pack_order'];
                $orderInfoToCheck['ifpack'] = $addonInfoArr['ifpack'];
                $orderInfoToCheck['ifprint'] = $addonInfoArr['ifprint'];
                $printRefundRuleParamDTO = $ticketRefund->toCheckPrintRefundRuleParamDTO($orderInfoToCheck, $ticketInfo, $memberInfo['data'], $refundParams, $orderDetailInfoArr);
                $checkPrintRefundRule = $ticketRefund->checkPrintRefundRule($printRefundRuleParamDTO);

                //端午期间去除这个日志
                $filterLogStartTime = strtotime('2025-05-31 00:00:00');
                $filterLogEndTime   = strtotime('2025-06-02 23:59:59');
                if (time() < $filterLogStartTime || time() > $filterLogEndTime) {
                    pft_log('order_refund/debug', json_encode(['checkPrintRefundRule_result',$ordernum, $checkPrintRefundRule, $addonInfoArr, $orderDetailInfoArr, $packOrderRes, $opId, $checkPrintRefundRule, $printRefundRuleParamDTO]));
                }

                if ($checkPrintRefundRule['code'] != 200) {
                    //供应身份时，预约规则和取票退票规则取
                    $refundAppointment = $ticketInfo['reserve_refund_rule'] ?? -1;
                    if ($refundAppointment > 0 && $checkAppointmentRes['res'] && $checkAppointmentRes['reservationOrder']
                        && in_array($opId, [$applyDid, $salerId]) && $ticketInfo['refund_rule'] != 2) {
                        $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '预约不可退特殊逻辑放行'];
                    } else {
                        $refuseArr[$ordernum] = [
                            'code' => $checkPrintRefundRule['code'],
                            'msg'  => $checkPrintRefundRule['msg'],
                            'log_err_msg'  => $checkPrintRefundRule['log_err_msg'],
                        ];
                    }
                    pft_log('order_refund/debug', json_encode(['checkPrintRefundRule_after',$ordernum, $canRefundArr, $refuseArr, $checkPrintRefundRule, $refundAppointment]));
                    continue;
                } else {
                    //先校验子票的不可退属性，之后再继续走子票for循环；校验随子票退，子票配置了【不可退】时，主票返回false
                    if ($addonInfoArr['ifpack'] == 1 && $ticketInfo['refund_rule'] == 4) {
                        $sonHasErr = false;
                        foreach ($packOrderRes[$ordernum] as $sonOrderNum) {
                            $sonOrderInfo = $orderInfoArr[$sonOrderNum];
                            $sonTicket = $ticketInfoArr[$sonOrderInfo['tid']];
                            if ($sonTicket['refund_rule'] == 2) {
                                $msg = "子票【{$sonOrderInfo['ordernum']}】配置了【不可退】属性";
                                $refuseArr[$ordernum] = [
                                    'code' => '4007',
                                    'msg'  => $msg,
                                    'log_err_msg'  => $msg,
                                ];
                                $sonHasErr = true;
                                pft_log('order_refund/debug', json_encode(['checkPrintRefundRule_after_son_limit',$ordernum, $canRefundArr, $refuseArr, $sonTicket, $sonOrderInfo, $sonOrderNum]));
                                break;
                            }
                        }
                        //套票主票-不再往下走_checkRefundOpPower方法
                        if (empty($sonHasErr)) {
                            $canRefundArr[$ordernum] = ['code' => 200, 'msg' => $checkPrintRefundRule['msg'], 'log_err_msg' => $checkPrintRefundRule['log_err_msg']];
                        }
                        continue;
                    }
                    if (in_array($opId, [$buyId, $orderOwnId]) && $addonInfoArr['ifprint'] == 1) {
                        $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '取票退票规则末级购买者已取票放行'];
                        //不再往下走_checkRefundOpPower方法
                        continue;
                    }
                    if ($opId == $applyDid && $addonInfoArr['ifprint'] ==2) {
                        $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '取票退票规则供应商逻辑放行'];
                        continue;
                    }
                }
            }


            //拿到演出的开场时间
            if ($orderDetailInfoArr[$ordernum]['series']) {
                $Mseries   = unserialize($orderDetailInfoArr[$ordernum]['series']);
                $showBegin = $Mseries[4];
            } else {
                $showBegin = '';
            }

            // 调用人员和退票时间限制的权限 - 非随时退,有效退,不可退的情况
            if (!in_array($orderJoinStatus, [5, 6 , 9, 7, 8]) && !in_array($ticketInfo['refund_rule'], [0, 2, 3, 4])) {
                if (in_array($orderInfoArr[$ordernum]['product_type'], ['A', 'B', 'C', 'F', 'G'])) {
                    //新验证退款时间规则
                    $checkRefundTimeRes = $this->checkRefundTimeRuleNew($ticketInfo, $ticketExtInfo, $orderInfoArr[$ordernum], $opId);
                } else {
                    // 验证退款时间规则
                    $checkRefundTimeRes = $this->checkRefundTimeRule(
                        $refundEarlyMinu,
                        $orderInfoArr[$ordernum]['playtime'],
                        $showBegin,
                        $modifyLimitTime,
                        $opId,
                        $ticketInfo['apply_did'],
                        $refundAfterTime
                    );
                }

                if (!in_array($checkRefundTimeRes['code'], [100, 200])) {
                    $refuseArr[$ordernum] = [
                        'code' => $checkRefundTimeRes['code'],
                        'msg'  => $checkRefundTimeRes['msg'],
                    ];
                    continue;
                }

                // 当验证  退款的时间 已经超出了规定时间内  定为过期
                if ($checkRefundTimeRes['code'] == 100) {
                    $orderJoinStatus = 6;
                }
            }

            // 调用判断人员和订单状态的权限
            $refundPower = $this->_checkRefundOpPower($ordernum,
                $opId,
                $buyId,
                $orderOwnId,
                $ticketInfo['apply_did'],
                $salerId,
                $ticketInfo['refund_rule'],
                $joinMode,
                $orderJoinStatus,
                $trackChannel,
                $packOrderRes,
                $orderInfoArr,
                $ticketInfoArr,
                $isAuditPass,
                $endTime,
                $ticketExtInfo,
                $showBegin
            );

            if ($refundPower['code'] === 200) {
                // 这边可能还要添加撤销撤改的判断
                // 同一个订单号不在拒绝退款的数组里 则放到同意退款
                if (!isset($refuseArr[$ordernum])) {
                    $canRefundArr[$ordernum] = ['code' => $refundPower['code'], 'msg' => $refundPower['msg']];
                }
            } else {
                $refuseArr[$ordernum] = ['code' => $refundPower['code'], 'msg' => $refundPower['msg']];
            }
        }
        if (ENV != 'PRODUCTION' || defined('IS_PFT_GRAY')) {
            $resultNew = ['canRefund' => $canRefundArr, 'refuseRefund' => $refuseArr];
            pft_log('refund/baseRefundCheck', json_encode($resultNew, JSON_UNESCAPED_UNICODE));
        }
//        pft_log('refund/baseRefundCheck', json_encode( ['canRefund' => $canRefundArr, 'refuseRefund' => $refuseArr], JSON_UNESCAPED_UNICODE));
        return ['canRefund' => $canRefundArr, 'refuseRefund' => $refuseArr];
    }

    /**
     * 简单判断用户是否有权限退
     * 只简单判断供应商和下单人员才有权限
     *
     * <AUTHOR>
     * @date 2021/9/23
     *
     * @param $orderNumArr
     * @param $opId
     *
     * @return array
     */
    public function _isCanSimpleRefund($orderNumArr, $opId)
    {
        $resArr = ['canRefund' => [], 'refuseRefund' => []];
        if (empty($orderNumArr) || empty($opId)) {
            return $resArr;
        }

        // 员工id 找到上级id
        $memberModel     = $this->_instanceMemberModel();
        $findParentIdRes = $memberModel->getStaffBySonId($opId);
        if ($findParentIdRes) {
            $opId = $findParentIdRes['parent_id'];
        }

        //获取进行订单和门票信息
        $orderAndTicketInfo = $this->_orderAndTicketInfo($orderNumArr);
        if ($orderAndTicketInfo === false) {
            return $resArr;
        }

        $orderInfoArr  = $orderAndTicketInfo['order'];
        $ticketInfoArr = $orderAndTicketInfo['ticket'];

        $canRefundArr = [];
        $refuseArr    = [];

        foreach ($orderInfoArr as $orderVal) {
            //订单信息
            $ordernum   = strval($orderVal['ordernum']);
            $tid        = $orderVal['tid'];
            $ticketInfo = $ticketInfoArr[$tid];

            $buyId    = $this->_findOriginBuyId($ordernum);
            $applyDid = $ticketInfo['apply_did'];
            $opId = intval($opId);
            $orderOwnId = intval($orderVal['member']);
            $buyId    = intval($buyId);
            $applyDid = intval($applyDid);
            if (in_array($opId, [$buyId, $orderOwnId, $applyDid])) {
                $canRefundArr[$ordernum] = ['code' => 200, 'msg' => '供应商和下单人员可以退票'];
            } else {
                $refuseArr[$ordernum] = ['code' => 124, 'msg' => '权限不够，订单不可退'];
            }
        }

        return ['canRefund' => $canRefundArr, 'refuseRefund' => $refuseArr];
    }


    /**
     * 验证当前是否可退票
     * TODO: 后期套票的退票的逻辑可以统一把逻辑理下
     * <AUTHOR>
     * @date 2019-06-27
     *
     * @param  string  $ordernum  订单号
     * @param  array  $ticketInfo  票类信息
     * @param  string  $playtime  sfasdfasdf
     * @param  string  $showBegin  演出时间
     * @param  int  $orderState  订单状态
     * @param  int  $ordermode  fdsafsdf
     * @param  int  $type  操作用户类型
     * @param  int  $ifpack  套票信息
     * @param  int  $cancelMemberId  会员ID【主账号ID，与员工无关】
     * @param  string  $endtime  订单有效期截止时间
     * @param  int  $buyerId  购买人ID
     * @param  int  $packOrdern  主票订单号
     * @param  string  $cancelType  取消类型：common=正常取消，revoke=终端撤改
     * @param  bool  $isForceCancel  是否强制取消
     * @param  array  $subOrderList  套票中子票列表
     * @param  int  $cancelChannel  退票渠道
     * @param  bool  $isAuditPass  是否是审核通过
     *
     * @return array
     */
    public function checkPackRefund($ordernum, $ticketInfo, $playtime, $showBegin, $orderState, $ordermode, $type,
        $ifpack, $ifPrint, $cancelMemberId, $endtime, $buyerId, $packOrdern = 0, $cancelType = 'common', $isForceCancel = false,
        $subOrderList = [], $cancelChannel = 0,$isAuditPass = false, $refundParams = [])
    {

        if ($cancelMemberId == 1 || PHP_SAPI == 'cli') {
            return $this->orderReturn(200, "管理员直接可退");
        }

        //管理员取消订单是没有限制的
        $refund_rule   = $ticketInfo['refund_rule'];
        $originApplyId = $ticketInfo['apply_did'];

        //退票规则：添加了这个值 -1=不可退且是可提现
        //所以=-1的时候也是不可退
        if ($refund_rule == -1) {
            $refund_rule = 2;
        }

        //不可退的话直接返回
        if ($refund_rule == 2) {
            return $this->orderReturn(0, "",
                ['err_code' => OrderConst::err1411, 'inner_msg' => "主票不可退 tid={$ticketInfo['id']}"]);
        }
        if ($refund_rule == 4) {
            /**
             * 生产：没有取票后不可退属性
             * - 主票
             *      - 子票1  10   不限
             *      - 子票2  10   配置了有效期前x天可退
             * 现状
             * - 主票
             *       - 子票1  10   取票后不可退
             *       - 子票2  10   配置了有效期前x天可退
             *
             * 退票流程：
             *  - 发起主票退
             *      - 先以主票的orderNumber去校验主票：筛掉景区资源账号、中间级分销链，置为false
             *          - 现状：增加主票取票后不可退规则校验
             *          - 生产：子票仅校验，退票属性为不可退时直接报错
             *          - 现状（需求）：增加取票后不可退规则校验
             *  - 如果上面校验都通过之后，开始起进程分别退子票和主票
             */

            //随子票退，也是需要判断当前退票人是否有退主票的权限  为了筛掉景区资源账号、中间级分销链
            $mainRefundRes = $this->_isCanSimpleRefund([$ordernum], $cancelMemberId);
            if (isset($mainRefundRes['refuseRefund']["{$ordernum}"])) {
                pft_log('order_refund/debug', json_encode(['isHaveCancelAuth_isCanSimpleRefund',$ordernum, $mainRefundRes]));
                $refuseInfo = $mainRefundRes['refuseRefund']["{$ordernum}"];
                $errCode    = $refuseInfo['code'];
                $errMsg     = $refuseInfo['msg'] . "【套票主票】";

                return $this->orderReturn(0, $errMsg, ['err_code' => $errCode]);
            }
            $subOrderArr = array_column($subOrderList, 'orderid');
            //主票取票退票规则校验，主票【随子票退票设置】一级控制，下面有二级控制【取票后退票规则】
            //主票传的退票操作为实际操作人
            $refundRes = $this->checkCanRefund([$ordernum], $cancelMemberId, [], [], [], [], [],
                $cancelChannel, [], $isAuditPass, $isForceCancel, $refundParams);
            if ($refundRes['refuseRefund']) {
                foreach ($refundRes['refuseRefund'] as $itemNum=>$item) {
                    $errCode    = $item['code'];
                    $errMsg     =  "套票主票". $item['msg'];
                    $logRrrMsg     =  $item['log_err_msg'] ?? '';
                    //区分报错类型-主票报错
                    if ($itemNum == $ordernum) {
                        pft_log('order_refund/debug', json_encode(['isHaveCancelAuth_checkCanRefund_main', $ordernum, $refundRes]));
                        return $this->orderReturn(0, $errMsg, ['err_code' => $errCode, 'log_err_msg'=>$logRrrMsg]);
                    }
                }
            }
            //里面的_orderAndTicketInfo方法还原后，传入的订单号仅查询订单号的数据，不会连着查询，主票和子票分开校验
            $refundResSub = $this->checkCanRefund($subOrderArr, $ticketInfo['apply_did'], [], [], [], [], [],
                $cancelChannel, [], $isAuditPass, $isForceCancel, $refundParams);
            pft_log('order_refund/debug', json_encode(['isHaveCancelAuth_checkCanRefund_son', $ordernum, $subOrderArr, $refundRes, $refundResSub]));
            if ($refundResSub['refuseRefund']) {
                foreach ($refundResSub['refuseRefund'] as $sonNum=>$item) {
                    $errCode    = $item['code'];
                    $errMsg     =  "子票【{$sonNum}】". $item['msg'];
                    $logRrrMsg     =  $item['log_err_msg'] ?? '';
                    //区分报错类型-子票报错;
                    //只要有一个子票是不可退的，整个主票就是不可退的
                    pft_log('order_refund/debug', json_encode(['isHaveCancelAuth_checkCanRefund_son', $ordernum, $sonNum, $refundResSub]));
                    return $this->orderReturn(0, $errMsg, ['err_code' => $errCode, 'log_err_msg'=>$logRrrMsg]);
                }
            }
            //有权限退
            return $this->orderReturn(200, '有权限退');
        } else {
//            //子票售后-走新版的校验逻辑，不走旧逻辑了
//            if ($usedPrintLimitRuleSwitch) {
//                $refundRes = $this->checkCanRefund([$ordernum], $cancelMemberId, [], [], [], [], [],
//                    $cancelChannel, [], $isAuditPass, $isForceCancel, $refundParams);
//                if ($refundRes['refuseRefund']) {
//                    foreach ($refundRes['refuseRefund'] as $itemNum=>$item) {
//                        $errCode    = $item['code'];
//                        $errMsg     =  "子票【{$itemNum}】". $item['msg'];
//                        $logRrrMsg     =  $item['log_err_msg']? "子票【{$itemNum}】".$item['log_err_msg'] : '';
//                        //区分报错类型-子票票报错，只有子票能进来
//                        if ($itemNum == $ordernum) {
//                            pft_log('order_refund/debug', json_encode(['isHaveCancelAuth_checkCanRefund_pack_son', $ordernum, $isAuditPass, $isForceCancel, $refundRes, $cancelMemberId]));
//                            return ['code' => 0, 'msg'=>$errMsg, 'data'=>['err_code' => $errCode, 'log_err_msg'=>$logRrrMsg]];
//                        }
//                    }
//                }
//                //有权限退
//                return $this->orderReturn(200, '有权限退');
//            } else {}
                //fixme 这么看的话，只有非【团队订单、报团计调下单】的单票会走到这里了，分别是ordermode != 24 44
                //fixme 捆绑票【主票】会走这段逻辑
                //旧的套票退票权限校验逻辑
            return $this->_oldCheckPackRefund($ordernum, $refund_rule, $ticketInfo, $playtime, $showBegin, $orderState,
                $ordermode, $type,
                $ifpack, $ifPrint, $cancelMemberId, $endtime, $buyerId, $packOrdern, $cancelType, $isForceCancel,
                $subOrderList,$cancelChannel);
        }
    }

    /**
     * 计算随子票退的退票规则
     *
     * @param $parentTid
     *
     * @return array
     * Author : liucm
     * Date : 2022/2/18
     * @throws \Exception
     */
    public function calcSonRefundRuleNew($parentTid)
    {
        if (!$parentTid) {
            return $this->returnData(0, '参数错误');
        }

        $packBiz    = new \Business\Product\PackTicket();
        $sonTidList = $packBiz->getTickets($parentTid);
        if (!$sonTidList) {
            return $this->returnData(0, '没有获取到子票数据');
        }

        //获取子票属性
        $sonTidArr = array_column($sonTidList, 'tid');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($sonTidArr, 'id,refund_rule,refund_early_time', '', '',
            'v_time_limit');

        if (!$ticketArr) {
            //属性获取失败
            return $this->returnData(0, '没有获取到子票门票数据');
        }

        foreach ($ticketArr as $ticketInfo) {
            $sonTicketList[$ticketInfo['ticket']['id']] = [
                'id'                       => $ticketInfo['ticket']['id'],
                'refund_rule'              => $ticketInfo['ticket']['refund_rule'],
                'refund_early_time'        => $ticketInfo['ticket']['refund_early_time'],
                'v_time_limit'             => $ticketInfo['land_f']['v_time_limit'],
                'refund_before_early_time' => $ticketInfo['ext']['refund_before_early_time'] ?? 0,
                'refund_after_early_time'  => $ticketInfo['ext']['refund_after_early_time'] ?? 0,
                'refund_after_time'        => $ticketInfo['ext']['refund_after_time'] ?? 0,
            ];
        }

        //退票数据
        $refundData = [
            'refund_rule'              => 3,
            'refund_early_time'        => 0,
            'refund_after_time'        => 0,
            'refund_before_early_time' => 0,
            'refund_after_early_time'  => 0,
        ];

        //循环判断子票的最严格退票属性
        foreach ($sonTicketList as $item) {
            $sonRefundRule  = $item['refund_rule'];
            $sonRefundRule  = $sonRefundRule == -1 ? 2 : $sonRefundRule;
            $sonProductInfo = $item;

            if ($sonRefundRule == 2) {
                //不可退，直接整个票都不可退
                $refundData['refund_rule']              = 2;
                $refundData['refund_after_early_time']  = 0;
                $refundData['refund_before_early_time'] = 0;
                $refundData['refund_early_time']        = 0;
                $refundData['refund_after_time']        = 0;
                break;
            } elseif ($sonRefundRule == 0) {
                //有效期前X天可退
                $refundData['refund_rule'] = 0;
                if ($refundData['refund_before_early_time'] == 0 && !empty($sonProductInfo['refund_before_early_time'])) {
                    $refundData['refund_before_early_time'] = $sonProductInfo['refund_before_early_time'];
                }
                $checkRes = $this->_checkTime($sonProductInfo['refund_before_early_time'],$refundData['refund_before_early_time']);
                if ($checkRes == true) {
                    $refundData['refund_before_early_time'] = $sonProductInfo['refund_before_early_time'];
                }
            } elseif ($sonRefundRule == 1) {
                if (!in_array($refundData['refund_rule'], [2, 0])) {
                    //有效期后X天可退
                    $refundData['refund_rule'] = 1;
                    if ($refundData['refund_after_early_time'] == 0 && !empty($sonProductInfo['refund_after_early_time'])) {
                        $refundData['refund_after_early_time'] = $sonProductInfo['refund_after_early_time'];
                    }
                    if ($sonProductInfo['refund_after_early_time'] < $refundData['refund_after_early_time']) {
                        $refundData['refund_after_early_time'] = $sonProductInfo['refund_after_early_time'];
                    }
                }

            } elseif ($sonRefundRule == 3) {
                //随时退的话，直接进行下一个票的判断
                continue;
            }
        }
        return $this->returnData(200, '获取成功', $refundData);
    }

    /**
     * 比较为负值分钟数的 时间大小
     *
     * @param $time1
     * @param $time2
     *
     * @return bool
     * Author : liucm
     * Date : 2022/2/18
     */
    private function _checkTime($time1, $time2)
    {
        $day1    = floor($time1 / 1440);
        $hour1   = floor(($time1 - $day1 * 1440) / 60);
        $minute1 = $time1 - ($day1 * 1440 + $hour1 * 60);

        $day2    = floor($time2 / 1440);
        $hour2   = floor(($time2 - $day2 * 1440) / 60);
        $minute2 = $time2 - ($day2 * 1440 + $hour2 * 60);

        //天数越大时间越小
        if ($day1 > $day2) {
            return true;
        } elseif ($day1 < $day2) {
            return false;
        } else {
            //天数相等，小时越小，时间越小
            if ($hour1 > $hour2) {
                return false;
            } elseif ($hour1 < $hour2){
                return true;
            } else {
                //天数相等，小时相等，分钟越小，时间越小
                if ($minute1 > $minute2) {
                    return false;
                } else {
                    return true;
                }
            }
        }

    }
    /**
     * 获取套票退票属性配置成"随子票"的情况下，计算所有子票的综合退票属性
     * 退票规则：0=有效期内可退 1=游玩日期内可退 2=不可退 3=随时退 4=套票随子票
     *
     * <AUTHOR>
     * @date   2019-09-04
     *
     * @param  int  $parentTid  主票的门票ID
     *
     * @return array
     */
    public function calcSonRefundRule($parentTid)
    {
        if (!$parentTid) {
            return $this->returnData(0, '参数错误');
        }

        $packBiz = new \Business\Product\PackTicket();
        $sonTidList  = $packBiz->getTickets($parentTid);
        if (!$sonTidList) {
            return $this->returnData(0, '没有获取到子票数据');
        }

        //获取子票属性
        $sonTidArr     = array_column($sonTidList, 'tid');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($sonTidArr, 'id,refund_rule,refund_early_time', '',  '','v_time_limit');

        if (!$ticketArr) {
            //属性获取失败
            return $this->returnData(0, '没有获取到子票门票数据');
        }

        foreach ($ticketArr as $ticketInfo) {
            $sonTicketList[$ticketInfo['ticket']['id']] = [
                'id'                       => $ticketInfo['ticket']['id'],
                'refund_rule'              => $ticketInfo['ticket']['refund_rule'],
                'refund_early_time'        => $ticketInfo['ticket']['refund_early_time'],
                'v_time_limit'             => $ticketInfo['land_f']['v_time_limit'],
                'refund_before_early_time' => $ticketInfo['ext']['refund_before_early_time'] ?? 0,
                'refund_after_early_time'  => $ticketInfo['ext']['refund_after_early_time'] ?? 0,
                'refund_after_time'        => $ticketInfo['ext']['refund_after_time'] ?? 0,
            ];
        }

        //退票数据
        $refundData = [
            'refund_rule'              => 3,
            'refund_early_time'        => 0,
            'refund_before_early_time' => 0,
            'refund_after_early_time'  => 0,
            'refund_after_time'        => 0,
        ];

        //循环判断子票的最严格退票属性
        foreach ($sonTicketList as $item) {
            $sonRefundRule  = $item['refund_rule'];
            $sonRefundRule  = $sonRefundRule == -1 ? 2 : $sonRefundRule;
            $sonProductInfo = $item;

            if ($sonRefundRule == 3) {
                //随时退的话，直接进行下一个票的判断
                continue;
            } else if ($sonRefundRule == 2) {
                //不可退，直接整个票都不可退
                $refundData['refund_rule']       = 2;
                $refundData['refund_early_time'] = 0;
                break;
            } else if ($sonRefundRule == 1) {
                //有效期前X天可退
                if ($refundData['refund_rule'] == 3 || $refundData['refund_rule'] == 0) {
                    //"有效期前X天可退"比"有效期可退"和"随时退"的优先级更高
                    $refundData['refund_rule']             = 1;
                    $refundData['refund_early_time']       = $sonProductInfo['refund_early_time'];
                    $refundData['refund_after_time']       = $sonProductInfo['refund_after_time'];
                    $refundData['refund_after_early_time'] = $sonProductInfo['refund_after_early_time'];
                } else {
                    //提前时间更短的更严格
                    if ($sonProductInfo['refund_early_time'] < $refundData['refund_early_time']) {
                        $refundData['refund_early_time'] = $sonProductInfo['refund_early_time'];
                    }

                    if ($sonProductInfo['refund_after_time'] < $refundData['refund_after_time']) {
                        $refundData['refund_after_time'] = $sonProductInfo['refund_after_time'];
                    }

                    if ($sonProductInfo['refund_after_early_time'] < $refundData['refund_after_early_time']) {
                        $refundData['refund_after_early_time'] = $sonProductInfo['refund_after_early_time'];
                    }
                }
            } else if ($sonRefundRule == 0) {
                //有效期可退
                if ($refundData['refund_rule'] == 3) {
                    //"有效期可退"比"随时退"的优先级更高
                    $refundData['refund_rule'] = 0;
                    $refundData['refund_before_early_time'] = $sonProductInfo['refund_before_early_time'];
                }
            }
        }

        return $this->returnData(200, '获取成功', $refundData);
    }

    public function checkPrintLimitRule($ifPrint, $printLimitRule)
    {
        $checkResult = false;
        if ($printLimitRule == TicketIfPrintRefundRule::NONE) {
            $checkResult = true;
        }
        if ($printLimitRule == TicketIfPrintRefundRule::ORDER_PRINT_NOT_CAN_REFUND) {
            $checkResult = true;
            if (in_array($ifPrint, [OrderIfPrintStatus::PART_PRINTED, OrderIfPrintStatus::ALL_PRINTED])) {
                $checkResult = false;
            }
        }
        if ($printLimitRule == TicketIfPrintRefundRule::ORDER_PART_PRINT_CAN_REFUND && in_array($ifPrint, [OrderIfPrintStatus::UN_PRINTED, OrderIfPrintStatus::PART_PRINTED])) {
            $checkResult = true;
        }
        return $checkResult;
    }

    /**
     * 旧的的套票验证当前是否可退票
     * <AUTHOR>
     * @date 2019-06-27
     *
     * @param  string  $ordernum  订单号
     * @param  array  $ticketInfo  票类信息
     * @param  string  $playtime  sfasdfasdf
     * @param  string  $showBegin  演出时间
     * @param  int  $orderState  订单状态
     * @param  int  $ordermode  fdsafsdf
     * @param  int  $type  操作用户类型
     * @param  int  $ifpack  套票信息
     * @param  int  $cancelMemberId  会员ID【主账号ID，与员工无关】
     * @param  string  $endtime  订单有效期截止时间
     * @param  int  $buyerId  购买人ID
     * @param  int  $packOrdern  主票订单号
     * @param  string  $cancelType  取消类型：common=正常取消，revoke=终端撤改
     * @param  bool  $isForceCancel  是否强制取消
     * @param  array  $subOrderList  套票中子票列表
     *
     * @return array
     */
    private function _oldCheckPackRefund($ordernum, $refund_rule, $ticketInfo, $playtime, $showBegin, $orderState, $ordermode, $type,
        $ifpack, $ifPrint, $cancelMemberId, $endtime, $buyerId, $packOrdern = 0, $cancelType = 'common', $isForceCancel = false, $subOrderList = [],$cancelChannel = 0)
    {
        //记得日志看下主票的取消是不是不会到这里面了
        if ($ifpack == 1) {
            pft_log('oldCheckPackRefund/statisc', json_encode([$ordernum, $refund_rule, $ticketInfo, $playtime, $showBegin, $orderState, $ordermode, $type, $ifpack, $ifPrint, $cancelMemberId], JSON_UNESCAPED_UNICODE));
        }

        $v_time_limit    = $ticketInfo['v_time_limit'];
        $advance_time    = $ticketInfo['refund_early_time'];
        $refundAftertime = $ticketInfo['refund_after_time'];
        $modifyLimitTime = isset($ticketInfo['modify_limit_time']) ? ($ticketInfo['modify_limit_time'] / 60) : 0;
        $originApplyId   = $ticketInfo['apply_did'];
        if ($modifyLimitTime > $advance_time) {
            $advance_time = $modifyLimitTime;
        }

        //TODO：这块的逻辑写得很乱，后面需要做下整理
        //这边添加取票权限的判断
        if (in_array($ordermode,
                [12]) || ($cancelMemberId == $originApplyId && $orderState == 2) || in_array($cancelType,
                ['revoke']) || $isForceCancel) {
            //不做是否取票情况下不可取消的情况:
            //自助机下单、原始供应商取消过期订单、撤销订单、强制取消的订单

        }
        $ticketObj  = new Ticket('slave');
        $packBiz    = new \Business\Product\PackTicket();

        if (!$this->checkPrintLimitRule($ifPrint, $ticketInfo['ifprint_refund_rule']) && $cancelType != 'revoke' && $cancelMemberId != $originApplyId) {
            //非原始供应商或是非撤销的情况下：已取票的订单不允许退票
            return $this->orderReturn(0, '', ['err_code' => OrderConst::err4005]);
        } elseif ($ifpack == 1 && $cancelMemberId != $originApplyId && $cancelType != 'revoke') {
            $sonTicketList = $ticketObj->getTicketInfoMulti(array_column($subOrderList, 'tid'),
                'id,refund_rule,ifprint_refund_rule,refund_early_time');
            // 取消主票的时候 进行判断其子票是否有已经取票的
            foreach ($subOrderList as $childVal) {
                $childTicket = $sonTicketList[$childVal['tid']];
                if (!$this->checkPrintLimitRule($childVal['ifprint'], $childTicket['ifprint_refund_rule'])) {
                    return $this->orderReturn(0, '', ['err_code' => OrderConst::err4005, 'inner_msg' => "存在已经取票的子票"]);
                }
            }
        }

        pft_log('order_refund/debug', json_encode(['oldCheckPackRefund_used_RefundIfprintLimit', $ordernum, $refund_rule,$cancelMemberId, $originApplyId, $cancelType]));

        //记录子票的退票配置
        $sonRefundRuleArr = [];

        //如果是套票，则判断循环取到最大时间
        if ($ifpack == 1) {
            //获取所有的子票
            $sonList = $packBiz->getTickets($ticketInfo['id']);

            if ($sonList) {
                $tmpTidArr = array_column($sonList, 'tid');

                $ticketList = $ticketObj->getTicketInfoMulti($tmpTidArr,
                    $field = 'id,refund_rule,refund_early_time,modify_limit_time');
                $ticketList = $ticketList ? $ticketList : [];

                foreach ($ticketList as $tmpTicketInfo) {
                    //退票规则：添加了这个值 -1=不可退且是可提现
                    //所以=-1的时候也是不可退
                    if ($tmpTicketInfo['refund_rule'] == -1) {
                        $tmpTicketInfo['refund_rule'] = 2;
                    }

                    if ($tmpTicketInfo['refund_rule'] == 2) {
                        return $this->orderReturn(0, "",
                            ['err_code' => OrderConst::err1413, 'inner_msg' => "子票 tid={$tmpTicketInfo['id']}"]);
                    }

                    //记录子票的退票配置
                    $sonRefundRuleArr[$tmpTicketInfo['id']] = $tmpTicketInfo['refund_rule'];

                    //如果子票是设置"游玩日期前可退 需在验证截止时间",才将子票设置的时间运用到套票上面去
                    if ($tmpTicketInfo['refund_rule'] == 1 && $advance_time < $tmpTicketInfo['refund_early_time']) {
                        $advance_time = $tmpTicketInfo['refund_early_time'];
                    }

                    //如果子票是设置"游玩日期后可退 需在验证截止时间",才将子票设置的时间运用到套票上面去
                    if ($tmpTicketInfo['refund_rule'] == 1 && $refundAftertime > $tmpTicketInfo['refund_after_time']) {
                        $refundAftertime = $tmpTicketInfo['refund_after_time'];
                    }

                    $tmpModifyLimitTime = isset($tmpTicketInfo['modify_limit_time']) ? ($tmpTicketInfo['modify_limit_time'] / 60) : 0;
                    if ($tmpModifyLimitTime > $advance_time) {
                        $advance_time = $tmpModifyLimitTime;
                    }
                }
            }
        }

        //设置了“不可退”属性，任何人都不可退
        $is_applyer = $originApplyId == $cancelMemberId ? 1 : 0;
        //未使用订单，供应商不可退;云票务,自助机，计调下单，智能终端机，闸机的订单除外
        if (!in_array($ordermode, [10, 12, 14, 15, 16]) && $orderState == 0 && $is_applyer && $buyerId != $originApplyId && !$isForceCancel) {
            return $this->orderReturn(0, "", ['err_code' => OrderConst::err1416]);
        }
        if ($refund_rule == 3 && in_array($orderState,[0,2,7,CommonOrderStatus::WAIT_APPOINTMENT_CODE,CommonOrderStatus::WAIT_PRINTTICKET_CODE])) {
            return $this->orderReturn(200, "可以退票");
        }

        //如果套票的主票是随子票的，而且子票都是随时可退的，那就都直接退
        if ($refund_rule == 4 && $sonRefundRuleArr) {
            $sonRefundRuleArr = array_unique(array_values($sonRefundRuleArr));

            //所有子票都是配置的随时退
            if (count($sonRefundRuleArr) == 1 && $sonRefundRuleArr[0] == 3) {
                return $this->orderReturn(200, "主票随子票属性，所有子票都是随时退");
            }
        }

        $apply_did = false;
        if ($ifpack == 2) {

            //$parentTicketId   = $orderModel->getInfoByOrder($packOrdern, $filed = 'tid');

            //订单查询迁移
            $orderMove = new OrderQueryMove();
            $parentTicketId = $orderMove->getListByOrderNumNew([$packOrdern]);
            $parentTicketId = $parentTicketId[0] ?? [];

            $parentTicketInfo = $ticketObj->getTicketInfoById($parentTicketId['tid'], 'apply_did');

            if ($parentTicketInfo['apply_did']) {
                $apply_did = $parentTicketInfo['apply_did'];
            } elseif ($type == 100) {
                //这个逻辑超级奇葩，正常是不会走这边的
                return $this->orderReturn(0, "",
                    ['err_code' => OrderConst::err1415, 'inner_msg' => "主票不可退 tid={$ticketInfo['id']}"]);
            }
        }

        if (in_array($ticketInfo['p_type'], ['A', 'B', 'C', 'F', 'G'])) {
            if ($orderState == 2) {
                //已过期随时退的都可退，已过期有效期和游玩日期内可退的只有供应商可退，分销商不可退
                if ($is_applyer || $refund_rule == 3 || ($ifpack == 2 && $type == 100 && $apply_did == $cancelMemberId && in_array($refund_rule,
                            [0, 1]))) {
                    return $this->orderReturn(200, "可以退票");
                } else {
                    //return $this->orderReturn(0, "", ['err_code' => OrderConst::err1412]);
                }
            }
        } else {
            if ($orderState == 2) {
                //已过期随时退的都可退，已过期有效期和游玩日期内可退的只有供应商可退，分销商不可退
                if ($is_applyer || $refund_rule == 3 || ($ifpack == 2 && $type == 100 && $apply_did == $cancelMemberId && in_array($refund_rule,
                            [0, 1]))) {
                    return $this->orderReturn(200, "可以退票");
                } else {
                    return $this->orderReturn(0, "", ['err_code' => OrderConst::err1412]);
                }
            }
        }

        $refundRule = new RefundRule();

        $ticketInfoRule = $refund_rule;
        $refundBeforeEarlyTime = $ticketInfo['refund_before_early_time'] ?? 0;
        $refundAfterEarlyTime = $ticketInfo['refund_after_early_time'] ?? 0;
        $refundExt             = [
            'endtime'                  => $endtime,
            'refund_before_early_time' => $refundBeforeEarlyTime,
            'refund_after_early_time'  => $refundAfterEarlyTime,
        ];

        if (in_array($ticketInfo['p_type'], ['A', 'B', 'C', 'F', 'G'])) {
            $resultRefundRes = $refundRule->actionRefundRuleTicket($ticketInfoRule, $refundExt);
            if ($resultRefundRes['status'] == 1) {
                return $this->orderReturn(200, "可以退票!");
            }

        } else {
            //未过期都可退
            if ($refund_rule == 0 && in_array($orderState,[0,CommonOrderStatus::WAIT_APPOINTMENT_CODE,CommonOrderStatus::WAIT_PRINTTICKET_CODE])) {
                return $this->orderReturn(200, "可以退票");
            }
        }


        //如果是子票，而且是非供应商或套票发布方操作的话，子票不可单独取消
        if ($ifpack == 2 && $type != 100) {
            if (($apply_did == $cancelMemberId) && $refund_rule == 3) {
                return $this->orderReturn(200, "可以退票");
            } else {
                if ($cancelChannel != 31){
                    return $this->orderReturn(0, "", ['err_code' => OrderConst::err1415, 'inner_msg' => "套票子票不能由用户直接发起取消"]);
                }

            }
        }

        if (in_array($ticketInfo['p_type'], ['A', 'B', 'C', 'F', 'G'])) {
            $resultRefundRes = $refundRule->actionRefundRuleTicket($ticketInfoRule, $refundExt);
            if ($resultRefundRes['status'] == 0) {
                return $this->orderReturn(0, "", ['err_code' => OrderConst::err1414, 'inner_msg' => "套票子票退票规则有效期-{$resultRefundRes['msg']}"]);
            }
        } else {
            $end_time = $playtime;
            //设置了验证时间的取验证结束时间，未设置验证时间取游玩当天23:59:59
            $end_time .= $v_time_limit ? end(explode('|', $v_time_limit)) : " 23:59:59";
            //有演出时间的取演出开始时间
            //演出且有配置游玩日期X分钟后的
            if ($showBegin && $refundAftertime > 0) {
                $end_time        = substr(trim($showBegin), 0, 16);
                $refund_end_time = strtotime($end_time . "+ {$refundAftertime} minutes");
            } else {
                $end_time        = $showBegin ? substr(trim($showBegin), 0, 16) : $end_time;
                $refund_end_time = strtotime($end_time . "- {$advance_time} minutes");
            }

            $now = time();
            //分销商，门票已超过退票有效期
            if (!$is_applyer && $refund_end_time < $now) {
                $showExpireTime = date('Y-m-d H:i:s', $refund_end_time);

                return $this->orderReturn(0, "",
                    ['err_code' => OrderConst::err1414, 'inner_msg' => "退票有效期-{$showExpireTime}"]);
            }
        }

        //到这边的话就是都可以退票了
        return $this->orderReturn(200, "可以退票");
    }

    /**
     * 返回订单的状态和打印状态 组合状态
     * <AUTHOR>
     * @date 2019-06-27
     *
     * @param  array  $orderNumArr  订单号数组
     * @param  array  $orderInfo  订单信息
     * @param  array  $orderAddon  订单扩展信息
     * @param  array  $orderDetail  订单详情信息
     * @param  array  $ticketInfo  门票信息
     * @param  array  $ticketExt  门票扩展信息
     * @param  array 订单号数组
     *
     * @return array
     */
    private function _orderAndTicketInfo($orderNumArr, $orderInfo = [], $orderAddon = [], $orderDetail = [], $ticketInfo = [], $ticketExt = [])
    {
        // 批量获取订单的 状态
        if (!$orderInfo) {
//            $subOrderMain    = $this->_instanceOrderQueryModel();
//            $subOrderInfoArr = $subOrderMain->getOrderListByOrder($orderNumArr,
//                'tid, ordernum, tprice, paymode, ordermode, pay_status, status, member, playtime, salerid, ordertime');

            $queryParams = [$orderNumArr];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
            if ($queryRes['code'] != 200) {
                $log = [
                    'parame' => $orderNumArr,
                    'apiData' => $queryRes,
                    'function' => 'queryOrderInfoByOrdernumList',
                ];
                pft_log('orderAndTicketInfo/Api/error',json_encode($log));
            }
            $subOrderInfoArr = $queryRes['data'];
            if (empty($subOrderInfoArr)) {
                return false;
            }
            $orderInfoArr = [];
            foreach ($subOrderInfoArr as $subOrderVal) {
                $orderInfoArr[$subOrderVal['ordernum']] = $subOrderVal;
            }
        } else {
            $orderInfoArr = $orderInfo;
        }

        if (empty($orderInfoArr)) {
            return false;
        }

        // 获取订单细节信息
        // 获取演出信息
        if (!$orderDetail) {
            $subOrderDetail     = new SubOrderDetail();
            $subOrderDetailInfo = $subOrderDetail->getDetailListByOrderArr($orderNumArr,
                'orderid,series,aids_money,concat_id,aids');

            $fxDetailInfoArr = [];
            if (!empty($subOrderDetailInfo)) {
                foreach ($subOrderDetailInfo as $subDetailVal) {
                    $fxDetailInfoArr[$subDetailVal['orderid']] = $subDetailVal;
                }
            }
        } else {
            $fxDetailInfoArr = $orderDetail;
        }

        // 批量获取订单是否已经打印的信息
        if (!$orderAddon) {
            //$SubOrderAddonModel = new SubOrderAddon();
            //$subOrderAddonArr   = $SubOrderAddonModel->getAddonInfoByOrderArr($orderNumArr,
            //    $field = 'orderid, ifprint, id, ifpack, pack_order');

            $subOrderAddonInfo = [];
            $queryParams       = [$orderNumArr];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderAddon',
                'batchQueryOrderAddonByorderid', $queryParams);
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $subOrderAddonInfo = $queryRes['data'];
            }

            $orderAddonArr = [];
            if ($subOrderAddonInfo) {
                foreach ($subOrderAddonInfo as $addonVal) {
                    $orderAddonArr[$addonVal['orderid']] = $addonVal;
                }
            }
        } else {
            $orderAddonArr = $orderAddon;
        }

        $tidArr = array_column($orderInfoArr, 'tid');
        // 返回门票的状态
        // $ticketModel        = new \Model\Product\Ticket();
        if (!$ticketInfo) {
            $ticketModel   = $this->_instanceTicketModel();
            $ticketInfoArr = $ticketModel->getTicketList($tidArr,
                'id, title, refund_rule, apply_did, refund_early_time, modify_limit_time');
        } else {
            $ticketInfoArr = $ticketInfo;
        }

        // 返回门票扩展信息
        if (!$ticketExt) {
            $javaApi      = new \Business\CommodityCenter\LandF();
            $ticketExtArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId($tidArr, [], [], 'tid, id, v_time_limit', true);
        } else {
            $ticketExtArr = $ticketExt;
        }
        return [
            'order'       => $orderInfoArr,
            'orderaddon'  => $orderAddonArr,
            'ticket'      => $ticketInfoArr,
            'orderdetail' => $fxDetailInfoArr,
            'ticketExt'   => $ticketExtArr,
        ];
    }

    private function _getOrderToolModel()
    {
        if (empty($this->_orderToolsModel)) {
            $this->_orderToolsModel = new OrderTools('slave');
        }

        return $this->_orderToolsModel;
    }


    /**
     * 通过orderTrack 表找出原始操作下单的sid
     *
     * @param  string  $orderNum
     *
     * @return int
     *
     */
    public function _findOriginBuyId($orderNum)
    {
        $trackModel        = $this->_instanceOrderTrackModel();
        $sankeOrderOperArr = $trackModel->getListByOrders([$orderNum], 'oper_member', 0);
        $buyId             = $sankeOrderOperArr[0]['oper_member'];

        $memberModel   = $this->_instanceMemberModel();
        $buyIdParentId = $memberModel->getStaffBySonId($buyId);
        if ($buyIdParentId) {
            $buyId = $buyIdParentId['parent_id'];
        }

        return $buyId ?: '0';
    }

    /**
     * 验证是否在退款票时间内，对过期可以操作退票的可以忽略了
     *
     * @param  string  $verifyTime  验证时间08:00|18:00
     * @param  int  $refundEarlyMinu  退票提前多少分钟
     * @param  int  $modifyLimitTime  修改提前多少分钟
     * @param  string  $playTime  游玩时间
     * @param  string  $showBegin  演出开始时间
     * @param  int  $opId  操作人id
     * @param  int  $applyDid  门票供应商id
     * @param  int  $orderStatus  订单状态
     * @param  int  $refundAfterTime  退票延迟多少分钟
     *
     * @return array
     */
    public function checkRefundTimeRule($refundEarlyMinu, $playTime, $showBegin, $modifyLimitTime, $opId, $applyDid, $refundAfterTime = 0)
    {
        if (!($opId && $applyDid && $playTime)) {
            return ['code' => 203, 'msg' => '参数有误'];
        }
        // 管理员 以及 cli 模式下不进行判断
        if ($opId == 1 || PHP_SAPI == 'cli') {
            return ['code' => 200, 'msg' => '管理员或任务执行'];
        }

        // 对订单和门票设置进行判断时间是否可以操作
        $nowTime = time();
        // 结束时间取得游玩时间
        $endTime = $playTime;
        // 设置了验证时间的取验证结束时间，未设置验证时间取游玩当天23:59:59
//        $endTime .= $verifyTime ? end(explode('|', $verifyTime)) : " 23:59:59";
        $endTime .= " 23:59:59";
        // 有演出时间的取演出开始时间

        //演出的切配置了游玩日期X分钟可退
        if ($showBegin && $refundAfterTime > 0) {
            $endTime       = substr(trim($showBegin), 0, 16);
            $refundEndTime = strtotime($endTime . "+ {$refundAfterTime} minutes");
        } else {
            $endTime       = $showBegin ? substr(trim($showBegin), 0, 16) : $endTime;
            $refundEndTime = strtotime($endTime . "- {$refundEarlyMinu} minutes");

            // 如果有设置修改时间则更新修改时间的值
            $modifyLimitTime = $modifyLimitTime / 60;
            if (!empty($modifyLimitTime) && ($modifyLimitTime > $refundEarlyMinu)) {
                $refundEndTime = strtotime($endTime . "- {$modifyLimitTime} minutes");
            }
        }

        // 对时间进行处理
        if ($refundEndTime < $nowTime) {
            // 已经超时退款时间的订单 视为过期
            if ($opId == $applyDid) {
                return ['code' => 100, 'msg' => '供应商执行过了退款时间的订单'];
            }

            return ['code' => 1414, 'msg' => '已过退票有效期'];
        }

        return ['code' => 200, 'msg' => '还在退票时间内'];
    }

    public function checkRefundTimeRuleNew($ticketInfo, $ticketExtInfo, $orderInfoArr, $opId)
    {
        $refundRule = $ticketInfo['refund_rule'] ?? 0;
        $refundBeforeEarlyTime = $ticketInfo['refund_before_early_time'] ?? 0;
        $refundAfterEarlyTime = $ticketInfo['refund_after_early_time'] ?? 0;
        $applyDid = $ticketInfo['apply_did'];
        $endtime = $orderInfoArr['endtime'];

        // 管理员 以及 cli 模式下不进行判断
        if ($opId == 1 || PHP_SAPI == 'cli') {
            return ['code' => 200, 'msg' => '管理员或任务执行'];
        }

        // 对订单和门票设置进行判断时间是否可以操作
        $nowTime = time();

        list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
        $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endtime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endtime)));
        $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
        pft_log('debug/checkRefundTimeRuleNew', json_encode([$orderInfoArr['ordernum'], $refundBeforeEarlyTimeDate, $tmphour, $tmpminute], JSON_UNESCAPED_UNICODE));
        // 对时间进行处理
        if ($refundBeforeEarlyTimeNew < $nowTime) {
            // 已经超时退款时间的订单 视为过期
            if ($opId == $applyDid) {
                return ['code' => 100, 'msg' => '供应商执行过了退款时间的订单'];
            }

            return ['code' => 1414, 'msg' => '已过退票有效期'];
        }

        return ['code' => 200, 'msg' => '还在退票时间内'];
    }

    public function checkRefundTimeRuleNewTeam($ticketInfo, $ticketExtInfo, $orderInfoArr, $opId)
    {
        $refundRule = $ticketInfo['refund_rule'] ?? 0;
        $refundBeforeEarlyTime = $ticketInfo['refund_before_early_time'] ?? 0;
        $refundAfterEarlyTime = $ticketInfo['refund_after_early_time'] ?? 0;
        $applyDid = $ticketInfo['apply_did'];
        $endtime = $orderInfoArr['endtime'];

        // 管理员 以及 cli 模式下不进行判断
        if ($opId == 1 || PHP_SAPI == 'cli') {
            return ['code' => 200, 'msg' => '管理员或任务执行'];
        }

        // 对订单和门票设置进行判断时间是否可以操作
        $nowTime = time();

        if ($refundRule == 1) {
            list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
            $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endtime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endtime)));
            $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
            pft_log('debug/checkRefundTimeRuleNew', json_encode([$orderInfoArr['ordernum'], $refundBeforeEarlyTimeDate, $tmphour, $tmpminute], JSON_UNESCAPED_UNICODE));
            // 对时间进行处理
            if ($refundBeforeEarlyTimeNew < $nowTime) {
                // 已经超时退款时间的订单 视为过期
                if ($opId == $applyDid) {
                    return ['code' => 100, 'msg' => '供应商执行过了退款时间的订单'];
                }

                return ['code' => 1414, 'msg' => '已过退票有效期'];
            }
        }

        if ($refundRule == 0) {
            //有效期前X天可退
            list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundBeforeEarlyTime);
            $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endtime : date('Y-m-d', strtotime("-{$tmpDay} day", strtotime($endtime)));
            $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
            pft_log('debug/checkRefundTimeRuleNew', json_encode([$refundBeforeEarlyTimeNew, $opId, $applyDid], JSON_UNESCAPED_UNICODE));
            if ($refundBeforeEarlyTimeNew < $nowTime) {
                // 已经超时退款时间的订单 视为过期
                if ($opId == $applyDid) {
                    return ['code' => 100, 'msg' => '供应商执行过了退款时间的订单'];
                }

                return ['code' => 1414, 'msg' => '已过退票有效期'];
            }
        }

        return ['code' => 200, 'msg' => '还在退票时间内'];
    }

    /**
     * 根据传入条件获取订单的游客idx
     * @param $ordernum
     * @param $cancelChannel
     * @param $personParams
     * @param $preCheckParams
     * @return array
     */
    public function getOrderTouristByCondition($ordernum,$cancelChannel, $personParams, $preCheckParams = [])
    {
        $personIndex = $personParams['personIndex'];
        $personIdList = $personParams['personIdList'];
        $ticketCodeList = $personParams['ticketCodeList'];
        $subOrderModel = new SubOrderQuery();
        $idxData  = [];
        $idxInfoList = [];
        $field = 'orderid,id,tourist,idcard,mobile,print_state,voucher_type,check_state,idx, chk_code';
        //todo  理论上应该只会有一个是ture 不确定外面是不是会乱传不敢限制
        $touristType = [
            'ticketCode' => false,
            'idCard'     => false,
            'idx'        => false,
        ];
        if ($personIndex > 0) {
            $indexWhere = ['idx' => $personIndex];
            if (is_array($personIndex) && !empty($personIndex)) {
                $indexWhere = ['idx'=>['in', $personIndex]];
            }
            $idxRes          = $subOrderModel->getInfoInTouristByOrder($ordernum,
                $field, 1, $indexWhere, '', true);
            if (empty($idxRes)) {
                return $this->orderReturn(0, "idx参数错误", ['err_code' => OrderConst::err1]);
            }
            $touristType['idx'] = true;
            $idxData = array_column($idxRes, 'idx');
            $idxInfoList = $idxRes;
        }

        $idCardParams = $ticketCodeParams = [];

        if ($personIdList) {
            $cardRes          = $subOrderModel->getInfoInTouristByOrder($ordernum,
                $field, 1, ['idcard' => ['in',$personIdList]], '', true);
            if (empty($cardRes) && $cancelChannel != 31) {//套票的身份证规则太乱了，这边不管了，搜不到就随机取消吧
                return $this->orderReturn(0, "身份证有误", ['err_code' => OrderConst::err1]);
            }
            $touristType['idCard']  = true;
            $cardRes = $cardRes ? $cardRes : [];
            $idxData = array_merge($idxData, array_column($cardRes, 'idx'));
            $idxInfoList = array_merge($idxInfoList,$cardRes);
            $idCardParams = $cardRes;
        }
        if ($ticketCodeList) {
            $ticketRes          = $subOrderModel->getInfoInTouristByOrder($ordernum,
                $field, 1, ['chk_code' => ['in',$ticketCodeList]], '', true);
            if (count($ticketRes) != count($ticketCodeList)){
                return $this->orderReturn(0, "门票信息有误", ['err_code' => OrderConst::err1]);
            }
            $touristType['ticketCode'] = true;
            $idxData = array_merge($idxData, array_column($ticketRes,'idx'));
            $idxInfoList = array_merge($idxInfoList,$ticketRes);
            $ticketCodeParams = $ticketRes;
        }

        //预检票校验
        $preCheckRes = $this->preCheckAction($ordernum, $preCheckParams, $idxData);
        if ($preCheckRes['code'] != 200) {
            return $preCheckRes;
        }
        //通过预检且需要退预检指定的idx
        if ($preCheckRes['data'] && isset($preCheckRes['data']['idx_arr']) && $preCheckRes['data']['idx_arr']) {
            $idxData     = $preCheckRes['data']['idx_arr'];
            $idxInfoList = $preCheckRes['data']['idx_info_arr'];
        }

        $idCardCodeParams = array_merge($idCardParams, $ticketCodeParams);

        return $this->returnData(200, '',['arrIdx' => array_unique($idxData),'idxList' => $idxInfoList,'touristType' => $touristType, 'idCardCodeParams' => $idCardCodeParams]);
    }

    /**
     * 验证退票的权限
     *
     * 退票规则：0=有效期内可退 ： 未使用订单末级分销商可退；过期订单供应商可退，1=游玩日期内可退，阶梯退：未使用订单末级分销商可退；过期订单供应商可退；2=不可退：分销商供应商都不可退; 3=随时退：未使用订单末级分销商可退；过期订单分销商可退，供应商可退。'
     *
     * @param  int  $orderNum  订单号
     * @param  int  $opId  操作人id
     * @param  int  $buyId  购买者id - 在平台下单的人
     * @param  int  $orderOwnId  订单所属者id - 订单ss_order 的member
     * @param  int  $applyDid  门票供应商id
     * @param  int  $joinMode  订单组合的模式
     * @param  int  $orderJoinStatus  订单组合的状态
     * @param  int  $trackChannel  追踪记录的渠道值
     * @param  bool  $isAuditPass  是否是审核通过的  这种只是临时解决方案，业务产景套票主票在子票都审核通过后发起了取消，可是这时候订单状态过期了，导致
     * 子票的操作员在$orderJoinStatus = 5 的时候判断不可退 ，这边临时解决这个问题，方案不是非常好
     * @param string $endTime 订单结束时间
     * @return array  err_code 使用 open 项目下的错误码
     *
     */
    private function _checkRefundOpPower($orderNum, $opId, $buyId, $orderOwnId, $applyDid, $salerId, $refundRule, $joinMode, $orderJoinStatus, $trackChannel, $packOrderRes = [], $orderInfoArr = [], $ticketInfoArr = [],$isAuditPass = false,$endTime = '', $ticketExtInfo = [], $showBegin = '')
    {
        if ($opId == 1 || PHP_SAPI == 'cli') {
            return ['code' => 200, 'msg' => '管理员或任务执行'];
        }

        if ($refundRule == 2) {
            return ['code' => 1411, 'msg' => '订单不可退'];
        }

        // 验证服务过来的改单验证，直接供应方和供应商可以取消
        if ($trackChannel == 21) {
            if (in_array($refundRule, ['0', '1', '3']) && in_array($opId, [$applyDid, $salerId])) {
                return ['code' => 200, 'msg' => '供应商或景区操作'];
            }
            //套票退款规则随子票设置
            $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                [$applyDid, $salerId], $packOrderRes, $orderInfoArr, $ticketInfoArr);
            if (!empty($checkSubPackCanRefund)) {
                return $checkSubPackCanRefund;
            }

            return ['code' => 4008, 'msg' => '供应商或景区才可操作'];
        }

        $refundBeforeEarlyTime = (int)$ticketInfoArr[$orderInfoArr[$orderNum]['tid']]['refund_before_early_time'] ?? 0;
        $refundAfterEarlyTime  = (int)$ticketInfoArr[$orderInfoArr[$orderNum]['tid']]['refund_after_early_time'] ?? 0;
        $refundAfterTime       = (int)$ticketInfoArr[$orderInfoArr[$orderNum]['tid']]['refund_after_time'] ?? 0;

        switch ($orderJoinStatus) {
            case '1': // 1、 未出票未使用    1也表示部分出票未使用
            case '2': // 2、 未出票部分使用，部分验证  2也表示部分出票部分使用
            case '9': // 9、 待预约和未使用差不多啦
            case '10': //等待出票状态  和未使用差不多
                switch ($joinMode) {
                    case '1': // 1.微平台+平台
                    case '3': // 3.微商城
                        if (in_array($orderInfoArr[$orderNum]['product_type'], ['A', 'B', 'C', 'F', 'G'])) {
                            //新的退票规则
                            if ($refundRule == 0 && in_array($opId, [$buyId, $applyDid])) {
                                //有效期前X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundBeforeEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("-{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    //不在可退时间内
                                    if ($opId == $applyDid) {
                                        //供应商可退
                                        return ['code' => 200, 'msg' => '不在可退时间内，供应商可退'];
                                    }
                                    if ($opId == $buyId) {
                                        //下单人不可退
                                        return ['code' => 4007, 'msg' => '不在可退时间内'];
                                    }
                                } else {
                                    //在可退时间内
                                    if ($opId == $buyId) {
                                        //下单人可退
                                        return ['code' => 200, 'msg' => '在可退时间内，下单人操作'];
                                    }
                                    if ($opId == $applyDid) {
                                        //供应商不可退
                                        return ['code' => 4006, 'msg' => '在可退时间内，供应商不可退'];
                                    }
                                }
                            }

                            if ($refundRule == 1 && in_array($opId, [$buyId])) {
                                //有效期后X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    return ['code' => 4006, 'msg' => '有效期后X天前可退，不在可退时间内'];
                                } else {
                                    return ['code' => 200, 'msg' => '有效期后X天前可退，下单人可操作'];
                                }
                            }

                            if ($refundRule == 3 && $opId == $buyId) {
                                return ['code' => 200, 'msg' => '下单人操作'];
                            }

                        } else {
                            if (in_array($refundRule, ['0', '1', '3']) && $opId == $buyId) {
                                if ($endTime && strtotime($endTime) + 86399 < time() && !in_array($opId,[$applyDid]) && $refundRule != 3){
                                    return ['code' => 4006, 'msg' => '超过有效期不可退'];
                                }
                                return ['code' => 200, 'msg' => '下单人操作'];
                            }
                        }

                        //套票退款规则随子票设置
                        $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                            [$buyId], $packOrderRes, $orderInfoArr, $ticketInfoArr);
                        if (!empty($checkSubPackCanRefund)) {
                            return $checkSubPackCanRefund;
                        }

                        return ['code' => 4006, 'msg' => '下单人才可操作'];
                        break;
                    case '2': // 2.计调下单+云票务to旅行社
                        if (in_array($orderInfoArr[$orderNum]['product_type'], ['A', 'B', 'C', 'F', 'G'])) {
                            //新的退票规则
                            if ($refundRule == 0 && in_array($opId, [$buyId, $applyDid])) {
                                //有效期前X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundBeforeEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("-{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    //不在可退时间内
                                    if ($opId == $applyDid) {
                                        //供应商可退
                                        return ['code' => 200, 'msg' => '不在可退时间内，供应商可退'];
                                    }
                                    if ($opId == $buyId) {
                                        //下单人不可退
                                        return ['code' => 4007, 'msg' => '不在可退时间内'];
                                    }
                                } else {
                                    //在可退时间内
                                    if ($opId == $buyId) {
                                        //下单人可退
                                        return ['code' => 200, 'msg' => '在可退时间内，下单人操作'];
                                    }
                                    if ($opId == $applyDid) {
                                        //供应商不可退
                                        return ['code' => 4006, 'msg' => '在可退时间内，供应商不可退'];
                                    }
                                }
                            }

                            if ($refundRule == 1 && in_array($opId, [$buyId])) {
                                //有效期后X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    return ['code' => 4006, 'msg' => '有效期后X天前可退，不在可退时间内'];
                                } else {
                                    return ['code' => 200, 'msg' => '有效期后X天前可退，下单人可操作'];
                                }
                            }

                            if ($refundRule == 3 && $opId == $buyId) {
                                return ['code' => 200, 'msg' => '下单人操作'];
                            }
                        } else {
                            if (in_array($refundRule, ['0', '1', '3']) && in_array($opId, [$buyId, $orderOwnId])) {
                                if ($endTime && strtotime($endTime) + 86399 < time() && $refundRule != 3){
                                    return ['code' => 4006, 'msg' => '超过有效期不可退'];
                                }
                                return ['code' => 200, 'msg' => '下单人操作'];
                            }
                        }

                        //套票退款规则随子票设置
                        $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                            [$buyId, $orderOwnId], $packOrderRes, $orderInfoArr, $ticketInfoArr);
                        if (!empty($checkSubPackCanRefund)) {
                            return $checkSubPackCanRefund;
                        }

                        return ['code' => 4006, 'msg' => '下单人才可操作'];
                        break;
                    case '4': // 4.终端+云票务to散客+自助机+闸机
                        if (in_array($orderInfoArr[$orderNum]['product_type'], ['A', 'B', 'C', 'F', 'G'])) {
                            //新的退票规则
                            if ($refundRule == 0 && in_array($opId, [$buyId, $applyDid])) {
                                //有效期前X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundBeforeEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("-{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    //不在可退时间内
                                    if ($opId == $applyDid) {
                                        //供应商可退
                                        return ['code' => 200, 'msg' => '不在可退时间内，供应商可退'];
                                    }
                                    if ($opId == $buyId) {
                                        //下单人不可退
                                        return ['code' => 4007, 'msg' => '不在可退时间内'];
                                    }
                                } else {
                                    //在可退时间内
                                    if ($opId == $buyId) {
                                        //下单人可退
                                        return ['code' => 200, 'msg' => '在可退时间内，下单人操作'];
                                    }
                                    if ($opId == $applyDid) {
                                        //供应商不可退
                                        return ['code' => 4006, 'msg' => '在可退时间内，供应商不可退'];
                                    }
                                }
                            }

                            if ($refundRule == 1 && in_array($opId, [$buyId])) {
                                //有效期后X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    return ['code' => 4006, 'msg' => '有效期后X天前可退，不在可退时间内'];
                                } else {
                                    return ['code' => 200, 'msg' => '有效期后X天前可退，下单人可操作'];
                                }
                            }

                            if ($refundRule == 3 && $opId == $buyId) {
                                return ['code' => 200, 'msg' => '下单人操作'];
                            }

                        } else {
                            if (in_array($refundRule, ['0', '1', '3']) && in_array($opId, [$buyId, $applyDid])) {
                                if ($endTime && strtotime($endTime) + 86399 < time() && !in_array($opId,[$applyDid]) && $refundRule != 3){
                                    return ['code' => 4006, 'msg' => '超过有效期不可退'];
                                }
                                return ['code' => 200, 'msg' => '下单人操作'];
                            }
                        }

                        //套票退款规则随子票设置
                        $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                            [$buyId], $packOrderRes, $orderInfoArr, $ticketInfoArr);
                        if (!empty($checkSubPackCanRefund)) {
                            return $checkSubPackCanRefund;
                        }

                        return ['code' => 4006, 'msg' => '下单人才可操作'];
                        break;
                }
                break;
            case '3': // 3、已出票未使用
            case '4': // 4、已出票部分验证，部分取消
            case '6': // 6、已出票已过期
                switch ($joinMode) {
                        case '1': // 1.微平台+平台
                    case '2': // 2.计调下单+云票务to旅行社
                    case '3': // 3.微商城
                        if (in_array($refundRule, ['0', '1', '3']) && $opId == $applyDid) {
                            return ['code' => 200, 'msg' => '供应商操作'];
                        }

                        if (in_array($orderInfoArr[$orderNum]['product_type'], ['A', 'B', 'C', 'F', 'G'])) {
                            if ($refundRule == 1 && in_array($opId, [$buyId])) {
                                //有效期后X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    return ['code' => 4006, 'msg' => '不在可退时间内'];
                                } else {
                                    return ['code' => 200, 'msg' => '分销商可操作'];
                                }
                            }
                        } elseif ($orderInfoArr[$orderNum]['product_type'] == 'H' && $refundRule == 1 && in_array($opId, [$buyId]) && $refundAfterTime) {
                            //演出条件退需要校验延迟可退配置
                            $tmpTime       = $showBegin ? substr(trim($showBegin), 0, 16) : $endTime;
                            $refundEndTime = strtotime($tmpTime . "+ {$refundAfterTime} minutes");
                            if ($refundEndTime < time()) {
                                return ['code' => 4006, 'msg' => '不在可退时间内'];
                            } else {
                                return ['code' => 200, 'msg' => '分销商可操作'];
                            }
                        }

                        //套票退款规则随子票设置
                        $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                            [$applyDid], $packOrderRes, $orderInfoArr, $ticketInfoArr);
                        if (!empty($checkSubPackCanRefund)) {
                            return $checkSubPackCanRefund;
                        }
                        return ['code' => 4007, 'msg' => '供应商才可操作'];
                        break;
                    case '4': // 4.终端+云票务to散客+自助机+闸机
                        if (in_array($refundRule, ['0', '1', '3']) && in_array($opId, [$buyId, $applyDid])) {
                            return ['code' => 200, 'msg' => '供应商操作'];
                        }

                        //套票退款规则随子票设置
                        $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                            [$applyDid], $packOrderRes, $orderInfoArr, $ticketInfoArr);
                        if (!empty($checkSubPackCanRefund)) {
                            return $checkSubPackCanRefund;
                        }

                        return ['code' => 4007, 'msg' => '供应商才可操作'];
                        break;
                }
                break;
            case '5': // 未出票已过期  fixme 这里应该还有部分取票+已过期
                switch ($joinMode) {
                    case '1': // 1.微平台+平台
                    case '2': // 2.计调下单+云票务to旅行社
                    case '3': // 3.微商城
                    case '4': // 4.终端+云票务to散客+自助机+闸机
                        if (in_array($refundRule, ['0', '1']) && $opId == $applyDid) {
                            return ['code' => 200, 'msg' => '供应商操作'];
                        }
                        // 随时退都可以退
                        if ($refundRule == 3 && in_array($opId, [$buyId, $orderOwnId, $applyDid])) {
                            return ['code' => 200, 'msg' => '下单人或供应商才可操作'];
                        }

                        if (in_array($orderInfoArr[$orderNum]['product_type'], ['A', 'B', 'C', 'F', 'G'])) {
                            if ($refundRule == 1 && in_array($opId, [$buyId])) {
                                //有效期后X天可退
                                list($tmpDay, $tmphour, $tmpminute) = \Business\Product\Ticket::handleRefundMinuToDate($refundAfterEarlyTime);
                                $refundBeforeEarlyTimeDate = empty($tmpDay) ? $endTime : date('Y-m-d', strtotime("+{$tmpDay} day", strtotime($endTime)));
                                $refundBeforeEarlyTimeNew = strtotime("{$refundBeforeEarlyTimeDate} {$tmphour}:{$tmpminute}");
                                if ($refundBeforeEarlyTimeNew < time()) {
                                    return ['code' => 4006, 'msg' => '不在可退时间内'];
                                } else {
                                    return ['code' => 200, 'msg' => '分销商可操作'];
                                }
                            }
                        } elseif ($orderInfoArr[$orderNum]['product_type'] == 'H' && $refundRule == 1 && in_array($opId, [$buyId]) && $refundAfterTime) {
                            //演出条件退需要校验延迟可退配置
                            $tmpTime       = $showBegin ? substr(trim($showBegin), 0, 16) : $endTime;
                            $refundEndTime = strtotime($tmpTime . "+ {$refundAfterTime} minutes");
                            if ($refundEndTime < time()) {
                                return ['code' => 4006, 'msg' => '不在可退时间内'];
                            } else {
                                return ['code' => 200, 'msg' => '分销商可操作'];
                            }
                        }

                        //套票退款规则随子票设置
                        $checkSubPackCanRefund = $this->_checkSubPackageCanRefund($orderNum, $refundRule, $opId,
                            [$buyId, $orderOwnId, $applyDid], $packOrderRes, $orderInfoArr, $ticketInfoArr);
                        if (!empty($checkSubPackCanRefund)) {
                            return $checkSubPackCanRefund;
                        }
                        if ($isAuditPass){
                            return ['code' => 200, 'msg' => '审核已经通过了'];
                        }

                        return ['code' => 4007, 'msg' => '供应商才可操作'];
                        break;
                }
                break;
            case '7':  //特产待发货情况
                switch ($joinMode) {
                    case '1': // 1.微平台+平台
                    case '3': // 3.微商城
                    case '4':// 4.终端+云票务to散客+自助机+闸机
                        if (in_array($refundRule, ['0', '1', '3']) && in_array($opId,[$buyId,$applyDid]) ) {
                            return ['code' => 200, 'msg' => '下单人或供应商操作'];
                        }

                        return ['code' => 4006, 'msg' => '下单人才可操作'];
                        break;
                    case '2': // 2.计调下单+云票务to旅行社
                        if (in_array($refundRule, ['0', '1', '3']) && in_array($opId, [$buyId, $orderOwnId])) {
                            return ['code' => 200, 'msg' => '下单人操作'];
                        }

                        return ['code' => 4006, 'msg' => '下单人才可操作'];
                        break;
                }
                break;
            case '8': //特产已发货情况
                switch ($joinMode) {
                    case '1': // 1.微平台+平台
                    case '2': // 2.计调下单+云票务to旅行社
                    case '3': // 3.微商城
                    case '4': // 4.终端+云票务to散客+自助机+闸机
                        if (in_array($refundRule, ['0', '1']) && $opId == $applyDid) {
                            return ['code' => 200, 'msg' => '供应商可以退'];
                        }
                        if (in_array($refundRule,[3]) && in_array($opId,[$buyId,$applyDid])){
                            return ['code' => 200, 'msg' => '随时退都能退'];
                        }
                        return ['code' => 4006, 'msg' => '供应商才可以操作'];
                        break;
                }
                break;
        }

        // 未知订单类型 先设置 权限未知不可退
        return ['code' => 124, 'msg' => '权限不够，订单不可退'];
    }

    /**
     * 判断套票子票能不能退 外部要传套票子票进来的情况
     * <AUTHOR>
     * @date 2020-01-09
     *
     * @param  int  $orderNum  订单号
     * @param  int  $refundRule  退票属性
     * @param  array  $canRefundUser  能不能退用户
     * @param  array  $packOrderRes  套票数组 【111：{111}】
     * @param  int  $opId  退票用户
     * @param  array  $orderInfoArr  订单信息
     *
     * @return array
     */
    private function _checkSubPackageCanRefund($orderNum, $refundRule, $opId, $canRefundUser = [], $packOrderRes = [], $orderInfoArr = [], $ticketInfoArr = [])
    {
        $res = [];
        //套票退款规则随子票设置
        if (isset($packOrderRes[$orderNum]) && $refundRule == 4 && in_array($opId, $canRefundUser)) {
            foreach ($packOrderRes[$orderNum] as $item) {
                $sonRefund = $ticketInfoArr[$orderInfoArr[$item]['tid']]['refund_rule'];
                if ($sonRefund == 2) {
                    return ['code' => 4009, 'msg' => '套票子票不可取消'];
                }
            }

            return ['code' => 200, '下单人操作'];
        }

        return $res;
    }

    /**
     * 退票频率控制
     * <AUTHOR>
     * @date 2019-07-15
     *
     * @param  int  $ordernum  订单号
     * @param  string  $reqSerialNumber  退票流水号
     * @param  int  $cancelType  退票类型
     * @param  int  $leftNum  剩余有效票数
     * @param  int  $opId  退票用户
     * @param  bool  $isNeedAudit  是否要审核
     * @param  int  $personIndex  指定退票的下标
     *
     * @return array
     */
    private function _frequencyCheck($ordernum, $reqSerialNumber, $cancelType, $leftNum, $opId, $isNeedAudit, $personIndex = 0)
    {
        $ordernum    = strval($ordernum);
        $isNeedAudit = intval($isNeedAudit);
        //如果是开发环境，直接跳过
        if (ENV == 'LOCAL') {
            return $this->orderReturn(200, '');
        }

        $cacheDriver = Cache::getInstance('redis');

        if (empty($reqSerialNumber)) {
            if ($personIndex) {
                $lockKey = "lock:order_refund:{$ordernum}_{$cancelType}_{$leftNum}_{$opId}_{$isNeedAudit}_{$personIndex}";
            } else {
                $lockKey = "lock:order_refund:{$ordernum}_{$cancelType}_{$leftNum}_{$opId}_{$isNeedAudit}";
            }
        } else {
            if ($personIndex) {
                $lockKey = "lock:order_refund:{$ordernum}_{$leftNum}_{$reqSerialNumber}_{$isNeedAudit}_{$personIndex}";
            } else {
                $lockKey = "lock:order_refund:{$ordernum}_{$leftNum}_{$reqSerialNumber}_{$isNeedAudit}";
            }
        }
        //记录key
        $this->_lockKeyArr[$ordernum] = $lockKey;

        $lockTime = 60;
        $lockInfo = $cacheDriver->lock($lockKey, 1, $lockTime);
        if (!$lockInfo) {
            return $this->orderReturn(0, '', ['err_code' => OrderConst::err5005]);
        } else {
            return $this->orderReturn(200, '');
        }
    }

    /**
     * 跟核销系统协调控制，确保不会出现同时被取消和验证的情况
     * <AUTHOR>
     * @date 2019-07-15
     *
     * @param  int  $ordernum  订单号
     * @param  string  $reqSerialNumber  退票流水号
     * @param  int  $cancelType  退票类型
     * @param  int  $leftNum  剩余有效票数
     * @param  int  $opId  退票用户
     *
     * @return array
     */
    private function _verifyCheck($ordernum, $reqSerialNumber, $cancelType, $leftNum, $opId)
    {
        $redis = RedisCache::Connect('terminal');
        //是否存在【订单锁定】
        $getKey = "{$ordernum}-check-lock";

        $res = false;
        try {
            $res = $redis->get($getKey);
        } catch (\Exception $e) {
            //pass
        }

        //说明被锁定
        if ($res) {
            return $this->orderReturn(0, '订单状态被锁定,请稍后再试', ['err_code' => OrderConst::err1419]);
        } else {
            $setKey = "{$ordernum}-cancel-lock";
            $redis->set($setKey, 1, ['nx', 'ex' => 5]);

            return $this->orderReturn(200, '');
        }
    }
    /**
     * 实例化门票模型
     * @return $this_ticketModel
     *
     */
    private function _instanceTicketModel()
    {
        if (!$this->_ticketModel) {
            $this->_ticketModel = new Ticket();
        }

        return $this->_ticketModel;
    }

    /**
     * 实例化分库订单的数据模型
     * @return $this->_subOrderMainModel
     *
     */
    private function _instanceOrderQueryModel()
    {
        if (!$this->_subOrderMainModel) {
            $this->_subOrderMainModel = new SubOrderMain();
        }

        return $this->_subOrderMainModel;
    }

    /**
     * 实例化会员模型
     * @return $this->_memberModel
     *
     */
    private function _instanceMemberModel()
    {
        if (!$this->_memberModel) {
            $this->_memberModel = new Member();
        }

        return $this->_memberModel;
    }

    /**
     * 实例化订单追踪模型
     * @return $this->_orderTrackModel
     *
     */
    private function _instanceOrderTrackModel()
    {
        if (!$this->_orderTrackModel) {
            $this->_orderTrackModel = new OrderTrack();
        }

        return $this->_orderTrackModel;
    }

    /**
     * 仅判断，商户是否有预约不可退功能，且当前操作满足已预约不可退
     * @param $applyDid
     * @param $opId
     * @param $orderInfoArr
     * @param $ticketInfo
     * @param string $orderNum
     * @return array
     */
    public function checkAppointmentRefund($applyDid,$opId,$orderInfoArr,$ticketInfo,$orderNum = ''){
        //否开通待预约退票规则开放功能
        $isAllowRefundAppointment = RefundAudit::isAllowRefundAppointment($applyDid);
        $reservationOrder = 0;
        $refundAppointment = -1;
        if($isAllowRefundAppointment){
            //判断票属性-预约退票规则 -1不限 1预约后不可退
            $refundAppointment = $ticketInfo['reserve_refund_rule'] ?? -1;
            if($refundAppointment > 0){
                //预约后不可退
                //判断订单是否预约 已预约1，否则为空
                if(empty($orderInfoArr)){
                    $queryParams = [$orderNum];
                    $queryRes    = \Business\JavaApi\Order\Query\Container::query('uuSsOrder','queryOrderInfoByOrdernumList', $queryParams);
                    $subOrderInfoArr = $queryRes['data'];
                    if (empty($subOrderInfoArr)) {
                        return ['res' =>false,'opId'=>$opId,'applyDid'=>$applyDid,'reservationOrder'=>$reservationOrder,'refundAppointment'=>$refundAppointment];
                    }
                    $orderInfoArr = array_column($subOrderInfoArr,null,'ordernum');
                }
                $productExt = json_decode($orderInfoArr['product_ext'],true) ?? 0;
                $reservationOrder = isset($productExt["reservationOperateTime"]) ? true : false;
                if($reservationOrder){
                    if($applyDid != $opId){
                        //配置了预约不可退时，当末级购买者已预约成功后，则无取消入口，而顶级供应商支持取消
                        return ['res' =>false,'opId'=>$opId,'applyDid'=>$applyDid,'reservationOrder'=>$reservationOrder,'refundAppointment'=>$refundAppointment];
                    }
                }
            }
        }
        return ['res' =>true,'opId'=>$opId,'applyDid'=>$applyDid,'reservationOrder'=>$reservationOrder,'refundAppointment'=>$refundAppointment];
    }

    /**
     * 报团预定限制规则的校验
     * @param $orderMode //下单渠道
     * @param $cancelList //取消列表 【】
     * @param $opId
     * @param $applyDid
     * @param false $isCancel  //是否全部取消 0否 1是
     * @return array
     */
    public function checkTeamReservationRule($orderMode,$cancelList,$opId,$applyDid,$isCancel = false){
        $orderArr = array_keys($cancelList);
        if($isCancel){
            $cancelList = [];
        }
        else{
            foreach ($cancelList as $key => $value){
                $cancelList[$key] = ['num'=>$value];
            }
        }
//        pft_log('check/Team',json_encode([$orderMode,$orderArr,$cancelList,$opId,$applyDid,$isCancel],JSON_UNESCAPED_UNICODE));
        if(in_array($orderMode,self::TEAM_ORDER_MODE)){
            $modifyBiz = new Modify();
            $res = $modifyBiz->teamOrderCheckDataToOtherChannel($orderArr,$opId,$applyDid,$cancelList);
            if ($res['code'] != 200) {
                return $this->returnData($res['code'], $res['msg']);
            }
//            pft_log('check/Team',json_encode(['res',$res],JSON_UNESCAPED_UNICODE));
        }
        return $this->returnData(200);
    }

    /**
     * 预检票检测操作
     *
     * @param  string  $ordernum  订单号
     * @param  array  $preCheckParams  预检信息
     * @param  array  $idxData  指定退idx数组
     *
     * @return array
     */
    public function preCheckAction($ordernum, $preCheckParams, $idxData)
    {
        $return = [];
        //增加演出预检后是否可退校验
        //判断订单产品类型为演出且普通退且非强制退
        if ($preCheckParams && isset($preCheckParams['p_type']) && isset($preCheckParams['cancel_type']) && isset($preCheckParams['is_force_cancel']) &&
            isset($preCheckParams['ticket_snapshot']) && isset($preCheckParams['cancel_num'])) {
            if ($preCheckParams['p_type'] == 'H' && $preCheckParams['cancel_type'] == 'common' && !$preCheckParams['is_force_cancel']) {
                $checkPreCheckRes = $this->checkShowPreCheckRefund($ordernum, $preCheckParams['ticket_snapshot'], $preCheckParams['ticket_info'], $preCheckParams['tnum'], $preCheckParams['cancel_num'], array_unique($idxData));
                if($checkPreCheckRes['code'] != 200) {
                    return $this->orderReturn(0, $checkPreCheckRes['msg'], ['err_code' => OrderConst::err1426, 'err_msg' => $checkPreCheckRes['msg']]);
                }
                //预检校验通过之后 需要将指定退的idx重新赋值下
                $return['idx_arr']      = $checkPreCheckRes['data']['idx_arr'];
                $return['idx_info_arr'] = $checkPreCheckRes['data']['idx_info_arr'];
            }
        }

        return $this->returnData(200, '检测通过', $return);
    }

    /**
     * 检测订单快照是否开启预检规则，有开启预检规则需要检测是否有预检记录
     *
     * @param  string  $orderNum  订单号
     * @param  array  $ticketSnapshot  门票快照信息
     * @param  array  $ticketInfo  门票信息
     * @param  int  $tnum  订单票数
     * @param  int  $cancelNum  取消数量
     * @param  array  $appointIdxData  指定退的idx数组
     *
     * @return array
     */
    public function checkShowPreCheckRefund($orderNum, $ticketSnapshot, $ticketInfo, $tnum, $cancelNum, $appointIdxData = [])
    {
        //暂时不走快照
        //$preCheckMode       = $ticketSnapshot['confs']['pre_check_mode'] ?? 0;
        //$preCheckRefundRule = $ticketSnapshot['confs']['pre_check_refund_rule'] ?? 1;
        //走实时票属性
        $preCheckMode       = $ticketInfo['pre_check_mode'] ?? 0;
        $preCheckRefundRule = $ticketInfo['pre_check_refund_rule'] ?? 1;

        //未开启预检
        if (!$preCheckMode) {
            return $this->returnData(200, '未开启预检规则，无需检测');
        }

        //未配置预检后不可退
        if ($preCheckRefundRule == 1) {
            return $this->returnData(200, '未配置预检后不可退，无需检测');
        }

        //配置预检不可退之后
        //先获取出所有可退的游客信息
        $handlerModel = new \Model\Order\OrderHandler();
        $touristList = $handlerModel->getOrderTouristInfo($orderNum, 0, '', [0, 4]);
        //没查到可退数据当做所有票都被预检了
        if (!$touristList) {
            return $this->returnData(204, '包含的票已预检，无法退票');
        }

        //查询已预检的信息【调用票务接口查询】

        //请求票务获取游客预检信息
        $preCheckJsonRpc    = new \Business\JsonRpcApi\ScenicLocalService\PreCheckTourist();
        $preCheckRes        = $preCheckJsonRpc->getPreCheckInfo($orderNum);
        $checkTouristIdxArr = [];
        $isAllPreCheck  = false;
        if ($preCheckRes['code'] == 200 && $preCheckRes['data']) {
            $checkTouristIdxArr = array_unique(array_column($preCheckRes['data'], 'idx'));
            $preCheckMaxIdx     = intval(max($checkTouristIdxArr));
            $preCheckNum        = intval(max(array_column($preCheckRes['data'], 'tnum')));
            if ($preCheckMaxIdx == 0 && $preCheckNum >= intval($tnum)){
                $isAllPreCheck = true;
            }
        }
        //组装下预检状态
        foreach ($touristList as &$item) {
            if ($isAllPreCheck) {
                $item['pre_check_state'] = 1;
                continue;
            }
            $item['pre_check_state'] = 0;
            if (in_array($item['idx'], $checkTouristIdxArr)) {
                $item['pre_check_state'] = 1;
            }
        }

        //如果有指定下标、身份证、门票码的 需要检测对应idx是否在 可预检的范围内
        $idxArr     = [];
        $idxInfoArr = [];
        if ($appointIdxData) {
            foreach ($touristList as $value) {
                if (in_array($value['idx'], $appointIdxData) && $value['pre_check_state'] == 0){
                    $idxArr[]     = $value['idx'];
                    $idxInfoArr[] = $value;
                }
            }

            if (count($idxArr) != count($appointIdxData)) {
                return $this->returnData(204, '包含的票已预检，无法退票');
            }
        }else{
            //这边需要取出取消数量对应的idx
            foreach ($touristList as $value){
                if (count($idxArr) < $cancelNum && $value['pre_check_state'] == 0){
                    $idxArr[]     = $value['idx'];
                    $idxInfoArr[] = $value;
                }
            }
        }

        //根据取消数量来判断 数量是否够退 不够直接给错误提示
        if ($cancelNum > count($idxArr)) {
            return $this->returnData(204, '包含的票已预检，无法退票');
        }

        return $this->returnData(200, '预检票退票规则检测通过', ['idx_arr' => $idxArr, 'idx_info_arr' => $idxInfoArr]);
    }
}
