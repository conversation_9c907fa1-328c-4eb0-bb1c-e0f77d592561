<?php
/**
 * 平台下单数据统一校验
 *
 * <AUTHOR>
 * @date 2019-02-15
 *
 */

namespace Business\Order;

use app\model\order\OrderTools;
use Business\Base;
use Business\JavaApi\StorageApi;
use Business\Product\Ticket as TicektBiz;
use Library\Constants\OrderConst;
use Library\Constants\Discount\DiscountType;
use Library\Tools;
use Model\Order\TeamOrderSearch;
use think\Model;

class BaseCheck extends Base
{

    //下单允许的支付方式
    private $_allowPaymodeArr = [
        -1, //java判断支付方式
        0, //余额支付
        1, //在线支付
        2, //授信支付
        3, //自供应
        4, //现场支付
        9, //现金支付
        13, //年卡特权支付
        17, //会员卡支付 - 实际还是授信
        18, // 园区计时卡支付
        33, //市民卡支付
        35, //积分支付
        37, //一卡通身份证支付
        38, //预存余额支付
        51, //预付卡
        74, //无需支付
        255, //未知支付方式
    ];

    private $_teamOrderDiscountKey =  'refund_check:team_order_discount:%s';

    const TEAM_ORDER_MODE = [
        '44',//团单报团计调
        '24'//团队订单
    ];

    public function __construct()
    {

    }

    /**
     * 内部下单时的支付方式的一些处理
     * <AUTHOR>
     * @data   2019-03-22
     *
     * @param  int  $paymode  外部传进来的支付方式
     * @param  bool  $tmpIsNeedInsidePay  是否需要内部支付（美团订单是采用授信或是余额支付的，但是支付和下单的流程是分开的）
     *
     * @return array
     */
    public function handleInsidePay($paymode, $tmpIsNeedInsidePay = true)
    {
        //参数是否允许
        if (!in_array($paymode, $this->_allowPaymodeArr)) {
            return $this->orderReturn(0, '支付方式不允许', ['err_code' => OrderConst::err3]);
        }

        //属于内部支付的支付方式
        if (in_array($paymode, [-1, 0, 2, 3, 4, 9, 13, 17, 35, 37, 38])) {
            //余额，授信，自供自销，现场支付，年卡特权，虚拟会员卡支付，积分，一卡通支付，预存余额支付
            $insidePaymode = $paymode;
        } else {
            $qconf = \Library\Container::pull(\Library\Util\QConfUtil::class);
            if ($qconf->abTest('/php/platform/common_order_online_paymode_transfer')) {
                //在线支付传入中台的值需要变更为255=未知
                $insidePaymode = 255;
            } else {
                //其他的都统一成在线支付
                $insidePaymode = 1;
            }
        }

        if ($tmpIsNeedInsidePay) {
            //默认就是需要支付
            //这个时候还需要看下具体支付方式可以进行内部支付
            if (in_array($paymode, [-1, 0, 2, 3, 9, 13, 17, 35, 37, 38])) {
                $isNeedInsidePay = true;
            } else {
                //现场支付的不需要进行支付，状态直接就是现场支付
                //现金支付和市民卡支付后续会有回调
                $isNeedInsidePay = false;
            }
        } else {
            //如果外部是传不需要支付，就直接不进行内部支付
            $isNeedInsidePay = false;
        }

        $data = [
            'insidePaymode'   => $insidePaymode,
            'isNeedInsidePay' => $isNeedInsidePay,
        ];

        return $this->orderReturn(200, '', $data);
    }

    /**
     * 下单参数基础校验
     * <AUTHOR>
     * @data   2019-02-26
     *
     * @param  int  $memberId  购买用户ID
     * @param  int  $aid  供应商ID
     * @param  int  $tid  门票ID
     * @param  int  $tnum  购买数量
     * @param  int  $playdate  开始游玩日期：2019-02-26
     * @param  int  $orderChannel  购买渠道：0=正常分销商下单, 1=普通用户支付, 2=用户手机支付, 9=会员卡购票, 10=云票务, 11=微信商城, 12=自助机, 14=闸机购票, 15=智能终端, 16=计调下单, 17=淘宝码商, 18=年卡, 19=微信端, 20=OTA, 22=一卡通, 23=套票子票, 33=e福州下单, 34=拼团下单, 35= 积分商城, 38=招行, 39=银联商务, 40=银联pos支付, 41=威富通, 42=易宝云企付, 43=微票房
     * @param  int  $paymode  支付方式：0=账户余额，1=在线支付，2=授信支付, 3=自供自销, 4=现场支付 255=未知支付方式
     * @param  bool  $isNeedPay  是否需要进行支付
     * @param  array  $saleSettingArr  售价信息相关数组
     * @param  array  $contactArr  联系人信息数组
     * @param  string  $remotenum  远程订单号
     * @param  array  $idcardArr  购买用户身份证数组
     * @param  array  $linkOrderInfoArr  关联订单信息数组，比如联票主票，套票主票，统一凭证码等
     * @param  array  $saleSiteArr  购票网点的信息数组
     * @param  array  $remarkArr  购票详情等信息数组
     * @param  array  $showInfoArr  演出相关数据数组
     * @param  array  $specialSettingArr  其他一些下单信息
     *
     * @return array
     */
    public function baseCheck($memberId, $aid, $tid, $tnum, $playdate, $orderTimestamp, $orderChannel, $paymode, $isNeedPay,
        array $saleSettingArr, array $contactArr, $remotenum = '', array $idcardArr = [], $linkOrderInfoArr = [],
        array $saleSiteArr = [], array $remarkArr = [], array $showInfoArr = [], array $specialSettingArr = [])
    {
        $memberId     = intval($memberId);
        $aid          = intval($aid);
        $tid          = intval($tid);
        $tnum         = intval($tnum);
        $playdate     = strval($playdate);
        $orderChannel = intval($orderChannel);
        $paymode      = intval($paymode);
        $remotenum    = strval($remotenum);
        if (!$memberId) {
            return $this->orderReturn(0, '购买用户ID错误', ['err_code' => OrderConst::err3]);
        }
        if (!$aid) {
            return $this->orderReturn(0, '供应商ID错误', ['err_code' => OrderConst::err3]);
        }
        if (!$tid) {
            return $this->orderReturn(0, '产品ID错误', ['err_code' => OrderConst::err3]);
        }
        if ($tnum <= 0) {
            return $this->orderReturn(0, '购买数量必须大于0', ['err_code' => OrderConst::err1053]);
        }

        $orderSetting = load_config('order_setting', 'order');
        $maxTnum      = $orderSetting['max_tnum'];

        if ($tnum > $maxTnum) {
            return $this->orderReturn(0, "购买数量不能大于{$maxTnum}", ['err_code' => OrderConst::err1053]);
        }

        if (!chk_date($playdate)) {
            return $this->orderReturn(0, "游玩日期{$playdate}格式错误", ['err_code' => OrderConst::err1069]);
        }

        if ($playdate < date('Y-m-d', $orderTimestamp) && $orderChannel != 46) {
            return $this->orderReturn(0, "游玩日期{$playdate}已超过当日购买的时间", ['err_code' => OrderConst::err1069]);
        }

        $ordermodeArr = load_config('order_mode_track_map', 'business');
        if (!array_key_exists($orderChannel, $ordermodeArr)) {
            return $this->orderReturn(0, "下单渠道[{$orderChannel}]不存在", ['err_code' => OrderConst::err3]);
        }

        if (!in_array($paymode, [-1, 0, 1, 2, 3, 4, 9, 13, 17, 35, 37, 38, 51, 255])) {
            return $this->orderReturn(0, "下单支付方式[{$paymode}]不存在", ['err_code' => OrderConst::err3]);
        }

        //自供自销判断
        if ($paymode == 3 && $memberId != $aid) {
            return $this->orderReturn(0, "自供自销参数错误", ['err_code' => OrderConst::err3]);
        }

        if (!isset($saleSettingArr['is_sale'])) {
            return $this->orderReturn(0, "是否散客购买参数缺失", ['err_code' => OrderConst::err3]);
        }

        //是否零售价，和优惠信息处理
        $isSale        = $saleSettingArr['is_sale'];
        $upperSupplier = isset($saleSettingArr['upper_supplier']) ? intval($saleSettingArr['upper_supplier']) : 0;
        $discountList  = isset($saleSettingArr['discount_list']) ? (array)($saleSettingArr['discount_list']) : [];
        $usePoint      = isset($saleSettingArr['use_point']) ? ($saleSettingArr['use_point']) : false;
        $useCoupon     = isset($saleSettingArr['use_coupon']) ? ($saleSettingArr['use_coupon']) : false;
        $useDiscount   = isset($saleSettingArr['use_discount']) ? ($saleSettingArr['use_discount']) : false;
        $useMarketingDiscount = isset($saleSettingArr['useMarketingDiscount']) ? ($saleSettingArr['useMarketingDiscount']) : false;
        //积分抵现使用详情
        $pointUseInfo = isset($saleSettingArr['point_use_info']) ? $saleSettingArr['point_use_info'] : [];
        //分销优惠特价快照ID
        $bargainPricePolicyId = isset($saleSettingArr['bargainPricePolicyId']) ? $saleSettingArr['bargainPricePolicyId'] : false;
        //分销优惠减免快照ID
        $specifyReducedPolicyId = isset($saleSettingArr['specifyReducedPolicyId']) ? $saleSettingArr['specifyReducedPolicyId'] : false;

        //联系人信息数组
        $tmpOrdername = isset($contactArr['ordername']) ? strval($contactArr['ordername']) : '';
        $tmpOrdername = explode(',', $tmpOrdername); //有可能通过','传多个姓名进来
        $ordername    = strval($tmpOrdername[0]);

        //联系人姓名长度不能超过40
        if (mb_strlen($ordername) > 40) {
            return $this->orderReturn(0, '联系人姓名不能超过40个字符', ['err_code' => OrderConst::err3]);
        }

        //取票人和联系人信息
        $ordertel     = isset($contactArr['ordertel']) ? strval($contactArr['ordertel']) : '';
        $mobileArea   = strval($contactArr['mobile_area'] ?? '');
        $mobileRegion   = strval($contactArr['mobile_region'] ?? '');
        $isSendSms    = isset($contactArr['is_sms']) ? boolval($contactArr['is_sms']) : true;
        $contacttel   = isset($contactArr['contacttel']) ? strval($contactArr['contacttel']) : '';
        $provinceCode = intval($contactArr['province_code'] ?? 0);
        $cityCode     = intval($contactArr['city_code'] ?? 0);
        $townCode     = intval($contactArr['town_code'] ?? 0);
        $address      = $contactArr['address'] ?? '';
        //判断是否是用会员优惠
        $isMemberDiscount = isset($saleSettingArr['member_discount_code']) ? $saleSettingArr['member_discount_code'] : 0;

        // 校验取票人身份信息
        $personId    = isset($contactArr['personid']) ? strval($contactArr['personid']) : '';
        $voucherType = isset($contactArr['voucher_type']) ? intval($contactArr['voucher_type']) : 0;

        if ($voucherType == 1) {
            // 暂时只对身份证类型进行校验
            $idCardCheckRes = Tools::personID_format_err($personId);
            if ($idCardCheckRes !== true) {
                return $this->orderReturn(0, "身份证[$personId]校验不通过", ['err_code' => OrderConst::err1093]);
            }
        }
        //取票人手机号长度不能超过16
        if (mb_strlen($ordertel) > 16) {
            return $this->orderReturn(0, '取票人手机号不能超过16个字符', ['err_code' => OrderConst::err3]);
        }

        //OTA下单的时候会带核销和取消的回调地址
        $callbackUrl = '';
        if ($remotenum && isset($contactArr['callback_url'])) {
            $tmpUrl = strval($contactArr['callback_url']);

            //判断地址是否是合法的
            if (checkUrl($tmpUrl)) {
                $callbackUrl = $tmpUrl;
            }
        }

        $ordername = filter_utf8($ordername);
        if (!$ordername) {
            return $this->orderReturn(0, "联系人姓名格式错误", ['err_code' => OrderConst::err35]);
        }
        if ($isMemberDiscount) {
            if (!$upperSupplier) {
                return $this->orderReturn(0, "使用会员下单缺失上级供应商或者分销商参数", ['err_code' => OrderConst::err36]);
            }
        }
        //手机号处理
        if ($ordertel == '12301') {
            //有些地方没有手机号就传12301过来
            $isSendSms = false;
            $ordertel  = '';
        } else {
            if ($isSendSms) {
                //需要发送短信的时候才进行手机号的校验
                if (in_array($mobileArea, ['', '86']) && !ismobile($ordertel)) {
                    return $this->orderReturn(0, "手机号格式错误", ['err_code' => OrderConst::err12]);
                }
            }
        }

        //外部如果没有转contacttel，使用ordertel
        $contacttel = $contacttel ?: $ordertel;

        //购买用户身份证数组
        if ($idcardArr) {
            //身份证临时数组,判断是否重复
            $tmpIdcardArr = [];
            foreach ($idcardArr as $k => $idcardItem) {
                if ($idcardItem['voucher_type'] == 1) {
                    // 暂时只对身份证校验
                    $tmpIdcard      = strtoupper($idcardItem['idcard']);
                    $idcardCheckRes = Tools::personID_format_err($tmpIdcard);
                    if ($idcardCheckRes !== true) {
                        return $this->orderReturn(0, "身份证[$tmpIdcard]校验不通过", ['err_code' => OrderConst::err1093]);
                    }

                    //身份证姓名长度不能超过20
                    if ($idcardItem['name'] && mb_strlen($idcardItem['name']) > 20) {
                        return $this->orderReturn(0, '身份证姓名不能超过20个字符', ['err_code' => OrderConst::err3]);
                    }

                    if (in_array($tmpIdcard, $tmpIdcardArr)) {
                        return $this->orderReturn(0, "身份证[$tmpIdcard]重复", ['err_code' => OrderConst::err1093]);
                    }
                    $idcardArr[$k]['idcard'] = $tmpIdcard;
                    $tmpIdcardArr[]          = $tmpIdcard;
                }
            }
        }

        //关联订单信息数组，比如联票主票，套票主票，统一凭证码等
        $linkType       = isset($linkOrderInfoArr['link_type']) ? strval($linkOrderInfoArr['link_type']) : 'common';
        $parentOrdernum = isset($linkOrderInfoArr['parent_ordernum']) ? strval($linkOrderInfoArr['parent_ordernum']) : '';

        //联票或是套票的子票的情况下
        if (in_array($linkType, ['link_son', 'package_son'])) {
            if (!$parentOrdernum) {
                return $this->orderReturn(0, "主票订单号不能为空", ['err_code' => OrderConst::err1086]);
            }
        }

        //分时预约参数检测
        if (isset($remarkArr['time_share_info']) && $remarkArr['time_share_info']) {
            if (!isset($remarkArr['time_share_info']['time_id'], $remarkArr['time_share_info']['time_str'])) {
                return $this->orderReturn(0, "分时预约参数错误", ['err_code' => OrderConst::err3]);
            }
            $tmpTimeArr = explode('-', $remarkArr['time_share_info']['time_str']);
            if (date('Y-m-d') == $playdate && date('H:i') > $tmpTimeArr[1]) {
                return $this->orderReturn(0, "预约时间段不能小于当前时间", ['err_code' => OrderConst::err3]);
            }
        }
        //套票子票分时预约参数检测
        if (isset($remarkArr['package_time_share_info']) && $remarkArr['package_time_share_info']) {
            foreach ($remarkArr['package_time_share_info'] as $tmpTid => $timeItem) {
                if (!is_int($tmpTid) || !isset($timeItem['time_id'], $timeItem['time_str'])) {
                    return $this->orderReturn(0, "分时预约参数错误", ['err_code' => OrderConst::err3]);
                }
                $tmpTimeArr = explode('-', $timeItem['time_str']);
                if (date('Y-m-d') == $playdate && date('H:i') > $tmpTimeArr[1]) {
                    return $this->orderReturn(0, "子票预约时间段不能小于当前时间", ['err_code' => OrderConst::err3]);
                }
            }
        }

        //传第一笔订单号(购票限制(黑白名单)合并付款需要传第一笔订单号)
        $firstOrderNum = isset($remarkArr['first_ordernum']) ? strval($remarkArr['first_ordernum']) : '';

        //购票网点的信息数组
        $siteId   = isset($saleSiteArr['site_id']) ? strval($saleSiteArr['site_id']) : '';
        $terminal = isset($saleSiteArr['terminal']) ? strval($saleSiteArr['terminal']) : '';
        $stuffId  = isset($saleSiteArr['stuff_id']) ? strval($saleSiteArr['stuff_id']) : 0;

        //客户端ip校验
        $clientIp    = '';
        $tmpClientIp = isset($saleSiteArr['client_ip']) ? strval($saleSiteArr['client_ip']) : '';
        if ($tmpClientIp) {
            $long = sprintf("%u", ip2long($tmpClientIp));
            if ($long) {
                $clientIp = $tmpClientIp;
            }
        }

        //购票详情等信息数组
        $memo                 = isset($remarkArr['memo']) && $remarkArr['memo'] ? filter_utf8($remarkArr['memo']) : '';
        $origin               = isset($remarkArr['origin']) ? strval($remarkArr['origin']) : '';
        $assembly             = isset($remarkArr['assembly']) ? intval($remarkArr['assembly']) : 0;
        $serialNumber         = isset($remarkArr['serial_number']) ? strval($remarkArr['serial_number']) : '';
        $timeShareInfo        = $remarkArr['time_share_info'] ?? [];
        $packageTimeShareInfo = $remarkArr['package_time_share_info'] ?? [];
        $packageShowInfo      = $remarkArr['package_show_info'] ?? [];
        $arrPrePoseId         = isset($remarkArr['pre_pose_ids']) ? $remarkArr['pre_pose_ids'] : [];

        //演出相关数据数组
        $venueId      = isset($showInfoArr['venue_id']) ? intval($showInfoArr['venue_id']) : '';
        $roundId      = isset($showInfoArr['round_id']) ? intval($showInfoArr['round_id']) : '';
        $areaId       = isset($showInfoArr['area_id']) ? intval($showInfoArr['area_id']) : '';
        $subAreaId    = isset($showInfoArr['sub_area_id']) ? intval($showInfoArr['sub_area_id']) : '';
        $seatIds      = isset($showInfoArr['seat_ids']) ? strval($showInfoArr['seat_ids']) : '';
        $LinkSeatMark = isset($showInfoArr['link_seat_mark']) ? strval($showInfoArr['link_seat_mark']) : '';
        //演出是否强制跳过提前预订判断
        $skipBooking  = isset($showInfoArr['skip_booking']) ? intval($showInfoArr['skip_booking']) : 0;
        //演出捆绑票信息
        $childTicketShowInfoArr  = isset($showInfoArr['childTicketShowInfoArr']) ? $showInfoArr['childTicketShowInfoArr'] : [];
        //演出下单对应的订单来源: 猫眼("maoyan") 大麦("damai")
        $orderSource  = isset($showInfoArr['order_source']) ? $showInfoArr['order_source'] : '';
        //下单来源是大麦的 需要限制下套票或捆绑票下单，因为大麦下单成功后订单一定要能成功  套票子票可能存在下单失败的风险，所以通用下单接口增加该校验
        if ($orderSource && in_array($orderSource, ['damai'])) {
            //判断下是否是套票主票
            $isPackageOrder = false;
            $sonTicketInfo = (new \Model\PackTicket\PackageTicketsModel())->getSonPackageTicketListByParentId($tid);
            if ($sonTicketInfo) {
                $isPackageOrder = true;
            }

            //有子票的情况 都不让下单
            if ($childTicketShowInfoArr || $isPackageOrder) {
                return $this->orderReturn(0, "大麦不支持演出捆绑票下单", ['err_code' => OrderConst::err3]);
            }
        }

        //积分兑换比例
        $pointRate = isset($specialSettingArr['point_rate']) ? $specialSettingArr['point_rate'] : 0;

        if ($roundId) {
            $showInfo = [
                'venue_id'       => $venueId,
                'round_id'       => $roundId,
                'area_id'        => $areaId,
                'sub_area_id'    => $subAreaId,
                'seat_ids'       => $seatIds,
                'link_seat_mark' => $LinkSeatMark,
                'skip_booking'   => $skipBooking,
            ];
        } else {
            $showInfo = [];
        }

        //是否进行特殊票种的判断
        $isVerifySpecial = isset($specialSettingArr['is_verify_special']) ? boolval($specialSettingArr['is_verify_special']) : true;

        //是否需要进行回调
        $isNeedCallback = isset($specialSettingArr['is_need_callback']) ? boolval($specialSettingArr['is_need_callback']) : false;

        //外部有传对应的景区id，外部接口有传这个参数，需要进行判断
        $landId = isset($specialSettingArr['land_id']) ? intval($specialSettingArr['land_id']) : 0;

        // 销售渠道同shop字段
        $shop = $specialSettingArr['shop'] ?? 0;

        //特产运费
        $carriage = intval($specialSettingArr['carriage'] ?? 0);
        //特产取货方式 0快递1自提
        $deliveryWay = intval($specialSettingArr['delivery_way'] ?? 0);

        //酒店离店时间
        $leavetime = $specialSettingArr['leavetime'] ?? '';

        // 下单提供的额外结算方式
        $distributePrices = $specialSettingArr['distributePrices'] ?? [];

        //下单价格方式：1窗口价 2零售价
        $useWindowPrice = $specialSettingArr['useWindowPrice'] ?? true;

        //特殊库存(库存为0时可预约) ：1是 2否
        $moreStorage = $specialSettingArr['moreStorage'] ?? 2;

        //计时卡是否收取押金  0否 1是
        $tmpDeposit = $specialSettingArr['time_card_deposit'] ?? 0;
	    $timing_device_id =  $specialSettingArr['timing_device_id'] ?? 0;

        $isNotSendSms = isset($contactArr['is_not_send_sms']) ? $contactArr['is_not_send_sms'] : 0;

        //需要收取押金的 检查一次押金情况
        $timeCardDeposit = 0;
        if ($tmpDeposit) {
            $timeCardApi    = new \Business\JsonRpcApi\ScenicLocalService\TimeProduct();
            $timeProductRes = $timeCardApi->getTimeProductInfoByTicketId($tid);

            if ($timeProductRes['code'] != 200 || empty($timeProductRes['data'])) {
                return $this->orderReturn(0, "计时产品押金数据获取异常", ['err_code' => OrderConst::err1066]);
            }

            //押金需要根据票数量来
            $timeCardDeposit = $timeProductRes['data']['deposit'] * $tnum;
        }

        //所有下单需要使用的参数
        $orderParams = [
            'memberId'               => $memberId,
            'aid'                    => $aid,
            'tid'                    => $tid,
            'tnum'                   => $tnum,
            'playdate'               => $playdate,
            'orderChannel'           => $orderChannel,
            'paymode'                => $paymode,
            'remotenum'              => $remotenum,
            'callbackUrl'            => $callbackUrl,
            'isSale'                 => $isSale,
            'upperSupplier'          => $upperSupplier,
            'discountList'           => $discountList,
            'ordername'              => $ordername,
            'ordertel'               => $ordertel,
            'mobile_area'            => $mobileArea,
            'mobile_region'          => $mobileRegion,
            'personid'               => $personId,
            'voucher_type'           => $voucherType,
            'contacttel'             => $contacttel,
            'idcardArr'              => $idcardArr,
            'province_code'          => $provinceCode,
            'city_code'              => $cityCode,
            'town_code'              => $townCode,
            'address'                => $address,
            'linkType'               => $linkType,
            'parentOrdernum'         => $parentOrdernum,
            'siteId'                 => $siteId,
            'terminal'               => $terminal,
            'stuffId'                => $stuffId,
            'clientIp'               => $clientIp,
            'memo'                   => $memo,
            'origin'                 => $origin,
            'assembly'               => $assembly,
            'serialNumber'           => $serialNumber,
            'showInfo'               => $showInfo,
            'isVerifySpecial'        => $isVerifySpecial,
            'isNeedCallback'         => $isNeedCallback,
            'isNeedSendSms'          => $isSendSms,
            'isNeedPay'              => $isNeedPay,
            'outLandId'              => $landId,
            'point_rate'             => $pointRate,
            'shop'                   => $shop,
            'carriage'               => $carriage,
            'delivery_way'           => $deliveryWay,
            'memberShipDiscountCode' => $isMemberDiscount,
            'timeShareInfo'          => $timeShareInfo,
            'packageTimeShareInfo'   => $packageTimeShareInfo,
            'packageShowInfo'        => $packageShowInfo,
            'leavetime'              => $leavetime,
            'contactMoinfolist'      => $contactArr['moreList'] ?? [],
            'distributePrices'       => $distributePrices,
            'firstOrderNum'          => $firstOrderNum,
            'pre_pose_ids'           => $arrPrePoseId,
            'useWindowPrice'         => $useWindowPrice,
            'moreStorage'            => $moreStorage,
            'is_not_send_sms'        => $isNotSendSms,
            'timeCardDeposit'        => $timeCardDeposit,
            'timing_device_id'       => $timing_device_id,
            'pointUseInfo'           => $pointUseInfo,
            'use_point'              => $usePoint,
            'use_coupon'             => $useCoupon,
            'use_discount'           => $useDiscount,
            'useMarketingDiscount'   => $useMarketingDiscount,
            'bargainPricePolicyId'   => $bargainPricePolicyId,
            'specifyReducedPolicyId' => $specifyReducedPolicyId,
            'childTicketShowInfoArr' => $childTicketShowInfoArr,
            'order_source'           => $orderSource,
        ];

        // 添加子商户操作人id
        if (isset($saleSiteArr['subStuffId'])) {
            $orderParams['subStuffId'] = intval($saleSiteArr['subStuffId']);
        }

        return $this->orderReturn(200, "", $orderParams);
    }

    /**
     * JAVA版本的分销权限判断
     * 主要还是为了能够拿到分销链，后面的演出系统里面需要
     *
     * <AUTHOR>
     * @date   2019-05-16
     *
     * @param  int  $memberId  购买用户ID
     * @param  int  $aid  供应商商ID
     * @param  int  $applyDid  原始供应商商ID
     * @param  int  $tid  商品ID
     * @param  int  $isSale  是否零售
     * @param  int  $orderChannel  购买渠道
     * @param  int  $upperSupplier  更上一级的供应商（散客找aid购买转分销来的产品的时候）
     *
     * @return array
     */
    public function newDistributeCheck($memberId, $aid, $applyDid, $tid, $pid, $playdate, $isSale, $orderChannel, $upperSupplier = 0)
    {
        $isHaveDistriubteAuth = false;
        $evoArr               = []; //分销链

        if ($isSale) {
            //散客购买
            //如果没有传更上一级的供应商过来，默认就是直接从原始供应商那里采购的
            $upperSupplier = $upperSupplier ?: $applyDid;

            if ($aid == $upperSupplier) {
                if ($upperSupplier == $applyDid) {
                    //自供应，如果不是自供应商，那就是参数有问题的
                    $isHaveDistriubteAuth = true;
                    $evoArr               = [];
                    $evoRes               = [
                        'sid' => $applyDid,
                        'fid' => $applyDid,
                    ];
                }
            } else {
                //采购过来的
                //$evoBiz = new \Business\JavaApi\EvoluteApi();
                //$evoRes = $evoBiz->getActiveChain($tid, $aid, $upperSupplier);
                $evoBiz = new \Business\JavaApi\Product\Distribution();
                $evoRes = $evoBiz->getDistributionByList($pid, $aid, $upperSupplier);

                if ($evoRes) {
                    $isHaveDistriubteAuth = true;

                    $aidsArr   = trim($evoRes['aids'], '') . ',' . $aid;
                    $tmpEvoArr = explode(',', $aidsArr);

                    //如果是直销的话，就没有分销链了
                    if (count($tmpEvoArr) <= 1) {
                        $evoArr = [];
                    } else {
                        $evoArr = $tmpEvoArr;
                    }
                }
            }
        } else {
            if ($memberId == $aid) {
                if ($aid == $applyDid) {
                    //自供应，如果不是自供应商，那就是参数有问题的
                    $isHaveDistriubteAuth = true;
                    $evoArr               = [];
                    $evoRes               = [
                        'sid' => $aid,
                        'fid' => $aid,
                    ];
                }
            } else {
                //采购过来的
                //$evoBiz = new \Business\JavaApi\EvoluteApi();
                //$evoRes = $evoBiz->getActiveChain($tid, $memberId, $aid);
                $evoBiz = new \Business\JavaApi\Product\Distribution();
                $evoRes = $evoBiz->getDistributionByList($pid, $memberId, $aid);
                if ($evoRes) {
                    $isHaveDistriubteAuth = true;

                    $aidsArr   = trim($evoRes['aids'], '');
                    $tmpEvoArr = explode(',', $aidsArr);

                    //如果是直销的话，就没有分销链了
                    if (count($tmpEvoArr) <= 1) {
                        $evoArr = [];
                    } else {
                        $evoArr = $tmpEvoArr;
                    }
                }
            }
        }

        if ($isHaveDistriubteAuth) {
            $data   = [
                'evo_arr' => $evoArr,
            ];
            $evoRes = empty($evoRes) ? [] : $evoRes;
            //组合数据下
            $data = array_merge($data, $evoRes);

            return $this->orderReturn(200, "", $data);
        } else {
            return $this->orderReturn(0, '', [
                'err_code'  => OrderConst::err1052,
                'inner_msg' => "产品tid={$tid}在{$playdate},分销商={$memberId},供应商={$aid},没有分销权限",
            ]);
        }
    }

    /**
     * 判断是否是资源中心关系
     *  之前版本只判断末级的关系，实际上是需要判断前两级的关系的
     *
     * @param $lastMemberRelation int 末级分销关系
     * @param $pid int 产品pid
     * @param $aidsArr array 上级所有分销商ID
     */
    public function isResourceOrder($lastMemberRelation, $pid, $aidsArr)
    {
        if ($lastMemberRelation == 4) {
            return true;
        } else {
            //分销链情况  A -> B -> C -> D
            //这种情况，只需要判断 A -> B 或是  B -> C 有没有资源中心关系，如果有的话就是有
            if (count($aidsArr) < 2) {
                //没有更上级的话，就直接返回false
                return false;
            } else {
                //只需要截取前三个分销商就可以了
                $needApplyIdNum = 3;
                $targetAidsArr  = array_splice($aidsArr, 0, $needApplyIdNum);

                $evoBiz = new \Business\JavaApi\Product\Distribution();

                $targetCount = count($targetAidsArr);
                for ($i = 0; $i <= $targetCount - 2; $i++) {
                    $upAid   = $targetAidsArr[$i];
                    $downAid = $targetAidsArr[$i + 1];

                    $evoRes = $evoBiz->getDistributionByList($pid, $downAid, $upAid);
                    if ($evoRes && $evoRes['memberRelation'] == 4) {
                        return true;
                    }
                }

                //如果都没有资源中心关系，直接返回false
                return false;
            }
        }
    }

    /**
     * 是否有下单权限，主要针对员工账号
     * <AUTHOR>
     * @date   2019-04-10
     *
     * @param  integer  $memberId  会员id
     *
     * @return boolean
     */
    public function isHaveOrderAuth($memberId)
    {
        if (!$memberId) {
            return false;
        }

        $memberBiz  = new \Business\Member\Member();
        $memberInfo = $memberBiz->getInfo($memberId);

        if (empty($memberInfo) || $memberInfo['status'] != 0) {
            return false;
        }
        if ($memberInfo['dtype'] == 6) {
            if (!in_array('pro', explode(',', $memberInfo['member_auth']))) {
                // 当前员工账号 没有产品预订权限
                return false;
            }
        }

        return true;
    }

    /**
     * 根据订单号 获取是否团
     *
     * @param $orderNum
     * @return array
     */
    public function checkTeamOrderDiscount($orderNum,$orderMode ='',$mainOrderNum =''){
        $teamModel = new TeamOrderSearch();
        if($mainOrderNum){
            goto GET_INFO;
        }
        if(empty($orderMode)){
            $orderTools = new \Model\Order\OrderTools();
            $mainOrderInfo = $orderTools->getOrderInfo($orderNum, 'ordermode');
            $orderMode     = (int)$mainOrderInfo['ordermode'];
        }
        if(!in_array($orderMode,self::TEAM_ORDER_MODE)){
            return $this->returnData(200);
        }
        //根据订单号查找同级团单
        $isUseCoupon  = false;
        $orderInfo = $teamModel->getMainOrderInfoBySonOrder($orderNum);
        if (empty($orderInfo)) {
            return $this->returnData(204, '订单不属于团单');
        }
        $mainOrderNum =  $orderInfo['main_ordernum'];
        GET_INFO:
        //判断是否未优惠团单 先从缓存中取
        $discountData = self::getCacheTeamOrderDiscount($mainOrderNum);
        if(!empty($discountData)){
            return $this->returnData(200,'',$discountData);
        }
        else{
            $sonOrderList = $teamModel->getDataByMainOrder($mainOrderNum);
            $orderNumArr = array_column($sonOrderList, 'son_ordernum');
            $orderQueryModel = new \Model\Order\OrderTools();
            $orderInfoArr = $orderQueryModel->getOrderInfo($orderNumArr);
            $useCouponList = [];
            foreach ($orderInfoArr as $value){
                $extContent = json_decode($value['ext_content'],true);
                if((isset($extContent['bargainPricePolicyId']) && !empty($extContent['bargainPricePolicyId'])) ||
                    (isset($extContent['specifyReducedPolicyId']) && !empty($extContent['specifyReducedPolicyId']))){
                    //有快照id就有使用折扣，没有快照id就是没有折扣
                    array_push($useCouponList,$value['ordernum']);
                }
            }
            if(!empty($useCouponList)){
                $isUseCoupon = true;
            }
            $discountData =  self::setCacheTeamOrderDiscount($mainOrderNum,$isUseCoupon,$useCouponList);
            return $this->returnData(200,'',$discountData);
        }
    }

    private function getCacheTeamOrderDiscount($mainOrderNum){
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        $cacheKey = sprintf($this->_teamOrderDiscountKey, $mainOrderNum);
        $data = $cacheRedis->hgetAll($cacheKey);
        if(empty($data)){
            return [];
        }
        return $data;
    }

    private function setCacheTeamOrderDiscount($mainOrderNum,$isUseCoupon,$useCouponList){
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        $cacheKey = sprintf($this->_teamOrderDiscountKey, $mainOrderNum);
        $data = [
            'isUseCoupon'=>$isUseCoupon,
            'useCouponList'=>$useCouponList
        ];
        $cacheRedis->hmset($cacheKey, $data);
        $cacheRedis->expire($cacheKey, 3600 * 24);
        return $data;
    }

    /**
     * 检测订单是否使用优惠
     * @param  string  $orderNum  订单号
     * @param  array  $extContentMap  扩展属性
     *
     * @return bool
     */
    public static function checkUseDiscount($orderNum, $extContentMap)
    {
        //判断是否有使用对应优惠类型
        $isUsePoint    = isset($extContentMap[$orderNum]['usePoint']) ? $extContentMap[$orderNum]['usePoint'] : false;
        $isUseCoupon   = isset($extContentMap[$orderNum]['useCoupon']) ? $extContentMap[$orderNum]['useCoupon'] : false;
        $isUseDiscount = isset($extContentMap[$orderNum]['useDiscount']) ? $extContentMap[$orderNum]['useDiscount'] : false;
        //TODO 后续上述字段后续全部更新后， 均废弃，使用营销优惠通用字段判断是否优惠
        $useMarketingDiscount = isset($extContentMap[$orderNum]['useMarketingDiscount']) ? $extContentMap[$orderNum]['useMarketingDiscount'] : false;
        if ($orderNum && ($isUsePoint || $isUseCoupon || $isUseDiscount || $useMarketingDiscount)) {
            return true;
        }

        return false;
    }

    /**
     * 检测订单 使用营销优惠信息
     * @param $orderNum
     * @param array $extContentMap
     * @param array $appointTypeArray
     * @return array
     * @throws \Exception
     */
    public static function checkUseCommonDiscount($orderNum,$extContentMap = [],$appointTypeArray= [])
    {
        $discountArray = [];//订单优惠明细
        $discountAmount = 0;//订单优惠总金额
        $useDiscount = false; //是否使用优惠
        //是否使用新版优惠
        $useMarketingDiscount = isset($extContentMap[$orderNum]['useMarketingDiscount']) ? $extContentMap[$orderNum]['useMarketingDiscount'] : false;
        //到中台查询当前订单的营销优惠信息
        $discountApi = new \Business\JavaApi\Order\OrderTicketDiscounts();
        $discountRes = $discountApi->summaryDiscountByOrderNums([$orderNum]);
        if ($discountRes['code'] == 200 && !empty($discountRes['data'])) {
            $discountDetail = array_column($discountRes['data'], null, 'orderNum');
            $useDiscount = true;
            $discountItems= array_column($discountDetail[$orderNum]['discountItems'],null,'discountType');
            $discountAmount = $discountDetail[$orderNum]['discountAmount'];
            //如果指定了优惠类型，则只返回对应类型的优惠

            if(!empty($appointTypeArray)){
                foreach ($appointTypeArray as $appointType){
                    if(array_key_exists($appointType,$discountItems)){
                        array_push($discountArray,$discountItems[$appointType]);
                    }
                }
            }
        }
        return [$useDiscount,$useMarketingDiscount,$discountArray,$discountAmount];
    }

    /**
     * 校验是否是属于新版营销优惠逻辑
     * @param $orderNum
     * @param $extContentMap
     * @return bool
     * @throws \Exception
     */
    public static function checkUseCommonDiscountForNewVersion($orderNum,$extContentMap){
        list($useDiscount,$useMarketingDiscount,$discountArray,$discountAmount) = self::checkUseCommonDiscount($orderNum,$extContentMap,[ DiscountType::DISCOUNT_DEDUCT,DiscountType::DISCOUNT_SPECIAL]);
        //有使用优惠，且使用的是新版营销优惠，且 【没有】指定类型优惠明细
        return $useDiscount && $useMarketingDiscount && !empty($discountArray);
    }

    /**
     * 检测订单是否使用分销优惠
     * @param  string  $orderNum  订单号
     * @param  array  $extContentMap  扩展属性
     *
     * @return bool
     */
    public static function checkUseSettlementDiscount($orderNum, $extContentMap)
    {
        //判断是否有使用对应优惠类型
        //特价快照ID
        $isUseSettlementDiscountBargain        = isset($extContentMap[$orderNum]['bargainPricePolicyId']) ? $extContentMap[$orderNum]['bargainPricePolicyId'] : false;
        //减免快照ID
        $isUseSettlementDiscountSpecifyReduced = isset($extContentMap[$orderNum]['specifyReducedPolicyId']) ? $extContentMap[$orderNum]['specifyReducedPolicyId'] : false;

        $useMarketingDiscount = isset($extContentMap[$orderNum]['useMarketingDiscount']) ? $extContentMap[$orderNum]['useMarketingDiscount'] : false;
        if ($orderNum && ($isUseSettlementDiscountBargain || $isUseSettlementDiscountSpecifyReduced || $useMarketingDiscount)) {
            return true;
        }

        return false;
    }
}
