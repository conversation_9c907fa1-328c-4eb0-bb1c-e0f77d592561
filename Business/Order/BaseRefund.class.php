<?php
/**
 * 平台退票基础接口
 *
 * <AUTHOR>
 * @date 2019-02-15
 *
 */

namespace Business\Order;

use Business\Admin\ModuleConfig;
use Business\AgreementTicket\Storage;
use Business\Authority\AuthContext;
use Business\Base;
use Business\EnterLand\EnterLand;
use Business\InfrastructureApi\OpenTelemetry\Counter as CounterApi;
use Business\JavaApi\Order\OrderDetailUpdate;
use Business\JavaApi\Order\OrderTicketDiscounts;
use Business\JavaApi\Order\Query\OrderDetailQuery;
use Business\JsonRpcApi\ScenicLocalService\TimeCard;
use Business\Order\ProductService\ServiceObject;
use Business\Order\Refund\HandleTourist;
use Business\Order\Refund\PackageChildOrderCancel;
use Business\Order\Refund\QConfigSwitchController;
use Business\Order\Refund\RefundParamsCheck;
use Business\Order\Refund\RefundRedisManage;
use Business\Order\RefundApprovalCenterService\RefundApprovalService;
use Business\PackTicket\PackConst;
use Business\Pay\PayBase;
use Business\Product\Ticket as TicketBiz;
use Exception;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Constants\Order\OrderChannel;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Constants\Order\OrderStatus\CommonOrderTouristStatus;
use Library\Constants\OrderConst;
use Library\Hashids\OrderHashids;
use Library\Kafka\KafkaProducer;
use Library\RateLimit\ConnectionPoolRateLimiter;
use Library\Resque\Queue;
use Library\Tools\Helpers;
use Library\Util\QConfUtil;
use Model\Member\Member;
use Model\Order\OrderHandler;
use Model\Order\OrderTools;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery;
use Model\Order\TerminalDbHandler;
use Model\Product\Presuming;
use Model\TradeRecord\OrderRefund;

class BaseRefund extends Base
{
    //套票异常流程记录
    use \Business\PackTicket\Traits\PackageAbnormalOrderTrait;
    //是否使用JAVA版本接口
    private $_isUseJavaVersion = false;
    const OP_VERIFICATION = 1;//核销
    const OP_PART_REVOCATION = 2;//撤改
    const OP_REVOCATION = 3;//撤销
    const OP_REFUND = 4;//取消
    const OP_VERIFICATION_PACKET_TERMINAL = 5;//分终端核销

    const DISCOUNT_STATUS_ALL  = 0;//未使用
    const DISCOUNT_STATUS_UNUSED  = 1;//未使用
    const DISCOUNT_STATUS_USED  = 2;//已使用
    const DISCOUNT_STATUS_USING  = 3;//使用中
    const DISCOUNT_STATUS_REFUND  = 4;//已取消

    public function __construct($isUseJavaVersion = false)
    {
        $this->_isUseJavaVersion = $isUseJavaVersion;
    }

    /**
     * 通用的退票接口，里面包含了取消第三方系统订单的逻辑
     * <AUTHOR>
     * @date 2019-06-23
     *
     * @param  string  $ordernum  订单号
     * @param  string  $cancelNum  取消票数，-1=取消剩余票数
     * @param  string  $opId  执行取消的用户ID
     * @param  string  $cancelChannel  取消渠道
     * @param  string  $cancelType  取消类型：common=正常取消，revoke=终端撤改
     * @param  string  $reqSerialNumber  取消流水号
     * @param  array  $cancelRemarkArr  取消备注信息数组
     *              {
     *                  'remark' : '订单取消的备注信息',
     *              }
     * @param  array  $cancelSiteArr  取消网点数组
     *              {
     *                  'terminal' : '取消终端号',
     *              }
     * @param  array  $cancelPersonArr  取消游客数据数组
     *              {
     *                  'person_index' ： '1', //取消第几个游客的数据
     *                  'person_id_list' : [], //取消游客身份证
     *                  'ticket_code_list':[],  //门票码取消
     *              }
     * @param  array  $cancelSpecialArr  取消的特殊数组
     *              {
     *                  'is_need_audit' : true, // 是否需要进行退票审核判断
     *                  'auto_cancel_fee' : 10, 供应商主动取消的手续费费率百分之几(0.00% - 100.00%)
     *                  'cancel_time' : '2019-06-24 01:22:23', //取消时间，如果有进行异步取消的时候
     *                  'is_cancel_sub' : true, //套票取消的时候，是否主动取消子票，默认是都会取消的; 过期套票过期取消的时候就不需要主动去取消子票
     *                  'is_force_cancel' ： false, //是否强制退票，强制退票的情况下，有些逻辑不需要判断
     *                  'is_cancel_third_system' ： true, //是否往三方系统取消订单，特殊情况下使用
     *                  'is_cancel_notice_third':true,   //是否取消通知三方系统  在三方系统下单失败后不需要通知
     *                  'is_audit_pass'  : false,      //是否是审核通过的取消
     *                  'is_allow_more_revoke':false,       //是否支持多次撤改
     *                  'cancel_audit_remark': '' //提交退票审核的备注
     *                  'son_idCard_and_onlyNum': true //套票主票退票，子票根据主票有的证件类型退绑定票+剩余票按数量退
     *              }
     *
     * @return array
     *
     * 退票成功的情况
     * {
     *     'code' : 200, //200=退票成功，1095=退票审核申请成功
     *     'msg'  : '退票成功',
     *     'data' : { //退票成功的情况
     *         'pft_serial_number' : '平台退款流水号',
     *         'req_serial_number' : '请求退款流水号',
     *         'order_status' : '退票后订单的状态',
     *         'cancel_num' : '退票数量',
     *         'left_num' : '剩余的可用票数',
     *         'valid_num' : '剩余的有效票数',
     *         'valid_money' : '剩余的有效金额',
     *         'track_id' : '退票追踪ID',
     *         'refund_amount' : 'int 末级退款金额 - 单位分',
     *         'refund_fee' : 'int 末级退款手续费 - 单位分',
     *     },
     *     'data' : { //退票审核申请成功的情况
     *         'pft_serial_number' : '平台退款流水号',
     *         'req_serial_number' : '请求退款流水号',
     *         'cancel_num' : '退票数量',
     *         'left_num' : '剩余的可用票数',
     *         'valid_num' : '剩余的有效票数',
     *         'valid_money' : '剩余的有效金额',
     *         'is_system_audit' : false, //是否系统统一的演出退票
     *     },
     * }
     *
     * 退票失败
     * @return {
     *    'code' : 0,
     *    'msg' : '退票失败',
     *    'data' : [
     *        'err_code' => 119,
     *        'err_msg'  => '订单不是未使用状态，不允许退款'
     *    ],
     * }
     */
    public function commonRefund($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber = '',
        $cancelRemarkArr = [], $cancelSiteArr = [], $cancelPersonArr = [], $cancelSpecialArr = [], $moreData = [])
    {
        $openTelemetryCounterSwitch = (new QConfUtil())->getQConf('/php/platform/open_telemetry_counter_switch');
        if ($openTelemetryCounterSwitch['common_refund_request_count']) {
            (new CounterApi())->simpleCounterAdd('common_refund_request_count');
        }

        $isRollback = isset($cancelSpecialArr['special']['is_rollback']) ? boolval($cancelSpecialArr['special']['is_rollback']) : false;
        // 连接池限流， 回滚和退码费取消
        if (!$isRollback && (new QConfUtil())->abTest('/php/platform/common_refund_rate_abtest' . (defined('IS_PFT_GRAY') ? '_gray' : ''))) {
            $rateLimitConf = (new QConfUtil())->getQConf('/php/platform/common_refund_rate_limit' . (defined('IS_PFT_GRAY') ? '_gray' : ''));
            $maxConnections = $rateLimitConf['max_connections'] ?? 50;
            $timeout = $rateLimitConf['timeout'] ?? 60;
            $poolKey = 'common_refund:' . \Library\Tools\Helpers::getServerIp();
            $limiter = new ConnectionPoolRateLimiter($poolKey, $maxConnections, $timeout);
            $connectionId = $limiter->acquireConnection();
            if (!$connectionId) {
                Helpers::sendGroupRobotTextMessages(sprintf('common_refund 连接数超出 %d，订单[%s]退票失败', $maxConnections, $ordernum), 'old_service');
                $isFail = true;
            }
            pft_log('order_refund/debug', json_encode([
                'name'               => 'rate_log',
                'connection_id'      => $connectionId,
                'connections'        => $limiter->length(),
                'pool_key'           => $poolKey,
                'is_fail'            => isset($isFail),
            ]));
        }

        $startTime = microtime(true);

        if (isset($isFail)) {
            $res = $this->orderReturn(500, '系统繁忙，请稍后再试', ['err_code' => 500, 'err_msg' => '系统繁忙，请稍后再试']);
        } else {
            $res = $this->_productLineDispatch($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);
        }

        $endTime  = microtime(true);
        $costTime = $endTime - $startTime;

        // 超时告警
        $warnTime = (new QConfUtil())->getQConf('/php/platform/common_refund_warn_time')['warn_time'] ?? 15;
        if ($costTime > $warnTime) {
            Helpers::sendGroupRobotTextMessages(sprintf('common_refund 耗时超过%ds，订单[%s]', $warnTime, $ordernum), 'old_service');
        }

        // 释放连接
        if (isset($limiter) && !empty($connectionId)) {
            $limiter->releaseConnection($connectionId);
        }

        if ($res['code'] != 200 && $openTelemetryCounterSwitch['common_refund_error_request_count']) {
            (new CounterApi())->simpleCounterAdd('common_refund_error_request_count');
        }

        return $res;
    }

    /**
     * 产品线内部支付接口调度
     * <AUTHOR>
     * @date 2023/11/27
     *
     * @param $ordernum
     * @param $cancelNum
     * @param $opId
     * @param $cancelChannel
     * @param $cancelType
     * @param $reqSerialNumber
     * @param $cancelRemarkArr
     * @param $cancelSiteArr
     * @param $cancelPersonArr
     * @param $cancelSpecialArr
     * @param $moreData
     *
     * @return array
     * @throws \Library\Lock\LockException
     */
    private function _productLineDispatch($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber = '',
        $cancelRemarkArr = [], $cancelSiteArr = [], $cancelPersonArr = [], $cancelSpecialArr = [], $moreData = [])
    {

        $refundParamArr = [
            'ordernum'         => $ordernum,
            'cancelNum'        => $cancelNum,
            'opId'             => $opId,
            'cancelChannel'    => $cancelChannel,
            'cancelType'       => $cancelType,
            'reqSerialNumber'  => $reqSerialNumber,
            'cancelRemarkArr'  => $cancelRemarkArr,
            'cancelSiteArr'    => $cancelSiteArr,
            'cancelPersonArr'  => $cancelPersonArr,
            'cancelSpecialArr' => $cancelSpecialArr,
            'moreData'         => $moreData,
        ];

        //通过订单号获取产品/供应商等信息
//        $lib      = new \Business\JavaApi\Order\Query\UusOrder();
//        $queryRes = $lib->queryOrderInfoByOrdernum($ordernum);

        // 获取订单详情，换一个接口才能区分是否购物车订单
        $orderDetailQuery = new OrderDetailQuery();
        $orderDetailArrRes = $orderDetailQuery->getOrderWithDetail([$ordernum]);

        $pType = $orderDetailArrRes['data'][0]['productType'] ?? '';

        // 根据产品类型对退票频率限制
        $rateConf = (new QConfUtil())->getQConf('/php/platform/common_refund_product_line_rate_limit' . (defined('IS_PFT_GRAY') ? '_gray' : ''));
        if (!empty($rateConf[$pType])) {
            pft_log('order_refund/debug', json_encode(['name' => 'common_refund_product_line_rate_limit', 'now' => time(), 'p_type' => $pType, 'config' => $rateConf[$pType]]));
            $rate = $rateConf[$pType]['rate'] ?? 5;
            $capacity = $rateConf[$pType]['capacity'] ?? 5;
            $limiter = new \Library\RateLimit\RedisTokenBucket('order_refund:product_line_dispatch:' . $pType, $rate, $capacity);
            if ($limiter->isLimit()) {
                Helpers::sendGroupRobotTextMessages(sprintf('退票请求过于频繁，订单[%s]，产品类型[%s]', $ordernum, $pType), 'old_service');
                return $this->orderReturn(0, '退票请求过于频繁，请稍后再试', ['err_code' => OrderConst::err11]);
            }
        }

        //服务对象
        $refundCategory = 'normalRefund';
        $serviceObject  = null;

        if ($orderDetailArrRes['code'] == 200) {
            //获取到了订单数据
            $orderInfo = $orderDetailArrRes['data'][0];

            $applyDid  = $orderInfo['applyDid'];
            $lid       = $orderInfo['lid'];
            $pType     = $orderInfo['productType'];
            $ordermode = $orderInfo['ordermode'];
            $payStatus = $orderInfo['payStatus'];

            //这个版本只处理未支付的订单，已经支付的还走原来的逻辑
            if ($payStatus == 2) {
                $isPackageCancelSub = $cancelSpecialArr['is_package_sub'] ? boolval($cancelSpecialArr['is_package_sub']) : false;
                $isRevokeCancel     = $cancelType == 'revoke' ? true : false;

                if ($isPackageCancelSub) {
                    $refundCategory = 'rollbackPackageRefund';
                } elseif ($isRevokeCancel) {
                    $refundCategory = 'revokeRefund';
                }

                //退票审核切换的逻辑在 \Business\Order\RefundAudit::baseAgreeAudit 进行切换
                //所以在这边统一入口的位置碰到退票审核退的逻辑统一走旧逻辑
                if ($cancelChannel == OrderConst::VERIFY_CANCEL) {
                    $serviceObject = null;
                } else {
                    $serviceObject = ServiceObject::getRefundServiceObject($pType, $cancelChannel, $refundCategory,
                        $lid,
                        $applyDid, $orderInfo, $refundParamArr);
                }
            } else {
                // 已支付 & 购物车产生的订单 -> 调用购物车退单
                // fixme: payStatus !=2 除购物车外的退单的逻辑，都返回为null，暂时先走旧逻辑
                $serviceObject = ServiceObject::getRefundServiceObject(
                    $pType, $cancelChannel, $refundCategory, $lid,
                    $applyDid, $orderInfo, $refundParamArr
                );
            }
        }

        if ($serviceObject) {
            //走具体的产品线逻辑

            //暂时在这边进行接口分流，后续在前端入口那边就请求不同的接口了
            if ($refundCategory == 'revokeRefund') {
                //撤改
                $res = $serviceObject->revokeRefund($refundParamArr);
            } elseif ($refundCategory == 'rollbackPackageRefund') {
                //回滚退(套票子票回滚退）
                $res = $serviceObject->rollbackPackageRefund($refundParamArr);
            } else {
                //其他的都走常规退
                $res = $serviceObject->normalRefund($refundParamArr);
            }
            //这边要用统一的orderReturn方法处理下
            return $this->orderReturn($res['code'], $res['msg'], $res['data']);
        } else {
            //原先的逻辑
            $res = $this->_oldCommonRefundHandle($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber,
                $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);

            return $res;
        }

    }

    /**
     * 原先各个产品线的退票逻辑
     * <AUTHOR>
     * @date 2023/11/27
     *
     * @param $ordernum
     * @param $cancelNum
     * @param $opId
     * @param $cancelChannel
     * @param $cancelType
     * @param $reqSerialNumber
     * @param $cancelRemarkArr
     * @param $cancelSiteArr
     * @param $cancelPersonArr
     * @param $cancelSpecialArr
     * @param $moreData
     *
     * @return array
     * @throws \Library\Lock\LockException
     */
    private function _oldCommonRefundHandle($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber = '',
        $cancelRemarkArr = [], $cancelSiteArr = [], $cancelPersonArr = [], $cancelSpecialArr = [], $moreData = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return self::_oldCommonRefundHandleForApproval($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber,
                $cancelRemarkArr , $cancelSiteArr , $cancelPersonArr, $cancelSpecialArr, $moreData);
        }

        $lockObj = \Library\Lock\LockFactory::factory('redis');
        try {
            $lockKey = "lock:cancel:{$ordernum}";
            //锁两秒
            $lockObj->acquire($lockKey, 30, 30);
            $checkBiz    = new BaseRefundCheck();
            $checkBizNew = new RefundParamsCheck();
            $baseCheckRes = $checkBizNew->baseCheck($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            pft_log('order_refund/debug', json_encode(['baseCheckRes', $baseCheckRes, $ordernum]));
            //基础校验错误返回
            if ($baseCheckRes['code'] != 200) {
                return $baseCheckRes;
            }

            //退款参数
            $refundParams = $baseCheckRes['data'];
            $refundParams['usedRefundFix'] = true;
            $ordernum     = $refundParams['ordernum'];
            if ($moreData['subSid'] && $moreData['subOpId']) {
                $refundParams['subSid']  = $moreData['subSid'];
                $refundParams['subOpId'] = $moreData['subOpId'];
            }
            //获取订单信息
            $orderModel   = new OrderTools('localhost');
            $tmpOrderInfo = $orderModel->getInfoForCancel($ordernum);
            if (!$tmpOrderInfo) {
                return $this->orderReturn(0, "订单数据不存在", ['err_code' => OrderConst::err11]);
            }

            //如果是已经支付的套票的主票，sub_list返回子票的取票信息
            $orderInfo    = $tmpOrderInfo['order_info'];
            if ($orderInfo['product_type'] == 'K') {
                $timeCardJsonRpc = new TimeCard();
                $timeCheckRes    = $timeCardJsonRpc->checkTimeOrderCanRefund([$ordernum]);
                if ($timeCheckRes['code'] != 200) {
                    return $this->orderReturn($timeCheckRes['code'], $timeCheckRes['msg'],  ['err_code' => $timeCheckRes['code']]);
                }
            }

            $subOrderList = $tmpOrderInfo['sub_list'];
            //根据远端退票流水号，看是否存在退票记录
            $reqCheckRes = $this->_checkRequestRecord($ordernum, $reqSerialNumber, $refundParams, $orderInfo);
            if ($reqCheckRes['code'] != 200) {
                if (in_array($reqCheckRes['code'], [101])) {
                    if (isset($refundParams['afterSaleNum']) && !empty($refundParams['afterSaleNum'])) {
                        $dataKafka = [
                            'op_id'             => $refundParams['opId'],
                            'refund_status'     => 1,// 0审核中 1退票成功  2失败
                            'after_sale_num'    => $refundParams['afterSaleNum'],
                            'req_serial_number' => '', // 售后要求不要传，因为这个流水号根本就没有保存，传过去是错的
                            'order_num' => $ordernum,
                            'cancel_type' =>$refundParams['cancelType'],
                            'refund_idx' => empty($refundParams['personIndex']) ? ["1"] : strval($refundParams['personIndex']),
                            'order_status' => $reqCheckRes['data']['order_status'] ?? '',
                            'cancel_num' =>  $cancelNum,
                            'left_num' => $reqCheckRes['data']['left_num']??'',
                            'is_retry' => $refundParams['isRetry'] ?? false,
                            'msg' => '退票成功'
                        ];
                        pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka]));
                        \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic',
                            $dataKafka, strval($dataKafka['order_num']));
                        if (isset($refundParams['batchRefundMoreIdx']) && !empty($refundParams['batchRefundMoreIdx'])) {
                            self::batchMoreRefund($ordernum, $refundParams,
                                ['refund_idx' => $refundParams['personIndex'], 'op_id' => $refundParams['opId']]);
                        }
                    }

                    return $this->orderReturn(200, $reqCheckRes['msg'], $reqCheckRes['data']);
                }

                //已经有退票记录，而且不需要继续进行退票，直接返回退票的结果
                return $this->orderReturn(0, $reqCheckRes['msg'], $reqCheckRes['data']);

            } else {
                //需要继续进行退票处理
                $refundParams['cancelNum']      = isset($reqCheckRes['data']['change_num']) ? $reqCheckRes['data']['change_num'] : $refundParams['cancelNum'];
                $refundParams['personIdList']   = isset($reqCheckRes['data']['tourist_info']) ? $reqCheckRes['data']['tourist_info'] : $refundParams['personIdList'];
                $refundParams['ticketCodeList'] = isset($reqCheckRes['data']['ticket_code_list']) ? $reqCheckRes['data']['ticket_code_list'] : $refundParams['ticketCodeList'];
                $refundParams['personIndex']    = isset($reqCheckRes['data']['personIndex']) ? $reqCheckRes['data']['personIndex'] : $refundParams['personIndex'];
                $refundParams['personInfoList'] = isset($reqCheckRes['data']['personInfoList']) ? $reqCheckRes['data']['personInfoList'] : $refundParams['personInfoList'];
                //如果是直接传入-1，直接取消订单，转换下取消的票数
                if ($refundParams['cancelNum'] === -1) {
                    $allNum       = intval($orderInfo['tnum']);
                    $verifiedNum  = intval($orderInfo['verified_num']);
                    $canRefundNum = intval($orderInfo['can_refund']);
                    if ($cancelType == 'revoke') {
                        $refundParams['cancelNum'] = $verifiedNum;
                    } else {
                        //todo  这边是多次入园新旧版的区分 新版多次入园验证数量不会+1  旧版验证数量会+1
                        $refundParams['cancelNum'] = $canRefundNum != -1 ? $canRefundNum : $allNum - $verifiedNum;
                    }
                }
                if ($cancelType == 'common') {
                    $orderCanRefundNum = $orderInfo['can_refund'] == -1 ? $orderInfo['origin_num'] - $orderInfo['verified_num'] - $orderInfo['refund_num'] : $orderInfo['can_refund'];
                    if ($orderCanRefundNum < $refundParams['cancelNum']) {
                        return $this->orderReturn(0, "可退数量不足", ['err_code' => OrderConst::err11]);
                    }
                }

                //重新判断下取消的数量
                if ($refundParams['cancelNum'] <= 0) {
                    return $this->orderReturn(0, "退票数量错误", ['err_code' => OrderConst::err11]);
                }

                //门票数据
                $ticketBiz = new TicketBiz();
                $tid       = $orderInfo['tid'];
                $ticketRes = $ticketBiz->getListForOrderNew($tid);
                if (!$ticketRes['ticket_info']) {
                    return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
                }
//                //退票规则 - 更多配置 - 需要订单范围
                $judgeRes = self::_judgeRefundAuditSetting($ticketRes, $orderInfo);
                if ($judgeRes['is_change_setting']) {
                    $refundParams['isNeedAudit'] = $judgeRes['is_need_audit'];
                }
                if ($orderInfo['ext_content']) {
                    $extContent = json_decode($orderInfo['ext_content'], true);
                } else {
                    $extContent = [];
                }
                //是否启用分时退票规则 && 符合分时退票规则
                if(isset($extContent['sectionTimeId'])){
                    pft_log('order_refund/canTicketSectionReturn',json_encode(['canTicketSectionReturn',$orderInfo,$extContent],JSON_UNESCAPED_UNICODE));
                    $isCanTicketSection = \Business\JavaApi\TicketApi::canTicketSectionReturn($ordernum,$extContent['sectionTimeId'], $orderInfo['tid']);
                    if(!$isCanTicketSection) {
                        return $this->orderReturn(0, "票_{$orderInfo['tid']}_不符合分时退票规则", ['err_code' => OrderConst::err1413]);
                    }
                }
                $version = $extContent['ticketVersion'] ?? 0;

                // 走订单票属性快照
                $ticketSnapshot = (new \Business\Product\Ticket())->getTicketSnapShotByCache($orderInfo['tid'],
                    $version);
                if (!$ticketSnapshot) {
                    return $this->orderReturn(0, '门票快照数据不存在', ['err_code' => OrderConst::err1054]);
                }
                $refundParams['ticketSnapShot'] = $ticketSnapshot;

                $tmpTicketInfo = $ticketRes['ticket_info'];
                $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
                $landInfo      = $ticketRes['land_info'] ?: [];
                $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);

                //景区的名称/二级类型也加入到$ticketInfo中去
                $ticketInfo['land_title'] = $landInfo['title'];
                $ticketInfo['sub_type']   = $landInfo['sub_type'];

                //获取取消操作用户信息
                $tmpMemberInfo = $this->_getOpInfo($opId);
                if ($tmpMemberInfo['code'] != 200) {
                    return $tmpMemberInfo;
                }
                $specialCarriage = 0;   //特产的运费
                if ($ticketInfo['p_type'] == 'J') {
                    $productExt = json_decode($orderInfo['product_ext'], true);
                    if (isset($productExt['carriage'])) {
                        $refundCarriage  = isset($productExt['refundCarriage']) ? $productExt['refundCarriage'] : 0;
                        $specialCarriage = $productExt['carriage'] - $refundCarriage;
                    }
                }
                //获取特产的运费  如果审核后在进来取审核时候的值
                $refundParams['carriage'] = isset($reqCheckRes['data']['carriage']) ? $reqCheckRes['data']['carriage'] : $specialCarriage;
                $refundParams['oneTouristOneTicket'] = $extContent['one_tourist_one_ticket'] ?? 0; //是否是新一人一票，如果是1，后面需要置换print_mode
                //取消用户的数据
                $memberInfo = $tmpMemberInfo['data'];
                if ($orderInfo['pay_status'] == 2) {
                    //订单未支付
                    $refundRes = $this->_nopayRefund($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList,
                        $refundParams);

                    if ($refundRes['code'] == 0) {
                        //退款失败，直接返回
                        return $refundRes;

                    } else {
                        //退票成功返回数据
                        $refundData = $refundRes['data'];

                        //添加其他的异步任务
                        $this->_addRefundSuccessTask($ordernum, false, $orderInfo, $ticketInfo, $refundData,
                            $refundParams);

                        return $this->orderReturn(200, '退票成功', $refundData);
                    }
                } else {
                    //订单已经支付
                    $refundRes = $this->_payedRefund($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList,
                        $refundParams);

                    if ($refundRes['code'] == 0) {
                        //解除频率控制锁
                        $checkBiz->removeRefundLock($ordernum);

                        //退款失败，直接返回
                        return $refundRes;

                    } else if ($refundRes['code'] == 1095) {
                        //需要进行退票审核
                        $auditInfo = $refundRes['data'];

                        $this->_addRefundAuditTask($ordernum, $orderInfo, $auditInfo, $ticketInfo);

                        //解除频率控制锁
                        $checkBiz->removeRefundLock($ordernum);

                        return $this->orderReturn(1095, '添加退票审核成功', $auditInfo);
                    } else {
                        //退票成功返回数据
                        $refundData      = $refundRes['data'];
                        $pftSerialNumber = $refundData['pft_serial_number'];

                        //是否供应商开启酒店预定确定拒绝的订单
                        $specialData = [];
                        if ($refundParams['isScheduledRefuseBack']) {
                            $specialData['hotel_scheduled_refuse'] = true;
                        }

                        //如果是景区产品线退票的订单，在退款的时候做特殊处理
                        if ($refundParams['isScenicLineRefund']) {
                            $specialData['isScenicLineRefund'] = $refundParams['isScenicLineRefund'];
                        }

                        //平台退款
                        $refundMoneyRes = $this->_handlerMoneyTask($ordernum, $pftSerialNumber, $specialData);
                        if ($refundMoneyRes['code'] == 200) {
                            $refundData['refund_amount'] = $refundMoneyRes['data']['refund_amount'];
                            $refundData['refund_fee']    = $refundMoneyRes['data']['refund_fee'];
                        }

                        //添加其他的异步任务
                        $this->_addRefundSuccessTask($ordernum, true, $orderInfo, $ticketInfo, $refundData,
                            $refundParams);

                        //解除频率控制锁
                        $checkBiz->removeRefundLock($ordernum);

                        return $this->orderReturn(200, '退票成功', $refundData);
                    }
                }
            }
        } catch (Exception $e) {
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            //解除频率控制锁
            $checkBiz->removeRefundLock($ordernum);

            return $this->orderReturn(0, '系统异常，请稍后再试',
                ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        } finally {
            $lockObj->release($lockKey);
        }
    }

    /**
     * 原先各个产品线的退票逻辑
     * <AUTHOR>
     * @date 2023/11/27
     *
     * @param $ordernum
     * @param $cancelNum
     * @param $opId
     * @param $cancelChannel
     * @param $cancelType
     * @param $reqSerialNumber
     * @param $cancelRemarkArr
     * @param $cancelSiteArr
     * @param $cancelPersonArr
     * @param $cancelSpecialArr
     * @param $moreData
     *
     * @return array
     * @throws \Library\Lock\LockException
     */
    private function _oldCommonRefundHandleForApproval($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType, $reqSerialNumber = '',
                                            $cancelRemarkArr = [], $cancelSiteArr = [], $cancelPersonArr = [], $cancelSpecialArr = [], $moreData = [])
    {
        $lockObj = \Library\Lock\LockFactory::factory('redis');
        try {
            $lockKey = "lock:cancel:{$ordernum}";
            //锁两秒
            $lockObj->acquire($lockKey, 30, 30);
            $checkBiz     = new BaseRefundCheck();
            $isCanRefundMoreTicketCode = true;
            $baseCheckRes = $checkBiz->baseCheck($ordernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $isCanRefundMoreTicketCode);
            //基础校验错误返回
            if ($baseCheckRes['code'] != 200) {
                return $baseCheckRes;
            }

            //退款参数
            $refundParams = $baseCheckRes['data'];
            $ordernum     = $refundParams['ordernum'];
            if ($moreData['subSid'] && $moreData['subOpId']) {
                $refundParams['subSid']  = $moreData['subSid'];
                $refundParams['subOpId'] = $moreData['subOpId'];
            }
            if (isset($moreData['isMainRefund'])){
                $refundParams['isMainRefund'] = $moreData['isMainRefund'];
            }
            if (isset($moreData['isApprovalFund'])){
                $refundParams['isApprovalFund'] = $moreData['isApprovalFund'];
            }
            if (isset($moreData['operatorID'])){
                $refundParams['operatorID'] = $moreData['operatorID'];
            }
            if (isset($moreData['approvalComment'])){
                $refundParams['approvalComment'] = $moreData['operatorID'];
            }
            if (isset($moreData['processInstanceId'])){
                $refundParams['processInstanceId'] = $moreData['processInstanceId'];
            }
            if (isset($moreData['mainApprovalCode'])){
                $refundParams['mainApprovalCode'] = $moreData['mainApprovalCode'];
            }
            if (isset($moreData['isThirdAuditRes'])){
                $refundParams['isThirdAuditRes'] = $moreData['isThirdAuditRes'];
            }
            //获取订单信息
            $orderModel   = new OrderTools('localhost');
            $tmpOrderInfo = $orderModel->getInfoForCancel($ordernum);
            if (!$tmpOrderInfo) {
                return $this->orderReturn(0, "订单数据不存在", ['err_code' => OrderConst::err11]);
            }

            //如果是已经支付的套票的主票，sub_list返回子票的取票信息
            $orderInfo    = $tmpOrderInfo['order_info'];
            $subOrderList = $tmpOrderInfo['sub_list'];
            //根据远端退票流水号，看是否存在退票记录
            $reqCheckRes = $this->_checkRequestRecord($ordernum, $reqSerialNumber, $refundParams, $orderInfo);
            if ($reqCheckRes['code'] != 200) {
                if (in_array($reqCheckRes['code'], [101])) {
                    if (isset($refundParams['afterSaleNum']) && !empty($refundParams['afterSaleNum'])) {
                        $dataKafka = [
                            'op_id'             => $refundParams['opId'],
                            'refund_status'     => 1,// 0审核中 1退票成功  2失败
                            'after_sale_num'    => $refundParams['afterSaleNum'],
                            'req_serial_number' => '', // 售后要求不要传，因为这个流水号根本就没有保存，传过去是错的
                            'order_num' => $ordernum,
                            'cancel_type' =>$refundParams['cancelType'],
                            'refund_idx' => empty($refundParams['personIndex']) ? ["1"] : strval($refundParams['personIndex']),
                            'order_status' => $reqCheckRes['data']['order_status'] ?? '',
                            'cancel_num' =>  $cancelNum,
                            'left_num' => $reqCheckRes['data']['left_num']??'',
                            'is_retry' => $refundParams['isRetry'] ?? false,
                            'msg' => '退票成功'
                        ];
                        pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka]));
                        \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic',
                            $dataKafka, strval($dataKafka['order_num']));
                        if (isset($refundParams['batchRefundMoreIdx']) && !empty($refundParams['batchRefundMoreIdx'])) {
                            self::batchMoreRefund($ordernum, $refundParams,
                                ['refund_idx' => $refundParams['personIndex'], 'op_id' => $refundParams['opId']]);
                        }
                    }

                    return $this->orderReturn(200, $reqCheckRes['msg'], $reqCheckRes['data']);
                }

                //已经有退票记录，而且不需要继续进行退票，直接返回退票的结果
                return $this->orderReturn(0, $reqCheckRes['msg'], $reqCheckRes['data']);

            } else {
                //需要继续进行退票处理
                $refundParams['cancelNum']      = isset($reqCheckRes['data']['change_num']) ? $reqCheckRes['data']['change_num'] : $refundParams['cancelNum'];
                $refundParams['personIdList']   = isset($reqCheckRes['data']['tourist_info']) ? $reqCheckRes['data']['tourist_info'] : $refundParams['personIdList'];
                $refundParams['ticketCodeList'] = isset($reqCheckRes['data']['ticket_code_list']) ? $reqCheckRes['data']['ticket_code_list'] : $refundParams['ticketCodeList'];
                $refundParams['personIndex']    = isset($reqCheckRes['data']['personIndex']) ? $reqCheckRes['data']['personIndex'] : $refundParams['personIndex'];
                $refundParams['personInfoList']    = isset($reqCheckRes['data']['personInfoList']) ? $reqCheckRes['data']['personInfoList'] : $refundParams['personInfoList'];
                //如果是直接传入-1，直接取消订单，转换下取消的票数
                if ($refundParams['cancelNum'] === -1) {
                    $allNum       = intval($orderInfo['tnum']);
                    $verifiedNum  = intval($orderInfo['verified_num']);
                    $canRefundNum = intval($orderInfo['can_refund']);
                    if ($cancelType == 'revoke') {
                        $refundParams['cancelNum'] = $verifiedNum;
                    } else {
                        //todo  这边是多次入园新旧版的区分 新版多次入园验证数量不会+1  旧版验证数量会+1
                        $refundParams['cancelNum'] = $canRefundNum != -1 ? $canRefundNum : $allNum - $verifiedNum;
                    }
                }
                if ($cancelType == 'common') {
                    $orderCanRefundNum = $orderInfo['can_refund'] == -1 ? $orderInfo['origin_num'] - $orderInfo['verified_num'] - $orderInfo['refund_num'] : $orderInfo['can_refund'];
                    if ($orderCanRefundNum < $refundParams['cancelNum']) {
                        return $this->orderReturn(0, "可退数量不足", ['err_code' => OrderConst::err11]);
                    }
                }

                //重新判断下取消的数量
                if ($refundParams['cancelNum'] <= 0) {
                    return $this->orderReturn(0, "退票数量错误", ['err_code' => OrderConst::err11]);
                }

                //门票数据
                $ticketBiz = new TicketBiz();
                $tid       = $orderInfo['tid'];
                $ticketRes = $ticketBiz->getListForOrderNew($tid);
                if (!$ticketRes['ticket_info']) {
                    return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
                }
//                //退票规则 - 更多配置 - 需要订单范围
                $judgeRes = self::_judgeRefundAuditSetting($ticketRes, $orderInfo);
                if ($judgeRes['is_change_setting']) {
                    $refundParams['isNeedAudit'] = $judgeRes['is_need_audit'];
                }
                if ($orderInfo['ext_content']) {
                    $extContent = json_decode($orderInfo['ext_content'], true);
                } else {
                    $extContent = [];
                }

                $version = $extContent['ticketVersion'] ?? 0;

                // 走订单票属性快照
                $ticketSnapshot = (new \Business\Product\Ticket())->getTicketSnapShotByCache($orderInfo['tid'],
                    $version);
                if (!$ticketSnapshot) {
                    return $this->orderReturn(0, '门票快照数据不存在', ['err_code' => OrderConst::err1054]);
                }
                $refundParams['ticketSnapShot'] = $ticketSnapshot;

                $tmpTicketInfo = $ticketRes['ticket_info'];
                $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
                $landInfo      = $ticketRes['land_info'] ?: [];
                $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);

                //景区的名称也加入到$ticketInfo中去
                $ticketInfo['land_title'] = $landInfo['title'];

                //获取取消操作用户信息
                $tmpMemberInfo = $this->_getOpInfo($opId);
                if ($tmpMemberInfo['code'] != 200) {
                    return $tmpMemberInfo;
                }
                $specialCarriage = 0;   //特产的运费
                if ($ticketInfo['p_type'] == 'J') {
                    $productExt = json_decode($orderInfo['product_ext'], true);
                    if (isset($productExt['carriage'])) {
                        $refundCarriage  = isset($productExt['refundCarriage']) ? $productExt['refundCarriage'] : 0;
                        $specialCarriage = $productExt['carriage'] - $refundCarriage;
                    }
                }
                //获取特产的运费  如果审核后在进来取审核时候的值
                $refundParams['carriage'] = isset($reqCheckRes['data']['carriage']) ? $reqCheckRes['data']['carriage'] : $specialCarriage;
                //取消用户的数据
                $memberInfo = $tmpMemberInfo['data'];

                if ($orderInfo['pay_status'] == 2) {
                    //订单未支付
                    $refundRes = $this->_nopayRefund($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList,
                        $refundParams);

                    if ($refundRes['code'] == 0) {
                        //退款失败，直接返回
                        return $refundRes;

                    } else {
                        //退票成功返回数据
                        $refundData = $refundRes['data'];

                        //添加其他的异步任务
                        $this->_addRefundSuccessTask($ordernum, false, $orderInfo, $ticketInfo, $refundData,
                            $refundParams);

                        return $this->orderReturn(200, '退票成功', $refundData);
                    }
                } else {
                    if(!$refundParams['isNeedAudit'] && $refundParams['isAuditPass']){
                        $refundApprovalService = new RefundApprovalService();
                        $objectParams['reqSerialNumber'] = $refundParams['reqSerialNumber'];
                        $objectParams['orderNum'] = $ordernum;
                        list($skipRes,$processInstanceId,$des) = $refundApprovalService->skipApprovalBeforeAudit($objectParams,$opId,$orderInfo['apply_did']);
                        if(!$skipRes){
                            return $this->orderReturn(271, $des, $processInstanceId);
                        }
                    }


                    //订单已经支付
                    $refundRes = $this->_payedRefund($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList,
                        $refundParams);
                    if ($refundRes['code'] == 0) {
                        //解除频率控制锁
                        $checkBiz->removeRefundLock($ordernum);

                        //退款失败，直接返回
                        return $refundRes;

                    } else if ($refundRes['code'] == 1095) {
                        //需要进行退票审核
                        $auditInfo = $refundRes['data'];

                        $this->_addRefundAuditTask($ordernum, $orderInfo, $auditInfo, $ticketInfo);

                        //解除频率控制锁
                        $checkBiz->removeRefundLock($ordernum);

                        return $this->orderReturn(1095, '添加退票审核成功', $auditInfo);
                    } else {
                        //退票成功返回数据
                        $refundData      = $refundRes['data'];
                        $pftSerialNumber = $refundData['pft_serial_number'];
                        //是否供应商开启酒店预定确定拒绝的订单
                        $specialData = [];
                        if ($refundParams['isScheduledRefuseBack']) {
                            $specialData['hotel_scheduled_refuse'] = true;
                        }
                        //平台退款
                        $refundMoneyRes = $this->_handlerMoneyTask($ordernum, $pftSerialNumber, $specialData);
                        if ($refundMoneyRes['code'] == 200) {
                            $refundData['refund_amount'] = $refundMoneyRes['data']['refund_amount'];
                            $refundData['refund_fee']    = $refundMoneyRes['data']['refund_fee'];
                        }

                        //添加其他的异步任务
                        $this->_addRefundSuccessTask($ordernum, true, $orderInfo, $ticketInfo, $refundData,
                            $refundParams);

                        //解除频率控制锁
                        $checkBiz->removeRefundLock($ordernum);

                        return $this->orderReturn(200, '退票成功', $refundData);
                    }
                }
            }
        } catch (Exception $e) {
            $errCode  = $e->getCode();
            $errMsg   = $e->getMessage();
            $innerMsg = $errMsg . "[{$errCode}]";

            //解除频率控制锁
            $checkBiz->removeRefundLock($ordernum);

            return $this->orderReturn(0, '系统异常，请稍后再试',
                ['err_code' => OrderConst::err15, 'inner_msg' => $innerMsg]);
        } finally {
            $lockObj->release($lockKey);
        }
    }

    /**
     * 检测退票记录是否存在，判断是否需要继续进行退票的动作
     *
     * <AUTHOR>
     * @date 2019-07-14
     *
     * @param  string  $ordernum  订单号
     * @param  string  $reqSerialNumber  退票请求流水号
     * @param  array  $refundParams  退票参数
     * @param  array  $orderInfo  订单详情
     *
     * @return array
     */
    private function _checkRequestRecord($ordernum, $reqSerialNumber, $refundParams, $orderInfo)
    {
        $refundInfo = [];
        if ($reqSerialNumber) {
            $refundModel = new OrderRefund();
            $refundInfo  = $refundModel->getRefundJournal($ordernum, $reqSerialNumber);
        }
        $code = 200;
        $msg  = '';
        $data = [];
        if ($refundInfo) {
            if (in_array($refundInfo['refund_status'], [1, 2, 3])) {
                $code = 101;
                $msg  = '已经退票成功';
                $data = [
                    'pft_serial_number' => '',
                    'req_serial_number' => '',
                    'order_status'      => $orderInfo['status'],
                    'cancel_num'        => $refundParams['cancelNum'],
                    'left_num'          => 0,
                    'valid_num'         => 0,
                    'valid_money'       => 0,
                    'track_id'          => 0,
                ];
            } elseif ($refundInfo['refund_status'] == 4) {
                // refund_status = 4;不是数据库备注的拒绝退款，应该是待审核默认状态
                if (!$refundParams['isNeedAudit']) {
                    //审核通过
                    $detailsData        = json_decode($refundInfo['details'], true);
                    $data['change_num'] = $refundInfo['change_num'];
                    if (isset($detailsData['carriage'])){    //审核后的运费，有接口会改这个表里的运费
                        $data['carriage']   = $detailsData['carriage'];
                    }
                    $touristInfo        = isset($detailsData['personIdList']) ? $detailsData['personIdList'] : [];
                    $ticketCodeList     = isset($detailsData['ticketCodeList']) ? $detailsData['ticketCodeList'] : [];
                    $personIndex       = isset($detailsData['personIndex']) ? $detailsData['personIndex'] : 0;
                    $personInfoList       = isset($detailsData['personInfoList']) ? $detailsData['personInfoList'] : 0;
                    if (QConfigSwitchController::getRefundPackageFixUsedWithTenant($ordernum)) {
                        //新版放量退票，按数量退，添加审核记录的时候，personIndex无值，会在refundIdx中有值，可以解决这个bug
                        if (empty($personIndex) && empty($ticketCodeList)){
                            $personIndex = $detailsData['refundIdx'] ?? 0;
                        }
                        if (isset($detailsData['isOnlyCancelNum']) && $detailsData['isOnlyCancelNum']) {
                            $personInfoList = [];
                            $personIndex = [];
                        }
                    }
                    if (!empty($touristInfo)) {
                        $data['tourist_info'] = $touristInfo;
                    }
                    if ($ticketCodeList){
                        $data['ticket_code_list'] = $ticketCodeList;
                    }
                    if ($personIndex){
                        $data['personIndex'] = $personIndex;
                    }
                    if ($personInfoList){
                        $idcardList = array_filter(array_column($personInfoList, 'idcard'));
                        //不相等的时候，是按数量退
                        if (count($idcardList) == count($personInfoList) && empty($personIndex)) {
                            $data['personInfoList'] = $personInfoList;
                        }
                    }
                } else {
                    $code = 0;
                    $msg  = '订单审核中';
                    $data = ['err_code' => OrderConst::err1097];
                }
            } elseif (intval($refundInfo['refund_status']) == 0) {
                //gogogo不处理
            } elseif ($refundInfo['refund_status'] == 5) {
                $code = 0;
                $msg  = '退款被拒绝';
                $data = ['err_code' => OrderConst::err1096];
            } else {
                $code = 0;
                $msg  = '未知状态';
                $data = ['err_code' => OrderConst::err21];
            }
            //存在退票记录
            //因为现在退票审核通过，也不会更新这边的值，所以后面需要进行更新，然后就可以根据这个状态进行判断和返回
            if ($code != 200) {
                return $this->orderReturn($code, $msg, $data);
            }

            return $this->orderReturn(200, '暂时这样处理', $data);
        } else {
            //不存在退票记录

            //这个逻辑暂时先这样去处理
            //如果订单已经取消或是撤销了，直接进行返回
            //正常是不会到这个逻辑，如果订单会是需要的状态，在上面应该都可以通过请求流水号查询到数据，然后返回
            if (in_array($orderInfo['status'], [3, 6])) {
                $resData = [
                    'pft_serial_number' => '',
                    'req_serial_number' => '',
                    'order_status'      => $orderInfo['status'],
                    'cancel_num'        => $refundParams['cancelNum'],
                    'left_num'          => 0,
                    'valid_num'         => 0,
                    'valid_money'       => 0,
                    'track_id'          => 0,
                ];

                return $this->orderReturn(101, '已经退款了', $resData);
            } else {
                return $this->orderReturn(200, '暂时这样处理');
            }
        }
    }

    /**
     * 已经支付的订单退票
     * @param string $ordernum 订单号
     * @param array $orderInfo 订单相关信息
     * @param array $ticketInfo 门票相关信息
     * @param array $memberInfo 取消用户相关信息
     * @param array $subOrderList 套票的子票列表
     * @param array $refundParams 退票请求参数，BaseRefundCheck->baseCheck初步处理参数
     *
     * @return array
     * @throws Exception
     * <AUTHOR>
     * @date 2019-07-06
     *
     */
    private function _payedRefund($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList, $refundParams)
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return self::_payedRefundForApproval($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList, $refundParams);
        }
        //校验过的退票参数
        $cancelNum            = $refundParams['cancelNum'];
        $opId                 = $refundParams['opId'];
        $cancelChannel        = $refundParams['cancelChannel'];
        $cancelType           = $refundParams['cancelType'];
        $reqSerialNumber      = $refundParams['reqSerialNumber'];
        $cancelRemark         = $refundParams['cancelRemark'];
        $cancelTerminal       = $refundParams['cancelTerminal'];
        $personIndex          = $refundParams['personIndex'];
        $personIdList         = $refundParams['personIdList'];
        $isNeedAudit          = $refundParams['isNeedAudit'];
        $isAuditAgreed        = $refundParams['isAuditAgreed'];
        $autoCancelFee        = $refundParams['autoCancelFee'];
        $isCancelSub          = $refundParams['isCancelSub'];
        $isForceCancel        = $refundParams['isForceCancel'];
        $isCancelThirdSystem  = $refundParams['isCancelThirdSystem'];
        $cancelTime           = $refundParams['cancelTime'];
        $realCancelChannel    = $refundParams['realCancelChannel'];
        $isRollBack           = $refundParams['isRollBack'];
        $isSyntaxRollBack     = $refundParams['isSyntaxRollback'] ?? false;
        $carriage             = $refundParams['carriage'];
        $ticketCodeList       = $refundParams['ticketCodeList'];
        $isAuditPass          = $refundParams['isAuditPass'];
        $ticketSnapshot       = $refundParams['ticketSnapShot'];
        $isAllowMoreRevoke    = $refundParams['isAllowMoreRevoke'];
        $isRefundDeposit      = $refundParams['isRefundDeposit'];
        $isRefundOverPay      = $refundParams['isRefundOverPay'];
        $cancelAuditRemark    = $refundParams['cancelAuditRemark']; //发起退票申请时候的备注
        $discount             = $refundParams['discount'];
        $refundOpenCode       = $refundParams['refundOpenCode'];
        $afterSaleNum         = $refundParams['afterSaleNum'];
        $isRetry              = $refundParams['isRetry'];
        $batchRefundMoreIdx   = $refundParams['batchRefundMoreIdx'];
        $isSkipExchangeTicket = $refundParams['isSkipExchangeTicket'] ?? false;
        $refundPersonInfoList = $refundParams['personInfoList'] ?? [];
        $usedRefundFix        = $refundParams['usedRefundFix'] ?? false;
        $oneTouristOneTicket  = $refundParams['oneTouristOneTicket'] ?? 0;
        pft_log('order_refund/debug',json_encode(['payedRefund_refundParams', $ordernum,$cancelNum, $reqSerialNumber, $isNeedAudit, $isAuditPass, $personIndex, $personIdList,$refundPersonInfoList, $subOrderList, $oneTouristOneTicket]));

        //逻辑取消时间处理，如果外部没有传，就取当前的时间
        $realCancelTime = date('Y-m-d H:i:s');
        $cancelTime     = $cancelTime ? $cancelTime : $realCancelTime;
        //退票锁处理
        $checkBiz     = new BaseRefundCheck();
        $lockLeftNum  = $cancelType == 'revoke' ? $orderInfo['verified_num'] : $orderInfo['tnum'];
        $lockCheckRes = $checkBiz->refundLock($ordernum, $reqSerialNumber, $cancelType, $lockLeftNum, $opId,
            $isNeedAudit, $personIndex);
        if($isSkipExchangeTicket){
            $lockCheckRes['code'] = 200;
        }
        if ($lockCheckRes['code'] != 200) {
            $orderTicketDiscounts = new OrderTicketDiscounts();
            $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($discount['serialNumber']);
            $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            return $lockCheckRes;
        }
        $fixObj = new HandleTourist();

        //门票数据
        $tid       = $orderInfo['tid'];
        $payStatus = $orderInfo['pay_status'];
        $ifpack    = $orderInfo['ifpack'];

        //取消用户数据
        $memberType     = $memberInfo['memberType'];
        $cancelMemberId = $memberInfo['cancelMemberId'];
        $isSuper        = $memberInfo['isSuper'];
        $opId           = $memberInfo['opId'];

        if ($usedRefundFix) {
            //调用java api获取游客身份信息;非已过期：未验证  已过期statu=2订单：未验证+已过期
            $leftTouristArr = $fixObj->getOrderLeftTourist($ordernum, $orderInfo);
            $codeList = $fixObj->getOneTicketOneCodeCodeList($ordernum, $leftTouristArr,$ticketInfo['batch_check'],$cancelNum);
            //未使用的身份证信息
            $availableIdcardArr = $fixObj->getIdcardArrWithTouristInfoList($leftTouristArr);
        } else {
            //如果是一票一证的门票，就需要获取未使用的码
            $leftTouristArr = $this->_getOrderLeftTourist($ordernum, $cancelNum, $ticketInfo, $orderInfo);

            //如果是一票一证的门票，就需要获取未使用的码
            $codeList = $leftTouristArr['codeList'];

            //未使用的身份证信息
            $availableIdcardArr = $leftTouristArr['availableIdcardArr'];
        }
        pft_log('order_refund/debug', json_encode(['getOrderLeftTourist', $ordernum, $leftTouristArr,$codeList,$availableIdcardArr,'batch_check'=>$ticketInfo['batch_check'],'cancelNum'=>$cancelNum]));

        //预检信息组装
        $preCheckParams = [
            'p_type'          => $ticketInfo['p_type'],
            'cancel_type'     => $cancelType,
            'is_force_cancel' => $isForceCancel,
            'ticket_snapshot' => $ticketSnapshot,
            'ticket_info'     => $ticketInfo,
            'cancel_num'      => $cancelNum,
            'tnum'            => $orderInfo['tnum'],
            'order_status'    => $orderInfo['status'],
            'ifpack'          => $orderInfo['ifpack'],
            'isAuditPass'     => $isAuditPass,
            'leftTouristNum'=>count($leftTouristArr),
        ];

        $refundCheckBiz = new BaseRefundCheck();
        //获取外部传入的指定门票码下标
        $personParams = [
            'personIndex'    => $personIndex,
            'personIdList'   => $personIdList,
            'ticketCodeList'  =>$ticketCodeList,
            'personInfoList'  => $refundPersonInfoList
        ];
        if ($usedRefundFix) {
            $appointIdxRes   = $fixObj->getOrderTouristByCondition($ordernum,$cancelChannel, $personParams, $preCheckParams);
            pft_log('order_refund/debug', json_encode(['getInfoInTouristByOrder_new', $appointIdxRes, $personParams, $ordernum]));
        } else {
            $appointIdxRes   = $refundCheckBiz->getOrderTouristByCondition($ordernum,$cancelChannel,$personParams, $preCheckParams);
            pft_log('order_refund/debug', json_encode(['getInfoInTouristByOrder_old', $appointIdxRes,$personParams, $ordernum]));
        }

        if ($appointIdxRes['code'] != 200){
            return $appointIdxRes;
        }
        $appointIdxData = $appointIdxRes['data']['arrIdx'];
        $appointIdxType = $appointIdxRes['data']['touristType'];
        $appointIdxList = $appointIdxRes['data']['idxList'];

        $idCardCodeParams = $appointIdxRes['data']['idCardCodeParams'];

        //判断对接三方系统订单和对接演出系统订单
        $isThirdOrder = $ticketInfo['Mdetails'] == 1 && $ticketInfo['sourceT'] > 0 ? true : false;
        $applyDid     = $ticketInfo['apply_did'];
        //是否是温泉系统订单
        $isPresumingOrder = $this->checkIsPresumingOrder($ordernum,$orderInfo);
        //判断是否独立收款
        $isSelfReceive = $this->_isSelfReceive($ordernum, $orderInfo);
        //判断下分终端是否有使用完
        $branchIsUsedNum  = $this->_isBranchUsed($ordernum);
        //$branchIsUsedNum = $this->_isBranchUsedByTourist($ordernum, $ticketSnapshot);
        //退票时需要问下其他的一些系统，该订单是否可以进行退票操作，比如优惠订单
        $otherSystemCheckRes = $checkBiz->otherSystemCheck($ordernum, $ticketInfo, $cancelNum, $cancelType, $orderInfo,$realCancelChannel,$isPresumingOrder,$isRollBack);

        if ($otherSystemCheckRes['code'] != 200) {
            return $otherSystemCheckRes;
        }

        //初步判断订单是否可以取消
        $statusCheckRes = $checkBiz->statusCheck($ordernum, $cancelNum, $cancelType, $orderInfo, $ticketInfo,
            $personIdList, $isThirdOrder,$carriage,$isPresumingOrder,$isAllowMoreRevoke, $isForceCancel);

        if ($statusCheckRes['code'] != 200) {
            return $statusCheckRes;
        }

        if (!$isRollBack) {
            //如果不是订单回滚的话，都需要进行退票属性判断
            $isRefund = $this->_checkMustRefundAttribute($ordernum, $ticketInfo, $orderInfo, $subOrderList);
            if ($isRefund['code'] != 200) {

                //记录个日志观察几天
                $jsonData = [
                    'ordernum' => $ordernum,
                    'force'    => $isForceCancel,
                    'res'      => $isRefund,
                ];
                pft_log('order_refund/noRefund', json_encode($jsonData));

                return $isRefund;
            }
        }
        //预约不可退  强制退要跳过该限制
        $checkAppointmentRes = $checkBiz->checkAppointmentRefund($applyDid,$cancelMemberId,$orderInfo,$ticketInfo);
        if(!$checkAppointmentRes['res'] && !$isForceCancel && $cancelChannel != 21){
            return $this->orderReturn(0,"预约退票规则：预约成功后不可退",['err_code'=>OrderConst::err1425,'err_msg'=>"预约退票规则：预约成功后不可退"]);
        }

        //退票权限判断
        $isNeedCheckRefundRule = $checkBiz->isNeedCheckRefundRule($ordernum, $payStatus, $cancelMemberId, $applyDid,
            $isNeedAudit,$isRollBack);
        if ($isNeedCheckRefundRule) {
            $checkAuthRes = $this->_isHaveCancelAuth($ordernum, $tid, $orderInfo, $ticketInfo, $cancelMemberId, $memberType,
                $cancelChannel, $cancelType, $isForceCancel, $subOrderList,$isAuditPass,$afterSaleNum, $refundParams);

            if ($checkAuthRes['code'] != 200) {
                return $checkAuthRes;
            }
        }
        //退票 - 优惠明细表，锁定
        if($cancelType == 'revoke'){
            $opType = $cancelNum < $orderInfo['tnum'] ? self::OP_PART_REVOCATION : self::OP_REVOCATION;
        }
        else{
            $opType = self::OP_REFUND;
        }
        if(empty($discount)){
            //初次退票 获取锁定票序号以及明细
            $arrDiscount = self::_LockRefundTouristForDiscount($ordernum, $opType, $cancelNum);
            $serialNums = [];
            if(!empty($arrDiscount)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($arrDiscount['refundTicketSerialNumber']);
            }
        }
        else{
            //退票审核 - 使用之前退票锁定的票序号
            $orderTicketDiscounts = new OrderTicketDiscounts();
            $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($discount['serialNumber']);
            $arrDiscount = self::_getRefundTouristForDiscount($ordernum,
                $cancelType == 'revoke' ? self::DISCOUNT_STATUS_USED : self::DISCOUNT_STATUS_UNUSED
                ,$serialNums);
        }
        //优惠信息重新计算 退票金额以及余额
        $refundAmount = false;
        if(!empty($arrDiscount) && empty($afterSaleNum)){
            //手续费
            $refundBiz = new Refund();
            $realFee = $refundBiz->getFeeByOrderNum($ordernum,$cancelNum,$orderInfo['status'],$isRollBack,$autoCancelFee);
            //检测余额够不够
            if($realFee > 0 && $realFee >= $arrDiscount['refundAmount']){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
                return $this->orderReturn(0, "退款金额必须大于0元才允许取消(全部&部分)订单", ['err_code' => 401, 'err_msg' => '退款金额必须大于0元才允许取消(全部&部分)订单,手续费','err_data'=>['$realFee'=>$realFee,'refundAmount'=>$arrDiscount['refundAmount']]]);
            }
            $refundAmount = $arrDiscount['refundAmount'];
        }

//        //检测余额够不够
//        $refundMoneyBiz = new RefundMoney();
//        $isMoneyEnough  = $refundMoneyBiz->checkRefundMoney($ordernum, $orderInfo, $cancelNum, $isSelfReceive,false,$refundAmount,$cancelType);
//        if (!$isMoneyEnough) {
//            if(!empty($serialNums)){
//                $orderTicketDiscounts = new OrderTicketDiscounts();
//                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
//            }
//            pft_log('order_refund/debug',
//                json_encode(['checkRefundMoney', $isMoneyEnough, $orderInfo, $cancelNum, $isSelfReceive]));
//            return $this->orderReturn(0, '', ['err_code' => OrderConst::err222]);
//        }

        //预售券产品：获取处于部分兑换的数量
        $exchangePartExchangeNum = $this->_getExchangePartExchangeNum($ordernum, $ticketInfo);
        //整理订单取消状态、金额等数据
        $orderRefundInfo = $this->_handleRefundDetail($ordernum, $cancelNum, $cancelType, $orderInfo,$branchIsUsedNum,$refundAmount, $exchangePartExchangeNum);
        $originMoney     = $orderRefundInfo['originMoney'];
        $validMoney      = $orderRefundInfo['validMoney'];
        $trackAction     = $orderRefundInfo['trackAction'];
        $validNum        = $orderRefundInfo['validNum'];
        $originNum       = $orderRefundInfo['originNum'];
        $leftNum         = $orderRefundInfo['leftNum'];
        $status          = $orderRefundInfo['status'];

        //门票码状态判断
        if ($cancelType == 'common' && $status != CommonOrderStatus::CANCELED_CODE){  //普通退票的情况订单最后状态不是取消的情况
            $touristInfoCheckRes = $this->_checkRefundTouristStatus($ordernum,$appointIdxList,$appointIdxType,$cancelChannel);
            if ($touristInfoCheckRes['code'] != 200){
                if(!empty($serialNums)){
                    $orderTicketDiscounts = new OrderTicketDiscounts();
                    $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
                }
                return $touristInfoCheckRes;
            }
        }
        //整理门票码的状态
        $orderTouristRes = $fixObj->handleRefundTourist($ordernum, $cancelNum, $cancelType, [
            'leftNum'=>$leftNum,
            'opId'=>$opId,
            'orderInfo'=>$orderInfo,
            'cancelChannel'=>$cancelChannel,
            'ticketSnapshot'=>$ticketSnapshot,
            'appointIdxData'=>$appointIdxData,
            'isAuditPass'=>$isAuditPass,
            'cancelTerminal'=>$cancelTerminal,
            'afterSaleNum'=>$afterSaleNum,
            'appointIdxType'=>$appointIdxType,
            'oneTouristOneTicket'=>$oneTouristOneTicket,
        ]);
        if ($orderTouristRes['code'] != 200) {
            if(!empty($serialNums)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            }
            return $orderTouristRes;
        }
        $refundIdxList    = $orderTouristRes['data']['refundIdxList'];
        $canTakeNum       = $orderTouristRes['data']['canTakeNum'];
        $touristTrackList = $orderTouristRes['data']['touristTrackList'];
        //这个取消的门票码带到异步任务里面去
        $refundIdx        = $orderTouristRes['data']['refundIdx'];
        $personInfoList   = $orderTouristRes['data']['personInfoList'];
        if (empty($usedRefundFix)) {
            $personInfoList = [];
        }
        $refundOtherData  = [
            'refundIdxList'    => $refundIdxList,
            'canTakeNum'       => $canTakeNum,
            'touristTrackList' => $touristTrackList,
        ];
        //如果是取消订单而且没有传身份证的话，使用未使用的身份证
        if ($leftNum == 0 && !$personIdList) {
            $personIdList = $availableIdcardArr;
        }

        //预售券产品：校验当前退票可退数量 是否预售券可退

        if(!$isSkipExchangeTicket){
            $checkCancelOrderRes = $this->_checkCancelOrder($ordernum, $ticketInfo,$refundIdx);
            if($checkCancelOrderRes['code'] != 200){
                return $this->orderReturn(0, $checkCancelOrderRes['msg'], ['err_code' => OrderConst::err10001, 'inner_msg' => $checkCancelOrderRes['msg']]);
            }
            if(!empty($checkCancelOrderRes['data'])){
                $refundErrorMsg = '券码 '.implode('、',$checkCancelOrderRes['data']).'的权益处于兑换中，不可取消';
                if($isForceCancel){
                    $refundErrorMsg = '券码 '.implode('、',$checkCancelOrderRes['data']).'是否确认继续强制取消?';
                }
                return $this->orderReturn(0, $refundErrorMsg, ['err_code' => OrderConst::err10001, 'inner_msg' => $refundErrorMsg]);
            }
        }

        if($cancelType == 'revoke') {
            [$status, $revokeCanUseNum, $leftNum] = $this->_getRevokeOrderRefundInfo($orderInfo, $cancelNum, $leftNum, $branchIsUsedNum, $refundIdxList);
            $orderRefundInfo['status'] = $status;
            $refundOtherData['revokeCanUseNum'] = $revokeCanUseNum;
        }

        //获取和插入退票信息
        $receiveType     = $isSelfReceive ? 1 : 0;
        $isNeedRefund    = true;
        $detailsArr      = [];
        $cancelTimestamp = strtotime($cancelTime);

        //真实的退票渠道
        $detailsArr['channel'] = $realCancelChannel;

        if ($autoCancelFee !== false) {
            $detailsArr['autoCancelFee'] = $autoCancelFee;
        }

        //退票身份证信息也要记录退票记录里面
        if ($personIdList) {
            $detailsArr['personIdList'] = $personIdList;
        }
        if ($carriage > 0){
            $detailsArr['carriage']  = $carriage;
        }
        if ($personIndex){
            $detailsArr['personIndex'] = $personIndex;
        }
        $detailsArr['isOnlyCancelNum'] = false;
        if ($appointIdxType['card_num_left_cancel_num'] || $appointIdxType['isOnlyCancelNum']) {
            $detailsArr['isOnlyCancelNum'] = true;
        }
        if (!empty($personInfoList) && !$detailsArr['isOnlyCancelNum']) {
            $detailsArr['personInfoList'] = $personInfoList;
        }
        if ($refundIdx && !$detailsArr['isOnlyCancelNum']){
            $detailsArr['refundIdx'] = $refundIdx;
        }
        //取消前的状态
        $detailsArr['before_status'] = $orderInfo['status'];
        if ($ticketCodeList){
            $detailsArr['ticketCodeList'] = $ticketCodeList;
            //如果是套票门票码 转化成Idx 本质是不需要这个判断，优先处理线上问题。保险
            //fixme 套票支持多门票码取消
            if($ifpack == 1  && $isCancelSub){
                $personIndex = $refundIdx;
            }
        }
        if ($isRollBack || $orderInfo['status'] == CommonOrderStatus::WAIT_PRINTTICKET_CODE){  //待出票的情况不收手续费
            $detailsArr['isRollBack'] = 1;
        }
        //获取套票子票取消的订单金额，特殊需求 lc
        //增加判断如果是套票才获取
        if ($orderInfo['product_type'] == 'F') {
            $orderRefundBiz       = new Refund();
            $packRefundOrderMoney = $orderRefundBiz->getPackOrderMoney($ordernum);
            if ($packRefundOrderMoney > 0) {
                $detailsArr['packRefundMoney'] = $packRefundOrderMoney;
            }
        }

        if(!empty($afterSaleNum)){
            $detailsArr['after_sale_num'] = $afterSaleNum;
            $detailsArr['cancel_type'] = $cancelType;
        }
        $detailsJson = json_encode($detailsArr);
        //生成取消操作唯一流水号，相当于这个流水号规则会同时作用于部分退、整票退、撤销撤改、回滚
        $reqSerialNumber = $this->_genReqSerialNumber($ordernum, $reqSerialNumber);
        //是否供应商开启酒店预定确定拒绝的订单
        $specialData = [];
        if ($refundParams['isScheduledRefuseBack']) {
            $specialData['hotel_scheduled_refuse'] = true;
        }

        //如果是景区产品线退票的订单，在退款的时候做特殊处理
        if ($refundParams['isScenicLineRefund']) {
            $specialData['isScenicLineRefund'] = $refundParams['isScenicLineRefund'];
        }
        //账户余额预检测
        $moneyRefundModel = new Refund();
        $moneyRefundPreCheckRes = $moneyRefundModel->orderTradeRefundPreCheck($ordernum, $reqSerialNumber, $applyDid,
            $trackAction, $validNum, $cancelNum, $originNum, $originMoney, $validMoney, $cancelTimestamp,
            $isNeedRefund, $opId, $receiveType, $detailsJson, 0, 0, $specialData);
        if ($moneyRefundPreCheckRes['code'] != 200) {
            if(!empty($serialNums)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum, $serialNums);
            }
            pft_log('order_refund/debug',
                json_encode(['orderTradeRefundPreCheck', $moneyRefundPreCheckRes, $ordernum, $reqSerialNumber, $applyDid,
                    $trackAction, $validNum, $cancelNum, $originNum, $originMoney, $validMoney, $cancelTimestamp,
                    $isNeedRefund, $opId, $receiveType, $detailsJson]));
            return $this->orderReturn(0, '余额不足', ['err_code' => OrderConst::err222]);
        }

        //将退票记录数据插入退票信息表，其实在上面账户余额检测方法里面已经插入了退票记录：orderTradeRefundPreCheck
        $refundModel     = new OrderRefund();
        $refundRecodeRes = $refundModel->addRefundJournal($ordernum, $reqSerialNumber, $applyDid, $trackAction,
            $validNum, $cancelNum,
            $originNum, $originMoney, $validMoney, $cancelTimestamp, $isNeedRefund, $opId, $receiveType, $detailsJson);
        //插入退款记录失败
        if (!$refundRecodeRes) {
            return $this->orderReturn(0, "系统异常，请稍后再试", ['err_code' => OrderConst::err15, 'inner_msg' => '退票记录写入失败']);
        }

        //平台的退款流水号
        $pftSerialNumber = $refundRecodeRes['id'];
        //退票审核 过期的供应商自己取消也要审核  by jackchb 2020 7月细节优化
        if ($isNeedAudit) {
            $refundAuditExt = [
                'card_num_left_tourist_num'=>$appointIdxType['card_num_left_cancel_num']
            ];
            $refundAuditBiz = new RefundAudit();
            $refundAuditRes = $refundAuditBiz->checkRefundAudit($ordernum, $validNum, $cancelMemberId,
                $modifyType = null,
                $orderInfo, $cancelNum, $pftSerialNumber, $personIdList,$isThirdOrder, $refundAuditExt);
        } else {
            $refundAuditRes = [
                'code' => 100,
                'msg'  => '不需要审核',
            ];
        }
        //计时订单退款处理
        $timeOrderRefund = [
            'is_refund_deposit'  => $isRefundDeposit,
            'is_refund_over_pay' => $isRefundOverPay,
        ];
        //退票审核时拓展参数
        $refundAuditExt = [
            'cancel_audit_remark' => $cancelAuditRemark,
            'discount'=>['serialNumber'=>$arrDiscount['refundTicketSerialNumber']],
            'card_num_left_tourist_num'=>$appointIdxType['card_num_left_cancel_num']
        ];
        $moreData = [];
        //将原始操作渠道和人员写进审核信息中
        $moreData['track_source'] = $cancelChannel;
        $moreData['op_id']        = $opId;
        if ($refundParams['subSid'] && $refundParams['subOpId']){
            $moreData['subSid'] = $refundParams['subSid'];
            $moreData['subOpId'] = $refundParams['subOpId'];
        }
        if (isset($refundParams['isRevokeSmsNotice']) && $refundParams['isRevokeSmsNotice']){
            $moreData['isRevokeSmsNotice'] = $refundParams['isRevokeSmsNotice'];
        }
        if (!empty($afterSaleNum)){
            $moreData['after_sale_num'] = $refundParams['afterSaleNum'];
        }
        if (!empty($isRetry)){
            $moreData['is_retry'] = $refundParams['is_retry'];
        }
        if (!empty($batchRefundMoreIdx)){
            $moreData['batch_refund_more_idx'] = $refundParams['batchRefundMoreIdx'];
        }
        if (!empty($refundIdx)){
            $moreData['tourist_idx'] = array_map('strval', $refundIdx);
        }
        if (!empty($refundIdxList) && $usedRefundFix) {
            $moreData['tourist_list'] = $refundIdxList;
        }
        //是否系统统一审核
        $isSysException = $refundAuditRes['code'] == 999 ? true : false;
        if (in_array($refundAuditRes['code'], [200, 999])) {
            //需要退票审核，提交退票审核
            pft_log('order_refund/debug', json_encode(['needRefundAudit', $refundAuditRes, $ordernum]));
            //提交退票审核
            $auditHandleRes = $this->_addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum,
                $opId, $pftSerialNumber, $reqSerialNumber, $applyDid, $personIdList, $isSysException, $timeOrderRefund, $refundAuditExt, $moreData);
            //直接写入退票审核失败
            if ($auditHandleRes['code'] != 200) {
                return $auditHandleRes;
            }

            //退票审核记录写入成功
            $auditInfo = [
                'pft_serial_number' => $pftSerialNumber,
                'req_serial_number' => $reqSerialNumber,
                'cancel_num'        => $cancelNum,
                'left_num'          => $leftNum,
                'valid_num'         => $validNum,
                'valid_money'       => $validMoney,
                'is_system_audit'   => $isSysException,
            ];
            return $this->orderReturn(1095, '退票审核提交成功', $auditInfo);

        } else if ($refundAuditRes['code'] == 100) {
            //不需要退票审核

            //判断是不是套票的主票
            $isPackageMain = $ifpack == 1 ? true : false;

            //如果套票主票，而且需要将子票给取消掉的情况下
            //里面的逻辑是很复杂的
            if ($isPackageMain && $isCancelSub) {
                //去取消所有的子票
                if (QConfigSwitchController::getRefundPackageFixUsedWithTenant($ordernum)) {
                    $packageChildObj = new PackageChildOrderCancel();
                    $packageCancelRes = $packageChildObj->cancelPackageChildOrder($ordernum, $subOrderList, [
                        'cancelNum'=>$cancelNum,
                        'cancelType'=>$cancelType,
                        'totalNum'=>$orderInfo['tnum'],
                        'opId'=>$opId,
                        'personIndex'=>$personIndex,
                        'isForceCancel'=>$isForceCancel,
                        'personIdList'=>$personIdList,
                        'isRollBack'=>$isRollBack,
                        'cancelChannel'=>$cancelChannel,
                        'approvalParams'=>[],
                        'pftSerialNumber'=>$pftSerialNumber,
                        'personInfoList'=>$personInfoList,
                        'refundType'=>$appointIdxType,
                        'isSyntaxRollback'=>$isSyntaxRollBack,
                    ]);
                } else {
                    $packageCancelRes = $this->_cancelPackageChildOrder($ordernum, $subOrderList, $cancelNum, $cancelType,
                        $orderInfo['tnum'], $opId, $personIndex,$isForceCancel,$personIdList,$isRollBack,[],$pftSerialNumber,$personInfoList, $isSyntaxRollBack);
                }
                pft_log('order_refund/debug', json_encode(['cancelPackageChildOrder',$packageCancelRes, $ordernum]));
                if ($packageCancelRes['code'] != 200) {
                    //记录异常日志
                    $logParams = [
                        'ac'     => 'cancelPackageChild',
                        'params' => [$ordernum, $subOrderList, $cancelNum, $cancelType,
                            $orderInfo['tnum'], $opId, $personIndex,$isForceCancel,$personIdList,$isRollBack,[],$pftSerialNumber, $personInfoList, $isSyntaxRollBack],
                    ];
                    $logResponse = [
                        'ac'        => 'cancelPackageChild',
                        'handleRes' => $packageCancelRes,
                        'response'   => $packageCancelRes['msg'],
                    ];

                    pft_log('order_refund/debug',
                        json_encode(['recordAnAbnormal',$logResponse, $ordernum]));
                    $reason = $packageCancelRes['msg'];
                    if (isset($packageCancelRes['data']['log_err_msg'])) {
                        $reason = $packageCancelRes['data']['log_err_msg'];
                    }
                    $this->recordAnAbnormal($ordernum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                        PackConst::ABNORMAL_REASON_CANCEL,PackConst::ABNORMAL_LINKAGE_YES,$reason,$opId);
                    //返回返回错误
                    return $packageCancelRes;
                }
            }
            //如果$refundIdx为空 确认是否一票种一票，一票种一票只这支持指定身份证取消，否则会乱序
            //TODO 核心原因 \Business\Order\BaseRefund::_handleRefundTourist idx置换逻辑不支持
            pft_log('order_refund/debug',
                json_encode(['ThirdRefundIdCard_refundIdx:', $isThirdOrder, $isCancelThirdSystem, $refundIdx, $usedRefundFix, $ordernum]));
            //三方退票 $refundIdx 转化为 $personIdList 支持 单个 多个 全部退票置换
            $thirdPersonIdList = [];
            $refundThirdPersonInfoList = [];
            if($isThirdOrder && $isCancelThirdSystem && $refundIdx){
                //$personIdList 是对身份证进行过滤
                if(!empty($personIdList) && count($personIdList) == $cancelNum){
                    $thirdPersonIdList = $personIdList;
                    pft_log('order_refund/debug',
                        json_encode(['ThirdRefundIdCard_person:', $refundIdx, $personIdList, $cancelNum,$ordernum]));
                }
                else{
                    //如果身份证不符合[取消（退票后票数为0）整笔订单且不传身份证，会系统匹配过滤身份证]，这个时候只能重新置换，专门用以三方。
                    //fixme 这里的置换过滤，未兼容取消数量大于实名制数量的场景
                    list($thirdPersonIdList,$idxRes) = self::getRealNameDataForRefundIdxArr($ordernum,$refundIdx);
//                    if((count($thirdPersonIdList) > 0) && count($thirdPersonIdList) != $cancelNum){
//                        pft_log('order_refund/debug',
//                            json_encode(['ThirdRefundIdCard_error', $idxRes, $thirdPersonIdList, $cancelNum,$ordernum]));
//                        return $this->orderReturn(0, '退票参数错误，身份证数量异常', ['err_code' => OrderConst::err1]);
//                    }
                    pft_log('order_refund/debug',
                        json_encode(['ThirdRefundIdCard_Third', $idxRes, $thirdPersonIdList, $cancelNum,$ordernum]));
                }
                //这里是实名制游客证件，包含除身份证以外的其他证件
                if ($usedRefundFix) {
                    if ($appointIdxType['idCard_new'] && static::getRefundParamsToOpenApiWithQconf()) {
                        $refundThirdPersonInfoList = $personInfoList;
                        pft_log('order_refund/debug',
                            json_encode(['ThirdRefundIdCard_Third_Person', $refundThirdPersonInfoList, $thirdPersonIdList, $cancelNum,$ordernum, $appointIdxType]));
                    }
                }
            }
            $thirdPersonIdList = empty($thirdPersonIdList) ? $personIdList : $thirdPersonIdList;
            if ($usedRefundFix) {
                pft_log('order_refund/debug',json_encode(['usedRefundFix', $appointIdxType, $cancelNum,$cancelNum, $personInfoList]));
                /**
                 * 兼容取消数量大于实名制数量的场景：转换为按数量退
                 * 下面的||条件兼容
                 * 1.一票种一票脏数据场景，仅一个游客实名制信息，且票数是n(>1)
                 * 2.退票数量大于身份证数量
                 * 3.非按证件类退，非按身份证退，且是仅按数量退
                 * */
                if ($appointIdxType['card_num_left_cancel_num'] || $cancelNum > count($thirdPersonIdList) ||
                    $cancelNum > count($personInfoList) ||
                    ($appointIdxType['isOnlyCancelNum'] && $appointIdxType['idCard_new'] == false
                        && $appointIdxType['idCard'] == false && $cancelNum > count($personInfoList) )
                ) {
                    $thirdPersonIdList = [];
                    $refundThirdPersonInfoList = [];
                }
            }
            if ($isThirdOrder && $cancelType == 'revoke') {
                //else分支：审核同意，或者撤销直接同意标识判断跳过加入审批，直接进行退票
                if (empty($isAuditAgreed)) {
                    //写入平台审核记录，不写入三方审核记录
                    //提交退票审核
                    $auditHandleRes = $this->_addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum,
                        $opId, $pftSerialNumber, $reqSerialNumber, $applyDid, $personIdList, $isSysException, $timeOrderRefund, $refundAuditExt, $moreData);
                    //直接写入退票审核失败
                    if ($auditHandleRes['code'] != 200) {
                        return $auditHandleRes;
                    }

                    //退票审核记录写入成功
                    $auditInfo = [
                        'pft_serial_number' => $pftSerialNumber,
                        'req_serial_number' => $reqSerialNumber,
                        'cancel_num'        => $cancelNum,
                        'left_num'          => $leftNum,
                        'valid_num'         => $validNum,
                        'valid_money'       => $validMoney,
                        'is_system_audit'   => $isSysException,
                    ];
                    return $this->orderReturn(1095, '审核提交成功', $auditInfo);
                }
            } elseif ($isThirdOrder && $isForceCancel) {
                // 不做退票审核处理
                pft_log('order_refund/debug',json_encode(['取消不经过上游审核', $isThirdOrder, $isForceCancel], JSON_UNESCAPED_UNICODE));
                $thirdRes = ['code' => 200];
            } else {
                //往三方系统退票
                $thirdRes = $this->_thirdSystemRefund([
                    'ordernum'         => $ordernum,
                    'leftNum'          => $validNum,
                    'orderInfo'         => $orderInfo,
                    'ticketInfo'         => $ticketInfo,
                    'isCancelThirdSystem' => $isCancelThirdSystem,
                    'isThirdOrder'       => $isThirdOrder,
                    'isNeedAudit'        => $isNeedAudit,
                    'thirdPersonIdList'  => $thirdPersonIdList,
                    'codeList'           => $codeList,
                    'pftSerialNumber'    => $pftSerialNumber,
                    'realCancelChannel'  => $realCancelChannel,
                    'cancelNum'          => $cancelNum,
                    'isRollBack'         => $isRollBack,
                    'refundOpenCode'     => $refundOpenCode,
                    'refundPersonInfoList'=>$refundThirdPersonInfoList
                ]);
            }
            //三方退票结果：1095=申请退款审核中  1096=拒绝退款申请 200=处理成功  1109=第三方退票请求超时
            if ($thirdRes['code'] == 1096 || $thirdRes['code'] == 1109) {
                //删除退款记录
                $refundModel->delRefundJournal($pftSerialNumber);

                //拒绝信息
                $errorMsg  = $thirdRes['msg'];
                $refundMsg = $errorMsg ? "订单号：【{$ordernum}】拒绝退款申请【{$errorMsg}】" : "订单号：【{$ordernum}】拒绝退款申请";
                $RefundApprovalService = new RefundApprovalService();
                $RefundApprovalService->approvalNotifyForOTA($ordernum,$reqSerialNumber,$opId,2,$refundMsg,false);
                if($orderInfo['ifpack']){
                    //记录异常日志
                    $logParams = [
                        'ac'     => 'thirdAudit',
                        'params' => [$ordernum, $validNum, $orderInfo, $ticketInfo, $isCancelThirdSystem,
                            $isThirdOrder, $isNeedAudit, $thirdPersonIdList, $codeList, $pftSerialNumber,$realCancelChannel,$cancelNum,
                            $isRollBack,$refundOpenCode],
                    ];
                    $logResponse = [
                        'ac'        => 'thirdAudit',
                        'handleRes' => $thirdRes,
                        'response'   => $refundMsg,
                    ];

                    $this->recordAnAbnormal($ordernum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                        PackConst::ABNORMAL_REASON_UPSTREAM,PackConst::ABNORMAL_LINKAGE_YES,$refundMsg,$opId);
                }
                //拒绝退票申请
                return $this->orderReturn(0, $refundMsg, ['err_code' => OrderConst::err1096]);

            } else if ($thirdRes['code'] == 1095) {
                //需要退票审核，提交退票审核
                //提交退票审核
                $auditHandleRes = $this->_addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum,
                    $opId, $pftSerialNumber, $reqSerialNumber, $applyDid, $personIdList, $isSysException = false, $timeOrderRefund, $refundAuditExt, $moreData);

                //直接写入退票审核失败
                if ($auditHandleRes['code'] != 200) {
                    return $auditHandleRes;
                }

                //退票审核记录写入成功
                $auditInfo = [
                    'pft_serial_number' => $pftSerialNumber,
                    'req_serial_number' => $reqSerialNumber,
                    'cancel_num'        => $cancelNum,
                    'left_num'          => $leftNum,
                    'valid_num'         => $validNum,
                    'valid_money'       => $validMoney,
                    'is_system_audit'   => $isSysException,
                ];
                return $this->orderReturn(1095, '退票审核提交成功', $auditInfo);

            } else {
                RefundRedisManage::delRefundAuditRecordRedisLock($pftSerialNumber);
                if(!empty($cancelAuditRemark)) {
                    $cancelRemark = empty($cancelRemark) ? $cancelAuditRemark : $cancelRemark . '，' . $cancelAuditRemark;
                }

                if($arrDiscount){
                    $refundOtherData['serial_number'] = $arrDiscount['refundTicketSerialNumber'];
                }
                //直接进行后续的退票
                $insideRefundRes = $this->_insideRefund($ordernum, $pftSerialNumber, $cancelType, $realCancelTime,
                    $orderRefundInfo, $cancelChannel,
                    $opId, $cancelTerminal, $cancelRemark, $orderInfo, $ticketInfo, $subOrderList, $personIndex,
                    $personIdList,$ticketCodeList,$carriage,$refundOtherData,$refundIdx, $moreData);

                if ($insideRefundRes['code'] != 200) {
                    //删除退款记录
                    $refundModel->delRefundJournal($pftSerialNumber);

                    //内部退票失败, 直接返回
                    return $insideRefundRes;
                } else {
                    //这边返回退票的追踪记录
                    $refundTrackId = $insideRefundRes['data']['track_id'];
                    $resData = [
                        'pft_serial_number' => $pftSerialNumber,
                        'req_serial_number' => $reqSerialNumber,
                        'order_status'      => $status,
                        'cancel_num'        => $cancelNum,
                        'left_num'          => $leftNum,
                        'valid_num'         => $validNum,
                        'valid_money'       => $validMoney,
                        'track_id'          => $refundTrackId,
                        'op_id'             => $opId,
                        'refund_idx'        => $refundIdx,
                        'idCardCodeParams'  => $idCardCodeParams
                    ];
                    if(!empty($arrDiscount)){
                        $resData['discount'] = $arrDiscount;
                    }

                    //需要同步执行的退票逻辑
                    $this->_syncRefundTask($ordernum, $orderInfo, $ticketInfo, $orderRefundInfo, $refundParams, $refundIdxList, $cancelType);

                    return $this->orderReturn(200, '', $resData);
                }
            }
        } else {
            //检测退票审核的时候，直接报错，不能退票
            $errCode  = $refundAuditRes['code'];
            $errMsg   = $refundAuditRes['msg'];
            $innerMsg = $errMsg . "【{$errCode}】";
            //释放 优惠 锁
            if(!empty($serialNums)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            }
            return $this->orderReturn(0, '', ['err_code' => OrderConst::err1096, 'inner_msg' => $innerMsg]);
        }
    }

    /**
     * 已经支付的订单退票
     *
     * @param  string  $ordernum  订单号
     * @param  array  $orderInfo  订单相关信息
     * @param  array  $ticketInfo  门票相关信息
     * @param  array  $memberInfo  取消用户相关信息
     * @param  array  $subOrderList  套票的子票列表
     * @param  array  $refundParams  退票请求参数，BaseRefundCheck->baseCheck初步处理参数
     *
     * @return array
     */
    private function _payedRefundForApproval($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList, $refundParams)
    {
        //校验过的退票参数
        $cancelNum           = $refundParams['cancelNum'];
        $opId                = $refundParams['opId'];
        $cancelChannel       = $refundParams['cancelChannel'];
        $cancelType          = $refundParams['cancelType'];
        $reqSerialNumber     = $refundParams['reqSerialNumber'];
        $cancelRemark        = $refundParams['cancelRemark'];
        $cancelTerminal      = $refundParams['cancelTerminal'];
        $personIndex         = $refundParams['personIndex'];
        $personIdList        = $refundParams['personIdList'];
        $isNeedAudit         = $refundParams['isNeedAudit'];
        $isAuditAgreed       = $refundParams['isAuditAgreed'];
        $autoCancelFee       = $refundParams['autoCancelFee'];
        $isCancelSub         = $refundParams['isCancelSub'];
        $isForceCancel       = $refundParams['isForceCancel'];
        $isCancelThirdSystem = $refundParams['isCancelThirdSystem'];
        $cancelTime          = $refundParams['cancelTime'];
        $realCancelChannel   = $refundParams['realCancelChannel'];
        $isRollBack          = $refundParams['isRollBack'];
        $isSyntaxRollBack    = $refundParams['isSyntaxRollBack'] ?? false;
        $carriage            = $refundParams['carriage'];
        $ticketCodeList      = $refundParams['ticketCodeList'];
        $isAuditPass         = $refundParams['isAuditPass'];
        $ticketSnapshot      = $refundParams['ticketSnapShot'];
        $isAllowMoreRevoke   = $refundParams['isAllowMoreRevoke'];
        $isRefundDeposit     = $refundParams['isRefundDeposit'];
        $isRefundOverPay     = $refundParams['isRefundOverPay'];
        $cancelAuditRemark   = $refundParams['cancelAuditRemark']; //发起退票申请时候的备注
        $discount   = $refundParams['discount'];
        $refundOpenCode   = $refundParams['refundOpenCode'];
        $afterSaleNum = $refundParams['afterSaleNum'];
        $isRetry = $refundParams['isRetry'];
        $batchRefundMoreIdx = $refundParams['batchRefundMoreIdx'];
        $isSkipExchangeTicket = $refundParams['isSkipExchangeTicket'] ?? false;
        $refundPersonInfoList = $refundParams['personInfoList'] ?? [];


        //逻辑取消时间处理，如果外部没有传，就取当前的时间
        $realCancelTime = date('Y-m-d H:i:s');
        $cancelTime     = $cancelTime ? $cancelTime : $realCancelTime;
        $isThirdAuditRes = $refundParams['isThirdAuditRes'] ?? '';
        //退票锁处理
        $checkBiz     = new BaseRefundCheck();
        $lockLeftNum  = $cancelType == 'revoke' ? $orderInfo['verified_num'] : $orderInfo['tnum'];
        $lockCheckRes = $checkBiz->refundLock($ordernum, $reqSerialNumber, $cancelType, $lockLeftNum, $opId,
            $isNeedAudit, $personIndex,$isThirdAuditRes);
        if($isSkipExchangeTicket){
            $lockCheckRes['code'] = 200;
        }
        if($reqSerialNumber && !$isNeedAudit && ($lockCheckRes['code'] != 200)){
            //用来解决无需前审情况下发起审批流程，造成的命中锁的情况导致退票卡住 【无需审核订单，一开始无绑定，审批过程中又绑定，重新发起审批】
            $lockCheckRes['code'] = 200;
        }
        if ($lockCheckRes['code'] != 200) {
            $orderTicketDiscounts = new OrderTicketDiscounts();
            $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($discount['serialNumber']);
            $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            return $lockCheckRes;
        }
        //是否使用改造后的放量
        $qConfigSwitch = new QConfigSwitchController();
        $usedRefundFix = false;
        if ($qConfigSwitch::getRefundFixUsedWithTenant($ordernum)) {
            $usedRefundFix = true;
            $fixObj = new HandleTourist();
        }

        //门票数据
        $tid       = $orderInfo['tid'];
        $payStatus = $orderInfo['pay_status'];
        $ifpack    = $orderInfo['ifpack'];

        //取消用户数据
        $memberType     = $memberInfo['memberType'];
        $cancelMemberId = $memberInfo['cancelMemberId'];
        $isSuper        = $memberInfo['isSuper'];
        $opId           = $memberInfo['opId'];

        if ($usedRefundFix) {
            //调用java api获取游客身份信息;非已过期：未验证  已过期statu=2订单：未验证+已过期
            $leftTouristArr = $fixObj->getOrderLeftTourist($ordernum, $orderInfo);
            $codeList = $fixObj->getOneTicketOneCodeCodeList($ordernum, $leftTouristArr,$ticketInfo['batch_check'],$cancelNum);
            //未使用的身份证信息
            $availableIdcardArr = $fixObj->getIdcardArrWithTouristInfoList($leftTouristArr);
        } else {
            //如果是一票一证的门票，就需要获取未使用的码
            $leftTouristArr = $this->_getOrderLeftTourist($ordernum, $cancelNum, $ticketInfo, $orderInfo);

            //如果是一票一证的门票，就需要获取未使用的码
            $codeList = $leftTouristArr['codeList'];

            //未使用的身份证信息
            $availableIdcardArr = $leftTouristArr['availableIdcardArr'];
        }
        pft_log('order_refund/debug', json_encode(['getOrderLeftTourist', $ordernum, $leftTouristArr,$codeList,$availableIdcardArr,'batch_check'=>$ticketInfo['batch_check'],'cancelNum'=>$cancelNum]));
        //预检信息组装
        $preCheckParams = [
            'p_type'          => $ticketInfo['p_type'],
            'cancel_type'     => $cancelType,
            'is_force_cancel' => $isForceCancel,
            'ticket_snapshot' => $ticketSnapshot,
            'ticket_info'     => $ticketInfo,
            'cancel_num'      => $cancelNum,
            'tnum'            => $orderInfo['tnum'],
            'order_status'    => $orderInfo['status'],
            'ifpack'          => $orderInfo['ifpack'],
            'isAuditPass'     => $isAuditPass,
        ];

        $refundCheckBiz = new BaseRefundCheck();
        //获取外部传入的指定门票码下标
        $personParams = [
            'personIndex'    => $personIndex,
            'personIdList'   => $personIdList,
            'ticketCodeList'  =>$ticketCodeList,
            'personInfoList'    => $refundPersonInfoList
        ];
        if ($usedRefundFix) {
            $appointIdxRes   = $fixObj->getOrderTouristByCondition($ordernum,$cancelChannel, $personParams, $preCheckParams);
        }else{
            $appointIdxRes   = $refundCheckBiz->getOrderTouristByCondition($ordernum,$cancelChannel,$personParams, $preCheckParams);
        }
        pft_log('order_refund/debug', json_encode(['getOrderTouristByCondition', $appointIdxRes,$personParams, $ordernum]));
        if ($appointIdxRes['code'] != 200){
            return $appointIdxRes;
        }
        $appointIdxData = $appointIdxRes['data']['arrIdx'];
        $appointIdxType  = $appointIdxRes['data']['touristType'];
        $appointIdxList  = $appointIdxRes['data']['idxList'];

        $idCardCodeParams = $appointIdxRes['data']['idCardCodeParams'];

        //判断对接三方系统订单和对接演出系统订单
        $isThirdOrder = $ticketInfo['Mdetails'] == 1 && $ticketInfo['sourceT'] > 0 ? true : false;
        $applyDid     = $ticketInfo['apply_did'];
        //是否是温泉系统订单
        $isPresumingOrder = $this->checkIsPresumingOrder($ordernum,$orderInfo);
        //判断是否独立收款
        $isSelfReceive = $this->_isSelfReceive($ordernum, $orderInfo);
        //判断下分终端是否有使用完
        $branchIsUsedNum  = $this->_isBranchUsed($ordernum);
        //$branchIsUsedNum = $this->_isBranchUsedByTourist($ordernum, $ticketSnapshot);
        //退票时需要问下其他的一些系统，该订单是否可以进行退票操作，比如优惠订单
        $otherSystemCheckRes = $checkBiz->otherSystemCheck($ordernum, $ticketInfo, $cancelNum, $cancelType, $orderInfo,$realCancelChannel,$isPresumingOrder,$isRollBack);

        if ($otherSystemCheckRes['code'] != 200) {
            return $otherSystemCheckRes;
        }

        //初步判断订单是否可以取消
        $statusCheckRes = $checkBiz->statusCheck($ordernum, $cancelNum, $cancelType, $orderInfo, $ticketInfo,
            $personIdList, $isThirdOrder,$carriage,$isPresumingOrder,$isAllowMoreRevoke, $isForceCancel);

        if ($statusCheckRes['code'] != 200) {
            return $statusCheckRes;
        }

        if (!$isRollBack) {
            //如果不是订单回滚的话，都需要进行退票属性判断
            $isRefund = $this->_checkMustRefundAttribute($ordernum, $ticketInfo, $orderInfo, $subOrderList);
            if ($isRefund['code'] != 200) {

                //记录个日志观察几天
                $jsonData = [
                    'ordernum' => $ordernum,
                    'force'    => $isForceCancel,
                    'res'      => $isRefund,
                ];
                pft_log('order_refund/noRefund', json_encode($jsonData));

                return $isRefund;
            }
        }
        //预约不可退  强制退要调过该限制
        $checkAppointmentRes = $checkBiz->checkAppointmentRefund($applyDid,$cancelMemberId,$orderInfo,$ticketInfo);
        if(!$checkAppointmentRes['res'] && !$isForceCancel && $cancelChannel != 21){
            return $this->orderReturn(0,"预约退票规则：预约成功后不可退",['err_code'=>OrderConst::err1425,'err_msg'=>"预约退票规则：预约成功后不可退"]);
        }
        //退票权限判断
        $isNeedCheckRefundRule = $checkBiz->isNeedCheckRefundRule($ordernum, $payStatus, $cancelMemberId, $applyDid,
            $isNeedAudit,$isRollBack);
        if ($isNeedCheckRefundRule) {
            $checkAuthRes = $this->_isHaveCancelAuth($ordernum, $tid, $orderInfo, $ticketInfo, $cancelMemberId, $memberType,
                $cancelChannel, $cancelType, $isForceCancel, $subOrderList,$isAuditPass,$afterSaleNum,$refundParams);

            if ($checkAuthRes['code'] != 200) {
                return $checkAuthRes;
            }
        }
        //退票 - 优惠明细表，锁定
        if($cancelType == 'revoke'){
            $opType = $cancelNum < $orderInfo['tnum'] ? self::OP_PART_REVOCATION : self::OP_REVOCATION;
        }
        else{
            $opType = self::OP_REFUND;
        }
        if(empty($discount)){
            //初次退票 获取锁定票序号以及明细
            $arrDiscount = self::_LockRefundTouristForDiscount($ordernum, $opType, $cancelNum);
            $serialNums = [];
            if(!empty($arrDiscount)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($arrDiscount['refundTicketSerialNumber']);
            }
        }
        else{
            //退票审核 - 使用之前退票锁定的票序号
            $orderTicketDiscounts = new OrderTicketDiscounts();
            $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($discount['serialNumber']);
            $arrDiscount = self::_getRefundTouristForDiscount($ordernum, self::DISCOUNT_STATUS_UNUSED,$serialNums);
        }
        //优惠信息重新计算 退票金额以及余额
        $refundAmount = false;
        if(!empty($arrDiscount) && empty($afterSaleNum)){
            //手续费
            $refundBiz = new Refund();
            $realFee = $refundBiz->getFeeByOrderNum($ordernum,$cancelNum,$orderInfo['status'],$isRollBack,$autoCancelFee);
            //检测余额够不够
            if($realFee > 0 && $realFee >= $arrDiscount['refundAmount']){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
                return $this->orderReturn(0, "退款金额必须大于0元才允许取消(全部&部分)订单", ['err_code' => 401, 'err_msg' => '退款金额必须大于0元才允许取消(全部&部分)订单,手续费','err_data'=>['$realFee'=>$realFee,'refundAmount'=>$arrDiscount['refundAmount']]]);
            }
            $refundAmount = $arrDiscount['refundAmount'];
        }

//        //检测余额够不够
//        $refundMoneyBiz = new RefundMoney();
//        $isMoneyEnough  = $refundMoneyBiz->checkRefundMoney($ordernum, $orderInfo, $cancelNum, $isSelfReceive,false,$refundAmount);
//        if (!$isMoneyEnough) {
//            if(!empty($serialNums)){
//                $orderTicketDiscounts = new OrderTicketDiscounts();
//                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
//            }
//            pft_log('order_refund/debug',
//                json_encode(['checkRefundMoney', $isMoneyEnough, $orderInfo, $cancelNum, $isSelfReceive]));
//            return $this->orderReturn(0, '', ['err_code' => OrderConst::err222]);
//        }

        //预售券产品：获取处于部分兑换的数量
        $exchangePartExchangeNum = $this->_getExchangePartExchangeNum($ordernum, $ticketInfo);

        //整理订单取消状态、金额等数据
        $orderRefundInfo = $this->_handleRefundDetail($ordernum, $cancelNum, $cancelType, $orderInfo,$branchIsUsedNum,$refundAmount, $exchangePartExchangeNum);

        $originMoney     = $orderRefundInfo['originMoney'];
        $validMoney      = $orderRefundInfo['validMoney'];
        $trackAction     = $orderRefundInfo['trackAction'];
        $validNum        = $orderRefundInfo['validNum'];
        $originNum       = $orderRefundInfo['originNum'];
        $leftNum         = $orderRefundInfo['leftNum'];
        $status          = $orderRefundInfo['status'];
        //门票码状态判断
        if ($cancelType == 'common' && $status != CommonOrderStatus::CANCELED_CODE){  //普通退票的情况订单最后状态不是取消的情况
            $touristInfoCheckRes = $this->_checkRefundTouristStatus($ordernum,$appointIdxList,$appointIdxType,$cancelChannel);
            if ($touristInfoCheckRes['code'] != 200){
                if(!empty($serialNums)){
                    $orderTicketDiscounts = new OrderTicketDiscounts();
                    $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
                }
                return $touristInfoCheckRes;
            }
        }

        //整理门票码的状态
        $orderTouristRes = $this->_handleRefundTourist($ordernum, $cancelNum, $leftNum, $cancelType, $opId, $orderInfo,
            $cancelChannel, $ticketSnapshot, $appointIdxData,$isAuditPass,$cancelTerminal,$afterSaleNum, $appointIdxType);
        if ($orderTouristRes['code'] != 200) {
            if(!empty($serialNums)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            }
            return $orderTouristRes;
        }
        $refundIdxList    = $orderTouristRes['data']['refundIdxList'];
        $canTakeNum       = $orderTouristRes['data']['canTakeNum'];
        $touristTrackList = $orderTouristRes['data']['touristTrackList'];
        //这个取消的门票码带到异步任务里面去
        $refundIdx        = $orderTouristRes['data']['refundIdx'];
        if ($usedRefundFix) {
            $personInfoList   = $orderTouristRes['data']['personInfoList'];
        } else {
            $personInfoList = [];
        }
        $refundOtherData  = [
            'refundIdxList'    => $refundIdxList,
            'canTakeNum'       => $canTakeNum,
            'touristTrackList' => $touristTrackList,
        ];
        //如果是取消订单而且没有传身份证的话，使用未使用的身份证
        if ($leftNum == 0 && !$personIdList) {
            $personIdList = $availableIdcardArr;
        }

        //预售券产品：校验当前退票可退数量 是否预售券可退
        if($isSkipExchangeTicket){
            $checkCancelOrderRes = $this->_checkCancelOrder($ordernum, $ticketInfo,$refundIdx);
            if($checkCancelOrderRes['code'] != 200){
                return $this->orderReturn(0, $checkCancelOrderRes['msg'], ['err_code' => OrderConst::err10001, 'inner_msg' => $checkCancelOrderRes['msg']]);
            }
            if(!empty($checkCancelOrderRes['data'])){
                $refundErrorMsg = '券码 '.implode('、',$checkCancelOrderRes['data']).'的权益处于兑换中，不可取消';
                if($isForceCancel){
                    $refundErrorMsg = '券码 '.implode('、',$checkCancelOrderRes['data']).'是否确认继续强制取消?';
                }
                return $this->orderReturn(0, $refundErrorMsg, ['err_code' => OrderConst::err10001, 'inner_msg' => $refundErrorMsg]);
            }
        }

        if($cancelType == 'revoke') {
            [$status, $revokeCanUseNum, $leftNum] = $this->_getRevokeOrderRefundInfo($orderInfo, $cancelNum, $leftNum, $branchIsUsedNum, $refundIdxList);
            $orderRefundInfo['status'] = $status;
            $refundOtherData['revokeCanUseNum'] = $revokeCanUseNum;
        }

        //获取和插入退票信息
        $receiveType     = $isSelfReceive ? 1 : 0;
        $isNeedRefund    = true;
        $detailsArr      = [];
        $cancelTimestamp = strtotime($cancelTime);

        //真实的退票渠道
        $detailsArr['channel'] = $realCancelChannel;

        if ($autoCancelFee !== false) {
            $detailsArr['autoCancelFee'] = $autoCancelFee;
        }

        //退票身份证信息也要记录退票记录里面
        if ($personIdList) {
            $detailsArr['personIdList'] = $personIdList;
        }
        if ($carriage > 0){
            $detailsArr['carriage']  = $carriage;
        }
        if ($personIndex){
            $detailsArr['personIndex'] = $personIndex;
        }
        if (!empty($personInfoList)){
            $detailsArr['personInfoList'] = $personInfoList;
        }
        $detailsArr['isOnlyCancelNum'] = false;
        if ($appointIdxType['card_num_left_cancel_num'] || $appointIdxType['isOnlyCancelNum']) {
            $detailsArr['isOnlyCancelNum'] = true;
        }
        if (!empty($personInfoList) && !$detailsArr['isOnlyCancelNum']) {
            $detailsArr['personInfoList'] = $personInfoList;
        }
        if ($refundIdx && !$detailsArr['isOnlyCancelNum']){
            $detailsArr['refundIdx'] = $refundIdx;
        }
        //取消前的状态
        $detailsArr['before_status'] = $orderInfo['status'];
        if ($ticketCodeList){
            $detailsArr['ticketCodeList'] = $ticketCodeList;
            //如果是套票门票码 转化成Idx 本质是不需要这个判断，优先处理线上问题。保险
            if($ifpack == 1  && $isCancelSub){
                $personIndex = reset($refundIdx);
            }
        }
        if ($isRollBack || $orderInfo['status'] == CommonOrderStatus::WAIT_PRINTTICKET_CODE){  //待出票的情况不收手续费
            $detailsArr['isRollBack'] = 1;
        }
        //获取套票子票取消的订单金额，特殊需求 lc
        $orderRefundBiz       = new Refund();
        $packRefundOrderMoney = $orderRefundBiz->getPackOrderMoney($ordernum);
        if ($packRefundOrderMoney > 0) {
            $detailsArr['packRefundMoney'] = $packRefundOrderMoney;
        }

        if(!empty($afterSaleNum)){
            $detailsArr['after_sale_num'] = $afterSaleNum;
            $detailsArr['cancel_type'] = $cancelType;
        }
        $detailsJson = json_encode($detailsArr);

        //插入退票信息表
        $refundModel     = new OrderRefund();
        $reqSerialNumber = $this->_genReqSerialNumber($ordernum, $reqSerialNumber);

        //是否供应商开启酒店预定确定拒绝的订单
        $specialData = [];
        if ($refundParams['isScheduledRefuseBack']) {
            $specialData['hotel_scheduled_refuse'] = true;
        }

        //如果是景区产品线退票的订单，在退款的时候做特殊处理
        if ($refundParams['isScenicLineRefund']) {
            $specialData['isScenicLineRefund'] = $refundParams['isScenicLineRefund'];
        }
        //账户余额预检测
        $moneyRefundModel = new Refund();
        $moneyRefundPreCheckRes = $moneyRefundModel->orderTradeRefundPreCheck($ordernum, $reqSerialNumber, $applyDid,
            $trackAction, $validNum, $cancelNum, $originNum, $originMoney, $validMoney, $cancelTimestamp,
            $isNeedRefund, $opId, $receiveType, $detailsJson, 0, 0, $specialData);
        if ($moneyRefundPreCheckRes['code'] != 200) {
            if(!empty($serialNums)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum, $serialNums);
            }
            pft_log('order_refund/debug',
                json_encode(['orderTradeRefundPreCheck', $moneyRefundPreCheckRes, $ordernum, $reqSerialNumber, $applyDid,
                    $trackAction, $validNum, $cancelNum, $originNum, $originMoney, $validMoney, $cancelTimestamp,
                    $isNeedRefund, $opId, $receiveType, $detailsJson]));
            return $this->orderReturn(0, '余额不足', ['err_code' => OrderConst::err222]);
        }

        $refundRecodeRes = $refundModel->addRefundJournal($ordernum, $reqSerialNumber, $applyDid, $trackAction,
            $validNum, $cancelNum,
            $originNum, $originMoney, $validMoney, $cancelTimestamp, $isNeedRefund, $opId, $receiveType, $detailsJson);

        //插入退款记录失败
        if (!$refundRecodeRes) {
            return $this->orderReturn(0, "系统异常，请稍后再试", ['err_code' => OrderConst::err15, 'inner_msg' => '退票记录写入失败']);
        }

        //平台的退款流水号
        $pftSerialNumber = $refundRecodeRes['id'];
        //退票审核 过期的供应商自己取消也要审核  by jackchb 2020 7月细节优化
        if ($isNeedAudit) {
            $refundAuditBiz = new RefundAudit();
            $refundAuditRes = $refundAuditBiz->checkRefundAudit($ordernum, $validNum, $cancelMemberId,
                $modifyType = null,
                $orderInfo, $cancelNum, $pftSerialNumber, $personIdList,$isThirdOrder);
        } else {
            $refundAuditRes = [
                'code' => 100,
                'msg'  => '不需要审核',
            ];
        }
        //计时订单退款处理
        $timeOrderRefund = [
            'is_refund_deposit'  => $isRefundDeposit,
            'is_refund_over_pay' => $isRefundOverPay,
        ];
        //退票审核时拓展参数
        $refundAuditExt = [
            'cancel_audit_remark' => $cancelAuditRemark,
            'discount'=>['serialNumber'=>$arrDiscount['refundTicketSerialNumber']]
        ];
        $moreData = [
            'isCancelSub' => $isCancelSub,
            'isThirdOrder' => $isThirdOrder,
            'isCancelThirdSystem' => $isCancelThirdSystem
        ];
        if ($refundParams['subSid'] && $refundParams['subOpId']){
            $moreData['subSid'] = $refundParams['subSid'];
            $moreData['subOpId'] = $refundParams['subOpId'];
        }
        if (isset($refundParams['isRevokeSmsNotice']) && $refundParams['isRevokeSmsNotice']){
            $moreData['isRevokeSmsNotice'] = $refundParams['isRevokeSmsNotice'];
        }
        if (!empty($afterSaleNum)){
            $moreData['after_sale_num'] = $refundParams['afterSaleNum'];
        }
        if (!empty($isRetry)){
            $moreData['is_retry'] = $refundParams['is_retry'];
        }
        if (!empty($batchRefundMoreIdx)){
            $moreData['batch_refund_more_idx'] = $refundParams['batchRefundMoreIdx'];
        }
        if (!empty($refundParams['isThirdAuditRes'])){
            $moreData['isThirdAuditRes'] = $refundParams['isThirdAuditRes'];
        }
        if (!empty($refundIdx)){
            $moreData['tourist_idx'] = array_map('strval', $refundIdx);
        }
        if($refundParams['mainApprovalCode']){
            $moreData['mainApprovalCode'] = $refundParams['mainApprovalCode'];
        }
        $moreData['is_main_audit'] = $refundAuditRes['data']['is_main_audit'];
        //是否系统统一审核
        $isSysException = $refundAuditRes['code'] == 999 ? true : false;
        if (in_array($refundAuditRes['code'], [200, 999])) {
            //需要退票审核，提交退票审核

            //提交退票审核
            $auditHandleRes = $this->_addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum,
                $opId, $pftSerialNumber, $reqSerialNumber, $applyDid, $personIdList, $isSysException, $timeOrderRefund, $refundAuditExt, $moreData);
            //直接写入退票审核失败
            if ($auditHandleRes['code'] != 200) {
                return $auditHandleRes;
            }

            //退票审核记录写入成功
            $auditInfo = [
                'pft_serial_number' => $pftSerialNumber,
                'req_serial_number' => $reqSerialNumber,
                'cancel_num'        => $cancelNum,
                'left_num'          => $leftNum,
                'valid_num'         => $validNum,
                'valid_money'       => $validMoney,
                'is_system_audit'   => $isSysException,
            ];
            pft_log('refund/approval',json_encode(['thirdApproval_approval_audit',$refundAuditRes,$isCancelThirdSystem,$isThirdOrder,$auditInfo]));
            return $this->orderReturn(1095, '退票审核提交成功', $auditInfo);

        } else if ($refundAuditRes['code'] == 100) {
            //不需要退票审核
            //判断是不是套票的主票
            $isPackageMain = $ifpack == 1 ? true : false;
            //组件审核中心数据
            $objectParams['procInsId'] = $refundParams['processInstanceId'] ?? '';
            $objectParams['reqSerialNumber'] = $refundParams['reqSerialNumber'];
            $objectParams['orderNum'] = $ordernum;
            $refundAuditModel = new RefundAuditModel();
            $refundRecord = $refundAuditModel->isUnderAudit($ordernum,$refundParams['reqSerialNumber'],null,null);
            $approvalOpId = $refundRecord['fxid'] ?? $opId;
            $approvalNodeInfo = (new RefundApprovalService())->getApprovalNodeInfo($objectParams,$approvalOpId,'businessNotifyWait');
            $isSysException = $refundAuditRes['code'] == 999 ? true : false;
            $approvalParams = [
                'approvalNodeInfo' => $approvalNodeInfo,
                'refundAuditParams' => [
                    'ordernum' => $ordernum,
                    'orderInfo' => $orderInfo,
                    'cancelNum' => $cancelNum,
                    'leftNum' => $leftNum,
                    'validNum' => $validNum,
                    'opId' => $approvalOpId,
                    'pftSerialNumber' => $pftSerialNumber,
                    'reqSerialNumber' => $reqSerialNumber,
                    'applyDid' => $applyDid,
                    'personIdList' => $personIdList,
                    'isSysException' => $isSysException,
                    'timeOrderRefund' => $timeOrderRefund,
                    'refundAuditExt' => $refundAuditExt,
                    'moreData' => $moreData,
                    'isForceCancel'=>$isForceCancel,
                    'cancelType' => $cancelType
                ]
            ];
            //如果套票主票，而且需要将子票给取消掉的情况下
            //里面的逻辑是很复杂的
            pft_log('refund/approval',json_encode(['thirdApproval_approval_NoAudit',$approvalParams,$refundRecord],JSON_UNESCAPED_UNICODE));
            if ($isPackageMain && $isCancelSub) {
                //去取消所有的子票
                $packageCancelRes = $this->_cancelPackageChildOrder($ordernum, $subOrderList, $cancelNum, $cancelType,
                    $orderInfo['tnum'], $opId, $personIndex,$isForceCancel,$personIdList,$isRollBack,$approvalParams,'',$personInfoList, $isSyntaxRollBack);
                if ($packageCancelRes['code'] != 200) {
                    //记录异常日志
                    $logParams = [
                        'ac'     => 'cancelPackageChild_payedRefundForApproval',
                        'params' => [$ordernum, $subOrderList, $cancelNum, $cancelType,
                            $orderInfo['tnum'], $opId, $personIndex,$isForceCancel,$personIdList,$isRollBack,[],$pftSerialNumber,$personInfoList,$isSyntaxRollBack],
                    ];
                    $logResponse = [
                        'ac'        => 'cancelPackageChild',
                        'handleRes' => $packageCancelRes,
                        'response'   => $packageCancelRes['msg'],
                    ];

                    $this->recordAnAbnormal($ordernum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                        PackConst::ABNORMAL_REASON_CANCEL,PackConst::ABNORMAL_LINKAGE_YES,$packageCancelRes['msg'],$opId);
                    //返回返回错误
                    return $packageCancelRes;
                }
            }

            //三方退票 $personIndex 转化为 $personIdList
            $refundThirdPersonInfoList = [];
            if($isThirdOrder && $isCancelThirdSystem && $personIndex){
                $subOrderModel = new SubOrderQuery();
                $field = 'orderid,id,tourist,idcard,mobile,print_state,voucher_type,check_state,idx, chk_code';
                $idxRes          = $subOrderModel->getInfoInTouristByOrder($ordernum,
                    $field, 1, ['idx' => $personIndex], '', false);
                if (empty($idxRes)) {
                    return $this->orderReturn(0, "idx参数错误", ['err_code' => OrderConst::err1]);
                }
                if(empty($personIdList) && !empty($idxRes['idcard'])){
                    $personIdList = [$idxRes['idcard']];
                }
                if ($usedRefundFix) {
                    if ($appointIdxType['idCard_new'] && static::getRefundParamsToOpenApiWithQconf()) {
                        $refundThirdPersonInfoList = $personInfoList;
                    }
                }
            }
            if ($usedRefundFix) {
                pft_log('order_refund/debug',json_encode(['usedRefundFix_payedRefundForApproval', $appointIdxType, $cancelNum,$cancelNum, $personInfoList]));
                /**
                 * 兼容取消数量大于实名制数量的场景：转换为按数量退
                 * 下面的||条件兼容
                 * 1.一票种一票脏数据场景，仅一个游客实名制信息，且票数是n(>1)
                 * 2.退票数量大于身份证数量
                 * 3.非按证件类退，非按身份证退，且是仅按数量退
                 * */
                if ($appointIdxType['card_num_left_cancel_num'] || $cancelNum > count($personIdList) ||
                    $cancelNum > count($personInfoList) ||
                    ($appointIdxType['isOnlyCancelNum'] && $appointIdxType['idCard_new'] == false
                        && $appointIdxType['idCard'] == false && $cancelNum > count($personInfoList) )
                ) {
                    $personIdList = [];
                    $refundThirdPersonInfoList = [];
                }
            }
            if ($isThirdOrder && $cancelType == 'revoke') {
                //审核同意，或者撤销直接同意标识判断跳过加入审批，直接进行退票
                if (empty($isAuditAgreed)) {
                    //提交退票审核
                    $auditHandleRes = $this->_addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum,
                        $opId, $pftSerialNumber, $reqSerialNumber, $applyDid, $personIdList, $isSysException, $timeOrderRefund, $refundAuditExt, $moreData);
                    //直接写入退票审核失败
                    if ($auditHandleRes['code'] != 200) {
                        return $auditHandleRes;
                    }

                    //退票审核记录写入成功
                    $auditInfo = [
                        'pft_serial_number' => $pftSerialNumber,
                        'req_serial_number' => $reqSerialNumber,
                        'cancel_num'        => $cancelNum,
                        'left_num'          => $leftNum,
                        'valid_num'         => $validNum,
                        'valid_money'       => $validMoney,
                        'is_system_audit'   => $isSysException,
                    ];
                    pft_log('open_refund',json_encode([$ordernum, '上游票撤销撤改转为平台审核'], JSON_UNESCAPED_UNICODE),3);
                    return $this->orderReturn(1095, '审核提交成功', $auditInfo);
                }
            } else {
                //往三方系统退票
                $thirdRes = $this->_thirdSystemRefund([
                    'ordernum' => $ordernum,
                    'leftNum' => $validNum,
                    'orderInfo' => $orderInfo,
                    'ticketInfo' => $ticketInfo,
                    'isCancelThirdSystem' => $isCancelThirdSystem,
                    'isThirdOrder' => $isThirdOrder,
                    'isNeedAudit' => $isNeedAudit,
                    'thirdPersonIdList' => $personIdList,
                    'codeList' => $codeList,
                    'pftSerialNumber' => $pftSerialNumber,
                    'realCancelChannel' => $realCancelChannel,
                    'cancelNum' => $cancelNum,
                    'isRollBack' => $isRollBack,
                    'refundOpenCode' => $refundOpenCode,
                    'approvalParams' => $approvalParams,
                    'refundPersonInfoList' => $refundThirdPersonInfoList
                ]);
            }
            //三方退票结果：1095=申请退款审核中  1096=拒绝退款申请 200=处理成功  1109=第三方退票请求超时
            if ($thirdRes['code'] == 1096 || $thirdRes['code'] == 1109) {
                //删除退款记录
                $refundModel->delRefundJournal($pftSerialNumber);

                //拒绝信息
                $errorMsg  = $thirdRes['msg'];
                $refundMsg = $errorMsg ? "订单号：【{$ordernum}】拒绝退款申请【{$errorMsg}】" : "订单号：【{$ordernum}】拒绝退款申请";

                $RefundApprovalService = new RefundApprovalService();
                $RefundApprovalService->approvalNotifyForOTA($ordernum,$reqSerialNumber,$opId,2,$refundMsg,false);
                if($orderInfo['ifpack']){
                    //记录异常日志
                    $logParams = [
                        'ac'     => 'thirdAudit_approval',
                        'params' => [$ordernum, $validNum, $orderInfo, $ticketInfo, $isCancelThirdSystem,
                            $isThirdOrder, $isNeedAudit, $personIdList, $codeList, $pftSerialNumber,$realCancelChannel,$cancelNum,
                            $isRollBack,$refundOpenCode,$approvalParams],
                    ];
                    $logResponse = [
                        'ac'        => 'thirdAudit_approval',
                        'handleRes' => $thirdRes,
                        'response'   => $refundMsg,
                    ];

                    $this->recordAnAbnormal($ordernum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_CANCEL,
                        PackConst::ABNORMAL_REASON_UPSTREAM,PackConst::ABNORMAL_LINKAGE_YES,$refundMsg,$opId);
                }
                //拒绝退票申请
                return $this->orderReturn(0, $refundMsg, ['err_code' => OrderConst::err1096]);

            } else if ($thirdRes['code'] == 1095) {
                //需要退票审核，提交退票审核
//                //提交退票审核
//                $auditHandleRes = $this->_addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum,
//                    $opId, $pftSerialNumber, $reqSerialNumber, $applyDid, $personIdList, $isSysException = false, $timeOrderRefund, $refundAuditExt, $moreData);

//                //直接写入退票审核失败
//                if ($auditHandleRes['code'] != 200) {
//                    return $auditHandleRes;
//                }

                //退票审核记录写入成功
                $auditInfo = [
                    'pft_serial_number' => $pftSerialNumber,
                    'req_serial_number' => $reqSerialNumber,
                    'cancel_num'        => $cancelNum,
                    'left_num'          => $leftNum,
                    'valid_num'         => $validNum,
                    'valid_money'       => $validMoney,
                    'is_system_audit'   => false,
                ];
                pft_log('refund/approval',json_encode(['thirdApproval_approval_third_audit',$approvalParams,$isCancelThirdSystem,$isThirdOrder,$auditInfo]));
                return $this->orderReturn(1095, '三方退票审核提交成功', $auditInfo);

            } else {
                RefundRedisManage::delRefundAuditRecordRedisLock($pftSerialNumber);
                if(!empty($cancelAuditRemark)) {
                    $cancelRemark = empty($cancelRemark) ? $cancelAuditRemark : $cancelRemark . '，' . $cancelAuditRemark;
                }

                if($arrDiscount){
                    $refundOtherData['serial_number'] = $arrDiscount['refundTicketSerialNumber'];
                }
                //直接进行后续的退票
                $insideRefundRes = $this->_insideRefund($ordernum, $pftSerialNumber, $cancelType, $realCancelTime,
                    $orderRefundInfo, $cancelChannel,
                    $opId, $cancelTerminal, $cancelRemark, $orderInfo, $ticketInfo, $subOrderList, $personIndex,
                    $personIdList,$ticketCodeList,$carriage,$refundOtherData,$refundIdx, $moreData);

                if ($insideRefundRes['code'] != 200) {
                    //删除退款记录
                    $refundModel->delRefundJournal($pftSerialNumber);

                    //内部退票失败, 直接返回
                    return $insideRefundRes;
                } else {
                    //这边返回退票的追踪记录
                    $refundTrackId = $insideRefundRes['data']['track_id'];
                    $resData = [
                        'pft_serial_number' => $pftSerialNumber,
                        'req_serial_number' => $reqSerialNumber,
                        'order_status'      => $status,
                        'cancel_num'        => $cancelNum,
                        'left_num'          => $leftNum,
                        'valid_num'         => $validNum,
                        'valid_money'       => $validMoney,
                        'track_id'          => $refundTrackId,
                        'op_id'             => $opId,
                        'refund_idx'        => $refundIdx,
                        'idCardCodeParams'  => $idCardCodeParams
                    ];
                    if(!empty($arrDiscount)){
                        $resData['discount'] = $arrDiscount;
                    }
                    //退票成功都检查是否有审批流程，有的话直接通知完成
                    $RefundApprovalService = new RefundApprovalService();
                    $objectParams['reqSerialNumber'] = $reqSerialNumber;
                    $objectParams['orderNum'] = $ordernum;
                    $nodeName = $RefundApprovalService::NOTIFY_NODE_PASS;
                    $judgeResult = $RefundApprovalService->getApprovalNodeInfo($objectParams,$opId,$nodeName);
                    //本来需要判断是否到pass节点，但是数据同步有延迟，只能直接尝试
                    if($judgeResult['has_approval']){
                        $params = [
                            'approvalNoticeType' => $RefundApprovalService::NOTIFY_TYPE_PASS,
                            'processInstanceId' => $judgeResult['processInstanceId'],
                            'operatorID' => $opId
                        ];
                        $RefundApprovalService->approvalNotify($params,['code'=>200,'msg'=>'退票成功']);
                    }

                    //需要同步执行的退票逻辑
                    $this->_syncRefundTask($ordernum, $orderInfo, $ticketInfo, $orderRefundInfo, $refundParams, $refundIdxList, $cancelType);

                    return $this->orderReturn(200, '', $resData);
                }
            }
        } else {
            //检测退票审核的时候，直接报错，不能退票
            $errCode  = $refundAuditRes['code'];
            $errMsg   = $refundAuditRes['msg'];
            $innerMsg = $errMsg . "【{$errCode}】";
            //释放 优惠 锁
            if(!empty($serialNums)){
                $orderTicketDiscounts = new OrderTicketDiscounts();
                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            }
            return $this->orderReturn(0, '', ['err_code' => OrderConst::err1096, 'inner_msg' => $innerMsg]);
        }
    }


    public function thirdSystemRefund($orderNum, $operatorID, $refundParams,$isThirdAuditRes)
    {
        $isCancelThirdSystem = $refundParams['otherData']['isCancelThirdSystem'] ?? false;
        $isThirdOrder = $refundParams['otherData']['isThirdOrder'] ?? false;
        if($isThirdOrder && $isCancelThirdSystem && !$isThirdAuditRes){
            //获取订单信息
            $orderModel   = new OrderTools('localhost');
            $tmpOrderInfo = $orderModel->getInfoForCancel($orderNum);
            if (!$tmpOrderInfo) {
                return $this->orderReturn(0, "订单数据不存在", ['err_code' => OrderConst::err11]);
            }
            $orderInfo    = $tmpOrderInfo['order_info'];

            //获取票信息
            $ticketBiz = new TicketBiz();
            $tid       = $orderInfo['tid'];
            $ticketRes = $ticketBiz->getListForOrderNew($tid);
            if (!$ticketRes['ticket_info']) {
                return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
            }
            $tmpTicketInfo = $ticketRes['ticket_info'];
            $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
            $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);
            $isThirdOrder = $ticketInfo['Mdetails'] == 1 && $ticketInfo['sourceT'] > 0 ? true : false;
            if(!$isThirdOrder){
                return $this->returnData(200, '无需向三方请求');
            }

            //构建请求三方数据
            $refundAuditModel = new RefundAuditModel();
            $refundRecord = $refundAuditModel->getTerminalChangeInfo($orderNum,0);
            //组件审核中心数据
            $objectParams['procInsId'] = $refundParams['processInstanceId'];
            $objectParams['orderNum'] = $orderNum;
            $approvalNodeInfo = (new RefundApprovalService())->getApprovalNodeInfo($objectParams,$operatorID,'businessNotifyWait');

            $personIdList = [];
            $personIndex = '';
            $auditData = json_decode($refundRecord['audit_data'],true) ;
            if(isset($auditData['tourist_idx']) && !empty($auditData['tourist_idx'])){
                $personIndex = reset($auditData['tourist_idx']);
            }

            if($isThirdOrder && $isCancelThirdSystem && $personIndex){
                $subOrderModel = new SubOrderQuery();
                $field = 'orderid,id,tourist,idcard,mobile,print_state,voucher_type,check_state,idx, chk_code';
                $idxRes          = $subOrderModel->getInfoInTouristByOrder($orderNum,
                    $field, 1, ['idx' => $personIndex], '', false);
                if (empty($idxRes)) {
                    return $this->orderReturn(0, "idx参数错误", ['err_code' => OrderConst::err1]);
                }
                if(empty($personIdList) && !empty($idxRes['idcard'])){
                    $personIdList = [$idxRes['idcard']];
                }
            }
            //调用java api获取游客身份信息;非已过期：未验证  已过期statu=2订单：未验证+已过期
            $leftTouristArr = $this->_getOrderLeftTourist($orderNum, $refundParams['modifyNum'], $ticketInfo, $orderInfo);
            //如果是一票一证的门票，就需要获取未使用的码
            $codeList = $leftTouristArr['codeList'];

            //订单已经支付了，需要往三方系统去取消
            $reqParam = [
                'Ordern'       => $orderNum,
                'Tnum'         => $refundRecord['tnum'],
                'Fid'          => $orderInfo['member'],
                'LandId'       => $ticketInfo['landid'],
                'PayStatus'    => $orderInfo['pay_status'],
                'ApplyId'      => $ticketInfo['landid'],
                'IdCardList'   => $personIdList ? implode(',', $personIdList) : '',
                'PftSerialNum' => $refundRecord['system_sn'],
            ];

            if ($codeList) {
                $reqParam['codes'] = implode(',', $codeList);
            }
            if ($refundParams['modifyNum'] > 0){
                $reqParam['cancelNum'] = $refundParams['modifyNum'];
            }
            //对接三方系统的模式
            $interfaceMode = $ticketInfo['sourceT'] == 3 ? 'new' : 'old';
            $thirdSystemLib = new ThirdSystem();
            $refundRes      = $thirdSystemLib->commonRefund($reqParam, $interfaceMode);
            $code = $refundRes['code'];
            $msg  = $refundRes['msg'];
            if($code == 200){
                $isAgree = $approvalNodeInfo['has_approval'] && $approvalNodeInfo['exist_node'] && !$approvalNodeInfo['finish_node'];
                if($isAgree &&  isset($approvalNodeInfo['processInstanceId'])){
                    //发起时绑定了三方 ,不管现在是不是三方票 跳过这个流程
                    $refundApprovalService = new RefundApprovalService();
                    $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
                    $status = $refundApprovalService::NOTIFY_STATUS['agree'];
                    $sid = $ticketInfo['apply_did'];
                    $refundApprovalService->notifyApprovalNode($noticeType,
                        $approvalNodeInfo['processInstanceId'],$status,$sid,'三方退票成功');
                }
                pft_log('refund/approval',json_encode(['thirdApproval',$approvalNodeInfo]));
            }
            return $this->returnData($code, $msg);
        }
        return $this->returnData(200, '无需向三方请求');
    }

    /**
     * 未支付订单退票
     * <AUTHOR>
     * @date 2019-07-06
     *
     * @param  string  $ordernum  订单号
     * @param  array  $orderInfo  订单相关信息
     * @param  array  $refundParams  退票请求参数，BaseRefundCheck->baseCheck初步处理参数
     *
     * @return array
     */
    private function _nopayRefund($ordernum, $orderInfo, $ticketInfo, $memberInfo, $subOrderList, $refundParams)
    {
        //校验过的退票参数
        $ordernum          = $refundParams['ordernum'];
        $cancelNum         = $refundParams['cancelNum'];
        $opId              = $refundParams['opId'];
        $cancelChannel     = $refundParams['cancelChannel'];
        $cancelType        = $refundParams['cancelType'];
        $reqSerialNumber   = $refundParams['reqSerialNumber'];
        $autoCancelFee     = $refundParams['autoCancelFee'];
        $cancelRemark      = $refundParams['cancelRemark'];
        $cancelTerminal    = $refundParams['cancelTerminal'];
        $personIndex       = $refundParams['personIndex'];
        $personIdList      = $refundParams['personIdList'];
        $cancelTime        = $refundParams['cancelTime'];
        $isRollBack          = $refundParams['isRollBack'];
        $realCancelChannel = $refundParams['realCancelChannel'];
        $ticketCodeList      = $refundParams['ticketCodeList'];
        $ticketSnapshot      = $refundParams['ticketSnapShot'];
        $isForceCancel       = $refundParams['isForceCancel'];
        $isAuditPass         = $refundParams['isAuditPass'];
        $refundPersonInfoList = $refundParams['personInfoList'] ?? [];
        if (empty($personIdList) && $refundPersonInfoList) {
            foreach ($refundPersonInfoList as $personInfo) {
                if (!empty($personInfo['idcard'])) {
                    $personIdList[] = $personInfo['idcard'];
                }
                if (!empty($personInfo['idx'])) {
                    $personIndex[] = $personInfo['idx'];
                }
            }
        }
        pft_log('order_refund/debug',json_encode(['nopayRefund_refundParams', $ordernum, $cancelNum, $reqSerialNumber, $personIndex, $personIdList,$refundPersonInfoList]));
        $personParams = [
            'personIndex'    => $personIndex,
            'personIdList'   => $personIdList,
            'ticketCodeList'  =>$ticketCodeList,
            'personInfoList'  => $refundPersonInfoList
        ];
        //预检信息组装
        $preCheckParams = [
            'p_type'          => $ticketInfo['p_type'],
            'cancel_type'     => $cancelType,
            'is_force_cancel' => $isForceCancel,
            'ticket_snapshot' => $ticketSnapshot,
            'ticket_info'     => $ticketInfo,
            'cancel_num'      => $cancelNum,
            'tnum'            => $orderInfo['tnum'],
            'order_status'    => $orderInfo['status'],
            'ifpack'          => $orderInfo['ifpack'],
        ];
        $refundCheckBiz = new BaseRefundCheck();
        $appointIdxRes   = $refundCheckBiz->getOrderTouristByCondition($ordernum,$cancelChannel,$personParams, $preCheckParams);
        pft_log('order_refund/debug', json_encode(['getInfoInTouristByOrder_old', $appointIdxRes,$personParams, $ordernum]));
        $appointIdxData = $appointIdxRes['data']['arrIdx'];
        //逻辑取消时间处理，如果外部没有传，就取当前的时间
        $realCancelTime = date('Y-m-d H:i:s');
        $cancelTime     = $cancelTime ? $cancelTime : $realCancelTime;
        //是否是温泉系统订单
        $isPresumingOrder = $this->checkIsPresumingOrder($ordernum,$orderInfo);

        //没有支付的统一都是非三方
        $isThirdOrder = false;
        $applyDid     = $ticketInfo['apply_did'];
        $checkBiz       = new BaseRefundCheck();
        //其他系统的判断
        $otherSystemCheckRes = $checkBiz->otherSystemCheck($ordernum, $ticketInfo, $cancelNum, $cancelType, $orderInfo,$realCancelChannel,$isPresumingOrder,$isRollBack);
        if ($otherSystemCheckRes['code'] != 200) {
            return $otherSystemCheckRes;
        }
        //初步判断订单是否可以取消
        $checkBiz       = new BaseRefundCheck();
        $statusCheckRes = $checkBiz->statusCheck($ordernum, $cancelNum, $cancelType, $orderInfo, $ticketInfo,
            $personIdList, $isThirdOrder,0,$isPresumingOrder);
        if ($statusCheckRes['code'] != 200) {
            return $statusCheckRes;
        }
        $arrDiscount = self::_LockRefundTouristForDiscount($ordernum, self::OP_REFUND, $cancelNum);
        //优惠信息重新计算 退票金额以及余额
        $refundAmount = false;
        if(!empty($arrDiscount)){
            //未支付，手续费不做判断
//            //手续费
//            $refundBiz = new Refund();
//            $realFee = $refundBiz->getFeeByOrderNum($ordernum,$cancelNum,$orderInfo['status'],$isRollBack,$autoCancelFee);
//            //检测余额够不够
//            if($realFee > 0 && $realFee <= $arrDiscount['refundAmount']){
//                $orderTicketDiscounts = new OrderTicketDiscounts();
//                $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($arrDiscount['refundTicketSerialNumber']);
//                $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
//                return $this->orderReturn(0, '退款金额必须大于0元才允许取消(全部&部分)订单', ['$realFee'=>$realFee,'refundAmount'=>$arrDiscount['refundAmount']]);
//            }
            $refundAmount = $arrDiscount['refundAmount'];
        }

        //未支付情况下, $exchangePartExchangeNum 值只会为0
        $exchangePartExchangeNum = 0;

        //整理订单取消状态、金额等数据
        $orderRefundInfo = $this->_handleRefundDetail($ordernum, $cancelNum, $cancelType, $orderInfo,0,$refundAmount, $exchangePartExchangeNum);

        $originMoney     = $orderRefundInfo['originMoney'];
        $validMoney      = $orderRefundInfo['validMoney'];
        $trackAction     = $orderRefundInfo['trackAction'];
        $validNum        = $orderRefundInfo['validNum'];
        $originNum       = $orderRefundInfo['originNum'];
        $leftNum         = $orderRefundInfo['leftNum'];
        $status          = $orderRefundInfo['status'];

        $orderTouristRes = $this->_handleRefundTourist($ordernum,$cancelNum,$leftNum,$cancelType,$opId,$orderInfo,$cancelChannel,$ticketSnapshot,$appointIdxData,false,$cancelTerminal);
        if ($orderTouristRes['code'] != 200){
            return $orderTouristRes;
        }
        $refundIdxList  = $orderTouristRes['data']['refundIdxList'];
        $canTakeNum      = $orderTouristRes['data']['canTakeNum'];
        $touristTrackList      = $orderTouristRes['data']['touristTrackList'];
        //这个取消的门票码带到异步任务里面去
        $refundIdx        = $orderTouristRes['data']['refundIdx'];
        $refundOtherData = [
            'refundIdxList' => $refundIdxList,
            'canTakeNum'    => $canTakeNum,
            'touristTrackList' => $touristTrackList
        ];
        //获取和插入退票信息
        $receiveType     = 0;
        $isNeedRefund    = false;
        $detailsData     = ['channel' => $realCancelChannel];
        $detailsJson     = json_encode($detailsData);
        $cancelTimestamp = strtotime($cancelTime);

        //插入退票信息表
        $refundModel     = new OrderRefund();
        $reqSerialNumber = $this->_genReqSerialNumber($ordernum, $reqSerialNumber);
        $refundRecodeRes = $refundModel->addRefundJournal($ordernum, $reqSerialNumber, $applyDid, $trackAction,
            $validNum, $cancelNum,
            $originNum, $originMoney, $validMoney, $cancelTimestamp, $isNeedRefund, $opId, $receiveType, $detailsJson);

        //插入退款记录失败
        if (!$refundRecodeRes) {
            return $this->orderReturn(0, "系统异常，请稍后再试", ['err_code' => OrderConst::err15, 'inner_msg' => '退票记录写入失败']);
        }

        //平台的退款流水号
        $pftSerialNumber = $refundRecodeRes['id'];
        $refundOtherData['serial_number'] = $arrDiscount['refundTicketSerialNumber'];
        $moreData = [];
        if ($refundParams['subSid'] && $refundParams['subOpId']){
            $moreData['subSid'] = $refundParams['subSid'];
            $moreData['subOpId'] = $refundParams['subOpId'];
        }
        //开始实际的取消
        $insideRefundRes = $this->_insideRefund($ordernum, $pftSerialNumber, $cancelType, $realCancelTime,
            $orderRefundInfo, $cancelChannel,
            $opId, $cancelTerminal, $cancelRemark, $orderInfo, $ticketInfo, $subOrderList, $personIndex, $personIdList,$ticketCodeList,0,$refundOtherData,$refundIdx, $moreData);

        if ($insideRefundRes['code'] != 200) {
            //删除退款记录
            $refundModel->delRefundJournal($pftSerialNumber);
            $orderTicketDiscounts = new OrderTicketDiscounts();
            $serialNums = $orderTicketDiscounts->_recoverDiscountSerialNumber($refundOtherData['serial_number']);
            $orderTicketDiscounts->releaseLock($ordernum,$serialNums);
            //内部退票失败, 直接返回
            return $insideRefundRes;
        } else {
            //这边返回退票的追踪记录
            $refundTrackId = $insideRefundRes['data']['track_id'];

            $resData = [
                'pft_serial_number' => $pftSerialNumber,
                'req_serial_number' => $reqSerialNumber,
                'order_status'      => $status,
                'cancel_num'        => $cancelNum,
                'left_num'          => $leftNum,
                'valid_num'         => $validNum,
                'valid_money'       => $validMoney,
                'track_id'          => $refundTrackId,
                'op_id'             => $opId,
                'refund_idx'        => $refundIdx,
            ];
            if(!empty($arrDiscount)){
                $resData['discount'] = $arrDiscount;
            }

            //需要同步执行的退票逻辑
            $this->_syncRefundTask($ordernum, $orderInfo, $ticketInfo, $orderRefundInfo, $refundParams, $refundIdxList);

            return $this->orderReturn(200, '', $resData);
        }
    }

    /**
     * 平台内部退票接口
     * <AUTHOR>
     * @date   2019-07-07
     *
     * @param  string  $ordernum  订单号
     * @param  string  $pftSerialNumber  平台退款流水号
     * @param  string  $cancelType  取消类型
     * @param  string  $realCancelTime  实际取消实际
     * @param  array  $orderRefundInfo  整理订单取消状态、金额等数据
     *             {
     *                 'leftNum'     => 5, //剩余可用票数
     *                 'validNum'    => 7, //总的有效票数：验证票数 + 剩余票数
     *                 'cancelNum'   => 3, //退的票数
     *                 'status'      => 7, //订单最后的状态
     *                 'dstatus'     => 5, // 4=取消, 5=修改
     *                 'trackAction' => 1, //取消类型 1=修改,2=取消,6=撤销,7=撤改
     *                 'validMoney'  => 100, //总的有效金额
     *                 'originMoney' => 200, //取消前的金额
     *                 'originNum'   => 10, //取消前的票数
     *             }
     * @param  int  $cancelChannel  取消渠道
     * @param  int  $opId  操作用户
     * @param  int  $terminal  取消终端
     * @param  string  $trackMsg  取消备注信息
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票信息
     * @param  array  $subOrderList  套票中子票订单列表
     * @param  int  $personIndex  一票一码取消的游客序号
     * @param  array  $personIdList  取消身份证列表
     * @param  array  $ticketCodeList  门票码
     * @param  int  $carriage  特产的运费
     *
     * @return array
     */
    private function _insideRefund($ordernum, $pftSerialNumber, $cancelType, $realCancelTime, $orderRefundInfo, $cancelChannel = 0,
        $opId, $terminal, $trackMsg, $orderInfo, $ticketInfo, $subOrderList = [], $personIndex = 0, $personIdList = [],$ticketCodeList = [], $carriage = 0,$refundOtherData = [],$refundIdx=[], $moreData = [])
    {
        //订单数据
        $leftNum          = $orderRefundInfo['leftNum'];
        $validNum         = $orderRefundInfo['validNum'];
        $cancelNum        = $orderRefundInfo['cancelNum'];
        $status           = $orderRefundInfo['status'];
        $dstatus          = $orderRefundInfo['dstatus'];
        $trackAction      = $orderRefundInfo['trackAction'];
        $validMoney       = $orderRefundInfo['validMoney'];
        $originMoney      = $orderRefundInfo['originMoney'];
        $originNum        = $orderRefundInfo['originNum'];
        $revokeCanUseNum  = $refundOtherData['revokeCanUseNum'] ?? -1; //剩余可用数, 由于撤改记录的leftNum有问题, 单独给撤改开一个字段储存
        $beforeStatus     = $orderInfo['status'];     //取消前状态
        $touristTrackList = $refundOtherData['touristTrackList'];
        $canTakeNum       = $refundOtherData['canTakeNum'];
        $canRefundNum     = $orderInfo['can_refund'] == -1 ? -1 : ($cancelType == 'revoke' ? $orderInfo['can_refund'] : $orderInfo['can_refund'] - $cancelNum);
        $refundIdxList    = $refundOtherData['refundIdxList'];
        $serialNumber    = $refundOtherData['serial_number'] ?? '';
        //订单数据
        $data        = ['status' => $status, 'tnum' => $validNum];
        $where       = ['status' => $orderInfo['status']];
        $applyParams = [
            'canTake'   => $canTakeNum,
            'canRefund' => $canRefundNum,
        ];
        if ($validMoney != 0) {
            //取消了部分票，需要更新订单的总的有效金额
            $data['totalmoney'] = $validMoney;
            $data['onsale']     = 2;
        }
        else{
            //优惠特殊处理
            if(!empty($serialNumber)){
                $data['totalmoney'] = $validMoney;
                $data['onsale']     = 2;
            }
        }

        //if ($leftNum == 0) {
        //    //如果已经没有可用票了，就更新门票取消时间
        //    $data['ctime'] = $realCancelTime;
        //}

        //这边调整成，只要退票了就更新订单里面的ctime
        $data['ctime'] = $realCancelTime;

        // $orderTrackArr = load_config('cancel_modify_track_map');
        // $trackSource   = isset($orderTrackArr[$cancelChannel]) ? $orderTrackArr[$cancelChannel] : \Model\Order\OrderTrack::SOURCE_INSIDE_SOAP;
        $trackSource = $cancelChannel > 0 ? $cancelChannel : \Model\Order\OrderTrack::SOURCE_INSIDE_SOAP;

        $orderApi     = new \Business\JavaApi\Order\OrderHandle();
        $handlerModel = new OrderHandler();

        if ($opId) {
            $addon = ['confirmTime' => date('Y-m-d H:i:s'), 'confirmOp' => $opId, 'dstatus' => $dstatus];
        } else {
            $addon = [];
        }
        $statusCancelRes = $orderApi->orderChangeOrCancel($ordernum, $opId, $data, $addon, $orderInfo['status'],$applyParams,$touristTrackList, $moreData);
        if ($statusCancelRes['code'] != 200) {
            //订单状态修改失败
            pft_log('refund/error',json_encode(['orderChangeOrCancel', $statusCancelRes]));
            return $this->orderReturn(0, '订单状态修改失败', ['err_code' => OrderConst::err15]);
        }
        $subSid = $moreData['subSid'] ?? 0;
        $subOpId = $moreData['subOpId'] ?? 0;
        //特产运费
        if ($carriage > 0){
            //把之前的product_ext数据拿出来加入以下已退运费
            $productExtInfo = json_decode($orderInfo['product_ext'],true);
            if ($productExtInfo){
                if (isset($productExtInfo['refundCarriage'])){
                    $refundCarriageExtInfo['refundCarriage'] = $productExtInfo['refundCarriage'] + $carriage;
                }else{
                    $refundCarriageExtInfo['refundCarriage'] = $carriage;
                }
            }else{
                $refundCarriageExtInfo = ['refundCarriage' => $carriage];
            }
            $detailsData  = ['productExt' => json_encode($refundCarriageExtInfo)];
            $orderDetailApi  = new OrderDetailUpdate();
            $updateDetailRes = $orderDetailApi->orderDetailInfoUpdate($ordernum,$detailsData, $opId, $subSid, $subOpId);
            if ($updateDetailRes['code'] != 200) {
                pft_log('carriage/error',json_encode($productExtInfo));
            }
        }


        //累加订单的退票数
        $isNeedRecover = $cancelType == 'revoke' ? true : false;
        $increRes      = (new \Model\Order\OrderSubmit())->increCount($ordernum, 'refund', $cancelNum, $isNeedRecover);

        if ($beforeStatus == CommonOrderStatus::WAIT_PRINTTICKET_CODE){
            $trackMsg = $trackMsg ? $trackMsg.',待出票状态取消订单' : '待出票状态取消订单';
        }

        //添加追踪记录
        $trackModel = new \Model\Order\OrderTrack();
        $extContent = [];
        if($revokeCanUseNum >= 0) {
            $extContent['revoke_can_use'] = $revokeCanUseNum;
        }
        if($serialNumber){
            $extContent['serial_number'] = $serialNumber;
        }
        if ($subSid && $subOpId){
            $extContent['subSid'] = $subSid;
            $extContent['subOpId'] = $subOpId;
        }
        //记录优惠信息明细 - 拓展字段
        $trackId    = $trackModel->addTrack($ordernum, $trackAction, $orderInfo['tid'], $cancelNum, $leftNum,
            $trackSource, 0, $terminal, 0, $opId, 0, $realCancelTime, $trackMsg, 0, false, $extContent);

        //修改退票表状态为退票成功
        $refundModel     = new OrderRefund();
        $refundRecordRes = $refundModel->updateRefundJournal($pftSerialNumber, OrderRefund::REFUND_ORDER_SUCCESS,
            $memo = '退票成功', $trackId);

        //更新游客表数据
        if ($refundIdxList){
            pft_log('order_refund/debug', json_encode(['touristInfoUpdate_params', $refundIdxList, $ordernum]));
            $updateIdxList = [];
            //按状态分类批量更新
            foreach ($refundIdxList as $key => $value){
                $updateIdxList[$value['status']][] = $value['idx'];
            }
            $api = new \Business\JavaApi\Order\OrderTouristUpdate();
            foreach ($updateIdxList as $k => $v) {
                $data = [
                    'checkState' => $k,
                ];
                $map  = [
                    'idxs' => $v,
                ];
                pft_log('order_refund/debug', json_encode(["touristInfoUpdate_params", $data, $map, $ordernum]));
                $api->touristInfoUpdate($ordernum, $data, $map);
            }
        }
//        //获取所有的游客信息
//        $orderIdxList = $handlerModel->getOrderToristIdx($ordernum);
//        if ($orderIdxList) {
//            if ($ticketInfo['batch_check'] == 2 || $ticketInfo['batch_check'] == 3 || $ticketInfo['tourist_info'] == 2) {
//                //一票一码 + 一票一证 + 实名制
//                if ($personIndex) {
//                    //有指定取消哪个游客
//                    $handlerModel->updateOrderTouristInfoState($ordernum, '', 2, false, $personIndex);
//                }else if ($ticketCodeList){
//                    $api = new \Business\JavaApi\Order\OrderTouristUpdate();
//
//                    $data = [
//                        'checkState' => 2
//                    ];
//                    $map = [
//                        'checkCode' => $ticketCodeList
//                    ];
//                    $api->touristInfoUpdate($ordernum, $data, $map);
//                } else if ($personIdList) {
//                    //如果有传身份证的话，取消身份证对应的游客
//                    $mainOrdernum = $orderInfo['concat_id'] ?: $ordernum;
//
//                    //更新订单游客信息
//                    $api = new \Business\JavaApi\Order\OrderTouristUpdate();
//
//                    $data = [
//                        'checkState' => 2
//                    ];
//                    $map = [
//                        'idcards' => $personIdList
//                    ];
//                    $api->touristInfoUpdate($mainOrdernum, $data, $map);
//
//                    if ($orderInfo['ifpack'] == 1 && $subOrderList) {
//                        //身份证取消套票的时候，同时取消该身份证所有子票
//                        foreach ($subOrderList as $childOrderId) {
//                            $api->touristInfoUpdate($childOrderId['ordernum'], $data, $map);
//                        }
//                    }
//                } else {
//                    $cutIdxList = array_slice($orderIdxList, 0, $cancelNum);
//                    $handlerModel->updateOrderTouristInfoState($ordernum, '', 2, false, $cutIdxList);
//                }
//            } else {
//                //要等订单都取消的时候，采取把游客信息取消
//                if ($leftNum == 0) {
//                    $cutIdxList = array_slice($orderIdxList, 0, $cancelNum);
//                    $handlerModel->updateOrderTouristInfoState($ordernum, '', 2, false, $cutIdxList);
//                }
//            }
//        }

        $aidsArr = $orderInfo['aids'] ? explode(',', $orderInfo['aids']) : [$orderInfo['apply_did']];
        if (!isset($aidsArr[1])) {
            // visitors 1=游客 2=分销商 3=自供自销
            $aidsArr[] = $orderInfo['visitors'] == 2 ? $orderInfo['member'] : $orderInfo['aid'];
        }
        $reinstateData = [];
        if ($ticketInfo['p_type'] == 'H') {
            $series        = unserialize($orderInfo['series']);  //0场馆id 1场次id 2分区id 3座位id 4演出日期 5座位号
            $roundId       = $series[1] ?? 0;
            $zoneId        = $series[2] ?? 0;
            $storageChange = $series[8] ?? [];
            $reserveModel  = $storageChange['isReserveMode'] ?? 0;

            $roundInfo     = (new \Model\Product\Show())->getRoundInfo($roundId);

            //先扣新场次的库存
            if ($roundInfo['is_new'] && $aidsArr && $zoneId) {

                //查询终端订单变动表是否存在改签记录
                $auditModel   = new \Model\Order\RefundAuditModel();
                $changeInfos  = $auditModel->getTicketChangingOrders([$ordernum], 'ordernum,dstatus');
                $changeRecord = false;
                if ($changeInfos) {
                    $changeInfo = array_shift($changeInfos);
                    if (in_array($changeInfo['dstatus'], [1])) {
                        //同意改签的记录
                        $changeRecord = true;
                    }
                }

                //需要判断下 非预留模式 或者有改签成功的情况下  需要退分销库存 否则库存数会对不上
                if (!$reserveModel || $changeRecord) {
                    $reinstateData = [
                        'userList'    => [['sid' => $aidsArr[0], 'fid' => $aidsArr[1]]],
                        'itemTag'     => \Business\PftShow\DisStorage::createItemTag($zoneId, $roundId),
                        'productType' => \Business\PftShow\DisStorage::PRODUCT_TYPE,
                        'changeNum'   => $cancelNum,
                        'changeLock'  => false,
                        'storageDate' => date('Y-m-d', strtotime($orderInfo['playtime'])),
                    ];
                }
            }
        } else if (in_array($orderInfo['product_type'], ['A', 'F']) && $aidsArr) {
            // 分销库存退回所需参数构造
            $storageDate = date('Y-m-d', strtotime($ticketInfo['pre_sale'] == 1 ? $orderInfo['ordertime'] : $orderInfo['playtime']));
            $itemTagDate = date('Ymd', strtotime($ticketInfo['pre_sale'] == 1 ? $orderInfo['ordertime'] : $orderInfo['playtime']));
            $reinstateData = [
                'changeLock' => false,
                'userList' => [['sid' => $aidsArr[0], 'fid' => $aidsArr[1]]],
                'itemTag' => "SKU_{$orderInfo['tid']}_{$itemTagDate}",
                'changeNum' => $cancelNum,
                'storageDate' => $storageDate,
                'productType' => 2
            ];
        }

        //TODO 后期统一用Business下面的orderRefundStorage（封装的）方法（期票那边先用用，这边保险占时不迁移过去）
        $refundBiz = new Refund();
        $refundBiz->orderRefundStorage($ordernum, $cancelNum,$opId,'refund',$orderInfo, $subSid, $subOpId, $reinstateData);
        //库存回退
//        $pftSerialNum = serialHashKey($ordernum);
//        $playtime     = substr($orderInfo['playtime'], 0, 10);
//        $productExt   = json_decode($orderInfo['product_ext'],true);
//        $orderExt     = json_decode($orderInfo['ext_content'], true);
//        if (isset($productExt['reservationOperateTime']) && $productExt['reservationOperateTime']) {   //已预约取消的
//            $sectionTimeId  = 0;
//            $sectionTimeStr = '';
//            if (isset($orderExt['sectionTimeId'])) {
//                $sectionTimeId  = $orderExt['sectionTimeId'];
//                $sectionTimeStr = $orderExt['sectionTimeStr'];
//                if ($orderInfo['ifpack'] == 2) {
//                    $mainOrderInfo = $handlerModel->getOrderInfo($orderInfo['pack_order'], 'ordermode');
//                    $tmpChannel    = $mainOrderInfo['ordermode'];
//                } else {
//                    $tmpChannel = $orderInfo['ordermode'];
//                }
//            }else{
//                    $tmpChannel = $orderInfo['ordermode'];
//             }
//            //退预约库存用playtime退
//            $orderReservationApi = new OrderReservation();
//            $resevationStorage   = $orderReservationApi->orderRefundReservation($ordernum, $orderInfo['tid'],
//                $orderInfo['member'], $tmpChannel
//                , $playtime, $cancelNum, $pftSerialNum, $opId,$sectionTimeId, $sectionTimeStr);
//            //退普通库存用ordertime退
//            $storageres = StorageApi::reinstateStorage($ordernum, $pftSerialNum, $orderInfo['tid'], $cancelNum,
//                $orderInfo['ordertime']);
//        } elseif (isset($orderExt['sectionTimeId'])) {
//            //需要传主票的渠道
//            if ($orderInfo['ifpack'] == 2) {
//                $mainOrderInfo = $handlerModel->getOrderInfo($orderInfo['pack_order'], 'ordermode');
//                $tmpChannel    = $mainOrderInfo['ordermode'];
//            } else {
//                $tmpChannel = $orderInfo['ordermode'];
//            }
//            $storageres = StorageApi::reinstateStorageWithTimeId($ordernum, $pftSerialNum, $orderInfo['tid'],
//                $cancelNum,
//                $playtime, $tmpChannel, $orderExt['sectionTimeId'], $orderExt['sectionTimeStr']);
//        } else {
//            if ($ticketInfo['pre_sale'] == 1){  //固定时间段
//                $playtime = $orderInfo['ordertime'];
//            }
//            $storageres = StorageApi::reinstateStorage($ordernum, $pftSerialNum, $orderInfo['tid'], $cancelNum,
//                $playtime);
//        }
        $queryParams = [[$ordernum],false,$refundIdx];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
            'queryOrderTouristInfoByOrderId', $queryParams);
        pft_log('order_refund/debug', json_encode(['orderTouristInfo', $ordernum, $queryRes, $cancelType]));
        $touristInfo = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $touristInfo = $queryRes['data'];
        }
        $idCards = array_column($touristInfo,'idcard');
        //限购回退
        $refundApi = new \Business\JavaApi\Order\Refund();
        if ($ticketInfo['buy_limit'] > 0) {
            //平台下单限购回滚
            $refundApi->rollbackLimitBuy($ordernum, $cancelNum, $idCards);
        }
        //购票限制(黑白名单) 限购回滚
        $refundApi->rollbackLandLimitBuy($ordernum, $cancelNum, $idCards);

        //TODO:这个逻辑很奇葩，正常应该是在在退票审核的逻辑里面做这个逻辑
        //暂时为了和之前的退票逻辑保持一致，这边也加这个逻辑
        //  $refundBiz = new \Model\Order\RefundAuditModel();
        //  $refundBiz->updateAudit($ordernum, 1, '系统处理');

        if($cancelType == 'revoke' ){
            $isRevokeSmsNotice = $moreData['isRevokeSmsNotice'] ?? 0;
            if($isRevokeSmsNotice){
                $notify =  new \Library\MessageNotify\OrderNotify($ordernum,0,$orderInfo['apply_did'],$orderInfo['contacttel'],$orderInfo['pid'],$orderInfo['apply_did']);
                $smsOrderNum = empty($orderInfo['remotenum']) ? $ordernum : $orderInfo['remotenum'];
                if(in_array($orderInfo['ordermode'],['56']) ){
                    //小程序下单，非上游票也会有远端订单号，故此特殊处理
                    $smsOrderNum = $ordernum;
                }
                $content =['{1}'=>"您退款的订单号：{$smsOrderNum}，景区已审核并提交原购买平台处理，具体时间以原购买平台为准，如有疑问请联系原购买平台客服处理。"];
                $notify->commonSendSmsForCustomSign($content,4,'撤销撤改通知短信');
            }
        }
        //返回数据
        return $this->orderReturn(200, '订单取消成功', ['track_id' => $trackId]);
    }

    /**
     * 获取操作用户的数据
     * <AUTHOR>
     * @date   2019-07-01
     *
     * @param  int  $opId
     *
     * @return array
     */
    private function _getOpInfo($opId)
    {
        $memberModel = new Member();
        $memberInfo  = $memberModel->getMemberInfo($opId);
        if (!$memberInfo) {
            //用户不存在
            return $this->orderReturn(0, '退票用户不存在', ['err_code' => OrderConst::err11]);
        }

        //取消人员类型
        $memberType = $memberInfo['dtype'];

        //如果是员工账号，获取主账号信息
        if ($memberType == 6) {
            $parentInfo = $memberModel->getStaffBySonId($opId);
            if (!$parentInfo) {
                //获取不到主账号信息
                return $this->orderReturn(0, '获取不到主账号信息', ['err_code' => OrderConst::err11]);
            }

            $cancelMemberId = $parentInfo['parent_id'];
        } else {
            $cancelMemberId = $opId;
        }

        //是不是管理员
        $isSuper = $cancelMemberId == 1 ? true : false;

        $resData = [
            'memberInfo'     => $memberInfo,
            'memberType'     => $memberType,
            'cancelMemberId' => $cancelMemberId,
            'opId'           => $opId,
            'isSuper'        => $isSuper,
        ];

        return $this->orderReturn(200, '', $resData);
    }

    /**
     * 是否有退票的权限
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum  订单号
     * @param  int  $tid  门票ID
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票信息
     * @param  int  $cancelMemberId  取消的主账号ID
     * @param  int  $memberType  用户类型
     * @param  int  $cancelChannel  取消渠道
     * @param  int  $cancelType  取消类型
     * @param  bool  $isForceCancel  是否强制取消
     * @param  array  $subOrderList  子票订单列表
     * @param  array  $refundParams  退票参数
     *
     * @return array
     */
    public function _isHaveCancelAuth($ordernum, $tid, $orderInfo, $ticketInfo, $cancelMemberId, $memberType, $cancelChannel,
    $cancelType, $isForceCancel = false, $subOrderList = [],$isAuditPass = false,$afterSaleNum = '', $refundParams = [])
    {
        $checkBiz = new BaseRefundCheck();
        if(($cancelChannel == \Library\Constants\OrderConst::AFTER_SALE_REFUND && $cancelType == 'revoke')
            || (!empty($afterSaleNum) && $cancelType == 'revoke') ){
            //售后 && 撤销撤改 && 买家 可退
            //判断是否处于分销链中
            $where = [
                'buyerid' => $cancelMemberId,
            ];
            $subOrderModel = new SubOrderQuery();
            $res = $subOrderModel->getInfoInSplitByOrder($ordernum, '*', $where, true);
            if(!empty($res)){
                return $this->orderReturn(200, '有权限退');
            }
        }
        if (($orderInfo['ifpack'] == 1 || $orderInfo['ifpack'] == 2) && !in_array($orderInfo['ordermode'],[24,44])) {
            //套票退票逻辑判断
            //团单票如果也是套票的话，特殊处理，不走这边的逻辑 - by cyw 2018-07-18

            //如果是演出订单，获取演出时间
            $showInfoArr   = isset($orderInfo['series']) ? unserialize($orderInfo['series']) : [];
            $showBeginTime = isset($showInfoArr[4]) ? $showInfoArr[4] : '';

            $packRes = $checkBiz->checkPackRefund($ordernum, $ticketInfo, $orderInfo['playtime'], $showBeginTime,
                $orderInfo['status'], $orderInfo['ordermode'],
                $memberType, $orderInfo['ifpack'], $orderInfo['ifprint'], $cancelMemberId, $orderInfo['endtime'],
                $orderInfo['member'], $orderInfo['pack_order'],
                $cancelType, $isForceCancel, $subOrderList, $cancelChannel,$isAuditPass, $refundParams);
            pft_log('order_refund/debug', json_encode(['isHaveCancelAuth_checkPackRefund', $ordernum, $packRes, $cancelMemberId, $cancelChannel, $memberType]));
            if ($packRes['code'] == 200) {
                return $this->orderReturn(200, '有权限退');
            } else {
                return $packRes;
            }
        } else {
            $refundOrderInfo  = [$ordernum => $orderInfo];
            $refundTicketInfo = [$tid => $ticketInfo];
            $refundTicketExt  = [
                $tid => [
                    'tid'          => $tid,
                    'v_time_limit' => $ticketInfo['v_time_limit'],
                ],
            ];

            $refundRes = $checkBiz->checkCanRefund([$ordernum], $cancelMemberId, $refundOrderInfo, $refundOrderInfo,
                $refundOrderInfo, $refundTicketInfo, $refundTicketExt, $cancelChannel,[],$isAuditPass,$isForceCancel, $refundParams);
            if (isset($refundRes['refuseRefund'][$ordernum])) {
                //返回了不能退的原因
                $refuseInfo = $refundRes['refuseRefund'][$ordernum];
                $errCode    = $refuseInfo['code'];
                $errMsg     = $refuseInfo['msg'];
                $logErrMsg  = $refuseInfo['log_err_msg'] ?: '';

                return $this->orderReturn(0, $errMsg, ['err_code' => $errCode, 'log_err_msg' => $logErrMsg]);
            } else {
                //有权限退
                return $this->orderReturn(200, '有权限退');
            }
        }
    }

    /**
     * 获取未使用游客信息
     * <AUTHOR>
     * @date   2019-07-02
     *
     * @param  string  $ordernum
     * @param  int  $cancelNum
     * @param  array  $ticketInfo
     * @param  array  $orderInfo
     *
     * @return array
     */
    private function _getOrderLeftTourist($ordernum, $cancelNum, $ticketInfo, $orderInfo)
    {
        //获取所有的未使用的身份证
        $handlerModel = new OrderHandler();
        if ($orderInfo['status'] == 2) {  //过期的时候要获取状态为4的
            $checkStateArr = [0, 4];
        } else {
            $checkStateArr = [0];
        }

        $allAvailableTouristArr = $handlerModel->getOrderTouristInfo($ordernum, 0, '', $checkStateArr);

        //如果是一票一码票类，获取没有被使用的码列表
        $codeList = [];

        //未使用身份证列表
        $availableIdcardArr = [];
        //未使用证件列表
        $availableIdNumberArr = [];
        if ($allAvailableTouristArr) {
            //未使用身份证列表
            foreach ($allAvailableTouristArr as $val) {
                if ($val['idcard'] && $val['voucher_type'] == 1) {
                    $availableIdcardArr[] = strval(trim($val['idcard']));
                }
                if ($val['idcard']) {
                    $availableIdNumberArr[] = strval(trim($val['idcard']));
                }
            }

            //一票一码票类
            if ($ticketInfo['batch_check'] == 2) {
                $orderHash = new OrderHashids();

                $orderIdxList = array_column($allAvailableTouristArr, 'idx');
                $cutIdxList   = array_slice($orderIdxList, 0, $cancelNum);

                $decodeOrdernum = $orderHash->encode($ordernum);
                foreach ($cutIdxList as $idx) {
                    $codeList[] = 'OD#' . $decodeOrdernum . str_pad($idx, 2, '0', STR_PAD_LEFT);
                }
            }
        }

        return ['codeList' => $codeList, 'availableIdcardArr' => $availableIdcardArr, 'availableIdNumberArr'=>$availableIdNumberArr];
    }

    /**
     * 获取一票一码类型票的门票码codeList
     * idx会往左填充0，保持两位格式
     * @param $orderNum
     * @param $touristInfoList
     * @param $ticketBatchCheckEnum
     * @param $cancelNum
     * @return array
     */
    private function getOneTicketOneCodeCodeList($orderNum, $touristInfoList, $ticketBatchCheckEnum, $cancelNum)
    {
        $codeList = [];
        if ($ticketBatchCheckEnum == 2) {
            $orderHash = new OrderHashids();
            $orderIdxList = array_column($touristInfoList, 'idx');
            $cutIdxList   = array_slice($orderIdxList, 0, $cancelNum);

            $decodeOrderNum = $orderHash->encode($orderNum);
            foreach ($cutIdxList as $idx) {
                //str_pad 往idx左边填充0,总长度为2，不足补0
                $codeList[] = 'OD#' . $decodeOrderNum . str_pad($idx, 2, '0', STR_PAD_LEFT);
            }
        }
        return $codeList;
    }

    /**
     * 是否独立收款
     * TODO:现在还是从配置获取，这样有可能独立收款配置改掉了，会导致旧订单判断错误
     *      所以后期在订单信息里面写入是否独立收款的信息，然后直接在订单信息里面获取
     *
     * @param string $ordernum 订单号
     * @param array $orderInfo 订单详情
     *
     * @return bool
     * @throws Exception
     * <AUTHOR>
     * @date   2019-07-03
     *
     */
    private function _isSelfReceive($ordernum, $orderInfo)
    {
        $isSelfReceive = false;

        $aid               = $orderInfo['aid'];
        $paymode           = $orderInfo['paymode'];
        $onlinePaymodeList = load_config('online_pay_mode');

        if (in_array($paymode, $onlinePaymodeList)) {
            $payBiz        = new PayBase();
            $isSelfReceive = $payBiz->isIndependentCollection($ordernum, $aid);
        }

        return $isSelfReceive;
    }

    /**
     * 是否分终端使用完了
     *
     * <AUTHOR>
     * @date   2019-07-03
     *
     * @param  string  $ordernum  订单号
     *
     * @return int
     */
    private function _isBranchUsed($ordernum)
    {
        $noUsedNum   = 0;
        $terminalModel = new TerminalDbHandler();
        $branchUseInfo = $terminalModel->getBranchUsedLog($ordernum,'used_num,finish_num');
        if ($branchUseInfo) {
            $noUsedNum = $branchUseInfo['used_num'] - $branchUseInfo['finish_num'] > 0 ? $branchUseInfo['used_num'] - $branchUseInfo['finish_num'] : 0;
        }

        return $noUsedNum;
    }
    /**
     * 处理退票的剩余票数、剩余金额、退票追踪、最早订单状态等数据
     * <AUTHOR>
     * @date   2019-07-07
     *
     * @param  string  $ordernum  订单号
     * @param  int  $cancelNum  退票数量
     * @param  string  $cancelType  退票类型
     * @param  array  $orderInfo  订单详情
     * @param  int  $branchIsUsedNum  分终端没有使用完的数量
     * @param  int  $exchangePartExchangeNum 预售券产品：获取处于部分兑换的数量
     *
     * @return array
     */
    private function _handleRefundDetail($ordernum, $cancelNum, $cancelType, $orderInfo,$branchIsUsedNum = 0,$refundAmount = false, $exchangePartExchangeNum = 0)
    {
        $totalNum    = intval($orderInfo['tnum']);
        $verifiedNum = intval($orderInfo['verified_num']);
        $oldStatus   = $orderInfo['status'];
        $originMoney = intval($orderInfo['totalmoney']);
        if ($cancelType == 'revoke') {
            //订单全部验证+部分验证的情况下
            //撤改的情况下，有将撤改部分和撤改全部做区分
            $leftNum    = $totalNum - $cancelNum;
            $leftCancelNum = $leftNum;
            $validNum   = $leftNum;
            $status = $oldStatus; //对于撤改, 订单状态这里先保持原样, 在下一步获取出本次撤改的游客信息后, 再进行赋值状态

            if ($leftNum == 0) {
                //撤销，全部退票
                $dstatus     = 4;
                $trackAction = 6;
            } else {
                //撤改，部分退票
                $dstatus     = 5;
                $trackAction = 7;
            }
        } else {
            //订单未验证、部分验证、过期、待确认的情况
            $leftNum = $totalNum - $verifiedNum - $cancelNum;
            $leftCancelNum = $leftNum;

            if ($verifiedNum > 0) {

                //剩余金额
                $validNum   = $leftNum + $verifiedNum;

                //分终端的未使用完数量，放在这边是防止$validNum 数量被算错了
                $leftNum    = $leftNum + $branchIsUsedNum;

                //预售券产品线：如果有部分兑换的数量，需要加上
                //避免出部分兑换情况下，剩余一张也取消了的时候，整笔订单就认为是验证了，正常应该保持部分验证状态
                //等后续剩余部分都兑换的时候下，才将订单修改了验证
                $leftNum    = $leftNum + $exchangePartExchangeNum;

                //部分验证的情况下
                if ($leftNum == 0) {
                    //剩下部分都取消，订单整体变成验证状态
                    //这种情况下，订单的剩余票数需要加上验证的数量
                    $status      = 1;
                    $dstatus     = 4;
                    $trackAction = 1;
                } else {
                    //还有部分没有取消，订单还是保持原来的状态
                    $status      = $oldStatus;
                    $dstatus     = 5;
                    $trackAction = 1;
                }
            } else {
                //都还没有验证
                $validNum   = $leftNum;

                if ($leftNum == 0) {
                    //订单直接取消，订单变成取消状态
                    $status      = 3;
                    $dstatus     = 4;
                    $trackAction = 2;
                } else {
                    //还有部分没有取消，订单还是保持原来的状态
                    $status      = $oldStatus;
                    $dstatus     = 5;
                    $trackAction = 1;
                }
            }
        }

        if($refundAmount!==false){
            $returnMoney = intval($refundAmount);
        } else {
            // $refundAmount 已经表示新版优惠券退款金额，走到这里表示没有使用新版优惠券
            $orderDistributionChain = OrderDistributionChain::getInstance();
            $options = [
                'allVerify' => $leftCancelNum <= 0
            ];
            $frozenResult = $orderDistributionChain->getLastSellerReturnAmount($orderInfo, $cancelNum, [], $options);
            $returnMoney = $frozenResult['data']['returnMoney'] ?? 0;
        }
        $validMoney = max(0, $originMoney - $returnMoney);

        $refundInfo = [
            'leftNum'     => $leftNum, //剩余可用票数
            'validNum'    => $validNum, //总的有效票数：验证票数 + 剩余票数
            'cancelNum'   => $cancelNum, //退的票数
            'status'      => $status, //订单最后的状态
            'dstatus'     => $dstatus, // 4=取消, 5=修改
            'trackAction' => $trackAction, //取消类型 1=修改,2=取消,6=撤销,7=撤改
            'validMoney'  => $validMoney, //总的有效金额
            'originMoney' => $originMoney, //取消前的金额
            'originNum'   => $totalNum, //取消前的票数
        ];

        return $refundInfo;
    }

    /**
     * 生成远端退票请求流水号
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum
     * @param  string  $reqSerialNumber
     *
     * @return string
     */
    public function _genReqSerialNumber($ordernum, $reqSerialNumber = '')
    {
        if ($reqSerialNumber) {
            return $reqSerialNumber;
        } else {
            //平台去生成一个唯一的退款请求流水号
            $microtime = str_pad(str_replace('.', '', microtime(true)), 14, '0');

            return strval("{$ordernum}_{$microtime}");
        }
    }

    /**
     * 三方系统订单退票
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum  订单号
     * @param  int  $leftNum  剩余票数
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票数据
     * @param  bool  $isCancelThirdSystem  程序控制的是否需要往三方系统去退票，默认都是需要，特殊情况下使用
     * @param  bool  $isThirdOrder  是否对接三方系统
     * @param  bool  $isNeedAudit  是否需要退票审核
     * @param  array  $personIdList  身份证数据
     * @param  array  $codeList  凭证码列表
     * @param  string  $pftSerialNum  退票流水号
     * @param  array  $refundOpenCode  下游透传券码至上游供应商
     * @return array
     */
    private function _thirdSystemRefund($params)
    {
        $ordernum = $params['ordernum'];
        $leftNum = $params['leftNum'];
        $orderInfo = $params['orderInfo'];
        $ticketInfo = $params['ticketInfo'];
        $isCancelThirdSystem = $params['isCancelThirdSystem'];
        $isThirdOrder = $params['isThirdOrder'];
        $isNeedAudit = $params['isNeedAudit'];
        $personIdList = $params['thirdPersonIdList'];
        $codeList = $params['codeList'];
        $pftSerialNum = $params['pftSerialNumber'];
        $realCancelChannel = $params['realCancelChannel'] ?? 0;
        $cancelNum = $params['cancelNum'] ?? 0;
        $isRollBack = $params['isRollBack'] ?? false;
        $refundOpenCode = $params['refundOpenCode'] ?? [];
        $approvalParams = $params['approvalParams'] ?? [];
        $refundPersonInfoList = $params['refundPersonInfoList'] ?? [];

        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return self::_thirdSystemRefundForApproval($ordernum, $leftNum, $orderInfo, $ticketInfo, $isCancelThirdSystem, $isThirdOrder,
                $isNeedAudit, $personIdList, $codeList, $pftSerialNum,$realCancelChannel,$cancelNum,$isRollBack,
                $refundOpenCode,$approvalParams);
        }
        if ($isCancelThirdSystem && $isThirdOrder && in_array($orderInfo['pay_status'], [0, 1])) {
            //订单已经支付了，需要往三方系统去取消
            $reqParam = [
                'Ordern'       => $ordernum,
                'Tnum'         => $leftNum,
                'Fid'          => $orderInfo['member'],
                'LandId'       => $ticketInfo['landid'],
                'PayStatus'    => $orderInfo['pay_status'],
                'ApplyId'      => $ticketInfo['apply_did'],
                'IdCardList'   => $personIdList ? implode(',', $personIdList) : '',
                'PftSerialNum' => $pftSerialNum,
                'RefundOpenCode'=> $refundOpenCode,
            ];
            if (!empty($refundPersonInfoList)) {
                $reqParam['refundPersonInfo'] = $refundPersonInfoList;
            }

            if ($codeList) {
                $reqParam['codes'] = implode(',', $codeList);
            }
            if ($cancelNum > 0){
                $reqParam['cancelNum'] = $cancelNum;
            }
            //对接三方系统的模式
            $interfaceMode = $ticketInfo['sourceT'] == 3 ? 'new' : 'old';

            //回滚的情况套票子票成功
            $isSonOrderRollBackNotice = false;
            //子票跟随主票传入的回滚,并且是套票主票那边发起的子票取消，并且是在addon表里面有
            if ($isRollBack && $realCancelChannel == 23 && ($orderInfo['tordernum'] || $orderInfo['status'] == CommonOrderStatus::WAIT_PRINTTICKET_CODE)){
                $isSonOrderRollBackNotice = true;
            }

            //如果新模式下而且退票审核通过的情况不需要继续请求了
            if ($interfaceMode == 'new' && !$isNeedAudit && $realCancelChannel != OrderConst::SET_EXPIRED_CANCEL && !$isSonOrderRollBackNotice
                && $realCancelChannel!= OrderConst::AFTER_SALE_REFUND) {
                return $this->returnData(200, '不需要退票审核，直接返回');
            }
            /**
             * 是三方订单，且在往上游退，票调用开放平台前，往redis中加入，正在进行中往审批记录中写记录,尚未完成；
             * 防止第三方系统是异步的自动审批，会导致审批流程第三方->开放->平台，找不到审批记录，因为这里是同步的。
             */
            RefundRedisManage::addRefundAuditRecordRedisLock($pftSerialNum);
            pft_log('order/refund_audit', json_encode(['addRefundAuditRecordRedisLock',$pftSerialNum, $ordernum],JSON_UNESCAPED_UNICODE));
            $thirdSystemLib = new ThirdSystem();
            $refundRes      = $thirdSystemLib->commonRefund($reqParam, $interfaceMode);

            $code = $refundRes['code'];
            $msg  = $refundRes['msg'];

            // 这个数据直接在日志系统去查询
            //if (in_array($code, [1096, 1109])) {
            //    Helpers::sendDingTalkGroupRobotMessage($msg, "在线取消失败", "订单号:{$ordernum}",
            //        \Library\Constants\DingTalkRobots::REFUND_ROBOT);
            //}

            return $this->returnData($code, $msg.json_encode($reqParam));
        } else {
            //不需要往三方系统去取消
            return $this->returnData(200, '不需要往三方系统去取消');
        }
    }


    /**
     * 三方系统订单退票
     *
     * @param  string  $ordernum  订单号
     * @param  int  $leftNum  剩余票数
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票数据
     * @param  bool  $isCancelThirdSystem  程序控制的是否需要往三方系统去退票，默认都是需要，特殊情况下使用
     * @param  bool  $isThirdOrder  是否对接三方系统
     * @param  bool  $isNeedAudit  是否需要退票审核
     * @param  array  $personIdList  身份证数据
     * @param  array  $codeList  凭证码列表
     * @param  string  $pftSerialNum  退票流水号
     * @param  array  $refundOpenCode  下游透传券码至上游供应商
     * @return array
     */
    private function _thirdSystemRefundForApproval($ordernum, $leftNum, $orderInfo, $ticketInfo, $isCancelThirdSystem, $isThirdOrder,
                                        $isNeedAudit, $personIdList, $codeList, $pftSerialNum,$realCancelChannel,$cancelNum = 0,$isRollBack = false,
                                        $refundOpenCode = [],$approvalParams=[])
    {
        if ($isCancelThirdSystem && $isThirdOrder && in_array($orderInfo['pay_status'], [0, 1])) {
            //订单已经支付了，需要往三方系统去取消
            $reqParam = [
                'Ordern'       => $ordernum,
                'Tnum'         => $leftNum,
                'Fid'          => $orderInfo['member'],
                'LandId'       => $ticketInfo['landid'],
                'PayStatus'    => $orderInfo['pay_status'],
                'ApplyId'      => $ticketInfo['apply_did'],
                'IdCardList'   => $personIdList ? implode(',', $personIdList) : '',
                'PftSerialNum' => $pftSerialNum,
                'RefundOpenCode'=> $refundOpenCode
            ];

            if ($codeList) {
                $reqParam['codes'] = implode(',', $codeList);
            }
            if ($cancelNum > 0){
                $reqParam['cancelNum'] = $cancelNum;
            }
            //对接三方系统的模式
            $interfaceMode = $ticketInfo['sourceT'] == 3 ? 'new' : 'old';

            //由于三方票都需要生成审核记录，所以现在都需要向上游发起审核
            /*
            //回滚的情况套票子票成功
            $isSonOrderRollBackNotice = false;
            //子票跟随主票传入的回滚,并且是套票主票那边发起的子票取消，并且是在addon表里面有
            if ($isRollBack && $realCancelChannel == 23 && ($orderInfo['tordernum'] || $orderInfo['status'] == CommonOrderStatus::WAIT_PRINTTICKET_CODE)){
                $isSonOrderRollBackNotice = true;
            }

            //如果新模式下而且退票审核通过的情况不需要继续请求了
            if ($interfaceMode == 'new' && !$isNeedAudit && $realCancelChannel != OrderConst::SET_EXPIRED_CANCEL && !$isSonOrderRollBackNotice
                && $realCancelChannel!= OrderConst::AFTER_SALE_REFUND) {
                return $this->returnData(200, '不需要退票审核，直接返回');
            }
            */
            $isSonOrderRollBackNotice = false;
            //子票跟随主票传入的回滚,并且是套票主票那边发起的子票取消，并且是在addon表里面有
            if ($isRollBack && $realCancelChannel == 23 && ($orderInfo['tordernum'] || $orderInfo['status'] == CommonOrderStatus::WAIT_PRINTTICKET_CODE)){
                $isSonOrderRollBackNotice = true;
            }
            //回滚、强制取消、撤销撤改 不向上游发起请求
            if (!$isSonOrderRollBackNotice && ($isRollBack || $approvalParams['refundAuditParams']['isForceCancel'] ||
                    $approvalParams['refundAuditParams']['cancelType'] =='revoke')) {
                return self::skipThirdSystemRefund($approvalParams,$ticketInfo,$isCancelThirdSystem,$isThirdOrder,'回滚、强制取消、撤销撤改无需向三方发起退票请求');
            }
            //套票主票发起的子票全部取消且不需要审核，不需要向上游发起请求 为了解决子票联动主票发起取消，子票又向上游发起审核，上游回复审核中导致的退票失败
            if ($realCancelChannel == 23 && !$isNeedAudit && !$isSonOrderRollBackNotice) {
                return self::skipThirdSystemRefund($approvalParams,$ticketInfo,$isCancelThirdSystem,$isThirdOrder,'子票均审核通过，主票联动子票退票');
            }
            pft_log('refund/approval',json_encode(['thirdApproval',$ordernum, $leftNum, $orderInfo, $ticketInfo, $isCancelThirdSystem, $isThirdOrder,
                $isNeedAudit, $personIdList, $codeList, $pftSerialNum,$realCancelChannel,$cancelNum,$isRollBack ,
                $refundOpenCode,$approvalParams]));
            //如果是三方审核结果返回的，就不需要审核
            if(isset($approvalParams['refundAuditParams']['moreData']['isThirdAuditRes']) && $approvalParams['refundAuditParams']['moreData']['isThirdAuditRes']){
                return self::skipThirdSystemRefund($approvalParams,$ticketInfo,$isCancelThirdSystem,$isThirdOrder,'三方审核通过');
//                $approvalNodeInfo = $approvalParams['approvalNodeInfo'];
//                $isAgree = $approvalNodeInfo['has_approval'] && $approvalNodeInfo['exist_node'] && !$approvalNodeInfo['finish_node'];
//                if($isAgree &&  isset($approvalNodeInfo['processInstanceId'])){
//                    $refundApprovalService = new RefundApprovalService();
//                    $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
//                    $status = $refundApprovalService::NOTIFY_STATUS['agree'];
//                    $sid = $ticketInfo['apply_did'];
//                    $refundApprovalService->notifyApprovalNode($noticeType,
//                        $approvalNodeInfo['processInstanceId'],$status,$sid,'三方退票成功');
//                }
//                pft_log('refund/approval',json_encode(['thirdApproval',$approvalNodeInfo,$isCancelThirdSystem,$isThirdOrder]));
//                return $this->returnData(200, '不需要退票审核，直接返回');
            }
            if(!$approvalParams['approvalNodeInfo']['exist_node']){
                //发起时未绑定三方 现在又是三方票 需重新发起审批  or 发起时该票无需审核，三方票都要进入审批流
                //是否系统统一审核
                $refundAuditParams = $approvalParams['refundAuditParams'];
                $refundAuditParams['moreData']['is_need_approval'] = $isNeedAudit;
                $refundAuditParams['refundAuditExt'] =
                $res = $this->_addRefundAudit($refundAuditParams['ordernum'], $refundAuditParams['orderInfo'],$refundAuditParams['cancelNum'],
                    $refundAuditParams['leftNum'], $refundAuditParams['validNum'], $refundAuditParams['opId'], $refundAuditParams['pftSerialNumber'] ,
                    $refundAuditParams['reqSerialNumber'], $refundAuditParams['applyDid'], $refundAuditParams['personIdList'],
                    $refundAuditParams['isSysException'], $refundAuditParams['timeOrderRefund'], $refundAuditParams['refundAuditExt'], $refundAuditParams['moreData']);
                pft_log('refund/approval',json_encode(['thirdApproval',$approvalParams,$isCancelThirdSystem,$isThirdOrder,$res]));
                if($res['code']!=200){
                    return $this->orderReturn(1109, "发起审批失败：{$res['msg']}",[$approvalParams['approvalNodeInfo']['processInstanceId']??'',
                        $approvalParams['ordernum'],$approvalParams['pftSerialNumber'],$res]);
                }
                else{
                    $refundModel     = new OrderRefund();
                    $refundModel->delRefundJournal($pftSerialNum);
                    $refundApprovalService = new RefundApprovalService();
                    $cancelProcessRes = $refundApprovalService->cancelProcess($approvalParams['approvalNodeInfo']['processInstanceId'],$refundAuditParams['applyDid'],'发起审核后绑定三方，需重新审核',false);
                    pft_log('refund/approval',json_encode(['thirdApproval_cancel',$approvalParams,$approvalParams['approvalNodeInfo'],$cancelProcessRes,$refundAuditParams['ordernum']]));
                    return $this->orderReturn(1095, '退票发起后绑定三方,需重新进入审核',[$approvalParams['processInstanceId']??'',
                        $approvalParams['ordernum'],$approvalParams['pftSerialNumber'],$res]);
                }
            }
            $thirdSystemLib = new ThirdSystem();
            $refundRes      = $thirdSystemLib->commonRefund($reqParam, $interfaceMode);
            $code = $refundRes['code'];
            $msg  = $refundRes['msg'];
            if($code == 200){
                $approvalNodeInfo = $approvalParams['approvalNodeInfo'];
                $isAgree = $approvalNodeInfo['has_approval'] && $approvalNodeInfo['exist_node'] && !$approvalNodeInfo['finish_node'];
                if($isAgree &&  isset($approvalNodeInfo['processInstanceId'])){
                    //发起时绑定了三方 ,不管现在是不是三方票 跳过这个流程
                    $refundApprovalService = new RefundApprovalService();
                    $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
                    $status = $refundApprovalService::NOTIFY_STATUS['agree'];
                    $sid = $ticketInfo['apply_did'];
                    $refundApprovalService->notifyApprovalNode($noticeType,
                        $approvalNodeInfo['processInstanceId'],$status,$sid,'三方退票成功');
                }
                pft_log('refund/approval',json_encode(['thirdApproval',$approvalNodeInfo,$isCancelThirdSystem,$isThirdOrder]));
            }
            // 这个数据直接在日志系统去查询
            //if (in_array($code, [1096, 1109])) {
            //    Helpers::sendDingTalkGroupRobotMessage($msg, "在线取消失败", "订单号:{$ordernum}",
            //        \Library\Constants\DingTalkRobots::REFUND_ROBOT);
            //}
            return $this->returnData($code, $msg);
        } else {
            //不需要往三方系统去取消
            return self::skipThirdSystemRefund($approvalParams,$ticketInfo,$isCancelThirdSystem,$isThirdOrder);
//            $approvalNodeInfo = $approvalParams['approvalNodeInfo'];
//            $isSkip = $approvalNodeInfo['has_approval'] && $approvalNodeInfo['exist_node'] && !$approvalNodeInfo['finish_node'];
//            if($isSkip &&  isset($approvalNodeInfo['processInstanceId'])){
//                //发起时绑定了三方 ,现在不是三方票 跳过这个流程
//                $refundApprovalService = new RefundApprovalService();
//                $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
//                $status = $refundApprovalService::NOTIFY_STATUS['agree'];
//                $sid = $ticketInfo['apply_did'];
//                $refundApprovalService->notifyApprovalNode($noticeType,
//                    $approvalNodeInfo['processInstanceId'],$status,$sid,'退票发起后无需向三方退票,跳过当前审核流程');
//            }
//            pft_log('refund/approval',json_encode(['thirdApproval',$approvalNodeInfo,$isCancelThirdSystem,$isThirdOrder]));
//            //不需要往三方系统去取消
//            return $this->returnData(200, '不需要往三方系统去取消');
        }
    }

    public function skipThirdSystemRefund($approvalParams,$ticketInfo,$isCancelThirdSystem,$isThirdOrder,$msg = '退票发起后无需向三方退票,跳过当前审核流程'){
        $approvalNodeInfo = $approvalParams['approvalNodeInfo'];
        $isSkip = $approvalNodeInfo['has_approval'] && $approvalNodeInfo['exist_node'] && !$approvalNodeInfo['finish_node'];
        if($isSkip &&  isset($approvalNodeInfo['processInstanceId'])){
            //发起时绑定了三方 ,现在不是三方票 跳过这个流程
            $refundApprovalService = new RefundApprovalService();
            $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
            $status = $refundApprovalService::NOTIFY_STATUS['agree'];
            $sid = $ticketInfo['apply_did'];
            $refundApprovalService->notifyApprovalNode($noticeType,
                $approvalNodeInfo['processInstanceId'],$status,$sid,$msg);
        }
        pft_log('refund/approval',json_encode(['thirdApproval_skip',$approvalNodeInfo,$isCancelThirdSystem,$isThirdOrder,$msg]));
        //不需要往三方系统去取消
        return $this->returnData(200, '不需要往三方系统去取消');
    }

    /**
     * 需要同步执行的退票逻辑
     * <AUTHOR>
     * @date 2019-07-07
     *
     * @param  string  $ordernum  订单号
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票信息
     * @param  array  $orderRefundInfo  整理订单取消状态、金额等数据
     * @param  array  $refundParams  取消传进来的参数
     * @param  array  $refundIdxList  取消序号信息
     * @param array $cancelType common=取消, revoke=撤改
     *
     * @return array
     */
    private function _syncRefundTask($ordernum, $orderInfo, $ticketInfo, $orderRefundInfo, $refundParams, $refundIdxList = [], $cancelType = 'common')
    {
        $isShowOrder = $ticketInfo['p_type'] == 'H' ? true : false;
        $cancelNum   = $orderRefundInfo['cancelNum'];
        $leftNum     = $orderRefundInfo['leftNum'];
        $validNum    = $orderRefundInfo['validNum'];

        $showRefundRes = -1;
        $seatIdList = [];
        if ($isShowOrder) {
            $refundIdxIdList = (array_column($refundIdxList ?? [], 'idx'));
            if(!empty($refundIdxIdList)) {
                //根据idx去查找座位信息
                $javaApi = new \Business\JavaApi\Order\OrderTouristInfoExtendService();
                $resExt  = $javaApi->queryOrderTouristInfoExtendByParam($ordernum, $refundIdxIdList);
                $seatIdList = [];
                if(!empty($resExt['data']) && is_array($resExt['data'])) {
                    //获取一下拓展信息里的座位信息, 如果座位信息为空, 则按照旧的逻辑(按数量退)
                    $seatIdList = array_map(function ($item) {
                        $extArr = @json_decode($item['extInfo'], true);
                        return $extArr['seat_info']['seat_id'] ?? 0;
                    }, $resExt['data']);
                    $seatIdList = array_filter($seatIdList);
                }
            }

            $showRefundRes = $this->_showRefund($ordernum, $cancelNum, $leftNum, $validNum, $orderInfo, $ticketInfo, $seatIdList);
        }
        if ($orderInfo['pay_status'] == 2) {  //未支付的同步取消后逻辑

        } else {                               //已支付的同步取消后逻辑
            $afterPayRes = $this->_refundAfterPay($ordernum, $orderInfo, $ticketInfo, $orderRefundInfo, $refundParams, $refundIdxList, $cancelType);
        }
        $logData = json_encode([
            'key'      => 'syncRefundTask',
            'ordernum' => $ordernum,
            'res'      => [
                'showRefundRes' => $showRefundRes,
                'afterPayRes'   => $afterPayRes,
                'seatIds' => $seatIdList,
            ],
        ]);

        pft_log('order_refund/debug', $logData);
    }

    /**
     * 取消已支付后的订单
     * <AUTHOR>
     * @date   2019-10-29
     *
     * @param  string  $ordernum订单号
     * @param  int  $cancelNum  退票数量
     * @param  int  $leftNum  剩余票数
     * @param  int  $validNum  有效票数
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票信息
     * @param  array  $refundIdxList  取消序号信息
     * @param array $cancelType common=取消, revoke=撤改
     *
     * @return array
     */
    private function _refundAfterPay($ordernum, $orderInfo, $ticketInfo, $orderRefundInfo, $refundParams, $refundIdxList, $cancelType)
    {
        $enterRes = [];
        if ($refundParams['cancelType'] == 'revoke') {     //撤销撤改的

        } else {                                             //普通取消的
            if ($orderRefundInfo['dstatus'] == 4) {//4取消 5 修改
                //景点核销分离业务
                $enterLandBiz = new EnterLand();
                $enterRes     = $enterLandBiz->invalidEnterOrder($ordernum);
            }
        }
        //计时订单业务处理
        if ($ticketInfo['p_type'] == 'K' && ($refundParams['isRefundDeposit'] == 1 || $refundParams['isRefundOverPay'] == 1)) {
            $timeCardBz  = new TimeCard();
            $timeCardRes = $timeCardBz->refundDepositAndOverPay($ordernum, $refundParams['opId'], $refundParams['isRefundDeposit'],
                $refundParams['isRefundOverPay'], $orderRefundInfo['cancelNum']);
        }
        //分终端订单撤改后需要通知票务处理
        if($this->_isBranchOrder($refundParams['ticketSnapShot']['uuJqTicketDTO']['chkTerminalInfo'] ?? '') && $cancelType == 'revoke') {
            $touristInfoList = array_map(function ($item){
                return [
                    'idx' => $item['idx'],
                    'old_check_state' => $item['old_status']
                ];
            }, $refundIdxList);

            $params = [
                $ordernum,
                $refundParams['cancelNum'],
                $touristInfoList,
            ];
            $rpcClient = new \Library\JsonRpc\PftRpcClient('pft_scenic_local_service');
            $data = $rpcClient->call('Order/TerminalBranch/modUseNumber', $params, 'scenic');
            if($data['code'] != 200) {
                pft_log('revoke/terminal_debug', json_encode([$params, $data], JSON_UNESCAPED_SLASHES));
            }
        }

        $resultLog = [
            'enterLog'    => $enterRes,
            'timeCardRes' => $timeCardRes ?? [],
        ];

        return $resultLog;
    }

    /**
     * 演出订单退票
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum订单号
     * @param  int  $cancelNum  退票数量
     * @param  int  $leftNum  剩余票数
     * @param  int  $validNum  有效票数
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  门票信息
     * @param  array  $seatIdList 退款的座位id信息
     *
     * @return array
     */
    private function _showRefund($ordernum, $cancelNum, $leftNum, $validNum, $orderInfo, $ticketInfo, $seatIdList = [])
    {
        //处理演出信息
        $seriesArr = $orderInfo['series'] ? unserialize($orderInfo['series']) : [];
        if (!$seriesArr) {
            //演出信息错误
            $resData = $this->returnData(0, '演出信息错误');
        } else {
            $venueID = $seriesArr[0] ? $seriesArr[0] : '';
            $roundID = $seriesArr[1] ? $seriesArr[1] : '';
            $areaID  = $seriesArr[2] ? $seriesArr[2] : '';
            $seatID  = $seriesArr[3] ? $seriesArr[3] : ''; //series里的下标3的这个元素, 应该已经被废弃了
            $refundSeatTags = $seriesArr[7] ?: '';

            //演出座位相关信息
            $storageChange = $seriesArr[8] ?? [];  //连座锁定的座位id等信息
            $lockIdStr     = $seriesArr[9] ?? '';  //连座锁定的座位id等信息
            $lockIdArr     = explode(',', $lockIdStr);
            $otherInfo     = [
                'storage_change' => $storageChange,
                'lock_id_arr'    => $lockIdArr,
                'pay_status'     => $orderInfo['pay_status'],
            ];

            //本次退的票数，如果取消的话直接传0进来
            //$showCancelNum = $leftNum == 0 ? 0 : $cancelNum;
            if ($leftNum == 0) {
                //$validNum 是剩余的数量+验证的数量 如果大于0就说明有存在已验证的，就要用取消的数量
                if ($validNum > 0) {
                    $showCancelNum = $cancelNum;
                } else {
                    $showCancelNum = 0;
                }
            } else {
                $showCancelNum = $cancelNum;
            }

            $reqParam = [
                'Ordern'        => $ordernum,
                'Tnum'          => $leftNum,
                'CancelNum'     => $showCancelNum,
                'Fid'           => $orderInfo['member'],
                'LandId'        => $ticketInfo['landid'],
                'PayStatus'     => $orderInfo['pay_status'],
                'ApplyId'       => $ticketInfo['apply_did'],
                'VenueID'       => $venueID,
                'RoundID'       => $roundID,
                'AreaID'        => $areaID,
                'SeatID'        => $seatID, //这个字段应该是要被废弃的
                'SeatIds'       => $seatIdList, //实际的需要退的座位id列表
                'OtherInfo'     => $otherInfo, //连座锁定的座位id等信息
            ];

            $thirdSystemLib = new ThirdSystem();
            $refundRes      = $thirdSystemLib->showRefund($reqParam);

            if ($refundRes['code'] != 200) {
                //演出退票失败
                $resData = $this->returnData(0, $refundRes['msg']);
            } else {
                //退票成功
                //如果是退部分票，需要更新订单里面的座位信息
                $seriesInfo = isset($refundRes['data']['seriesInfo']) ? $refundRes['data']['seriesInfo'] : [];

                $seriesUpdateRes = -1;
                if ($seriesInfo) {
                    //把已退座位给叠加进series
                    $refundSeatStr = implode("_", array_map(function ($item) {
                        return $item['custom_num'] . '-' . $item['custom_pos'];
                    }, $refundRes['data']['refundSeatList'] ?? []));
                    if($refundSeatTags) {
                        $refundSeatStr = $refundSeatTags . '_' . $refundSeatStr;
                    }
                    $seriesInfo[7] = $refundSeatStr;

                    $updateFxDetailData = [
                        'series' => serialize($seriesInfo),
                    ];

                    $api  = new \Business\JavaApi\Order\OrderDetailUpdate();
                    $api->orderDetailInfoUpdate($ordernum, $updateFxDetailData, 0);
                }

                $resData = $this->returnData(0, '', ['update_res' => $seriesUpdateRes]);
            }
        }

        return $resData;
    }

    /**
     * 添加退票审核记录
     * <AUTHOR>
     * @date 2019-07-06
     *
     * @param  string  $ordernum
     * @param  array  $orderInfo
     * @param  int  $cancelNum
     * @param  int  $leftNum
     * @param  int  $validNum
     * @param  int  $opId
     * @param  string  $pftSerialNumber
     * @param  string  $reqSerialNumber
     * @param  int  $applyDid
     * @param  array  $personIdList
     * @param  bool  $isSysException
     * @param  array  $timeOrderRefund 计时订单退票审核特殊参数
     * @param  array $refundAuditExt 退票审核拓展属性
     *
     * @return array
     */
    private function _addRefundAudit($ordernum, $orderInfo, $cancelNum, $leftNum, $validNum, $opId, $pftSerialNumber,
        $reqSerialNumber, $applyDid, $personIdList = [], $isSysException = false, $timeOrderRefund = [], $refundAuditExt = [], $moreData = [], $personInfoList = [])
    {
        //16内部，17外部
//        $source = 16;
        $source = $moreData['track_source'] ?? 16; //原始渠道

        //TODO:$cancelNum之前的逻辑是美团那边有传取消的票数，然后写入
        //现在是直接将本次取消的票数直接写入
        pft_log('order_refund/debug', json_encode(['addRefundAudit',$ordernum, $personIdList, $moreData]));
        //TODO:后面这个逻辑可以直接使用uu_order_refund->details->personIdList传入退票接口中就可以了
        //如果是退票审核提交成功而且有传身份证的话
        //这边把tourist表的状态先修改成3，然后退票审核的时候会把3的状态修改为2
        $qConfigSwitch = new QConfigSwitchController();
        if ($qConfigSwitch::getRefundFixUsedWithTenant($ordernum)) {
            $handlerModel = new \Model\Order\OrderHandler();
            //非身份证的证件类型退
            if (!empty($personInfoList)) {
                $idCardArr = array_filter(array_unique(array_column($personInfoList, 'idcard')));
                $idxArr = array_column($personInfoList, 'person_index');
                //仅证件号都有才增加审核中，即实名制，如果是实名制数量小于票数量的则不增加
                if (count($idCardArr) == count($personInfoList)) {
                    pft_log('order_refund/debug', json_encode(['addRefundAudit_updateOrderTouristInfoState_personInfoList_new',$ordernum, $personInfoList]));
                    $handlerModel->updateOrderTouristInfoState($ordernum, $idxArr, 3);
                }
            } elseif ($personIdList) {
                $handlerModel->updateOrderTouristInfoState($ordernum, $personIdList, 3);
                pft_log('order_refund/debug', json_encode(['addRefundAudit_updateOrderTouristInfoState_personIdList_new',$ordernum, $personIdList]));
            }
        } else {
            if ($personIdList) {
                $handlerModel = new \Model\Order\OrderHandler();
                $handlerModel->updateOrderTouristInfoState($ordernum, $personIdList, 3);
                pft_log('order_refund/debug', json_encode(['addRefundAudit_updateOrderTouristInfoState_personIdList_old',$ordernum, $personIdList]));
            }
        }

        $refundAuditBiz = new RefundAudit();
        $addRes         = $refundAuditBiz->addRefundAudit($ordernum, $validNum, $opId, $source, 0, $orderInfo, false,
            $reqSerialNumber, $pftSerialNumber, $cancelNum, $isSysException, $personIdList, $timeOrderRefund, $refundAuditExt, $moreData);
        if ($addRes['code'] != 200) {
            //删除退款记录
            $refundModel = new OrderRefund();

            if ($addRes['code'] == 240) {
                //有未审核订单，不能重复发
                return $this->orderReturn(0, $addRes['msg'], ['err_code' => OrderConst::err1097]);

            } elseif ($addRes['code'] == 206){
                return $this->orderReturn(0, '审核申请数量有误', ['err_code' => OrderConst::err1096]);
            } else {
                // 退票审核写入失败
                $errCode = $addRes['code'];
                $errMsg  = $addRes['msg'] . "【{$errCode}】";
                $refundModel->delRefundJournal($pftSerialNumber);
                pft_log('order_refund/debug', json_encode(['refundAuditInsertFail',$addRes, $pftSerialNumber]));

                return $this->orderReturn(0, '写入退票审核数据失败', ['err_code' => OrderConst::err1096]);
            }
        } else {
            //写入成功，删除redis锁
            RefundRedisManage::delRefundAuditRecordRedisLock($pftSerialNumber);
            //把退票记录更新为退票在审核中
            $refundModel = new OrderRefund();
            $refundModel->updateRefundJournal($pftSerialNumber, OrderRefund::REFUND_AUDIT, $memo = '退票审核提交成功');

            // 退票审核记录写入成功
            return $this->orderReturn(200, '写入退票审核数据成功');
        }
    }

    /**
     * 取消套票的子票
     * <AUTHOR>
     * @date   2019-07-10
     *
     * @param  string  $ordernum
     * @param  array  $subOrderList
     * @param  string  $cancelType
     * @param  int  $cancelNum
     * @param  int  $totalNum
     * @param  int  $opId
     * @param  bool  $isForceCancel  是否强制取消
     * @param  int  $personIndex  取消的idx
     * @param  array  $personIdList  取消的身份证列表
     * @param  bool  $isRollBack  是否是套票主票回滚
     * @param  bool  $isSyntaxRollback  是否系统原因导致的回滚，需要退回码费等特殊款项
     *
     * @return []
     */
    private function _cancelPackageChildOrder($ordernum, $subOrderList, $cancelNum, $cancelType, $totalNum, $opId, $personIndex,
                                              $isForceCancel = false, $personIdList = [],$isRollBack = false,$approvalParams = [],$pftSerialNumber='',$personInfoList = [], $isSyntaxRollback = false)
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return self::_cancelPackageChildOrderForApproval($ordernum, $subOrderList, $cancelNum, $cancelType, $totalNum, $opId, $personIndex,
                $isForceCancel, $personIdList, $isRollBack, $approvalParams, $isSyntaxRollback);
        }
        //如果子票还没有生成的话，也让他取消吧
        if (!$subOrderList) {
            return $this->orderReturn(200, '子票还没有生成，也直接取消掉去');
        }

        $orderModel   = new OrderTools('localhost');
        $orderArr     = array_column($subOrderList, 'ordernum');
        //$orderDetails = $orderModel->getInfoInFxOrder($orderArr, 'orderid,aids_money');
        //$orderDetails = array_key($orderDetails, 'orderid');

        $queryParams = [$orderArr];
        $checkRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail','getLinkOrdersInfoAndRemoveOrders', $queryParams);
        if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
            $orderDetails = $checkRes['data'];
            $orderDetails = array_key($orderDetails, 'ordernum');
        }

        //TODO : 暂时先这样去后去，后期统一在 OrderTools -> getInfoForCancel 直接返回
        foreach ($subOrderList as $key => $item) {
            $tmpOrdernum  = $item['ordernum'];
            $tmpAidsMoney = isset($orderDetails[$tmpOrdernum]) ? $orderDetails[$tmpOrdernum]['aids_money'] : '';

            $subOrderList[$key]['aids_money'] = $tmpAidsMoney;
        }

        //检测余额
        $refundMoneyBiz = new RefundMoney();
        $isSelfReceive  = false; //子票默认都不是独立收款

        foreach ($subOrderList as $subInfo) {
            $subOrdernum   = $subInfo['ordernum'];
            //todo 参考使用中台提供的前置余额检测接口 \Business\Order\Refund::orderTradeRefundPreCheck
            $isMoneyEnough = $refundMoneyBiz->checkRefundMoney($subOrdernum, $subInfo, $cancelNum, $isSelfReceive,
                $isPackageSub = true, false, $cancelType);

            //如果子票的余额不够
            if (!$isMoneyEnough) {
                //日志
                pft_log('order_refund/debug', json_encode([
                    'checkRefundMoney',
                    'package_sub',
                    $isMoneyEnough,
                    $subInfo,
                    $cancelNum,
                    $isSelfReceive,
                ]));

                return $this->orderReturn(0, '套票中的子票退票的时候，上级余额不足', ['err_code' => OrderConst::err222]);
            }
        }

        //检测状态，如果是强制取消的话，不进行检测
        if ($isForceCancel) {
            //不检测
        } else {
            //判断状态
            foreach ($subOrderList as $subInfo) {

                //部分账号允许子票部分使用时还可退
                if ($this->_isAllowPartUsedRefund($subInfo['apply_did']) && $subInfo['status'] == 7) {
                    continue;
                }

                if (in_array($subInfo['status'], [1, 7])) {
                    //如果已经有被验证的订单话，直接返回
                    return $this->orderReturn(0, '部分子票已经验证，不能退票', ['err_code' => OrderConst::err2023]);
                }
            }
        }

        //将已经取消的订单返回到外层
        $cancelOrderList = [];

        foreach ($subOrderList as $subInfo) {
            //未使用和过期的订单进行取消
            $allowStatus = [0, 2, CommonOrderStatus::WAIT_APPOINTMENT_CODE, CommonOrderStatus::WAIT_PRINTTICKET_CODE];
            if ($this->_isAllowPartUsedRefund($subInfo['apply_did'])) {
                $allowStatus = array_merge($allowStatus, [7]);
            }

            if (in_array($subInfo['status'], $allowStatus)) {
                $subOrdernum = $subInfo['ordernum'];
                $subTotalNum = $subInfo['tnum'];

                //取消的子票数 - 按比例计算子票的取消数量
                $subCancelNum = ceil(($cancelNum * $subTotalNum) / $totalNum );
                $subCancelNum = $subCancelNum > $subTotalNum ? $subTotalNum : $subCancelNum;

                //实际的退票数
                $realityNum = ($cancelNum / $totalNum) * $subTotalNum;

                $cancelChannel    = 23;
                $reqSerialNumber  = '';
                $cancelRemarkArr  = ['remark' => '主票取消/修改，子票自动取消/修改'];
                $cancelSiteArr    = [];
                $cancelPersonArr  = [];
                $cancelSpecialArr = [
                    'is_cancel_sub' => false,
                    //和之前退票逻辑一致，退子票的时候不需要进行退票审核判断
                    'is_need_audit' => false,
                    'is_rollback'   => $isRollBack,
                    'is_syntax_rollback' => $isSyntaxRollback, //是否系统原因导致的回滚，需要退回码费等特殊款项
                ];
                if (bccomp($realityNum,$cancelNum,2) === 0) {   //判断下如果子票数量等于主票的时候就按主票要取消的idx去取消
                    $cancelPersonArr['person_index'] = $personIndex;
                }
                //比例为1：n的时候，需要重新获取子票的person_info_list,使用上面重新计算的person_id_list去获取
                $cancelPersonArr['person_id_list'] = $personIdList;
                $cancelPersonArr['person_info_list'] = $personInfoList;
                if (count($personIdList)!=$subCancelNum) {
                    $personRes = self::sonTicketRealNameForThird($subOrdernum,$pftSerialNumber,$personIdList,$subCancelNum);
                    pft_log('order_refund/debug',
                        json_encode(['ThirdRefundIdCard_PackSonRefund:', $personRes, $subOrdernum,$pftSerialNumber,$personIdList,$subCancelNum]));
                    if($personRes['code']!=200){
                        $this->returnData($personRes['code'], $personRes['msg']);
                    }
                    $cancelPersonArr['person_id_list'] = $personRes['data']['personIdList'];
                    if (!empty($personRes['data']['personInfoList'])) {
                        $cancelPersonArr['person_info_list'] = $personRes['data']['personInfoList'];
                    }
                }

                $subRefundRes = $this->commonRefund($subOrdernum, $subCancelNum, $opId, $cancelChannel, $cancelType,
                    $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
                $request      = [
                    'ordernum'        => $subOrdernum,
                    'cancelNum'       => $subCancelNum,
                    'opId'            => $opId,
                    'cancelChannel'   => $cancelChannel,
                    'cancelType'      => $cancelType,
                    'reqSerialNumber' => $reqSerialNumber,
                    'remark'          => $cancelRemarkArr,
                    'site'            => $cancelSiteArr,
                    'person'          => $cancelPersonArr,
                    'special'         => $cancelSpecialArr,
                ];

                //记录日志
                $word = json_encode([
                    'key' => '取消套票子票',
                    'req' => $request,
                    'res' => $subRefundRes,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('open_refund', $word, 3);

                if ($subRefundRes['code'] == 200) {
                    $cancelOrderList[] = $subOrdernum;
                } else if ($subRefundRes['code'] == 1095) {
                    //退票审核提交成功
                    if ($isForceCancel || $isRollBack) {
                        continue;
                    } else {
                        return $this->orderReturn(1095, '子票提交退票审核',
                            ['err_code' => $subRefundRes['code'], 'cancel_order' => $cancelOrderList]);
                    }
                } else {
                    //如果是强制取消的话，就不去管他是否取消成功
                    //其他情况下，直接就不让取消子票了，顺便把前面已经取消的订单返回
                    if ($isForceCancel || $isRollBack) {
                        continue;
                    } else {
                        $subMsg = $subRefundRes['msg'] ?? '';
                        if (empty($subMsg)) {
                            $subMsg = $subRefundRes['data']['err_msg'] ?? '';
                        }
                        $errCode = $subRefundRes['data']['err_code'] ?? '';
                        $errormsg = "子票【{$subOrdernum}】退票失败，套票不能退票";
                        $logErrormsgContent = $subRefundRes['data']['log_err_msg'] ?? '';
                        $logErrormsg = $errormsg .$subMsg .'log_err_msg:'. $logErrormsgContent;
                        if($errCode == OrderConst::err917){
                            $errormsg = '套票子票部分退票目前仅支持操作一个门票码';
                        }
                        return $this->orderReturn(0, $errormsg,
                            ['err_code' => $errCode, 'cancel_order' => $cancelOrderList, 'log_err_msg'=>$logErrormsg]);
                    }
                }
                if ($subRefundRes['code'] != 200) {
                    Helpers::sendDingTalkGroupRobotMessage($subRefundRes['msg'] . '|' . $subRefundRes['data']['err_code'],
                        "在线取消套票失败", "订单号:爸爸订单{$ordernum}.儿子{$subOrdernum}",
                        \Library\Constants\DingTalkRobots::REFUND_ROBOT);
                }
            }
        }

        //可以直接取消
        return $this->orderReturn(200, '子票还没有生成，也直接取消掉去');
    }


    private function _cancelPackageChildOrderForApproval($ordernum, $subOrderList, $cancelNum, $cancelType, $totalNum, $opId, $personIndex,
                                              $isForceCancel = false, $personIdList = [],$isRollBack = false,$approvalParams = [], $isSyntaxRollback = false)
    {
        //如果子票还没有生成的话，也让他取消吧
        if (!$subOrderList) {
            return $this->orderReturn(200, '子票还没有生成，也直接取消掉去');
        }

        $orderModel   = new OrderTools('localhost');
        $orderArr     = array_column($subOrderList, 'ordernum');
        //$orderDetails = $orderModel->getInfoInFxOrder($orderArr, 'orderid,aids_money');
        //$orderDetails = array_key($orderDetails, 'orderid');

        $queryParams = [$orderArr];
        $checkRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail','getLinkOrdersInfoAndRemoveOrders', $queryParams);
        if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
            $orderDetails = $checkRes['data'];
            $orderDetails = array_key($orderDetails, 'ordernum');
        }

        //TODO : 暂时先这样去后去，后期统一在 OrderTools -> getInfoForCancel 直接返回
        foreach ($subOrderList as $key => $item) {
            $tmpOrdernum  = $item['ordernum'];
            $tmpAidsMoney = isset($orderDetails[$tmpOrdernum]) ? $orderDetails[$tmpOrdernum]['aids_money'] : '';

            $subOrderList[$key]['aids_money'] = $tmpAidsMoney;
        }

        //检测余额
        $refundMoneyBiz = new RefundMoney();
        $isSelfReceive  = false; //子票默认都不是独立收款

        foreach ($subOrderList as $subInfo) {
            $subOrdernum   = $subInfo['ordernum'];
            $isMoneyEnough = $refundMoneyBiz->checkRefundMoney($subOrdernum, $subInfo, $cancelNum, $isSelfReceive,
                $isPackageSub = true);

            //如果子票的余额不够
            if (!$isMoneyEnough) {
                //日志
                pft_log('order_refund/debug', json_encode([
                    'checkRefundMoney',
                    'package_sub',
                    $isMoneyEnough,
                    $subInfo,
                    $cancelNum,
                    $isSelfReceive,
                ]));

                return $this->orderReturn(0, '套票中的子票退票的时候，上级余额不足', ['err_code' => OrderConst::err222]);
            }
        }

        //检测状态，如果是强制取消的话，不进行检测
        if ($isForceCancel) {
            //不检测
        } else {
            //判断状态
            foreach ($subOrderList as $subInfo) {

                //部分账号允许子票部分使用时还可退
                if ($this->_isAllowPartUsedRefund($subInfo['apply_did']) && $subInfo['status'] == 7) {
                    continue;
                }

                if (in_array($subInfo['status'], [1, 7])) {
                    //如果已经有被验证的订单话，直接返回
                    return $this->orderReturn(0, '部分子票已经验证，不能退票', ['err_code' => OrderConst::err2023]);
                }
            }
        }

        //将已经取消的订单返回到外层
        $cancelOrderList = [];

        foreach ($subOrderList as $subInfo) {
            //未使用和过期的订单进行取消
            $allowStatus = [0, 2, CommonOrderStatus::WAIT_APPOINTMENT_CODE, CommonOrderStatus::WAIT_PRINTTICKET_CODE];
            if ($this->_isAllowPartUsedRefund($subInfo['apply_did'])) {
                $allowStatus = array_merge($allowStatus, [7]);
            }

            if (in_array($subInfo['status'], $allowStatus)) {
                $subOrdernum = $subInfo['ordernum'];
                $subTotalNum = $subInfo['tnum'];

                //取消的子票数 - 按比例计算子票的取消数量
                $subCancelNum = ceil(($cancelNum * $subTotalNum) / $totalNum );
                $subCancelNum = $subCancelNum > $subTotalNum ? $subTotalNum : $subCancelNum;

                //实际的退票数
                $realityNum = ($cancelNum / $totalNum) * $subTotalNum;

                $cancelChannel    = 23;
                $reqSerialNumber  = $approvalParams['refundAuditParams']['reqSerialNumber'];
                $cancelRemarkArr  = ['remark' => '主票取消/修改，子票自动取消/修改'];
                $cancelSiteArr    = [];
                $cancelPersonArr  = [];
                $cancelSpecialArr = [
                    'is_cancel_sub' => false,
                    //和之前退票逻辑一致，退子票的时候不需要进行退票审核判断
                    'is_need_audit' => false,
                    'is_rollback'   => $isRollBack,
                    'is_syntax_rollback' => $isSyntaxRollback, //是否系统原因导致的回滚，需要退回码费等特殊款项
                ];
                if(isset($approvalParams['approvalNodeInfo']['processInstanceId'])){
                    $moreData['mainApprovalCode'] = $approvalParams['approvalNodeInfo']['processInstanceId'];
                }
                if (bccomp($realityNum,$cancelNum,2) === 0) {   //判断下如果子票数量等于主票的时候就按主票要取消的idx去取消
                    $cancelPersonArr['person_index'] = $personIndex;
                }
                if (!empty($personIdList)){
                    $cancelPersonArr['person_id_list'] = $personIdList;
                }
                $subRefundRes = $this->commonRefund($subOrdernum, $subCancelNum, $opId, $cancelChannel, $cancelType,
                    $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr,$moreData);
                $request      = [
                    'ordernum'        => $subOrdernum,
                    'cancelNum'       => $subCancelNum,
                    'opId'            => $opId,
                    'cancelChannel'   => $cancelChannel,
                    'cancelType'      => $cancelType,
                    'reqSerialNumber' => $reqSerialNumber,
                    'remark'          => $cancelRemarkArr,
                    'site'            => $cancelSiteArr,
                    'person'          => $cancelPersonArr,
                    'special'         => $cancelSpecialArr,
                ];

                //记录日志
                $word = json_encode([
                    'key' => '取消套票子票',
                    'req' => $request,
                    'res' => $subRefundRes,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('open_refund', $word, 3);

                if ($subRefundRes['code'] == 200) {
                    $cancelOrderList[] = $subOrdernum;
                } else if ($subRefundRes['code'] == 1095) {
                    //退票审核提交成功
                    if ($isForceCancel || $isRollBack) {
                        continue;
                    } else {
                        return $this->orderReturn(1095, '子票提交退票审核',
                            ['err_code' => $subRefundRes['code'], 'cancel_order' => $cancelOrderList]);
                    }
                } else {
                    //如果是强制取消的话，就不去管他是否取消成功
                    //其他情况下，直接就不让取消子票了，顺便把前面已经取消的订单返回
                    if ($isForceCancel || $isRollBack) {
                        continue;
                    } else {
                        $errCode = $subRefundRes['data']['err_code'];
                        $errormsg = $subRefundRes['data']['err_msg'] ?? '子票退票失败，套票不能退票';
                        if($errCode == OrderConst::err917){
                            $errormsg = '套票子票部分退票目前仅支持操作一个门票码';
                        }
                        return $this->orderReturn(0, "{$subOrdernum}:{$errormsg}",
                            ['err_code' => $errCode, 'cancel_order' => $cancelOrderList]);
                    }
                }
                if ($subRefundRes['code'] != 200) {
                    Helpers::sendDingTalkGroupRobotMessage($subRefundRes['msg'] . '|' . $subRefundRes['data']['err_code'],
                        "在线取消套票失败", "订单号:爸爸订单{$ordernum}.儿子{$subOrdernum}",
                        \Library\Constants\DingTalkRobots::REFUND_ROBOT);
                }
            }
        }

        //可以直接取消
        return $this->orderReturn(200, '子票还没有生成，也直接取消掉去');
    }

    /**
     * 添加退票成功后异步任务
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum  订单号
     * @param  bool  $isPayed  是否是支付成功后取消
     * @param  array  $orderInfo  订单详情
     * @param  array  $ticketInfo  门票详情
     * @param  array  $refundData  退款详情
     * @param  array  $refundParams  退票參數
     *
     * @return  bool
     */
    private function _addRefundSuccessTask($ordernum, $isPayed, $orderInfo, $ticketInfo, $refundData, $refundParams)
    {
        //和订单取消相关的一些票类属性
        $needTicketInfo = [
            'id'                  => $ticketInfo['id'],
            'apply_did'           => $ticketInfo['apply_did'],
            'title'               => $ticketInfo['title'],
            'land_title'          => $ticketInfo['land_title'],
            'cancel_sms_buyer'    => $ticketInfo['cancel_sms_buyer'], //取消短信通知取票人手机，0:不发送,1:发送
            'cancel_sms_supplier' => $ticketInfo['cancel_sms_supplier'], //取消短信通知供应商, 0:不发送 1：发送
            'sourceT'             => $ticketInfo['sourceT'],
            'Mpath'               => $ticketInfo['Mpath'],
            'Mdetails'            => $ticketInfo['Mdetails'],
            'p_type'              => $ticketInfo['p_type'], //一级分类
            'sub_type'            => $ticketInfo['sub_type'], //二级分类
        ];
        //是否独立收款
        /*$isSelfReceive = false;
        try {
            $isSelfReceive = $this->_isSelfReceive($ordernum, $orderInfo);
        } catch (Exception $e) {
        }*/
        $needRefundParams = [
            'cancelChannel'       => $refundParams['cancelChannel'],
            'isCancelNoticeThird' => $refundParams['isCancelNoticeThird'],
            'isRollBack'          => $refundParams['isRollBack'],
            'cancelType'          => $refundParams['cancelType'], //取消类型：common=正常取消，revoke=终端撤改
            'opId'                => $refundParams['opId'], //操作用户id
            'cancelRemark'        => $refundParams['cancelRemark'], //取消备注
            //'isSelfReceive'       => $isSelfReceive,//是否独立收款 独立收款的不推送解冻资金消息
            'isSyntaxRollback'    => $refundParams['isSyntaxRollback'],//是否是语法回滚
        ];
        $queueData        = [
            'ordernum'     => $ordernum,
            'isPayed'      => $isPayed,
            'orderInfo'    => $orderInfo,
            'ticketInfo'   => $needTicketInfo,
            'refundData'   => $refundData,
            'refundParams' => $needRefundParams,
        ];
        //TODO 这个异步里面存在改订单的状态，如果其他业务的异步有进行订单状态的判断，请手动传入当前这步的订单状态，不然有可能订单状态会被改成过期
        $cancelJobData = [
            'job_type' => 'cancel_async_task',
            'job_data' => $queueData,
        ];
        // 使用qconf abTest走新老逻辑
        $qConfigSwitch = new \Library\Util\QConfUtil();
        if ($qConfigSwitch->abTest('/php/platform/order_cancel_async_switch')) {
            // 新逻辑：推送到Kafka
            \Library\Kafka\KafkaProducer::push('platform_aliyun', 'order_cancel_async_topic', $cancelJobData, strval($ordernum));
        } else {
            // 老逻辑：使用Resque队列
            Queue::push('order', 'Order_Job', $cancelJobData);
        }

//        //分销专员订单取消佣金结算业务
//        $multiDistData = [
//            'action' => 'cancel',
//            'data'   => ['ordernum' => $ordernum],
//        ];
//        $multiDistJobId   = Queue::push('independent_system', 'MultiDist_Job', $multiDistData);
        //取消成功，卡券核销业务
        $weChatData = [
            'order_num' => $ordernum,
            'ticket_id' => $orderInfo['tid'],
        ];
//        $weChatCardOrderJobId = Queue::push('marketing_system', 'WeChatCardOrder_Job', $weChatData);

        $externalCodeJobId = -1;
        //未支付的取消 增加第三方发码的异步任务
        if ($orderInfo['pay_status'] == 2) {
            $externalCodeData = [
                'action' => 'cancel_release_code',
                'data'   => ['ordernum' => $ordernum],
            ];
            $externalCodeJobId  = Queue::push('independent_system', 'ExternalCode_Job', $externalCodeData);
        }

        //协议票异步任务
        $agreementTicketJobId = Storage::pushCancelStorage($ordernum, (int)$refundParams['cancelNum']);

        //订单取消成功通知（推送kafka）
        $dataKafka = [
            'orderNum' => $ordernum,
            'status'   => $refundData['order_status'],
            'data' => [
                'type' => 1,
                'params' => [
                    'pftSerialNumber'       => $refundData['pft_serial_number'],//平台退款流水号
                    'reqSerialNumber'       => $refundData['req_serial_number'],//请求退款流水号
                    'orderStatus'           => $refundData['order_status'],//退票后订单的状态
                    'cancelNum'             => $refundData['cancel_num'],//退票数量
                    'leftNum'               => $refundData['left_num'],//剩余的可用票数
                    'validNum'              => $refundData['valid_num'],//剩余的有效票数
                    'validMoney'            => $refundData['valid_money'],//剩余的有效金额
                    'trackId'               => $refundData['track_id'],//退票追踪ID
                    'opId'                  => $refundData['op_id'],//退票操作人
                    'refundIdx'             => $refundData['refund_idx'],//已取消的订单序号
                    'idCardCodeParams'      => $refundData['idCardCodeParams'],//实名制退票参数
                    'refundAmount'          => $refundData['refund_amount'],//退票金额
                    'refundFee'             => $refundData['refund_fee'],//退票手续费
                    'cancelChannel'         => $refundParams['realCancelChannel'],//取消渠道
                    'isCancelNoticeThird'   => $refundParams['isCancelNoticeThird'],//是否需要取消了通知三方系统
                    'isRollBack'            => $refundParams['isRollBack'],//是否是回滚订单
                    //'refundParams' => $refundParams
                ],
            ],
        ];

        if (isset($orderInfo['callback']) && $orderInfo['callback'] == 0) {
            //callback=0的时候不发消息
        } else {
            //callback=1的时候才发消息 - 防止callback数据没有获取，
            //因为之前都是有发消息的
            pft_log('debug/refundKafka', json_encode(['order_status_topic', $dataKafka]));
            KafkaProducer::push('platform', 'order_status_topic', $dataKafka, strval($ordernum));

            if ($refundParams['realCancelChannel'] == OrderConst::VERIFY_CANCEL) {
                pft_log('debug/refundKafka', json_encode(['refund_examine_topic', $dataKafka]));
                KafkaProducer::push('platform', 'refund_examine_topic', $dataKafka, strval($ordernum));
            }
        }

        //写入日志
        $logData = json_encode([
            'key'                  => 'addRefundSuccessTask',
            'ordernum'             => $ordernum,
            'externalCodeJobId'    => $externalCodeJobId,
            'agreementTicketJobId' => $agreementTicketJobId,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('order_refund/debug', $logData);
        
        // ============ 新增：预售券退票成功 Kafka 通知（仅预售券兑换订单） ============
        if ($orderInfo['ordermode'] == OrderChannel::EXCHANGE_COUPON_REDEEM_CHANNEL) {
            \Business\Order\ExchangeBaseTicket\KafkaNotificationService::sendCancelSuccessNotification(
                $refundParams['realCancelChannel'],
                $ordernum, 
                $refundData['req_serial_number'], 
                $refundParams['cancelType'], 
                $refundData['cancel_num'], 
                $refundData['left_num'], 
                $refundParams['opId'], 
                '退票成功'
            );
        }
        // ========================================================
        
        if (isset($refundParams['afterSaleNum']) && !empty($refundParams['afterSaleNum'])) {
            foreach ($refundData['refund_idx'] as &$value){
                $value = strval($value);
            }
            $dataKafka = [
                'op_id' => $refundParams['opId'],
                'refund_status'  => 1,// 0审核中 1退票成功  2失败
                'after_sale_num' => $refundParams['afterSaleNum'],
                'req_serial_number' => '', // 售后要求不要传，因为这个流水号根本就没有保存，传过去是错的
                'order_num' => $ordernum,
                'cancel_type' =>$refundParams['cancelType'],
                'refund_idx' => empty($refundData['refund_idx']) ? ["1"] : $refundData['refund_idx'],
                'order_status' => $refundData['order_status'],
                'cancel_num' => $refundData['cancel_num'],
                'left_num' => $refundData['left_num'],
                'is_retry' => $refundParams['isRetry'] ?? false,
                'msg' => '退票成功'
            ];
            pft_log('debug/refundKafka', json_encode(['batch_refund_notice_after_sale_topic', $dataKafka]));
            \Library\Kafka\KafkaProducer::push('platform', 'batch_refund_notice_after_sale_topic', $dataKafka, strval($dataKafka['order_num']));
        }
        pft_log('order_refund/debug', json_encode(['addRefundSuccessTask', 'refundParams', $refundParams],JSON_UNESCAPED_UNICODE));
        if (isset($refundParams['batchRefundMoreIdx']) && !empty($refundParams['batchRefundMoreIdx'])) {
            self::batchMoreRefund($ordernum,$refundParams,$refundData);
        }

        //优惠-订单取消成功通知
        if (isset($refundData['discount']) && !empty($refundData['discount'])) {
            //有积分的时候发
            $dataKafka = [
                'cancelType' => $refundParams['cancelType']== 'common' ? 1 : 2,//1取消 2撤销撤改,
                'params' => [
                    'orderNum' => $refundData['ordernum'],//订单号
                    'tnum'  => $orderInfo['tnum'],//退票前的数量
                    'cancelNum' => $refundData['cancel_num'],//退票数量
                    'originNum' => $orderInfo['origin_num'],//原始订单数量
                    'refundAmount' => $refundData['discount']['refundAmount'],//退票金额
                    'refundCouponPrice' => $refundData['discount']['refundCouponPrice'],//退还积分对应的金额
//                    'refundPointAmount' => $refundData['discount']['refundPointAmount'],//退还积分
                    'refundTicketSerialNumber' => $refundData['discount']['refundTicketSerialNumber']//退还票序号
                ],
            ];
            pft_log('debug/refundKafka', json_encode(['order_refund_topic', $dataKafka]));
            KafkaProducer::push('platform', 'order_refund_topic', $dataKafka, strval($refundData['ordernum']));
        }
        return true;
    }

    public function batchMoreRefund($ordernum,$refundParams,$refundData){
        $cancelList = [];
        $revokeList = [];
        foreach ($refundParams['batchRefundMoreIdx'] as $idx){
            if($idx == reset($refundData['refund_idx'])){
                continue;
            }
            if($refundParams['cancelType'] == 'revoke'){
                $revokeList[]=[
                    'order_num'        => $ordernum,
                    'op_id'            => $refundData['op_id'],
                    'cancel_idx_arr'   => [strval($idx)],
                    'revoke_num'       => 1,
                    'cancel_audit_remark' => '售后退票成功',
                    'cancel_special_arr' => [
                        "after_sale_num" => $refundParams['afterSaleNum'],
                        "is_need_audit" => false,
                        "is_audit_pass" => true,
                        "cancel_audit_remark" => '售后退票成功',
                        "is_allow_more_revoke" => true,
                        'is_cancel_sub'        => true,
                    ],
                    "surplus_num"=> '',
                    "terminal" =>$refundParams['cancelTerminal']
                ];
            }
            else{
                $cancelList[]=[
                    'order_num'        => $ordernum,
                    'cancel_num'       => 1,
                    'op_id'            => $refundData['op_id'],
                    'cancel_channel'   => $refundParams['realCancelChannel'],
                    'cancel_type'      => $refundParams['cancelType'],
                    'req_serial_number'=> '',
                    'cancel_remarkArr' => ['remark' => $refundParams['cancelAuditRemark']],
                    'cancel_siteArr'   => [
                        "terminal" => $refundParams['cancelTerminal']
                    ] ,
                    'cancel_personArr' => [
                        "person_index"=> strval($idx)
                    ] ,
                    'cancel_specialArr' => [
                        "after_sale_num" => $refundParams['afterSaleNum'],
                        "is_need_audit" => false,
                        "is_audit_pass" => true,
                        "is_allow_more_revoke" => true,
                        'is_cancel_sub'        => true,
                    ],
                ];
            }

        }
        $checkJobData = [
            'job_type' => 'batch_order_cancel',
            'job_data' => [
                'cancel_list' => $cancelList,
                'revoke_list' => $revokeList
            ],
        ];
        \Library\Resque\Queue::push('order', 'Order_Job', $checkJobData);
    }

    /**
     * 添加退票审核成功后异步任务
     * <AUTHOR>
     * @date   2019-07-11
     *
     * @param  string  $ordernum
     * @param  array  $orderInfo
     * @param  array  $auditInfo
     * @param  array  $ticketInfo
     *
     * @return  bool
     */
    private function _addRefundAuditTask($ordernum, $orderInfo, $auditInfo, $ticketInfo)
    {
        if ($auditInfo['is_system_audit']) {
            $isRefundAudit = 1;
        } else {
            $isRefundAudit = $ticketInfo['refund_audit'];
        }
        $queueData     = [
            'ordernum'        => $ordernum,
            'orderInfo'       => $orderInfo,
            'auditInfo'       => $auditInfo,
            'is_refund_audit' => $isRefundAudit,
        ];
        $cancelJobData = [
            'job_type' => 'audit_async_task',
            'job_data' => $queueData,
        ];
        $auditJobId    = Queue::push('order', 'Order_Job', $cancelJobData);

        //写入日志
        $logData = json_encode([
            'key'        => 'addRefundAuditTask',
            'ordernum'   => $ordernum,
            'auditJobId' => $auditJobId,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('order_refund/debug', $logData);

        return true;
    }

    /**
     * 处理退款
     * <AUTHOR>
     * @date   2019-07-09
     *
     * @param  string  $ordernum  订单号
     * @param  string  $pftSerialNumber  平台退款流水号
     *
     * @return []
     */
    private function _handlerMoneyTask($ordernum, $pftSerialNumber, $specialData = [])
    {
        $moneyRefundModel = new Refund();
        $moneyRefundRes   = $moneyRefundModel->orderTradeRefund($pftSerialNumber, [], '', null, null, $specialData);

        //写日志
        pft_log('order_refund/debug', json_encode(['refund_money', $ordernum, $moneyRefundRes]));

        if ($moneyRefundRes['code'] == 200) {
            //退款成功
            return $this->orderReturn(200, '退款成功', $moneyRefundRes['data']);
        } else {
            //退款失败
            $errMsg = $moneyRefundRes['msg'] . "【{$moneyRefundRes['code']}】";
            //记录异常日志
            $logParams = [
                'ac'     => 'orderTradeRefund',
                'params' => [$ordernum, $pftSerialNumber, $specialData],
            ];
            $logResponse = [
                'ac'      => 'orderTradeRefund',
                'respone' => $moneyRefundRes,
            ];
            $this->recordAnAbnormal($ordernum,$logParams,$logResponse,PackConst::ABNORMAL_PROCESS_OTHER,PackConst::ABNORMAL_REASON_REFUND,false,$errMsg);
            return $this->orderReturn(0, $errMsg);
        }
    }

    /**
     * 取消必须判断的参数
     * <AUTHOR>
     * @date   2020-01-08
     *
     * @param  int  $ordernum
     * @param  array  $ticketInfo
     * @param  array  $orderInfo
     * @param  array  $subOrderList
     *
     * @return []
     */
    public function _checkMustRefundAttribute($ordernum, $ticketInfo, $orderInfo, $subOrderList)
    {
        //套票类型
        if (($orderInfo['ifpack'] == 1 || $orderInfo['ifpack'] == 2) && $orderInfo['ordermode'] != 24) {
            $checkRes = $this->_checkRefundRule($ticketInfo);
            if ($checkRes['code'] != 200) {
                return $checkRes;
            }
            if (!empty($subOrderList)) {   //套票子票提前判断
                $ticketBiz = new TicketBiz();
                foreach ($subOrderList as $key => $value) {
                    $tid         = $value['tid'];
                    $subOrderNum = $value['orderid'];

                    $ticketRes = $ticketBiz->getListForOrderNew($tid, 0, 0, 0);
                    if (!$ticketRes['ticket_info']) {
                        return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
                    }
                    $subTicketInfo = $ticketRes['ticket_info'];
                    $subCheckRes   = $this->_checkRefundRule($subTicketInfo, "[子票{$subOrderNum}]");
                    if ($subCheckRes['code'] != 200) {
                        return $subCheckRes;
                    }
                }
            }
        } else { //普通类型
            $checkRes = $this->_checkRefundRule($ticketInfo);
            if ($checkRes['code'] != 200) {
                return $checkRes;
            }
        }

        return $this->orderReturn(200, '通过');
    }

    private function _checkRefundRule($ticketInfo, $memo = '')
    {
        $refundRole  = $ticketInfo['refund_rule'];
        $redisCache  = Cache::getInstance('redis');
        $redisKey    = "ticket_refund_withdraw:{$ticketInfo['tid']}";
        $isJumpCheck = $redisCache->get($redisKey);

        if ($refundRole == -1 && !$isJumpCheck) {
            return $this->orderReturn(0, $memo . '可提现不可退', ['err_code' => OrderConst::err1411]);
        }

        return $this->orderReturn(200, '有权限退');
    }

    /**
     * 处理取消游客信息
     * <AUTHOR>
     * @date   2020-01-08
     *
     * @param  int  $ordernum
     * @param  array  $ticketInfo
     * @param  array  $orderInfo
     * @param  array  $subOrderList
     *
     * @return []
     */
    private function _handleRefundTourist($ordernum, $cancelNum, $leftNum, $cancelType, $opId, $orderInfo, $cancelChannel, $ticketSnapshot, $appointIdxData = [],$isAuditPass = false,$cancelTerminal = 0,$afterSaleNum ='', $appointIdxType=[])
    {
        //剩余可取数量
        $canTakeNum = $orderInfo['can_take'];
        $cancelTouristList = [];
        $touristTrackList  = [];
        //可取数量的判断
        $refundCanTakeNum = 0;
        $resData          = [
            'refundIdxList'    => $cancelTouristList,
            'canTakeNum'       => $canTakeNum == -1 ? $canTakeNum : ($canTakeNum - $cancelNum > 0 ? $canTakeNum - $cancelNum : 0),
            'touristTrackList' => $touristTrackList,
        ];
        if (!isset($ticketSnapshot['confs']['print_mode'])) {
            //这边需要判断下tourist表里面有没有值了
            $subOrderModel = new SubOrderQuery();
            $touristData = $subOrderModel->getInfoInTouristByOrder($ordernum,'id');
            if ($touristData){
                $ticketSnapshot['confs']['print_mode'] = 3;
            }else{
                pft_log('order_refund/debug', json_encode(['emptyTourist',$ticketSnapshot['confs']['print_mode'],$appointIdxData, $cancelNum, $ordernum]));
                return $this->returnData(200, '', $resData);
            }

        }
        $qConfigSwitch = new QConfigSwitchController();
        if ($qConfigSwitch::getRefundFixUsedWithTenant($ordernum)) {
            if (isset($appointIdxType['isOnlyCancelNum']) && $appointIdxType['isOnlyCancelNum']) {
                $appointIdxData = $leftNum == 0 ? $appointIdxData : [];
            }
        }
        //判断是不是套票的主票
        $isPackageMain = $orderInfo['ifpack'] == 1 ? true : false;
        //1:不打印，2 :一笔订单一张票，3：一票种一张票 , 4:一人一票
        $printMode    = $ticketSnapshot['confs']['print_mode'];
        $checkMode    = $isPackageMain ? ($ticketSnapshot['confs']['check_mode'] ?? 3) : ($ticketSnapshot['confs']['check_mode'] ?? 1);
        pft_log('order_refund/debug', json_encode(['printModeSwitch',$printMode, $checkMode,$appointIdxData, $cancelNum, $ordernum]));
        $handlerModel = new OrderHandler();
        switch ($checkMode) {
            case 1:   //整批入园
                if ($cancelType == 'revoke') {
                    //1=已验证
                    $checkStateArr = [1];
                } else {
                    $checkStateArr = $isAuditPass ? [0,3, 4] : [0,4];
                }
                $touristList = $handlerModel->getOrderTouristInfo($ordernum, 0, '', $checkStateArr);
                if (empty($touristList)) {
                    pft_log('order_refund/debug', json_encode(['getOrderTouristInfo',$touristList, $printMode, $checkMode,$appointIdxData, $cancelNum, $ordernum]));
                    return $this->returnData(200, '', $resData);
                    //return $this->returnData(0, '无可退门票码', ['err_code' => OrderConst::err1055, 'err_msg' => '无可退门票码']);
                }
                if($isAuditPass){
                    $touristList = $this->_refundTouristListSortForAuditPass($touristList);
                }
                else{
                    $touristList = $this->_refundTouristListSort($touristList);
                }
                //这边根据printMode判断是订单一个码还是多个码
                if ($printMode == 3) {  //只存在一个码整批的如果验证了正常是不能退了
                    if ($appointIdxData && $leftNum != 0 && count($touristList) > 1){  //这个就是一票中然后全身份证  特殊处理
                        foreach ($touristList as $key => $value) {
                            if (in_array($value['idx'], $appointIdxData)) {
                                $cancelTouristList[] = [
                                    'idx'    => $value['idx'],
                                    'status' => 2,
                                    'old_status' => $value['check_state'],
                                    'tnum'   => 1,
                                ];
                                if ($value['print_state'] == 0) {
                                    $refundCanTakeNum += 1;
                                }
                            }
                        }
                    }else{
                        foreach ($touristList as $key => $value){  //多条就代表他是一票中然后全身份证
                            $cancelTouristList[] = [
                                'idx'    => $value['idx'],
                                'status' => $leftNum == 0 ? 2 : $value['check_state'],  //是否全部取消
                                'old_status' => $value['check_state'],
                                'tnum'   => $cancelNum,
                            ];
                        }
                        $refundCanTakeNum    += $cancelNum;
                    }
                    //总数 - 已验证数 + 分批未撤改的
                } else {      //订单数量多少就有多少个码
                    if ($appointIdxData  && $leftNum != 0) {
                        foreach ($touristList as $key => $value) {
                            if (in_array($value['idx'], $appointIdxData)) {
                                $cancelTouristList[] = [
                                    'idx'    => $value['idx'],
                                    'status' => 2,
                                    'old_status' => $value['check_state'],
                                    'tnum'   => 1,
                                ];
                                if ($value['print_state'] == 0) {
                                    $refundCanTakeNum += 1;
                                }
                            }
                        }
                        if (count($cancelTouristList) != count($appointIdxData) || $cancelNum != count($appointIdxData)) {  //说明外部传入的有不能取消的码或者不存在的码
                            pft_log('order_refund/debug', json_encode(['cancelTouristList', 'case 1',$cancelTouristList, $touristList, $appointIdxData, $cancelNum, $printMode, $ordernum]));
                            return $this->orderReturn(0, '传入的游客信息有误', ['err_code' => OrderConst::err1]);
                        }
                    } else {
                        if ($leftNum != 0){  //如果剩余数量都是0了，那就不管他取消哪几个了
                            $touristList = array_slice($touristList, 0, $cancelNum);
                        }
                        if (count($touristList) != $cancelNum) {   //TODO 说明这边有异常，写个日志记录下
                            $logData = [
                                'key'    => 'refund_ticket_code',
                                'params' => [$printMode, $checkMode, $ordernum, $cancelNum, $leftNum, $appointIdxData],
                                'data'   => ['tourist' => $touristList],
                                'code'   => 80,
                            ];
                            pft_log('lc/refund', json_encode($logData));
                            if ($leftNum != 0){  //todo 这种不等于0的先提示出去，然后上面完全取消导致的数量不对的先发钉钉到群里看看是不是和我预想的一样
                                return $this->returnData(0, '门票码可取消数量有误',
                                    ['err_code' => OrderConst::err1056, 'err_msg' => '门票码可取消数量有误']);
                            }
                        }
                        foreach ($touristList as $key => $value) {
                            $cancelTouristList[] = [
                                'idx'    => $value['idx'],
                                'status' => 2,
                                'old_status' => $value['check_state'],
                                'tnum'   => 1,
                            ];
                            if ($value['print_state'] == 0) {
                                $refundCanTakeNum += 1;
                            }
                        }
                    }
                }
                break;
            case 2:   //分批一票种一码
                if ($cancelType == 'revoke') {
                    $checkStateArr = [1, 7];
                } else {
                    $checkStateArr = [0,3, 4, 7];
                }
                $touristList = $handlerModel->getOrderTouristInfo($ordernum, 0, '', $checkStateArr);
                if (empty($touristList)) {
                    pft_log('order_refund/debug', json_encode(['getOrderTouristInfo',$touristList, $printMode, $checkMode,$appointIdxData, $cancelNum, $ordernum]));
                    return $this->returnData(200, '', $resData);
//                    return $this->returnData(0, '无可退门票码', ['err_code' => OrderConst::err1055, 'err_msg' => '无可退门票码']);
                }
                if($isAuditPass){
                    $touristList = $this->_refundTouristListSortForAuditPass($touristList);
                }
                else{
                    $touristList = $this->_refundTouristListSort($touristList);
                }
                if ($printMode == 1){  //这个就是不打印属性坑的一批
                    if ($appointIdxData  && $leftNum != 0) {
                        foreach ($touristList as $key => $value) {
                            if (in_array($value['idx'], $appointIdxData)) {
                                $cancelTouristList[] = [
                                    'idx'    => $value['idx'],
                                    'status' => 2,
                                    'old_status' => $value['check_state'],
                                    'tnum'   => 1,
                                ];
                                if ($value['print_state'] == 0) {
                                    $refundCanTakeNum += 1;
                                }
                            }
                        }
                    } else {
                        if ($leftNum != 0){
                            $touristList = array_slice($touristList, 0, $cancelNum);
                        }
                        foreach ($touristList as $key => $value) {
                            $cancelTouristList[] = [
                                'idx'    => $value['idx'],
                                'status' => 2,
                                'old_status' => $value['check_state'],
                                'tnum'   => 1,
                            ];
                            if ($value['print_state'] == 0) {
                                $refundCanTakeNum += 1;
                            }
                        }
                    }
                }else{
                    //一票种一票 且指定退 且 实名制 且售后编号特殊处理
                    if (!empty($afterSaleNum) && $appointIdxData && count($touristList) > 1){
                        foreach ($touristList as $key => $value) {
                            if (in_array($value['idx'], $appointIdxData)) {
                                $cancelTouristList[] = [
                                    'idx'    => $value['idx'],
                                    'status' => 2,
                                    'old_status' => $value['check_state'],
                                    'tnum'   => 1,
                                ];
                            }
                        }
                    }
                    else{

                        $cancelTouristList[] = [
                            'idx'    => $touristList[0]['idx'],
                            'status' => $leftNum == 0 ? ($orderInfo['verified_num'] > 0 ? 1 : 2) : $touristList[0]['check_state'],
                            'old_status' => $touristList[0]['check_state'],
                            'tnum'   => $cancelNum,
                        ];
                    }
                    $refundCanTakeNum    += $cancelNum;
                }
                break;
            case 3:   //分批入园
                if ($cancelType == 'revoke') {
                    $checkStateArr = [1, 7];
                } else {
                    $checkStateArr = $isAuditPass ? [0,3, 4] : [0,4];
                }
                $touristList = $handlerModel->getOrderTouristInfo($ordernum, 0, '', $checkStateArr);
                if (empty($touristList)) {
                    pft_log('order_refund/debug', json_encode(['getOrderTouristInfo',$touristList, $printMode, $checkMode,$appointIdxData, $cancelNum, $ordernum]));
                    return $this->returnData(200, '', $resData);
                    //return $this->returnData(0, '无可退门票码', ['err_code' => OrderConst::err1055, 'err_msg' => '无可退门票码']);
                }
                if($isAuditPass){
                    $touristList = $this->_refundTouristListSortForAuditPass($touristList);
                }
                else{
                    $touristList = $this->_refundTouristListSort($touristList);
                }
                if ($appointIdxData && $leftNum != 0) {
                    foreach ($touristList as $key => $value) {
                        if (in_array($value['idx'], $appointIdxData)) {
                            $cancelTouristList[] = [
                                'idx'    => $value['idx'],
                                'status' => 2,
                                'old_status' => $value['check_state'],
                                'tnum'   => 1,
                            ];
                            if ($value['print_state'] == 0) {
                                $refundCanTakeNum += 1;
                            }
                        }
                    }
                    if (count($cancelTouristList) != count($appointIdxData) || $cancelNum != count($appointIdxData)) {  //说明外部传入的有不能取消的码或者不存在的码
                        pft_log('order_refund/debug', json_encode(['cancelTouristList', 'case 3',$cancelTouristList, $touristList, $appointIdxData, $cancelNum, $printMode, $ordernum]));
                        return $this->returnData(0, '传入的游客信息有误', ['err_code' => OrderConst::err1]);
                    }
                } else {
                    if ($leftNum != 0){
                        $touristList = array_slice($touristList, 0, $cancelNum);
                    }
                    if (count($touristList) != $cancelNum) {   //TODO 说明这边有异常，前几个月可能先观察下，免的不能退P0，写个日志记录下
                        $logData = [
                            'key'    => 'refund_ticket_code',
                            'params' => [$printMode, $checkMode, $ordernum, $cancelNum, $leftNum, $appointIdxData],
                            'data'   => ['tourist' => $touristList],
                            'code'   => 82,
                        ];
                        pft_log('lc/refund', json_encode($logData));
                        if ($leftNum != 0){  //和上面的tudo一样的意思
                            return $this->returnData(0, '门票码可取消数量有误',
                                ['err_code' => OrderConst::err1056, 'err_msg' => '门票码可取消数量有误']);
                        }
                    }
                    foreach ($touristList as $key => $value) {
                        $cancelTouristList[] = [
                            'idx'    => $value['idx'],
                            'status' => 2,
                            'old_status' => $value['check_state'],
                            'tnum'   => 1,
                        ];
                        if ($value['print_state'] == 0) {
                            $refundCanTakeNum += 1;
                        }
                    }
                }

                break;
            default:
                if ($cancelType == 'revoke') {
                    $checkStateArr = [1];
                } else {
                    $checkStateArr = $isAuditPass ? [0,3, 4] : [0,4];
                }
                $touristList = $handlerModel->getOrderTouristInfo($ordernum, 0, '', $checkStateArr);
                if (empty($touristList)) {
                    return $this->returnData(200, '', $resData);
                    //return $this->returnData(0, '无可退门票码', ['err_code' => OrderConst::err1055, 'err_msg' => '无可退门票码']);
                }
                if($isAuditPass){
                    $touristList = $this->_refundTouristListSortForAuditPass($touristList);
                }
                else{
                    $touristList = $this->_refundTouristListSort($touristList);
                }
                if ($leftNum != 0){  //如果剩余数量都是0了，那就不管他取消哪几个了
                    $touristList = array_slice($touristList, 0, $cancelNum);
                }
                foreach ($touristList as $key => $value) {
                    $cancelTouristList[] = [
                        'idx'    => $value['idx'],
                        'status' => 2,
                        'old_status' => $value['check_state'],
                        'tnum'   => $cancelNum,
                    ];
                    if ($value['print_state'] == 0) {
                        $refundCanTakeNum += $cancelNum;
                    }
                }
                //return $this->returnData(0, '未知的验证方式', ['err_code' => OrderConst::err1]);
                break;
        }
        $insertTime = time();
        $refundIdx  = [];
        foreach ($cancelTouristList as $tourist) {
            if ($tourist['status'] == CommonOrderTouristStatus::TOURIST_CANCELED_CODE){
                $refundIdx[] = $tourist['idx'];
            }
            $touristTrackList[] = [
                'ordernum'       => $ordernum,
                'idx'            => $tourist['idx'],
                'trackAction'    => $cancelType == 'revoke' ? 5 : 2,  //区分下撤销和取消  报表要用
                'tid'            => $orderInfo['tid'],
                'tnum'           => $tourist['tnum'],
                'source'         => $cancelChannel,
                'terminal'       => $cancelTerminal,
                'branchTerminal' => 0,
                'salerId'        => $orderInfo['salerid'],
                'insertTime'     => $insertTime,
                'operMember'     => $opId,
                'msg'            => '',
                'extContent'     => '',
                'applyDid'       => $orderInfo['apply_id'],

            ];
        }
        $resData['refundIdxList']    = $cancelTouristList;
        $resData['canTakeNum']       = $canTakeNum == -1 ? $canTakeNum : ($canTakeNum - $refundCanTakeNum > 0 ? $canTakeNum - $refundCanTakeNum : 0);
        $resData['touristTrackList'] = $touristTrackList;
        $resData['refundIdx']        = $refundIdx;
        //传入完整的touristList，以$refundIdx实际要退票的游客idx来做过滤，取出最终退票的游客数据
        $resData['personInfoList']   = static::getRefundPersonInfoListDTO($touristList, $refundIdx);
        //日志记录-用作问题排查
        $logDataRes = [
            'params' => [$printMode, $checkMode, $ordernum, $cancelNum, $leftNum, $appointIdxData],
            'data'   => ['touristList' => $touristList, 'refundIdxList' => $cancelTouristList, 'refundIdx'=>$refundIdx, 'personInfoList'=>$resData['personInfoList']],
            'code'   => 82,
        ];
        pft_log('lc/tourist', json_encode($logDataRes));
        return $this->returnData(200, '', $resData);
    }
    /**
     * 临时写的排序，其他地方不要调用
     * <AUTHOR>
     * @date   2020-01-08
     * @param $arrays array 游客表数组
     * @return []
     */
    private function _refundTouristListSort($arrays){
        if (!is_array($arrays) || !is_array($arrays[0])) {
            return $arrays;
        }
        foreach ($arrays as $key => $value){
            $dataOne[$key] = $value['check_state'];
            $dataTwo[$key] = $value['print_state'];
        }
        array_multisort($dataOne, SORT_ASC, $dataTwo, SORT_ASC, $arrays);
        return $arrays;
    }

    /**
     * 为了解决实名制情况下的取消错位问题
     * 只有实名制 才有审核状态，
     * @param $arrays
     * @return array
     */
    private function _refundTouristListSortForAuditPass($arrays){
        if (!is_array($arrays) || !is_array($arrays[0])) {
            return $arrays;
        }
        $expire = [];
        $init = [];
        $audit = [];
        $partialUse = [];
        $res = [];
        foreach ($arrays as $key => $value){
            if($value['check_state'] == 4){
                array_push($expire,$value);
            }
            if($value['check_state'] == 0){
                array_push($init,$value);
            }
            if($value['check_state'] == 3){
                array_push($audit,$value);
            }
            if($value['check_state'] == 7){
                array_push($partialUse,$value);
            }
        }
        if(!empty($audit)){
            $res = array_merge($res,$audit);
        }
        if(!empty($expire)){
            $res = array_merge($res,$expire);
        }
        if(!empty($init)){
            $res = array_merge($res,$init);
        }
        if(!empty($partialUse)){
            $res = array_merge($res,$partialUse);
        }
        return $res;
    }


    private function _isAllowPartUsedRefund(int $applyDid)
    {
        if(empty($applyDid)) {
            return false;
        }
        $moduleConfigBiz = new ModuleConfig();
        $resultRes = $moduleConfigBiz->getOpenModuleBySidAndAppKey([$applyDid], 'special_pack_check', 60);
        if($resultRes['code'] != 200) {
            return false;
        }
        return !empty($resultRes['data']);
    }

    /**
     * 校验下外部传入的指定取消idx的状态
     * <AUTHOR>
     * @date   2020-01-08
     *
     * @param string  $ordernum     订单号
     * @param  array  $appointIdxList  外部需要取消的
     * @param  array  $appointIdxType  外部需要取消的
     * @param  array  $cancelChannel  取消渠道
     *
     * @return array
     */
    public function _checkRefundTouristStatus($ordernum,$appointIdxList,$appointIdxType,$cancelChannel){
        foreach ($appointIdxList as $key => $value){
            if (in_array($value['check_state'],[CommonOrderTouristStatus::TOURIST_USED_CODE,CommonOrderTouristStatus::TOURIST_CANCELED_CODE,CommonOrderTouristStatus::TOURIST_SOME_USED_CODE])){   //如果是已使用或者部分使用的不让他退
                if ($appointIdxType['idCard'] == true){   //身份证的观察下，有可能三方会乱传，这边先记录下日志让他过
                    pft_log('refund/idx',$ordernum.':身份证取消状态有误'.$value['idx']);
                    Helpers::sendDingTalkGroupRobotMessageRaw("订单号{$ordernum}传的身份证状态有点问题快去看看",
                        DingTalkRobots::CANCEL_AND_VERIFY);
                }elseif ($cancelChannel == 31){  //这种套票主票取消的情况出发的子票可能出现对应的idx已经被子票取消的情况或者找不到对应的情况，这种让他过
                    pft_log('refund/idxpack',$ordernum.':子票对应不上'.$value['idx']);
                    Helpers::sendDingTalkGroupRobotMessageRaw("订单号{$ordernum}门票码子票对应不上",
                        DingTalkRobots::CANCEL_AND_VERIFY);
                }else{
                    return $this->returnData(0, '取消的门票码已使用或者已取消', ['err_code' => OrderConst::err1057, 'err_msg' => $value['idx'].'号码状态不可以退']);
                }
            }
        }
        return $this->returnData(200,'success');
    }

    /**
     * 判断是不是温泉系统的订单
     * <AUTHOR>
     * @date   2020-01-08
     *
     * @param  string  $ordernum  订单号
     * @param  array  $orderInfo  订单信息
     *
     * @return bool
     */
    private function checkIsPresumingOrder($ordernum, $orderInfo)
    {
        $isPresumingOrder = false;
        $presumingOrder   = $ordernum;
        //是否是温泉系统订单
        if ($orderInfo['ifpack'] == 2) {
            $presumingOrder = $orderInfo['pack_order'];
        }
        $presumingMdl       = new Presuming();
        $presumingOrderInfo = $presumingMdl->orderInfo($presumingOrder);
        if ($presumingOrderInfo) {
            $isPresumingOrder = true;
        }

        return $isPresumingOrder;
    }

    /**
     * 获取分终端未使用完的门票数量(通过门票码状态)
     * author queyourong
     * date 2022/8/1
     * @param string $ordernum
     * @param array $ticketSnapshot
     * @return int
     */
    private function _isBranchUsedByTourist($ordernum, $ticketSnapshot)
    {
        if(!$this->_isBranchOrder($ticketSnapshot['uuJqTicketDTO']['chkTerminalInfo'])) {
            return 0;
        }

        $subOrderModel = new SubOrderQuery();
        $statusList          = $subOrderModel->getInfoInTouristByOrder($ordernum, 'check_state', 1, [], '', true);
        $statusList = array_filter($statusList, function ($item) {
            return $item['check_state'] == 7; //过滤出部分使用状态下的门票码数量
        });
        return count($statusList);
    }

    /**
     * 根据票属性判断是否分终端订单
     * author queyourong
     * date 2022/8/2
     * @param $chkTerminalInfo
     *
     * @return bool
     */
    private function _isBranchOrder($chkTerminalInfo)
    {
        if(empty($chkTerminalInfo)) {
            return false;
        }
        $info = @json_decode($chkTerminalInfo, true);
        if(empty($info)) {
            return false;
        }
        return true;
    }

    /**
     * 获取撤改订单信息
     * author queyourong
     * date 2022/8/2
     * @param array $orderInfo
     * @param $leftNum
     * @param $cancelNum
     * @param int $branchIsUsedNum
     * @param $refundIdxList
     *
     * @return array
     */
    private function _getRevokeOrderRefundInfo(array $orderInfo, $cancelNum, $leftNum, $branchIsUsedNum, $refundIdxList)
    {
        if($orderInfo['status'] == 2) {
            //如果订单状态为过期, 则直接不变
            return [2, 0, 0];
        }
        $revokeBranchUseCnt = 0;
        //撤改后的可使用票数 = 票数 - 已验证票数(包括了已入园) + 分终端还未验完票数
        $revokeCanUseNum = $orderInfo['tnum'] - $orderInfo['verified_num'] + $branchIsUsedNum;

        if($branchIsUsedNum > 0) {
            //如果分终端还有未验完票, 需要看一下本次取消的票中有没有已入园(部分使用)的票
            //因为只要分终端未验完票>0, 代表一定已开启分终端, 则check_state = 7 一定是已入园
            $revokeBranchUseCnt = count(array_filter($refundIdxList, function ($item) {
                return $item['old_status'] == 7;
            }));
            //最终可使用的票数再减去分终端还未验完票
            $revokeCanUseNum -= $revokeBranchUseCnt;
        }

        $realRevokeBranchIsUseNum = $branchIsUsedNum - $revokeBranchUseCnt;
        if ($leftNum == 0) {
            //撤销，全部退票
            $status      = 6;
        } else {
            //撤改，部分退票
            $status      = 5;
            if($cancelNum >= $orderInfo['verified_num']) {
                //还有未撤改(未使用)的票 并且 撤改票数==已验证票数
                $status = 0; //订单状态变更回未使用
            } else if(($orderInfo['verified_num'] - $realRevokeBranchIsUseNum) < $orderInfo['tnum']) {
                //如果还有未使用的票(总票数-(验证票数 - 分终端没有使用完的票)>0), 订单状态变更为部分使用
                $status = 7;
            }
        }

        return [$status, $revokeCanUseNum, $leftNum];
    }

    private function _LockRefundTouristForDiscount($orderNum, $opType, $ticketNum){
        //中台获取锁定可退票
        $orderTicketDiscounts = new OrderTicketDiscounts();
        $res = $orderTicketDiscounts->lockAndGet($orderNum, $opType, $ticketNum);
        if($res['code']!=200 || empty($res['data'])){
            return [];
        }
        else{
            $resData = $this->_handleDiscountList($orderNum,$res['data']);
            $resData['orderNum'] = $orderNum;
            $resData['cancelNum'] = $ticketNum;
            $resData['opType'] = $opType;
            //返回序号以及明细
            return $resData;
        }
    }

    private function _getRefundTouristForDiscount($orderNum, $status, $serialNums){
        $orderTicketDiscounts = new OrderTicketDiscounts();
        $res = $orderTicketDiscounts->list($orderNum, $status, $serialNums);
        if($res['code']!=200 || empty($res['data'])){
            return [];
        }
        else{
            $resData = $this->_handleDiscountList($orderNum,$res['data']);
            $resData['orderNum'] = $orderNum;
            $resData['serialNums'] = $serialNums;
            $resData['status'] = $status;
            //返回序号以及明细
            return $resData;
        }
    }

    private function _handleDiscountList($orderNum,$discountRes){
        $resData = [
            'refundTicketSerialNumber' =>'',//String '1-2,4-6'
            'refundAmount' => 0,
            'refundCouponPrice' => 0,
        ];
        //计算总退款金额 【当前退票数对应的实付退款金额以及积分】
        $serialNumArr =  array_column($discountRes,'serialNum');
        sort($serialNumArr);
        $discountApi = new \Business\JavaApi\Order\OrderTicketDiscounts();
        $res = $discountApi->listDiscounts($orderNum,$serialNumArr);
        if($res['code']!=200 || empty($res['data'])){
            return $resData;
        }
        $refundAmount = 0;
        $refundCouponPrice = 0;
        foreach ($res['data'] as $k => $v){
            $refundAmount += $v['payPrice'];
            $refundCouponPrice += $v['couponPrice'];
        }


        $temp = array();
        $i = 0;
        foreach($serialNumArr as $v){
            if(!isset($temp[$i])){
                $temp[$i] =[];
            }
            array_push($temp[$i],$v);
            if(next($serialNumArr) != $v+1){
                $i++;
            }
        }
        //转化格式对外格式
        foreach($temp as $k =>$v){
            $temp[$k] = implode('-',[reset($v),end($v)]);
        }
        $refundTicketSerialNumber = implode(",",$temp);
        $resData['refundTicketSerialNumber'] = $refundTicketSerialNumber;
        $resData['refundAmount'] = $refundAmount;
        $resData['refundCouponPrice'] = $refundCouponPrice;
        pft_log('order_refund/debug',json_encode(['handleDiscountList',$res,$resData, $orderNum],JSON_UNESCAPED_UNICODE));
        return $resData;
        /*
        //切割连续序号为多个数组
        $temp = array();
        $i = 0;
        foreach($serialNumArr as $v){
            if(!isset($temp[$i])){
                $temp[$i] =[];
            }
            array_push($temp[$i],$v);
            if(next($serialNumArr) != $v+1){
                $i++;
            }
        }
        //转化格式对外格式
        foreach($temp as $k =>$v){
            $temp[$k] = implode('-',[reset($v),end($v)]);
        }
        $refundTicketSerialNumber = implode(",",$temp);
        $refundAmount = array_sum(array_column($discountRes,'payPrice'));//单位 分
        $refundCouponPrice = array_sum(array_column($discountRes,'couponPrice')); //单位 分
//        $refundPointAmount = array_sum(array_column($discountRes,'couponValue'));
        return [
            'refundTicketSerialNumber' =>$refundTicketSerialNumber,//String '1-2,4-6'
            'refundAmount' => $refundAmount,
            'refundCouponPrice' => $refundCouponPrice,
//            'refundPointAmount' => $refundPointAmount,
        ];
        */
    }

    /**
     * 根据开放功能管控 - 人工审核- 更多配置
     * @param $ticketRes
     * @param $orderInfo
     * @return array
     */
    private function _judgeRefundAuditSetting($ticketRes,$orderInfo){
        $_ruleMap = [
            'default' => '0',//未设置
            'un_expired' => '1',//未过期_需审核
            'expired' => '2',//已过期_需审核
        ];
        $isChangeAuditSetting = false;
        $isNeedAudit = $ticketRes['ticket_info']['refund_audit'];
        $refundRule = $ticketRes['ticket_info']['refund_rule'];
        $tmpTicketInfo = $ticketRes['ticket_info'];
        $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
        $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);
        $isThirdOrder = $ticketInfo['Mdetails'] == 1 && $ticketInfo['sourceT'] > 0 ? true : false;
        //增加开放功能二次校验
        $AuthContext = new AuthContext();
        $isHaveOpenFunc = false;
        $openAppRes = $AuthContext->checkAccessByKey($ticketRes['ticket_info']['apply_did'],['refund_audit_setting']);
        if ($openAppRes['code'] == 200 && isset($openAppRes['data']['refund_audit_setting']) && $openAppRes['data']['refund_audit_setting']) {
            $isHaveOpenFunc = true;
        }
        pft_log('refund/setting',json_encode([$isHaveOpenFunc,$isThirdOrder,$ticketInfo],JSON_UNESCAPED_UNICODE));
        //未开放或三方票，不受此规则约束
        if (!$isHaveOpenFunc || $isThirdOrder){
            return ['is_change_setting' => $isChangeAuditSetting];
        }
        if($isNeedAudit && in_array($refundRule,[0,1,3,4])){
            $orderStatus = $orderInfo['status'];
            $refundRuleScope = $ticketRes['ticket_info']['refund_rule_scope'] ?? '';
            $ruleList = explode(',',$refundRuleScope);
            //增加为0 或者 空 的判断
            if(empty($refundRuleScope) || in_array($_ruleMap['default'],$ruleList)){
                return ['is_change_setting' => $isChangeAuditSetting,'is_need_audit' => $isNeedAudit];
            }
            switch ($orderStatus){
                case CommonOrderStatus::EXPIRED_CODE://已过期
                    if(!in_array($_ruleMap['expired'],$ruleList) ){
                        $isChangeAuditSetting = true;
                        $isNeedAudit = false;
                    }
                    break;
                case CommonOrderStatus::BE_CONFIRMED_CODE:
                case CommonOrderStatus::SOME_USED_CODE:
                case CommonOrderStatus::WAIT_PRINTTICKET_CODE:
                case CommonOrderStatus::WAIT_APPOINTMENT_CODE:
                case CommonOrderStatus::UNUSED_CODE ://未过期
                    if(!in_array($_ruleMap['un_expired'],$ruleList) ){
                        $isChangeAuditSetting = true;
                        $isNeedAudit = false;
                    }
                    break;
                default:
                    $isChangeAuditSetting = false;
            }
        }
        return ['is_change_setting' => $isChangeAuditSetting,'is_need_audit' => $isNeedAudit];
    }

    /**
     *  预售券产品：获取处于部分兑换的数量
     *
     * @param $ordernum
     * @param $ticketInfo
     *
     * @return int
     */
    private function _getExchangePartExchangeNum($ordernum, $ticketInfo) {
        if($ticketInfo['p_type'] == 'A' && $ticketInfo['sub_type'] == 1) {
            //预售券产品
            $exchangeTicketOrderLib = new \Business\JavaApi\Order\ExchangeTicketOrder();
            $queryRes = $exchangeTicketOrderLib->getPartExchangeNum($ordernum);

            if($queryRes['code'] == 200) {
                $exchangePartExchangeNum = intval($queryRes['data']);
            } else {
                $exchangePartExchangeNum = 0;
            }
        } else {
            //非预售券产品
            $exchangePartExchangeNum = 0;
        }

        return $exchangePartExchangeNum;
    }

    /**
     * 校验预售券 可退数量
     * @param $ordernum
     * @param $ticketInfo
     * @return array
     * @throws Exception
     */
    private function _checkCancelOrder($ordernum, $ticketInfo, $refundIdx) {
        $code = 200;
        $msg = 'success';
        $data = [];
        if($ticketInfo['p_type'] == 'A' && $ticketInfo['sub_type'] == 1) {
            //预售券产品
            $exchangeTicketOrderLib = new \Business\JavaApi\Order\ExchangeTicketOrder();
            $queryRes = $exchangeTicketOrderLib->checkCancelOrder($ordernum,$refundIdx);
            if($queryRes['code'] == 200) {
                $data = $queryRes['data'];
            } else {
                $code = $queryRes['code'];
                $msg = $queryRes['msg'] ?? '预售券校验异常';
            }
        } else {
            //非预售券产品
            $data = [];
        }

        return $this->returnData($code,$msg,$data);
    }

    /**
     * 子票 三方退票置换身份信息
     * @param $ordernum
     * @param $personIdList
     * @param $modifyNum
     * @return array
     * @throws Exception
     */
    public function sonTicketRealNameForThird($orderNum,$pftSerialNumber,$personIdList,$modifyNum){
        $orderModel   = new OrderTools('localhost');
        $tmpOrderInfo = $orderModel->getInfoForCancel($orderNum);
        $orderInfo = $tmpOrderInfo['order_info'];
        if (empty($orderInfo)) {
            return $this->orderReturn(0, "订单数据不存在", ['err_code' => OrderConst::err11]);
        }
        $ticketBiz = new TicketBiz();
        $tid = $orderInfo['tid'];
        $ticketRes = $ticketBiz->getListForOrderNew($tid);
        if (!$ticketRes['ticket_info']) {
            return $this->orderReturn(0, '门票数据不存在', ['err_code' => OrderConst::err1051]);
        }
        $tmpTicketInfo = $ticketRes['ticket_info'];
        $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
        $ticketInfo    = array_merge($tmpTicketInfo, $ticketExtInfo);
        pft_log('order_refund/debug',json_encode(['sonTicketRealNameForThird', $orderNum, $pftSerialNumber, $personIdList, $modifyNum]));
        if(empty($personIdList) && $pftSerialNumber){
            $refundModel = new OrderRefund();
            $arrPftSerialNumber = explode('_',$pftSerialNumber);
            $refundId = reset($arrPftSerialNumber);
            $refundInfo  = $refundModel->getRefundJournalById($refundId);
            if(empty($refundInfo)){
                return $this->orderReturn(0, '主订单退票数据不存在', ['err_code' => OrderConst::err1051]);
            }
            $auditDetails = $refundInfo['details']??[];
            if(empty($auditDetails)){
                return $this->orderReturn(0, '主订单数据退票数据组缺失', ['err_code' => OrderConst::err1051]);
            }
            $auditDetailsArr = json_decode($auditDetails,true);
            $refundIdxArr = isset($auditDetailsArr['refundIdx']) ? $auditDetailsArr['refundIdx'] : [$auditDetailsArr['personIndex']];
            if(empty($refundIdxArr)){
                return $this->orderReturn(0, '主订单数据退票数据缺失', ['err_code' => OrderConst::err1051]);
            }
            list($personIdList,$idxRes) = self::getRealNameDataForRefundIdxArr($orderNum,$refundIdxArr);
            if(count($personIdList)==0){
                return $this->returnData(200 ,'不需要置换',['personIdList'=>$personIdList, 'personInfoList'=>[]]);
            }
        }
        //是否使用改造后的放量
        $qConfigSwitch = new QConfigSwitchController();
        $usedRefundFix = false;
        if ($qConfigSwitch::getRefundFixUsedWithTenant($orderNum)) {
            $usedRefundFix = true;
            $fixObj = new HandleTourist();
        }
        if($modifyNum>count($personIdList)){
            $handlerModel = new OrderHandler();
            //获取未使用票数据，根据身份优先匹配【套票主票不支持撤销撤改，即便部分情况下支持也不联动子票】
            //如果是一票一证的门票，就需要获取未使用的码
            if ($usedRefundFix) {
                $leftTouristArr = $fixObj->getOrderLeftTourist($orderNum, $orderInfo);
                $availableIdCardArr = $fixObj->getAvailableIdNumberArr($leftTouristArr);
            } else {
                $leftTouristArr = $this->_getOrderLeftTourist($orderNum, $modifyNum, $ticketInfo, $orderInfo);
                $availableIdCardArr = $leftTouristArr['availableIdNumberArr'];
            }
            pft_log('order_refund/debug',json_encode(['sonTicketRealNameForThird_getAvailableIdNumberArr', $availableIdCardArr, $personIdList, $orderNum, $modifyNum, $personIdList]));

            foreach ($personIdList as $personId) {
                if(!in_array($personId,$availableIdCardArr)){
                    if ($orderInfo['status'] == 2) {  //过期的时候要获取状态为4的
                        $checkStateArr = [0, 4];
                    } else {
                        $checkStateArr = [0];
                    }
                    $allAvailableTouristArr = $handlerModel->getOrderTouristInfo($orderNum, 0, $personId, $checkStateArr);
                    if(empty($allAvailableTouristArr)){
                        return $this->returnData(257 ,"三方系统申请退票失败:该证件不可退{$personId}",$personIdList);
                    }
                }
                else{
                    //过滤已指定的身份证
                    $filteredArray = array_filter($availableIdCardArr, function($item) use ($personId) {
                        return $item != $personId;
                    });
                    $availableIdCardArr = array_values($filteredArray);
                }
            }
            $diffNum = $modifyNum - count($personIdList);
            $diffArr = array_slice($availableIdCardArr,0,$diffNum);
//            if(empty($diffArr)){
//                return $this->returnData(257 ,'三方系统申请退票失败:可退证件数量不足',$personIdList);
//            }
            if(!empty($diffArr)){
                $personIdList = array_merge($personIdList,$diffArr);
            }
            $personInfoList = [];
            foreach ($personIdList as $personId) {
                foreach ($leftTouristArr as $itemTourist) {
                    //如果为空，则直接跳过，这个
                    if (empty($itemTourist['idcard'])) {
                        continue;
                    }
                    if ($personId == $itemTourist['idcard']) {
                        $personInfoList[] = [
                            "person_index"=>$itemTourist['idx'],
                            "idcard"=>$itemTourist['idcard'],
                            "idcard_type"=>$itemTourist['voucher_type'],
                            "ticket_code"=>$itemTourist['chk_code'],
                        ];
                    }
                }
            }
            return $this->returnData(200 ,'完成置换',['personIdList'=>$personIdList, 'personInfoList'=>$personInfoList]);
        }
        elseif($modifyNum==count($personIdList)){
            return $this->returnData(200 ,'不需要置换',['personIdList'=>$personIdList, 'personInfoList'=>[]]);
        }
        else{
            return $this->returnData(257, "三方系统申请退票失败:修改数量与身份证不一致");
        }
    }

    /**
     * 根据订单号置换证件信息
     * @param $orderNum
     * @param $refundIdxArr
     * @return array
     */
    public function getRealNameDataForRefundIdxArr($orderNum,$refundIdxArr){
        //针对发起主票审核时没有填写身份证的情况 此时子票应与主票取消身份证一致
        $subOrderModel = new SubOrderQuery();
        $personIdList = [];
        $field = 'orderid,id,tourist,idcard,mobile,print_state,voucher_type,check_state,idx, chk_code';
        $idxRes = $subOrderModel->getInfoInTouristByOrder($orderNum,
            $field, 2, ['idx'=>['in',$refundIdxArr]], '', true);
        if (empty($idxRes)) {
            pft_log('order_refund/debug',
                json_encode(['ThirdRefundIdxRes', $idxRes, $refundIdxArr,$orderNum]));
            return $this->orderReturn(0, "退票参数错误，无法置换上游所需身份信息参数", ['err_code' => OrderConst::err1]);
        }
        //三方没有强约束身份证，其他证件类型不可信，应过滤。
        foreach ($idxRes as $idxInfo){
            if(in_array($idxInfo['idcard'],$personIdList)){
                continue;
            }
            //仅取出有证件号的数据
            if(!empty($idxInfo['idcard'])){
                array_push($personIdList,$idxInfo['idcard']);
            }
        }
        return [$personIdList,$idxRes];
    }

    /**
     * 获取退票游客信息DTO
     * @param $touristInfoList
     * @return array
     */
    public static function getRefundPersonInfoListDTO($touristInfoList, $refundIdxArr = [])
    {
        $result = [];
        if (empty($touristInfoList) || empty($refundIdxArr)){
            return $result;
        }
        foreach ($touristInfoList as $item) {
            if (!in_array($item['idx'], $refundIdxArr)) {
                continue;
            }
            $result[] = [
                "person_index" => $item['idx'],
                "idcard" => $item['idcard'],
                "idcard_type" => $item['voucher_type'],
                "ticket_code" => $item['chk_code'],
            ];
        }
        return $result;
    }

    /**
     * 获取退票请求第三方是否增加idcard_info_list配置开关
     * @return bool
     */
    public static function getRefundParamsToOpenApiWithQconf()
    {
        $key = 'refund_idcard_info_open_api';
        if (!extension_loaded('qconf')) {
            return false;
        }
        $refundIdCardInfoOpenApi = \qconf::getConf("/php/platform/".$key);
        if (empty($refundIdCardInfoOpenApi)){
            return false;
        }
        return true;
    }
}
