<?php
/**
 * 第三方系统订单交互
 * 平台的演出系统也算是第三方系统
 *
 * <AUTHOR>
 * @date 2019-02-15
 *
 */

namespace Business\Order;

use Business\Base;
use Library\Constants\OrderConst;
use Library\Container;
use Library\Tools\Helpers;
use Library\Cache\Cache;

class ThirdSystem extends Base
{

    /**
     * 往第三方系统下单
     * <AUTHOR>
     * @data   2019-03-25
     *
     * @param  array  $params  参数
     *
     * @return []
     */
    public function commonOrder($params)
    {
        //开始时间
        $startTime = microtime(true);

        if ($params['sourceT'] == 3) {
            // 开放接口订单支付信息推送
            $rMret = $this->_callOtaOpenOrder($params);
        } else {
            //类型
            $params['Action'] = 'common_order';
            $version          = $this->_getSysVersion($params['LandId']);
            if (!empty($version) && $version == 'new') {
                $rMret = Helpers::callThirdSystemJsonRpc($params);
            } else {
                $rMret = Helpers::callThirdSystem($params);

                pft_log('third_call', json_encode([1, $params], JSON_UNESCAPED_UNICODE));
            }
        }

        //花费时间
        $endTime  = microtime(true);
        $costTime = $endTime - $startTime;

        $rMret   = strval($rMret);
        $arrMret = explode('|', $rMret);

        //记录请求日志
        $logData = json_encode([
            'req'      => $params,
            'response' => $rMret,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('open/third_order_system', $logData);

        //下单时间超过10秒
        if (intval($costTime) >= 10) {
            $queryModel = new \Model\Ota\OtaQueryModel();
            $coopInfo   = $queryModel->getCsysid($params['LandId']);

            $timeOutMsg = [
                "ordernum"       => $params['PftOrderSn'],
                'cost_time'      => intval($costTime),
                'third_sys_name' => $coopInfo['name'],
            ];
            pft_log('open/third_order_system_timeout', json_encode($timeOutMsg, JSON_UNESCAPED_UNICODE));
        }

        if ($arrMret[0] != 200) {
            //下单报错
            $errMsg = "第三方系统错误代码:{$arrMret[0]},错误描述:{$arrMret[1]}";

            return $this->orderReturn(0, $errMsg, ['err_code' => $arrMret[0]]);
        } else {
            //请求成功
            $resArr = [
                'VTcode'     => '',
                'VTordernum' => '',
            ];

            //第三方的凭证码
            if (isset($arrMret[4]) && $arrMret[4]) {
                $resArr['VTcode'] = strval($arrMret[4]);
            }

            //第三方订单号
            if (isset($arrMret[5]) && $arrMret[5]) {
                $resArr['VTordernum'] = strval($arrMret[5]);
            }

            return $this->orderReturn(200, '', $resArr);
        }
    }

    /**
     * 往第三方系统下单2.0版本
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function commonOrderV2($params)
    {
        //开始时间
        $startTime = microtime(true);

        $params['method'] = 'order.create';
        $params['request_id'] = uniqid('req_', true) . mt_rand(1000, 9999);
        $resArr = Helpers::callThirdSystemJsonRpcStandard($params, 'CommonOrder/Order/common');

        //花费时间
        $endTime  = microtime(true);
        $costTime = $endTime - $startTime;

        //记录请求日志
        $logData = json_encode([
            'version'  => '2.0',
            'req'      => $params,
            'response' => $resArr,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('open/third_order_system', $logData);

        //下单时间超过10秒
        if (intval($costTime) >= 10) {
            $queryModel = Container::pull(\Model\Ota\OtaQueryModel::class);
            $coopInfo   = $queryModel->getCsysid($params['LandId']);

            $timeOutMsg = [
                "ordernum"       => $params['PftOrderSn'],
                'cost_time'      => intval($costTime),
                'third_sys_name' => $coopInfo['name'],
            ];
            pft_log('open/third_order_system_timeout', json_encode($timeOutMsg, JSON_UNESCAPED_UNICODE));
        }

        if ($resArr['code'] == 200) {
            //请求成功
            $resArr = [
                //第三方的凭证码
                'VTcode'     => $resArr['data']['api_code'],
                //第三方订单号
                'VTordernum' => $resArr['data']['api_order'],
                //{voucher:'', qrcode_url:'', certificate_id:''}
                'voucherList' => $resArr['data']['voucher_list'],
            ];
            return $this->orderReturn(200, '出票成功', $resArr);
        } else if ($resArr['code'] == OrderConst::err598) {
            return $this->orderReturn($resArr['code'], '三方出票中', ['err_code' => $resArr['code']]);
        } else {
            //下单报错
            $errMsg = "第三方系统错误代码:{$resArr['code']},错误描述:{$resArr['msg']}";
            return $this->orderReturn(0, $errMsg, $resArr['data']);
        }
    }

    /**
     * 往演出系统下单
     * <AUTHOR>
     * @data   2019-03-25
     *
     * @param  array  $postData  参数
     *
     * @return []
     */
    public function showOrder($postData)
    {
        $tid            = $postData['TicketID'] + 0;
        $round_id       = $postData['RoundID'] + 0;
        $venue_id       = $postData['VenueID'] + 0;
        $zone_id        = $postData['AreaID'] + 0;
        $seatIds        = trim(strval($postData['SeatIds']), ',');
        $linkMark       = isset($postData['LinkSeatMark']) ? strval($postData['LinkSeatMark']) : ''; //合并付款连座标记信息
        $playtime       = $postData['PftPlayTime'];
        $oneLevelSeller = $postData['OneLevelSeller'] + 0;
        $tnum           = $postData['Tnum'] + 0;
        $fid            = $postData['Fid'] + 0;
        $itemTag        = $postData['ItemTag'] ?? '';
        $lockIdStr      = $postData['LockIdStr'] ?? '';
        //如果有锁定座位的情况 需要取到指定门票对应之前锁定的座位id
        $ticketIdentify = [];
        if ($lockIdStr) {
            $cacheKey     = \Business\Order\ProductService\ServiceShow::CHILD_LOCK_IDENTIFY_KEY . $lockIdStr;
            $identifyInfo = \Library\Cache\Cache::getInstance('redis')->get($cacheKey);
            if ($identifyInfo) {
                //解析到对应门票之前锁定的信息
                $identifyArr    = json_decode($identifyInfo, true);
                $ticketIdentify = $identifyArr[$tid] ?? [];
            }
        }

        //座位数据处理
        $tmpSeats   = explode(',', $seatIds);
        $tmpSeats   = array_unique($tmpSeats);
        $seatIdList = [];
        foreach ($tmpSeats as $item) {
            if ($item) {
                $seatIdList[] = intval($item);
            }
        }

        //请求数据
        $roundBiz = new \Business\Product\Show();
        $roundRes = $roundBiz->holdingZoneSeats($venue_id, $round_id, $zone_id, $playtime, $tnum,
            $oneLevelSeller, $fid, $seatIdList, $linkMark, $itemTag, $ticketIdentify);

        $code = $roundRes['code'];
        $msg  = $roundRes['msg'];
        //新版分销库存场次下单才会有值
        $storageChange = $roundRes['data']['storageChange'] ?: [];
        $roundTime     = $roundRes['data']['roundTime'] ?: '';

        $rMret   = strval($msg);
        $arrMret = explode('|', $rMret);

        if ($code != 1) {
            //下单报错
            $errMsg = "错误描述:{$msg}";

            return $this->orderReturn($code, $errMsg, ['err_code' => $code]);
        } else {
            //请求成功
            $resArr = [
                'series'         => '',
                'round_info'     => '',
                'seat_id_list'   => '',
                'storage_change' => $storageChange,
                'lock_id_list'   => '',
            ];

            //说明是演出
            if (isset($arrMret[2]) && $arrMret[2]) {
                $seriesInfo = [
                    0 => $postData['VenueID'],
                    1 => $postData['RoundID'],
                    2 => $postData['AreaID'],
                ];

                //$seriesInfo[3]        = $arrMret[4];// 座位ID
                $seriesInfo[4] = $arrMret[2]; // 演出时间
                $seriesInfo[5] = $arrMret[3]; // 座位号
                $seriesInfo[6] = $arrMret[1]; // 演出时间、座位号，一大串中文
                $seriesInfo[8] = $storageChange ?? []; // 连座锁座的座位信息、 未支付取消或者超时自动取消的 需要用到
                $seriesInfo[9] = $arrMret[5] ?? ''; // 连座锁座的座位信息、 未支付取消或者超时自动取消的 需要用到
                $seriesInfo[11] = $roundTime ?: $arrMret[2];       // 演出的时间 根据场次模式动态展示

                $resArr['series']       = serialize($seriesInfo);
                $resArr['round_info']   = $arrMret[1];
                $resArr['seat_id_list'] = isset($arrMret[4]) ? $arrMret[4] : '';
                $resArr['lock_id_list'] = isset($arrMret[5]) ? $arrMret[5] : '';
            }

            return $this->orderReturn(200, '', $resArr);
        }
    }

    /**
     * 演出订单支付逻辑
     * <AUTHOR>
     * @date 2021-12-21
     *
     */
    public function showPay($postData)
    {
        $tid       = $postData['Tid'];
        $lid       = $postData['LandId'];
        $tnum      = $postData['Tnum'];
        $fid       = $postData['Fid'];
        $applyId   = $postData['ApplyId'];
        $pftOrder  = $postData['Ordern'];
        $payStatus = isset($postData['PayStatus']) ? $postData['PayStatus'] : 0;

        if (!$pftOrder || !$fid) {
            return $this->orderReturn(0, '105|参数错误');
        }

        if ($payStatus != 1) {
            return $this->orderReturn(0, '105|订单未支付');
        }

        //未对接第三方演出产品
        $showBiz = new \Business\Product\Show();
        $res     = $showBiz->payZoneSeats($pftOrder, $fid);

        if ($res) {
            return $this->orderReturn(200, '');
        } else {
            return $this->orderReturn(0, '演出系统支付失败');
        }
    }

    /**
     * 往演出系统下单
     * <AUTHOR>
     * @data   2019-03-25
     *
     * @param  array  $postData  参数
     *
     * @return []
     */
    public function showRelated($postData)
    {
        $roundId        = $postData['RoundID'];
        $zoneId         = $postData['AreaID'];
        $ordernum       = $postData['Ordern'];
        $buyId          = $postData['Fid'];
        $tnum           = $postData['Tnum'];
        $payStatus      = $postData['PayStatus'];
        $oneLevelSeller = $postData['OneLevelSeller'];

        $tmpJit    = explode(',', trim($postData['SeatsId'], ','));
        $jitIdList = array_unique($tmpJit);

        $roundBiz = new \Business\Product\Show();

        $roundRes = $roundBiz->relationZoneSeats($roundId, $zoneId, $jitIdList, $ordernum, $buyId, $tnum,
            $payStatus, $oneLevelSeller);
        if ($roundRes['code'] != 1) {
            $rMret   = strval($roundRes['msg']);
            $arrMret = explode('|', $rMret);
            $errMsg  = "错误描述:{$arrMret[1]}";

            return $this->orderReturn(0, $errMsg, ['err_code' => $arrMret[0]]);
        }

        return $this->orderReturn(200, '');
    }

    /**
     * 回滚演出座位
     * <AUTHOR>
     * @data   2019-04-03
     *
     * @param  string  $ordernum
     * @param  int  $roundId
     * @param  int  $areaId
     * @param  array  $jitIdList
     *
     * @return []
     */
    public function rollbackShowOrder($ordernum, $roundId, $areaId, $jitIdList, $otherInfo = [])
    {
        if (!$ordernum || !$roundId || !$areaId || !$jitIdList || !is_array($jitIdList)) {
            return $this->orderReturn(0, '参数错误');
        }

        $postData = [
            'Action'        => 'roundRollback',
            'PftOrderSn'    => $ordernum,
            'RoundID'       => $roundId,
            'AreaID'        => $areaId,
            'JitIdList'     => $jitIdList,
            'OtherInfo'     => $otherInfo,
        ];

        $rMret   = Helpers::callThirdSystem($postData);
        $rMret   = strval($rMret);
        $arrMret = explode('|', $rMret);

        if ($arrMret[0] != 200) {
            //下单报错
            $errMsg = "错误描述:{$arrMret[1]}";

            return $this->orderReturn(0, $errMsg);
        } else {
            //请求成功
            return $this->orderReturn(200, '库存回滚成功');
        }
    }

    /**
     * 往第三方系统取消订单
     * <AUTHOR>
     * @data   2019-03-25
     *
     * @param  array  $params  参数
     * @param  string  $interfaceMode  对接类型 old=旧模式，new=open开发模式
     *
     * @return ['code' => 1096]
     */
    public function commonRefund($params, $interfaceMode = 'old')
    {
        if ($interfaceMode == 'new') {
            $otaOpenBiz = new \Business\Ota\Open\Order();
            $cancelRes  = $otaOpenBiz->pushCancel($params);

            //记录请求日志
            $logData = json_encode([
                'req'      => $params,
                'response' => $cancelRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('open/third_order_system', $logData);

            $tmpCode = $cancelRes['code'];
            $resMsg  = $cancelRes['msg'];

            if ($tmpCode == 1095) {
                //申请退款审核中
                $resCode = 1095;
            } elseif ($tmpCode == 1000) {
                //拒绝退款申请
                $resCode = 1096;
            } elseif ($tmpCode == 200) {
                //退票成功
                $resCode = 200;
            } elseif ($tmpCode == 1109) {
                //第三方退票请求超时
                $resCode = 1109;
            } else {
                //默认都是退票失败
                $resCode = 1096;
            }
        } else {
            $params['Action'] = 'MOD';
            $version          = '';
            if (!empty($params['LandId'])) {
                $version = $this->_getSysVersion($params['LandId']);
            }

            if ($version == 'new') {
                $cancelRes = Helpers::callThirdSystemJsonRpc($params);
            } else {
                $cancelRes = Helpers::callThirdSystem($params);
            }
            $cancelRes = strval($cancelRes);

            //记录请求日志
            $logData = json_encode([
                'req'      => $params,
                'response' => $cancelRes,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('open/third_order_system', $logData);

            $tmpCodeArr = explode('|', $cancelRes);
            $tmpCode    = $tmpCodeArr[0];
            $resMsg     = isset($tmpCodeArr[1]) ? $tmpCodeArr[1] : '';

            $refundCodeArr = [
                1095, //申请退款审核中
                1096, //拒绝退款申请
                200, //处理成功
                1109, //第三方退票请求超时
            ];

            if (in_array($tmpCode, $refundCodeArr)) {
                $resCode = $tmpCode;
            } else {
                //默认都是退票失败
                $resCode = 1096;
            }
        }

        return $this->returnData($resCode, $resMsg);
    }

    /**
     * 演出订单退票
     * <AUTHOR>
     * @date   2019-06-24
     *
     * @param  array  $postData
     *
     * @return []
     */
    public function showRefund($postData)
    {

        if (!is_numeric($postData['Tnum'])) {
            return $this->returnData(101, '参数错误');
        }
        $fid      = $postData['Fid']; // 购买用户ID
        $tNum     = $postData['Tnum']; // 修改后的数量 0 取消 -1 不做修改
        $orderNum = $postData['Ordern']; // 预定订单号
        $round_id = $postData['RoundID']; // 预定场次ID
        $zoneId   = $postData['AreaID']; // 分区ID

        //新加的参数：本次退的票数，如果取消的话直接传0进来
        //新版的退票接口是退票之后才退演出座位的，所以再去数据库获取tnum是有问题的
        $cancelNum = isset($postData['CancelNum']) ? intval($postData['CancelNum']) : false;

        //可以根据座位号去释放座位
        $seatIdList = $postData['SeatIds'] ?? []; //座位号
        $otherInfo = $postData['OtherInfo'] ?? []; //锁定座位的座位信息

        //实例
        $showBiz = new \Business\Product\Show();

        if ($tNum == -1) {
            //如果都没有修改的话，直接返回成功
            //正常这样的数据从open那边就不应该请求到这边来
            return $this->returnData(200, '修改成功01');
        } else if (!$zoneId) {
            //如果沒有座位分区的话，直接就修改成功
            return $this->returnData(200, '修改成功02');
        } else {
            if ($cancelNum !== false) {
                //取消的票数，如果取消的话直接传0进来
                //新的退票模式是，订单完全退票之后才取消演出座位的，所以不需要之前的缓存机制
                $ticketNum = $cancelNum;

                //释放座位
                $res  = $showBiz->modZoneSeats($round_id, $orderNum, $ticketNum, $zoneId, $seatIdList, $otherInfo);
                $code = $res['code'];
                $msg  = $res['msg'];

                $roundTime = $res['data']['roundTime'] ?: '';

                //记录日志
                pft_log('order_show/mod', json_encode([$res, $round_id, $orderNum, $ticketNum, $zoneId, $seatIdList, $otherInfo]));
                if ($code == 0) {
                    return $this->returnData(201, $msg);
                } else {
                    $rMret      = strval($res['msg']);
                    $arrMret    = explode('|', $rMret);
                    $seriesInfo = [];
                    $refundSeatList = [];

                    if (isset($arrMret[2])) {
                        //如果是退部分票，需要更新订单里面的座位信息
                        //带上场次之类的数据
                        $seriesInfo[0] = $postData['VenueID'];
                        $seriesInfo[1] = $postData['RoundID'];
                        $seriesInfo[2] = $postData['AreaID'];
                        $seriesInfo[3] = $postData['SeatID'];

                        $seriesInfo[4] = $arrMret[2];
                        $seriesInfo[5] = $arrMret[3];
                        $seriesInfo[6] = $arrMret[1];
                        $seriesInfo[8] = $otherInfo['storage_change'] ?? [];
                        $seriesInfo[11] = $roundTime ?: $arrMret[2];       // 演出的时间 根据场次模式动态展示
                    }

                    if(!empty($arrMret[5])) {
                        //根据座位id查询座位信息
                        $showModel = new \Model\Product\Show();
                        $refundSeatList = $showModel->getSeatsList($postData['VenueID'], $postData['AreaID'], array_filter(explode(",", $arrMret[5])));
                    }

                    return $this->orderReturn(200, '', ['seriesInfo' => $seriesInfo, 'refundSeatList' => $refundSeatList]);
                }
            } else {
                $orderModel = new \Model\Order\OrderTools();
                $sTnum      = $orderModel->getOrderInfo($orderNum, 'tnum');
                $ticketNum  = $sTnum['tnum'] - $tNum;

                if ($ticketNum <= 0) {
                    //没有取消订单
                    return $this->returnData(200, '修改成功02');
                }

                //如果是取消订单的话，直接传0进去
                if ($tNum == 0) {
                    $ticketNum = 0;
                }

                //因为现在订单取消之前，是先将座位取消，然后取消订单，中间可能存在座位取消了
                //但是订单因为余额不足，导致充钱之后再次取消订单的时候座位取消失败了。
                //如果是座位取消成功了，数据暂时放缓存2天的时间
                $cacheKey = "round_mod:{$zoneId}:{$orderNum}:{$sTnum['tnum']}";
                $cacheLib = Cache::getInstance('redis');
                $cacheRes = $cacheLib->get($cacheKey);

                if ($cacheRes) {
                    //如果之前已经取消成功了，就直接返回之前的数据
                    $rMret      = strval($cacheRes);
                    $arrMret    = explode('|', $rMret);
                    $seriesInfo = [];
                    $refundSeatList = [];

                    if (isset($arrMret[2])) {
                        //如果是退部分票，需要更新订单里面的座位信息
                        //带上场次之类的数据
                        $seriesInfo[0] = $postData['VenueID'];
                        $seriesInfo[1] = $postData['RoundID'];
                        $seriesInfo[2] = $postData['AreaID'];
                        $seriesInfo[3] = $postData['SeatID'];

                        $seriesInfo[4] = $arrMret[2];
                        $seriesInfo[5] = $arrMret[3];
                        $seriesInfo[6] = $arrMret[1];
                        $seriesInfo[8] = $otherInfo['storage_change'] ?? [];
                    }

                    if(!empty($arrMret[5])) {
                        //根据座位id查询座位信息
                        $showModel = new \Model\Product\Show();
                        $refundSeatList = $showModel->getSeatsList($postData['VenueID'], $postData['AreaID'], array_filter(explode(",", $arrMret[5])));
                    }

                    return $this->orderReturn(200, '', ['seriesInfo' => $seriesInfo, 'refundSeatList' => $refundSeatList]);
                } else {
                    //释放座位
                    $res  = $showBiz->modZoneSeats($round_id, $orderNum, $ticketNum, $zoneId, $seatIdList, $otherInfo);
                    $code = $res['code'];
                    $msg  = $res['msg'];

                    $roundTime = $res['data']['roundTime'] ?: '';


                    //记录日志
                    pft_log('order_show/mod',
                        json_encode([$res, $round_id, $orderNum, $ticketNum, $zoneId, $seatIdList, $otherInfo]));
                    if ($code == 0) {
                        return $this->returnData(201, $msg);
                    } else {
                        //缓存数据
                        $cacheLib->set($cacheKey, $msg, $prefix = '', 172800);

                        $rMret      = strval($res['msg']);
                        $arrMret    = explode('|', $rMret);
                        $seriesInfo = [];
                        $refundSeatList = [];

                        if (isset($arrMret[2])) {
                            //如果是退部分票，需要更新订单里面的座位信息
                            //带上场次之类的数据
                            $seriesInfo[0] = $postData['VenueID'];
                            $seriesInfo[1] = $postData['RoundID'];
                            $seriesInfo[2] = $postData['AreaID'];
                            $seriesInfo[3] = $postData['SeatID'];

                            $seriesInfo[4] = $arrMret[2];
                            $seriesInfo[5] = $arrMret[3];
                            $seriesInfo[6] = $arrMret[1];
                            $seriesInfo[8] = $otherInfo['storage_change'] ?? [];
                            $seriesInfo[11] = $roundTime ?: $arrMret[2];       // 演出的时间 根据场次模式动态展示
                        }

                        if(!empty($arrMret[5])) {
                            //根据座位id查询座位信息
                            $showModel = new \Model\Product\Show();
                            $refundSeatList = $showModel->getSeatsList($postData['VenueID'], $postData['AreaID'], array_filter(explode(",", $arrMret[5])));
                        }

                        return $this->orderReturn(200, '', ['seriesInfo' => $seriesInfo, 'refundSeatList' => $refundSeatList]);
                    }
                }
            }
        }

        return $this->returnData(101, '流程异常了，请联系开发');
    }

    /**
     * 根据景点id获取绑定的系统的版本号
     */
    private function _getSysVersion($lid)
    {
        $otaQueryModel = new \Model\Ota\OtaQueryModel();
        $coopInfo      = $otaQueryModel->getCsysid($lid);
        $csysId        = $coopInfo['csysid'];

        // 获取系统的版本号
        $sysConfigModel   = new \Model\Ota\SysConfig();
        $configField      = 'id, version';
        $sysConfigInfoArr = $sysConfigModel->getSysConfigInfo($csysId, $configField);

        return empty($sysConfigInfoArr['version']) ? '' : $sysConfigInfoArr['version'];
    }

    /**
     * 通知三方系统开放接口下单支付推送
     *
     * @param $params
     */
    private function _callOtaOpenOrder($params)
    {
        $otaOpenBiz   = new \Business\Ota\Open\Order();
        $pushOrderRes = $otaOpenBiz->pushOrder($params);
        // 下单失败直接返回错误信息
        if ($pushOrderRes['code'] == 1000) {
            return '1000|' . $pushOrderRes['msg'];
        } else {
            // 下单成功或者超时都要推送支付信息
            $payJobparams   = [
                'ordernum' => $params['PftOrderSn'],
                'ApplyId'  => $params['ApplyId'],
                'LandId'   => $params['LandId'],
            ];
            $openPayJobData = [
                'job_type' => 'third_open_pay',
                'job_data' => $payJobparams,
            ];
            //\Library\Resque\Queue::push('order', 'Order_Job', $openPayJobData);
            \Library\Resque\Queue::delay(strtotime("+ 2 seconds"), 'order', 'Order_Job', $openPayJobData);

            $res = "200|0|0|0";
            if (!empty($pushOrderRes['data'])) {
                if (!empty($pushOrderRes['data']['apiCode'])) {
                    $res = "200|0|0|0|" . $pushOrderRes['data']['apiCode'];
                } else {
                    $res = "200|0|0|0|";
                }
                if (!empty($pushOrderRes['data']['apiOrder'])) {
                    $res .= "|" . $pushOrderRes['data']['apiOrder'];
                }
            }

            // return "200|0|0|0";
            return $res;
        }
    }

    /**
     * 在线（线下支付成功）往三方系统下单(对三方对接业务不熟，先照搬原来的代码)
     * <AUTHOR>
     * @date   2019-08-19
     *
     * @param  array  $orderData  订单信息
     *
     * @return void
     */
    public function orderAfterPay($orderData)
    {

        if (!isset($orderData['mainOrder'])) {
            return;
        }

        $tradeModel = new \Model\TradeRecord\OnlineTrade();
        //主票
        $main = $orderData['mainOrder'];

        $mainTimeStr = '';
        if (!empty($main['ext_content'])) {
            $mainrderExt = json_decode($main['ext_content'], true);
            if (isset($mainrderExt['sectionTimeStr'])) {
                $mainTimeStr = $mainrderExt['sectionTimeStr'];
            }
        }

        // 在线支付切换到新工程
        $version = '';
        if (!empty($main['lid'])) {
            $version = $this->_getSysVersion($main['lid']);
        }

        //第三方平台产品二次交互
        $tradeModel->secondRequest($main['tid'], $main['ordernum'], $main['member'], $main['tnum'], $mainTimeStr,
            $version);

        if (count($orderData['childOrder'])) {
            foreach ($orderData['childOrder'] as $child) {
                if ($child['paystatus'] != 1) {
                    $orderExt = json_decode($child['ext_content'], true);
                    $timeStr  = '';
                    if (isset($orderExt['sectionTimeStr'])) {
                        $timeStr = $orderExt['sectionTimeStr'];
                    }

                    // 在线支付切换到新工程
                    $childVersion = '';
                    if (!empty($child['lid'])) {
                        $childVersion = $this->_getSysVersion($child['lid']);
                    }

                    $tradeModel->secondRequest($child['tid'], $child['ordernum'], $child['member'], $child['tnum'],
                        $timeStr, $childVersion);
                }
            }
        }
    }
}
