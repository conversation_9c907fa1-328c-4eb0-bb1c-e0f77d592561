<?php
/**
 * 平台订单内部支付统一封装
 * <AUTHOR>
 * @date: 3/13-013
 * @modify dwer.cn 2019-02-14
 */

namespace Business\Order;

use Business\Base;
use Business\Order\ProductService\ServiceObject;
use Library\Resque\Queue;
use Library\Tools;
use Model\Member\Member;
use Model\Order\OrderHandler;
use Model\Order\OrderTrack;
use Model\Product\Land;
use \Exception;

class Payment extends Base
{

    /**
     * 美团订单支付
     * <AUTHOR>
     * @date   2018-05-18
     *
     * @param  string  $ordernum  订单号
     */
    public function MeiTuanPay($ordernum)
    {
        try {
            $landModel   = new Land('slave');
            $orderModel  = new OrderHandler('localhost');
            $memberModel = new Member();
            $trackModel  = new OrderTrack();

            $ordernum    = strval($ordernum);
            $orderField  = 'lid,tid,pid,tnum,ordername,ordertel,ss.pay_status,status,paymode,totalmoney,member,aid,playtime';
            $detailField = 'aids_money, product_ext';
            $orderInfo   = $orderModel->getOrderInfo($ordernum, $orderField, $detailField);

            $landInfo = $landModel->getLandInfo($orderInfo['lid'], false, 'p_type');

            if (!$orderInfo) {
                return ['code' => 105, 'msg' => '订单不存在'];
            }
            if ($orderInfo['pay_status'] == 1) {
                //$cache->rm($lockey);
                return ['code' => 200, 'msg' => '订单已支付'];
            }
            if ($orderInfo['status'] == 3) {
                return ['code' => 105, 'msg' => '订单已取消'];
            }

            $paymode      = $orderInfo['paymode'];
            $totalmoney   = $orderInfo['totalmoney'];
            $member       = $orderInfo['member'];
            $aid          = $orderInfo['aid'];
            $aidsMoneyStr = $orderInfo['aids_money'] ? $orderInfo['aids_money'] : '';
            $aidsMoney    = Tools::aidMoneyFormat($aidsMoneyStr);

            //余额判断
            if ($paymode == 0) {
                $amoney = $memberModel->getMoney($member, Member::ACCOUNT_MONEY, 0, false, true);
                if ($totalmoney > $amoney) {
                    return ['code' => 122, 'msg' => '账户余额不足'];
                }
            } elseif ($paymode == 2) {
                $money   = $memberModel->getMoney($member, Member::ACCOUNT_APPLYER_BOTH, $aid, true, true);
                $Kkmoney = $money['kmoney'] + $money['basecredit'];
                if ($totalmoney > $Kkmoney) {
                    return ['code' => 122, 'msg' => '授信余额不足'];
                }
            }

            //处理分销链数据
            $resellArr = [];
            if ($aidsMoney && is_array($aidsMoney)) {
                foreach ($aidsMoney as $moneyKey => $moneyVal) {
                    $moneyFid    = $moneyVal['fid']; //分销商
                    $moneyType   = $moneyVal['payway']; //0=账户余额，2=授信
                    $moneySymbol = $moneyVal['type']; //0=扣费，1=加钱
                    $moneyTmp    = $moneyVal['money']; //具体的金额（分）
                    $moneyAid    = $moneyVal['aid']; //供应商

                    //order_fx_details -> aids_money 这边数据库记录得比较坑爹,
                    //$moneyType=0 授信支付
                    //$moneyType=1 余额支付
                    if ($moneyType == 0) {
                        $realPayType = 2;
                    } elseif ($moneyType == 2) {
                        $realPayType = 37;
                    } else {
                        $realPayType = 0;
                    }

                    //余额支付里面那条加钱的记录不需要加进去
                    //大于0才需要支付
                    if ($moneySymbol == 0 && $moneyTmp > 0) {
                        $resellArr[] = [
                            'memberId'   => $moneyFid,
                            'supplierId' => $moneyAid,
                            'tradeMoney' => $moneyTmp,
                            'payType'    => $realPayType,
                            'remark'     => '',
                        ];
                    }
                }
            }

            $orderArr = [
                ['orderId' => $ordernum],
            ];

            if ($paymode == 0) {
                $subjectCode = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
            } else {
                $subjectCode = \Library\Constants\Account\BookSubject::CREDIT_SUBJECT_CODE;
            }

            $paymentApi = new \Business\JavaApi\Order\Pay();
            $tradeRes   = $paymentApi->pay($orderArr, '', $totalmoney, $paymode, $subjectCode, $member, '', 24, '');

            //如果是系统异常的话，直接抛出异常
            if ($tradeRes['code'] == 500) {
                throw new Exception('支付异常', 500);
            }

            if ($tradeRes['code'] != 200) {
                return ['code' => 122, 'msg' => '支付失败'];
            }

            //判断是否外部发码的订单
            if (!empty($orderInfo['product_ext'])) {
                $extContent = json_decode($orderInfo['product_ext'], true) ?? [];
                if (isset($extContent['externalSendCode']) && $extContent['externalSendCode'] == 1) {
                    //第三方发码的订单
                    $externalCodeBiz                    = new \Business\ExternalCode\CodeManage();
                    $orderData['mainOrder']['ordernum'] = $ordernum;
                    $externalCodeBiz->asyncPaySuccessTask($orderData);
                }
            }

            //添加短信队列数据
            $job_id = Queue::push('notify', 'OrderNotify_Job',
                [
                    'ordernum' => $ordernum,
                    'buyerId'  => $orderInfo['member'], //购买人的ID
                    'mobile'   => $orderInfo['ordertel'],
                    'aid'      => $orderInfo['aid'],
                ]
            );
            pft_log('MeiTuanPay/ok', "$ordernum|job_id=$job_id");

            //美团套票主票
            if (in_array($landInfo['p_type'], ['F', 'H'])) {
                // 获取演出子票的座位信息
                $cache             = \Library\Cache\Cache::getInstance('redis');
                $childShowInfoJson = $cache->get('childShowInfoJson:' . $ordernum);
                $childShowInfoArr  = [];
                if (in_array($landInfo['p_type'], ['F', 'H']) && !empty($childShowInfoJson)) {
                    $childShowInfoArr = json_decode($childShowInfoJson, true);
                    $cache->set('childShowInfoJson:' . $ordernum, $childShowInfoJson, '', 720); // 修改时效时间
                }

                $jobid = Queue::push('package_order', 'PackageOrder_Job',
                    [
                        'orderName'              => $orderInfo['ordername'],
                        'orderTel'               => $orderInfo['ordertel'],
                        'parentPid'              => $orderInfo['pid'],
                        'parentOrdernum'         => $ordernum,
                        'playtime'               => $orderInfo['playtime'],
                        'tnum'                   => $orderInfo['tnum'],
                        'ptype'                  => $landInfo['p_type'],
                        'actions'                => ['submitChildrenOrder'],
                        'childTicketShowInfoArr' => $childShowInfoArr,
                    ]
                );
                pft_log('order/package', "美团套票主票(在线支付):$ordernum,jobid:$jobid");
            }

            //返回结果
            return ['code' => 200, 'msg' => 'success'];
        } catch (Exception $e) {
            //返回系统异常
            return ['code' => 500, 'msg' => $e->getMessage()];
        }
    }

    /**
     * 团购订单支付
     *
     * @param  string  $ordernum  订单号
     */
    public function GroupOrderPay($ordernum)
    {
        //查询订单数据
        //通过订单号获取产品/供应商等信息
        $lib      = new \Business\JavaApi\Order\Query\UusOrder();
        $queryRes = $lib->queryOrderInfoByOrdernum($ordernum);

        //服务对象
        $serviceObject = null;

        if ($queryRes['code'] == 200) {
            //获取到了订单数据
            $orderInfo = $queryRes['data'];

            $applyDid  = $orderInfo['apply_did'];
            $lid       = $orderInfo['lid'];
            $pType     = $orderInfo['product_type'];
            $ordermode = $orderInfo['ordermode'];
            $tid       = $orderInfo['tid'];

            //这边这个值只是为了流量切换用
            $sourceT = -1;

            $serviceObject = ServiceObject::getPayCallbackServiceObject($pType, $sourceT, $lid, $applyDid, $ordermode);
        }

        if ($serviceObject) {
            //走具体的产品线逻辑
            $payParamArr = [
                'ordernum'   => $ordernum,
                'payChannel' => 0,
                'payOpId'    => 0,
                'tid'        => $tid ?? 0,
                'applyDid'   => $applyDid ?? 0,
            ];
            $payRes      = $serviceObject->insidePayCallback($payParamArr);
        } else {
            //原先的逻辑
            $payRes = $this->_oldPlatformPay($ordernum);
        }

        return $payRes;
    }

    /**
     * 旧的平台内支付逻辑
     * <AUTHOR>
     * @date 2022/08/15
     *
     * @param $ordernum string 平台订单号
     *
     * @return array
     */
    private function _oldPlatformPay($ordernum)
    {
        try {
            $landModel   = new Land('slave');
            $orderModel  = new OrderHandler();
            $memberModel = new Member();
            $trackModel  = new OrderTrack();

            $ordernum    = strval($ordernum);
            $orderField  = 'lid,tid,pid,tnum,ordername,ordertel,ss.pay_status,status,paymode,totalmoney,member,aid,playtime';
            $detailField = 'aids_money, product_ext';
            $orderInfo   = $orderModel->getOrderInfo($ordernum, $orderField, $detailField);

            if (!$orderInfo) {
                return ['code' => 105, 'msg' => '订单不存在'];
            }

            if ($orderInfo['pay_status'] == 1) {
                //$cache->rm($lockey);
                return ['code' => 200, 'msg' => '订单已支付'];
            }
            if ($orderInfo['status'] == 3) {
                return ['code' => 105, 'msg' => '订单已取消'];
            }

            $landInfo = $landModel->getLandInfo($orderInfo['lid'], false, 'p_type');

            $paymode      = $orderInfo['paymode'];
            $totalmoney   = $orderInfo['totalmoney'];
            $member       = $orderInfo['member'];
            $aid          = $orderInfo['aid'];
            $aidsMoneyStr = $orderInfo['aids_money'] ? $orderInfo['aids_money'] : '';
            $aidsMoney    = Tools::aidMoneyFormat($aidsMoneyStr);

            //余额判断
            if ($paymode == 0) {
                $amoney = $memberModel->getMoney($member, Member::ACCOUNT_MONEY, 0, false, true);
                if ($totalmoney > $amoney) {
                    return ['code' => 122, 'msg' => '账户余额不足'];
                }
            } elseif ($paymode == 2) {
                $money   = $memberModel->getMoney($member, Member::ACCOUNT_APPLYER_BOTH, $aid, true, true);
                $Kkmoney = $money['kmoney'] + $money['basecredit'];
                if ($totalmoney > $Kkmoney) {
                    return ['code' => 122, 'msg' => '授信余额不足'];
                }
            }

            //处理分销链数据
            $resellArr = [];
            if ($aidsMoney && is_array($aidsMoney)) {
                foreach ($aidsMoney as $moneyKey => $moneyVal) {
                    $moneyFid    = $moneyVal['fid']; //分销商
                    $moneyType   = $moneyVal['payway']; //0=账户余额，2=授信
                    $moneySymbol = $moneyVal['type']; //0=扣费，1=加钱
                    $moneyTmp    = $moneyVal['money']; //具体的金额（分）
                    $moneyAid    = $moneyVal['aid']; //供应商

                    //order_fx_details -> aids_money 这边数据库记录得比较坑爹,
                    //$moneyType=0 授信支付
                    //$moneyType=1 余额支付
                    if ($moneyType == 0) {
                        $realPayType = 2;
                    } elseif ($moneyType == 2) {
                        $realPayType = 37;
                    } else {
                        $realPayType = 0;
                    }

                    //余额支付里面那条加钱的记录不需要加进去
                    //大于0才需要支付
                    if ($moneySymbol == 0 && $moneyTmp > 0) {
                        $resellArr[] = [
                            'memberId'   => $moneyFid,
                            'supplierId' => $moneyAid,
                            'tradeMoney' => $moneyTmp,
                            'payType'    => $realPayType,
                            'remark'     => '',
                        ];
                    }
                }
            }

            $orderArr = [
                ['orderId' => $ordernum],
            ];

            if ($paymode == 0) {
                $subjectCode = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
            } else {
                $subjectCode = \Library\Constants\Account\BookSubject::CREDIT_SUBJECT_CODE;
            }

            $paymentApi = new \Business\JavaApi\Order\Pay();
            $tradeRes   = $paymentApi->pay($orderArr, '', $totalmoney, $paymode, $subjectCode, $member, '', 24, '');

            //如果是系统异常的话，直接抛出异常
            if ($tradeRes['code'] == 500) {
                throw new Exception('支付异常', 500);
            }

            if ($tradeRes['code'] != 200) {
                return ['code' => 122, 'msg' => '支付失败'];
            }

            //判断是否外部发码的订单
            if (!empty($orderInfo['product_ext'])) {
                $extContent = json_decode($orderInfo['product_ext'], true) ?? [];
                if (isset($extContent['externalSendCode']) && $extContent['externalSendCode'] == 1) {
                    //第三方发码的订单
                    $externalCodeBiz                    = new \Business\ExternalCode\CodeManage();
                    $orderData['mainOrder']['ordernum'] = $ordernum;
                    $externalCodeBiz->asyncPaySuccessTask($orderData);
                }
            }

            // 判断是否是对接的门票
            $otaProductBiz      = new \Business\Ota\Product();
            $thirdTicketConfArr = $otaProductBiz->getThirdTicketConfArr($orderInfo['tid']);
            if (!empty($thirdTicketConfArr) && in_array($thirdTicketConfArr['sourceT'], [1, 2, 3])) {
                $cancelChannel     = \Library\Constants\OrderConst::THIRD_CANCEL;
                $otaOpenBiz        = new \Business\Ota\Open\Order();
                $pushThirdOrderRes = $otaOpenBiz->pushThirdOrder($ordernum);

                if ($pushThirdOrderRes['code'] != 200) {
                    // 回滚取消订单 todo
                    $modifyBiz = new \Business\Order\Modify();

                    $cancelSpecialArr = [
                        'is_cancel_third_system' => false,
                    ];

                    $res = $modifyBiz->cancelParamsCommonFormat($ordernum, $member, $cancelChannel, '', -1,
                        'common', ['remark' => '三方系统下单失败，取消票付通订单'], [], [], $cancelSpecialArr);

                    if ($pushThirdOrderRes['code'] == 205) {
                        return ['code' => 500, 'msg' => '请求三方系统数据出错'];
                    } else {
                        return ['code' => $pushThirdOrderRes['code'], 'msg' => $pushThirdOrderRes['msg']];
                    }
                }
            }

            //套票主票
            if (in_array($landInfo['p_type'], ['F', 'H'])) {
                // 获取演出子票的座位信息
                $cache             = \Library\Cache\Cache::getInstance('redis');
                $childShowInfoJson = $cache->get('childShowInfoJson:' . $ordernum);
                $childShowInfoArr  = [];
                if (in_array($landInfo['p_type'], ['F', 'H']) && !empty($childShowInfoJson)) {
                    $childShowInfoArr = json_decode($childShowInfoJson, true);
                    $cache->set('childShowInfoJson:' . $ordernum, $childShowInfoJson, '', 720); // 修改时效时间
                }

                $specialSettingArr['sms_info'] = self::getPackSmsInfo($orderInfo);
                $specialSettingArr['parent_contact_info_more'] = [
                    'mobileArea'             => $orderInfo['mobile_area'],
                    'mobileRegion'           => $orderInfo['mobile_region'],
                ];
                $jobid = Queue::push('package_order', 'PackageOrder_Job',
                    [
                        'orderName'              => $orderInfo['ordername'],
                        'orderTel'               => $orderInfo['ordertel'],
                        'parentPid'              => $orderInfo['pid'],
                        'parentOrdernum'         => $ordernum,
                        'playtime'               => $orderInfo['playtime'],
                        'tnum'                   => $orderInfo['tnum'],
                        'ptype'                  => $landInfo['p_type'],
                        'actions'                => ['submitChildrenOrder'],
                        'childTicketShowInfoArr' => $childShowInfoArr,
                        'childSpecialSettingArr' => $specialSettingArr
                    ]
                );
                pft_log('order/package', "套票主票(在线支付):$ordernum,jobid:$jobid");

                // 演出座位占座
                if ($landInfo['p_type'] == 'H') {
                    //演出产品
                    $params     = [
                        'Ordern'    => $ordernum,
                        'Fid'       => (int)$orderInfo['member'],
                        'LandId'    => '', // 景点ID
                        'ApplyId'   => '', // 直接供应商
                        'Tid'       => '', // 门票ID
                        'Tnum'      => '', // 门票数
                        'PayStatus' => 1,  // 支付状态
                    ];
                    $showBiz    = new \Business\Order\ThirdSystem();
                    $showPayRes = $showBiz->showPay($params);
                    pft_log('third_call', json_encode(["show", $params, $showPayRes], JSON_UNESCAPED_UNICODE));
                }
            }
            if($landInfo['p_type'] == 'F'){
                $baseSubmit = new BaseSubmit();
                $isAllowPack = $baseSubmit->isAllowPackNotice($orderInfo['apply_did']);
                if($isAllowPack){
                    //套票是否发送短信 涉及下游统计以及短信通知，如果开通开放功能，收口到套票下单队列中去
                    return ['code' => 200, 'msg' => 'success'];
                }
            }
            //与景区线一致 根据uu_order_fx_details 的 notice_ext.remsg 来判断 0不发 1发 [实际是 is_sms]
            //景区线还判断外部发码订单强制发，此处我们不判断，我们已经在预定的时候就转化成 is_sms
            $noticeExt = json_decode($orderInfo['notice_ext'],true);
            $notifyType = $noticeExt['remsg'] ? 0 : 1;
            //国际号支持
            $internationalCall = \Library\MessageNotify\OrderNotify::getInternationalCall($orderInfo['mobile_area'] ?? '', $orderInfo['ordertel'] ?? '');
            //添加短信队列数据
            $job_id = Queue::push('notify', 'OrderNotify_Job',
                [
                    'ordernum' => $ordernum,
                    'buyerId'  => $orderInfo['member'], //购买人的ID
                    'mobile'   => $internationalCall,
                    'aid'      => $orderInfo['aid'],
                    'notify'   => $notifyType,//是否发送短信给游客：0=发送，1=不发送
                ]
            );
            @pft_log('order/package', json_encode(['美团 - 套票订单通知',$ordernum,$orderInfo,$job_id], JSON_UNESCAPED_UNICODE));
            //返回结果
            return ['code' => 200, 'msg' => 'success'];
        } catch (Exception $e) {
            //返回系统异常
            return ['code' => 500, 'msg' => $e->getMessage()];
        }
    }

    public function getPackSmsInfo($orderInfo){
        //与景区线一致 根据uu_order_fx_details 的 notice_ext.remsg 来判断 0不发 1发 [实际是 is_sms]
        //景区线还判断外部发码订单强制发，此处我们不判断，我们已经在预定的时候就转化成 is_sms
        $noticeExt = json_decode($orderInfo['notice_ext'],true);
        $notifyType = $noticeExt['remsg'] ? 0 : 1;
        //添加短信队列数据
        @pft_log('order/package', json_encode(['美团 - 套票订单通知',$orderInfo['ordernum'],$orderInfo], JSON_UNESCAPED_UNICODE));
        //国际号支持
        $internationalCall = \Library\MessageNotify\OrderNotify::getInternationalCall($orderInfo['mobile_area'] ?? '', $orderInfo['ordertel'] ?? '');
        return [
            'ordernum' => $orderInfo['ordernum'],
            'buyerId'  => $orderInfo['member'], //购买人的ID
            'mobile'   => $internationalCall,
            'aid'      => $orderInfo['aid'],
            'notify'   => $notifyType,//是否发送短信给游客：0=发送，1=不发送
        ];
    }
}
