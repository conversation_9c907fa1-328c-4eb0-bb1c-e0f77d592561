<?php
/**
 * 订单查询导出
 * <AUTHOR>
 * @date   2025/02/19
 */

namespace Business\Order;

use Business\AppCenter\Module as ModuleBiz;
use Business\Authority\DataAuthLogic;
use Business\Base;
use Business\Dc\DcExcelTaskBase;
use Business\DownloadCenter\CustomField\Driver\ExportDetailOrderSearch;
use Business\DownloadCenter\CustomField\Factory;
use Business\MemberLogin\MemberLoginHelper;
use Business\SubMerchant\SubMerchant;

class OrderSearchExport extends Base
{
    private $_selectType;
    private $_orderBusiness;

    const ORDER_SEARCH_EXPORT_EXCEL = 'order_search_export';

    public function __construct()
    {
    }

    private function getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        }

        return $this->_orderBusiness;
    }

    /**
     * 创建订单查询导出任务
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param  array   $request
     * @param  int     $fid
     * @param  int     $opId
     * @param  int     $sdtype
     * @param  int     $dtype
     * @param  string  $account
     *
     * @return array
     */
    public function createExportTask($request, $fid, $opId, $sdtype, $dtype, $account)
    {
        $data = 0;
        $code = 200;
        try {
            $request = $this->_handleParams($request, $fid, $opId, $sdtype, $dtype, $account);
            $head    = $this->_handleHead($fid, $opId, $sdtype, $dtype, $request);
            //创建下载任务
            $taskRes = $this->_createExportTask($fid, $opId, $request, $head);
            if ($taskRes['code'] != self::CODE_SUCCESS) {
                throw new \Exception($taskRes['msg'] ?? '下载失败', 400);
            }
            $data = $taskRes['data'] ?? 0;
            $msg  = $taskRes['msg'] ?? '下载失败';
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            // 记录日志
            $log = [
                'function' => 'getTotalNum',
                'msg'      => $msg,
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
            ];
            pft_log(self::ORDER_SEARCH_EXPORT_EXCEL . "/error", json_encode($log, JSON_UNESCAPED_UNICODE));
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 导出任务
     * <AUTHOR>
     * @date   2025/02/20
     *
     * @param  array   $request
     * @param  string  $lastSequence
     * @param  int     $pageNum
     * @param  int     $pageSize
     *
     * @return array
     */
    public function exportPaginate($request, $lastSequence = '', $pageNum = 1, $pageSize = 20)
    {
        $list   = [];
        $isOver = true;
        $code   = 200;
        $msg    = '导出成功';
        try {
            if (empty($request['sid']) || empty($request['current_fid'])) {
                throw new \Exception('参数错误', 400);
            }

            $sid        = $request['sid'];
            $memberType = $request['dtype'];

            if ($sid == 1) {
                //管理端
                $res = $this->_queryAdminList($request, $lastSequence, $pageNum, $pageSize);
            } else if (in_array($memberType, [2, 3])) {
                //资源账号
                $res = $this->_queryScenicList($request, $lastSequence, $pageNum, $pageSize);
            } else {
                //供应商/分销商/集团账号
                $res = $this->_queryBusinessList($request, $lastSequence, $pageNum, $pageSize);
            }

            $list         = $res['list'] ?? [];
            $isOver       = $res['isOver'] ?? true;
            $lastSequence = $res['lastSequence'] ?? '';

        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            // 记录日志
            $log = [
                'function' => 'getTotalNum',
                'msg'      => $msg,
                'file'     => $e->getFile(),
                'line'     => $e->getLine(),
            ];
            pft_log(self::ORDER_SEARCH_EXPORT_EXCEL . "/error", json_encode($log, JSON_UNESCAPED_UNICODE));
        }

        return $this->returnDataV2($code, $this->_responseData($list, $isOver ?? true, $lastSequence ?? ''), $msg);
    }

    /**
     * 创建导出任务
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param $sid
     * @param $memberId
     * @param $request
     * @param $head
     *
     * @return array
     */
    private function _createExportTask($sid, $memberId, $request, $head)
    {
        //带上导出头信息
        $request['excel_head'] = $head;
        //唯一标识
        $request['hash'] = hash('sha256', json_encode($request) . "_" . uniqid());
        //导出字段
        $selfDefinedStructure = [
            'head'  => [],
            'field' => [],
        ];
        //整理导出头信息
        foreach ($head as $itemKey => $itemVal) {
            $selfDefinedStructure['head'][]  = [$itemVal];
            $selfDefinedStructure['field'][] = $itemKey;
        }
        //文件信息
        $fileName  = '订单查询';
        $firstHead = '';
        $pageCount = 0;
        //创建导出任务
        $dcExcelTaskBase = new DcExcelTaskBase();
        $result          = $dcExcelTaskBase->addTask($sid, $memberId, self::ORDER_SEARCH_EXPORT_EXCEL, $request,
            $pageCount, $fileName, $firstHead, $selfDefinedStructure);
        //返回结果处理
        $statusCode = $result['code'] == self::CODE_SUCCESS ? 200 : 400;
        $taskId     = $result['code'] == self::CODE_SUCCESS ? ($result['data']['taskId'] ?? 0) : 0;
        $message    = $result['code'] == self::CODE_SUCCESS ? ($result['msg'] ?? '记录任务') : $result['msg'];

        return $this->returnDataV2($statusCode, $taskId, $message);
    }

    /**
     * 处理导出参数
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param $request
     * @param $fid
     * @param $opId
     * @param $sdtype
     * @param $dtype
     * @param $account
     *
     * @return array
     * @throws
     */
    private function _handleParams($request, $fid, $opId, $sdtype, $dtype, $account)
    {
        if (!is_array($request)) {
            throw new \Exception('参数错误', 400);
        }

        //时间类型
        $this->_selectType = $request['time_type'];
        //请求参数处理
        $res = $this->getOrderBusiness()->handleOrderParam($request, $fid);
        if ($res['code'] === false) {
            throw new \Exception($res['msg'], 400);
        }

        //订单号去重过滤
        if (!empty($request['ordernum']) && is_array($request['ordernum'])) {
            //去重，"" 和0 过滤
            $request['ordernum'] = array_filter($request['ordernum'], function ($item) {
                return !empty($item);
            });
            $request['ordernum'] = array_values(array_unique($request['ordernum']));
        }

        //登录信息处理
        $request['account'] = $account;
        $request['dtype']   = $dtype;
        $request['sdtype']  = $sdtype;

        //景区id处理
        $this->_handleParamsLid($request, $fid, $sdtype, $opId, $dtype);

        //不同账号类型，sid处理
        $this->_handleParamsSid($request, $fid, $opId, $sdtype, $dtype, $account);

        //门票tid
        $this->_handleParamsTid($request);

        return $request;
    }

    /**
     * 景区参数处理
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param $request
     * @param $fid
     * @param $sdtype
     * @param $opId
     * @param $dtype
     *
     * @return true
     * @throws
     */
    private function _handleParamsLid(&$request, $fid, $sdtype, $opId, $dtype)
    {
        if (in_array($sdtype, [2, 3])) {
            return true;
        }

        $lidArr    = false;
        $notLidArr = false;
        if ($request['lid']) {
            //直接按选定的景区查询
            $lidArr = [$request['lid']];
        } else {
            //如果有按资源库查询的话
            if ($request['source_id']) {
                $landApi      = new \Business\CommodityCenter\Land();
                $resourceList = $landApi->queryLandMultiQueryByResourceId([$request['source_id']]);
                $lidArr       = array_column($resourceList, 'id');
            }
        }
        //供应商/分销商/集团账号 需要接入数据岗位权限
        if ($fid != 1) {
            $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($opId, $dtype, $sdtype);
            $condition     = $dataAuthLimit->transInOrNotCondition(['lidList' => $lidArr]);
            if ($condition === false) {
                throw new \Exception('数据岗位权限为全限制，不可导出', 400);
            }
            $lidArr    = $condition['lidList'] ?? false;
            $notLidArr = $condition['notLidList'] ?? false;
        }
        if ($lidArr) {
            $request['lid'] = $lidArr;
        }
        if ($notLidArr) {
            $request['not_lid'] = $notLidArr;
        }

        return true;
    }

    private function _handleParamsTid(&$request)
    {
        $request['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($request['tid']);
        $tidArr         = $request['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            throw new \Exception('一次最多可选20个票种，已超过，请重试', 400);
        }

        return true;
    }

    /**
     * 商户类型获取对应商户id
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param $request
     * @param $fid
     * @param $opId
     * @param $sdtype
     * @param $dtype
     * @param $account
     *
     * @return true
     */
    private function _handleParamsSid(&$request, $fid, $opId, $sdtype, $dtype, $account)
    {
        //不同账号类型，sid处理
        $sid        = $fid;
        $currentFid = $opId;
        if (in_array($sdtype, [2, 3])) {
            // 资源方账号登录，获取供应商ID
            $landApi      = new \Business\CommodityCenter\Land();
            $applyInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [],
                [], [$account]);
            if (!empty($applyInfoRes['list'])) {
                $sid = $applyInfoRes['list'][0]['apply_did'];
            }
        }
        //子商户订单查询导出
        $subMerchantInfo = $this->_getSubMerchant($fid, $sdtype, $dtype);
        $request         = array_merge($request, $subMerchantInfo);
        //如果有子商户信息，则用这个信息覆盖之前的sid和fid
        if (!empty($request['sub_supplier_id']) && !empty($request['sub_sid'])) {
            $currentFid = $request['sub_supplier_id'];
            $sid        = $request['sub_supplier_id'];
        }
        $request['sid']         = $sid;
        $request['current_fid'] = $currentFid;

        return true;
    }

    /**
     * excel导出头部处理
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param $fid
     * @param $opId
     * @param $sdtype
     * @param $dtype
     * @param $request
     *
     * @return array
     * @throws
     */
    private function _handleHead($fid, $opId, $sdtype, $dtype, $request)
    {
        $head = load_config('order_search', 'exportHead');
        $head = (new \Business\Order\OrderList())->_handleSpecialOrderExport($fid, $head, [], [], true); //特殊处理下
        if ($fid == 1 || in_array($sdtype, [2, 3])) {
            //管理员 不用分组
            unset($head['group']); //去掉分组字段
            unset($head['orderman']); //去掉下单员工字段
            if (in_array($sdtype, [2, 3])) {
                unset($head['noChecked']); //去掉未验证数
                unset($head['expireTNum']); //去掉已过期数
                unset($head['completeTNum']); //去掉已完结数
                unset($head['cmbId']);//去掉合并付款订单号
            }
            unset($head['saleDiscountMoney']); //去掉销售优惠总金额
            unset($head['points']); //去掉支付积分
            unset($head['buyAmount']); //去掉采购实付金额（扣除优惠金额）
            unset($head['buyDiscountAmount']); //去掉采购优惠总金额
            unset($head['after_sale_refund_money']); //去掉售后退回金额
            unset($head['after_sale_income_money']); //去掉售后收入金额
        }
        //从创建导出任务的入口增加透传判断是否显示子商户字段
        if (empty($request['needSubMerchantField']) || $fid == 1 || in_array($sdtype, [2, 3])) {
            unset($head['subSname']); //去掉子商户名称
        }
        //分销商账号只要在供应商、分销商角色的导出上增加即可（商家端），资源账号、子商户、管理端导出不需要增加这个字段
        if ($fid == 1 || !in_array($sdtype, [0, 1]) || ($dtype==6 && !in_array($sdtype, [0, 1]))) {
            unset($head['reseller_account']); //去掉分销商账号
        }
        /** @var ExportDetailOrderSearch $customField */
        $customField = Factory::getDriver(ExportDetailOrderSearch::class, $fid, $opId, $sdtype);

        return $customField->sort($head, null);
    }

    private function _getSubMerchant($fid, $sdtype, $dtype)
    {
        $info = [
            'sub_supplier_id'      => 0,
            'sub_sid'              => 0,
            'needSubMerchantField' => false,//excel是否显示子商户列
        ];
        if (MemberLoginHelper::isSubMerchantLogin()) {
            $info['sub_supplier_id'] = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
            $info['sub_sid']         = MemberLoginHelper::getLoginBusinessMember()->getSId();
        }
        //如果登录的是供应商
        if ($dtype == 0) {
            //获取子商户应用记录
            $userModuleUsedRes = (new ModuleBiz())->getUserModuleUsed([$fid],
                [SubMerchant::SUB_MERCHANT_APP_CENTER_TAG], false);
        }
        //如果登录的是员工
        if ($dtype == 6 && $sdtype == 0) {
            //获取子商户应用记录
            $userModuleUsedRes = (new ModuleBiz())->getUserModuleUsed([$fid],
                [SubMerchant::SUB_MERCHANT_APP_CENTER_TAG], false);
        }
        //存在子商户应用记录，则excel会有商户列
        if (!empty($userModuleUsedRes[$fid][SubMerchant::SUB_MERCHANT_APP_CENTER_TAG])) {
            $info['needSubMerchantField'] = true;
        }

        return $info;
    }

    /**
     * 商户导出订单明细
     * <AUTHOR>
     * @date   2025/03/03
     *
     * @param  array   $request
     * @param  string  $lastSequence
     * @param  int     $pageNum
     * @param  int     $pageSize
     *
     * @return array
     * @throws
     */
    private function _queryBusinessList($request, $lastSequence = '', $pageNum = 1, $pageSize = 200)
    {
        if (empty($request['sid']) || empty($request['current_fid']) || !isset($request['dtype']) || !isset($request['sdtype']) || empty($request['excel_head'])) {
            throw new \Exception("请求参数错误", 500);
        }
        $sid        = $request['sid'];
        $memberId   = $request['current_fid'];
        $memberType = $request['dtype'];
        $sdtype     = $request['sdtype'];
        $head       = $request['excel_head'];
        $requestId  = '';//废弃字段，可以忽略

        if ($memberType == 7) {
            //集团账号
            $orderSearchBusiness = new \Business\Order\OrderSearch();
            $memberArr           = $orderSearchBusiness->getRelationMember($sid);
        } else {
            $memberArr = [$sid];
        }
        //处理过滤参数
        $request = $this->_handleFilterFields($sid, $memberType, $request);
        //自定义导出字段, 传入创建任务时已选的导出字段
        /** @var ExportDetailOrderSearch $customField */
        $customField = Factory::getDriverFromExternalSelect(ExportDetailOrderSearch::class, $sid, $memberId, $sdtype,
            array_keys($head));
        $queryRes    = $this->getOrderBusiness()->getBusinessExportByOrderService($memberId, $sid, $memberType,
            $memberArr, $request['dbStart'], $request['dbEnd'], $pageNum, $pageSize, $request['orderNumArr'],
            $request['ordername'], $request['person_id'], $request['userMobileSubject'], $request['order_time_start'],
            $request['order_time_end'], $request['play_time_start'], $request['play_time_end'], $request['dtime_start'],
            $request['dtime_end'], $request['begin_time_start'], $request['begin_time_end'], $request['status'],
            $request['pay_status'], $request['payModeIn'], $request['orderModeIn'], $request['p_type'],
            $request['operate_id'], $request['check_resource'], $request['lidArr'], $request['pidArr'],
            $request['sellerIdArr'], $request['buyerIdArr'], $request['tidArr'], $request['order_source'],
            $request['if_print'], $request['mark'], $request['begin_first_time_start'],
            $request['begin_first_time_end'], $request['is_combine'], $request['upstreamOrderId'], $request['sub_sid'],
            $request['afterSaleState'], $request['check_code'], $request['notLidArr'], $customField,
            $request['sub_type'], $requestId, $request['round_id'], $request['cancel_time_start'] ?? false,
            $request['cancel_time_end'] ?? false, $request['touristMobileSubject'] ?? false,
            $request['touristIdentificationCode'] ?? false, $request['cmbId'], $request['personid'],
            $request['remotenum'], $request['apiOrder'] ?? '', $lastSequence, intval($request['identity_photo'] ?? 0));

        //因为后面实在不好加参数判断reseller_account，所以在外层处理
        $list = $queryRes['data']['list'] ?? [];
        foreach ($list as $key => $value) {
            if (!isset($head['reseller_account']) && isset($value['reseller_account'])) {
                unset($list[$key]['reseller_account']);
            }
        }
        $queryRes['data']['list'] = $list;

        return $this->_handleResult($queryRes, $pageSize);
    }

    /**
     * 资源方导出订单明细
     * <AUTHOR>
     * @date   2025/03/03
     *
     * @param  array   $request
     * @param  string  $lastSequence
     * @param  int     $pageNum
     * @param  int     $pageSize
     *
     * @return array
     * @throws
     */
    private function _queryScenicList($request, $lastSequence = '', $pageNum = 1, $pageSize = 200)
    {
        if (empty($request['sid']) || empty($request['current_fid']) || !isset($request['dtype']) || empty($request['account'])) {
            throw new \Exception("请求参数错误", 500);
        }

        $sid        = $request['sid'];
        $memberId   = $request['current_fid'];
        $memberType = $request['dtype'];
        $account    = $request['account'];
        $requestId  = ''; //废弃字段，可以忽略

        //景区数据查询
        $request['salerid'] = $account;

        //处理过滤参数
        $request  = $this->_handleFilterFields($sid, $memberType, $request);
        $queryRes = $this->getOrderBusiness()->getScenicExportByOrderService($memberId, $sid, $memberType,
            $request['salerid'], $request['dbStart'], $request['dbEnd'], $pageNum, $pageSize, $request['orderNumArr'],
            $request['ordername'], $request['person_id'], $request['userMobileSubject'], $request['order_time_start'],
            $request['order_time_end'], $request['play_time_start'], $request['play_time_end'], $request['dtime_start'],
            $request['dtime_end'], $request['begin_time_start'], $request['begin_time_end'], $request['status'],
            $request['pay_status'], $request['payModeIn'], $request['orderModeIn'], $request['p_type'],
            $request['operate_id'], $request['check_resource'], $request['pidArr'], $request['tidArr'],
            $request['order_source'], $request['if_print'], $request['mark'], $request['begin_first_time_start'],
            $request['begin_first_time_end'], $request['upstreamOrderId'], $request['afterSaleState'],
            $request['check_code'], $request['sub_type'], $requestId, $request['round_id'],
            $request['cancel_time_start'] ?? false, $request['cancel_time_end'] ?? false,
            $request['touristMobileSubject'] ?? false, $request['touristIdentificationCode'] ?? false, $request['cmbId'],
            $request['personid'], $request['remotenum'], $request['apiOrder'] ?? '', $lastSequence);

        return $this->_handleResult($queryRes, $pageSize);
    }

    /**
     * 管理端导出订单明细
     * <AUTHOR>
     * @date   2025/03/03
     *
     * @param       $request
     * @param       $lastSequence
     * @param       $pageNum
     * @param  int  $pageSize
     *
     * @return array
     * @throws
     */
    private function _queryAdminList($request, $lastSequence = '', $pageNum = 1, $pageSize = 200)
    {
        if (empty($request['sid']) || empty($request['current_fid']) || !isset($request['dtype'])) {
            throw new \Exception("请求参数错误", 500);
        }

        $sid        = $request['sid'];
        $memberId   = $sid;
        $memberType = $request['dtype'];
        $requestId  = $request['request_id'];
        //处理过滤参数
        $request  = $this->_handleFilterFields($sid, $memberType, $request);
        $queryRes = $this->getOrderBusiness()->getAdminExportByOrderService($memberId, $sid, $memberType,
            $request['dbStart'], $request['dbEnd'], $pageNum, $pageSize, $request['orderNumArr'], $request['ordername'],
            $request['person_id'], $request['userMobileSubject'], $request['order_time_start'], $request['order_time_end'],
            $request['play_time_start'], $request['play_time_end'], $request['dtime_start'], $request['dtime_end'],
            $request['begin_time_start'], $request['begin_time_end'], $request['status'], $request['pay_status'],
            $request['payModeIn'], $request['orderModeIn'], $request['p_type'], $request['operate_id'],
            $request['check_resource'], $request['lidArr'], $request['pidArr'], $request['sellerIdArr'],
            $request['buyerIdArr'], $request['tidArr'], $request['order_source'], $request['if_print'],
            $request['mark'], $request['begin_first_time_start'], $request['begin_first_time_end'],
            $request['is_combine'], $request['upstreamOrderId'], $request['afterSaleState'], $request['check_code'],
            $request['sub_type'], $requestId, $request['round_id'], $request['cancel_time_start'] ?? false,
            $request['cancel_time_end'] ?? false, $request['touristMobileSubject'] ?? false,
            $request['touristIdentificationCode'] ?? false, $request['cmbId'], $request['personid'],
            $request['remotenum'], $request['apiOrder'] ?? '', $lastSequence);

        return $this->_handleResult($queryRes, $pageSize);
    }

    /**
     * 查询时间段处理
     * <AUTHOR>
     * @date   2025/02/20
     *
     * @param $request
     *
     * @return array
     */
    private function _getTimeTypeConf($request)
    {
        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $request['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        return [$dbStart, $dbEnd];
    }

    /**
     * 参数处理，转为数组形式
     * <AUTHOR>
     * @date   2025/03/03
     *
     * @param $request
     * @param $key
     *
     * @return array|false
     */
    private function _paramHandleArray($request, $key)
    {
        if (!isset($request[$key])) {
            return false;
        }
        if ($request[$key] === false) {
            return false;
        }
        if (is_array($request[$key])) {
            return $request[$key];
        }

        return $request[$key] ? [$request[$key]] : false;
    }

    /**
     * 查询条件数据格式处理
     * <AUTHOR>
     * @date   2025/03/05
     *
     * @param $sid
     * @param $memberType
     * @param $request
     *
     * @return mixed
     */
    private function _handleFilterFields($sid, $memberType, $request)
    {
        //查询时间段处理
        list($dbStart, $dbEnd) = $this->_getTimeTypeConf($request);
        $pidArr      = $this->_paramHandleArray($request, 'pid');
        $tidArr      = $this->_paramHandleArray($request, 'tid');
        $orderNumArr = $this->_paramHandleArray($request, 'ordernum');
        $orderModeIn = $this->_paramHandleArray($request, 'order_mode');
        $payModeIn   = $this->_paramHandleArray($request, 'pay_mode');
        //需要处理的条件组装
        $filter = [
            'dbStart'     => $dbStart, //查询时段开始时间
            'dbEnd'       => $dbEnd, //查询时段结束时间
            'pidArr'      => $pidArr, //产品id数组
            'tidArr'      => $tidArr, //门票id数组
            'orderNumArr' => $orderNumArr, //订单号数组
            'orderModeIn' => $orderModeIn, //下单渠道数组
            'payModeIn'   => $payModeIn, //支付方式数组
            'lidArr'      => false, //景区id数组
            'sellerIdArr' => false, //供应商id数组
            'buyerIdArr'  => false, //分销商id数组
            'notLidArr'   => false, //排除景区id数组
        ];
        if ($sid == 1) {//管理端
            $filter['lidArr']      = $this->_paramHandleArray($request, 'lid');
            $filter['sellerIdArr'] = $this->_paramHandleArray($request, 'aid');
            $filter['buyerIdArr']  = $this->_paramHandleArray($request, 'reseller_id');
        } else if (in_array($memberType, [2, 3])) {//资源账号
            //资源方查询额外条件
        } else {//供应商/分销商/集团账号
            $filter['sellerIdArr'] = $this->_paramHandleArray($request, 'aid');
            $filter['buyerIdArr']  = $this->_paramHandleArray($request, 'reseller_id');
            $filter['lidArr']      = $this->_paramHandleArray($request, 'lid');
            $filter['notLidArr']   = $this->_paramHandleArray($request, 'not_lid');
        }
        //查询条件组装替换
        foreach ($filter as $key => $val) {
            $request[$key] = $val;
        }

        return $request;
    }

    /**
     * 返回数据格式处理
     * <AUTHOR>
     * @date   2025/03/05
     *
     * @param  array|null   $list
     * @param  bool|null    $isOver
     * @param  string|null  $lastSequence
     *
     * @return array
     */
    private function _responseData(?array $list = [], ?bool $isOver = true, ?string $lastSequence = ''): array
    {
        return [
            'list'         => $list ?? [],
            'isOver'       => $isOver ?? true,
            'lastSequence' => empty($lastSequence) ? uniqid() : $lastSequence,
        ];
    }

    /**
     * 结果处理
     * <AUTHOR>
     * @date   2025/03/05
     *
     * @param  array  $queryRes
     * @param  int    $pageSize
     *
     * @return array
     * @throws
     */
    private function _handleResult(array $queryRes, int $pageSize = 1000): array
    {
        if ($queryRes['code'] != 200) {
            $errMsg = $queryRes['msg'];
            throw new \Exception("订单数据查询失败[{$errMsg}]", 500);
        }

        $list         = $queryRes['data']['list'];
        $lastSequence = $queryRes['data']['scrollKey'] ?? '';
        $isOver       = false;
        //判断是否还有数据
        if (empty($list) || count($list) < $pageSize) {
            $isOver = true;
            //不为空，默认一个
            $lastSequence = uniqid();
        }

        return $this->_responseData($list, $isOver, $lastSequence);
    }
}
