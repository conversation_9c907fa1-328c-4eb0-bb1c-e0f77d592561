<?php
/**
 * 订单修改业务的封装
 *
 * <AUTHOR>
 * @date    2017-04-05
 */

namespace Business\Order;

use Business\Base;
use Business\CommodityCenter\Ticket as ticketBiz;
use Business\Finance\Credit as BizCredit;
use Business\JavaApi\Fund\Trade;
use Business\JavaApi\Order\OrderDetailUpdate;
use Business\JavaApi\Order\OrderParamConvert;
use Business\JavaApi\Order\OrderReservation;
use Business\JavaApi\Order\OrderTicketDiscounts;
use Business\JavaApi\Order\OrderTicketDiscounts as OrderTicketDiscountsApi;
use Business\JavaApi\Order\OrderTouristInfoExtendService;
use Business\JavaApi\Product\ReservationOrderStorage;
use Business\JavaApi\StorageApi;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Member\PlatformExpense;
use Business\NewJavaApi\NewPms\StockQuery;
use Business\NewJavaApi\Order\FeignClient;
use Business\Order\RefundApprovalCenterService\AbstractApprovalCenterService;
use Business\Order\RefundApprovalCenterService\RefundApprovalService;
use Business\PackTicket\PackOrder;
use Business\Pay\PayBase;
use Business\TeamOrder\Application;
use Business\TeamOrder\TeamRules;
use Library\Cache\Cache;
use Library\Constants\OrderConst;
use Library\Constants\Product\ProductConst;
use Library\Container;
use Library\Tools;
use Library\Tools\Helpers;
use Model\AdminConfig\AdminConfig;
use Model\Member\AccessRecord;
use Model\Member\Member;
use Model\Order\OrderHandler;
use Model\Order\OrderRefer;
use Model\Order\OrderSonRefundLog;
use Model\Order\OrderTools;
use Model\Order\OrderTrack;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery;
use Model\Order\SubOrderQuery\SubOrderAddon;
use Model\Order\TeamOrderSearch;
use Model\Product\Land;
use Model\Product\PriceRead;
use Model\Product\Ticket;
use Model\Team\TeamConfig;
use Model\Terminal\FaceCompare;
use Model\TradeRecord\OrderRefund;
use \Exception;
use Model\Wechat\WxMember;

class Modify extends Base
{
    private $_numModType;
    private $_memberId;
    private $_serverInsideHandle;
    private $_OrderHandlerModel;

    private $_orderCommonModel;
    private $_orderTrackModel;
    private $_memberModel;
    private $_orderToolsModel;
    private $_ticketModel;
    private $_buyChainModel;
    private $_landModel;

    private $_reduceIdCards;
    // 团队订单模型
    private $_teamOrderModel;

    private $_creditBiz;

    public function __construct()
    {
        $this->_numModType    = 0;
        $this->_reduceIdCards = [];

        $this->_orderCommonModel  = new \Model\Order\OrderCommon();
        $this->_orderTrackModel   = new \Model\Order\OrderTrack();
        $this->_orderToolsModel   = new \Model\Order\OrderTools();
        $this->_memberModel       = new \Model\Member\Member();
        $this->_ticketModel       = new \Model\Product\Ticket('slave');
        $this->_buyChainModel     = new \Model\Order\BuyChain('slave');
        $this->_OrderHandlerModel = new \Model\Order\OrderHandler();
    }

    /**
     * 订单增加票数
     * <AUTHOR>
     * @date   2019-07-31
     *
     * @param  string  $ordernum  主订单号
     * @param  array  $numMapping  ['订单号1' => 增加后的票数, '订单号2' => '增加后的票数']
     * @param  int  $memberId  操作人主账号id
     * @param  int|integer  $oper  操作人id
     * @param  array  $touristInfoArray  [['tourist' => '游客姓名', 'idcard' => '证件号', 'voucher_type' => 证件类型]]
     *
     * @return array
     */
    public function orderAddNum(string $ordernum, array $numMapping, int $memberId, int $oper = 0, array $touristInfoArray = [], $getOrderInfoCache = false)
    {

        if (!$ordernum || !$numMapping || !$memberId) {
            return $this->returnData(204, '参数错误');
        }
        //增加票数基本检测
        $checkRes = $this->_numAddCheck($ordernum, $numMapping, $memberId);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        //$baseCheck = new BaseCheck();
        //$res = $baseCheck->checkTeamOrderDiscount($ordernum);
        //if($res['code']==200 && $res['data']['isUseCoupon']){
        //    return $this->returnData(205, '已享受报团优惠，不允许加票，可取消订单后重新下单');
        //}

        $failArr   = [];
        $changeNum = 0;
        $extra = [];
        $getOrderInfoCache && $extra = ['get_order_info_cache' => 1];
        foreach ($numMapping as $eachOrdernum => $tnum) {
            $res = Helpers::orderAddNum($eachOrdernum, $tnum, $memberId, $oper, $touristInfoArray, $extra);
            if ($res['code'] != 200) {
                //返回订单失败信息
                $msg       = $res['msg'];
                $failArr[] = "{$eachOrdernum}操作失败:{$msg}";
            }
            $changeNum += $res['data']['change_num'];
        }

        if ($failArr) {
            return $this->returnData(204, implode('|', $failArr));
        } else {
            return $this->returnData(200, '', ['change' => $changeNum]);
        }

    }

    /**
     * 订单增加票数参数检测
     * <AUTHOR>
     * @date   2019-07-31
     *
     * @param  string  $ordernum  主订单号
     * @param  array  $numMapping  ['订单号1' => 增加后的票数, '订单号2' => '增加后的票数']
     * @param  int  $memberId  操作人主账号id
     *
     * @return array
     */
    private function _numAddCheck($ordernum, $numMapping, $memberId)
    {
        $code   = 200;
        $errMsg = '';
        $data   = [];

        //获取订单详情
        $field     = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid,ss.ordermode,ss.tid';
        $orderInfo = $this->_orderToolsModel->getOrderInfo($ordernum, $field);

        if (!$orderInfo) {
            $errMsg = '订单不存在';
        }

        $orderList = [$orderInfo];
        //联票
        if (count($numMapping) > 1) {
            //获取联票信息
            $field     = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
            $orderList = $this->_orderToolsModel->getLinkOrdersInfo($ordernum, $field);
        }
        $linkOrderArr = array_column($orderList, 'ordernum');

        foreach ($numMapping as $eachOrdernum => $tnum) {
            if (!in_array($eachOrdernum, $linkOrderArr)) {
                $errMsg = '订单号有误';
                break;
            }
            if ($tnum < 1) {
                $errMsg = '票数有误';
                break;
            }
        }

        if (!$errMsg) {
            $data = ['order_list' => array_key($orderList, 'ordernum')];
        } else {
            $code = 204;
        }

        return $this->returnData($code, $errMsg, $data);
    }

    /**
     * 订单取消接口（调这个方法的时候可以看下下面2个关联的方法，一个针对用户目前逻辑和这个一样，一个针对不是本人取消）
     * <AUTHOR>
     * @date   2019-07-17
     *
     * @param  string  $ordernum  退票订单号
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断，默认是需要的
     * @param  boolean  $linkFilter  联票状态是否过滤状态是已使用的票 true过滤
     * @param  bool  $isFailRollBack  是否是失败的订单回滚
     * @param  int $isRefundDeposit 是否退押金 0：不退押金 1:退押金（计时特有）
     * @param  int $isRefundOverPay 是否退超时补费金额 0：不退 1:退（计时特有）
     * @param  array $moreData  其他参数
     * ```
     * {
     *     'is_syntax_rollback' => false, //是否系统原因导致的回滚，需要退回码费等特殊款项
     * }
     * ```
     *
     * @return array
     */
    public function baseCancel($ordernum, $opId, $parentId, $cancelChannel, $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $linkFilter = false, $isFailRollBack = false, $isRefundDeposit = 0, $isRefundOverPay = 0, $moreData = [])
    {
        $ordernum        = strval($ordernum);
        $opId            = intval($opId);
        $parentId        = intval($parentId);
        $reqSerialNumber = strval($reqSerialNumber);
        $isNeedAudit     = boolval($isNeedAudit);

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }
        if(!$isFailRollBack){
            // 回滚订单 跳过团单退票规则校验
            $baseRefundCheck = new BaseRefundCheck();
            $checkRes = $baseRefundCheck->checkTeamReservationRule($orderInfo['ordermode'],[$ordernum => 0],$opId,$parentId,true);
            if ($checkRes['code'] != 200) {
                return $checkRes;
            }
        }
        //判断取消权限
        $isHaveAuth = $this->_isHaveRefundAuth($orderInfo, $opId, $parentId);
        if (!$isHaveAuth) {
            return $this->returnData(204, '没有退票权限');
        }
        $orderInfo['reqSerialNumber'] = $reqSerialNumber;
        //需要取消的订单列表
        $cancelOrderList = [$orderInfo];

        //如果是联票的话，而且是取消主票的话，把所有的票都取消掉
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent && $linkParent == $ordernum) {
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                if ($linkFilter) {
                    foreach ($linkOrderList as $linkKey => $linkValue) {
                        if ($linkValue['ordernum'] != $ordernum && $linkValue['status'] == 1) {
                            unset($linkOrderList[$linkKey]);
                        }
                    }
                    $linkOrderList = array_values($linkOrderList);
                }
                $cancelOrderList = $this->_getAuditOrderInfo($linkOrderList);
            }
        }
        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];

        foreach ($cancelOrderList as $subIndex => $subOrderInfo) {
            //调用统一的退票接口
            $subOrdernum      = $subOrderInfo['ordernum'];
            $cancelNum        = -1;
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_rollback'        => $isFailRollBack,
                'is_refund_deposit'  => $isRefundDeposit,
                'is_refund_over_pay' => $isRefundOverPay,
                'is_syntax_rollback' => $moreData['is_syntax_rollback'] ?? false, //是否系统原因导致的回滚，需要退回码费等特殊款项
            ];
            $reqSerialNumber  = $subOrderInfo['reqSerialNumber'];

            $refundRes = Helpers::platformRefund($subOrdernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                //记录第一个票的退票流水信息
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(0, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    /**
     * 订单强制取消接口
     * <AUTHOR>
     * @date   2019-09-30
     *
     * @param  string  $ordernum  退票订单号
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断，默认是需要的
     * @param  int  $ip  ip
     *
     * @return array
     */
    public function forceCancel($ordernum, $opId, $parentId, $cancelChannel, $cancelRemark, $isNeedAudit = false, $ip = 0,$otherData = [])
    {
        $ordernum    = strval($ordernum);
        $opId        = intval($opId);
        $parentId    = intval($parentId);
        $isNeedAudit = boolval($isNeedAudit);
        $ticketCodeArr = $otherData['cancelTicketCode'] ?? [];
        $cancelNum     = $otherData['cancelNum'] ?? -1;
        //判断是否有在审核中
        $auditMdl  = new RefundAuditModel();
        $accessMdl = new AccessRecord();
        $isInAudit = $auditMdl->isAllUnderAudit([$ordernum]);
        if ($isInAudit) {
            return $this->returnData(203, '订单审核中');
        }

        //插入敏感表
        $accessRes = $accessMdl->insert($opId, 1, "退款|$ordernum|{$opId}", $ip);
        if (!$accessRes) {
            return $this->returnData(203, '操作信息记录失败');
        }

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $applyField  = 'apply.apply_id';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField, false, $applyField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }
        if($orderInfo['pay_status'] == 2 && $cancelNum >0){
            $baseCheck = new BaseCheck();
            $res = $baseCheck->checkTeamOrderDiscount($ordernum);
            if($res['code']==200 && $res['data']['isUseCoupon']){
                return $this->returnData(205, '已享受报团优惠，未支付下，不允许部分退票');
            }
        }
        if ($parentId != 1) {
            $adminMdl      = new  AdminConfig();
            $checkPowerRes = $adminMdl->havePermission($parentId, 25);
            if (!$checkPowerRes) {
                return $this->returnData(203, '无使用权限');
            }
            if ($orderInfo['apply_id'] != $parentId) {
                return $this->returnData(203, '不是自供应不能退');
            }
        }

        $cancelOrderList = [$orderInfo];
        //如果是联票的话，而且是取消主票的话，把所有的票都取消掉
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent && $linkParent == $ordernum) {
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');
            $linkOrderNum  = array_column($linkOrderList, 'ordernum');
            $isLinkAudit   = $auditMdl->isAllUnderAudit($linkOrderNum);
            if ($isLinkAudit) {
                return $this->returnData(203, '联票存在订单审核中');
            }
            $cancelOrderList = $linkOrderList;
        }

        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];

        foreach ($cancelOrderList as $subIndex => $subOrderInfo) {
            //调用统一的退票接口
            $subOrdernum      = $subOrderInfo['ordernum'];
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [
                'ticket_code_list' => $ticketCodeArr,
            ];
            $cancelSpecialArr = [
                'is_need_audit'           => $isNeedAudit,
                'is_force_cancel'         => true,
                'is_refund_deposit'       => $otherData['isRefundDeposit'],
                'is_skip_exchange_ticket' => $otherData['isSkipExchangeTicket'],
            ];
            $reqSerialNumber  = '';

            $refundRes = Helpers::platformRefund($subOrdernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                //记录第一个票的退票流水信息
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(0, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];
            $msg     = $successCode == 200 ? '退票成功' : '提交退票审核';

            return $this->returnData($successCode, $msg, $resData);
        }

    }

    /**
     * 审核通过取消
     * <AUTHOR>
     * @date   2019-09-23
     *
     * @param  string  $orderNum  退票订单号
     * @param  bool  $isAdmin  是否用管理员退票
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断，默认是需要的
     * @param  boolean  $linkFilter  联票状态是否过滤状态是已使用的票 true过滤
     * @param  bool  $isAuditPass  是否是审核通过的
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function auditPassCancel($orderNum, $isAdmin = false, $opId, $parentId, $cancelChannel, $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $linkFilter = false, $isAuditPass = false, $otherParams = [])
    {
        if ($isAdmin) {
            $cancelRes = $this->noAuthCancel($orderNum, $opId, $cancelChannel, $cancelRemark, $reqSerialNumber,
                $isNeedAudit, $linkFilter, false, $isAuditPass, [], -1, $otherParams);
        } else {
            $cancelRes = $this->userCancel($orderNum, $opId, $parentId, $cancelChannel, $cancelRemark, $reqSerialNumber,
                $isNeedAudit, $linkFilter, $isAuditPass, $otherParams);
        }
        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $orderNum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';

            return $this->returnData($cancelRes['code'], $succMsg, $resData);
        } else {
            return $this->returnData($cancelRes['code'], $cancelRes['msg'], $cancelRes['data']);
        }
    }

    /**
     * 用户类型取消接口（对应上面的baseCancel，切出来的一个针对用户的）
     * <AUTHOR>
     * @date   2019-09-23
     *
     * @param  string  $ordernum  退票订单号
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断，默认是需要的
     * @param  boolean  $linkFilter  联票状态是否过滤状态是已使用的票 true过滤
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function userCancel($ordernum, $opId, $parentId, $cancelChannel, $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $linkFilter = false, $isAuditPass = false, $otherParams = [])
    {
        $ordernum        = strval($ordernum);
        $opId            = intval($opId);
        $parentId        = intval($parentId);
        $reqSerialNumber = strval($reqSerialNumber);
        $isNeedAudit     = boolval($isNeedAudit);

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }

        //判断取消权限
        $isHaveAuth = $this->_isHaveRefundAuth($orderInfo, $opId, $parentId);
        if (!$isHaveAuth) {
            return $this->returnData(204, '没有退票权限');
        }

        return $this->_handleCancel($orderInfo, $ordernum, $opId, $cancelChannel, $cancelRemark, $reqSerialNumber,
            $isNeedAudit, $linkFilter, false, $isAuditPass, [], -1, $otherParams);

    }

    /**
     * 不提前判断取消权限的取消接口（对应上面的baseCancel，切出来的一个不提前判断取消权限的）
     * <AUTHOR>
     * @date   2019-09-23
     *
     * @param  string  $ordernum  退票订单号
     * @param  int  $opId  退票用户ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断，默认是需要的
     * @param  boolean  $linkFilter  联票状态是否过滤状态是已使用的票 true过滤
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function noAuthCancel($ordernum, $opId, $cancelChannel, $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $linkFilter = false, $isFailRollBack = false, $isAuditPass = false, $cancelPersonArr = [], $cancelNum = -1, $otherParams = [])
    {
        $ordernum        = strval($ordernum);
        $opId            = intval($opId);
        $reqSerialNumber = strval($reqSerialNumber);
        $isNeedAudit     = boolval($isNeedAudit);

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }

        return $this->_handleCancel($orderInfo, $ordernum, $opId, $cancelChannel, $cancelRemark, $reqSerialNumber,
            $isNeedAudit, $linkFilter, $isFailRollBack, $isAuditPass, $cancelPersonArr, $cancelNum, $otherParams);
    }

    /**
     * 订单部分退票接口
     * <AUTHOR>
     * @date   2019-07-17
     *
     * @param  string  $ordernum  退票主订单号
     * @param  array  $cancelList  订单的退票数量 {'24425694':1,'24425695':2}
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  array  $touristList  退票身份证
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断
     * @param  int  $shop  操作渠道  同shop字段
     * @param  int  $isAuditPass  是否是审核通过的
     * @param  array  $orderIdxList  ['ordernum' =>idx]
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function baseRefund($ordernum, $cancelList, $opId, $parentId, $cancelChannel, $touristList = [], $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $isAuditPass = false, $orderIdxList = [], $otherParams = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return  self::baseRefundForApprova($ordernum, $cancelList, $opId, $parentId, $cancelChannel,
                $touristList, $cancelRemark, $reqSerialNumber, $isNeedAudit, $isAuditPass,
                $orderIdxList, $otherParams);
        }

        $ordernum        = strval($ordernum);
        $opId            = intval($opId);
        $parentId        = intval($parentId);
        $cancelRemark    = strval($cancelRemark);
        $reqSerialNumber = strval($reqSerialNumber);
        $isNeedAudit     = boolval($isNeedAudit);

        if (!$cancelList || !is_array($cancelList)) {
            return $this->returnData(203, '退票参数错误');
        }

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }

        $baseRefundCheck = new BaseRefundCheck();
        $checkRes = $baseRefundCheck->checkTeamReservationRule($orderInfo['ordermode'],$cancelList,$opId,$parentId);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        //$baseCheck = new BaseCheck();
        //$checkDiscountRes = $baseCheck->checkTeamOrderDiscount($ordernum,$orderInfo['ordermode']);
        //if($checkDiscountRes['code']==200 && $checkDiscountRes['data']['isUseCoupon']){
        //    return $this->returnData(204, '已达到报团优惠最低票数量条件，不允许退票，可取消订单后重新下单');
        //}

        //判断取消权限
        $isHaveAuth = $this->_isHaveRefundAuth($orderInfo, $opId, $parentId);
        if (!$isHaveAuth) {
            return $this->returnData(204, '没有退票权限');
        }

        //需要取消的订单列表
        $allOrdernum = [$ordernum];
        //如果是联票的话，获取所有的子票
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent) {
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                $allOrdernum = array_column($linkOrderList, 'ordernum');
            }
        }

        //判断需要退票的订单是否都是属于该订单
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            if (!in_array($subOrdernum, $allOrdernum)) {
                return $this->returnData(204, '退票参数错误');
            }
        }

        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $subIndex      = 0;
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];
        $moreData = [];
        if ($otherParams['subSid'] && $otherParams['subOpId']){
            $moreData['subSid'] = $otherParams['subSid'];
            $moreData['subOpId'] = $otherParams['subOpId'];
        }
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            //调用统一的退票接口
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            if(isset($otherParams['audit_data']['after_sale_num']) && !empty($otherParams['audit_data']['after_sale_num'])){
                $cancelPersonArr  = [
                    'person_index'  => reset($otherParams['audit_data']['tourist_idx']) ?? 0,
                ];
            }
            else{
                $cancelPersonArr  = [
                    'person_id_list' => $touristList,
                    'person_index'   => $orderIdxList[$subOrdernum] ?? 0,
                ];
            }
            if (isset($otherParams['person']['person_info_list'])) {
                $cancelPersonArr['person_info_list'] = $otherParams['person']['person_info_list'];
            }

            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_audit_pass'      => $isAuditPass,
                'is_refund_deposit'  => $otherParams['audit_data']['is_refund_deposit'],
                'is_refund_over_pay' => $otherParams['audit_data']['is_refund_over_pay'],
                'cancel_audit_remark' => $otherParams['audit_data']['cancel_audit_remark'] ?? '',
                'discount' => $otherParams['audit_data']['discount'] ?? '',
                'after_sale_num'=>$otherParams['audit_data']['after_sale_num'],
                'batch_refund_more_idx'=>$otherParams['audit_data']['batch_refund_more_idx']
            ];

            $refundRes = Helpers::platformRefund($subOrdernum, $subCancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);

            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }

            $subIndex += 1;
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(205, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    /**
     * 订单部分退票接口
     * <AUTHOR>
     * @date   2019-07-17
     *
     * @param  string  $ordernum  退票主订单号
     * @param  array  $cancelList  订单的退票数量 {'24425694':1,'24425695':2}
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  array  $touristList  退票身份证
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断
     * @param  int  $shop  操作渠道  同shop字段
     * @param  int  $isAuditPass  是否是审核通过的
     * @param  array  $orderIdxList  ['ordernum' =>idx]
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function baseRefundForApprova($ordernum, $cancelList, $opId, $parentId, $cancelChannel, $touristList = [], $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $isAuditPass = false, $orderIdxList = [], $otherParams = [])
    {
        $ordernum        = strval($ordernum);
        $opId            = intval($opId);
        $parentId        = intval($parentId);
        $cancelRemark    = strval($cancelRemark);
        $reqSerialNumber = strval($reqSerialNumber);
        $isNeedAudit     = boolval($isNeedAudit);

        if (!$cancelList || !is_array($cancelList)) {
            return $this->returnData(203, '退票参数错误');
        }

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }

        $baseRefundCheck = new BaseRefundCheck();
        $checkRes = $baseRefundCheck->checkTeamReservationRule($orderInfo['ordermode'],$cancelList,$opId,$parentId);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        //$baseCheck = new BaseCheck();
        //$checkDiscountRes = $baseCheck->checkTeamOrderDiscount($ordernum,$orderInfo['ordermode']);
        //if($checkDiscountRes['code']==200 && $checkDiscountRes['data']['isUseCoupon']){
        //    return $this->returnData(204, '已达到报团优惠最低票数量条件，不允许退票，可取消订单后重新下单');
        //}

        //判断取消权限
        $isHaveAuth = $this->_isHaveRefundAuth($orderInfo, $opId, $parentId);
        if (!$isHaveAuth) {
            return $this->returnData(204, '没有退票权限');
        }

        //需要取消的订单列表
        $allOrdernum = [$ordernum];
        //如果是联票的话，获取所有的子票
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent) {
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                $allOrdernum = array_column($linkOrderList, 'ordernum');
            }
        }

        //判断需要退票的订单是否都是属于该订单
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            if (!in_array($subOrdernum, $allOrdernum)) {
                return $this->returnData(204, '退票参数错误');
            }
        }

        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $subIndex      = 0;
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];
        $moreData = [];
        if ($otherParams['subSid'] && $otherParams['subOpId']){
            $moreData['subSid'] = $otherParams['subSid'];
            $moreData['subOpId'] = $otherParams['subOpId'];
        }
        if (isset($otherParams['isMainRefund'])){
            $moreData['isMainRefund'] = $otherParams['isMainRefund'];
        }
        if (isset($otherParams['isApprovalFund'])){
            $moreData['isApprovalFund'] = $otherParams['isApprovalFund'];
        }
        if (isset($otherParams['operatorID'])){
            $moreData['operatorID'] = $otherParams['operatorID'];
        }
        if (isset($otherParams['approvalComment'])){
            $moreData['approvalComment'] = $otherParams['operatorID'];
        }
        if (isset($otherParams['processInstanceId'])){
            $moreData['processInstanceId'] = $otherParams['processInstanceId'];
        }
        if (isset($otherParams['isThirdAuditRes'])){
            $moreData['isThirdAuditRes'] = $otherParams['isThirdAuditRes'];
        }
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            //调用统一的退票接口
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            if(isset($otherParams['audit_data']['after_sale_num']) && !empty($otherParams['audit_data']['after_sale_num'])){
                $cancelPersonArr  = [
                    'person_index'  => reset($otherParams['audit_data']['tourist_idx']) ?? 0,
                ];
            }
            else{
                $cancelPersonArr  = [
                    'person_id_list' => $touristList,
                    'person_index'   => $orderIdxList[$subOrdernum] ?? 0,
                ];
            }

            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_audit_pass'      => $isAuditPass,
                'is_refund_deposit'  => $otherParams['audit_data']['is_refund_deposit'],
                'is_refund_over_pay' => $otherParams['audit_data']['is_refund_over_pay'],
                'cancel_audit_remark' => $otherParams['audit_data']['cancel_audit_remark'] ?? '',
                'discount' => $otherParams['audit_data']['discount'] ?? '',
                'after_sale_num'=>$otherParams['audit_data']['after_sale_num'],
                'batch_refund_more_idx'=>$otherParams['audit_data']['batch_refund_more_idx']
            ];

            $refundRes = Helpers::platformRefund($subOrdernum, $subCancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);

            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }

            $subIndex += 1;
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(205, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    /**
     * 订单部分退票接口
     * <AUTHOR>
     * @date   2019-07-17
     *
     * @param  string  $ordernum  退票主订单号
     * @param  array  $cancelList  订单的退票数量 {'24425694':1,'24425695':2}
     * @param  int  $opId  退票用户ID
     * @param  int  $cancelChannel  退票渠道
     * @param  array  $touristList  退票身份证
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function noAuthRefund($ordernum, $cancelList, $opId, $cancelChannel, $touristList = [], $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $isAuditPass = false, $otherParams = [])
    {
        $ordernum        = strval($ordernum);
        $opId            = intval($opId);
        $cancelRemark    = strval($cancelRemark);
        $reqSerialNumber = strval($reqSerialNumber);
        $isNeedAudit     = boolval($isNeedAudit);

        if (!$cancelList || !is_array($cancelList)) {
            return $this->returnData(203, '退票参数错误');
        }

        //获取订单信息
        $orderModel = new OrderTools();

        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid';
        $detailField = 'de.concat_id,de.aids';
        $orderInfo   = $orderModel->getOrderInfo($ordernum, $field, $detailField);
        if (!$orderInfo) {
            return $this->returnData(203, '没有退票权限');
        }

        return $this->_handleRefund($orderInfo, $ordernum, $cancelList, $opId, $cancelChannel, $touristList,
            $cancelRemark, $reqSerialNumber, $isNeedAudit, $isAuditPass, $otherParams);
    }

    /**
     * 团队订单取消接口
     * <AUTHOR>
     * @date   2019-09-04
     *
     * @param  array  $ordernumArr  退票订单号数组
     * @param  string  $teamOrder  团单主订单
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断，默认是需要的
     *
     * @return array
     */
    public function teamBaseCancel($ordernumArr, $teamOrder, $opId, $parentId, $cancelChannel, $cancelRemark = '', $isNeedAudit = true, $isNeedAuth = true)
    {
        $orderArr    = (array)array_keys($ordernumArr);
        $opId        = intval($opId);
        $parentId    = intval($parentId);
        $isNeedAudit = boolval($isNeedAudit);

        $numMapping  = [];
        $orderParams = $this->teamOrderCheckData($orderArr, $teamOrder, $opId, $parentId, $numMapping, $isNeedAuth);
        if ($orderParams['code'] != 200) {
            return $this->returnData($orderParams['code'], $orderParams['msg']);
        }
        //需要取消的订单列表
        $cancelOrderList = $orderParams['data']['orderInfo'];
        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail      = false;
        $isUpdateTeamOrder = false;
        $cancelFailArr     = [];
        foreach ($cancelOrderList as $subIndex => $subOrderInfo) {
            //调用统一的退票接口
            $subOrdernum      = $subOrderInfo['ordernum'];
            $cancelNum        = -1;
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit' => $isNeedAudit,
            ];
            $reqSerialNumber  = isset($ordernumArr[$subOrdernum]) ? $ordernumArr[$subOrdernum] : '';
            $refundRes        = Helpers::platformRefund($subOrdernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            self::cancelLog(func_get_args(), $refundRes);
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;
                if ($refundRes['code'] == 200) {
                    //判断更新团单主订单信息
                    $isUpdateTeamOrder = true;
                }
                //记录第一个票的退票流水信息
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }
        }
        if ($isUpdateTeamOrder) {
            $res = $this->updateTeamOrderInfo($teamOrder);
            if ($res['code'] != 200) {
                return $this->returnData($res['code'], $res['msg']);
            }
        }
        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(0, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    /**
     * 团队订单部分退票接口
     * <AUTHOR>
     * @date   2019-09-01
     *
     * @param  string  $teamOrder  退票团队主订单号
     * @param  array  $cancelList  订单的退票数量 {'24425694':{num:1,tourist_info:1231231,serial_number:123154642}}
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     * @param  int  $cancelChannel  退票渠道
     * @param  string  $cancelRemark  退票备注
     * @param  string  $reqSerialNumber  退票远端流水号
     * @param  boolean  $isNeedAudit  是否需要进行退票审核判断
     *
     * @return array
     */
    public function teamBaseRefund(
        $cancelList, $teamOrder, $opId, $parentId, $cancelChannel,
        $cancelRemark = '', $isNeedAudit = true, $extParams = []
    )
    {
        $arrOrder     = array_keys($cancelList);
        $opId         = intval($opId);
        $parentId     = intval($parentId);
        $cancelRemark = strval($cancelRemark);
        $isNeedAudit  = boolval($isNeedAudit);

        if (!$cancelList || !is_array($cancelList)) {
            return $this->returnData(203, '退票参数错误');
        }

        $orderParams = $this->teamOrderCheckData($arrOrder, $teamOrder, $opId, $parentId, $cancelList);
        if ($orderParams['code'] != 200) {
            return $this->returnData($orderParams['code'], $orderParams['msg']);
        }
        //需要取消的订单列表
        $allOrdernum = $orderParams['data']['orderInfo'];

        //判断需要退票的订单是否都是属于该订单
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            if (!in_array($subOrdernum, array_column($allOrdernum, 'ordernum'))) {
                return $this->returnData(204, '退票参数错误');
            }
        }

        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $subIndex      = 0;
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail      = false;
        $cancelFailArr     = [];
        $isUpdateTeamOrder = false;
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            //调用统一的退票接口
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark ?: '减少' . $subCancelNum['num'] . '张'];
            $cancelSiteArr    = [];
            if (!empty($extParams['cancelOtherVoucherPersonInfoMap'])) {
                $cancelPersonArr = [
                    'person_info_list' => $extParams['cancelOtherVoucherPersonInfoMap'][$subOrdernum],
                ];
            } else {
                $cancelPersonArr  = [
                    'person_id_list' => $subCancelNum['tourist_info'] ?? [],
                ];
            }
            $cancelSpecialArr = [
                'is_need_audit' => $isNeedAudit,
            ];
            $reqSerialNumber  = $subCancelNum['serial_number'];
            $refundRes        = Helpers::platformRefund($subOrdernum, $subCancelNum['num'], $opId, $cancelChannel,
                $cancelType, $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            self::cancelLog(func_get_args(), $refundRes);
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;
                if ($refundRes['code'] == 200) {
                    //判断更新团单主订单信息
                    $isUpdateTeamOrder = true;
                }
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }

            $subIndex += 1;
        }
        if ($isUpdateTeamOrder) {    //更新主订单
            $res = $this->updateTeamOrderInfo($teamOrder);
            if ($res['code'] != 200) {
                return $this->returnData($res['code'], $res['msg']);
            }
        }
        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(205, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    /***
     * 取消返回参数转换
     * <AUTHOR>
     * @date   2019-08-30
     *
     * @param $orderNum  string   订单号
     * @param $num  int    数量
     * @param $verifyNum  int|bool        验证票数
     *
     * @return array
     */
    public function cancelBaseFormat($ordernum, $opId, $parentId, $cancelChannel, $cancelRemark = '', $reqSerialNumber = '',
                                     $isNeedAudit = true, $isFailRollBack = false, $isSyntaxRollback = false)
    {
        $cancelRes = $this->baseCancel($ordernum, $opId, $parentId, $cancelChannel, $cancelRemark, $reqSerialNumber,
            $isNeedAudit, false, $isFailRollBack, 0, 0, ['is_syntax_rollback' => $isSyntaxRollback]);
        self::cancelLog(func_get_args(), $cancelRes, 'cancelBaseFormat');
        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $ordernum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';

            return $this->returnData($cancelRes['code'], $succMsg, $resData);
        } else {
            return $this->returnData($cancelRes['code'], $cancelRes['msg'], $cancelRes['data']);
        }
    }

    /***
     * 部分取消返回参数转换
     * <AUTHOR>
     * @date   2019-08-30
     *
     * @param $orderNum  string   订单号
     * @param $num  int    数量
     * @param $verifyNum  int|bool        验证票数
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    public function refundBaseFormat($ordernum, $cancelList, $opId, $parentId, $cancelChannel, $touristList = [], $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $isNeedAuth = false, $isAuditPass = false, $otherParams = [])
    {
        if ($isNeedAuth) {
            $cancelRes = $this->noAuthRefund($ordernum, $cancelList, $opId, $cancelChannel, $touristList, $cancelRemark,
                $reqSerialNumber, $isNeedAudit, $isAuditPass, $otherParams);
        } else {
            $cancelRes = $this->baseRefund($ordernum, $cancelList, $opId, $parentId, $cancelChannel, $touristList,
                $cancelRemark, $reqSerialNumber, $isNeedAudit, $isAuditPass, [], $otherParams);
            self::cancelLog(func_get_args(), $cancelRes, 'baseRefund');
        }
        if (in_array($cancelRes['code'], [200, 1095])) {
            $resData = [
                'ordernum'          => $ordernum,
                'req_serial_number' => $cancelRes['data']['req_serial_number'],
                'pft_serial_number' => $cancelRes['data']['pft_serial_number'],
            ];

            $succMsg = $cancelRes['code'] == 200 ? '退票成功' : '退票审核提交成功';

            return $this->returnData($cancelRes['code'], $succMsg, $resData);
        } else {
            return $this->returnData($cancelRes['code'], $cancelRes['msg'],$cancelRes['data']);
        }
    }

    public function cancelParamsCommonFormat($orderNum, $opId = 0, $cancelChannel = 0, $reqSerialNumber = '',
        $cancelNum = -1, $cancelType = 'common', $cancelRemarkArr = [],
        $cancelSiteArr = [], $cancelPersonArr = [], $cancelSpecialArr = [])
    {
        $refundRes = Helpers::platformRefund($orderNum, $cancelNum, $opId, $cancelChannel, $cancelType,
            $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
        self::cancelLog(func_get_args(), $refundRes);
        if ($refundRes['code'] != 200) {
            $refundRes['msg'] = $refundRes['msg'] ? $refundRes['msg'] : $refundRes['data']['err_msg'];
        }

        return $refundRes;
    }

    /**
     * 添加票或者取消票（兼容以前逻辑，不支持批量操作）
     *
     * <AUTHOR>
     * @date   2019-09-30
     *
     * @param  array  $numMapping  ['订单号' => 数量]  多个的时候是代表联票的，以前是这样
     * @param  int  $sid  主账号id
     * @param  int  $opId  操作员id
     * @param  int  $cancelChannel  取消渠道
     * @param  array  $touristList  身份证 新增要传[['tourist' => '姓名', 'idcard' => '身份证号']]  修改传【'tourist' => '','idcard' => 12356456123123312】
     * @param  string  $serialNumber  流水号
     *
     * @return array
     */
    public function addOrCancelTicket($orderNum, $numMapping = [], $sid, $opId, $cancelChannel = 0, $touristList = [], $cancelRemark = '', $isNeedAudit = true)
    {
        if (empty($numMapping) || !$orderNum) {
            return $this->returnData(204, '订单参数错误');
        }
        $modifyList = $this->handleCancelList($numMapping, $opId, $sid);
        $addData    = [];
        $cutData    = [];
        foreach ($modifyList as $key => $value) {
            if (!empty($value['errorMsg'])) {
                return $this->returnData(204, $value['errorMsg']);
            }
            if ($value['type'] == 'add') {
                $addData[$value['ordernum']] = $value['cancelNum'];
            } elseif ($value['type'] == 'del') {
                $cutData[$value['ordernum']] = $value['cancelNum'];
            }
        }
        if (empty($addData) && empty($cutData)) {
            return $this->returnData(204, "修改信息有误");
        }
        if (!empty($addData)) {
            $result = $this->orderAddNum($orderNum, $addData, $sid, $opId, $touristList);
        }
        if (!empty($cutData)) {
            if (!empty($touristList)) {
                $touristList = array_column($touristList, 'idcard');
            }
            $reqSerialNumber = $this->getAuditInfoByOrder($orderNum);
            $result          = $this->baseRefund($orderNum, $cutData, $opId, $sid, $cancelChannel, $touristList,
                $cancelRemark, $reqSerialNumber, $isNeedAudit);
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 取消订单
     *
     * <AUTHOR>
     * @date   2017-05-04
     *
     * @param  string  $ordernum  订单号
     * @param  int  $sid  主账号id
     * @param  int  $opId  操作员id
     * @param  int  $memType  用户类型
     * @param  bool  $auditPass  是否退票审核
     * @param  string  $serialNumber  流水号
     *
     * @return array
     */
    public function cancel($ordernum, $sid, $opId, $memType = 100, $auditPass = false, $serialNumber = '')
    {
        @pft_log('order/cancel', json_encode(func_get_args()), 'day');

        return $this->returnData(204, '接口已迁移');
    }

    /**
     * 判断增减，计算修改票数(之前走numModify的都是要加上验证的数量的)
     * <AUTHOR>
     * @date   19-09-09
     *
     * @param  string  $teamOrder  团单号
     * @param  int  $opId  操作人
     * @param  int  $parentId  父级
     * @param  array  $tmpCancelList  取消数组
     * @param  array  $tmpTouristInfo  身份证
     * @param  int  $cancelChannel  渠道
     * @param  bool  $isNeedAudit  是否要审核
     *
     * @return array
     */
    public function batchModifyTeamOrder($teamOrder, $opId, $parentId, $tmpCancelList, $tmpTouristInfo = [], $cancelChannel = OrderConst::TEAM_CANCEL, $isNeedAudit = true): array
    {
        if (empty($tmpCancelList)) {
            return $this->returnData(204, '参数错误');
        }
        $app       = new Application();
        $orderData = $app->order_class->getTeamModel()->getMainOrderInfoByOrderNum($teamOrder);
        if (!in_array($orderData['status'], [1, 4]) && $orderData['aid'] == $parentId) {
            return $this->returnData(205, '团队订单未确认，无法修改');
        }
        //团队限制逻辑
        $TeamLimitBiz   = new \Business\TeamOrder\TeamLimit();
        $checkTeamLimit = $TeamLimitBiz->checkTeamOrderEditLimit($teamOrder, $orderData['aid'], $orderData['fid'], $tmpCancelList);
        if ($checkTeamLimit['is_error']) {
            return $this->returnData(205, $checkTeamLimit['error_info']);
        }
        $cancelInfo  = $this->handleCancelList($tmpCancelList, $opId, $parentId);
        $successData = [];
        $errorMsg    = '';
        foreach ($cancelInfo as $key => $value) {
            $authRes = $this->_isHaveRefundAuth($value['orderInfo'], $opId, $parentId);
            if (!$authRes) {
                $errorMsg .= $key . '无权修改,';
                continue;
            }
            if (!empty($value['errorMsg'])) {
                $errorMsg .= $key . $value['errorMsg'];
                continue;
            }
            $touristInfo = [];
            //加票
            if ($value['type'] == 'add') {
                $addTouristData = $tmpTouristInfo[$key] ?? '';
                if (!empty($addTouristData)) {
                    $info = explode(',', $addTouristData);
                    foreach ($info as $k => $v) {
                        if (strpos($v, '&') === false) {
                            continue;
                        } else {
                            [$name, $card, $voucherType] = explode('&', html_entity_decode($v));
                            $touristInfo[] = ['tourist' => $name, 'idcard' => $card, 'voucher_type' => (int)$voucherType];
                        }
                    }
                }
                $addRes = $this->orderAddNum($key, [$key => $value['cancelNum']], $parentId, $opId, $touristInfo, true);
                if ($addRes['code'] != 200) {
                    $errorMsg .= $key . $addRes['msg'] . ',';
                } else {
                    $successData[] = $key;
                }
            } elseif ($value['type'] == 'del') {
                //减票 https://12301-cc.feishu.cn/wiki/NhPYwj0uaiWUSgkPYACcE8WgnOe?from=from_copylink
                if (!empty($tmpTouristInfo[$key])) {
                    $touristInf = explode(',', $tmpTouristInfo[$key]);
                    foreach ($touristInf as $v) {
                        if (strpos($v, '&') === false) {
                            continue;
                        } else {
                            [$card, $voucherType] = explode('&', html_entity_decode($v));
                            $touristInfo[] = ['idcard' => $card, 'idcard_type' => (int)$voucherType];
                        }
                    }
                }
                $cancelPersonArr  = [
                    //'person_id_list' => isset($tmpTouristInfo[$key]) ? explode(',', $tmpTouristInfo[$key]) : [],
                    'person_info_list' => $touristInfo
                ];
                $cancelSpecialArr = [
                    'is_need_audit' => $isNeedAudit,
                ];
                $reqSerialNumber  = $this->getAuditInfoByOrder($key);
                $refundRes        = $this->cancelParamsCommonFormat(
                    $key, $opId, $cancelChannel, $reqSerialNumber, $value['cancelNum'],
                    'common', [], [], $cancelPersonArr, $cancelSpecialArr
                );
                if ($refundRes['code'] != 200) {
                    $refundMsg = $refundRes['data']['err_msg'] ?? $refundRes['msg'];
                    $errorMsg  .= $key . $refundMsg . ',';
                } else {
                    $successData[] = $key;
                }
            }
        }
        if (!empty($successData)) {
            $res = $this->updateTeamOrderInfo($teamOrder);
            if ($res['code'] != 200) {
                return $this->returnData($res['code'], $res['msg'] . $errorMsg);
            }
        }
        if (!empty($errorMsg)) {
            return $this->returnData(204, $errorMsg, $successData);
        }

        return $this->returnData(200, $errorMsg);
    }

    /**
     * 取消数量计算
     * <AUTHOR>
     * @date   19-10-13
     *
     * @param  string  $orderNum  订单号
     * @param  int  $num  订单的剩余票数
     *
     * @return array
     * @throws Exception
     */
    public function handleOnlyCancel($orderNum, $num)
    {
        $num       = intval($num);
        $field     = 'ss.pid,ss.member,ss.tid,ss.tnum,ss.tprice,ss.ordernum,ss.status,ss.aid,ss.pay_status,ss.remotenum,ss.playtime,ss.paymode,ss.lid,ss.ordermode';
        $orderInfo = $this->_orderToolsModel->getOrderInfo($orderNum, $field, "de.series", false,
            'apply.verified_num,apply.refund_num');
        if ($num == 0) {
            $cancelNum = -1;
        } else {
            $cancelNum = $orderInfo['tnum'] - $num; //本次修改的票数
        }
        $type   = 'del';
        $result = [
            'orderInfo' => $orderInfo,
            'type'      => $type,
            'cancelNum' => $cancelNum,
        ];

        return $result;
    }

    /**
     * 判断增减，计算修改票数多个订单版 (之前走numModify的都是要加上验证的数量的)
     * <AUTHOR>
     * @date   19-09-09
     *
     * @param  array  $cancelList  [241124512:2] 订单号：修改后剩余数量
     * @param  int  $opType  3减票 2加票
     *
     * @return array
     */
    public function handleCancelList($cancelList = [], $opId, $sid, $opType = 0)
    {
        $result = [];
        if (!$cancelList) {
            return $result;
        }
        $arrOrder  = array_keys($cancelList);
        $field     = 'ss.pid,ss.member,ss.tid,ss.tnum,ss.tprice,ss.ordernum,ss.status,ss.aid,ss.pay_status,ss.remotenum,ss.playtime,ss.paymode,ss.lid,ss.ordermode';
        $orderInfo = $this->_orderToolsModel->getOrderInfo($arrOrder, $field, "de.series,de.aids", false,
            'apply.verified_num,apply.refund_num');
        foreach ($orderInfo as $key => $value) {
            $cancelNum = 0;
            $type      = '';
            $num       = $cancelList[$value['ordernum']];
            $change    = $num - $value['tnum'];
            if ($change > 0) {
                $type      = 'add';
                $cancelNum = $num;
                $tmpOpType = $opType ?: 2;
            } else if ($change < 0) {
                $type      = 'del';
                $cancelNum = $value['tnum'] - $num; //本次修改的票数
                if ($cancelNum + $value['verified_num'] > $value['tnum']) {
                    $cancelNum = 0;
                }
                if ($num == 0) {
                    $cancelNum = -1;
                }
                $tmpOpType = $opType ?: 3;
            }

            $errorMsg = '';
            $checkRes = $this->_numModifyCheck($value, $cancelList, $opId, $sid, $tmpOpType);   //团单的业务比较坑，必须先判断掉团单存不存在验证的，不然前端传的团单的都是操作后的票数
            pft_log('order/modify_rule',
                '修改订单规则验证：' . $value['ordernum'] . '操作人' . $opId . '|' . $sid . json_encode($checkRes) . json_encode($value));

            if ($checkRes['status'] != 1) {
                $errorMsg = $checkRes['msg'];
            }

            $result[$value['ordernum']] = [
                'orderInfo' => $value,
                'type'      => $type,
                'cancelNum' => $cancelNum,
                'ordernum'  => $value['ordernum'],
                'tid'       => $value['tid'],
                'errorMsg'  => $errorMsg,
            ];
        }

        return $result;
    }

    /**
     * 处理门票数改变游客身份信息
     *
     * @param  array  $orderInfo  订单信息
     * @param  array  $touristInfo  要改变的身份证信息
     * @param  int  $changeNum  要改变的门票数  +为加  -为减
     *
     * @return bool
     * @throws Exception
     */
    private function _checkIdCard($orderInfo, $touristInfo, $idcardType)
    {
        if (!in_array($idcardType, ['add', 'reduce']) || empty($touristInfo)) {
            return false;
        }
        $orderNum          = $orderInfo['ordernum'];
        $orderTouristArray = $this->getOrderTouristInfoByOrderNum($orderNum); //获取该订单的游客信息
        if (!$orderTouristArray) {
            $orderTouristArray = [];
        }
        $touristNum = count($orderTouristArray);

        $idCardArray = array_column($orderTouristArray, 'idcard');
        $idArray     = array_column($orderTouristArray, 'id');

        $ticketBiz  = new \Business\Product\Ticket();
        $ticketInfo = $ticketBiz->getListForOrderNew($orderInfo['tid']);
        //$landFInfo   = $this->_ticketModel->getTicketExtInfoByTid($orderInfo['tid']); //获取门票是否是一票一证
        $touristType = $ticketInfo['ticket_ext_info']['tourist_info'];
        $checkMode   = $ticketInfo['ticket_info']['check_mode'];
        if ($touristType == ProductConst::TOURIST_NEED_EVERYONE) {

            if (empty($orderInfo) || empty($touristInfo)) {
                throw new Exception("身份证信息传输错误");
            }

            if ($idcardType == 'add') {
                //加票
                $insertIdName = [];
                $insertIdCard = [];
                foreach ($touristInfo as $value) {
                    if (in_array($value['idcard'], $idCardArray)) {
                        throw new Exception("身份证信息重复");
                    }
                    if (\Library\Tools::idcard_checksum18($value['idcard']) == false) {
                        throw new Exception("身份证号码格式不正确");
                    }
                    $touristName = trim($value['tourist']);
                    if (empty($touristName) || preg_match("/[^\x80-\xff]/i", $touristName)) {
                        throw new Exception("姓名不能为空或格式不对");
                    }
                    $insertIdName[] = trim($value['tourist']);
                    $insertIdCard[] = trim($value['idcard']);
                    $idCardArray[]  = $value['idcard'];
                }

                $offlinePrintBiz = new \Business\Order\OfflinePrint();
                foreach ($insertIdName as $key => $value) {
                    $index        = $touristNum + 1;
                    $code         = $offlinePrintBiz->createTicketCode($orderNum, 4, $checkMode, $index);
                    $insertData[] = [
                        'orderid'      => $orderNum,
                        'tourist'      => $value,
                        'idcard'       => $insertIdCard[$key],
                        'voucher_type' => 1,
                        'chk_code'     => $code,
                        'idx'          => $index,
                    ];
                }

                //添加游客信息
                if ($insertData) {
                    $api = new \Business\JavaApi\Order\OrderTouristUpdate();
                    $res = $api->addTourist($insertData);
                    if ($res['code'] != 200) {
                        throw new Exception("添加身份信息失败");
                    }
                }
            } else {
                //减票
                $insertIdArray = [];
                $touristInfo   = explode(',', $touristInfo);
                foreach ($touristInfo as $touristId) {
                    if (!in_array($touristId, $idArray)) {
                        throw new Exception("要删除的身份证信息不存在");
                    }
                    $insertIdArray[] = (int)$touristId;
                }
                $touristInfoArray  = $this->_orderToolsModel->getOrderTouristInfo($orderNum);
                $reduceIdCardArray = [];
                foreach ($touristInfoArray as $value) {
                    if (in_array($value['id'], $insertIdArray)) {
                        $reduceIdCardArray[] = $value['idcard'];
                    }
                }

                //记录要删除的身份证号码
                $this->_reduceIdCards = $reduceIdCardArray;
            }
        }
    }

    /**
     * 订单数量修改前检测
     *
     * <AUTHOR>
     * @date   2017-04-05
     *
     * @param  array  $main  主票信息
     * @param  array  $numMapping  ['ordernum1' => 1, 'ordernum2' => 2]
     * @param  int  $opId  用户id
     * @param  int  $sid  员工上级id
     *
     * @return array
     */
    public function _numModifyCheck($main, $numMapping, $opId, $sid, $type = 0)
    {
        if ($main['pay_status'] != 1 && $main['ordermode'] != 24 && $main['ordermode'] != 44) {
            return ['status' => 0, 'msg' => '订单状态错误'];
        }

        // 团单修改逻辑 -- 增加的情况必须在这判断
        if ($main['ordermode'] == 24 || $main['ordermode'] == 44) {
            $checkRes = $this->checkTeamOrdeModifyRule($main, $sid, '', $type, $numMapping);
            if ($checkRes['status'] != 1) {
                return $checkRes;
            }
        }

        foreach ($numMapping as $eachOrdernum => $tnum) {
            if ($eachOrdernum < 1 || $tnum < 0) {
                return ['status' => 0, 'msg' => '参数错误'];
            }
        }

        return ['status' => 1];
    }

    /**
     * 实例团队订单模型
     *
     * @param  _instanceTeamOrderModel
     *
     * @return $this->_instanceTeamOrderModel
     *
     */
    private function _instanceTeamOrderModel()
    {
        if (!$this->_teamOrderModel) {
            $this->_teamOrderModel = new \Model\Order\TeamOrderSearch();
        }

        return $this->_teamOrderModel;
    }

    /**
     * 设置订单票数修改规则
     *
     * <AUTHOR>
     * @date   2017-04-06
     *
     * @param  int  $tid
     */
    private function _setNumModType($tid)
    {
        $tInfo = $this->_ticketModel->getTicketInfoById($tid, 'num_modify');

        //0 : 只能减少, > 0 减少以及增加的上限比例
        $this->_numModType = (int)$tInfo['num_modify'];
    }

    /**
     * 在原有的基础上增加票数
     *
     * <AUTHOR>
     * @date   2017-04-05
     *
     * @param  string  $ordernum  订单号
     * @param  int  $nowNum  修改后的票数
     * @param  int  $preNum  原先的票数
     * @param  array  $orderInfo  订单信息
     * @param  int  $originTnum  初始订单票数
     * @param  int  $opId  会员id
     * @param  string  $teamOrder  团队订单
     * @param  bool  $ifChild  是否子票
     *
     * @return array
     * @throws Exception
     */
    private function _numModifyForInc($ordernum, $nowNum, $preNum, $orderInfo, $originTnum, $opId, $teamOrder, $ifChild = false)
    {
        //获取验证订单数
        $checkNum = $this->_orderToolsModel->getVerifiedNum($ordernum);

        $change       = abs($nowNum - $originTnum + $checkNum);
        $maxMOdifyNum = ceil($originTnum * $this->_numModType / 100);

        //分销第三方的订单不允许修改增加
        $ticketBiz = new \Business\Product\Ticket();
        $tInfo     = $ticketBiz->getListForOrderNew($orderInfo['tid']);
        $tExtInfo  = $tInfo['ticket_ext_info'];

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($orderInfo['tid']);
        if (empty($thirdBindInfoArr)) {
            throw new \Exception("获取门票绑定数据失败");
        }
        $Mdetails = $thirdBindInfoArr['Mdetails'];
        $Mpath    = $thirdBindInfoArr['Mpath'];
        $sourceT  = $thirdBindInfoArr['sourceT'];

        if (!$ifChild) {
            if ($this->_numModType == 0) {
                throw new Exception("票类属性设置不允许【增加票数】");
            } elseif ($change > $maxMOdifyNum) {
                throw new Exception("浮动比例超过上限【%" . $this->_numModType . "】");
            }

            //非团单自供自销不允许修改增加
            if ($orderInfo['aid'] == $opId && !$teamOrder) {
                throw new Exception("自供自销不允许修改增加");
            }
            //对接第三方的订单不允许修改增加
            if ($orderInfo['remotenum']) {
                throw new Exception("对接第三方的订单不允许修改增加");
            }

            if ($Mdetails && $Mpath && $sourceT) {
                throw new \Exception("分销第三方的订单不允许修改增加");
            }

            //todo : 在线支付不支持
            if ($orderInfo['pay_status'] == 1 && !in_array($orderInfo['paymode'], [0, 2])) {
                throw new Exception("在线支付不支持增加票数");
            }
        }

        //仅支持景区、线路、餐饮修改增加票数
        $this->_landModel = new \Model\Product\Land();
        $landInfo         = $this->_landModel->getLandInfoByLandId($orderInfo['lid']);

        $allowArr = ['A', 'B', 'G'];

        if ($teamOrder) {
            $allowArr[] = 'F';
        }

        if (!in_array($landInfo['p_type'], $allowArr)) {
            throw new Exception("仅支持景区、线路、餐饮修改增加票数");
        }

        //增加的票数
        $addNum = $nowNum - $preNum;

        //先占库存再增加门票
        $serialNum = serialHashKey($ordernum);
        $res       = StorageApi::deductionStorage($ordernum, $serialNum, $orderInfo['tid'], $addNum,
            substr($orderInfo['playtime'], 0, 10));
        if ($res == false) {
            pft_log('storage/fail_deduction', 'orderid:' . $ordernum . ',tnum:' . $addNum . 'serialNum:' . $serialNum);
            throw new \Exception("扣减库存失败");
        }

        if ($orderInfo['aid'] != $orderInfo['member']) {
            //不是自供自销,获取订单的分销链信息
            //$chain = $this->_buyChainModel->getListByOrderId($ordernum, 'buyerid,sellerid,pmode,sale_money');

            //订单查询迁移三期
            $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
            $chain                  = $orderAidsSplitQueryLib->getSplitListByOrderIdSubNew([$ordernum]);

            if (!$teamOrder) {
                //各级资金检测
                if (!$this->_fundsCheck($chain, $addNum)) {
                    StorageApi::rollbackStorage($ordernum, $serialNum);
                    throw new \Exception("您或您的上级余额不足");
                } else {
                    //扣款
                    if (!$this->_cutPayment($chain, $addNum, $nowNum, $ordernum)) {
                        StorageApi::rollbackStorage($ordernum, $serialNum);
                        throw new \Exception("余额扣除失败");
                    }
                }
            } else {
                //团单只有一级
                if (in_array($chain[0]['pmode'], [0, 2])) {
                    //各级资金检测
                    if (!$this->_fundsCheck($chain, $addNum)) {
                        StorageApi::rollbackStorage($ordernum, $serialNum);
                        throw new \Exception("您的余额不足");
                    } else {
                        //扣款
                        if (!$this->_cutPayment($chain, $addNum, $nowNum, $ordernum)) {
                            StorageApi::rollbackStorage($ordernum, $serialNum);
                            throw new \Exception("余额扣除失败");
                        }
                    }
                }
            }
        }

        //减少调用次数，直接去扣减，失败就返回
        // $ticketStorage = TicketApi::getStorageByPlayDate($orderInfo['tid'], substr($orderInfo['playtime'], 0, 10));
        // //获取库存失败或者库存不足
        // if (is_array($ticketStorage) || ($ticketStorage != -1 && $ticketStorage < $tnum)) {
        //     throw new \Exception("库存不足");
        // }

        //更改订单信息
        if (!$this->_updateOrderInfo($orderInfo, $addNum)) {
            $res = StorageApi::rollbackStorage($ordernum, $serialNum);
            if ($res == false) {
                pft_log('storage/rollbackStorage', 'orderid:' . $ordernum . 'serialNum:' . $serialNum);
            }
            throw new Exception("票数增加失败");
        }

        //更新订单分销链详情
        $this->_updateOrderFxDetails($ordernum, $nowNum, $preNum);
        // 一票一码订单增加票数
        $printMode = $tInfo['print_mode'] ?: 4;
        $checkMode = $tInfo['check_mode'] ?: 1;
        if ($printMode == 4 && in_array($tExtInfo['tourist_info'], [0, 1])) {
            $data = $this->_generateOrderTouristData($orderInfo['lid'], $ordernum, $preNum, $addNum, [], $printMode,
                $checkMode);

            $api = new \Business\JavaApi\Order\OrderTouristUpdate();
            $api->addTourist($data, $opId);
        }

        //写入订单追踪记录
        $result = $this->_orderTrack($orderInfo, $addNum);
        if (!$result) {
            $res = StorageApi::rollbackStorage($ordernum, $serialNum);
            if ($res == false) {
                pft_log('storage/rollbackStorage', 'orderid:' . $ordernum . 'serialNum:' . $serialNum);
            }
            throw new Exception("订单追踪表添加失败");
        }

        return $result;

    }

    /**
     * 验证团队订单修改取消规则
     *
     * @param  array  $main  订单信息
     * @param  int  $sid  总帐号id
     * @param  array  $orderAddonArr  订单addon信息
     *
     * @return array
     */
    public function checkTeamOrdeModifyRule($main, $sid, $orderAddonArr = '', $type = 0, $numMapping = [])
    {
        if ($sid == 1 || PHP_SAPI == 'cli') {
            return ['status' => 1];
        }
        $checkTimeOrderArr   = [];
        $checkTimeOrderArr[] = $main;

        $searchTeamOrderNum = $main['ordernum'];

        $orderAddonInfo = [];
        $queryParams    = [[$main['ordernum']]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderAddon',
            'batchQueryOrderAddonByorderid', $queryParams);
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $orderAddonInfo = $queryRes['data'][0];
        }

        //$subOrderAddon  = new SubOrderAddon();
        //$orderAddonInfo = $subOrderAddon->getPackInfoByOrderSingle($main['ordernum'], 'ifpack,pack_order');
        // 判断订单是否是套票
        //$orderAddonInfo = $this->_ticketModel->getOrderAddonByOrderId($main['ordernum']);
        if ((int)$orderAddonInfo['ifpack'] != 0 && $orderAddonInfo['pack_order'] == 1) {
            //$packageOrders     = $this->_orderToolsModel->getPackOrdersInfo($main['ordernum'],
            //    'ss.ordernum,ss.status,ss.pay_status,ss.tid');
            $packOrder = new PackOrder();
            $packageOrderArr = $packOrder->batchGetOrderWithAddon([$main['ordernum']]);
            /*$queryParams     = [[$main['ordernum']]];
            $packageOrderArr = \Business\JavaApi\Order\Query\Container::query('orderAddon', 'batchGetOrderWithAddon',
                $queryParams);*/
            if ($packageOrderArr['code'] == 200 && !empty($packageOrderArr['data'])) {
                $packageOrders = array_column($packageOrderArr['data'], null, 'ordernum');
                foreach ($packageOrders as $ordernum => &$item) {
                    if (isset($item['addon'])) {
                        $addon = $item['addon'];
                        unset($item['addon']);
                        $item              = array_merge($item, $addon);
                        if (!isset($item['dtime'])) {
                            $item['dtime'] = '0000-00-00 00:00:00';
                        }
                        if (!isset($item['ctime'])) {
                            $item['ctime'] = '0000-00-00 00:00:00';
                        }
                    }
                }

                $checkTimeOrderArr = array_merge($checkTimeOrderArr, $packageOrders);
            } else {
                return ['status' => 1, 'msg' => '门票数据获取异常'];
            }
        }

        // 子订单获取总订单
        if ((int)$orderAddonInfo['ifpack'] == 2) {
            $searchTeamOrderNum = $orderAddonInfo['pack_order'];
        }

        $orderNumArr     = array_column($checkTimeOrderArr, 'ordernum');
        $orderStatusArr  = array_unique(array_column($checkTimeOrderArr, 'status'));
        $checkStatus     = array_diff($orderStatusArr, [0, 2]);
        $orderToolModel  = new OrderTools();
        $orderInfo       = $orderToolModel->getOrderInfo($searchTeamOrderNum);
        $teamConfigModel = new TeamConfig();
        $configInfo      = $teamConfigModel->getConfigBySid($orderInfo['apply_did'], 'config');
        $configInfo      = empty($configInfo['config']) ? [] : json_decode($configInfo['config'], true);
        // 订单必须时未使用未出票
        if (!empty($checkStatus)  && $configInfo['modify_use_order'] != 2) {
            return ['status' => 0, 'msg' => '非未使用订单不可修改'];
        }

        // 订单打印状态， 订单支付状态，订单使用状态，操作人
        if ($orderAddonArr == '') {
            //$subOrderAddon     = new SubOrderAddon();
            //$subOrderAddonInfo = $subOrderAddon->getAddonInfoByOrderArr($orderNumArr, 'orderid,ifprint,id');

            $subOrderAddonInfo = [];
            $queryParams       = [$orderNumArr];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderAddon',
                'batchQueryOrderAddonByorderid', $queryParams);
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $subOrderAddonInfo = $queryRes['data'];
            }

            $orderAddonArr     = [];
            if (!empty($subOrderAddonInfo)) {
                foreach ($subOrderAddonInfo as $subOrderAddonVal) {
                    $orderAddonArr[$subOrderAddonVal['orderid']] = $subOrderAddonVal;
                }
            }
        }

        if (empty($orderAddonArr)) {
            return ['status' => 0, 'msg' => '订单信息查询失败'];
        }

        //$ifprintArr = array_unique(array_column($orderAddonArr, 'ifprint'));
        //$checkPrint = array_diff($ifprintArr, [0]);

        if ($orderAddonArr[$main['ordernum']]['ifprint'] != 0 && ($configInfo['refund_check_print'] == 1 || !isset($configInfo['refund_check_print']))) {
            return ['status' => 4005, 'msg' => '已出票订单不可修改'];
        }

        $tidArr = array_column($checkTimeOrderArr, 'tid');
        if (empty($tidArr)) {
            return ['status' => 0, 'msg' => '订单门票信息查询有误'];
        }

        // 已经确认的订单不让分销商修改
        $teamModel        = new \Model\Order\TeamOrderSearch;
        $mainOrder        = $teamModel->getMainOrderInfoBySonOrder($searchTeamOrderNum);
        $mainOrderInfoArr = $teamModel->getMainOrderInfoByOrderNum($mainOrder['main_ordernum']);

        if (empty($mainOrderInfoArr) || !in_array($mainOrderInfoArr['status'], [0, 1, 4])) {
            return ['status' => 0, 'msg' => '该订单状态下不可修改'];
        }

        $checkTimeResArr = $this->_checkModifyTimeRule($main['tid'], $main['playtime'], $main['series'], $sid,
            $mainOrderInfoArr['status'], $main);
        pft_log('debug/checkModifyTimeRule', json_encode($checkTimeResArr, JSON_UNESCAPED_UNICODE));
        if ($checkTimeResArr['code'] != 200) {
            return ['status' => $checkTimeResArr['code'], 'msg' => $checkTimeResArr['msg']];
        }
        //团单支持了多级分销，这边得判断下，中间级不允许团单修改
        $splitInfo = $teamModel->getTeamOrderSplitInfo($sid, $searchTeamOrderNum, 'buyerid,order_level,apply_did');
        //当前登录商家即不是购买者，也不是最顶级
        if (empty($splitInfo) || (!in_array($splitInfo['order_level'], [0, -1]) && $splitInfo['apply_did'] != $sid)) {
            return ['status' => 0, 'msg' => '中间级不允许修改团单信息'];
        }
        if (in_array($splitInfo['order_level'],
                [0, -1]) && $splitInfo['buyerid'] != $sid && $splitInfo['apply_did'] != $sid) {
            return ['status' => 0, 'msg' => '中间级不允许修改团单信息'];
        }
        //团单多一个判断订单是否允许减少票数
        $teamRulesBz = new TeamRules();
        $opType = 2;
        if ($type == 2) {
            $opType = 1;
        } elseif ($type == 3) {
            $opType = 2;
        }
        $checkRes    = $teamRulesBz->teamOrderChangeJudge($splitInfo['apply_did'], $main['tid'], $main['playtime'], $opType, $mainOrder['main_ordernum'], $numMapping);

        if ($checkRes['code'] != 200) {
            return ['status' => 0, 'msg' => $checkRes['msg']];
        }
        //allow  数量是否允许修改 1：允许  2：不允许
        if ($checkRes['data']['allow'] != 1) {
            return ['status' => 0, 'msg' => $checkRes['msg']];
        }

        return ['status' => 1];
    }

    /**
     * 验证是否在可修改时间内
     *
     * @param  int  $tid  门票id
     * @param  string  $playTime  游玩时间
     * @param  string  $series  演出信息
     * @param  int  $opSid  操作人上级id
     *
     * @return array
     *
     */
    private function _checkModifyTimeRule($tid, $playTime, $series, $opSid, $isCheckTeamOrder = 0, $orderInfoMain)
    {
        // 根据tid 获取门票设置的，验证时间，修改时间，供应商
        $ticketModel   = new Ticket();
        //$ticketInfoArr = $ticketModel->getTicketInfoById($tid,
        //    'modify_limit_time, apply_did, refund_early_time, apply_did, refund_rule');

        $ticketInfoArr = [];
        $javaApi   = new ticketBiz();
        $ticketArrRes = $javaApi->queryTicketInfoById($tid);
        if (!empty($ticketArrRes)) {
            $ticketInfoArr = $ticketArrRes['ticket'];
            $ticketInfoArr['p_type'] = $ticketArrRes['land']['p_type'];
            $ticketInfoArr = array_merge($ticketInfoArr, $ticketArrRes['ext']);
        }

        if (empty($ticketInfoArr)) {
            return ['code' => 0, 'msg' => '参数有误'];
        }

        pft_log('order/modify_rule', '修改票规则验证：' . $tid . '|' . json_encode($ticketInfoArr));

        if ($ticketInfoArr['refund_rule'] == 2) {
            return ['code' => 1411, 'msg' => '订单不可修改或取消-不可退属性'];
        }

        if ($isCheckTeamOrder == 0 && $opSid == $ticketInfoArr['apply_did']) {
            return ['code' => 4006, 'msg' => '未确认订单供应商不可修改'];
        }

        $ticketExtInfoArr = $ticketModel->getTicketExtInfoByTid($tid, 'v_time_limit,tid');

        if (empty($ticketExtInfoArr)) {
            return ['code' => 0, 'msg' => '参数有误'];
        }

        $verifyTime      = $ticketExtInfoArr['v_time_limit'];
        $refundEarlyMinu = $ticketInfoArr['refund_early_time'];
        $modifyLimitTime = $ticketInfoArr['modify_limit_time'];
        $applyDid        = $ticketInfoArr['apply_did'];
        $refundAfterTime = $ticketInfoArr['refund_after_time'] ?? 0;

        $showBegin = '';
        if ($series) {
            $Mseries   = unserialize($series);
            $showBegin = $Mseries[4];
        }

        $refundCheckBiz = new BaseRefundCheck();
        if (in_array($ticketInfoArr['p_type'], ['A', 'B', 'C', 'F', 'G'])) {
            $ticketInfo = [
                'refund_rule' => $ticketInfoArr['refund_rule'],
                'apply_did' => $ticketInfoArr['apply_did'],
                'refund_before_early_time' => $ticketInfoArr['refund_before_early_time'] ?? 0,
                'refund_after_early_time' => $ticketInfoArr['refund_after_early_time'] ?? 0,
            ];
            $ticketExtInfo = [];
            $orderInfoArrNew = [
                'endtime' => $orderInfoMain['endtime']
            ];

            $checkResArr = $refundCheckBiz->checkRefundTimeRuleNewTeam($ticketInfo, $ticketExtInfo, $orderInfoArrNew, $opSid);
        } else {
            $checkResArr    = $refundCheckBiz->checkRefundTimeRule($refundEarlyMinu, $playTime, $showBegin,
                $modifyLimitTime, $opSid, $applyDid, $refundAfterTime);
        }


        if (in_array($checkResArr['code'], [100, 200])) {
            return ['code' => 200, 'msg' => '允许修改'];
        }

        return ['code' => 0, 'msg' => '已过修改时间'];
    }

    /**
     * 更新订单分销链详细表
     *
     * @param  string  $ordernum  订单号
     * @param  int  $nowNum  修改后的票数
     * @param  int  $preNum  原先的票数
     *
     * @return bool
     * @throws Exception
     */
    private function _updateOrderFxDetails($ordernum, $nowNum, $preNum)
    {
        if (!$ordernum || (int)$nowNum <= 0 || (int)$nowNum != $nowNum || (int)$preNum <= 0 || (int)$preNum != $preNum) {
            throw new Exception("订单数据缺失或错误");
        }

        $orderDetailInfo = $this->_orderToolsModel->getOrderDetailInfo($ordernum, 'aids_money');
        if (!$orderDetailInfo) {
            throw new Exception("订单分销链详细表数据缺失");
        }

        $aidsMoney = \Library\Tools::aidMoneyFormat($orderDetailInfo['aids_money']);

        if (!$aidsMoney) {
            return true; //没有数据不更新
        }

        foreach ($aidsMoney as $key => $value) {
            $aidsMoney[$key]['money'] = $aidsMoney[$key]['money'] * $nowNum / $preNum;
        }

        //新格式，后面切换过来后只要这边注释打开，下面注释就可以了
        $newAidsMoney = json_encode($aidsMoney);

        $api  = new \Business\JavaApi\Order\OrderDetailUpdate();
        $data = ['aidsMoney' => $newAidsMoney];
        $res  = $api->orderDetailInfoUpdate($ordernum, $data, 0);

        if ($res['code'] != 200) {
            throw new Exception("订单分销链详细表数据更新失败");
        }

        return true;
    }

    /**
     * 增加票数，检测各级之间的资金是否足够(支付方式不变)
     *
     * <AUTHOR>
     * @date   2017-04-10
     *
     * @param  array  $chains  分销链信息
     * @param  int  $num  增加的票数
     *
     * @return bool
     */
    private function _fundsCheck($chains, $num)
    {
        foreach ($chains as $item) {
            $need = $num * $item['sale_money'];

            if ($item['pmode'] == 0) {
                //余额支付
                $left = $this->_memberModel->getMoney($item['buyerid'], 0);

                if ($left < $need) {
                    return false;
                }

            } elseif ($item['pmode'] == 2) {
                //授信支付
                $moneyInfo = $this->_memberModel->getMoney($item['buyerid'], 3, $item['sellerid'], true);
                $left      = $moneyInfo['kmoney'] + $moneyInfo['basecredit'];
                if ($left < $need) {
                    return false;
                }

            } else {
                //当前仅支持余额 授信
                return false;
            }
        }

        return true;
    }

    /**
     * 实例化授信相关的业务
     * @return object
     *
     */
    private function _instanceCreditBiz()
    {
        if (!$this->_creditBiz) {
            $this->_creditBiz = new BizCredit;
        }

        return $this->_creditBiz;
    }

    /**
     * 增加票数扣款(支付方式不变)
     *
     * <AUTHOR>
     * @date   2017-04-10
     *
     * @param  array  $chains  分销链信息
     * @param  int  $num  增加的票数
     * @param  int  $totalNum  增加后的票數，作为流水号
     * @param  array  $orderInfo  订单信息
     *
     * @return bool
     */
    private function _cutPayment($chains, $num, $totalNum, $ordernum)
    {
        if (!$chains || $num <= 0 || !$ordernum) {
            return false;
        }

        $memo = '订单添加票数消费';

        //整合扣费信息
        if (count($chains) >= 2) {
            //将扣费信息进行排序
            usort($chains, function ($firstNode, $secondNode) {
                //考虑到就只有一级和末级比较特殊，所以中间级别统一用2标识
                //所处的级别：1=第一级，0=既是1级也是末级，-1=最末级，2=中间级别
                if ($firstNode['level'] == -1 || $firstNode['level'] == 0) {
                    return 1;
                } else if ($secondNode['level'] == -1 || $secondNode['level'] == 0) {
                    return -1;
                } else if ($firstNode['level'] == 1) {
                    return -1;
                } else if ($secondNode['level'] == 1) {
                    return 1;
                } else {
                    return 0;
                }
            });

            $lastInfo  = array_pop($chains);
            $resellArr = [];
            foreach ($chains as $item) {
                $resellArr[] = [
                    'memberId'   => $item['buyerid'],
                    'supplierId' => $item['sellerid'],
                    'tradeMoney' => $item['sale_money'] * $num,
                    'payType'    => $item['pmode'],
                    'remark'     => $memo,
                ];
            }
        } else {
            $lastInfo  = array_pop($chains);
            $resellArr = [];
        }

        $memberId   = $lastInfo['buyerid'];
        $operaterId = $memberId;
        $supplierId = $lastInfo['sellerid'];
        $tradeMoney = $lastInfo['sale_money'] * $num;
        $payType    = $lastInfo['pmode'];

        //流水号 - 总的票数应该时不断增加的，可以保证流水号不重复
        $serialNumber = $ordernum . '_' . $totalNum . '_' . time();

        $tradeBiz = new Trade();
        $tradeRes = $tradeBiz->balancePayAddTicket($ordernum, $serialNumber, $operaterId, $memberId, $supplierId,
            $tradeMoney, $payType, $memo, $resellArr);

        $retry = 0;
        if ($tradeRes['code'] == 408) {
            //如果是请求超时了，重新进行请求
            sleep(0.2);
            $retry    = 1;
            $tradeRes = $tradeBiz->balancePayAddTicket($ordernum, $operaterId, $memberId, $supplierId, $tradeMoney,
                $payType, $memo, $resellArr);
        }

        //请求报错
        if ($tradeRes['code'] != 200) {
            pft_log('order/trade/error', json_encode([
                'add_ticket',
                $tradeRes,
                $retry,
                $ordernum,
                $operaterId,
                $memberId,
                $supplierId,
                $tradeMoney,
                $payType,
                $memo,
                $resellArr,
            ]));

            return false;
        } else {
            return true;
        }
    }

    /**
     * 更新订单票数
     *
     * @param  array  $orderInfo  订单信息
     * @param  int  $num  要增加的票数
     *
     * @return bool
     */
    private function _updateOrderInfo($orderInfo, $num)
    {
        if (empty($orderInfo) || (int)$num <= 0 || (int)$num != $num) {
            return false;
        }

        $saveNum    = $orderInfo['tnum'] + $num;
        $totalMoney = (int)$orderInfo['tprice'] * $saveNum;
        $totalMoney = round($totalMoney, 2);
        if (!is_numeric($saveNum)) {
            return false;
        }

        $data = [
            'tnum'       => $saveNum,
            'totalmoney' => $totalMoney,
        ];

        $api = new \Business\JavaApi\Order\OrderInfoUpdate();
        $res = $api->baseOrderInfoUpdate($orderInfo['ordernum'], $data, 0);
        if ($res['code'] != 200) {
            return false;
        }

        //同时更新订单原始信息表里面的原始票数 - 原始订单票数保留，不做修改
        // $submitModel = new OrderSubmit();
        // $submitModel->increCount($orderInfo['ordernum'], 'origin', $num);

        return $res['code'] == 200;
    }

    /**
     * 订单增加票数
     *
     * @param  array  $orderInfo  订单信息
     * @param  int  $num  要增加的票数
     *
     * @return bool|mixed
     */
    private function _orderTrack($orderInfo, $num)
    {
        if (empty($orderInfo) || (int)$num <= 0 || (int)$num != $num) {
            return false;
        }

        $leftNum = $orderInfo['tnum'] + $num;
        $result  = $this->_orderTrackModel->addTrack($orderInfo['ordernum'], OrderConst::ORDER_TICKET_MORE,
            $orderInfo['tid'],
            $num, $leftNum, OrderConst::SOURCE_INSIDE_SOAP, 0, 0, 0, $this->_memberId, 0, '', "订单增加$num 张票");

        if (!$result) {
            return false;
        }

        return $result;
    }

    /**
     * 根据订单号获取未验证游客身份信息
     *
     * @param  string  $orderNum  订单号
     *
     * @return array|bool
     */
    public function getOrderTouristInfoByOrderNum($orderNum, $isAll = false)
    {
        if (!$orderNum) {
            return false;
        }
        $result = $this->_orderToolsModel->getOrderTouristInfo($orderNum);
        if (empty($result)) {
            return false;
        }

        //游客添加返回更多信息
        $touristJavaApi = new \Business\JavaApi\Order\OrderTouristInfo();
        $touristExInfo  = $touristJavaApi->getOrderTouristInfosExt([$orderNum]);
        foreach ($result as &$touristData) {
            $touristData['more_credentials'] = [];
            if (!empty($touristExInfo['data'])) {
                foreach ($touristExInfo['data'] as $key => $moreData) {
                    if ($touristData['id'] == $moreData['touristId']) {
                        if ($moreData['certType'] == 0) {
                            $moreDataName = $moreData['remark'];
                        } else {
                            $moreDataName = $this->_moreCredentialType($moreData['certType']);
                        }
                        if ($moreDataName) {
                            $touristExInfos                    = [
                                'name'          => $moreDataName,
                                'text'          => $moreData['remark'],
                                'realName'      => $touristData['tourist'],
                                'documentValue' => $moreData['certInfo'],
                            ];
                            $touristData['more_credentials'][] = $touristExInfos;
                        }
                    }
                }
            }
        }
        $return = [];

        if ($result[0]['idcard'] == $result[1]['idcard']) {
            unset($result[0]);
        }

        $return = $result;
        if ($isAll === false) {
            $return = [];
            foreach ($result as $value) {
                if ($value['check_state'] == OrderConst::ORDER_TOURIST_NO_VERIFIED) {
                    $return[] = $value;
                }
            }
        }

        return $return;
    }
    /**
     * 更多信息 对应的证件类型
     * <AUTHOR>
     * @date 2020-7-14
     *
     * @param  string $type
     *
     * @return array
     */
    private function _moreCredentialType($type)
    {
        $data = [
            0 => '其他',
            1 => '护照',
            2 => '回乡证',
            3 => '台胞证',
            4 => '军官证',
        ];

        return $data[$type] ?? '';
    }

    /**
     * 根据主票订单号获取订单各联票原始票数
     *
     * @param  string  $mainOrderNum  主票订单号
     *
     * @return array|bool
     */
    public function getOriginTnumArrayByMainOrderNum($mainOrderNum)
    {
        if ((int)$mainOrderNum <= 0 || (int)$mainOrderNum != $mainOrderNum) {
            return false;
        }
        $where = [
            'action' => OrderConst::ORDER_ADD,
        ];

        $result = $this->_orderToolsModel->getLinkSubOrder($mainOrderNum);
        if ($result) {
            $orderIdArray      = $result;
            $orderIds          = implode(',', $orderIdArray);
            $where['ordernum'] = ['in', $orderIds];
        } else {
            $where['ordernum'] = $mainOrderNum;
        }
        //$list = $this->_orderTrackModel->getList($where, 'ordernum,tnum');

        //订单查询迁移二期
        $orderTrackQueryLib = new OrderTrackQuery();
        $list               = $orderTrackQueryLib->getOrderRecordNew(1, 20, $where);

        if (!$list) {
            return false;
        }
        $return = [];
        foreach ($list as $value) {
            $return[$value['ordernum']] = $value['tnum'];
        }

        return $return;

    }

    /**
     * 重发短信
     *
     * <AUTHOR>
     * @date   2017-05-20
     *
     * @param  string  $ordernum  订单号
     * @param  int  $sid  供应商id
     * @param  string  $mobile  手机号
     *
     * @return array
     */
    public function resendMsg($ordernum, $sid, $mobile = 0)
    {
        //是否有重发权限
        $permission = $this->_orderToolsModel->hasPermissionToResend($ordernum, $sid);

        if ($permission['status'] == 0) {

            return $this->returnData(204, $permission['msg']);

        } else {
            $order  = $permission['data']['order'];
            $ticket = $permission['data']['ticket'];

            //对接第三方
            if ($ticket['sourceT'] == 2) {
                if ($mobile) {
                    return $this->returnData(204, '对接第三方不支持修改手机号重发短信');
                }

                $otaGatewayFile = HTML_DIR . '/ota/common/OTAGateway.class.php';
                include $otaGatewayFile;

                $params            = array();
                $params['Action']  = 'resend';
                $params['Ordern']  = $ordernum;
                $params['LandId']  = $ticket['landid']; //景点ID
                $params['ApplyId'] = $ticket['apply_did']; //原始供应商

                $otaSmsRes = \OTAGateway::dispatch($params);
                if (!$otaSmsRes) {
                    //第三方系统错误，重发失败
                    return $this->returnData(204, '第三方系统错误');
                }

                $res  = explode('|', $otaSmsRes);
                $code = $res[0];
                if ($code != 200) {
                    //第三方系统错误，重发失败
                    return $this->returnData(204, '第三方系统错误');
                }

                //第三方系统已重发短信成功
                return $this->returnData(200, '重发成功');

            } else {
                //票付通发短信
                $smsLib = new \Library\MessageNotify\OrderNotify($ordernum, 0, 0, 0, 0, 0, 0, '', 0, 1);
                $smsRet = $smsLib->Send(0, false, true, $mobile);

                if ($smsRet) {
                    return $this->returnData(200, '重发成功');
                } else {
                    return $this->returnData(204, '重发失败');
                }
            }
        }
    }

    /**
     * 更新游客信息
     *
     * <AUTHOR>
     * @date   2017-05-27
     *
     * @param  array  $params  ['res' => 退票接口返回值, 'args' => '调用退票接口传的参数']
     */
    public function updateTouristInfo($params)
    {
        pft_log('debug', json_encode($params), 'day');

        $result = $params['res'];
        $args   = $params['args'];
        //订单号
        $ordernum = $args[0];
        //剩余票数
        $num = $args[1];
        //身份证号
        $idcardList = isset($args[15]) ? $args[15] : '';

        // 获取主票id
        $detail = $this->_OrderHandlerModel->getOrderDetailInfo($ordernum, 'concat_id');

        if ($detail && $detail['concat_id']) {
            $mainOrderId = $detail['concat_id'];
        } else {
            //是否是套票
            $addon = $this->_OrderHandlerModel->getPackSubOrder($ordernum);
            if ($addon) {
                $mainOrderId   = array_column($addon, 'orderid');
                $mainOrderId[] = $ordernum;
            } else {
                $mainOrderId = $ordernum;
            }
        }

        if (is_numeric($result) && $result == 1095) {
            //退票审核
            if ($idcardList) {
                $setStatus = 3;
            } else {
                //取消订单
                if ($num == 0) {
                    $setStatus  = 3;
                    $idcardList = '';
                }
            }
        } else {
            if (is_array($result) && $result['code'] == 200) {
                //取消成功
                if ($idcardList) {
                    $setStatus = 2;
                } else {
                    if ($num == 0) {
                        $setStatus  = 2;
                        $idcardList = '';
                    }
                }
            }
        }

        if (isset($setStatus)) {
            $this->_OrderHandlerModel->updateOrderTouristInfoState($mainOrderId, $idcardList, $setStatus);
        }
    }

    /**
     * 修改订单价格——特价票的应用场景，如淘宝下单后未支付可以向卖家提出修改运费的要求
     * <AUTHOR> Chen
     * @date 2017-10-02
     *
     * @param  string  $orderNum  订单号
     * @param  int  $totalMoney  修改后的总金额，单位为元
     * @param  int  $bossId  上级DI
     * @param  int  $opId  员工ID
     *
     * @return array  ['code'=>200,'msg'=>'成功']
     */
    public function updateOrderTotalMoney($orderNum, $totalMoney, $bossId, $opId, $moreData = [])
    {
        $totalMoney *= 100;

        $field   = 'ss.aid,ss.pid,ss.playtime,ss.tprice,ss.tid,ss.tnum,ss.totalmoney,ss.pay_status,ss.status,ss.member';
        $oldData = $this->_OrderHandlerModel->getOrderInfo($orderNum, $field);
        if (!$oldData) {
            return ['code' => 0, 'msg' => '数据不存在'];
        } elseif ($totalMoney == $oldData['totalmoney']) {
            return ['code' => 0, 'msg' => '价格未做修改'];
        } elseif ($oldData['aid'] != $bossId) {
            return ['code' => 0, 'msg' => '非您供应的产品无权限修改价格'];
        } elseif ($oldData['pay_status'] == 1) {
            return ['code' => 0, 'msg' => '订单已经支付完成无法修改价格'];
        } elseif ($oldData['status'] == 1) {
            return ['code' => 0, 'msg' => '订单已经验证无法修改价格'];
        } elseif ($oldData['tnum'] == 0) {
            return ['code' => 0, 'msg' => '票数为0，无法修改'];
        }

        // 未支付 & 购物车订单，不允许改价
        if (($oldData['pay_status'] == 2)
            && (OrderHelper::checkIsShoppingCarOrder(['fxDetails' => ['extContent' => $oldData['ext_content']]]))
        ) {
            return ['code' => 0, 'msg' => '对不起，购物车未支付订单，暂不支持当前操作。'];
        }

        //判断订单是否有使用新积分、新优惠券 有用营销优惠的不让改价
        $extContent                          = isset($oldData['ext_content']) && $oldData['ext_content'] ? json_decode($oldData['ext_content'], true) : [];
        $usePoint                            = isset($extContent['usePoint']) && $extContent['usePoint'] ? true : false;
        $useCoupon                           = isset($extContent['useCoupon']) && $extContent['useCoupon'] ? true : false;
        $useDiscount                         = isset($extContent['useDiscount']) && $extContent['useDiscount'] ? true : false;
        $useSettlementDiscountBargain        = isset($extContent['bargainPricePolicyId']) && $extContent['bargainPricePolicyId'] ? true : false;
        $useSettlementDiscountSpecifyReduced = isset($extContent['specifyReducedPolicyId']) && $extContent['specifyReducedPolicyId'] ? true : false;

        if ($usePoint && $useCoupon && $useDiscount) {
            return ['code' => 0, 'msg' => '当前订单使用了积分、优惠券和会员折扣，无法修改价格'];
        } elseif ($usePoint) {
            return ['code' => 0, 'msg' => '当前订单使用了积分，无法修改价格'];
        } elseif ($useCoupon)  {
            return ['code' => 0, 'msg' => '当前订单使用了优惠券，无法修改价格'];
        } elseif ($useDiscount)  {
            return ['code' => 0, 'msg' => '当前订单使用了会员折扣，无法修改价格'];
        }

        if ($useSettlementDiscountBargain || $useSettlementDiscountSpecifyReduced) {
            return ['code' => 0, 'msg' => '当前订单使用了分销优惠，无法修改价格'];
        }

        //大于成本价 小于 门市价
        $priceModel  = new PriceRead();
        $priceLowest = $priceModel->get_Dynamic_Price_Merge($oldData['pid'], $oldData['playtime'], 1);

        if ($totalMoney % $oldData['tnum'] != 0) {
            return ['code' => 0, 'msg' => '修改后的金额必须能被票数整除'];
        }

        $newPrice = $totalMoney / $oldData['tnum'];

        if ($newPrice < $priceLowest) {
            return ['code' => 0, 'msg' => '修改后的价格不能小于成本价'];
        }

        $memo = '修改前实付单价:' . money_fmt($oldData['tprice']) . ',修改后实付单价:' . money_fmt($newPrice) . ';修改前总金额:' . money_fmt($oldData['totalmoney']) . ',修改后总金额:' . money_fmt($totalMoney);

        $saveData = ['tprice' => $newPrice, 'totalmoney' => $totalMoney];
        $result   = $this->updateOrderBasicInfo($orderNum, 21, $saveData, $opId, $memo, $moreData);
        //团单支持多级分销后，这边订单金额进行修改后要同步到团单那边
        if ($result['code'] == 200) {
            $this->updateTeamOrderTotalMoney($orderNum, $oldData, $newPrice);
        }

        return $result;
    }

    /**
     * 同步更新团单订单追踪表金额信息
     *
     * @param  string  $orderNum  订单号
     * @param  array  $oldData  订单数据
     * @param  int  $newPrice  新的价格
     *
     * @return array
     * <AUTHOR>
     * @date 2021/5/28
     *
     *
     */
    public function updateTeamOrderTotalMoney($orderNum, $oldData, $newPrice)
    {
        if (empty($oldData) || empty($newPrice) || empty($orderNum)) {
            return $this->returnData(203, "参数错误");
        }
        //判断订单是否是团单
        $teamOrderSearchModel = new TeamOrderSearch();
        $orderInfo            = $teamOrderSearchModel->getTeamOrderLinkInfo($orderNum, 'id');
        if (empty($orderInfo)) {
            return $this->returnData(204, "非团单无需同步");
        }
        $result = $teamOrderSearchModel->updateTeamOrderSplitInfoByOrderNumAndBuyerId($orderNum, $oldData['member'],
            $newPrice);
        if ($result === false) {
            pft_log("teamOrder/split", "订单{$orderNum}金额同步失败，金额：{$newPrice}");

            return $this->returnData(205, "金额同步数百");
        }

        return $this->returnData(200, "金额同步成功");
    }

    /**
     * 修改订单游玩日期，类似改签
     * <AUTHOR> Chen
     * @date 2017-10-02
     *
     * @param  string  $orderNum  订单号
     * @param  string  $newPlayDate  改签日期
     * @param  int  $opId  操作员ID
     *
     * @return array  ['code'=>200,'msg'=>'成功']
     */
    public function updateOrderPlayDate($orderNum, $newPlayDate, $opId)
    {
        $oldData = $this->_OrderHandlerModel->getOrderInfo($orderNum,
            'ss.tid,ss.lid,ss.tnum,ss.playtime,ss.begintime,ss.endtime,ss.status');
        if (!$oldData) {
            return ['code' => 0, 'msg' => '订单不存在'];
        } elseif ($oldData['status'] != 0) {
            return ['code' => 0, 'msg' => '订单已经状态发生变化，无法修改'];
        } elseif ($oldData['begintime'] == $newPlayDate) {
            return ['code' => 0, 'msg' => '数据没有发送改变'];
        }
        $tInfo      = $this->_ticketModel->getTicketInfoById($oldData['tid'], 'delaytype,pid');
        $_landModel = new Land('slave');
        $lInfo      = $_landModel->getLandInfo($oldData['lid'], false, 'p_type');
        if ($lInfo['p_type'] == 'H' || $lInfo['p_type'] == 'I') {
            return ['code' => 0, 'msg' => '演出、年卡累订单无法修改游玩日期'];
        }
        //TODO::获取改签日期的价格，如果价格不等，无法修改

        // 判断是否游玩日期当天有效
        if ($tInfo['delaytype'] == 2) {
            // 期票模式
            if ($newPlayDate < $oldData['begintime'] || $newPlayDate > $oldData['endtime']) {
                return ['code' => 0, 'msg' => '游玩日期不在订单有效期内无法修改'];
            }
            $saveData = ['playtime' => $newPlayDate, 'begintime' => $newPlayDate];
        } else {
            $saveData = ['playtime' => $newPlayDate, 'begintime' => $newPlayDate, 'endtime' => $newPlayDate];
        }

        $result = $this->updateOrderBasicInfo($orderNum, 21, $saveData, $opId, '');

        return $result;
    }

    /**
     * 下单后补录身份证信息
     * <AUTHOR> Chen
     * @date 2017-10-02
     *
     * @param  string  $orderNum  订单号
     * @param  array  $idCardInfo  身份证信息 [['name'=>'xxx','idcard'=>'yyy'],['name'=>'zzz','idcard'=>'aaa'],]
     * @param  int  $opId  操作员ID
     *
     * @return array
     */
    public function saveOrderTouristInfo($orderNum, array $idCardInfo, $opId)
    {
        $newNum  = count($idCardInfo);
        $oldData = $this->_OrderHandlerModel->getOrderInfo($orderNum,
            'ss.tid,ss.lid,ss.tnum,ss.tprice,ss.totalmoney,ss.status');
        if (!$oldData) {
            return ['code' => 0, 'msg' => '订单不存在'];
        } elseif ($oldData['status'] != 0) {
            return ['code' => 0, 'msg' => '订单已经状态发生变化，无法修改'];
        }
        $lid = $oldData['lid'];
        // 检测是否已经有录入身份证信息
        $existInfo   = $this->_orderToolsModel->getOrderTouristInfo($orderNum);
        $existIdCard = [];
        $existCount  = count($existInfo);
        if ($existInfo) {
            foreach ($existInfo as $item) {
                $existIdCard[] = $item['idcard'];
            }
        }
        $idx         = 1;
        $touristList = [];
        foreach ($idCardInfo as $key => $item) {
            // 已存在的，过滤
            if (in_array($item['idcard'], $existIdCard)) {
                $newNum -= 1;
                unset($idCardInfo[$key]);
                continue;
            }
            $touristList[$key]['lid']     = $lid;
            $touristList[$key]['tourist'] = $item['name'];
            $touristList[$key]['idcard']  = $item['idcard'];
            $touristList[$key]['orderid'] = $orderNum;
            $touristList[$key]['idx']     = $existCount + $idx;
            $idx                          += 1;
        }

        if (count($idCardInfo)) {
            $saveData               = [];
            $newNum                 += $existCount;
            $newTotalMoney          = $newNum * $oldData['tprice'];
            $saveData['tnum']       = $newNum;
            $saveData['totalmoney'] = $newTotalMoney;

            $api = new \Business\JavaApi\Order\OrderTouristUpdate();
            $api->addTourist($touristList, $opId);

            return $this->updateOrderBasicInfo($orderNum, 21, $saveData, $opId, '');
        }

        return ['code' => 0, 'msg' => '没有身份证信息可以保存'];
    }

    /**
     * 改签后修改自动验证时间
     * <AUTHOR>
     * @date   2019-08-15
     *
     * @param  string  $orderNum  平台订单号
     * @param  string  $date  改签的日期
     * @param  integer  $hourTime  票属性中:游玩日期当天几点自动验证
     *
     * @return boolean
     */
    private function _modifyAutoVerityTime($orderNum, $date, $hourTime)
    {
        $verifyDateTime  = $date . " {$hourTime}:00:00";
        $verifyTimeStamp = strtotime($verifyDateTime);

        $resultMsg = '更新自动验证时间失败:';
        $result    = false;

        if ($verifyTimeStamp) {
            $expireActionModel = new \Model\Order\ExpireAction();
            $result            = $expireActionModel->updateAutoCheckTime($orderNum, $verifyTimeStamp);
            $result && $resultMsg = '更新自动验证时间成功:';
        }

        pft_log('order/ticket_change/modify_verify',
            $resultMsg . ' orderNum:' . $orderNum . ' date:' . $date . ' hourTime:' . $hourTime);

        return $result;
    }

    /**
     * 订单改签
     * <AUTHOR> Yiqiang
     * @date   2018-09-19
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $newDate  改签日期
     * @param  int  $opId  操作人员
     * @param  string  $downstreamSn  下游传递的三方操作流水号，如果下游有传这个参数需要透传
     *
     * @return array
     */
    public function ticketChanging($ordernum, $newDate, $sid, $opId, $source, $shareTimeId = 0, $shareTimeStr = '', $isSms = false, $moreData = [], $downstreamSn = '')
    {
        if (!$ordernum || !$newDate || !$source) {
            return $this->returnData(204, '参数错误');
        }
        //查询原有的游玩日期 相同就返回成功
        $oldData = $this->_OrderHandlerModel->getOrderInfo($ordernum,
            'ss.aid,ss.salerid,ss.tid,ss.lid,ss.tnum,ss.playtime,ss.begintime,ss.endtime,ss.status,ss.member,ss.playtime,ss.ordermode',
            'de.product_ext,de.ext_content,de.aids', 'addon.pack_order,addon.ifpack');

        if (!$oldData) {
            return $this->returnData(204, '订单不存在');
        }
        if ($isSms) {
            $sid  = $oldData['member'];
            $opId = $oldData['member'];
        } else {
            if (!$sid || !$opId) {
                return $this->returnData(204, '操作人错误');
            }
        }
        $tnum               = $oldData['tnum'];
        $tid                = $oldData['tid'];
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $javaRes            = $commodityTicketBiz->queryTicketInfoById($tid,
            'id,pre_sale,ticket_changing_audit,ticket_changing_weekend,ticket_changing_range,apply_did,delaytype,use_early_days,delaydays',
            '', 'p_type,terminal', 'auto_checked_at');
        if (empty($javaRes)) {
            return $this->returnData(204, '门票获取失败');
        }
        $ticketInfo = array_merge($javaRes['ticket'], $javaRes['land'], $javaRes['ext'], $javaRes['land_f']);
        $pType      = $ticketInfo['p_type'];
        if (!in_array($pType, ['A', 'B'])) {
            return $this->returnData(204, '该产品类型不可改签');
        }

        if ($oldData['status'] != 0) {
            return $this->returnData(204, '只有未使用订单可以改签');
        }
        //这边判断下他是否有取过票
        $orderTouristMdl = new \Model\Order\OrderTourist();
        $isPrintTicket   = $orderTouristMdl->getOneNoUsedAndPrintTourist($ordernum);
        if (!empty($isPrintTicket)) {
            return $this->returnData(204, '已取票的订单不允许改签');
        }
        //获取下之前是不是分时预约的
        $extProduct        = json_decode($oldData['ext_content'], true) ?? [];
        $oldSectionTimeStr = isset($extProduct['sectionTimeStr']) ? $extProduct['sectionTimeStr'] : '';
        $newTime           = strtotime($newDate);
        $orderEndTime      = strtotime($oldData['endtime']);
        $oldTime           = strtotime($oldData['playtime']);
        if ($newTime == $oldTime) {
            //现有逻辑会产生bug，如果是普通订单，当我传参time_share_info不为空，相同日期就也能改签通过，这里应该判断都有分时预约的信息
            if (($oldSectionTimeStr && $shareTimeStr) && $oldSectionTimeStr != $shareTimeStr) {
                //通过
            } else {
                return $this->returnData(203, '改签日期相同无需更改');
            }
        }

        $memberId = $oldData['member'];
        $topId    = explode(',', $oldData['aids'])[0] ?? 0;
        if ($oldData['ordermode'] == 23) {
            return $this->returnData(203, '子票无法改签操作');
        }
        if (!in_array($sid, [$memberId, $topId, $javaRes['ticket']['apply_did']])) {
            return $this->returnData(203, '无权操作');
        }

        //获取快照的属性
        if ($oldData['ext_content']) {
            $extContent = json_decode($oldData['ext_content'], true);
        } else {
            $extContent = [];
        }

        $version = $extContent['ticketVersion'] ?? 0;

        // 走订单票属性快照
        $ticketBiz      = new \Business\Product\Ticket();
        $ticketSnapshot = $ticketBiz->getTicketSnapShotByCache($tid, $version);
        if (!$ticketSnapshot) {
            return $this->returnData(500, '获取票快照数据错误');
        }
        $SnapshotTicketData = Tools::multiTableUnCamelize($ticketSnapshot);

        //ticket表放最后保证tid是主id
        $SnapshotTicketInfo = array_merge($SnapshotTicketData['confs'], $SnapshotTicketData['uuLandFDTO'],
            $SnapshotTicketData['uuJqTicketDTO']);
        //校验改签快照必须要的属性

        $ticketNeedField = [
            'auto_checked_at',
            'delaytype',
            'delaydays',
            'use_early_days',
            'ticket_changing_audit',
            'ticket_changing_weekend',
            'ticket_changing_range',
        ];
        if (count(array_intersect($ticketNeedField, array_keys($SnapshotTicketInfo))) != count($ticketNeedField)) {
            return $this->returnData(500, '获取票快照数据缺失');
        }
        if (!isset($SnapshotTicketInfo['pre_sale'])) {
            $SnapshotTicketInfo['pre_sale'] = 0;
        }
        $changeAudit = $SnapshotTicketInfo['ticket_changing_audit'];
        $canWeekend  = $SnapshotTicketInfo['ticket_changing_weekend'];
        $dayChange   = $SnapshotTicketInfo['ticket_changing_range'];

        $isOnlineReserve = isset($SnapshotTicketInfo['is_online_reserve']) ? $SnapshotTicketInfo['is_online_reserve'] : 0;
        if (!$dayChange) {
            return $this->returnData(203, '不允许改签');
        }
        if ($SnapshotTicketInfo['pre_sale'] == 1 && $isOnlineReserve == 0) {
            return $this->returnData(203, '期票无需改签');
        }

        //判断下单后几天有效的逻辑
        if ($SnapshotTicketInfo['pre_sale'] != 1 && $SnapshotTicketInfo['delaytype'] == 1 && $SnapshotTicketInfo['delaydays'] > 0 && $newTime > $orderEndTime) {
            return $this->returnData(203, '改签日期不能超过下单时间后' . $SnapshotTicketInfo['delaydays'] . '天');
        }
        //库存检测
        $checkStorageRes = $this->_checkChangingStorage($oldData['tid'], $newDate, $shareTimeId, $oldData['ordermode'],
            $SnapshotTicketInfo, $oldData);
        if ($checkStorageRes['code'] != 200) {
            return $this->returnData($checkStorageRes['code'], $checkStorageRes['msg']);
        }
        //是否可以平时改周末
        if (!$canWeekend && !in_array(date('w', $oldTime), [0, 6]) && in_array(date('w', $newTime), [0, 6])) {
            return $this->returnData(204, '不允许平时改至周末');
        }
        //查询终端订单变动表是否存在改签记录
        $auditModel  = new RefundAuditModel();
        $changeInfos = $auditModel->getTicketChangingOrders([$ordernum], 'ordernum,dstatus');
        if ($changeInfos) {
            $changeInfo = array_shift($changeInfos);
            if (in_array($changeInfo['dstatus'], [0, 1, 2, 3])) {
                //未审核，同意，等待第三方,拒绝
                //已经改签过或者有待审核改签
                return $this->returnData(203, '订单已改签过了或申请过改签');
            }
        }
        $timeMax = $oldTime + 60 * 60 * 24 * intval($dayChange);
        $timeMin = $oldTime - 60 * 60 * 24 * intval($dayChange);
        //获取今日整时间
        $todayTime = strtotime(date('Y-m-d', time())); //要check一下
        if ($newTime < $todayTime || $newTime < $timeMin || $newTime > $timeMax) {
            return $this->returnData(204, "改签日期不能小于当天或超过原游玩日期前后{$dayChange}天");
        }

        //判断下要不要审核的
        $recordModel = new AccessRecord();
        $recordRe    = $recordModel->insert($opId, 9, "改签|$ordernum|{$opId}"); //不需要记录主账号id吗
        if (!$recordRe) {
            return $this->returnData(201, '改签操作失败');
        }
        $terminal  = $ticketInfo['terminal'] ?: 0;
        $salerid   = $oldData['salerid'];
        $auditorId = $ticketInfo['apply_did'];
        $lid       = $oldData['lid'];
        $stype     = 4;
        $remoteSn  = $newDate; //第三方流水号存储改签日期
        $systemSn  = $oldData['playtime']; //平台流水号存储旧日期
        $note      = "游玩日期：{$oldData['playtime']} $oldSectionTimeStr 改至$newDate $shareTimeStr";
        $dStatus   = $changeAudit ? 0 : 1;
        //这边获取一些冗余字段插入到审核表
        $otherData               = $this->getAuditOtherData($ordernum);
        $otherData['audit_data'] = [
            'playTime'            => $newDate,
            'shareId'             => $shareTimeId,//分时id
            'shareStr'            => $shareTimeStr,
            'downstreamSn'        => $downstreamSn, //下游传递的第三方流水号暂时先存入这个字段，因为大佬们担心影响原有逻辑
            'cancel_audit_remark' => $note,
        ];
        $trackExtContent = [];
        if (!empty($moreData)){
            $otherData['sub_merchant_id'] = $moreData['subSid'] ?? -1;
            $trackExtContent['subSid']    = $moreData['subSid'] ?? 0;
            $trackExtContent['subOpId']   = $moreData['subOpId'] ?? 0;
        }
        if ($changeAudit) {   //需要审核
            $action = 23; //提交改签申请
            //审核表的终端订单库表结构不同 是否有在使用?
            $auditRe = $auditModel->addRefundAudit($ordernum, $tnum, $terminal, $salerid, $lid, $tid,
                $stype, $tnum, $sid, $dStatus, $auditorId, 0, $note, 0, $remoteSn, $systemSn, false,
                $otherData);

            if (!$auditRe) {
                return $this->returnData(201, '插入审核记录失败');
            }

            $trackModel = new OrderTrack();
            //source
            $source = 16; //内部接口
            $trackModel->addTrack($ordernum, $action, $tid, $oldData['tnum'], $oldData['tnum'], $source, $terminal,
                '', $opId, $salerid, time(), '', $note, 0, false, $trackExtContent);

            return $this->returnData(200, '已提交审核，待审核通过后完成改签', ['isChangeAudit' => $changeAudit]);
        }

        $changeRes = $this->_orderTicketChangeHandle($ordernum, $newDate, $oldData, $SnapshotTicketInfo, $source, $opId,
            $shareTimeId, $shareTimeStr, $note, $moreData);
        if ($changeRes['code'] == 200) {
            $auditRe = $auditModel->addRefundAudit($ordernum, 0, $terminal, $salerid, $lid, $timeMax, $stype,
                $tnum, $opId, $dStatus, $auditorId, 0, $note, 0, $remoteSn, $systemSn, false,
                $otherData);
            if (!$auditRe) {
                return $this->returnData(204, '插入审核数据错误');
            }
        }

        return $changeRes;
    }

    /**
     * 订单改签权限判断
     * @todo  后面把ticketChanging里面关于权限判断的部分调用这个方法
     *
     * <AUTHOR>
     * @date   2021-09-08
     *
     * @param  string  $ordernum  平台订单号
     * @param  string  $newDate  改签日期
     * @param  int  $opId  操作人员
     *
     * @return array
     */
    public function ticketChangingJudge($ordernum, $newDate, $sid, $opId, $source, $shareTimeId = 0, $shareTimeStr = '', $isSms = false)
    {
        if (!$ordernum || !$newDate || !$source) {
            return $this->returnData(204, '参数错误');
        }
        //查询原有的游玩日期 相同就返回成功
        $oldData = $this->_OrderHandlerModel->getOrderInfo($ordernum,
            'ss.aid,ss.salerid,ss.tid,ss.lid,ss.tnum,ss.playtime,ss.begintime,ss.endtime,ss.status,ss.member,ss.playtime,ss.ordermode',
            'de.product_ext,de.ext_content,de.aids', 'addon.pack_order,addon.ifpack');

        if (!$oldData) {
            return $this->returnData(204, '订单不存在');
        }
        if ($isSms) {
            $sid  = $oldData['member'];
            $opId = $oldData['member'];
        } else {
            if (!$sid || !$opId) {
                return $this->returnData(204, '操作人错误');
            }
        }
        $tnum               = $oldData['tnum'];
        $tid                = $oldData['tid'];
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $javaRes            = $commodityTicketBiz->queryTicketInfoById($tid,
            'id,pre_sale,ticket_changing_audit,ticket_changing_weekend,ticket_changing_range,apply_did,delaytype,use_early_days,delaydays',
            '', 'p_type,terminal', 'auto_checked_at');
        if (empty($javaRes)) {
            return $this->returnData(204, '门票获取失败');
        }
        $ticketInfo = array_merge($javaRes['ticket'], $javaRes['land'], $javaRes['ext'], $javaRes['land_f']);
        $pType      = $ticketInfo['p_type'];
        if (!in_array($pType, ['A', 'B'])) {
            return $this->returnData(204, '该产品类型不可改签');
        }

        if ($oldData['status'] != 0) {
            return $this->returnData(204, '只有未使用订单可以改签');
        }
        //这边判断下他是否有取过票
        $orderTouristMdl = new \Model\Order\OrderTourist();
        $isPrintTicket   = $orderTouristMdl->getOneNoUsedAndPrintTourist($ordernum);
        if (!empty($isPrintTicket)) {
            return $this->returnData(204, '已取票的订单不允许改签');
        }
        //获取下之前是不是分时预约的
        $extProduct        = json_decode($oldData['ext_content'], true) ?? [];
        $oldSectionTimeStr = isset($extProduct['sectionTimeStr']) ? $extProduct['sectionTimeStr'] : '';
        $newTime           = strtotime($newDate);
        $orderEndTime      = strtotime($oldData['endtime']);
        $oldTime           = strtotime($oldData['playtime']);
        if ($newTime == $oldTime) {
            if ($oldSectionTimeStr != $shareTimeStr) {
                //通过
            } else {
                return $this->returnData(203, '改签日期相同无需更改');
            }

        }

        $memberId = $oldData['member'];
        $topId    = explode(',', $oldData['aids'])[0] ?? 0;
        if ($oldData['ordermode'] == 23) {
            return $this->returnData(203, '子票无法改签操作');
        }
        if (!in_array($sid, [$memberId, $topId, $javaRes['ticket']['apply_did']])) {
            return $this->returnData(203, '无权操作');
        }

        //获取快照的属性
        if ($oldData['ext_content']) {
            $extContent = json_decode($oldData['ext_content'], true);
        } else {
            $extContent = [];
        }

        $version = $extContent['ticketVersion'] ?? 0;

        // 走订单票属性快照
        $ticketBiz      = new \Business\Product\Ticket();
        $ticketSnapshot = $ticketBiz->getTicketSnapShotByCache($tid, $version);
        if (!$ticketSnapshot) {
            return $this->returnData(500, '获取票快照数据错误');
        }
        $SnapshotTicketData = Tools::multiTableUnCamelize($ticketSnapshot);

        //ticket表放最后保证tid是主id
        $SnapshotTicketInfo = array_merge($SnapshotTicketData['confs'], $SnapshotTicketData['uuLandFDTO'],
            $SnapshotTicketData['uuJqTicketDTO']);

        //校验改签快照必须要的属性
        $ticketNeedField = [
            'auto_checked_at',
            'delaytype',
            'delaydays',
            'use_early_days',
            'ticket_changing_audit',
            'ticket_changing_weekend',
            'ticket_changing_range',
        ];
        if (count(array_intersect($ticketNeedField, array_keys($SnapshotTicketInfo))) != count($ticketNeedField)) {
            return $this->returnData(500, '获取票快照数据缺失');
        }
        if (!isset($SnapshotTicketInfo['pre_sale'])) {
            $SnapshotTicketInfo['pre_sale'] = 0;
        }
        $changeAudit = $SnapshotTicketInfo['ticket_changing_audit'];
        $canWeekend  = $SnapshotTicketInfo['ticket_changing_weekend'];
        $dayChange   = $SnapshotTicketInfo['ticket_changing_range'];

        $isOnlineReserve = isset($SnapshotTicketInfo['is_online_reserve']) ? $SnapshotTicketInfo['is_online_reserve'] : 0;
        if (!$dayChange) {
            return $this->returnData(203, '不允许改签');
        }
        if ($SnapshotTicketInfo['pre_sale'] == 1 && $isOnlineReserve == 0) {
            return $this->returnData(203, '期票无需改签');
        }

        //判断下单后几天有效的逻辑
        if ($SnapshotTicketInfo['pre_sale'] != 1 && $SnapshotTicketInfo['delaytype'] == 1 && $SnapshotTicketInfo['delaydays'] > 0 && $newTime > $orderEndTime) {
            return $this->returnData(203, '改签日期不能超过下单时间后' . $SnapshotTicketInfo['delaydays'] . '天');
        }
        //库存检测
        $checkStorageRes = $this->_checkChangingStorage($oldData['tid'], $newDate, $shareTimeId, $oldData['ordermode'],
            $SnapshotTicketInfo, $oldData);
        if ($checkStorageRes['code'] != 200) {
            return $this->returnData($checkStorageRes['code'], $checkStorageRes['msg']);
        }
        //是否可以平时改周末
        if (!$canWeekend && !in_array(date('w', $oldTime), [0, 6]) && in_array(date('w', $newTime), [0, 6])) {
            return $this->returnData(204, '不允许平时改至周末');
        }

        //查询终端订单变动表是否存在改签记录
        $auditModel  = new RefundAuditModel();
        $changeInfos = $auditModel->getTicketChangingOrders([$ordernum], 'ordernum,dstatus');
        if ($changeInfos) {
            $changeInfo = array_shift($changeInfos);
            if (in_array($changeInfo['dstatus'], [0, 1, 2, 3])) {
                //未审核，同意，等待第三方,拒绝
                //已经改签过或者有待审核改签
                return $this->returnData(203, '订单已改签过了或申请过改签');
            }
        }
        $timeMax = $oldTime + 60 * 60 * 24 * intval($dayChange);
        $timeMin = $oldTime - 60 * 60 * 24 * intval($dayChange);
        //获取今日整时间
        $todayTime = strtotime(date('Y-m-d', time())); //要check一下
        if ($newTime < $todayTime || $newTime < $timeMin || $newTime > $timeMax) {
            return $this->returnData(204, "改签日期不能小于当天或超过原游玩日期前后{$dayChange}天");
        }

        return $this->returnData(200, "可以改签");
    }


    /***
     * 改签审核
     * <AUTHOR> Yiqiang
     * @date   2018-09-21
     *
     * @param $orderNum
     * @param $auditResult
     * @param $auditNote
     * @param $operatorID
     * @param $auditID
     *
     * @return array|int
     */
    public function ticketChangingAudit($orderNum, $auditResult, $auditNote, $operatorID, $auditID)
    {
        $refundModel = new RefundAuditModel();
        if ($auditID == 0) {
            $tChangingRecord = $refundModel->getTerminalChangeInfo($orderNum, 0);
        } else {
            $tChangingRecord = $refundModel->getRefundAuditDetailById($auditID);
        }
        //如果该订单已经审核处理过了，就不再重复处理
        if ($tChangingRecord['dstatus'] != 0) {
            //审核数据已经处理过，不需要处理了
            $logData = json_encode([
                'status'   => 256,
                'ordernum' => $orderNum,
                'tnum'     => $tChangingRecord['tnum'],

            ]);
            pft_log('order/ticket_changing_audit', $logData);

            return 258;
        }

        if (!in_array($auditResult, [1, 2])) {
            //审核结果只能是同意或拒绝
            return 250;
        }

        $auditData = json_decode($tChangingRecord['audit_data'], true) ?? [];
        if (empty($auditData) || !isset($auditData['playTime'])) {
            $playTime     = $tChangingRecord['remote_sn'];
            $shareTimeId  = 0;
            $shareTimeStr = '';
            //演出改签信息
            $sid        = 0;
            $aid        = 0;
            $venueId    = 0;
            $roundId    = 0;
            $source     = 0;
            $seatIdList = [];
            $isSms      = false;

        } else {
            $playTime     = $auditData['playTime'];
            $shareTimeId  = isset($auditData['shareId']) ? $auditData['shareId'] : 0;
            $shareTimeStr = isset($auditData['shareStr']) ? $auditData['shareStr'] : '';
            $shareTimeStr = !empty($shareTimeStr) ? $shareTimeStr : ($auditData['timeStr'] ?? ''); //兼容演出这边
            //演出改签信息
            $sid        = isset($auditData['sid']) ? $auditData['sid'] : 0;
            $aid        = isset($auditData['aid']) ? $auditData['aid'] : 0;
            $venueId    = isset($auditData['venueId']) ? $auditData['venueId'] : 0;
            $roundId    = isset($auditData['roundId']) ? $auditData['roundId'] : 0;
            $seatIdList = isset($auditData['seatIdList']) ? $auditData['seatIdList'] : [];
            $source     = isset($auditData['source']) ? $auditData['source'] : 16;
            $isSms      = isset($auditData['isSms']) ? $auditData['isSms'] : false;

        }

        $orderInfo = $this->_OrderHandlerModel->getOrderInfo($orderNum,
            'ss.aid,ss.salerid,ss.tid,ss.lid,ss.tnum,ss.playtime,ss.begintime,ss.endtime,ss.status,ss.member,ss.ordermode',
            'de.product_ext,de.ext_content');

        if (!$orderInfo) {
            return 250;
        }

        if ($auditResult == 1) {  //同意的话判断下是不是游玩时间没超过今天
            if ($orderInfo['product_type'] == 'H') {
                $series  = unserialize($orderInfo['series']);  //0场馆id 1场次id 2分区id 3座位id 4演出日期 5座位号
                $venueID = $series[0] ? $series[0] : '';
                $roundID = $series[1] ? $series[1] : '';

                $newTime = strtotime($playTime);
                $oldTime = strtotime($orderInfo['playtime']);

                if ($venueId != $venueID) {
                    return 283;
                }

                if ($newTime == $oldTime) {
                    if ($roundID != $roundId) {
                        //通过
                    } else {
                        return 284;
                    }
                }
            } else {
                if (strtotime($playTime) < strtotime(date('Y-m-d'))) {
                    return 280;
                }
            }
        }

        //获取快照的属性
        if ($orderInfo['ext_content']) {
            $extContent = json_decode($orderInfo['ext_content'], true);
        } else {
            $extContent = [];
        }

        $version = $extContent['ticketVersion'] ?? 0;

        // 走订单票属性快照
        $ticketBiz      = new \Business\Product\Ticket();
        $ticketSnapshot = $ticketBiz->getTicketSnapShotByCache($orderInfo['tid'], $version);
        if (!$ticketSnapshot) {
            return 250;
        }
        $SnapshotTicketData = Tools::multiTableUnCamelize($ticketSnapshot);

        //ticket表放最后保证tid是主id
        $SnapshotTicketInfo = array_merge($SnapshotTicketData['confs'], $SnapshotTicketData['uuLandFDTO'],
            $SnapshotTicketData['uuJqTicketDTO']);
        //校验改签快照必须要的属性

        $ticketNeedField = [
            'auto_checked_at',
            'delaytype',
            'delaydays',
            'pre_sale',
            'use_early_days',
            'ticket_changing_audit',
            'ticket_changing_weekend',
            'ticket_changing_range',
        ];
        if (count(array_intersect($ticketNeedField, array_keys($SnapshotTicketInfo))) != count($ticketNeedField)) {
            return 250;
        }

        $opId   = $tChangingRecord['dadmin'];
        $action = 25;
        //更新日期
        if ($auditResult == 1) {
            $code = 200;
            $msg  = '';
            try {
                if ($orderInfo['product_type'] == 'H') {
                    $orderBiz     = new ShowModify();
                    $showChangRes = $orderBiz->showTicketChanging($orderNum, $playTime, $sid, $aid, $venueId,
                        $roundId, $opId, $source, $seatIdList, $isSms, $isAudit = true);
                    if ($showChangRes['code'] != 200) {
                        pft_log('order/ticket_changing_audit', $orderNum . ":" . $showChangRes['msg']);
                        throw new \Exception($showChangRes['msg'], 252);
                    }
                } else {
                    //这边判断下他是否有取过票
                    $orderTouristMdl = new \Model\Order\OrderTourist();
                    $isPrintTicket   = $orderTouristMdl->getOneNoUsedAndPrintTourist($orderNum);
                    if (!empty($isPrintTicket)) {
                        throw new \Exception('已取票的订单不允许改签', 282);
                    }
                    //库存检测
                    $checkStorageRes = $this->_checkChangingStorage($orderInfo['tid'], $playTime, $shareTimeId,
                        $orderInfo['ordermode'], $SnapshotTicketInfo, $orderInfo);
                    if ($checkStorageRes['code'] != 200) {
                        throw new \Exception($checkStorageRes['msg'], 281);
                    }
                    $changeRes = $this->_orderTicketChangeHandle($orderNum, $playTime, $orderInfo, $SnapshotTicketInfo,
                        16,
                        $opId, $shareTimeId, $shareTimeStr, '审核改签通过');
                    if ($changeRes['code'] != 200) {
                        throw new \Exception($changeRes['msg'], 252);
                    }
                }

            } catch (\Exception $e) {
                $code = $e->getCode();
                $msg  = $e->getMessage();
            }
            if ($code != 200) {
                pft_log('order/ticket_changing_audit', $orderNum . ":" . $msg);

                return $code;
            }
        } else {
            $trackSql = $this->_orderTrackModel->addTrack($orderNum, $action,
                $orderInfo['tid'], $orderInfo['tnum'], $orderInfo['tnum'], 16, 0,
                0, '', $opId, '', '', '', 0, true);
            if ($trackSql) {
                $terminalDB = new \Model\Order\TerminalDbHandler();
                //同步终端库
                $terminalDB->execute($trackSql);
            }
        }
        //更新审核记录
        $result = $refundModel->updateAudit($orderNum, $auditResult, $auditNote, $operatorID, 0, $auditID);
        pft_log('order/ticket_changing_audit', "$orderNum :" . json_encode($result));
        if (!$result) {
            return 259;
        }

        //判断是否是下游订单，需要通知审核结果给下游
        $callBack  = $orderInfo['callback'] ?? 0; //与退票审核保持一致，callback=0的时候不发消息
        if ($callBack) {
            (new RefundAudit())->_ticketChangeNotifyDownstream($orderInfo, $tChangingRecord, $auditResult, $auditNote, $playTime, $shareTimeStr);
        }

        return 200;
    }

    /***
     * 如果设置取票后多少天有效，需要修改订单有效期
     * <AUTHOR> Yiqiang
     * @date   2018-09-29
     *
     * @param $ordernum
     * @param $afterDraft
     * @param $ticketInfo
     * @param $orderInfo
     * @param $opId
     *
     * @return array
     */
    public function changeEndTimeIfSetDraft($ordernum, $afterDraft, $ticketInfo, &$orderInfo, $opId)
    {
        //取票后多少天有效修改订单的有效期
        if ($afterDraft > 0 && !($ticketInfo['delaytype'] == 0 && $ticketInfo['delaydays'] == 0 && $ticketInfo['use_early_days'] == 0)) {
            //排除游玩当天有效
            //$handlerModel = new OrderHandler();
            //有效期按订单，取票后多少天有效按票属性
            $newEndTime = date('Y-m-d', strtotime(date('Y-m-d', time())) + 60 * 60 * 24 * ($afterDraft - 1));
            if (strtotime($newEndTime) < strtotime($orderInfo['begintime']) || strtotime($newEndTime) > strtotime($orderInfo['endtime'])) {
                //新日期跟订单有效期没有交集不改变订单
                return $this->returnData(200, "success");
            }
            //正常修改
            if (strtotime($newEndTime) < strtotime($orderInfo['endtime']) && strtotime($newEndTime) >= strtotime($orderInfo['begintime'])) {
                $noticeBegin = date('Ymd', strtotime($orderInfo['begintime']));
                $noticeEnd   = date('Ymd', strtotime($newEndTime));
                $changeMsg   = "取票后变更有效期:\n $noticeBegin~$noticeEnd";
                $saveData    = ['endtime' => $newEndTime];

                $modifyRe = $this->updateOrderBasicInfo($ordernum, 21, $saveData, $opId, $changeMsg);

                $playData    = date('Y-m-d', $orderInfo['playtime']);
                $startData   = date('Y-m-d', $orderInfo['begintime']);
                $queryParams = [$ordernum, $playData, $startData, $newEndTime];
                $queryRes    = \Business\JavaApi\Order\Query\Container::query('OrderReferModify',
                    'modifyOrderReferPlayTimeAndValidityByOrderNum', $queryParams);

                $referRe = false;
                if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                    $referRe = true;
                }

                //$referRe     = $handlerModel->updateReferOrder($ordernum, $saveData);
                if ($modifyRe['code'] != 200 && $referRe !== false) {
                    return $this->returnData(204, "订单号:{$ordernum}," . '修改有效期失败');
                }
                $orderInfo['endtime'] = $newEndTime;

                return $this->returnData(200, "success");
            }
        }

        return $this->returnData(200, "success");
    }

    /***
     * 获取要添加到终端订单变动记录表里的参数
     * <AUTHOR>
     * @date   2019-04-10
     *
     * @param $ordernum string 订单号
     *
     * @return array
     */
    public function getAuditOtherData($ordernum)
    {
        $field            = "ss.lid,ss.tid,addon.ifpack,ss.ordernum,de.concat_id,de.aids,addon.pack_order";
        $subOrderModel    = new SubOrderQuery();
        $memberMdl        = new Member();
        $commodityLandBiz = new \Business\CommodityCenter\Land();
        $orderInfo        = $subOrderModel->getDetailByOrderJoin($ordernum, $field, true, true);
        $landInfo         = $commodityLandBiz->getLandInfoByTidToJava($orderInfo['tid']);
        $tInfo            = $this->_ticketModel->getTicketInfoById($orderInfo['tid'], 'apply_did,title');
        $memberInfo       = $memberMdl->getMemberInfo($tInfo['apply_did'], 'id', 'dcodeURL');

        $data = [
            'aids'        => isset($orderInfo['aids']) ? $orderInfo['aids'] : '',
            'land_name'   => isset($landInfo['title']) ? $landInfo['title'] : '',
            'ticket_name' => isset($tInfo['title']) ? $tInfo['title'] : '',
            'apply_did'   => isset($tInfo['apply_did']) ? $tInfo['apply_did'] : 0,
            'concat_id'   => isset($orderInfo['concat_id']) ? $orderInfo['concat_id'] : '',
            'pack_order'  => isset($orderInfo['pack_order']) ? $orderInfo['pack_order'] : '',
            'dcodeURL'    => isset($memberInfo['dcodeURL']) ? $memberInfo['dcodeURL'] : '',
            'ifpack'      => $orderInfo['ifpack'],
            'p_type'      => isset($landInfo['p_type']) ? $landInfo['p_type'] : '',
        ];

        return $data;
    }

    /**
     * 判断取消权限
     * 暂时只判断操作用户是否在购买链上面
     *
     * <AUTHOR>
     * @date 2019-07-17
     *
     * @param  array  $orderInfo  订单信息
     * @param  int  $opId  退票用户ID
     * @param  int  $parentId  如果是员工账号，主账号ID
     *
     * @return boolean
     */
    private function _isHaveRefundAuth($orderInfo, $opId, $parentId)
    {
        $buyMembers = [$orderInfo['member'], $orderInfo['aid']];
        if ($orderInfo['aids']) {
            $tmpMembers = explode(',', $orderInfo['aids']);
            $buyMembers = array_merge($buyMembers, $tmpMembers);
        }
        if (in_array($parentId, $buyMembers)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取保存游客信息的数据
     * <AUTHOR> Chen
     *
     * @param  int  $lid  景点ID
     * @param  string  $ordernum  订单号
     * @param  int  $preNum  之前已有的数量
     * @param  int  $tnum  数量
     * @param  array  $personIDList
     * @param  int  $printMode  门票打印类型
     * @param  int  $checkMode  入园方式
     *
     * @return array
     */
    private function _generateOrderTouristData($lid, $ordernum, $preNum, $tnum, array $personIDList, $printMode = 4, $checkMode = 1)
    {
        $nowNum          = $preNum + $tnum;
        $personInfo      = [];
        $offlinePrintBiz = new OfflinePrint();
        if (count($personIDList) > 0) {
            foreach ($personIDList as $idx => $item) {
                $index        = $idx + 1;
                $code         = $offlinePrintBiz->createTicketCode($ordernum, $printMode, $checkMode, $index);
                $personInfo[] = [
                    'lid'         => $lid,
                    'orderid'     => $ordernum,
                    'tourist'     => $item['name'],
                    'idcard'      => $item['idcard'],
                    'mobile'      => isset($item['mobile']) ? $item['mobile'] : '',
                    'idx'         => $index, //序号
                    'voucherType' => 1,
                    'chkCode'     => $code,
                ];
            }
        } elseif ($printMode == 4) {
            for ($idx = $preNum; $idx < $nowNum; $idx++) {
                $index        = $idx + 1;
                $code         = $offlinePrintBiz->createTicketCode($ordernum, $printMode, $checkMode, $index);
                $personInfo[] = [
                    'lid'     => $lid,
                    'orderid' => $ordernum,
                    'idx'     => $index, //序号
                    'chkCode' => $code,
                ];
            }
        }

        return $personInfo;
    }

    /**
     * 解析错误代码
     *
     * <AUTHOR>
     * @date   2017-05-04
     *
     * @param  int  $code  错误代码
     *
     * @return string
     */
    private function _errorParse($code)
    {
        $errPaser = load_config('error_list', 'order');
        //未定义的错误
        if (!isset($errPaser[$code])) {
            $msg = '错误代码:' . $code;
        } else {
            $msg = $errPaser[$code];
        }

        return $msg;
    }

    /***
     * 套票子订单强制退票加上退钱
     * <AUTHOR>
     * @date   2019-04-10
     *
     * @param $ordernum string 订单号
     *
     * @return array
     */
    public function forcedRefundMoneyAndCancel($orderNum, $num, $totalMoney, $opId, $ip, $sid)
    {
        $orderMdl  = new OrderTools();
        $field     = 'ss.ordernum,ss.aid,ss.tid ,ss.member,ss.totalmoney,ss.tnum,ss.pay_status,ss.status, addon.ifpack, addon.pack_order,ss.ordermode,ss.paymode';
        $orderInfo = $orderMdl->getOrderInfo($orderNum, $field, false, true, false);
        //子票校验
        $resCheck = $this->_baseCheckRefundOrder($orderInfo, $num);
        if ($resCheck['code'] != 200) {
            return $this->returnData($resCheck['code'], $resCheck['msg']);
        }
        $fieldMainOrder = 'ss.ordernum,ss.aid,ss.tid ,ss.tprice,ss.member,ss.totalmoney,ss.tnum,ss.pay_status,ss.status, addon.ifpack, addon.pack_order,ss.ordermode,ss.paymode,de.aids_price,de.aids_money,de.aids';
        $mainOrderInfo  = $orderMdl->getOrderInfo($orderInfo['pack_order'], $fieldMainOrder, true, true, false);
        //主票校验
        $mainOrderRes = $this->_checkMainOrderRefund($mainOrderInfo, $totalMoney, $sid, $opId);
        if ($mainOrderRes['code'] != 200) {
            return $this->returnData($mainOrderRes['code'], $mainOrderRes['msg']);
        }

        $cancelChannel    = OrderConst::PACK_SON_FORCE_CANCEL;
        $cancelType       = 'common';
        $reqSerialNumber  = '';
        $cancelRemarkArr  = [
            'remark' => "强制取消子票退钱",
        ];
        $cancelSiteArr    = [];
        $cancelPersonArr  = [];
        $cancelSpecialArr = [
            'is_need_audit'   => false,
            'is_force_cancel' => true,
            'is_cancel_sub'   => false,
        ];

        $refundRes = Helpers::platformRefund($orderNum, $num, $opId, $cancelChannel, $cancelType,
            $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
        if ($refundRes['code'] == 1095) {
            //需要审核的票修改成功
            return $this->returnData(204, '退票审核已提交，暂时无法退主票钱，请关闭审核功能', $refundRes['data']);
        } elseif ($refundRes['code'] != 200) {
            return $this->returnData(204, '取消出错', $refundRes['data']);
        }
        //扣去主票的钱
        $deductRes = $this->deductMainTicketMoney($mainOrderInfo, $orderNum, $totalMoney, $opId, $ip);

        return $this->returnData($deductRes['code'], $deductRes['msg']);
    }

    /***
     * 退款钱判断
     * <AUTHOR>
     * @date   2019-08-12
     *
     * @param $orderInfo  array 订单详情
     * @param $num int 数量
     *
     * @return array
     */
    private function _baseCheckRefundOrder($orderInfo, $num)
    {
        if ($orderInfo['pay_status'] != 1 || $orderInfo['tnum'] < 1 || $orderInfo['ordermode'] == 24) {
            return $this->returnData(204, '订单不可取消');
        }
        if ($orderInfo['ifpack'] != 2) {
            return $this->returnData(204, '只支持套票子票取消');
        }
        if (!in_array($orderInfo['status'], [0, 2, 7])) {
            return $this->returnData(204, '订单状态不允许取消');
        }
        if ($orderInfo['tnum'] - $num < 0) {
            return $this->returnData(204, '票数有误');
        }
        $auditMdl  = new RefundAuditModel();
        $isInAudit = $auditMdl->isAllUnderAudit([$orderInfo['ordernum']]);
        if ($isInAudit) {
            return $this->returnData(203, '订单审核中');
        }

        return $this->returnData(200, 'gogogo');
    }

    /***
     * 获取要添加到终端订单变动记录表里的参数
     * <AUTHOR>
     * @date   2019-08-12
     *
     * @param $orderNum  string 订单号
     *
     * @return array
     */
    public function getSonOrderCostMoney($orderNum)
    {
        $orderMdl  = new OrderTools();
        $field     = 'ss.ordernum,ss.tid, ss.member,ss.totalmoney,ss.tnum,ss.pay_status,ss.status, addon.ifpack, addon.pack_order';
        $orderInfo = $orderMdl->getOrderInfo($orderNum, $field, false, true, false);
        if ($orderInfo['ifpack'] != 2 || !$orderInfo['pack_order']) {
            return $this->returnData(204, '请选择套票的子票');
        }
        $mainOrderInfo = $orderMdl->getOrderInfo($orderInfo['pack_order'], $field, false, true, false);
        if (empty($mainOrderInfo)) {
            return $this->returnData(204, '主票查询失败');
        }

        $packBiz  = new \Business\Product\PackTicket();
        $packInfo = $packBiz->getCostPriceByParentAndTid($mainOrderInfo['tid'], $orderInfo['tid']);
        if (empty($packInfo)) {
            return $this->returnData(204, '获取打包成本价失败');
        }

        $totalMoney = [
            'total_money' => $mainOrderInfo['totalmoney'],
        ];
        $packInfo   = array_merge($packInfo, $totalMoney);

        return $this->returnData(200, 'success', $packInfo);
    }

    /***
     * 套票取消成功扣去主票的钱
     * <AUTHOR>
     * @date   2019-08-12
     *
     * @param $mainInfo  array 主票订单号
     * @param $sonOrderNum  string 子票订单号
     * @param $money  int    金额
     *
     * @return array
     */
    private function deductMainTicketMoney($mainInfo, $sonOrderNum, $money = 0, $opId = 0, $ip = '')
    {
        $mainOrderNum = $mainInfo['ordernum'];
        //自供自销，不做退款记录
        if ($mainInfo['paymode'] == 3) {
            return $this->returnData(200, '主票是自供应产品,自供应产品不需要退钱,已取消子票');
        }

        //订单追踪
        $track        = new \Model\Order\OrderTrack();
        $staffId      = $opId;
        $trackChannel = \Model\Order\OrderTrack::SOURCE_INSIDE_SOAP;
        $trackId      = $track->addTrack($mainOrderNum, 2, $mainInfo['tid'],
            0, $mainInfo['tnum'], $trackChannel, 0, 0, 0,
            $staffId, 0, '', '套票子票取消退钱'
        );

        $modelRefund     = new OrderRefund();
        $microtime       = str_pad(str_replace('.', '', microtime(true)), 14, '0');
        $reqSerialNumber = strval("{$mainOrderNum}_{$microtime}");
        //为了保障不修改主订单num传0
        $log_result = $modelRefund->addRefundJournal($mainOrderNum, $reqSerialNumber, $mainInfo['aid'], 2, 0, 0,
            $mainInfo['tnum'], $mainInfo['totalmoney'],
            $mainInfo['totalmoney'], time(), true, $opId, $receiveType = 0, '', $trackId, $refundStatus = 1);
        if ($log_result === false) {
            pft_log('/sql_error', $modelRefund->getLastSql() . ';errmsg:' . $modelRefund->getDbError());

            return $this->returnData(204, '插入退款记录错误');
        }

        //插入一条未生效的退款记录
        $refundSonLogMdl = new OrderSonRefundLog();
        $refundLog       = $refundSonLogMdl->addRefundSonLog($mainOrderNum, $sonOrderNum, $money, $opId, $ip,
            $log_result['id']);
        if (!$refundLog) {
            return $this->returnData(204, '插入主票退款记录失败');
        }

        $refundBiz   = new Refund();
        $specialData = [
            'is_pack_refund' => true,
            'pack_money'     => $money,
        ];
        $refundRes   = $refundBiz->orderTradeRefund($log_result['id'], [], '', null, null, $specialData);
        if ($refundRes['code'] != 200) {
            return $this->returnData($refundRes['code'], '取消成功,退款失败,原因：' . $refundRes['msg']);
        }

        $editRefundLog = $refundSonLogMdl->editRefundSonLog($refundLog);
        if (!$editRefundLog) {
            pft_log('/sql_error', '跟新主票失败' . $modelRefund->getLastSql() . ';errmsg:' . $modelRefund->getDbError());

            return $this->returnData(204, '更新主票支付记录失败，联系管里员');
        }

        return $this->returnData(200, '取消子票，主票退款成功');
    }

    /***
     * 验证主票逻辑
     * <AUTHOR>
     * @date   2019-08-12
     *
     * @param $mainOrderInfo  array 主票订单号
     * @param $totalMoney  int    金额
     *
     * @return array
     */
    private function _checkMainOrderRefund($mainOrderInfo, $totalMoney, $sid, $opId)
    {
        if ($mainOrderInfo['totalmoney'] - $totalMoney < 0 || $mainOrderInfo['tnum'] < 1) {
            return $this->returnData(204, '金额或者数量超过最大退款');
        }
        if (in_array($mainOrderInfo['status'], [3])) {
            return $this->returnData(204, '主票已经被取消,不能操作退钱');
        }
        if ($mainOrderInfo['ordermode'] == 24) {
            return $this->returnData(204, '团单的暂不支持取消退钱');
        }
        if ($mainOrderInfo['status'] == 1 && !in_array($sid,[4910675, ])) {
            return $this->returnData(204, '主票已使用,不能操作退钱');
        }
//        if ($mainOrderInfo['aprice'] * $mainOrderInfo['tnum'] < $totalMoney) {
//            return $this->returnData(204, '退票金额不能高于成本价');
//        }

        //目前只能让主票实现一次退款
        $refundSonLogMdl = new OrderSonRefundLog();
        $refundLog       = $refundSonLogMdl->getPackOrderRefundMoney($mainOrderInfo['ordernum']);
        if ($refundLog) {
            return $this->returnData(204, '目前只开放子票使用一次功能');
        }

        if ($mainOrderInfo['aids_price']) {
            $priceArr = explode(',', $mainOrderInfo['aids_price']);
            $num      = count($priceArr);
            if ($priceArr[$num - 1] * $mainOrderInfo['tnum'] - $refundLog < $totalMoney) {
                return $this->returnData(204, '超过分销商最大可退票金额');
            }
        } elseif ($mainOrderInfo['totalmoney'] - $refundLog < $totalMoney) {
            return $this->returnData(204, '超过游客最大可退票金额');
        }
        //自供自销，不做退款记录，不需要做退款预判断
        if ($mainOrderInfo['paymode'] == 3) {
            return $this->returnData(200, '主票是自供应产品,自供应产品不需要退钱');
        }

        //是否供应商开启酒店预定确定拒绝的订单
        $specialData = [
            'is_pack_refund' => true,
            'pack_money'     => $totalMoney,
        ];
        //账户余额预检测
        $moneyRefundModel = new Refund();
        $microtime       = str_pad(str_replace('.', '', microtime(true)), 14, '0');
        $reqSerialNumber = strval("{$mainOrderInfo['ordernum']}_{$microtime}");
        $moneyRefundPreCheckRes = $moneyRefundModel->orderTradeRefundPreCheck($mainOrderInfo['ordernum'],
            $reqSerialNumber, $mainOrderInfo['aid'], 2, 0, 0, $mainOrderInfo['tnum'],
            $mainOrderInfo['totalmoney'], $mainOrderInfo['totalmoney'], time(), true, $opId,
            0, '', 0, 0, $specialData);
        if ($moneyRefundPreCheckRes['code'] != 200) {
            return $this->returnData($moneyRefundPreCheckRes['code'], $moneyRefundPreCheckRes['msg']);
        }
        return $this->returnData(200, '检测通过');
    }

    /***
     * 团队订单取消基础验证
     *
     * @param $orderArr  array   订单号
     * @param $opId  int        员工id
     * @param $parentId  int    商家id
     * @param $numMapping  array  退票限制判断
     * @param $isNeedAuth  bool  是否要判断权限
     *
     * @return array
     */
    public function teamOrderCheckDataToOtherChannel($orderArr, $opId, $parentId, $numMapping = [], $isNeedAuth = true)
    {
        if (empty($orderArr)) {
            return $this->returnData(203, '订单号不能为空');
        }
        $app       = new Application();
        $orderInfo = $app->order_class->getTeamModel()->getMainOrderInfoBySonOrder($orderArr[0]);
        if (empty($orderInfo)) {
            return $this->returnData(204, '订单不属于团单');
        }

        return $this->teamOrderCheckData($orderArr, $orderInfo['main_ordernum'], $opId, $parentId, $numMapping, $isNeedAuth);
    }

    /***
     * 团队订单取消基础验证
     * <AUTHOR>
     * @date   2019-08-30
     *
     * @param $orderArr  array   订单号
     * @param $teamOrder  int    团单
     * @param $opId  int        员工id
     * @param $parentId  int    爸爸id
     * @param $numMapping  array  退票限制判断
     * @param $isNeedAuth  bool  是否要判断权限
     *
     * @return array
     */
    public function teamOrderCheckData($orderArr, $teamOrder, $opId, $parentId, $numMapping = [], $isNeedAuth = true)
    {
        $app       = new Application();
        $orderData = $app->order_class->getTeamModel()->getMainOrderInfoByOrderNum($teamOrder);
        if (!in_array($orderData['status'], [1, 4])  && $orderData['aid'] == $parentId) {
            return $this->returnData(203, '团队订单未确认，无法修改');
        }
        //获取订单信息
        $orderModel  = new OrderTools();
        $field       = 'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid,ss.ordermode';
        $detailField = 'de.concat_id,de.aids';
        $applyField  = 'apply.refund_num,apply.verified_num,apply.origin_num';
        $orderInfo   = $orderModel->getOrderInfo($orderArr, $field, $detailField, false, $applyField);
        if (count($orderInfo) != count($orderArr)) {
            return $this->returnData(203, '订单错误');
        }
        $mode = false;
        if (empty($numMapping)) {
            $mode = true;  //取消模式
        }
        $newNumMapping = [];
        foreach ($orderInfo as $key => $value) {
            //判断取消权限
            if ($isNeedAuth) {
                $isHaveAuth = $this->_isHaveRefundAuth($value, $opId, $parentId);
                if (!$isHaveAuth) {
                    return $this->returnData(204, '没有退票权限');
                } elseif (!in_array($value['ordermode'], [24, 44])) {
                    return $this->returnData(204, '不是团单,没有退票权限');
                }
            }

            if ($mode) {
                $newNumMapping[$value['ordernum']] = $value['verified_num'];
            } else {   //修改模式
                $cancelNum    = $numMapping[$value['ordernum']]['num'] ?? 0;
                $newNumMapping[$value['ordernum']] = $value['tnum'] - $cancelNum;
            }
        }
        if (!empty($newNumMapping)) {
            //团队限制逻辑
            $TeamLimitBiz   = new \Business\TeamOrder\TeamLimit();
            $checkTeamLimit = $TeamLimitBiz->checkTeamOrderEditLimit($teamOrder, $orderData['aid'], $orderData['fid'], $newNumMapping, $mode);
            if ($checkTeamLimit['is_error']) {
                return $this->returnData(205, $checkTeamLimit['error_info']);
            }
        }
        $result = [
            'orderInfo' => $orderInfo,
        ];

        return $this->returnData(200, 'gogogo', $result);
    }

    /***
     * 更新团队状态
     * <AUTHOR>
     * @date   2019-08-30
     *
     * @param $teamOrder  string   团队订单号
     *
     * @return array
     */
    private function updateTeamOrderInfo($teamOrder)
    {
        $app   = new Application();
        $order = $app->order;
        $res   = $order->modifyTeamTicketNum($teamOrder, 0);
        if (!$res) {
            return $this->returnData(400, '更新团队订单人数失败');
        }

        return $this->returnData(200, 'gogogo');
    }

    /***
     * 取消的观察日志，没问题就删除
     * <AUTHOR>
     * @date   2019-08-30
     *
     * @param $orderNum  string   订单号
     * @param $num  int    数量
     *
     * @return array
     */
    public static function cancelLog($data, $res, $remark = '')
    {
        @pft_log('cancel/linchen',
            '参数:' . json_encode($data) . '返回:' . json_encode($res, JSON_UNESCAPED_UNICODE) . '备注:' . $remark);
    }

    /***
     * 获取联票要审核的流水号
     * <AUTHOR>
     * @date   2019-09-12
     *
     * @param $linkList  array 联票列表
     *
     * @return array
     */
    private function _getAuditOrderInfo($linkList)
    {
        if (empty($linkList)) {
            return [];
        }
        $arrOrderNum = array_column($linkList, 'ordernum');
        $auditMdl    = new OrderRefer();
        $auditInfo   = $auditMdl->getRefundAuditList($arrOrderNum);

        foreach ($linkList as $key => $value) {
            $linkList[$key]['reqSerialNumber'] = '';
            foreach ($auditInfo as $k => $v) {
                if ($value['ordernum'] == $k) {
                    $linkList[$key]['reqSerialNumber'] = $v['remote_sn'];
                    break;
                }
            }
        }

        return $linkList;
    }

    /***
     * 获取普通订单号的流水号
     * <AUTHOR>
     * @date   2019-09-12
     *
     * @param $ordernum  string 联票列表
     *
     * @return array
     */
    public function getAuditInfoByOrder($ordernum)
    {
        $reqSerialNumber = '';
        $auditMdl        = new RefundAuditModel();
        $auditInfo       = $auditMdl->getUnderAuditInfo($ordernum, 0, [2, 3]);
        if ($auditInfo) {
            $reqSerialNumber = $auditInfo['remote_sn'];
        }

        return $reqSerialNumber;
    }

    /***
     * 获取联票要审核的流水号
     * <AUTHOR>
     * @date   2019-09-12
     *
     * @param $orderInfo  array 订单详情
     * @param $ordernum  string 订单号
     * @param $opId  int 订单号
     * @param $cancelChannel  int 渠道
     * @param $cancelRemark  string  备注
     * @param $reqSerialNumber  string  备注
     * @param $isNeedAudit  bool  是否是需要审核
     * @param $linkFilter  bool  是否要取消联票已使用的
     * @param $isFailRollBack  bool  是否是失败回滚的
     * @param $isAuditPass  bool  是否是审核通过的
     * @param  array  $otherParams  审核通过后其他的取消参数
     *
     * @return array
     */
    private function _handleCancel($orderInfo, $ordernum, $opId, $cancelChannel, $cancelRemark = '', $reqSerialNumber, $isNeedAudit = true, $linkFilter = false, $isFailRollBack = false, $isAuditPass = false, $cancelPersonArr = [], $cancelNum = -1, $otherParams = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return self::_handleCancelForApproval($orderInfo, $ordernum, $opId, $cancelChannel, $cancelRemark,
                $reqSerialNumber, $isNeedAudit, $linkFilter, $isFailRollBack, $isAuditPass, $cancelPersonArr, $cancelNum, $otherParams);
        }
        if (empty($orderInfo)) {
            return $this->returnData(203, '订单错误');
        }
        $orderInfo['reqSerialNumber'] = $reqSerialNumber;
        $cancelOrderList              = [$orderInfo];
        //如果是联票的话，而且是取消主票的话，把所有的票都取消掉
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent && $linkParent == $ordernum) {
            $orderModel    = new OrderTools();
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                if ($linkFilter) {
                    foreach ($linkOrderList as $linkKey => $linkValue) {
                        if ($linkValue['ordernum'] != $ordernum && $linkValue['status'] == 1) {
                            unset($linkOrderList[$linkKey]);
                        }
                    }
                    $linkOrderList = array_values($linkOrderList);
                }
                $cancelOrderList = $this->_getAuditOrderInfo($linkOrderList);
            }
        }
        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];
        foreach ($cancelOrderList as $subIndex => $subOrderInfo) {
            //调用统一的退票接口
            $subOrdernum      = $subOrderInfo['ordernum'];
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            //$cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_rollback'        => $isFailRollBack,
                'is_audit_pass'      => $isAuditPass,
                'is_refund_deposit'  => $otherParams['audit_data']['is_refund_deposit'],
                'is_refund_over_pay' => $otherParams['audit_data']['is_refund_over_pay'],
                'cancel_audit_remark' => $otherParams['audit_data']['cancel_audit_remark'] ?? '',
                'discount' => $otherParams['audit_data']['discount'] ?? '',
                'after_sale_num'=>$otherParams['audit_data']['after_sale_num'],
                'batch_refund_more_idx'=>$otherParams['audit_data']['batch_refund_more_idx']
            ];
            $reqSerialNumber  = $subOrderInfo['reqSerialNumber'];
            $moreData = [];
            if (isset($otherParams['subSid']) && isset($otherParams['subOpId'])){
                $moreData['subSid'] = $otherParams['subSid'];
                $moreData['subOpId'] = $otherParams['subOpId'];
            }
            $refundRes = Helpers::platformRefund($subOrdernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);
            self::cancelLog(func_get_args(), $refundRes, '_handleCancel');
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                //记录第一个票的退票流水信息
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(0, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    private function _handleCancelForApproval($orderInfo, $ordernum, $opId, $cancelChannel, $cancelRemark = '', $reqSerialNumber, $isNeedAudit = true, $linkFilter = false, $isFailRollBack = false, $isAuditPass = false, $cancelPersonArr = [], $cancelNum = -1, $otherParams = [])
    {
        if (empty($orderInfo)) {
            return $this->returnData(203, '订单错误');
        }
        $orderInfo['reqSerialNumber'] = $reqSerialNumber;
        $cancelOrderList              = [$orderInfo];
        //如果是联票的话，而且是取消主票的话，把所有的票都取消掉
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent && $linkParent == $ordernum) {
            $orderModel    = new OrderTools();
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                if ($linkFilter) {
                    foreach ($linkOrderList as $linkKey => $linkValue) {
                        if ($linkValue['ordernum'] != $ordernum && $linkValue['status'] == 1) {
                            unset($linkOrderList[$linkKey]);
                        }
                    }
                    $linkOrderList = array_values($linkOrderList);
                }
                $cancelOrderList = $this->_getAuditOrderInfo($linkOrderList);
            }
        }
        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];
        $moreData = [];
        if (isset($otherParams['subSid']) && isset($otherParams['subOpId'])){
            $moreData['subSid'] = $otherParams['subSid'];
            $moreData['subOpId'] = $otherParams['subOpId'];
        }
        if (isset($otherParams['isMainRefund'])){
            $moreData['isMainRefund'] = $otherParams['isMainRefund'];
        }
        if (isset($otherParams['isApprovalFund'])){
            $moreData['isApprovalFund'] = $otherParams['isApprovalFund'];
        }
        if (isset($otherParams['operatorID'])){
            $moreData['operatorID'] = $otherParams['operatorID'];
        }
        if (isset($otherParams['operatorID'])){
            $moreData['approvalComment'] = $otherParams['approvalComment'];
        }
        if (isset($otherParams['processInstanceId'])){
            $moreData['processInstanceId'] = $otherParams['processInstanceId'];
        }
        if (isset($otherParams['isThirdAuditRes'])){
            $moreData['isThirdAuditRes'] = $otherParams['isThirdAuditRes'];
        }
        foreach ($cancelOrderList as $subIndex => $subOrderInfo) {
            //调用统一的退票接口
            $subOrdernum      = $subOrderInfo['ordernum'];
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            //$cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_rollback'        => $isFailRollBack,
                'is_audit_pass'      => $isAuditPass,
                'is_refund_deposit'  => $otherParams['audit_data']['is_refund_deposit'],
                'is_refund_over_pay' => $otherParams['audit_data']['is_refund_over_pay'],
                'cancel_audit_remark' => $otherParams['audit_data']['cancel_audit_remark'] ?? '',
                'discount' => $otherParams['audit_data']['discount'] ?? '',
                'after_sale_num'=>$otherParams['audit_data']['after_sale_num'],
                'batch_refund_more_idx'=>$otherParams['audit_data']['batch_refund_more_idx']
            ];
            $reqSerialNumber  = $subOrderInfo['reqSerialNumber'];
            $refundRes = Helpers::platformRefund($subOrdernum, $cancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr, $moreData);
            self::cancelLog(func_get_args(), $refundRes, '_handleCancel');
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                //记录第一个票的退票流水信息
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(0, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    private function _handleRefund($orderInfo, $ordernum, $cancelList, $opId, $cancelChannel, $touristList = [], $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $isAuditPass = false, $otherParams = [])
    {
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($ordernum);
        if($isWhite){
            return self::_handleRefundForApproval($orderInfo, $ordernum, $cancelList, $opId, $cancelChannel,
                $touristList, $cancelRemark, $reqSerialNumber, $isNeedAudit, $isAuditPass, $otherParams);
        }
        //需要取消的订单列表
        $allOrdernum = [$ordernum];
        //如果是联票的话，获取所有的子票
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent) {
            $orderModel    = new OrderTools();
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                $allOrdernum = array_column($linkOrderList, 'ordernum');
            }
        }

        //判断需要退票的订单是否都是属于该订单
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            if (!in_array($subOrdernum, $allOrdernum)) {
                return $this->returnData(204, '退票参数错误');
            }
        }

        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $subIndex      = 0;
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];

        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            //调用统一的退票接口
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [
                'person_id_list' => $touristList,
            ];
            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_audit_pass'      => $isAuditPass,
                'is_refund_deposit'  => $otherParams['audit_data']['is_refund_deposit'],
                'is_refund_over_pay' => $otherParams['audit_data']['is_refund_over_pay'],
                'cancel_audit_remark' => $otherParams['audit_data']['cancel_audit_remark'] ?? '',
                'discount' => $otherParams['audit_data']['discount'] ?? '',
            ];

            $refundRes = Helpers::platformRefund($subOrdernum, $subCancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            self::cancelLog(func_get_args(), $refundRes, '_handleRefund');
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }

            $subIndex += 1;
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(205, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    private function _handleRefundForApproval($orderInfo, $ordernum, $cancelList, $opId, $cancelChannel, $touristList = [], $cancelRemark = '', $reqSerialNumber = '', $isNeedAudit = true, $isAuditPass = false, $otherParams = [])
    {
        //需要取消的订单列表
        $allOrdernum = [$ordernum];
        //如果是联票的话，获取所有的子票
        $linkParent = $orderInfo['concat_id'] ? strval($orderInfo['concat_id']) : '';
        if ($linkParent) {
            $orderModel    = new OrderTools();
            $linkOrderList = $orderModel->getLinkOrdersInfo($linkParent,
                'ss.ordernum,ss.tnum,ss.status,ss.member,ss.aid');

            if ($linkOrderList) {
                $allOrdernum = array_column($linkOrderList, 'ordernum');
            }
        }

        //判断需要退票的订单是否都是属于该订单
        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            if (!in_array($subOrdernum, $allOrdernum)) {
                return $this->returnData(204, '退票参数错误');
            }
        }

        //返回说明：只要有一个票取消成功就返回成功
        //同时将每个票的退票情况返回
        $subIndex      = 0;
        $cancelRes     = [];
        $refundSuccArr = [];
        $successCode   = 200;

        $isCancelFail  = false;
        $cancelFailArr = [];
        $moreData = [];
        if ($otherParams['subSid'] && $otherParams['subOpId']){
            $moreData['subSid'] = $otherParams['subSid'];
            $moreData['subOpId'] = $otherParams['subOpId'];
        }
        if (isset($otherParams['isMainRefund'])){
            $moreData['isMainRefund'] = $otherParams['isMainRefund'];
        }
        if (isset($otherParams['isApprovalFund'])){
            $moreData['isApprovalFund'] = $otherParams['isApprovalFund'];
        }
        if (isset($otherParams['operatorID'])){
            $moreData['operatorID'] = $otherParams['operatorID'];
        }
        if (isset($otherParams['operatorID'])){
            $moreData['approvalComment'] = $otherParams['approvalComment'];
        }
        if (isset($otherParams['processInstanceId'])){
            $moreData['processInstanceId'] = $otherParams['processInstanceId'];
        }

        foreach ($cancelList as $subOrdernum => $subCancelNum) {
            //调用统一的退票接口
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => $cancelRemark];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [
                'person_id_list' => $touristList,
            ];
            $cancelSpecialArr = [
                'is_need_audit'      => $isNeedAudit,
                'is_audit_pass'      => $isAuditPass,
                'is_refund_deposit'  => $otherParams['audit_data']['is_refund_deposit'],
                'is_refund_over_pay' => $otherParams['audit_data']['is_refund_over_pay'],
                'cancel_audit_remark' => $otherParams['audit_data']['cancel_audit_remark'] ?? '',
                'discount' => $otherParams['audit_data']['discount'] ?? '',
            ];

            $refundRes = Helpers::platformRefund($subOrdernum, $subCancelNum, $opId, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr,$moreData);
            self::cancelLog(func_get_args(), $refundRes, '_handleRefund');
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }

            $subIndex += 1;
        }

        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(205, $cancelFailMsg, $resData);
        } else {
            //取消成功
            $resData = [
                'req_serial_number'  => $refundSuccArr['req_serial_number'],
                'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
                'success_order_list' => $cancelRes,
            ];

            return $this->returnData($successCode, '退票成功', $resData);
        }
    }

    /**
     * 订单过期批量操作
     * <AUTHOR>
     * @date   2019-10-15
     *
     * @param  int  $sid  `name:sid|title:供应商id|min:1|binding:required`
     * @param  int  $oper  `name:oper|title:操作人idmin:1|binding:required`
     * @param  int  $type  `name:handleType|title:操作类型|min:1|max:3|binding:required|error:操作类型错误【1验证2完结3取消】`
     * @param  array  $orderArr  `name:orderArr|title:过期的订单数组|arrayVal:_|binding:required`
     *
     * @return array
     */
    public function batchHandleForExpired(int $sid, int $oper, int $handleType, array $orderArr)
    {
        $checkRes = $this->_validateRequest();
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }

        //实例化模型
        $orderTool   = new \Model\Order\OrderTools();
        $ticketModel = new \Model\Product\Ticket();
        $landModel   = new \Model\Product\Land();

        //需要获取票ID的订单
        $orderTidInfos = [];

        //已经获取过子票的联票的订单 - 防止联票处理多次
        $haveQuerySumOrders = [];

        //如果订单是联票的，要将所有的订单找出
        $handleOrderArr = [];

        //需要获取验证票数的订单
        $queryCheckedArr = [];

        //验证票数数组
        $checkedNumArr = [];

        //获取订单信息
        $orderList = $orderTool->getOrderInfo($orderArr, 'ss.ordernum,ss.tid,ss.lid,ss.status', 'de.concat_id');
        $orderList = $orderList ?: [];

        foreach ($orderList as $item) {
            $ordernum = strval($item['ordernum']);
            $tid      = $item['tid'];
            $lid      = $item['lid'];
            $concatId = $item['concat_id'];
            $status   = $item['status'];

            //将非过期状态的订单过滤
            if ($status != 2 && $status != '已过期') {
                continue;
            }

            if ($concatId) {
                //联票之前已经处理过
                if (in_array($concatId, $haveQuerySumOrders)) {
                    continue;
                }

                $subOrders = $orderTool->getLinkSubOrder($ordernum);
                if ($subOrders) {
                    $orders = $subOrders; //array_column($subOrders, 'orderid');

                    $handleOrderArr[$concatId] = $orders;
                    $haveQuerySumOrders[]      = $concatId;

                    $queryCheckedArr = array_merge($queryCheckedArr, $orders);
                } else {
                    //查询不到联票信息
                    $orderTidInfos[$ordernum]  = ['tid' => $tid, 'lid' => $lid];
                    $handleOrderArr[$ordernum] = [$ordernum];

                    $queryCheckedArr = array_merge($queryCheckedArr, [$ordernum]);
                }
            } else {
                $orderTidInfos[$ordernum]  = ['tid' => $tid, 'lid' => $lid];
                $handleOrderArr[$ordernum] = [$ordernum];

                $queryCheckedArr = array_merge($queryCheckedArr, [$ordernum]);
            }
        }

        //删除不需要的数据
        unset($orderList);

        //根据订单号找到相应的票类ID和验证数据
        $tmp = $orderTool->getOrderListNew($queryCheckedArr, $field = 'ordernum,tid,lid,status', false, false,
            'refund_num,verified_num,origin_num');
        if ($tmp) {
            foreach ($tmp as $item) {
                $orderTidInfos[$item['ordernum']] = [
                    'tid'    => $item['tid'],
                    'lid'    => $item['lid'],
                    'status' => $item['status'],
                ];
                //如果是批量取消订单的话，需要获取订单的验证票数传入到取消接口中
                //TODO:这个是取消接口写得太奇葩了，后期会进行修改
                $checkedNumArr[$item['ordernum']] = isset($item['verified_num']) ? intval($item['verified_num']) : 0;
            }
        }

        //相应订单的票类信息
        $tidArr = array_column($orderTidInfos, 'tid');
        foreach ($orderTidInfos as $ordernum => $val) {
            $tid = $val['tid'];
            $lid = $val['lid'];

            $landInfo   = $landModel->getLandInfo($lid, false, 'p_type');
            $ticketInfo = $ticketModel->getTicketInfoById($tid,
                'id,apply_did,expire_action,expire_action_fee');

            if (!$landInfo || !$ticketInfo) {
                $orderTidInfos[$ordernum]['ticket'] = false;
            } else {
                $ticketInfo['p_type']               = $landInfo['p_type'];
                $orderTidInfos[$ordernum]['ticket'] = $ticketInfo;
            }
        }

        $handleModel = new \Model\Order\OrderHandler();

        //返回数据
        $resData = ['success' => [], 'fail' => []];

        // 切换门票获取第三方系统信息
        $otaProductBiz    = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTidArr($tidArr);

        foreach ($handleOrderArr as $ordernum => $orders) {
            foreach ($orders as $subOrder) {

                //如果联票里面的其他票是已经验证，取消，完结的状态，就不进行处理了
                if (isset($orderTidInfos[$subOrder]) && isset($orderTidInfos[$subOrder]['status'])) {
                    $tmpStatus = $orderTidInfos[$subOrder]['status'];
                    if (in_array($tmpStatus, [1, 3, 8])) {
                        continue;
                    }
                }

                // $resData[$subOrder] = ['status' => 1, 'msg' => ''];

                //订单的权限判断
                $ticketInfo = isset($orderTidInfos[$subOrder]['ticket']) ? $orderTidInfos[$subOrder]['ticket'] : false;

                if (!$ticketInfo) {
                    $resData['fail'][] = [$subOrder, "订单[{$subOrder}]票类数据缺失"];
                    continue;
                }

                $ptype    = $ticketInfo['p_type'];
                $Mdetails = empty($thirdBindInfoArr[$ticketInfo['id']]['Mdetails']) ? 0 : $thirdBindInfoArr[$ticketInfo['id']]['Mdetails'];
                $applyDid = $ticketInfo['apply_did'];

                //判断是不是直接供应商
                if ($applyDid != $sid) {
                    $resData['fail'][] = [$subOrder, "订单[{$subOrder}]产品发布方才有过期操作的权限"];
                    continue;
                }

                if ($handleType == 2) {
                    //完结操作，对接第三方系统的订单，不能做自动处理
                    if ($ptype != 'H' && $Mdetails == 1) {
                        $resData['fail'][] = [$subOrder, "订单[{$subOrder}]对接第三方系统，不做处理"];
                        continue;
                    }
                }

                //针对不同类型进行处理
                if ($handleType == 1) {
                    //验证
                    $res = $handleModel->CheckOrderSimply($subOrder, $oper, $order_info = null, $memo = '批量验证',
                        $source = 1);
                    if ($res === true) {
                        $resData['success'][] = $subOrder;
                    } else {
                        $msg               = $res['msg'];
                        $resData['fail'][] = [$subOrder, "订单[{$subOrder}]验证失败 - {$msg}"];
                    }
                } else if ($handleType == 2) {
                    //完结
                    $res = $this->finishOrder($subOrder, $oper, 16, '批量完结');

                    if ($res['code'] == 200) {
                        $resData['success'][] = $subOrder;
                    } else {
                        $resData['fail'][] = [$subOrder, "订单[{$subOrder}]完结失败"];
                    }
                } else {
                    //取消
                    $res = $this->baseCancel($subOrder, $oper, $sid, OrderConst::PC_CANCEL, '订单过期批量取消');
                    if ($res['code'] == 200) {
                        $resData['success'][] = $subOrder;
                    } else {
                        $resData['fail'][] = [$subOrder, "订单[{$subOrder}]取消失败 - {$res['msg']}"];
                    }
                }
            }
        }

        return $this->returnData(200, '', $resData);
    }

    /**
     * 完结订单（已过期的订单）
     * <AUTHOR>
     * @date   2020-02-20
     *
     * @param  string  $ordernum  订单号
     * @param  int|integer  $operId  操作人id
     * @param  int|integer  $source  来源
     * @param  string  $memo  备注
     *
     * @return array
     */
    public function finishOrder(string $ordernum, int $operId = 0, int $source = 0, string $memo = '', $moreData = [])
    {
        if (!$ordernum) {
            return $this->returnData(204, '参数错误');
        }

        //新版优惠
        $orderTicketDiscounts = new OrderTicketDiscountsApi();
        // 状态：0.全部 1.未使用 2.已使用 3.使用中 4.已取消
        $orderDiscountsRes = $orderTicketDiscounts->list($ordernum, 1);
        if ($orderDiscountsRes['code'] != 200) {
            return $this->returnData(self::CODE_INVALID_REQUEST, "订单号查询优惠详情失败，{$ordernum}");
        }
        $orderDiscounts = $orderDiscountsRes['data'] ?: [];
        //实际优惠金额
        $ticketDiscountsMoney = array_sum(array_column($orderDiscounts, 'couponPrice'));
        $serialNums = array_column($orderDiscounts, 'serialNum');
        $options = [
            'ticketDiscountsMoney' => $ticketDiscountsMoney,
            'allVerify' => true,
            'serialNums' => $serialNums,
        ];

        $api    = new \Business\JavaApi\Order\OrderHandle();
        $result = $api->finishOrder($ordernum, $operId, $source, $memo);

        if ($result['code'] == 200) {
            $orderField = 'status, tnum, tid, salerid';
            $applyField = 'verified_num, origin_num, refund_num';
            $orderInfo  = $this->_orderToolsModel->getOrderInfo($ordernum, $orderField, false, false, $applyField);

            //添加订单追踪记录 (下一版将追踪记录的写入移到java)
            $finishNum = intval($orderInfo['tnum'] - $orderInfo['verified_num']);
            $finishNum = max(0, $finishNum);
            $extContent = [];
            if ($moreData['subSid'] && $moreData['subOpId']){
                $extContent['subSid'] = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            $trackId = $this->_orderTrackModel->addTrack($ordernum, $action = 17, $orderInfo['tid'], $finishNum, 0, $source,
                $terminal_id = 0,
                $branch_terminal = 0, $id_card = '', $operId, $orderInfo['salerid'], $create_time = '', $memo, 0, false, $extContent);

//            //分销专员订单取消佣金结算业务
//            $multiDistData = [
//                'action' => 'finsh',
//                'data'   => ['ordernum' => $ordernum],
//            ];
//            \Library\Resque\Queue::push('independent_system', 'MultiDist_Job', $multiDistData);

            //订单完结发送解冻通知
            $orderAsyncBiz = new OrderAsync();
            $orderAsyncBiz->sendLastStageUnfreezeNotify($orderInfo, $finishNum, $operId, strval($trackId), $options);

        }

        return $result;
    }

    /**
     * 更新主订单的验证状态
     * 当联票的子票取消之后，如果主票已经验证完了处于部分验证状态，而且所有子票都已经验证完了
     * 就需要将主票的状态修改为已经验证
     *
     * <AUTHOR> (2020.2.21 wengbin 从model搬过来)
     * @date   2017-03-15
     *
     * @param  string  $concatId  主订单号
     *
     * @return bool
     */
    public function updatePackOrder($concatId)
    {
        $concatId = strval($concatId);
        if (!$concatId) {
            return false;
        }

        $orderTools = new \Model\Order\OrderTools('slave');
        $orderInfo  = $orderTools->getOrderInfo($concatId, 'ordernum, status, tnum', false, false,
            'verified_num, origin_num');

        //订单查不到或是不是处于分批验证状态
        if (!$orderInfo || $orderInfo['status'] != 7) {
            return false;
        }

        //如果主票还有部分没有验证
        if ($orderInfo['tnum'] < $orderInfo['verified_num']) {
            return false;
        }

        //$field              = "orderid";
        //$subOrderQueryModel = new SubOrderQuery();
        //$orderList          = $subOrderQueryModel->getInfoInDetailByConcatId(
        //    $concatId,
        //    $field,
        //    ['orderid' => ['neq', $concatId]]
        //);
        $orderList   = [];
        $queryParams = [$concatId];
        $checkRes    = \Business\JavaApi\Order\Query\Container::query('orderDetails', 'getLinkOrdersInfo',
            $queryParams);
        if ($checkRes['code'] == 200 && !empty($checkRes['data'])) {
            $sonOrderIdArr = array_column($checkRes['data'], 'ordernum');
            foreach ($sonOrderIdArr as $key => $value) {
                if ($value == $concatId) {
                    unset($sonOrderIdArr[$key]);
                }
            }
            $sonOrderIdArr = array_values($sonOrderIdArr);

            $queryParams = [$sonOrderIdArr];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail',
                'queryOrderDetailsByOrdernums',
                $queryParams);

            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $orderList = array_filter($queryRes['data']);
            }
        }

        $orderList    = $orderList ? $orderList : [];
        $subOrderList = array_column($orderList, 'orderid');
        if (!$subOrderList) {
            return false;
        }

        //所有的子票是不是已经验证了
        //$subOrderMain  = new \Model\Order\SubOrderQuery\SubOrderMain();
        //$checkCountArr = $subOrderMain->getOrderListByOrderAndStatus($subOrderList, [1, 3, 5, 6]);

        //订单查询迁移
        $orderMove     = new OrderQueryMove();
        $checkCountArr = $orderMove->getOrderListByOrderAndStatusNew($subOrderList, [1, 3, 5, 6]);

        $checkCount = count($checkCountArr);

        if ($checkCount >= count($subOrderList)) {
            //子票都已经验证了，将主票的状态修改为已经验证
            $data = ['status' => 1];

            $api = new \Business\JavaApi\Order\OrderInfoUpdate();
            $res = $api->baseOrderInfoUpdate($concatId, $data);

            return $res['code'] == 200;
        } else {
            return false;
        }
    }

    /**
     * 旧取消接口迁移过来的修改时间和电话
     * <AUTHOR>
     * @date   2020-02-27
     *
     * @param  int  $orderNum  订单号
     * @param  int  $opId  操作员
     * @param  string  $orderTel  订单电话
     * @param  string  $beginTime  开始时间
     * @param  string  $endTime  结束时间
     *
     * @return array
     */
    public function orderModifyTimeAndPhone($orderNum, $orderTel, $opId = 0)
    {
        $modifyLog = "订单号：{$orderNum} 电话：{$orderTel} 操作员:{$opId}";
        if (!$orderNum) {
            return $this->returnData(204, '参数错误');
        }
        if (!$orderTel) {
            return $this->returnData(204, '修改订单内容不能全部为空');
        }
        $handlerModel = new \Model\Order\OrderHandler();
        $orderInfo    = $handlerModel->getOrderInfo($orderNum, 'ordernum', false, false, false);
        if (!$orderInfo) {
            return $this->returnData(204, '订单不存在');
        }
        $lockKey    = "lock:Order_Change:{$orderNum}_{$opId}";
        $redisCache = Cache::getInstance('redis');
        $lock       = $redisCache->lock($lockKey, 1, 25);
        if (!$lock) {
            pft_log('open/lock', $lockKey);

            return $this->returnData(205, '提交数据频繁，请间隔60秒后再尝试提交');
        }
        $data['ordertel'] = $orderTel;

        $api = new \Business\JavaApi\Order\OrderHandle();
        $res = $api->orderChangeOrCancel($orderNum, 0, ['ordertel' => $orderTel]);

        $logData = [
            'msg' => $modifyLog,
            'res' => $res,
        ];
        pft_log('modify/order', json_encode($logData, JSON_UNESCAPED_UNICODE));
        if ($res['code'] == 200) {
            return $this->returnData(200, '修改成功');
        }

        return $this->returnData(204, '数据为空');
    }

    /**
     * 修改订单基础信息
     * <AUTHOR>
     * @date   2020-02-21
     *
     * @param  string  $ordernum  订单号
     * @param  int  $action  追踪表action
     * @param  array  $data  更新的数据（驼峰写法）
     * @param  int|integer  $operId  操作人id
     * @param  string  $memo  备注
     *
     * @return array
     */
    public function updateOrderBasicInfo(string $ordernum, int $action, array $data, int $operId = 0, string $memo = '', $moreData = [])
    {
        if (!$ordernum || !$action || !$data) {
            return $this->returnData(204, '参数错误');
        }

        $orderInfo = $this->_orderToolsModel->getOrderInfo($ordernum, 'tid,tnum');
        if (!$orderInfo) {
            return $this->returnData(204, '订单不存在');
        }

        $api    = new \Business\JavaApi\Order\OrderHandle();
        $result = $api->updateOrderBasicInfo($ordernum, $action, $data, $operId, $memo);
        if ($result['code'] == 200) {
            $extContent = [];
            if ($moreData['subSid'] && $moreData['subOpId']) {
                $extContent['subSid']  = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            //下个版本将追踪记录的写入移动到java
            $trackSql = $this->_orderTrackModel->addTrack($ordernum, $action,
                $orderInfo['tid'], $orderInfo['tnum'], $orderInfo['tnum'], 16, 0,
                0, '', $operId, '', '', $memo, 0, true, $extContent);
            if ($trackSql) {
                $terminalModel = new \Model\Order\TerminalOrderHandler();
                $terminalModel->execute($trackSql);
            }
        }

        return $result;
    }

    /**
     * 更新订单时间
     * <AUTHOR>
     * @date 2022/3/8
     *
     * @param  string  $ordernum  订单号
     * @param  int  $action
     * @param  array  $data
     * @param  int  $operId
     * @param  string  $memo
     *
     * @return array
     */
    public function updateOrderDateByParam(string $ordernum, int $action, array $data, int $operId = 0, string $memo = '', $moreData = [])
    {
        if (!$ordernum || !$action || !$data || !$operId) {
            return $this->returnData(204, '参数错误');
        }

        $orderInfo = $this->_orderToolsModel->getOrderInfo($ordernum, 'tid,tnum');
        if (!$orderInfo) {
            return $this->returnData(204, '订单不存在');
        }

        $api    = new \Business\JavaApi\Order\OrderHandle();
        $result = $api->updateOrderDateByParam($ordernum, $data, $operId, $moreData);

        if ($result['code'] == 200) {
            $extContent = [];
            if ($moreData['subSid'] && $moreData['subOpId']){
                $extContent['subSid'] = $moreData['subSid'];
                $extContent['subOpId'] = $moreData['subOpId'];
            }
            //下个版本将追踪记录的写入移动到java
            $trackSql = $this->_orderTrackModel->addTrack($ordernum, $action,
                $orderInfo['tid'], $orderInfo['tnum'], $orderInfo['tnum'], 16, 0,
                0, '', $operId, '', '', $memo, 0, true, $extContent);
            if ($trackSql) {
                $terminalModel = new \Model\Order\TerminalOrderHandler();
                $terminalModel->execute($trackSql);
            }
        }

        return $result;
    }

    /**
     * 订单改签操作  (目前不存在套票的改签)
     * <AUTHOR>
     * @date   2020-06-24
     *
     * @param  string  $ordernum  订单号
     * @param  string  $newDate  改签时间
     * @param  array  $orderInfo  订单信息
     * @param  array  $ticketInfo  票快照信息
     * @param  string  $source  来源
     * @param  string  $opId  操作员
     * @param  int  $shareTimeId  分时预约id
     * @param  string  $shareTimeStr  分时预约字符串
     *
     * @return array
     */
    private function _orderTicketChangeHandle($ordernum, $newDate, $orderInfo, $ticketInfo, $source, $opId, $shareTimeId = 0, $shareTimeStr = '', $remark = '', $moreData = [])
    {
        if (!$ordernum || !$newDate || empty($orderInfo) || empty($ticketInfo) || !$opId || !$source) {
            return $this->returnData(204, '参数错误');
        }
        $code        = 200;
        $msg         = '改签成功';
        $tid         = $orderInfo['tid'];
        $buyId       = $orderInfo['member'];
        $tnum        = $orderInfo['tnum'];
        $oldPlayTime = $orderInfo['playtime'];

        //获取对应下单渠道的分时预约库存销售渠道
        $orderConvertApi = new OrderParamConvert();
        $convertResult   = $orderConvertApi->getShopByChannel((int)$orderInfo['ordermode']);
        if ($convertResult['code'] != 200) {
            return $this->returnData(204, '销售渠道获取失败');
        }
        $channel = $convertResult['data'];

        $newTime            = strtotime($newDate);
        $isReservationOrder = false;
//        $productInfo        = json_decode($orderInfo['product_ext'], true) ?? [];
//        if (isset($productInfo['reservationOrder']) && $productInfo['reservationOrder'] == 1 && isset($productInfo['reservationOperateTime'])) {  //代表是预约改签的
//            $isReservationOrder = true;
//        }
        //还是走票属性吧
        if ($ticketInfo['pre_sale'] == 1 && $ticketInfo['is_online_reserve'] == 1) {  //代表是预约改签的
            $isReservationOrder = true;
        }
        $playData  = date('Y-m-d', $orderInfo['playtime']);
        $startData = date('Y-m-d', $orderInfo['begintime']);
        $endData   = date('Y-m-d', $orderInfo['endtime']);

        $action = 22;
        try {
            $serialNum = serialHashKey($ordernum);
            //获取单天库存看看有没有
            if ($isReservationOrder) {
                //分时预约改签时要把旧的数据传给java
                $extContent        = json_decode($orderInfo['ext_content'], true);
                $oldSectionTimeId  = $extContent['sectionTimeId'] ?? 0;
                $oldSectionTimeStr = $extContent['sectionTimeStr'] ?? "";
                //预约的
                $reservationStorageApi = new ReservationOrderStorage();
                $javaStorageRes        = $reservationStorageApi->getReservationStorageList($tid, $newDate, $newDate,
                    $channel);
                if ($javaStorageRes['code'] != 200) {
                    throw new \Exception("暂无预约库存");
                }
                if ($javaStorageRes['data'][$newDate]['sectionFlag'] && !$shareTimeId && !$shareTimeStr) {
                    throw new \Exception("分时预约id缺失");
                }
                $orderReservationApi  = new OrderReservation();
                $reservationChangeRes = $orderReservationApi->orderReservationChangePlayDate($ordernum, $tid, $buyId,
                    $channel, $source, $newDate, $oldPlayTime, $tnum, $serialNum, $opId, date('Y-m-d H:i:s'),
                    $shareTimeId, $shareTimeStr, $remark, $oldSectionTimeId, $oldSectionTimeStr, $moreData);
                if ($reservationChangeRes['code'] != 200) {
                    throw new \Exception($reservationChangeRes['msg']);
                }
                $saveData = [
                    'begintime' => $newDate,
                    'playtime'  => $newDate,
                    'endtime'   => $newDate,
                ];
                $playData  = $newDate;
                $startData = $newDate;
                $endData   = $newDate;
                if ($javaStorageRes['data'][$newDate]['sectionFlag']) {
                    $shareData           = [
                        'sectionTimeId'  => intval($shareTimeId),
                        'sectionTimeStr' => $shareTimeStr,
                    ];
                    $changeShareTimeData = [
                        'extContent' => json_encode($shareData),
                    ];
                }

                //开始更新订单的信息
                $result = $this->updateOrderBasicInfo($ordernum, $action, $saveData, $opId, $remark, $moreData);
                if ($result['code'] != 200) {
                    throw new \Exception("数据同步更新失败");
                }
            } else {     //普通的  （期票不是线上预约的这种不能改签）
                $isOpenShareTime = false;
                // 统一库存查询支持分销库存
                $aidsArr = $orderInfo['aids'] ? explode(',', $orderInfo['aids']) : [$orderInfo['apply_did']];
                if (!isset($aidsArr[1])) {
                    // visitors 1=游客 2=分销商 3=自供自销
                    $aidsArr[] = $orderInfo['visitors'] == 2 ? $orderInfo['member'] : $orderInfo['aid'];
                }
                $stockQueryApi = new StockQuery;
                $stockQueryRes = $stockQueryApi->queryRangeSkuAvailableStock($tid, $newDate, $newDate, $aidsArr[1], $aidsArr[0], true, $channel);
                if ($stockQueryRes['code'] != self::CODE_SUCCESS || empty($stockQueryRes['data'])) {
                    return $this->returnData(204, '暂无预约库存');
                }
                foreach ($stockQueryRes['data'] as $item) {
                    // 是否开启分时预约
                    $isOpenShareTime = !empty($item['sectionDateId']);
                }

                if ($isOpenShareTime) {    //分时的扣库存
                    $res = StorageApi::reinstateStorageDeductWithTimeId($ordernum, $serialNum, $tid, $tnum, $newDate,
                        $channel, $shareTimeId, $shareTimeStr);
                    if ($res == false) {
                        pft_log('storage/fail_deduction',
                            'orderid:' . $ordernum . ',tnum:' . $tnum . 'serialNum:' . $serialNum . 'channel:' . 5);
                        throw new \Exception("扣减库存失败");
                    }
                    $shareData           = [
                        'sectionTimeId'  => intval($shareTimeId),
                        'sectionTimeStr' => $shareTimeStr,
                    ];
                    $changeShareTimeData = [
                        'extContent' => json_encode($shareData),
                    ];
                }

                // 分销库存扣减所需参数构造
                $aidsArr = $orderInfo['aids'] ? explode(',', $orderInfo['aids']) : [$orderInfo['apply_did']];
                if (!isset($aidsArr[1])) {
                    // visitors 1=游客 2=分销商 3=自供自销
                    $aidsArr[] = $orderInfo['visitors'] == 2 ? $orderInfo['member'] : $orderInfo['aid'];
                }
                $reinstateData = [];
                if (in_array($orderInfo['product_type'], ['A', 'F']) && $aidsArr) {
                    $itemTagDate = date('Ymd', strtotime($newDate));
                    $reinstateData = [
                        'changeLock' => false,
                        'userList' => [['sid' => $aidsArr[0], 'fid' => $aidsArr[1]]],
                        'itemTag' => "SKU_{$orderInfo['tid']}_{$itemTagDate}",
                        'changeNum' => $tnum,
                        'storageDate' => $newDate,
                        'productType' => 2
                    ];
                }

                //库存扣减操作
                $res = StorageApi::deductionStorage($ordernum, $serialNum, $tid, $tnum,
                    $newDate, $reinstateData);
                if ($res == false) {
                    pft_log('storage/fail_deduction',
                        'orderid:' . $ordernum . ',tnum:' . $tnum . 'serialNum:' . $serialNum);
                    throw new \Exception("扣减库存失败");
                }

                // 分销库存退回所需参数构造
                $reinstateData = [];
                if (in_array($orderInfo['product_type'], ['A', 'F']) && $aidsArr) {
                    $storageDate = date('Y-m-d', strtotime($ticketInfo['pre_sale'] == 1 ? $orderInfo['ordertime'] : $orderInfo['playtime']));
                    $itemTagDate = date('Ymd', strtotime($ticketInfo['pre_sale'] == 1 ? $orderInfo['ordertime'] : $orderInfo['playtime']));
                    $reinstateData = [
                        'changeLock' => false,
                        'userList' => [['sid' => $aidsArr[0], 'fid' => $aidsArr[1]]],
                        'itemTag' => "SKU_{$orderInfo['tid']}_{$itemTagDate}",
                        'changeNum' => $tnum,
                        'storageDate' => $storageDate,
                        'productType' => 2
                    ];
                }

                //判断之前是不是分时预约的要去退库存
                $refundBiz = new Refund();
                $refundBiz->orderRefundStorage($ordernum, $tnum, $opId, 'ticket_change', [], $moreData['subSid'], $moreData['subOpId'], $reinstateData);

                //游玩时间和有效期处理
                $dateResult = $this->updateOrderDateByParam($ordernum, $action, ['orderDate' => $newDate], $opId, $remark, $moreData);
                if ($dateResult['code'] != 200 || empty($dateResult['data'])) {
                    throw new \Exception($dateResult['msg'] ?? '日期处理失败');
                }
                $saveData = [
                    'begintime' => date("Y-m-d", $dateResult['data']['begintime'] ?? 0),
                    'playtime'  => date("Y-m-d", $dateResult['data']['playtime'] ?? 0),
                    'endtime'   => date("Y-m-d", $dateResult['data']['endtime'] ?? 0),
                ];

                $playData  = $saveData['playtime'];
                $startData = $saveData['begintime'];
                $endData   = $saveData['endtime'];
            }

            //同步order_refer
            //$res = $this->_OrderHandlerModel->updateReferOrder($ordernum, $saveData);

            $queryParams = [$ordernum, $playData, $startData, $endData];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('OrderReferModify',
                'modifyOrderReferPlayTimeAndValidityByOrderNum', $queryParams);

            $res = false;
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $res = true;
            }

            if (!$res) {
                $errorData = [
                    'refer' => '同步失败',
                    'data'  => $saveData,
                ];
                pft_log('order/ticketchange', json_encode($errorData));
            }
            if (!empty($changeShareTimeData)) {
                $orderDetailApi  = new OrderDetailUpdate();
                $updateDetailRes = $orderDetailApi->orderDetailInfoUpdate($ordernum, $changeShareTimeData, $opId, $moreData['subSid'], $moreData['subOpId']);
                if ($updateDetailRes['code'] != 200) {
                    $errorData = [
                        'detail' => '[0x30121]同步失败',
                        'ordernum' => $ordernum,
                        'data'   => $changeShareTimeData,
                        'opId' => $opId,
                        'moreData' => $moreData
                    ];
                    pft_log('order/ticketchange', json_encode($errorData));
                }
                if (!empty($shareData['sectionTimeStr'])) {
                    $feignClient = new FeignClient();
                    $editOrderSnapshotRes = $feignClient->editOrderSnapshot($ordernum, ['isSectionOrder' => true, 'sectionTime' => $shareData['sectionTimeStr']]);
                    if ($editOrderSnapshotRes['code'] != 200) {
                        $errorData = [
                            'detail' => '[0x30129]同步失败',
                            'ordernum' => $ordernum,
                            'data'   => $shareData,
                        ];
                        pft_log('order/ticketchange', json_encode($errorData));
                    }
                }
            }
            if (isset($saveData['endtime'])) {
                //更改门票码
                $ticketEndTiem              = $saveData['endtime'] . ' 23:59:59';
                $remark                     = $orderInfo['endtime'] . ' 23:59:59改签至' . $ticketEndTiem;
                $orderTouristInfoServiceApi = new OrderTouristInfoExtendService();
                $orderTouristInfoServiceApi->updateOrderTouristInfoExtendEndTime($ordernum, $ticketEndTiem, $opId,
                    $source, $remark, 0, 0, $moreData);
            }
            if (isset($ticketInfo['auto_checked_at']) && $ticketInfo['auto_checked_at'] != 0) {
                $this->_modifyAutoVerityTime($ordernum, $newDate, $ticketInfo['auto_checked_at']);
            }
            $jobData = [
                'action'   => 'order_ticket_change',
                'job_data' => [
                    'ordernum'        => $ordernum,
                    'tid'             => $tid,
                    'after_end_time'  => $saveData['endtime'] ?? '',
                    'after_play_time' => $saveData['playtime'] ?? '',
                ],
            ];
            $queueId = \Library\Resque\Queue::push('order', 'OrderNotice_Job', $jobData);
        } catch (\Exception $exception) {
            $msg  = $exception->getMessage();
            $code = 204;
        }

        return $this->returnData($code, $msg);
    }

    /**
     * 改签后动作
     * <AUTHOR>
     * @date   2020-06-24
     *
     * @param  string  $orderNum  订单号
     * @param  string  $afterEndTime  修改后的结束时间
     * @param  string  $afterPlayTime  修改后的游玩时间
     *
     * @return array
     */
    public function afterTicketChanging($orderNum, $afterEndTime = '', $afterPlayTime = '')
    {
        if (!$orderNum) {
            return $this->returnData(204, '参数错误');
        }
        try {
            $orderInfo = new OrderTools();
            $orderInfo = $orderInfo->getOrderInfo($orderNum,
                'ss.ordertel,ss.tnum,ss.tid,ss.ordername,ss.playtime,ss.pid', 'de.product_ext,de.ext_content');
            if (empty($orderInfo)) {
                throw new  \Exception("订单未找到");
            }

            $orderProductInfo = json_decode($orderInfo['product_ext'], true) ?? [];
            $isReservation    = isset($orderProductInfo['reservationOperateTime']) ? true : false;

            $extContent      = $orderInfo['ext_content'] ? json_decode($orderInfo['ext_content'], true) : [];
            $shareTimeStr    = isset($extContent['sectionTimeStr']) ? $extContent['sectionTimeStr'] : '';
            $reservationTime = $shareTimeStr ? $orderInfo['playtime'] . ' ' . $shareTimeStr : $orderInfo['playtime'];

            $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
            $ticketInfo         = $commodityTicketBiz->queryTicketInfoById($orderInfo['tid'], 'id,title,apply_did', '',
                'fax,title');
            if (empty($ticketInfo)) {
                throw new  \Exception("门票未找到");
            }
            if ($ticketInfo['ext']['sms_change_reserve_success'] == 1 && Tools::ismobile($ticketInfo['land']['fax']) && $isReservation) {  //短信通知下
                $title      = $ticketInfo['land']['title'] . '-' . $ticketInfo['ticket']['title'];
                $titleInfo = $title . '-' . $orderInfo['tnum'] . '张';
//                $sendData  = [$orderInfo['ordername'], $titleInfo, $reservationTime, $orderNum, $orderInfo['ordertel']];
                $sendData  = [$orderInfo['ordername'], $titleInfo, $reservationTime, $orderNum];
                $messageServiceApi = Container::pull(MessageService::class);
                $messageServiceApi->dispatchMessageSend(MessageService::V1, 'XZ_ORDER_RESERVATION_CHANGE_NOTICE', $ticketInfo['land']['fax'],
                    $sendData, $ticketInfo['ticket']['apply_did'], $orderNum, '订单预约改签通知供应商', '', '', 1, true, true, true);
            }
            if ($ticketInfo['ext']['wx_change_reserve_success'] == 1 && $isReservation) {  //微信通知下
                $time        = time();
                $data        = array(
                    'first'         => ['value' => '订单预约改签成功', 'color' => '#173177'],
                    'tradeDateTime' => ['value' => $time, 'color' => '#173177'],
                    'orderType'     => ['value' => '预约改签订单', 'color' => '#ff9900'],
                    'customerInfo'  => ['value' => $orderInfo['ordername'], 'color' => '#173177'],
                    'orderItemName' => [
                        'value' => $ticketInfo['land']['title'] . '-' . $ticketInfo['ticket']['title'],
                        'color' => '#173177',
                    ],
                    'orderItemData' => ['value' => '', 'color' => '#173177'],
                    'remark'        => ['value' => '订单号:' . $orderNum . '预约时间：' . $reservationTime, 'color' => ''],
                );
                $modelWechat = new WxMember('slave');
                $wxInfoList  = $modelWechat->getWxInfo($ticketInfo['ticket']['apply_did'], PFT_WECHAT_APPID, 1);
                if ($wxInfoList) {
                    foreach ($wxInfoList as $openid) {
                        if (empty($openid)) {
                            continue;
                        }
                        \Library\Resque\Queue::push(
                            'notify', 'WxNotify_Job',
                            [
                                'data'   => $data,
                                'openid' => $openid['fromusername'],
                                'tplid'  => 'NEW_ORDER',
                                'url'    => MOBILE_DOMAIN . 'c/order_detail.html?ordernum=' . \Library\MessageNotify\OrderNotify::url_sms($orderNum),
                                'color'  => '#FF0000',
                                'appid'  => PFT_WECHAT_APPID,
                            ]
                        );
                    }
                }
            }

            //更新下人脸的结束时间
            // ScenicLocalService有重复逻辑Business/Face/FaceOrderDateSyncService.class.php
            /*if ($afterEndTime || $afterPlayTime) {
                $faceCompareMdl = new FaceCompare();
                $bindFaceList   = $faceCompareMdl->getFaceByOrder($orderNum, 0);
                if ($bindFaceList) {  //更新人脸结束时间
                    $arrId             = array_column($bindFaceList, 'id');
                    // pft_log('debug/face_order_change_ticket', json_encode($ticketInfo));
                    // 人脸有效期需要加上入园后多少天有效的规则
                    $afterEndTimeDate  = date('Ymd', strtotime($afterEndTime)+$ticketInfo['ext']['first_into_verify_time']*60+86400);
                    // ;
                    $afterPlayTimeDate = date('Ymd', strtotime($afterPlayTime));
                    $updateFaceBindRes = $faceCompareMdl->updateFaceBindEndTimeById($arrId, $afterEndTimeDate,
                        $afterPlayTimeDate);
                    if (!$updateFaceBindRes) {
                        $errorData = [
                            'key'     => '更新人脸结束时间',
                            'endTime' => $afterEndTime,
                            'id'      => $arrId,
                        ];
                        pft_log('order/ticketchange/sync', json_encode($errorData));
                    }
                }
            }*/

        } catch (\Exception $e) {
            $msg = $e->getMessage();
        }
    }

    /**
     * 订单确认从控制层移植到业务层这里
     * 供统一调用
     * <AUTHOR>
     * @date 2020/9/28
     *
     * 原信息：
     *      html\Service\Controller\Order\OrderModify.class.php ->orderConfirm
     *      确认订单
     * @Author: zhujb
     *      2018/11/26
     *
     * @param  string  $orderNumStr  订单号
     * @param  int  $stuffId  未知
     * @param  int  $siteId  未知
     *
     * @return array
     */
    public function handleOrderConfirm(string $orderNumStr, int $sid, int $mid, string $account, int $stuffId = 0, int $siteId = 0)
    {
        if (empty($orderNumStr)) {
            return $this->returnData(203, '订单号异常');
        }
        // 修改订单状态为 未使用
        $orderHandlerModel  = $this->_OrderHandlerModel;
        $orderUpdateBiz     = new \Business\JavaApi\Order\OrderInfoUpdate();
        $orderTrackModel    = $this->_orderTrackModel;
        $landModel          = new Land();
        $subOrderSplitModel = new SubOrderQuery\SubOrderSplit();
        $orderToolsModel    = $this->_orderToolsModel;

        $orderNumArr = $orderToolsModel->getLinkSubOrder($orderNumStr);
        if (empty($orderNumArr)) {
            $orderNumArr[] = $orderNumStr;
        }
        $orderArr = $orderToolsModel->getOrderInfo($orderNumArr,
            'ordernum,tid,lid,tnum,aid,ordertel,pid,member,status,pay_status');

        if (empty($orderArr)) {
            return $this->returnData(400, '订单不存在');
        }

        if ($orderArr[0]['pay_status'] == 2) {
            return $this->returnData(400, '未支付订单不能确认');
        }

        //$orderSplitArr = $subOrderSplitModel->getListByOrderArr($orderNumArr, 'sellerid,level');

        //订单查询迁移三期
        $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
        $orderSplitArr          = $orderAidsSplitQueryLib->getListByOrderArrNew($orderNumArr);

        if (empty($orderSplitArr)) {
            return $this->returnData(400, '订单分销关系不存在');
        }

        $levelArr    = array_column($orderSplitArr, 'level');
        $topLevelKey = array_search(0, $levelArr) === 0 ? array_search(0, $levelArr) : array_search(1, $levelArr);
        $status      = array_unique(array_column($orderArr, 'status'));
        $topSellerId = isset($orderSplitArr[$topLevelKey]['sellerid']) ? $orderSplitArr[$topLevelKey]['sellerid'] : 0;

        if ($sid != $topSellerId) {
            //$landModel = new Land('slave');
            //$landInfo  = $landModel->getLandInfoBySalerId($account, false, 'apply_did');

            $landApi  = new \Business\CommodityCenter\Land();
            $landInfo = $landApi->queryLandMultiQueryBySalerid([$account])[0];
            if ($landInfo['apply_did'] !== $topSellerId) {
                return $this->returnData(400, '非顶级分销商或资源方账号不能确认订单');
            }
        }

        if ($status[0] != 4) {
            return $this->returnData(400, '订单状态不正确');
        }

        //$res = $orderHandlerModel->updateOrderStatus($orderNumArr, 0);
        $confirmTime = date('Y-m-d H:i:s');
        $updateRes   = $orderUpdateBiz->supplierConfirm($orderNumArr, $confirmTime);

        if ($updateRes['code'] != 200) {
            return $this->returnData(400, '订单状态修改失败');
        }

        $lidArr = array_column($orderArr, 'lid');
        //$landInfo = $landModel->getLandInfoByLids($lidArr, 'title,p_type');

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr);

        $stuffId = $stuffId == 0 ? $mid : $stuffId;
        foreach ($orderArr as $order) {
            $orderTrackModel->addTrack($order['ordernum'], $orderTrackModel::ORDER_CONFIRMED, $order['tid'],
                $order['tnum'],
                $order['tnum'], 16, 0,
                0, '', $stuffId, 0, '', '', $siteId);
        }

        // 通知客人订单已确认成功
        \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
            [
                'ordernum' => $orderArr[0]['ordernum'],
                'sellerId' => $sid, //扣钱的人的ID
                'buyerId'  => $orderArr[0]['member'], //购买人的ID
                'mobile'   => $orderArr[0]['ordertel'],
                'ptype'    => $landInfo[0]['p_type'],
                'ltitle'   => $landInfo[0]['title'],
                'aid'      => $orderArr[0]['aid'],
                'pid'      => '',
                'notify'   => 0,
            ]
        );

        return $this->returnData(200, '确认成功');
    }

    /**
     * 订单拒绝从控制层移植到业务层这里
     * 供统一调用
     * <AUTHOR>
     * @date 2020/9/28
     *
     * 原信息：
     *      html\Service\Controller\Order\OrderModify.class.php ->orderRefundConfirm
     *      确认订单
     * @Author: zhujb
     *      2018/11/26
     *
     * @param  string  $orderNumStr  订单号
     * @param  int  $sid  上级账号id
     * @param  int  $mid  当前账号id
     * @param  string  $account  当前账号
     * @param  string  $reqSerialNumber  可不传
     *
     * @return array
     */
    public function handleOrderRefundConfirm(string $orderNumStr, int $sid, int $mid, string $account, string $reqSerialNumber = '')
    {
        // 追加审核记录到订单变更记录表
        $landModel          = new Land();
        $subOrderSplitModel = new SubOrderQuery\SubOrderSplit();
        $orderToolsModel    = $this->_orderToolsModel;

        $orderNumArr = $orderToolsModel->getLinkSubOrder($orderNumStr);
        if (empty($orderNumArr)) {
            $orderNumArr[] = $orderNumStr;
        }
        $orderArr = $orderToolsModel->getOrderInfo($orderNumArr, 'ordernum,tid,lid,tnum,aid,ordertel,pid,member');

        if (empty($orderArr)) {
            return $this->returnData(400, '订单不存在');
        }

        //$orderSplitArr = $subOrderSplitModel->getListByOrderArr($orderNumArr, 'sellerid,level');

        //订单查询迁移三期
        $orderAidsSplitQueryLib = new OrderAidsSplitQuery();
        $orderSplitArr          = $orderAidsSplitQueryLib->getListByOrderArrNew($orderNumArr);

        if (empty($orderSplitArr)) {
            return $this->returnData(400, '订单分销关系不存在');
        }

        $levelArr    = array_column($orderSplitArr, 'level');
        $topLevelKey = array_search(0, $levelArr) === 0 ? array_search(0, $levelArr) : array_search(1, $levelArr);
        $topSellerId = isset($orderSplitArr[$topLevelKey]['sellerid']) ? $orderSplitArr[$topLevelKey]['sellerid'] : 0;

        if ($sid != $topSellerId) {
            //$landModel = new Land('slave');
            //$landInfo  = $landModel->getLandInfoBySalerId($account, false, 'apply_did');
            $landApi  = new \Business\CommodityCenter\Land();
            $landInfo = $landApi->queryLandMultiQueryBySalerid([$account])[0];
            if ($landInfo['apply_did'] !== $topSellerId) {
                return $this->returnData(400, '非顶级分销商或资源方账号不能拒绝订单');
            }
        }

        $lidArr   = array_column($orderArr, 'lid');
        //$landInfo = $landModel->getLandInfoByLids($lidArr, 'title,p_type');

        $javaAPi  = new \Business\CommodityCenter\Land();
        $landInfo = $javaAPi->queryLandMultiQueryById($lidArr);

        // 修改订单状态为 已取消
        $cancelChannel = 0;
        $subIndex      = 0;
        $isCancelFail  = false;
        foreach ($orderArr as $order) {
            //lc切换取消
            //调用统一的退票接口
            $subOrdernum      = $order['ordernum'];
            $cancelNum        = -1;
            $cancelType       = 'common';
            $cancelRemarkArr  = ['remark' => ''];
            $cancelSiteArr    = [];
            $cancelPersonArr  = [];
            $cancelSpecialArr = [
                'is_need_audit'          => true,
                'hotel_scheduled_refuse' => true,
            ];

            $refundRes = Helpers::platformRefund($subOrdernum, $cancelNum, $mid, $cancelChannel, $cancelType,
                $reqSerialNumber, $cancelRemarkArr, $cancelSiteArr, $cancelPersonArr, $cancelSpecialArr);
            if (in_array($refundRes['code'], [200, 1095])) {
                //200=退票成功，1095=退票审核申请成功
                $cancelRes[] = $subOrdernum;

                //记录第一个票的退票流水信息
                if ($subIndex == 0) {
                    $refundSuccArr = $refundRes['data'];
                    $successCode   = $refundRes['code'];
                }
            } else {
                //退票失败
                $isCancelFail  = true;
                $cancelFailArr = $refundRes['data'];
            }
        }
        if ($isCancelFail) {
            //有订单取消失败
            $resData       = [
                'err_code'           => $cancelFailArr['err_code'],
                'err_msg'            => $cancelFailArr['err_msg'],
                'success_order_list' => $cancelRes,
            ];
            $cancelFailMsg = $cancelFailArr['err_msg'] . "【{$cancelFailArr['err_code']}】";

            return $this->returnData(400, '拒绝失败:' . $cancelFailMsg, $resData);
        }
        if ($successCode == 200) {
            // 通知客人订单已被拒绝
            \Library\Resque\Queue::push('notify', 'OrderNotify_Job',
                [
                    'ordernum' => $orderArr[0]['ordernum'],
                    'sellerId' => $sid, //扣钱的人的ID
                    'buyerId'  => $orderArr[0]['member'], //购买人的ID
                    'mobile'   => $orderArr[0]['ordertel'],
                    'ptype'    => $landInfo[0]['p_type'],
                    'ltitle'   => $landInfo[0]['title'],
                    'aid'      => $orderArr[0]['aid'],
                    'pid'      => '',
                    'notify'   => 0,
                ]
            );
        }
        $resData = [
            'req_serial_number'  => $refundSuccArr['req_serial_number'],
            'pft_serial_number'  => $refundSuccArr['pft_serial_number'],
            'success_order_list' => $cancelRes,
        ];

        return $this->returnData(200, '已拒绝', $resData);
    }

    /**
     * 获取订单待确认信息从控制层移植到业务层这里
     * 供统一调用
     * <AUTHOR>
     * @date 2020/9/28
     *
     * 原信息：
     *      html\Service\Controller\Order\OrderModify.class.php ->getOrderConfirmInfo
     *      确认订单
     * @Author: zhujb
     *      2018/11/26
     *
     * @param  string  $ordernum  订单号
     *
     * @return array
     */
    public function getOrderConfirmInfoByOrdernum(string $ordernum)
    {
        $orderToolsModel = $this->_orderToolsModel;
        $orderInfo       = $orderToolsModel->getOrderInfo($ordernum,
            'tid,tnum,aid,totalmoney,ordername,ordertel,ordernum,ordertime,totalmoney,playtime');

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketById($orderInfo['tid'], 'title');

        $orderInfo['ttitle'] = isset($ticketArr['title']) ? $ticketArr['title'] : '';

        if (empty($orderInfo)) {
            return $this->returnData(204, '找不到订单');
        }

        $orderArr = $orderToolsModel->getLinkSubOrder($ordernum);
        if (empty($orderArr)) {
            $orderArr[] = $ordernum;
        }
        $orderList = $orderToolsModel->getOrderInfo($orderArr, 'tprice,aid,member,ordernum,totalmoney,playtime,tnum');

        if (empty($orderList)) {
            return $this->returnData(202, '暂无订单信息');
        }

        //获取包含子票的订单供应商
        $orderAids = [];
        //获取包含子票的订单分销商
        $orderMids = [];
        //订单分销价
        $orderTprice = [];
        foreach ($orderList as $value) {
            $orderAids[$value['ordernum']]   = $value['aid'];
            $orderMids[$value['ordernum']]   = $value['member'];
            $orderTprice[$value['ordernum']] = $value['tprice'];
        }

        // 获取分销价格
        $orderIdArr = array_column($orderList, 'ordernum');
        //订单详细信息获取
        $detailArr = $orderToolsModel->getOrderDetailInfo($orderIdArr,
            'orderid, aids, aids_price, aids_money, concat_id');

        $orderSplitPrice = [];
        $orderSplitAids  = [];

        foreach ($detailArr as $item) {
            if (!empty($item['aids'])) {
                //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                $splitAids = $item['aids'] . ',' . $orderMids[$item['orderid']];
            } elseif ($orderAids[$item['orderid']] != $orderMids[$item['orderid']]) {
                //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                $splitAids = $orderAids[$item['orderid']] . ',' . $orderMids[$item['orderid']];
            } else {
                //自供自销
                $splitAids = $orderAids[$item['orderid']] . ',' . $orderAids[$item['orderid']];
            }

            //分销链
            $splitAids = explode(",", $splitAids);
            //取出所有涉及到的会员ID (供应商/分销商)
            $orderSplitAids[$item['orderid']] = $splitAids;

            //分销价格链
            if ($item['aids_price'] !== '') {
                $splitPrice = $item['aids_price'] . ',' . $orderTprice[$item['orderid']];
            } else {
                $splitPrice = $orderTprice[$item['orderid']];
            }

            $splitPrice                        = explode(',', $splitPrice);
            $orderSplitPrice[$item['orderid']] = $splitPrice;

        }

        // 因为只有顶级分销商可以确认，所有都显示第一级价格
        $orderInfo['totalmoney'] = 0;
        foreach ($orderList as $key => $info) {
            $splitTotalPrice               = $orderSplitPrice[$info['ordernum']][0] * $info['tnum'];
            $orderInfo['totalmoney']       += $splitTotalPrice;
            $orderList[$key]['totalmoney'] = $splitTotalPrice;
        }

        $res['order_info'] = $orderInfo;
        $res['order_list'] = $orderList;

        return $this->returnData(200, 'success', $res);
    }

    /**
     * 检测库存
     * <AUTHOR>
     * @date   2020-06-24
     *
     * @param  int  $tid  票id
     * @param  string  $playTime  游玩日期
     * @param  string  $shareTimeId  分时id
     * @param  int  $orderMode  订单mode
     * @param  array  $ticketInfo  票信息或者快照信息
     *
     * @return array
     */
    private function _checkChangingStorage($tid, $playTime, $shareTimeId, $orderMode, $ticketInfo, $orderInfo = [])
    {
        $orderConvertApi = new OrderParamConvert();
        $convertResult   = $orderConvertApi->getShopByChannel((int)$orderMode);
        if ($convertResult['code'] != 200) {
            return $this->returnData(204, '销售渠道获取失败');
        }
        $channel = $convertResult['data'];
        if ($ticketInfo['pre_sale'] == 1 && $ticketInfo['is_online_reserve'] == 1) {
            $reservationStorageApi = new ReservationOrderStorage();
            $javaStorageRes        = $reservationStorageApi->getReservationStorageList($tid, $playTime, $playTime,
                $channel, true);
            if ($javaStorageRes['code'] != 200) {
                return $this->returnData(204, '暂无库存');
            }
            $javaStorageData = $javaStorageRes['data'];
            if ($javaStorageData[$playTime]['sectionFlag'] == 1) {
                $reservationShareList = $javaStorageData[$playTime]['time_share_info']['time_slices'];
                if (empty($reservationShareList)) {
                    return $this->returnData(204, '未找到分时时间段');
                }
                $reservationShareList = array_column($reservationShareList, null, 'id');
                if (isset($reservationShareList[$shareTimeId])) {
                    $saleTimeMsg = $reservationShareList[$shareTimeId]['saleTimeMsg'] ?? '';
                    if ($saleTimeMsg) {
                        return $this->returnData(204, $saleTimeMsg);
                    } elseif ($reservationShareList[$shareTimeId]['storage'] == -2) {
                        return $this->returnData(204, '时间段已停售');
                    } elseif ($reservationShareList[$shareTimeId]['storage'] != -1 && $reservationShareList[$shareTimeId]['storage'] == 0) {
                        return $this->returnData(204, '时间段库存不足');
                    }
                } else {
                    return $this->returnData(204, '未找到分时时间段');
                }
            } else {
                if ($javaStorageData[$playTime]['storage'] == -2) {
                    return $this->returnData(204, '当天已停售');
                }
                if ($javaStorageData[$playTime]['storage'] == 0 && $javaStorageData[$playTime]['storage'] != -1) {
                    return $this->returnData(204, '库存不足');
                }
            }

        } else {
            // 使用统一库存查询接口
            $aidsArr = $orderInfo['aids'] ? explode(',', $orderInfo['aids']) : [$orderInfo['apply_did']];
            if (!isset($aidsArr[1])) {
                // visitors 1=游客 2=分销商 3=自供自销
                $aidsArr[] = $orderInfo['visitors'] == 2 ? $orderInfo['member'] : $orderInfo['aid'];
            }
            $stockQueryApi = new StockQuery;
            $stockQueryRes = $stockQueryApi->queryRangeSkuAvailableStock($tid, $playTime, $playTime, $aidsArr[1], $aidsArr[0], true, $channel, true, true);
            if ($stockQueryRes['code'] != self::CODE_SUCCESS || empty($stockQueryRes['data'])) {
                return $this->returnData(204, '暂无预约库存');
            }
            foreach ($stockQueryRes['data'] as $item) {
                // 是否开启分时预约
                $sectionFlag = !empty($item['sectionDateId']) ? 1 : 0;
                if ($sectionFlag) {
                    if (!$item['sectionTimeList'] || !$shareTimeId) {
                        return $this->returnData(204, '请选择分时时间段，如果您已选择了分时时间段，请尝试刷新页面后再试！');
                    }
                    $sectionStock = array_filter($item['sectionTimeList'], function ($v) use ($shareTimeId) {
                        return $v['sectionTimeId'] == $shareTimeId;
                    });
                    if (!$sectionStock) {
                        return $this->returnData(204, '请选择分时时间段，如果您已选择了分时时间段，请尝试刷新页面后再试！');
                    }
                    $sectionStock = array_shift($sectionStock);
                    $saleTimeMsg = $sectionStock['saleTimeMsg'] ?? '';
                    if ($saleTimeMsg) {
                        return $this->returnData(204, $saleTimeMsg);
                    } elseif ($sectionStock['availableStock'] == -2) {
                        return $this->returnData(204, '时间段已停售');
                    } elseif (!$sectionStock['availableStock']) {
                        return $this->returnData(204, '时间段库存不足');
                    }
                } else {
                    if ($item['availableStock'] == -2) {
                        return $this->returnData(204, '已停售');
                    } elseif (!$item['availableStock']) {
                        return $this->returnData(204, '单日库存不足');
                    }
                }
            }
        }

        return $this->returnData(200, '校验成功');
    }

    /**
     * 重发短信
     * @author: zhangyz
     * @date: 2020/12/18
     *
     * @param  array  $memberInfo
     * @param  string  $ordernum
     * @param  string  $mobile
     *
     * @return array
     * @throws Exception
     */
    public function resendMessage(array $memberInfo, string $ordernum, $mobile = '')
    {
        if (!$ordernum || !$memberInfo) {
            return $this->returnDataV2(203, [], '参数错误');
        }
        if(!is_numeric($ordernum) || (mb_strlen($ordernum)!=14)){
            return $this->returnDataV2(203, [], '仅支持订单号重发');
        }

        if (!empty($mobile) && !ismobile($mobile)) {
            return $this->returnDataV2(203, [], '手机号格式错误');
        }

        $orderTool = new OrderTools();
        $orderInfo = $orderTool->getOrderInfo($ordernum);
        if ($orderInfo['remsg'] >= 3) {
            return $this->returnDataV2(203, [], '短信发送次数不能超过3次');
        }

        if (empty($mobile) || $mobile == $orderInfo['ordertel']) {
            $mobile = 0;
        }

        $tid         = $orderInfo['tid'];
        $ticketModel = new \Model\Product\Ticket();
        $ticketInfo  = $ticketModel->getTicketInfoById($tid, 'sourceT,landid,apply_did');
        $lid         = $ticketInfo['landid'];
        $sid         = $ticketInfo['apply_did'];

        // 获取门票的绑定信息
        $otaProductBiz      = new \Business\Ota\Product();
        $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($tid);
        if (empty($thirdTicketConfArr)) {
            return $this->returnDataV2(203, [], '获取门票信息异常');
        }

        $sourceT = $thirdTicketConfArr['sourceT'];

        if ($sourceT == 1 && $mobile) {
            return $this->returnDataV2(203, [], '对接九天不支持修改手机号重发短信');
        }
        //判断是否对接第三方
        if ($sourceT == 2) {
            if ($mobile) {
                return $this->returnDataV2(203, [], '对接第三方不支持修改手机号重发短信');
            }
            //对接第三方走第三方短信重发
            if (!defined('COOL_OTA_DIR')) {
                $otaGatewayFile = HTML_DIR . '/ota/common/OTAGateway.class.php';
                include $otaGatewayFile;
            }

            $params            = [];
            $params['Action']  = 'resend';
            $params['Ordern']  = $ordernum;
            $params['LandId']  = $lid; //景点ID
            $params['ApplyId'] = $sid; //原始供应商

            $otaSmsRes = \OTAGateway::dispatch($params);
            if (!$otaSmsRes) {
                return $this->returnDataV2(203, [], '第三方系统错误，重发失败');
            }

            $res  = explode('|', $otaSmsRes);
            $code = $res[0];
            if ($code != 200) {
                return $this->returnDataV2(203, [], '第三方系统错误，重发失败');
            }

            //第三方系统已重发短信成功
            return $this->returnDataV2(200, [], '第三方系统已重发短信成功');
        }

        // 判断是否是对接开放接口的第三方
        if ($sourceT == 3) {
            if ($mobile) {
                return $this->returnDataV2(203, [], '对接第三方不支持修改手机号重发短信');
            }
            // 调用推送开放接口重发短信
            $params            = [];
            $params['Ordern']  = $ordernum;
            $params['LandId']  = $lid; //景点ID
            $params['ApplyId'] = $sid; //原始供应商

            $otaOpenOrderBiz = new \Business\Ota\Open\Order();
            $resendResArr    = $otaOpenOrderBiz->pushResendMsg($params);
            if ($resendResArr['code'] == 200) {
                return $this->returnDataV2(200, [], '重发短信成功');
            }
        }

        $ticketExtInfo = $ticketModel->getTicketExtInfoByTid($tid, 'tid, sendVoucher');
        $sendVoucher   = $ticketExtInfo['sendVoucher'];
        if ($sendVoucher == 1) {
            return $this->returnDataV2(203, [], '不发送凭证码');
        }

        //票付通发送短信
        $smsLib = new \Library\MessageNotify\OrderNotify($ordernum, 0, 0, 0, 0, 0, 0, '', 0, 1);
        $smsRet = $smsLib->Send(0, false, true, $mobile);
        if (!$smsRet) {
            return $this->returnDataV2(203, [], '重发短信失败');
        }

        //订单操作记录备注
        $memo = '重发到手机：';
        //员工操作记录备注
        $daction = "重发订单短信($ordernum),到手机";

        if ($mobile) {
            //重发到新手机
            $memo    .= $mobile;
            $daction .= $mobile;
        } else {
            //重发到原手机
            $memo    .= $orderInfo['ordertel'];
            $daction .= $orderInfo['ordertel'];
        }

        $orderTrackModel = new \Model\Order\OrderTrack();
        //订单日志记录
        $orderTrackModel->addTrack($ordernum, 18, (int)$tid, 0,
            (string)$orderInfo['tnum'], 16, $terminal_id = 0, $branch_terminal = 0, $id_card = '',
            $memberInfo['memberID'], $salerid = 0, $create_time = '', $memo);

        if ($memberInfo['dtype'] == 6) {
            $optLogModel = new \Model\SystemLog\OptLog();
            //员工操作记录
            $res = $optLogModel->StuffOptLog($memberInfo['memberID'], $memberInfo['sid'], $daction);
        }

        return $this->returnDataV2(200, [], '重发短信成功');
    }

    /**
     * 指定idx退票
     * @author: linchen
     * @date: 2021/07/24
     *
     * @param  string  $orderNum  订单号
     * @param  int  $idx  idx
     * @param  int  $mid  操作员
     * @param  int  $sid  供应商
     * @param  int  $cancelChannel  取消渠道
     * @param  string  $cancelRemark  备注
     *
     * @return array
     */
    public function appointIdxRefundService($orderNum, $idx, $mid, $sid,
        $cancelChannel, $cancelRemark, $otherParams=[])
    {
        if (!$orderNum || !$idx) {
            return $this->returnData(204, '参数错误');
        }
        //简单做根据游客表里面有多少个门票码确定是不是要用idx指定取消
        $touristMdl = new \Model\Order\OrderTourist();
        $touristNum = $touristMdl->getTouristNumByStatus($orderNum);
        //todo 实际上这边产品需求是要根据票属性是否是一票一码还是一票种一张票判断，这边直接简单判断下门票码生成的数量
        if ($touristNum > 1) {
            $cancelList[$orderNum]   = 1;  //如果有多个门票码的情况直接当成是一人一票
            $orderIdxList[$orderNum] = $idx;
        } else {
            $cancelList[$orderNum] = -1;
        }

        $reqSerialNumber = $this->getAuditInfoByOrder($orderNum);

        return $this->baseRefund($orderNum, $cancelList, $mid, $sid, $cancelChannel, $touristList = [], $cancelRemark,
            $reqSerialNumber, $isNeedAudit = true, $isAuditPass = false, $orderIdxList, $otherParams);
    }
}
