<?php

namespace Business\Order;

use Business\Base;
use Business\CommodityCenter\Land;
use Business\JavaApi\CommodityCenter\Ticket as ticketBiz;
use Business\TeamOrder\TeamOrderReport;
use Business\TeamOrder\TeamRules;
use Library\Constants\Team\TeamConst;
use Library\Util\Team\TeamUtil;
use Pimple\Container;
use Model\Order\OrderTools;
use Model\Product\Ticket;

/**
 * 订单增加票数
 */
class OrderAddNum extends Base {


    public function __construct() {
        \Library\Tools\Helpers::composerAutoload();
        
        $this->container = new Container();
        $this->container['order_tool'] = function () {
            return new OrderTools('slave');
        };
        $this->container['ticket_model'] = function () {
            return new Ticket('slave');
        };
    }

    /**
     * 订单增加票数
     * @param  string      $ordernum         主订单号
     * @param  int         $tnum             增加后的票数
     * @param  int         $memberId         操作人主账号id
     * @param  int|integer $oper             操作人id
     * @param  array       $touristInfoArray [['tourist' => '姓名', 'idcard' => '证件号', 'voucher_type' => '证件类型'， 'more_list' => []],...]
     * @param  array       $extra            额外的参数
     *                                       [
     *                                           'team_order' => '团单单号'
     *                                       ]
     * @return array
     * <AUTHOR>
     * @date   2019-07-31
     */
    public function orderAddNum(string $ordernum, int $tnum, int $memberId, int $oper = 0, array $touristInfoArray = [], array $extra = [])
    {
        if (!$ordernum || !$tnum || !$memberId) {
            return $this->returnData(204, '参数错误');
        }

        $checkRes = $this->_baseCheck($ordernum, $tnum, $memberId, $touristInfoArray, $extra);
        if ($checkRes['code'] != 200) {
            return $checkRes;
        }
        //要增加的票数
        $changeNum = $checkRes['data']['change_num'];
        $remark    = $checkRes['data']['remark'];
        $forceAdd  = $checkRes['data']['force_allow_add'] ?? 0;

        // 修复extra空数组问题：转换为空对象
        $extraForJava = empty($extra) ? new \stdClass() : $extra;
        $orderMap = ['orderNum' => $ordernum, 'modifyNum' => $changeNum, 'extra' => $extraForJava]; //需要透传extra参数给中台
        $touristInfo = [];
        foreach ($touristInfoArray as $item) {
            $data = $item;
            $data['mobile'] = '';
            $data['ordernum'] = $ordernum;
            $data['voucherType'] = $item['voucher_type'] ?? 1;
            $data['moreList'] = $item['more_list'] ?? null;
            $touristInfo[] = $data;
        }

        //流水号
        $serialNumber = $ordernum . '_' . str_pad(str_replace('.', '', microtime(true)), 14, '0');
        //调用java接口
        $api = new \Business\JavaApi\Order\Modify();
        $res = $api->addItemNum($memberId, $oper ?: $memberId, $serialNumber, [$orderMap], $touristInfo, $forceAdd, $remark, $extra);
        if ($res['code'] != 200) {
            return $res;
        }

        $data = [
            'change_num' => $checkRes['data']['change_num']
        ];
        //订单加票成功后的后续操作
        $this->_afterAddOrderAddNum($ordernum, $checkRes['data']['change_num'], $oper ?: $memberId);

        return $this->returnData(200, '', $data);
    }


    /**
     * 基本检测
     *
     * @param string $ordernum 订单号
     * @param int    $tnum     增加后的票数
     * @param int    $memberId 操作人主账号id
     * @param array  $touristInfoArray 游客信息
     * @param array  $extra 额外参数
     * @return array
     * <AUTHOR>
     */
    private function _baseCheck($ordernum, $tnum, $memberId, $touristInfoArray, $extra) {

        //订单信息，状态检测
        $orderCheckRes = $this->_orderCheck($ordernum, $memberId,$extra, $tnum);
        if ($orderCheckRes['code'] != 200) {
            return $orderCheckRes;
        }

        //订单信息
        $orderInfo = $orderCheckRes['data']['order_info'];

        //游客信息检测
        $touristCheckRes = $this->_touristInfoCheck($orderInfo, $touristInfoArray, $extra);
        if ($touristCheckRes['code'] != 200) {
            return $touristCheckRes;
        }

        //票类配置检测
        $ticketCheckRes = $this->_ticketCheck($orderInfo, $tnum);
        if ($ticketCheckRes['code'] != 200) {
            return $ticketCheckRes;
        }

        //其他非共性的检测逻辑，可以 通过hook在各自的业务里拦截(人脸，团单等)
        try {
            $args = [
                'ordernum'   => $ordernum,
                'member_id'  => $memberId,
                'after_tnum' => $tnum
            ];

            $interceptorParams = ['args' => $args];
            //退票拦截
            \Library\Hook::Listen('order_add_num_interceptor', $interceptorParams);
        } catch (\Exception $e) {
            return $this->returnData($e->getCode(), $e->getMessage());
        }

        $data = [
            'change_num' => $ticketCheckRes['data']['change_num'],
            'remark'     => $extra['remark'] ?? '',
            'force_allow_add'  => $extra['force_allow_add'] ?? 0,
        ];

        return $this->returnData(200, '', $data);
    }

    /**
     * 订单信息检测
     *
     * @param string $ordernum 订单号
     * @param int $memberId 操作人主账号id
     * @param  int  $tnum  增加后的票数
     *
     * @return array
     * <AUTHOR>
     *
     */
    private function _orderCheck($ordernum, $memberId,$extra = [], $tnum = 0) {
        //获取订单详情
        $field     = 'ordernum,tnum,status,member,aid,ordermode,tid,paymode,pay_status,playtime,apply_did';
        $orderInfo = $this->container['order_tool']->getOrderInfo($ordernum, $field);
        if (!$orderInfo) {
            return $this->returnData(204, '订单不存在');
        }
        $forceAdd = $extra['force_allow_add'] ?? 0;
//        if ($orderInfo['member'] != $memberId) {
//            return $this->returnData(204, '无权操作');
//        }

        if ($orderInfo['pay_status'] != 1 && !in_array($orderInfo['ordermode'], [24, 44]) && !$forceAdd) {
            return $this->returnData(204, '订单未支付不支持增加票数');
        }

        if ($orderInfo['aid'] == $orderInfo['member'] && !in_array($orderInfo['ordermode'], [24, 44])  && !$forceAdd) {
            return $this->returnData(204, '自供自销不支持增加票数');
        }

        //在线支付不支持
        if (!in_array($orderInfo['paymode'], [0, 2]) && $orderInfo['pay_status'] == 1) {
            return $this->returnData(204, '仅余额支付和授信支付支持增加订单');
        }

        //对接第三方的订单不允许修改增加
        if ($orderInfo['remotenum']) {
            return $this->returnData(204, '对接第三方的订单不允许修改增加');
        }
        //如果是团单，这边要走下团单修改限制条件判断
        if (in_array($orderInfo['ordermode'], [24, 44])) {
            $numMapping       = [
                $ordernum => $tnum
            ];
            // 已经确认的订单不让分销商修改
            $teamModel        = new \Model\Order\TeamOrderSearch;
            $mainOrder        = $teamModel->getMainOrderInfoBySonOrder($ordernum);
            $teamRulesBz = new TeamRules();
            $checkRes    = $teamRulesBz->teamOrderChangeJudge($orderInfo['apply_did'], $orderInfo['tid'], $orderInfo['playtime'], 1, $mainOrder['main_ordernum'], $numMapping);
            if ($checkRes['code'] != 200) {
                return $this->returnData(204, $checkRes['msg']);
            }
            //allow  数量是否允许修改 1：允许  2：不允许
            if ($checkRes['data']['allow'] != 1) {
                return $this->returnData(204, $checkRes['msg']);
            }
        }

        // 切换门票获取第三方系统信息
        $otaProductBiz = new \Business\Ota\Product();
        $thirdBindInfoArr = $otaProductBiz->getTicketConfigByTid($orderInfo['tid']);
        if (empty($thirdBindInfoArr)) {
            return $this->returnData(204, '获取门票绑定数据失败');
        }
        $Mdetails = $thirdBindInfoArr['Mdetails'];
        $Mpath    = $thirdBindInfoArr['Mpath'];
        $sourceT  = $thirdBindInfoArr['sourceT'];

        if ($Mdetails && $Mpath && $sourceT) {
            return $this->returnData(204, '分销第三方的订单不允许修改增加');
        }

        $data = ['order_info' => $orderInfo];

        return $this->returnData(200, '', $data);
    }


    /**
     * 游客信息检测
     *
     * @param array $orderInfo 订单信息
     * @param array $touristInfoArray 游客信息
     * @return array
     * <AUTHOR>
     */
    private function _touristInfoCheck($orderInfo, $touristInfoArray, $extra = []) {

        $tid      = $orderInfo['tid'];
        $ordernum = $orderInfo['ordernum'];
        /** if (empty($touristInfoArray)){
            //云票务需要实名制的时候可能也不传证件号过来，需要校验
             if (in_array($orderInfo['ordermode'],[24,44])){
                //TODO 后期要根据渠道特殊判断，这边只能先判断没传上来就先过去了，兼容以前旧的逻辑
                return $this->returnData(200);
            }
        }*/
        //走订单快照
        if (isset($extra['get_order_info_cache'])) {
            $ticketVersionRes = json_decode($orderInfo['ext_content'], true) ?? [];
            if (!$ticketVersionRes) {
                pft_log('order_add_num', 'touristInfoCheck'.json_encode([$ticketVersionRes]));
                return $this->returnData(204, "获取订单快照失败");
            }
            $ticketExt = (new \Business\Product\Ticket())->getTicketSnapShotByCache($tid, $ticketVersionRes['ticketVersion']);
            if (!$ticketExt) {
                return $this->returnData(204, "获取订单快照失败");
//                    pft_log('order_add_num', 'touristInfoCheck'.json_encode([$ticketExt, $tid, $ticketVersionRes['ticketVersion']]));
            }
            $touristInfo = $ticketExt['uuLandFDTO']['touristInfo'];
        }else{
            //(一票一证)是否身份证必传
            $ticketBiz = new ticketBiz();
            $ticketExt = $ticketBiz->queryTicketInfoById($tid);
            if ($ticketExt['code'] != 200 || empty($ticketExt['data'])) {
                return $this->returnData(204, "获取门票信息失败");
            }
            $touristInfo = $ticketExt['data']['uuLandFDTO']['tourist_info'];
        }

        if (in_array($touristInfo, [2, 4])) {
            if (!$touristInfoArray) {
                return $this->returnData(204, '身份证信息缺失');
            } else {
                //获取订单的游客信息，身份证不能重复
                $orderTouristArray = $this->_getOrderTouristInfoByOrderNum($ordernum);
                //订单里已存在的身份证
                $existIdCardArr    = array_column($orderTouristArray, 'idcard');
                foreach ($touristInfoArray as $item) {
                    if (\Library\Tools::idcard_checksum18($item['idcard']) == false && $item['voucher_type'] == 1) {
                        return $this->returnData(204, "身份证格式错误:{$item['idcard']}");
                    }
                    if (in_array($item['idcard'], $existIdCardArr)) {
                        return $this->returnData(204, "身份证重复:{$item['idcard']}");
                    }
                    if (!$item['tourist']) {
                        return $this->returnData(204, "游客姓名不能为空");
                    }
                }
            }

            //购票前置校验
            $preCheckUsersRes = $this->_preCheckUsers($orderInfo, $touristInfo, $touristInfoArray);
            if ($preCheckUsersRes['code'] != 200) {
                return $this->returnData($preCheckUsersRes['code'], $preCheckUsersRes['msg']);
            }
        }
        return $this->returnData(200);
    }

    /**
     * 根据订单号获取未验证游客身份信息
     * @param string $ordernum 订单号
     * @return array
     */
    private function _getOrderTouristInfoByOrderNum($ordernum) {
        $list = $this->container['order_tool']->getOrderTouristInfo($ordernum);

        $return = [];
        if ($list) {
            if ($list[0]['idcard'] == $list[1]['idcard']) {
                unset($list[0]);
            }

            foreach ($list as $value) {
                if ($value['check_state'] == 0) {
                    $return[] = $value;
                }
            }
        }

        return $return;
    }

    /**
     * 门票信息检测
     *
     * @param  array  $orderInfo  订单信息
     * @param  int  $tnum  增加后的票数
     *
     * @return array
     * <AUTHOR>
     */
    private function _ticketCheck($orderInfo, $tnum)
    {
        //$ordernum         = $orderInfo['ordernum'];
        $commodityLandBiz = new Land();
        //获取产品类型

        $landInfo = $commodityLandBiz->getLandInfoByTidToJava($orderInfo['tid']);
        $ptype    = $landInfo['p_type'];

        $allowPtype = ['A', 'B', 'G'];

        if ($orderInfo['ordermode'] == 24) {
            $allowPtype[] = 'F';
        }

        if (!in_array($ptype, $allowPtype)) {
            return $this->returnData(204, '该类型的产品不支持增加票数');
        }

        if (\Business\Product\Ticket::judgeNewChangeRule($ptype)) {
            //根据产品类型判断是否走新的退改参数规则
            $ticketBiz = new \Business\CommodityCenter\Ticket();
            $ticketRes = $ticketBiz->queryTicketInfoById($orderInfo['tid']);
            if (!isset($ticketRes['ext'])) {
                return $this->returnData(204, '获取票类信息失败');
            }
            $supplementRule = $ticketRes['ext']['supplement_rule'] ?? 0;
            if ($supplementRule == 0) {
                return $this->returnData(204, '票类属性设置不允许补票');
            }
        } else {
            //旧的退改参数规则
            //获取门票信息
            $ticket      = $this->container['ticket_model']->getTicketInfoById($orderInfo['tid'], 'num_modify');
            $numModLimit = (int)$ticket['num_modify'];
            if ($numModLimit <= 0) {
                return $this->returnData(204, '票类属性设置不允许【增加票数】');
            }
        }

        //要增加的票数
        $change = $tnum - $orderInfo['tnum'];
        if ($change < 1) {
            return $this->returnData(204, "要增加的票数量错误[{$change}]");
        }

        return $this->returnData(200, '', ['change_num' => $change]);
    }

    /**
     * @param $orderInfo
     * @param int $touristInfo 游客信息0不需要填写1需要填写2需要填写所有游客身份证
     * @param array $touristInfoArray
     * @return array
     */
    public function _preCheckUsers($orderInfo, $touristInfo, $touristInfoArray)
    {
        //游客身份证信息
        $orderTouristIdCards = [];
        foreach ($touristInfoArray as $item) {
            $orderTouristIdCard = [
                'tourist'     => $item['tourist'], //姓名
                'idcard'      => $item['idcard'], //身份证信息
                'voucherType' => 1, // 身份信息类型
                'mobile'      => '',
                'ticketId'    => $orderInfo['tid']
            ];
            if (!empty($item['voucher_type'])) {
                $orderTouristIdCard['voucherType'] = $item['voucher_type']; //身份信息类型
            }
            if (!empty($item['more_list'])) {
                $orderTouristIdCard['moreList'] = $item['more_list']; //更多用户身份信息
            }
            $orderTouristIdCards[] = $orderTouristIdCard;
        }

        $queryParams = [[$orderInfo['ordernum']]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo','getOrderUserInfoByOrderNum', $queryParams);
        if ($queryRes['code'] == 200) {
            $orderContact = array_shift($queryRes['data']);
        }
        $productExt = $orderInfo['product_ext'] ? json_decode($orderInfo['product_ext'], true) : [];
        $moreList = $productExt['moreInfo'] ?? [];
        $buyTicketList[] = [
            'ticketId'              => $orderInfo['tid'],
            'landId'                => $orderInfo['lid'],
            'tnum'                  => count($touristInfoArray),
            'pType'                 => $orderInfo['product_type'],
            'touristInfo'           => $touristInfo,
            'playTime'              => $orderInfo['begintime'],
            //取票人身份信息
            'orderPurchaseContacts' => [
                'ordername'    => $orderContact['ordername'] ?? $orderInfo['ordername'],
                'ordertel'     => $orderContact['ordertel'] ?? $orderInfo['ordertel'],
                'contacttel'   => $orderContact['contacttel'] ?? $orderInfo['contacttel'],
                'personid'     => $orderContact['personid'] ?? $orderInfo['personid'],
                'voucherType'  => $orderContact['voucher_type'] ?? 0,
                'provinceCode' => $orderContact['province_code'] ?? '',
                'cityCode'     => $orderContact['city_code'] ?? '',
                'townCode'     => $orderContact['town_code'] ?? '',
                'address'      => $orderContact['address'] ?? '',
                'moreList'     => $moreList,
            ],
            //游客身份信息
            'orderTouristIdCards'   => $orderTouristIdCards,
        ];

        $requestData = [
            'buyTicketList' => array_values($buyTicketList),
            'verifySpecial' => 1,//是否进行特殊票验证
            'channel'       => $orderInfo['ordermode'],
        ];
        //请求Java判断
        $res = (new \Business\JavaApi\Order\Order())->preCheckUsers($requestData);

        if ($res['code'] != 200) {
            return $this->returnData($res['code'], $res['msg'], $res['data']);
        }
        foreach ($res['data'] as $key => $item) {
            if ($item['userType'] == 'TAKER') {
                continue;
            }
            if ($item['limit']) {
                $limitTypeStr = '';
                switch ($item['limitType']) {
                    case 1: $limitTypeStr = $item['mobilePhone'];break;
                    case 2: $limitTypeStr = $item['identityCode'];break;
                    case 3: $limitTypeStr = "{$item['identityCode']}|{$item['mobilePhone']}";break;
                }
                return $this->returnData(204, "【{$limitTypeStr}】{$item['errorMessage']}");
            }
        }
        return $this->returnData(200);
    }

    /**
     * 订单加票成功后的后续操作
     *
     * @param string $ordernum 订单号
     * @param int $changeNum  修改数量
     * @param int $oper  操作人
     *
     * <AUTHOR>
     * @date 2021/11/09
     *
     */
    public function _afterAddOrderAddNum($ordernum, $changeNum, $oper)
    {
        //新增票数，对应的操作记录添加
        $teamReportTask = new TeamOrderReport();
        $teamReportTask->addOneTask($ordernum, $oper, $changeNum);
        //写入实时报表pft_report_real_task表是由中台接口触发 因此这里不需要写入
    }

}