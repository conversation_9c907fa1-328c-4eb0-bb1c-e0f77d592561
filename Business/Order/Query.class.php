<?php

/**
 * 订单查询封装
 *
 * @date 2017-04-13
 * <AUTHOR>
 *
 */

namespace Business\Order;

use Business\Base;
use Business\CommodityCenter\Ticket as ticketBiz;
use Business\JavaApi\Order\OrderTouristInfo;
use Business\JavaApi\Ticket\TicketSnapshot;
use Business\JavaApi\TicketApi;
use Business\NewJavaApi\Order\ProductOrder\AdminProductOrder;
use Business\NewJavaApi\Order\ProductOrder\BizProductOrder;
use Business\NewJavaApi\Order\ProductOrder\QueryParamConverter;
use Business\Order\Refund\QConfigSwitchController;
use Business\Product\HandleTicket;
use Business\Product\Show;
use Library\ApplicationContext;
use Library\Constants\Discount\DiscountType;
use Library\Constants\Order\OrderStatus\CommonOrderTouristStatus;
use Library\Traits\IdentityConversionTrait;
use Model\Member\Member;
use Model\Member\MemberRelationship;
use Model\Order\AllApiOrderModel;
use Model\Order\OrderHandler;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\OrderTourist;
use Model\Order\OrderTrack;
use Model\Order\RefundAuditModel;
use Model\Order\SubOrderQuery;
use Model\Order\SubOrderQuery\SubOrderTrack;
use Model\Product\AnnualCard;
use Model\Product\Area;
use Model\Product\Land;
use Model\Product\Ticket;

class Query extends Base
{
    use IdentityConversionTrait;
    //查询日期类型
    private $_dateType = [
        0 => 'ordertime', //下单时间
        1 => 'playtime', //游玩时间
        2 => 'dtime', //验证时间
    ];
    const TEAM_ORDER_MODE = [
        '44',//团单报团计调
        '24'//团队订单
    ];
    private $_toolModel;
    private $_landModel;
    private $_ticModel;
    private $_queryModel;
    private $_memModel;
    private $_printStateConf;
    private $_printStateAllowTypeConf;
    private $_sid;
    private $_memberid;
    private $_orderInfoHiding = false;

    public function __construct()
    {
        $this->_toolModel  = new OrderTools('slave');
        $this->_ticModel   = new Ticket('slave');
        $this->_landModel  = new Land('slave');
        $this->_queryModel = new OrderQuery();
        $this->_memModel   = new Member('slave');
        $this->_trackModel = new OrderTrack('slave');
        //取票状态的配置
        $this->_printStateConf = load_config('print_state', 'orderSearch');
        //允许展示取票状态的配置
        $this->_printStateAllowTypeConf = load_config('print_state_allow_type', 'orderSearch');
    }

    /**
     * 景区账号查询数据
     * <AUTHOR>
     * @date 2020/12/18
     *
     * @param  int  $memberId  登录用户ID
     * @param  int  $sid  主账号ID
     * @param  int  $salerId  景区账号
     * @param  string  $orderTimeStart  下单开始时间
     * @param  string  $orderTimeEnd  下单结束时间
     * @param  string  $dtimeStart  验证开始时间
     * @param  string  $dtimeEnd  验证结束时间
     * @param  int  $page  页码
     * @param  int  $size  条数
     * @param  string  $ordernum  查询订单号
     * @param  string  $remoteOrder  查询远端订单号
     * @param  string  $thirdOrder  查询三方订单号
     * @param  int  $status  订单状态
     * @param  string  $mobile  查询手机号
     * @param  string  $idcard  查询身份证
     * @param  string  $ordername  联系人姓名
     * @param  int  $lid  产品ID
     * @param  boolean  $orderInfoHiding  是否需要隐藏中间级分销商隐藏订单游客信息
     * @param  bool  $showMoreCredential  是否展示游客取票人更多信息
     *
     * @return array
     */
    public function searchScenicList($memberId, $sid, $salerId, $dbStart = false, $dbEnd = false, $orderTimeStart = false,
        $orderTimeEnd = false, $dtimeStart = false, $dtimeEnd = false, $page = false, $size = false, $ordernum = false,
        $remoteOrder = false, $thirdOrder = false, $status = false, $mobile = false, $touristIdCard = false, $ordername = false,
        $lid = false, $memberRelationship = false, $orderInfoHiding = false, $showMoreCredential = true)
    {
        $this->_memberid = $memberId;
        $this->_sid      = $sid;
        try {

            $orderNumArr = [];
            if ($ordernum) {
                $orderNumArr = [$ordernum];
            }

            //我采购/分销的订单
            $queryParams = $this->_makeScenicParam($salerId, $dbStart, $dbEnd, $orderTimeStart, $orderTimeEnd, $dtimeStart,
                $dtimeEnd, $page, $size, $orderNumArr, $status, $mobile, $ordername, $lid, $memberRelationship,
                $remoteOrder, $thirdOrder, $touristIdCard);

            $convertParams = QueryParamConverter::convert($queryParams);
            $convertParams['param']['memberId'] = AdminProductOrder::MEMBER_ID; // 资源账号特殊使用管理端接口，查询全量订单，用 salerid 过滤
            $queryRes = (new AdminProductOrder())->pageInfoBest($convertParams);

            $orderList              = $queryRes['rows'];
            $this->_orderInfoHiding = $orderInfoHiding;

            //剥离旅游券的订单
            $orderListOld = [];
            foreach ($orderList as $orderListValue) {
                if ($orderListValue['itemType'] == 'Q') {
                    continue;
                }
                $orderListOld[] = $orderListValue['productOrderId'];
            }

            $listMap = [];
            if ($orderListOld) {
                $orderRes = $this->_getOrderDetails($sid, $orderListOld, $showMoreCredential);
                $listMap = array_key($orderRes, 'ordernum');
            }

            //其他类型的订单和旅游券订单聚合显示
            $listNew = [];
            foreach ($orderList as $orderListTmp) {
                if ($orderListTmp['itemType'] == 'Q') {
                    $listNew[] = $this->handleLvOrderList($orderListTmp['productOrderId'], $orderListTmp['itemType']);
                } else {
                    $listNew[] = $listMap[$orderListTmp['productOrderId']];
                }
            }

            return $this->returnData(200, '', $listNew);
        } catch (\Exception $e) {
            //异常返回
            return $this->returnData(500, $e->getMessage());
        }
    }

    /**
     *
     * @param  int  $memberId  登录用户ID
     * @param  int  $sid  主账号ID
     * @param  string  $orderTimeStart  下单开始时间
     * @param string  $orderTimeEnd 下单结束时间
     * @param  string  $dtimeStart 验证开始时间
     * @param  string  $dtimeEnd  验证结束时间
     * @param  int $page  页码
     * @param int  $size  条数
     * @param  string  $ordernum  查询订单号
     * @param  string  $remoteOrder  查询远端订单号
     * @param  string  $thirdOrder 查询三方订单号
     * @param  int  $status  订单状态
     * @param  string  $mobile  查询手机号
     * @param  string $idcard  查询身份证
     * @param  string $ordername  联系人姓名
     * @param  int $lid  产品ID
     * @param  bool  $showMoreCredential 是否展示游客取票人更多信息
     * @param array $extCondition ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     *
     * @return array
     *<AUTHOR>
     * @date 2020/12/18
     *
     */
    public function searchBusinessList($memberId, $sid, $sellerId, $buyerId, $orderTimeStart = false, $orderTimeEnd = false,
       $dtimeStart = false, $dtimeEnd = false, $page = false, $size = false, $ordernum = false, $remoteOrder = false,
       $thirdOrder = false, $status = false, $mobile = false, $idcard = false, $ordername = false, $lid = false,
       $memberRelationship = false, $orderInfoHiding = false, $showMoreCredential = true, array $extCondition = [])
    {
        $this->_sid      = $sid;
        $this->_memberid = $memberId;
        try {
            $queryParams = $this->_makeBusinessParam($sellerId, $buyerId, $orderTimeStart, $orderTimeEnd, $dtimeStart,
                $dtimeEnd, $page, $size, $ordernum, $status, $mobile, $ordername, $lid, $memberRelationship,
                $remoteOrder, $thirdOrder, $idcard, $extCondition);

            $convertParams = QueryParamConverter::convert($queryParams);
            $convertParams['param']['memberId'] = $sid;
            $queryRes = (new BizProductOrder())->pageInfoBest($convertParams);

            $orderList = $queryRes['rows'];

            //剥离旅游券的订单
            $orderListOld = [];
            foreach ($orderList as $orderListValue) {
                if ($orderListValue['itemType'] == 'Q') {
                    continue;
                }
                $orderListOld[] = $orderListValue['productOrderId'];
            }

            $listMap = [];
            if ($orderListOld) {
                $this->_orderInfoHiding = $orderInfoHiding;
                $orderRes               = $this->_getOrderDetails($sid, $orderListOld, $showMoreCredential);
                $listMap = array_key($orderRes, 'ordernum');
            }

            //其他类型的订单和旅游券订单聚合显示
            $listNew = [];
            foreach ($orderList as $orderListTmp) {
                if ($orderListTmp['itemType'] == 'Q') {
                    $listNew[] = $this->handleLvOrderList($orderListTmp['productOrderId'], $orderListTmp['itemType']);
                } else {
                    $listNew[] = $listMap[$orderListTmp['productOrderId']];
                }
            }

            return $this->returnData(200, '', $listNew);
        } catch (\Exception $e) {
            //异常返回
            return $this->returnData(500, $e->getMessage());
        }
    }

    /**
     * 获取打印凭证需要的数据，用于“支付即验证”,其中还有快速验证的功能
     *
     * @param  string  $outTradeNo  订单号
     * @param  array  $orderInfo  数组，订单数据
     * @param  int  $totalFee  支付金额，单位:分
     * @param  int  $paymode  支付方式
     * @param  string  $terminal  终端号
     * @param  int  $checkSource  验证人
     * @param  bool  $packageRule  套票新规则
     * @param  boolean  $isBatch  是否分批取票
     * @param  int  $printNum
     * @param  bool  $verify  是否验证
     *
     * @return array
     */
    public function getOrderInfoForPrint($outTradeNo, $orderInfo, $totalFee, $paymode, $terminal = 0, $checkSource = false, $checkMember = 0, $packageRule = false, $isBatch = false, $printNum = 0, $verify = true)
    {
        //产品类型
        $landInfo = $this->_landModel->getLandInfo($orderInfo['lid'], false, 'p_type');
        //套票不参与买即验
        // <AUTHOR> | @date 2018/09/10
        //套票买即验取即验新规则  false保持旧版规则
        if ($packageRule == false) {
            if ($landInfo['p_type'] == 'F') {
                return [];
            }
        }

        $modelOrder = new OrderHandler();
        //$dname           = $this->_memModel->getMemberCacheById($orderInfo['member'], 'dname');

        $queryParams = [[$orderInfo['member']]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);
        // pft_log('debug/check_pay', json_encode([$outTradeNo, $queryRes], JSON_UNESCAPED_UNICODE));

        if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
            return [];
        }
        $dname = array_column($queryRes['data'], null, 'id')[$orderInfo['member']]['dname'];

        $tickets         = [
            $orderInfo['tid'] => ['num' => $orderInfo['tnum']],
        ];
        $orderCheckParam = [
            'member'       => isset($orderInfo['real_member_id']) ? $orderInfo['real_member_id'] : $orderInfo['member'],
            'lid'          => $orderInfo['lid'],
            'tid'          => $orderInfo['tid'],
            'tnum'         => $orderInfo['tnum'],
            'callback'     => $orderInfo['callback'],
            'remotenum'    => $orderInfo['remotenum'],
            'status'       => 0,
            'salerid'      => $orderInfo['salerid'],
            'verified_num' => 0,
            'paymode'      => $orderInfo['paymode'],
            'ordermode'    => $orderInfo['ordermode'],
            'pay_status'   => 1,
        ];

        if ($checkSource == 4) {
            //云票务
            $tmpMemo = '';
        } else {
            $tmpMemo = '终端购票即验证';
        }

        if ($verify) {
            //验证操作人员ID取支付人员ID，没有的话取下单人ID
            if (!$checkMember) {
                $subTrack    = new SubOrderTrack();
                $trackInfo   = $subTrack->getTrackListByOrderAndAction($outTradeNo, 0, 'oper_member');
                $checkMember = intval($trackInfo['oper_member']);
            }
        }

        //验证操作
        if ($verify) {
            if ($isBatch) {
                $modelOrder->CheckOrderSimply($outTradeNo, $checkMember, $orderCheckParam, $tmpMemo, $checkSource,
                    $dtime = false, $terminal, $printNum);
            } else {
                $modelOrder->CheckOrderSimply($outTradeNo, $checkMember, $orderCheckParam, $tmpMemo, $checkSource,
                    $dtime = false, $terminal);
            }
        }

        $res = \Library\Cache\Cache::getInstance('redis', ['master'])->set('combine_buy_verify:' . $outTradeNo, '验证成功',
            '', 500);
        //检测是否联票
        $subOrderIds = $this->_toolModel->getLinkSubOrder($outTradeNo);
        //如果是联票，获取子票id
        if ($subOrderIds) {
            //$subOrderArr = array_column($subOrderIds, 'orderid');
            $subTickets = $this->_toolModel->getOrderListNew($subOrderIds, 'ordernum,tid, tnum, tprice');
            foreach ($subTickets as $item) {

                $tickets[$item['tid']]['num'] = $item['tnum'];
                $orderCheckParam['tid']       = $item['tid'];
                $orderCheckParam['tnum']      = $item['tnum'];
                if ($verify) {
                    $modelOrder->CheckOrderSimply($item['ordernum'], $checkMember, $orderCheckParam, $tmpMemo,
                        $checkSource,
                        $dtime = false, $terminal);
                }
            }
        }

        //处理门票信息
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds(array_keys($tickets), 'id,title');

        $ticketsList = [];
        foreach ($ticketArr as $ticketInfos) {
            $ticketsList[$ticketInfos['ticket']['id']] = [
                'tid'   => $ticketInfos['ticket']['id'],
                'title' => $ticketInfos['ticket']['title'],
            ];
        }

        //获取小票需要显示的单价 - 门市价
        $tidArr   = array_keys($tickets);
        $tidStr   = implode(',', $tidArr);
        $newDate  = date('Y-m-d');
        $priceRes = TicketApi::getSinglePrices($tidStr, $newDate);

        foreach ($ticketsList as $item) {
            $tmpTid   = $item['id'];
            $tmpPrice = isset($priceRes[$tmpTid]) ? $priceRes[$tmpTid]['counter_price'] : -1;

            $tickets[$item['id']]['title'] = $item['title'];
            $tickets[$item['id']]['price'] = $tmpPrice;
        }

        $codeLength = strlen($orderInfo['code']);
        $output     = [
            'order'   => $outTradeNo,
            'dname'   => $dname,
            'code'    => ($codeLength > 6 && $codeLength < 8) ? str_pad($orderInfo['code'], 8, '0',
                STR_PAD_LEFT) : $orderInfo['code'],
            'member'  => $orderInfo['ordername'],
            'mobile'  => $orderInfo['ordertel'],
            'paymode' => $paymode,
            'dtime'   => date('Y-m-d H:i:s'),
            'ptime'   => date('Y-m-d H:i:s'),
            'money'   => $totalFee,
            'tickets' => array_values($tickets),
            'ptype'   => $landInfo['p_type'],
        ];
        //如果是演出产品查询分区座位
        if ($landInfo['p_type'] == 'H' && $orderInfo['series'] != '') {
            //淘宝那边进来的订单 series = taoXbao_4208780317814408，反序列化会报错
            $tmpSeries = @unserialize($orderInfo['series']);
            [$t, $zone, $seat] = explode(',', $tmpSeries[6]);
            $output['zone']     = explode(':', $zone)[1];
            $output['seat']     = $tmpSeries[5];
            $output['showtime'] = $tmpSeries[4];

            $ticketExtInfo = [];
            $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
            $getTicketInfo = $ticketBiz->queryTicketAttrsById($orderInfo['tid']);
            if ($getTicketInfo['code'] == 200) {
                foreach ($getTicketInfo['data'] as $attr) {
                    if ($attr['key'] == 'is_show_seat') {
                        $ticketExtInfo[$attr['ticket_id']] = $attr;
                    }
                }
            }

            //$ticketExtInfo = $this->_ticModel->getTicketExtConfig([$orderInfo['tid']], 'ticket_id,key,val', ['is_show_seat']);
            if (!empty($ticketExtInfo[$orderInfo['tid']]['val']) && $ticketExtInfo[$orderInfo['tid']]['val'] == 2) {
                $output['seat'] = '';
            }
        }
        unset($tickets);
        unset($ticketsList);
        unset($modelTicket);
        unset($modelMember);

        return $output;
    }

    /**
     * 买即验RPC调用
     * User: lanwanhui
     * Date: 2021/6/24
     *
     * @param  string  $outTradeNo  订单号
     * @param  array  $orderInfo  订单信息数组
     * @param  int  $totalFee  支付金额
     * @param  int  $paymode  支付方式
     * @param  int  $terminal  终端号
     * @param  int  $checkSource  操作来源
     * @param  int  $checkMember  操作员
     * @param  int  $subSid  子商户支付的时候，这个是子商户的供应商id
     *
     * @return array
     */
    public function getOrderInfoForPrintByRpc($outTradeNo, $orderInfo, $totalFee, $paymode, $terminal, $checkSource, $checkMember, $subSid = 0)
    {
        try {

            $rpcClinet = new \Library\JsonRpc\PftRpcClient('pft_scenic_local_service');

            $params = [
                $outTradeNo,
                $orderInfo,
                $totalFee,
                $paymode,
                $terminal,
                $checkSource,
                $checkMember,
                ApplicationContext::get('device_key', ""),
                $subSid,
            ];

            $data = $rpcClinet->call('Verify/OrderBuyVerify/orderBuyAfterVery', $params, 'check');

            return $data;

        } catch (\Exception $e) {
            return [];
        }

    }

    /**
     * 根据日期范围查询订单
     *
     * <AUTHOR>
     * @date   2017-04-13
     *
     * @param  int  $memberId  会员id
     * @param  int  $sid  上级ID
     * @param  string  $beginDate  开始日期
     * @param  string  $endDate  截至日期
     * @param  string  $dateType  0 : 下单时间, 1 : 游玩时间 , 2 : 验证时间
     * @param  string  $status  订单状态，-1不限
     * @param  int  $page  当前页码
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function searchOnlyByDate($memberId, $sid, $beginDate, $endDate, $dateType = 0, $status = -1, $memId = 0, $memType = 0, $page = 1, $size = 15)
    {
        return $this->returnData(200, '', '暂时关闭，请联系管理员');
        /*$dbStart        = false;
        $dbEnd          = false;
        $orderTimeStart = false;
        $orderTimeEnd   = false;
        $dtimeStart     = false;
        $dtimeEnd       = false;

        if ($dateType == 0) {
            //下单时间
            $orderTimeStart = date('Y-m-d H:i:s', strtotime($beginDate));
            $orderTimeEnd   = date('Y-m-d H:i:s', strtotime($endDate));
        } else {
            //验证时间
            $dtimeStart = date('Y-m-d H:i:s', strtotime($beginDate));
            $dtimeEnd   = date('Y-m-d H:i:s', strtotime($endDate));
        }

        if ($memId) {
            if ($memType == 0) {
                //我分销给某人的订单
                $queryParams = $this->_makeSellOthersParam($memberId, $sid, $memId, $dbStart, $dbEnd, $orderTimeStart,
                    $orderTimeEnd, $dtimeStart, $dtimeEnd, $page, $size, [], $status);
            } elseif ($memType == 1) {
                //我采购某人的订单
                $queryParams = $this->_makeBuyOthersParam($memberId, $sid, $memId, $dbStart, $dbEnd,
                    $orderTimeStart, $orderTimeEnd, $dtimeStart, $dtimeEnd, $page, $size, [], $status);
            } else {
                //我采购/分销某人的订单
                $queryParams = $this->_makeBuyOrSellOthersParam($memberId, $sid, $memId, $dbStart, $dbEnd,
                    $orderTimeStart, $orderTimeEnd, $dtimeStart, $dtimeEnd, $page, $size, [], $status);
                $queryParams['selleridIn'] = [$sid, $memId];
                $queryParams['buyeridIn'] = [$sid, $memId];
            }
        } else {
            //我采购/分销的订单
            $queryParams = $this->_makeBuyOrSellParam($memberId, $sid, $dbStart, $dbEnd,
                $orderTimeStart, $orderTimeEnd, $dtimeStart, $dtimeEnd, $page, $size, [], $status);
            $queryParams['selleridIn'] = $sid;
        }

        try {
            $convertParams = QueryParamConverter::convert($queryParams);
            $convertParams['param']['memberId'] = $sid;
            $queryRes = (new BizProductOrder())->pageInfoBest($convertParams);
            $orderArr = array_column($queryRes['rows'], 'productOrderId');
        } catch (\Exception $e) {
            $orderArr = [];
        }

        $return = [];
        if ($orderArr) {
            //联票
            //$linkOrder = $this->_toolModel->getOrderConcatId($orderArr);
            $queryParams = [$orderArr, 'orderid', 'concat_id'];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail', 'queryOrderDetailsByOrdernums',
                $queryParams);
            $linkOrder   = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $linkOrder = $queryRes['data'];
            }
            $linkOrder = array_filter($linkOrder);

            if ($linkOrder) {
                //如果有联票订单,需要获取到所有的子票订单号
                $mainArr = array_values($linkOrder);
                //获取子票订单号
                $subList = $this->_toolModel->getLinkSubOrderInfo($mainArr, 'orderid,concat_id');
                //合并
                foreach ($subList as $subArr) {
                    $orderArr = array_merge($orderArr, [$subArr['orderid'], $subArr['concat_id']]);
                }
            }

            $orderArr = array_unique($orderArr);
            //获取每笔订单详细信息
            $return = $this->_getOrderDetails($memberId, $orderArr);
        }

        return $this->returnData(200, '', $return);*/
    }

    /**
     * 根据订单号搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  string  $ordernum  订单号
     *
     * @return array
     */
    public function searchByOrdernum($memberId, $ordernum)
    {
        //获取主票信息
        $main = $this->_toolModel->getOrderInfo($ordernum);

        if (!$main) {
            return $this->returnData(204, '查无订单信息');
        }

        //获取联票信息
        $field      = 'ss.ordernum';
        $linkOrders = $this->_toolModel->getLinkOrdersInfo($ordernum, $field);

        if ($linkOrders) {
            $orderArr = array_column($linkOrders, 'ordernum');
        } else {
            $orderArr[] = $ordernum;
        }

        $list = $this->_getOrderDetails($memberId, $orderArr);

        if (!$list) {
            return $this->returnData(204, '查无此订单');
        }

        return $this->returnData(200, '', $list);
    }

    /**
     * 根据手机号搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  int  $mobile  联系人手机号
     * @param  string  $beginDate  开始日期
     * @param  string  $endDate  截至日期
     * @param  string  $dateType  日期类型
     * @param  int  $status  订单状态
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function searchByMobile(
        $memberId,
        $mobile,
        $beginDate,
        $endDate,
        $dateType = 0,
        $status = -1,
        $memId = 0,
        $memType = 0,
        $page = 1,
        $size = 15)
    {

        if (!\ismobile($mobile)) {
            return [];
        }

        $orderArr = $this->_queryModel->getRelativeOrdersForMobile(
            $memberId,
            $mobile,
            $beginDate,
            $endDate,
            $dateType,
            $status,
            $memId,
            $memType,
            $page,
            $size
        );

        $return = [];
        if ($orderArr) {
            $return = $this->_getOrderDetails($memberId, $orderArr);
        }

        return $this->returnData(200, '', $return);

    }

    /**
     * 根据景区id搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  int  $landId  景区id
     * @param  string  $beginDate  开始日期
     * @param  string  $endDate  截至日期
     * @param  string  $dateType  日期类型
     * @param  int  $status  订单状态
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function searchByProduct(
        $memberId,
        $landId,
        $beginDate,
        $endDate,
        $dateType = 0,
        $status = -1,
        $memId = 0,
        $memType = 0,
        $page = 1,
        $size = 15)
    {

        if (intval($landId) < 1) {
            return [];
        }

        $orderArr = $this->_queryModel->getRelativeOrdersForProduct(
            $memberId,
            $landId,
            $beginDate,
            $endDate,
            $dateType,
            $status,
            $memId,
            $memType,
            $page,
            $size
        );

        $return = [];
        if ($orderArr) {
            $return = $this->_getOrderDetails($memberId, $orderArr);
        }

        return $this->returnData(200, '', $return);
    }

    /**
     * 根据远端订单号订单号搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  string  $remoteOrder  订单号
     *
     * @return array
     */
    public function searchByRemoteOrder($memberId, $remoteOrder)
    {

        if (!$remoteOrder || !$memberId) {
            return [];
        }

        $ordernum = $this->_toolModel->getOrdernumByRemote($remoteOrder);

        if (!$ordernum) {
            return [];
        }

        return $this->searchByOrdernum($memberId, $ordernum);

    }

    /**
     * 根据远端订单号订单号搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  string  $thirdOrder  订单号
     *
     * @return array
     */
    public function searchByThirdOrder($memberId, $thirdOrder)
    {

        if (!$thirdOrder || !$memberId) {
            return [];
        }

        $apiOrder = new \Model\Ota\AllApiOrderModel();

        $where = ['apiOrder' => $thirdOrder];

        $orderInfo = $apiOrder->getInfo('pftOrder', $where);

        if (!$orderInfo) {
            return [];
        }

        return $this->searchByOrdernum($memberId, $orderInfo['pftOrder']);
    }

    /**
     * 根据身份证搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  int  $idCard  身份证号
     * @param  string  $beginDate  开始日期
     * @param  string  $endDate  截至日期
     * @param  string  $dateType  日期类型
     * @param  int  $status  订单状态
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function searchByIdCard(
        $memberId,
        $idCard,
        $beginDate,
        $endDate,
        $dateType = 0,
        $status = -1,
        $memId = 0,
        $memType = 0,
        $page = 1,
        $size = 15)
    {

        if (!$memberId || !\Library\Tools::personID_format_err($idCard)) {
            return [];
        }

        $orderArr = $this->_queryModel->getRelativeOrdersForIdCard(
            $memberId,
            $idCard,
            $beginDate,
            $endDate,
            $dateType,
            $status,
            $memId,
            $memType,
            $page,
            $size
        );

        $return = [];
        if ($orderArr) {
            $return = $this->_getOrderDetails($memberId, $orderArr);
        }

        return $this->returnData(200, '', $return);
    }

    /**
     * 根据联系人搜索订单
     *
     * <AUTHOR>
     * @date   2017-06-19
     *
     * @param  int  $memberId  会员id
     * @param  int  $linkman  联系人姓名
     * @param  string  $beginDate  开始日期
     * @param  string  $endDate  截至日期
     * @param  string  $dateType  日期类型
     * @param  int  $status  订单状态
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function searchByLinkman(
        $memberId,
        $linkman,
        $beginDate,
        $endDate,
        $dateType = 0,
        $status = -1,
        $memId = 0,
        $memType = 0,
        $page = 1,
        $size = 15)
    {

        if (!$memberId || !$linkman) {
            return [];
        }

        $orderArr = $this->_queryModel->getRelativeOrdersForLinkman(
            $memberId,
            $linkman,
            $beginDate,
            $endDate,
            $dateType,
            $status,
            $memId,
            $memType,
            $page,
            $size
        );

        $return = [];
        if ($orderArr) {
            $return = $this->_getOrderDetails($memberId, $orderArr);
        }

        return $this->returnData(200, '', $return);
    }

    /**
     * 获取票类凭证码信息
     * <AUTHOR>
     * @since 2017-05-17
     *
     * @param  array  $orderNumArr  订单号数组  ['4010290', '4010291']
     * @param  int|null  $code  凭证号
     *
     * @return array
     */
    public function getOrderCodeInfo($orderNumArr, $code = null)
    {
        if (!$orderNumArr || !is_array($orderNumArr)) {
            return $this->returnData(203, '参数错误', []);
        }

        //去重复订单号
        $orderNumArr = array_unique($orderNumArr);
        //把订单号转为字符串
        $orderNumArr = array_map(function ($ordernum) {
            return strval($ordernum);
        }, $orderNumArr);

        //订单状态筛选(0-未使用，1-已使用)
        $checkStatus = [0, 1];

        $queryParams  = [$orderNumArr, false, [], [], $checkStatus];
        $queryRes     = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
            'queryOrderTouristInfoByOrderId', $queryParams);
        $touristInfos = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $touristInfos = $queryRes['data'];
        }
        //获取游客的信息
        //$touristInfos = $this->_queryModel->getOrderTicketCodes($orderNumArr, $checkStatus, false);
        if (!$touristInfos) {
            return $this->returnData(204, '查询失败', []);
        }

        //获取处理过的码
        //$codeRes = $this->_queryModel->genOrderMultiCode($touristInfos, $code);
        $codeRes = $this->_queryModel->genOrderMultiCode($touristInfos);
        if (!$codeRes) {
            return $this->returnData(204, '查询失败', []);
        }

        $orderToolsModel = new \Model\Order\OrderTools();
        //查询订单的相关信息，为了获取对应门票tid
        $orderInfoRes = $orderToolsModel->getOrderInfo($orderNumArr, 'ordernum, tid');
        if (!$orderInfoRes) {
            return $this->returnData(204, '查询失败', []);
        }

        //门票tid数组
        $tidArr = [];
        //归纳的订单信息数组
        $orderInfos = [];
        foreach ($orderInfoRes as $ois) {
            //归纳订单信息
            $orderInfos[$ois['ordernum']] = $ois;
            //提取tid
            $tidArr[] = $ois['tid'];
        }
        //去除重复tid
        $tidArr = array_unique($tidArr);
        //根据tid获取门票信息，为了获取门票名称
        $ticketList = $this->_ticModel->getTicketList($tidArr, 'id, title');

        //凭证码信息
        $codeInfos = [];
        foreach ($touristInfos as $info) {
            //组装凭证码的具体信息
            $tmpOrderid  = $info['orderid'];
            $tmpIdx      = $info['idx'];
            $tmpCode     = $codeRes[$tmpOrderid][$tmpIdx] ?: '';
            $tmpTid      = $orderInfos[$tmpOrderid]['tid'] ?: 0;
            $tmpTtitle   = $ticketList[$tmpTid]['title'] ?: '';
            $codeInfos[] = [
                'orderid' => $tmpOrderid,
                'ttitle'  => $tmpTtitle,
                'idcard'  => $info['idcard'],
                'checked' => $info['check_state'],
                'code'    => $tmpCode,
                'idx'     => $info['idx'],
            ];
            unset($tmpOrderid, $tmpIdx, $tmpCode, $tmpTid, $tmpTtitle);
        }

        return $this->returnData(200, '', $codeInfos);
    }

    /**
     * 获取订单详细信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  int  $memberId  登陆用户信息
     * @param  string  $ordernum  主订单号
     * @param  int  $currentId  当前登入的用户id
     *
     * @return array
     */
    public function getOrderDetail($memberId, $ordernum, $currentId = '', $orderInfoHiding = false)
    {
        $this->_sid             = $memberId;
        $this->_memberid        = $currentId;
        $this->_orderInfoHiding = $orderInfoHiding;
        //获取主票信息
        $main = $this->_toolModel->getOrderInfo($ordernum, '*', false, true);

        if (!$main) {
            return $this->returnData(204, '查无订单信息');
        }

        //获取联票信息
        $field      = 'ss.ordernum';
        $linkOrders = $this->_toolModel->getLinkOrdersInfo($ordernum, $field);

        if ($linkOrders) {
            $orderArr = array_column($linkOrders, 'ordernum');
        } else {
            $orderArr[] = $ordernum;
        }

        $detail = $this->_getOrderDetails($memberId, $orderArr);
        if (!$detail) {
            return $this->returnData(204, '无权查看本订单');
        }

        $queryParams = [$orderArr];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
            'queryOrderTouristInfoByOrderId', $queryParams);
        $touristInfo = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $touristInfo = $queryRes['data'];
        }

        //$touristInfo = $this->getTouristInfoByOrderId($orderArr);

        $return = array_pop($detail);
        //取票地址
        $ticInfo = $this->_ticModel->getTicketInfoById($main['tid'], 'getAddr,expire_action_days');
        //产品类型
        $type = $this->_ticModel->getProductType($main['pid']);

        //显示优惠信息

        $couponInfo = $this->getDiscountTotalPrice($memberId, $main, $return['ext_content']);
        $return['couponInfo'] = $couponInfo;

        //if ($type == 'J') {
        //    //演出类型需要获取场次信息
        //    $Order      = new \Model\Order\OrderTools();
        //    $orderExtra = $Order->getOrderDetailInfo($ordernum);
        //
        //    //特产类型，需要展示收货/取货信息
        //    $productExt = json_decode($orderExtra['product_ext'], true);
        //
        //    //快递还是自提,0快递自提
        //    $deliveryType = $productExt['deliveryType'] ?? 1;
        //    $expInfo      = $selfPickup = [];
        //    if ($deliveryType == 0) {
        //        //获取收货人信息
        //        $orderUserModel = new \Model\Order\OrderUser();
        //        $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($ordernum,
        //            'province_code,city_code,town_code,address');
        //        $areaCodeArr    = [
        //            $orderUserInfo['province_code'],
        //            $orderUserInfo['city_code'],
        //            $orderUserInfo['town_code'],
        //        ];
        //        $areaCodeArr    = array_filter($areaCodeArr);
        //        if ($areaCodeArr) {
        //            $areaModel = new \Model\Product\Area();
        //            $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
        //        } else {
        //            $codeMap = [];
        //        }
        //
        //        //快递物流信息
        //        $expInfo = [
        //            'carriage'      => $productExt['carriage'] ?? 0,
        //            'province_code' => $codeMap[$orderUserInfo['province_code']] ?? '',
        //            'city_code'     => $codeMap[$orderUserInfo['city_code']] ?? '',
        //            'town_code'     => $codeMap[$orderUserInfo['town_code']] ?? '',
        //            'address'       => $orderUserInfo['address'] ?? '',
        //        ];
        //    } else {
        //        $pickPointApi = new \Business\JavaApi\Express\PickPoint();
        //        $pickData     = $pickPointApi->queryExpressPickPointByTid($main['tid']);
        //        if ($pickData) {
        //            $areaCodeArr = explode('|', $pickData['areaCode']);
        //            $areaCodeArr = array_filter($areaCodeArr);
        //            if ($areaCodeArr) {
        //                $areaModel = new \Model\Product\Area();
        //                $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
        //            } else {
        //                $codeMap = [];
        //            }
        //            //自提点信息
        //            $selfPickup = [
        //                'acceptTime' => $pickData['acceptTime'],
        //                'area'       => implode('', array_values($codeMap)),
        //                'address'    => $pickData['address'],
        //            ];
        //        } else {
        //            $selfPickup = [];
        //        }
        //    }
        //
        //    $specialInfo = ['delivery_way' => $deliveryType, 'exp_info' => $expInfo, 'pick_info' => $selfPickup];
        //}

        //$return['specialInfo'] = $specialInfo;
        $return['getAddr'] = $ticInfo['getAddr'];
        //下单后多久时间取货
        $return['expire_action_days'] = $ticInfo['ticket']['expire_action_days'];
        //期票是否需要线上预约
        $return['is_online_reserve']    = $ticInfo['ext']['is_online_reserve'] ?? 0; //线上预约0：无需线上预约 1：需线上预约
        $return['is_advance_reserve']   = $ticInfo['ext']['is_advance_reserve'] ?? 0; //提前预约 0：当天可预约 1：需提前预约
        $return['advance_reserve_time'] = empty($ticInfo['ext']['advance_reserve_time']) ? [] :
            json_decode($ticInfo['ext']['advance_reserve_time'], true);//提前预约的时间 当且仅当需提前预约时才有效 {"day":1,"time":"23:59"}
        //游玩时间
        $return['playtime'] = $main['playtime'];
        //身份证
        $return['id_card'] = $return['personid'];
        //重发短信次数
        $return['remsg'] = $main['remsg'] ? $main['remsg'] : '非平台发送';
        //开始时间
        $return['begintime'] = $main['begintime'];
        //结束时间
        $return['endtime'] = $main['endtime'];
        //凭证码
        $return['code'] = $return['code'];
        //产品类型
        $return['ptype'] = $type;
        // //游客信息
        // $return['visitor_info'] = $touristInfo;
        // var_dump($touristInfo);die;

        //景区门票、线路、套票，需要取票状态  $isPrintState 展示取票状态：1展示；2不展示
        //ifprint: 0,1,2  (未打印、已打印、部分打印)->(未取票、已取票、部分取票)
        if (in_array($type, $this->_printStateAllowTypeConf)) {
            $ifPrint      = $main['ifprint'];
            $isPrintState = 1;
            if (empty($this->_printStateConf[$ifPrint])) {
                $ifPrint = '';
            } else {
                $ifPrint = $this->_printStateConf[$ifPrint];
            }
        } else {
            $isPrintState = 2;
            $ifPrint      = ''; //默认为空
        }
        //是否展示
        $return['is_print_state'] = $isPrintState;
        //订单取票状态
        $return['if_print'] = $ifPrint;

        $return = $this->_getExtraInfoForType($return, $type);
        //判断订单是否展示凭证码喝预计游玩时间,false不展示，复制为空
        $show = $this->_showPlayTimeAndCode($return);

        if ($type == 'A') {
            //外部码处理
            $return = $this->_showExternalCode($return);
        }

        if ($type == 'H') {
            //演出的需要判断下供应商是否有选座的功能
            $configModel = new \Model\AdminConfig\AdminConfig();
            $result      = $configModel->havePermission($this->_sid, 44);
            $return['select_seat'] = $result ? 1 : 0;
        }

        if ($show === false) {
            $return['code']     = "";
            $return['playtime'] = "";
        }

        //短信实际发送数量，未支付短信发送次数为0
        if ($return['pay_status'] == 2) { //未支付情况，不会发送短信
            if ($return['remsg'] != '非平台发送') {
                $return['remsg'] = '0次';
            }
        } else {
            if ($return['remsg'] != '非平台发送') {
                $return['remsg'] = $return['remsg'] . '次';
            }
        }

        return $this->returnData(200, '', $return);
    }

    /**
     * 订单详情显示折扣优惠金额
     * <AUTHOR>
     * @date 2021/8/14
     *
     * @param  int  $uid 用户ID
     * @param  array  $orderInfo 订单用户信息
     * @param  array  $exContent 订单扩展数据
     *
     * @return array
     */
    private function getDiscountTotalPrice(int $uid, array $orderInfo, string $exContent)
    {
        $result = [
            'discountTotalPrice' => 0,
        ];
        $exContent = json_decode($exContent, true);
        if (empty($exContent['settlementDiscount'])) {
            return $result;
        }

        $fid         = (int)$exContent['settlementDiscount'][0]['fid'];
        $couponMoney = (int)$exContent['settlementDiscount'][0]['couponMoney'];

        if ($uid == 1) {
            $result['discountTotalPrice'] = $couponMoney * $orderInfo['tnum'];
        }

        if ($orderInfo['apply_did'] == $uid) {
            $result['discountTotalPrice'] = $couponMoney * $orderInfo['tnum'];
        }

        if ($fid == $uid) {
            $result['discountTotalPrice'] = $couponMoney * $orderInfo['tnum'];
        }

        return $result;
    }

    /**
     * 获取游客信息
     * Create by zhangyangzhen
     * Date: 2018/11/20
     * Time: 11:54
     *
     * @param $orderIdArr
     *
     * @return array
     */
    private function getTouristInfoByOrderId($orderIdArr)
    {
        if (empty($orderIdArr)) {
            return [];
        }

        $data         = [];
        $touristModel = new \Model\Order\OrderTourist();
        $res          = $touristModel->getTouristInfoByOrderId($orderIdArr, 'idcard, tourist, idx');

        foreach ($res as $item) {
            $data[$item['idcard']] = $item['tourist'];
        }

        return $data;
    }

    /**
     * 订单操作记录
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  int  $memberId  会员id
     * @param  string  $ordernum  订单号
     *
     * @return array
     */
    public function operatingRecord($memberId, $ordernum)
    {

        //获取主票信息
        $main = $this->_toolModel->getOrderInfo($ordernum, 'ss.id', 'de.aids');

        if (!$main) {
            return $this->returnData(204, '查无订单信息');
        }

        //获取联票信息
        $field      = 'ss.ordernum';
        $linkOrders = $this->_toolModel->getLinkOrdersInfo($ordernum, $field);

        if ($linkOrders) {
            $orderArr = array_column($linkOrders, 'ordernum');
        } else {
            $orderArr = [$ordernum];
        }

        //$field = 'action,tid,tnum,left_num,source,oper_member,insertTime, ext_content';
        //$list  = $this->_trackModel->getListByOrders($orderArr, $field, false, 'id asc');

        //订单查询迁移二期
        $orderTrackQueryLib = new OrderTrackQuery();
        $list               = $orderTrackQueryLib->getLogByOrderId($orderArr);

        if (!$list) {
            return $this->returnData(200, '', []);
        }
        //动作映射
        $actionMap = OrderTrack::getActionList();
        //渠道映射
        $sourceMap = OrderTrack::getSourceList();
        //票类名称映射
        $tidArr    = array_unique(array_column($list, 'tid'));
        $javaApi   = new ticketBiz();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title');
        $nameMap   = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticket) {
                $nameMap[$ticket['ticket']['id']] = $ticket['ticket']['title'];
            }
        }
        //$nameMap = $this->_ticModel->getTtitleMapping($tidArr);
        //操作员名称映射
        $memArr = array_unique(array_column($list, 'oper_member'));
        $memMap = $this->_memModel->getMemberInfoByMulti($memArr, 'id', 'id,dname', true);

        $return = [];
        foreach ($list as $item) {
            $tmp = [
                'pre_num'  => $item['tnum'],
                'left_num' => $item['left_num'],
                'time'     => $item['insertTime'],
                'title'    => $nameMap[$item['tid']],
                'action'   => $actionMap[$item['action']],
            ];

            //操作人员的查看权限
            $see = $this->_hasRightToSeeOp($memberId, $main['aids'], $item['oper_member']);

            if ($see) {
                $tmp['op_memebr'] = $memMap[$item['oper_member']]['dname'];
            } else {
                $tmp['op_memebr'] = '';
            }

            if ($item['action'] == 5) {
                $tmp['source'] = $sourceMap[$item['source']];
            }

            $extContent = [];
            if ($item['ext_content'] && $item['ext_content'] != "{}") {
                $extContent = json_decode($item['ext_content'], true);
            }
            $tmp['ticket_can_take_num'] = isset($extContent['can_take']) ? $extContent['can_take'] : "-1";
            $tmp['ticket_can_refund']   = isset($extContent['can_refund']) ? $extContent['can_refund'] : "-1";

            $return[] = $tmp;
        }

        return $this->returnData(200, '', $return);
    }

    /**
     * 是否能查看操作人员
     *
     * <AUTHOR>
     * @date   2017-05-23
     *
     * @param  int  $memberId  当前会员id
     * @param  string  $aids  订单分销链
     * @param  int  $opId  操作人员
     *
     * @return boolean
     */
    private function _hasRightToSeeOp($memberId, $aids, $opId)
    {

        static $staffArr = [];

        if (!$staffArr) {
            $relationModel = new MemberRelationship();
            $staffArr      = $relationModel->getStaffList($memberId);
        }

        if (!$opId) {
            return false;
        }

        if ($memberId != $opId && !in_array($opId, $staffArr)) {
            return false;
        }

        return true;

    }

    /**
     * 是否显示取消按钮
     *
     * <AUTHOR>
     * @date   2017-04-26
     *
     * @param  mixed  $ordernum  订单号
     * @param  int  $memberId  当前用户id
     * @param  int  $channel  渠道
     *
     * @return array
     */
    public function cancelCheck($ordernum, $memberId)
    {
        //订单详情
        $field = 'member,ordernum,playtime,lid,tid,status,ordertime,product_type,apply_did';
        $order = $this->_toolModel->getOrderInfo($ordernum, $field);

        //是否退票记录
        $where  = [
            'stype'   => ['in', [2, 3]],
            'dstatus' => 0,
        ];
        $change = $this->_toolModel->getOrderChangeRecord($ordernum, 'id', $where);
        if ($change) {
            //退票中
            return [
                'status' => 0,
                'msg'    => '退票中,请勿重复申请',
            ];
        }

        $status = $order['status'];
        $ptype = $order['product_type'];

        if (!in_array($status, [0, 2, 7, 10, 80, 81, 11])) {
            return [
                'status' => 0,
                'msg'    => '订单状态不支持退票',
            ];
        }

        $orderIdArr = [$ordernum];
        //查找出套票子票订单信息集合
        $packOrder = $this->_toolModel->getPackSubOrder($orderIdArr, 'orderid, pack_order');
        //套票子票的订单号集合
        $packOrderRes = [];
        $packOrderArr = [];
        foreach ($packOrder as $item) {
            //主票包含的子票订单号
            $packOrderRes[$item['pack_order']][] = $item['orderid'];
            $packOrderArr[] = $item['orderid'];
        }

        //联票子票信息集合
        $linkOrder    = $this->_toolModel->getLinkSubOrderInfo($orderIdArr, 'orderid, concat_id');
        $linkOrderRes = [];
        $linkOrderArr = [];
        foreach ($linkOrder as $item) {
            if ($item['concat_id'] == $item['orderid']) {
                continue;
            }
            //联票包含的票
            $linkOrderRes[$item['concat_id']][] = $item['orderid'];
            $linkOrderArr[]                     = $item['orderid'];
        }

        //包含子票的订单集合
        $orderIdArr = array_merge($orderIdArr, $packOrderArr);
        //包含联票的订单集合
        $orderIdArr = array_merge($orderIdArr, $linkOrderArr);

        //订单详细信息获取
        $detailInfo = $this->_toolModel->getOrderDetailInfo($orderIdArr,
            'orderid, aids, aids_price, aids_money, concat_id, memo, series, assembly, origin, ext_content, product_ext');

        $orderSearchModel = new \Model\Order\OrderSearch('master');
        $orderAddonArr    = $orderSearchModel->getOrderAddon($orderIdArr, 'orderid, ifprint, id, ifpack, pack_order');

        //获取包含子票的所有订单的信息
        $orderInfo = $this->_toolModel->getOrderInfo($orderIdArr, 'ordernum, remotenum, ordertel, ordername, status, pay_status,
                    lid, tid, pid, aid, member, tnum, tprice, paymode, ordermode, totalmoney, code, remsg,playtime,
                    ordertime, begintime, endtime, dtime, personid, totalmoney');

        $tidArr    = array_unique(array_column($orderInfo, 'tid'));
        $ticketRes = OrderList::formatTicketRes($this->_ticModel, $tidArr);

        //以订单为key的订单信息 待调用退款
        $orderKeyOrderArr = array_column($orderInfo, null, 'ordernum');
        // 订单号key  detail数组
        $orderKeyDetailArr = array_column($detailInfo, null, 'orderid');

        $lib = new \Library\JsonRpc\PftRpcClient('common_service_router_proxy');
        $res = $lib->call('Order/Modify/check_can_refund', [$orderIdArr, $memberId, $orderKeyOrderArr, $orderAddonArr,
            $orderKeyDetailArr, $ticketRes, '', '', $packOrderRes], 'order');
        $result = $res['data'] ?? [];
        if (!empty($result['canRefund'][$ordernum])) {
            return [
                'status' => 1,
                'msg'    => $result['canRefund'][$ordernum]['msg'],
            ];
        } else {
            $msg = !empty($result['refuseRefund'][$ordernum]) ? $result['refuseRefund'][$ordernum]['msg'] : '订单不支持退票';
            return [
                'status' => 0,
                'msg'    => $msg,
            ];
        }
    }

    /**
     * 微商城订单列表是否显示取消按钮  ===>复制上面  cancelCheck 方法
     *
     * @editor wangdd
     * @date   2019/05/22
     *
     * @param  mixed  $ordernum  订单号
     * @param  int  $memberId  当前用户id
     *
     * @return int
     */
    public function mallOrderCancelCheck($ordernumArr, $memberIds)
    {
        //订单详情
        $field  = 'member,ordernum,playtime,lid,tid,status';
        $orders = $this->_toolModel->getOrderInfo($ordernumArr, $field);

        //是否退票记录
        $where      = [
            'stype'   => ['in', [2, 3]],
            'dstatus' => 0,
        ];
        $changeList = $this->_toolModel->getOrderChangeRecordList($ordernumArr, 'ordernum,id', $where);

        //获取门票信息
        $tidArr     = array_column($orders, 'tid');
        $field      = 'id,refund_rule,apply_did,refund_early_time';
        $ticketList = $this->_ticModel->getTicketInfoMulti($tidArr, $field);

        //产品获取信息
        $lidArr = array_column($orders, 'lid');

        $javaApi      = new \Business\CommodityCenter\LandF();
        $landInfoList = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], $lidArr, [], 'lid,p_type', true);

        $cancelOrder = [];
        foreach ($orders as $order) {
            $ordernum = $order['ordernum'];
            $lid      = $order['lid'];
            $tid      = $order['tid'];
            $status   = $order['status'];

            //该订单是否有退票记录
            $change = $changeList[$ordernum] ?: [];

            if ($change) {
                //退票中,请勿重复申请
                $cancelOrder[$ordernum] = 0;
                continue;
            }

            //订单状态不支持退票
            if (!in_array($status, [0, 2, 7])) {
                $cancelOrder[$ordernum] = 0;
                continue;
            }

            //是否是购买者进行取消
            $isBuyer = in_array($order['member'], $memberIds);

            //该订单的门票信息
            $ticket = $ticketList[$tid];

            //退票规则：添加了这个值 -1=不可退且是可提现
            //所以=-1的时候也是不可退
            if ($ticket['refund_rule'] == -1) {
                $ticket['refund_rule'] = 2;
            }

            $ruleAllow = 0;
            switch ($ticket['refund_rule']) {
                //有效期可退
                case 0:
                    if (in_array($status, [0, 7]) && $isBuyer) {
                        //未使用下单人可退
                        $ruleAllow = 1;

                    } else {
                        $ruleAllow = 0;
                    }
                    break;
                //游玩日期可退
                case 1:
                    if (in_array($status, [0, 7]) && $isBuyer) {
                        //产品获取信息
                        $landInfo = $landInfoList[$lid];

                        if ($status == 0 && $landInfo['p_type'] == 'J') {
                            $ruleAllow = 1;
                        } else {
                            //允许退票的时间
                            if ($landInfo['p_type'] == 'H' && $ticket['refund_after_time'] > 0) {
                                $allow = strtotime($order['playtime']) + 3600 * 24 + $ticket['refund_after_time'] * 60;
                            } else {
                                $allow = strtotime($order['playtime']) + 3600 * 24 - $ticket['refund_early_time'] * 60;
                            }
                            if (time() < $allow) {
                                $ruleAllow = 1;
                            }
                        }

                    } else {
                        $ruleAllow = 0;
                    }
                    break;
                //不可退
                case 2:
                    $ruleAllow = 0;
                    break;
                //随时退
                case 3:
                    if (in_array($status, [0, 2, 7]) && $isBuyer) {
                        //未使用、过期分销商可退
                        $ruleAllow = 1;

                    } else {
                        $ruleAllow = 0;
                    }
                    break;
                case 4:
                    //随子票退票规则设置，只有套票产品有
                    $sonTicket = HandleTicket::getSonTicket($order['tid']);
                    foreach ($sonTicket as $k => $v) {
                        if ($v['refund_rule'] == 2) {
                            $ruleAllow = 0;
                            break;
                        } else {
                            $ruleAllow = 1;
                        }
                    }
                    break;
                default:
                    $ruleAllow = 0;
            }

            if ($ruleAllow) {
                $cancelOrder[$ordernum] = 1;
            } else {
                //该门票设置了退票限制，不可退票
                $cancelOrder[$ordernum] = 0;
            }
        }

        return $cancelOrder;

    }

    /**
     * 获取订单详细信息
     *
     * <AUTHOR>
     * @date   2017-04-16
     *
     * @param  int  $memberId  用户id
     * @param  array  $orderArr  订单号数组
     * @param  bool  $showMoreCredential  是否展示游客取票人更多信息
     *
     * @return array
     */
    public function _getOrderDetails($memberId, $orderArr, $showMoreCredential = true)
    {
        $splitField = 'split.sellerid,split.buyerid,split.cost_money,split.sale_money,split.level,split.pmode';
        $ssField    = 'ss.id,ss.lid,ss.member,ss.tid,ss.pid,ss.ordernum,ss.ordermode,ss.tnum,ss.tprice,
            ss.ordertime,ss.dtime,ss.playtime,ss.status,ss.pay_status,ss.ordername,ss.ordertel,ss.remotenum,ss.salerid,ss.aid,ss.personid,ss.code,ss.totalmoney,ss.apply_did';

        $list = $this->_toolModel->getOrderInfoWithSplit(
            $orderArr,
            $ssField,
            'de.series,de.memo,de.ext_content,de.product_ext,de.aids',
            false,
            false,
            $splitField,
            'desc'
        );
        //资源账号
        //$member = new Member();
        //$memberInfo  = $member->getMemberCacheById($memberId, 'dtype,account');
        $queryParams = [[$memberId]];
        $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
            $queryParams);

        $memberInfo = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $memberInfo = array_column($queryRes['data'], null, 'id')[$memberId];
        }

        $account = isset($memberInfo['account']) ? $memberInfo['account'] : 0;

        if (!$account) {
            return [];
        }

        //微平台资源账号搜索订单处理
        if (isset($memberInfo['dtype']) && $memberInfo['dtype'] == 2) {
            //资源账号处理,这里注意为了确保不同的账号类型，这边需要取sid上级账号类型
            $orderDeliverBiz = new \Business\Order\OrderDeliver();
            $res             = $orderDeliverBiz->changeApplyDidByResourceId($account, $memberInfo['dtype']);
            $memberId        = isset($res['apply_did']) ? $res['apply_did'] : $memberId;
        }

        $return    = $pidArr = $lidArr = [];
        $ticketArr = [];
        //订单快照信息
        $snapshotOrder = [];

        if ($list) {
            //判断是否隐藏订单号
            $memberConf       = new \Model\AdminConfig\AdminConfig();
            $ifHide           = $memberConf->verifyAuthority($memberId);
            $orderModel       = new OrderTools('slave');
            $orderOtaBz       = new OrderOta();
            $allApiOrderModel = new AllApiOrderModel();
            $orderBiz         = new \Business\Order\OrderList();

            $ordernumArr = array_column($list, 'ordernum');
            //第三方凭证码和订单号
            $tmpAddonInfo = $orderModel->getOrderAddonInfo($ordernumArr, 'orderid,vcode,tordernum');
            if ($tmpAddonInfo) {
                $tmpAddonInfo = array_key($tmpAddonInfo, 'orderid');
            }

            //获取一次套票信息
            $packOrder    = $this->_toolModel->getPackSubOrder($ordernumArr);
            $packOrderRes = [];
            foreach ($packOrder as $item) {
                //主票包含的子票订单号
                $packOrderRes[$item['pack_order']][] = $item['orderid'];
            }

            $extContentMap = [];
            foreach ($list as $item) {
                $orderAids[$item['ordernum']] = $item['aid'];
                $orderMids[$item['ordernum']] = $item['member'];
                $ticketArr[$item['ordernum']] = $item['tid'];

                $extContent                       = json_decode($item['ext_content'], true) ?? [];
                $extContentMap[$item['ordernum']] = $extContent;
                $snapshotOrder[$item['ordernum']] = [
                    'version' => $extContent['ticketVersion'] ?? 0,
                    'tid'     => $item['tid'],
                ];
            }

            $tidArr    = array_unique(array_values($ticketArr));
            $ticketRes = $orderBiz->getTicketInfo($tidArr);

            //快照属性
            $snapshotOrder = $orderBiz->batchOrderTicketSnapShot($snapshotOrder);

            //查询订单供应价 零售价 已验证票数 已退票数
            $applyInfo     = $this->getApplyInfo($ordernumArr);
            $applyRes      = $applyInfo['applyRes'];

            //批量查询中间商隐藏客户关键信息配置缓存在对象中
            $orderInfoHidingBuz = new OrderInfoHiding();
            $tsids              = [];
            $currentOrderSplit  = [];
            foreach ($list as $item) {
                if (!empty($item['aids'])) {
                    //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                    $splitAids = $item['aids'] . ',' . $orderMids[$item['ordernum']];
                } elseif ($orderAids[$item['ordernum']] != $orderMids[$item['ordernum']]) {
                    //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                    $splitAids = $orderAids[$item['ordernum']] . ',' . $orderMids[$item['ordernum']];
                } else {
                    //自供自销
                    $splitAids = $orderAids[$item['ordernum']] . ',' . $orderAids[$item['ordernum']];
                }
                //分销链
                $splitAids                            = explode(",", $splitAids);
                $currentOrderSplit[$item['ordernum']] = $splitAids;
                $tsids[]                              = $splitAids[0];
            }
            //是否需要针对中间级分销商隐藏订单游客信息
            if ($this->_orderInfoHiding) {
                $tsids = array_unique($tsids);
                $re    = $orderInfoHidingBuz->batchGetHidingConfig($tsids);
            }
            //获取下单人详细信息
            $queryParams   = [$orderArr, true];
            $queryRes      = \Business\JavaApi\Order\Query\Container::query('orderUserInfo', 'getOrderUserInfoByOrderNum',
                $queryParams);
            $orderUserList = [];
            if ($queryRes['code'] == 200) {
                $orderUserList = $queryRes['data'];
            }

            $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
            $ticketBiz          = new \Business\Product\Ticket();
            $landBiz            = new \Business\CommodityCenter\Land();
            foreach ($list as $item) {
                $buyerid  = $item['buyerid'];
                $sellerid = $item['sellerid'];
                $ordernum = $item['ordernum'];
                $salerid  = $item['salerid'];
                //资源账号过滤
                if (isset($memberInfo['dtype']) && $memberInfo['dtype'] == 2) {
                    if ($salerid != $account) {
                        continue;
                    }
                }

                //多级转分销,跳过无效的信息
                if ($sellerid != $memberId && $buyerid != $memberId) {
                    continue;
                }
                if (!empty($item['aids'])) {
                    //如果订单在分销详情表里有数据且存在多级分销的情况  --多级分销
                    $splitAids = $item['aids'] . ',' . $orderMids[$item['ordernum']];
                } elseif ($orderAids[$item['ordernum']] != $orderMids[$item['ordernum']]) {
                    //如果订单在订单表里 分销商和供应商不是同一个 但是在分销详情表里没有 --一级分销
                    $splitAids = $orderAids[$item['ordernum']] . ',' . $orderMids[$item['ordernum']];
                } else {
                    //自供自销
                    $splitAids = $orderAids[$item['ordernum']] . ',' . $orderAids[$item['ordernum']];
                }
                //分销链
                $splitAids = explode(",", $splitAids);
                if (!isset($return[$ordernum])) {

                    if ($ifHide) {
                        $contacttel = substr_replace($item['ordertel'], "****", 3, 4);
                    } else {
                        $contacttel = $item['ordertel'];
                    }
                    //三方订单号/三方消费码
                    $thirdApiCode  = isset($tmpAddonInfo[$ordernum]['vcode']) ? $tmpAddonInfo[$ordernum]['vcode'] : '';
                    $thirdOrdernum = $orderOtaBz->handleThirdOrdernum($tmpAddonInfo[$ordernum]['tordernum']);
                    //order_addon没有从all_api_order取
                    if (empty($thirdApiCode)) {
                        $apiOrderInfo = $allApiOrderModel->getInfoByOrderId($ordernum, 'apiOrder, apiCode');
                        if (!empty($apiOrderInfo)) {
                            $thirdOrdernum = $apiOrderInfo['apiOrder'];
                            $thirdApiCode  = $apiOrderInfo['apiCode'];
                        }
                    }

                    //演出是否可预约
                    $ticketReserve     = false;
                    $isCanChangeTicket = 0;
                    $canWeekend        = 0;
                    $reserveTime       = '';
                    //演出预约有效期时间范围
                    $reserveTimeArr = [
                        'start_time' => 0,
                        'end_time'   => 0,
                    ];

                    //套票不能改签
                    if (!isset($packOrderRes[$ordernum]) || is_null($packOrderRes[$ordernum])) {
                        if (isset($snapshotOrder[$ordernum]['ticket_changing_range']) && isset($snapshotOrder[$ordernum]['pre_sale'])) {
                            $isOnlineReserve = isset($snapshotOrder[$ordernum]['is_online_reserve']) ? $snapshotOrder[$ordernum]['is_online_reserve'] : 0;
                            if ($snapshotOrder[$ordernum]['pre_sale'] == 0 || ($snapshotOrder[$ordernum]['pre_sale'] == 1 && $isOnlineReserve == 1)) {
                                $isCanChangeTicket = $snapshotOrder[$ordernum]['ticket_changing_range'];
                            }
                            $canWeekend = $snapshotOrder[$ordernum]['ticket_changing_weekend'];
                        }
                    }

                    if ($snapshotOrder[$ordernum]['pre_sale'] == 1 && $ticketRes[$item['tid']]['p_type'] == 'H' && $item['status'] == 10 && $item['pay_status'] != 2) {
                        $ticketReserve = true;

                        $tmpTicketRes = $commodityTicketBiz->queryTicketInfoById($item['tid']);
                        if ($tmpTicketRes) {
                            $ticketInfo  = array_merge($tmpTicketRes['land'], $tmpTicketRes['ext'], $tmpTicketRes['land_f'], $tmpTicketRes['ticket']);
                            $reserveTime = $ticketBiz->showReserveTime($ticketInfo);

                            //获取一次演出预约的开始时间和结束时间
                            if (isset($ticketInfo['reserve_storage'])) {
                                $reserveTimeArr = [
                                    'start_time' => strtotime($ticketInfo['reserve_storage']['start_time']),
                                    'end_time'   => strtotime($ticketInfo['reserve_storage']['end_time']),
                                ];
                            }
                        }
                    }

                    $showArr = [];
                    if (isset($ticketRes[$item['tid']]) && $ticketRes[$item['tid']]['p_type'] == 'H') {
                        $series = $item['series'] ?? '';
                        if ($series) {
                            $showInfo = $orderBiz->getShowInfo($item['series'] ?? '', $ticketArr, $ordernum, $ticketRes);
                        } else {
                            $showInfo = [
                                'venue_id' => $ticketRes[$item['tid']]['venus_id'],
                                'zone_id'  => $ticketRes[$item['tid']]['zone_id'],
                            ];
                        }

                        $venueId     = $showInfo['venue_id'] ?? '';
                        $zoneId      = $showInfo['zone_id'] ?? '';
                        $partition   = $showInfo['partition'] ?? '';
                        $seat        = $showInfo['seat'] ?? '';
                        $performance = $showInfo['performance'] ?? '';
                        $showArr     = [
                            'aid'         => $item['aid'],
                            'pid'         => $item['pid'],
                            'venue_id'    => $venueId,
                            'zone_id'     => $zoneId,
                            'partition'   => $partition,
                            'seat'        => $seat,
                            'performance' => $performance,
                        ];
                    }
                    $voucherType = $orderUserList[$ordernum]['voucher_type'];
                    $voucherTypeName = $this->conversionIdentityToName($voucherType);
                    //公共信息
                    $return[$ordernum] = [
                        'ordernum'                => $ordernum,
                        'remotenum'               => $item['remotenum'],
                        'dtime'                   => $item['dtime'],
                        'ordertime'               => $item['ordertime'],
                        'pay_status'              => $item['pay_status'],
                        'ordername'               => $item['ordername'],
                        'ordertel'                => $contacttel,
                        'member'                  => $item['member'],
                        'playtime'                => $item['playtime'],
                        'lid'                     => $item['lid'],
                        'status'                  => $item['status'],
                        'ordermode'               => $item['ordermode'],
                        'memo'                    => $item['memo'],
                        'ext_content'             => $item['ext_content'],
                        'apiorder'                => !empty($thirdOrdernum) ? $thirdOrdernum : '--',
                        'apicode'                 => !empty($thirdApiCode) ? $thirdApiCode : '--',
                        'remotenum'               => $item['remotenum'] ?: '-',
                        'product_ext'             => $item['product_ext'],
                        'personid'                => $item['personid'],
                        'voucher_type'                => $voucherType,
                        'voucher_type_name'                => $voucherTypeName,
                        'code'                    => $item['code'],
                        'ticket_list'             => [
                            [
                                'tid'    => $item['tid'],
                                'series' => $item['series'],
                                'pid'    => $item['pid'],
                                'tnum'   => $item['tnum'],
                                'status' => $item['status'],
                            ],
                        ],
                        'reserve_time'            => $reserveTime,
                        'reserve_time_map'        => $reserveTimeArr,
                        'ticket_reserve'          => $ticketReserve,
                        'ticket_changing_weekend' => $canWeekend,
                        'ticket_changing_range'   => $isCanChangeTicket,
                        'show_arr'                => $showArr,
                    ];
                }

                //优惠券的使用
                $couponName   = '';
                $couponAmount = 0;

                $useCouponLog = $orderBiz->getUseInfoByUseOrder($ordernum,
                    'fid, aid, all_use_coupon_money, use_order_num, coupon_code', $memberId);
                if (!empty($useCouponLog) && is_array($useCouponLog) && !in_array($memberInfo['dtype'], [2, 3])) {
                    if ($useCouponLog[0]['aid'] == $memberId) {
                        //优惠券名称(ID)
                        $couponName = $useCouponLog[0]['coupon_code'];
                        //优惠金额（分）
                        $couponAmount = $useCouponLog[0]['all_use_coupon_money'];

                    } elseif ($useCouponLog[0]['fid'] == $memberId) {
                        //优惠券名称(ID)
                        $couponName = $useCouponLog[0]['coupon_code'];
                        //优惠金额（分）
                        $couponAmount = $useCouponLog[0]['all_use_coupon_money'];
                    }
                } else {
                    $couponInfo = $orderBiz->getMemberCouponUseMulti($ordernum,
                        'count(*) as coupon_num, coupon_name, coupon_value, aid, fid', $memberId);
                    if (!empty($couponInfo) && is_array($couponInfo) && !in_array($memberInfo['dtype'], [2, 3])) {
                        if ($couponInfo[0]['aid'] == $memberId) {
                            //优惠券名称(ID)
                            $couponName = $couponInfo[0]['coupon_name'] . '(' . $couponInfo[0]['coupon_num'] . '张)';
                            //优惠金额（分）
                            $couponAmount = $couponInfo[0]['coupon_value'] * $couponInfo[0]['coupon_num'];
                        } elseif ($couponInfo[0]['fid'] == $memberId) {
                            //优惠券名称(ID)
                            $couponName = $couponInfo[0]['coupon_name'] . '(' . $couponInfo[0]['coupon_num'] . '张)';
                            //优惠金额（分）
                            $couponAmount = $couponInfo[0]['coupon_value'] * $couponInfo[0]['coupon_num'];
                        }
                    }
                }

                if ($memberId == $item['member']) {
                    //末级进来【卖出】需要显示零售价
                    $retail                                       = $this->_ticModel->getRetailPrice($item['pid'],
                        $item['playtime']);
                    $return[$ordernum]['ticket_list'][0]['price'] = $retail * 100;
                }

                if ($sellerid == $memberId) {
                    $price                                        = $item['sale_money'];
                    $return[$ordernum]['ticket_list'][0]['price'] = $price;
                }

                $buyMoney  = $item['cost_money'] * $item['tnum'];
                $saleMoney = $item['sale_money'] * $item['tnum'];

                $discountDetail = $orderBiz->getDiscountDetail($memberId, $item, $item['ext_content'], $couponAmount, $couponName, $snapshotOrder[$ordernum]['settlement_discount']);
                //处理买入金额
                $settlementDiscountFieldMap = DisCountType::SETTLEMENT_DISCOUNT_FIELD_MAP;
                if ($memberId == $item['member']) {
                    $settlementDiscountFieldMap = DisCountType::SETTLEMENT_DISCOUNT_FIELD_MAP_FOR_B;
                }
                $settlementDiscount = 0;
                foreach ($settlementDiscountFieldMap as $resKey => $fields) {
                    foreach ($fields as $field) {
                        $settlementDiscount += $discountDetail['discount_detail'][$resKey][$field] ?? 0;
                    }
                }

                $buySettlementDiscount  = !$discountDetail['is_sale_settlement_discount'] ? $settlementDiscount : 0;
                $saleSettlementDiscount = $discountDetail['is_sale_settlement_discount'] ? $settlementDiscount : 0;

                //判断是否使用积分或者优惠券  有使用 卖出价格直接展示实付金额
                if ($memberId == $item['aid'] && ($couponAmount || \Business\Order\BaseCheck::checkUseDiscount($ordernum, $extContentMap))) {
                    $saleMoney = $item['totalmoney'];
                }
                //自供自销
                if ($buyerid == $sellerid) {
                    $return[$ordernum]['is_self']    = 1;
                    $return[$ordernum]['buyerid']    = $buyerid;
                    $return[$ordernum]['sellerid']   = $sellerid;
                    $return[$ordernum]['buy_money']  = $buyMoney - $buySettlementDiscount;
                    $return[$ordernum]['sale_money'] = $saleMoney - $saleSettlementDiscount;
                } else {
                    $return[$ordernum]['is_self'] = 0;
                    //$return[$ordernum]['buyerid']    = $buyerid;
                    //$return[$ordernum]['sellerid']  = $sellerid;
                    //$return[$ordernum]['memberid']  = $memberId;

                    if ($buyerid == $memberId) {
                        $return[$ordernum]['buy_money'] = $saleMoney - $saleSettlementDiscount - $buySettlementDiscount;
                        $return[$ordernum]['buy_pmode'] = $item['pmode'];
                        $return[$ordernum]['sellerid']  = $sellerid;
                    }

                    // if ($memberId == $item['member']) {
                    //     $return[$ordernum]['sale_money'] = $retail * 100 * $item['tnum'];
                    //     $return[$ordernum]['buyerid']    = $memberId;
                    // }

                    if ($sellerid == $memberId) {
                        $return[$ordernum]['sale_money'] = $saleMoney - $saleSettlementDiscount;
                        $return[$ordernum]['sale_pmode'] = $item['pmode'];
                        $return[$ordernum]['buyerid']    = $buyerid;
                        $return[$ordernum]['delivery']   = true;

                        //最顶级供应商
                        // if ($item['level'] == 0) {
                        //     $return[$ordernum]['buy_money'] = $buyMoney;
                        //     $return[$ordernum]['sellerid']  = $sellerid;
                        // }
                    }

                    // if ($sellerid != $memberId && $buyerid != $item['member'] && $item['level'] < 2) {
                    //     $return[$ordernum]['sale_pmode'] = $item['pmode'];
                    // }

                }

                //资源账号买入价格特殊处理一下
                if (in_array($memberInfo['dtype'], [2,3])) {
                    $return[$ordernum]['sellerid']  = $sellerid;
                    //景区ID卖出金额取成本价，modify by zhangyangzhen 2019-03-28
                    $return[$ordernum]['buy_money'] = 0;
                    $return[$ordernum]['sale_money'] = $applyRes[$ordernum]['aprice'] * $item['tnum'];
                    $return[$ordernum]['sale_pmode'] = $item['pmode'];
                    $return[$ordernum]['ticket_list'][0]['price'] = $applyRes[$ordernum]['aprice'] ?? 0;
                }

                $pidArr[] = $item['pid'];
                $lidArr[] = $item['lid'];

                //更多游客信息 -证件
                $moreCredentials = [];
                if ($item['product_ext']) {
                    $productExt      = json_decode($item['product_ext'], true);
                    $moreCredentials = isset($productExt['moreInfo']) ? $productExt['moreInfo'] : [];
                }
                $return[$ordernum]['more_credentials'] = $moreCredentials;

                //显示折扣、积分、优惠券使用明细
                $return[$ordernum]['discounts_info'] = $discountDetail;
            }
            $return = $this->_linkMerge($return, $orderArr);

            //获取供应商或者分销商的名称映射
            $sellerArr = array_column($return, 'sellerid');
            $buyerArr  = array_column($return, 'buyerid');
            $memArr    = array_unique(array_merge($sellerArr, $buyerArr));
            $memMap    = $this->_memModel->getMemberInfoByMulti($memArr, 'id', 'id,dname', true);
            //获取景区名称映射
            $pidArr    = array_unique($pidArr);

            $ticketArr = $commodityTicketBiz->queryTicketInfoByProductIds($pidArr, 'title,id,pid,apply_did', 'id',
                'title,imgpath,id');
            $proMap    = [];
            foreach ($ticketArr as $ticketInfo) {
                $proMap[$ticketInfo['product']['id']] = [
                    'id'        => $ticketInfo['product']['id'],
                    'ttitle'    => $ticketInfo['ticket']['title'],
                    'tid'       => $ticketInfo['ticket']['id'],
                    'pid'       => $ticketInfo['ticket']['pid'],
                    'title'     => $ticketInfo['land']['title'],
                    'imgpath'   => $ticketInfo['land']['imgpath'],
                    'landid'    => $ticketInfo['land']['id'],
                    'apply_did' => $ticketInfo['ticket']['apply_did'],
                ];
            }

            //获取景区图片
            $lidArr     = array_unique($lidArr);

            $landRes    = $landBiz->queryLandMultiQueryById($lidArr);
            $lidMap     = array_column($landRes, null, 'id');

            //获取已验证数量
            //$verifiedMap = $this->_toolModel->getVerifiedNumList($orderArr);
            $queryParams = [$orderArr, true];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('applyInfo',
                'getVerifiedNumList', $queryParams);
            if ($queryRes['code'] != 200) {
                $log = [
                    'parame'   => $queryParams,
                    'apiData'  => $queryRes,
                    'function' => 'getVerifiedNumList',
                ];
                pft_log('getVerifiedNumList/Api/error', json_encode($log));
            }
            $verifiedMap  = $queryRes['data'];
            $queryParams  = [$orderArr];
            $queryRes     = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
                'queryOrderTouristInfoByOrderId', $queryParams);
            $visitor_info = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $visitor_info = $queryRes['data'];
            }
            $teamDiscountOrderNumArray = [];
            foreach ($return as &$item) {
                //是否显示支付按钮
                $item['pay'] = $this->_isShowPay($item, $memberId);
                //是否显示重发按钮
                $item['resend'] = $this->_isShowResend($item['ordernum'], $memberId);
                //是否显示取消按钮
                $item['cancel'] = $this->_isShowCancel($item['ordernum'], $memberId);
                //是否显示预约按钮
                $item['appointmentButton'] = $this->_isShowAppointment($item);
                //是否显示改签按钮
                $item['changeButton'] = $this->_isShowChange($item);
                //是否可以预约票 除了下单者跟顶级供应商其他人都无权操作
                $item['can_appointment'] = false;
                //景区图片
                $item['imgpath'] = $lidMap[$item['lid']]['imgpath'];
                $item['p_type']  = $lidMap[$item['lid']]['p_type'];
                $item['seller']  = $memMap[$item['sellerid']]['dname'];
                 //微平台资源账号分销商显示散客
                if (isset($memberInfo['dtype']) && in_array($memberInfo['dtype'], [2,3])) {
                    $item['buyer']   = '散客';
                }else{
                    $item['buyer']   = $memMap[$item['buyerid']]['dname'];
                }
                $landApplyDid    = $lidMap[$item['lid']]['apply_did'];
                //酒店确认按钮处理
                $item['confirmButton'] = false;
                if ($item['p_type'] == 'C') {
                    if ($item['status'] == 4 && $memberId == $landApplyDid && $item['pay_status'] == 1) {
                        $item['confirmButton'] = true;
                    }
                }

                //判断酒店类型的 如果是联票子票的 不能有确认按钮
                if ($item['confirmButton'] && $item['p_type'] == 'C' && isset($item['link_main_ordernum']) && $item['link_main_ordernum'] != $item['ordernum']) {
                    $item['confirmButton'] = false;
                }

                foreach ($item['ticket_list'] as &$ticket) {
                    $ticket['ltitle']    = $proMap[$ticket['pid']]['title'];
                    $ticket['ttitle']    = $proMap[$ticket['pid']]['ttitle'];
                    $ticket['landid']    = $proMap[$ticket['pid']]['landid'];
                    $ticket['apply_did'] = $proMap[$ticket['pid']]['apply_did'];  //票顶级供应商
                    //分批验证获取已验证数量
                    if ($ticket['status'] == 7) {
                        if (isset($verifiedMap[$item['ordernum']])) {
                            $ticket['veri_num'] = $verifiedMap[$item['ordernum']];
                        } else {
                            $ticket['veri_num'] = 0;
                        }
                    }
                    if (in_array($memberId, [$proMap[$ticket['pid']]['apply_did'], $item['member']])) {
                        $item['can_appointment'] = true;
                    }
                }

                if ($visitor_info && $showMoreCredential) {
                    //游客取票人更多信息处理
                    $visitor_info = $this->_handleMoreCredential($visitor_info, $item['ordernum']);
                }

                $item['visitor_info'] = $visitor_info;
                //中间商隐藏客户关键信息
                if ($this->_sid && $this->_memberid && !is_array($this->_memberid) && !$ifHide && $this->_orderInfoHiding) {
                    $item = $orderInfoHidingBuz->batchHideCustomerInformation($item,
                        $currentOrderSplit[$item['ordernum']],
                        $item['ticket_list'][0]['tid'], $this->_sid, $this->_memberid);
                }
                //获取团单订单且未支付 的订单信息
                if(in_array($item['ordermode'],self::TEAM_ORDER_MODE) && $item['pay_status'] ==2){
                    $teamDiscountOrderNumArray[] = $item['ordernum'];
                }
            }
            //团单未支付优惠处理
            if(!empty($teamDiscountOrderNumArray)){
                $teamOrderCmbIds = [];
                try {
                    $combineOrderInfoRes = (new BizProductOrder())->batchDetailInfo($memberId, $teamDiscountOrderNumArray);
                } catch (\Exception $e) {
                    return $return;
                }
                $baseCheck  = new BaseCheck();
                foreach ($teamDiscountOrderNumArray as $orderNum){
                    $res = $baseCheck->checkTeamOrderDiscount($orderNum, 24);
                    if($res['code']==200 &&  $res['data']['isUseCoupon']){
                        $teamOrderCmbIds[] = $orderNum;
                    }
                }
                if(empty($teamOrderCmbIds)){
                    return $return;
                }
                $combineOrderInfoList = array_column($combineOrderInfoRes,null,'productOrderId');
                foreach ($return as &$item){
                    if (in_array($item['ordernum'], $teamOrderCmbIds)) {
                        $item['cmb_id'] = $combineOrderInfoList[$item['ordernum']]['tradeOrderId'];
                        $item['pay'] = false;
                    }
                }
            }
        }
        return $return;
    }

    /**
     * 是否展示凭证码跟游玩日期
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return array|bool
     * <AUTHOR>
     * @date 2020/7/6
     *
     *
     */
    private function _showPlayTimeAndCode($orderInfo)
    {
        $productExt = json_decode($orderInfo['product_ext'], true);
        if ($productExt['reservationOrder'] == 1 && isset($productExt['reservationOrder']) && empty($productExt['reservationOperateTime'])
            && $orderInfo['status'] == 3) {
            return false;
        }

        return true;
    }

    /**
     * 是否显示预约按钮
     *
     * <AUTHOR>
     * @date   2020/6/20
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return int
     */
    private function _isShowAppointment($orderInfo)
    {
        $productExt = json_decode($orderInfo['product_ext'], true);
        if ($productExt['reservationOrder'] != 1 || !isset($productExt['reservationOrder'])) {
            return false;
        }

        return true;
    }

    /**
     * 是否展示外部码信息
     * <AUTHOR>  Li
     * @date  2020-07-24
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return array|bool
     */
    private function _showExternalCode($orderInfo)
    {
        $productExt = json_decode($orderInfo['product_ext'], true);
        if (isset($productExt['externalSendCode']) && $productExt['externalSendCode'] == 1) {
            $externalBiz = new \Business\ExternalCode\CodeManage();
            $externalRes = $externalBiz->getExternalCodeInfoByOrdernum($orderInfo['ticket_list'][0]['apply_did'],
                $orderInfo['ordernum']);
            $tidArr      = array_column($orderInfo['ticket_list'], 'tid');
            if (isset($externalRes['code']) && $externalRes['code'] == 200) {
                $orderInfo['externalCode']['code'] = $externalRes['data']['list'];

                //查询是否设置游玩日期
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $ticketArr = $javaApi->batchQueryTicketByTicketIds($tidArr, 'pre_sale');
                $preSale   = $ticketArr[0]['pre_sale']; //是否选择游玩日期 1是2否

                //有效期开始和结束时间的处理
                $expireType = $externalRes['data']['expire_type']; //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
                $orderTime  = $orderInfo['ordertime']; //下单时间
                $playTime   = $orderInfo['playtime']; //游玩时间
                $expireTime = $externalRes['data']['expire_time'];  //外部码过期时间， 0为长期有效
                $startDt    = $externalRes['data']['start_dt']; //固定日期的开始时间
                $endDt      = $externalRes['data']['end_dt']; //固定日期的结束时间
                //有效期处理
                $handleRes = $externalBiz->handleExternalCodeOrderTime($expireType, $preSale, $orderTime, $playTime,
                    $expireTime, $startDt, $endDt);
                if (isset($handleRes['code']) && $handleRes['code'] == 200) {
                    $orderInfo['externalCode']['begin_time'] = $handleRes['data']['begin_time'];
                    $orderInfo['externalCode']['end_time']   = $handleRes['data']['end_time'];
                }
            }
        }

        return $orderInfo;
    }

    /**
     * 是否显示改签按钮
     *
     * @param  array  $orderInfo  订单信息
     *
     * @return bool
     * @throws \Exception
     * <AUTHOR>
     * @date   2020/6/20
     *
     */
    private function _isShowChange($orderInfo)
    {
        //套票子票不允许改签，直接false（不知道为什么，就是这么定）
        if ($orderInfo['ordermode'] == 23) {
            return false;
        }
        //查询终端订单变动表是否存在改签记录
        $auditModel  = new RefundAuditModel();
        $changeInfos = $auditModel->getTicketChangingOrders([$orderInfo['ordernum']], 'id');
        //获取票类快照属性
        $versionInfo = json_decode($orderInfo['ext_content'], true);
        $versionInfo = $versionInfo['ticketVersion'] ?? 0;
        $ticketApi   = new TicketSnapshot();
        $ticketInfo  = $ticketApi->queryTicketSnapshot($orderInfo['ticket_list'][0]['tid'], $versionInfo);
        if ($ticketInfo['data']['uuJqTicketDTO']['ticketChangingRange'] != 0 && empty($changeInfos) && $orderInfo['status'] == 0) {
            return true;
        }

        return false;

    }

    /**
     * 是否显示支付按钮
     *
     * <AUTHOR>
     * @date   2017-04-26
     *
     * @param  array  $order  订单信息
     * @param  int  $memberId  当前用户id
     *
     * @return int
     */
    private function _isShowPay($order, $memberId)
    {

        //$status 补充一个酒店待确认的 4
        if (in_array($order['status'], [0, 4, 11]) && $order['pay_status'] == 2 && $order['member'] == $memberId) {
            return 1;
        } else {
            return 0;
        }

    }

    /**
     * 是否显示退票按钮
     * <AUTHOR>
     * @date   2017-05-04
     *
     * @param  string  $ordernum  订单号
     * @param  int  $memberId  会员id
     *
     * @return int
     */
    private function _isShowCancel($ordernum, $memberId)
    {

        $result = $this->cancelCheck($ordernum, $memberId);

        return $result['status'];

    }

    /**
     * 是否显示重发按钮
     *
     * <AUTHOR>
     * @date   2017-05-20
     *
     * @param  string  $ordernum  订单号
     * @param  int  $memberId  会员id
     *
     * @return int
     */
    private function _isShowResend($ordernum, $memberId)
    {

        $result = $this->_toolModel->hasPermissionToResend($ordernum, $memberId);

        return $result['status'];

    }

    /**
     * 合并联票订单信息
     *
     * <AUTHOR>
     * @date   2017-04-17
     *
     * @param  array  $list  订单列表
     *
     * @return array
     */
    private function _linkMerge($list, $orderArr)
    {

        //联票
        //$linkOrder = $this->_toolModel->getOrderConcatId($orderArr);
        $queryParams = [$orderArr, 'orderid', 'concat_id'];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderDetail', 'queryOrderDetailsByOrdernums',
            $queryParams);
        $linkOrder   = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $linkOrder = $queryRes['data'];
        }
        $linkOrder = array_filter($linkOrder);

        $mainTicketArr = array_keys($linkOrder);
        foreach ($list as $key => $item) {

            if (!isset($linkOrder[$item['ordernum']])) {
                //非联票订单
                continue;
            }
            //联票关联订单号补充
            $list[$key]['link_main_ordernum'] = $linkOrder[$item['ordernum']];

            if (in_array($item['ordernum'], $mainTicketArr)) {
                //订单主票
                continue;
            }
            $mainOrdernum = $item['ordernum'];

            if (isset($list[$mainOrdernum]['sale_money'])) {
                $list[$mainOrdernum]['sale_money'] += $item['sale_money'];
            }

            if (isset($list[$mainOrdernum]['buy_money'])) {
                $list[$mainOrdernum]['buy_money'] += $item['buy_money'];
            }

            $list[$mainOrdernum]['ticket_list'][] = $item['ticket_list'][0];
            unset($list[$key]);
        }

        return $list;
    }

    /**
     * 根据产品类型获取一些额外的信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  array  $info  主要信息
     * @param  string  $type  产品类型
     *
     * @return array
     */
    private function _getExtraInfoForType($info, $type)
    {

        switch ($type) {

            case 'B':
                //线路产品
                $return = $this->_fillOrderInfoForRoute($info);
                break;

            case 'C':
                //酒店产品
                $return = $this->_fillOrderInfoForHotel($info);
                break;

            case 'H':
                //演出产品
                $return = $this->_fillOrderInfoForShow($info);
                break;

            case 'F':
                //套票产品
                $return = $this->_fillOrderInfoForPack($info);
                break;

            case 'I':
                //年卡产品
                $return = $this->_fillOrderInfoForAnnual($info);
                break;

            case 'J':
                //特产产品
                $return = $this->_fillOrderInfoForSpecialV2($info);
                break;

            default:
                //分时预约数据处理
                $extContent               = json_decode($info['ext_content'], true);
                $info['time_share_order'] = $extContent['sectionTimeStr'] ? $extContent['sectionTimeStr'] .= (new \Business\Product\TimeShare())->getTimeShareDelayTime($extContent['sectionTimeStr'], $extContent['sectionDelayCheckInTime'] ?? 0, $extContent['sectionAheadCheckInTime'] ?? 0) : "";
                $return                   = $info;
                break;
        }

        return $return;

    }

    /**
     * 填充线路产品信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  array  $info  订单信息
     *
     * @return array
     */
    private function _fillOrderInfoForRoute($info)
    {

        $tid = $info['ticket_list'][0]['tid'];
        //集合地点
        $ext     = $this->_ticModel->getTicketExtInfoByTid($tid, 'ass_station');
        $station = json_decode($ext['ass_station'], true);

        //下单时选择的集合地点
        $detail = $this->_toolModel->getOrderDetailInfo($info['ordernum'], 'assembly');

        if (isset($station[$detail['assembly']])) {
            $info['station'] = [$station[$detail['assembly']]];
        } else {
            $info['station'] = [];
        }

        return $info;
    }

    /**
     * 填充酒店信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  array  $info  订单信息
     *
     * @return array
     */
    private function _fillOrderInfoForHotel($info)
    {

        //酒店订单视作联票
        $link = $this->_toolModel->getLinkOrdersInfo($info['ordernum'], 'playtime');

        $last = array_pop($link);

        if (!$last) {
            $last['playtime'] = $info['begintime'];
        }

        $begintime = strtotime($info['begintime']);
        $endtime   = strtotime($last['playtime']) + 3600 * 24;

        //住店时间
        $date = $info['begintime'] . '~' . date('Y-m-d', $endtime);

        //住店天数
        $days = ($endtime - $begintime) / 3600 / 24;

        $info['days']    = $days;
        $info['endtime'] = date('Y-m-d', $endtime);

        return $info;
    }

    /**
     * 填充演出产品信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  array  $info  订单信息
     *
     * @return array
     */
    private function _fillOrderInfoForShow($info)
    {

        $detail = $this->_toolModel->getOrderDetailInfo($info['ordernum'], 'series');

        //分区名
        $tid     = $info['ticket_list'][0]['tid'];
        $zoneMap = $this->_ticModel->getZoneName([$tid]);

        $ticketExtInfo = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($tid);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                if ($attr['key'] == 'is_show_seat') {
                    $ticketExtInfo[$attr['ticket_id']] = $attr;
                }
            }
        }
        //$ticketExtInfo = $this->_ticModel->getTicketExtConfig([$tid], 'ticket_id,key,val', ['is_show_seat']);
        $isShowSeat = 1;
        if (!empty($ticketExtInfo[$tid]['val']) && $ticketExtInfo[$tid]['val'] == 2) {
            $isShowSeat = 2;
        }

        if ($zoneMap[$tid]) {
            $zoneName = '(' . $zoneMap[$tid]['zone_name'] . ')';
        } else {
            $zoneName = '';
        }

        $landModel = new \Model\Product\Land();
        $landInfo  = $landModel->getLandInfo($info['lid'], false, 'venus_id');

        if (!$landInfo['venus_id']) {
            $venueName = '';
        } else {
            //场次名称
            $showModel = new \Model\Product\Show();
            $venueInfo = $showModel->getVenuesInfo($landInfo['venus_id']);
            $venueName = $venueInfo ? $venueInfo['venue_name'] : '';
        }

        $seat = unserialize($detail['series']);

        //演出座位修改
        $showBiz = new Show();
        $seatFive = $showBiz->handleSeatParamsFenQu($seat[5], $zoneName);

        $info['seat'] = [
            ($seat[11] ?? $seat[4]) . "($venueName)",
            $seatFive,
            $seat[6],
        ];

        if ($isShowSeat == 2) {
            $info['seat'][1] = '';
        }

        return $info;
    }

    /**
     * 填充套票信息
     *
     * <AUTHOR>
     * @date   2017-04-18
     *
     * @param  array  $info  订单信息
     *
     * @return array
     */
    private function _fillOrderInfoForPack($info)
    {
        $tid = $info['ticket_list'][0]['tid'];
        //分时预约数据 + 演出子票处理
        $packBiz      = new \Business\Product\PackTicket();
        $sons         = $packBiz->getSonTickets($tid);
        $timeShareBiz = new \Business\Product\TimeShare();
        $sons    = array_key($sons, 'tid');
        if ($sons) {
            //套票子票演出信息
            $packageSeriesInfo = [];

            //根据套票主票获取所有子票订单
            $orderToolsMode = new OrderTools();
            $sonOrderInfo   = $orderToolsMode->getPackSubOrder($info['ordernum']);
            $orderIdArr     = array_column($sonOrderInfo, 'orderid');
            $orderInfo      = $orderToolsMode->getOrderInfo($orderIdArr, 'ss.tid,ss.playtime',
                'de.ext_content,de.product_ext,series');
            $orderInfo      = array_key($orderInfo, 'tid');

            foreach ($sons as $item) {
                $extContent = json_decode($orderInfo[$item['tid']]['ext_content'], true);
                $productExt = json_decode($orderInfo[$item['tid']]['product_ext'], true);

                //演出的单独处理下
                if ($item['p_type'] == 'H') {
                    $packageSeriesInfo[$item['tid']] = $orderInfo[$item['tid']]['series'];
                }

                $tmp = [
                    'title'    => $item['title'],
                    'num'      => $item['num'],
                    'time_str' => $extContent['sectionTimeStr'] ? $orderInfo[$item['tid']]['playtime'] . "  " . $extContent['sectionTimeStr'] .= $timeShareBiz->getTimeShareDelayTime($extContent['sectionTimeStr'], $extContent['sectionDelayCheckInTime'] ?? 0, $extContent['sectionAheadCheckInTime'] ?? 0) :
                        $orderInfo[$item['tid']]['playtime'],
                ];
                if ($productExt['reservationOrder'] == 1 && empty($productExt['reservationOperateTime'])) {
                    $tmp['time_str'] = "";
                }
                $info['sub_list'][] = $tmp;

            }

            $orderListBiz                  = new \Business\Order\OrderList();
            $info['package_show_info_str'] = $orderListBiz->getPackageShowInfo($packageSeriesInfo, $sons);
        }

        return $info;
    }

    /**
     * 填充年卡信息
     *
     * <AUTHOR>
     * @date   2017-05-16
     *
     * @return array
     */
    private function _fillOrderInfoForAnnual($info)
    {

        $annualModel = new AnnualCard('slave');

        $virtul = $annualModel->getVirtualNoByOrdernum($info['ordernum']);

        if ($virtul) {
            $card = $annualModel->getAnnualCard($virtul, 'virtual_no');

            if ($card) {
                //实体卡号
                $info['virtual_no'] = $virtul;
                //虚拟卡号
                $info['card_no'] = $card['card_no'];
            }

        }

        return $info;

    }

    /**
     * 填充特产配送信息
     *
     * <AUTHOR>
     * @date   2017-05-16
     *
     * @return array
     */
    private function _fillOrderInfoForSpecial($info)
    {

        //特产类型，需要展示收货/取货信息
        $specialBiz = new \Business\Product\Specialty();
        $series     = $info['ticket_list'][0]['series'];
        $shipId     = $tempShipId = 0;
        if ($series) {
            [$shipId, $expressWay] = explode('|', $series);
            if ($expressWay == 0) {
                $tempShipId = 0;
            } else {
                $tempShipId = $shipId;
            }
        }
        $info['shippingId'] = $shipId;
        $specialRes         = $specialBiz->parsePackInfo($info['ticket_list'][0]['tid'], $tempShipId,
            $info['ordernum']);
        $info['special']    = [];
        if ($specialRes['code'] == 200) {
            $info['special'] = $specialRes['data'];
        }
        $specRes = $specialBiz->getOneSpecification($info['ticket_list'][0]['tid'], 'id,ticket_id,express_pay_way');
        if ($specRes['code'] == 200) {
            $info['express_pay_way'] = $specRes['data']['express_pay_way'];
        }

        return $info;

    }

    /**
     * 新版特产信息获取
     * @author: zhangyz
     * @date: 2020/5/27
     *
     * @param $info
     *
     * @return mixed
     */
    private function _fillOrderInfoForSpecialV2($info)
    {
        $ordernum = $info['ordernum'];
        $ptype    = $info['ptype'];
        $mainTid  = $info['ticket_list'][0]['tid'];

        if ($ptype == 'J') {
            //演出类型需要获取场次信息
            $Order      = new \Model\Order\OrderTools();
            $orderExtra = $Order->getOrderDetailInfo($ordernum);

            //特产类型，需要展示收货/取货信息
            $productExt = json_decode($orderExtra['product_ext'], true);

            //快递还是自提,0快递自提
            $deliveryType = $productExt['deliveryType'] ?? 1;
            $expInfo      = $selfPickup = [];
            if ($deliveryType == 0) {
                //获取收货人信息
//                $orderUserModel = new \Model\Order\OrderUser();
//                $orderUserInfo  = $orderUserModel->getOneOrderUserByOrdernum($ordernum,
//                    'province_code,city_code,town_code,address');
                $queryParams   = [[$ordernum]];
                $queryRes      = \Business\JavaApi\Order\Query\Container::query('OrderUserInfo',
                    'getOrderUserInfoByOrderNum', $queryParams);
                $orderUserInfo = [];
                if ($queryRes['code'] == 200) {
                    $orderUserInfo = $queryRes['data'][0];
                }
                $areaCodeArr = [
                    $orderUserInfo['province_code'],
                    $orderUserInfo['city_code'],
                    $orderUserInfo['town_code'],
                ];
                $areaCodeArr = array_filter($areaCodeArr);
                if ($areaCodeArr) {
                    $areaModel = new \Model\Product\Area();
                    $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
                } else {
                    $codeMap = [];
                }

                //快递物流信息
                $expInfo = [
                    'carriage'       => $productExt['carriage'] ?? 0,
                    'exp_company'    => $productExt['expCompany'] ?? '',
                    'exp_no'         => $productExt['expNo'] ?? '',
                    'exp_company_no' => $productExt['expCompanyNo'] ?? '',
                    'province_code'  => $codeMap[$orderUserInfo['province_code']] ?? '',
                    'city_code'      => $codeMap[$orderUserInfo['city_code']] ?? '',
                    'town_code'      => $codeMap[$orderUserInfo['town_code']] ?? '',
                    'address'        => $orderUserInfo['address'] ?? '',
                ];
            } else {
                $specialApi = new \Business\JavaApi\Ticket\SpecialtyTicket();
                $res        = $specialApi->querySpecialGoodsDeliveryInfo($mainTid);
                if ($res['code'] == 200) {
                    $pickPointData = $res['data']['pickPoint'];
                    $areaCodeArr   = explode('|', $pickPointData['areaCode']);
                    $areaCodeArr   = array_filter($areaCodeArr);
                    if ($areaCodeArr) {
                        $areaModel = new \Model\Product\Area();
                        $codeMap   = $areaModel->getAreaNameByCodeArr($areaCodeArr);
                    } else {
                        $codeMap = [];
                    }
                    //自提点信息
                    $selfPickup = [
                        'area'           => implode('', array_values($codeMap)),
                        'address'        => $pickPointData['address'],
                        'linkman'        => $pickPointData['linkman'] . '/' . $pickPointData['phone'],
                        'reception_time' => $pickPointData['acceptTime'],
                        'no_pick_cancel' => $res['data']['noPickAutoCancel'],
                    ];
                } else {
                    $selfPickup = [];
                }
            }

            $specialInfo = ['delivery_way' => $deliveryType, 'exp_info' => $expInfo, 'pick_info' => $selfPickup];
        }

        $info['special'] = $specialInfo;

        return $info;
    }

    /**
     * 读取客源地的人类可读的字符串
     * <AUTHOR>
     * @DateTime 2017-09-24T15:35:28+0800
     *
     * @param  int|array  $origin  客源地编码
     *
     * @return   返回可读的客源地信息
     */
    public function getFountainReadable($origin)
    {
        $area = new Area();
        $area->getAreaList();
        $origin         = explode("|", $origin);
        $readAbleString = "";
        foreach ($origin as $key => $value) {
            if (isset($area->areas[$value])) {
                $readAbleString .= $area->areas[$value];
            }

        }

        return $readAbleString;
    }

    /***
     * @param $ordernum
     *
     * @return array
     */
    public function getCancelInfoForSms($ordernum, $main)
    {
        //获取主票信息
        $landInfo     = $this->_landModel->getLandInfo($main['lid'], false, 'title,tel');
        $javaApi      = new \Business\CommodityCenter\LandF();
        $arrLandArr   = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([$main['tid']], [], [],
            'cancel_sms_buyer,cancel_sms_supplier');
        $cancelNotify = $arrLandArr[0];

        $ticketTitle = $this->_ticModel->getTicketInfoById($main['tid'], 'title');
        $opName      = $this->_memModel->getMemberInfo($main['member'], 'id', 'dname');
        $opname      = implode(',', $opName);
        $data        = [
            'ordernum'     => $ordernum,
            'memdname'     => $opname,
            'aid'          => $main['aid'],
            'ordertel'     => $main['ordertel'],
            'tnum'         => $main['tnum'],
            'ordername'    => $main['ordername'],
            'sms_buyer'    => $cancelNotify['cancel_sms_buyer'],
            'sms_supplier' => $cancelNotify['cancel_sms_supplier'],
            'ltel'         => $landInfo['tel'],
            'ltitle'       => $landInfo['title'],
            'ttitle'       => $ticketTitle['title'],
            'infos'        => implode(',', $main),
            'pay_status'   => $main['pay_status'],
        ];

        return $data ?: [];
    }

    /***
     * 根据订单号获取产品类型
     * <AUTHOR>
     * @date   2018-02-05
     *
     * @param $ordernum
     *
     * @return string
     */
    public function getOrderPtype($ordernum, $lid)
    {
        //获取主票信息
        $landInfo = $this->_landModel->getLandInfo($lid, false, 'p_type');

        return $landInfo['p_type'];
    }

    /**
     * 微平台订单查询的一些配置项
     * <AUTHOR>
     * @date   2019-08-12
     * @return array
     */
    public function getQueryConfig()
    {
        //支付方式的映射
        $paymodeMap = load_config('order_pay_mode');
        //订单状态映射
        $statusMap      = load_config('order_status', 'trade_record');
        $statusMap[101] = '退票中';

        $data = [
            'paymode_map' => $paymodeMap,
            'status_map'  => $statusMap,
        ];

        return $this->returnData(200, '', $data);
    }

    /**
     * 获取订单数据(包含子票信息及相关产品信息)
     *
     * <AUTHOR> Li
     * @date 2020-05-11
     *
     * @param $orderNum
     *
     * @return array
     */
    public function getSimpleOrderInfo($orderNum)
    {
        //获取订单信息
        $orderModel = new OrderQuery();
        $orderInfo  = $orderModel->getSimpleOrderInfo($orderNum);

        //初始化
        $memberIdArr = [];
        $ticketIdArr = [];
        $memberMap   = [];
        $ticketMap   = [];

        if (!empty($orderInfo['mainOrder'])) {
            $memberIdArr[] = $orderInfo['mainOrder']['member'];
            $ticketIdArr[] = $orderInfo['mainOrder']['tid'];
        }

        if (!empty($orderInfo['childOrder'])) {
            $memberIdArr = array_unique(array_merge($memberIdArr, array_column($orderInfo['childOrder'], 'member')));
            $ticketIdArr = array_unique(array_merge($ticketIdArr, array_column($orderInfo['childOrder'], 'tid')));
        }

        //批量获取用户信息
        if ($memberIdArr) {
            $memberBiz  = new \Business\Member\Member();
            $memberInfo = $memberBiz->getMemberInfoByMulti($memberIdArr);
            foreach ($memberInfo as $member) {
                $memberMap[$member['id']] = $member['account'];
            }
        }

        //批量获取门票信息
        if ($ticketIdArr) {
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketInfo = $javaApi->queryTicketInfoByIds($ticketIdArr, 'id,pid,title', '', 'title,p_type');
            if ($ticketInfo) {
                foreach ($ticketInfo as $ticket) {
                    $ticketMap[$ticket['ticket']['id']] = [
                        'pid'    => $ticket['ticket']['pid'],
                        'p_type' => $ticket['land']['p_type'],
                        'ttitle' => $ticket['ticket']['title'],
                        'ltitle' => $ticket['land']['title'],
                    ];
                }
            }
        }

        //数据组装
        if (!empty($orderInfo['mainOrder'])) {
            $orderInfo['mainOrder']['fromt']  = $memberMap[$orderInfo['mainOrder']['member']];
            $orderInfo['mainOrder']['p_type'] = $ticketMap[$orderInfo['mainOrder']['tid']]['pid'];
            $orderInfo['mainOrder']['p_type'] = $ticketMap[$orderInfo['mainOrder']['tid']]['p_type'];
            $orderInfo['mainOrder']['ttitle'] = $ticketMap[$orderInfo['mainOrder']['tid']]['ttitle'];
            $orderInfo['mainOrder']['ltitle'] = $ticketMap[$orderInfo['mainOrder']['tid']]['ltitle'];
            $mainOrder                        = $orderInfo['mainOrder'];
        }

        if (!empty($orderInfo['childOrder'])) {
            foreach ($orderInfo['childOrder'] as &$child) {
                $child['fromt']  = $memberMap[$child['member']];
                $child['pid']    = $ticketMap[$child['tid']]['pid'];
                $child['p_type'] = $ticketMap[$child['tid']]['p_type'];
                $child['ttitle'] = $ticketMap[$child['tid']]['ttitle'];
                $child['ltitle'] = $ticketMap[$child['tid']]['ltitle'];
            }
            $childOrder = $orderInfo['childOrder'];
        }

        return ['mainOrder' => $mainOrder, 'childOrder' => $childOrder];
    }

    /**
     * 景区账号的订单查询参数
     */
    private function _makeScenicParam(
        $salerId, $dbStart = false, $dbEnd = false, $orderTimeStart = false, $orderTimeEnd = false,
        $dtimeStart = false, $dtimeEnd = false, $page = false, $size = false, $ordernumArr = false, $status = false,
        $mobile = false, $ordername = false, $lid = false, $memberRelationship = false, $remoteOrder = '', $thirdOrder = '', $idcard = '')
    {
        $queryParam            = [];
        $queryParam['salerid'] = $salerId;
        $queryParam['remotenum'] = $remoteOrder;
        $queryParam['thirdOrder'] = $thirdOrder;
        $queryParam['personid'] = $idcard;

        if ($dbEnd) {
            $queryParam['dbEnd'] = $dbEnd;
        }
        if ($dbStart) {
            $queryParam['dbStart'] = $dbStart;
        }
        if ($orderTimeStart) {
            $queryParam['minOrdertime'] = $orderTimeStart;
        }
        if ($orderTimeEnd) {
            $queryParam['maxOrdertime'] = $orderTimeEnd;
        }
        if ($dtimeStart) {
            $queryParam['minDtime'] = $dtimeStart;
        }
        if ($dtimeEnd) {
            $queryParam['maxDtime'] = $dtimeEnd;
        }
        if ($ordernumArr) {
            $queryParam['ordernumIn'] = $ordernumArr;
        }
        if ($status !== false && $status != -1) {
            $queryParam['statusIn'] = [$status];
        }
        if ($mobile) {
            $queryParam['ordertel'] = $mobile;
        }
        if ($lid) {
            $queryParam['lidIn'] = [$lid];
        }
        if ($ordername) {
            $queryParam['ordername'] = $ordername;
        }
        if ($page) {
            $queryParam['page'] = $page;
        }
        if ($size) {
            $queryParam['size'] = $size;
        }
        if ($memberRelationship) {
            $queryParam['memberRelationship'] = $memberRelationship;
        }

        $queryParam['pTypeIn'] = ['A', 'B', 'C', 'F', 'G', 'H', 'I', 'J', 'K'];

        return $queryParam;
    }

    private function _makeBusinessParam(
        $sellerId, $buyerId, $orderTimeStart, $orderTimeEnd, $dtimeStart, $dtimeEnd, $page, $size, $ordernum, $status,
        $mobile, $ordername, $lid, $memberRelationship, $remoteOrder, $thirdOrder, $idcard, $extCondition = []
    )
    {
        $queryParam = [];

        if ($sellerId) {
            $queryParam['sellerId'] = $sellerId;
        }
        if ($buyerId) {
            $queryParam['buyerId'] = $buyerId;
        }
        if ($remoteOrder) {
            $queryParam['remotenum'] = $remoteOrder;
        }
        if ($thirdOrder) {
            $queryParam['thirdOrder'] = $thirdOrder;
        }
        if ($idcard) {
            $queryParam['personid'] = $idcard;
        }
        if ($orderTimeStart) {
            $queryParam['minOrdertime'] = $orderTimeStart;
        }
        if ($orderTimeEnd) {
            $queryParam['maxOrdertime'] = $orderTimeEnd;
        }
        if ($dtimeStart) {
            $queryParam['minDtime'] = $dtimeStart;
        }
        if ($dtimeEnd) {
            $queryParam['maxDtime'] = $dtimeEnd;
        }
        if ($ordernum) {
            $queryParam['ordernums'] = [$ordernum];
        }
        if ($status !== false && $status != -1) {
            $queryParam['status'] = $status;
        }
        if ($mobile) {
            $queryParam['ordertel'] = $mobile;
        }
        if ($lid) {
            $queryParam['lid'] = $lid;
        }
        if ($ordername) {
            $queryParam['orderName'] = $ordername;
        }
        if ($page) {
            $queryParam['page'] = $page;
        }
        if ($size) {
            $queryParam['size'] = $size;
        }
        if ($memberRelationship) {
            $queryParam['memberRelationship'] = $memberRelationship;
        }
        if (!empty($extCondition['lidList'])) {
            $queryParam['lidIn'] = $extCondition['lidList'];
        }
        if (!empty($extCondition['notLidList'])) {
            $queryParam['notLidList'] = $extCondition['notLidList'];
        }
        $queryParam['pTypeIn'] = ['A', 'B', 'C', 'F', 'G', 'H', 'I', 'J', 'K'];

        return $queryParam;
    }

    /**
     * 处理取票人游客更多信息 参数转换
     * <AUTHOR>
     * @time   2020-7-10
     *
     * @param  array  $moreCredentials
     * @param  array  $visitorInfo
     * @param  string  $ordernum
     *
     */
    private function _handleMoreCredential($visitorInfo, $ordernum)
    {
        //游客
        $touristJavaApi = new OrderTouristInfo();
        $touristExInfo  = $touristJavaApi->getOrderTouristInfoExt($ordernum);
        foreach ($visitorInfo as &$touristData) {
            //身份证转化
            $touristData['voucher_type_name'] = $this->conversionIdentityToName($touristData['voucher_type']);
            $touristData['more_credentials'] = [];
            foreach ($touristExInfo['data'] as $key => $moreData) {
                if ($touristData['id'] == $moreData['touristId']) {
                    $name = '';
                    if(isset($moreData['certType'])){
                        $name = $this->conversionIdentityToName($moreData['certType']);
                    }
                    $touristExInfos                    = [
                        'name'          => $name,
                        'text'          => $moreData['remark'],
                        'realName'      => $touristData['tourist'],
                        'documentValue' => $moreData['certInfo'],
                    ];
                    $touristData['more_credentials'][] = $touristExInfos;
                }
            }
        }

        return $visitorInfo;
    }

    /**
     * 获取原始信息
     */
    private function getApplyInfo($orderIdArr)
    {
        $applyRes = [];
        $errPid   = [];
        $errOrder = [];

        $model     = (new OrderTools());
        $applyInfo = $model->getOrderApplyInfo($orderIdArr,
            'orderid, aprice, lprice, pid, refund_num, verified_num, origin_num, can_refund');

        //已验证的票数
        $checkedTicket = [];
        //已退票的票数
        $cancelTicket = [];
        //原始票数
        $originTnum = [];

        foreach ($applyInfo as $item) {
            $applyRes[$item['orderid']] = $item;

            $checkedTicket[$item['orderid']] = $item['verified_num'];
            $cancelTicket[$item['orderid']]  = $item['refund_num'];
            $originTnum[$item['orderid']]    = $item['origin_num'];
        }
        unset($applyInfo);

        return [
            'applyRes'      => $applyRes,
            'checkedTicket' => $checkedTicket,
            'cancelTicket'  => $cancelTicket,
            'originTnum'    => $originTnum,
        ];
    }

    public function handleLvOrderList($ordernum, $pType)
    {
        $temp = [
            'ordernum'          => $ordernum,
            'p_type'        => $pType,

        ];

        return $temp;
    }

    public function getIdentity(){
        $list = $this->getIdentityList();
        $res = [];
        if(!empty($list)){
            foreach ($list as $key =>$value){
                array_push($res,['id'=>$key,'name'=>$value]);
            }
            return $this->returnData(200, '',$res);
        }
        else{
            return $this->returnData(204, '身份字典获取失败');
        }
    }

    /**
     * 通过订单号获取cmb
     * <AUTHOR>
     * @date   2023/10/12
     *
     * @param array $orderArr 订单号集
     *
     * @return array
     */
    public function getCmbMapByOrdeerNumArr($orderArr)
    {
        if (empty($orderArr)) {
            return [];
        }

        try {
            $orderArr = array_values(array_unique($orderArr));
            $pageSize = 200;
            $total    = ceil(count($orderArr) / $pageSize);

            $return = [];
            for ($i = 0; $i < $total; $i++) {
                $queryIds = array_slice($orderArr, $i * $pageSize, $pageSize);
                $combineOrderInfoRes = (new AdminProductOrder())->batchDetailInfo(AdminProductOrder::MEMBER_ID, $queryIds);
                foreach ($combineOrderInfoRes as $item) {
                    $return[$item['productOrderId']] = $item['tradeOrderId'] ?? '';
                }
            }
        }catch (\Exception $e) {
            $return = [];
        }

        return $return;
    }

    /**
     * 批量获取支付中心订单号
     * <AUTHOR>
     * @date   2023/10/12
     *
     * @param array $orderArr
     *
     * @return array
     */
    public function getPayCenterOrderMapByOrdeerNumArr($orderArr)
    {
        if (empty($orderArr)) {
            return [];
        }

        try {
            $orderArr = array_values(array_unique($orderArr));
            $pageSize = 20;
            $total    = ceil(count($orderArr) / $pageSize);

            $model = new \Model\TradeRecord\OnlineTrade();

            $return = [];
            for ($i = 0; $i < $total; $i++) {

                $queryIds = array_slice($orderArr, $i * $pageSize, $pageSize);
                $res = $model->getRecListByOutTradeNoArr($queryIds);
                if (!empty($res)) {
                    foreach ($res as $item)
                        $return[$item['out_trade_no']] = $item['payid'] ?? '';
                }
            }
        }catch (\Exception $e) {
            $return = [];
        }

        return $return;
    }

    /**
     * 获取套票子票信息以及票券信息
     * @param array $params
     * @return array
     */
    public function orderListByMainOrderNum($mainOrderNum,$size,$result)
    {
        $modelTerminal      = new \Model\Order\OrderTerminal();
        //获取订单信息
        $res = self::getOrderList($mainOrderNum);
        if($res['code']!=200){
            return $this->returnData($res['code'], $res['msg']);
        }
        $subOrderIdArr = $res['data']['subOrderIdArr'];
        $orderList = array_column($res['data']['orderList'],null,'ordernum');

        $auditList = [];
        //获取票券审核信息
        $auditListRes = self::getAuditListByOrderNum(array_merge($subOrderIdArr,[$mainOrderNum]));
        if($auditListRes['code'] = 200 && !empty($auditListRes['data'])){
            foreach ($auditListRes['data'] as $value){
                $value['audit_data'] = empty($value['audit_data']) ? [] : json_decode($value['audit_data'],true) ;
                if(isset($value['audit_data']['after_sale_num']) && !empty($value['audit_data']['after_sale_num'])){
                    $auditList[$value['ordernum']][$value['stype']]['afterSale']= $value;
                }
                else{
                    $auditList[$value['ordernum']][$value['stype']]['common'] = $value;
                }
            }
        }
        //取主票第一级价格
        $mainWhere = [
            'level' => ['in', ['0', '1']], //取分销链顶级
        ];
        $mainOrderPrice =[];
        $mainOrderPriceRes = self::getSplitListByOrderNum($mainOrderNum,$mainWhere);
        if($mainOrderPriceRes['code'] == 200 && !empty($mainOrderPriceRes['data'])){
            $mainOrderPrice = reset($mainOrderPriceRes['data']);
        }

        //获取子票采购价格
        $subWhere = [
            'buyerid' => $orderList[$mainOrderNum]['apply_did'], //取主票供应商身份 获取 对应的采购价
        ];
        $subOrderPrice = [];
        $subOrderPriceRes = self::getSplitListByOrderNum($subOrderIdArr,$subWhere);
        if($subOrderPriceRes['code'] == 200 && !empty($subOrderPriceRes['data'])){
            $subOrderPrice = array_column($subOrderPriceRes['data'],null,'orderid');
        }
        //遍历构建列表数据
        foreach ($orderList as $item){
            $linkItem = [];
            $touristListRes = self::getTouristListForOrderNumArray($item['ordernum'],null,1,$size);
            if($touristListRes['code']!=200){
                return $this->returnData($touristListRes['code'], $touristListRes['msg']);
            }
            $touristList = $touristListRes['data'];

            //审核数据匹配
            $auditItem = $auditList[$item['ordernum']] ?? [];

            $auditTouristIdx = [];
            foreach ($auditItem as $sType => $ItemInfo){
                foreach ($ItemInfo as $refundType =>$ItemTypeInfo){
                    if(in_array($sType,[$modelTerminal::STYPE_REVOKE_PART,$modelTerminal::STYPE_REVOKE])){
                        $auditTouristIdx = self::deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,true);
                    }
                    elseif (in_array($sType,[$modelTerminal::STYPE_MODIFY,$modelTerminal::STYPE_CANCEL])){
                        $auditTouristIdx = self::deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,false);
                    }
                    else{
                        $auditTouristIdx = [];
                    }
                }
            }
            $linkItem['orderNum'] = $item['ordernum'];
            $linkItem['aid'] = $item['aid'];
            $linkItem['applyDid'] = $item['apply_id'];
            $linkItem['productType'] = $item['product_type'];
            $linkItem['status'] = $item['status'];
            $linkItem['payMode'] = $item['paymode'];
            $linkItem['tid'] = $item['tid'];
            $linkItem['pid'] = $item['pid'];
            $linkItem['lid'] = $item['lid'];
            $linkItem['refundNum'] = $item['refund_num'];
            $linkItem['verifiedNum'] = $item['verified_num'];
            $linkItem['originNum'] = $item['origin_num'];
            $linkItem['canRefund'] = $item['can_refund'];
            $linkItem['currentNum'] = $item['current_num'];
            $linkItem['isSelfSupply'] = ($item['aid'] == $item['apply_id']) ? 1 : 0;
            $linkItem['isAudit'] = array_key_exists($item['ordernum'],$auditList) ? 1 : 0;
            $isOneVote = false; //是否一票种一票
            if($linkItem['originNum']!= count( $touristList['list']) && count($touristList['list'])==1){
                $isOneVote = true;
            }
            $touristArray = [];
            foreach ($touristList['list'] as $touristInfo){
                $linkArray['touristId'] = $touristInfo['id'];
                $linkArray['tourist'] = $touristInfo['tourist'];
                $linkArray['voucherType'] = $touristInfo['voucher_type'];
                $linkArray['mobile'] = $touristInfo['mobile'];
                $linkArray['idx'] = $touristInfo['idx'];

                $linkArray['refundNum'] = 0;
                $linkArray['revokeNum'] = 0;
                $linkArray['refundAfterSaleNum'] = 0;
                $linkArray['revokeAfterSaleNum'] = 0;
                $linkArray['checkState'] = $touristInfo['check_state'];
                foreach ($auditTouristIdx as $refundType => $refundInfo){
                    if($refundType == 'common'){
                        foreach ($refundInfo as $refundName => $touristValue){
                            if(in_array($touristInfo['idx'],$touristValue)){
                                if($refundName == 'refund'){
                                    if($isOneVote){
                                        $linkArray['refundNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['refundNum'] = 1;
                                    }
                                }
                                else{
                                    if($isOneVote){
                                        $linkArray['revokeNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['revokeNum'] = 1;
                                    }
                                }
                            }
                        }
                    }
                    else{
                        foreach ($refundInfo as $refundName => $touristValue){
                            if(in_array($touristInfo['idx'],$touristValue)){
                                if($refundName == 'refund'){
                                    if($isOneVote){
                                        $linkArray['refundAfterSaleNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['refundAfterSaleNum'] = 1;
                                    }
                                }
                                else{
                                    if($isOneVote){
                                        $linkArray['revokeAfterSaleNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['revokeAfterSaleNum'] = 1;
                                    }
                                }
                            }
                        }
                    }
                }
                if($linkArray['refundNum'] || $linkArray['revokeNum'] || $linkArray['refundAfterSaleNum'] || $linkArray['revokeAfterSaleNum']){
                    $linkArray['checkState'] = CommonOrderTouristStatus::TOURIST_AUDIT_CODE;
                }

                $linkArray['idCard'] = $touristInfo['idcard'];
                $linkArray['chkCode'] = $touristInfo['chk_code'];
                array_push($touristArray,$linkArray);
                $linkItem['touristList'] = $touristArray;
                $linkItem['touristTotal'] = $touristList['total'];
            }
            if($item['ordernum'] == $mainOrderNum){
                $linkItem['salePrice'] = $mainOrderPrice['sale_money'];
                $linkItem['costPrice'] =  $mainOrderPrice['cost_money'];
                foreach ($subOrderIdArr as $subOrder){
                    $linkItem['packScale'][] = [
                        'orderNum' => $subOrder,
                        'tid' => $orderList[$subOrder]['tid'],
                        'scaleNum' => bcdiv($orderList[$subOrder]['origin_num'],$item['origin_num'])
                    ];
                }
                $linkItem['subList'] = $result['subList'];
                $result = $linkItem;
            }
            elseif(in_array($item['ordernum'],$subOrderIdArr)){
                $linkItem['salePrice'] = $subOrderPrice[$item['ordernum']]['sale_money'];
                $linkItem['costPrice'] = $subOrderPrice[$item['ordernum']]['cost_money'];
                array_push($result['subList'],$linkItem);
            }
            else{
                continue;
            }

        }
        return $this->returnData(200, '',$result);
    }

    /**
     * 根据主票获取子票订单信息
     * @param $mainOrderNum
     * @return array
     */
    private function getOrderList($mainOrderNum){
        //获取套票主子订单信息
        $orderToolsMode = new OrderTools();
        $orderIdArr = [$mainOrderNum];
        $subOrderIdArr = [];
        $sonOrderInfo   = $orderToolsMode->getPackSubOrder($mainOrderNum);
        if(!empty($sonOrderInfo)){
            $subOrderIdArr = array_column($sonOrderInfo, 'orderid');
            $orderIdArr = array_merge($orderIdArr,$subOrderIdArr);
        }
        $orderList = $orderToolsMode->getOrderInfo($orderIdArr);
        if(empty($orderList)){
            return $this->returnData(400, '非套票订单');
        }
        return $this->returnData(200, '',['mainOrderNum'=>$mainOrderNum,'subOrderIdArr'=>$subOrderIdArr,'orderList'=>$orderList]);
    }

    /**
     * 获取订单审核详情
     * @param $params
     * @return array
     */
    public function orderListByOrderNum($orderNum,$size,$result){
        $modelTerminal      = new \Model\Order\OrderTerminal();
        //获取订单信息
        $orderToolsMode = new OrderTools();
        $orderIdArr = [$orderNum];
        $orderList = $orderToolsMode->getOrderInfo($orderIdArr);
        if(empty($orderList)){
            return $this->returnData(400, '订单信息不存在');
        }

        $auditList = [];
        //获取票券审核信息
        $auditListRes = self::getAuditListByOrderNum($orderNum);
        if($auditListRes['code'] = 200 && !empty($auditListRes['data'])){
            foreach ($auditListRes['data'] as $value){
                $value['audit_data'] = empty($value['audit_data']) ? [] : json_decode($value['audit_data'],true) ;
                if(isset($value['audit_data']['after_sale_num']) && !empty($value['audit_data']['after_sale_num'])){
                    $auditList[$value['ordernum']][$value['stype']]['afterSale']= $value;
                }
                else{
                    $auditList[$value['ordernum']][$value['stype']]['common'] = $value;
                }
            }
        }
        //取订单第一级价格
        $mainWhere = [
            'level' => ['in', ['0', '1']], //取分销链顶级
        ];
        $orderPrice =[];
        $orderPriceRes = self::getSplitListByOrderNum($orderNum,$mainWhere);
        if($orderPriceRes['code'] == 200 && !empty($orderPriceRes['data'])){
            $orderPrice = reset($orderPriceRes['data']);
        }
        //遍历构建列表数据
        foreach ($orderList as $item){
            $linkItem = [];
            $touristListRes = self::getTouristListForOrderNumArray($item['ordernum'],null,1,$size);
            if($touristListRes['code']!=200){
                return $this->returnData($touristListRes['code'], $touristListRes['msg']);
            }
            $touristList = $touristListRes['data'];

            //审核数据匹配
            $auditItem = $auditList[$item['ordernum']] ?? [];

            $auditTouristIdx = [];
            foreach ($auditItem as $sType => $ItemInfo){
                foreach ($ItemInfo as $refundType =>$ItemTypeInfo){
                    if(in_array($sType,[$modelTerminal::STYPE_REVOKE_PART,$modelTerminal::STYPE_REVOKE])){
                        $auditTouristIdx = self::deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,true);
                    }
                    elseif (in_array($sType,[$modelTerminal::STYPE_MODIFY,$modelTerminal::STYPE_CANCEL])){
                        $auditTouristIdx = self::deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,false);
                    }
                    else{
                        $auditTouristIdx = [];
                    }
                }
            }
            $linkItem['orderNum'] = $item['ordernum'];
            $linkItem['aid'] = $item['aid'];
            $linkItem['applyDid'] = $item['apply_id'];
            $linkItem['productType'] = $item['product_type'];
            $linkItem['status'] = $item['status'];
            $linkItem['payMode'] = $item['paymode'];
            $linkItem['tid'] = $item['tid'];
            $linkItem['pid'] = $item['pid'];
            $linkItem['lid'] = $item['lid'];
            $linkItem['refundNum'] = $item['refund_num'];
            $linkItem['verifiedNum'] = $item['verified_num'];
            $linkItem['originNum'] = $item['origin_num'];
            $linkItem['canRefund'] = $item['can_refund'];
            $linkItem['currentNum'] = $item['current_num'];
            $linkItem['isSelfSupply'] = ($item['aid'] == $item['apply_id']) ? 1 : 0;
            $linkItem['isAudit'] = array_key_exists($item['ordernum'],$auditList) ? 1 : 0;
            $isOneVote = false; //是否一票种一票
            if($linkItem['originNum']!= count( $touristList['list']) && count($touristList['list'])==1){
                $isOneVote = true;
            }
            $touristArray = [];
            foreach ($touristList['list'] as $touristInfo){
                $linkArray['touristId'] = $touristInfo['id'];
                $linkArray['tourist'] = $touristInfo['tourist'];
                $linkArray['voucherType'] = $touristInfo['voucher_type'];
                $linkArray['mobile'] = $touristInfo['mobile'];
                $linkArray['idx'] = $touristInfo['idx'];

                $linkArray['refundNum'] = 0;
                $linkArray['revokeNum'] = 0;
                $linkArray['refundAfterSaleNum'] = 0;
                $linkArray['revokeAfterSaleNum'] = 0;
                $linkArray['checkState'] = $touristInfo['check_state'];
                foreach ($auditTouristIdx as $refundType => $refundInfo){
                    if($refundType == 'common'){
                        foreach ($refundInfo as $refundName => $touristValue){
                            if(in_array($touristInfo['idx'],$touristValue)){
                                if($refundName == 'refund'){
                                    if($isOneVote){
                                        $linkArray['refundNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['refundNum'] = 1;
                                    }
                                }
                                else{
                                    if($isOneVote){
                                        $linkArray['revokeNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['revokeNum'] = 1;
                                    }
                                }
                            }
                        }
                    }
                    else{
                        foreach ($refundInfo as $refundName => $touristValue){
                            if(in_array($touristInfo['idx'],$touristValue)){
                                if($refundName == 'refund'){
                                    if($isOneVote){
                                        $linkArray['refundAfterSaleNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['refundAfterSaleNum'] = 1;
                                    }
                                }
                                else{
                                    if($isOneVote){
                                        $linkArray['revokeAfterSaleNum'] = count($touristValue);
                                    }
                                    else{
                                        $linkArray['revokeAfterSaleNum'] = 1;
                                    }
                                }
                            }
                        }
                    }
                }
                if($linkArray['refundNum'] || $linkArray['revokeNum'] || $linkArray['refundAfterSaleNum'] || $linkArray['revokeAfterSaleNum']){
                    $linkArray['checkState'] = CommonOrderTouristStatus::TOURIST_AUDIT_CODE;
                }

                $linkArray['idCard'] = $touristInfo['idcard'];
                $linkArray['chkCode'] = $touristInfo['chk_code'];
                array_push($touristArray,$linkArray);
                $linkItem['touristList'] = $touristArray;
                $linkItem['touristTotal'] = $touristList['total'];

            }
            if($item['ordernum'] == $orderNum){
                $linkItem['salePrice'] = $orderPrice['sale_money'];
                $linkItem['costPrice'] =  $orderPrice['cost_money'];
            }
            else{
                continue;
            }
            $result = $linkItem;
        }
        return $this->returnData(200, '',$result);
    }

    /**
     * 获取指定订单号 票券信息 支持分页
     * @param array $params
     * @return array
     */
    public function touristListByOrderNum($orderNum,$page,$size,$touristIdx)
    {
        $modelTerminal      = new \Model\Order\OrderTerminal();
        //获取订单信息
        $orderToolsMode = new OrderTools();
        $orderInfo = $orderToolsMode->getOrderInfo($orderNum);
        if(empty($orderInfo)){
            return $this->returnData(400, '订单参数错误');
        }

        $auditList = [];
        //获取票券审核信息
        $auditListRes = self::getAuditListByOrderNum($orderNum);
        if($auditListRes['code'] = 200 && !empty($auditListRes['data'])){
            foreach ($auditListRes['data'] as $value){
                $value['audit_data'] = empty($value['audit_data']) ? [] : json_decode($value['audit_data'],true) ;
                if(isset($value['audit_data']['after_sale_num']) && !empty($value['audit_data']['after_sale_num'])){
                    $auditList[$value['ordernum']][$value['stype']]['afterSale']= $value;
                }
                else{
                    $auditList[$value['ordernum']][$value['stype']]['common'] = $value;
                }
            }
        }

        //取主票第一级价格
        if(in_array($orderInfo['ifpack'],[0,1])){
            $where = [
                'level' => ['in', ['0', '1']], //取分销链顶级
            ];
        }
        else{
            $where = [
                'buyerid' => $orderInfo['apply_did'], //取主票供应商身份 获取 对应的采购价
            ];
        }
        $mainOrderPrice =[];
        $mainOrderPriceRes = self::getSplitListByOrderNum($orderNum,$where);
        if($mainOrderPriceRes['code'] == 200 && !empty($mainOrderPriceRes['data'])){
            $mainOrderPrice = reset($mainOrderPriceRes['data']);
        }

        $touristListRes = self::getTouristListForOrderNumArray($orderInfo['ordernum'],$touristIdx,$page,$size);
        if($touristListRes['code']!=200){
            return $this->returnData($touristListRes['code'], $touristListRes['msg']);
        }
        $touristList = $touristListRes['data'];
        //审核数据匹配
        $auditItem = $auditList[$orderInfo['ordernum']] ?? [];
        $auditTouristIdx = [];
        foreach ($auditItem as $sType => $ItemInfo){
            foreach ($ItemInfo as $refundType =>$ItemTypeInfo){
                if(in_array($sType,[$modelTerminal::STYPE_REVOKE_PART,$modelTerminal::STYPE_REVOKE])){
                    $auditTouristIdx = self::deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,true);
                }
                elseif (in_array($sType,[$modelTerminal::STYPE_MODIFY,$modelTerminal::STYPE_CANCEL])){
                    $auditTouristIdx = self::deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,false);
                }
                else{
                    $auditTouristIdx = [];
                }
            }
        }
        $result['orderNum'] = $orderInfo['ordernum'];
        $result['aid'] = $orderInfo['aid'];
        $result['applyDid'] = $orderInfo['apply_id'];
        $result['productType'] = $orderInfo['product_type'];
        $result['status'] = $orderInfo['status'];
        $result['payMode'] = $orderInfo['paymode'];
        $result['tid'] = $orderInfo['tid'];
        $result['pid'] = $orderInfo['pid'];
        $result['lid'] = $orderInfo['lid'];
        $result['refundNum'] = $orderInfo['refund_num'];
        $result['verifiedNum'] = $orderInfo['verified_num'];
        $result['originNum'] = $orderInfo['origin_num'];
        $result['canRefund'] = $orderInfo['can_refund'];
        $result['currentNum'] = $orderInfo['current_num'];
        $result['isAudit'] = array_key_exists($orderInfo['ordernum'],$auditList) ? 1 : 0;
        $isOneVote = false; //是否一票种一票
        if($result['originNum']!= count( $touristList['list']) && count($touristList['list'])==1){
            $isOneVote = true;
        }
        $touristArray = [];
        foreach ($touristList['list'] as $touristInfo){
            $linkArray = [];
            $linkArray['touristId'] = $touristInfo['id'];
            $linkArray['tourist'] = $touristInfo['tourist'];
            $linkArray['voucherType'] = $touristInfo['voucher_type'];
            $linkArray['mobile'] = $touristInfo['mobile'];
            $linkArray['idx'] = $touristInfo['idx'];
            $linkArray['refundNum'] = 0;
            $linkArray['revokeNum'] = 0;
            $linkArray['refundAfterSaleNum'] = 0;
            $linkArray['revokeAfterSaleNum'] = 0;
            $linkArray['checkState'] = $touristInfo['check_state'];
            foreach ($auditTouristIdx as $refundType => $refundInfo){
                if($refundType == 'common'){
                    foreach ($refundInfo as $refundName => $touristValue){
                        if(in_array($touristInfo['idx'],$touristValue)){
                            if($refundName == 'refund'){
                                if($isOneVote){
                                    $linkArray['refundNum'] = count($touristValue);
                                }
                                else{
                                    $linkArray['refundNum'] = 1;
                                }
                            }
                            else{
                                if($isOneVote){
                                    $linkArray['revokeNum'] = count($touristValue);
                                }
                                else{
                                    $linkArray['revokeNum'] = 1;
                                }
                            }
                        }
                    }
                }
                else{
                    foreach ($refundInfo as $refundName => $touristValue){
                        if(in_array($touristInfo['idx'],$touristValue)){
                            if($refundName == 'refund'){
                                if($isOneVote){
                                    $linkArray['refundAfterSaleNum'] = count($touristValue);
                                }
                                else{
                                    $linkArray['refundAfterSaleNum'] = 1;
                                }
                            }
                            else{
                                if($isOneVote){
                                    $linkArray['revokeAfterSaleNum'] = count($touristValue);
                                }
                                else{
                                    $linkArray['revokeAfterSaleNum'] = 1;
                                }
                            }
                        }
                    }
                }
            }
            if($linkArray['refundNum'] || $linkArray['revokeNum'] || $linkArray['refundAfterSaleNum'] || $linkArray['revokeAfterSaleNum']){
                $linkArray['checkState'] = CommonOrderTouristStatus::TOURIST_AUDIT_CODE;
            }

            $linkArray['idCard'] = $touristInfo['idcard'];
            $linkArray['chkCode'] = $touristInfo['chk_code'];
            array_push($touristArray,$linkArray);
            $result['touristList'] = $touristArray;
            $result['touristTotal'] = $touristList['total'];
        }
        $result['salePrice'] = $mainOrderPrice['sale_money'] ?? 0;
        $result['costPrice'] =  $mainOrderPrice['cost_money'] ?? 0;
        if(in_array($orderInfo['ifpack'],[0,1]) && $mainOrderPrice['buyerid'] ==  $mainOrderPrice['sellerid']){
            $result['salePrice'] = 0;
            $result['costPrice'] = 0;
        }
        $result['isSelfSupply'] = ($orderInfo['aid'] == $orderInfo['apply_id']) ? 1 : 0;
        return $this->returnData(200, '',$result);
    }

    /**
     * 拆分审核数据
     * @param $auditData
     * @param $auditTouristIdx
     * @param $refundType
     * @param $isRevoke
     * @return array
     */
    private function deconstructionAuditData($ItemTypeInfo,$auditTouristIdx,$refundType,$isRevoke){
        $auditData = $ItemTypeInfo['audit_data'];
        $refundName = $isRevoke ? 'revoke' : 'refund';
        if(isset($auditData['batch_refund_more_idx']) && !empty($auditData['batch_refund_more_idx'])){
            $auditTouristIdx[$refundType][$refundName] = $auditData['batch_refund_more_idx'];
        }
        elseif(isset($auditData['tourist_idx']) && !empty($auditData['tourist_idx'])){
            $auditTouristIdx[$refundType][$refundName] = $auditData['tourist_idx'];
        }
        else{
            //一票种一票特殊处理
            if($ItemTypeInfo['modify_tnum']){
                $linkIdx = [];
                for ($i = 0; $i < $ItemTypeInfo['modify_tnum']; $i++) {
                    $linkIdx[] = 1;
                }
                $auditTouristIdx[$refundType][$refundName] = $linkIdx;
            }
            else{
                $auditTouristIdx[$refundType][$refundName] = [];
            }

        }
        return $auditTouristIdx;
    }

    /**
     * 根据订单号 获取对应票券信息，支持分页
     * @param $orderNum //支持数组或者字符串
     * @param int $page
     * @param int $size
     * @return array
     */
    private function getTouristListForOrderNumArray($orderNum,$idxArr = null,$page=1,$size=50){
        if(is_array($orderNum)){
            $orderNumArr = $orderNum;
        }
        else{
            $orderNumArr = [$orderNum];
        }
        $orderTouristInfoApi = new \Business\JavaApi\Order\Query\OrderTouristInfo();
        $orderTouristRes     = $orderTouristInfoApi->queryOrderTouristInfoByOrderIdPage($orderNumArr,false,$idxArr,null,null,null,$page,$size);
        if ($orderTouristRes['code'] != 200) {
            return $this->returnData($orderTouristRes['code'], $orderTouristRes['msg']);
        }
        $touristList = $orderTouristRes['data']['list'];
        $total = $orderTouristRes['data']['total'];
        return $this->returnData(200, '',['list'=>$touristList, 'total'=>$total]);
    }

    /**
     * 根据订单获取审核中数据
     * @param $orderNum
     * @return array
     */
    private function getAuditListByOrderNum($orderNum){
        if(is_array($orderNum)){
            $orderNumArr = $orderNum;
        }
        else{
            $orderNumArr = [$orderNum];
        }
        //获取审核表信息
        $modelTerminal      = new \Model\Order\OrderTerminal();
        $terminalChangeList = $modelTerminal->getTicketRefundOrders($orderNumArr, '*',[
            $modelTerminal::AUDIT_STATUS_INIT,
            $modelTerminal::AUDIT_STATUS_THIRD_AUDIT,
        ]);
        return $this->returnData(200, '',$terminalChangeList);
    }

    /**
     * 根据订单以及条件 获取分销链价格
     * @param $orderNum
     * @param $where
     * @param string $field
     * @return array
     */
    private function getSplitListByOrderNum($orderNum,$where,$field = 'orderid, buyerid, sellerid, cost_money, sale_money'){
        $subOrderModel = new SubOrderQuery();
        $res           = $subOrderModel->getInfoInSplitByOrder($orderNum, $field, $where, true);
        return $this->returnData(200, '',$res);
    }

    /**
     * 根据主票id 判断是否为演出捆绑票
     * @param $mainTid
     * @return bool
     * @throws \Exception
     */
    public function isBindTicketForMainTid($mainTid){
        if (empty($mainTid)) {
            return false;
        }
        $javaApi    = new \Business\CommodityCenter\Ticket();
        $ticketInfo = $javaApi->queryTicketInfoById($mainTid, 'id,pid,title', '', 'title,p_type');
        pft_log('BindTicket/debug',json_encode(['isBindTicketForMainTid',$ticketInfo,$mainTid],JSON_UNESCAPED_UNICODE));
        if (empty($ticketInfo)) {
            return false;
        }
        if($ticketInfo['land']['p_type'] != 'F') {
            return true;
        }
        else{
            return false;
        }
    }

    /**
     * 根据主票订单号 判断是否为演出捆绑票
     * @param $mainTid
     * @return bool
     * @throws \Exception
     */
    public function isBindTicketForMainOrderNum($mainOrderNum){
        if (empty($mainOrderNum)) {
            return false;
        }
        $OrderTools = new OrderTools();
        $queryRes =  $OrderTools ->getOrderInfo($mainOrderNum);
        pft_log('BindTicket/debug',json_encode(['isBindTicketForMainTid',$queryRes,$mainOrderNum],JSON_UNESCAPED_UNICODE));
        if(empty($queryRes)){
            return false;
        }
        $mainTid = $queryRes['tid'] ;
        return self::isBindTicketForMainTid($mainTid);
    }


    public function isBindTicketForOrderNum($orderNum){
        if (empty($orderNum)) {
            return false;
        }
        $OrderTools = new OrderTools();
        $orderInfo =  $OrderTools ->getOrderInfo($orderNum);
        pft_log('BindTicket/debug',json_encode(['isBindTicketForOrderNum',$orderNum,$orderInfo],JSON_UNESCAPED_UNICODE));
        if($orderInfo['ifpack'] == 1){
            return self::isBindTicketForMainTid($orderInfo['tid']);
        }
        elseif($orderInfo['ifpack'] == 2){
            return self::isBindTicketForMainOrderNum($orderInfo['pack_order']);
        }
        else{
            return false;
        }
    }
}
