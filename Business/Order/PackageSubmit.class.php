<?php
/**
 * 套票下单的相关业务封装
 * <AUTHOR>
 * @date 4/12-012
 * @modify dwer.cn 2019-02-14
 */

namespace Business\Order;

use Business\PackTicket\PackConst;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Exception;
use Library\Resque\Queue;
use Model\Order\OrderTools;
use Model\Order\SubOrderQuery;
use Model\PackTicket\PackageOrderProcess;
use Model\Product\Ticket;
use Library\Kafka\KafkaProducer;
use think\queue\Job;
use Business\PackTicket\Traits\PackageAbnormalOrderTrait;

class PackageSubmit
{
    use PackageAbnormalOrderTrait;
    const PAGE_LIMIT = 200;
    const PAGE_INIT = 1;
    const ORDER_STATE_SUCCESS = 1; //主票完成下单
    const ORDER_STATE_FAIL = 0; //主票下单异常

    const REVIEW_STATUS_CHECK = 0; //待复核
    const REVIEW_STATUS_SUCCESS = 1; //出票成功
    const REVIEW_STATUS_FAIL = 2; //出票失败

    const REVIEW_TYPE_REVIEW = 1; //主子未出票 待复核
    const REVIEW_TYPE_FAILED = 2; //下单失败短信异常 未通知
    const REVIEW_TYPE_COMPLETED = 3; //主子已出票 未通知
    const REVIEW_TYPE = [
        self::REVIEW_TYPE_REVIEW => '主子未出票 待复核',
        self::REVIEW_TYPE_FAILED => '下单失败短信异常 未通知',
        self::REVIEW_TYPE_COMPLETED => '主子已出票 未通知'
    ];

    const RETRY_TIME_MAP = [
        1 => 15,
        2 => 30,
        3 => 60,
        4 => 120,
        5 => 180,
        6 => 600,
        7 => 600,
        8 => 600,
        9 => 600,
        10 => 600
    ];

    //已经迁移到Business\Order\OrderNotify
    public static function MailNotify($ordernum, $email)
    {
        return true;
    }

    /**
     * 在线（线下）二次支付成功，下套票子票
     * <AUTHOR>
     * @date   2019-09-02
     */
    public function childOrderAfterPay($orderData)
    {
        $main = $orderData['mainOrder'];

        if (!in_array($main['p_type'], ['F', 'H'])) {
            return true;
        }

        $redisObj          = \Library\Cache\Cache::getInstance('redis');
        $showInfoKey       = 'childShowInfoJson:' . $main['ordernum'];
        $childShowInfoJson = $redisObj->get($showInfoKey);
        $childShowInfoArr  = [];

        if (in_array($main['p_type'], ['F', 'H']) && !empty($childShowInfoJson)) {
            $childShowInfoArr = json_decode($childShowInfoJson, true);
            // 修改时效时间
            $redisObj->set($showInfoKey, $childShowInfoJson, '', 720);
        }

        $orderName      = $main['ordername'];
        $orderTel       = $main['ordertel'];
        $mobileArea     = $main['order_area'];
        $mobileRegion   = $main['order_region'];
        $parentPid      = $main['pid'];
        $parentOrderNum = $main['ordernum'];
        $playTime       = $main['playtime'];
        $parentTnum     = $main['tnum'];
        $ptype          = $main['p_type'];
        $payMode        = -1;

        $childSpecialSettingArr = [
            'parent_contact_info_more' => [
                'mobileArea' => $mobileArea,
                'mobileRegion' => $mobileRegion,
            ]
        ];
        try {
            pft_log('order/package', "{$parentOrderNum}:开始下子单");
            $this->submitChildrenOrder($orderName, $orderTel, $parentPid, $parentOrderNum, $playTime, $parentTnum,
                $ptype, $payMode, '', '', [], $childShowInfoArr, 0, $childSpecialSettingArr);
        } catch (Exception $e) {
            $errMsg  = $e->getMessage();
            $errCode = $e->getCode();
            //重试
            if ($errCode == 201) {
                return;
            }

            pft_log('order/package', "{$parentOrderNum}:下子单失败:[$errMsg]");
        }

        return true;
    }

    /***
     * 演出套票下单——生成子订单
     *
     * @param  string  $orderName  游客姓名
     * @param  string  $orderTel  游客手机号
     * @param  int  $parentPid  主票pid
     * @param  string  $parentOrderNum  演出门票主票订单号
     * @param  string  $playTime  演出时间
     * @param  int  $parentTnum  购票数量
     * @param  string  $ptype  产品类型，目前只有F和H有传到这里
     * @param  string  $payMode  产品类型，目前只有F和H有传到这里
     * @param  string  $orderMode  产品类型，目前只有F和H有传到这里
     * @param  string  $serialsLetter  流水号字母部分（云票务用户自己填写）
     * @param  string  $serialsNumber  流水号数字部分 (云票务用户自己填写)
     * @param  string  $visibleArr  温泉手牌号
     * @param  array  $childTicketShowInfoArr  演出子票订单座位信息
     * @param  int  $retryTimes  已经尝试次数
     * @param  array  $specialSettingArr  子票遵循主票下单的一些更多信息
     *
     * @return bool
     * @throws Exception
     */
    public function submitChildrenOrder($orderName, $orderTel, $parentPid, $parentOrderNum, $playTime, $parentTnum, $ptype, $payMode, $serialsLetter = '', $serialsNumber = '', $visibleArr = [], $childTicketShowInfoArr = [], $retryTimes = 0, $specialSettingArr = [])
    {
        $startTime = microtime(true);

        $contactInfo = $specialSettingArr['parent_contact_info_more'] ?? [];
        //基础检测,并返回相关数据
        $checkRes = $this->_packCheck($parentPid, $parentOrderNum, $ptype, $parentTnum, $visibleArr, $playTime,
            $orderTel, $contactInfo);
        if ($checkRes[0] != 0) {
            pft_log('order/order/package', $parentOrderNum . '|' . json_encode($checkRes));
            throw new Exception($checkRes[1]);
        }
        $parentOrderSmsInfo = $specialSettingArr['sms_info'] ?? [];

        $parentId             = $checkRes[2]['tid'];
        $buyId                = $checkRes[2]['member_id'];
        $successTidArr        = $checkRes[2]['success_tid_arr'];
        $childrenTickets      = $checkRes[2]['child_tickets'];
        $personId             = $checkRes[2]['person_id'];
        $voucherType          = $checkRes[2]['voucher_type'];
        $idcardArr            = $checkRes[2]['idcardArr'];
        $packageTimeShareInfo = $checkRes[2]['package_time_share_info'];
        $contactsMoreInfo     = $checkRes[2]['contacts_more_info'];

        $pidList = [];
        foreach ($childrenTickets as $key => $ticket) {
            // 如果有部分子票已经下单成功了那么跳过
            if (in_array($ticket['tid'], $successTidArr)) {
                unset($childrenTickets[$key]);
                continue;
            }
            //演出套票，检测子票库存
            if (!$ticket['tid']) {
                $pidList[] = $ticket['pid'];
            }
        }

        if (count($pidList)) {
            //兼容旧的数据，没有配置tid的
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketArr  = $javaApi->queryTicketInfoByProductIds($pidList, 'id,pid');
            $ticketInfo = array_column($ticketArr, 'ticket');
            $tidList    = [];
            foreach ($ticketInfo as $item) {
                $tidList[$item['pid']] = $item['id'];
            }

            //$ticketModel = new Ticket('slave');
            //$tidList     = $ticketModel->getTicketInfoByPid($pidList, 'pid,id', true);
            foreach ($childrenTickets as $key => $item) {
                if (isset($tidList[$item['pid']])) {
                    $childrenTickets[$key]['tid'] = $tidList[$item['pid']];
                }
            }
        }
        //开放功能校验
        $isAllowPackNotice = (new BaseSubmit())::isAllowPackNotice($buyId);
        //并行下单的参数
        $multiCallElems = $this->_createMultiOrderParams($parentOrderNum, $parentTnum, $playTime, $orderTel, $orderName,
            $serialsLetter, $serialsNumber, $visibleArr, $childTicketShowInfoArr, $childrenTickets, $personId, $buyId,
            $payMode, $voucherType, $idcardArr, $packageTimeShareInfo, $contactsMoreInfo, $specialSettingArr);

        //并行下单
        $yarClient = new \Library\Tools\YarClient('open');
        //切片
        $chunk = array_chunk($multiCallElems, 20);

        //未知错误（网络错误）这时候需要重试,而不是直接判定为下单失败
        $retry = 0;

        $errMsg             = '';
        $successChildOrders = $allResult = [];
        foreach ($chunk as $oneChunk) {
            $result = $yarClient->multiCall($oneChunk);
            if (!is_array($result) || count($result) != count($oneChunk)) {
                $errMsg = '子票下单出错';
                $retry  = 1;
                break;
            }

            foreach ($result as $key => $item) {
                if (!isset($item['code']) || $item['code'] != 200) {
                    if ($item['code'] != 110) {
                        $errMsg = $item['msg'] ?: $item['data']['err_msg'];
                        $errMsg = "套票子票{$oneChunk[$key][2][2]}下单失败，主票自动取消；子票{$oneChunk[$key][2][2]} 失败原因：{$errMsg}";
                        @pft_log('order/package', json_encode(['$oneChunk',$oneChunk,$item], JSON_UNESCAPED_UNICODE));
                        break 2;
                    } else {
                        continue;
                    }
                }
                $successChildOrders[] = $item['data']['ordernum'];
            }

            $allResult[] = $result;
        }

        $endTime = microtime(true);

        $logData = [
            'key'       => '套票子票下单新',
            'ordernum'  => $parentOrderNum,
            'res'       => $allResult,
            'retry'     => $retry,
            'cost_time' => substr($endTime - $startTime, 0, 5),
        ];

        @pft_log('order/package', json_encode($logData, JSON_UNESCAPED_UNICODE));

        if ($errMsg) {
            if ($retry && $retryTimes < 3) {
                $this->_orderRetry(func_get_args());
                throw new Exception("套票子票下单重试", 201);
            } else {
                @pft_log('order/package', "下单失败:".json_encode([$parentOrderNum, $visibleArr, $buyId, $errMsg], JSON_UNESCAPED_UNICODE));
                //增加失败库表记录以及通知
                $this->_orderFail($parentOrderNum, $visibleArr, $buyId, $errMsg);
                if($isAllowPackNotice && $ptype =='F'){
                    //记录db 并初次校验
                    $packageOrderProcess = new PackageOrderProcess();
                    $insertData = [
                        'parent_ordernum'=>$parentOrderNum,
                        'parent_tid'=>$parentId,
                        'order_state'=>$packageOrderProcess::UN_ORDER_STATUS,
                        'ticked_state'=>$packageOrderProcess::UN_TICKET_STATUS,
                        'notice_state' => $packageOrderProcess::UN_NOTICE_STATUS,
                        'expand_info' => json_encode(['sms_info'=>$parentOrderSmsInfo],JSON_UNESCAPED_UNICODE),
                    ];
                    $processId = $packageOrderProcess->insertData($insertData);
                    $remark = "下单失败短信异常发送通知，订单号：{$parentOrderNum}";
                    self::openPaltFormNotice(false, $processId, $parentOrderNum,[],$remark,[],$errMsg);
                }
                //记录异常日志
                $logParams = [
                    'ac'     => 'orderPackageChild',
                    'params' => [$parentOrderNum, $parentTnum, $playTime, $orderTel, $orderName,
                        $serialsLetter, $serialsNumber, $visibleArr, $childTicketShowInfoArr, $childrenTickets, $personId, $buyId,
                        $payMode, $voucherType, $idcardArr, $packageTimeShareInfo, $contactsMoreInfo, $specialSettingArr],
                ];
                $logResponse = [
                    'ac'        => 'orderPackageChild',
                    'handleRes' => $logData,
                    'response'  => $errMsg,
                ];
                $this->recordAnAbnormal($parentOrderNum, $logParams,$logResponse,PackConst::ABNORMAL_PROCESS_ORDER,
                    PackConst::ABNORMAL_REASON_OTHER,PackConst::ABNORMAL_LINKAGE_YES,$errMsg);
                throw new Exception("子票下单失败，错误代码:" . json_encode($result), 500);
            }
        }

        //更新套票状态
        $this->_packageOrderSuccess($parentOrderNum);
        if ($ptype == 'H') {
            //更新演出套票订单的状态
            $this->_updateShowPackageStatus($successChildOrders);
        }
        //记录套票下单时 温泉手牌信息， 用于三方通知出票失败，异步回滚
        if(!empty($visibleArr)){
            $cacheKey = "pack_ticket_visible:{$parentOrderNum}";
            $cache = \Library\Cache\RedisCache::Connect('master');
            $cache->hSet($cacheKey,'visible',json_encode([$visibleArr],JSON_UNESCAPED_UNICODE));
            $cache->expire($cacheKey, 86400);//设置过期时间为1天
        }

        if($isAllowPackNotice && $ptype =='F'){
            //记录db 并初次校验
            $packageOrderProcess = new PackageOrderProcess();
            $insertData = [
                'parent_ordernum'=>$parentOrderNum,
                'parent_tid'=>$parentId,
                'order_state'=>$packageOrderProcess::ORDER_STATUS,
                'ticked_state'=>$packageOrderProcess::UN_TICKET_STATUS,
                'notice_state' => $packageOrderProcess::UN_NOTICE_STATUS,
                'expand_info' => json_encode(['sms_info'=>$parentOrderSmsInfo],JSON_UNESCAPED_UNICODE),
            ];
            $processId = $packageOrderProcess->insertData($insertData);
            self::reviewPackOrderStatus($parentOrderNum,$processId,$parentOrderSmsInfo);
        }
        else{
            $this->_otaNotify($parentOrderNum,true);
        }
        return true;
    }

    /**
     * 子票都下单成功通知ota
     *
     * @param  string  $parentOrderNum  主票订单号
     *
     * @return string
     */
    private function _otaNotify($parentOrderNum ,$isSucceed,$desc= '')
    {
        $jobId = -1;
        if($isSucceed){
            $otaOrderBiz   = new \Business\Ota\Order();
            $allCodeResArr = $otaOrderBiz->getAllCode($parentOrderNum);
            if ($allCodeResArr['code'] == 200) {
                // 开始触发异步通知
                // 队列通知下游对接系统
                $queue = new \Library\Resque\Queue();
                $jobId = $queue->delay(strtotime("+ 3 seconds"), 'cooperation_system', 'AsyncCode_Job',
                    [
                        'pftOrder'  => $parentOrderNum,
                        'code'      => $allCodeResArr['data']['code'],
                        'qrcodeUrl' => $allCodeResArr['data']['qrcode'],
                        'isSucceed' => $isSucceed,
                    ]
                );
                // $res = $otaOrderBiz->asyncCode($parentOrderNum, $allCodeResArr['data']['code'], 1, true, $allCodeResArr['data']['qrcode']);
                pft_log('order/package/code/notice', json_encode([$parentOrderNum, $allCodeResArr, $jobId]));
            } else {
                // 重新触发这个通知
                // 记录日志
                pft_log('order/package/code/notice', '失败-' . json_encode([$parentOrderNum, $allCodeResArr]));
            }
        }
        else{
            // 开始触发异步通知
            $queue = new \Library\Resque\Queue();
            $jobId = $queue->push('cooperation_system', 'AsyncCode_Job',
                [
                    'pftOrder'  => $parentOrderNum,
                    'isSucceed' => $isSucceed,
                    'desc'=> $desc,
                ]
            );
            // $res = $otaOrderBiz->asyncCode($parentOrderNum, $allCodeResArr['data']['code'], 1, true, $allCodeResArr['data']['qrcode']);
            pft_log('order/package/code/notice', '进入异步通知-' . json_encode([$parentOrderNum, $isSucceed, $jobId]));
        }
        return $jobId;
    }

    /**
     * 套票主票发送短信
     * @param $parentOrderNum
     * @param array $parentOrderSmsInfo
     * @param int $processId
     * @return int|string
     */
    private function _sendPackageSms($parentOrderNum,$parentOrderSmsInfo = [])
    {
        $notifyJobId = -1;
        if(empty($parentOrderSmsInfo)){
            //从库表中读取,判断是否需要发送短信
            $packageOrderProcess = new PackageOrderProcess();
            $res = $packageOrderProcess->getRetryRecordListForOrderNum([$parentOrderNum]);
            if(empty($res)){
                @pft_log('order/package', json_encode(['套票订单通知异常:空数据',$res, $parentOrderNum], JSON_UNESCAPED_UNICODE));
                return $notifyJobId;
            }
            $res = reset($res);
            $processId = $res['id'];
            if(empty($res['expand_info'])){
                @pft_log('order/package', json_encode(['套票订单通知异常',$res, $parentOrderNum,$processId], JSON_UNESCAPED_UNICODE));
                return $notifyJobId;
            }
            $parentOrderSmsInfo = json_decode($res['expand_info'],true);
            $parentOrderSmsInfo = $parentOrderSmsInfo['sms_info'] ?? [];
        }
        if(!empty($parentOrderSmsInfo)){
            $notifyJobId = Queue::push('notify', 'OrderNotify_Job', $parentOrderSmsInfo);
            @pft_log('order/package', json_encode(['套票订单通知',$notifyJobId, $parentOrderNum], JSON_UNESCAPED_UNICODE));
            return $notifyJobId;
        }
        else{
            @pft_log('order/package', json_encode(['套票订单通知异常',$parentOrderSmsInfo, $parentOrderNum,$processId], JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    private function _packageOrderSuccess($parentOrdernum)
    {
        $successData = [
            'packOrder' => 1,
            'ifpack'    => 1,
            'sysorder'  => 100,
        ];
        $api         = new \Business\JavaApi\Order\OrderAddonUpdate();
        $api->orderAddonInfoUpdate($parentOrdernum, $successData);

        return true;
    }

    private function _updateShowPackageStatus($successChildOrders)
    {
        $data['dstatus'] = 6;
        $api             = new \Business\JavaApi\Order\OrderAddonUpdate();
        $api->BatchOrderAddonInfoUpdate($successChildOrders, $data);

        return true;
    }

    /**
     * 生成并行下单的参数
     *
     * @param  string  $parentOrderNum  主票订单号
     * @param  int  $parentTnum  主票票数
     * @param  string  $playTime  游玩日期
     * @param  string  $orderTel  手机号
     * @param  string  $orderName  联系人
     * @param  string  $serialsLetter  流水号
     * @param  string  $serialsNumber  流水号
     * @param  array  $visibleArr  手牌
     * @param  array  $childTicketShowInfoArr  演出信息
     * @param  array  $childrenTickets  子票列表
     * @param  int  $personId  身份证数组
     * @param  int  $buyId  购买者id
     * @param  int  $payMode  支付方式
     * @param  int  $voucherType  身份类型
     * @param  array  $idcardArr  游客信息
     * @param  array  $packageTimeShareInfo  子票分时预约信息
     * param  array  $specialSettingArr  子票遵循主票下单的一些更多信息
     *
     * @return array
     */
    private function _createMultiOrderParams($parentOrderNum, $parentTnum, $playTime, $orderTel, $orderName, $serialsLetter, $serialsNumber, $visibleArr, $childTicketShowInfoArr, $childrenTickets,
        $personId, $buyId, $payMode, $voucherType = 0, $idcardArr = [], $packageTimeShareInfo = [], $contactsMoreInfo = [], $specialSettingArr = [])
    {
        $parentContactInfo = $specialSettingArr['parent_contact_info_more'] ?? [];
        //联系人信息
        $contactArr = [
            'ordername'    => $orderName,
            'ordertel'     => $orderTel ?: '12301',
            'mobile_area'  => $parentContactInfo['mobileArea'] ?? '',
            'mobile_region' => $parentContactInfo['mobileRegion'] ?? '',
            'contacttel'   => '',
            'personid'     => $personId,
            'voucher_type' => $voucherType,
            'moreList'     => $contactsMoreInfo,
        ];
        //售卖站点信息
        $saleSiteArr = [
            'site_id'  => 0,
            'terminal' => 0,
            'stuff_id' => $buyId,
        ];
        //分销链信息
        $saleSetting = [
            'is_sale'        => false, //是否散客购买
            'upper_supplier' => 0, //更上一级的供应商，如果末级散客没有设置分销链的时候就有这个参数
            'discount_list'  => [], //各种优惠策略数组
        ];
        //备注信息
        $remarkArr = [
            'memo'     => '',
            'origin'   => '',
            'assembly' => 0,
        ];

        //下单类型,套票子票
        $linkOrderInfoArr = ['link_type' => 'package_son', 'parent_ordernum' => $parentOrderNum];
        //游玩日期
        $playTime = date('Y-m-d', strtotime($playTime));
        //渠道
        $orderMode = 23;

        $multiCallElems = [];
        foreach ($childrenTickets as $ticket) {
            $tid  = $ticket['tid'];
            $tnum = $ticket['num'];

            //自定义流水号
            $remarkArr['serial_number'] = $this->_dealSerialNumber($serialsLetter, $serialsNumber, $tnum);

            if (isset($packageTimeShareInfo[$tid])) {
                $remarkArr['time_share_info'] = $packageTimeShareInfo[$tid];
            } else {
                $remarkArr['time_share_info'] = [];
            }

            //演出信息处理
            if (isset($childTicketShowInfoArr[$tid])) {
                $showInfoArr          = $this->_dealShowInfoArr($childTicketShowInfoArr, $tid);
                $contactArr['is_sms'] = true;
            } else {
                $contactArr['is_sms'] = false;
                $showInfoArr          = [];
            }

            if ($payMode == 4) {
                //现场支付
                $realPaymode = 4;
            } elseif ($buyId == $ticket['aid']) {
                //自供自销
                $realPaymode = 3;
            } else {
                //不指定支付方式
                $realPaymode = -1;
            }

            $params           = [
                $buyId,
                $ticket['aid'],
                $tid,
                $tnum * $parentTnum,
                $playTime,
                $orderMode,
                $realPaymode,
                true,
                $saleSetting,
                $contactArr,
                '',
                $idcardArr,
                $linkOrderInfoArr,
                $saleSiteArr,
                $remarkArr,
                $showInfoArr,
                $specialSettingArr,
            ];
            $multiCallElems[] = ['open', 'common_order', $params];
        }

        return $multiCallElems;
    }

    /**
     * 自定义流水号处理
     *
     * @param [type] $serialsLetter
     * @param [type] $serialsNumber
     * @param [type] $tnum
     *
     * @return string
     * <AUTHOR>
     */
    private function _dealSerialNumber($serialsLetter, $serialsNumber, $tnum)
    {
        $serialNumber = '';
        if (!empty($serialsLetter) && !empty($serialsNumber)) {
            //云票务自填的流水号
            $packProcessArr = [];
            $tmpSerialsNum  = $serialsNumber;
            for ($i = 1; $i <= $tnum; $i++) {
                $packProcessArr['serials'][] = $serialsLetter . $tmpSerialsNum;
                $tmpSerialsNum++;
            }
            $serialNumber = json_encode($packProcessArr);
        }

        return $serialNumber;
    }

    /**
     * 演出信息处理
     *
     * @param [type] $childTicketShowInfoArr
     * @param [type] $tid
     *
     * @return array
     * <AUTHOR>
     */
    private function _dealShowInfoArr($childTicketShowInfoArr, $tid)
    {
        $showInfoArr = [];
        if (isset($childTicketShowInfoArr[$tid])) {
            $showInfoArr['venue_id']       = $childTicketShowInfoArr[$tid]['showInfoArr']['venue_id'];       //演出场馆ID
            $showInfoArr['round_id']       = $childTicketShowInfoArr[$tid]['showInfoArr']['round_id'];       //演出场次ID
            $showInfoArr['area_id']        = $childTicketShowInfoArr[$tid]['showInfoArr']['area_id'];        //演出分区ID
            $showInfoArr['link_seat_mark'] = $childTicketShowInfoArr[$tid]['showInfoArr']['link_seat_mark']; //演出连坐标识
        }

        return $showInfoArr;
    }

    /**
     * 下单检测
     *
     * @param [type] $parentPid
     * @param [type] $parentOrderNum
     *
     * @return array
     * <AUTHOR>
     */
    private function _packCheck($parentPid, $parentOrderNum, $ptype, $parentTnum, $visibleArr, $playTime, $orderTel, $contactInfo = [])
    {
        $modelTicket = new Ticket();
        $ticketInfo  = $modelTicket->getTicketInfoByPid($parentPid, 'id,apply_did');
        //获取扩展属性
        if (!$ticketInfo) {
            return [204, '门票不存在'];
        }

        $packBiz         = new \Business\Product\PackTicket();
        $childrenTickets = $packBiz->getTickets($ticketInfo['id']);
        if (!$childrenTickets) {
            return [204, '套票关联的子票不存在或已下架'];
        }

        //检测是否已经处理过
        //$submitModel = new OrderSubmit();
        //if ($submitModel->isSubOrderFinish($parentOrderNum)) {
        //    return [204, '子票已经处理完成，不需要重复处理'];
        //}

        $queryParams = [$parentOrderNum];
        $checkRes    = \Business\JavaApi\Order\Query\Container::query('orderAddon', 'packageTicketGenComplete',
            $queryParams);
        if ($checkRes['code'] == 200 && $checkRes['data']) {
            return [204, '子票已经处理完成，不需要重复处理'];
        }

        //打包者也就是下单人
        $buyId = $ticketInfo['apply_did'];

        //获取取票人信息
//        $orderUserModel = new OrderUser();
        $orderToolModel = new OrderTools();
        $subOrderModel  = new SubOrderQuery();

        //已经处理的门票
        $successTidArr = [];
        $subOrderModel = new SubOrderQuery();
        $successArr  = $subOrderModel->getPackOrdersInfo($parentOrderNum);
        //$successArr    = $orderToolModel->getPackOrdersInfo($parentOrderNum);
        if ($successArr) {
            $successTidArr = array_column($successArr, 'tid');
        }

//        $orderInfo = $orderUserModel->getOneOrderUserByOrdernum($parentOrderNum, 'personid,voucher_type');
        $queryParams = [[$parentOrderNum]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo', 'getOrderUserInfoByOrderNum',
            $queryParams);
        $orderInfo   = [];
        if ($queryRes['code'] == 200) {
            $orderInfo = $queryRes['data'][0];
        }
        if (empty($orderInfo)) {
            $orderInfo = $orderToolModel->getOrderInfo($parentOrderNum, 'personid');
        }
        $personId    = $orderInfo['personid'];
        $voucherType = isset($orderInfo['voucher_type']) ? $orderInfo['voucher_type'] : 1;

        //获取游客信息
        $touristInfoList = $orderToolModel->getOrderTouristInfo($parentOrderNum);
        //获取取票人更多信息
        $touristInfoMoreList = $subOrderModel->getOrderDetail($parentOrderNum, 'de.product_ext');
        //游客更多信息 -证件
        $touristMoreInfo = [];
        //取票人更多信息 -证件
        $contactsMoreInfo = [];
        if ($touristInfoMoreList) {
            $touristInfoMoreList = json_decode($touristInfoMoreList['product_ext'], true);
            if (!empty($touristInfoMoreList['moreInfo'])) {
                foreach ($touristInfoMoreList['moreInfo'] as $moreTouristdata) {
                    //证件类型
                    $documentType = isset($moreTouristdata['name']) ? (int)$moreTouristdata['name'] : '';
                    //证件名称 证件类型为0时有值
                    $documentKey = isset($moreTouristdata['text']) ? trim($moreTouristdata['text']) : '';
                    //证件号
                    $documentValue = isset($moreTouristdata['documentValue']) ? trim($moreTouristdata['documentValue']) : '';
                    if ($moreTouristdata['target'] == 1) {
                        $contactsMoreInfo[] = [
                            'name'          => $documentType,
                            'text'          => $documentKey,
                            'documentValue' => $documentValue,
                        ];
                    }
                }
            }
        }

        //获取游客更多信息和证件照片
        $completeTouristInfoJavaApi = new \Business\JavaApi\Order\Query\OrderTouristInfo();
        $completeTouristInfoListRs  = $completeTouristInfoJavaApi->getCompleteTouristInfo([$parentOrderNum]);
        $completeTouristInfoList    = empty($completeTouristInfoListRs['data']) ? [] : $completeTouristInfoListRs['data'];

        if (!empty($touristInfoList)) {
            foreach ($touristInfoList as $key => $touristData) {
                foreach ($completeTouristInfoList as $completeTouristInfo) {
                    if ($touristData['id'] == $completeTouristInfo['id']) {
                        $touristInfoList[$key]['certPic'] = $completeTouristInfo['ext']['certPic'] ?? [];
                        $touristInfoMoreList = empty($completeTouristInfo['more_info']) ? [] : $completeTouristInfo['more_info'];
                        foreach ($touristInfoMoreList as $moreInfo) {
                            $touristExInfos                            = [
                                'name'          => $moreInfo['cert_type'],
                                'text'          => $moreInfo['remark'],
                                'realName'      => $touristData['tourist'],
                                'documentValue' => $moreInfo['cert_info'],
                                'extParam'      => [
                                    'certPic'       => $moreInfo['ext']['certPic'] ?? [],
                                ],
                            ];
                            if (!empty($moreInfo['ext']['fieldType'])) {
                                $touristExInfos['extParam']['fieldType'] = $moreInfo['ext']['fieldType'];
                            }
                            $touristMoreInfo[$touristData['id']][] = $touristExInfos;
                        }
                    }
                }
            }
        }

        $idcardArr = [];
        if (!empty($touristInfoList)) {
            foreach ($touristInfoList as $item) {
                if ($item['idcard'] == '' && !isset($touristMoreInfo[$item['id']])) {
                    continue;
                }
                $idcardArr[] = [
                    'name'         => $item['tourist'],
                    'idcard'       => $item['idcard'],
                    'voucher_type' => $item['voucher_type'],
                    'mobile'       => $item['mobile'],
                    'mobile_area'  => $item['mobile_area'] ?? '',
                    'mobile_region' => $item['mobile_region'] ?? '',
                    'moreList'     => isset($touristMoreInfo[$item['id']]) ? $touristMoreInfo[$item['id']] : [],
                    'cert_url'     => isset($item['certPic']) ? $item['certPic'] : [],
                ];
            }
        }

        //获取子票的分时预约信息
        $orderExt = $orderToolModel->getOrderDetailInfo($parentOrderNum, 'ext_content');
        $extInfo  = json_decode($orderExt['ext_content'], true);

        $packageTimeShareInfo = [];
        if (isset($extInfo['packageTimeShareInfo']) && $extInfo['packageTimeShareInfo']) {
            $packageTimeShareInfo = $extInfo['packageTimeShareInfo'];
        }

        $data = [
            'tid'                     => $ticketInfo['id'],
            'person_id'               => $personId,
            'voucher_type'            => $voucherType,
            'idcardArr'               => $idcardArr,
            'member_id'               => $buyId,
            'child_tickets'           => $childrenTickets,
            'success_tid_arr'         => $successTidArr,
            'package_time_share_info' => $packageTimeShareInfo,
            'contacts_more_info'      => $contactsMoreInfo,
        ];

        return [0, '', $data];
    }

    /**
     * 子票下单失败，取消主票
     *
     * @param [type] $parentOrderNum
     * @param [type] $visibleArr
     * @param [type] $buyId
     * @param [type] $errMsg
     *
     * @return void
     * <AUTHOR>
     */
    private function _orderFail($parentOrderNum, $visibleArr, $buyId, $errMsg)
    {
        $jobInfo = [
            'job_type' => 'package_cancel',
            'job_data' => [
                'ordernum'   => $parentOrderNum,
                'visibleArr' => $visibleArr,
                'error_info' => $errMsg,
                'apply_did'  => $buyId,
            ],
        ];

        Queue::push('order', 'Order_Job', $jobInfo);
    }

    private function _orderRetry($originParams)
    {
        $args = [
            'ordername'       => $originParams[0],
            'ordertel'        => $originParams[1],
            'parent_pid'      => $originParams[2],
            'parent_ordernum' => $originParams[3],
            'playtime'        => $originParams[4],
            'parent_tnum'     => $originParams[5],
            'ptype'           => $originParams[6],
            'paymode'         => $originParams[7],
            'serials_letter'  => $originParams[8],
            'serials_number'  => $originParams[9],
            'visible_arr'     => $originParams[10],
            'child_show_info' => $originParams[11],
            'retry_times'     => $originParams[12] ?? 0,
            'special_setting_arr' => $originParams[13] ?? [],
        ];

        Queue::push('order', 'PackageOrderRetry_Job', $args);
    }

    /**
     * 校验主票下所有子票是否都是已出票状态
     * @param $parentOrderNum
     * @return array
     */
    public function checkPackOrderStatus($parentOrderNum){
        $rejectOrderStatus = [
            CommonOrderStatus::CANCELED_CODE =>  CommonOrderStatus::CANCELED_NAME,
            CommonOrderStatus::CANCEL_BY_TERMINAL_CODE =>  CommonOrderStatus::CANCEL_BY_TERMINAL_NAME,
            CommonOrderStatus::DELETED_CODE =>  CommonOrderStatus::DELETED_NAME,
        ];
        $packOrder = new OrderTools();
        //检索套票子票订单信息
        $childOrderList = $packOrder->getPackOrdersInfo($parentOrderNum);
        @pft_log('order/package', json_encode([$childOrderList], JSON_UNESCAPED_UNICODE));
        if($childOrderList){
            $childOrderList = array_column($childOrderList,null,'orderid');
            //所有子票都下单完成并且主子票都是已出票状态 且任一主/子订单没有被全部取消为出票成功，其他情况算是出票失败
            foreach ($childOrderList as $orderId => $orderInfo){
                //失败优先 校验 会出现多个子票绑定上游，任一子票取消，及失败，不用一直等其他子票出票
                if(array_key_exists($orderInfo['status'],$rejectOrderStatus)){
                    return ['is_order_completed'=>self::REVIEW_STATUS_FAIL, 'order_id'=>$orderId,
                        'order_status'=>$orderInfo['status'],'order_status_msg'=>$rejectOrderStatus[$orderInfo['status']]];
                }
            }
            foreach ($childOrderList as $orderId => $orderInfo){
                if(($orderInfo['status']  == CommonOrderStatus::WAIT_PRINTTICKET_CODE)  || ($orderInfo['payStatus'] == 2)){
                    return ['is_order_completed'=>self::REVIEW_STATUS_CHECK, 'order_id'=>$orderId,
                        'order_status'=>$orderInfo['status'],'order_status_msg'=>CommonOrderStatus::WAIT_PRINTTICKET_NAME];
                }
            }
            return ['is_order_completed' => self::REVIEW_STATUS_SUCCESS, 'order_id'=>$parentOrderNum, 'order_status'=>0,'order_status_msg'=>'未使用'];
        }
        else{
            return [];
        }

    }

    /**
     * 通知开发平台【下游】出票结果
     * @param $isSucceed
     * @param $parentOrderNum
     * @return bool
     */
    public function openPaltFormNotice($isSucceed, $id, $parentOrderNum, $updateData = [],$remark ='',$parentOrderSmsInfo= [],$desc ='出票失败'){
        $packageOrderProcess = new PackageOrderProcess();
        $res = $packageOrderProcess->getRetryRecordListForId([$id]);
        if(empty($res)){
            @pft_log('order/package', "通知为空:".json_encode([$parentOrderNum, $res,$id], JSON_UNESCAPED_UNICODE));
            return true;
        }
        $this->_otaNotify($parentOrderNum,$isSucceed,$desc);
        $noticeRes = true;
        if($isSucceed){
            //发送套票主票预订短信
            $res =$this->_sendPackageSms($parentOrderNum,$parentOrderSmsInfo);
            if($res === false){
                $noticeRes = false;
            }
        }
        if($noticeRes){
            //成功通知 删除数据
            $packageOrderProcess->deleteData(intval($id),$parentOrderNum,$remark);
        }
        else{
            if(!empty($updateData)){
                //通知返回失败 更新数据
                $packageOrderProcess->updateData(intval($id),$updateData);
            }
        }
        return true;
    }

    /**
     * 下单后校验出票状态，以及下单后队列校验
     * @param $parentOrderNum
     * @param $processId
     * @param $parentOrderSmsInfo
     * @param int $reTryTime
     * @return array
     */
    public function reviewPackOrderStatus($parentOrderNum,$processId,$parentOrderSmsInfo,$reTryTime = 1){
        $isNotice = false;
        $isSucceed = false;
        $updateArr = [];
        $remark = '';
        $judgeRes = self::checkPackOrderStatus($parentOrderNum);
        if(empty($judgeRes)){
            @pft_log('order/package', json_encode([$judgeRes, $parentOrderNum], JSON_UNESCAPED_UNICODE));
            return [$isNotice, $isSucceed, $updateArr,$remark];
        }
        if($judgeRes['is_order_completed'] == self::REVIEW_STATUS_CHECK){
            $remark = "套票中仍有订单处于带出票状态，订单号：{$judgeRes['order_id']}，订单状态：{$judgeRes['order_status']} || {$judgeRes['order_status_msg']}";
        }
        else{
            $isNotice = true;
            if($judgeRes['is_order_completed'] == self::REVIEW_STATUS_FAIL){
                $isSucceed = false;
                $updateArr = ['ticked_state'=>self::REVIEW_STATUS_FAIL];
                $remark = "出票失败发送通知，订单号：{$judgeRes['order_id']}，订单状态：{$judgeRes['order_status']} || {$judgeRes['order_status_msg']}";
            }
            else{
                $isSucceed = true;
                $updateArr = ['ticked_state'=>self::REVIEW_STATUS_SUCCESS];
                $remark = "出票成功发送通知，订单号：{$judgeRes['order_id']}，订单状态：{$judgeRes['order_status']} || {$judgeRes['order_status_msg']}";
            }
        }
        @pft_log('order/package', json_encode([$isNotice, $isSucceed, $updateArr,$remark], JSON_UNESCAPED_UNICODE));

        if($isNotice){
            self::openPaltFormNotice($isSucceed, $processId, $parentOrderNum,$updateArr,$remark,$parentOrderSmsInfo);
        }
        else{
            //前1分钟按照5秒，10秒，15秒，30秒，60秒查询5次，接着每2分钟查询一次,共5次
            if($reTryTime <= 10){
                $delaySecond =  self::RETRY_TIME_MAP[$reTryTime];
//                Queue::push( 'order', 'PackageOrderNotice_Job',
//                    [
//                        'parent_orderNum' => $parentOrderNum,
//                        'process_id' => $processId,
//                        'retry_time' => $reTryTime
//                    ]
//                );
                $time = strtotime("+ {$delaySecond} seconds");
                @pft_log('order/package', json_encode("{$parentOrderNum} 队列重试第{$reTryTime}次,下次执行时间：{$delaySecond}s后,{$time}", JSON_UNESCAPED_UNICODE));
                Queue::delay($time, 'order', 'PackageOrderNotice_Job',
                    [
                        'parent_orderNum' => $parentOrderNum,
                        'process_id' => $processId,
                        'retry_time' => $reTryTime
                    ]
                );

            }
        }
        return [$isNotice, $isSucceed, $updateArr,$remark];
    }

    /**
     * 轮询 - 定时 5分钟一次
     * 根据类型 完成 下游&&短信 通知
     * @param $reviewType
     * @return bool
     */
    public function reviewPackOrderNotice($reviewType){
        $pageLimit = self::PAGE_LIMIT;
        $page = self::PAGE_INIT;
        $packageOrderProcess = new PackageOrderProcess();
        switch ($reviewType){
            case self::REVIEW_TYPE_REVIEW:  //主子未出票 待复核 ->查询订单状态复核
                $orderState = $packageOrderProcess::ORDER_STATUS;
                $tickedState = $packageOrderProcess::UN_TICKET_STATUS;
                break;
            case self::REVIEW_TYPE_FAILED:  //下单失败短信异常 未通知
                $orderState = $packageOrderProcess::UN_ORDER_STATUS;
                $tickedState = $packageOrderProcess::UN_TICKET_STATUS;
                break;
            case self::REVIEW_TYPE_COMPLETED: //主子已出票 未通知
                $orderState = $packageOrderProcess::ORDER_STATUS;
                $tickedState = $packageOrderProcess::TICKET_STATUS;
                break;
            default:
                return false;
        }

        do{
            $list = $packageOrderProcess->getRetryRecordList($tickedState,
                $orderState, $page);
            if(empty($list)){
                break;
            }
            foreach ($list as $info){
                list($isNotice, $isSucceed, $updateArr,$remark) = $this->judgeNotice($reviewType,$info);
                @pft_log('order/package', json_encode(['轮询结果',$reviewType,$info,$isNotice, $isSucceed, $updateArr,$remark], JSON_UNESCAPED_UNICODE));
                if($isNotice){
                    self::openPaltFormNotice($isSucceed, $info['id'], $info['parent_ordernum'],$updateArr,$remark);
                }
            }
            $page++;
        }while($page > $pageLimit);
        return true;
    }

    /**
     * 判断当前记录是否发送短信 及其他数据
     * @param $reviewType
     * @param $processInfo
     * @return array
     */
    private function judgeNotice($reviewType,$processInfo){
        $isNotice = false;
        $isSucceed = false;
        $updateArr = [];
        $remark = '';
        if($reviewType == self::REVIEW_TYPE_REVIEW){
            $judgeRes = self::checkPackOrderStatus($processInfo['parent_ordernum']);
            if(empty($judgeRes)){
                @pft_log('order/package', json_encode([$judgeRes, $processInfo['parent_ordernum']], JSON_UNESCAPED_UNICODE));
                return [$isNotice, $isSucceed, $updateArr,$remark];
            }
            if($judgeRes['is_order_completed'] == self::REVIEW_STATUS_CHECK){
                $remark = "套票中仍有订单处于带出票状态，订单号：{$judgeRes['order_id']}，订单状态：{$judgeRes['order_status']} || {$judgeRes['order_status_msg']}";
            }
            else{
                $isNotice = true;
                if($judgeRes['is_order_completed'] == self::REVIEW_STATUS_FAIL){
                    $isSucceed = false;
                    $updateArr = ['ticked_state'=>self::REVIEW_STATUS_FAIL];
                    $remark = "出票失败发送通知，订单号：{$judgeRes['order_id']}，订单状态：{$judgeRes['order_status']} || {$judgeRes['order_status_msg']}";
                }
                else{
                    $isSucceed = true;
                    $updateArr = ['ticked_state'=>self::REVIEW_STATUS_SUCCESS];
                    $remark = "出票成功发送通知，订单号：{$judgeRes['order_id']}，订单状态：{$judgeRes['order_status']} || {$judgeRes['order_status_msg']}";
                }
            }
        }
        if($reviewType == self::REVIEW_TYPE_FAILED){
            $isSucceed = false;
            $isNotice = true;
            $remark = "下单失败短信异常发送通知，订单号：{$processInfo['parent_ordernum']}}";
        }
        if($reviewType == self::REVIEW_TYPE_COMPLETED){
            $isSucceed = true;
            $isNotice = true;
            $remark = "主子已出票发送通知，订单号：{$processInfo['parent_ordernum']}}";
        }
        return [$isNotice, $isSucceed, $updateArr, $remark];
    }
}
