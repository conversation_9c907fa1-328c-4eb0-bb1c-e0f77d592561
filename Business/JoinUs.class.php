<?php
/**
 * Created by <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>.
 * User: chenguangpeng
 * Date: 2/12-012
 * Time: 11:55
 */

namespace Business;


class JoinUs
{
    private $model;

    public function __construct()
    {
        $this->model = new \Model\Wechat\JoinUs();
    }

    /**
     * 发放微信红包
     *
     * @param  string   $openid   微信openid
     * @param  int      $money    提现金额(分)
     * @return array
     */
    public function sendRedPack($openid, $money) {

        include_once("/var/www/html/Service/Conf/WePay.conf.php");
        $payConfig = array(
            'appid'     => 'wxd72be21f7455640d',
            'mchid'     => '10036546',
            'key'       => 'a8884215d651c6b1ea0ea0ded4a04179',
            'app_secret'=>'fb330082b1f0d8a82049a8c2098276be',
            'sslcert_path' => '/public/certs/wepay/red/apiclient_cert.pem',
            'sslkey_path'  => '/public/certs/wepay/red/apiclient_key.pem',
        );

        $mchid      = $payConfig['mchid'];
        $key        = $payConfig['key'];
        $app_secret = $payConfig['app_secret'];

        define('SSLCERT_PATH',$payConfig['sslcert_path']);
        define('SSLKEY_PATH', $payConfig['sslkey_path']);

        //使用发放红包接口
        $redPack = new \WeChat\Pay2\Sendredpack_pub(PFT_WECHAT_APPID, $mchid, $key, $app_secret);

        $mchBillno = $mchid . date('Ymd').mt_rand(1000000000,9999999999);
        $redPack->setParameter("mch_billno", $mchBillno);//商户订单号
        $redPack->setParameter("nick_name", '新年快乐');//提供方名称
        $redPack->setParameter("send_name", '票付通');//红包发送者名称
        $redPack->setParameter("re_openid", $openid);//接受收红包的用户
        $redPack->setParameter("total_amount", $money);//付款金额，单位分
        $redPack->setParameter("total_num", 1);//红包发放总人数
        $redPack->setParameter("wishing", "票付通祝您新年快乐~");//祝福语
        $redPack->setParameter("client_ip", "*************");//IP
        $redPack->setParameter("act_name", "招聘分享送红包");//活动名称
        $redPack->setParameter("remark", "招聘海报分享送红包");//备注信息
        //调用结果
        $result = $redPack->getResult();
        //发放记录
        pft_log('wechat/job/cash', json_encode($result), 'day');

        if ($result['result_code'] == 'SUCCESS' || $result['err_code'] == 'PROCESSING' || $result['err_code'] == 'SYSTEMERROR') {
            return $result;
        } elseif ($result['result_code'] == 'FAIL') {
            return false;
        } else {
            return false;
        }
    }
}