<?php
/**
 * 卡管理业务逻辑层
 * <AUTHOR> Li
 * @date    2022-10-10
 */

namespace Business\AnnualCard;

use Business\CommodityCenter\Ticket;
use Business\Face\AnnualAliFaceBiz;
use Business\JsonRpcApi\MessageCenter\MessageCenterService;
use Business\NewJavaApi\BaseServer\BaseServer;
use Business\Product\AnnualCardConst;
use Business\Product\AnnualCardPrivilege;
use Business\Product\AnnualCardVerifyBiz;
use Library\Cache\Cache;
use Library\Traits\IdentityConversionTrait;
use Library\Util\AnnualUtil;
use Model\Annual\AliFaceMapModel;
use Model\Order\OrderTourist;
use Model\Product\AnnualCard;
use Model\Product\AnnualCard as AnnualCardModel;
use Business\Product\AnnualCardConst as AnnualCardConstBiz;
use Business\AnnualCard\Package as AnnualPackageBiz;

class CardManage extends AnnualBase
{
    use IdentityConversionTrait;
    //日志目录
    private $_debugLogDir = 'annual_card/manage/';

    public function __construct()
    {

    }

    /**
     * 获取年卡会员列表
     * @param int $sid
     * @param bool $isSelf
     * @param int $page
     * @param int $pageSize
     * @param string $identify
     * @param int $searchType
     * @param int $status
     * @param int $lid
     * @param int $tid
     * @param array $pid
     * @param string $saleStart
     * @param string $saleEnd
     * @param string $activeStart
     * @param string $activeEnd
     * @param array $validStart
     * @param array $validEnd
     * @param int $distributorId
     * @param int $packState 主套餐状态 1未生效、2生效中、3已过期
     * @param bool $isUseCnt
     * @param string $table 归档相关参数
     * @return array
     * <AUTHOR>
     * @date   2022/10/8
     *
     */
    public function queryCardList(int $sid = 0, bool $isSelf = true, int $page = 1, int $pageSize = 10, string $identify = '', int $searchType = -1, int $status = 5, int $lid = 0, int $tid = 0, array $pid = [], string $saleStart = '', string $saleEnd = '', string $activeStart = '', string $activeEnd = '', array $validStart = [], array $validEnd = [], int $distributorId = 0, int $packState = 0, bool $isUseCnt = false, string $table = AnnualCard::ANNUAL_CARD_TABLE)
    {
        $code = 200;
        $msg  = '';
        try {
            //如果是需要通过pid查询的，可以设置isSelf等于false
            !$isSelf && $sid = 0;

            //查询做下限制，如果sid为空，pid不能为空
            if (!$sid && empty($pid)){
                throw new \Exception('无数据', 200);
            }

            $pageSize = $pageSize < 1 ? 10 : $pageSize;
            $page     = $page < 1 ? 1 : $page;
            $options  = [
                'page_size'      => $pageSize,      //每页数量
                'page'           => $page,          //页数
                'status'         => $status,        //年卡状态 0未激活 1正常 2禁用 3仓库中 4挂失 5除了仓库中的所有 6年卡延期中的全部 （未激活、已激活）
                'identify'       => $identify,      //搜索参数
                'sale_start'     => $saleStart,     //售出开始时间
                'sale_end'       => $saleEnd,       //售出结束时间
                'active_start'   => $activeStart,   //激活开始时间
                'active_end'     => $activeEnd,     //激活结束时间
                'distributor_id' => $distributorId, //分销商id
                'search_type'    => $searchType,    //搜索类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
                'valid_start'    => $validStart,    //有效期开始时间范围
                'valid_end'      => $validEnd,      //有效期结束时间范围
                'pid'            => $pid,           //门票pid
                'pack_state'     => $packState,     //主套餐状态
            ];

            //默认返回列表格式
            $data = [
                'list'       => [],             //列表数据
                'page'       => $page,          //当前页
                'page_size'  => $pageSize,      //页数
                'total_page' => 0,              //总页码
                'total'      => 0,              //总数
            ];

            $cardModel = new \Model\Product\AnnualCard(0, 0, $table);

            //获取查询总数
            $total = $cardModel->getCardPageList($sid, $options, true);
            if (empty($total)) {
                throw new \Exception('无数据', 200);
            }

            //获取当前页数据
            $result = $cardModel->getCardPageList($sid, $options);
            $isArchive = $table !== AnnualCard::ANNUAL_CARD_TABLE;
            //处理当前页数据
            $result = $this->handleCardListData($result, $isSelf, $isUseCnt, $isArchive);

            //更新返回数据
            $data['list']       = $result;
            $data['total_page'] = ceil($total / $pageSize);
            $data['total']      = $total;

        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
        }

        return $this->returnData($code, $msg, $data);
    }

    /**
     * 年卡归档日期下拉列表
     * <AUTHOR>
     * @Date 2023/8/29 9:56
     * @return array
     */
    public function getCardArchiveConfig()
    {
        $timeType = load_config('time_type', 'annualSearch');
        if (count($timeType) == 2) {
            $ret = [
                ['key' => 0, 'value' => $timeType[0][0].'至今'],
                ['key' => 1, 'value' => $timeType[1][0].'以前']
            ];
            return $this->returnData(200, '', $ret);
        }
        return $this->returnData(200, '', $timeType);
    }
    /**
     * 处理年卡分页列表数据
     * <AUTHOR>
     * @date   2022/10/8
     *
     * @param  array  $data      年卡分页列表数据集
     * @param  bool   $isSelf    查询的是否是当前账号
     * @param  bool   $isUseCnt    是否加入使用次数字段
     * @param  bool   $isArchive  是否是归档数据
     *
     * @return array
     * @throws
     */
    public function handleCardListData(array $data, bool $isSelf, bool $isUseCnt, bool $isArchive = false): array
    {
        if (empty($data)) {
            return $data;
        }

        $getMemberId = [];

        //当前页产品id
        $getPid = array_column($data, 'pid');

        foreach ($data as $item) {
            if ($item['memberid']) {
                $getMemberId[] = $item['memberid'];
            }
            $getMemberId[] = $item['sid'];
            $getMemberId[] = $item['distributor_id'];
        }

        $annualBase       = new \Business\Product\AnnualCardConst();
        $memberBiz        = new \Business\Member\Member();
        $productBiz       = new \Business\CommodityCenter\Ticket();
        $annualPackageBiz = new \Business\AnnualCard\Package();

        //用户id和产品id去重过滤
        $getMemberId = array_unique(array_filter($getMemberId));
        $getPid      = array_unique(array_filter($getPid));

        //查询用户信息
        $memberMap = $memberBiz->getMemberInfoByMulti($getMemberId, 'id', true);
        //查询门票信息
        $ticketArr = $productBiz->queryTicketInfoByProductIds($getPid);
        if (!$ticketArr) {
            throw new \Exception('年卡产品获取异常', 204);
        }

        //获取门票产品信息
        $ticketMap = [];
        foreach ($ticketArr as $ticketInfo) {
            $ticketMap[$ticketInfo['product']['id']] = [
                'id'     => $ticketInfo['product']['id'],
                'ttitle' => $ticketInfo['ticket']['title'],
                'tid'    => $ticketInfo['ticket']['id'],
                'pid'    => $ticketInfo['ticket']['pid'],
                'title'  => $ticketInfo['land']['title'],
                'lid'    => $ticketInfo['land']['id'],
            ];
        }

        //查询使用次数
        if ($isUseCnt) {
            //虚拟卡号不为空时  获取虚拟卡号对应的人脸数量
            $virtualUseCountMap = [];
            //当前页虚拟卡号
            $virtualNoArr = array_column($data, 'virtual_no');
            if ($virtualNoArr) {
                $virtualUseCountMap = (new \Model\Product\AnnualCard())->batchGetVirtualOrderCount($virtualNoArr);
            }
        }

        $list = [];
        foreach ($data as $tmp) {
            $item = [
                'title'        => $ticketMap[$tmp['pid']]['title'] ?? '',//景区名称
                'ttitle'       => $ticketMap[$tmp['pid']]['ttitle'] ?? '',//门票名称
                's_name'       => '',
                'sale_time'    => '',//售出时间
                'active_time'  => '',//激活时间
                'avalid_begin' => '',//有效期开始
                'avalid_end'   => '',//有效期结束
                'avalid_time'  => '',//有效期范围
                'self'         => (int)$isSelf,
                'account'      => '',//会员名称
                'dname'        => $tmp['dname'] ?? '',//持卡人名称
                'mobile'       => $tmp['mobile'] ?? '',//手机号
                'contact_id'   => $ticketMap[$tmp['pid']]['lid'] ?? 0,//景区id
                'tid'          => $ticketMap[$tmp['pid']]['tid'] ?? 0,//门票id
                'physics_no'   => $tmp['physics_no'] ? hexdec($tmp['physics_no']) : 0,//物理卡
                'virtual_no'   => $tmp['virtual_no'] ?? '',//虚拟卡
                'card_no'      => $tmp['card_no'] ?? '',//实体卡
                'id'           => $tmp['id'] ?? 0,//卡id
                'pack_status'  => '未知',//套餐状态
                'member_id'    => $tmp['memberid'],//会员id
                'id_card_no'   => $tmp['id_card_no'],//身份证
                'sid'          => $tmp['sid'],//供应商id
                'status'       => $tmp['status'],//年卡状态
                'remarks'      => $tmp['remarks'],//年卡状态
                'province'     => $tmp['province'] ?? 0,
                'city'         => $tmp['city'] ?? 0,
                'address'      => $tmp['address'] ?? '',
            ];
            if (isset($memberMap[$tmp['memberid']])) {
                $item['account'] = $tmp['dname'] ?: '';
                $item['mobile']  = $tmp['mobile'] ?: '';
            }
            if (isset($memberMap[$tmp['sid']])) {
                $item['supply'] = $memberMap[$tmp['sid']]['dname'];
            }
            if ($tmp['sale_time']) {
                $item['sale_time'] = date('Y-m-d H:i:s', $tmp['sale_time']);
            }
            if ($tmp['active_time']) {
                $item['active_time'] = date('Y-m-d H:i:s', $tmp['active_time']);
            }
            if ($tmp['avalid_begin'] && $tmp['avalid_end']) {
                $item['avalid_begin'] = date('Y-m-d', $tmp['avalid_begin']);
                $item['avalid_end']   = date('Y-m-d', $tmp['avalid_end']);
                $item['avalid_time']  = $item['avalid_begin'] . ' ~ ' . $item['avalid_end'];
            }
            $extArr = json_decode($tmp['ext_info'] ?? '', true);
            $item['voucher_type'] = $extArr['voucher_type'] ?? 1;
            //未激活，存在x天有效 以及固定有效期
            if (isset($tmp['status']) && in_array($tmp['status'], [AnnualCardConstBiz::STATUS_NOT_ACTIVE, AnnualCardConstBiz::STATUS_AUDIT]) || ($tmp['status'] == AnnualCardConstBiz::STATUS_BAN && empty($tmp['avalid_begin']))) {
                //获取套餐中的有效期
                $packageRes = $annualPackageBiz->getPeriodPackageByCardAndSid($tmp['id'], $tmp['sid']);
                if ($packageRes['code'] == 200 && !empty($packageRes['data']) && $packageRes['data'][0]['start_time'] && $packageRes['data'][0]['end_time']) {
                    $packageInfo = $packageRes['data'][0] ?? [];
                    //时段套餐时间
                    $item['avalid_begin'] = date('Y-m-d', $packageInfo['start_time']);
                    $item['avalid_end']   = date('Y-m-d', $packageInfo['end_time']);
                } else {
                    //有效期会不会重新赋值卡表
                    $annualValidType = $extArr['annual_valid_type'] ?? 0;
                    if ($annualValidType == AnnualCardConstBiz::ANNUAL_VALID_TYPE_DAY) {
                        //x天有效
                        $validDay                 = $extArr['annual_valid_day'] ?? 0;
                        $item['annual_valid_day'] = "激活后 $validDay 天有效";
                        //固定有效期置空
                        $item['annual_valid_start'] = '';
                        $item['annual_valid_end']   = '';
                        $item['pack_status']        = '未生效';
                    }

                    if ($annualValidType == AnnualCardConstBiz::ANNUAL_VALID_TYPE_DATE) {
                        //x天有效置空
                        $item['annual_valid_day'] = '';
                        //固定有效期
                        $item['annual_valid_start'] = empty($extArr['annual_valid_start']) ? '' : (date("Y-m-d", strtotime($extArr['annual_valid_start'])));
                        $item['annual_valid_end']   = empty($extArr['annual_valid_end']) ? '' : (date("Y-m-d", strtotime($extArr['annual_valid_end'])));
                        //卡有效期更新
                        $item['avalid_begin']       = $item['annual_valid_start'];
                        $item['avalid_end']         = $item['annual_valid_end'];
                        $item['avalid_time']        = $item['avalid_begin'] . ' ~ ' . $item['avalid_end'];
                    }
                }
            }

            if (!empty($item['avalid_begin']) && !empty($item['avalid_end'])) {
                $current     = date('Ymd');
                $avalidBegin = date('Ymd', strtotime($item['avalid_begin']));
                $avalidEnd   = date('Ymd', strtotime($item['avalid_end']));
                $packStatus  = '未知';
                //生效中
                $current >= $avalidBegin && $current <= $avalidEnd && $packStatus = '生效中';
                //已过期
                $current > $avalidBegin && $current > $avalidEnd && $packStatus = '已过期';
                //未生效
                $current < $avalidBegin && $current < $avalidEnd && $packStatus = '未生效';

                $item['pack_status'] = $packStatus;
            }
            //撤销的年卡 有效期时间为--
            if (isset($tmp['status']) && $tmp['status'] == AnnualCardConst::STATUS_REVOKE) {
                $item['avalid_begin'] = '--';
                $item['avalid_end_real'] = $item['avalid_end'];
                $item['avalid_end']   = '--';
                $item['avalid_time']  = '--';
            }
            //获取一次年卡状态
            $annualHand          = $annualBase->annualCardStatusHandle($tmp, $isArchive);
            $item['status_name'] = $annualHand['status_name'];
            $item['button']      = $annualHand['button'];

            //是否加入使用次数
            $isUseCnt && $item['use_cnt'] = $virtualUseCountMap[$item['virtual_no']] ?? 0;

            $list[] = $item;
        }

        return $list;
    }

    /**
     * 分销年卡续费
     * 年卡状态为撤销的数据不渲染
     * <AUTHOR>
     * @Date 2023/8/9 10:18
     * @param array $data
     * @return array
     */
    public function cleanRevokeCard(array $data): array
    {
        $ret = [];
        foreach ($data['list'] as $item) {
            if ($item['status'] == AnnualCardConst::STATUS_REVOKE) {
                continue;
            }
            $ret[] = $item;
        }
        $data['list'] = $ret;
        return $data;
    }

    /**
     * 年卡详情
     * <AUTHOR>
     * @date   2022/10/12
     *
     * @param  int     $sid          商户id
     * @param  int     $cardId       卡id
     * @param  int     $memberId     会员id
     * @param  string  $virtualNo    虚拟卡号
     *
     * @return array
     */
    public function getCardDetail(int $sid, int $memberId, string $virtualNo, int $cardId, string $table = AnnualCard::ANNUAL_CARD_TABLE)
    {
        $code = 200;
        $msg  = '';
        $data = [];
        try {
            if (!$virtualNo) {
                throw new \Exception('virtual_no参数为空', 203);
            }
            //验证卡是否可以访问
            //年卡转让后memberid会表 由于前端使用地址栏的memberid 因此这里不使用memberid的查询详情
            $cardInfo = $this->getCardInfoByCardIdAndVirtualNo($cardId, $virtualNo, 0, $table);
            if (empty($cardInfo)) {
                return $this->returnData(400, '不存在卡信息');
            }
            $cardModel = new AnnualCardModel(0, 0, $table);
            $annualBase          = new AnnualCardConstBiz();
            $faceBiz             = new \Business\Face\FaceBase();
            $javaApi             = new \Business\CommodityCenter\LandF();
            $annualCardVerifyBiz = new AnnualCardVerifyBiz();
            $annualAliFaceIdMapModel = new AliFaceMapModel();
            //年卡状态处理
            $annualHand = $annualBase->annualCardStatusHandle($cardInfo);
            //年卡获取使用次数
            $virtualUseCountMap = $cardModel->getVirtualOrderCount($virtualNo);
            //是否归档(特权存在整体归档和部分归档 要结合card_id查询展示)
            $isArchive = $table !== AnnualCard::ANNUAL_CARD_TABLE;
            //获取年卡权益
            $ticketAndPrivilege = $this->getTicketAndPrivileges($cardInfo['sid'], $cardInfo['pid'], $cardInfo['id'], $isArchive);
            $tInfo = $ticketAndPrivilege['tInfo'];
            $isAliFacePlatform = AnnualAliFaceBiz::getInstance()->callIsAliFacePlatform($sid, $cardInfo['pid']);
            //获取卡信息
            $info = [
                //卡id
                'id'          => $cardInfo['id'],
                'member_id'   => $cardInfo['memberid'],
                //年卡产品名称
                'title'       => $tInfo[$cardInfo['pid']]['pname'] ?? '',
                //虚拟卡
                'virtual_no'  => $cardInfo['virtual_no'],
                //实体卡
                'card_no'     => $cardInfo['card_no'],
                //物理ID
                'physics_no'  => $cardInfo['physics_no'] ? hexdec($cardInfo['physics_no']) : 0,//物理卡,
                //激活时间
                'active_time' => empty($cardInfo['active_time']) ? '' : date("Y-m-d H:i:s", $cardInfo['active_time']),
                //售出时间
                'sale_time'   => date("Y-m-d H:i:s", $cardInfo['sale_time']),
                //年卡状态
                'status'      => $cardInfo['status'],
                //年卡状态名称
                'status_name' => $annualHand['status_name'] ?? '',
                //年卡使用次数
                'use_count'   => $virtualUseCountMap[$virtualNo] ?? 0,
                //是否归档 false否 true是
                'is_archive' => $isArchive,
                //支付宝人脸id数组
                'face_id_info' => $isAliFacePlatform ? $annualAliFaceIdMapModel->getValidFaceIdListByVirtualNo($virtualNo) : [],
            ];
            //年卡特权
            $privilege = $ticketAndPrivilege['privilege'];
            $extInfo   = json_decode($cardInfo['ext_info'], true);
            //持卡人信息
            $cardholder = [
                //会员名称
                'dname'        => $cardInfo['dname'] ?: '',
                //手机号
                'mobile'       => $cardInfo['mobile'] ?: '',
                //身份证
                'id_card_no'   => $cardInfo['id_card_no'] ?: '',
                //所在省
                'province'     => $cardInfo['province'] ?: 0,
                //所在市
                'city'         => $cardInfo['city'] ?: 0,
                //详细地址
                'address'      => $cardInfo['address'] ?: '',
                //头像
                'headphoto'    => '',
                //其他信息
                'other_les'    => '',
                'voucher_type' => $extInfo['voucher_type'] ?? 1,
            ];
            //持卡人备注信息
            $cardholder['remarks'] = $cardInfo['remarks'] ?: '';
            //持卡人人脸信息处理
            $field     = 'family_card_num,tid';
            $ticketArr = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [], [$cardInfo['pid']], $field);
            $extInfo   = $ticketArr[0];
            //家庭人脸数
            $cardholder['family_card_num'] = (int)$extInfo['family_card_num'];
            //人脸照片
            //$tmpHeadphoto         = $faceBiz->faceUrlConversion($cardholder['headphoto']);
            $cardholder['avatar'] = [];

            //获取人脸照片组
            $group = $cardModel->getGroupPhotos($virtualNo);
            if ($group) {
                $photos   = array_filter(json_decode($group['photos'], true));
                $photoArr = [];
                foreach ($photos as $p) {
                    $photoArr[] = $faceBiz->faceUrlConversion($p);
                }
                $cardholder['avatar'] = $photoArr;
            }
            unset($cardholder['headphoto']);
            //实名制判断
            $checkResult                 = $annualCardVerifyBiz->checkAnnualRealNameAndPhoto($extInfo['tid'], 1, 2);
            $cardholder['need_ID']       = intval($checkResult['data']['annual_identity_info'] ?? 0);
            $cardholder['is_need_wirte'] = intval($checkResult['data']['is_need_wirte'] ?? 0);

            //购卡人信息
            $cardPurchaser = [
                'dname'      => '',
                'mobile'     => '',
                'id_card_no' => '',
                'ordernum'   => '',
            ];
            //查询那个一级分销商售出
            $orderNum = $cardModel->getCardPurchaserOrderNum($virtualNo);
            if (!empty($orderNum)) {
                //获取下取票人信息
                $orderTouristMdl               = new OrderTourist();
                $touristList                   = $orderTouristMdl->getTouristInfoByOrderId($orderNum);
                $cardPurchaser['ordernum']     = $orderNum;
                $orderInfo                     = (new \Model\Order\OrderTools())->getOrderInfo($orderNum);
                $cardPurchaser['dname']        = $orderInfo['ordername'] ?? '';
                $cardPurchaser['mobile']       = $orderInfo['ordertel'] ?? '';
                $cardPurchaser['id_card_no']   = $orderInfo['personid'] ?? '';
                $cardPurchaser['voucher_type'] = $touristList[0]['voucher_type'] ?? 1;
            }
            $aliFaceMapModel = new AliFaceMapModel();
            $aliFaceInfo = $aliFaceMapModel->getListByVirtualNo($cardInfo['virtual_no'], 'face_id,id_card_no');
            $aliFaceData = [];
            foreach ($aliFaceInfo as $value) {
                if (!empty($value['id_card_no'])) {
                    $aliFaceData[$value['id_card_no']] = $value['face_id'];
                    continue;
                }
                $column            = explode('_', $value['face_id']);
                $virtualInfo       = explode(':', $column[1]);
                $idx               = $virtualInfo[1];
                $aliFaceData[$idx] = $value['face_id'];
            }
            $annualCardManageBiz       = new AnnualCardManage();
            $affiliatesPerson          = $annualCardManageBiz->getAffiliatesPerson($cardInfo['id']);
            $affiliatesPerson          = $affiliatesPerson['data'];
            foreach ($affiliatesPerson as &$value) {
                $value['face_id'] = $aliFaceData[$value['idx']] ?? $aliFaceData[$value['id_card']];
            }
            unset($value);
            $info['face_id'] = $aliFaceData[0] ?? $aliFaceData[$cardholder['id_card_no']];
            //组合数据
            $data = [
                //年卡信息
                'info'              => $info,
                //年卡权益
                'privilege'         => $privilege,
                //持卡人
                'cardholder'        => $cardholder,
                //购卡人
                'card_purchaser'    => $cardPurchaser,
                'affiliates_person' => $affiliatesPerson,
            ];

        } catch (\Exception $e) {
            $code = $e->getCode();
            if ($code != 200) {
                $log = [
                    'mdg'     => $e->getMessage(),
                    'line'    => $e->getLine(),
                    'file'    => $e->getFile(),
                    'request' => func_get_args(),
                ];

                pft_log($this->_debugLogDir . 'error', json_encode($log, JSON_UNESCAPED_UNICODE));
            }
        }

        return $this->returnData($code, $msg, $data);
    }

    public function getArchivePackageAndPrivilege(int $cardId)
    {
        $cardModel = new \Model\Product\AnnualCard(0, 0, AnnualCard::ANNUAL_CARD_TABLE);
        $cardInfo = $cardModel->getAnnualCard($cardId);
        if (!$cardInfo) {
            return $this->returnData(203, '不存在卡信息，请确认是否归档');
        }
        $ret          = $this->getTicketAndPrivileges($cardInfo['sid'], $cardInfo['pid'], $cardId);
        $privilegeBiz = new Privilege();
        $returnData   = $privilegeBiz->privilegeHandler($ret['privilege']);


        return $this->returnData(200, '', $returnData);
    }

    public function getTicketAndPrivileges($sid, $pid, $cardId, $isArchive = true, $withPrivileges = true): array
    {
        $commodityTicketBiz  = new \Business\CommodityCenter\Ticket();
        $asField = [
            'uuJqTicketDTO' => [
                'title' => 'ticketName',
            ],
            'uuLandDTO' => [
                'title' => 'landName',
            ]
        ];
        $javaData = $commodityTicketBiz->getTicketAndLandInfoByArrPid([$pid],'pid','id', $asField);
        $tInfo = [];
        foreach ($javaData as $value){
            $tInfo[$value['pid']]['pname'] = $value['landName'] .'-'.$value['ticketName']; //产品名称
            $tInfo[$value['pid']]['lname'] = $value['landName']; //景区名称
            $tInfo[$value['pid']]['tname'] = $value['ticketName']; //套餐名称
        }
        $privilegeRes = [];
        if ($withPrivileges) {
            $annualPackageBiz    = new AnnualPackageBiz();
            //获取年卡权益
            $privilegeRes = $annualPackageBiz->getPeriodPackagePrivilegeInfo($cardId, $sid, '', [1, 2, 3], $isArchive);
        }
        //年卡特权
        return [
            'tInfo' => $tInfo,
            'privilege' => [
                'package_title'     => $tInfo[$pid]['tname'] ?? '',
                'package_privilege' => $privilegeRes['data'] ?? [],
            ]
        ];
    }
    /**
     * 通过年卡门票产品分页获取年卡特权
     * <AUTHOR>
     * @date   2022/10/13
     *
     * @param  int     $sid
     * @param  int     $memberid
     * @param  string  $virtualNo
     * @param  int  $cardId
     * @param  array   $orderTime
     * @param  int     $page
     * @param  int     $size
     *
     * @return array
     */
    public function getHistoryOrderList(int $sid, int $memberid = 0, string $virtualNo = '', int $cardId = 0, array $orderTime = [], int $page = 1, int $size = 15)
    {
        if (!$sid || !$virtualNo) {
            return $this->returnData(203, '参数异常', []);
        }

        //验证卡是否可以访问
        /*$checkRes = $this->getCardInfoByCardIdAndVirtualNo($cardId, $virtualNo);
        if (empty($checkRes)) {
            return $this->returnData(400, '不存在卡信息');
        }*/

        $paramsData = [
            'sid'        => 1,
            'memberid'   => $memberid,
            'virtual_no' => $virtualNo,
            'page'       => $page,
            'size'       => $size,
            'orderTime'  => $orderTime,
        ];

        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__GET_HISTORY_ORDER_LIST__, $paramsData, self::__CARD_CONSUME_SERVICE__);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        $list      = $result['data']['list'] ?? [];
        $tidArr    = array_column($list, 'tid');
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,title', '', 'title');
        $ticketMap = [];
        //获取门票产品信息
        foreach ($ticketArr as $ticketInfo) {
            $ticketMap[$ticketInfo['ticket']['id']] = [
                'ttitle' => $ticketInfo['ticket']['title'],
                'title'  => $ticketInfo['land']['title'],
            ];
        }
        foreach ($list as $key => $tmp) {
            $list[$key]['title']  = $ticketMap[$tmp['tid']]['title'] ?? '';//景区名称
            $list[$key]['ttitle'] = $ticketMap[$tmp['tid']]['ttitle'] ?? '';//门票名称
        }

        $result['data']['list'] = $list;
        
        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 选择优先使用的年卡
     * @param int $sid 供应商id
     * @param array $cards 年卡列表
     * @param int $pid 特权门票pid
     * @param bool $mulit 查找多张年卡
     * @param int $tnum 年卡购票张数（手持机可以购买多张）
     *
     * @return array
     * @throws \Exception
     * <AUTHOR>  Li
     * @date   2022-10-14
     *
     */
    public function selectPriorityCard($sid, $cards, $pid = 0, $mulit = false, $tnum = 1, $virtualNoToIdx = [])
    {
        if (!$sid || !$cards) {
            return $this->returnData(204, '缺少必要参数');
        }
        $cardIdArr = array_column($cards, 'id');
        if (!empty($virtualNoToIdx)) {
            $annualCardManage = new AnnualCardManage();
            $affiliatesMap    = $annualCardManage->batchGetAffiliatesPerson($cardIdArr);
            $affiliatesData   = [];
            foreach ($affiliatesMap['data'] as $value) {
                $affiliatesData[$value['card_id']][] = $value['idx'];
            }
            foreach ($cards as $key => $value) {
                if (!empty($virtualNoToIdx[$value['virtual_no']]) && (!in_array($virtualNoToIdx[$value['virtual_no']], $affiliatesData[$value['id']]) || empty($affiliatesData[$value['id']]))) {
                    unset($cards[$key]);
                }
            }
            $cards = array_values($cards);
        }
        //过滤掉未激活、禁用、仓库、挂失状态的card_id
        $cardIdArr = [];
        $overDateCardArr = [];//过期年卡
        foreach ($cards as $card) {
            if ($this->annualCardBaseFilter($card)) {
                $cardIdArr[] = $card['id'];
            } elseif (isset($card['avalid_end']) && $card['avalid_end'] < time()) {
                $overDateCardArr[] = $card['virtual_no'];
            }
        }
        if (empty($cardIdArr)) {
            $msg = '没有匹配到可用的年卡-01';
            if (!empty($overDateCardArr)) {
                $msg = '年卡已过期';
            }
            unset($overDateCardArr);
            return $this->returnData(204, $msg);
        }
        //获取查到的年卡中 卡套餐属于有效的、有效期结束时间正序返回
        $packageBiz = new \Business\AnnualCard\Package();

		$pendingPeriodResult = $packageBiz->getDelayEnablePeriodPackageInfo($sid, $cardIdArr);
		$pending = [];
		if($pendingPeriodResult['code'] == 200){
			$pending = $pendingPeriodResult['data'];
		}

        $packageRes = $packageBiz->getValidPeriodPackageInfo($sid, $cardIdArr);
		AnnualUtil::debug([
			'tag' => 'selectPriorityCard',
			'args' => func_get_args(),
            '$cardIdArr' => $cardIdArr,
			'$packageRes' => $packageRes,
		]);

        if ($packageRes['code'] != 200 || empty($packageRes['data'])) {
			if(count($pending)) {
				foreach($pending as $_){
					if((int)$_['delay_enable'] && $_['state'] == 1) {
						//$hour = ceil(((int)$_['start_time'] - time()) / 3600);
						$enableStartTime = date('Y-m-d H:i', (int)$_['start_time']);
						// return $this->returnData(204, "该年卡未到生效时间，{$hour}小时后生效");
						return $this->returnData(204, "该年卡未到生效时间，{$enableStartTime}后生效");
					}
				}
			}
            return $this->returnData(204, '没有匹配到可用的年卡11');
        }

        //取到卡对应的套餐
        $packageMap = array_column($packageRes['data'], null, 'card_id');
        //处于生效中的卡ID
        $validCardIdArr    = array_column($packageRes['data'], 'card_id');
        //处于生效中的套餐ID
        $validPackageIdArr = array_column($packageRes['data'], 'id');

        $cardMap = array_column($cards, null, 'id');

        $card = [];
        //有指定特权门票的 需要校验一次套餐是否包含该特权
        $errMsgArr = [];
        if ($pid) {
            $tid = (new \Model\Product\Ticket())->getTid($pid);
            if (!$tid) {
                return $this->returnData(204, '特权门票不存在');
            }
            $javaApi    = new \Business\CommodityCenter\Ticket();
            $ticketInfo  = $javaApi->queryTicketInfoById($tid);
            if (empty($ticketInfo)) {
                return $this->returnData(203, '门票信息获取失败01');
            }
            //通过卡套餐范围 获取包含指定特权的套餐
            $privilegeRes = $packageBiz->getContainPackagePrivilegeCarId($validCardIdArr, $validPackageIdArr, $tid, true);
            if ($privilegeRes['code'] != 200 || empty($privilegeRes['data'])) {
                return $this->returnData(204, '没有匹配到包含该特权门票的年卡');
            }
            AnnualUtil::debug([
                'tag' => 'selectPriorityCard',
                '$cardMap' => $cardMap,
                '$privilegeRes' => $privilegeRes,
            ]);
            //通过获取到指定特权套餐 得到有效期结束时间 最临近的的一个卡套餐
            //如果用户有多张年卡 匹配出的年卡 既要考虑优先使用临期的 又要考虑是否在年卡可用时间段
            //还要考虑特权使用次数限制规则
            $privilegeCardIdArr = array_column($privilegeRes['data'], 'card_id');
            $privilegeRes       = array_column($privilegeRes['data'], null, 'card_id');
            foreach ($cardMap as $cardId => $item) {
                if (!in_array($cardId, $privilegeCardIdArr)) {
                    continue;
                }
                //先这样改下，value为年卡特权信息
                $value = $privilegeRes[$cardId];
                //购买张数不满足使用次数限制的也过滤掉
                //有可能给用户造成困惑的是有可使用的某个门票特权的年卡 但匹配不到
                //此时需要看下是否有使用次数与买票数量上的冲突
                $useTimesCheck = $this->privilegeCanUseTimesLimitCheck($value, $item, $tnum, $ticketInfo);
                if (!$useTimesCheck[0]) {
                    $errMsgArr[] = '虚拟卡号：' . $item['virtual_no'] . $useTimesCheck[1];
                    continue;
                }
                if (!empty($value['holiday_limit'])) {
                    $checkHolidayRes = $this->checkHolidayLimit($value['holiday_limit'], date('Y-m-d'));
                    if ($checkHolidayRes['code'] != 200) {
                        $errMsgArr[] = '虚拟卡号：' . $item['virtual_no'] . $checkHolidayRes['msg'];
                        continue;
                    }
                }
                //没有跳过的走到这里然后break 算是匹配到符合的年卡
                $card          = $item;
                $annualExtInfo = json_decode($item['ext_info'], true);
                //扩展属性取套餐中的
                $card['ext_info']          = $packageMap[$cardId]['ext_info'];
                $card['package_id']        = $packageMap[$cardId]['id'];
                $card['voucher_type_name'] = $this->conversionIdentityToName($annualExtInfo['voucher_type']);
                break;
            }
        } else {
            //获取有效期结束时间临近的卡
            //不指定特权门票的场景应该不存在
            //如果用户有多张年卡 匹配出的年卡 既要考虑优先使用临期的 又要考虑是否在年卡可用时间段
            $card          = $cardMap[$packageRes['data'][0]['card_id']];
            $annualExtInfo = json_decode($card['ext_info'], true);
            //扩展属性取套餐中的
            $card['ext_info']          = $packageMap[$packageRes['data'][0]['card_id']]['ext_info'];
            $card['package_id']        = $packageMap[$packageRes['data'][0]['card_id']]['id'];
            $card['voucher_type_name'] = $this->conversionIdentityToName($annualExtInfo['voucher_type']);
        }

        //判断时段套餐有效期
        if ($packageMap[$packageRes['data'][0]['card_id']]['start_time'] > time() || $packageMap[$packageRes['data'][0]['card_id']]['end_time'] < time()) {
            return $this->returnData(204, '没有匹配到可用的年卡,套餐有效期时段不满足');
        }

        if (!$card) {
            AnnualUtil::debug([
                'tag' => 'selectPriorityCard',
                '$errMsgArr' => $errMsgArr,
            ]);
            $msg = empty($errMsgArr) ? '没有匹配到可用的年卡' : '没有匹配到可用的年卡:' . $errMsgArr[0];
            return $this->returnData(204, $msg);
        }

        if ($mulit) {
            $virtualNoList           = array_column($cards, 'virtual_no');
            $card['virtual_no_list'] = $virtualNoList;
        }
        AnnualUtil::debug([
            'tag' => 'selectPriorityCard',
            '返回成功' => $card,
            '$errMsgArr' => $errMsgArr,
        ]);

        return $this->returnData(200, '', $card);
    }

    /**
     * 年卡有效期和状态校验
     * <AUTHOR>
     * @Date 2023/7/24 17:09
     * @param array $card
     * @return bool
     */
    private function annualCardBaseFilter(array $card)
    {
        $statusCheck = isset($card['status']) && $card['status'] == 1;
        $validTimeCheck = isset($card['avalid_end']) && $card['avalid_end'] >= time();
        return $statusCheck && $validTimeCheck;
    }

    /**
     * 检测年卡特权次数
     * 新增年卡可用时间校验
     * @param int $sid 供应商id
     * @param int $cardId 年卡id
     * @param int $packageId 套餐id
     * @param int $tid 特权门票id
     * @param bool $srcZj 请求来源是否是闸机
     * @param int $tnum 手持机年卡购票一个订单号可以多张票
     *
     * @return array
     * @throws \Exception
     * <AUTHOR>  Li
     * @date   2022-10-17
     */
    public function privilegeCheck($sid, $cardId, $packageId, $tid, $srcZj = false, $tnum = 1): array
    {
        if (!$cardId || !$sid || !$packageId || !$tid) {
            return $this->returnData(203, '参数错误');
        }
        $javaApi    = new \Business\CommodityCenter\Ticket();
        $ticketInfo  = $javaApi->queryTicketInfoById($tid);
        if (empty($ticketInfo)) {
            return $this->returnData(203, '门票信息获取失败');
        }
        return $this->useTimesLimitCheckLogic($cardId, $sid, $packageId, $tid, $ticketInfo, $srcZj, $tnum);
    }
    /**
     * 年卡特权可用时间段校验
     * <AUTHOR>
     * @Date 2023/7/12 20:34
     * @param string $timeLimitRule {"type":1,"begin_time":"09:00","end_time":"23:00"}
     * @return bool true-通过校验 false-未通过校验
     */
    public function privilegeTimeLimitCheck(string $timeLimitRule): bool
    {
        $format = 'H:i';
        $useTimeLimit = json_decode($timeLimitRule, true);
        //1-需要校验可用时间 0-不需要校验
        if ($useTimeLimit['type'] != 1) {
            return true;
        }
        $now = date_create_from_format($format, date('H:i'));
        $begin = date_create_from_format($format, $useTimeLimit['begin_time']);
        $end = date_create_from_format($format, $useTimeLimit['end_time']);
        if ($now < $begin || $now > $end) {
            return false;
        }
        return true;
    }

    /**
     * 特权次数限制
     * 1.手持机用年卡可以购买多张票
     * 2.组合特权共享使用次数
     * <AUTHOR>
     * @Date 2023/11/9 9:32
     * @param array $privilege 特权表一维数组
     * @param array $card 年卡表一维数组
     * @param int $tnum 购票数
     * @param array $ticketInfo 票信息
     * @return array
     */
    private function privilegeCanUseTimesLimitCheck(array $privilege, array $card, int $tnum, array $ticketInfo): array
    {
        $res = $this->useTimesLimitCheckLogic($card['id'], $card['sid'], $privilege['package_id'], $privilege['tid'], $ticketInfo, false, $tnum);
        if ($res['code'] != 200) {
            return [false, $res['msg']];
        }
        return [true, ''];
    }

    /**
     * 这个是和privilegeCheck公用的检测逻辑
     * <AUTHOR>
     * @Date 2023/11/9 9:48
     * @param $cardId
     * @param $sid
     * @param $packageId
     * @param $tid
     * @param $ticketInfo
     * @param bool $srcZj
     * @param int $tnum
     * @return array
     */
    public function useTimesLimitCheckLogic($cardId, $sid, $packageId, $tid, $ticketInfo, $srcZj = false, $tnum = 1): array
    {
        //这里先不传tid过滤 拿到所有套餐下特权后再过滤
        $privilegeRes = (new \Business\AnnualCard\Package())->getPackagePrivilegeByCardIdAndAid($cardId, $sid, $packageId);
        if ($privilegeRes['code'] != 200 || empty($privilegeRes['data'])) {
            return $this->returnData(204, '年卡特权异常或特权无法使用');
        }
        $tmpDay   = date('Ymd');
        $tmpMonth = date('Ym');
        //按group_id分组 相同组的次数共享
        $grouped = [];
        $groupId = 0;
        foreach ($privilegeRes['data'] as $privilege) {
            //获取到所验的票(tid)的group_id
            if ($tid == $privilege['tid']) {
                $groupId = $privilege['group_id'];
                break;
            }
        }
        foreach ($privilegeRes['data'] as $privilege) {
            if ($privilege['group_id'] == $groupId) {
                $grouped[] = $privilege;
            }
        }
        //由于传递的有tid 因此需要查询出group_id 反查出所有同个group_id的特权 这些共享使用次数 如果设置组合特权的话
        //只能使用group_id不能用package_id
        //此时的$grouped就是共享同个次数的组
        //手持机可以购买多张票 所以校验使用次数要加上增量
        $useTimeLimit = empty($grouped[0]['use_time_limit']) ? '' : $grouped[0]['use_time_limit'];
        if (!empty($useTimeLimit) && !$this->privilegeTimeLimitCheck($useTimeLimit)) {
            //闸机核验 返回的文案要带上可用时间段
            $msg = '该年卡特权不在使用时间';
            if ($srcZj) {
                $useTimeLimitArr = json_decode($useTimeLimit, true);
                $msg = "该年卡特权不在使用时间\n" . '(' . $useTimeLimitArr['begin_time'] . '-' . $useTimeLimitArr['end_time'].'可用)';
            }
            return $this->returnData(204, $msg);
        }
        $useLimitData = json_decode($grouped[0]['use_limit_json'], true);
        $useAll = $useMonth = $useDay = 0;
        foreach ($grouped as $privilege) {
            $day   = $useLimitData['day'] != -1 ? $useLimitData['day'] : -1;
            $month = $useLimitData['month'] != -1 ? $useLimitData['month'] : -1;
            $all   = $useLimitData['all'] != -1 ? $useLimitData['all'] : -1;
            //都设置成不限的  直接跳出
            if ($day == -1 && $month == -1 && $all == -1) {
                continue;
            }
            $useRecord    = json_decode($privilege['use_recode_json'], true);
            //有设置限制次数的  检测下对应数量是否达到配置上限
            //先判断总次数
            if ($all != -1 && isset($useRecord['all'])) {
                $useAll += $useRecord['all'];
            }
            //再判断月次数
            if ($month != -1 && isset($useRecord['month'][$tmpMonth])) {
                $useMonth += $useRecord['month'][$tmpMonth];
            }
            //最后判断日次数
            if ($day != -1 && isset($useRecord['day'][$tmpDay])) {
                $useDay += $useRecord['day'][$tmpDay];
            }
        }
        $add = $tnum;
        $willUseAll = $useAll + $add;
        $txt = $tnum > 1 ? $ticketInfo['ticket']['title'] . '购买' . $tnum . '张票后特权次数将不足' : $ticketInfo['ticket']['title'] . '特权次数不足';
        if ($useLimitData['all'] != -1 && $willUseAll > $useLimitData['all']) {
            return $this->returnData(204, $txt);
        }
        $willUseMonth = $useMonth + $add;
        if ($useLimitData['month'] != -1 && $willUseMonth > $useLimitData['month']) {
            return $this->returnData(204, $txt);
        }
        $willUseDay = $useDay + $add;
        if ($useLimitData['day'] != -1 && $willUseDay > $useLimitData['day']) {
            return $this->returnData(204, $txt);
        }
        //返回的时候按tid过滤下
        $privilegeData = array_filter($privilegeRes['data'], function ($item) use ($tid) {
            return $item['tid'] == $tid;
        });
        $privilegeData = array_values($privilegeData);
        return $this->returnData(200, '特权检测成功', $privilegeData);
    }
    /**
     * 特权消费的订单取消
     * <AUTHOR>  Li
     * @date   2022-10-20
     *
     * @param  string $orderNum  订单号
     * @param  int $num  数量
     *
     * @return array
     */
    public function cancelPrivilegeOrder(string $orderNum, int $num = 0)
    {
        if (!$orderNum) {
            return $this->returnData(203, '参数错误');
        }

        $paramsData = [
            'ordernum' => $orderNum,
            'num'      => $num,
        ];

        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__CANCEL_PRIVILEGE_ORDER__, $paramsData, self::__CARD_CONSUME_SERVICE__);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 根据卡id和虚拟卡号获取卡信息
     * <AUTHOR>
     * @date   2022/11/8
     *
     * @param  int     $cardId       卡id
     * @param  string  $virtualNo    虚拟卡号
     * @param  int     $memberId     会员id
     *
     * @return array|bool
     */
    public function getCardInfoByCardIdAndVirtualNo(int $cardId, string $virtualNo, int $memberId = 0, string $table = AnnualCard::ANNUAL_CARD_TABLE)
    {
        if (!$cardId || (empty($virtualNo) && !$memberId)) {
            return [];
        }
        $map = [];
        if ($virtualNo) {
            $map['virtual_no'] = $virtualNo;
        } else {
            $map['memberid'] = $memberId;
        }
        $field = 'id,sid,pid,memberid,card_no,physics_no,virtual_no,status,avalid_end,active_time,sale_time,dname,mobile,id_card_no,province,city,address,remarks,ext_info';
        $annualCardModel = new \Model\Product\AnnualCard(0, 0, $table);
        $checkRes        = $annualCardModel->checkCard('id', $cardId, $map, $field);
        if (!$checkRes) {
            return [];
        }
        return $checkRes;
    }

    /**
     * 获取年卡审核列表
     *
     * @param int $sid 商家id
     * @param string $identify 查询关键字
     * @param int $searchType 查询类型
     * @param string $applyStart 申请开始时间
     * @param string $applyEnd 申请结束时间
     * @param string $auditStart 审核开始时间
     * @param string $auditEnd 审核开始时间
     * @param int $searchTid 查询票id
     * @param int $status 审核状态 -1 全部 0未审核  1：审核通过 2：审核不通过
     * @param int $excel 是否导出excel 0：否 1：是
     * @param int $page
     * @param int $size
     * @date 2023/04/11
     * @auther yangjianhui
     * @return array
     */
    public function getAnnualActiveAuditList($sid, $identify, $searchType, $applyStart, $applyEnd, $auditStart, $auditEnd, $searchTid,
        $status, $excel, $page = 1, $size = 10)
    {
        if (empty($sid)) {
            return $this->returnData(203, "商家信息有误");
        }
        $paramsData = compact('sid', 'identify', 'searchType', 'applyStart', 'applyEnd', 'auditStart', 'auditEnd',
            'searchTid', 'status', 'excel', 'page', 'size');
        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__GET_ANNUAL_ACTIVE_AUDIT_LIST, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
        if ($result['code'] != 200 || empty($result['data'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $asField            = [
            'uuJqTicketDTO' => [
                'title' => 'ticketName',
            ],
            'uuLandDTO'     => [
                'title' => 'landName',
            ],
        ];
        $tidArr = array_column($result['data']['list'], 'tid');
        $javaData           = $commodityTicketBiz->getTicketAndLandInfoByArrTid($tidArr, 'id,title', 'title', $asField);
        $tInfo              = [];
        foreach ($javaData as $value) {
            $tInfo[$value['id']]['pname'] = $value['landName'] .'-'.$value['ticketName']; //产品名称
            $tInfo[$value['id']]['lname'] = $value['landName']; //景区名称
            $tInfo[$value['id']]['tname'] = $value['ticketName']; //套餐名称
        }
        foreach ($result['data']['list'] as &$value) {
            $value['title']         = $tInfo[$value['tid']]['pname'] ?? "";
            $value['lname']         = $tInfo[$value['tid']]['lname'] ?? "";
            $value['package_title'] = $tInfo[$value['tid']]['tname'] ?? '';
            $value['active_params'] = json_decode($value['active_params'], true);
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 创建审核信息
     *
     * @param int $sid 商家id
     * @param string $auditMaterials 审核材料
     * @param int $remark 备注
     * @date 2023/04/10
     * @auther yangjianhui
     * @return array
     */
    public function createAnnualActiveAudit($sid, $auditMaterials, $remark, $ticketId, $mobile, $idCard, $name, $activeParams, $annualId)
    {
        if (empty($sid)) {
            return $this->returnData(203, "sid参数有误02");
        }
        if (empty($auditMaterials)) {
            return $this->returnData(204, "要审核的照片参数缺失");
        }
        //做个锁，防止反复提交
        $cacheRedis = Cache::getInstance('redis');
        $key        = 'annual_active_audit:' . $annualId;
        if (!$cacheRedis->lock($key, 1, 10)) {
            return $this->returnData(204, "请勿重复提交审核");
        }

        $paramsData = compact('sid', 'auditMaterials', 'remark', 'ticketId', 'mobile', 'idCard', 'name', 'activeParams', 'annualId');
        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__CREATE_ANNUAL_ACTIVE_AUDIT, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
        $cacheRedis->rm($key);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取年卡是否已存在激活申请记录
     *
     * @param int $sid 商家id
     * @param int $annualId 年卡主键id
     *
     *
     * @date 2023/04/12
     * @auther yangjianhui
     * @return array
     */
    public function getActiveApplyBySidAndAnnualId($sid, $annualId)
    {
        if (empty($sid) || empty($annualId)) {
            return $this->returnData(203, "参数有误01");
        }
        $paramsData = compact('sid', 'annualId');
        $result = $this->ExternalCodeCall(self::__GET_ACTIVE_APPLY_BY_SID_AND_ANNUAL_ID, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 审核年卡激活申请
     *
     * @param int $sid 商家id
     * @param int $id 主键id
     * @param int $status 审核状态  1：审核通过 2：审核不通过
     * @param int $auditRemark 审核备注
     *
     *
     * @date 2023/04/12
     * @auther yangjianhui
     * @return array
     */
    public function auditActiveApply($sid, $memberId, $id, $status, $auditRemark)
    {
        if (empty($sid) || empty($id)) {
            return $this->returnData(203, "参数有误");
        }
        $applyParams = compact( 'sid', 'id');
        //获取申请信息
        $applyInfo = $this->ExternalCodeCall(self::__GET_APPLY_DETAIL_INFO, $applyParams, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
        if ($applyInfo['code'] != 200) {
            return $this->returnData($applyInfo['code'], $applyInfo['msg'], $applyInfo['data']);
        }
        $applyInfo = $applyInfo['data'];
        if ($applyInfo['state'] != 0) {
            return $this->returnData(204, "申请记录非待审核状态");
        }
        $activeParams = empty($applyInfo['active_params']) ? [] : json_decode($applyInfo['active_params'], true);
        $auditActualActive = $activeParams['audit_actual_active'] ?? false;
        $paramsData = compact('sid', 'memberId', 'id', 'status', 'auditRemark');
        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__AUDIT_ACTIVE_APPLY, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $annualId = $result['data']['annual_id'];
        $tid      = $result['data']['tid'];
        //获取年卡信息
        $annualCardModel = new AnnualCard();
        $annualInfo = $annualCardModel->getAnnualCard($annualId);
        if (empty($annualInfo)) {
            $paramsData['status'] = AnnualCardConst::ANNUAL_AUDIT_REJECT;//审核拒绝
            $paramsData['auditRemark'] = "年卡已删除";
            $this->ExternalCodeCall(self::__AUDIT_ACTIVE_APPLY, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);

            return $this->returnData(203, "年卡激活失败，年卡信息有误");
        }
        if (!in_array($annualInfo['status'], [0, 5])) {
            $msg = $annualInfo['status'] == AnnualCardConst::ANNUAL_AUDIT_PASS ? "年卡已激活，无需审核" : "年卡信息有误:年卡订单被取消";
            $paramsData['status'] = AnnualCardConst::ANNUAL_AUDIT_REJECT;//审核拒绝
            $paramsData['auditRemark'] .= $msg;
            $this->ExternalCodeCall(self::__AUDIT_ACTIVE_APPLY, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
            return $this->returnData(204, $msg);
        }
        if ($status == AnnualCardConst::ANNUAL_AUDIT_REJECT) {
            //发送短信
            $ticketApi  = new Ticket();
            $ticketInfo = $ticketApi->queryTicketInfoById($tid);
            if ($ticketInfo['ext']['annual_active_audit_sms'] == 1) {
                $messageCenterJsonRpc = new MessageCenterService();
                $tempName             = "refuse_material_audit";
                $data                 = [
                    substr($result['data']['mobile'], 7, 4),
                    $ticketInfo['product']['p_name'],
                    $annualInfo['virtual_no'],
                ];
                $messageCenterJsonRpc->sendMessageCenter($tempName, $result['data']['mobile'], $data, $sid);
            }
            if ($auditActualActive) {
                //年卡下单激活+审核拒绝的时候要删除年卡和退单
                $cancelParams = [
                    'ordernum' => $activeParams['ordernum'],
                    'sid' => $sid,
                    'annualId' => $annualId,
                ];
                $ret = AnnualAuditService::getInstance()->annualOrderCancel($cancelParams);
                if ($ret['code'] != 200) {
                    AnnualUtil::info([
                        'tag' => 'auditActiveApply',
                        'cancelParams' => $cancelParams,
                    ]);
                    return $this->returnData(203, "操作失败：" . $ret['msg']);
                }
            }
            return $this->returnData(200, "审核成功");
        }
        $activeData   = json_decode($result['data']['active_params'], true);
        $annualCardBz = new \Business\Product\AnnualCard();
        //判断是实体卡还是虚拟卡  active_type激活类型 1：虚拟卡 2：实体卡
        //审核是商家进行审核 因此操作人是sid
        if ($activeData['active_type'] == 1) {
            $activeResult = $annualCardBz->activeVirtualCardForshop($activeData['virtualNo'], $activeData['memberId'],
                $activeData['sid'], $activeData['pid'], $activeData['ordernum'], (object)$activeData['userObj'], $activeData['avatars'],
                $activeData['source'], '', '', true, $sid, $activeData['affiliates_person']);
        } else {
            $activeResult = $annualCardBz->activeAnnualCardForshop($activeData['mobile'], $activeData['cardNo'],
                $activeData['id_card'], $activeData['avatar'], $activeData['source'], $activeData['name'], $sid,
                0, 0, '', '', '', true, $activeData['voucher_type'] ?? 1, $sid, $activeData['affiliates_person']);
        }
        //发送激活成功短信
        $ticketApi  = new Ticket();
        $ticketInfo = $ticketApi->queryTicketInfoById($tid);
        if ($ticketInfo['ext']['annual_active_audit_sms'] == 1) {
            $messageCenterJsonRpc = new MessageCenterService();
            $tempName = "pass_material_audit";
            if ($activeResult['code'] != 200) {
                $tempName = "refuse_material_audit";
            }
            $data = [
                substr($result['data']['mobile'], 7, 4),
                $ticketInfo['product']['p_name'],
                $annualInfo['virtual_no'],
            ];
            $messageCenterJsonRpc->sendMessageCenter($tempName, $result['data']['mobile'], $data, $sid);
        }
        if ($activeResult['code'] != 200) {
            $activeFailParamsData                = compact('sid', 'memberId', 'id', 'auditRemark');
            $activeFailParamsData['status']      = 2;
            $activeFailParamsData['auditRemark'] .= "年卡激活失败：" . $activeResult['msg'];
            $this->ExternalCodeCall(self::__AUDIT_ACTIVE_APPLY, $activeFailParamsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);

            return $this->returnData(204, "审核拒绝,年卡激活失败，请联系工作人员,失败原因：" . $activeResult['msg']);
        }


        return $this->returnData(200, "审核成功");
    }

    /**
     * 修改审核状态
     *
     * @param int $annualId 年卡id
     * @param int $sid 商家id
     * @param int $memberId 操作人id
     * @param int $status 审核状态  1：审核通过 2：审核不通过
     * @param string $auditRemark 审核状态  1：审核通过 2：审核不通过
     *
     * @date 2023/04/12
     * @auther yangjianhui
     * @return array
     */
    public function changeApplyStatus($annualId, $sid, $memberId, $status, $auditRemark = "商家手动激活")
    {
        if (empty($annualId) || empty($sid)) {
            return $this->returnData(203, "参数有误");
        }
        $paramsData = compact('annualId', 'sid', 'memberId', 'status', 'auditRemark');
        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__CHANGE_APPLY_STATUS, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取申请详情
     *
     * @param int $id 主键iD
     * @param int $sid 商家id
     *
     * @date 2023/04/13
     * @auther yangjianhui
     * @return array
     */
    public function getApplyDetailInfo($sid, $id)
    {
        if (empty($sid) || empty($id)) {
            return $this->returnData(203, "参数有误");
        }
        $paramsData = compact( 'sid', 'id');
        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__GET_APPLY_DETAIL_INFO, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $returnData = $result['data'];
        $commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        $asField            = [
            'uuJqTicketDTO' => [
                'title' => 'ticketName',
            ],
            'uuLandDTO'     => [
                'title' => 'landName',
            ],
        ];
        $javaData           = $commodityTicketBiz->getTicketAndLandInfoByArrTid([$returnData['tid']], 'id,title', 'title', $asField);
        $tInfo              = [];
        foreach ($javaData as $value) {
            $tInfo[$value['id']]['pname'] = $value['landName'] .'-'.$value['ticketName']; //产品名称
            $tInfo[$value['id']]['lname'] = $value['landName']; //景区名称
            $tInfo[$value['id']]['tname'] = $value['ticketName']; //套餐名称
        }
        $returnData['active_params']                      = json_decode($returnData['active_params'], true);
        $returnData['active_params']['affiliates_person'] = json_decode($returnData['active_params']['affiliates_person'], true);
        $voucherType = $returnData['active_params']['userObj']['voucher_type'] ?? $returnData['active_params']['voucher_type'];
        $returnData['active_params']['voucher_type_name'] = $this->conversionIdentityToName($voucherType);
        $returnData['title']         = $tInfo[$returnData['tid']]['pname'];
        $returnData['package_title'] = $tInfo[$returnData['tid']]['tname'] ?? '';

        return $this->returnData(200, "获取成功", $returnData);
    }

    /**
     * 重发审核短信
     *
     * @param int $id 主键iD
     * @param int $sid 商家id
     *
     * @date 2023/04/13
     * @auther yangjianhui
     * @return array
     */
    public function resendAuditMessage($sid, $id)
    {
        if (empty($sid) || empty($id)) {
            return $this->returnData(203, "参数有误");
        }
        $paramsData = compact( 'sid', 'id');
        //获取关联票的配置列表
        $result = $this->ExternalCodeCall(self::__GET_APPLY_DETAIL_INFO, $paramsData, self::__ANNUAL_ACTIVE_AUDIT_SERVICE);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $messageCenterJsonRpc = new MessageCenterService();
        $tempName = $result['data']['state'] == 1 ? 'pass_material_audit' : 'refuse_material_audit';
        $annualCardModel = new AnnualCard();
        $annualInfo = $annualCardModel->getAnnualCard($result['data']['annual_id']);
        //发送激活成功短信
        $ticketApi  = new Ticket();
        $ticketInfo = $ticketApi->queryTicketInfoById($result['data']['tid']);
        $data = [
            substr($result['data']['mobile'], 7, 4),
            $ticketInfo['product']['p_name'],
            $annualInfo['virtual_no'],
        ];
        $result = $messageCenterJsonRpc->sendMessageCenter($tempName, $result['data']['mobile'], $data, $sid);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 检测年卡是否在节假日可用
     *
     * @param string $holidayLimit 配置信息
     * @param string $date 日期 （格式：yyyy-MM-dd）
     *
     * @date 2024/04/28
     * @auther yangjianhui
     * @return array
     */
    public function checkHolidayLimit($holidayLimit, $date)
    {
        if (empty($holidayLimit)) {
            return $this->returnData(200);
        }
        //type为0是不限制
        $holidayLimit = json_decode($holidayLimit, true);
        if ($holidayLimit['type'] == 0) {
            return $this->returnData(200);
        }
        //is_holiday_today 是否节假日当天(可能是多天) 0否 1是
        //获取日期
        $baseServerApi = new BaseServer();
        $dateInfo      = $baseServerApi->dateDetail($date);
        $holidayTag    = $dateInfo['data']['holidayTypes'][0] ?? '';
        $weekEnd       = $dateInfo['data']['weekEnd'];
        $workday       = $dateInfo['data']['workday'];
        //是否节假日当天 true-是 false-否
        $holidayFestival = $dateInfo['data']['holidayFestival'];
        //在限制节假日，或者限制周末不可用
        if (in_array($holidayTag, $holidayLimit['holiday_limit']) || (in_array('WEEKEND', $holidayLimit['holiday_limit']) && $weekEnd === true && $workday === false)) {
            $annualPrivilegeBz = new AnnualCardPrivilege();
            //如果是只限制节假日当天不可用
            if (isset($holidayLimit['is_holiday_today']) && $holidayLimit['is_holiday_today'] == 1) {
                if (!$holidayFestival) {
                    return $this->returnData(200);
                }
                $msg = $annualPrivilegeBz->_getPrivilegeHolidayLimitString($holidayLimit, true);
            } else {
                $msg = $annualPrivilegeBz->_getPrivilegeHolidayLimitString($holidayLimit);
            }
            return $this->returnData(204, $msg);
        }

        return $this->returnData(200);
    }
}
