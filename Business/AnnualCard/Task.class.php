<?php
/**
 * 年卡异步任务相关
 * <AUTHOR> Li
 * @date  2021-08-31
 */

namespace Business\AnnualCard;

use Business\Base;
use Business\CommodityCenter\Product;
use Business\CommodityCenter\Ticket;
use Model\Product\AnnualCard;

class Task extends Base
{
    //日志目录
    private $_debugLogDir = 'annual_card/task/';
    private $limit        = 3000;

    const ANNUAL_ORDER    = 0; //下单
    const ANNUAL_IMPORT   = 1; //导入
    const ANNUAL_POSTPONE = 2; //延期
    const ANNUAL_FACE_SMS = 3; //人脸短信
    const ANNUAL_DISABLED = 4; //禁用
    const ANNUAL_RECOVER  = 5; //恢复

    const ANNUAL_ADD_PRIVILEGE  = 6; //添加特权

    const ANNUAL_DELETE_PRIVILEGE  = 7; //删除特权
    const ANNUAL_IMPORT_GIFT_CARD = 8;//导入礼品卡
    const BULK_ASSOCIATE_PHYSICS = 9;//通过导入批量关联实体卡然后发卡

    const ANNUAL_PHYSICAL_ORDER = 10; //年卡物理卡下单

    //异步任务类型
    private $taskMap = [
        self::ANNUAL_ORDER            => 'order',       //下单
        self::ANNUAL_IMPORT           => 'import',      //导入
        self::ANNUAL_POSTPONE         => 'postpone',    //延期
        self::ANNUAL_FACE_SMS         => 'sms',         //人脸短信
        self::ANNUAL_DISABLED         => 'disabled',    //禁用
        self::ANNUAL_RECOVER          => 'recover',     //恢复
        self::ANNUAL_ADD_PRIVILEGE    => 'add_privilege',//添加特权
        self::ANNUAL_DELETE_PRIVILEGE => 'delete_privilege',//删除特权
        self::ANNUAL_IMPORT_GIFT_CARD => 'import_gift_card',//导入礼品卡
        self::BULK_ASSOCIATE_PHYSICS  => 'bulk_associate_physics_card',//通过导入批量关联实体卡然后发卡
        self::ANNUAL_PHYSICAL_ORDER   => 'physical_order',//年卡物理卡下单
    ];

    const TASK_MAP_TITLE = [
        self::ANNUAL_ORDER            => '年卡下单',       //下单
        self::ANNUAL_IMPORT           => '年卡批量导入',      //导入
        self::ANNUAL_POSTPONE         => '年卡批量延期',    //延期
        self::ANNUAL_FACE_SMS         => '年卡批量人脸短信',         //人脸短信
        self::ANNUAL_DISABLED         => '年卡批量禁用',    //禁用
        self::ANNUAL_RECOVER          => '年卡批量恢复',     //恢复
        self::ANNUAL_ADD_PRIVILEGE    => '年卡批量新增特权',//添加特权
        self::ANNUAL_DELETE_PRIVILEGE => '年卡批量删除特权',//删除特权
        self::ANNUAL_IMPORT_GIFT_CARD => '年卡礼品卡批量导入',//导入礼品卡
        self::BULK_ASSOCIATE_PHYSICS  => '实体卡库存批量导入',//通过导入批量关联实体卡然后发卡,
        self::ANNUAL_PHYSICAL_ORDER   => '年卡物理卡下单',//年卡物理卡下单
    ];

    //年卡异步任务type数组
    const TASK_TYPE_ARR = [
        self::ANNUAL_ORDER,
        self::ANNUAL_IMPORT,
        self::ANNUAL_POSTPONE,
        self::ANNUAL_FACE_SMS,
        self::ANNUAL_DISABLED,
        self::ANNUAL_RECOVER,
        self::ANNUAL_ADD_PRIVILEGE,
        self::ANNUAL_DELETE_PRIVILEGE,
        self::ANNUAL_IMPORT_GIFT_CARD,
        self::BULK_ASSOCIATE_PHYSICS,
        self::ANNUAL_PHYSICAL_ORDER,
    ];

    //年卡状态枚举
    const ANNUAL_STATUS_MAP = [
        -1 => '已过期',
        0  => '未激活',
        1  => '已激活',
        2  => '已禁用',
        4  => '已挂失',
        8  => '仓库中',
        16 => '已使用',
    ];

    //年卡状态枚举
    const STATUS_MAP = [
        0  => '未激活',
        1  => '已激活',
        2  => '已禁用',
        3  => '仓库中',
        4  => '已挂失',
        5  => '审核中',
        8  => '撤销',
    ];

    public function __construct()
    {

    }

    /**
     * <AUTHOR>
     * @Date 2023/8/30 16:46
     * @param array $annualIdArr
     * @param $sid
     * @param int $timeType 为0是实时表 其他为归档表
     * @return array|mixed
     */
    public function getAnnualInfoByIdArr(array $annualIdArr, $sid, $table = AnnualCard::ANNUAL_CARD_TABLE)
    {
        if (!$annualIdArr || !$sid) {
            return [];
        }
        $field = 'id,sid,memberid,virtual_no,physics_no,card_no,status,update_time,avalid_begin,avalid_end,dname,mobile,id_card_no,annual_status,pid,ext_info';
        $annualInfo = (new \Model\Product\AnnualCard(0, 0, $table))->getMultiAnnualCard($annualIdArr, $field, $sid);
        if (!$annualInfo) {
            return [];
        }

        return array_values($annualInfo);
    }

    /**
     * 年卡导入Task处理
     * <AUTHOR> Li
     * @date  2021-08-31
     *
     * @param  int  $ticketId  门票id
     * @param  int  $sid  供应商id
     * @param  array  $excelData  导入的数据
     * @param  int  $operatorId  操作员id
     *
     * @return  array
     * @throws \Exception
     */
    public function annualCardImport(int $ticketId, int $sid, array $excelData, int $operatorId)
    {
        if (!$ticketId || !$sid || !$excelData || !$operatorId) {
            return $this->returnData(203, '参数异常', []);
        }

        //循环下数据 获取出有效的数据
        $realExcelData = [];
        $res = (new \Business\JavaApi\Order\IdCardService())->idType();
        $resList = array_column($res['data'],'type','name');
        foreach ($excelData as $key => $value) {
            //表头部分需要跳过
            if ($key > 0) {
                $value[4]        = $resList[$value[4]] ?? 1;
                $value[9]        = $resList[$value[9]] ?? 1;
                $value[12]       = $resList[$value[12]] ?? 1;
                $value[15]       = $resList[$value[15]] ?? 1;
                $value[18]       = $resList[$value[18]] ?? 1;
                $realExcelData[] = $value;
            }
        }

        //一次只能操作 $this->limit 调数据
        if (count($realExcelData) > $this->limit) {
            return $this->returnData(203, "数量超过限制: {$this->limit}条", []);
        }

        //记录task
        $result = $this->taskWareHousing($sid, $realExcelData, $operatorId, self::ANNUAL_IMPORT, $ticketId);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '您提交的年卡信息正在导入中，导入结果请到年卡批量操作管理页面进行查看。', []);
    }

    /**
     * @throws \Exception
     */
    public function annualGiftCardImport(array $params): array
    {
        if (empty($params['tid'])) {
            throw new \Exception('tid参数异常');
        }
        if (empty($params['sid'])) {
            throw new \Exception('sid参数异常');
        }
        if (empty($params['excel_data'])) {
            throw new \Exception('excel_data参数异常');
        }
        if (empty($params['member_id'])) {
            throw new \Exception('member_id参数异常');
        }
        //循环下数据 获取出有效的数据
        $realExcelData = [];
        //数据重复校验
        $physicsNoArr = $cardNoArr = [];
        foreach ($params['excel_data'] as $key => $value) {
            //表头部分需要跳过
            if ($key > 0) {
                if (!is_numeric($value[0])) {
                    $txt = '第' . ($key + 1) . '行物理卡号必须是纯数字';
                    if ($value[0] != '') {
                        $txt .= '：' . $value[0];
                    }
                    throw new \Exception($txt);
                }
                if (!preg_match("/^[a-zA-Z0-9]+$/", $value[1])) {
                    $txt = '第' . ($key + 1) . '行实体卡号格式不正确，需要字母或数字组合';
                    if ($value[1] != '') {
                        $txt .= '：' . $value[1];
                    }
                    throw new \Exception($txt);
                }
                $realExcelData[] = $value;
                $physicsNoArr[] = $value[0];
                $cardNoArr[] = $value[1];
            }
        }
        //一次只能操作 $this->limit 调数据
        if (count($realExcelData) > $this->limit) {
            throw new \Exception("数量超过限制: {$this->limit}条");
        }
        //数据重复校验
        $uniPhysicsNo = array_unique($physicsNoArr);
        if (count($uniPhysicsNo) != count($physicsNoArr)) {
            throw new \Exception('表格物理卡号有重复，请处理后提交');
        }
        $uniCardNo = array_unique($cardNoArr);
        if (count($uniCardNo) != count($cardNoArr)) {
            throw new \Exception('表格实体卡号有重复，请处理后提交');
        }
        $extraParams = [
            'order_name' => $params['order_name'],
            'order_mobile' => $params['order_mobile'],
        ];
        //记录task
        $result = $this->taskWareHousing($params['sid'], $realExcelData, $params['member_id'], self::ANNUAL_IMPORT_GIFT_CARD, $params['tid'], AnnualCard::ANNUAL_CARD_TABLE, $extraParams);
        if ($result['code'] != 200) {
            throw new \Exception($result['msg']);
        }
        return $this->returnData(200, '可前往年卡批量操作管理查看结果');
    }

    /**
     * @throws \Exception
     */
    public function importForBulkEntityCardAssociations($params): array
    {
        //记录task
        $result = $this->taskWareHousing($params['sid'], $params['excel_data'], $params['member_id'], self::BULK_ASSOCIATE_PHYSICS, 0, AnnualCard::ANNUAL_CARD_TABLE, ['pid' => $params['pid']]);
        if ($result['code'] != 200) {
            throw new \Exception($result['msg']);
        }
        return $this->returnData(200, '可前往年卡批量操作管理查看结果');
    }
    /**
     * 年卡批量延期
     * <AUTHOR> Li
     * @date  2022-06-21
     *
     * @param  int  $sid  供应商id
     * @param  array  $realExcelData  年卡id数组
     * @param  int  $operatorId  操作员id
     *
     * @return  array
     * @throws \Exception
     */
    public function annualPostpone(int $sid, array $realExcelData, int $operatorId)
    {
        if (!$sid || !$realExcelData || !$operatorId) {
            return $this->returnData(203, '参数异常', []);
        }

        //一次只能操作 $this->limit 调数据
        if (count($realExcelData) > $this->limit) {
            return $this->returnData(203, "数量超过限制: {$this->limit}条", []);
        }

        //记录task
        $result = $this->taskWareHousing($sid, $realExcelData, $operatorId, self::ANNUAL_POSTPONE);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '您提交的延期年卡信息正在处理中，处理结果请到年卡批量操作管理页面进行查看。', []);
    }

    /**
     * 年卡批量发送人脸短信
     * <AUTHOR> Li
     * @date  2022-06-21
     *
     * @param  int  $sid  供应商id
     * @param  array  $realExcelData  年卡id数组
     * @param  int  $operatorId  操作员id
     *
     * @return  array
     * @throws \Exception
     */
    public function annualFaceSms(int $sid, array $realExcelData, int $operatorId)
    {
        //一次只能操作 $this->limit 调数据
        if (count($realExcelData) > $this->limit) {
            return $this->returnData(203, "数量超过限制: {$this->limit}条", []);
        }

        //记录task
        $result = $this->taskWareHousing($sid, $realExcelData, $operatorId, self::ANNUAL_FACE_SMS);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '您提交的发送年卡人脸短信正在处理中，处理结果请到年卡批量操作管理页面进行查看。', []);
    }

    /**
     * 年卡批量禁用
     * <AUTHOR> Li
     * @date  2022-06-21
     *
     * @param  int  $sid  供应商id
     * @param  array  $realExcelData  年卡id数组
     * @param  int  $operatorId  操作员id
     *
     * @return  array
     * @throws \Exception
     */
    public function annualDisabled(int $sid, array $realExcelData, int $operatorId, $table = AnnualCard::ANNUAL_CARD_TABLE)
    {
        //一次只能操作 $this->limit 调数据
        if (count($realExcelData) > $this->limit) {
            return $this->returnData(203, "数量超过限制: {$this->limit}条", []);
        }

        //记录task
        $result = $this->taskWareHousing($sid, $realExcelData, $operatorId, self::ANNUAL_DISABLED, 0, $table);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '您提交的年卡禁用操作正在处理中，处理结果请到年卡批量操作管理页面进行查看。', []);
    }

    /**
     * 年卡批量恢复
     * <AUTHOR> Li
     * @date  2022-06-21
     *
     * @param  int  $sid  供应商id
     * @param  array  $realExcelData  年卡id数组
     * @param  int  $operatorId  操作员id
     *
     * @return  array
     * @throws \Exception
     */
    public function annualRecover(int $sid, array $realExcelData, int $operatorId, string $table = AnnualCard::ANNUAL_CARD_TABLE)
    {
        //一次只能操作 $this->limit 调数据
        if (count($realExcelData) > $this->limit) {
            return $this->returnData(203, "数量超过限制: {$this->limit}条", []);
        }

        //记录task
        $result = $this->taskWareHousing($sid, $realExcelData, $operatorId, self::ANNUAL_RECOVER, 0, $table);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '您提交的年卡恢复操作正在处理中，处理结果请到年卡批量操作管理页面进行查看。', []);
    }

    /**
     * 年卡统一写入Task
     * @param $sid
     * @param $realExcelData
     * @param $operatorId
     * @param $taskType
     * @param int $tid
     * @param string $table
     * @param array $extraParams
     * @return array
     * @throws \Exception
     * <AUTHOR> Li
     * @date  2021-09-01
     */
    public function taskWareHousing($sid, $realExcelData, $operatorId, $taskType, $tid = 0, $table = AnnualCard::ANNUAL_CARD_TABLE, $extraParams = []): array
    {
        if (!$sid || !$realExcelData || !in_array($taskType, self::TASK_TYPE_ARR)) {
            return $this->returnData(203, '请求参数异常', []);
        }
        //通过tid 获取到lid  pid 等信息
        $lid = 0;
        $pid = 0;
        //如果值传递pid 则取pid下第一个tid
        $onlyPid = false;
        if (!$tid && !empty($extraParams['pid'])) {
            $onlyPid = true;
            $tid = (new \Model\Product\Ticket())->getTid($extraParams['pid']);
        }
        if ($tid) {
            $ticketInfo = (new Ticket())->queryTicketInfoById($tid, 'id,apply_did', 'id', 'id,p_type');
            if (empty($ticketInfo)) {
                return $this->returnData(204, '门票数据获取异常', []);
            }
            $lid   = $ticketInfo['land']['id'];
            $pid   = $ticketInfo['product']['id'];
            $pType = $ticketInfo['land']['p_type'];
            $tid   = $ticketInfo['ticket']['id'];
            $aid   = $ticketInfo['ticket']['apply_did'];
            if ($sid != $aid) {
                return $this->returnData(204, '没有权限操作当前产品', []);
            }
            if ($pType != 'I') {
                return $this->returnData(204, '非年卡产品无法操作', []);
            }
        }
        $orderTime = time();
        //写入一条任务记录
        $taskModel = new \Model\Product\AnnualCardTask();
        if ($onlyPid) {
            $tid = 0;
        }
        $taskId    = $taskModel->insertTask($sid, $sid, $paymode = 255, $ordermode = 255, $payMoney = 0,
            count($realExcelData), $lid, $pid, $tid, $operatorId, $orderTime, $dname = '', $mobile = '',
            $remark = '', $taskType);
        if (!$taskId) {
            pft_log($this->_debugLogDir, json_encode([
                'ac'         => 'taskWareHousing',
                'sid'        => $sid,
                'pid'        => $pid,
                'operatorId' => $operatorId,
                'taskType'   => $taskType,
                'taskId'     => $taskId,
            ], JSON_UNESCAPED_UNICODE));
            return $this->returnData(500, '任务记录失败，请稍后重试');
        }
        //任务类型  import 导入 postpone 延期
        $payTrackType = $this->taskMap[$taskType];
        //写入异步对接处理
        $saveData = [
            'sid'           => $sid,             //购买者id
            'pid'           => $pid,             //年卡产品id
            'tid'           => $tid,             //
            'lid'           => $lid,             //
            'opid'          => $operatorId,      //操作人id
            'realExcelData' => $realExcelData,   //虚拟卡号数组
            'taskId'        => $taskId,          //异步任务id
            'taskType'      => $taskType,        //异步任务类型 0下单 1导入 2延期 3人脸短信 4禁用 5恢复 6批量新增特权 7批量删除特权
            'payTrackType'  => $payTrackType,    //任务类型  import 导入 postpone 延期 add_privilege新增 delete_privilege删除
            'table'  => $table,    //归档或实时表
        ];
        if (!empty($extraParams)) {
            $saveData['extraParams'] = $extraParams;
        }
        $jobid = \Library\Resque\Queue::push('independent_system', 'AnnualCardTask_Job',
            [
                'payTrackType' => $payTrackType,
                'saveData' => $saveData
            ]);
        pft_log('annual/task',
            json_encode(['年卡批量操作申请成功', 'saveData: ' . json_encode($saveData), ' jobid: ' . $jobid],
                JSON_UNESCAPED_UNICODE));
        return $this->returnData(200, "年卡批量操作申请成功");
    }

    /**
     * 批量文件处理
     * <AUTHOR>
     * @date   2022/10/18
     *
     * @param  array   $excelPath    文件信息
     * @param  string  $type         操作类型
     * @param  int     $max          最大行数
     *
     * @return array
     */
    public function annualBatchPostponeFileCheck(array $excelPath, string $type = 'postpone', int $max = 0): array
    {
        if (empty($excelPath)) {
            return $this->returnData(203, '模板数据为空，请重新上传');
        }

        //文件解析
        $excelProcess = new \Process\Order\OrderFromExcel();
        $excelData    = $excelProcess->parseForBatchOrder($excelPath['tmp_name']);

        //延期导入字段处理
        $tmpArr = [];
        if ($type == 'postpone' || $type == 'opt_privilege') {
            $filter = ['虚拟卡号'];
            foreach ($excelData as $key => $arr) {
                $val = $arr[0] ?? '';
                $val = str_replace(' ', '', $val);
                if (in_array($val, $filter) || empty($val)) {
                    unset($excelData[$key]);
                }

                //判断下是否有重复的  有重复项直接抛异常
                if ($val && in_array($val, $tmpArr)) {
                    return $this->returnData(203, '当前文件中存在重复项，请确认');
                }
                $tmpArr[] = $val;
            }
            $excelData = array_merge($excelData);
        }

        //为空处理
        if (empty($excelData)) {
            return $this->returnData(203, '模板数据为空，请重新上传');
        }

        //行数限制
        $max = $max ?: $this->limit;
        if (count($excelData) > $max) {
            return $this->returnData(203, '当前模板的数据超过最大任务量');
        }

        return $this->returnData(200, '获取成功', $excelData);
    }

    /**
     * 批量延期处理
     * <AUTHOR>
     * @date   2022/10/19
     *
     * @param  int     $type          批量延期操作：1.勾选延期；2.列表延期；3.导入虚拟卡号文件延期
     * @param  int     $delayType     延期方式 1调整至指定日期 2顺眼X天
     * @param  string  $delayData     延期内容
     * @param  int     $tid           门票id
     * @param  string  $annualId      年卡主键id  多个以逗号隔开
     * @param  string  $params        列表请求参数
     * @param  array   $importFile    导入虚拟卡文件
     * @param  int     $sid           供应商id
     * @param  int     $opid          操作人id
     *
     * @return array
     */
    public function annualBatchPostpone(int $sid, int $opid, int $type, int $delayType, string $delayData, int $tid, string $annualId, string $params, array $importFile)
    {
        $code = 200;
        $msg  = '您提交的延期年卡信息正在处理中，处理结果请到年卡批量操作管理页面进行查看。';
        try {
            if (!$type || !$delayType || empty($delayData) || !$tid || !$sid) {
                throw new \Exception('参数错误', 203);
            }

            //通过传入门票id 获取到pid
            $ticketModel = new \Model\Product\Ticket();
            $pid         = $ticketModel->getTicketInfoById($tid, 'pid');
            $pid         = $pid['pid'] ?? 0;
            if (!$pid) {
                throw new \Exception('查询结果错误', 203);
            }

            $realExcelData = [];

            //处理年卡id
            if ($type == 1 && !empty($annualId)) {
                $annualIdArr = explode(',', $annualId);
                if (count($annualIdArr) > $this->limit) {
                    throw new \Exception('当前模板的数据超过最大任务量', 203);
                }
                $realExcelData = $this->getAnnualInfoByIdArr($annualIdArr, $sid);
            }

            //处理列表延期参数
            if ($type == 2 && !empty($params)) {
                $paramsArr       = json_decode($params, true);
                $page            = 1;
                $size            = 100;
                $identify        = $paramsArr['identify'] ?? '';
                $searchType      = $paramsArr['search_type'] ?? -1;
                $status          = $paramsArr['status'] ?? 5;
                $pidArr          = [$pid];
                $saleStart       = $paramsArr['sale_start'] ?? '';
                $saleEnd         = $paramsArr['sale_end'] ?? '';
                $activeStart     = $paramsArr['active_start'] ?? '';
                $activeEnd       = $paramsArr['active_end'] ?? '';
                $validStartStart = $paramsArr['valid_start_start'] ?? '';
                $validStartEnd   = $paramsArr['valid_start_end'] ?? '';
                $validEndStart   = $paramsArr['valid_end_start'] ?? '';
                $validEndEnd     = $paramsArr['valid_end_end'] ?? '';
                $distributorId   = $paramsArr['distributor_id'] ?? 0;
                $packState       = $paramsArr['pack_status'] ?? 0; //套餐状态：1未生效、2生效中、3已过期

                //有效期开始时间范围
                $validStart = [
                    'start' => $validStartStart,
                    'end'   => $validStartEnd,
                ];

                //有效期结束时间范围
                $validEnd = [
                    'start' => $validEndStart,
                    'end'   => $validEndEnd,
                ];

                $options  = [
                    'page_size'      => $size,          //每页数量
                    'page'           => $page,          //页数
                    'status'         => $status,        //年卡状态 0未激活 1正常 2禁用 3仓库中 4挂失 5除了仓库中的所有 6已过期
                    'identify'       => $identify,      //搜索参数
                    'sale_start'     => $saleStart,     //售出开始时间
                    'sale_end'       => $saleEnd,       //售出结束时间
                    'active_start'   => $activeStart,   //激活开始时间
                    'active_end'     => $activeEnd,     //激活结束时间
                    'distributor_id' => $distributorId, //分销商id
                    'search_type'    => $searchType,    //搜索类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
                    'valid_start'    => $validStart,    //有效期开始时间范围
                    'valid_end'      => $validEnd,      //有效期结束时间范围
                    'pid'            => $pidArr,        //门票pid
                    'pack_state'     => $packState,     //主套餐状态
                ];

                $cardModel = new \Model\Product\AnnualCard();

                //获取查询总数
                $total = $cardModel->getCardPageList($sid, $options, true);
                if (empty($total)) {
                    throw new \Exception('无数据', 200);
                }
                if ($total > $this->limit) {
                    throw new \Exception('当前模板的数据超过最大任务量', 203);
                }
                $totalPage = ceil($total / $size);
                for ($i = 1; $i <= $totalPage; $i++) {

                    $options['page'] = $i;

                    $result = $cardModel->getCardPageList($sid, $options);

                    $realExcelData = array_merge($realExcelData, $result);
                }
            }

            //文件导入处理
            if ($type == 3 && !empty($importFile)) {
                $excelDataRes = $this->annualBatchPostponeFileCheck($importFile);
                if ($excelDataRes['code'] != 200) {
                    return $this->returnData($excelDataRes['code'], $excelDataRes['msg']);
                }
                $excelData     = $excelDataRes['data'] ?? [];
                $virtualNoData = [];
                foreach ($excelData as $ld) {
                    if (empty($ld[0])) {
                        continue;
                    }
                    $virtualNoData[] = $ld[0];
                }
                //查询全部
                $cardModel     = new \Model\Product\AnnualCard();
                $realExcelData = $cardModel->getCardInfoByVirtual($virtualNoData);
                $virtualNoArr  = array_column($realExcelData, 'virtual_no');
                //将查询不到的数据补齐下
                foreach ($virtualNoData as $tmp) {
                    if (!in_array($tmp, $virtualNoArr)) {
                        $realExcelData[] = [
                            'virtual_no' => $tmp,
                        ];
                    }
                }
            }

            if (empty($realExcelData)) {
                throw new \Exception('卡数据不能为空', 203);
            }

            //加上延期参数,过滤非当前查询套餐的
            foreach ($realExcelData as $key => $item) {
                $realExcelData[$key]['type']       = $delayType;
                $realExcelData[$key]['delay_data'] = $delayData;

                //非当前查询套餐的，清除其他参数
                if (!isset($item['pid']) || $item['pid'] != $pid) {
                    $realExcelData[$key] = [
                        'virtual_no' => $item['virtual_no'],
                    ];
                }
            }

            //记录task，走异步队列
            $taskType = self::ANNUAL_POSTPONE;
            $result   = $this->taskWareHousing($sid, $realExcelData,
                $opid, $taskType, $tid);
            if ($result['code'] != 200) {
                throw new \Exception($result['msg'], $result['code']);
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            //记录日志
            pft_log($this->_debugLogDir . "error", json_encode([
                'ac'      => 'annualBatchPostpone',
                'code'    => $code,
                'msg'     => $msg,
                'requert' => func_get_args(),
                'line'    => $e->getLine(),
                'file'    => $e->getFile(),
            ], JSON_UNESCAPED_UNICODE));
        }

        return $this->returnData($code, $msg);
    }
    /**
     * 批量操作特权
     * <AUTHOR>
     * @date   2023/06/26
     *
     * @param  int     $type          批量操作：1.勾选；2.列表；3.导入虚拟卡号文件
     * @param  int     $optType       批量操作类型：1.批量新增特权 2.批量删除特权
     * @param  int     $tid           门票id
     * @param  string  $annualId      年卡主键id  多个以逗号隔开
     * @param  array  $group_priv_arr    年卡特权信息
     * @param  string  $params        列表请求参数
     * @param  array   $importFile    导入虚拟卡文件
     * @param  int     $sid           供应商id
     * @param  int     $opId          操作人id
     *
     * @return array
     */
    public function annualBatchPrivilege(int $sid, int $opId, int $type, int $optType, int $tid, string $annualId, array $group_priv_arr, string $params, array $importFile)
    {
        $code = 200;
        //操作类型 1-批量添加特权 2-批量删除特权
        $text = $optType == 1 ? '新增' : '删除';
        $msg  = '您提交的'.$text.'年卡特权信息正在处理中，处理结果请到年卡批量操作管理页面进行查看。';
        try {
            if (!in_array($type, [1, 2, 3]) || !in_array($optType, [1, 2]) || !$tid || !$sid || !$group_priv_arr) {
                throw new \Exception('参数错误', 203);
            }
            $realExcelData = [];
            //处理年卡id
            if ($type == 1 && !empty($annualId)) {
                $annualIdArr = explode(',', $annualId);
                if (count($annualIdArr) > $this->limit) {
                    throw new \Exception('当前模板的数据超过最大任务量', 203);
                }
                $realExcelData = $this->getAnnualInfoByIdArr($annualIdArr, $sid);
            }
            //通过传入门票id 获取到pid
            $ticketModel = new \Model\Product\Ticket();
            $pid         = $ticketModel->getTicketInfoById($tid, 'pid');
            $pid         = $pid['pid'] ?? 0;
            if (!$pid) {
                throw new \Exception('查询pid结果错误', 203);
            }
            //处理列表参数
            if ($type == 2 && !empty($params)) {
                $paramsArr       = json_decode($params, true);
                $page            = 1;
                $size            = 3000;
                $identify        = $paramsArr['identify'] ?? '';
                $searchType      = $paramsArr['search_type'] ?? -1;
                $status          = $paramsArr['status'] ?? 5;
                $pidArr          = [$pid];
                $saleStart       = $paramsArr['sale_start'] ?? '';
                $saleEnd         = $paramsArr['sale_end'] ?? '';
                $activeStart     = $paramsArr['active_start'] ?? '';
                $activeEnd       = $paramsArr['active_end'] ?? '';
                $validStartStart = $paramsArr['valid_start_start'] ?? '';
                $validStartEnd   = $paramsArr['valid_start_end'] ?? '';
                $validEndStart   = $paramsArr['valid_end_start'] ?? '';
                $validEndEnd     = $paramsArr['valid_end_end'] ?? '';
                $distributorId   = $paramsArr['distributor_id'] ?? 0;
                $packState       = $paramsArr['pack_status'] ?? 0; //套餐状态：1未生效、2生效中、3已过期

                //有效期开始时间范围
                $validStart = [
                    'start' => $validStartStart,
                    'end'   => $validStartEnd,
                ];

                //有效期结束时间范围
                $validEnd = [
                    'start' => $validEndStart,
                    'end'   => $validEndEnd,
                ];

                $options  = [
                    'page_size'      => $size,          //每页数量
                    'page'           => $page,          //页数
                    'status'         => $status,        //年卡状态 0未激活 1正常 2禁用 3仓库中 4挂失 5除了仓库中的所有 6已过期
                    'identify'       => $identify,      //搜索参数
                    'sale_start'     => $saleStart,     //售出开始时间
                    'sale_end'       => $saleEnd,       //售出结束时间
                    'active_start'   => $activeStart,   //激活开始时间
                    'active_end'     => $activeEnd,     //激活结束时间
                    'distributor_id' => $distributorId, //分销商id
                    'search_type'    => $searchType,    //搜索类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
                    'valid_start'    => $validStart,    //有效期开始时间范围
                    'valid_end'      => $validEnd,      //有效期结束时间范围
                    'pid'            => $pidArr,        //门票pid
                    'pack_state'     => $packState,     //主套餐状态
                ];
                //var_dump($options);exit;
                $cardModel = new \Model\Product\AnnualCard();

                //获取查询总数
                $total = $cardModel->getCardPageList($sid, $options, true);
                if (empty($total)) {
                    throw new \Exception('无数据', 200);
                }
                if ($total > $this->limit) {
                    throw new \Exception('当前模板的数据超过最大任务量', 203);
                }
                $totalPage = ceil($total / $size);
                for ($i = 1; $i <= $totalPage; $i++) {

                    $options['page'] = $i;

                    $result = $cardModel->getCardPageList($sid, $options);

                    $realExcelData = array_merge($realExcelData, $result);
                }
            }
            //var_dump($realExcelData);exit();
            //文件导入处理 (虚拟卡号)
            if ($type == 3 && !empty($importFile)) {
                $excelDataRes = $this->annualBatchPostponeFileCheck($importFile, 'opt_privilege');
                if ($excelDataRes['code'] != 200) {
                    return $this->returnData($excelDataRes['code'], $excelDataRes['msg']);
                }
                $excelData     = $excelDataRes['data'] ?? [];
                $virtualNoData = [];
                foreach ($excelData as $ld) {
                    if (empty($ld[0])) {
                        continue;
                    }
                    $virtualNoData[] = $ld[0];
                }
                //查询全部
                $cardModel     = new \Model\Product\AnnualCard();
                $realExcelData = $cardModel->getCardInfoByVirtual($virtualNoData);
                $virtualNoArr  = array_column($realExcelData, 'virtual_no');
                //将查询不到的数据补齐下
                foreach ($virtualNoData as $tmp) {
                    if (!in_array($tmp, $virtualNoArr)) {
                        $realExcelData[] = [
                            'virtual_no' => $tmp,
                        ];
                    }
                }
            }

            if (empty($realExcelData)) {
                throw new \Exception('卡数据不能为空', 203);
            }
            //加上特权参数,过滤非当前查询套餐的
            foreach ($realExcelData as $key => $item) {
                $realExcelData[$key]['group_priv'] = $group_priv_arr;
                //非当前查询套餐的，清除其他参数
                if (!isset($item['pid']) || $item['pid'] != $pid) {
                    throw new \Exception('存在非当前套餐数据请检查，虚拟卡号：'.$item['virtual_no'], 203);
                    /*$realExcelData[$key] = [
                        'virtual_no' => $item['virtual_no'],
                    ];*/
                }
            }
            //记录task，走异步队列
            $taskType = $optType == 1 ? self::ANNUAL_ADD_PRIVILEGE : self::ANNUAL_DELETE_PRIVILEGE;
            $result   = $this->taskWareHousing($sid, $realExcelData,
                $opId, $taskType, $tid);
            if ($result['code'] != 200) {
                throw new \Exception($result['msg'], $result['code']);
            }
        } catch (\Exception $e) {
            $code = $e->getCode();
            $msg  = $e->getMessage();
            //记录日志
            pft_log($this->_debugLogDir . "error", json_encode([
                'ac'      => 'annualBatchPrivilege',
                'code'    => $code,
                'msg'     => $msg,
                'request' => func_get_args(),
                'line'    => $e->getLine(),
                'file'    => $e->getFile(),
            ], JSON_UNESCAPED_UNICODE));
        }
        return $this->returnData($code, $msg);
    }
}