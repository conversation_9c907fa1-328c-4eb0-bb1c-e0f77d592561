<?php
/**
 * <AUTHOR>
 * @date   2024/09/25
 */

namespace Business\InfrastructureApi;

use Business\Base;

class CallBase extends Base
{
    private $_logPath;
    private $_apiInfo;

    public function __construct($apiInfo = [], $logName = 'default')
    {
        $this->_apiInfo = $apiInfo;
        $this->_logPath = "infrastructure_api/{$logName}/";
    }

    public function call($method, $params, $version = 'v1')
    {
        if (empty($this->_apiInfo)) {
            return $this->returnData(500, '请求异常');
        }

        //请求接口处理
        $baseUrl    = $this->_apiInfo['path'];
        $methodList = $this->_apiInfo['method_list'];
        $methodPath = $methodList[$method];

        //获取最后的url
        $requestUrl = '/' . $baseUrl . '/' . $methodPath;
        $requestUrl = str_replace('{version}', $version, $requestUrl);

        $apiConfig = load_config('infrastructure_api', 'api');

        $apiPort = $apiConfig['api_port'] ?? 80;
        $timeOut = $apiConfig['time_out'] ?? 25;
        $realUrl = $apiConfig['api_base_url'] . $requestUrl;
        $logPath = empty($this->_logPath) ? '/api/curl_post' : $this->_logPath;

        $header   = ['Content-Type:application/json'];

        $result = curl_post($realUrl, json_encode($params), $apiPort, $timeOut, $logPath, $header);

        if (is_array($result) || $result === false) {
            //主要用来进行接口超时判断的
            return ['code' => 500, 'data' => [], 'msg' => '接口异常'];
        } else {
            $result = @json_decode($result, true);
            if (isset($result['code'])) {
                $showMsg = $result['code'] == 500 ? '系统异常，请重试' : ($result['msg'] ?? '业务异常');

                return [
                    'code' => $result['code'],
                    'data' => $result['data'],
                    'msg'  => $showMsg,
                ];
            }
        }

        if (empty($result)) {
            return $this->returnData(500, '接口异常');
        }

        return $result;
    }
}