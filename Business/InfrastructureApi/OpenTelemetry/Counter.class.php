<?php

namespace Business\InfrastructureApi\OpenTelemetry;

use Business\InfrastructureApi\CallBase;

class Counter extends CallBase
{
    public function __construct()
    {
        $tmpList = load_config('openTelemetry', 'infrastructureApiUrl');
        $apiInfo = $tmpList['counter'] ?? [];

        parent::__construct($apiInfo, 'counter');
    }

    public function counterAdd($counterName = '', $counterDesc = '', $counterUnit = '', $addValue = 0)
    {
        if (empty($counterName) || empty($counterDesc) || empty($counterUnit) || empty($addValue)) {
            return $this->returnData(204, '参数错误');
        }

        $params = [
            'counter_name' => $counterName,
            'counter_desc' => $counterDesc,
            'counter_unit' => $counterUnit,
            'add_value'    => intval($addValue),
        ];

        return $this->call('add', $params);
    }

    public function simpleCounterAdd($counterName, $addValue = 1): void
    {
        try {
            $this->call('add', [
                'counter_name' => $counterName,
                'add_value'    => intval($addValue),
            ]);
        } catch (\Exception $e) {}
    }
}