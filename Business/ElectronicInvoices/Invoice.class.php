<?php
/**
 * 诺诺发票Business层
 * <AUTHOR> Li
 * @date 2018-11-22
 */

namespace Business\ElectronicInvoices;

use Business\Base;
use Business\NewJavaApi\Order\ProductOrder\BizProductOrder;
use Business\NewJavaApi\Order\ProductOrder\FeignClient;
use Exception;
use Library\Business\Invoice\CryptAES;
use Library\Cache\Cache as Cache;
use Library\Business\NuoInvoice as invoiceLib;
use Model\Order\OrderTools;
use Business\ElectronicInvoices\InvoiceApi;

class Invoice extends base
{
    //AES加密参数与
    private $_cipher      = MCRYPT_RIJNDAEL_128;
    private $_mode        = MCRYPT_MODE_ECB;
    private $_pkmethod    = "pkcs5";
    private $_method      = 'AES-128-ECB';
    private $_configure   = [];
    private $_invoiceApi  = null;
    private $_orderModel  = '';
    private $_ticketModel = '';
    private $_moduleModel = '';

    public function __construct()
    {
        $this->_configure = load_config('nuonuo', 'electronic_invoice');
    }

    /**
     * 通过refresh_token刷新新的access_token
     *
     * <AUTHOR> Li
     * @date 2018-11-22
     *
     * @param  string  $refreshToken  企业refresh_token
     * @param  string  $userId  企业在诺诺对应的用户id
     * @param  string  $userName  企业税号
     */
    public function refreshAccessToken($refreshToken, $userId, $userName, $sid, $sysId = 0)
    {
        $invoiceLib = new invoiceLib();
        $result     = $invoiceLib->refreshAccessToken($refreshToken, $userId, $userName, $sid, $sysId);
        if ($result === false) {
            return false;
        }
        $result = json_decode($result, true);
        if (isset($result['error']) && !empty($result['error'])) {
            return false;
        }

        //将票付通的access_token 存到缓存中
        if ($userName == $this->_configure['userTax'] && $sid == 1) {
            $redis = Cache::getInstance('redis');
            //保存access_token
            $access = "access_token";
            $redis->set($access, $result['access_token']);

            pft_log('Invoice/debug', json_encode([
                '刷新缓存',
                'access_token: ' . $result['access_token'],
                'refresh_token: ' . $result['refresh_token'],
                'userTax: ' . $userName,
                '请求时间： ' . date('Y-m-d H:i:m', time()),
            ], JSON_UNESCAPED_UNICODE));
        }

        $expireTime = time() + ($result['expires_in'] ?? 86400);
        //获取模型
        //通过得到用户税号 对应修改 access_token和refresh_token之后保存到数据库中
        $res = $this->getInvoiceApi()->editEnterpriseInfoByTariffNumber($userName, $result['access_token'],
            $result['refresh_token'], $userId, $sid, $expireTime, $sysId);

        if ($res['code'] != 200) {
            pft_log('Invoice/fail', json_encode([
                'editEnterpriseInfoByTariffNumber_err',
                'params: ' . $userName,
                $result['access_token'],
                $result['refresh_token'],
                $userId,
                $res,
                '请求时间： ' . date('Y-m-d H:i:m', time()),
            ], JSON_UNESCAPED_UNICODE));
            return false;
        }

        pft_log('Invoice/success', json_encode([
            'refresh_token',
            'postData: ' . [$refreshToken, $userId, $userName, $sid, $sysId],
            'result: ' . $result,
        ]));

        return $result;

    }

    /**
     * 获取加解密AES参数
     * <AUTHOR> Li
     * @date 2018-11-30
     *
     * @param  string  $params
     * @param  int  $type  0加密 1解密
     */
    public function getParams($params, $type, $app_secret = 'B522B977306A4BF2')
    {
        $aes = new CryptAES();
        $aes->iniAES($this->_cipher, $this->_mode, $this->_pkmethod, $app_secret, $this->_method);

        if ($type) {
            $result = $aes->openssldecrypt($params);
        } else {
            $result = $aes->opensslencrypt($params);
        }

        return $result;
    }

    private function getInvoiceApi()
    {
        if ($this->_invoiceApi == null) {
            $this->_invoiceApi = new InvoiceApi();
        }

        return $this->_invoiceApi;
    }

    private function getOrdereModel()
    {
        if (!$this->_orderModel) {
            $this->_orderModel = new OrderTools('slave');
        }

        return $this->_orderModel;
    }

    private function getModuleModel()
    {
        if (!$this->_moduleModel) {
            $this->_moduleModel = new \Model\AppCenter\ModuleDetail();
        }

        return $this->_moduleModel;
    }

    /**
     * 获取未开票/已开票订单信息
     * <AUTHOR> Li
     * @date 2019-10-17
     *
     * @param  array  $options  订单列表查询条件
     * @param  int  $invoiceType  0 未开票订单 1 已开票订单
     *
     * @return array
     */
    public function getInvoiceOrderList($memberId = 0, $options = [], $invoiceType = 0)
    {
        if (empty($options) || empty($memberId)) {
            return [];
        }

        //先查出pft001 对应用户开票订单的信息

        $memberIdArr = [];
        if (is_array($memberId)) {
            $memberIdArr = $memberId;
        } else {
            $memberIdArr = [$memberId];
        }

        $orderNumArr    = [];
        $ordernumsNotIn = [];
        $invoiceInfo    = $this->getInvoiceApi()->checkInvoiceRecordByMixed(0, [], $memberIdArr);
        if ($invoiceInfo['code'] == 200) {
            $orderNumArr = array_column($invoiceInfo['data'], 'ordernum');
        }
        //再通过查出的对应订单号到myuu 查出对应订单信息返回
        if ($orderNumArr && $invoiceType) {
            //$options['where']['ordernum'] = ['IN', $orderNumArr];
        } else if ($orderNumArr && !$invoiceType) {
            $ordernumsNotIn = $orderNumArr;
            //$options['where']['ordernum'] = ['NOT IN', $orderNumArr];
        } else if (empty($orderNumArr) && $invoiceType) {
            return ['code' => 204, 'data' => [], 'msg' => '暂无数据'];
        }

        //获取总数
        //$totalOption = $options;
        //unset($totalOption['limit'], $totalOption['field']);
        //$totalOption['field'] = 'count(*) as total';
        //
        //$orderModel = new OrderTools();
        //$orderInfo  = $orderModel->getOrders($options);
        //$total      = $orderModel->getOrders($totalOption);
        //
        //if (!empty($orderInfo)) {
        //    return ['code' => 200, 'data' => $orderInfo, 'total' => $total[0]['total'], 'msg' => '获取成功'];
        //}
        //
        //return ['code' => 204, 'data' => [], 'msg' => '暂无数据'];

        $orderMode   = empty($options['ordermode']) ? [] : $options['ordermode'];
        $orderStatus = empty($options['status']) ? [] : $options['status'];
        $payStatus   = empty($options['pay_status']) ? [] : $options['pay_status'];
        $beginTime   = empty($options['begin_time']) ? date('2020-01-01') : $options['begin_time'];
        $endTime     = empty($options['end_time']) ? date('Y-m-d') : $options['end_time'];
        $page        = empty($options['page']) ? '' : $options['page'];
        $pageSize    = empty($options['page_size']) ? '' : $options['page_size'];

        $queryParams = [
            $memberId,
            $orderMode,
            $orderStatus,
            $payStatus,
            $beginTime,
            $endTime,
            $page,
            $pageSize,
            $ordernumsNotIn,
        ];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderPage', 'findOpenTicket', $queryParams);

        if ($queryRes['code'] != 200) {
            pft_log('order/cancel', json_encode($queryRes));

            return ['code' => 204, 'data' => [], 'msg' => $queryRes['msg']];
        }

        return [
            'code'  => 200,
            'data'  => $queryRes['data']['list'],
            'total' => $queryRes['data']['total'],
            'msg'   => '获取成功',
        ];
    }

    /**
     * 假如是套票 子票订单也取主票的
     * <AUTHOR> Li
     * @date 2019-10-17
     *
     * @param  string  $orderNum  订单号
     *
     * @return string
     */
    public function getParentOrderNum($orderNum)
    {
        if (empty($orderNum)) {
            return '';
        }
        //判断当前订单号是否属于套票的子票  如果是子票 需要获取出主票的订单号用来绑定人脸
        //主票订单号
        $parentOrderNum  = $orderNum;
        $parentOrderInfo = $this->getOrdereModel()->getPackOrdersInfoByOrderId($parentOrderNum, 'pack_order');
        if (!empty($parentOrderInfo[0]['pack_order'])) {
            if ($parentOrderInfo[0]['pack_order'] > 1) {
                $parentOrderNum = $parentOrderInfo[0]['pack_order'];
            }
        }

        return $parentOrderNum;
    }

    /**
     * 百旺开票中转接口
     * <AUTHOR>  Li
     * @date 2021-09-03
     *
     * @param  $sid  $sid  供应商id
     * @param  string  $orderNum  订单号
     * @param  int  $invoiceType 0 增值税普通电子发票 1 增值税电子专用发票 3 增值税专用发票, 4 增值税普通发票
     * @param  int  $openUserId 开票人id
     * @param  string  $mergeId 合并id
     *
     * @return array
     */
    public function baiwangTransit($sid, $orderNum, $openUserId, $invoiceType, $mergeId = '')
    {
        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();

        if (!$mergeId) {
            //解析下订单号
            $orderNum = \Library\MessageNotify\OrderNotify::url_sms_decode($orderNum)[0];
            if (empty($orderNum)) {
                return $this->returnData(203, '开票链接参数有误', []);
            }
            //校验下订单号是否能开票
            $checkRes   = $invoiceApi->checkInvoiceAuth([$orderNum], $sid);

            if ($checkRes['code'] != 200 || $checkRes['data'][$orderNum]['code'] != 200) {
                pft_log('Invoice/distributor',
                    json_encode(['action' => 'checkInvoiceAuth', 'checkRes' => $checkRes, 'ordernum' => $orderNum],
                        JSON_UNESCAPED_UNICODE));

                return $this->returnData(204, $checkRes['data'][$orderNum]['msg']);
            }

            $orderInfo = $checkRes['data'][$orderNum];

            //检测下是否有生成过代开数据
            $openInvoiceUrlRes = $invoiceApi->getOpenInvoiceUrl($sid, $orderInfo['tid'], $orderNum,
                $isTransit = true, $invoiceType);
            if (isset($openInvoiceUrlRes['code']) && $openInvoiceUrlRes['code'] == 200) {
                return $this->returnData(200, "开票地址获取成功", ['openInvoiceUrl' => $openInvoiceUrlRes['data']]);
            }

            return $this->returnData($openInvoiceUrlRes['code'], $openInvoiceUrlRes['msg'], $openInvoiceUrlRes['data']);
        }else{
            if (!$mergeId) {
                return $this->returnData(203, '合并开票链接参数有误', []);
            }
            // 合并发票 中转
            $openInvoiceUrlRes = $invoiceApi->getMergeOpenInvoiceUrl($sid, [],
                $isTransit = true, $openUserId, $invoiceType, $mergeId);
            if (isset($openInvoiceUrlRes['code']) && $openInvoiceUrlRes['code'] == 200) {
                return $this->returnData(200, "开票地址获取成功", ['openInvoiceUrl' => $openInvoiceUrlRes['data']]);
            }

            return $this->returnData($openInvoiceUrlRes['code'], $openInvoiceUrlRes['msg'], $openInvoiceUrlRes['data']);
        }
    }

    /**
     * 分页模糊搜索商品名配置
     * <AUTHOR>  Li
     * @date   2022-01-18
     *
     * @param  int  $sid  当前登录供应商id
     * @param  string  $name  定义商品名称
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return  array
     */
    public function getDefinitionRecordsListPage(int $sid, string $name, int $page = 1, int $size = 10)
    {
        if (!$sid || !$name) {
            return $this->returnData(203, '参数信息有误');
        }

        //需要查出当前用户上级供应商id
        $memberModel   = new \Model\Member\MemberRelationship();
        $suppliersInfo = $memberModel->getValidSuppliers($sid);
        if (empty($suppliersInfo['list'])) {
            return $this->returnData(204, '供应商信息获取异常');
        }
        $sidArr     = array_column($suppliersInfo['list'], 'id');
        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();
        $result     = $invoiceApi->getDefinitionRecordsListPage($sidArr, $name, $page, $size);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 取消开发票
     * <AUTHOR>  Li
     * @date  2022-03-18
     *
     * @param  int  $recordId  开票记录id
     *
     * @return  array
     */
    public function invoiceCancel(int $recordId, int $sid = 0)
    {
        if (!$recordId) {
            return $this->returnData(203, '参数有误');
        }

        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();
        $result     = $invoiceApi->invoiceCancel($recordId, $sid);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '取消操作成功', []);
    }

    /**
     * 红冲发票
     * <AUTHOR>  Li
     * @date  2022-11-10
     *
     * @param  int  $recordId  开票记录id
     *
     * @return  array
     */
    public function invoiceRedDashed(int $recordId, int $sid = 0)
    {
        if (!$recordId) {
            return $this->returnData(203, '参数有误');
        }

        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();
        $result     = $invoiceApi->invoiceRedDashed($recordId, $sid);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }

        return $this->returnData(200, '发票红冲发起成功', []);
    }

    /**
     * 通过开票单号获取订单信息
     *
     * @param  string  $mergeId  合并订单号
     * @param  string|string[]  $platformOrderNum  平台订单号
     * @param  string|string[]  $tvOrderNum  旅游券订单号
     * @param  string|string[]  $tsOrderNum  共享租赁订单号
     *
     * @return array
     */
    public function getInvoiceOrderInfo($mergeId = '', $platformOrderNum = '', $tvOrderNum = '', $tsOrderNum = '', $sid = 0)
    {
        if (!$mergeId && !$platformOrderNum && !$tvOrderNum && !$tsOrderNum) {
            return $this->returnData(203, '订单信息异常');
        }

        $commonOrderArr        = [];
        $travelVoucherOrderArr = [];
        $sharedLeaseOrderArr   = [];
        $invoiceApi            = new \Business\ElectronicInvoices\InvoiceApi();
        if ($mergeId) {
            $getMergeList = $invoiceApi->getMergeSonList($mergeId, 1, $sid);
            $getMergeList = $getMergeList['data'];

            //需要通过订单号 获取到对应订单的类型
            foreach ($getMergeList as $orderInfo) {
                //平台订单
                if ($orderInfo['order_type'] == 1) {
                    $commonOrderArr[] = $orderInfo['ordernum'];
                }
                //旅游券订单
                if ($orderInfo['order_type'] == 2) {
                    $travelVoucherOrderArr[] = $orderInfo['ordernum'];
                }
                //共享租赁订单
                if ($orderInfo['order_type'] == 3) {
                    $sharedLeaseOrderArr[] = $orderInfo['ordernum'];
                }
            }
        }
        if ($tvOrderNum) {
            $_travelVoucherOrderArr = is_array($tvOrderNum) ? $tvOrderNum : [(string)$tvOrderNum];
            $travelVoucherOrderArr = array_merge($travelVoucherOrderArr, $_travelVoucherOrderArr);
        }
        if ($platformOrderNum) {
            $_commonOrderArr = is_array($platformOrderNum) ? $platformOrderNum : [(string)$platformOrderNum];
            $commonOrderArr = array_merge($commonOrderArr, $_commonOrderArr);
        }
        if ($tsOrderNum) {
            $_sharedLeaseOrderArr = is_array($tsOrderNum) ? $tsOrderNum : [(string)$tsOrderNum];
            $sharedLeaseOrderArr  = array_merge($sharedLeaseOrderArr, $_sharedLeaseOrderArr);
        }

        $tmpOrderInfo = [];
        if ($commonOrderArr) {
            //根据订单号查询订单
            $subOrderModel   = new \Model\Order\SubOrderQuery();
            $commonOrderInfo = $subOrderModel->getOrderDetail($commonOrderArr);
            if (!$commonOrderInfo) {
                pft_log('invoice/hanxinCallbackMerge/error',
                    '合并发票，未查到订单信息' . json_encode(['ordernum' => $mergeId, 'sonOrderArr' => $commonOrderArr, 'tmpOrderInfo'=>$commonOrderInfo]));
                return $this->returnData(204, '未查到订单信息', []);
            }
            $tmpOrderInfo = array_merge($tmpOrderInfo, $commonOrderInfo);
        }

        if ($travelVoucherOrderArr) {
            //通过订单号先到旅游券检测下
            $tvInvoiceApi = new \Business\JsonRpcApi\TravelVoucherService\OrderQuery();
            $tvOrderInfo  = $tvInvoiceApi->getOrderInfoDetail($travelVoucherOrderArr, true);
            if (!$tvOrderInfo) {
                pft_log('invoice/hanxinCallbackMerge/error',
                    '合并发票，未查到订单信息' . json_encode(['ordernum' => $mergeId, 'sonOrderArr' => $travelVoucherOrderArr, 'tmpOrderInfo'=>$tvOrderInfo]));
                return $this->returnData(204, '未查到订单信息', []);
            }
            $tmpOrderInfo = array_merge($tmpOrderInfo, $tvOrderInfo);
        }

        if ($sharedLeaseOrderArr) {
            $slOrderInfo = $invoiceApi->getSharedLeaseOrderInfo($sharedLeaseOrderArr);
            if ($slOrderInfo['code'] == 200 && $slOrderInfo['data']) {
                $tmpOrderInfo = array_merge($tmpOrderInfo, array_values($slOrderInfo['data']));
            }
        }

        $tmpOrderInfo = array_map(function ($item) {
            $item['aids_money'] = $item['aids_money'] ? json_decode($item['aids_money'], true) : [];
            $item['aids_money'][] = ['aid' => $item['aid'], 'fid' => $item['member'], 'money' => $item['totalmoney'], 'payway' => $item['paymode']];
            return $item;
        }, $tmpOrderInfo);

        return $this->returnData(200, '订单信息获取成功', $tmpOrderInfo);
    }

    /**
     * 电子发票中转页获取发票信息
     *
     * @param  string  $orderNum  订单号
     * @param  int  $sid  开票方商家id
     * @param  int  $fid  开票人操作id
     *
     * @return array
     */
    public function checkInvoiceRecordByMixed($orderNum, $sid, $fid, $isM = 0, $isN = 0, $isSandBox = false, $orderType = -1)
    {
        $invoiceApi = new InvoiceApi();
        //判断订单是否开过票
        if ($isM) {
            $result = (new \Business\ElectronicInvoices\InvoiceApi())->getUserNowSys($sid);
            $prefix = 'NNFP_m_';
            if ($result['code'] == 200 && $result['data']) {
                $prefix = $result['data'] == 4 ? 'BWJS_m_' : 'NNFP_m_';
            }
            $tmpOrderNum = $prefix . $orderNum;

        } else {
            $tmpOrderNum = $orderNum;
        }

        $invoiceRes = $invoiceApi->checkInvoiceRecordByMixed($sid, [$tmpOrderNum], [], false, $isM);
        if ($invoiceRes['code'] != 200) {
            return $this->returnData($invoiceRes['code'], $invoiceRes['msg'], $invoiceRes['data']);
        }

        //非合并开票，订单被合并开票了 发票状态是开票中的 需要给提示
        if (!$isM && $invoiceRes['data'][0]['records_type'] != 1 && in_array($invoiceRes['data'][0]['invoice_status'], [1])) {
            return $this->returnData(203, '当前订单开票中，请勿重复操作');
        }

        $tmpRecord     = $invoiceRes['data'][0] ?? [];
        $orderType     = $tmpRecord['order_type'] ?? ($orderType != -1 ? $orderType : 1);
        $invoiceState  = $tmpRecord['invoice_state'] ?? 0;
        //$invoiceUrlArr = $tmpRecord['image_url'] ? json_decode($tmpRecord['image_url'], true) : '';
        $outTradeNo    = $tmpRecord['out_trade_no'] ?? '';
        //发票订单信息
        $originalOrderInfo = $tmpRecord['original_order_info'] ? json_decode($tmpRecord['original_order_info'], true) : [];

        //$invoiceUrl    = isset($invoiceUrlArr['url']) ? $invoiceUrlArr['url'] : $invoiceUrlArr;
        //$invoicePdfUrl = isset($invoiceUrlArr['pdf']) ? $invoiceUrlArr['pdf'] : $invoiceUrlArr;
        $invoiceUrl    = $tmpRecord['image_url'] ?? '';
        $invoicePdfUrl = $tmpRecord['image_pdf'] ?? '';
        $sysId         = (int)$tmpRecord['sys_id'] ?? 3;

        //没开过票的 获取开票记录的信息
        //共享租赁的需要重新赋值下
        if (empty($invoiceRes['data']) && $orderType == 3) {
            $sysId         = 3;
            $invoiceState  = 0;
            $invoiceUrl    = '';
            $invoicePdfUrl = '';
        } elseif (empty($invoiceRes['data']) && !$isM && $orderType != 3) {
            $invoiceRes = $invoiceApi->findDistributorInvoiceByOrdernum($orderNum, intval($sid), intval($fid));
            if ($invoiceRes['code'] != 200) {
                return $this->returnData($invoiceRes['code'], $invoiceRes['msg'], $invoiceRes['data']);
            }

            $sysId         = $invoiceRes['data']['sys_id'] ?? 3;
            $orderType     = $invoiceRes['data']['order_type'] ?? 1;
            $invoiceState  = $invoiceRes['data']['invoice_status'] ?? 0;
            $invoiceUrl    = $invoiceRes['data']['image_url'] ?? '';
            $invoicePdfUrl = '';
        }

        $invoiceDetail = [];
        $invoiceRecord = [
            'order_num'      => $tmpRecord['ordernum'] ?? '',
            'invoice_state'  => $invoiceState,
            'image_url'      => $invoiceUrl,
            'image_pdf_url'  => $invoicePdfUrl,
            'out_trade_no'   => $outTradeNo,
            'invoice_detail' => $invoiceDetail,
            'sys_id'         => $sysId,
            'sid'            => $sid,
            'fid'            => $fid,
        ];

        //未开票、已红冲或者开票失败的的需要能够重新开票
        if (in_array($invoiceState, [0, 4, 5]) || ($isM && $outTradeNo && in_array($invoiceState, [0, 1, 4, 5]))) {
            //判断订单号是否是合并开票的  如果是合并开票  需要将合并开票的商品信息（票名称、商品名、税率、税额）
            if (strpos($tmpOrderNum, '_m_') !== false) {
                $orderInfoRes = $this->getInvoiceOrderInfo($tmpOrderNum, '', '', '', $sid);
            } else {
                //平台订单号
                $platformOrderNum = $orderType == 1 ? $orderNum : '';
                //旅游券订单号
                $tvOrderNum   = $orderType == 2 ? $orderNum : '';
                //共享租赁订单号
                $tsOrderNum   = $orderType == 3 ? $orderNum : '';

                $orderInfoRes = $this->getInvoiceOrderInfo('', $platformOrderNum, $tvOrderNum, $tsOrderNum, $sid);
            }

            if ($orderInfoRes['code'] != 200 || empty($orderInfoRes['data'])) {
                return $this->returnData(204, '订单信息获取失败');
            }
            $orderInfo = $orderInfoRes['data'];

            if ($orderType != 3) {
                $ticketIds = array_column($orderInfo, 'tid');
                $javaApi   = new \Business\CommodityCenter\Ticket();
                $ticketArr = $javaApi->queryTicketInfoByIds($ticketIds, 'id,pid,title,tprice,batch_check');
                $tickets   = [];
                if (!empty($ticketArr)) {
                    foreach ($ticketArr as $ticket) {
                        $tickets[$ticket['ticket']['id']] = $ticket['ticket'];
                    }
                }

                $result = $invoiceApi->getInvoiceConfigByTicketIds($sid, $ticketIds);
                if ($result['code'] != 200) {
                    return $this->returnData($result['code'], $result['msg'], $result['data']);
                }
                // tid与配置的映射关系
                $tidConfigMapper = $result['data']['tidConfigMapper'];
                // 开票人相关配置
                $billerInfo = $result['data']['billerInfo'];
                // 公共开票信息
                $invoiceInfo = $result['data']['invoiceInfo'];
                $invoiceInfo = $invoiceInfo[0];
                // 配置列表
                $invoiceConf = $result['data']['invoiceConf'];
                $invoiceConf = array_column($invoiceConf, null, 'id');

                $ext         = $billerInfo['expand'] ? json_decode($billerInfo['expand'], true) : [];
                $dutyFree    = !empty($ext['duty_free']) ? intval($ext['duty_free']) : 0;
                //发票类型 0/普通发票（电子）：P   1/数电普票（电子）：PC
                $invoiceType = isset($ext['invoice_type']) ? explode(',', $ext['invoice_type']) : [($sysId == 4 ? 1 : 0)];
                $invoiceType = array_map(function ($v) {return intval($v);}, $invoiceType);

                //增加订单状态的判断 未使用、已过期、已取消、待确认、撤销、部分使用、删除、待出票 【非已使用 已完结的 撤改的】 不让开票
                $orderStatusArr = array_column($orderInfo, 'status');
                if (array_intersect($orderStatusArr, [0, 2, 3, 4, 6, 7, 9, 11])) {
                    return $this->returnData(204, '当前开票订单状态不可开票');
                }
            } else {
                $billerInfo = $this->getInvoiceApi()->getBillerConfig($sid);
                if ($billerInfo['code'] != 200 || empty($billerInfo['data']) || empty($billerInfo['data']['extension'])) {
                    return $this->returnData(204, '开票方配置信息不存在');
                }
                $billerInfo = $billerInfo['data'];
                $ext         = $billerInfo['expand'] ? json_decode($billerInfo['expand'], true) : [];
                $dutyFree    = !empty($ext['duty_free']) ? intval($ext['duty_free']) : 0;
                //发票类型 0/普通发票（电子）：P   1/数电普票（电子）：PC
                $invoiceType = isset($ext['invoice_type']) ? explode(',', $ext['invoice_type']) : [($sysId == 4 ? 1 : 0)];
                $invoiceType = array_map(function ($v) {return intval($v);}, $invoiceType);
                $extension   = $billerInfo['extension'] ? json_decode($billerInfo['extension'], true) : [];
                $invoiceConfig = $extension['shared_lease'];
                if (!$invoiceConfig) {
                    return $this->returnData(204, '共享租赁开票配置异常，请检查');
                }
                if ($invoiceConfig['product_tag']) {
                    $invoiceConfig['use_product_tag'] = true;
                }

                $invoiceInfo    = [];
                $enterPriseInfo = $this->getInvoiceApi()->getEnterpriseOpenRecordsList($sid, $invoiceConfig['billing_party']);
                if ($enterPriseInfo['code'] != 200 || empty($enterPriseInfo['data'])) {
                    return $this->returnData(204, '开票方配置信息不存在');
                }
                $invoiceInfo = $enterPriseInfo['data'][0];
            }

            //补充发票金额
            $invoiceRecord['invoice_amount'] = 0;
            foreach ($orderInfo as $order) {
                $inChain = array_filter($order['aids_money'], function ($item) use ($sid) {
                    return $item['aid'] == $sid;
                });
                if (!$inChain) {
                    return $this->returnData(203, '订单异常，不可开票');
                }
                $inChain = array_shift($inChain);
                $invoiceRecord['invoice_amount'] += $inChain['money'];
                if ($orderType != 3) {
                    $configId = $tidConfigMapper[$order['tid']];
                    $invoiceConfig = $invoiceConf[$configId];
                }

                $taxRate = !$dutyFree && $invoiceConfig['tax_rate'] > 0 ? number_format($invoiceConfig['tax_rate'] * 100, 2, '.', '') : 0;
                $goodsName = $invoiceConfig['use_product_tag'] ? $invoiceConfig['product_tag'] : ($orderType == 3 ? $order['title'] : $tickets[$order['tid']]['title']); //票名称
                $_tax = round($inChain['money'] * ($taxRate / (100 + $taxRate)) / 100, 2);
                $_money = round($inChain['money'] / 100, 2);
                if (in_array($sysId, [1, 3])) {
                    if ($orderType != 3) {
                        $_uuid = implode('-', [$goodsName, $invoiceConfig['spbm'], $taxRate, $invoiceConfig['favoured_policy'], $invoiceConfig['sd_favoured_policy']]);
                    } else {
                        $_uuid = implode('-', [$goodsName, $invoiceConfig['spbm'], $taxRate]);
                    }
                    if (isset($invoiceDetail[$_uuid])) {
                        $invoiceDetail[$_uuid]['money'] = bcadd($invoiceDetail[$_uuid]['money'], $_money, 2);
                        $invoiceDetail[$_uuid]['tax'] = bcadd($invoiceDetail[$_uuid]['tax'], $_tax, 2);
                    } else {
                        $invoiceDetail[$_uuid] = [
                            'goodsname' => $goodsName,
                            'tax_rate'  => $taxRate,
                            'tax'       => $_tax,
                            'money'     => $_money,
                            'duty_free' => $dutyFree,
                        ];
                    }
                } else {
                    $invoiceDetail[] = [
                        'goodsname' => $goodsName,
                        'tax_rate'  => $taxRate,
                        'tax'       => $_tax,
                        'money'     => $_money,
                        'duty_free' => $dutyFree,
                    ];
                }
            }
            $invoiceDetail = array_values($invoiceDetail);
            //补充发票金额
            $invoiceRecord['invoice_amount'] = round($invoiceRecord['invoice_amount'] / 100, 2);
            //组装开票方
            $invoiceRecord['order_num']       = strval($orderNum);
            $invoiceRecord['enterprise_name'] = $invoiceInfo['enterprise_name'];
            $invoiceRecord['tariff_number']   = $invoiceInfo['tariff_number'];
            $invoiceRecord['invoice_detail']  = $invoiceDetail;
            $invoiceRecord['invoice_type']    = $invoiceType;
            $invoiceRecord['order_type']      = $orderType;
            if ($isSandBox) {
                $invoiceRecord['tariff_number'] = '339901999999142';
            }

            //如果是合并开票的情况 需要查询一次开票结果
            if ($isM && $outTradeNo) {
                //先更新下合并开票的状态
                $invoiceCode = '';
                $invoiceNum  = '';
                $invoiceUrl  = '';
                $invoicePdf  = '';
                if ($invoiceState == 0) {
                    $upRes = $invoiceApi->updateInvoiceStatusByOrderNum($outTradeNo, $invoiceCode, $invoiceNum, $invoiceUrl, $invoicePdf, 1, $isM);
                    if ($upRes['code'] != 200) {
                        return $this->returnData($upRes['code'], $upRes['msg']);
                    }
                } else {
                    if ($sysId == 4) {
                        //如果百旺金穗云还没提交过开票 发票状态需要重置下
                        if (!isset($originalOrderInfo['tid'])) {
                            $invoiceState = 0;
                        } else {
                            //需要查询一次开票状态 如果没查到发票信息 就将状态重置成可开票
                            $invoiceRes = $invoiceApi->queryInvoiceBWJSResult($sid, $originalOrderInfo['tid'], $outTradeNo);
                            if ($invoiceRes['code'] != 200) {
                                $invoiceState = 0;
                            }
                        }
                    } else {
                        //查询一次开票状态  如果是开票中的
                        $invoiceRes = $invoiceApi->queryInvoiceResult($invoiceRecord['tariff_number'], [$outTradeNo], [], 0, $isSandBox, $sysId);
                        //没找到发票
                        if ($invoiceRes['code'] == 204 || $invoiceRes['code'] == 500) {
                            $invoiceState = 0;
                        }
                    }
                }
            } else {
                //重置开票状态
                $invoiceState = 0;
            }

            //重置状态
            $invoiceRecord['invoice_state'] = $invoiceState;
        }

        if ($invoiceState == 0 && $isN) {
            //通过校验资质认证 获取固定抬头名称
            $checkRes = $invoiceApi->checkCerificationInfo($sid, $fid);
            if ($checkRes['code'] != 200) {
                return $this->returnData($checkRes['code'], $checkRes['msg']);
            }
        }

        $invoiceRecord['company_name']   = empty($checkRes['data']['subjectFullName']) ? '': $checkRes['data']['subjectFullName'];
        $invoiceRecord['company_type']   = empty($checkRes['data']['subjectType']) ? 0: $checkRes['data']['subjectType'];
        $invoiceRecord['company_limit']  = empty($checkRes['data']['subjectFullName']) ? 0 : 1;
        $invoiceRecord['invoice_detail'] = $invoiceDetail;

        return $this->returnData(200, '获取成功', $invoiceRecord);
    }

    /**
     * 电子发票中转页获取开票所需信息
     *
     * @param  string  $orderNum  订单号
     * @param  int  $sid  开票方商家id
     * @param  int  $fid  开票人操作id
     *
     * @return array
     */
    public function getInvoiceDetailByMixed($orderNum, $sid, $fid, $isN = 0, $orderType = -1, $source = '')
    {
        if (!$orderNum || !$sid) {
            return $this->returnData(203, '参数有误');
        }

        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();
        $result     = $invoiceApi->getInvoiceDetailByMixed($orderNum, $sid, $fid, $isN, $orderType, $source);
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 电子发票中转页获取开票所需信息
     *
     * @param  string  $orderNum  订单号
     * @param  int  $sid  开票方商家id
     * @param  int  $fid  开票人操作id
     *
     * @return array
     */
    public function getInvoiceRecordDetailByMixed($orderNum, $sid, $fid, $isMerge, $sysId, $extData = [])
    {
        if (!$orderNum || !$sid) {
            return $this->returnData(203, '参数有误');
        }

        $invoiceApi = new \Business\ElectronicInvoices\InvoiceApi();
        $result     = $invoiceApi->getInvoiceRecordDetailByMixed($orderNum, $sid, $fid, $isMerge, $sysId);
        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $result['data']['invoice_form_url'] = '';
        $source = $extData['source'] ?? 'platform';
        if ($result['data']['invoice_state'] == 5 && in_array($source, ['sms', 'hardware'])) {
            //扫码开票失败需要重新开票，返回重新开票页面地址
            if ($result['data']['records_type'] != 2) {
                $extData['major'] = $result['data']['ordernum'];
            } else if (empty($extData['major']) || !in_array($extData['major'], $result['data']['son_list'])) {
                $extData['major'] = $result['data']['son_list'][0] ?? '';
            }
            if (!empty($extData['major'])) {
                $result['data']['invoice_form_url'] = $this->getMultipleInvoiceUrl($extData['major'], $sid, $source);
            }
        }
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    public function getMultipleInvoiceUrl(string $orderNum, int $sid, string $source): string
    {
        $invoiceFormUrl = '';
        try {
            // 获取订单信息(合并支付订单号)
            try {
                $orderInfo = (new BizProductOrder())->detailInfo($sid, $orderNum);
            } catch (Exception $e) {}
            if (empty($orderInfo)) {
                return $invoiceFormUrl;
            }

            // 获取开票H5地址
            $invoiceFormUrlResult = $this->getInvoiceApi()->getOpenInvoiceUrl($sid, $orderInfo['itemSku'], $orderNum, false, 0, $source);
            $invoiceFormUrl = $invoiceFormUrlResult['data'] ?? '';

            // 获取开票系统
            $userNowSysId = $this->getInvoiceApi()->getUserNowSys($sid);
            // 线下多选开票仅支持航信普电、数电
            if (!in_array($userNowSysId['data'], [1, 3])) {
                return $invoiceFormUrl;
            }

            // 不存在合并支付订单，返回开票H5地址
            $cmbId = $orderInfo['tradeOrderId'];
            if (!$cmbId) {
                return $invoiceFormUrl;
            }

            // 获取合并支付订单关联订单号
            try {
                $orderList = (new FeignClient())->listByTradeOrderId($cmbId);
            } catch (Exception $e) {}
            // 合并支付订单关联订单号为空，返回开票H5地址
            if (empty($orderList)) {
                return $invoiceFormUrl;
            }
            $orderNumArr = array_column($orderList, 'productOrderId');

            // 校验订单是否能开票
            $canInvoiceOrderNumArr = $this->getInvoiceApi()->getCanInvoiceOrderList($orderNumArr, $sid, $source);
            // 无可开票订单，返回开票H5地址
            if (empty($canInvoiceOrderNumArr)) {
                return $invoiceFormUrl;
            }
            // 只有1个可开票订单，返回开票H5地址
            if (count($canInvoiceOrderNumArr) == 1) {
                return $invoiceFormUrl;
            }

            // 使用正则表达式匹配域名
            preg_match('/(?:https?:\/\/)?([^\/]+)/', $invoiceFormUrl, $matches);
            $domain = $matches[1];
            return sprintf(
                '//%s/wx/multipleInvoice.html#/?cmb_id=%s&sid=%s&source=%s&ordernum=%s',
                $domain,
                base64_encode($cmbId), // url_sms只能加密纯数字，所以这里用base64加密
                \Library\MessageNotify\OrderNotify::url_sms($sid),
                base64_encode($source),
                \Library\MessageNotify\OrderNotify::url_sms($orderNum)
            );
        } catch (Exception $e) {
        }
        return $invoiceFormUrl;
    }

    /**
     * 处理合并订单开票信息数据
     * @deprecated 【电子发票二期】供应商开票废弃
     * @param  array $orderInfos 订单信息
     * @param  array $ticketInfo 订单关联票信息
     *
     * @return  array
     */
//    private function _handleMergeUpdateData(int $sid, array $orderInfos, array $ticketInfo, $spbm = '', $taxRate = 0, $dutyFree = 0)
//    {
//        $exist               = [];
//        $zdyIds              = [];
//        $zdyName             = [];
//        $stayOpenInvoiceData = [];
//        $invoiceApi          = new InvoiceApi();
//        //获取自定义商品名称
//        $tidArr     = array_unique(array_column($orderInfos, 'tid'));
//        $zdyNameRes = $invoiceApi->productDefinitionNameList(0, 0, $tidArr, '', '', 0, 0);
//        if ($zdyNameRes['code'] == 200 && $zdyNameRes['data']['list']) {
//            $zdyNameRes = $zdyNameRes['data']['list'];
//            $exist      = array_reduce(array_column($zdyNameRes, 'tids'), 'array_merge', array());
//            foreach ($zdyNameRes as $item) {
//                foreach ($item['tids'] as $v) {
//                    $zdyIds[$v]  = $item['id'];
//                    $zdyName[$v] = ['name' => $item['name'], 'tax_rate' => !$dutyFree ? $item['tax_rate'] : 0, 'spbm' => $item['spbm']];
//                }
//            }
//        }
//
//        foreach ($orderInfos as $item) {
//            $key = 'zdy_';
//            //有自定义商品名称 订单合并
//            if (in_array($item['tid'], $exist)) {
//                $k = $key . $zdyIds[$item['tid']];
//                if (isset($stayOpenInvoiceData[$k])) {
//                    $stayOpenInvoiceData[$k]['je'] = $stayOpenInvoiceData[$k]['je'] + ($item['totalmoney']);
//                } else {
//                    $stayOpenInvoiceData[$k] = [
//                        'je'        => $item['totalmoney'],
//                        'spmc'      => $zdyName[$item['tid']]['name'] ?? '',
//                        'tax_rate'  => $zdyName[$item['tid']]['tax_rate'] ?? '',
//                        'spbm'      => $zdyName[$item['tid']]['spbm'] ?? '',
//                        'duty_free' => intval($zdyName[$item['tid']]['tax_rate'] * 100) == 0 ? 1 : 0,   //1免税 2非免税
//                    ];
//                }
//            } else {
//                if (isset($stayOpenInvoiceData[$item['tid']])) {
//                    $stayOpenInvoiceData[$item['tid']]['je'] = $stayOpenInvoiceData[$item['tid']]['je'] + ($item['totalmoney']);
//                } else {
//                    $stayOpenInvoiceData[$item['tid']] = [
//                        'je'        => $item['totalmoney'],
//                        'spmc'      => $ticketInfo[$item['tid']]['title'],
//                        'tax_rate'  => $taxRate,
//                        'spbm'      => $spbm,
//                        'duty_free' => intval($taxRate * 100) == 0 ? 1 : 0,   //1免税 2非免税
//                    ];
//                }
//            }
//        }
//
//        return $stayOpenInvoiceData;
//    }
}

