<?php
/**
 * 电子发票服务相关
 */

namespace Business\ElectronicInvoices;

use Business\JavaApi\Order\Query\OrderPage;
use Business\NewJavaApi\DistributionCenter\PlatformSaleList;
use Business\NewJavaApi\Order\ProductOrder\BizProductOrder;
use Business\NewJavaApi\Order\ProductOrder\FeignClient;
use Exception;
use Library\Cache\Cache;
use Library\Cache\CacheRedis;
use Model\ElectronicInvoices\Invoice as invoiceModel;
use Pimple\Container;

class InvoiceApi extends ECBase
{
    protected $container;

    //日志目录
    private $_debugLogDir = 'electronic/invoice/';

    public function __construct()
    {
        //composer自动加载
        \Library\Tools\Helpers::composerAutoload();

        //模型容器
        $this->container = new Container();
        $this->container['platformSaleListBiz'] = function () {
            return new PlatformSaleList();
        };
        $this->container['distributionProductBiz'] = function () {
            return new \Business\NewJavaApi\DistributionCenter\Product();
        };
    }

    /**
     * 新增企业开票信息
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  array  $invoiceInfo  开票企业信息
     * @param  int  $sid  供应商id
     * @param  int  $operatorId  操作员id
     *
     * @return array
     */
    public function addEnterpriseInvoice(array $invoiceInfo, int $sid, int $operatorId, int $id = 0): array
    {
        if (!$invoiceInfo || !$sid || !$operatorId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'invoice_info' => $invoiceInfo,
            'sid'          => $sid,
            'operator_id'  => $operatorId,
            'id'           => $id,
        ];

        $result = $this->ExternalCodeCall(self::__ADD_ENTERPRISE_INVOICE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 删除企业开票信息 （软删除）
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $id  主键id
     * @param  int  $operatorId  操作员ID
     *
     * @return array
     */
    public function deleteEnterpriseInvoice(int $id, int $operatorId): array
    {
        if (!$id || !$operatorId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'id'          => $id,
            'operator_id' => $operatorId,
        ];

        $result = $this->ExternalCodeCall(self::__DELETE_ENTERPRISE_INVOICE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 供应商下企业的开通记录
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  string  $enterpriseName  企业名称
     * @param  string  $enterpriseCode  企业开票六位码
     * @param  string  $tariffNumber  企业税号
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getEnterpriseOpenRecordsTrade(int $sid, string $enterpriseName, string $enterpriseCode, string $tariffNumber): array
    {
        if (!$sid || !$enterpriseName || !$enterpriseCode || !$tariffNumber) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'enterprise_name' => $enterpriseName,
            'enterprise_code' => $enterpriseCode,
            'tariff_number'   => $tariffNumber,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_OPEN_RECORD_TRADE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 通过税号修改企业开票信息
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  string  $tariffNumber  企业税号
     * @param  string  $accessToken  企业access_token
     * @param  string  $refreshToken  企业refresh_token
     * @param  string  $userId  企业在诺诺对应的用户id
     * @param  int  $sid  供应商id
     * @param  int  $expireTime  accessToken过期时间
     *
     * @return array
     */
    public function editEnterpriseInfoByTariffNumber(string $tariffNumber, string $accessToken, string $refreshToken, string $userId, int $sid = 0, int $expireTime = 0, int $sysId = 0): array
    {
        if (!$tariffNumber || !$accessToken || !$refreshToken) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'tariff_number' => $tariffNumber,
            'access_token'  => $accessToken,
            'refresh_token' => $refreshToken,
            'user_id'       => $userId,
            'sid'           => $sid,
            'expire_time'   => $expireTime,
            'sys_id'         => $sysId,
        ];

        $result = $this->ExternalCodeCall(self::__EDIT_ENTERPRISE_INFO_BY_TARIFF_NUMBER_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票企业开通记录列表
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  string  $tariffNumber  供应商id
     * @param  string  $enterpriseName  供应商id
     * @param  int  $isAuth  供应商id
     * @param  int  $page  当天页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function getEnterpriseOpenRecords(int $sid, string $tariffNumber = '', string $enterpriseName = '', int $isAuth = -1, int $page = 1, int $size = 10): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'tariff_number'   => $tariffNumber,
            'enterprise_name' => $enterpriseName,
            'is_auth'         => $isAuth,
            'page'            => $page,
            'size'            => $size,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_OPEN_RECORDS_FUN__, $data);
        pft_log('invoice/getEnterpriseOpenRecords', json_encode(compact('data', 'result')));
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票企业开通记录列表开票记录
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberId  搜索用户id
     * @param  string  $tariffNumber  供应商id
     * @param  string  $orderNum  搜索订单号
     * @param  int  $beginTime  开始时间戳
     * @param  int  $endTime  结束时间戳
     * @param  int  $page  当天页数
     * @param  int  $size  每页条数
     * @param  int  $tip  开票入口标识 1供应商开票记录列表请求  2分销商订单开票记录
     * @param  int  $handle -1=全部 1=普通开票 2=合并开票
     * @param  int  $orderType 1=普通开票 2=旅游券 3=共享租赁 4=托管开票
     * @return array
     */
    public function getEnterpriseInvoiceRecords(int $sid, int $beginTime = 0, int $endTime = 0, int $memberId = 0, 
        string $tariffNumber = '', string $orderNum = '',  string $invoiceNum = '', int $sysId = -1,int $handle = -1, 
        string $enterpriseName = '', int $status = -1, int $page = 1, int $size = 10, int $tip = 1, $source = '', $orderType = -1): array
    {
        if (!$sid && !$memberId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'begin_time'      => $beginTime,
            'end_time'        => $endTime,
            'member_id'       => $memberId,
            'tariff_number'   => $tariffNumber,
            'ordernum'        => $orderNum,
            'page'            => $page,
            'size'            => $size,
            'invoice_num'     => $invoiceNum,
            'handle'          => $handle,
            'enterprise_name' => $enterpriseName,
            'status'          => $status,
            'sys_id'          => $sysId,
            'tip'             => $tip,
            'source'          => $source,
            'order_type'      => $orderType,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_INVOICE_RECORDS_FUN__, $data);
        //echo '<pre>';print_r($result);die;
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票企业开通记录
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  string  $tariffNumber  供应商id
     *
     * @return array
     */
    public function getEnterpriseOpenRecordsByMixed(int $sid, string $tariffNumber = ''): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'           => $sid,
            'tariff_number' => $tariffNumber,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_OPEN_RECORDS_BY_MIXED_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 查找订单号的开票记录
     * <AUTHOR>  Li
     * @date  2021-06-11
     * @param  int $sid 供应商id
     * @param  array  $ordernumArr  订单号数组
     * @param  array  $memberIdArr  用户id数组
     * @param  bool  $isCheckPending  是否检测待开发票数据（百旺用）
     *
     * @return array
     */
    public function checkInvoiceRecordByMixed(int $sid, array $ordernumArr = [], array $memberIdArr = [], bool $isCheckPending = false, bool $isMerge = false, array $excludeStatus = [4]): array
    {
        if (empty($ordernumArr) && empty($memberIdArr)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'              => $sid,
            'ordernum_arr'     => $ordernumArr,
            'member_id_arr'    => $memberIdArr,
            'is_check_pending' => $isCheckPending,
            'is_merge'         => $isMerge,
            'exclude_status'   => $excludeStatus,
        ];
        $result = $this->ExternalCodeCall(self::__CHECK_INVOICE_RECORD_BY_MIXED_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存开票记录
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberId  用户id
     * @param  string  $ordernum  订单号
     * @param  string  $outTradeNo  订单流水号
     * @param  string  $serialNum  该订单在诺诺网的流水号
     * @param  string  $ordertel  取票人电话
     * @param  string  $personid  身份证
     * @param  string  $enterpriseName  企业名称
     * @param  string  $enterpriseCode  企业开票六位码
     * @param  string  $tariffNumber  企业税号
     * @param  string  $imageUrl  发票图片地址
     * @param  int  $addTime  开票时间
     * @param  int  $operatorId  操作员ID
     * @param  string  $invoiceCode  发票代码
     * @param  string  $invoiceNum  发票号码
     *
     * @return array
     */
    public function saveInvoiceRecord(int $sid, int $memberId, string $ordernum, string $outTradeNo, string $serialNum, string $ordertel, string $personid = '', string $enterpriseName, string $enterpriseCode, string $tariffNumber, string $imageUrl, int $addTime, int $operatorId = 0, string $invoiceCode, string $invoiceNum): array
    {
        if (!$sid || !$ordernum || !$serialNum) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'member_id'       => $memberId,
            'ordernum'        => $ordernum,
            'out_trade_no'    => $outTradeNo,
            'serial_num'      => $serialNum,
            'ordertel'        => $ordertel,
            'personid'        => $personid,
            'enterprise_name' => $enterpriseName,
            'enterprise_ode'  => $enterpriseCode,
            'tariff_number'   => $tariffNumber,
            'image_url'       => $imageUrl,
            'add_time'        => $addTime,
            'operator_id'     => $operatorId,
            'invoice_code'    => $invoiceCode,
            'invoice_num'     => $invoiceNum,
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_INVOICE_RECORD_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取所有需要更新access_token的企业
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @return array
     */
    public function getNeedRefreshTokenEnterprise(): array
    {
        $result = $this->ExternalCodeCall(self::__GET_NEED_REFRESH_TOKEN_ENTERPRISE_FUN__, []);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存开票配置
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商ID
     * @param  int  $billingParty  开票方
     * @param  int  $id  配置id
     * @param  int  $tid  门票ID
     * @param  string  $channel  开票渠道 多个以逗号隔开
     * @param  int  $operatorId  操作员ID
     * @param  int  $validityPeriod  有效期
     * @param  int  $status  配置状态 0关闭 1开启
     * @param  int  $validityType  开票有效期 0长期有效 1有期限
     * @param  float  $taxRate  开票税率
     * @param  string  $spbm  商品编码
     *
     * @return array
     */
    public function saveInvoiceConfig(int $sid, int $billingParty, int $tid, int $id = 0, string $channel = '', int $operatorId = 0, int $validityPeriod = 0, int $status = 0, int $validityType = 0, float $taxRate = 0.0000, string $spbm = '', int $distribution = 0, string $otaUserList = ''): array
    {
        if (!$sid || !$billingParty || !$tid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'billing_party'   => $billingParty,
            'id'              => $id,
            'tid'             => $tid,
            'channel'         => $channel,
            'operator_id'     => $operatorId,
            'validity_period' => $validityPeriod,
            'status'          => $status,
            'alidity_type'    => $validityType,
            'tax_rate'        => $taxRate,
            'spbm'            => $spbm,
            'distribution'    => $distribution,
            'ota_user_list'   => $otaUserList,
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_INVOICE_CONFIG_FUN__, $data);
        // var_dump($result);die;

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票企业开票配置
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  array  $ticketIdArr  门票数组
     *
     * @return array
     */
    public function getInvoiceConfigList(int $sid, array $ticketIdArr = []): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'           => $sid,
            'ticket_id_arr' => $ticketIdArr,
        ];

        $result = $this->ExternalCodeCall(self::__GET_INVOICE_CONFIG_LIST_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 批量保存开票配置
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  array  $idArr  配置id数组
     * @param  int  $sid  供应商id
     * @param  int  $status  状态
     *
     * @return array
     */
    public function saveInvoiceConfigBatch(array $idArr, int $sid, int $status = 0): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'    => $sid,
            'status' => $status,
            'id_arr' => $idArr,
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_INVOICE_CONFIG_BATCH_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取某个企业开通记录
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商ID
     * @param  int  $id  记录id
     *
     * @return array
     */
    public function getEnterpriseOpenRecordsList(int $sid = 0, int $id = 0): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
            'id'  => $id,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_OPEN_RECORDS_LIST_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取某个企业开通记录
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  array  $idArr  记录id
     *
     * @return array
     */
    public function getEnterpriseInfoByIdArr(int $sid, array $idArr): array
    {
        if (!$idArr || !$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'id_arr' => $idArr,
            'sid'    => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_INFO_BY_ID_ARR_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票企业开票配置
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $tid  门票id
     * @param  int  $sid  供应商id
     * @param  bool  $online  是否只获取开启的配置项
     *
     * @return array
     */
    public function getInvoiceConfigByTid(int $tid, int $sid, bool $online = false): array
    {
        if (!$tid || !$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'tid'    => $tid,
            'sid'    => $sid,
            'online' => $online,
        ];

        $result = $this->ExternalCodeCall(self::__GET_INVOICE_CONFIG_BY_TID_FUN__, $data);
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 通过企业名称 模糊搜索供应商下企业的开通记录
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  string  $enterpriseName  企业名称
     *
     * @return array
     */
    public function getEnterpriseOpenRecordsByName(int $sid, string $enterpriseName): array
    {
        if (!$sid || !$enterpriseName) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'enterprise_name' => $enterpriseName,
        ];

        $result = $this->ExternalCodeCall(self::__GET_ENTERPRISE_OPEN_RECORDS_BY_NAME_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存开票人相关信息
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  int  $id  配置id
     * @param  string  $drawer  开票人
     * @param  string  $payer  收款人
     * @param  string  $reviewer  复核人
     *
     * @return array
     */
    public function saveBillerConfig(int $sid, int $id = 0, string $drawer = '', string $payer = '', string $reviewer = '', array $expand = []): array
    {
        if (!$sid || !$drawer) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'      => $sid,
            'id'       => $id,
            'drawer'   => $drawer,
            'payer'    => $payer,
            'reviewer' => $reviewer,
            'expand'   => $expand,
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_BILLER_CONFIG_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票人相关信息
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     *
     * @return array
     */
    public function getBillerConfig(int $sid): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__GET_BILLER_CONFIG_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 添加分销商开票信息
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  订单供应商ID
     * @param  int  $did  订单购买人ID
     * @param  int  $tid  订单门票ID
     * @param  string  $orderNum  订单号
     * @param  int  $orderTime  下单时间
     *
     * @return array
     */
    public function addDistributorInvoice(int $sid, int $did, int $tid, string $orderNum, int $orderTime): array
    {
        if (!$sid || !$did || !$tid || !$orderNum || !$orderTime) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'        => $sid,
            'did'        => $did,
            'tid'        => $tid,
            'ordernum'   => $orderNum,
            'order_time' => $orderTime,
        ];

        $result = $this->ExternalCodeCall(self::__ADD_DISTRIBUTOR_INVOICE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 更新分销商开票信息
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  订单供应商ID
     * @param  int  $did  订单购买人ID
     * @param  string  $orderNum  订单号
     * @param  string  $imgUrl  电子发票地址
     *
     * @return array
     */
    public function updateDistributorInvoice(int $sid, int $did, string $orderNum, string $imgUrl): array
    {
        if (!$sid || !$did || !$orderNum || !$imgUrl) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'      => $sid,
            'did'      => $did,
            'ordernum' => $orderNum,
            'img_url'  => $imgUrl,
        ];

        $result = $this->ExternalCodeCall(self::__UPDATE_DISTRIBUTOR_INVOICE_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取可开票列表
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $memberId  当前登录用户id
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     * @param  int  $sid  供应商id
     * @param  string  $orderNum  订单号
     * @param  int  $beginTime  下单开始时间
     * @param  int  $endTime  下单结束时间
     * @param  int  $type  开票状态 0全部 1未开票 2已开票
     *
     * @return array
     */
    public function getInvoicableList(int $fid, int $page = 1, int $size = 10, int $memberId = 0, int $sid = 0,
        string $orderNum = '', int $beginTime = 0, int $endTime = 0, int $type = -1, string $taxRate = '',
        int $status = -1, int $start = 0, int $end = 0, string $tariffNumber = '', string $tariffName = '',
        string $spbm = '', int $endTimeType = 0, int $tid = 0, string $productTag = ''): array
    {
        if (!$sid && !$memberId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'fid'           => $fid,
            'page'          => $page,
            'size'          => $size,
            'member_id'     => $memberId,
            'sid'           => $sid,
            'ordernum'      => $orderNum,
            'begin_time'    => $beginTime,
            'end_time'      => $endTime,
            'tax_rate'      => $taxRate,
            'type'          => $type,
            'status'        => $status,
            'start'         => $start,
            'end'           => $end,
            'tariff_number' => $tariffNumber,
            'tariff_name'   => $tariffName,
            'spbm'          => $spbm,
            'end_time_type' => $endTimeType,
            'tid'           => $tid,
            'product_tag'   => $productTag,
        ];

        $result = $this->ExternalCodeCall(self::__GET_INVOICE_ABLE_LIST_V2_FUN__, $data);
         //echo '<pre>';print_r($result);die;
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 检测订单号是否写入过
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  string  $orderNum  订单号
     *
     * @return array
     */
    public function findDistributorInvoiceByOrdernum(string $orderNum, int $sid = 0, int $did = 0): array
    {
        if (!$orderNum) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'ordernum' => $orderNum,
            'sid'      => $sid,
            'did'      => $did,
        ];

        $result = $this->ExternalCodeCall(self::__FIND_DISTRIBUTOR_INVOICE_BY_ORDERNUM_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存开票记录
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberId  用户id
     * @param  string  $ordernum  订单号
     * @param  string  $outTradeNo  订单流水号
     * @param  string  $serialNum  该订单在诺诺网的流水号
     * @param  string  $ordertel  取票人电话
     * @param  string  $personid  身份证
     * @param  string  $enterpriseName  企业名称
     * @param  string  $enterpriseCode  企业开票六位码
     * @param  string  $tariffNumber  企业税号
     * @param  string  $imageUrl  发票图片地址
     * @param  int  $addTime  开票时间
     * @param  int  $operatorId  操作员ID
     * @param  string  $invoiceCode  发票代码
     * @param  string  $invoiceNum  发票号码
     *
     * @return array
     */
    public function addEnterpriseInvoiceRecords(int $sid, int $memberId, string $ordernum, string $outTradeNo, string $serialNum, string $ordertel, string $personid = '', string $enterpriseName, string $enterpriseCode, string $tariffNumber, string $imageUrl, int $addTime, int $operatorId = 0, string $invoiceCode, string $invoiceNum, array $originalOrderInfo = []): array
    {
        if (!$sid || !$ordernum || !$serialNum) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'                 => $sid,
            'member_id'           => $memberId,
            'ordernum'            => $ordernum,
            'out_trade_no'        => $outTradeNo,
            'serial_num'          => $serialNum,
            'ordertel'            => $ordertel,
            'personid'            => $personid,
            'enterprise_name'     => $enterpriseName,
            'enterprise_ode'      => $enterpriseCode,
            'tariff_number'       => $tariffNumber,
            'image_url'           => $imageUrl,
            'add_time'            => $addTime,
            'operator_id'         => $operatorId,
            'invoice_code'        => $invoiceCode,
            'invoice_num'         => $invoiceNum,
            'original_order_info' => $originalOrderInfo,
        ];

        $result = $this->ExternalCodeCall(self::__ADD_ENTERPRISE_INVOICE_RECORDS_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 校验该笔订单是否能开票
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  array  $orderNumArr  订单号数组
     * @param  bool  $isInsertCheck  是否写入过期数据
     * @param  string  $source 来源 platform
     *
     * @return array
     */
    public function checkInvoiceAuth(array $orderNumArr, int $sid = 0, bool $isInsertCheck = false, $source = 'platform'): array
    {
        if (!$orderNumArr || !is_array($orderNumArr)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'ordernum_arr'    => $orderNumArr,
            'is_insert_check' => $isInsertCheck,
            'source'          => $source,
            'sid'             => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__CHECK_INVOICE_AUTH_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取企业扫码开票链接地址
     *
     * <AUTHOR>  Li
     * @date  2021-06-14
     *
     * @param  int  $sid  供应商id
     * @param  int  $tid  用户id
     * @param  string  $orderNum  订单号
     * @param  bool  $isTransit  是否中转页面请求 （百旺用）
     * @param  int  $invoiceType 开票类型
     *
     * @return array
     */
    public function getOpenInvoiceUrl(int $sid, int $tid, string $orderNum, bool $isTransit = false, int $invoiceType = 0, string $source = 'platform', $fid = 0, $isNeedCerifi = 0, $orderType = -1, $extData = []): array
    {
        if (!$sid || ($orderType != 3 && !$tid) || !$orderNum) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'            => $sid,
            'tid'            => $tid,
            'ordernum'       => $orderNum,
            'is_transit'     => $isTransit,
            'invoice_type'   => $invoiceType,
            'source'         => $source,
            'open_user_id'   => $fid,
            'is_need_cerifi' => $isNeedCerifi,
            'order_type'     => $orderType,
            'extData'        => $extData,
        ];

        $result = $this->ExternalCodeCall(self::__GET_OPEN_INVOICE_URL_FUN__, $data);
        pft_log('invoice/getOpenInvoiceUrl', json_encode($result, JSON_UNESCAPED_UNICODE));
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取多选订单页面地址（无登录态，线下扫码开票使用)
     * @doc http://axure.12301.test/hsm/应用/电子发票/4.5 合并开票优化/#id=x2fyra&p=流程图&g=1
     * @throws Exception
     */
    public function getMultipleInvoiceUrl(string $orderNum, int $sid, string $source, int $tid): string
    {
        $exitsInvoiceUrl = $this->getExitsInvoiceUrlByOrderNum($sid, $orderNum, $tid);
        if ($exitsInvoiceUrl) {
            return $exitsInvoiceUrl;
        }

        // 获取订单信息
        try {
            $orderInfo = (new BizProductOrder())->detailInfo($sid, $orderNum);
        } catch (Exception $e) {}
        if (empty($orderInfo)) {
            throw new Exception('订单信息获取失败');
        }

        // 获取散客支付金额
        $queryRes = \Business\JavaApi\Order\Query\Container::query('orderQuery', 'completeOrder', [$orderNum]);
        if (empty($queryRes['data'])) {
            throw new Exception('获取支付金额失败');
        }
        $requestAmount = $queryRes['data'][0]['totalmoney'];
        if ($requestAmount <= 0) {
            throw new Exception('开票金额为0，无法开票');
        }

        // 获取开票H5地址
        $invoiceFormUrlResult = $this->getOpenInvoiceUrl($sid, $tid, $orderNum, false, 0, $source);
        if (!$invoiceFormUrlResult['data']) {
            throw new Exception($invoiceFormUrlResult['msg']);
        }
        $invoiceFormUrl = $invoiceFormUrlResult['data'];

        // 获取开票系统
        $userNowSysRes = $this->getUserNowSys($sid);
        if ($userNowSysRes['code'] != 200) {
            throw new Exception('获取系统失败');
        }
        // 线下多选开票仅支持航信普电、数电
        if (!in_array($userNowSysRes['data'], [1, 3])) {
            return $invoiceFormUrl;
        }

        // 不存在合并支付订单，返回开票H5地址
        $cmbId = $orderInfo['tradeOrderId'];
        if (!$cmbId) {
            return $invoiceFormUrl;
        }

        // 获取合并支付订单关联订单号
        try {
            $orderList = (new FeignClient())->listByTradeOrderId($cmbId);
        } catch (Exception $e) {
            throw new Exception('关联订单信息获取失败');
        }
        // 合并支付订单关联订单号为空，返回开票H5地址
        if (empty($orderList)) {
            return $invoiceFormUrl;
        }
        $orderNumArr = array_column($orderList, 'productOrderId');

        // 校验订单是否能开票
        $canInvoiceOrderNumArr = $this->getCanInvoiceOrderList($orderNumArr, $sid, $source);
        // 无可开票订单，返回开票H5地址
        if (empty($canInvoiceOrderNumArr)) {
            return $invoiceFormUrl;
        }
        // 只有1个可开票订单，返回开票H5地址
        if (count($canInvoiceOrderNumArr) == 1) {
            return $invoiceFormUrl;
        }

        // 使用正则表达式匹配域名
        preg_match('/(?:https?:\/\/)?([^\/]+)/', $invoiceFormUrl, $matches);
        $domain = $matches[1];
        return sprintf(
            '//%s/wx/multipleInvoice.html#/?cmb_id=%s&sid=%s&source=%s&ordernum=%s',
            $domain,
            base64_encode($cmbId), // url_sms只能加密纯数字，所以这里用base64加密
            \Library\MessageNotify\OrderNotify::url_sms($sid),
            base64_encode($source),
            \Library\MessageNotify\OrderNotify::url_sms($orderNum)
        );
    }

    /**
     * 查询订单是否已存在开票记录，如果存在，返回已有开票地址
     * H5扫码开票，若对应订单存在开票记录，需要根据最后提交的开票记录状态，展示对应的页面
     * @throws Exception
     */
    public function getExitsInvoiceUrlByOrderNum(int $sid, string $orderNum, int $tid = 0, $excludeStatus = [4]): string
    {
        $checkInvoiceRecordByMixedRes = $this->checkInvoiceRecordByMixed($sid, [$orderNum], [], false, false, $excludeStatus);
        if ($checkInvoiceRecordByMixedRes['code'] != 200) {
            throw new Exception($checkInvoiceRecordByMixedRes['msg']);
        }
        if (empty($checkInvoiceRecordByMixedRes['data'])) {
            return '';
        }
        $record = $checkInvoiceRecordByMixedRes['data'][0];
        if ($record['records_type'] == 1) { // 1普通开票
            if (!$tid) {
                $orderInfo = (new BizProductOrder())->detailInfo($sid, $orderNum);
                if (empty($orderInfo)) {
                    throw new Exception('订单信息获取失败');
                }
                $tid = $orderInfo['itemSku'];
            }
            $invoiceUrl = $this->getOpenInvoiceUrl($sid, $tid, $orderNum);
            if ($invoiceUrl['code'] != 200) {
                throw new Exception('获取开票地址异常: ' . $invoiceUrl['msg']);
            }
            return $invoiceUrl['data'];
        }
        $serialNum = $record['serial_num'];
        $invoiceUrl = $this->getMergeOpenInvoiceUrl($sid, [], false, -1, 0, $serialNum, 0, '', ['major' => $orderNum]);
        if ($invoiceUrl['code'] != 200) {
            throw new Exception('获取已有开票地址异常: ' . $invoiceUrl['msg']);
        }
        return $invoiceUrl['data'];
    }

    /**
     * 获取能开票的订单信息
     *
     * @param array $orderNumArr
     * @param int $sid
     * @param string $source
     * @return array
     * @throws Exception
     */
    public function getCanInvoiceOrderList(array $orderNumArr, int $sid, string $source): array
    {
        // 获取能开票的订单信息
        $checkRes = $this->checkInvoiceAuth($orderNumArr, $sid, false, $source);
        if ($checkRes['code'] != 200) {
            throw new Exception($checkRes['data']['msg']);
        }
        $canInvoiceOrderNumArr = [];
        foreach ($checkRes['data'] as $orderNum => $checkInfo) {
            if ($checkInfo['code'] == 200) {
                $canInvoiceOrderNumArr[] = $orderNum;
            }
        }
        if (empty($canInvoiceOrderNumArr)) {
            return [];
        }

        // 剔除已合并开票的订单
        $checkInvoiceRecordByMixedRes = $this->checkInvoiceRecordByMixed($sid, $canInvoiceOrderNumArr);
        if ($checkInvoiceRecordByMixedRes['code'] != 200) {
            throw new Exception($checkInvoiceRecordByMixedRes['msg']);
        }
        if (empty($checkInvoiceRecordByMixedRes['data'])) {
            return $canInvoiceOrderNumArr;
        }
        $openingInvoiceRecords = array_filter($checkInvoiceRecordByMixedRes['data'], function ($item) {
            return in_array(intval($item['invoice_state']), [0, 1, 2, 3]); // 未开票|开票中|开票成功|红冲中，排除5=开票失败，4=已红冲
        });
        if (empty($openingInvoiceRecords)) {
            return $canInvoiceOrderNumArr;
        }
        return array_diff($canInvoiceOrderNumArr, array_column($openingInvoiceRecords, 'ordernum'));
    }

    /**
     * 获取合并支付订单关联订单列表（无登录态，线下扫码开票使用)
     * @throws Exception
     */
    public function getCanInvoiceOrderListByCmbId(string $cmbId, int $sid, string $source, string $forcedSelectionOrderNum = ''): array
    {
        // 获取合并支付订单关联订单号
        try {
            $orderListRes = (new FeignClient())->listByTradeOrderId($cmbId);
        } catch (Exception $e) {
            throw new Exception('关联订单信息获取失败');
        }
        if (empty($orderListRes)) {
            throw new Exception('关联订单为空');
        }
        $orderNumArr = array_column($orderListRes, 'productOrderId');
        $orderMap = array_column($orderListRes, null, 'productOrderId');
        $ticketIds = array_column($orderListRes, 'itemSku');

        // 获取能开票的订单信息
        $canInvoiceOrderNumArr = $this->getCanInvoiceOrderList($orderNumArr, $sid, $source);
        if (empty($canInvoiceOrderNumArr)) {
            return [];
        }

        // 获取产品、票信息
        $ticketInfoList = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($ticketIds);
        $mapTicketInfoList = [];
        foreach ($ticketInfoList as $ticketInfo) {
            $mapTicketInfoList[$ticketInfo['ticket']['id']] = $ticketInfo;
        }

        // 获取分销金额
        $queryRes = \Business\JavaApi\Order\Query\Container::query('orderQuery', 'completeOrder', [$canInvoiceOrderNumArr]);
        $orderPriceMap = [];
        foreach ($queryRes['data'] as $order) {
            $orderPriceMap[$order['ordernum']] = $order;
        }

        $orderList = [];
        foreach ($canInvoiceOrderNumArr as $orderNum) {
            $orderInfo = $orderMap[$orderNum];
            $orderList[] = [
                'ordernum'         => $orderNum,
                'spu_name'         => $mapTicketInfoList[$orderInfo['itemSku']]['land']['title'],
                'sku_name'         => $mapTicketInfoList[$orderInfo['itemSku']]['ticket']['title'],
                'order_time'       => $orderInfo['orderTime'],
                'order_amount'     => $orderPriceMap[$orderNum]['totalmoney'],
                'forced_selection' => $orderNum == $forcedSelectionOrderNum,
            ];
        }

        return $orderList;
    }

    /**
     * 获取交易订单开票地址
     * @throws Exception
     */
    public function getTradeOpenInvoiceUrl(string $cmbId, int $sid, string $source, array $orderNumArr, string $refOrderNum): string
    {
        //失败和已红冲跳过校验，直接跳转开票页面，避免页面循环跳转
        $exitsInvoiceUrl = $this->getExitsInvoiceUrlByOrderNum($sid, $refOrderNum, 0, [4, 5]);
        if ($exitsInvoiceUrl) {
            return $exitsInvoiceUrl;
        }
        $orderListRes = (new FeignClient())->listByTradeOrderId($cmbId);
        if (empty($orderListRes)) {
            throw new Exception('关联订单为空');
        }
        $tradeOrderNumArr = array_column($orderListRes, 'productOrderId');
        foreach ($orderNumArr as $orderNum) {
            if (!in_array($orderNum, $tradeOrderNumArr)) {
                throw new Exception(sprintf('订单 %s 与交易单号不匹配', $orderNum));
            }
        }
        $orderMap = array_column($orderListRes, null, 'productOrderId');

        // 获取能开票的订单信息
        $orderList = $this->getCanInvoiceOrderListByCmbId($cmbId, $sid, $source);
        $canInvoiceOrderNumArr = [];
        foreach ($orderList as $orderInfo) {
            $canInvoiceOrderNumArr[] = $orderInfo['ordernum'];
        }
        if (empty($canInvoiceOrderNumArr)) {
            throw new Exception('无可开票订单');
        }
        foreach ($orderNumArr as $orderNum) {
            if (!in_array($orderNum, $canInvoiceOrderNumArr)) {
                throw new Exception(sprintf('订单 %s 不能开票', $orderNum));
            }
        }

        // 独立开票
        if (count($orderNumArr) === 1) {
            $orderInfo = $orderMap[$orderNumArr[0]];
            $openInvoiceUrl = $this->getOpenInvoiceUrl($sid, $orderInfo['itemSku'], $orderNumArr[0], false, 0 , $source);
            if ($openInvoiceUrl['code'] != 200) {
                throw new Exception($openInvoiceUrl['msg']);
            }
            return $openInvoiceUrl['data'];
        }
        // 合并开票
        $result = $this->getBillableListByOrderNums($orderNumArr, $sid);
        if ($result['code'] != 200) {
            throw new Exception($result['msg']);
        }
        $billableList = $result['data'];
        $newOrderNumArr = [];
        foreach ($billableList as $billable) {
            $newOrderNumArr[$billable['order_type']][] = $billable['ordernum'];
        }
        $res = $this->getMergeOpenInvoiceUrl($sid, $newOrderNumArr, false, -1, 0, '', 0, $source, ['major' => $refOrderNum]);
        if ($res['code'] != 200) {
            throw new Exception($res['msg']);
        }
        return $res['data'];
    }

    /**
     * 获取可开票订单列表
     * @param array $orderNumArr
     * @param $sid
     * @return array
     */
    public function getBillableListByOrderNums(array $orderNumArr, $sid) {
        if (!$orderNumArr || !$sid) {
            return $this->returnData(203, '参数异常');
        }
        $data = [
            'order_nums' => $orderNumArr,
            'sid' => intval($sid)
        ];

        $result = $this->ExternalCodeCall(self::__GET_BILLABLE_LIST_BY_ORDER_NUMS_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取当前用户使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  int  $sid  用户id
     *
     * @return array
     */
    public function getUserNowSys(int $sid)
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__GET_USER_NOW_SYS_ID__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取当前用户使用开票系统
     * <AUTHOR>  Li
     * @date 2022-01-14
     *
     * @param  int $sid 用户id
     *
     * @return array
     */
    public function getUserNowSysInfo(int $sid)
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__GET_USER_NOW_SYS_INFO__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 切换当前用户使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  int  $sid  用户id
     *
     * @return array
     */
    public function changeSys(int $sid, int $sysId, int $opid)
    {
        if (!$sid || !$sysId || !$opid) {
            return $this->returnData(203, '参数错误', []);
        }

        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'    => $sid,
            'sys_id' => $sysId,
            'opid'   => $opid,
        ];

        $result = $this->ExternalCodeCall(self::__CHANGE_SYS__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 添加使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  int  $sid  用户id
     * @param  int  $sysId  开票系统id
     * @param  int  $opid  操作人id
     *
     * @return array
     */
    public function addUserSys(int $sid, int $sysId, int $opid)
    {
        if (!$sid || !$opid) {
            return $this->returnData(203, '参数错误', []);
        }

        $data = [
            'sid'    => $sid,
            'sys_id' => $sysId,
            'opid'   => $opid,
        ];

        $result = $this->ExternalCodeCall(self::__ADD_USER_SYS__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 删除使用开票系统
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  int  $sid  用户id
     * @param  int  $sysId  开票系统id
     * @param  int  $opid  操作人id
     *
     * @return array
     */
    public function delUserSys(int $sid, int $sysId, int $opid)
    {
        if (!$sid || !$opid || !$sysId) {
            return $this->returnData(203, '参数错误', []);
        }
        $data = [
            'sid'    => $sid,
            'sys_id' => $sysId,
            'opid'   => $opid,
        ];

        $result = $this->ExternalCodeCall(self::__DEL_USER_SYS__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取当前用户添加得开票系统列表
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  int  $sid  用户id
     *
     * @return array
     */
    public function getUserSysList($sid)
    {
        if (!$sid) {
            return $this->returnData(203, '参数错误', []);
        }
        $data = [
            'sid' => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__GET_USER_SYS_LIST__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取需要更新状态的订单列表缓存
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  string $ordernum
     *
     * @return array
     */
    public function getUpdateListCache(string $ordernum)
    {
        if (!$ordernum) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'ordernum' => $ordernum,
        ];

        $result = $this->ExternalCodeCall(self::__UPDATE_ORDER_LIST_CACHE__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取需要更新状态的订单列表缓存
     * <AUTHOR>
     * @date 2021-08-31
     *
     * @param  string $ordernum
     *
     * @return array
     */
    public function updateOrderInfoByordernum(string $ordernum, $data)
    {
        if (!$ordernum) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'ordernum' => $ordernum,
            'data'     => $data,
        ];

        $result = $this->ExternalCodeCall(self::__UPDATE_BILLABLE_BY__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 根据合并id获取合并开票记录
     *
     * <AUTHOR>
     * @date  2021-11-11
     *
     * @param  string $merge 合并id
     * @param  int  $getOrderType  是否获取订单对应的类型 0否 1是
     *
     * @return array
     */
    public function getMergeSonList(string $merge, int $getOrderType = 0, int $sid = 0)
    {
        if (!$merge) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'mergeId'      => $merge,
            'getOrderType' => $getOrderType,
            'sid'          => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__GETMERGESONLIST__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 根据合并id更新合并开票开票信息
     *
     * <AUTHOR>
     * @date  2021-11-11
     *
     * @param  string $merge 合并id
     *
     * @return array
     */
    public function updateMergeInvoiceRecordInfoBy(string $merge, array $data)
    {
        if (!$merge) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'mergeId' => $merge,
            'data'    => $data,
        ];

        $result = $this->ExternalCodeCall(self::__UPDATE_MERGE_INVOICE_RECORD_INFO__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }
    /**
     * 手动百旺待开数据刷新
     *
     * <AUTHOR>
     * @date  2021-11-11
     *
     *
     * @return array
     */
    public function baiwangManualUpdate($opid, $sid, $did)
    {
        if (!$opid || (empty($sid) && empty($did))) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = compact('opid','sid','did');

        $result = $this->ExternalCodeCall(self::__BAIWANG_MANUAL_UPDATE__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 合并开票
     *
     * <AUTHOR>
     * @date  2021-06-14
     *
     * @param  int  $sid  供应商id
     * @param  array  $orderNum  需要合并订单号
     * @param  bool  $isTransit  是否中转页面请求 （百旺用）
     * @param  int  $openUserId 开票人
     * @param  int  $invoiceType 开票类型
     *
     * @return array
     */
    public function getMergeOpenInvoiceUrl(int $sid, array $orderNum = [], bool $isTransit = false, int $openUserId = -1,
        int $invoiceType = 0, string $mergeId = '', $isNeedCerifi = 0, $source = 'platform', $extData = []): array    {
        if (!$sid || (!$orderNum && !$mergeId)) {
            return $this->returnData(203, '参数异常', []);
        }

        if ($isTransit && !$mergeId) {
            return $this->returnData(203, '合并参数参数异常', []);
        }

        //兼容版订单号处理
        $newOrderNumArr = [];
        $tmpOrderNumArr = $orderNum;
        if ($tmpOrderNumArr && is_array($tmpOrderNumArr)) {
            //判断是否需要置空原始订单号
            $isDelete = false;
            foreach ($tmpOrderNumArr as $idx => $item) {
                //然后订单号格式是兼容版的 数据组装下
                if (is_array($item)) {
                    $newOrderNumArr[$idx] = $item;
                    $isDelete              = true;
                }
            }
            //将原始订单号置空
            if ($isDelete) {
                $orderNum = [];
            }
        }

        $data = [
            'sid'              => $sid,
            'ordernum_arr'     => $orderNum,
            'is_transit'       => $isTransit,
            'invoice_type'     => $invoiceType,
            'open_user_id'     => $openUserId,
            'merge_id'         => $mergeId,
            'new_ordernum_arr' => $newOrderNumArr,
            'is_need_cerifi'   => $isNeedCerifi,
            'source'           => $source,
            'extData'          => $extData,
        ];

        $result = $this->ExternalCodeCall(self::__GET_MERGE_OPEN_INVOICE_URL__, $data);
        pft_log('invoice/getMergeOpenInvoiceUrl', json_encode($result, JSON_UNESCAPED_UNICODE));
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData($result['code'], empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }


    /**
     * 获取自定义商品记录
     * @deprecated 【电子发票二期】供应商开票废弃
     * <AUTHOR>
     * @date   2021-12-06
     *
     * @param  int $sid 供应商id
     * @param  int $landid 景区id
     * @param  int|array $tid 票id
     * @param  string $spbm  商品编码
     * @param  string $name 定义商品名称
     *
     * @return array
     */
//    public function productDefinitionNameList(int $sid = 0, int $landid = 0, $tid = [], string $name = '',string $spbm = '', int $page = 1, int $size = 10): array
//    {
//        $data = [
//            'land_id' => $landid,
//            'tid'     => $tid,
//            'sid'     => $sid,
//            'name'    => $name,
//            'spbm'    => $spbm,
//            'page'    => $page,
//            'size'    => $size,
//        ];
//        $result = $this->ExternalCodeCall(self::__PRODUCT_DEFINITION_NAME_LIST__, $data);
//        if ($result['data']['list']) {
//            $tids = array_column($result['data']['list'],'tids');
//            $tidArr = [];
//            foreach ($tids as $v){
//                $tidArr = array_unique(array_merge($tidArr, $v));
//            }
//            $ticketInfo = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tidArr);
//            foreach ($result['data']['list'] as &$item){
//                foreach ($item['list'] as &$v){
//                    foreach ($ticketInfo as $info){
//                        if ($v['tid'] == $info['ticket']['id']) {
//                            $v['land_name'] = $info['land']['title'];
//                            $v['ticket_name'] = $info['ticket']['title'];
//                            break;
//                        }
//                    }
//                }
//                unset($v);
//            }
//            unset($item);
//            $result['data']['list'] = array_values($result['data']['list']);
//        }
//        return $this->returnData($result['code'], $result['msg'], $result['data']);
//    }

    /**
     * 保存自定义商品名称信息
     *
     * <AUTHOR>
     * @date  2021-12-03
     * @param int $sid 供应商
     * @param string $type 操作类型  add 新增 update 更新
     * @param array $add  ['landid' => ['tids','tids1']]
     * @param array $del
     * @param string $name
     * @param string $taxRate
     * @param string $spbm
     * @param int $id
     * @param int $opid
     * @return array
     */
    public function saveProductDefinitionNameInfo(string $type, int $sid, array $add = [], array $del = [], string $name = '',string $taxRate = '', string $spbm = '',int $id = 0, $opid = 0): array
    {
        if (!in_array($type, ['add', 'update'])) {
            return $this->returnData(203, '参数类型错误', []);
        }
        if (!$sid) {
            return $this->returnData(203, '参数错误', []);
        }

        $data = [
            'opid'     => $opid,
            'type'     => $type,
            'sid'      => $sid,
            'add'      => $add,
            'del'      => $del,
            'name'     => $name,
            'tax_rate' => $taxRate,
            'spbm'     => $spbm,
            'id'       => $id,
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_PRODUCT_DEFINITION_NAME_INFO__, $data);
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 保存自定义商品名称信息
     *
     * <AUTHOR>
     * @date  2021-12-03
     * @param int $id
     * @return array
     */
    public function delDefinitionName(int $id, int $opid): array
    {
        if (!$id) {
            return $this->returnData(203, '参数错误', []);
        }
        $data = [
            'id' => $id,
            'opid' => $opid,
        ];
        $result = $this->ExternalCodeCall(self::__DEL_DEFINITION_NAME__, $data);
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 检测订单号是否能写入可开票列表
     *
     * <AUTHOR>
     * @date  2021-12-10
     *
     * @param  array  $orderNumArr  订单号数组
     *
     * @return array
     */
    public function checkOrderAddBillable(array $orderNumArr): array
    {
        if (!$orderNumArr || !is_array($orderNumArr)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'ordernum_arr'    => $orderNumArr,
        ];

        $result = $this->ExternalCodeCall(self::__CHECK_ORDER_ADD_BILLABLE__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    public function test()
    {
        $result = $this->ExternalCodeCall(self::__TEST__, []);

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 判断供应商是否有开票的权限(注意这是对外的jsonRpc接口)
     * 请求地址示例：ElectronicInvoices/InvoiceApi/getEnterpriseOpenRecordsByMixedRpc
     * <AUTHOR>
     * @date 2021/3/30
     *
     * @param  int  $sid 供应商ID
     *
     * @return array
     */
    public function getEnterpriseOpenRecordsByMixedRpc(int $sid)
    {
        if (empty($sid)) {
            return $this->returnData(203, '参数缺失', []);
        }

        $authInvoice = 0;

        //判断供应商是否有开票的权限
        $invoiceModel = new invoiceModel();
        $result       = $invoiceModel->getEnterpriseOpenRecordsByMixed($sid);
        if (!empty($result)) {
            $authInvoice = 1;
        }

        return $this->returnData(200, '', ['authInvoice' => $authInvoice]);
    }

    /**
     * 分页模糊搜索商品名配置
     * <AUTHOR>  Li
     * @date   2022-01-18
     *
     * @param  array  $sidArr  供应商id数组
     * @param  string  $name  定义商品名称
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     *
     * @return array
     */
    public function getDefinitionRecordsListPage(array $sidArr, string $name, int $page = 1, int $size = 10): array
    {
        if (!$sidArr || !$name) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid_arr' => $sidArr,
            'name'    => $name,
            'page'    => $page,
            'size'    => $size,
        ];

        $result = $this->ExternalCodeCall(self::__GET_DEFINITION_RECORDS_LIST_PAGE__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 取消开发票
     * <AUTHOR>  Li
     * @date  2022-03-18
     *
     * @param  int  $recordId  开票记录id
     *
     * @return  array
     */
    public function invoiceCancel(int $recordId, int $sid = 0)
    {
        if (!$recordId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'record_id' => $recordId,
            'sid'       => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__INVOICE_CANCEL__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 红冲发票
     * <AUTHOR>  Li
     * @date  2022-11-10
     *
     * @param  int  $recordId  开票记录id
     *
     * @return  array
     */
    public function invoiceRedDashed(int $recordId, int $sid = 0)
    {
        if (!$recordId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'record_id' => $recordId,
            'sid'       => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__INVOICE_RED_DASHED__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 校验资质认证信息
     *
     * @param  int  $sid  开票方商家id
     * @param  int  $fid  开票操作人id
     *
     * @return  array
     */
    public function checkCerificationInfo(int $sid, int $fid = 0)
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
            'fid' => $fid,
        ];

        $result = $this->ExternalCodeCall(self::__CHECK_CERIFICATION_INVO__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 航信开发票
     *
     * @param  string  $tariffNumber  开票方税号
     * @param  string  $orderNum  订单号
     * @param  array  $postData  开票信息
     * @param  string  $fid  开票人id
     *
     * @return array
     */
    public function writeReceipt(string $tariffNumber, string $orderNum, array $postData, string $sid, string $fid,
        string $outTradeNo = '', $isMerge = 'rx', $orderType = 'w5', $source = '', $extData = [])
    {
        if (!$tariffNumber || !$orderNum || !$postData) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'tariff_number' => $tariffNumber,
            'order_num'     => $orderNum,
            'post_data'     => $postData,
            'is_merge'      => $isMerge,
            'sid'           => $sid,
            'fid'           => $fid,
            'out_trade_no'  => $outTradeNo,
            'order_type'    => $orderType,
            'source'        => $source,
            'extData'       => $extData,
        ];

        $result = $this->ExternalCodeCall(self::__WRITE_RECEIPT__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 查询开票结果
     *
     * @param  string  $tariffNumber  开票方税号
     * @param  array  $orderNumArr  订单编号
     * @param  array  $serialNoArr  发票流
     * @param  int  $isOfferInvoiceDetail  是否需要提供明细 1-是, 0-否
     * @param  bool  $isSandBox  是否沙箱模式
     *
     * @return array
     */
    public function queryInvoiceResult(string $tariffNumber, array $orderNumArr = [], array $serialNoArr = [], int $isOfferInvoiceDetail = 0, $isSandBox = false, $sysId = 1)
    {
        if (!$tariffNumber || (!$orderNumArr && !$serialNoArr)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'tariff_number'           => $tariffNumber,
            'order_num_arr'           => $orderNumArr,
            'serial_no_arr'           => $serialNoArr,
            'is_offer_invoice_detail' => $isOfferInvoiceDetail,
            'is_sand_box'             => $isSandBox,
            'sys_id'                  => $sysId,
        ];

        $result = $this->ExternalCodeCall(self::__QUERY_INVOICE_RESULT__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 通过订单号更新发票状态
     *
     * @param  string  $outTradeNo  订单流水号
     * @param  string  $invoiceCode  发票代码
     * @param  string  $invoiceNum  发票号码
     * @param  string  $invoiceUrl  发票图片地址
     * @param  string  $invoicePdf  发票pdf地址
     * @param  int  $state  发票状态   0未开票 1进行中 2已开票 3红冲中 4已红冲 5开票失败
     *
     * @return array
     */
    public function updateInvoiceStatusByOrderNum(string $outTradeNo, string $invoiceCode = '', string $invoiceNum = '', string $invoiceUrl = '', string $invoicePdf = '', int $state = 1, int $isMerge = 0)
    {
        if (!$outTradeNo) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'out_trade_no' => $outTradeNo,
            'invoice_code' => $invoiceCode,
            'invoice_num'  => $invoiceNum,
            'invoice_url'  => $invoiceUrl,
            'invoice_pdf'  => $invoicePdf,
            'state'        => $state,
            'is_merge'     => $isMerge,
        ];

        $result = $this->ExternalCodeCall(self::__UPDATE_INVOICE_STATE__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取商家下的游客开票记录
     *
     * @param  int  $sid  供应商id
     * @param  int  $memberId  搜索用户id
     * @param  int  $status  开票状态
     * @param  int  $page  当天页数
     * @param  int  $size  每页条数
     * @param  string  $tariffNumber  供应商id
     * @param  string  $orderNum  搜索订单号
     * @param  int  $beginTime  开始时间戳
     * @param  int  $endTime  结束时间戳
     *
     * @return array
     */
    public function getMemberEnterpriseInvoiceRecords(int $sid, int $memberId, int $page = 1, int $size = 10, int $status = -1, int $beginTime = 0, int $endTime = 0, string $tariffNumber = '', string $orderNum = '',  string $invoiceNum = '', string $enterpriseName = ''): array
    {
        if (!$sid || !$memberId) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'             => $sid,
            'begin_time'      => $beginTime,
            'end_time'        => $endTime,
            'member_id'       => $memberId,
            'tariff_number'   => $tariffNumber,
            'ordernum'        => $orderNum,
            'page'            => $page,
            'size'            => $size,
            'invoice_num'     => $invoiceNum,
            'enterprise_name' => $enterpriseName,
            'status'          => $status,
        ];

        $result = $this->ExternalCodeCall(self::__GET_MEMBER_ENTERPRISE_INVOICE_RECORDS_FUN__, $data);
        //echo '<pre>';print_r($result);die;
        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        if ($result['data']['list']) {
            $list = [];
            foreach ($result['data']['list'] as $item) {
                $list[] = [
                    'sid'             => $item['sid'],
                    'member_id'       => $item['member_id'],
                    'ordernum'        => $item['ordernum'],
                    'out_trade_no'    => $item['out_trade_no'],
                    'enterprise_name' => $item['enterprise_name'],
                    'image_url'       => $item['image_url'] ?? '',
                    'image_pdf'       => $item['image_pdf'] ?? '',
                    'add_time'        => $item['add_time'],
                    'add_date'        => date('Y-m-d H:i:s', $item['add_time']),
                    'invoice_state'   => (int)$item['invoice_state'],
                    'buyer_name'      => $item['original_order_info']['buyer_name'] ?? '--',
                    'totalmoney'      => (int)$item['original_order_info']['totalmoney'] ?? '--',
                ];
            }
            $result['data']['list'] = $list;
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 为用户添加开票系统
     *
     * @param  int  $sid  用户id
     *
     * @return array
     */
    public function addUserSystem(int $sid)
    {
        if (!$sid) {
            return $this->returnData(203, '用户id异常', []);
        }

        $data = [
            'sid' => $sid,
        ];

        $result = $this->ExternalCodeCall(self::__ADD_USER_SYSTEM__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 百旺金穗云回调业务处理
     *
     * @param  array  $content  回调内容
     *
     * @return array
     */
    public function invoicesCallbackJS(array $content)
    {
        if (!$content) {
            return $this->returnData(203, '回调内容为空', []);
        }

        $data = [
            'content' => $content,
        ];

        $result = $this->ExternalCodeCall(self::__INVOICES_CALLBACK_JS__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 航信回调业务处理
     *
     * @param  string  $operation  操作类型   callback【开票结果回调】
     * @param  string  $orderNo  流水号
     * @param  array  $content  回调内容
     *
     * @return array
     */
    public function invoicesCallbackHX($operation, $orderNo, $content)
    {
        if (!$operation || !$orderNo || !$content) {
            return $this->returnData(203, '缺少必要参数');
        }

        $data = [
            'operation' => $operation,
            'orderNo'   => $orderNo,
            'content'   => $content,
        ];

        $result = $this->ExternalCodeCall(self::__INVOICES_CALLBACK_HX__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取沙箱环境配置
     *
     * @return bool
     */
    public function getSandBox()
    {
        $data   = [];
        $result = $this->ExternalCodeCall(self::__GET_SAND_BOX__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return false;
        }

        return $result['data'];
    }

    /**
     * 发票订单核销通知
     *
     * @return array
     */
    public function writeOfNoticeInvoice($orderNum, $dtime = 0)
    {
        if (!$orderNum) {
            return $this->returnData(203, '缺少必要参数');
        }
        $data = [
            'ordernum' => $orderNum,
            'dtime'    => $dtime,
        ];

        $result = $this->ExternalCodeCall(self::__WRITE_OF_NOTICE_INVOICE__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票企业开票配置
     *
     * @param int $sid 供应商id
     * @param int $pageNum
     * @param int $pageSize
     * @param int $spuId
     * @param int $skuId
     * @param string $spbm
     * @param string $productTag
     * @param int $productTagStatus
     * @param int $status
     * @return array
     *
     */
    public function getInvoiceConfigListV2(int $sid, int $pageNum = 1, int $pageSize = 15, int $spuId = 0,
        int $skuId = 0, string $spbm = '', string $productTag = '', int $productTagStatus = -1, int $status = -1)
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
            'spu_id' => $spuId,
            'sku_id' => $skuId,
            'spbm' => $spbm,
            'product_tag' => $productTag,
            'product_tag_status' => $productTagStatus,
            'status' => $status,
            'page_num' => $pageNum,
            'page_size' => $pageSize
        ];

        $result = $this->ExternalCodeCall(self::__GET_INVOICE_CONFIG_LIST_V2_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存开票配置
     *
     * <AUTHOR>  Li
     * @date  2021-06-11
     *
     * @param  int  $sid  供应商ID
     * @param  int  $billingParty  开票方
     * @param  int  $id  配置id
     * @param  string  $tid  门票ID， 多个以逗号隔开
     * @param  string  $channel  开票渠道 多个以逗号隔开
     * @param  int  $operatorId  操作员ID
     * @param  int  $validityPeriod  有效期
     * @param  int  $validityType  开票有效期 0长期有效 1有期限
     * @param  float  $taxRate  开票税率
     * @param  string  $spbm  商品编码
     *
     * @return array
     */
    public function saveInvoiceConfigV2(int $sid, int $billingParty, string $tid, int $id = 0, string $channel = '',
        int $operatorId = 0, int $validityPeriod = 0, int $validityType = 0, float $taxRate = 0.0000,
        string $spbm = '', int $distribution = 0, string $otaUserList = '', int $useProductTag = 0, $productTag = '',
        $favouredPolicy = 0, $sdFavouredPolicy = 0): array
    {
        if (!$sid || !$billingParty || ($useProductTag && !$productTag) || (!$id && !$tid)) {
            return $this->returnData(203, '参数异常', []);
        }
        $tidArr = $tid ? explode(',', $tid) : [];

        $data = [
            'sid'             => $sid,
            'billing_party'   => $billingParty,
            'id'              => $id,
            'tid'             => $tidArr,
            'channel'         => $channel,
            'operator_id'     => $operatorId,
            'validity_period' => $validityPeriod,
            'validity_type'    => $validityType,
            'tax_rate'        => $taxRate,
            'spbm'            => $spbm,
            'distribution'    => $distribution,
            'ota_user_list'   => $otaUserList,
            'use_product_tag' => $useProductTag,
            'product_tag'     => $productTag,
            'favoured_policy' => $favouredPolicy,
            'sd_favoured_policy' => $sdFavouredPolicy
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_INVOICE_CONFIG_V2_FUN__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取票列表
     * @param $fid
     * @param $keyword
     * @param $ticketStatus
     * @param $supplyType
     * @param $pageNum
     * @param $pageSize
     * @return array|array[]
     */
    public function getInvoiceTicketList($fid, $keyword, $ticketStatus, $supplyType, $pageNum, $pageSize)
    {
        //处理下查询的产品参数  门票范围 1=在售的门票 2=下架的门票 6=已删除的门票 -1=不限
        $result = $this->getPlatformSaleListBiz()->queryPlatformTicketAndInvalidByPaging($fid, $pageNum,
            $pageSize, true, null, $ticketStatus, $keyword, null, $supplyType, ['A','F','I','B','C','G','H','J','Q']);
        $result['list'] = array_map(function ($item) {
            return [
                'tid' => $item['tid'],
                'land_name' => $item['landTitle'],
                'ticket_name' => $item['ticketTitle']
            ];
        }, $result['list'] ?? []);
        return $this->returnDataV2(200, ['list' => $result['list'], 'total' => $result['total'] ?? 0]);
    }

    /**
     * 获取配置中已选择的票
     * @param int $sid
     * @param int $withoutConfigId
     * @return array
     */
    public function getSelectedTicketList(int $sid, int $withoutConfigId)
    {
        $data = [
            'sid' => $sid,
            'not_config_id_list' => [$withoutConfigId]
        ];
        $result = $this->ExternalCodeCall(self::__GET_CONFIG_SELECTED_TICKET_LIST_FUN__, $data);
        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * @return PlatformSaleList
     */
    protected function getPlatformSaleListBiz() {
        return $this->container['platformSaleListBiz'];
    }

    /**
     * 保存关联产品
     * @param int $sid
     * @param int $operatorId
     * @param int $configId
     * @param string $tid
     * @return array
     */
    public function saveInvoiceConfigRelation(int $sid, int $operatorId, int $configId, string $tid)
    {
        if (!$configId || !$tid) {
            return $this->returnData(204, '参数错误');
        }
        $tidArr = explode(',', $tid);

        $data = [
            'sid' => $sid,
            'operator_id' => $operatorId,
            'config_id' => $configId,
            'tid' => $tidArr,
        ];
        $result = $this->ExternalCodeCall(self::__SAVE_INVOICE_CONFIG_RELATION_FUN__, $data);
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 更新配置状态
     * @param int $sid
     * @param array $ids
     * @param int $status
     * @return array
     */
    public function updateInvoiceConfigStatus(int $sid, array $ids, int $status)
    {
        $data = [
            'sid' => $sid,
            'ids' => $ids,
            'status' => $status,
        ];
        $result = $this->ExternalCodeCall(self::__UPDATE_INVOICE_CONFIG_STATUS_FUN__, $data);
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 批量删除配置
     * @param int $sid
     * @param array $ids
     * @return array
     */
    public function delInvoiceConfig(int $sid, array $ids)
    {
        $data = [
            'sid' => $sid,
            'ids' => $ids,
        ];
        $result = $this->ExternalCodeCall(self::__DEL_INVOICE_CONFIG_FUN__, $data);
        return $this->returnData(200, $result['msg'], $result['data']);
    }

    public function getSpuList(int $sid, string $keyword, int $pageNum = 1, int $pageSize = 20)
    {
        $res = $this->getDistributionProductBiz()->queryDistributionAndInvalidLandTitlePage($sid, $pageNum, $pageSize, ['A','F','I','B','C','G','H','J','Q'],
            0, $keyword);
        if ($res['code'] != 200 || empty($res['data'])) {
            return $this->returnDataV2(200, ['list' => [], 'total' => 0]);
        }
        $res['data']['list'] = array_map(function ($item) {
            return [
                'id' => $item['lid'],
                'spu_name' => $item['land_title']
            ];
        }, $res['data']['list'] ?? []);
        return $this->returnDataV2(200, ['list' => $res['data']['list'], 'total' => $res['data']['total'] ?? 0]);
    }

    public function getSkuList(int $sid, int $lid, string $keyword, int $pageNum = 1, int $pageSize = 20)
    {
        $res = $this->getDistributionProductBiz()->queryDistributionAndInvalidTicketTitlePage($sid, $lid, $pageNum, $pageSize,
            ['A','F','I','B','C','G','H','J','Q'], 0, null, $keyword);
        if ($res['code'] != 200 || empty($res['data'])) {
            return $this->returnDataV2(200, ['list' => [], 'total' => 0]);
        }
        $res['data']['list'] = array_map(function ($item) {
            return [
                'id' => $item['tid'],
                'sku_name' => $item['ticket_title']
            ];
        }, $res['data']['list'] ?? []);
        return $this->returnDataV2(200, ['list' => $res['data']['list'], 'total' => $res['data']['total'] ?? 0]);
    }

    /**
     * @return \Business\NewJavaApi\DistributionCenter\Product
     */
    private function getDistributionProductBiz() {
        return $this->container['distributionProductBiz'];
    }

    /**
     * 查询金穗云开票结果
     *
     * @param  int  $sid  供应商id
     * @param  int  $tid  门票id
     * @param  string  $outTradeNo  三方订单号
     *
     * @return array
     */
    public function queryInvoiceBWJSResult($sid, $tid, $outTradeNo)
    {
        if (!$sid || !$tid || !$outTradeNo) {
            return $this->returnData(203, '缺少必要参数');
        }
        $data = [
            'sid'          => $sid,
            'tid'          => $tid,
            'out_trade_no' => $outTradeNo,
        ];

        $result = $this->ExternalCodeCall(self::__QUERY_INVOICE_BWJS_RESULT__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 通过tid获取开票信息
     * @param int $sid
     * @param array $ticketIds
     * @return array
     */
    public function getInvoiceConfigByTicketIds(int $sid, array $ticketIds)
    {
        $data = [
            'sid' => $sid,
            'ticketIds' => $ticketIds,
        ];
        $result = $this->ExternalCodeCall(self::__GET_INVOICE_CONFIG_BY_TICKET_IDS__, $data);
        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取共享租赁订单信息
     *
     * @param  array  $orderNumArr  订单号数组
     *
     * @return array
     */
    public function getSharedLeaseOrderInfo(array $orderNumArr): array
    {
        if (!$orderNumArr) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'orderNumArr' => $orderNumArr,
        ];

        $result = $this->ExternalCodeCall(self::__GET_SHARED_LEASE_ORDER__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存开票配置扩展属性信息
     *
     * @param  int  $sid  供应商id
     * @param  int  $id  配置id
     * @param  int  $billingParty  销售方id
     * @param  float  $taxRate  配置的税率
     * @param  string  $spbm  配置的商品编码
     * @param  string  $type  配置标签  shared_lease 共享租赁
     * @param  string  $productTag  自定义商品标签
     *
     * @return array
     */
    public function saveExtensionConfig(int $sid, int $id = 0, int $billingParty = 0, float $taxRate = 0.0000, string $spbm = '', string $type = 'shared_lease', string $productTag = ''): array
    {
        if (!$sid || !$id || !$spbm || !$billingParty) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'           => $sid,
            'id'            => $id,
            'billing_party' => $billingParty,
            'type'          => $type,
            'tax_rate'      => $taxRate,
            'spbm'          => $spbm,
            'product_tag'   => $productTag,
        ];

        $result = $this->ExternalCodeCall(self::__SAVE_EXTENSION_CONFIG__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取买家可开票列表
     * @param array $orderIds
     * @param int $lastSequence
     * @param int $pageSize
     * @return array
     */
    public function getBuyerInvoicableList(array $orderIds, int $lastSequence, int $pageSize)
    {
        if (!$orderIds || !$pageSize) {
            return $this->returnData(200);
        }

        $data = [
            'orderIds' => $orderIds,
            'lastSequence' => $lastSequence,
            'pageSize' => $pageSize,
        ];

        $result = $this->ExternalCodeCall(self::__GET_BUYER_INVOICABLE_LIST__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取买家已开票列表
     * @param array $orderIds
     * @param int $lastSequence
     * @param int $pageSize
     * @return array
     */
    public function getBuyerInvoiceRecordList(array $orderIds, int $lastSequence, int $pageSize, string $source)
    {
        if (!$orderIds || !$pageSize) {
            return $this->returnData(200);
        }

        $data = [
            'orderIds' => $orderIds,
            'lastSequence' => $lastSequence,
            'pageSize' => $pageSize,
            'source' => $source,
        ];

        $result = $this->ExternalCodeCall(self::__GET_BUYER_INVOICE_RECORD_LIST__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票渠道列表
     * @return array
     */
    public function getReceiptChannelList()
    {
        $cacheKey = 'invoice:receipt_channel';
        /** @var CacheRedis $redis */
        $redis = Cache::getInstance('redis');
        $list = $redis->get($cacheKey);
        $list = json_decode($list, true);
        if (!$list) {
            $result = $this->ExternalCodeCall(self::__GET_RECEIPT_CHANNEL_LIST__, []);

            if (!isset($result['code']) || $result['code'] != 200) {
                return [];
            }
            $list = $result['data'];
            $redis->set($cacheKey, json_encode($list), '', 3600);
        }
        return $list;
    }

    /**
     * 重新开票
     *
     * @param string $orderNum 订单号
     * @param string $isMerge 是否合并开票
     * @param string $sid 供应商id
     * @param string $fid 开票人id
     * @param string $orderType 订单类型
     * @param string $source 开票来源
     * @return array
     */
    public function freshOpenInvoiceUrl($orderNum, $isMerge, $sid, $fid, $orderType, $source = '')
    {
        $data = [
            'order_num'     => $orderNum,
            'is_merge'      => $isMerge,
            'sid'           => $sid,
            'fid'           => $fid,
            'order_type'    => $orderType,
            'source'        => $source,
        ];

        $result = $this->ExternalCodeCall(self::__FRESH_OPEN_INVOICE_URL__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票详情，支持多订单号
     * @param string $orderNum 多订单号用逗号分割
     * @param $sid
     * @param $fid
     * @param $isN
     * @param $orderType
     * @param $source
     * @return array
     */
    public function getInvoiceDetailByMixed($orderNum, $sid, $fid, $isN, $orderType, $source = '')
    {
        $data = [
            'ordernum'     => $orderNum,
            'sid'           => $sid,
            'fid'           => $fid,
            'isN'           => $isN,
            'orderType'    => $orderType,
            'source'        => $source,
        ];

        $result = $this->ExternalCodeCall(self::__GET_INVOICE_DETAIL_BY_MIXED__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取开票记录详情
     * @param string $orderNum
     * @param $sid
     * @param $fid
     * @param $isMerge
     * @param $sysId
     * @return array
     */
    public function getInvoiceRecordDetailByMixed($orderNum, $sid, $fid, $isMerge, $sysId)
    {
        $data = [
            'ordernum'     => $orderNum,
            'sid'           => $sid,
            'fid'           => $fid,
            'isMerge'       => $isMerge,
            'sysId'         => $sysId,
        ];

        $result = $this->ExternalCodeCall(self::__GET_INVOICE_RECORD_DETAIL_BY_MIXED__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }
    
    /**
     * 获取开票所需基础信息
     * @param $sid
     * @param $fid
     * @param $billingPartyId
     * @param $isN
     * @param $orderType
     * @param $source
     * @return array|mixed
     */
    public function getBasicInvoiceDetailByMixed($sid, $fid, $billingPartyId, $isN, $orderType, $source = '') {
        $data = [
            'sid'           => $sid,
            'fid'           => $fid,
            'bpi'           => $billingPartyId,
            'isN'           => $isN,
            'orderType'     => $orderType,
            'source'        => $source,
        ];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_BASIC_INVOICE_DETAIL_BY_MIXED__, $data);
        if (!isset($result['code']) || $result['code'] != 200) {
            return [];
        }
        return $result['data'];
    }

    public function getInvoiceRecordByOrderNum($ordernum, $sid, $did)
    {
        if (empty($ordernum) || empty($sid) || empty($did)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'ordernum' => $ordernum,
            'sid' => $sid,
            'did' => $did,
        ];
        $result = $this->ExternalCodeCall(self::__CHECK_INVOICE_RECORD_BY_ORDER_NUM__, $data);

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }
}