<?php
/**
 * 请求AppCenterService电子发票服务接口
 * <AUTHOR>  Li
 * @date 2021-06-11
 *
 */

namespace Business\ElectronicInvoices;

use Library\JsonRpc\PftRpcClient;
use Business\Base;

class ECBase extends Base
{

    //新增企业开票信息
    const __ADD_ENTERPRISE_INVOICE_FUN__ = 'index/Invoice/addEnterpriseInvoice';
    //删除企业开票信息 （软删除）
    const __DELETE_ENTERPRISE_INVOICE_FUN__ = 'index/Invoice/deleteEnterpriseInvoice';
    //供应商下企业的开通记录
    const __GET_ENTERPRISE_OPEN_RECORD_TRADE_FUN__ = 'index/Invoice/getEnterpriseOpenRecordsTrade';
    //通过税号修改企业开票信息
    const __EDIT_ENTERPRISE_INFO_BY_TARIFF_NUMBER_FUN__ = 'index/Invoice/editEnterpriseInfoByTariffNumber';
    //获取开票企业开通记录列表
    const __GET_ENTERPRISE_OPEN_RECORDS_FUN__ = 'index/Invoice/getEnterpriseOpenRecords';
    //获取开票企业开通记录列表
    const __GET_ENTERPRISE_INVOICE_RECORDS_FUN__ = 'index/Invoice/getEnterpriseInvoiceRecords';
    //获取开票企业开通记录
    const __GET_ENTERPRISE_OPEN_RECORDS_BY_MIXED_FUN__ = 'index/Invoice/getEnterpriseOpenRecordsByMixed';
    //查找订单号的开票记录
    const __CHECK_INVOICE_RECORD_BY_MIXED_FUN__ = 'index/Invoice/checkInvoiceRecordByMixed';
    const __CHECK_INVOICE_RECORD_BY_ORDER_NUM__ = 'index/Invoice/getInvoiceRecordByOrderNum';
    //保存开票记录
    const __SAVE_INVOICE_RECORD_FUN__ = 'index/Invoice/saveInvoiceRecord';
    //获取所有需要更新access_token的企业
    const __GET_NEED_REFRESH_TOKEN_ENTERPRISE_FUN__ = 'index/Invoice/getNeedRefreshTokenEnterprise';
    //保存开票配置
    const __SAVE_INVOICE_CONFIG_FUN__ = 'index/Invoice/saveInvoiceConfig';
    //保存开票配置【分销商开票版本】
    const __SAVE_INVOICE_CONFIG_V2_FUN__ = 'index/Invoice/saveInvoiceConfigV2';
    //获取已配置的票列表
    const __GET_CONFIG_SELECTED_TICKET_LIST_FUN__ = 'index/Invoice/getConfigSelectedTicketList';
    //保存关联产品
    const __SAVE_INVOICE_CONFIG_RELATION_FUN__ = 'index/Invoice/saveInvoiceConfigRelation';
    //批量启停配置
    const __UPDATE_INVOICE_CONFIG_STATUS_FUN__ = 'index/Invoice/updateInvoiceConfigStatus';
    //批量删除配置
    const __DEL_INVOICE_CONFIG_FUN__ = 'index/Invoice/delInvoiceConfig';
    // 通过tid获取开票信息
    const __GET_INVOICE_CONFIG_BY_TICKET_IDS__ = 'index/Invoice/getInvoiceConfigByTicketIds';
    //获取开票企业开票配置
    const __GET_INVOICE_CONFIG_LIST_FUN__ = 'index/Invoice/getInvoiceConfigList';
    //获取开票企业开票配置【分销商开票版本】
    const __GET_INVOICE_CONFIG_LIST_V2_FUN__ = 'index/Invoice/getInvoiceConfigListV2';
    //批量保存开票配置
    const __SAVE_INVOICE_CONFIG_BATCH_FUN__ = 'index/Invoice/saveInvoiceConfigBatch';
    //获取某个企业开通记录
    const __GET_ENTERPRISE_OPEN_RECORDS_LIST_FUN__ = 'index/Invoice/getEnterpriseOpenRecordsList';
    //获取开票企业开通信息
    const __GET_ENTERPRISE_INFO_BY_ID_ARR_FUN__ = 'index/Invoice/getEnterpriseInfoByIdArr';
    //获取开票企业开票配置
    const __GET_INVOICE_CONFIG_BY_TID_FUN__ = 'index/Invoice/getInvoiceConfigByTid';
    //通过企业名称 模糊搜索供应商下企业的开通记录
    const __GET_ENTERPRISE_OPEN_RECORDS_BY_NAME_FUN__ = 'index/Invoice/getEnterpriseOpenRecordsByName';
    //保存开票人相关信息
    const __SAVE_BILLER_CONFIG_FUN__ = 'index/Invoice/saveBillerConfig';
    //获取开票人相关信息
    const __GET_BILLER_CONFIG_FUN__ = 'index/Invoice/getBillerConfig';
    //添加分销商开票信息
    const __ADD_DISTRIBUTOR_INVOICE_FUN__ = 'index/Invoice/addDistributorInvoice';
    //更新分销商开票信息
    const __UPDATE_DISTRIBUTOR_INVOICE_FUN__ = 'index/Invoice/updateDistributorInvoice';
    //获取可开票列表
    const __GET_INVOICE_ABLE_LIST_FUN__ = 'index/Invoice/getInvoicableList';
    //获取可开票列表【电子发票二期分销商开票】
    const __GET_INVOICE_ABLE_LIST_V2_FUN__ = 'index/Invoice/getInvoicableListV2';
    //获取可开票订单信息
    const __GET_BILLABLE_LIST_BY_ORDER_NUMS_FUN__ = 'index/Invoice/getBillableListByOrderNums';
    //检测订单号是否写入过
    const __FIND_DISTRIBUTOR_INVOICE_BY_ORDERNUM_FUN__ = 'index/Invoice/findDistributorInvoiceByOrdernum';
    //保存供应商开票记录
    const __ADD_ENTERPRISE_INVOICE_RECORDS_FUN__ = 'index/Invoice/addEnterpriseInvoiceRecords';
    //检测订单号是否能开票
    const __CHECK_INVOICE_AUTH_FUN__ = 'index/Invoice/checkInvoiceAuth';
    //获取企业扫码开票链接地址
    const __GET_OPEN_INVOICE_URL_FUN__ = 'index/Invoice/getOpenInvoiceUrl';
    //获取当前用户添加得开票系统列表
    const __GET_USER_SYS_LIST__ = 'index/Invoice/getUserSysList';
    //切换当前用户使用开票系统
    const __CHANGE_SYS__ = 'index/Invoice/changeSys';
    //添加使用开票系统
    const __ADD_USER_SYS__ = 'index/Invoice/addUserSys';
    //删除使用开票系统
    const __DEL_USER_SYS__ = 'index/Invoice/delUserSys';
    //获取当前用户使用开票系统id
    const __GET_USER_NOW_SYS_ID__ = 'index/Invoice/getUserNowSysId';
    //获取当前用户使用开票系统信息
    const __GET_USER_NOW_SYS_INFO__ = 'index/Invoice/getUserNowSysInfo';
    //获取需要更新状态的订单列表缓存
    const __UPDATE_ORDER_LIST_CACHE__ = 'index/Invoice/getUpdateListCache';
    //根据订单号更新可开票列表记录
    const __UPDATE_BILLABLE_BY__ = 'index/Invoice/updateBillableOrderInfoByOrdernum';
    //根据合并id获取被合并记录
    const __GETMERGESONLIST__ = 'index/Invoice/getMergeSonList';
    //根据合并id更新合并开票开票信息 （发票号码、发票地址）
    const __UPDATE_MERGE_INVOICE_RECORD_INFO__ = 'index/Invoice/updateMergeInvoiceRecordInfo';
    //合并开票
    const __GET_MERGE_OPEN_INVOICE_URL__ = 'index/Invoice/getMergeOpenInvoiceUrl';
    //手动百旺待开数据刷新
    const __BAIWANG_MANUAL_UPDATE__ = 'index/Invoice/baiwangManualUpdate';

    //保存自定义商品名称信息
    const __SAVE_PRODUCT_DEFINITION_NAME_INFO__ = 'index/Invoice/saveProductDefinitionNameInfo';
    //获取自定义商品记录
    const __PRODUCT_DEFINITION_NAME_LIST__ = 'index/Invoice/productDefinitionNameList';
    const __DEL_DEFINITION_NAME__ = 'index/Invoice/delDefinitionName';
    //检测订单号是否能写入可开票列表
    const __CHECK_ORDER_ADD_BILLABLE__ = 'index/Invoice/checkOrderAddBillable';
    const __TEST__ = 'index/Invoice/test';
    //分页模糊搜索商品名配置
    const __GET_DEFINITION_RECORDS_LIST_PAGE__ = 'index/Invoice/getDefinitionRecordsListPage';
    //取消开发票
    const __INVOICE_CANCEL__ = 'index/Invoice/invoiceCancel';
    //红冲发票
    const __INVOICE_RED_DASHED__ = 'index/Invoice/invoiceRedDashed';
    //校验资质认证信息
    const __CHECK_CERIFICATION_INVO__ = 'index/Invoice/checkCerificationInfo';
    //查询航信开票结果
    const __QUERY_INVOICE_RESULT__ = 'index/Invoice/queryInvoiceResult';
    //航信开发票
    const __WRITE_RECEIPT__ = 'index/Invoice/writeReceipt';
    //更新开票状态
    const __UPDATE_INVOICE_STATE__ = 'index/Invoice/updateInvoiceStatusByOrderNum';
    //获取商家对应游客下的开票记录
    const __GET_MEMBER_ENTERPRISE_INVOICE_RECORDS_FUN__ = 'index/Invoice/getMemberEnterpriseInvoiceRecords';
    //系统为商家添加开票系统
    const __ADD_USER_SYSTEM__ = 'admin/Invoice/addUserSystem';
    //百旺金穗云开票回调逻辑
    const __INVOICES_CALLBACK_JS__ = 'index/InvoiceCallback/invoicesCallbackJS';
    //航信回调业务处理
    const __INVOICES_CALLBACK_HX__ = 'index/InvoiceCallback/invoicesCallbackHX';
    //获取沙箱环境配置
    const __GET_SAND_BOX__ = 'index/Invoice/getSandBox';
    //订单核销通知发票中心
    const __WRITE_OF_NOTICE_INVOICE__ = 'index/Invoice/writeOfNoticeInvoice';
    //查询百旺金穗云开票结果
    const __QUERY_INVOICE_BWJS_RESULT__ = 'index/Invoice/queryInvoiceBWJSResult';
    //保存开票配置扩展属性信息
    const __SAVE_EXTENSION_CONFIG__ = 'index/Invoice/saveExtensionConfig';

    //获取共享租赁订单信息
    const __GET_SHARED_LEASE_ORDER__ = 'index/Invoice/getSharedLeaseOrderInfo';
    //获取买家可开票列表
    const __GET_BUYER_INVOICABLE_LIST__ = 'index/Invoice/getBuyerInvoicableList';
    //获取买家已开票列表
    const __GET_BUYER_INVOICE_RECORD_LIST__ = 'index/Invoice/getBuyerInvoiceRecordList';
    //获取开票渠道列表
    const __GET_RECEIPT_CHANNEL_LIST__ = 'index/Invoice/getReceiptChannelList';
    //重新开票
    const __FRESH_OPEN_INVOICE_URL__ = 'index/Invoice/freshOpenInvoiceUrl';
    //获取开票详情，支持多订单号
    const __GET_INVOICE_DETAIL_BY_MIXED__ = 'index/Invoice/getInvoiceDetailByMixed';
    //获取开票记录详情
    const __GET_INVOICE_RECORD_DETAIL_BY_MIXED__ = 'index/Invoice/getInvoiceRecordDetailByMixed';
    //获取托管开票规则
    const __GET_AGENT_RULE_CONFIG__ = 'index/AgentInvoice/getAgentRuleConfig';
    //保存托管开票规则
    const __SAVE_AGENT_RULE_CONFIG__ = 'index/AgentInvoice/saveAgentRuleConfig';
    //获取托管开票-可开科目列表
    const __GET_AGENT_INVOICE_SUBJECT_LIST__ = 'index/AgentInvoice/getSubjectList';
    //获取托管开票-可开科目详情
    const __GET_AGENT_INVOICE_SUBJECT_INFO__ = 'index/AgentInvoice/getSubjectInfo';
    //保存可开科目
    const __SAVE_AGENT_SUBJECT__ = 'index/AgentInvoice/saveSubject';
    //删除可开科目
    const __DEL_AGENT_SUBJECT__ = 'index/AgentInvoice/delSubject';
    //查询代理开票额度
    const __GET_AGENT_INVOICE_LIMIT__ = 'index/AgentInvoice/getInvoiceLimit';
    const __GET_AGENT_INVOICE_MY_SUPPLIER_ID_PAGE__ = 'index/AgentInvoice/getMySupplierIdPage';
    const __GET_AGENT_INVOICE_MY_SUPPLIER_OPEN_RECORDS_PAGE__ = 'index/AgentInvoice/getMySupplierOpenRecordsPage';
    const __GET_AGENT_BASIC_INVOICE_DETAIL_BY_MIXED__ = 'index/Invoice/getBasicInvoiceDetailByMixed';

    public function __construct()
    {

    }

    /**
     * 请求服务接口
     *
     * <AUTHOR>  Li
     * @date 2020-11-05
     *
     * @param  string  $method  请求的方法
     * @param  array  $params  请求的参数
     * @param  string  $callService  对应服务
     *
     * @return array
     */
    public function ExternalCodeCall($method, $params, $callService = 'electronic_invoice')
    {
        if (!is_array($params)) {
            return $this->returnData(204, '参数异常', []);
        }

        if (!isset($method)) {
            return $this->returnData(204, '方法缺失', []);
        }

        $module = 'user'; //模块名

        try {
            $lib  = new PftRpcClient($callService);
            $res  = $lib->call($method, $params, $module);
            $data = $res['data'];
            $code = $res['code'];
            $msg  = $res['msg'];
        } catch (\Exception $e) {
            $msg  = $e->getMessage();
            $code = $e->getCode();
            $data = [];
        }

        return $this->returnData($code, $msg, $data);
    }
}