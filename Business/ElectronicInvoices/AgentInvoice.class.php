<?php
/**
 * 电子发票服务相关
 */

namespace Business\ElectronicInvoices;

use Library\Container;

class AgentInvoice extends ECBase
{
    /**
     * 获取我的供应商对应销售方翻页列表（有过交易记录的供应商）
     * @param $fid
     * @param $page
     * @param $pageSize
     * @return array
     */
    public function getMySupplierOpenRecordsPageWithMemberInfo($fid, $options = [], $page = 1, $pageSize = 15)
    {
        $returnData = [
            'list' => [],
            'total' => 0
        ];
        $agentInvoiceApi = Container::pull(AgentInvoiceApi::class);
        $pagination = $agentInvoiceApi->getMySupplierOpenRecordsPage($fid, $page, $pageSize, $options['sid'], $options['tariffNumber']);
        if (!$pagination) {
            return $returnData;
        }
        $sidList = array_values(array_unique(array_column($pagination['list'], 'sid')));
        $api = Container::pull(\Business\JavaApi\Member\MemberQuery::class);
        $res = $api->queryMemberByMemberQueryInfo(['idList' => $sidList]);
        if ($res['code'] != 200 || !$res['data']) {
            return $this->returnDataV2(self::CODE_SUCCESS, $returnData, '查询成功');
        }
        $memberInfoList = [];
        foreach ($res['data'] as $item) {
            $memberInfo = $item['memberInfo'];
            $memberInfoList[$memberInfo['id']] = [
                'account' => $memberInfo['account'],
                'dname' => $memberInfo['dname'],
            ];
        }
        $returnData['total'] = $pagination['total'];
        $returnData['list'] = array_map(function ($item) use ($memberInfoList) {
            $memberInfo = $memberInfoList[$item['sid']] ?? [];
            return array_merge($item, $memberInfo);
        }, $pagination['list']);
        return $returnData;
    }

    /**
     * 获取我的供应商对应销售方列表（有过交易记录的供应商）
     * @param $fid
     * @return array
     * @throws
     */
    public function getMySupplierOpenRecordsList($fid)
    {
        $agentInvoiceApi = Container::pull(AgentInvoiceApi::class);
        $pagination = $agentInvoiceApi->getMySupplierOpenRecordsPage($fid, 1, 500);
        if (!$pagination) {
            return [];
        }
        $resultData = [];
        foreach ($pagination['list'] as $record) {
            $resultData[] = [
                'enterprise_name' => $record['enterprise_name'],
                'tariff_number' => $record['tariff_number']
            ];
        }
        return $resultData;
    }
    
    /**
     * 获取我的供应商列表（有过交易记录的供应商）
     * @param $fid
     * @return array
     * @throws 
     */
    public function getMySupplierList($fid) {
        $agentInvoiceApi = Container::pull(AgentInvoiceApi::class);
        $sidList = $agentInvoiceApi->getMySupplierIdPage($fid, 1, 500);
        if (!$sidList) {
            return [];
        }
        $api = Container::pull(\Business\JavaApi\Member\MemberQuery::class);
        $res = $api->queryMemberByMemberQueryInfo(['idList' => $sidList]);
        if ($res['code'] != 200 || !$res['data']) {
            return $this->returnDataV2(self::CODE_SUCCESS, [], '查询成功');
        }
        $memberInfoList = [];
        foreach ($res['data'] as $item) {
            $memberInfo = $item['memberInfo'];
            $memberInfoList[] = [
                'id' => $memberInfo['id'],
                'account' => $memberInfo['account'],
                'dname' => $memberInfo['dname'],
            ];
        }
        return $memberInfoList;
    }

    /**
     * 获取开票企业开通记录列表开票记录
     * @param  int  $sid  供应商id
     * @param  int  $memberId  搜索用户id
     * @param  string  $tariffNumber  供应商id
     * @param  int  $beginTime  开始时间戳
     * @param  int  $endTime  结束时间戳
     * @param  int  $page  当天页数
     * @return array
     */
    public function getEnterpriseInvoiceRecords($sid, $beginTime = 0, $endTime = 0, $memberId = 0, $tariffNumber = '', 
        $invoiceNum = '', $sysId = -1, $enterpriseName = '', $status = -1, $page = 1, $pageSize = 10, $source = '')
    {
        if (!$sid && !$memberId) {
            return $this->returnData(203, '参数异常', []);
        }
        $returnData = [
            'list' => [],
            'total' => 0
        ];
        $invoiceApi = Container::pull(InvoiceApi::class);
        $result = $invoiceApi->getEnterpriseInvoiceRecords($sid, $beginTime, $endTime, $memberId, $tariffNumber, '',
            $invoiceNum, $sysId, 4, $enterpriseName, $status, $page, $pageSize, 2, $source);
        if ($result['code'] != 200) {
            return $returnData;
        }
        $sidList = array_column($result['data']['list'], 'sid');
        $api = Container::pull(\Business\JavaApi\Member\MemberQuery::class);
        $res = $api->queryMemberByMemberQueryInfo(['idList' => $sidList]);
        if ($res['code'] != 200 || !$res['data']) {
            return $returnData;
        }
        $memberInfoList = [];
        foreach ($res['data'] as $item) {
            $memberInfo = $item['memberInfo'];
            $memberInfoList[$memberInfo['id']] = [
                'account' => $memberInfo['account'],
                'dname' => $memberInfo['dname'],
            ];
        }
        $returnData['total'] = $result['data']['total'];
        $returnData['list'] = array_map(function ($item) use ($memberInfoList) {
            $memberInfo = $memberInfoList[$item['sid']] ?? [];
            return array_merge($item, $memberInfo);
        }, $result['data']['list']);
        return $returnData;
    }
}