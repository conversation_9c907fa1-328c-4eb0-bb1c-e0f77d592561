<?php
/**
 * 电子发票服务相关
 */

namespace Business\ElectronicInvoices;

class AgentInvoiceApi extends ECBase
{
    //日志目录
    private $_debugLogDir = 'electronic/invoice/';

    public function __construct()
    {
    }

    /**
     * 获取托管开票规则
     *
     *
     * @param  int  $sid  供应商id
     *
     * @return array
     */
    public function getAgentRuleConfig(int $sid): array
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = ['sid' => $sid];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_RULE_CONFIG__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__GET_AGENT_RULE_CONFIG__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存托管开票规则配置
     *
     * @param  int  $sid  供应商id
     * @param  int  $id  配置id
     * @param  int  $opId  操作人id
     * @param  string  $status  状态 1=开启 2=关闭
     * @param  array  $rules  规则
     *
     * @return array
     */
    public function saveAgentRuleConfig(int $sid, int $id = 0, int $opId = 0, int $status = 0, array $rules = []): array
    {
        if (!($sid && $status) || ($status == 1 && empty($rules))) {
            return $this->returnData(203, '参数异常', []);
        }

        $accumulationType     = $rules['accumulationType'] ?? 0; //1=采购价
        $expirationType       = $rules['expirationType'] ?? 0;   //1=按年失效
        $expirationDelay      = $rules['expirationDelay'] ?? 0;  //0=关闭 1=开启
        $expirationDelayMonth = $rules['expirationDelay'] ?? 0;  //失效延迟月数
        if ($status == 1) {
            if (!($accumulationType && $expirationType)) {
                return $this->returnData(203, '规则参数异常', []);
            }
            if ($expirationDelay && empty($expirationDelayMonth)) {
                return $this->returnData(203, '失效延迟月数不能为空', []);
            }
        }

        $data = [
            'sid'    => $sid,
            'id'     => $id,
            'op_id'  => $opId,
            'status' => $status,
            'rules'  => $rules
        ];
        $result = $this->ExternalCodeCall(self::__SAVE_AGENT_RULE_CONFIG__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__SAVE_AGENT_RULE_CONFIG__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取可开科目
     *
     * @param int $sid 供应商id
     * @param int $pageNum
     * @param int $pageSize
     * @param string $spName 商品名称
     * @param string $spbm 税收分类编码
     *
     * @return array
     *
     */
    public function getSubjectList(int $sid, int $pageNum = 1, int $pageSize = 15, string $spName = '', string $spbm = '')
    {
        if (!$sid) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'       => $sid,
            'sp_name'   => $spName,
            'spbm'      => $spbm,
            'page_num'  => $pageNum,
            'page_size' => $pageSize
        ];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_INVOICE_SUBJECT_LIST__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__GET_AGENT_INVOICE_SUBJECT_LIST__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取可开科目详情
     *
     * @param int $sid 供应商id
     * @param int $id
     *
     * @return array
     *
     */
    public function getSubjectInfo(int $sid, int $id = 0)
    {
        if (!($sid && $id)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid' => $sid,
            'id'  => $id
        ];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_INVOICE_SUBJECT_INFO__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__GET_AGENT_INVOICE_SUBJECT_INFO__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存可开科目
     *
     * @param  int  $sid  供应商id
     * @param  int  $id  配置id
     * @param  string  $name  商品名称
     * @param  string  $spbm  税收分类编码
     * @param  string  $taxRate  税率
     *
     * @return array
     */
    public function saveSubject($sid, $id = 0, $name = '', $spbm = '', $taxRate = '')
    {
        if (!($sid && $name && $spbm)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'      => $sid,
            'id'       => $id,
            'name'     => $name,
            'spbm'     => $spbm,
            'tax_rate' => $taxRate
        ];
        $result = $this->ExternalCodeCall(self::__SAVE_AGENT_SUBJECT__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__SAVE_AGENT_SUBJECT__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 删除可开科目
     *
     * @param  int  $sid  供应商id
     * @param  int  $id  配置id
     *
     * @return array
     */
    public function delSubject($sid, $id = 0)
    {
        if (!($sid && $id)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = ['sid' => $sid,  'id'  => $id];
        $result = $this->ExternalCodeCall(self::__DEL_AGENT_SUBJECT__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__DEL_AGENT_SUBJECT__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 查询开票额度
     *
     * @param int $sid 供应商id
     * @param int $fid 分销商id
     *
     * @return array
     *
     */
    public function getInvoiceLimit(int $sid, int $fid = 0)
    {
        if (!($sid && $fid)) {
            return $this->returnData(203, '参数异常', []);
        }

        $data = [
            'sid'    => $sid,
            'fid'    => $fid,
            'status' => 1   //状态 1=有效 2=无效
        ];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_INVOICE_LIMIT__, $data);

        pft_log('invoice/agent_invoice', json_encode([$data, self::__GET_AGENT_INVOICE_LIMIT__, $result], JSON_UNESCAPED_UNICODE));

        if (!isset($result['code']) || $result['code'] != 200) {
            return $this->returnData(500, empty($result['msg']) ? '接口异常' : $result['msg'], $result['data']);
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取我的供应商对应销售方列表（有过交易记录的供应商）
     * @param $fid
     * @param $page
     * @param $pageSize
     * @return array|mixed
     */
    public function getMySupplierOpenRecordsPage($fid, $page, $pageSize, $sid = 0, $tariffNumber = '') {
        if (!$fid) {
            return $this->returnData(203, '参数异常', []);
        }
        $data = [
            'fid' => $fid,
            'status' => 1, //0=全部 1=有效 2=无效
            'sid' => $sid,
            'tariff_number' => $tariffNumber,
            'page' => $page,
            'page_size' => $pageSize
        ];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_INVOICE_MY_SUPPLIER_OPEN_RECORDS_PAGE__, $data);
        if (!isset($result['code']) || $result['code'] != 200) {
            return [];
        }
        return $result['data'];
    }

    /**
     * 获取我的供应商对应销售方列表（有过交易记录的供应商）
     * @param $fid
     * @param $page
     * @param $pageSize
     * @return array|mixed
     */
    public function getMySupplierIdPage($fid, $page, $pageSize) {
        if (!$fid) {
            return $this->returnData(203, '参数异常', []);
        }
        $data = [
            'fid' => $fid,
            'status' => 1, //0=全部 1=有效 2=无效
            'page' => $page,
            'page_size' => $pageSize
        ];
        $result = $this->ExternalCodeCall(self::__GET_AGENT_INVOICE_MY_SUPPLIER_ID_PAGE__, $data);
        if (!isset($result['code']) || $result['code'] != 200) {
            return [];
        }
        return $result['data'];
    }
}