<?php

namespace Business\RiskManagement;

use Business\Base;
use Business\Captcha\CaptchaApi;
use Library\Cache\Cache;
use Library\Cache\CacheRedis;

class ShumeiRiskCheck extends Base
{

    const SHUMEI_REGISTER_REJECT = 'shumei_register_reject_';

    const SESSION_CHECKED_DENY_MOBILE = 'checked_deny_mobile';//记录是否黑产的手机号 true为黑产, false非黑产

    /**
     * 数美黑产手机号校验
     *
     * @param $mobile //手机号
     * @param $event //中台的事件标记 1=注册、2=充值、3=下单、4=提现、5=添加分销商、6=海报邀请分销商、7=登录
     *
     * @return array
     * <AUTHOR> lingfeng
     * 2023/1/11 15:06
     */
    public function mobileCheck($mobile, $event)
    {
        if (!ismobile($mobile)) {
            return $this->returnData(200, '空手机号');
        }

        $ip           = get_client_ip();
        $riskCenter   = new \Business\NewJavaApi\RiskCenter\ShumeiRiskCheck();
        $whiteListRes = $riskCenter->checkWhiteList($event, '', $mobile, $ip);
        if ($whiteListRes['data']) {
            return $this->returnData(200, '验证通过');
        }

        /**
         * @var $cache CacheRedis
         */
        $cache = Cache::getInstance('redis');
        $key   = self::SHUMEI_REGISTER_REJECT . $ip;

        $rejectCount = $cache->get($key);
        if ($rejectCount >= 5) {
            return $this->returnData(400, '当前IP已经提交了过多风险手机号，请稍后再试。若有疑问，请联系 400-99-22301');
        }

        $result = $riskCenter->mobileCheck($mobile, $event);

        //数美返回拒绝 记录当前ip提交手机号被拒次数
        if ($result['code'] == 200 && $result['data']['riskLevel'] == 'REJECT') {
            $cache->incrBy($key);
            $rejectCount = $cache->get($key);
            if ($rejectCount >= 5) {//如果在半小时内, 提交了5次 黑产手机号, 那么将在2小时内不得再进行注册
                $cache->expire($key, 3600 * 2);
            }

            $riskLevelDetail = $result['data']['detail']['description'] ?? '高风险';

            return $this->returnData(400,
                "手机号为{$riskLevelDetail}，请更换手机号进行注册。若有疑问，请联系 400-99-22301");
        }

        return $this->returnData(200, '验证通过');
    }

    /**
     * 数美通用风险校验
     * @param $eventId //数美事件id
     * @param $javaEvent //中台事件id
     * @param $account //数美需要的token(即account)
     * @param $eventData //相应事件所需数据
     *
     * @return array
     * <AUTHOR> lingfeng
     * 2023/1/12 16:29
     */
    public function shumeiCheck($eventId, $javaEvent, $account, $eventData, $ip = '')
    {
        if (empty($eventId) || empty($javaEvent) || empty($eventData)) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误');
        }

        $shumeiParams = [
            'eventId' => $eventId,
            'event'   => $javaEvent,
        ];

        $data = [
            'tokenId'     => $account,
            'ip'        => $ip ?: get_client_ip(),
            'timestamp' => intval(microtime(true) * 1000),
            'deviceId'  => $_SERVER['HTTP_DEVICEID'],
        ];

        $shumeiParams['data'] = array_merge($data, $eventData);

        $riskCenter = new \Business\NewJavaApi\RiskCenter\ShumeiRiskCheck();
        $checkRes   = $riskCenter->shumeiRiskCheck($shumeiParams);

        return $this->returnData($checkRes['code'], $checkRes['msg'], $checkRes['data']);
    }

    /**
     * 数美通用风险校验(含滑块验证)
     * @param $eventId
     * @param $javaEvent
     * @param $account
     * @param $eventData
     * @param $captchaCode
     *
     * @return array
     * <AUTHOR> lingfeng
     * 2023/1/12 16:30
     */
    public function shumeiCheckWithCaptcha($eventId, $javaEvent, $account, $eventData, $captchaCode, $ip = '')
    {
        if (empty($captchaCode)) {
            $checkRes = $this->shumeiCheck($eventId, $javaEvent, $account, $eventData, $ip);
            if ($checkRes['code'] == 200 && $checkRes['data']['riskLevel'] == 'REJECT') {
                return $this->returnDataV2(209, ['need_captcha' => true], '请验证滑块');
            }
        } else {
            $captchaCheck = new CaptchaApi();
            $captchaRes   = $captchaCheck->handleSecondCheck($captchaCode);
            if ($captchaRes !== true) {
                return $this->returnDataV2(209, ['need_captcha' => true], '请验证滑块');
            }
        }

        return $this->returnData(200, '验证通过');
    }
}