<?php
/**
 *
 * 专项资费配置相关
 *
 * <AUTHOR>
 * @date 2019-10-12
 */

namespace Business\ExclusiveAccount;

use Business\Base;
use Business\JavaApi\ExclusiveAccount\ExclusiveConfig;
use Business\JavaApi\ExclusiveAccount\ExclusivePurchaseRecord;
use Library\Constants\Account\BookSubject;
use Library\Model;

class SpecialedAccount extends Base
{

    protected $exclusiveConfigApi       = null;

    protected $subjectCodeArr = [
        BookSubject::VOUCHER_ACCOUNT_CODE,      //专项电子凭证账本
        BookSubject::SMS_ACCOUNT_CODE           //专项短信账本
    ];

    //收银台的回调错误日志
    const CHECK_STAND_FAIL = '/exclusive/check_stand_response_error';
    //收银台的回调成功日志
    const CHECK_STAND_OK = '/exclusive/check_stand_response_success';

    private function getExclusiveConfigBuz()
    {
        if (!$this->exclusiveConfigApi) {
            $this->exclusiveConfigApi = new ExclusiveConfig();
        }

        return $this->exclusiveConfigApi;
    }

    /**
     * 资费配置列表
     * <AUTHOR>
     * @date   2020-09-24
     *
     * @param  integer  $configType  资费类型默认传-1 0 电子凭证 1 短信
     * @param  integer  $state  资费状态 1有效，0无效
     * @param  integer  $purchaseType  购买类型 0固定 1阶梯
     * @param  integer  $page  页数
     * @param  integer  $pageSize  每页数量
     *
     * @return array
     */
    public function configList(int $configType = -1, int $state = -1, int $purchaseType = -1, int $page = 1, int $pageSize = 20)
    {
        if (($configType > -1 && !in_array($configType, [0, 1])) || ($state > -1 && !in_array($state,
                    [0, 1, 2]))) {
            return $this->returnData(204, '参数错误');
        }
        $specialedAcocountBuz = $this->getExclusiveConfigBuz();
        $result               = $specialedAcocountBuz->configList($configType, $state, $purchaseType, $page, $pageSize);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 资费配置详情
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @param  integer  $configId  配置id
     * @param  integer  $type  1阶梯 0 固定
     *
     * @return array
     */
    public function configDetail(int $configId)
    {
        if (!$configId) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getExclusiveConfigBuz()->configFixationDetail($configId);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 根据 num,subjectCode 获取配置阶梯价格信息
     * <AUTHOR>  Li
     * @date  2020-11-11
     *
     * @param  int  $num  购买数量
     * @param  int  $rateType  资费类型  0凭证 1短信
     *
     * @return array
     */
    public function queryLadderConfigByNumAndSubjectCode(int $num, int $rateType)
    {
        if (!$num || !in_array($rateType, [0, 1])) {
            return $this->returnData(204, '参数错误');
        }

        $subjectCode = $this->subjectCodeArr[$rateType];

        $result = $this->getExclusiveConfigBuz()->queryLadderConfigByNumAndSubjectCode($num, $subjectCode);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 专项资费配置购买订单
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @param  array $configId 配置id
     * @param  string $orderNum 购买的订单号
     * @param  string $beginDate 开始日期，格式'2020-09-08'
     * @param  string $endDate 截止日期 格式'2020-09-08'
     * @param  int $fid id
     * @param  int $subjectCode 账本科目
     * @param  int $purchaseType 账本科目
     * @param  int $page
     * @param  int $size
     *
     * @return array
     */
    public function relateOrderList(array $configId = [], string $orderNum = '', string $beginDate = '', string $endDate = '', int $fid = null, int $subjectCode = -1, int $purchaseType = -1, int $page = 1, int $size = 20)
    {
        if ($purchaseType > -1 && !in_array($purchaseType,[0,1])) {
            return $this->returnData(204, '购买类型参数错误');
        }

        if ($subjectCode > -1 && !in_array($subjectCode, [BookSubject::SMS_ACCOUNT_CODE, BookSubject::VOUCHER_ACCOUNT_CODE])) {
            return $this->returnData(204, '账本科目编码参数错误');
        }

        $result = (new ExclusivePurchaseRecord())->relateOrderList($configId, $orderNum, $beginDate, $endDate, $fid, $subjectCode, $purchaseType, $page, $size);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 充值后续
     * @param  string  $outTradeNo  外部订单号
     * @param  int  $sourceT  支付渠道
     * @param  string  $tradeNo  支付充值流水号
     * @param  int  $payTotalFee  总金额
     *
     * @return bool
     */
    public function platformSpecialDepositsAfter($outTradeNo, $sourceT, $tradeNo, $payTotalFee)
    {
        // 参数判断
        if (!$outTradeNo || !$tradeNo || !$payTotalFee) {
            return false;
        }
        // 避免重复通知
        $lockKey = 'prestore:' . $outTradeNo . ':' . $tradeNo;
        $cache   = \Library\Cache\Cache::getInstance('redis');
        $lockRet = $cache->lock($lockKey, 1, 120);
        if (!$lockRet) {
            pft_log('prestore/lock', "[$lockKey]预存失败,请求太频繁还有正在处理中的业务");

            return false;
        }

        // 获取支付记录
        $tradeLogModel = new \Model\TradeRecord\OnlineTrade();
        $payLogArr     = $tradeLogModel->getLog($outTradeNo, $sourceT);
        if ($payLogArr['status'] == 1) {
            $cache->rm($lockKey);

            return true;
        }

        //开通前的检测
        $logInfo          = $tradeLogModel->getLogByOrderId($outTradeNo);
        if (empty($logInfo)) {
            pft_log(self::CHECK_STAND_FAIL, "1:$tradeNo:$outTradeNo|找不到记录");
            return false;
        }

        if ($logInfo['status'] == 1) {
            pft_log(self::CHECK_STAND_FAIL, "4:$tradeNo:$outTradeNo|该笔充值已处理");
            $cache->rm($lockKey);
            return true;
        }

        if ($logInfo['total_fee'] != $payTotalFee) {
            pft_log(self::CHECK_STAND_FAIL,
                "5:{$logInfo['total_fee']}:$payTotalFee:$tradeNo:$outTradeNo|金额不正确");
            return false;
        }

        //请求java
        $description = $logInfo['description'];
        $description = json_decode($description, true);

        if (empty($description)) {
            pft_log(self::CHECK_STAND_FAIL,
                "6:$payTotalFee:$tradeNo:$outTradeNo|信息解析失败");
            return false;
        }

        $uid          = $description['uid'];
        $operateId    = $description['operate_id'];
        $fee          = $description['amount'];
        $configId     = $description['config_id'];
        $payWay       = $description['pay_way'];
        $type         = $description['type'];
        $purchaseNum  = $description['p_num'];
        $purchaseType = $description['p_type'];

        //支付方式
        if ($sourceT == 41) {
            $sourceTPayTypeMap = load_config('sourceT_pay_type_map');
            $payWay            = $sourceTPayTypeMap[$sourceT];
        }

        //迁移到Java新版充值的接口
        $res = $this->rechargeAccount($type, $uid, $purchaseNum, $fee, $configId, $purchaseType, $operateId,
            $payWay, "购买条数{$purchaseNum}", $outTradeNo, $tradeNo);

        if ($res['code'] == 200) {
            $data = [
                'trade_no' => $tradeNo,
                'dtime'    => date('Y-m-d H:i:s'),
                'status'   => 1,
            ];
            $tradeRes = $tradeLogModel->updateRecord('out_trade_no', $outTradeNo, $data);
            if (!$tradeRes) {
                pft_log(self::CHECK_STAND_FAIL, "更新在线交易记录表失败");
                return false;
            }

            $this->_addCache($uid, $type);
            pft_log(self::CHECK_STAND_OK, "{$outTradeNo}|开通成功");
            $cache->rm($lockKey);
            return true;
        } else {
            pft_log(self::CHECK_STAND_FAIL, "{$outTradeNo}|开通失败|{$res['msg']}");
            return false;
        }
    }

    /**
     * 专项费用充值
     * <AUTHOR>  Li
     * @data  2020-10-09
     *
     * @param  integer  $type  充值类型  0凭证费 1短信
     * @param  integer  $memberId  用户id
     * @param  integer  $purchaseNum  //购买总数
     * @param  integer  $price  /售价
     * @param  integer  $exclusiveId  /配置表主键
     * @param  integer  $purchaseType  0 固定 1 阶梯
     * @param  integer  $opid  下单人id
     * @param  integer  $payWay  支付方式
     * @param  string  $remarks  备注信息系
     * @param  string  $orderId  订单号
     * @param  string  $orderNo  第三方流水号
     *
     * @return array
     */
    public function rechargeAccount($type, $memberId, $purchaseNum, $price, $exclusiveId, $purchaseType, $opid, $payWay, $remarks = '', $orderId = '', $orderNo = '')
    {
        if (!$purchaseNum || !$exclusiveId || !in_array($purchaseType, [0, 1]) || !$opid) {
            return ['code' => 204, 'msg' => '参数错误'];
        }

        $exclusiveAccountpBiz = new \Business\ExclusiveAccount\ExclusiveAccount();
        $result               = $exclusiveAccountpBiz->prestore($type, $payWay, $memberId, $purchaseNum, $purchaseType,
            $orderId, $opid, $price, $exclusiveId, $remarks, $orderNo);

        pft_log('debug/lee', json_encode([
            'ac'           => 'rechargeAccount',
            'type'         => $type,
            'payWay'       => $payWay,
            'memberId'     => $memberId,
            'purchaseNum'  => $purchaseNum,
            'purchaseType' => $purchaseType,
            'orderId'      => $orderId,
            'opid'         => $opid,
            'price'        => $price,
            'exclusiveId'  => $exclusiveId,
            'remarks'      => $remarks,
            'orderNo'      => $orderNo,
            'result'       => $result,
        ], JSON_UNESCAPED_UNICODE));

        return $result;
    }

    /**
     * 添加已开通缓存
     */
    private function _addCache($uid, $type)
    {
        if ($type == 1) {
            //凭证码
            $cType = 2;
        } else {
            //短信费
            $cType = 1;
        }
        $exclusiveBusiness = new \Business\Finance\ExclusiveAccount();
        $exclusiveBusiness->addOpenCache($uid, $cType);
    }
}