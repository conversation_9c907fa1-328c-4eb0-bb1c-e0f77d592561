<?php
/**
 * 专项预存账务对账
 * @date 2020-09-24
 * <AUTHOR>  Li
 *
 */

namespace Business\ExclusiveAccount;

use Business\Base;
use \Business\JavaApi\ExclusiveAccount\ExclusiveAccountFinanceService as AccountFinance;

class AccountsHandle extends Base
{
    protected $accountFinanceApi = null;


    private function getAccountFinanceBiz()
    {
        if (!$this->accountFinanceApi) {
            $this->accountFinanceApi = new AccountFinance();
        }
        return $this->accountFinanceApi;
    }


    /**
     * 短信费和凭证费明细
     * <AUTHOR>
     * @date   2021-08-28
     *
     * @param  string $dname 用户名称
     * @param  string $account 用户账号
     * @param  string $startTime 导出开始时间  yyyy-mm-dd
     * @param  string $endTime 导出结束时间 yyyy-mm-dd
     * @param  int $groupType 账号类型 0 不限(默认) 1 正式 2 测试
     * @param  int $page 页
     * @param  int $size 条数
     *
     * @return array
     */
    public function queryTransactionAccountList(string $startTime, string $endTime, int $getTotal = 0,string $dname = null, string $account = null, int $groupType = 0, int $fid = 0, string $cname = '', $page = 1, $size = 10)
    {
        if (!$startTime || !$endTime) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getAccountFinanceBiz()->queryTransactionAccountIds($startTime, $endTime, $dname, $account, $groupType, $fid, $cname, $page, $size);

        if ($result['code'] != 200) {
            return $this->returnData($result['code'], $result['msg']);
        }
        if ($getTotal) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        }
        $accounts = $result['data']['list'];
        $infos = $this->getAccountFinanceBiz()->exportFinanceSmsAndEvidenceFee($startTime, $endTime, $accounts);

        if ($infos['code'] != 200) {
            return $this->returnData($infos['code'], $infos['msg']);
        }
        $fids = array_column($infos['data'], 'fid');
        $feeInfo = (new \Business\JavaApi\Member\MemberConfig())->getConfigWithMemberIds($fids);
        if ($feeInfo['code'] != 200 || !$feeInfo['data']) {
            $cfgRes['data'] = [];
            //$this->apiReturn(400, [], '用户费用配置获取失败!');
        }
        $feeInfo = array_key($feeInfo['data'], 'member_id');

        foreach ($infos['data'] as &$item) {
            $item['fee_code']     = isset($feeInfo[$item['fid']]['fee_code']) ? $feeInfo[$item['fid']]['fee_code'] : 0;
            $item['fee_sms']      = isset($feeInfo[$item['fid']]['fee_sms']) ? $feeInfo[$item['fid']]['fee_sms'] : 0;
        }
        $result['data']['list'] = $infos['data'];

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }
}