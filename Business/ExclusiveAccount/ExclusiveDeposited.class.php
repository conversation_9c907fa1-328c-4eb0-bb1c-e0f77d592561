<?php
/**
 * 专项预存配置
 * @date 2020-09-24
 * <AUTHOR>
 *
 */

namespace Business\ExclusiveAccount;

use Business\Base;
use Business\JavaApi\ExclusiveAccount\ExclusiveOrderRecord;
use Business\JavaApi\ExclusiveAccount\ExclusiveRemain;
use Library\Constants\Account\BookSubject;
use Library\Constants\Account\TemplateItem;

class ExclusiveDeposited extends Base
{
    protected $accountsHandleApi = null;
    protected $exclusiveRemainApi = null;

    private function getExclusiveRemainBiz()
    {
        if (!$this->exclusiveRemainApi) {
            $this->exclusiveRemainApi = new ExclusiveRemain();
        }

        return $this->exclusiveRemainApi;
    }
    private function getExclusivBiz()
    {
        if (!$this->accountsHandleApi) {
            $this->accountsHandleApi = new \Business\JavaApi\ExclusiveAccount\Exclusive();
        }

        return $this->accountsHandleApi;
    }

    protected $subjectCodeArr = [
        BookSubject::VOUCHER_ACCOUNT_CODE,      //专项电子凭证账本
        BookSubject::SMS_ACCOUNT_CODE    //专项短信账本
    ];

    protected $templateItenArr = [
        TemplateItem::SPECIAL_PRESTORE_CERT_FEE_CODE,   //专项凭证费
        TemplateItem::SPECIAL_PRESTORE_SMS_FEE_CODE,    //专项短信费
    ];

    /**
     * 用户专项预存使用记录
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @param  integer $fid 用户id
     * @param  string $orderNum 订单号
     * @param  string $beginDate 交易开始
     * @param  string $endDate 交易结束
     * @param  int $rateType 0 电子凭证 1 短信 交易类型
     * @param  integer $page 页数
     * @param  integer $pageSize 每页数量
     *
     * @return array
     */
    public function userRecordQuery(int $fid, string $orderNum = '', string $beginDate = '', string $endDate = '', int $subjectCode = -1, int $page = 1, int $pageSize = 20, $excel = false)
    {
        if (!$fid) {
            return $this->returnData(204, '参数错误');
        }
        if ($subjectCode > -1 && !in_array($subjectCode, [BookSubject::SMS_ACCOUNT_CODE, BookSubject::VOUCHER_ACCOUNT_CODE])) {
            return $this->returnData(204, '交易类型参数错误');
        }

        $exclusiveDepositedBiz = new ExclusiveOrderRecord();
        $result                = $exclusiveDepositedBiz->userRecordQuery($fid, $orderNum, $beginDate, $endDate,
            $subjectCode, $page, $pageSize);

        //获取一次订单类型
        $orderType = [];
        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            $orderNumArr = array_column($result['data']['list'], 'orderid');
            $orderTypeRes = (new \Business\NewJavaApi\Order\OrderDistribution())->getPtypeByOrderNumArr($orderNumArr);
            if ($orderTypeRes['code'] == 200 && !empty($orderTypeRes['data'])) {
                $orderType = array_column($orderTypeRes['data'], 'itemType', 'orderId');
            }
            foreach ($result['data']['list'] as &$item) {
                $item['p_type'] = $orderType[$item['orderid']] ?? '';
            }
        }

        if ($excel) {
            $data         = [];
            $subject_code = [
                '2503' => '电子凭证',
                '2504' => '短信费',
            ];
            foreach ($result['data']['list'] as $list) {
                $data[] = [
                    'trade_time'      => date('Y-m-d', $list['trade_time']),
                    'orderid'         => $list['orderid'],
                    'trade_no'        => $list['trade_no'],
                    'outter_trade_no' => $list['outter_trade_no'],
                    'subject_code'    => $subject_code[$list['subject_code']],
                    'trade_num'       => $list['trade_num'],
                    'remain_num'      => $list['remain_num'],
                    'memo'            => $list['memo'],
                ];
            }
            $result['data']['list'] = $data;
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 使用记录汇总使用条数
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @param  int  $fid  会员id
     * @param  string  $orderId  订单id
     * @param  string  $startTime 开始时间
     * @param  string  $endTime  结束时间
     *
     * @return array
     */
    public function summaryUseNum(int $fid = null, string $orderId = null, string $startTime = null, string $endTime = null)
    {

        if (!is_null($startTime) && !is_null($endTime)) {
            $startTime = strtotime($startTime . ' 00:00:00');
            $endTime   = strtotime($endTime. ' 23:59:59');
        }


        $result = (new ExclusiveOrderRecord())->summaryUseNum($fid, $orderId, $startTime,$endTime);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 查询账户剩余条数
     * <AUTHOR>  Li
     * @date  2020-11-10
     *
     * @param  int  $subjectCode  账本科目编码  账本编码 2503：电子凭证 2504：短信  不传获取所有
     * @param  array  $memberIdArr  用户id数组
     *
     * @return array
     */
    public function getRemainCount(array $memberIdArr, int $subjectCode = null)
    {
        if (!$memberIdArr) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getExclusiveRemainBiz()->getRemainCount($memberIdArr, $subjectCode);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 用户专项预存统计
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @param  integer $fid 用户ID
     *
     * @return array
     */
    public function statistics(int $fid)
    {
        $exclusiveRemainBiz = $this->getExclusiveRemainBiz();
        $result                = $exclusiveRemainBiz->statistics($fid);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 专项预存配置列表(管理端)
     * <AUTHOR>
     * @date   2020-09-16
     *
     * @param  integer  $fid  用户id
     * @param  integer  $signId  签单人id
     * @param  integer  $customer  客服id
     * @param  string  $beginDate  预存时间开始
     * @param  string  $endDate  预存时间结束
     * @param  integer  $page  页数
     * @param  integer  $pageSize  每页数量
     * @param  integer $subjectCode  账本科目 2503 2504
     * @param  integer $mark 条数规则 1.大于 2.小于
     * @param  integer $tsNum 条数
     * @param  integer $all短信费和凭证费是否都有  0 不开启 1开启
     *
     * @return array
     */
    public function accountRecord(string $beginDate, string $endDate,int $fid = 0, int $signId = 0, int $customer = 0, $subjectCode = -1, $mark = -1, $tsNum = null,  int $page = 1, int $pageSize = 20, $all = 0)
    {
        $exclusiveDepositedBiz = $this->getExclusivBiz();
        if ($beginDate && $endDate) {
            $beginDate .= ' 00:00:00';
            $endDate   .= ' 23:59:59';
        }
        $result = $exclusiveDepositedBiz->accountRecord($beginDate, $endDate,$fid, $signId, $customer,$subjectCode, $mark, $tsNum, $all,  $page,
            $pageSize);

        if ($result['code'] == 200 && !empty($result['data']['list'])) {
            //获取签单人及客服id
            $customerIdArr = array_column($result['data']['list'], 'customer_service');
            $saleIdArr     = array_column($result['data']['list'], 'sale_person');
            $memberIdArr   = array_values(array_unique(array_filter(array_merge($customerIdArr, $saleIdArr))));
            if ($memberIdArr) {
                //获取账号对应的名称
                $MemberBus  = new \Business\Member\Member();
                $memberInfo = $MemberBus->getList($memberIdArr);

                if (!$memberInfo) {
                    return $this->returnData(204, '客户信息获取失败');
                }

            }

            foreach ($result['data']['list'] as &$list) {
                $list['customer_service'] = empty($memberInfo[$list['customer_service']]['dname']) ? '' : $memberInfo[$list['customer_service']]['dname'];
                $list['sale_person']      = empty($memberInfo[$list['sale_person']]['dname']) ? '' : $memberInfo[$list['sale_person']]['dname'];
            }
            unset($list);

            foreach ($result['data']['list'] as $list) {
                $data[] = [
                    'dname'               => $list['dname'],
                    'fid'                 => $list['fid'],
                    'account'             => $list['account'],
                    'cname'               => $list['cname'],
                    'mobile'              => $list['mobile'],
                    'remain_voucher_num'  => $list['remain_voucher_num'],
                    'remain_sms_num'      => $list['remain_sms_num'],
                    'total_voucher_money' => $list['total_voucher_money'],
                    'total_sms_money'     => $list['total_sms_money'],
                    'sale_person'         => $list['sale_person'],
                    'customer_service'    => $list['customer_service'],
                    'update_date'         => $list['update_date'],
                    'sms_stat'            => $list['sms_stat'],
                    'voucher_stat'        => $list['voucher_stat'],
                ];
            }
            $result['data']['list'] = $data;
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }
}