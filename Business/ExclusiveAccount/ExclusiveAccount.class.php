<?php
/**
 *
 * 专项资费配置相关
 *
 * <AUTHOR>
 * @date 2019-10-12
 */

namespace Business\ExclusiveAccount;

use Business\Base;
use Library\Constants\Account\BookSubject;
use Library\Constants\Account\TemplateItem;
use Business\JavaApi\ExclusiveAccount\ExclusiveAccount as exclusiceAccountApi;

class ExclusiveAccount extends Base
{

    protected $exclusiceAccountApi = null;
    protected $subjectCodeArr      = [
        BookSubject::VOUCHER_ACCOUNT_CODE,      //专项电子凭证账本
        BookSubject::SMS_ACCOUNT_CODE           //专项短信账本
    ];

    protected $templateItenArr = [
        TemplateItem::SPECIAL_PRESTORE_CERT_FEE_CODE,   //专项凭证费
        TemplateItem::SPECIAL_PRESTORE_SMS_FEE_CODE,    //专项短信费
    ];

    private function getExclusiceAccountApiBiz()
    {
        if (!$this->exclusiceAccountApi) {
            $this->exclusiceAccountApi = new exclusiceAccountApi();
        }

        return $this->exclusiceAccountApi;
    }

    /**
     * 专项费用预存
     * <AUTHOR>  Li
     * @date   2020-11-10
     *
     * @param  int  $type  充值类型  0凭证费 1短信
     * @param  int  $payWay  支付方式
     * @param  int  $memberId  用户id
     * @param  int  $purchaseNum  充值条数
     * @param  int  $purchaseType  充值类型0 固定 1 阶梯
     * @param  string  $orderNum  订单号
     * @param  int  $operaterId  操作员
     * @param  int  $tradeMoney  交易金额
     * @param  int  $exclusiveId  配置id
     * @param  string  $remark  备注信息
     * @param  string  $tradeNo  流水号
     *
     * @return array
     */
    public function prestore(int $type, int $payWay, int $memberId, int $purchaseNum, int $purchaseType, string $orderNum, int $operaterId, int $tradeMoney, int $exclusiveId, string $remark = '', string $tradeNo = '')
    {
        if (!$memberId || !$purchaseNum || !in_array($purchaseType,
                [0, 1]) || !$operaterId || !$exclusiveId) {
            return $this->returnData(204, '参数错误');
        }

        if (!$orderNum) {
            $orderNum = time() . rand(100000, 999999);
        }

        //ptype映射
        $ptype2SubjectCode = load_config('ptype_2_subject_code', 'trade_record');

        $subjectCode = $ptype2SubjectCode[$payWay];
        $tradeTime   = date('Y-m-d H:i:s');

        $remark = $remark . '|流水号:' . $tradeNo;

        $rechargeSubjectCode = $this->subjectCodeArr[$type];
        $itemCode            = $this->templateItenArr[$type];

        $result = $this->getExclusiceAccountApiBiz()->prestore($memberId, $purchaseNum, $purchaseType, $orderNum,
            $operaterId, $tradeMoney, $itemCode, $subjectCode, $rechargeSubjectCode, $tradeTime, $exclusiveId, $remark,
            $tradeNo);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 专项扣款接口
     * <AUTHOR>  Li
     * @date   2020-11-10
     *
     * @param  int  $memberId  用户id
     * @param  int  $subjectCode  账本科目编码
     * @param  int  $templateItemCode  费用项编码
     * @param  int  $tradeNum  交易数
     * @param  string  $orderNum  订单号
     * @param  int  $operaterId  操作员id
     * @param  int  $channel  渠道
     * @param  string  $serialNumber  流水号
     * @param  string  $remark  备注信息
     *
     * @return array
     */
    public function deduceDeal(int $memberId, int $subjectCode, int $templateItemCode, int $tradeNum, string $orderNum, int $operaterId, $channel = 0, $serialNumber = '', $remark = '')
    {
        if (!($memberId || $subjectCode || $operaterId || $templateItemCode || $tradeNum || $orderNum || $operaterId)) {
            return $this->returnData(204, '参数错误');
        }

        if (!in_array($subjectCode, $this->subjectCodeArr)) {
            return $this->returnData(204, '账本科目类型错误');
        }

        if (!in_array($templateItemCode, $this->templateItenArr)) {
            return $this->returnData(204, '费用项类型错误');
        }

        $result = $this->getExclusiceAccountApiBiz()->deduceDeal($memberId, $subjectCode, $templateItemCode, $tradeNum,
            $orderNum, $operaterId, $channel, $serialNumber, $remark);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 余额转条数接口
     * <AUTHOR>  Li
     * @date  2020-11-10
     *
     * @param  int  $fid  用户id
     * @param  int  $accountId  用户账户id
     * @param  int  $depositedAmount  预存金额
     * @param  int  $unitPrice  单价
     * @param  int  $subjectCode  账本科目编码
     * @param  int  $itemCode  费用项编码
     * @param  int  $nums  充值数量
     * @param  int  $validPeriod  有效期 月数
     * @param  string  $maturityDate  到期时间 yyyy-mm-dd
     * @param  string  $orderNum  订单号
     * @param  string  $tradeNo  流水号
     * @param  string  $remark  备注信息
     *
     * @return array
     */
    public function dealRemain(int $fid, int $accountId, int $depositedAmount, int $unitPrice, int $subjectCode, int $itemCode, int $nums, int $validPeriod, string $maturityDate, string $orderNum, string $tradeNo = '', string $remark = '')
    {
        if (!($fid || $accountId || is_numeric($depositedAmount) || $unitPrice || $subjectCode || $orderNum)) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getExclusiceAccountApiBiz()->dealRemain($fid, $accountId, $depositedAmount, $unitPrice, $subjectCode, $itemCode, $nums, $validPeriod, $maturityDate, $orderNum, $tradeNo, $remark);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 专项扣款退还
     * <AUTHOR>  Li
     * @date   2020-12-13
     *
     * @param  string  $orderNum  订单号
     * @param  int  $opId  操作人
     * @param  string  $memo  备注信息
     *
     * @return array
     */
    public function refundDeal(string $orderNum, int $opId, string $memo)
    {
        if (!$orderNum || !$opId || !$memo) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getExclusiceAccountApiBiz()->refundDeal($orderNum, $opId, $memo);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }
}