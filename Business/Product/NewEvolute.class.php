<?php

namespace Business\Product;

use Business\Authority\DataAuthLogic;
use Business\Base;
use Business\CommodityCenter\Ticket;
use Business\Hotel\Intranet\Hotel;
use Business\Hotel\Intranet\RatePlan;
use Business\Hotel\Intranet\RoomType;
use Business\JavaApi\Product\Land;
use Business\JavaApi\Product\ManagementLand;
use Business\JavaApi\Ticket\TicketExtendAttr;
use Business\NewJavaApi\DistributionCenter\TransferProductQuery;
use Business\Order\OrderBook;
use Business\Product\SubProduct as SubProductBiz;
use Business\Product\ProductTag as ProductTagBiz;

class NewEvolute extends Base
{
    public static function formatOtherProductsByDistributionCenter(array $products, int $sid, int $memberId): array
    {
        if (empty($products)) {
            return [];
        }
        $mapProducts = array_key($products, 'itemId');

        // 批量查询Spu详情
        $spuIds = array_keys($mapProducts);
        $landDetailList = (new Land())->queryLandByIds($spuIds);
        $mapLandDetailList = array_key($landDetailList['data'], 'id');

        // 批量查询Sku详情
        $ticketDetailList = self::getTicketsByProducts($products, $sid, $memberId);
        $mapTicketDetailList = array_key($ticketDetailList, 'tid');

        $productData = [];
        foreach ($products as $spuInfo) {
            $landDetail = $mapLandDetailList[$spuInfo['itemId']] ?? [];
            $spuData = [
                'address'         => $landDetail['address'],
                'area'            => $landDetail['area'],
                'imgpath'         => $landDetail['imgpath'],
                'jtype'           => $landDetail['jtype'],
                'lid'             => $spuInfo['itemId'],
                'pType'           => $landDetail['pType'],
                'subType'         => $landDetail['subType'],
                'title'           => $landDetail['title'],
                'chainIdList'     => $spuInfo['chainIdList'],
                'ticketItemTemps' => array_slice($spuInfo['skuIdList'] ?? [], 3),
                'ticketList'      => [],
            ];
            foreach ($spuInfo['skuList'] as $skuInfo) {
                $spuData['ticketList'][] = $mapTicketDetailList[$skuInfo['skuId']] ?? [];
            }
            $productData[] = $spuData;
        }

        return self::handleLandList($productData, $sid, $memberId);
    }

    public static function formatHotelProductsByDistributionCenter(array $products, int $memberId): array
    {
        if (empty($products)) {
            return [];
        }
        // 整理出三级结构的IDS
        $poiIds = $roomTypeIds = $skuIds = [];
        foreach ($products as $poiInfo) {
            $poiIds[] = $poiInfo['itemId'];
            foreach ($poiInfo['secondList'] as $roomTypeInfo) {
                $roomTypeIds[] = $roomTypeInfo['itemId'];
                foreach ($roomTypeInfo['skuList'] as $skuInfo) {
                    $skuIds[] = $skuInfo['skuId'];
                }
            }
        }
        // 批量查询三级结构详情
        $hotels = (new Hotel())->listByIds($poiIds, $memberId);
        $mapHotels = array_key($hotels, 'centre_poi_id');
        $roomTypes = (new RoomType())->listByIds($roomTypeIds);
        $mapRoomTypes = array_key($roomTypes, 'id');
        $ratePlans = (new RatePlan())->listByIds($skuIds);
        $mapRatePlans = array_key($ratePlans, 'centre_sku_id');

        // 整理数据
        $productData = [];
        foreach ($products as $poiInfo) {
            $hotel = $mapHotels[$poiInfo['itemId']] ?? [];
            $poiData = [
                'pType'              => $poiInfo['productType'],
                'centre_poi_id'      => $poiInfo['itemId'],
                'name'               => $hotel['name'],
                'inbound_name'       => $hotel['inbound_name'],
                'star_rate_name'     => $hotel['star_rate_name'],
                'type_name'          => $hotel['type_name'],
                'province_name'      => $hotel['province_name'],
                'city_name'          => $hotel['city_name'],
                'district_name'      => $hotel['district_name'],
                'address'            => $hotel['address'],
                'contact'            => $hotel['contact'],
                'images'             => $hotel['images'],
                'more_room_type_ids' => $poiInfo['downIdList'],
                'room_types'         => [],
            ];
            foreach ($poiInfo['secondList'] as $roomTypeInfo) {
                $roomType = $mapRoomTypes[$roomTypeInfo['itemId']] ?? [];
                $roomTypeData = self::formatRoomType($roomType, $roomTypeInfo);
                foreach ($roomTypeInfo['skuList'] as $skuInfo) {
                    $ratePlan = $mapRatePlans[$skuInfo['skuId']] ?? [];
                    $roomTypeData['rate_plans'][] = self::formatRatePlan($ratePlan, $skuInfo);
                }
                $poiData['room_types'][] = $roomTypeData;
            }
            $productData[] = $poiData;
        }

        return $productData;
    }

    public static function formatRoomType(array $roomType, array $roomTypeInfoFormDC): array
    {
        return [
            'id'           => $roomType['id'],
            'name'         => $roomType['name'],
            'area'         => $roomType['area'],
            'bed_types'    => $roomType['bed_types'],
            'smoke_type'   => $roomType['smoke_type'],
            'window_type'  => $roomType['window_type'],
            'chainIdList'  => $roomTypeInfoFormDC['chainIdList'],
            'more_sku_ids' => array_slice($roomTypeInfoFormDC['skuIdList'] ?? [], 3),
            'rate_plans'   => [],
        ];
    }

    public static function formatRatePlan(array $ratePlan, array $skuInfoFormDC): array
    {
        return [
            'centre_sku_id'    => $ratePlan['centre_sku_id'],
            'centre_spu_id'    => $ratePlan['centre_spu_id'],
            'front_tags'       => $ratePlan['front_tags'],
            'name'             => $ratePlan['name'],
            'distributionList' => $skuInfoFormDC['chainList'],
        ];
    }

    /**
     * @throws \Exception
     */
    public static function export(array $params, int $sid, int $memberId): array
    {
        $page = 1;
        $pageSize = 50;

        $city = $params['city'] ? intval($params['city']) : null; // 城市code
        $oversea = $params['oversea'] != '' ? intval($params['oversea']) : null; // 境内外
        $province = $params['province'] != '' ? intval($params['province']) : null; // 省份code
        $title = $params['title'] ?: null; // 景区标题
        $supplier = $params['supplier'] ?: null; // 供应商名称
        $type = $params['type'] ?: null; // 产品类型
        $subType = $params['subType'] ?: null;// 产品子类型
        // 开启转分销状态 -1=全部 0=关闭 1=开启
        $disOpen = $params['openDistribution'] != -1 ? boolval($params['openDistribution']) : null;

        [$type, $subType] = SubProductBiz::decodeTypeAndSubType($type, $subType);
        $subType = $subType ?: ($type ? 0 : null);

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($memberId);
        $condition = $dataAuthLimit->transInOrNotCondition();
        if ($condition === false) {
            return [];
        }

        // 查询分销中心导出接口，获取分销链记录
        $data = [];
        while (true) {
            $result = TransferProductQuery::exportSaleTransferChainByPaging($page, $pageSize, 0, $city, $sid,
                $title, $disOpen, $oversea, $type, $subType, $province, $supplier, $condition['lidList'], $condition['notLidList']);
            $dcChains = $result['rows'];
            if (empty($dcChains)) {
                break;
            }
            $data = array_merge($data, self::getExportDataByTransferProductResult($result, $memberId));
            $page++;
        }

        return $data;
    }

    public static function getExportDataByTransferProductResult(array $result, int $memberId): array
    {
        $dcChains = $result['rows'];

        // 拆分为酒店分销记录和其他分销记录
        $dcHotelChains = $dcOtherChains = [];
        foreach ($dcChains as $chain) {
            if (self::isLandChain($chain)) {
                $dcOtherChains[] = $chain;
            } else {
                $dcHotelChains[] = $chain;
            }
        }

        // 查询其他分销记录的景区信息和票类信息
        $otherSkuIds = array_column($dcOtherChains, 'skuId');
        $mapTicketList = [];
        if (!empty($otherSkuIds)) {
            $ticketList = (new Ticket())->queryTicketInfoByIds($otherSkuIds);
            foreach ($ticketList as $ticket) {
                $mapTicketList[$ticket['ticket']['id']] = $ticket;
            }
        }

        // 查询酒店分销记录的酒店信息、房型信息和价格计划信息
        $poiIds = $roomTypeIds = $hotelSkuIds = [];
        foreach ($dcHotelChains as $chain) {
            if (!empty($chain['roomTypeId'])) {
                $roomTypeIds[] = $chain['roomTypeId'];
            }
            if (!empty($chain['skuId'])) {
                $hotelSkuIds[] = $chain['skuId'];
            }
        }
        $roomTypes = [];
        $mapHotels = $mapRoomTypes = $mapRatePlans = [];
        if (!empty($roomTypeIds)) {
            $roomTypes = (new RoomType())->listByIds($roomTypeIds);
            $mapRoomTypes = array_key($roomTypes, 'id');
        }
        if (!empty($hotelSkuIds)) {
            $ratePlans = (new RatePlan())->listByIds($hotelSkuIds);
            $mapRatePlans = array_key($ratePlans, 'centre_sku_id');
        }
        foreach ($roomTypes as $roomType) {
            $poiIds[] = $roomType['centre_poi_id'];
        }
        $poiIds = array_unique($poiIds);
        if (!empty($poiIds)) {
            $hotels = (new Hotel())->listByIds($poiIds, $memberId);
            $mapHotels = array_key($hotels, 'centre_poi_id');
        }

        // 获取省市配置
        $provinces = load_config('province', 'account');
        $cities = load_config('cities', 'account');
        $areas = [];
        foreach ($provinces as $value) {
            $areas[$value["area_id"]] = $value["area_name"];
        }
        foreach ($cities as $value) {
            foreach ($value as $value2) {
                $areas[$value2['area_id']] = $value2["area_name"];
            }
        }

        // 获取景区类型的景点名称
        $landIdList = $mapManagementLandTitleWithLid = [];
        foreach ($dcOtherChains as $chain) {
            if (!self::isLandChain($chain)) {
                continue;
            }
            if (empty($mapTicketList[$chain['skuId']])) {
                throw new \Exception('票类信息不存在, skuId=' . $chain['skuId']);
            }
            $ticketInfo = $mapTicketList[$chain['skuId']];
            if ($ticketInfo['land']['p_type'] == 'A') {
                $landIdList[] = $ticketInfo['land']['id'];
            }
        }
        if (!empty($landIdList)) {
            $landIdList = array_values(array_unique($landIdList));
            $managementLandResult = (new ManagementLand())->queryResourceByLid($landIdList, 'A');
            if ($managementLandResult['code'] == 200) {
                foreach ($managementLandResult['data'] as $datum) {
                    $mapManagementLandTitleWithLid[$datum['lid']] = $datum['list'][0]['title'];
                }
            }
        }

        // 根据原始顺序，拼接数据
        $data = [];
        foreach ($dcChains as $chain) {
            if (self::isLandChain($chain)) {
                $ticketInfo = $mapTicketList[$chain['skuId']];
                // 获取省、市
                list($prov, $city) = explode('|', $ticketInfo['land']['area']);
                $data[] = [
                    // 省
                    $areas[$prov] ?? '',
                    // 市
                    $areas[$city] ?? '',
                    // lid
                    $lid = $ticketInfo['land']['id'],
                    // 产品名称
                    $ticketInfo['land']['title'],
                    // tid
                    $ticketInfo['ticket']['id'],
                    // 票种名称
                    $ticketInfo['ticket']['title'],
                    // 供应商名称
                    $chain['supplierName'],
                    // 在售,停售
                    '在售',
                    // 关联的景区POI名称,若无则显示无景点
                    $mapManagementLandTitleWithLid[$lid] ?? '无景点',
                    // 景区级别,非A等
                    $ticketInfo['land']['jtype'],
                    // 发布产品时勾选的主题
                    trim($ticketInfo['land']['topic'], ','),
                    // -
                    '',
                    // 创建tid的时间
                    date('Y-m-d H:i:s', $ticketInfo['ticket']['update_time']),
                    // 供应商自己是成本价,分销商是上级给的结算价
                    round($chain['settlementPrice'] / 100, 2),
                    // 供应商定义的零售价
                    round($chain['retailPrice'] / 100, 2),
                    // 供应商定义的门市价
                    round($chain['marketPrice'] / 100, 2),
                    // 票种中填写的取票信息
                    $ticketInfo['ticket']['getaddr'],
                ];
            } else {
                if (empty($mapRoomTypes[$chain['roomTypeId']])) {
                    throw new \Exception('房型信息不存在, roomTypeId=' . $chain['roomTypeId']);
                }
                $roomType = $mapRoomTypes[$chain['roomTypeId']];
                if (empty($mapHotels[$roomType['centre_poi_id']])) {
                    throw new \Exception('酒店信息不存在, centre_poi_id=' . $roomType['centre_poi_id']);
                }
                $hotel = $mapHotels[$roomType['centre_poi_id']];
                if (empty($mapRatePlans[$chain['skuId']])) {
                    throw new \Exception('价格计划信息不存在, skuId=' . $chain['skuId']);
                }
                $ratePlan = $mapRatePlans[$chain['skuId']];

                $data[] = [
                    // 省
                    $hotel['province_name'],
                    // 市
                    $hotel['city_name'],
                    // spuid
                    $ratePlan['centre_spu_id'],
                    // 房型名称
                    $roomType['name'],
                    // skuid
                    $ratePlan['centre_sku_id'],
                    // 价格计划名称
                    $ratePlan['name'],
                    // 供应商名称
                    $chain['supplierName'],
                    // 在售,停售
                    '在售',
                    // 酒店POI名称
                    $hotel['name'],
                    // 酒店星级
                    $hotel['star_rate_name'],
                    // -
                    '',
                    // 酒店基本属性中的标签
                    implode(array_column($hotel['tags'], 'tag_name'), ','),
                    // 创建sku的时间
                    $ratePlan['created_at'],
                    // 供应商自己是成本价,分销商是上级给的结算价
                    round($chain['settlementPrice'] / 100, 2),
                    // 供应商定义的零售价
                    round($chain['retailPrice'] / 100, 2),
                    // 供应商定义的门市价
                    round($chain['marketPrice'] / 100, 2),
                    // -
                    '',
                ];
            }
        }

        return $data;
    }

    private static function isLandChain(array $chain): bool
    {
        return empty($chain['roomTypeId']) || $chain['roomTypeId'] == 0;
    }

    public static function isEnabled(int $memberId): bool
    {
        return TransferProductQuery::queryNewPoiDistributionFunctionUser($memberId);
    }

    private static function handleLandList(array $landList, int $sid, int $memberId): array
    {
        foreach ($landList as &$land) {
            //资源中心 供应按钮展示 产品类型：门票、酒店、特产、演出、套票、线路
            $land['showResourceCenterBtn'] = in_array($land['pType'], ['A', 'B', 'C', 'J', 'H', 'F']);
        }

        //SKU标签
        $landList = (new ProductTagBiz())->handleDistributionProductsSkuTag($sid, $landList);

        return (new Product())->getProductTagForDistribution($sid, $memberId, $landList);
    }

    private static function getTicketsByProducts(array $products, int $sid, int $memberId): array
    {
        $skuIds = [];
        foreach ($products as $product) {
            if (empty($product['skuList'])) {
                continue;
            }
            $skuIds = array_merge($skuIds, array_column($product['skuList'], 'skuId'));
        }
        $skuIds = array_unique($skuIds);
        if (empty($skuIds)) {
            return [];
        }
        $ticketsInfo = (new Ticket())->queryTicketInfoByIds($skuIds);
        $mapTicketInfo = [];
        foreach ($ticketsInfo as $ticket) {
            $mapTicketInfo[$ticket['ticket']['id']] = $ticket;
        }

        // 重新组装原始数据
        $ticketList = [];
        foreach ($products as $product) {
            if (empty($product['skuList'])) {
                continue;
            }
            foreach ($product['skuList'] as $sku) {
                $ticketInfo = $mapTicketInfo[$sku['skuId']] ?? [];
                $ticketList[] = self::formatTransferTicket($ticketInfo, $sku);
            }
            $ticketList = array_merge($ticketList, $product['skuList']);
        }

        return self::handleTicketList($ticketList, $mapTicketInfo, $skuIds, $sid, $memberId);
    }

    public static function handleTransferTicketList(array $transferTicketList, array $skuIds, int $sid, int $memberId): array
    {
        // 批量查询Sku详情
        $ticketsInfo = (new Ticket())->queryTicketInfoByIds($skuIds);
        $mapTicketInfo = [];
        foreach ($ticketsInfo as $ticket) {
            $mapTicketInfo[$ticket['ticket']['id']] = $ticket;
        }

        $newTransferTicketList = [];
        foreach ($transferTicketList as $transferTicket) {
            $ticketInfo = $mapTicketInfo[$transferTicket['skuId']] ?? [];
            $newTransferTicketList[] = self::formatTransferTicket($ticketInfo, $transferTicket);
        }

        return self::handleTicketList($newTransferTicketList, $mapTicketInfo, $skuIds, $sid, $memberId);
    }

    private static function formatTransferTicket(array $ticketInfo, array $transferSkuInfo): array
    {
        $newSku = [
            'ddays'             => $ticketInfo['ticket']['ddays'],
            'entranceTimeLimit' => $ticketInfo['land_f']['entrance_time_limit'],
            'explain'           => '',
            'getaddr'           => $ticketInfo['ticket']['getaddr'],
            'ifVerify'          => $ticketInfo['ticket']['if_verify'],
            'lid'               => $ticketInfo['land_f']['lid'],
            'pType'             => $ticketInfo['land_f']['p_type'],
            'type'              => $ticketInfo['land_f']['p_type'],
            'pid'               => $ticketInfo['land_f']['pid'],
            'refundAudit'       => $ticketInfo['ticket']['refund_audit'],
            'refundEarlyTime'   => $ticketInfo['ticket']['refund_early_time'],
            'refundRule'        => $ticketInfo['ticket']['refund_rule'],
            'tid'               => $ticketInfo['ticket']['id'],
            'title'             => $ticketInfo['ticket']['title'],
            'useEarlyDays'      => $ticketInfo['ticket']['use_early_days'],
            'usetime'           => self::formatUseTime($ticketInfo),
        ];
        foreach ($transferSkuInfo['chainList'] as $chain) {
            $newSku['distributionList'][] = [
                'aids'              => $chain['aids'],
                'costPrice'         => $chain['settlementPrice'],
                'distributionLevel' => $chain['lvl'],
                'distributionLower' => intval($chain['hasDownDistribution']),
                'openDistribution'  => $chain['active'],
                'retailPrice'       => $chain['retailPrice'],
                'supplierId'        => $chain['sid'],
                'supplierName'      => $chain['supplierName'],
                'tid'               => $transferSkuInfo['skuId'],
                'dayStock'          => $chain['dayStock'],
            ];
        }
        return $newSku;
    }

    private static function handleTicketList(array $ticketList, array $mapTicketInfo, array $skuIds, int $sid, int $memberId): array
    {
        // 以下代码基本参考 \Controller\product\Evolute::getTickets 逻辑处理

        // 额外填写更多信息
        $ticketSpecialValRes = (new TicketExtendAttr())->queryTicketSpecialVal($skuIds, [
            'is_start_stop_sale_limit', 'auto_sale_rule', 'refund_before_early_time', 'refund_after_early_time'
        ]);
        $ticketSpecialValRes = $ticketSpecialValRes['data'] ?? [];
        $autoArr = $ticketAttr = [];
        $refundBeforeEarlyTime = $refundAfterEarlyTime = [];
        foreach ($ticketSpecialValRes as $ticketConf) {
            if ($ticketConf['key'] == 'auto_sale_rule') {
                $autoArr[$ticketConf['ticketId']] = $ticketConf;
            }
            if ($ticketConf['key'] == 'is_start_stop_sale_limit') {
                $ticketAttr[$ticketConf['ticketId']] = $ticketConf;
            }
            if ($ticketConf['key'] == 'refund_before_early_time') {
                $refundBeforeEarlyTime[$ticketConf['ticketId']] = $ticketConf;
            }
            if ($ticketConf['key'] == 'refund_after_early_time') {
                $refundAfterEarlyTime[$ticketConf['ticketId']] = $ticketConf;
            }
        }

        foreach ($ticketList as &$value) {
            $ticketInfo = $mapTicketInfo[$value['tid']] ?? [];

            $value['autoSaleRule'] = [];
            if (!empty($autoArr[$value['tid']]['val'])) {
                $value['autoSaleRule'] = json_decode($autoArr[$value['tid']]['val'], true);
            }
            $value['isStartStopSaleLimit'] = $ticketAttr[$value['tid']]['val'] ?? 0;
            //参数转换
            $value['valid_period_type'] = $ticketInfo['ticket']['delaytype'];
            $value['valid_period_days'] = $ticketInfo['ticket']['delaydays'];
            $value['type'] = $value['pType'];
            $value['use_early_days'] = $value['useEarlyDays'];
            $value['valid_period_start'] = $ticketInfo['ticket']['order_start'];
            $value['valid_period_end'] = $ticketInfo['ticket']['order_end'];

            //添加期票标识
            $value['pre_sale'] = $ticketInfo['ticket']['pre_sale'] ?? null;

            $value['ext']['son_ticket_validity'] = $ticketInfo['ext']['son_ticket_validity'] ?? 0;
            $value['ext']['effective_limit'] = $ticketInfo['ext']['effective_limit'] ?? '';

            //演出票类有效期特殊处理
            if ($value['type'] == 'H') {
                $value['validityDate'] = OrderBook::getShowValidityDate($value);
            } else {
                $value['validityDate'] = OrderBook::getValidityDate($value);
            }

            if (in_array($value['type'], ['A', 'B', 'C', 'F', 'G'])) {
                $ticketInfoData = [
                    'refund_rule'              => $value['refundRule'],
                    'refund_before_early_time' => $refundBeforeEarlyTime[$value['tid']]['val'] ?? 0,
                    'refund_after_early_time'  => $refundAfterEarlyTime[$value['tid']]['val'] ?? 0,
                ];
                $value['refundRules'] = (new \Business\Product\Ticket())->refundRuleTag($ticketInfoData);
            } else {
                //处理退款规则描述
                $value['refundRules'] = OrderBook::handleRefundRule($value['refundAudit'], $value['refundRule'],
                    $value['refundEarlyTime']);
            }
        }

        //SKU标签
        $ticketList = (new ProductTagBiz())->handleDistributionProductsSkuTagMoreTickets($sid, $ticketList);

        return (new Product())->getProductTagForDistributionMoreTickets($sid, $memberId, $ticketList);
    }

    public static function formatUseTime($ticketInfo): string
    {
        $valid = '';
        if ($ticketInfo['delaydays'] > 0) {
            if ($ticketInfo['delaytype'] == 0) {
                $valid = '游玩日期(含)' . $ticketInfo['delaydays'] . '天内有效';
            }
            if ($ticketInfo['delaytype'] == 1) {
                $valid = '下单日期(含)' . $ticketInfo['delaydays'] . '天内有效';
            }
        }
        if ($ticketInfo['delaydays'] == 0) {
            $valid = '订单当天有效';
        }
        return $valid;
    }
}