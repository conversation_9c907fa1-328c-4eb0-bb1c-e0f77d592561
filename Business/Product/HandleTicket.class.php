<?php
/**
 * 门票属性的相关操作-业务层
 * User: Luo<PERSON>hen <PERSON>n
 * Date: 2017/7/21
 * Time: 17:26
 */

namespace Business\Product;

use Business\Base;
use Business\CardSolution\TimingProduct;
use Business\JavaApi\Product\SpecialTicket;
use Business\JavaApi\Product\TicketDeputyService;
use Business\JavaApi\Product\TimeShareOrdeAction;
use Business\JavaApi\TicketApi;
use Business\Product\Show as ShowBiz;
use Library\Business\TerminalCache;
use Library\MessageNotify\OtaProductNotify;
use Library\Resque\Queue;
use Model\Member\Member;
use Model\Product\AnnualCard;
use Model\Product\Ticket as TicketModels;
use Business\JavaApi\StorageApi;
use Business\JavaApi\Product\Ticket as NewTicketApi;
use Business\JavaApi\Product\PackageTicket as PackageTicketApi;
use Business\JavaApi\Ticket\TicketExtendAttr;
use Business\ElectronicInvoices\InvoiceApi;

class HandleTicket extends Base
{
    private $_annualCard;
    private $_memberId;
    private $_operateId;

    private $specialType = [
        1 => '年龄限制',
        2 => '性别限制',
        3 => '区域限制',
        4 => '免票限制',
    ];

    public function __construct($memberId, $operateId = 0)
    {
        $this->_memberId  = $memberId;
        $this->_operateId = $operateId;
    }

    /**
     * 新增门票, 该接口已废弃, 创建门票接口请使用setTicketNew接口 -- jinmin
     *
     * @deprecated
     *
     * @date   2017-07-21
     * <AUTHOR> Lan
     *
     * @param  array  $ticketData  门票信息
     *
     * @deprecated
     *
     * @return array
     */
    public function addTicket($ticketData)
    {
        //$business = new TicketApi();
        //$res      = $business->addTicket($ticketData);
        //return $res;
        return false;
    }

    /**
     * 新增门票
     *
     * @date   2017-07-21
     * <AUTHOR> Lan
     *
     * @param  array  $ticketData  门票信息
     *
     * @deprecated
     *
     * @return array
     */
    public function updateTicket($ticketData)
    {
        //$business = new TicketApi();
        //$res      = $business->updateTicket($ticketData);
        //return $res;
        return false;
    }

    /**
     * 创建或更新门票新版
     * <AUTHOR>
     * @date 2019-10-27
     *
     * @param  array  $ticketData  门票信息
     * @param  int  $memberId  当前用户ID
     *
     * @return array
     */
    public function setTicketNew(array $ticketData, int $memberId)
    {
        // 转换创建门票字段
        $ticketApi                = new TicketApi();
        $newTicketData            = $ticketApi->createOrUpdateTicketAttrMap($ticketData);
        $ticketBuyLimitInfoContex = $this->handleLandBuyLimitConfig($newTicketData);
        // 一些新接口需要手动转换的参数
        $convertData   = [
            'tid' => $newTicketData['id'],
        ];
        $newTicketData = array_merge($newTicketData, $convertData);

        // 生成上下文
        $ticketContext            = new \Business\JavaApi\Context\Product\TicketInfoContext($newTicketData);
        $landFContext             = new \Business\JavaApi\Context\Product\LandFInfoContext($newTicketData);
        $productContext           = new \Business\JavaApi\Context\Product\ProductInfoContext($newTicketData);
        // 处理参数名重复的问题, 还有一些创建参数java需要，但是原先并没有
        unset($landFContext->id, $productContext->id);
        if ($newTicketData['id'] < 0) {
            // 库存java需要， 但是已废弃
            $productContext->storage = -1;
            $ticketContext->storage  = -1;
            unset($ticketContext->id);
        }

        $operaterId = !empty($this->_operateId) ? (int)$this->_operateId : $memberId;

        // java接口不需要该参数
        unset($productContext->pName);
        $data = [
            'ticketInfo'            => $ticketContext,
            'landFInfo'             => $landFContext,
            'productInfo'           => $productContext,
            'accountId'             => $memberId,
            'operaterId'            => $operaterId,
            'specialConfigInfo'     => ['specialConfig' => $ticketData['special_config'] ? json_encode($ticketData['special_config']) : ""],
            'landBuyLimitConfigDTO' => $ticketBuyLimitInfoContex,
        ];

        $data                = array_merge($data, $newTicketData);
        $createTicketContext = new \Business\JavaApi\Context\Product\CreateOrUpdateTicketContext($data);

        $newTicketBiz = new NewTicketApi();

        if (($ticketData['id'] && $ticketData['id'] > 0)) {
            // 更新
            $res = $newTicketBiz->updateTicket($createTicketContext);
            if ($res['code'] == 200) {
                if (!empty($ticketData['extAttribute'])) {
                    $versionUuid = uniqid();
                    $extData = [];
                    foreach ($ticketData['extAttribute'] as $attrItem) {
                        $extData[] = [
                            'ticketId' => $ticketData['id'],
                            'key'      => $attrItem['key'],
                            'val'      => $attrItem['val'],
                        ];
                    }
                    $ticketExtConf = new TicketExtendAttr();
                    $ticketExtConfRes = $ticketExtConf->save($memberId, $memberId, $extData, $versionUuid);
                }
            }

        } else {
            // 新增
            $res = $newTicketBiz->createTicket($createTicketContext);
            if ($res['code'] == 200) {
                if (!empty($ticketData['extAttribute'])) {
                    $versionUuid = uniqid();
                    $extData = [];
                    foreach ($ticketData['extAttribute'] as $attrItem) {
                        $extData[] = [
                            'ticketId' => $res['data']['ticketId'],
                            'key'      => $attrItem['key'],
                            'val'      => $attrItem['val'],
                        ];
                    }
                    $ticketExtConf = new TicketExtendAttr();
                    $ticketExtConfRes = $ticketExtConf->save($memberId, $memberId, $extData, $versionUuid);
                }
            }
        }


        return $res;
    }

    /**
     *
     * <AUTHOR>
     * @date 2019/10/23
     *
     * @param  mixed  $ticketIds  门票ID
     * @param  string  $title  特殊配置名称
     * @param  string  $specialConfig  特殊配置信息
     * @param  int  $accountId
     * @param  int  $operaterId
     *
     * @return array
     */
    public function addSpecialTicket($ticketIds, string $title, array $specialConfigArr, int $accountId = 0, int $operaterId = 0)
    {
        $ticketIdArr   = explode(',', $ticketIds);
        $specialConfig = json_encode($specialConfigArr);

        $business = new SpecialTicket();
        $res      = $business->addSpecialTicket($ticketIdArr, $title, $specialConfig, $accountId, $operaterId);

        return $res;
    }

    /**
     * 门票数据保存前处理
     * @TODO：全部入口都要调用这个方法作为预处理
     *
     * @date   2017-07-21
     * <AUTHOR> Lan
     *
     * @param  array  $ticketData  单个门票信息数组
     * @param  array  $priceData  单个价格信息数组
     *
     * @return array
     */
    public function beforeHandel($ticketData, $priceData)
    {
        $checkResult = $this->_checkTicket($this->_memberId, $ticketData, $priceData);

        if ($checkResult[0] != 0) {
            return $checkResult;
        }
        if (isset($ticketData['check_terminal_info'])) {
            $handleRes = self::handleTerminalInfoBySave($ticketData['check_terminal_info'], $ticketData['verify_way'],
                $ticketData['identity_info'], $ticketData['check_terminal_time_info']);
            if ($handleRes[0] != 200) {
                return $handleRes;
            }

            $ticketData['check_terminal_info'] = $handleRes[1];
        }

        if ($ticketData['account_id'] != $this->_memberId) {
            return self::_return(201, '非供应商,无法编辑保存');
        }

        if ($ticketData['status'] == 6) {
            return self::_return(206, '删除的门票无法保存');
        }

        if ($ticketData['ticket_price'] === '' || $ticketData['ticket_price'] === null) {
            return self::_return(206, '门票价格不能为空');
        }

        if (trim($ticketData['name']) == '') {
            return self::_return(206, '门票名称不能为空');
        }

        if (isset($ticketData['ticket_changing_range']) && (!is_numeric($ticketData['ticket_changing_range']) || $ticketData['ticket_changing_range'] < 0 || $ticketData['ticket_changing_range'] > 365)) {
            return self::_return(202, '改签日期有误');
        }

        $ticketData['name'] = \content_filter($ticketData['name']);

        $ticketData['introduction'] = \content_filter($ticketData['introduction']);

        if ($ticketData['buy_limit_type'] < 5 && isset($ticketData['buy_limit_unit']) && $ticketData['buy_limit_unit'] == 1) {
            $ticketData['buy_limit_type'] += 2;
            unset($ticketData['buy_limit_unit']);
        }

        if (isset($ticketData['docking_url']) && $ticketData['docking_url'] != '') {
            $ticketData['docking_offline'] = 1;
        }

        if (!isset($ticketData['uprice']) || isset($ticketData['uprice']) && !$ticketData['uprice']) {
            $ticketData['uprice'] = $ticketData['ticket_price'] * 100;
        }

        //票类渠道未传递或者格式非法，则为默认
        if (!isset($ticketData['shop'])) {
            $ticketData['shop'] = '1,2,5,10,11,12,13';
        }

        if ($ticketData['shop'] !== '') {
            $saleChannel = load_config('sale_channel', 'business');
            //前端传来的值是否在定义范围内
            $diff = array_diff(explode(',', $ticketData['shop']), array_keys($saleChannel));

            if ($diff) {
                return self::_return(206, '门票渠道值未定义');
            }
        }

        if ($ticketData['modify_limit_time']) {
            [$limitDay, $limitHour, $limitMinute] = explode(',', $ticketData['modify_limit_time']);
            if ($limitHour > 23 || $limitMinute > 59) {
                return self::_return(206, '退改时间输入有误');
            }
            $ticketData['modify_limit_time'] = $limitDay * 86400 + $limitHour * 3600 + $limitMinute * 60;
        }

        if (!isset($ticketData['after_draft_days']) || !$ticketData['after_draft_days']) {
            $ticketData['after_draft_days'] = 0;
        } else if (!is_numeric($ticketData['after_draft_days']) || $ticketData['after_draft_days'] < 1 || $ticketData['after_draft_days'] > 15) {
            return self::_return(206, '取票后有效期设置错误,不能小于1或大于15');
        }

        //对指定上下架时间做个校验
        if (isset($ticketData['auto_grounding_status']) && $ticketData['auto_grounding_status'] == 3 && isset($ticketData['auto_grounding_date']) && (date('Y-m-d H:i:s',
                    strtotime($ticketData['auto_grounding_date'])) != $ticketData['auto_grounding_date'])) {
            return self::_return(206, '指定上架时间有误，请确认');
        }
        if (isset($ticketData['auto_undercarriage_status']) && $ticketData['auto_undercarriage_status'] == 2 && isset($ticketData['auto_undercarriage_date']) && (date('Y-m-d H:i:s',
                    strtotime($ticketData['auto_undercarriage_date'])) != $ticketData['auto_undercarriage_date'])) {
            return self::_return(206, '指定下架时间有误，请确认');
        }

        if (isset($ticketData['notice_before_expire_days']) && $ticketData['notice_before_expire_days'] > 7) {
            return self::_return(206, '有效期到期通知游客配置最大7天');
        }
        //固定有效期暂时注释
        //        if (isset($ticketData['ticket_total_storage']) && $ticketData['valid_period_type'] == 2) {
        //            if ($ticketData['ticket_total_storage'] < 1 || $ticketData['ticket_total_storage'] > 9999999) {
        //                return self::_return(206, '设置总库存不能小于1或大于9999999');
        //            }
        //            //日库存都设置为不限
        //            array_walk($priceData, function (&$price, $k) {
        //                $price['storage'] = -1;
        //            });
        //        }

        //产品的价格数据校验
        if ($priceData && is_array($priceData)) {
            foreach ($priceData as $tmpPriceKey => $tmpPriceData) {
                if (!is_numeric($tmpPriceData['storage'])) {
                    //写日志，便于后面的排查
                    pft_log('product/price_exception', json_encode([$ticketData, $priceData], JSON_UNESCAPED_UNICODE));

                    return self::_return(206, "日库存的设置异常【{$tmpPriceData['storage']}】");
                }
            }
        }

        switch ($ticketData['type']) {
            case 'B':
                //团号
                if (isset($ticketData['g_number']) && $ticketData['g_number']) {
                    $ticketData['touring_party_no'] = $ticketData['g_number'] . '{fck_date}';

                    if (isset($ticketData['s_number']) && $ticketData['s_number']) {
                        $ticketData['touring_party_no'] .= '-' . $ticketData['s_number'];
                        unset($ticketData['s_number']);
                    }
                    unset($ticketData['g_number']);
                }

                //集合地点
                $station                        = str_replace('；', ';', $ticketData['assembling_place']);
                $ticketData['assembling_place'] = json_encode(explode(';', $station), JSON_UNESCAPED_UNICODE);

                //线路游玩天数
                if (isset($ticketData['play_days'])) {
                    $ticketData['play_days'] = intval($ticketData['play_days']);
                }

                break;

            case 'I':
                $crdModel = $this->_getAnnualInstance($ticketData['tid']);
                //if (!isset($crdConf)) {
                //    $crdConf = [];
                //}

                $defaultParams = $crdModel->createDefaultParams();
                $ticketData    = array_merge($ticketData, $defaultParams);

                //$crdConf['auto_act_day'] = isset($ticketData['auto_active_days']) ? intval($ticketData['auto_active_days']) : -1; //自动激活天数 -1 不自动激活
                //$crdConf['srch_limit']   = isset($ticketData['search_limit']) ? $ticketData['search_limit'] : 1; //购买搜索限制 0 不限制 1：卡号（实体卡/虚拟卡）  2：身份证号 4：手机号
                //$crdConf['cert_limit']   = isset($ticketData['cert_limit']) ? intval($ticketData['cert_limit']) : 0; //身份证限制 0 无需填写 1：需要填写

                if ($ticketData['family_card_num'] > 5) {
                    return self::_return(203, '多人卡人数不能超过5');
                }
                $ticketData['family_card_num'] = (int)$ticketData['family_card_num'];

                //激活通知 0 不通知 1 通知游客 2通知供应商 3 通知游客和供应商
                $notice_tourist  = isset($ticketData['nts_tour']) ? ($ticketData['nts_tour'] + 0) : 0;
                $notice_supplier = isset($ticketData['nts_sup']) ? ($ticketData['nts_sup'] + 0) : 0;

                //$crdConf['act_notice'] = 0;
                //if ($notice_tourist) {
                //    $crdConf['act_notice'] += 1;
                //}
                //if ($notice_supplier) {
                //    $crdConf['act_notice'] += 2;
                //}

                //if (count($ticketData['priv'])) {
                //    $crdInfo = json_encode(['crdConf'   => $crdConf, 'crdPriv'   => $ticketData['priv']]);
                //    $crdModel->rmCache();
                //    $crdModel->setCache($crdInfo);
                //} else {
                //    return self::_return(203, '年卡特权信息未配置');
                //}

                if (count($ticketData['group_priv'])) {
                    $crdInfo = json_encode(['groupPriv' => $ticketData['group_priv']]);
                    $crdModel->rmCache();
                    $crdModel->setCache($crdInfo);
                } else {
                    return self::_return(203, '年卡特权信息未配置');
                }

                break;

            case 'C':
                //酒店默认都可以
                $ticketData['verify_disable_week'] = '';
                break;

            case 'H':
                //酒店默认都可以
                $ticketData['docking_url']         = MAIN_DOMAIN . 'api/Product_check_h.php';
                $ticketData['docking_offline']     = 1;
                $ticketData['verify_disable_week'] = '';
                break;

            default:
                break;
        }

        //票类特殊属性 1年龄限制 2性别限制 3区域限制 4接待票配置
        if (!empty($ticketData['special_config_type'])) {
            $specialConfigValue           = is_array($ticketData['special_config_value']) ? $ticketData['special_config_value'] : [];
            $specialConfig                = [
                'type'  => $ticketData['special_config_type'],
                'name'  => isset($ticketData['special_config_name']) ? $ticketData['special_config_name'] : '',
                'value' => $specialConfigValue,
            ];
            $ticketData['special_config'] = json_encode($specialConfig);
        }

        // * <AUTHOR>
        // * @date   2018/07/05
        // * -1不可退，而且是可以提现; refund_rule_bool：下单后即可提现按钮
        if ($ticketData["refund_rule"] == 2 && $ticketData["refund_rule_bool"] == 1) {
            $ticketData["refund_rule"] = -1; //表示不可退，而且是可以提现
        }

        //保存数据要进行一次过滤
        if ($ticketData['id'] > 0) {
            //当不可退，而且是可以提现 要进行一次过滤
            $ticketBiz = new \Business\CommodityCenter\Ticket();
            $queryInfo = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($ticketData['id']));
            //$ticketApi = new \Business\JavaApi\TicketApi;
            //$queryInfo = $ticketApi->getTickets($ticketData['id']);
            if ($queryInfo["refund_rule"] == -1) {
                $ticketData["refund_rule"] = $queryInfo["refund_rule"]; //退票规则
                unset($ticketData["expire_action_days"]); //过期多少天后自动处理
                unset($ticketData["num_modify"]); //订单修改规则
                unset($ticketData["refund_type"]); //取消费用类型
            } else {
                $ticketData["refund_redis_check"] = 1; //用在后置方法这个判断是否加入redis队列
            }
            //增加是否更新库存开启日期临时标识
            $ticketData["change_storage"] = strtotime($queryInfo['valid_period_start']) != strtotime($ticketData['valid_period_start']);
        }
        //填写更多取票人游客信息
        if ($ticketData['more_credentials'] && !empty($ticketData['more_credentials_content'])) {
            $type                                      = $ticketData['more_credentials'];
            $ticketData['more_credentials']            = [];
            $ticketData['more_credentials']['type']    = $type;
            $ticketData['more_credentials']['content'] = $ticketData['more_credentials_content'];
            $ticketData['more_credentials']            = json_encode($ticketData['more_credentials']);
            unset($type);
            unset($ticketData['more_credentials_content']);
        } else {
            $ticketData['more_credentials'] = '';
        }

        //自定义标签
        if ($ticketData['type'] == 'A' && $ticketData['open_custom_tag'] && !empty($ticketData['custom_tag_json'])) {
            $ticketData['custom_tag_json'] = json_encode($ticketData['custom_tag_json']);
        } else {
            $ticketData['custom_tag_json'] = '';
            $ticketData['open_custom_tag'] = 0;
        }

        if (\Business\Product\Ticket::judgeNewChangeRule($ticketData['type'])) {
            if (isset($ticketData['refund_num']) && $ticketData['refund_num'] == 0) {
                return self::_return(203, '可退数量比例不允许为0');
            }
            if (isset($ticketData['supplement_num']) && $ticketData['supplement_num'] == 0) {
                return self::_return(203, '可补数量比例不允许为0');
            }
        }


        return self::_return(0, $ticketData);
    }

    public function handleAutoParam($ticketData, $priceData)
    {
        // var_dump($ticketData['status'], $ticketData['auto_grounding_status'], $ticketData['auto_undercarriage_status']);
        // var_dump('*******');
        $timeNow = strtotime(date("Y-m-d H:i:s"), time());
        //记录下原始的rule_status
        $oldRuleStatus = $ticketData['rule_status'];
        //自动上架方式票状态为自动上下架的时候
        if (isset($ticketData['auto_grounding_status']) && $ticketData['status'] == 4) {
            $ticketData['auto_grounding_date'] = date('Y-m-d H:i:s', strtotime($ticketData['auto_grounding_date']));
            if ($ticketData['auto_grounding_status'] == 1) {
                //预定时间自动上架
                $startDate = '';
                foreach ($priceData as $dateValue) {
                    if (empty($startDate)) {
                        $startDate = $dateValue['sdate'];
                        continue;
                    }
                    if (strtotime($dateValue['sdate']) < strtotime($startDate)) {
                        $startDate = $dateValue['sdate'];
                    }
                }
                $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d',
                        strtotime($startDate)) . ' 00:00:00';
            }
            if ($ticketData['auto_grounding_status'] == 2) {
                //发布后自动上架
                $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . date('Y-m-d') . ' 00:00:00';
            }
            if ($ticketData['auto_grounding_status'] == 3) {
                //指定时间上架
                $ticketData['preinstall_listing_date_rule'] = $ticketData['auto_grounding_status'] . ',' . $this->timeChange(date('Y-m-d H:i:s',
                        strtotime($ticketData['auto_grounding_date'])));
            }
        } else {
            $ticketData['auto_grounding_date'] = date('Y-m-d') . ' 00:00:00';
        }

        //自动下架方式票状态为自动上下架的时候
        if (isset($ticketData['auto_undercarriage_status']) && $ticketData['status'] == 4) {
            $ticketData['auto_undercarriage_date'] = date('Y-m-d H:i:s',
                strtotime($ticketData['auto_undercarriage_date']));
            if ($ticketData['auto_undercarriage_status'] == 1) {
                //预定时间到期自动下架
                $endDate = '';
                foreach ($priceData as $dateValue) {
                    if (empty($startDate)) {
                        $endDate = $dateValue['edate'];
                        continue;
                    }
                    if (strtotime($dateValue['edate']) > strtotime($endDate)) {
                        $endDate = $dateValue['edate'];
                    }
                }
                $ticketData['preinstall_delisting_date_rule'] = $ticketData['auto_undercarriage_status'] . ',' . date('Y-m-d',
                        strtotime($endDate)) . ' 23:59:59';
            }

            if ($ticketData['auto_undercarriage_status'] == 2) {
                $ticketData['preinstall_delisting_date_rule'] = $ticketData['auto_undercarriage_status'] . ',' . $this->timeChange(date('Y-m-d H:i:s',
                        strtotime($ticketData['auto_undercarriage_date'])));
            }
            unset($ticketData['auto_undercarriage_status'], $ticketData['auto_undercarriage_date']);
        } else {
            $ticketData['auto_undercarriage_date'] = date('Y-m-d') . ' 23:59:59';
        }

        //状态转换兼容java接口
        if ($ticketData['rule_status'] == 1) {
            $ticketData['ticket_shelf_rule'] = 0;
            $ticketData['status']            = 1;
        }

        if ($ticketData['rule_status'] == 2) {
            $ticketData['ticket_shelf_rule'] = 1;
            $ticketData['status']            = 2;
        }

        //自动上下架特殊处理
        if ($ticketData['rule_status'] == 4) {
            $ticketData['ticket_shelf_rule'] = 2;
            $ticketData['status']            = 2;
            //立即上架的几种情况
            // 1、设置预定时间开始自动上架的时候 预定日期开始时间小于等于当前时间
            // 2、设置立即上架的时候
            // 3、设置指定日期上架的时候 指定的日期小于等于当前时间
            if ($ticketData['auto_grounding_status'] == 1) {
                //预定时间自动上架
                if (strtotime($startDate) <= $timeNow) {
                    $ticketData['status'] = 1;
                }
            }
            if ($ticketData['auto_grounding_status'] == 2) {
                //发布后立即上架
                $ticketData['status'] = 1;
            }
            if ($ticketData['auto_grounding_status'] == 3) {
                //指定时间上架
                if (strtotime($ticketData['auto_grounding_date']) <= $timeNow) {
                    $ticketData['status'] = 1;
                }
            }

        }
        //自动起售停售
        if (isset($ticketData['auto_sale_rule'])) {
            $ticketData['auto_sale_rule'] = !empty($ticketData['auto_sale_rule']['start']) && !empty($ticketData['auto_sale_rule']['end']) ? json_encode($ticketData['auto_sale_rule']) : '';
        }
        if ($ticketData['rule_status'] == 5) {
            $ticketData['rule_status']       = 1;
            $ticketData['ticket_shelf_rule'] = 0;
            $ticketData['status']            = 1;
        }

        if ($oldRuleStatus == 5 && (!empty($ticketData['start_sale_datetime']) || !empty($ticketData['stop_sale_datetime']))) {
            $ticketData['rule_status']       = 5;
            $ticketData['ticket_shelf_rule'] = 3;
            $ticketData['status']            = 1;
        }

        // var_dump($ticketData['status'], $ticketData['ticket_shelf_rule'], $ticketData['preinstall_delisting_date_rule'], $ticketData['preinstall_listing_date_rule']);exit;
        return $ticketData;
    }

    /**
     * 特殊门票属性保存前处理
     * Create by zhangyangzhen
     * Date: 2018/8/3
     * Time: 14:50
     *
     * @param $submitData
     *
     * @return array
     */
    public function beforeHandleSpecial($submitData)
    {
        $ticketId    = $submitData['tid'];
        $specialType = $submitData['specialType'];

        if (empty($ticketId) || $ticketId < 0) {
            return ['code' => 203, 'msg' => '未检测到门票'];
        }

        if (empty($specialType) || !in_array($specialType, [1, 2, 3, 4])) {
            return ['code' => 203, 'msg' => '特殊票种类型有误'];
        }

        if (!is_array($submitData['price'])) {
            return ['code' => 203, 'msg' => '门票价格有误'];
        }

        if (empty(trim($submitData['name']))) {
            return ['code' => 203, 'msg' => '请输入门票名称'];
        }

        if (empty($submitData['ticketType'])) {
            return ['code' => 203, 'msg' => '请选择门票类型'];
        }

        //年卡价格应该大于0
        if ($submitData['ptype'] == 'I') {
            foreach ($submitData['price'] as $key => $val) {
                if ($val[0] <= 0) {
                    return ['code' => 203, 'msg' => '年卡的供货价格必须大于0'];
                }
            }
        }

        $specialData = [
            'special_config_type' => $specialType,
            'special_config_name' => $this->specialType[$specialType],
        ];

        switch ($specialType) {
            case 1:
                if ($submitData['minAge'] > $submitData['maxAge']) {
                    return ['code' => 203, 'msg' => '年龄区间有误', 'data' => []];
                }

                if (empty($submitData['maxAge'])) {
                    return ['code' => 203, 'msg' => '请输入最大年龄'];
                }

                $specialData['special_config_value'] = [
                    'minAge' => $submitData['minAge'] ?: 0,
                    'maxAge' => $submitData['maxAge'],
                ];
                break;
            case 3:
            case 4:
                $areaArr = $submitData['area'];

                if (empty($areaArr) || !is_array($areaArr)) {
                    return ['code' => 203, 'msg' => '请选择对应的区域', 'data' => []];
                }

                $specialData['special_config_value'] = [
                    'area' => $areaArr,
                ];
                break;
            case 2:
                break;
            default:
                break;
        }

        $specialData['special_config_value']['ticketType'] = $submitData['ticketType'];

        return ['code' => 200, 'msg' => 'success', 'data' => $specialData];
    }

    /**
     * 创建特殊产品后的处理
     * Create by zhangyangzhen
     * Date: 2018/8/21
     * Time: 10:58
     *
     * @param $data
     *
     * @return array
     */
    public function afterHandleSpecial($data, $type)
    {
        $packBiz = new \Business\Product\PackTicket();

        $oldTicketId = array_column($data, 'oldTicketId');

        //套票产品
        if ($type == 'F') {
            $packTicket = $packBiz->getTickets($oldTicketId);

            foreach ($data as $key => $item) {
                $data[$item['oldTicketId']] = $item;
                unset($data[$key]);
            }

            if (!empty($packTicket)) {
                foreach ($packTicket as $key => $val) {
                    unset($packTicket[$key]['id']);
                    $packTicket[$key]['parent_tid'] = $data[$val['parent_tid']]['ticketId'];
                }

                $res = $packBiz->savePackageTickets($packTicket);
                if (!$res || empty($res)) {
                    return ['code' => 201, 'msg' => '保存失败'];
                }
            }
        } else if ($type == 'I') {
            //年卡套餐特权处理，先用 oldTicketId 取出相关配置，在保存到新的票ID中
            foreach ($data as $key => $val) {
                $crdModel = $this->_getAnnualInstance($val['oldTicketId']);
                $crdConf  = $crdModel->getCrdConf($val['oldTicketId']);

                $saveData = [
                    'crdConf' => [
                        'auto_act_day' => $crdConf['auto_active_days'],
                        'srch_limit'   => $crdConf['search_limit'],
                        'cert_limit'   => $crdConf['cert_limit'],
                        'act_notice'   => $crdConf['act_notice'],
                    ],
                ];

                foreach ($crdConf['priv'] as $v) {
                    $saveData['crdPriv'][$v['tid']] = [
                        'aid'       => $v['aid'],
                        'use_limit' => $v['use_limit'],
                    ];
                }

                $res = $this->saveAnnualConfig($val['ticketId'], true, $saveData);
                if ($res[0] != 200) {
                    return [$res[0], $res[1]];
                }
                //if (!$res) {
                //    return ['code' => 202, 'msg' => '保存年卡特权信息失败'];
                //}
            }
        }

        return ['code' => 200, 'msg' => '保存成功'];
    }

    /**
     * 门票信息的校验
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @param  int  $memberId  会员ID
     * @param  array  $ticketData  单个门票信息数组
     * @param  array  $priceData  单个价格信息数组
     *
     * @return array
     */
    private static function _checkTicket($memberId, $ticketData, $priceData)
    {
        $isSectionTicket = false; // 是否是期票

        if ($ticketData['order_start'] && $ticketData['order_end']) {
            $isSectionTicket = true;
        }

        if (isset($ticketData['ticket_changing_range']) && !in_array($ticketData['type'], ['A', 'B', 'H'])) {
            return self::_return(202, '改签只支持景区、路线和演出');
        }

        if (!empty($priceData)) {
            foreach ($priceData as $row) {
                // 期票模式（有效期是时间段）只能全部有价格
                if ($isSectionTicket && ($row['weekdays'] != '0,1,2,3,4,5,6')) {
                    return self::_return(202, '期票模式必须每天都有价格');
                }
            }
        }

        //套票产品的提前预定时间必须大于等于子票
        $packBiz = new \Business\Product\PackTicket();
        if ($ticketData['type'] == 'F') {
            $child_ticket = $packBiz->getChildTickets($ticketData['tid'] + 0);
            foreach ($child_ticket as $item) {
                if ($item['dhour'] < $ticketData['preorder_expire_time']) {
                    return self::_return(203, '套票的提前时间不能小于' . $item['dhour']);
                }
                if ($ticketData['ddays'] < $item['preorder_early_days']) {
                    return self::_return(204, '套票的提前预定天数不能小于' . $item['ddays']);
                }

                if ($item['refund_rule'] > $ticketData['refund_rule']) {
                    if ($item['refund_rule'] == 1) {
                        $warning = '您只能选择[游玩日期前可退]或者[不可退]';
                    } else {
                        $warning = '您只能选择[不可退]';
                    }

                    return self::_return(205, '由于子票的限制，' . $warning);
                }
            }
            $ticketModel = new TicketModels();
            if ($ticketData['pay_way'] == 0 && !$ticketModel->allowOfflinePackage($memberId)) {
                return self::_return(206, '套票产品只允许在线支付');
            }
        }

        return self::_return(0, 'success');
    }

    /**
     * 上下架门票
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     * @modify dwer.cn 2019-10-23
     *
     * @param  int  $ticketId  门票ID
     * @param  int  $operatorId  会员ID
     * @param  string  $action  操作动作
     *
     * @return array|bool
     */
    public function setTicketStatus($ticketId, $operatorId, $action, $subSid = 0)
    {
        // 操作版本ID
        $versionUuid = uniqid();
        //如果是删除或是下架，获取包含改子票的主票，同时需要将主票下架
        if (in_array($action, ['deleteTicket', 'deListTicket'])) {
            $packageTicketApi = new PackageTicketApi();
            $packBiz          = new \Business\Product\PackTicket();

            $parentTicketIds = $packBiz->getParentsByTid($ticketId);
            if ($parentTicketIds) {
                $ticketIds = implode(',', $parentTicketIds);
                $ticketIds .= ',' . $ticketId;
                $isPackageTicket = 1;
            } else {
                $ticketIds = $ticketId;
                $isPackageTicket = 0;
            }

            if ($action == 'deleteTicket') {
                //删除
                $res = $packageTicketApi->deletePackTicket($ticketIds, $operatorId, $versionUuid, $isPackageTicket, $subSid);
            } else {
                //下架
                $res = $packageTicketApi->deListPackTicket($ticketIds, $operatorId, $versionUuid, $isPackageTicket, $subSid);
            }
        } else {
            $newTicketApi = new NewTicketApi();
            if ($action == 'listTicket') {
                //上架
                $res = $newTicketApi->listTicket($ticketId, $this->_memberId, $operatorId, $versionUuid, $subSid);
            } else {
                //恢复
                $res = $newTicketApi->recoveryTicket($ticketId, $this->_memberId, $operatorId, $versionUuid, $subSid);
            }
        }

        if ($res) {
            $extData = [];
            //获取下票类基础信息
            $ticketBiz  = new \Business\CommodityCenter\Ticket();
            $ticketInfo = $ticketBiz->queryTicketById($ticketId, 'pre_sale');
            if ($ticketInfo['pre_sale'] == 1) {
                $ticketExtInfoArr = [];
                $ticketBiz        = new \Business\JavaApi\CommodityCenter\Ticket();
                $getTicketInfo    = $ticketBiz->queryTicketAttrsById($ticketId);
                if ($getTicketInfo['code'] == 200) {
                    $ticketExtInfoArr = $getTicketInfo['data'];
                }

                //$ticketModel      = new \Model\Product\Ticket();
                //$ticketExtInfoArr = $ticketModel->getTicketExtAttr([$ticketId]);

                if (!empty($ticketExtInfoArr)) {
                    foreach ($ticketExtInfoArr as $item) {
                        if ($item['key'] == 'effective_limit') {
                            $extData[] = [
                                'ticketId' => $ticketId,
                                'key'      => 'effective_limit',
                                'val'      => $item['val'],
                            ];
                        }
                        if ($item['key'] == 'valid_rule_self_ex') {
                            $extData[] = [
                                'ticketId' => $ticketId,
                                'key'      => 'valid_rule_self_ex',
                                'val'      => $item['val'],
                            ];
                        }
                        if ($item['key'] == 'valid_rule_self_type') {
                            $extData[] = [
                                'ticketId' => $ticketId,
                                'key'      => 'valid_rule_self_type',
                                'val'      => $item['val'],
                            ];
                        }
                    }
                }
            }

            //上架下架时候同步更新上下架规则参数
            if ($action == 'listTicket') {
                $extData[] = ['ticketId' => $ticketId, 'key' => 'ticket_shelf_rule', 'val' => 0];
            }
            if ($action == 'deListTicket') {
                $extData[] = ['ticketId' => $ticketId, 'key' => 'ticket_shelf_rule', 'val' => 1];
            }
            if (!empty($extData)) {
                //获取票类扩展属性字典
                $ticketExtConf = new TicketExtendAttr();
                $ticketExtConf->save($this->_memberId, $operatorId, $extData, $versionUuid, $subSid);
            }

            $tempArr = ['deleteTicket' => '删除', 'recoveryTicket' => '恢复', 'listTicket' => '上架', 'deListTicket' => '下架'];
            //通知OTA
            OtaProductNotify::notify($ticketId, $tempArr[$action]);

            //分销专员自供应产品更新通知
            $notifyTids = $ticketId;
            if (!empty($ticketIds)) {
                $notifyTids = $ticketIds;
            }
            \Business\MultiDist\Product::pushUpdataSelfTicket($action, $notifyTids, $this->_memberId);
        }

        return $res;
    }

    /**
     * 批量删除门票
     * <AUTHOR>
     * @date   2017-12-18
     *
     * @param  string  $tids  门票id,1,2,3
     * @param  int  $sid  供应商id
     * @param  int  $oper  操作人id
     *
     * @return array
     */
    public function batchDelete($tids, $sid, $oper)
    {
        if (!$tids || !$sid || !$oper) {
            return $this->returnData(204, '参数错误');
        }

        $tidArr = explode(',', $tids);

        //获取门票信息
        $ticModel = new TicketModels('slave');
        $tickets  = $ticModel->getTicketInfoMulti($tidArr, 'id,apply_did');

        if (array_diff($tickets, [$sid])) {
            return $this->returnData(204, '无权修改');
        }

        $newTicketBiz = new NewTicketApi();
        $result       = $newTicketBiz->batchDelete($tids, $sid, $oper);

        if (isset($result['code'])) {
            return $this->returnData($result['code'], $result['msg'], $result['data']);
        } else {
            return $this->returnData(500, '接口出错', []);
        }
    }

    /**
     * 获取门票信息
     *
     * @date   2017-07-21
     * <AUTHOR> Lan
     *
     * @param  int  $ticketId  门票ID
     * @param  int  $delFlag  是否显示删除的门票
     *
     * @return array|bool
     */
    public function getTickets($ticketId, $delFlag)
    {
        $ticketBiz  = new \Business\CommodityCenter\Ticket();
        $ticketData = $ticketBiz->fieldConversion($ticketBiz->queryTicketInfoById($ticketId));

        $business = new TicketApi();
        //$ticketData = $business->getTickets($ticketId);

        if ($ticketData == false) {
            return false;
        }

        $otherData = $this->_getDataByType($ticketId, $ticketData);

        if ($otherData[0] == 0) {
            if ($otherData[1]) {
                // * <AUTHOR>
                // * @date   2018/07/06
                // * _getDataByType 判断（refund_rule）子票 > 票 为了兼容 -1不可退
                if ($ticketData["refund_rule"] == -1) {
                    $otherData[1]["refund_rule"] = $ticketData["refund_rule"];
                }
                $ticketData = array_merge($ticketData, $otherData[1]);
                unset($ticketData['touring_party_no']);
            }
        } else {
            return $otherData;
        }

        $ticketData     = $this->_handelTicketData($ticketData);
        $printMode      = isset($ticketData['ext']['print_mode']) ? $ticketData['ext']['print_mode'] : 3;
        $checkMode      = isset($ticketData['ext']['check_mode']) ? $ticketData['ext']['check_mode'] : 1;
        $orderMode      = isset($ticketData['ext']['order_mode']) ? $ticketData['ext']['order_mode'] : 0;
        $data['ticket'] = $ticketData;

        // 如果用户选择了 一人一票的打印规则 和 分批入园， 则验证方式改为 分批入园，一人一票，输出数据时反过来
        if ($printMode == 4 && $checkMode == 3) {
            $checkMode = 2;
        }
        $ticketPrintCheckRule                      = [
            'print_mode' => $printMode,
            'check_mode' => $checkMode,
            'order_mode' => $orderMode,
        ];
        $data['ticket']['ticket_print_check_rule'] = $ticketPrintCheckRule;

        $landId = $ticketData['item_id'];

        $data['ticket_title'] = false;

//        if ($ticketData['type'] != 'F') {
        if ($ticketData['status'] == 1) {
            $data['ticket_title'] = $business->getOtherTickets($landId);
        } else {
            $data['ticket_title'] = $business->getOutTickets($landId, $delFlag);
        }
//        }

//        if ($data['ticket_title'] == false) {
        //            $data['ticket_title'] = [['id' => $ticketData['id'], 'name' => $ticketData['name']]];
        //        }
        $timeShareOrdeActionService = new TimeShareOrdeAction();
        $result                     = $timeShareOrdeActionService->queryStorageSection($landId);
        if ($result['code'] == 200) {
            $data['ticket']['is_section_storage'] = $result['data'];
        }
        //获取区间段价格和库存
        $rangeRes = $this->listValidRangePrice($ticketId);

        if ($rangeRes['code'] == 200) {
            $data['price_section'] = $rangeRes['data'];
        } else {
            $data['price_section'] = [];
        }

        //景区获取总库存信息 ---- 固定有效期相关暂时注释
        //        if ($ticketData['type'] == 'A') {
        //            $tStorage = $this->_getTotalStorageInfo($ticketId);
        //            foreach ($tStorage as $stkey => $stv) {
        //                $ticketData[$stkey] = $stv;
        //            }
        //            $data['ticket'] = $ticketData;
        //        }

        //返回门票渠道
        $channelMap = \load_config('sale_channel');
        foreach ($channelMap as $idx => $name) {
            $data['channel_map'][$idx] = [
                'id'   => $idx,
                'name' => $name,
            ];
        }
        //除景区外，屏蔽积分商城渠道
        if ($ticketData['type'] != 'A') {
            unset($data['channel_map'][14]);
        }
        if (!in_array($ticketData['type'], ['A', 'F', 'I'])) {
            unset($data['channel_map'][18]);
        }

        //年卡可售渠道只保留 分销后台、计调下单、散客窗口、团队窗口、微商城、小程序、桌面云票务
        if ($ticketData['type'] == 'I') {
            unset($data['channel_map'][10], $data['channel_map'][13], $data['channel_map'][6], $data['channel_map'][8],
                $data['channel_map'][3], $data['channel_map'][4], $data['channel_map'][11], $data['channel_map'][2],
                $data['channel_map'][15], $data['channel_map'][17], $data['channel_map'][18], $data['channel_map'][19]);
        }

        $data['channel_map']            = array_values($data['channel_map']);
        $data['ticket']['auth_invoice'] = 0;

        //判断供应商是否有开票的权限
        $invoiceApi = new InvoiceApi();
        $result     = $invoiceApi->getEnterpriseOpenRecordsByMixed($data['ticket']['account_id']);
        if ($result['code'] == 200) {
            $data['ticket']['auth_invoice'] = 1;
        }

        $buyLimitRes = (new \Business\BuyLimit\Index())->queryLandLimitConfigByTicketIds([$ticketId]);
        if ($buyLimitRes['code'] == 200 && $buyLimitRes['data']) {
            $buyLimitInfo = $buyLimitRes['data'][0];
            $data['ticket']['land_buy_limit_rule_config'] = [
                //购票限制相关
                "buyer_limit_type"     => $buyLimitInfo['buyerLimitType'], // 取票人限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                "buyer_order_days"     => $buyLimitInfo['buyerOrderDays'], // 取票人限购订单笔数间隔天数
                "buyer_order_total"    => $buyLimitInfo['buyerOrderTotal'], // 取票人限购订单笔数
                "buyer_ticket_days"    => $buyLimitInfo['buyerTicketDays'], // 取票人限购票笔数间隔天数
                "buyer_ticket_total"   => $buyLimitInfo['buyerTicketTotal'], // 取票人限购票类总数
                "tourist_limit_type"   => $buyLimitInfo['touristLimitType'], // 游客限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
                "tourist_order_days"   => $buyLimitInfo['touristOrderDays'], // 游客限购订单笔数
                "tourist_order_total"  => $buyLimitInfo['touristOrderTotal'], // 游客限购订单笔数间隔天数
                "tourist_ticket_days"  => $buyLimitInfo['touristTicketDays'], // 游客限购票笔数间隔天数
                "tourist_ticket_total" => $buyLimitInfo['touristTicketTotal'], // 游客限购票类总数
                "id"                   => $buyLimitInfo['id'],
                "ruleType"             => $buyLimitInfo['ruleType']
            ];
        }

        return [0, $data];
    }

    /**
     * mini云小程序获取票类详情, 通过景区取获取当前景区下所有票类信息
     * <AUTHOR>
     * @date 2018-06-19
     */
    public function getMiniTickets($lid)
    {

        if (empty($lid)) {
            $this->returnData('204', '景区id不能为空');
        }

        $ticketApi = new \Business\JavaApi\Product\Ticket();
        $result    = $ticketApi->queryMiniCloudSmallProgramTicketList($lid);
        //$ticketApi = new TicketApi();
        //$result    = $ticketApi->getMiniTicketList($lid);

        // 返回错误码 和 java 的错误提示
        return $result;
    }

    /**
     * 获取门票信息列表， 如果存在门票id，则只根据门票id获取一张，否则获取景区下所有门票
     *
     * @date    2017-10-11
     * <AUTHOR> Lin
     *
     * @param  int  $landId  景点id   默认为0
     * @param  int  $ticketId  门票Id  默认为0
     *
     * @return array
     */
    public function getTicketList($ticketId, $landId = 0)
    {

        $business = new TicketApi();
        $result   = $business->getTicketList($ticketId, $landId);
        if ($result == false) {
            return $this->returnData(204, '获取数据失败');
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 保存价格
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @param  int  $productId  产品ID
     * @param  array  $priceArr  价格数组信息
     *
     * @return array
     */
    public function savePrice($productId, $priceArr, $tid, $isPartner = 0)
    {
        $productBusiness = new ProductBusiness();
        $ticketModel     = new TicketModels();
        $originalPrice   = $ticketModel->getPriceSection($productId);

        return $productBusiness->SavePrice($productId, $priceArr, $this->_memberId, $originalPrice, $tid, $isPartner,
            $this->_operateId);
    }

    /**
     * 保存特殊票种价格
     * Create by zhangyangzhen
     * Date: 2018/8/21
     * Time: 20:21
     *
     * @param $data
     *
     * @return \Business\JavaApi\mix
     */
    public function saveSpecialPrice($data)
    {
        $business = new TicketApi();
        $res      = $business->saveSpecialPrice($data);

        return $res;
    }

    /**
     * 获取有效区间段价格和库存
     * <AUTHOR>
     * @date   2017-10-17
     *
     * @param  int  $tid  门票id
     *
     * @return array
     */
    public function listValidRangePrice($tid)
    {

        if (!$tid) {
            $this->returnData(204, '参数错误');
        }

        //请求java服务
        $ticketBiz = new TicketApi();

        $result = $ticketBiz->listRangePrice($tid);

        $return = $expired = [];
        if ($result['code'] == 200 && $result['data']) {

            $list = $result['data'];
            //当前日期
            $today = date('Y-m-d');
            foreach ($list as $item) {
                if ($item['endDate'] >= $today) {
                    $return[$item['id']] = array(
                        'js'       => $item['nPrice'],
                        'ls'       => $item['lPrice'],
                        'm_price'  => $item['mPrice'],
                        'w_price'  => $item['windowPrice'],
                        'id'       => $item['id'],
                        'ptype'    => 0,
                        's_price'  => 0,
                        'sdate'    => $item['startDate'],
                        'edate'    => $item['endDate'],
                        'storage'  => $item['storage'],
                        'weekdays' => $item['weekdays'],
                    );
                } else {
                    $expired[$item['id']] = array(
                        'js'       => $item['nPrice'],
                        'ls'       => $item['lPrice'],
                        'm_price'  => $item['mPrice'],
                        'w_price'  => $item['windowPrice'],
                        'id'       => $item['id'],
                        'ptype'    => 0,
                        's_price'  => 0,
                        'sdate'    => $item['startDate'],
                        'edate'    => $item['endDate'],
                        'storage'  => $item['storage'],
                        'weekdays' => $item['weekdays'],
                    );
                }
            }
            //门票过期返回最后一次的价格
            if (empty($return) && !empty($expired)) {
                krsort($expired);
                if (count($expired) > 1) {
                    $d                = array_shift($expired);
                    $return[$d['id']] = $d;
                } else {
                    $return = $expired; // current($expired);
                }
            }
        }

        return $this->returnData(200, '', $return);
    }

    /***
     * 获取票类总库存信息
     * <AUTHOR> Yiqiang
     * @date   2018-10-11
     * 固定有效期相关 暂时弃用
     *
     * @param $ticketId
     *
     * @return array
     */
    private function _getTotalStorageInfo($ticketId)
    {
        if (!$ticketId) {
            $this->returnData(204, '参数错误');
        }

        //请求java服务
        $data = StorageApi::getTotalStorage($ticketId);

        if (!empty($data) && $data['storage'] != -1) {
            $return = [
                'ticket_total_storage' => $data['storage'],
                'ticket_sold_count'    => $data['soldCount'] - $data['returnCount'],
            ];

        } else {
            $return = [
                'ticket_total_storage' => 0,
                'ticket_sold_count'    => 0,
            ];
        }

        return $return;
    }

    /**
     * 根据产品类型获取产品的额外信息
     *
     * @date   2017-07-21
     * <AUTHOR> Lan
     *
     * @param  int  $ticketId  景区ID
     * @param  array  $ticketData  门票数据
     *
     * @return array
     */
    public function _getDataByType($ticketId, $ticketData)
    {
        $tempArr = [];
        switch ($ticketData['type']) {
            case 'H':
                $venusArr = $this->getVenue($ticketData['venus_id']);
                $tempArr  = array_merge($tempArr, $venusArr);
                break;

            case 'I':
                //$annualModel = new AnnualCard();
                //$tempArr     = $annualModel->getCrdConf($ticketId);

                $annualPrivBiz = new \Business\AnnualCard\Privilege();
                $privRes = $annualPrivBiz->getPrivilegeInfoByParentTid([$ticketId]);
                $tempArr = $annualPrivBiz->formatTicketPrivilegeData($privRes);

                break;

            case 'F':
                $packBiz         = new \Business\Product\PackTicket();
                $childTicketData = $packBiz->getChildTickets($ticketId);

                //酒店子票添加房型字段
                $hotelLib = new Hotel();
                $roomType = $roomTids = [];
                if (!empty($childTicketData)) {
                    foreach ($childTicketData as $chidValue) {
                        if ($chidValue['p_type'] == 'C') {
                            $roomTids[] = intval($chidValue['tid']);
                        }
                    }

                    if (!empty($roomTids)) {
                        $roomTids = array_values(array_unique($roomTids));
                        //批量获取房型数据
                        $resultRoom = $hotelLib->queryRoomTypeByTids($roomTids);
                        if ($resultRoom['code'] == self::CODE_SUCCESS && !empty($resultRoom['data'])) {
                            $roomInfo = $resultRoom['data'];
                            $roomType = array_key($roomInfo, 'tid');
                        }
                    }
                }

                //子票不是自供应 1都是自供应的 2包含转分销的
                $tempArr['is_self_supply'] = 1;
                $isChange                  = false;
                foreach ($childTicketData as $child) {
                    $isMy = 2; //是否自供应
                    if ($this->_memberId == $child['apply_did']) {
                        $isMy = 1;
                    }

                    //多景点配置参数转换
                    $checkScenicInfo = $this->handleCheckScenicInfo($child['check_terminal_info']);

                    //多终端新版参数转换
                    $checkTerminalInfoV2 = !empty($child['check_terminal_info']) ? json_decode($child['check_terminal_info'],
                        JSON_UNESCAPED_UNICODE) : 0;

                    $checkTerminalInfoRes  = self::handleTerminalInfoBySelect($child['check_terminal_info']);
                    $checkTerminalInfo     = $checkTerminalInfoRes['info'];
                    $checkTerminalTimeInfo = $checkTerminalInfoRes['checkTimeInfo'];

                    $tempArr['childTicket'][] = array(
                        'ltitle'                    => $child['ltitle'],
                        'ttitle'                    => $child['ttitle'],
                        'pid'                       => $child['pid'],
                        'lid'                       => $child['lid'],
                        'tid'                       => $child['tid'],
                        'num'                       => $child['num'],
                        'valid_period_type'         => $child['delaytype'],
                        'valid_period_days'         => $child['delaydays'],
                        'valid_period_start'        => $child['order_start'],
                        'valid_period_end'          => $child['order_end'],
                        'use_early_days'            => $child['use_early_days'],
                        'valid_period_timecancheck' => $child['if_verify'],
                        'p_type'                    => $child['p_type'],
                        'is_my'                     => $isMy,
                        'room_name'                 => $roomType[$child['tid']]['typeName'] ?? '',
                        'preorder_early_days'       => $child['ddays'],
                        'preorder_expire_time'      => $child['dhour'],
                        'buy_min_amount'            => $child['buy_limit_low'],
                        'buy_max_amount'            => $child['buy_limit_up'],
                        'buy_limit_type'            => $child['buy_limit'],
                        'buy_limit_num'             => $child['buy_limit_num'],
                        'buy_limit_period'          => $child['buy_limit_date'],
                        'identity_info'             => $child['tourist_info'],
                        'check_scenic_info'         => $checkScenicInfo,
                        'check_terminal_info_v2'    => $checkTerminalInfoV2,
                        'check_terminal_info'       => $checkTerminalInfo,
                        'check_terminal_time_info'  => $checkTerminalTimeInfo,
                        'land_buy_limit_rule_config'  => $child['land_buy_limit_rule_config'],
                        'refund_rule'               => $child['refund_rule'],
                        'refund_before_early_time'  => $child['refund_before_early_time'],
                        'refund_after_early_time'   => $child['refund_after_early_time'],
                        'refund_audit'              => $child['refund_audit'],
                        'refund_num'                => $child['refund_num'],
                        'ifprint_refund_rule'                => $child['ifprint_refund_rule'],
                    );

                    // if ($child['refund_rule'] > $ticketData['refund_rule']) {
                    //     $tempArr['refund_rule'] = $child['refund_rule'];
                    // }
                    //子票不是自供应
                    if ($isMy == 2 && !$isChange) {
                        $tempArr['is_self_supply'] = 2;
                        $isChange                  = true;
                    }
                }

                if (!$packBiz->checkEffectivePack($ticketId)) {
                    return self::_return(202, implode(',', $packBiz->message));
                }

                $tempArr['preorder_early_days'] = $packBiz->advance > $ticketData['preorder_early_days'] ? $packBiz->advance : $ticketData['preorder_early_days']; // 提前天数
                $payMode                        = $packBiz->paymode; // 支付方式
                $useDate                        = $packBiz->usedate; // 套票使用时间
                if ($useDate['section'] == 0) {
                    $tempArr['minActive'] = floor((strtotime($useDate['eDate']) - strtotime($useDate['sDate'])) / 86400);
                }
                $memberObj = new Member();

                //$groupId = $memberObj->getMemberCacheById($ticketData['account_id'], 'group_id');

                $queryParams = [[$ticketData['account_id']]];
                $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                    $queryParams);

                if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                    return self::_return(202, '用户信息获取失败');
                }
                $groupId = array_column($queryRes['data'], null, 'id')[$ticketData['account_id']]['group_id'];
                // 只有云顶允许打包现场支付
                if ($payMode == 0 && ($groupId != 4)) {
                    return self::_return(202, '现场支付不支持打包');
                }
                break;

            case 'B':
                if ($ticketData['assembling_place']) {
                    if ($ticketData['assembling_place']) {
                        $tmpPlace = @json_decode($ticketData['assembling_place'], true);
                        $tmpPlace = is_array($tmpPlace) ? $tmpPlace : @unserialize($ticketData['assembling_place']);
                        $tmpPlace = $tmpPlace ?: [];
                    } else {
                        $tmpPlace = [];
                    }
                    $tempArr['assembling_place'] = implode(';', $tmpPlace);
                    $arrayTemp                   = explode('{fck_date}', $ticketData['touring_party_no']);
                    $tempArr['g_number']         = isset($arrayTemp[0]) ? $arrayTemp[0] : ''; // 团号
                    $tempArr['s_number']         = isset($arrayTemp[1]) ? $arrayTemp[1] : ''; // 编号

                    if ($tempArr['s_number']) {
                        $tempArr['s_number'] = substr($tempArr['s_number'], 1);
                    }
                }
                break;

            default:
                break;
        }

        return self::_return(0, $tempArr);
    }
    /**
     * 创建门票设置默认渠道
     *
     * @date   2017-07-25
     * <AUTHOR> Lan
     *
     * @param  int  $productId  产品ID
     *
     */
    public function setChannel($productId)
    {
        return true;
    }

    /**
     * 门票数据显示的加工处理
     *
     * @date   2017-07-24
     * <AUTHOR> Lan
     *
     * @param  array  $ticketData  门票数据
     *
     * @return array
     */
    private function _handelTicketData($ticketData)
    {
        $ticketData['buy_limit_unit'] = 0; //购票限制单位，0以票数限制，1以订单数限制
        $ticketData['buy_limit_type'] = intval($ticketData['buy_limit_type']);
        if ($ticketData['buy_limit_type'] == 6) {
            $ticketData['buy_limit_unit'] = 1;
        }

        if ($ticketData['buy_limit_type'] > 2 && $ticketData['buy_limit_type'] < 5) {
            $ticketData['buy_limit_type'] -= 2;
            $ticketData['buy_limit_unit'] = 1;
        }

        //多景点配置参数转换
        $ticketData['check_scenic_info'] = $this->handleCheckScenicInfo($ticketData['check_terminal_info']);

        //多终端新版参数转换
        $ticketData['check_terminal_info_v2'] = !empty($ticketData['check_terminal_info']) ? json_decode($ticketData['check_terminal_info'],
            JSON_UNESCAPED_UNICODE) : 0;

        $checkTerminalInfo                      = self::handleTerminalInfoBySelect($ticketData['check_terminal_info']);
        $ticketData['check_terminal_info']      = $checkTerminalInfo['info'];
        $ticketData['check_terminal_time_info'] = $checkTerminalInfo['checkTimeInfo'];

        $ticketData['modify_limit_time'] = intval($ticketData['modify_limit_time']);

        $limitDay    = intval($ticketData['modify_limit_time'] / 86400);
        $limitHour   = intval(($ticketData['modify_limit_time'] - $limitDay * 86400) / 3600);
        $limitMinute = intval(($ticketData['modify_limit_time'] - $limitDay * 86400 - $limitHour * 3600) / 60);

        $ticketData['modify_limit_time'] = $limitDay . ',' . $limitHour . ',' . $limitMinute;

        // * <AUTHOR>
        // * @date   2018/07/05
        // * -1不可退，而且是可以提现; refund_rule_bool 下单后即可提现按钮
        if ($ticketData["refund_rule"] == -1) {
            $ticketData["refund_rule"]      = 2; //前端转换为 不可退 2
            $ticketData["refund_rule_bool"] = 1;
        } else {
            $ticketData["refund_rule_bool"] = 0;
        }

        // 计时类产品的数据获取
        $tid                         = $ticketData['id'];
        $timingProductBusiness       = new TimingProduct();
        $timingTicket                = $timingProductBusiness->getTimingProduct($tid);
        $ticketData['timing_ticket'] = $timingTicket;

        // 初始化扩展信息默认显示座位号
        if (isset($ticketData['ext']['is_show_seat']) && empty($ticketData['ext']['is_show_seat'])) {
            $ticketData['ext']['is_show_seat'] = 1;
        }

        //票类到期前7天，短信通知供应商
        $ticketData['ext']['ticket_sms_expire'] = 0;
        if (isset($ticketData['ext']['expire_before_notice_to_supplier'])) {
            $ticketData['ext']['ticket_sms_expire'] = (int)$ticketData['ext']['expire_before_notice_to_supplier'];
        }

        //自动上下架参数处理
        if (isset($ticketData['ext']['ticket_shelf_rule'])) {
            if ($ticketData['ext']['ticket_shelf_rule'] == 0) {
                $ticketData['ext']['rule_status'] = 1;
            }
            if ($ticketData['ext']['ticket_shelf_rule'] == 1) {
                $ticketData['ext']['rule_status'] = 2;
            }
            if ($ticketData['ext']['ticket_shelf_rule'] == 2) {
                $ticketData['ext']['rule_status'] = 4;
            }
            if ($ticketData['ext']['ticket_shelf_rule'] == 3) {
                $ticketData['ext']['rule_status'] = 5;
            }
            if ($ticketData['ext']['ticket_shelf_rule'] == '') {
                $ticketData['ext']['rule_status'] = $ticketData['status'];
            }
        } else {
            $ticketData['ext']['ticket_shelf_rule'] = 0;
            $ticketData['ext']['rule_status']       = 1;
        }

        //自动上架参数处理
        if (isset($ticketData['ext']['preinstall_listing_date_rule'])) {
            $autoShelfPattern                           = explode(',',
                $ticketData['ext']['preinstall_listing_date_rule']);
            $ticketData['ext']['auto_grounding_status'] = $autoShelfPattern[0];
            $ticketData['ext']['auto_grounding_date']   = $autoShelfPattern[1];
        }
        //自动下架参数处理
        if (isset($ticketData['ext']['preinstall_delisting_date_rule'])) {
            $autoSoldOut                                    = explode(',',
                $ticketData['ext']['preinstall_delisting_date_rule']);
            $ticketData['ext']['auto_undercarriage_status'] = $autoSoldOut[0];
            $ticketData['ext']['auto_undercarriage_date']   = $autoSoldOut[1];
        }

        //年卡特权消费限制特殊处理
        if (!isset($ticketData['ext']['enable_privilege_limit'])) {
            $ticketData['ext']['enable_privilege_limit'] = -1;
        }

        //如果是演出的门票 需要返回是否被打包成套票子票属性
        if ($ticketData['pType'] == 'H') {
            $parentTids                 = (new \Business\Product\PackTicket())->getParentsByTid($tid);
            //验证是否是套票，而不是捆绑票 (捆绑票主票类型是H)
            $landModel   = new \Business\CommodityCenter\Land();
            $landInfoArr = $landModel->getLandInfoByArrTidToJava($parentTids);
            //验证是否有套票类型
            $checkShowType = array_column($landInfoArr, 'p_type');
            if (in_array('F', $checkShowType)) {
                $ticketData['is_show_pack'] = 1;
            } else {
                $ticketData['is_show_pack'] = 0;
            }

            $ticketData['pre_check_mode']              = $ticketData['pre_check_mode'] ?? 0;
            $ticketData['pre_check_time_rule']         = $ticketData['pre_check_time_rule'] ?? 1;
            $ticketData['pre_check_time_ahead_minute'] = $ticketData['pre_check_time_ahead_minute'] ?? 60;
            $ticketData['pre_check_limit_times']       = $ticketData['pre_check_limit_times'] ?? 0;
            $ticketData['pre_check_rule']              = $ticketData['pre_check_rule'] ?? 1;
            $ticketData['pre_check_refund_rule']       = $ticketData['pre_check_refund_rule'] ?? 1;
            $ticketData['show_order_limit_time']       = $ticketData['show_order_limit_time'] ?? [];
        }

        return $ticketData;
    }

    /**
     * 创建门票后其他的处理
     *
     * @date   2017-07-26
     * <AUTHOR> Lan
     *
     * @param  int  $productId  产品Id
     * @param  int  $ticketId  门票Id
     * @param  array  $ticketData  门票数据
     * @param  int  $sid  商家id
     *
     * @return array
     */
    public function afterHandle($productId, $ticketId, $ticketData, $sid = 0, $packageCacheKey = '')
    {

        //附加票保存
        if ($ticketData['type'] == 'A') {
            $this->setTicketDeputy($ticketId, $ticketData);
        }

        $packBiz = new \Business\Product\PackTicket();

        //监听子票的提前预定时间的变化
        $packBiz->updateParentAdvanceAttr($ticketId, $ticketData['preorder_early_days'],
            $ticketData['preorder_expire_time']);

        //监听子票退票规则的变化
        $packBiz->updateParentRefundRuleAttr($ticketId, $ticketData['refund_rule'],
            $ticketData['refund_early_minu']);

        //mTid 子票信息有被编辑的标识
        if ($ticketData['type'] == 'F' && ($ticketData['id'] < 0 || !empty($ticketData['mTid']))) {
            if (!$packageCacheKey) {
                return self::_return(202, '保存套票信息失败');
            }
            $res = $this->savePackage($ticketId, $packageCacheKey);
            if (!$res) {
                return self::_return(202, '保存套票信息失败');
            }
        } else if ($ticketData['type'] == 'I') {
            $res = $this->saveAnnualConfig($ticketId);
            if ($res[0] != 200) {
                return [$res[0], $res[1]];
            }
            //if (!$res) {
            //    return ['code' => 202, 'msg' => '保存年卡特权信息失败'];
            //}

        } else if ($ticketData['type'] == 'K') {
            $timingProductBusiness = new TimingProduct();
            $res                   = $timingProductBusiness->handleTimingProduct($ticketId,
                $ticketData['timing_ticket'], $sid);
            if (!$res) {
                return self::_return(202, '保存计时产品属性失败');
            }
        }

        // * <AUTHOR>
        // * @date   2018/07/10
        // * 如果将门票的退票属性修改位"不可退，而且可提现"，需要将已经生成的订单设置为可提现
        if ($ticketData['id'] > 0 && $ticketData['refund_rule'] == -1 && $ticketData['refund_redis_check'] == 1) {
            $jobData = [
                'job_type' => 'withdraw_order',
                'job_data' => [
                    'tid' => $ticketData['id'],
                ],
            ];
            //插入队列
            $jobId = Queue::push('ticket_system', 'Ticket_Job', $jobData);
            //记录传递参数到日志中
            pft_log('ticket_withdraw/debug', json_encode(['Ticket_Job', $jobId, $jobData]));
        }

        if ($ticketData['id'] > 0) {
            //终端有缓存门票数据，门票更新，删除缓存
            TerminalCache::DeleteTicketCache($ticketData['id']);
        }

        return self::_return(0, '');
    }

    /**
     * 保存年卡特权信息
     *
     * @date   2017-07-26
     * <AUTHOR> Lan
     *
     * @param  int  $parentId  主卡ID
     *
     * @return bool
     */
    private function saveAnnualConfig($parentId, $isCache = false, $data = [])
    {
        $crdModel = $this->_getAnnualInstance($parentId);

        if ($isCache) {
            $cardData = $data;
        } else {
            $cardInfo = $crdModel->getCache();

            if (empty($cardInfo)) {
                return false;
            }

            $cardData = json_decode($cardInfo, true);
        }

        //$crdConf = $cardData['crdConf'];
        //$crdPriv = $cardData['crdPriv'];
        //
        //foreach ($crdPriv as $key => $item) {
        //    $packData[$key]['parent_tid'] = $parentId;
        //}

        //$crdConf['tid'] = $parentId;
        //$crdConf['aid'] = $this->_memberId;
        //$ret            = $crdModel->saveCardConfig($parentId, $crdConf, $crdPriv);
        //if ($ret !== false) {
        //    $crdModel->rmCache();
        //}

        $groupPriv = $cardData['groupPriv'];

        $code          = 200;
        $msg           = '特权保存成功';
        $annualPrivBiz = new \Business\AnnualCard\Privilege();
        //新增特权分组
        if (isset($groupPriv['add']) && is_array($groupPriv['add'])) {
            foreach ($groupPriv['add'] as $item) {
                $result = $annualPrivBiz->addCardPrivilege($this->_memberId, $parentId, $item['group_name'],
                    $item['use_limit'], $item['tid_arr'], $item['superior_id'], $item['sort'], $item['tid_aid_map'] ?? []);
                if ($result['code'] != 200) {
                    $code = 204;
                    $msg  .= "'{$item['group_name']}' 特权分组添加失败:{$result['msg']}；";
                }
            }
        }

        //修改特权分组
        if (isset($groupPriv['update']) && is_array($groupPriv['update'])) {
            foreach ($groupPriv['update'] as $item) {
                $result = $annualPrivBiz->updateCardPrivilege($this->_memberId, $parentId, $item['group_id'],
                    $item['group_name'], $item['use_limit'], $item['sort']);
                if ($result['code'] != 200) {
                    $code = 204;
                    $msg  .= "'{$item['group_name']}' 特权分组编辑失败:{$result['msg']}；";
                }
            }
        }
        //删除特权分组
        if (isset($groupPriv['delete']) && is_array($groupPriv['delete'])) {
            foreach ($groupPriv['delete'] as $groupId => $groupName) {
                $result = $annualPrivBiz->deleteCardPrivilege($this->_memberId, $parentId, (int)$groupId);
                if ($result['code'] != 200) {
                    $code = 204;
                    $msg  .= "'{$groupName}' 特权分组删除失败:{$result['msg']}；";
                }
            }
        }

        $crdModel->rmCache();

        return self::_return($code, $msg);
    }

    /**
     * 保存套票
     *
     * @date   2017-07-26
     * <AUTHOR> Lan
     *
     * @param  int  $parentId  主票ID
     * @param  int  $operatorId  操作员ID
     *
     * @return bool
     */
    private function savePackage($parentId, $packageCacheKey = '')
    {
        $packBiz = new \Business\Product\PackTicket();

        $childInfo = $packBiz->getCache($packageCacheKey);
        if (empty($childInfo)) {
            return false;
        }

        $packData     = json_decode($childInfo, true);
        $savePackData = [];
        foreach ($packData as $tmp) {
            foreach ($tmp as $key => $item) {
                $data               = $item;
                $data['parent_tid'] = $parentId;
                $savePackData[]     = $data;
            }
        }

        $ret = $packBiz->savePackageTickets($savePackData);
        if ($ret !== false) {
            $packBiz->rmCache($packageCacheKey);
        }

        return $ret;
    }

    /**
     * 获取年卡模型实例化
     *
     * @date   2017-07-26
     * <AUTHOR> Lan
     *
     * @param  int  $parentId  年卡产品ID
     *
     * @return object
     */
    protected function _getAnnualInstance($parentId)
    {
        if (!$this->_annualCard) {
            $this->_annualCard = new AnnualCard($parentId, $this->_memberId);
        }

        return $this->_annualCard;
    }

    /**
     * 获取场馆信息
     *
     * @date   2017-08-03
     * <AUTHOR> Lan
     *
     * @param  int  $venusId  场馆ID
     *
     * @return array
     */
    public function getVenue($venusId)
    {
        $tempArr = [];

        $roundObj               = new ShowBiz();
        $roundData              = $roundObj->getZones($venusId);
        $tempArr['venus_areas'] = $roundData;
        $tempArr['mpath']       = MY_DOMAIN . '/api/Product_check_h.php';

        return $tempArr;
    }

    /**
     * 编辑或添加票类, mini云小程序专用， 参数区别较大
     * <AUTHOR>
     * @date 2018-06-11
     *
     * @param  array  $submitData  前端传递的票类数组
     * @param  string  $ticketId  票类id  -1 则是新增
     * @param  int  $memberId  用户Id
     *
     * @return array
     */
    public function setMiniCommonTicket($submitData, $ticketId, $memberId)
    {
        // 票信息
        $ticketData = $submitData['ticket'];
        // 价格信息
        $priceData = $submitData['price_section'];

        // 对参数进行解析，是否符合
        $parseRes = $this->parseMiniTicket($ticketData, $priceData);
        if ($parseRes['code'] != 200) {
            return $parseRes;
        }

        // 主账号id
        $ticketData['account_id'] = $this->_memberId;
        // 操作员id
        $ticketData['operater_id'] = $this->_memberId;
        // 提前预定日期
        $ticketData['preorder_early_days'] = 0;
        // 取消费用参数
        $ticketData['refund_value'] = 0;
        // 状态参数
        $ticketData['status'] = 1;
        // 渠道
        $ticketData['shop'] = '11';
        // 预定成功，凭证码（短信）发送取票人手机，不发送
        $ticketData['order_sms_buyer'] = 0;
        // 预定成功，短信通知供应商, 0:不发送
        $ticketData['order_sms_supplier'] = 0;
        // 预定成功，微信通知供应商, 0:不发送
        $ticketData['order_wx_supplier'] = 0;
        // 取消短信通知供应商, 0:不发送
        $ticketData['cancel_sms_supplier'] = 0;
        // 取消短信通知取票人手机， 0:不发送
        $ticketData['cancel_sms_buyer'] = 0;

        $res = $this->setTicketNew($ticketData, $memberId);

        if ($res['code'] != 200) {
            $returnData['code'] = 201;
            $returnData['msg']  = '保存失败,' . $res['msg'];

            return $returnData;
        }
        $productId         = $res['productId'];
        $returnData['tid'] = $res['ticketId'];
        $ticketId          = $res['ticketId'];

        $returnData['lid'] = $ticketData['item_id'];
        $returnData['pid'] = $productId;

        if ($priceData) {
            $res = $this->savePrice($productId, $priceData, $ticketId);

            $returnData['price'] = $res;
        }

        return $returnData;
    }

    /**
     * 处理mini云小程序编辑或添加票类前的处理
     * <AUTHOR>
     * @date 2018-06-15
     *
     * @return array
     */
    private function parseMiniTicket($ticketData, $priceData)
    {
        if (empty($ticketData['name'])) {
            return ['code' => 204, 'msg' => '景区名称不能为空', 'data' => []];
        }

        if (empty($ticketData['item_id'])) {
            return ['code' => 204, 'msg' => '景区id不能为空', 'data' => []];
        }

        if (empty($ticketData['ticket_price'])) {
            return ['code' => 204, 'msg' => '价格不能为空', 'data' => []];
        }

//        if (empty($ticketData['refund_rule'])) {
        //            return ['code' => 204, 'msg' => '退订规则不能为空', 'data' => []];
        //        }

//        if (empty($priceData['weekdays'])) {
        //            return ['code' => 204, 'msg' => '出售时间不能为空', 'data' => []];
        //        }

        return ['code' => 200, 'msg' => 'success', 'data' => []];
    }

    /**
     * 返回数据封装
     *
     * @date   2017-07-20
     * <AUTHOR> Lan
     *
     * @param  int  $code  返回码
     * @param  string  $msg  信息说明
     *
     * @return array
     */
    private static function _return($code, $msg)
    {
        return [$code, $msg];
    }

    /**
     * 获取子票ID
     *
     * @date   2017-10-31
     * <AUTHOR> Lan
     *
     * @param  mixed  $parentTicketIds  主票Id
     *
     * @return array
     */
    public static function getSonTicketId($parentTicketIds)
    {
        if (!$parentTicketIds) {
            return [];
        }

        $packBiz = new \Business\Product\PackTicket();

        $sonTickets = [];

        if (is_array($parentTicketIds)) {
            foreach ($parentTicketIds as $key => $item) {
                $tickets[$key]['sonTickets'] = $packBiz->getSonTickets($item['tid']);
            }
        } else {
            $sonTickets = $packBiz->getSonTickets($parentTicketIds);
        }

        return $sonTickets;
    }

    /**
     * 通过套票ID获取子票
     * Create by zhangyangzhen
     * Date: 2019/3/22
     * Time: 17:03
     *
     * @param  int  $parentTicketIds  套票ID
     *
     * @return array
     */
    public static function getSonTicket($parentTicketIds)
    {
        if (!$parentTicketIds) {
            return [];
        }

        $packBiz    = new \Business\Product\PackTicket();
        $sonTickets = $packBiz->getSonTickets($parentTicketIds);

        $tickets = [];
        foreach ($sonTickets as $key => $val) {
            $tickets[$val['tid']] = $val;
        }

        return $tickets ?: [];
    }

    /**
     * 获取包含该子票的所有套票信息
     * Create by zhangyangzhen
     * Date: 2019/3/21
     * Time: 21:47
     *
     * @param $ticketId
     *
     * @return array
     */
    public static function getLinkPackTicket($sid, $ticketIds, $getField = false)
    {
        if (!$ticketIds) {
            return [];
        }

        /*$ticketModel = new TicketApi();
        $result      = $ticketModel->getPackTicketsBySonTicket($sid, $ticketIds);*/

        if (!is_array($ticketIds)){
            $ticketIds = [intval($ticketIds)];
        }
        //查询包含该子票的主票tid
        $packRelationBiz = new \Business\PackTicket\PackRelation();
        $relationRes = $packRelationBiz->queryParentList($ticketIds);
        $parentTidList = array_column($relationRes['data'], 'parentTid');
        $ticketNumArray = array_column($relationRes['data'], 'num', 'parentTid');

        //走中台接口获取主票信息
        $productApi = new \Business\JavaApi\CommodityCenter\Product();
        $result = $productApi->queryContainingPackageTicket($sid, $parentTidList);

        $ticket = [];
        foreach ($result['data'] as $key => $value) {
            if ($getField) {
                $ticket[$value['ticket_id']] = [
                    'id'        => $value['ticket_id'],
                    'ttitle'    => $value['ticketTitle'],
                    'ltitle'    => $value['title'],
                    'num'       => strval($ticketNumArray[$value['ticket_id']]),//保持数据一致
                    'lid'       => $value['lid'],
                    'pid'       => $value['pid'],
                    'apply_did' => $value['sid'],
                ];
            } else {
                $ticket[] = [
                    'id'        => $value['ticket_id'],
                    'ttitle'    => $value['ticketTitle'],
                    'ltitle'    => $value['title'],
                    'num'       => strval($ticketNumArray[$value['ticket_id']]),//保持数据一致
                    'lid'       => $value['lid'],
                    'pid'       => $value['pid'],
                    'apply_did' => $value['sid'],
                ];
            }
        }

        return $ticket;
    }

    /***
     * 保存总库存
     * <AUTHOR> Yiqiang
     * @date   2018-10-11
     * 由于固定有效期相关先下线  暂时弃用
     *
     * @param  array  $ticketData
     * @param  int  $ticketId
     *
     * @return array
     */
    public function saveTotalStorage($ticketData, $ticketId, $sid, $opid)
    {
        $storage = intval($ticketData['ticket_total_storage']);
        //先获取总库存
        $storageInfo = StorageApi::getTotalStorage($ticketId);
        if (empty($storageInfo) || $storageInfo['storage'] == -1 && $storageInfo['storage_open'] == null) {
            $openTime = date('Y-m-d', time());
            $data     = StorageApi::addTotalStorage($ticketId, $storage, $openTime, $sid, $opid);
        } else {
            if ($storage < $storageInfo['soldCount']) {
                return $this->returnData(204, '总库存不能小于已售库存');
            }
            $openTime = date('Y-m-d', $ticketData['change_storage'] ? time() : $storageInfo['openDate'] / 1000);
            $data     = StorageApi::openTotalStorage($ticketId, $storage, $openTime, $sid, $opid);
        }

        $code = $data ? 200 : 201;
        $msg  = $data ? 'success' : 'fail';

        return $this->returnData($code, $msg, ['openTime' => $openTime]);
    }

    /***
     * 固定有效期票重置日历库存
     * <AUTHOR> Yiqiang
     * @date   2018-11-01
     *
     * @param $sid
     * @param $mid
     * @param $priceData
     * @param $productId
     * @param $ticketId
     * @param $openDate
     * @param $validEnd
     *
     * @return array
     */
    public function setDailyStorageByValidate($sid, $mid, $priceData, $productId, $ticketId, $openDate, $validEnd)
    {
        //获取需要重置的价格日历-接口限制只能一个月一个月获取数据
        $calendar = array();
        if (strtotime(date('Y-m', strtotime($openDate))) != strtotime(date('Y-m', strtotime($validEnd)))) {
            $rangeTime = strtotime($validEnd) - strtotime($openDate);
            $firstday  = date('Y-m-01', strtotime($openDate));
            $startDate = date('Y-m-d', strtotime($openDate));
            $endDate   = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
            $largest   = 24; //最大查询次数上限
            while ($rangeTime > 0 && $largest > 0) {
                $largest--;
                $rangeTime -= (strtotime(date('Y-m-d', strtotime("$endDate +1 day"))) - strtotime($startDate));
                $cal       = TicketApi::getCalendarPriceAndStorage($ticketId, $startDate, $endDate);
                $calendar  = array_merge($calendar, $cal);
                $startDate = date('Y-m-d', strtotime("$endDate +1 day"));
                $endDate   = date('Y-m-d', strtotime("$startDate +1 month -1 day"));
                if (strtotime($endDate) > strtotime($validEnd)) {
                    $endDate = $validEnd;
                }
            }
        } else {
            $calendar = TicketApi::getCalendarPriceAndStorage($ticketId, $openDate, $validEnd);
        }
        $list = array();
        if ($calendar) {
            array_walk($priceData, function (&$v, $k) {
                $v['sdate'] = strtotime($v['sdate']);
                $v['edate'] = strtotime($v['edate']);
            });

            foreach ($calendar as $k => $set) {
                $day     = $set['time'];
                $dayTime = strtotime($day);
                foreach ($priceData as $priceSet) {
//预定时段数据量基本都很小
                    if ($dayTime >= $priceSet['sdate'] && $dayTime <= $priceSet['edate'] && $set['id']) {
                        $newSet = [
                            'id'      => $set['id'],
                            'sdate'   => $day,
                            'edate'   => $day,
                            'js'      => $priceSet['js'],
                            'ls'      => $priceSet['ls'],
                            'm_price' => $priceSet['m_price'],
                            'w_price' => $priceSet['w_price'],
                            'storage' => -1,
                        ];
                        $list[] = $newSet;
                    }
                }
            }
            $ticketApi = new TicketApi();
            //设置操作员信息
            $ticketApi->setOperator($sid, $mid);
            $res = $ticketApi->setDailyPrice($productId, $ticketId, $list);

            if ($res['code'] != 200) {
                pft_log('product/set_daily_fail', "设置固定有效期时重置日库存失败 $ticketId $productId |" . json_encode($res));
            }
        }
    }

    /**
     * 处理分终端验证的逻辑
     *
     * @date   2017-12-11
     * <AUTHOR> Lan
     *
     * @param  string  $terminalInfo  配置分终端值信息
     * @param  int  $verifyWay  门票验证方式
     * @param  int  $identityInfo  门票验证方式
     *
     * @return array
     */
    private static function handleTerminalInfoBySave($terminalInfo, $verifyWay, $identityInfo, $terminalCheckTimeInfo)
    {
        if ($terminalInfo) {
            //if ($verifyWay == 1 || $verifyWay == 2 && $identityInfo != 2) {
            //    return self::_return(201, '启动每个终端验证不许分批验证');

            if ($verifyWay == 1 || $verifyWay == 2 && $identityInfo != 2) {
                //return self::_return(201, '启动每个终端验证不许分批验证');
            }
            if (!isset($terminalInfo['list'])) {
                $arr     = explode(',', $terminalInfo);
                $tempArr = [];
                foreach ($arr as $key => $val) {
                    [$terminalId, $times] = explode('-', $val);
                    if ($key == 0) {
                        $tempArr[$terminalId] = intval($times);
                    } else {
                        $tempArr['list'][$terminalId] = intval($times);
                    }
                }

                // $terminalCheckTimeInfo = 'total-2,89670-1,89671-1,space_time-10,error_mark-\u8bf7\u95f4\u9694\u4e00\u4f1a\u518d\u5237\u7968,jump_terminal-89671';

                if ($terminalCheckTimeInfo) {
                    //分终端时限验证 - 只有开启了分终端验证才有
                    $timeArr = explode(',', $terminalCheckTimeInfo);
                    foreach ($timeArr as $key => $val) {
                        [$terminalId, $times] = explode('-', $val);
                        if ($terminalId != 'jump_terminal') {
                            $tempArr[$terminalId] = $times;
                        }
                    }
                    //分终端时限验证信息 -jump_terminal不受时限验证的终端号
                    if (!isset($terminalCheckTimeInfo['jump_terminal'])) {
                        $flag                     = strpos($terminalCheckTimeInfo, "jump_terminal");
                        $jumpTerminal             = substr($terminalCheckTimeInfo, $flag);
                        $jumpTerminal             = explode('-', $jumpTerminal);
                        $jumpTerminal             = $jumpTerminal[1];
                        $tempArr['jump_terminal'] = $jumpTerminal;
                    }
                }

            } else {
                $tempArr = $terminalInfo;
            }
            if (count($tempArr['list']) > 70) {
                return self::_return(201, '部分启用分终端不许超过70个');
            }

            return self::_return(200, json_encode($tempArr));
        } else {
            return self::_return(200, '');
        }
    }

    /**
     * 转化分终端信息字段
     *
     * @date   2017-12-11
     * <AUTHOR> Lan
     *
     * @param  string  $terminalInfo  配置分终端值信息
     *
     * @return string
     */
    public function handleTerminalInfoBySelect($terminalInfo)
    {
        if (!$terminalInfo) {
            return ['info' => 0, 'checkTimeInfo' => 0];
        }
        $tempArr = json_decode($terminalInfo, true);

        //$terminalInfo = 'total-2,89670-1,89671-1,space_time-10,error_mark-\u8bf7\u95f4\u9694\u4e00\u4f1a\u518d\u5237\u7968,jump_terminal-89671,89672';
        $listArr = isset($tempArr['list']) && $tempArr['list'] ? $tempArr['list'] : [];
        $total   = isset($tempArr['total']) && $tempArr['total'] ? $tempArr['total'] : 0;

        $terminalInfo = 'total' . '-' . $total . ',';
        if ($listArr) {
            foreach ($listArr as $key => $val) {
                $terminalInfo .= $key . '-' . $val . ',';
            }
        }
        $checkTimeInfo = '';
        //分终端时限验证信息
        if (isset($tempArr['space_time']) && isset($tempArr['error_mark']) && isset($tempArr['jump_terminal'])) {
            $spaceTime     = isset($tempArr['space_time']) && $tempArr['space_time'] ? $tempArr['space_time'] : 0;
            $errorMark     = isset($tempArr['error_mark']) && $tempArr['error_mark'] ? $tempArr['error_mark'] : '';
            $jumpTerminal  = isset($tempArr['jump_terminal']) && $tempArr['jump_terminal'] ? $tempArr['jump_terminal'] : 0;
            $checkTimeInfo = 'space_time' . '-' . $spaceTime . ',error_mark' . '-' . $errorMark . ',jump_terminal' . '-' . $jumpTerminal;
        }

        return ['info' => trim($terminalInfo, ','), 'checkTimeInfo' => $checkTimeInfo];
    }

    /**
     * 转换分景点配置参数
     * <AUTHOR>
     * @date 2019/10/21 0021
     *
     * @param $data 分景点配置参数
     *
     * @return array
     */
    public function handleCheckScenicInfo($data)
    {
        $res = [
            'totalScenicNumber' => 0,
            'subScenic'         => [],
        ];

        if (!$data) {
            return $res;
        }

        $param = json_decode($data, true);

        if (isset($param['totalScenicNumber'])) {
            $res['totalScenicNumber'] = $param['totalScenicNumber'];
        }

        if (isset($param['subScenic'])) {
            $res['subScenic'] = $param['subScenic'];
        }

        return $res;
    }

    /**
     * 自动上下架时间转换处理（处理半点）
     * <AUTHOR>  Li
     * @date  2020-10-02
     *
     * @param  string  $data  转换的日期格式
     *
     * @return string
     */
    private function timeChange($data)
    {
        //取出日期中的分钟数
        $minute = date('i', strtotime($data));
        //取出年月日 小时
        $hour = date('Y-m-d H', strtotime($data));

        //判断分钟数是否再整点范围内  不在范围内 向上取整 （半点整数）
        if ($minute >= 30) {
            $str = '30:00';
        } else {
            $str = '00:00';
        }

        return $hour . ':' . $str;
    }

    /**
     * 延长票的到期时间
     * <AUTHOR>
     * @date  2020-12-15
     *
     * @param  string  $postponeDate  延期日期
     * @param  array  $ticketIds  票ids
     * @param  int  $accountId  操作人id
     *
     * @return array
     */
    public function postponeTicketDate(string $postponeDate, array $ticketIds, int $accountId, $operaterId, $subSid = 0)
    {
        if (!$postponeDate || !$ticketIds || !$accountId) {
            return ['code' => 204, 'msg' => '参数错误', 'data' => []];
        }
        if (count($ticketIds) > 500) {
            return ['code' => 204, [], "每次批量延期不得大于500张"];
        }

        $result = (new \Business\JavaApi\Product\Ticket())->postponeTicketDate($postponeDate, $ticketIds, $accountId, $operaterId, $subSid);
        if ($result['code'] == 200 && $result['data']) {
            pft_log('product/ota_product_notify', "通知OTA上架|" . json_encode($result['data']));
            foreach ($result['data'] as $item) {
                //通知OTA
                OtaProductNotify::notify($item, '上架');
            }
            //分销专员自供应产品更新通知
            \Business\MultiDist\Product::pushUpdataSelfTicket('listTicket', implode(',', $result['data']),
                $this->_memberId);
        }

        return ['code' => $result['code'], 'msg' => $result['msg'], 'data' => $result['data']];
    }

    /**
     * 保存附加票
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @param  int  $tid 票ID
     * @param  array  $ticketData 票数据
     *
     * @return array
     */
    private function setTicketDeputy(int $tid, array $ticketData) : void
    {
        $enableDeputyTicket = $ticketData['enable_deputy_ticket'] ?? 0;
        //$enableDeputyTicketLimit = $ticketData['enable_deputy_ticket_limit'] ?? 0;
        $ticketDeputySets = $ticketData['ticket_deputy_sets'] ?? [];

        //如果未开启就清空附属票ID集合
        if ($enableDeputyTicket == 0) {
            $ticketDeputySets = [];
        }

        if (!empty($ticketDeputySets)) {
            foreach ($ticketDeputySets as $key => $value) {
                $ticketDeputySets[$key] = (int)$value;
            }
        }

        $ticketDeputyService = new TicketDeputyService();
        $ticketDeputyService->setTicketDeputy($this->_memberId, $tid, $ticketDeputySets);
    }


    /**
     * 转换票属性设置购票限制配置参数
     * <AUTHOR>
     * @date 2021/10/21 0021
     *
     * @param $data 购票限制配置参数
     *
     * @return array
     */
    public function handleLandBuyLimitConfig($data)
    {
        $res = [
            "buyerLimitType"     => 0,
            "buyerOrderDays"     => 0,
            "buyerOrderTotal"    => 0,
            "buyerTicketDays"    => 0,
            "buyerTicketTotal"   => 0,
            "touristLimitType"   => 0,
            "touristOrderDays"   => 0,
            "touristOrderTotal"  => 0,
            "touristTicketDays"  => 0,
            "touristTicketTotal" => 0,
            "id"                 => 0,
        ];

        if (!$data) {
            return $res;
        }

        if (isset($data['landBuyLimitRuleConfig'])) {
            $landBuyLimitRuleConfig = $data['landBuyLimitRuleConfig'];
            $res = [
                "buyerLimitType"     => $landBuyLimitRuleConfig['buyer_limit_type'] ?? 0,
                "buyerOrderDays"     => $landBuyLimitRuleConfig['buyer_order_days'] ?? 0,
                "buyerOrderTotal"    => $landBuyLimitRuleConfig['buyer_order_total'] ?? 0,
                "buyerTicketDays"    => $landBuyLimitRuleConfig['buyer_ticket_days'] ?? 0,
                "buyerTicketTotal"   => $landBuyLimitRuleConfig['buyer_ticket_total'] ?? 0,
                "touristLimitType"   => $landBuyLimitRuleConfig['tourist_limit_type'] ?? 0,
                "touristOrderDays"   => $landBuyLimitRuleConfig['tourist_order_days'] ?? 0,
                "touristOrderTotal"  => $landBuyLimitRuleConfig['tourist_order_total'] ?? 0,
                "touristTicketDays"  => $landBuyLimitRuleConfig['tourist_ticket_days'] ?? 0,
                "touristTicketTotal" => $landBuyLimitRuleConfig['tourist_ticket_total'] ?? 0,
                "id"                 => $landBuyLimitRuleConfig['id'] ?? 0,
            ];
        }

        return $res;
    }
}
