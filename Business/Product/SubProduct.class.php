<?php
/**
 * 基于产品类型的子产品
 * <AUTHOR>
 * @date   2024/1/3
 */

namespace Business\Product;

use Business\Base;

class SubProduct extends Base
{
    //连接符号
    const CONNECT_SYMBOL = '-';

    /**
     * 产品类型解析为产品类型和子产品类型
     * <AUTHOR>
     * @date   2024/1/3
     *
     * @param  mixed  $productType    产品类型 含子产品类型格式A-1
     * @param  mixed  $subType        子产品类型
     *
     * @return string[]
     */
    public static function decodeTypeAndSubType($productType, $subType = null): array
    {
        $sType = false;

        //不存在子产品类型
        if (strpos((string)$productType, self::CONNECT_SYMBOL) === false) {
            $pType = $productType;
        } else {
            list($pType, $sType) = explode('-', (string)$productType);
        }

        //productType含有子产品类型和请求subtype也带有子产品类型，优先使用subtype
        if (!empty($subType)) {
            $sType = $subType;
        }

        return [$pType, $sType];
    }

    /**
     * 产品类型和子产品还原产品类型
     * <AUTHOR>
     * @date   2024/1/4
     *
     * @param  mixed  $productType    产品类型
     * @param  mixed  $subType        子产品类型
     *
     * @return string|null
     */
    public static function encodeTypeAndSubType($productType, $subType): string
    {
        //目前还不支持A-0这种场景，这边限制下
        if (empty($productType) || is_null($subType) || !$subType) {
            return $productType;
        }

        return sprintf("%s%s%d", $productType, self::CONNECT_SYMBOL, $subType);
    }
}