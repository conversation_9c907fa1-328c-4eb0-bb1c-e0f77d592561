<?php
/**
 * 产品相关业务
 *
 * <AUTHOR>
 * @date 2017-06-29
 *
 */

namespace Business\Product;

use Business\Admin\ModuleConfig;
use Business\Authority\AuthContext;
use Business\Base;
use Business\BuyLimit\Index;
use Business\CommodityCenter\TerminalLand;
use Business\JavaApi\EvoluteApi;
use Business\JavaApi\Product\EvoluteQuery;
use Business\JavaApi\Product\ProductTag;
use Business\JavaApi\ProductApi;
use Business\JavaApi\Ticket\Listing;
use Business\JavaApi\Ticket\TicketExtendAttr;
use Business\JavaApi\Ticket\TicketPrice;
use Business\JavaApi\TicketApi;
use Business\JsonRpcApi\TravelVoucherService\VoucherService;
use Business\MemberLogin\MemberLoginHelper;
use Business\NewJavaApi\UserCenter\UserConfigRule;
use Business\Notice\WxConfig;
use Business\Order\OrderBook;
use Business\Product\Get\Evolute;
use Library\Constants\Product\ProductConst;
use Library\Exception;
use Model\Member\Member;
use Model\Product\Area as AreaModel;
use Model\Product\Land;
use Model\Product\LandResource;
use Model\Product\Ticket;
use Business\Product\ExchangeCouponProduct as ExchangeCouponProductBiz;

class Product extends Base
{

    //产品类型
    private $_proTypeList = ['A', 'B', 'C', 'F', 'G', 'H', 'I', 'G', 'J', 'K', 'L'];

    //产品级别
    private $_proLevel = [
        'A' => ['非A级', 'A', 'AA', 'AAA', 'AAAA', 'AAAAA'],
        'B' => [0],
        'C' => ['非星级', '二星级下', '三星级', '四星级', '五星级'],
        'F' => [0],
        'H' => [0],
        'I' => [0],
        'G' => [0],
        'J' => [0],
        'K' => [0],
        'L' => [0],
    ];

    //指定产品类型 产品预订列表返回字段
    private $_tagPtypeArr = [
        'A',
        'B',
        'C',
        'F',
        'G',
        'H',
    ];

    //产品状态
    private $_proStatus = [1, 2, 3];

    //必传属性
    private $_necessaryAttr = [];
    //可选属性
    private $_optionalAttr = [];
    //内部属性
    private $_internalAttr = [];
    //拓展属性
    private $_extendAttr = [];

    //是否是更新操作
    private $_isUpdate = false;

    /**
     * 获取产品信息
     * <AUTHOR>
     * @date   2017-12-07
     *
     * @param  int $sid 供应商id
     * @param  int $lid 景区id
     * @param  int $isCheckApply 是否确认供应商
     *
     * @return array
     */
    public function getProductInfo($sid, $lid, $isCheckApply = 1)
    {

        if (!$sid || !$lid) {
            return $this->returnData(204, '参数错误');
        }

        // 2018-3-11 切换java
        //$productApi  = new ProductApi();
        //$landInfoArr = $productApi->getLandInfo($lid);
        //景区旧工程迁移
        $landApi     = new \Business\CommodityCenter\Land();
        $landInfoArr = $landApi->queryLandById(intval($lid));

        if ($landInfoArr['code'] != 200) {
            return $this->returnData(204, $landInfoArr['msg']);
        }

        // 返回数据
        $landInfo['title']       = $landInfoArr['data']['name'];
        $landInfo['letters']     = $landInfoArr['data']['letters'];
        $landInfo['tel']         = $landInfoArr['data']['tel'];
        $landInfo['jtype']       = $landInfoArr['data']['type'];
        $landInfo['opentime']    = '';
        $landInfo['runtime']     = $landInfoArr['data']['runtime'];
        $landInfo['area']        = $landInfoArr['data']['area'];
        $landInfo['jtzn']        = $landInfoArr['data']['traffic'];
        $landInfo['imgpath']     = $landInfoArr['data']['img_path'];
        $landInfo['venus_id']    = $landInfoArr['data']['venus_id'];
        $landInfo['topic']       = $landInfoArr['data']['topics'];
        $landInfo['p_type']      = $landInfoArr['data']['type'];
        $landInfo['jdjj']        = $landInfoArr['data']['introduce'];
        $landInfo['bhjq']        = $landInfoArr['data']['details'];
        $landInfo['jqts']        = $landInfoArr['data']['notice'];
        $landInfo['imgpathGrp']  = $landInfoArr['data']['img_path_group'];
        $landInfo['lng_lat_pos'] = $landInfoArr['data']['lng_lat_pos'];
        $landInfo['apply_did']   = $landInfoArr['data']['account_id'];
        $landInfo['address']     = $landInfoArr['data']['address'];

        $landInfo['province_name'] = $landInfoArr['data']['province_name'];
        $landInfo['city_name']     = $landInfoArr['data']['city_name'];
        $landInfo['ext']           = $landInfoArr['data']['ext'] ?? (object)[];
        $landInfo['videoUrl']      = $landInfoArr['data']['videoUrl'] ?? '';
        $landInfo['oversea']       = $landInfoArr['data']['oversea'];

        if ($landInfo['imgpathGrp']) {
            $tmpImgpathGrp          = @json_decode($landInfo['imgpathGrp'], true);
            $imgpathGrp             = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($landInfo['imgpathGrp']);
            $landInfo['imgpathGrp'] = $imgpathGrp;
        } else {
            $landInfo['imgpathGrp'] = [];
        }

        if ($isCheckApply == 1 && $landInfo['apply_did'] != $sid) {
            return $this->returnData(204, '无权查看');
        }

        return $this->returnData(200, '', $landInfo);
    }

    /**
     * Create by zhangyangzhen
     * Date: 2018/12/28
     * Time: 16:52
     *
     * @param $sid
     * @param $aid
     * @param $lid
     * @param $channel
     *
     * @return array
     */
    public function getWPTProductInfo($sid, $aid, $lid, $channel = '')
    {
        if (!$sid || !$aid || !$lid) {
            return $this->returnData(203, '参数错误');
        }

        $productApi  = new ProductApi();
        $landInfoArr = $productApi->getWPTLandInfo($sid, $aid, $lid, $channel);

        if ($landInfoArr['code'] != 200) {
            return $this->returnData($landInfoArr['code'], $landInfoArr['msg']);
        }

        return $this->returnData(200, '', $landInfoArr['data']);
    }

    /**
     * 发布产品
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @param  integer $sid 主账号id
     * @param  integer $mid 操作人id
     * @param  string $title 产品名称
     * @param  string $ptype 产品类型
     * @param  string $level 产品级别
     * @param  string $address 产品地址
     * @param  integer $province 省份代码
     * @param  integer $city 城市代码
     * @param  string $notice 预定须知
     * @param  string $details 产品详情
     * @param  string $thumbImg 产品缩略图
     * @param  integer $status 产品状态
     * @param  array $optional 可选属性数组
     * @param  array $internal 内部使用属性数组
     * @param  array $extend 扩展属性数组
     *
     * @return array
     */
    public function publish($sid, $mid, $title, $ptype, $level, $address, $province,
        $city, $notice, $details, $thumbImg, $status = 1, $optional = [], $internal = [], $extend = [], $oversea = 0, $subSid = 0, $zoneId = 0)
    {

        $this->_necessaryAttr = [
            'id'          => 0,
            'account_id'  => $sid,
            'operater_id' => $mid,
            'name'        => $title,
            'type'        => $ptype,
            'level'       => $level,
            'address'     => $address,
            'province_id' => $province,
            'city_id'     => $city,
            'zone_id'     => $zoneId,
            'oversea'     => $oversea,
            'notice'      => $notice,
            'details'     => $details,
            'img_path'    => $thumbImg,
            'status'      => $status,
        ];
        $checkRes             = $this->_necessaryAttrCheck();

        if ($checkRes['code'] != 200) {
            $this->returnData(204, $checkRes['msg']);
        }

        $this->_optionalAttr = $optional;
        $this->_internalAttr = $internal;
        $this->_extendAttr   = $extend;

        $result = $this->_saveAction(true, $subSid);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 发布产品
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @param  integer $sid 主账号id
     * @param  integer $mid 操作人id
     * @param  integer $proId 产品id
     * @param  string $title 产品名称
     * @param  string $ptype 产品类型
     * @param  string $level 产品级别
     * @param  string $address 产品地址
     * @param  integer $province 省份代码
     * @param  integer $city 城市代码
     * @param  string $notice 预定须知
     * @param  string $details 产品详情
     * @param  string $thumbImg 产品缩略图
     * @param  integer $status 产品状态
     * @param  array $optional 可选属性数组
     * @param  array $internal 内部使用属性数组
     * @param  array $extend 扩展属性数组
     * @param  int $oversea 所在地区
     *
     * @return array
     */
    public function update($sid, $mid, $proId, $title, $ptype, $level, $address, $province,
        $city, $notice, $details, $thumbImg, $status = 1, $optional = [], $internal = [], $extend = [], $oversea = 0, $subSid = 0, $zoneId = 0)
    {

        if (!$proId) {
            return $this->returnData(204, '产品id缺失');
        }

        if (!$this->_hasRightToDeal($sid, $proId)) {
            return $this->returnData(204, '无操作权限');
        }

        $this->_necessaryAttr = [
            'id'          => $proId,
            'account_id'  => $sid,
            'operater_id' => $mid,
            'name'        => $title,
            'type'        => $ptype,
            'level'       => $level,
            'address'     => $address,
            'province_id' => $province,
            'oversea'     => $oversea,
            'city_id'     => $city,
            'zone_id'     => $zoneId,
            'notice'      => $notice,
            'details'     => $details,
            'img_path'    => $thumbImg,
            'status'      => $status,
        ];

        $this->_optionalAttr = $optional;

        $checkRes = $this->_necessaryAttrCheck();

        if ($checkRes['code'] != 200) {
            $this->_optionalAttr = [];

            return $this->returnData(204, $checkRes['msg']);
        }

        $this->_internalAttr = $internal;
        $this->_extendAttr   = $extend;

        $this->_isUpdate = true;

        $result = $this->_saveAction(false, $subSid);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 上架产品
     *
     * <AUTHOR>
     * @date   2017-07-06
     *
     * @param  int $sid 主账号id
     * @param  int $mid 操作人员id
     * @param  int $lid 产品id
     *
     * @return array
     */
    public function putaway($sid, $mid, $lid, $subSid = 0)
    {
        if (!($sid && $mid && $lid)) {
            return $this->returnData(204, '参数缺失');
        }

        if (!$this->_hasRightToDeal($sid, $lid)) {
            return $this->returnData(204, '无操作权限');
        }

        $landJavaBus = new \Business\JavaApi\CommodityCenter\Land();
        $result      = $landJavaBus->listingLand($sid, $mid, $lid, $subSid);

        if ($result['code'] == 200) {
            //分销专员自供应产品更新通知
            if (\Business\MultiDist\Product::SELF_ACTION_SWITCH) {
                \Business\MultiDist\Product::pushUpdataSelfByPon($lid, $sid);
            }

            return $this->returnData(200, '上架成功');
        } else {
            return $this->returnData(204, '上架失败');
        }
    }

    /**
     * 下架产品
     *
     * <AUTHOR>
     * @date   2017-07-06
     *
     * @param  int $sid 主账号id
     * @param  int $mid 操作人员id
     * @param  int $lid 产品id
     *
     * @return array
     */
    public function soldOut($sid, $mid, $lid, $subSid = 0)
    {

        if (!($sid && $mid && $lid)) {
            return $this->returnData(204, '参数缺失');
        }

        if (!$this->_hasRightToDeal($sid, $lid)) {
            return $this->returnData(204, '无操作权限');
        }

        $landJavaBus = new \Business\JavaApi\CommodityCenter\Land();
        $result      = $landJavaBus->deListingLand($sid, $mid, $lid, $subSid);

        if ($result['code'] == 200) {
            //分销专员自供应产品更新通知
            if (\Business\MultiDist\Product::SELF_ACTION_SWITCH) {
                \Business\MultiDist\Product::pushUpdataSelfByPoff($lid, $sid);
            }

            return $this->returnData(200, '下架成功');
        } else {
            return $this->returnData(204, '下架失败');
        }
    }

    /**
     * 删除产品
     *
     * <AUTHOR>
     * @date   2017-07-06
     *
     * @param  int $sid 主账号id
     * @param  int $mid 操作人员id
     * @param  int $lid 产品id
     *
     * @return array
     */
    public function delete($sid, $mid, $lid)
    {

        if (!($sid && $mid && $lid)) {
            return $this->returnData(204, '参数缺失');
        }

        if (!$this->_hasRightToDeal($sid, $lid)) {
            return $this->returnData(204, '无操作权限');
        }

        $landJavaBus = new \Business\JavaApi\CommodityCenter\Land();
        $result      = $landJavaBus->deleteLand($sid, $mid, $lid);

        if ($result['code'] == 200) {
            return $this->returnData(200, '删除成功');
        } else {
            return $this->returnData(204, '删除失败');
        }
    }

    /**
     * 恢复产品
     *
     * <AUTHOR>
     * @date   2017-07-06
     *
     * @param  int $sid 主账号id
     * @param  int $mid 操作人员id
     * @param  int $lid 产品id
     *
     * @return array
     */
    public function restore($sid, $mid, $lid, $subSid = 0)
    {

        if (!($sid && $mid && $lid)) {
            return $this->returnData(204, '参数缺失');
        }

        if ($sid != 1) {
            if (!$this->_hasRightToDeal($sid, $lid)) {
                return $this->returnData(204, '无操作权限');
            }
        }
        //新办有问题先用旧接口 -2020-7-24
//        $landJavaBus = new \Business\JavaApi\CommodityCenter\Land();
//        $result      = $landJavaBus->recoveryLand($sid, $mid, $lid);
        $landJavaBus = new ProductApi();
        $result      = $landJavaBus->restore($sid, $mid, $lid, $subSid);

        if ($result['code'] == 200) {
            return $this->returnData(200, '恢复成功');
        } else {
            return $this->returnData(204, '恢复失败');
        }
    }

    /**
     * 必须属性验证
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @return array
     */
    private function _necessaryAttrCheck()
    {

        $attr = $this->_necessaryAttr;

        if (!($attr['account_id'] && $attr['operater_id'] && $attr['name'] && $attr['type'])) {
            return $this->returnData(204, '参数缺失');
        }

        //保险产品兼容
        if ($attr['type'] == 'L') {
            if (!($attr['province_id'] && $attr['details'] && $attr['img_path'])) {
                return $this->returnData(204, '参数缺失');
            }
        } else {
            if (!($attr['province_id'] && $attr['notice'] && $attr['details'] && $attr['img_path'])) {
                return $this->returnData(204, '参数缺失');
            }
        }

        if (!in_array($attr['type'], $this->_proTypeList)) {
            return $this->returnData(204, '产品类型错误');
        }

        if (!in_array($attr['status'], $this->_proStatus)) {
            return $this->returnData(204, '产品状态错误');
        }

        if (!in_array($attr['level'], $this->_proLevel[$attr['type']]) && !ExchangeCouponProductBiz::isExchangeCouponProduct($attr['type'], $this->_optionalAttr['sub_type'] ?? 0)) {
            return $this->returnData(204, '产品级别错误');
        }

        return $this->returnData(200);

    }

    /**
     * 保存产品数据
     *
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @param  boolean $updateAuth 发布产品时是否更新景区账号的权限
     *
     * @return mixtrue
     */
    private function _saveAction($updateAuth = true, $subSid = 0)
    {

        //解析产品参数
        $data = $this->_parseProDataForJavaApi();

        //$result = $data['id'] ? ProductApi::update($data) : ProductApi::publish($data);

        $landJavaBus = new \Business\JavaApi\CommodityCenter\Land();

        $data = $this->changeParamsToJava($data);
        if ($subSid){
            $data['subSid'] = $subSid;
        }

        if ($data['id']) {
            $result = $landJavaBus->updateLand($data);
            if ($result['code'] == 200) {
                //分销专员自供应产品更新通知
                if (\Business\MultiDist\Product::SELF_ACTION_SWITCH) {
                    \Business\MultiDist\Product::pushUpdataSelfDataByPdata($data['id'], $data['applyDid'],
                        $data['title']);
                }
            }
        } else {
            $result = $landJavaBus->createLand($data);
            if ($result['code'] == 200) {
                WxConfig::pushJobForRelease($data['applyDid'], $result['data']);
            }
        }

        return $result;
    }

    /**
     * 解析java所需要的参数
     * <AUTHOR>
     * @date   2017-07-05
     *
     * @return array
     */
    private function _parseProDataForJavaApi()
    {

        $necessaryAttr = $this->_necessaryAttr;

        if ($this->_isUpdate) {
            $optionalAttr = $this->_optionalAttr;
            $internalAttr = $this->_internalAttr;
            $extendAttr   = $this->_parseExtendData($this->_extendAttr);
        } else {
            //解析可选属性
            $optionalAttr = $this->_parseOptionalData($this->_optionalAttr);
            //解析内部属性
            $internalAttr = $this->_parInternalData($this->_internalAttr);
             //解析拓展信息
            $extendAttr = $this->_parseExtendData($this->_extendAttr);
        }

        $necessaryAttr['details'] = $this->_filterContent($necessaryAttr['details']);
        $necessaryAttr['notice']  = $this->_filterContent($necessaryAttr['notice']);
        $optionalAttr['traffic']  = $this->_filterContent($optionalAttr['traffic']);

        $data = array_merge($necessaryAttr, $optionalAttr, $internalAttr, $extendAttr);

        return $data;
    }

    /**
     * 解析可选项参数
     *
     * <AUTHOR>
     * @date   2017-06-30
     *
     * @param  array $optional 可选项列表
     *
     * @return array
     */
    private function _parseOptionalData(array $optional)
    {

        if (!$optional) {
            return [];
        }

        $data = [];
        //区域id
        if (isset($optional['zone_id']) && $optional['zone_id']) {
            $data['zone_id'] = intval($optional['zone_id']);
        }

        $data['custom_made'] = 1;
        $data['sub_type']    = $optional['sub_type'] ?? 0; //产品子类型

        //产品简介
        if (isset($optional['introduce']) && $optional['introduce']) {
            $data['introduce'] = $optional['introduce'];
        }

        //交通简介
        if (isset($optional['traffic']) && $optional['traffic']) {
            $data['traffic'] = $optional['traffic'];
        }

        //景区图片
        if (isset($optional['img_path_group']) && $optional['img_path_group']) {
            $data['img_path_group'] = $optional['img_path_group'];
        }

        //联系电话
        if (isset($optional['tel']) && $optional['tel']) {
            $data['tel'] = $optional['tel'];
        }

        //主题
        if (isset($optional['topics']) && $optional['topics']) {
            $data['topics'] = $optional['topics'];
        }

        //营业时间
        if (isset($optional['runtime']) && $optional['runtime']) {
            $data['runtime'] = $optional['runtime'];
        }

        //出发地
        if (isset($optional['start_place']) && $optional['start_place']) {
            $data['start_place'] = $optional['start_place'];
        }

        //目的地
        if (isset($optional['end_place']) && $optional['end_place']) {
            $data['end_place'] = $optional['end_place'];
        }

        //订单通知联系人
        if (isset($optional['sms_phone']) && $optional['sms_phone']) {
            $data['sms_phone'] = $optional['sms_phone'];
        }

        //资源库id
        if (isset($optional['resource_id']) && $optional['resource_id']) {
            $data['resource_id'] = $optional['resource_id'];
        }

        //创建新的资源
        if (isset($optional['create_resource']) && $optional['create_resource']) {
            $data['create_resource'] = $optional['create_resource'];
        }

        //景点经纬度
        if (isset($optional['lng_lat_pos']) && $optional['lng_lat_pos']) {
            $data['lng_lat_pos'] = $optional['lng_lat_pos'];
        }

        //子景点列表
        if (isset($optional['scenicid_list']) && $optional['scenicid_list']) {
            $data['scenicid_list'] = $optional['scenicid_list'];
        }

        //场馆id
        if (isset($optional['venus_id'])) {
            $data['venus_id'] = $optional['venus_id'];
        }

        //场馆id
        if (isset($optional['order_flag'])) {
            $data['order_flag'] = $optional['order_flag'];
        }

        //团单限制json
        if (isset($optional['group_limit'])) {
            $data['group_limit'] = $optional['group_limit'];
        }

        //团单购买至少
        if (isset($optional['group_number_limit'])) {
            $data['group_number_limit'] = $optional['group_number_limit'];
        }

        //团单具体限制
        if (isset($optional['group_ticket_limit'])) {
            $data['group_ticket_limit'] = $optional['group_ticket_limit'];
        }

        //视频
        $data['videoUrl'] = '';
        if (isset($optional['videoUrl'])) {
            $data['videoUrl'] = $optional['videoUrl'];
        }

        return $data;
    }

    /**
     * 内部使用的属性(不对外开放)
     * <AUTHOR>
     * @date   2017-07-03
     *
     * @param  array $internal 内部属性数组
     *
     * @return array
     */
    private function _parInternalData(array $internal)
    {

        if (!$internal) {
            return [];
        }

        return [];
    }

    /**
     * 产品扩展信息(预留)
     * <AUTHOR>
     * @date   2017-07-03
     *
     * @param  array $extend 拓展属性数组
     *
     * @return array
     */
    private function _parseExtendData(array $extend)
    {
        if (!$extend) {
            return [];
        }
        $data = [];
        //景区首图图片
        if (isset($extend['first_picture']) && $extend['first_picture']) {
            $data['ext'][] = [
                'key' => 'first_picture',
                'val' => $extend['first_picture'],
            ];
        }

        return $data;
    }

    /**
     * 是否具有操作权限
     *
     * <AUTHOR>
     * @date   2017-07-06
     *
     * @param  int $sid 供应商id
     * @param  int $lid 景区id
     *
     * @return boolean
     */
    private function _hasRightToDeal($sid, $lid)
    {

        $landModel = new Land();

        $landInfo = $landModel->getLandInfo($lid, false, 'apply_did');

        if (!$landInfo) {
            return false;
        }
        if (MemberLoginHelper::isSubMerchantLogin()){
            $sid = MemberLoginHelper::getLoginBusinessMember()->getSupplierId();
        }

        return $landInfo['apply_did'] == $sid;

    }

    /**
     * 过滤一些乱七八糟的字符
     * <AUTHOR>
     * @date   2017-08-17
     *
     * @param  string $content 文本内容
     *
     * @return string
     */
    private function _filterContent($content)
    {

        return preg_replace_callback('/[\xf0-\xf7].{3}/', function ($r) {
            return '';
        }, $content);

    }

    /**
     * 产品关联资源
     * <AUTHOR>
     * @date   2017-09-01
     *
     * @param  int $lid 景区id
     * @param  int $resourceId 资源id
     *
     * @return array
     *
     */
    public function releateSource($lid, $resourceId)
    {

        if (!$lid || !$resourceId) {
            return $this->returnData(204, '参数缺失');
        }

        //$productApi = new ProductApi();
        //$result     = $productApi->updateResouceId($lid, $resourceId);

        //景区旧工程迁移
        $result = (new \Business\CommodityCenter\Land())->resourceBind(intval($lid), intval($resourceId));

        $code = 204;
        if ($result['code'] == 200) {
            $code = 200;
        }

        // 返回错误码 和 java 的错误提示
        return $this->returnData($code, $result['msg']);
    }

    /**
     * 获取产品信息
     * <AUTHOR>
     * @date   2017-09-05
     *
     * @param  int $lid 景点id
     *
     * @return array
     *
     */
    public function getLandInfo($lid)
    {
        if (!$lid) {
            return $this->returnData(204, '参数缺失');
        }

        //$productApi = new ProductApi();
        //$result     = $productApi->getLandInfo($lid);

        //景区旧工程迁移
        $landApi = new \Business\CommodityCenter\Land();
        $result  = $landApi->queryLandById(intval($lid));

        $code = 204;
        if ($result['code'] == 200) {
            $code = 200;
        }

        // 返回错误码 和 java 的错误提示
        return $this->returnData($code, $result['msg'], $result['data']);
    }

    /**
     * 获取景区列表
     * <AUTHOR> Lin
     *
     * @param  int $member 会员id
     * @param  int $pageSize 每页显示记录数
     * @param  int $startLine 起始行,默认为0
     *
     * @return array
     */
    public function getLandList($member, $pageSize, $startLine = 0, $pTypes = [])
    {

        $productApi = new ProductApi();
        $result     = $productApi->getLandList($member, $pageSize, $startLine, $pTypes);

        if ($result == false) {
            return $this->returnData(204, '获取数据失败');
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取自供应产品列表
     * <AUTHOR>
     * @date 2018-06-11
     *
     * @param  int $memberId 会员id
     * @param  string $listType 获取类型， sale为出售中自供应产品， out为仓库中自供应产品
     * @param  int $page 起始页 默认1
     * @param  int $pageSize 页显示数量 默认15
     * @param  string $itemId 产品id 默认空
     * @param  string $itemName 产品名称 默认空
     * @param  string $itemCode 6位数产品编码
     * @param  string $terminalNum 终端号 默认空
     * @param  string $province 省份， 默认空
     * @param  string $city 城市，默认空
     * @param  string $type 产品类别,多个类别逗号分割;(A:景点门票;B:旅游线路;C:酒店客房;F:套票产品;H:剧场演出;I:年卡产品;G:餐饮产品;J:旅游特产;)
     * @param  string $startDate 发布产品的时间-开始时间【yyyy-MM-dd】
     * @param  string $endDate 发布产品的时间-结束时间【yyyy-MM-dd】
     * @param  bool $showExpire 过期：true是;false否;
     * @param  bool $onlyShowItem 仅列出产品:true是;false;
     *
     * @return array
     */
    public function getProductList($memberId, $listType = 'sale', $page = 1, $pageSize = 15, $itemId = '', $itemName = '', $itemCode = '', $terminalNum = '', $province = '', $city = '', $type = '', $startDate = '', $endDate = '', $showExpire = false, $onlyShowItem = false)
    {

        $params = [];

        if (!$memberId || !$page || !$pageSize) {
            return $this->returnData(204, '参数缺失');
        }

        $params['account_id'] = $memberId;
        $params['page_num']   = $page;
        $params['page_size']  = $pageSize;

        if (!empty($itemId)) {
            // 产品id
            $params['item_id'] = $itemId;
        }

        if (!empty($itemName)) {
            // 产品名称
            $params['item_name'] = $itemName;
        }

        if (!empty($itemCode)) {
            // 6位数产品编码
            $params['item_code'] = $itemCode;
        }

        if (!empty($terminalNum)) {
            // 终端号
            $params['terminal_num'] = $terminalNum;
        }

        if (!empty($province)) {
            // 省份id
            $params['province'] = $province;
        }

        if (!empty($city)) {
            // 城市id
            $params['city'] = $city;
        }

        if (!empty($type)) {
            //产品类别,多个类别逗号分割;(A:景点门票;B:旅游线路;C:酒店客房;F:套票产品;H:剧场演出;I:年卡产品;G:餐饮产品;J:旅游特产;)
            $params['type'] = $type;
        }

        if (!empty($startDate)) {
            // 发布产品的时间-开始时间【yyyy-MM-dd】
            $params['start_date'] = $startDate;
        }

        if (!empty($endDate)) {
            // 发布产品的时间-结束时间【yyyy-MM-dd】
            $params['end_date'] = $endDate;
        }

        if ($showExpire !== false) {
            // 过期：true是;false否;
            $params['show_expire'] = true;
        }

        if ($onlyShowItem !== false) {
            // 仅列出产品:true是;false;
            $params['only_show_item'] = true;
        }

        $productApi = new ProductApi();

        if ($listType == 'out') {
            // 获取仓库中自供应产品
            $result = $productApi->getSelfProductOutList($params);
        } else {
            // 获取出售中自供应产品
            $result = $productApi->getSelfProductSaleList($params);
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    ///**
    // * 根据关键字 模糊匹配 产品名称 支持汉字/拼音   支持不同身份角色
    // * <AUTHOR>
    // * @date   2017-11-14
    // */
    //public function getLandInfoByKeyWord($keyWord, $dtype, $memberId, $account = '')
    //{
    //    $list = [];
    //
    //    if (mb_strlen($keyWord) == strlen($keyWord)) {
    //        //英文
    //        $type = 2;
    //    } else {
    //        //汉字
    //        $type = 1;
    //    }
    //
    //    if ($dtype == 7) {
    //        //集团账号
    //        $memberRelationship = new MemberRelationship($memberId);
    //        $memRelationRes     = $memberRelationship->getMemberByParentId($memberId, 'son_id', false);
    //        $memList            = array_column($memRelationRes, 'son_id');
    //        $ticketModel        = new Ticket();
    //        $list               = $ticketModel->getLandListByMember($memList, $keyWord, $type);
    //    } else if (in_array($dtype, [2, 3])) {
    //        //景区账号
    //        $landModel = new Land();
    //        $list      = $landModel->getListByResource($account, $dtype, $keyWord);
    //    } else {
    //        $ticketModel = new Ticket();
    //        $list        = $ticketModel->getLandListByMember($memberId, $keyWord, $type);
    //    }
    //
    //    return $list;
    //}

    /**
     * 兼容new d项目下的  salePros方法,吐血
     * <AUTHOR>
     * @date   2018-01-22
     *
     * @param  integer $fid 分销商id
     * @param  integer $lid 景区id
     * @param  boolean $needActive false:需要active=1
     * @param  boolean $includeSecond 是否获取二手转分销
     *
     * @return array
     */
    public function salePros($fid = 0, $lid = 0, $active = true, $includeSecond = true)
    {
        if (!$fid && !$lid) {
            return $this->returnData(204, '参数错误');
        }

        if ($lid) {
            //获取单个景区的数据
            //queryEvolutes mod2 success
            $products = $this->_getProductsForLid($fid, $lid, $active);
        } else {
            //获取分销商的全部数据
            //queryEvolutes mod3
            $products = $this->_getProductsForMember($fid, $active, $includeSecond);
        }

        return $this->returnData(200, '', $products ?: []);
    }

    /**
     * 获取一个景区下的票类
     *
     * @param  int $fid 分销商id
     * @param  int $lid 景区id
     *
     * @return array
     */
    private function _getProductsForLid($fid, $lid, $active)
    {
        $ticModel = new Ticket('slave');

        //queryEvolutes mod2 success
        $getEvoluteBiz = new Evolute();
        $activeNew     = -1;
        if ($active === false) {
            $activeNew = 1;
        }
        $list = $getEvoluteBiz->getEvoluteByFidLidStatusActive($fid, $lid, 0, $activeNew);

        $return = [];
        if ($list) {
            $pidArr = array_column($list, 'pid');

            //获取产品信息
            $condition = [
                'pStatuss'    => [0],
                'applyLimit'  => 1,
                'landFlag'    => 1,
                'productFlag' => 1,
                'landFFlag'   => 1,
                'landStatus'  => 1,
            ];
            $javaApi   = new \Business\CommodityCenter\Ticket();
            $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('pid,id,pay,delaytime,getaddr,title,tprice,notes,shop,buy_limit_low,ddays,delaydays,batch_check,batch_day_check,order_limit,overdue_refund,status,order_end,refund_audit,order_start,delaytype,cancel_cost',
                'id,apply_did,p_status',
                'id,p_type,jtype,areacode,terminal,title,salerid,area,topic,px,runtime,imgpath,letters,order_flag,address',
                'ass_station,tourist_info,mdays,mhour,rdays,age_limit_min,age_limit_max,dhour,sendVoucher,v_time_limit',
                '',
                null, null, null, $pidArr, $condition);

            $products = [];
            if (!empty($ticketArr)) {
                foreach ($ticketArr as $ticketInfo) {
                    $products[$ticketInfo['product']['id']]['tid']             = $ticketInfo['ticket']['id'];
                    $products[$ticketInfo['product']['id']]['pay']             = $ticketInfo['ticket']['pay'];
                    $products[$ticketInfo['product']['id']]['delaytime']       = $ticketInfo['ticket']['delaytime'];
                    $products[$ticketInfo['product']['id']]['getaddr']         = $ticketInfo['ticket']['getaddr'];
                    $products[$ticketInfo['product']['id']]['ttitle']          = $ticketInfo['ticket']['title'];
                    $products[$ticketInfo['product']['id']]['tprice']          = $ticketInfo['ticket']['tprice'];
                    $products[$ticketInfo['product']['id']]['notes']           = $ticketInfo['ticket']['notes'];
                    $products[$ticketInfo['product']['id']]['shop']            = $ticketInfo['ticket']['shop'];
                    $products[$ticketInfo['product']['id']]['ticket_id']       = $ticketInfo['ticket']['id'];
                    $products[$ticketInfo['product']['id']]['buy_limit_low']   = $ticketInfo['ticket']['buy_limit_low'];
                    $products[$ticketInfo['product']['id']]['ddays']           = $ticketInfo['ticket']['ddays'];
                    $products[$ticketInfo['product']['id']]['delaydays']       = $ticketInfo['ticket']['delaydays'];
                    $products[$ticketInfo['product']['id']]['batch_check']     = $ticketInfo['ticket']['batch_check'];
                    $products[$ticketInfo['product']['id']]['batch_day_check'] = $ticketInfo['ticket']['batch_day_check'];
                    $products[$ticketInfo['product']['id']]['order_limit']     = $ticketInfo['ticket']['order_limit'];
                    $products[$ticketInfo['product']['id']]['overdue_refund']  = $ticketInfo['ticket']['overdue_refund'];
                    $products[$ticketInfo['product']['id']]['status']          = $ticketInfo['ticket']['status'];
                    $products[$ticketInfo['product']['id']]['order_end']       = $ticketInfo['ticket']['order_end'];
                    $products[$ticketInfo['product']['id']]['refund_audit']    = $ticketInfo['ticket']['refund_audit'];
                    $products[$ticketInfo['product']['id']]['order_start']     = $ticketInfo['ticket']['order_start'];
                    $products[$ticketInfo['product']['id']]['delaytype']       = $ticketInfo['ticket']['delaytype'];
                    $products[$ticketInfo['product']['id']]['cancel_cost']     = $ticketInfo['ticket']['cancel_cost'];
                    $products[$ticketInfo['product']['id']]['id']              = $ticketInfo['product']['id'];
                    $products[$ticketInfo['product']['id']]['pid']             = $ticketInfo['product']['id'];
                    $products[$ticketInfo['product']['id']]['apply_did']       = $ticketInfo['product']['apply_did'];
                    $products[$ticketInfo['product']['id']]['p_status']        = $ticketInfo['product']['p_status'];
                    $products[$ticketInfo['product']['id']]['lid']             = $ticketInfo['land']['id'];
                    $products[$ticketInfo['product']['id']]['p_type']          = $ticketInfo['land']['p_type'];
                    $products[$ticketInfo['product']['id']]['jtype']           = $ticketInfo['land']['jtype'];
                    $products[$ticketInfo['product']['id']]['areacode']        = $ticketInfo['land']['areacode'];
                    $products[$ticketInfo['product']['id']]['terminal']        = $ticketInfo['land']['terminal'];
                    $products[$ticketInfo['product']['id']]['title']           = $ticketInfo['land']['title'];
                    $products[$ticketInfo['product']['id']]['salerid']         = $ticketInfo['land']['salerid'];
                    $products[$ticketInfo['product']['id']]['area']            = $ticketInfo['land']['area'];
                    $products[$ticketInfo['product']['id']]['topic']           = $ticketInfo['land']['topic'];
                    $products[$ticketInfo['product']['id']]['px']              = $ticketInfo['land']['px'];
                    $products[$ticketInfo['product']['id']]['runtime']         = $ticketInfo['land']['runtime'];
                    $products[$ticketInfo['product']['id']]['imgpath']         = $ticketInfo['land']['imgpath'];
                    $products[$ticketInfo['product']['id']]['py']              = $ticketInfo['land']['letters'];
                    $products[$ticketInfo['product']['id']]['order_flag']      = $ticketInfo['land']['order_flag'];
                    $products[$ticketInfo['product']['id']]['address']         = $ticketInfo['land']['address'];
                    $products[$ticketInfo['product']['id']]['ass_station']     = $ticketInfo['land_f']['ass_station'];
                    $products[$ticketInfo['product']['id']]['tourist_info']    = $ticketInfo['land_f']['tourist_info'];
                    $products[$ticketInfo['product']['id']]['mdays']           = $ticketInfo['land_f']['mdays'];
                    $products[$ticketInfo['product']['id']]['mhour']           = $ticketInfo['land_f']['mhour'];
                    $products[$ticketInfo['product']['id']]['rdays']           = $ticketInfo['land_f']['rdays'];
                    $products[$ticketInfo['product']['id']]['age_limit_min']   = $ticketInfo['land_f']['age_limit_min'];
                    $products[$ticketInfo['product']['id']]['age_limit_max']   = $ticketInfo['land_f']['age_limit_max'];
                    $products[$ticketInfo['product']['id']]['dhour']           = $ticketInfo['land_f']['dhour'];
                    $products[$ticketInfo['product']['id']]['sendVoucher']     = $ticketInfo['land_f']['sendVoucher'];
                    $products[$ticketInfo['product']['id']]['v_time_limit']    = $ticketInfo['land_f']['v_time_limit'];
                }
            }

            foreach ($list as $item) {
                if (isset($products[$item['pid']])) {
                    $tmp               = $products[$item['pid']];
                    $tmp['px']         = $item['px'];
                    $tmp['tx']         = $item['tx'];
                    $tmp['status']     = 0;
                    $tmp['channel']    = $item['channel'];
                    $tmp['aid']        = $item['sid'];
                    $tmp['sapply_did'] = $item['sid'];
                    $tmp['py']         = substr($tmp['py'], 0, 1);
                    if ($item['sid'] == $item['sourceid']) {
                        $return[$item['pid']] = $tmp;
                    } else {
                        $return[] = $tmp;
                    }
                }
            }

            return $return;
        }
    }

    /**
     * 获取能销售的全部产品
     *
     * @param  int $fid 分销商id
     * @param  bool $active false：需要active=1
     * @param  bool $include_second 是否包含转分销
     *
     * @return array
     */
    private function _getProductsForMember($fid, $active, $includeSecond)
    {

        $ticModel = new Ticket('slave');
        //需要获取的字段
        $option = ['field' => $this->_getField()];
        //获取自供应产品
        $self = $ticModel->getSaleProducts($fid, $option);

        //获取转分销产品
        $dis = [];
        if ($includeSecond) {
            $evoWhere    = [];
            $activeValue = -1;
            if ($active === false) {
                //需要打开转分销按钮
                $evoWhere['active'] = 1;
                $activeValue        = 1;
            }

            //echo 4545;exit;
            //$dis = $ticModel->getSaleDisProducts($fid, $evoWhere, $option);
            $dis = $ticModel->getSaleDisProductsByFidSourceIdStatusActive($fid, $activeValue, $option);
        }

        $proLists = array_merge($self, $dis);

        //旧版程序兼容
        foreach ($proLists as &$item) {
            if ($item['level'] == 0) {
                $item['aid'] = 0;
            }
            $item['py']         = substr($item['py'], 0, 1);
            $item['estatus']    = 0;
            $item['lvl']        = $item['level'];
            $item['sapply_did'] = $item['apply_sid'];
        }

        return $proLists;
    }

    /**
     * 获取产品的字段
     * <AUTHOR>
     * @date   2017-11-03
     * @return string
     */
    private function _getField()
    {

        //需要获取的字段
        $field = 'p.id,t.pid,p.apply_did,p.p_status,l.id as lid,l.p_type,l.jtype,l.areacode,l.terminal,
                l.title,l.salerid,l.area,l.topic,l.px,l.runtime,l.imgpath,l.letters as py,l.order_flag,
                l.address,t.id as tid,t.pay,t.delaytime,t.getaddr,t.title as ttitle,t.tprice,t.notes,t.shop,t.id as ticket_id,
                t.buy_limit_low,t.ddays,t.delaydays,t.batch_check,t.batch_day_check,t.order_limit,t.overdue_refund,t.status,uf.ass_station,uf.tourist_info,
                uf.mdays,uf.mhour,uf.rdays,uf.age_limit_min,uf.age_limit_max,t.order_end,t.refund_audit,t.order_start,t.delaytype,uf.dhour,uf.sendVoucher,uf.v_time_limit,t.cancel_cost';

        return $field;
    }

    /**
     * 兼容new/d项目下的letmebuy(废弃)
     * <AUTHOR>
     * @date   2018-01-26
     *
     * @param  int $fid 分销商id
     * @param  int $aid 上级供应商id
     * @param  int $lastAid 上级的上级供应商id
     * @param  int $sid 原始供应商id
     * @param  int $pid 门票pid
     *
     * @return array
     */
    public function letmebuy($fid, $aid, $lastAid, $sid, $pid)
    {
        return false;
    }

    /**
     * 获取打包套票业务处理
     *
     * @date   2018-05-21
     * <AUTHOR> Lan
     *
     * @param  int $memberId 会员Id
     * @param  string $productName 产品名称
     * @param  int $province 省
     * @param  int $city 市
     * @param  int $page 页码
     * @param  int $pageSize 条目
     * @param  bool $isTeam 是否是团单
     * @param  string $type 产品类型
     * @param $oversea
     * @param  int  $aid
     * @param  int  $source
     * @param  int  $landId
     * @param  string  $ticketTitle
     * @param  int  $ticketPage
     * @param  string  $ticketIds
     * @param  array  $showParams  演出票需要额外传参数
     * @param  int  $ticketSize  门票的每页条数
     *
     * @return array
     * @throws
     */
    public function getPacketProducts($memberId, $productName, $province, $city, $page, $pageSize, $isTeam, $type, $oversea, $aid = 0, $source = 0, $landId = 0, $ticketTitle = '', $ticketPage = 0, $ticketIds = '', $showParams = [], $ticketSize = 10)
    {
        $memberModel = new Member();

        //是否显示现场支付
        $saleScene = false;

        //对于云顶用户做特殊处理
        //group_id=4 云顶用户组
        //$memberId == 2148 九天测试专用账号
        $group = $memberModel->getMemberInfo($memberId, 'id', 'group_id');
        if ($group['group_id'] == 4 || $memberId == 2148) {
            $saleScene = true;
        }

        if ($isTeam) {
            $isTeam = true;
        }

        $params = [
            'member_id'       => $memberId,
            'type'            => $type,
            'province'        => $province,
            'city'            => $city,
            'land_name'       => $productName,
            'show_locale_pay' => $saleScene,
            'is_team'         => $isTeam,
            'page_size'       => $pageSize,
            'page_no'         => $page,
            'oversea'         => $oversea,
            'queryType'       => $source,
        ];

        if (!empty($landId)) {
            $params['landId'] = $landId;
        }

        if (!empty($ticketTitle)) {
            $params['ticketTitle'] = $ticketTitle;
        }

        if (!empty($ticketPage)) {
            $params['ticketPage'] = $ticketPage;
        }

        if (!empty($ticketSize)) {
            $params['ticketSize'] = $ticketSize;
        }

        if (!empty($ticketIds)) {
            $params['ticketIds'] = $ticketIds;
        }

        if ($aid > 0) {
            $params['supplierId'] = $aid;
        }
        foreach ($params as $key => $val) {
            if (!$val) {
                unset($params[$key]);
            }
        }

        $data = ProductApi::getPacketProducts($params);
        if ($data['code'] != 200) {
            return $data;
        }

        //列表参数过滤
        $data = $this->handlePackParams($data);

        //演出的处理
        $packBiz = new PackTicket();
        $result  = $packBiz->handlePacketShowParams($data, $showParams);

        return $result;
    }

    /**
     * 获取资源页面下可关联产品列表
     * @date   2019-04-08
     * <AUTHOR>
     *
     * @param  int $province 省
     * @param  int $city 市
     * @param  string $title 产品名称
     * @param  int $page 页码
     * @param  int $size 条目
     * @param  string $ptype 产品类型
     *
     * @return array
     */
    public function getResourceProductList($province, $city, $title, $page, $size, $ptype)
    {
        if (empty($title) || empty($ptype)) {
            return $this->returnData(203, '参数缺失');
        }
        $productApi = new ProductApi();
        $result     = $productApi->getResourceProductList($province, $city, $title, $page, $size, $ptype);
        $code       = 204;
        if ($result['code'] == 200) {
            $code = 200;
        }

        // 返回错误码 和 java 的错误提示
        return $this->returnData($code, $result['msg'], $result['data']);
    }

    // /**
    //  * 因新权限的数据同步，在发布新产品的时候， 会同步一份角色到新的权限中心,
    //  * 新发布产品会创建用户
    //  * <AUTHOR>
    //  * @date 2019-06-17
    //  *
    //  * @deprecated
    //  */
    // private function syncOpenPackageMenu($landId, $sid = 0)
    // {
    //     //$productApi  = new ProductApi();
    //     //$landInfoArr = $productApi->getLandInfo($landId);
    //
    //     //景区旧工程迁移
    //     $landApi     = new \Business\CommodityCenter\Land();
    //     $landInfoArr = $landApi->queryLandById(intval($landId));
    //
    //     $account = $landInfoArr['data']['salerid'];
    //
    //     $memberModel = new Member();
    //     $member      = $memberModel->getMemberInfo($account, 'account', 'id');
    //
    //     if (empty($account) || empty($member)) {
    //         pft_log('public_product/debug/', json_encode([$landId, $landInfoArr, $member]));
    //     }
    //
    //     if(empty($sid)) {
    //         $sid = $this->_necessaryAttr['account_id'];
    //     }
    //
    //     $authContext = new AuthContext();
    //     $authContext->syncOpenPackageMenu($sid, $member['id'], 2, time(), time() + 3600 * 24 * 365, 0, 1);
    // }

    /**
     * 列表参数过滤
     * <AUTHOR>
     * @date 2019-06-17
     *
     * @param $data array 产品列表
     *
     * @return array
     */
    public function handlePackParams($data)
    {
        $ticketBiz = new \Business\Product\Ticket();
        $handleTicketBiz = new OrderBook();
        $javaList        = $data['data']['list'];
        $tids = [];
        foreach ($javaList as $key => $value) {
            if (isset($value['tickets'])){
                $tids = array_unique(array_merge($tids, array_column($value['tickets'], 'ticketId')));
            }
        }
        $buyLimit = [];
        $buyLimitRes = (new Index())->queryLandLimitConfigByTicketIds($tids);
        if ($buyLimitRes['code'] == 200 && $buyLimitRes['data']) {
            //过滤掉产品限制
            $buyLimitData = [];
            foreach ($buyLimitRes['data'] as $limit){
                if ($limit['limitConfigType'] == 1) {
                    $buyLimitData[] = $limit;
                }
            }
            $buyLimit = array_column($buyLimitData, null, 'ticketId');
        }

        //旅游券票id集合
        $tvTidArr         = [];
        $tvTagArr         = [];
        //获取一次期票配置
        $ticketPreSaleMap = [];
        $ticketRes        = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tids);
        if ($ticketRes) {
            $ticketInfoArr = [];
            foreach ($ticketRes as $item) {
                $ticketInfoArr[$item['ticket']['id']] = [
                    'id'       => $item['ticket']['id'],
                    'pre_sale' => $item['ticket']['pre_sale'],
                    'p_type'   => $item['land']['p_type'],
                ];
                if ($item['land']['p_type'] == 'Q') {
                    $tvTidArr[] = $item['ticket']['id'];
                }
            }
        }

        //旅游券标签获取下
        if ($tvTidArr) {
            $tvTagRes = (new \Business\JsonRpcApi\TravelVoucherService\ProductTag())->getTicketTag($tvTidArr);
            if ($tvTagRes['code'] == 200 && !empty($tvTagRes['data'])) {
                $tvTagArr = $tvTagRes['data'];
            }
        }

        $ticketExtendAttr    = new TicketExtendAttr();
        $ticketSpecialValRes = $ticketExtendAttr->queryTicketSpecialVal($tids,
            ['is_start_stop_sale_limit', 'auto_sale_rule', 'refund_before_early_time', 'refund_after_early_time', 'is_online_reserve', 'refund_after_time']);
        $ticketSpecialValRes = $ticketSpecialValRes['code'] == 200 ? $ticketSpecialValRes['data'] : [];
        $refundBeforeEarlyTime = $refundAfterEarlyTime = $refundAfterTime = [];
        foreach ($ticketSpecialValRes as $ticketConf) {
            if ($ticketConf['key'] == 'refund_before_early_time') {
                $refundBeforeEarlyTime[] = $ticketConf;
            }
            if ($ticketConf['key'] == 'refund_after_early_time') {
                $refundAfterEarlyTime[] = $ticketConf;
            }
            if ($ticketConf['key'] == 'refund_after_time') {
                $refundAfterTime[] = $ticketConf;
            }
        }
        $refundBeforeEarlyTime = array_key($refundBeforeEarlyTime, 'ticketId');
        $refundAfterEarlyTime  = array_key($refundAfterEarlyTime, 'ticketId');
        $refundAfterTime       = array_key($refundAfterTime, 'ticketId');
        foreach ($javaList as $key => $value) {
            foreach ($value['tickets'] as $k => $v) {
                $tmpTicketInfoArr = $ticketInfoArr[$v['ticketId']] ?? null;

                $mainTicket                                = $this->_changeTicketValue($v);
                $mainTicket['type']                        = $value['landType'];
                $mainTicket['pre_sale']                    = $tmpTicketInfoArr['pre_sale'] ?? null;
                $javaList[$key]['tickets'][$k]['validTag'] = $handleTicketBiz->getValidityDate($mainTicket);

                if ($mainTicket['type'] == 'Q') {
                    $javaList[$key]['tickets'][$k]['tag_list'] = $tvTagArr[$v['ticketId']];
                }

                if ($v['ddays'] == 0) {
                    $javaList[$key]['tickets'][$k]['bookingTag'] = '当天可预订';
                } else {
                    $javaList[$key]['tickets'][$k]['bookingTag'] = '需提前' . $v['ddays'] . '天预订';
                }

                if (in_array($value['landType'], ['A', 'B', 'C', 'F', 'G'])) {
                    $ticketInfoData = [
                        'refund_rule'               => $v['refundRule'],
                        'refund_before_early_time'  => $refundBeforeEarlyTime[$v['ticketId']]['val'] ?? 0,
                        'refund_after_early_time'   => $refundAfterEarlyTime[$v['ticketId']]['val'] ?? 0,
                    ];
                    $javaList[$key]['tickets'][$k]['refundTag'] = $ticketBiz->refundRuleTag($ticketInfoData);
                } else {
                    $javaList[$key]['tickets'][$k]['refundTag'] = $handleTicketBiz->handleRefundRule($v['refundAudit'],
                        $v['refundRule'], $v['refundEarlyTime'], $refundAfterTime[$v['ticketId']]['val'] ?? 0);
                }

                $javaList[$key]['tickets'][$k]['buyLimitInfo'] = $buyLimit[$v['ticketId']] ?? [];
                $javaList[$key]['tickets'][$k]['preSale']      = $ticketPreSaleMap[$v['ticketId']]['pre_sale'] ?? 0;
            }
        }
        $data['data']['list'] = $javaList;

        //添加酒店房型字段
        $hotelLib             = new Hotel();
        $data['data']['list'] = $hotelLib->handleRoomParamsSon($data['data']['list']);

        return $data;
    }

    /**
     * 参数转换
     * <AUTHOR>
     * @date 2019-08-17
     *
     * @param $data array 产品列表
     *
     * @return array
     */
    private function _changeTicketValue($data)
    {
        $data = [
            'valid_period_days'         => $data['validDays'],
            'valid_period_start'        => $data['validStartDateStr'] ?: '',
            'valid_period_end'          => $data['validEndDateStr'] ?: '',
            'valid_period_type'         => $data['validType'],
            'use_early_days'            => $data['useEarlyDays'],
            'valid_period_timecancheck' => $data['ifVerify'],
            'verify_disable_week'       => $data['orderLimit'],
            'order_limit'               => $data['orderLimit'],
            'verify_time'               => 0,
        ];

        return $data;
    }

    /**
     * 获取景区的城市和省份
     *
     * <AUTHOR>  迁移翁彬的
     * @date   2020-01-21
     *
     * @param  array $pidArr pid 数组
     *
     * @return array
     */
    public function parseCityProvinceByPid($pidArr)
    {
        $commodityProductBiz = new \Business\CommodityCenter\Product();
        $productInfo         = $commodityProductBiz->getProductInfoAndLandInfoByArrPid($pidArr, 'id', 'area');
        $list                = [];
        foreach ($productInfo as $key => $value) {
            $list[$value['id']] = $value['area'];
        }
        $list    = $list ?: [];
        $areaMap = (new AreaModel)->getAreaList();
        foreach ($list as $pid => $code) {
            if ($code) {
                $tmp          = explode('|', $code);
                $provinceCode = $tmp[0] ?: 1;
                $cityCode     = $tmp[1] ?: 1;
            }

            $list[$pid] = [
                'city'     => $areaMap[$cityCode],
                'province' => $areaMap[$provinceCode],
            ];
        }

        return $list;
    }

    /**
     * 产品类型及描述
     * <AUTHOR>
     * @date 2020-01-02
     *
     * @param   $productType    array   产品类型， [A, B, C, F, G, H]
     *
     * @return array [['type' => 'A', 'name' => '景区'], ['type' => 'B', 'name' => '路线'], ...]
     *
     */
    public function getProductType($productType = [])
    {
        $allType  = load_config('land_ptype', 'account');
        $typeDesc = [];

        if ($productType) {
            //返回查询的产品类型
            foreach ($allType as $type => $desc) {
                if (!in_array($type, $productType)){
                    continue;
                }

                $typeDesc[] = ['type' => $type, 'name' => $desc,];
            }

        } else {
            //返回全部的产品类型
            foreach ($allType as $type => $desc) {
                $typeDesc[] = ['type' => $type, 'name' => $desc,];
            }
        }

        return $typeDesc;
    }

    /**
     * 模糊搜索可售产品列表
     * @Author: zhujb
     * 2020/3/3
     *
     * @param  int $sid 供应商id
     * @param  int $fid 分销商id
     * @param  int $lid 景区id
     * @param  int $pid 产品id
     * @param  string $keyword 产品名称
     * @param  array $pType 产品类型
     * @param  int $payType 支付方式 1：在线支付，2：一卡通支付，3：现金支付
     * @param  array $inPidArr 产品id数组
     * @param  int $neqPid 需要排除的产品id
     * @param  int $page 当前页数
     * @param  int $pageSize 每页条数
     *
     * @return array
     */
    public function getSaleProductByCondition($sid = 0, $fid = 0, $lid = 0, $pid = 0, $keyword = '', $pType = [], $payType = 0, $inPidArr = [], $neqPid = 0, $page = 1, $pageSize = 10, $active = -1)
    {
        if ($pid || $inPidArr) {
            array_push($inPidArr, $pid);
        }

        $neqPidArr = empty($neqPid) ? [] : [$neqPid];
        $pTypeArr  = empty($pType) ? [] : $pType;

        $evoluteArrInfo = $this->querySaleProductByCondition($sid, $fid, $lid, $keyword, $inPidArr, $pTypeArr, $payType,
            $neqPidArr, $page, $pageSize, $active);

        if ($evoluteArrInfo['code'] != 200 || empty($evoluteArrInfo['data']['list'])) {
            return [];
        }

        // 获取所有的分销链信息
        $evoluteArr = $evoluteArrInfo['data'];

        return $evoluteArr;
    }

    public function getUserCanSaleProductService($sid, $lid)
    {
        if (!$sid || !$lid) {
            return $this->returnData(204, '参数错误', []);
        }
        $productList = $this->salePros($sid, $lid);
        if ($productList['code'] != 200 || empty($productList['data'])) {
            return $this->returnData(200, '', []);
        }
        $productData = $productList['data'];
        $landInfo    = array_values($productData)[0];
        $ticketIds   = array_column($productData, 'tid');
        //获取有价格的产品
        $ticketIds                 = implode(',', $ticketIds);
        $resLandData               = [];
        $resLandData['p_type']     = $landInfo['p_type'];
        $resLandData['ltitle']     = $landInfo['title'];
        $resLandData['sapply_did'] = $landInfo['sapply_did'];
        $resLandData['imgpath']    = '//www.12301.cc/' . $landInfo['imgpath'];
        $resLandData['tickets']    = [];

        $priceData = TicketApi::getSinglePrices($ticketIds);
        if (!$priceData) {
            return $this->returnData(200, '', []);
        }
        $productPriceBiz = new Price();

        $date = date('Y-m-d');
        foreach ($productData as $key => $value) {
            $ticket = [];
            //获取票的零售价格
            $retailPriceData = $productPriceBiz->DynamicPriceAndStorage('', $value['pid'], $date, 1, 1);
            if ($retailPriceData['code'] != 200) {
                continue;
            }
            $ticket['uprice'] = $retailPriceData['data'][0]['sprice'] / 100;
            if (!isset($priceData[$value['tid']])) {
                continue;
            }
            $ticket['tprice']         = $priceData[$value['tid']]['counter_price'] / 100;
            $ticket['wprice']         = $priceData[$value['tid']]['window_price'] / 100;
            $ticket['title']          = $value['ttitle'];
            $resLandData['tickets'][] = $ticket;
        }

        return $this->returnData(200, '', $resLandData);
    }

    /**
     * 新增/更新景区 请求参数转换
     * <AUTHOR>
     * @date 2020/2/7 0007
     *
     * @param $data
     *
     * @return array
     */
    public function changeParamsToJava($data)
    {
        $notLike = [
            'account_id'         => 'applyDid',
            'city_id'            => 'cityId',
            'province_id'        => 'provinceId',
            'zone_id'            => 'zoneId',
            'notice'             => 'jqts',
            'details'            => 'bhjq',
            'img_path_group'     => 'imgpathgrp',
            'img_path'           => 'imgpath',
            'introduce'          => 'jdjj',
            'lng_lat_pos'        => 'lngLatPos',
            'zone_id'            => 'zoneId',
            'lng_lat_pos'        => 'lngLatPos',
            'name'               => 'title',
            'venus_id'           => 'venusId',
            'start_place'        => 'startPlace',
            'end_place'          => 'endPlace',
            'resource_id'        => 'resourceId',
            'create_resource'    => 'createResource',
            'group_limit'        => 'groupLimit',
            'group_number_limit' => 'groupNumberLimit',
            'group_ticket_limit' => 'groupTicketLimit',
            'operater_id'        => 'operaterId',
            'custom_made'        => 'customMade',
            'order_flag'         => 'orderFlag',
            'type'               => 'pType',
            'level'              => 'jtype',
            'traffic'            => 'jtzn',
            'topics'             => 'topic',
            'verify_status'      => 'verifyStatu',
            'ship_route_code'    => 'qt',
            'custom_made'        => 'customMade',
            'opentime'           => 'runtime',
            'sms_phone'          => 'fax',
            'add_time'           => 'addtime',
            'update_time'        => 'uptime',
            'sub_type'           => 'subType',
        ];

        foreach ($data as $key => $value) {
            if (isset($notLike[$key])) {
                $data[$notLike[$key]] = $value;
                unset($data[$key]);
            }
        }

        return $data;
    }

    /**
     *  获取微商城可售产品门票列表
     * <AUTHOR>
     * @date 2020/6/18
     *
     * @param  int $sid 供应商id
     * @param  string $keyword 搜索关键字
     * @param  int $pageSize 分页的单页数
     * @param  bool $seaType 类型
     *
     * @return array
     */
    public function getProductListForMallActivity(int $sid, string $keyword, int $pageSize, bool $seaType)
    {

        $code = 200;
        $data = [];
        $msg  = "获取优惠券可使用的产品列表成功";

        try {

            if (!$sid || !$pageSize) {
                throw new Exception("参数缺失", 203);
            }

            //获取产品列表
            $listingApi = new Listing();
            $listRes    = $listingApi->selectListing($sid, $keyword, [], $pageSize, $seaType, 1);
            if (empty($listRes) || $listRes['code'] != 200) {
                throw new Exception("无数据", 200);
            }

            $listRes   = $listRes['data'];
            $landIdArr = array_column($listRes, 'id');

            //$landModel   = new \Model\Product\Land();
            //$landInfoArr = $landModel->getLandInfoByMuli($landIdArr, 'id,title,p_type', true);

            $javaAPi     = new \Business\CommodityCenter\Land();
            $landInfo    = $javaAPi->queryLandMultiQueryById($landIdArr);
            $landInfoArr = array_column($landInfo, null, 'id');

            foreach ($listRes as $key => $value) {
                $data[$key]          = $value;
                $data[$key]['ptype'] = $landInfoArr[$value['id']]['p_type'];
            }

        } catch (\Exception $exception) {

            $code = $exception->getCode();
            $msg  = $exception->getMessage();
        }

        return $this->returnDataV2($code, $data, $msg);
    }

    /**
     * 获取自供应景区列表
     * @Author: PeiJun Li
     * @date: 2020-03-31
     *
     * @param  int $sid 供应商id
     * @param  string $keyword 景区名称
     * @param  int $page 当前页数
     * @param  int $pageSize 每页条数
     *
     * @return array
     */
    public function getSaleLandList($sid, $keyword = '', $landIdArr = null, $landStatus = 1, $pType = '', $page = 1, $pageSize = 10)
    {
        $res = [
            'list'  => [],
            'total' => 0,
        ];

        if (!$sid) {
            return $res;
        }

        $landApi  = new \Business\JavaApi\CommodityCenter\Land();
        $javaData = $landApi->getLandInfoList($sid, $landIdArr, $landStatus, $pType, $keyword, $page, $pageSize);

        if ($javaData['code'] != 200 || !$javaData['data']) {
            return $res;
        }

        $res = [
            'list'  => $javaData['data']['list'] ? $javaData['data']['list'] : [],
            'total' => $javaData['data']['total'] ? $javaData['data']['total'] : 0,
        ];

        return $res;
    }

    /**
     * 根据状态获取自供应景区列表
     * <AUTHOR>
     * @date   2024/07/23
     *
     * @param  int     $sid         供应商id
     * @param  string  $keyword     景区名称
     * @param  int     $page        当前页数
     * @param  int     $pageSize    每页条数
     * @param  array   $extCondition    ['lidList' => '允许产品列表', 'notLidList' => '排除的产品列表']
     *
     * @return array
     */
    public function getSaleLandListByStatusList($sid, $keyword = '', $landIdArr = null, $landStatus = null, $landStatusArr = [], $pType = '', $page = 1, $pageSize = 10, $extCondition = [])
    {
        $res = [
            'list'  => [],
            'total' => 0,
        ];

        if (!$sid) {
            return $res;
        }

        $landApi  = new \Business\JavaApi\CommodityCenter\Land();
        $javaData = $landApi->getLandInfoList($sid, $landIdArr, $landStatus, $pType, $keyword, $page, $pageSize, null, null,
            $landStatusArr, 0, [], 0, $extCondition);

        if ($javaData['code'] != 200 || !$javaData['data']) {
            return $res;
        }

        return [
            'list'  => $javaData['data']['list'] ?? [],
            'total' => $javaData['data']['total'] ?? 0,
        ];
    }

    /***
     * 判断是否为该产品的顶级供应商或一级分销商
     * <AUTHOR> Yiqiang
     * @date   2018-09-06
     *
     * @param  int $pid 产品id
     * @param  int $memberId 会员id
     * @param  int $aid 上级id
     *
     * @return bool
     */
    public function isSupplyOrLvlOne($pid, $memberId, $aid)
    {
        if (!((int)$memberId && (int)$aid && (int)$pid)) {
            return false;
        }

        $evoFilter = [
            'fid'    => $memberId,
            'sid'    => $aid,
            'pid'    => $pid,
            'lvl'    => ['in', [0, 1]],
            'status' => 0,
        ];

        $proFilter = [
            'id'          => $pid,
            'apply_limit' => 1,
            'p_status'    => ['in', [0, 1]],
        ];

        $landFilter = [
            'p_type' => ['neq', 'J'],
            'status' => 1,
        ];

        $re = $this->_getOneSalePro($evoFilter, $proFilter, $landFilter);

        return $re ? true : false;
    }

    /***
     * 获取一个同salerid产品的分销相关信息
     * <AUTHOR> Yiqiang
     * @date   2018-09-06
     *
     * @param  array $evoFilter 分销链查询限制
     *
     * @return array
     */
    private function _getOneSalePro($evoFilter)
    {
        $evoluteJavaBus = new EvoluteApi();
        //分销查询切换java参数兼容
        $evoField = 'pid,sid,dprice,aids,lvl';

        $evoParamSid = $evoFilter['sid'] ?? [];

        if (is_array($evoFilter['pid'])) {
            $evoParamPid = $evoFilter['pid'][1];
        } else {
            $evoParamPid = $evoFilter['pid'];
        }

        $evoParamActive = $evoFilter['active'] ?? -1;

        if (is_array($evoFilter['lvl'])) {
            if ($evoFilter['lvl'][0] == 'in') {
                $evoParamLvl   = 1;
                $evoParamLvleq = 5;
            }
            if ($evoFilter['lvl'][0] == 'egt') {
                $evoParamLvl   = $evoFilter['lvl'][1];
                $evoParamLvleq = 3;
            }
        } else {
            $evoParamLvl   = $evoFilter['lvl'];
            $evoParamLvleq = 1;
        }

        //分销查询请求java接口
        $evoRe = $evoluteJavaBus->getEvoluteList(
            $evoFilter['fid'],
            $evoParamSid,
            $evoParamPid,
            [],
            0,
            $evoFilter['status'],
            $evoParamActive,
            $evoParamLvl,
            -1,
            $evoField,
            $evoParamLvleq
        );

        $condition = [
            'pStatuss'    => [0, 1],
            'applyLimit'  => 1,
            'landFlag'    => 1,
            'productFlag' => 1,
            'pTypes'      => ['A', 'B', 'C', 'I', 'F', 'H', 'G', 'K', 'L'],
            'landStatus'  => 1,
        ];
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('',
            'id,p_name,apply_did,contact_id', 'title,id', '', '',
            null, null, null, [$evoParamPid], $condition);

        if (empty($ticketArr)) {
            return [];
        }
        $tickets = [];
        if (!empty($ticketArr)) {
            foreach ($ticketArr as $ticketInfo) {
                $tickets[$ticketInfo['product']['id']]['id']         = $ticketInfo['product']['id'];
                $tickets[$ticketInfo['product']['id']]['p_name']     = $ticketInfo['product']['p_name'];
                $tickets[$ticketInfo['product']['id']]['apply_did']  = $ticketInfo['product']['apply_did'];
                $tickets[$ticketInfo['product']['id']]['contact_id'] = $ticketInfo['product']['contact_id'];
                $tickets[$ticketInfo['product']['id']]['title']      = $ticketInfo['land']['title'];
                $tickets[$ticketInfo['product']['id']]['lid']        = $ticketInfo['land']['id'];
            }
        }

        $return = array();
        if ($evoRe) {
            foreach ($evoRe as $evo) {
                if (isset($tickets[$evo['pid']])) {
                    //过滤删除产品
                    $return[] = array_merge($evo, $tickets[$evo['pid']]);
                }
            }
        }

        return $return;
    }

    ///**
    // * 模糊搜索可售产品列表
    // * @Author: PeiJun Li
    // * 2020/05/24
    // *
    // * @param  int  $sid  供应商idd
    // * @param  int  $fid  分销商id
    // * @param  int  $lid  景区id
    // * @param  array  $pidArr  产品id
    // * @param  string  $pName  产品名称
    // * @param  string  $pType  产品类型：A=景点，B=线路，C=酒店，F=套票，G=餐饮，H=演出，I=年卡套餐
    // * @param  int  $payType  支付方式 1：在线支付，2：一卡通支付，3：现金支付
    // * @param  array  $excludePidArr  需要排除的产品ID
    // * @param  int  $pageNum  当前页数
    // * @param  int  $pageSize  每页条数
    // *
    // * @return array
    // */
    //public function querySaleProductByCondition($sid = null, $fid = null, $lid = null, $pidArr = null, $pName = null, $pType = null, $payType = null, $excludePidArr = null, $pageNum = 1, $pageSize = 10)
    //{
    //    if ($sid && !$fid) {
    //        // 获取自供应 + 分销产品
    //        $fid = $sid;
    //    }
    //
    //    $javaApi = new \Business\JavaApi\Product\EvoluteProduct();
    //    $result  = $javaApi->querySaleProductByCondition((int)$sid, (int)$fid, (int)$lid, $pName, $pidArr, $pType, (int)$payType, $excludePidArr, $pageNum, $pageSize);
    //
    //    $data = [
    //        'list' => [],
    //        'pageNum' => $pageNum,
    //        'pageSize' => $pageSize,
    //        'pages' => 1,
    //        'total' => 0,
    //    ];
    //    if ($result['code'] != 200 || empty($result['data']['list'])) {
    //        return $this->returnData($result['code'], $result['msg'], $data);
    //    }
    //
    //    foreach ($result['data']['list'] as &$product) {
    //        foreach ($product as $key => $value) {
    //            $product[uncamelize($key)] = $value;
    //        }
    //    }
    //
    //    return $this->returnData($result['code'], $result['msg'], $result['data']);
    //}

    /**
     * pc平台预定页添加 是否设置了不同价格 票说明，提前预订，延迟验证
     * @Author: xwh
     * @date: 2020-07-15
     *
     * @param  array $param
     *
     * @return array
     */
    public function handleReturnConfigMuchPriceList(array $param)
    {
        if (empty($param)) {
            return $param;
        }
        $ticketApi  = new TicketPrice();
        $ticketTids = [];
        foreach ($param as $key => $item) {
            if (!empty($item['ticket'])) {
                foreach ($item['ticket'] as $k => $v) {
                    $ticketTids[] = $v['tid'];
                }
            }
        }

        // 根据ticketIds 查询返回当前时间有效期,有配置多个价格的票列表
        $calendar = $ticketApi->queryReturnConfigMuchPriceTicketId($ticketTids);
        if ($calendar['code'] != 200) {
            return $param;
        }
        $javaApi = new \Business\CommodityCenter\Ticket();
        //根据ticketIds 获取票据信息

        $ticketArr = $javaApi->queryTicketInfoByIds($ticketTids, 'id,delaytime,ddays,buy_limit_low,buy_limit_up',
            'salerid', 'title,p_type',
            'dhour,tid,buy_limit,buy_limit_date,buy_limit_num,tourist_info,age_limit_min,age_limit_max');
        if (!$ticketArr) {
            return $param;
        }

        $ticketBuz      = new \Business\Product\Ticket();
        $tagArr         = $ticketBuz->getTicketTagByIds($ticketTids, 5);

        $ticketData = [];
        foreach ($ticketArr as $ticketInfos) {
            $data                                     = [
                'is_online_reserve'         => $ticketInfos['ext']['is_online_reserve'],
                'reserve_after_verify_time' => $ticketInfos['ext']['reserve_after_verify_time'],
                'delaytime'                 => $ticketInfos['ticket']['delaytime'],
            ];
            $delaytime                                = $ticketBuz->verifyTheDelayTag($data);
            $ticketData[$ticketInfos['ticket']['id']] = [
                'delaytime' => $delaytime,
                'ddays'     => $ticketInfos['ticket']['ddays'],
                'dhour'     => $ticketInfos['land_f']['dhour'],
                'p_type'    => $ticketInfos['land']['p_type'],
                'tag_list'  => $tagArr[$ticketInfos['land']['p_type']][$ticketInfos['ticket']['id']] ?: [],
            ];
        }

        foreach ($param as $key => &$item) {
            if (!empty($item['ticket'])) {
                foreach ($item['ticket'] as $k => &$v) {
                    //是否设置了不同价格
                    $dayPrice = 0;
                    if ($calendar['data']) {
                        $dayPrice = in_array($v['tid'], $calendar['data']) ? 1 : 0;
                    }
                    $v['day_price']            = $dayPrice;
                    $v['delaytime']            = $ticketData[$v['tid']]['delaytime'];
                    $v['preorder_early_days']  = $ticketData[$v['tid']]['ddays'];
                    $v['preorder_expire_time'] = $ticketData[$v['tid']]['dhour'];
                    $v['day_price']            = $dayPrice;
                    $v['tag_list']             = $ticketData[$v['tid']]['tag_list'] ?? [];
                }
            }
        }

        return $param;
    }

    /**
     * pc平台预定页更多 添加 是否设置了不同价格 票说明，提前预订，延迟验证
     * @Author: xwh
     * @date: 2020-07-15
     *
     * @param  array $param
     *
     * @return array
     */
    public function handleReturnConfigMuchPriceListMore(array $param)
    {
        if (empty($param)) {
            return $param;
        }
        $ticketApi = new TicketPrice();
        $tidArr    = array_column($param, 'tid');
        // 根据ticketIds 查询返回当前时间有效期,有配置多个价格的票列表
        $calendar = $ticketApi->queryReturnConfigMuchPriceTicketId($tidArr);
        if ($calendar['code'] != 200) {
            return $param;
        }

        $handleTicketBiz = new \Business\Product\Ticket();
        $ticketAllTag    = $handleTicketBiz->getTicketTagByIds($tidArr);

        $javaApi = new \Business\CommodityCenter\Ticket();
        //根据ticketIds 获取票据信息
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'id,delaytime,ddays', 'salerid', 'title,p_type',
            'dhour,tid');
        if (!$ticketArr) {
            return $param;
        }
        $ticketData = [];
        $ticketBuz  = new \Business\Product\Ticket();
        foreach ($ticketArr as $ticketInfos) {
            $data = [
                'is_online_reserve'         => $ticketInfos['ext']['is_online_reserve'],
                'reserve_after_verify_time' => $ticketInfos['ext']['reserve_after_verify_time'],
                'delaytime'                 => $ticketInfos['ticket']['delaytime'],
            ];
            //延迟验证的标签
            $delaytime                                = $ticketBuz->verifyTheDelayTag($data);
            $ticketData[$ticketInfos['ticket']['id']] = [
                'delaytime' => $delaytime,
                'ddays'     => $ticketInfos['ticket']['ddays'],
                'dhour'     => $ticketInfos['land_f']['dhour'],
                'p_type'    => $ticketInfos['land']['p_type'],
            ];
        }
        foreach ($param as $key => &$item) {
            //是否设置了不同价格
            $dayPrice = 0;
            if ($calendar['data']) {
                $dayPrice = in_array($item['tid'], $calendar['data']) ? 1 : 0;
            }
            $item['delaytime']            = isset($ticketData[$item['tid']]['delaytime']) ? $ticketData[$item['tid']]['delaytime'] : '';
            $item['preorder_early_days']  = isset($ticketData[$item['tid']]['ddays']) ? $ticketData[$item['tid']]['ddays'] : '';
            $item['preorder_expire_time'] = isset($ticketData[$item['tid']]['dhour']) ? $ticketData[$item['tid']]['dhour'] : '';
            $item['day_price']            = $dayPrice;
            $item['p_type']               = $ticketData[$item['tid']]['p_type'];
            if ($item['p_type'] == 'J') {
                $specialInfo = (new SpecialtyV2())->getGoodsDeliveryInfo($item['tid']);

                $item['special_info'] = $specialInfo;
            }

            $item['tag_list'] = $ticketAllTag[$item['p_type']][$item['tid']] ?? [];
        }

        return $param;
    }

    /**
     * 景区信息兼容处理
     * <AUTHOR>
     * @date 2020/6/16
     *
     * @param $data 景区信息
     *
     * @return array
     */
    public function handleLandInfo($data)
    {
        $ptype = $data['ptype'];//产品类似

        //获取景区类型配置
        $landPtype = load_config('land_ptype', 'account');

        $landInfo = [
            'lid'        => $data['lid'],
            'title'      => $data['landName'],
            'prodType'   => $landPtype[$data['ptype']],
            'ptype'      => $data['ptype'],
            'traffic'    => nl2br($data['jtzn']),
            'booking'    => nl2br($data['jqts']),
            'intro'      => htmlspecialchars_decode($data['bhjq']),
            'supplyType' => $data['supplyType'],
            'supplyId'   => $data['supplierId'],
            'supplyName' => $data['supplierName'],
            'province'   => $data['provinceName'],
            'city'       => $data['cityName'],
        ];

        //处理轮播图
        if ($data['imgpathGrp']) {
            $tmpImgpathGrp          = @json_decode($data['imgpathGrp'], true);
            $imgpathGrp             = is_array($tmpImgpathGrp) ? $tmpImgpathGrp : unserialize($data['imgpathGrp']);
            $landInfo['imgpathGrp'] = $imgpathGrp;
        } else {
            $landInfo['imgpathGrp'] = [];
        }

        //处理退款规则
        $tickets = $data['ticketList'];

        //获取一次门票扩展属性
        $tidArr    = array_unique(array_column($tickets, 'ticketId'));
        $ticketArr = (new \Business\CommodityCenter\Ticket())->queryTicketInfoByIds($tidArr);
        $ticketExt = [];
        foreach ($ticketArr as $ticket) {
            $ticketExt[$ticket['ticket']['id']] = $ticket['ext'];
        }

        if (!empty($tickets)) {
            foreach ($tickets as $key => $val) {
                //处理退款规则描述
                $message                     = OrderBook::handleRefundRule($val['refundAudit'], $val['refundRule'],
                    $val['refundEarlyTime'], $ticketExt[$val['ticketId']]['refund_after_time'] ?? 0);
                $tickets[$key]['refundRule'] = $message;
                $tickets[$key]['pre_sale']   = $val['preSale'];

                if ($ptype == 'F') {
                    $packBiz   = new PackTicket();
                    $childData = $packBiz->getChildTickets($val['ticketId']);

                    if (!empty($childData)) {
                        $tickets[$key]['childTickets'] = $childData;
                        $sonPidArr                     = array_column($childData, 'pid');
                        $tickets[$key]['usetime']      = OrderBook::parsePackValidTag($sonPidArr);
                    }
                } else {
                    $validData                = [
                        'type'                      => $ptype,
                        'valid_period_start'        => $val['validStartDateStr'],
                        'valid_period_end'          => $val['validEndDateStr'],
                        'valid_period_type'         => $val['validType'],
                        'use_early_days'            => $val['useEarlyDays'],
                        'valid_period_days'         => $val['validDays'],
                        'valid_period_timecancheck' => $val['ifVerify'],
                        'delaytype'                 => $val['delayType'],
                    ];
                    $tickets[$key]['usetime'] = OrderBook::getValidityDate($validData);
                }
            }

        }

        $landInfo['tickets'] = $tickets;

        return $landInfo;
    }

    /**
     * 获取产品销售列表
     * <AUTHOR> Li
     * @data  2020-08-25
     *
     * @param  int $sid 供应商id
     * @param  int $fid 分销商id
     * @param  int $lid 景区id
     * @param  string $pName 产品名称
     * @param  int $pageNum 当前页数
     * @param  int $pageSize 每页条数
     * @param  int $active 是否过滤转分销
     *
     * @PS: 该接口用于获取可售的自供应和转分销产品列表(带分页信息)
     * 1.获取可售的自供应产品: 例fid = 3385， sid = 3385
     * 2.获取可售特定供应商的分销产品： 例fid = 6970 ， sid = 3385
     * 3.获取可售自供应+转分销产品： 只传fid = 3385
     * 4.如果查询条件只有sid,会被认为是非法请求（sid不能走索引会导致慢sql出现）
     * 5.满足可出售条件（p_status = 0,land.status = 1, 分销 status =0 ,active =1）
     *
     * @return array
     */
    public function querySaleProductList($sid = 0, $fid = 0, $lid = 0, $pName = '', $pageNum = 1, $pageSize = 10, $active = -1)
    {
        if (!$sid && !$fid) {
            return self::returnData(204, '缺少必要参数');
        }

        $javaApi = new EvoluteQuery();
        $result  = $javaApi->querySaleProductList($sid, $fid, $lid, $pName, $pageNum, $pageSize, $active);

        if (!isset($result['code']) || $result['code'] != 200) {
            return self::returnData(204, '接口数据获取失败');
        }

        return self::returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 查询可售产品列表带查询条件
     * <AUTHOR> Li
     * @data  2020-08-25
     *
     * @param  int $sid 供应商id
     * @param  int $fid 分销商id
     * @param  int $lid 景区id
     * @param  string $pName 产品名称
     * @param  array $pidArr 产品id数组
     * @param  array $pTypeArr 产品类型：A=景点，B=线路，C=酒店，F=套票，G=餐饮，H=演出，I=年卡套餐
     * @param  int $payType 支付方式 1：在线支付，2：一卡通支付，3：现金支付
     * @param  array $excludePidArr 需要排除的产品ID
     * @param  int $pageNum 当前页数
     * @param  int $pageSize 每页条数
     *
     * @PS: 该接口用于获取可售的自供应和转分销产品列表(带分页信息)
     * 1.获取可售的自供应产品: 例fid = 3385， sid = 3385
     * 2.获取可售特定供应商的分销产品： 例fid = 6970 ， sid = 3385
     * 3.获取可售自供应+转分销产品： 只传fid = 3385
     * 4.如果查询条件只有sid,会被认为是非法请求（sid不能走索引会导致慢sql出现）
     * 5.满足可出售条件（p_status = 0,land.status = 1, 分销 status =0 ,active =1）
     *
     * @return array
     */
    public function querySaleProductByCondition($sid = 0, $fid = 0, $lid = 0, $pName = '', $pidArr = [], $pTypeArr = [], $payType = '', $excludePidArr = [], $pageNum = 1, $pageSize = 10, $active = -1)
    {
        if (!$sid && !$fid) {
            return self::returnData(204, '缺少必要参数');
        }

        if ($sid && !$fid) {
            $fid = $sid;
        }

        $javaApi = new EvoluteQuery();
        $result  = $javaApi->querySaleProductByCondition($sid, $fid, $lid, $pName, $pidArr, $pTypeArr, $payType,
            $excludePidArr, $pageNum, $pageSize, $active);

        if (!isset($result['code']) || $result['code'] != 200) {
            return self::returnData(204, $result['msg']);
        }

        return self::returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 获取产品详情
     * <AUTHOR>
     * @date 2021/1/21
     *
     * @param  int  $landId  景区ID
     *
     * @return array
     */
    public function queryProductInfo(int $landId)
    {
        if (!$landId) {
            return self::returnData(203, '参数缺失');
        }

        $productBiz  = new Product();
        $landInfoArr = $productBiz->getLandInfo($landId);
        if ($landInfoArr['code'] != 200 || !$landInfoArr['data']) {
            return self::returnData(203, '产品不存在');
        }

        $jdata = $landInfoArr['data'];

        // 数组键名转换
        $jdata['title']      = $jdata['name'];
        $jdata['jtype']      = $jdata['level'];
        $jdata['p_type']     = $jdata['type'];
        $jdata['resourceID'] = $jdata['resource_id'];
        $jdata['apply_did']  = $jdata['account_id'];
        $jdata['imgpath']    = $jdata['img_path'];
        $jdata['imgpathGrp'] = $jdata['img_path_group'];
        $jdata['bhjq']       = $jdata['details'];
        $jdata['jqts']       = $jdata['notice'];
        $jdata['jtzn']       = $jdata['traffic'];
        $jdata['topic']      = $jdata['topics'];
        $jdata['lngLatPos']  = $jdata['lng_lat_pos'];
        $jdata['ext']        = $jdata['ext'] ?? (object)[];
        $jdata['videoUrl']   = $jdata['videoUrl'];

        if ($jdata['order_flag'] == 1) {
            // 返回团队订单预定的规则属性
            if (isset($jdata['group_limit']) && !empty($jdata['group_limit'])) {
                $groupLimitArr = json_decode($jdata['group_limit'], true);
            }
            $groupNumLimit['is_limit']     = 0;
            $groupNumLimit['limit_number'] = 0;
            if (isset($jdata['group_number_limit']) && $jdata['group_number_limit'] == 1) {
                $groupNumLimit['is_limit']     = 1;
                $groupNumLimit['limit_number'] = isset($groupLimitArr['group_number_limit']['limit_min_num']) ? $groupLimitArr['group_number_limit']['limit_min_num'] : 0;
            }
            $groupTicketLimit['is_limit'] = 0;
            $groupTicketLimit['tickets']  = [];
            if (isset($jdata['group_ticket_limit']) && $jdata['group_ticket_limit'] == 1) {
                $groupTicketLimit['is_limit'] = 1;
                $groupTicketLimit['tickets']  = isset($groupLimitArr['group_ticket_limit']) ? $groupLimitArr['group_ticket_limit'] : [];
            }
            $jdata['group_ticket_limit'] = $groupTicketLimit;
            $jdata['group_number_limit'] = $groupNumLimit;
        }

        $jdata['resourceName'] = '';
        if (!empty($jdata['resource_id'])) {
            $landResource          = new LandResource();
            $resourceRes           = $landResource->findResourceById($jdata['resource_id'], 'title');
            $jdata['resourceName'] = $resourceRes['title'] ?? '';
        }

        if (!$jdata) {
            return self::returnData(203, '产品不存在');
        }

        $imgUrl = $jdata['imgpath'] ?? 'images/defaultThum.jpg';

        //轮播展示图
        $imgArrInfo = [];
        if ($jdata['imgpathGrp']) {
            $imgUrls = @unserialize($jdata['imgpathGrp']);
            $imgUrls = is_array($imgUrls) ? $imgUrls : json_decode($jdata['imgpathGrp'], true);

            $count = count($imgUrls);
            for ($i = 0; $i < $count; $i++) {
                $imgArrInfo[$i] = (is_array($imgUrls) && $imgUrls[$i]) ? $imgUrls[$i] : 'images/defaultThum.jpg';
            }
            $imgArrString = implode(",", $imgArrInfo);
        }

        if (!$imgArrString) {
            $imgArrString = '';
        }

        $jdata['end_place_tude'] = $jdata['lng_lat_pos'] ? $jdata['lng_lat_pos'] : '0,0';
        $jdata['imgUrl']         = $imgUrl;
        $jdata['imgArrString']   = $imgArrString;

        unset($jdata['group_limit'], $jdata['account_id'], $jdata['add_time'], $jdata['details'], $jdata['areacode'],
            $jdata['imgpath'], $jdata['imgpathGrp'], $jdata['verify_status'], $jdata['update_time'], $jdata['traffic'],
            $jdata['topics'], $jdata['terminal'], $jdata['status'], $jdata['sms_phone'], $jdata['ship_route_code'], $jdata['salerid'],
            $jdata['resource_id'], $jdata['province_name'], $jdata['operater_id'], $jdata['notice'], $jdata['name'], $jdata['lng_lat_pos'],
            $jdata['level'], $jdata['letters'], $jdata['introduce'], $jdata['img_path_group'], $jdata['img_path'], $jdata['city_name'],
            $jdata['attribute']);

        return self::returnData(200, '', $jdata);
    }

    /**
     * 微平台预订列表添加微商城链接
     * <AUTHOR>
     * @date 2021/3/11
     *
     * @param  array  $data  预订列表数据
     *
     * @return array
     */
    public function addhandleWxMallUrl(string $sAccount, array $data)
    {
        if (empty($data)) {
            return $data;
        }

        foreach ($data as &$value) {
            $value['wxMallUrl'] = $this->createWxMallUrlForProduct($sAccount, $value['ptype'], $value['lid'],
                $value['aid']);
        }

        return $data;
    }

    /**
     * 微平台供应商店铺列表添加微商城链接
     * <AUTHOR>
     * @date 2021/3/24
     *
     * @param  int  $sid  上级供应商ID
     * @param  string  $sAccount  用户账号
     * @param  array  $data  列表数据
     *
     * @return array
     */
    public function addhandleEvoluteWxMallUrl(int $sid, string $sAccount, array $data)
    {
        if (empty($data)) {
            return $data;
        }

        foreach ($data as &$value) {
            $value['wxMallUrl'] = $this->createWxMallUrlForProduct($sAccount, $value['type'], $value['landId'], $sid);
        }

        return $data;
    }

    /**
     * 生成微商城产品页链接
     * <AUTHOR>
     * @date 2021/3/11
     *
     * @param  string  $sAccount  账号
     * @param  string  $pType  产品类型
     * @param  int  $lid  产品的景区ID
     * @param  int  $aid  产品的上级供应商ID
     *
     * @return string
     */
    public function createWxMallUrlForProduct(string $sAccount, string $pType, int $lid, int $aid)
    {
        $urlResult = '';
        if (empty($sAccount) || empty($pType) || empty($lid) || empty($aid)) {
            return $urlResult;
        }

        $baseUrl = MOBILE_DOMAIN;
        $url     = str_replace('wx.', $sAccount . '.', $baseUrl);
        switch ($pType) {
            case 'J':
                $urlResult = "{$url}h5/specialty/{$lid}?aid={$aid}";
                break;
            default:
                $urlResult = "{$url}h5/prod/{$lid}?aid={$aid}";
        }

        return $urlResult;
    }

	/**
	 * 更新产品凭证码类型(也叫：终端类型)
	 * <AUTHOR>
	 * @param $lid
	 * @param $sid
	 * @param $dtype
	 * @return array
	 */
	public function changeTerminalType($lid, $sid, $dtype){
    	// 权限判断
		// terminal_type 状态扭转
		$landInfoArr = $this->getLandInfo($lid);
		if ($landInfoArr['code'] != 200 || !$landInfoArr['data']) {
			return $this->returnData(204, $landInfoArr['msg']);
		}
		$landInfo = $landInfoArr['data'];
		if($dtype != 9 &&  $landInfo['account_id'] != $sid){
			return $this->returnData(401, '无权操作');
		}

		if($landInfo['terminalType'] == ProductConst::CODE_TYPE_EIGHT){ // 8位
			$newType = ProductConst::CODE_TYPE_SIX;
		} else {    // 6位
			$newType = ProductConst::CODE_TYPE_EIGHT;
		}
		//var_dump($newType);
		$terminalLandBiz = new TerminalLand();
		$switchRes = $terminalLandBiz->switchLandTerminalType($lid, $newType);
		//var_dump($switchRes);exit;
		if($switchRes){ // 修改成功
			return $this->returnData(200, '终端类型更新成功');
		} else {
			return $this->returnData(204, '终端类型更新失败');
		}
		// return $this->updateProductTerminalType($lid, $newType);
	}

	/**
	 * 这个是测试方法。不要使用这个方法。
	 * @param      $lid
	 * @param      $terminalType
	 * @param null $memberId
	 * @return array
	 */
    /*public function updateProductTerminalType($lid, $terminalType, $memberId = null){
    	$landInfoArr = $this->getLandInfo($lid);

	    if ($landInfoArr['code'] != 200 || !$landInfoArr['data']) {
		    return $this->returnData(204, $landInfoArr['msg']);
	    }

	    $landInfo = $landInfoArr['data'];

	    if($landInfo['terminalType'] == $terminalType){
		    return $this->returnData(200, '终端类型类型一致，无需更新。');
	    }

	    $operaterId = $landInfo['account_id'];
	    if($memberId){
		    $operaterId = $memberId;
	    }

	    $areaArr = explode('|', $landInfo['area']);
	    $provinceId = $areaArr[0] ?? 0;
	    $cityId = $areaArr[1] ?? 0;
	    $updateData['id'] = $lid;
	    $updateData['terminalType'] = $terminalType;
	    $updateData["title"] =  $landInfo['name'];  //'福鼎小白鹭-'.time();
	    $updateData['provinceId'] = $provinceId;
	    $updateData['cityId'] = $cityId;
	    $updateData['applyDid'] = $landInfo['account_id'];
	    $updateData['operaterId'] = $operaterId;
	    $updateData['pType'] = $landInfo['type'];
	    $updateData['imgpath'] = $landInfo['img_path'];
	    $updateData['status'] = $landInfo['status'];
	    $updateData['bhjq'] = $landInfo['details'];
	    $updateData['jqts'] = $landInfo['notice'];

	    $landApi = new \Business\JavaApi\CommodityCenter\Land();
	    $res = $landApi->updateLand($updateData);
	    if($res['code'] == 200){
		    return $this->returnData($res['code'], '终端类型更新成功');
	    } else {
		    return  $this->returnData($res['code'], $res['msg']);
	    }
    }*/

    /**
     * 根据tid 获取显示标签
     * @param $memberId
     * @param $sid
     * @param $tidList
     * @return array
     */
    public function getProductTageForTid($memberId,$paramData){
        $param = [];
        $productLevel = 2;//票维度 是2
        foreach ($paramData as $lid => $info){
            foreach ($info['ticket_ids'] as $tid){
                $param[] = [
                    'memberId' =>$memberId,
                    'sid' => $info['supplier_id'],
                    'productId' => $tid,
                ];
            }
        }
        $productTag = new \Business\NewJavaApi\Product\ProductTag();
        $res = $productTag->getLabelList(['list'=>$param,'productLevel'=>$productLevel]);
        if($res['code'] != 200){
            return $res;
        }
        return self::returnData(200, '', $res['data']);
    }

    /**
     * 产品列表 - 产品标签
     * @param $sid
     * @param $memberId
     * @param $products
     * @return array
     */
    public function getProductTag($sid,$memberId,$products){
        if (empty($products)) {
            return [];
        }
        $appId = 95 ;//商品标签 product_tag
        $moduleConfigBiz = new ModuleConfig();
        $res = $moduleConfigBiz->getOpenModuleBySidAndAppId([$sid], $appId);
        if($res['code']!=200 || empty($res['data'])){
            return $products;
        }
        $ticketData = array_column($products, 'tickets');
        if (empty($ticketData)) {
            return $products;
        }
        $tidArr = [];
        foreach ($ticketData as $item) {
            $tidArr = array_merge(array_column($item, 'ticketId'), $tidArr);
        }

        $paramData = [];
        foreach ($products as $value){
            $paramData[] = [
                'supplier_id' => $value['supplierId'],
                'ticket_ids' => array_column($value['tickets'],'ticketId')
            ];
        }
        $tagRes = $this->getProductTageForTid($sid,$paramData);
        if($tagRes['code'] != 200 ){
            return  $products;
        }
        $tagList = array_column($tagRes['data'],null,'productId');
        foreach ($products as  &$product){
            $product['product_tag'] = [];
            $ids = array_column($product['tickets'],'ticketId');
            foreach ($ids as $id){
                if(array_key_exists($id,$tagList) ){
                    array_push($product['product_tag'],$tagList[$id]);
                }
            }

        }
        return $products;

    }

    public function getProductTagForDistribution($sid,$memberId,$products){
        if (empty($products)) {
            return [];
        }
        $appId = 95 ;//商品标签 product_tag
        $moduleConfigBiz = new ModuleConfig();
        $res = $moduleConfigBiz->getOpenModuleBySidAndAppId([$sid], $appId);
        if($res['code']!=200 || empty($res['data'])){
            return $products;
        }
        $paramData = [];
        foreach ($products as $value){
            foreach ($value['ticketList'] as $ticketInfo){
                $tidArr = [$ticketInfo['tid']];
                $distributionIds = array_column($ticketInfo['distributionList'],'aids');
                $supplierIds = [];
                foreach ($distributionIds as $aids){
                    $aidsArr =  explode(',',$aids);
                    array_push($supplierIds,end($aidsArr));
                }
                foreach ($supplierIds as $supplierId){
                    $paramData[] = [
                        'supplier_id' => $supplierId,
                        'ticket_ids' => $tidArr
                    ];
                }
            }
        }
        $tagRes = $this->getProductTageForTid($sid,$paramData);
        if($tagRes['code'] != 200 ){
            return  $products;
        }
//        $tagList = array_column($tagRes['data'],null,'productId');
        foreach ($products as  &$productInfo){
            foreach ($productInfo['ticketList'] as &$ticketInfo){
                $ticketInfo['product_tag'] = [];
                foreach ($tagRes['data'] as $tagInfo){
                    if($tagInfo['productId'] == $ticketInfo['tid']){
                        array_push($ticketInfo['product_tag'],$tagInfo);
                    }
                }
            }
        }
        return $products;

    }

    public function getProductTagMoreTickets($sid,$memberId,$list){
        if (empty($list)) {
            return [];
        }
        $appId = 95 ;//商品标签 product_tag
        $moduleConfigBiz = new ModuleConfig();
        $res = $moduleConfigBiz->getOpenModuleBySidAndAppId([$sid], $appId);
        if($res['code']!=200 || empty($res['data'])){
            return $list;
        }
        $tidArr = array_column($list, 'ticketId');
        if (empty($tidArr)) {
            return $list;

        }
        $paramData[] = [
            'supplier_id' => $sid,
            'ticket_ids' => $tidArr
        ];
        $tagRes = $this->getProductTageForTid($sid,$paramData);

        if($tagRes['code'] != 200 ){
            return  $list;
        }
        $tagList = array_column($tagRes['data'],null,'productId');
        foreach ($list as  &$ticketInfo){
            $ticketInfo['product_tag'] = [];
            if(array_key_exists($ticketInfo['ticketId'],$tagList)){
                $ticketInfo['product_tag'] = $tagList[$ticketInfo['ticketId']];
            }
        }
        return $list;

    }

    public function getProductTagForDistributionMoreTickets($sid,$memberId,$list){
        if (empty($list)) {
            return [];
        }
        $appId = 95 ;//商品标签 product_tag
        $moduleConfigBiz = new ModuleConfig();
        $res = $moduleConfigBiz->getOpenModuleBySidAndAppId([$sid], $appId);
        if($res['code']!=200 || empty($res['data'])){
            return $list;
        }
        $paramData = [];
        foreach ($list as $value){
            $tidArr = [$value['tid']];
            $distributionIds = array_column($value['distributionList'],'aids');
            $supplierIds = [];
            foreach ($distributionIds as $aids){
                $aidsArr =  explode(',',$aids);
                array_push($supplierIds,end($aidsArr));
            }
            foreach ($supplierIds as $supplierId){
                $paramData[] = [
                    'supplier_id' => $supplierId,
                    'ticket_ids' => $tidArr
                ];
            }
        }
        $tagRes = $this->getProductTageForTid($sid,$paramData);
        if($tagRes['code'] != 200 ){
            return  $list;
        }
        foreach ($list as  &$ticketInfo){
            $ticketInfo['product_tag'] = [];
            foreach ($tagRes['data'] as $tagInfo){
                if($tagInfo['productId'] == $ticketInfo['tid']){
                    array_push($ticketInfo['product_tag'],$tagInfo);
                }
            }
        }
        return $list;

    }

    /**
     * 关联业态资源信息参数兼容
     */
    public function reclandManagerRelation($data, $isSuper)
    {
        foreach ($data as &$value) {
            $value['managements'] = [];
        }

        if (!$isSuper) {
            return $data;
        }

        $areaModel = new AreaModel();
        $areaSeaModel = new AreaModel(1);
        foreach ($data as &$value) {
            if (!empty($value['managementLands'])) {

                foreach ($value['managementLands'] as $manValue) {
                    $proCity = [$manValue['province'], $manValue['municipality'], '0'];
                    if ($manValue['oversea'] == 0) {
                        $areaNameArr = $areaModel->getInfoByCodeFromCache($proCity);
                    } else {
                        $areaNameArr = $areaSeaModel->getInfoByOverSeaCodeFromCache($proCity);
                    }
                    $value['managements'][] = [
                        'mt_id'         => $manValue['id'],
                        'mtype'         => $manValue['mtype'],
                        'mt_name'       => $manValue['title'],
                        'oversea'       => $manValue['oversea'],
                        'city'          => $manValue['municipality'],
                        'province'      => $manValue['province'],
                        'land_id'       => $manValue['landId'],
                        'province_name' => $areaNameArr[0] ?? '',
                        'city_name'     => $areaNameArr[1] ?? '',
                        'create_time'   => strtotime($manValue['resourceTime']),
                    ];
                }
            }
        }

        return $data;
    }

    /**
     * 处理旅游券是否可以编辑字段
     */
    public function handleTravelVoucherCanEdit($productArray, $travelVoucherLidArray)
    {
        if (empty($travelVoucherLidArray)) {
            return $productArray;
        }

        $voucherService = new VoucherService();
        $voucherCanEditRes = $voucherService->getSpuList($travelVoucherLidArray);
        if (empty($voucherCanEditRes['data'])) {
            return $productArray;
        }

        $voucherCanEditRes = array_column($voucherCanEditRes['data'], 'can_edit', 'lid');
        foreach ($productArray as &$product) {
            if ($product['type'] == 'Q') {
                $product['can_edit'] = $voucherCanEditRes[$product['landId']];
                foreach ($product['tickets'] as &$ticket) {
                    $ticket['can_edit'] = $voucherCanEditRes[$product['landId']];
                }
            }
        }

        return $productArray;
    }

    /**
     * 通过票查询顶级供应商的自定义配置
     * @param int $sid
     * @param int $ticketId
     * @param int $productId
     * @param string $code
     * @return array
     */
    public function getConfigRuleByTicket(int $sid, string $code, int $ticketId = 0, int $productId = 0)
    {
        if (!$ticketId && !$productId) {
            return $this->returnDataV2(self::CODE_PARAM_ERROR, [], '参数错误');
        }
        $ticketApi = new \Business\JavaApi\CommodityCenter\Ticket();
        if ($ticketId) {
            $res = $ticketApi->queryTicketById($ticketId);
            if ($res['code'] != self::CODE_SUCCESS) {
                return $this->returnDataV2($res['code'], $res['data'], $res['msg']);
            }
            $ticketInfo = $res['data'];
        } else {
            $res = $ticketApi->queryTicketInfoByProductIds([$productId]);
            if ($res['code'] != self::CODE_SUCCESS || empty($res['data'])) {
                return $this->returnDataV2($res['code'], $res['data'], $res['msg']);
            }
            $ticketInfo = $res['data'][0]['uuJqTicketDTO'];
        }
        $userConfigRule = new UserConfigRule();
        $res = $userConfigRule->getByMemberIdAndCode(['memberId' => $ticketInfo['apply_did'], 'code' => $code]);
        $codeValue = array_shift($res['data']);
        return $this->returnDataV2(self::CODE_SUCCESS, [$code => $codeValue ?: 1], "成功");
    }
}
