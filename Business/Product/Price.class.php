<?php

namespace Business\Product;

use Business\Base;
use Business\JavaApi\TicketApi;
use Business\JavaApi\Ticket\Price as PriceBiz;
use Business\Member\MemberRelation;
use Business\NewJavaApi\NewPms\StockQuery;
use Library\MulityProcessHelper;
use Model\Member\Member;
use Model\Product\PriceGroup;
use Business\JavaApi\Product\TicketCalcPrice;

/**
 * 价格业务接口
 *
 * <AUTHOR>
 * @date    2018-01-24
 *
 */
class Price extends Base
{
    private static $calcPriceBiz = null;
    private static $stockQueryBiz = null;

    /**
     * 兼容 以前serverinside中的Dynamic_Price_And_Storage方法
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  string  $date  日期
     * @param  integer  $mode  模式[1单个价格 2单个最低价 3时间段XML格式价格]
     * @param  integer  $ptype  $ptype 类型[0分销价 1零售价]
     * @param  integer  $getStorage  是否返回库存
     * @param  integer  $sid  供应商id
     * @param  string  $sdate  开始日期
     * @param  string  $edate  结束日期
     */
    public function DynamicPriceAndStorage($account, $pid, $date = '', $mode = 0, $ptype = 0, $getStorage = 0, $sid = 0, $sdate = '', $edate = '')
    {
        if (!$pid) {
            return $this->returnData(105, '参数错误');
        }

        switch ($mode) {
            //指定日期价格
            case 1:
                $date   = date('Y-m-d', strtotime($date));
                $result = $this->_getTheDatePrice($account, $pid, $date, $ptype, $getStorage, $sid);
                break;
            //获取最低价格
            case 2:
                $result = $this->_getLowestPrice($account, $pid, $ptype, $sid);
                break;
            //获取时间段价格
            case 3:
                $result = $this->_getRangePrice($account, $pid, $ptype, $sid, $sdate, $edate);
            default:
                break;
        }

        if (isset($result['code'])) {
            return $result;
        } else {
            return $this->returnData(105, '价格获取失败');
        }
    }

    /**
     * 获取产品在游玩日期的价格
     * <AUTHOR>
     * @data   2019-03-13
     *
     * @param  int  $tid  门票ID
     * @param  int  $playDate  游玩日期
     * @param  int  $priceType  价格类型 - setting=配置价(门市价/零售价/窗口价), settlement=分销价
     * @param  int  $memberId  购买用户
     * @param  int  $aid  供应商ID
     *
     * @throws 如果接口异常的话，会抛出异常
     * @return int
     */
    public function getPlayPrice($tid, $playDate, $priceType, $memberId = 0, $aid = 0)
    {
        $tid      = intval($tid);
        $playDate = strval($playDate);
        $memberId = intval($memberId);
        $aid      = intval($aid);

        if (!$tid || !$playDate) {
            return $this->returnData(105, '参数错误');
        }

        switch ($priceType) {
            case 'settlement': //分销价
                if (!$memberId || !$aid) {
                    return $this->returnData(105, '参数错误');
                }

                $priceBiz = new PriceBiz();
                $priceRes = $priceBiz->getDistributedPriceAndStg($memberId, $aid, $tid, $playDate);
                if ($priceRes['code'] == 200) {
                    $data = [
                        'tprice' => $priceRes['data']['settlementPrice'],
                    ];

                    return $this->returnData(200, '', $data);
                } else if ($priceRes['code'] == 500) {
                    //系統异常，直接抛出异常
                    throw new \Exception('价格获取失败', 500);
                } else {
                    return $this->returnData(105, '价格获取失败');
                }

                break;
            case 'setting': //门市价/零售价/窗口加
            default:
                $priceRes = TicketApi::getSinglePrices($tid, $playDate);
                if ($priceRes && isset($priceRes[$tid])) {
                    $data = [
                        'cost_price'    => $priceRes[$tid]['cost_price'],
                        'counter_price' => $priceRes[$tid]['counter_price'],
                        'window_price'  => $priceRes[$tid]['window_price'],
                        'retail_price'  => $priceRes[$tid]['retail_price'],
                    ];

                    return $this->returnData(200, '', $data);
                } else if ($priceRes === false) {
                    //系統异常，直接抛出异常
                    throw new \Exception('价格获取失败', 500);
                } else {
                    //没有配置价格
                    return $this->returnData(105, '价格获取失败');
                }

                break;
        }
    }

    /**
     * 价格配置完成，移动分组
     * <AUTHOR>
     * @date   2018-06-07
     *
     * @param  array  $taskList  完成的任务
     */
    public function movePriceGroupForTask()
    {

        $priceBiz = new PriceBiz();
        $result   = $priceBiz->getMoveGroupTaskDoneList();

        if (!isset($result['code']) || $result['code'] != 200 || !$result['data']) {
            return $this->returnData(204, '没有要处理的数据');
        }

        $taskList = $result['data'];
        //总条数
        $count = count($taskList);
        //每个进程处理10个
        $processNum = ceil($count / 10);

        defined('IS_MULITY_PROCESS') or define('IS_MULITY_PROCESS', true);
        $task = new MulityProcessHelper($this, $processNum, 10);
        $task->run($taskList);
    }

    /**
     * 移动分组动作
     * <AUTHOR>
     * @date   2018-06-07
     *
     * @param  array  $data  要处理的数据
     */
    public function runWorker($data)
    {
        if (!$data) {
            return false;
        }

        $fail = $success = [];
        foreach ($data as $item) {
            $from = $item['source'];
            $to   = $item['target'];
            $did  = $item['distributor_id'];
            $sid  = $item['operator_id'];

            $moveRes = $this->movePriceGroup($did, $sid, $from, $to);
            if ($moveRes['code'] == 200) {
                $success[] = $item['id'];
            } else {
                $fail[] = $item['id'];
            }
        }

        $priceBiz = new PriceBiz();
        //更新任务的状态
        if ($success) {
            $priceBiz->moveGroupSuccess($success);
        }
        if ($fail) {
            $priceBiz->moveGroupSuccess($fail);
        }
    }

    /**
     * 移动价格分组
     * <AUTHOR>
     * @date   2018-06-07
     *
     * @param  int  $did  分销商id
     * @param  int  $sid  供应商id
     * @param  int  $from  来源分组id
     * @param  int  $to  目标分组id
     *
     * @return array
     */
    public function movePriceGroup($did, $sid, $from, $to)
    {
        if (!$did || !$sid) {
            return $this->returnData(204, '参数错误');
        }

        if ($from == 0) {
            $addRes = $this->joinGroup($to, $did, $sid, $from);
            if ($addRes['code'] != 200) {
                return $this->returnData(204, '移动失败1');
            }
        } else {
            //将分销商移出原分组
            $moveRes = $this->moveFromGroup($did, $sid, $from, $to);
            if ($moveRes['code'] != 200) {
                return $this->returnData(204, '移动失败2');
            }

            if ($to) {
                $addRes = $this->joinGroup($to, $did, $sid, $from);
                if ($addRes['code'] != 200) {
                    return $this->returnData(204, '移动失败3');
                }
            }
        }

        return $this->returnData(200, '移动成功');
    }

    /**
     * 获取移动分组历史记录
     * <AUTHOR>
     * @date   2018-06-11
     *
     * @param  integer  $sid  供应sid
     * @param  integer  $page  当前页码
     * @param  integer  $size  每页条数
     *
     * @return array
     */
    public function groupMoveRecords($sid, $page = 1, $size = 10)
    {
        if (!$sid || !$page || !$size) {
            return $this->returnData(204, '参数错误');
        }

        //$priceBiz = new PriceBiz();
        //$result   = $priceBiz->groupMoveRecords($sid, $page, $size);
        $javaApi = new \Business\JavaApi\Product\EvoluteTask();
        $result  = $javaApi->queryTaskPage($sid, $page, $size);

        if ($result['code'] == 200) {
            $list  = $result['data']['list'];
            $total = $result['data']['total'];
        } else {
            $list  = [];
            $total = 0;
        }

        if ($list) {
            $fidArr  = array_column($list, 'fid');
            $operArr = array_column($list, 'operatorId');
            $midArr  = array_unique(array_merge($fidArr, $operArr));

            //获取分销商名称
            $memModel = new Member('slave');
            $disMap   = $memModel->getMemberInfoByMulti($midArr, 'id', 'id,dname', 1);
            //获取分组名称
            $gidArr      = array_merge(array_column($list, 'source'), array_column($list, 'target'));
            $groupBiz        = new \Business\Product\Update\EvoluteGroup();
            $originGroupInfo = $groupBiz->queryByGroupIds($gidArr);
            if ($originGroupInfo['code'] == 200 && $originGroupInfo['data']) {
                $groupMap = array_column($originGroupInfo['data'], 'groupName', 'id');
            }
            $groupMap[0] = '未分组';

            $data = [];
            foreach ($list as $item) {
                $data[] = [
                    'name'   => $disMap[$item['fid']]['dname'],
                    'from'   => $item['sourceInfo'],
                    'to'     => $item['targetInfo'],
                    'time'   => $item['updateTime'],
                    'status' => 1,
                    'oper'   => $disMap[$item['operatorId']]['dname'],
                ];
            }
        }

        $return = [
            'total'      => $total,
            'total_page' => ceil($total / $size),
            'page'       => $page,
            'list'       => $data,
        ];

        return $this->returnData(200, '', $return);

    }

    /**
     * 动态获取价格
     * <AUTHOR>
     * @date   2018-11-07
     *
     * @param  int  $pid  产品ID
     * @param  string  $date  日期
     * @param  int  $mode  模式[0XML 1单个价格 2单个最低价]
     * @param  string  $sdate  开始时间
     * @param  string  $edate  结束时间
     * @param  int  $ptype  类型[0供应价 1零售价],说明：时间段价格$date,$mode必须为默认空值并会跨时间集
     * @param  int  $get_storage  是否需要获取库存1需要
     * @param  int  $add_dprice  上级供应商配置的差价
     * @param  int  $out  0 mode3的返回值少一个rid
     *
     * @return int|null|string
     */
    public function getDynamicPriceMerge($pid, $date = '', $mode = 0, $sdate = '', $edate = '', $ptype = 0, $get_storage = 0, $add_dprice = 0, $out = 0)
    {
        $price    = '';
        $priceBiz = new \Business\JavaApi\Ticket\Price();
        $ticModel = new \Model\Product\Ticket('slave');
        if ($mode == 1 && $date) {
            $onday = date('w', strtotime($date)); //获取星期
            //获取传入data的价格
            $tid = $ticModel->getTid($pid);
            if (!$tid) {
                return -1;
            }
            $priceData = $priceBiz->getSpecialPrice($tid, $date, $onday);
            if ($priceData['code'] != 200) {
                return -1;
            }
            $price = $this->_formatPrice($priceData, $ptype, $get_storage, $add_dprice);

            return $price;
        } elseif ($mode == 2) {
            $tid = $ticModel->getTid($pid);
            if (!$tid) {
                return -1;
            }
            $price = $this->getLowestPrice($tid, $ptype);
            if ($price != -1) {
                $price += $add_dprice;
            }

            return $price;
        } elseif ($mode == 0 || $mode == 3) {
            $priceData = [];
            $tid       = $ticModel->getTid($pid);
            if (!$tid) {
                return $priceData;
            }
            $priceList = $priceBiz->getPriceList($tid, $sdate, $edate);
            if ($priceList['code'] != 200) {
                return $priceData;
            }
            $tmpPrice = $priceList['data'];

            $price = [];
            foreach ($tmpPrice as $key => $value) {
                $price[$key]['id']         = $value['id'];
                $price[$key]['pid']        = $value['pid'];
                $price[$key]['ptype']      = $value['ptype'];
                $price[$key]['memo']       = $value['memo'];
                $price[$key]['n_price']    = $value['nPrice'] + $add_dprice;
                $price[$key]['s_price']    = $value['sPrice'] + $add_dprice;
                $price[$key]['l_price']    = $value['lPrice'];
                $price[$key]['start_date'] = date("Y-m-d", $value['startDate']);
                $price[$key]['end_date']   = date("Y-m-d", $value['endDate']);
                $price[$key]['weekdays']   = $value['weekdays'];
                $price[$key]['storage']    = $value['storage'];

                if ($out != 0) {
                    $price[$key]['rid'] = $value['rid'];
                }
            }

            return $price;
        }
    }

    /**
     * 设置结算方式
     * author  leafzl
     * Date:2018-12-26
     *
     * @param $sid
     * @param $fidArr
     * @param $way
     * @param $type
     *
     * @return array
     */
    public function setClearingWay($sid, $fidArr, $way, $type, $opid = 0)
    {
        if (!$sid || !is_array($fidArr) || $way === false || !$type) {
            return $this->returnData(204, '参数错误');
        }

        $memberRelationBus = new MemberRelation();

        $fail_list = [];
        $fail      = 0;
        $success   = 0;
        foreach ($fidArr as $value) {
            $res = $memberRelationBus->addOrUpdateClearingWay($sid, $value, $way, $opid);
            if ($res === false) {
                $fail_list[] = ['id' => $value, 'name' => ''];
                $fail++;
            } else {
                $success++;
            }
        }
        if ($type == 2) {
            $list = ['fail_list' => $fail_list, 'fail' => $fail, 'success' => $success];

            return $this->returnData(200, '', $list);
        }
        if ($type == 1) {
            if (!$fail_list) {
                return $this->returnData(200);
            } else {
                return $this->returnData(204, '未知错误');

            }
        }

    }

    /**
     * 批量设置结算方式
     * author  leafzl
     * Date: 2018-12-26
     *
     * @param $sid
     * @param $groupId
     * @param $way
     * @param  int  $isNew  是否是新分组 0：不是 1：是
     */
    public function batchSetClearingWay($sid, $groupId, $way, $isNew = 0, $opid = 0)
    {

        if (!$sid || !$groupId || $way === false) {
            return $this->returnData(204, '参数错误');
        }
        $fail_list         = [];
        $data              = [];
        $fail              = 0;
        $success           = 0;
        $memberRelationBus = new MemberRelation();

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds([$groupId]);
        if ($groupFidRes['code'] != 200) {
            return $this->returnData(204, '分销商信息获取失败');
        }
        $fidArr = !empty($groupFidRes['data']) ? array_column($groupFidRes['data'], 'fid') : [];

        $arrayParams       = [];

        foreach ($fidArr as $value) {
            $arrayParams[] = [
                'sid'          => $sid,
                'fid'          => $value,
                'clearingMode' => $way,
            ];
            $fail_list[]   = ['id' => $value, 'name' => ''];
        }

        $res = $memberRelationBus->batchSetClearingWay($arrayParams, $opid);

        //结算迁移兼容旧逻辑
        if ($res) {
            $fail_list = [];
            $fail      = 0;
            $success   = count($fidArr);
        } else {
            $fail    = count($fidArr);
            $success = 0;
        }

        $list = ['fail_list' => $fail_list, 'fail' => $fail, 'success' => $success];

        return $this->returnData(200, '', $list);

    }

    /**
     * 获取指定日期的价格
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  string  $date  日期
     * @param  integer  $ptype  $ptype 类型[0分销价 1零售价]
     * @param  integer  $getStorage  是否返回库存
     * @param  integer  $sid  供应商id
     */
    private function _getTheDatePrice($account, $pid, $date, $ptype, $getStorage, $sid)
    {
        if (!$date) {
            return $this->returnData(105, '日期参数缺失');
        }

        $date = date('Y-m-d', strtotime($date));

        switch ($ptype) {
            case 1:
                //获取零售价
                $result = $this->_getTheDateRetailPrice($pid, $date, $getStorage);
                break;
            default:
                //获取分销价
                $result = $this->_getTheDateDistributedPrice($account, $pid, $date, $getStorage, $sid);
                break;
        }

        return $result;
    }

    /**
     * 获取指定日期的分销价
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  string  $date  日期
     * @param  integer  $getStorage  是否返回库存
     * @param  integer  $sid  供应商id
     */
    private function _getTheDateDistributedPrice($account, $pid, $date, $getStorage, $sid)
    {
        //获取分销商id
        $memModel = new Member('slave');
        $member   = $memModel->getMemberInfo($account, 'account', 'id,status');

        if (!$member || !in_array($member['status'], [0, 3])) {
            return $this->returnData(101, '用户不存在或状态错误');
        }
        //获取tid
        $ticModel = new \Model\Product\Ticket('slave');
        $ticket   = $ticModel->getTicketInfoByPid($pid, 'apply_did,id');
        if (!$ticket) {
            return $this->returnData(1065, '门票不存在');
        }
        $tid = $ticket['id'];
        if ($sid == 0) {
            $sid = $ticket['apply_did'];
        }

        $fid = $member['id'];
        //获取分销价格
        $data = [];

        $priceBiz = new PriceBiz();
        $priceRes = $priceBiz->getDistributedPriceAndStg($fid, $sid, $tid, $date);
        if ($priceRes['code'] == 200) {
            $priceData     = $priceRes['data'];
            $tmp['sprice'] = $priceData['settlementPrice'];
            //同时返回库存
            if ($getStorage) {
                $tmp['storage'] = $priceData['storage'];
            }

            $data[] = $tmp;

            return $this->returnData(200, '', $data);
        } else {
            return $this->returnData(105, '价格获取失败');
        }

    }

    /**
     * 获取指定日期的零售价
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  int  $pid  门票pid
     * @param  string  $date  日期
     * @param  integer  $getStorage  是否返回库存
     */
    private function _getTheDateRetailPrice($pid, $date, $getStorage)
    {
        //获取tid
        $ticModel = new \Model\Product\Ticket('slave');
        $tid      = $ticModel->getTid($pid);
        if (!$tid) {
            return $this->returnData(1065, '门票不存在');
        }

        $priceRes = TicketApi::getDailyStorage(1, $tid, $date);
        if (isset($priceRes[$tid])) {
            $tmp['sprice'] = $priceRes[$tid]['lPrice'];
            if ($getStorage) {
                $tmp['storage'] = $priceRes[$tid]['storage'];
            }

            $data[] = $tmp;

            return $this->returnData(200, '', $data);
        } else {
            return $this->returnData(105, '零售价获取失败');
        }
    }

    /**
     * 获取最低分销价|零售价
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  string  $date  日期
     * @param  integer  $getStorage  是否返回库存
     * @param  integer  $sid  供应商id
     */
    private function _getLowestPrice($account, $pid, $ptype, $sid)
    {
        switch ($ptype) {

            case 1:
                //获取最低零售价
                $result = $this->_getLowestRetailPrice($pid);
                break;
            case 0:

            default:
                //获取最低分销价
                $result = $this->_getLowestDateDistributedPrice($account, $pid, $sid);
                break;
        }

        return $result;
    }

    /**
     * 获取最低零售价
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  int  $pid  门票pid
     *
     * @return array
     */
    private function _getLowestRetailPrice($pid)
    {
        //获取tid
        $ticModel = new \Model\Product\Ticket('slave');
        $tid      = $ticModel->getTid($pid);
        if (!$tid) {
            return $this->returnData(1065, '门票不存在');
        }

        $priceBiz = new PriceBiz();
        $result   = $priceBiz->getLowestRetailPrice($tid);

        if ($result['code'] == 200) {
            $data[]['sprice'] = $result['data'];

            return $this->returnData(200, '', $data);
        } else {
            return $this->returnData(105, '最低价获取失败');
        }
    }

    /**
     * 获取最低分销价
     * <AUTHOR>
     * @date   2018-01-24
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  integer  $sid  供应商id
     *
     * @return array
     */
    private function _getLowestDateDistributedPrice($account, $pid, $sid)
    {
        //获取分销商id
        $memModel = new Member('slave');
        $member   = $memModel->getMemberInfo($account, 'account', 'id,status');

        if (!$member || !in_array($member['status'], [0, 3])) {
            return $this->returnData(101, '用户不存在或状态错误');
        }
        $fid = $member['id'];

        //获取tid
        $ticModel = new \Model\Product\Ticket('slave');
        $ticket   = $ticModel->getTicketInfoByPid($pid, 'apply_did,id');
        if (!$ticket) {
            return $this->returnData(1065, '门票不存在');
        }
        $tid = $ticket['id'];
        if ($sid == 0) {
            $sid = $ticket['apply_did'];
        }

        $priceBiz = new PriceBiz();
        $result   = $priceBiz->getLowestDistributedPrice($fid, $sid, $tid);

        if ($result['code'] == 200 && $result['data'] != -1) {
            $data[]['sprice'] = $result['data'];

            return $this->returnData(200, '', $data);
        } else {
            return $this->returnData(105, '最低价获取失败');
        }
    }

    /**
     * 获取区间段价格
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  int  $ptype  1零售价|0分销价
     * @param  int  $sid  上级供应商id
     * @param  string  $sdate  开始日期
     * @param  string  $edate  结束日期
     *
     * @return [type]              [description]
     */
    private function _getRangePrice($account, $pid, $ptype, $sid, $sdate, $edate)
    {
        switch ($ptype) {

            case 1:
                //获取区间段零售价
                $result = $this->_getRangeRetailPrice($pid, $sdate, $edate);
                break;
            case 0:

            default:
                //获取区间段分销价
                $result = $this->_getRangeDistributedPrice($account, $pid, $sid, $sdate, $edate);
                break;
        }

        return $result;
    }

    /**
     * 获取区间段零售价
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @param  int  $pid  门票pid
     * @param  string  $sdate  开始日期
     * @param  string  $edate  结束日期
     *
     * @return array
     */
    private function _getRangeRetailPrice($pid, $sdate, $edate)
    {
        //获取tid
        $ticModel = new \Model\Product\Ticket('slave');
        $tid      = $ticModel->getTid($pid);
        if (!$tid) {
            return $this->returnData(1065, '门票不存在');
        }

        $priceBiz = new PriceBiz();
        $result   = $priceBiz->getRangeRetailPrice($tid, $sdate, $edate);

        $data = [];
        if ($result['code'] == 200 && $result['data']) {

            foreach ($result['data'] as $item) {
                $data[] = [
                    'id'         => $item['id'],
                    'pid'        => $item['pid'],
                    'memo'       => '',
                    'ptype'      => $item['ptype'],
                    'n_price'    => $item['nPrice'],
                    's_price'    => $item['sPrice'],
                    'l_price'    => $item['lPrice'],
                    'start_date' => $item['startDate'],
                    'end_date'   => $item['endDate'],
                    'weekdays'   => $item['weekdays'],
                    'storage'    => $item['storage'],
                ];
            }
        }

        return $this->returnData(200, '', $data);
    }

    /**
     * 获取区间段分销价
     * <AUTHOR>
     * @date   2018-01-25
     *
     * @param  string  $account  账号
     * @param  int  $pid  门票pid
     * @param  int  $sid  上级供应商id
     * @param  string  $sdate  开始日期
     * @param  string  $edate  结束日期
     *
     * @return array
     */
    private function _getRangeDistributedPrice($account, $pid, $sid, $sdate, $edate)
    {
        //获取分销商id
        $memModel = new Member('slave');
        $member   = $memModel->getMemberInfo($account, 'account', 'id,status');

        if (!$member || !in_array($member['status'], [0, 3])) {
            return $this->returnData(101, '用户不存在或状态错误');
        }
        $fid = $member['id'];

        //获取tid
        $ticModel = new \Model\Product\Ticket('slave');
        $ticket   = $ticModel->getTicketInfoByPid($pid, 'apply_did,id');
        if (!$ticket) {
            return $this->returnData(1065, '门票不存在');
        }
        $tid = $ticket['id'];
        if ($sid == 0) {
            $sid = $ticket['apply_did'];
        }

        $priceBiz = new PriceBiz();
        $result   = $priceBiz->getRangeDistributedPrice($tid, $fid, $sid, $sdate, $edate);

        $data = [];
        if ($result['code'] == 200 && $result['data']) {

            foreach ($result['data'] as $item) {
                $data[] = [
                    'id'         => $item['id'],
                    'pid'        => $item['pid'],
                    'memo'       => '',
                    'ptype'      => $item['ptype'],
                    'n_price'    => $item['nPrice'],
                    's_price'    => $item['sPrice'],
                    'l_price'    => $item['lPrice'],
                    'start_date' => $item['startDate'],
                    'end_date'   => $item['endDate'],
                    'weekdays'   => $item['weekdays'],
                    'storage'    => $item['storage'],
                ];
            }
        }

        return $this->returnData(200, '', $data);
    }

    private function _formatPrice($data, $ptype, $get_storage, $add_dprice)
    {
        $newData = $data['data'];
        if (isset($newData)) {
            if ($newData['ptype'] == 1) {
                if ($ptype == 0) {
                    $price = $newData['sPrice'];
                } else {
                    $price = $newData['lPrice'];
                }
                if ($get_storage == 1) {
                    $price += $add_dprice;
                    $price = $price . ',' . $newData['storage'];
                } else {
                    $price += $add_dprice;
                }
            } else {
                if ($ptype == 0) {
                    $price = $newData['nPrice'];
                } else {
                    $price = $newData['lPrice'];
                }
                if ($get_storage == 1) {
                    $price += $add_dprice;
                    $price = $price . ',' . $newData['storage'];
                } else {
                    $price += $add_dprice;
                }
            }

            return $price;
        } else {
            return -1;
        }
    }

    /**
     * 获取最低价
     *
     * @param $pid
     * @param $ptype
     *
     * @return int|null
     */
    private function getLowestPrice($tid, $ptype)
    {
        $priceBiz = new \Business\JavaApi\Ticket\Price();
        $data     = $priceBiz->getLowestPrice($tid);
        if ($data['code'] != 200) {
            return -1;
        }
        foreach ($data['data'] as $item) {
            $price[$item['ptype']] = $item;
        }
        if ($ptype == 0) {
            $mp = $price[0]['nPrice'];
            $np = $price[1]['sPrice'];
        } else {
            $mp = $price[0]['lPrice'];
            $np = $price[1]['lPrice'];
        }
        if ($mp === null && $np !== null) {
            $lp = $np;
        } elseif ($np === null && $mp !== null) {
            $lp = $mp;
        } elseif ($mp !== null && $np !== null) {
            $lp = ($mp > $np) ? $np : $mp;
        } else {
            $lp = -1;
        }

        return $lp;
    }

    /**
     * 批量获取转分销结算价
     * <AUTHOR>
     * @date 2020/10/9
     *
     * @param  array  $paramArr
     * [
     *   [
     *     ticketId => 票ID
     *     fid => 分销商ID
     *     sid => 供应商ID
     *   ],
     * ]
     *
     * @return array
     */
    public function batchSettlementQuery(array $paramArr)
    {
        if (empty($paramArr)) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误', []);
        }

        $javaApi = new TicketApi();
        $result  = $javaApi->batchSettlementQuery($paramArr);

        if ($result['code'] !== 200 || empty($result['data'])) {
            return $this->returnData($result['code'], $result['msg'], []);
        }

        //格式处理
        $return = [];
        foreach ($result['data'] as $item) {
            if ((!isset($item['fid']) || empty($item['fid'])) || (!isset($item['sid']) || empty($item['sid'])) || (!isset($item['ticket_id']) || empty($item['ticket_id']))) {
                continue;
            }
            $return["{$item['sid']}_{$item['ticket_id']}"] = $item['settlement_price'];
        }

        return $this->returnData(self::CODE_SUCCESS, '', $return);

    }

    /**
     * 批量获取转分销结算价
     * @aurhtor  PeiJun Li
     * @data  2023-04-04
     *
     * @param  array  $paramArr
     * [
     *   [
     *     ticketId => 票ID
     *     fid => 分销商ID
     *     sid => 供应商ID
     *   ],
     * ]
     *
     * @return array
     */
    public function batchSettlementQueryNew(array $paramArr)
    {
        if (empty($paramArr)) {
            return $this->returnData(self::CODE_PARAM_ERROR, '参数错误', []);
        }

        $javaApi = new TicketApi();
        $result  = $javaApi->batchSettlementQueryNew($paramArr);

        if ($result['code'] !== 200 || empty($result['data'])) {
            return $this->returnData($result['code'], $result['msg'], []);
        }

        //格式处理
        $return = [];
        foreach ($result['data'] as $item) {
            if ((!isset($item['fid']) || empty($item['fid'])) || (!isset($item['sid']) || empty($item['sid'])) || (!isset($item['ticket_id']) || empty($item['ticket_id']))) {
                continue;
            }
            $return["{$item['sid']}_{$item['ticket_id']}"] = $item['settlement_price'];
        }

        return $this->returnData(self::CODE_SUCCESS, '', $return);

    }

    public static function getCalcPriceBiz()
    {
        if (!self::$calcPriceBiz) {
            self::$calcPriceBiz = new TicketCalcPrice();
        }

        return self::$calcPriceBiz;
    }

    /**
     * 新版库存查询
     * @return StockQuery
     */
    public static function getStockQueryBiz()
    {
        if (!self::$stockQueryBiz) {
            self::$stockQueryBiz = new StockQuery();
        }
        return self::$stockQueryBiz;
    }

    /**
     * 下单批量获取门票的可用库存和价格信息
     * 【指定单个日期】的门票库存和价格
     * <AUTHOR> Li
     * @date 2020-10-20
     *
     * @param  int  $fid  账号Id
     * @param  int  $sid  上级分销商Id
     * @param  string  $playDate  游玩时间
     * @param  string  $ticketIds  门票Ids 多个Id使用逗号分隔
     * @param  bool  $unifiedStorage  是否使用新版库存聚合
     * @return array
     */
    public function buyBatchGet(int $fid, int $sid, string $playDate, string $ticketIds, bool $unifiedStorage = false)
    {
        if (!$fid || !$sid || !$playDate || !$ticketIds) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getCalcPriceBiz()->buyBatchGet($fid, $sid, $playDate, $ticketIds);

        if ($result['code'] != 200) {
            return $this->returnData(204, $result['msg']);
        }

        if (!$result['data']) {
            return $this->returnData(200, $result['msg']);
        }

        $returnData = [];
        $ticketIdArr = [];
        foreach ($result['data'] as $key => $val) {
            $ticketIdArr[] = $val['ticketId'];
            $returnData[$val['ticketId']] = $val;
        }

        if ($unifiedStorage) {
            $stockQueryApi = new StockQuery;
            $stockQueryRes = $stockQueryApi->compactQueryBatchSkuAvailableStock($fid, $sid, $ticketIdArr, $playDate);
            if ($stockQueryRes['code'] != self::CODE_SUCCESS) {
                return $this->returnData(203, $stockQueryRes['msg']);
            }
            $storageData = $stockQueryRes['data'] ?? [];
            foreach ($storageData as $storageItem) {
                if (!isset($returnData[$storageItem['skuId']])) {
                    continue;
                }
                $returnData[$storageItem['skuId']]['availableStorage'] = $storageItem['availableStock'];
            }
        }

        return $this->returnData(200, $result['msg'], $returnData);
    }

    /**
     * 下单获取门票的可用库存和价格信息
     * 【指定日期范围】的门票库存和价格
     * <AUTHOR> Li
     * @date 2020-10-20
     *
     * @param  int  $fid  账号Id
     * @param  int  $sid  上级分销商Id
     * @param  string  $playDateStart  开始日期
     * @param  string  $playDateEnd  结束日期
     * @param  int  $ticketId  门票Id
     * @param  bool  $unifiedStorage  是否使用新版库存聚合
     * @return array
     */
    public function buyGetCalendar(int $fid, int $sid, string $playDateStart, string $playDateEnd, int $ticketId, bool $unifiedStorage = false)
    {
        if (!$fid || !$sid || !$playDateStart || !$playDateEnd || !$ticketId) {
            return $this->returnData(204, '参数错误');
        }

        $result = $this->getCalcPriceBiz()->buyGetCalendar($fid, $sid, $playDateStart, $playDateEnd, $ticketId);

        if ($result['code'] != 200) {
            return $this->returnData(204, $result['msg']);
        }

        $result['data'] = array_column($result['data'], null, 'date');

        if ($unifiedStorage) {
            $stockQueryResults = self::getStockQueryBiz()->compactQueryRangeSkuAvailableStock($ticketId, $playDateStart, $playDateEnd, $fid, $sid);
            if ($stockQueryResults['code'] != self::CODE_SUCCESS) {
                return $this->returnData($stockQueryResults['code'], $result['msg']);
            }
            foreach ($stockQueryResults['data'] as $item) {
                if (isset($result['data'][$item['stockDate']])) {
                    $result['data'][$item['stockDate']]['availableStorage'] = $item['availableStock'];
                }
            }
        }

        return $this->returnData(200, $result['msg'], $result['data']);
    }

    /**
     * 批量设置还款方式
     * author  xwh
     * Date: 2021-08-11
     *
     * @param $sid
     * @param $groupId
     * @param $way 0：不限 1：线下
     * @param int $isNew  是否是新分组 0：不是 1：是
     */
    public function batchSetRepayWay($sid, $groupId, $way, $isNew = 0, $opid = 0)
    {
        if (!$sid || !$groupId || !in_array($way, [0,1])) {
            return $this->returnData(204, '参数错误');
        }
        $fail_list         = [];
        $memberRelationBus = new MemberRelation();

        $getEvoluteGroupBiz = new \Business\Product\Get\EvoluteGroupUser();
        $groupFidRes        = $getEvoluteGroupBiz->queryGroupFidsByGroupIds([$groupId]);
        if ($groupFidRes['code'] != 200 || empty($groupFidRes['data'])) {
            return $this->returnData(204, '分销商信息获取失败');
        }
        $fidArr = array_column($groupFidRes['data'], 'fid');


        $arrayParams       = [];

        foreach ($fidArr as $value) {
            $arrayParams[] = [
                'sid'          => $sid,
                'fid'          => $value,
                'repayMode'    => $way,
            ];
            $fail_list[]   = ['id' => $value, 'name' => ''];
        }
        $res = $memberRelationBus->batchSetClearingWay($arrayParams, $opid);

        //结算迁移兼容旧逻辑
        if ($res) {
            $fail_list = [];
            $fail      = 0;
            $success   = count($fidArr);
        } else {
            $fail    = count($fidArr);
            $success = 0;
        }

        $list = ['fail_list' => $fail_list, 'fail' => $fail, 'success' => $success];

        return $this->returnData(200, '', $list);

    }

    /**
     * 设置还款方式
     * author  xwh
     * Date: 2021-08-11
     *
     * @param $sid
     * @param $fid
     * @param $way 0：不限 1：线下
     */
    public function setRepayWay($sid, $fid, $way, $opid = 0)
    {
        if (!$sid || !$fid || !in_array($way, [0,1])) {
            return $this->returnData(204, '参数错误');
        }
        $memberRelationBus = new MemberRelation();
        $arrayParams       = [];
        $arrayParams[] = [
            'sid'          => $sid,
            'fid'          => $fid,
            'repayMode'    => $way,
        ];
        $res = $memberRelationBus->batchSetClearingWay($arrayParams, $opid);
        if($res){
            return $this->returnData(200, '成功', $res);
        }else{
            return $this->returnData(400, '失败');
        }
    }

    /**
     * 添加分销商分组
     * <AUTHOR>  Li
     * @date  2021-10-12
     *
     * @param  int  $groupId  移动目标分组id
     * @param  int  $did  分销商id
     * @param  int  $sid  供应商id
     * @param  int  $from  来源分组id
     * @param  bool  $isJava  是否调用java接口
     *
     * @return array
     */
    public function joinGroup(int $groupId, int $did, int $sid, int $from = 0, bool $isJava = true)
    {
        //调用Java新版移动分组
        $addEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $addRes             = $addEvoluteGroupBiz->moveDistributorGroup($sid, [$did], $from, $groupId, $sid);
        if ($addRes['code'] != 200) {
            return $this->returnData(204, '移动失败');
        }
        // 设置分销商分组ID
        $memberRelationModel = new \Model\Member\MemberRelationship();
        $memberRelationModel->setRelationShipGroupId($sid, $did, $groupId);

        return $this->returnData(200, '移动成功');
    }

    /**
     * 从分组中移除
     * <AUTHOR>  Li
     * @date  2021-10-12
     *
     * @param  int  $did  分销商id
     * @param  int  $sid  供应商id
     * @param  int  $from  来源分组id
     * @param  int  $groupId  移动目标分组id
     *
     * @return array
     */
    public function moveFromGroup(int $did, int $sid, int $from = 0, int $groupId = 0)
    {
        //调用Java新版移动分组
        $movedEvoluteGroupBiz = new \Business\Product\Update\EvoluteGroupUser();
        $moveRes              = $movedEvoluteGroupBiz->moveDistributorGroup($sid, [$did], $from, $groupId, $sid);
        if ($moveRes['code'] != 200) {
            return $this->returnData(204, $moveRes['msg']);
        }

        // 设置分销商分组ID
        $memberRelationModel = new \Model\Member\MemberRelationship();
        $memberRelationModel->setRelationShipGroupId($sid, $did, $groupId);

        return $this->returnData(200, '移动成功3');
    }
}
