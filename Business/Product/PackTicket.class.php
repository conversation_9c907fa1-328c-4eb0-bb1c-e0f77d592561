<?php
/**
 * 套票相关的业务封装
 *
 * <AUTHOR>
 * @date 2020-02-04
 */

namespace Business\Product;

use Business\Base;
use Business\PackTicket\PackRelation;
use Library\Cache\Cache;
use Business\JavaApi\Product\PackageTicket as PackApi;
use Exception;

class PackTicket extends Base
{
    public $paymode          = 0; // 支付方式
    public $advance          = 0; // 套票需要提前多少天购买
    public $section          = 0; // 是否存在验证区间，1 存在
    public $usedate          = []; // 套票有效使用日期数组
    public $message          = []; // 提示/错误消息记录
    public $paymode_continer = []; //

    /**
     * 根据套票的门票ID获取拥有的子票
     * <AUTHOR>
     * @date 2020/2/20
     *
     * @param  mixed  $parentTid
     *
     * @return array
     */
    public function getTickets($parentTid)
    {
        if (empty($parentTid)) {
            return [];
        }

        if (is_array($parentTid)) {
            $parentTidArr = $parentTid;
        } else {
            $parentTidArr = [intval($parentTid)];
        }

        $baseApi = new PackRelation();
        $res     = $baseApi->querySubList($parentTidArr);

        if ($res['code'] != 200) {
            throw new Exception($res['msg'], $res['code']);
        }

        $resList = [];
        $tmpData = (array)$res['data'];

        foreach ($tmpData as $item) {
            $resList[] = [
                'id'         => $item['id'],
                'parent_tid' => $item['parent_tid'],
                'lid'        => $item['lid'],
                'tid'        => $item['tid'],
                'pid'        => $item['pid'],
                'num'        => $item['num'],
                'aid'        => $item['aid'],
                'cost_price' => $item['cost_price'],
            ];
        }

        return $resList;
    }

    /**
     * 根据门票ID获取该门票的主票ID
     * <AUTHOR>
     * @date 2020/2/20
     *
     * @param  int  $tid  门票ID
     *
     * @return array
     */
    public function getParentsByTid($tid)
    {
        if (!$tid) {
            return [];
        }

        if (is_array($tid)) {
            $subTicketIdArr = $tid;
        } else {
            $subTicketIdArr = [intval($tid)];
        }

        // $baseApi = new PackApi();
        // $res     = $baseApi->queryParentList($subTicketIdArr);
        $baseApi = new PackRelation();
        $res     = $baseApi->queryParentList($subTicketIdArr);

        if ($res['code'] != 200) {
            throw new Exception($res['msg'], $res['code']);
        }

        $resList = [];
        $tmpData = (array)$res['data'];

        foreach ($tmpData as $item) {
            $resList[] = $item['parentTid'];
        }

        return $resList;
    }

    /**
     * 根据门票ID获取该门票的主票ID
     * <AUTHOR>
     * @date 2020/2/20
     *
     * @param  int  $tid  门票ID
     * @param  int  $parentId  父级票id
     *
     * @return array
     */
    public function getCostPriceByParentAndTid($parentId, $tid, $field = '')
    {
        $parentId = intval($parentId);
        $tid      = intval($tid);

        if (!$parentId || !$tid) {
            return [];
        }

        // $baseApi = new PackApi();
        $baseApi = new PackRelation();
        $res     = $baseApi->getInfoByParentAndSubId($parentId, $tid);

        if ($res['code'] != 200) {
            throw new Exception($res['msg'], $res['code']);
        }

        $resInfo = [];
        $tmpData = (array)$res['data'];

        if ($tmpData) {
            $resInfo = [
                'id'         => $tmpData['id'],
                'parent_tid' => $tmpData['parentTid'],
                'lid'        => $tmpData['lid'],
                'tid'        => $tmpData['tid'],
                'pid'        => $tmpData['pid'],
                'num'        => $tmpData['num'],
                'aid'        => $tmpData['aid'],
                'cost_price' => $tmpData['costPrice'],
            ];
        }

        return $resInfo;
    }

    /**
     * 保存套票子票数据
     * <AUTHOR>
     * @date 2020/2/20
     *
     * @param  array  $data
     *
     * @return bool
     */
    public function savePackageTickets(array $data)
    {
        if (!$data) {
            return false;
        }

        //数据校验
        $jsonStr  = json_encode($data);
        $checkRes = $this->checkPackData($jsonStr);
        if (!$checkRes) {
            return false;
        }

        //数据处理
        $packTicketArr = [];
        foreach ($data as $item) {
            $packTicketArr[] = [
                'parentTid' => intval($item['parent_tid']),
                'lid'       => intval($item['lid']),
                'tid'       => intval($item['tid']),
                'pid'       => intval($item['pid']),
                'aid'       => intval($item['aid']),
                'num'       => intval($item['num']),
                'costPrice' => intval($item['cost_price']),
            ];
        }

        // $baseApi = new PackApi();
        $baseApi = new PackRelation();
        $res     = $baseApi->addTickets($packTicketArr);

        if ($res['code'] == 500) {
            throw new Exception($res['msg'], $res['code']);
        }

        if ($res['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 删除子票
     * <AUTHOR>
     * @date 2020/2/20
     *
     * @param $id
     *
     * @return mixed
     */
    public function rmChildTicket($id)
    {
        $id = intval($id);
        if (!$id) {
            return false;
        }

        // $baseApi = new PackApi();
        $baseApi = new PackRelation();
        $res     = $baseApi->delTicketById($id);

        if ($res['code'] == 500) {
            throw new Exception($res['msg'], $res['code']);
        }

        if ($res['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 子票退票规则更新后,同时更新套票的退票规则
     * <AUTHOR>
     * @date 2020/2/5
     *
     * @param  int  $tid  门票ID
     * @param  int  $rule  退票规则
     * @param  int  $early_time
     *
     * @return bool
     */
    public function updateParentRefundRuleAttr($tid, $rule = 0, $early_time = 0)
    {
        $tid = intval($tid);
        if (!$tid) {
            return false;
        }

        //$baseApi = new PackApi();
        //$res     = $baseApi->updateParentRefundRule($tid);
        $baseApi = new PackRelation();
        $res     = $baseApi->updatePackageTicketRefundRule($tid);

        if ($res['code'] == 500) {
            throw new Exception($res['msg'], $res['code']);
        }

        if ($res['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 子票提前预定时间更新后,同时更新套票的提前预定时间属性
     * <AUTHOR>
     * @date 2020/2/5
     *
     * @param  int  $tid  门票ID
     * @param  int  $day  日期
     * @param  int  $hour
     *
     * @return bool
     */
    public function updateParentAdvanceAttr($tid, $day = 0, $hour = 0)
    {
        $tid = intval($tid);
        if (!$tid) {
            return false;
        }

        //$baseApi = new PackApi();
        //$res     = $baseApi->updateParentEarlyBuyDate($tid);
        $baseApi = new PackRelation();
        $res     = $baseApi->updatePackageTicketEarlyBuyDate($tid);

        if ($res['code'] == 500) {
            throw new Exception($res['msg'], $res['code']);
        }

        if ($res['code'] == 200) {
            return true;
        } else {
            return false;
        }
    }

    public function getCache($key = '')
    {
        $cacheLib = Cache::getInstance('redis');
        $cacheKey = $this->_getCacheKey($key);

        return $cacheLib->get($cacheKey);
    }

    public function setCache($json, $key = '')
    {
        $cacheLib = Cache::getInstance('redis');
        $cacheKey = $this->_getCacheKey($key);

        return $cacheLib->set($cacheKey, $json, '', 86400*3);
    }

    public function rmCache($key = '')
    {
        $cacheLib = Cache::getInstance('redis');
        $cacheKey = $this->_getCacheKey($key);

        return $cacheLib->rm($cacheKey);
    }

    /**
     * 通过主票ID和关键字搜索套票子票信息
     * <AUTHOR>
     * @date 2019/9/19
     *
     * @param  int|array  $tid  门票ID
     * @param  string  $keyword  关键字
     *
     * @return array
     */
    public function getSonTicketByTidAndKeyword($tid, $keyword = '')
    {
        $tid     = intval($tid);
        $keyword = strval($keyword);

        if (!$tid || empty($tid)) {
            return [];
        }

        //获取所有的子票
        $packBiz = new \Business\Product\PackTicket();
        $res     = $packBiz->getTickets($tid);

        if (!$res) {
            return [];
        }

        //获取门票数据
        $sonTidArr = array_column($res, 'tid');
        $javaApi                  = new \Business\CommodityCenter\Ticket();
        $condition['ticketTitle'] = $keyword;
        $ticketArr                = $javaApi->queryTicketByTicketIdAndLandIdAndApplyDidFilterTable('id,title,pid',
            '', '', '', false,
            $sonTidArr, null, null, null, $condition);
        $ticketList = array_column($ticketArr, 'ticket');

        $resList = [];
        foreach ($ticketList as $item) {
            $resList[] = [
                'id'    => $item['id'],
                'title' => $item['title'],
            ];
        }

        return $resList;
    }

    /**
     * 获取所有的子票
     * <AUTHOR>
     * @date 2020/2/4
     * @return array
     */
    public function getChildTickets($parentTid)
    {
        $parentTid = intval($parentTid);
        if (!$parentTid) {
            return [];
        }

        //获取所有的子票
        $packBiz = new \Business\Product\PackTicket();
        $res     = $packBiz->getTickets($parentTid);

        if (!$res) {
            return [];
        }

        $packArr = [];
        foreach ($res as $item) {
            $packArr[$item['tid']] = $item;
        }

        //获取门票数据
        $sonTidArr = array_column($res, 'tid');

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($sonTidArr,
            'id,pid,title,refund_rule,ddays,pay,order_start,order_end,delaytype,delaydays,if_verify,use_early_days,apply_did,buy_limit_low,buy_limit_up,chk_terminal_info,refund_before_early_time,refund_after_early_time,refund_audit,refund_num', '',
            'p_type,title,imgpath', 'dhour,buy_limit,buy_limit_date,buy_limit_num,tourist_info,ifprint_refund_rule');

        if (!$ticketArr) {
            return [];
        }

        $resList = [];
        foreach ($ticketArr as $ticketInfos) {
            $tid      = $ticketInfos['ticket']['id'];
            $packInfo = $packArr[$tid];

            // if (isset($ticketInfos['landBuyLimitRuleConfig'])) {
            //     $landBuyLimitRuleConfig = $ticketInfos['landBuyLimitRuleConfig'];
            // } else {
            //     $landBuyLimitRuleConfig = [
            //         //购票限制相关
            //         "buyer_limit_type"     => 0, // 取票人限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
            //         "buyer_order_days"     => 0, // 取票人限购订单笔数间隔天数
            //         "buyer_order_total"    => 0, // 取票人限购订单笔数
            //         "buyer_ticket_days"    => 0, // 取票人限购票笔数间隔天数
            //         "buyer_ticket_total"   => 0, // 取票人限购票类总数
            //         "tourist_limit_type"   => 0, // 游客限制类型 0：无 1：手机号，2：身份证，3：手机号+身份证
            //         "tourist_order_days"   => 0, // 游客限购订单笔数
            //         "tourist_order_total"  => 0, // 游客限购订单笔数间隔天数
            //         "tourist_ticket_days"  => 0, // 游客限购票笔数间隔天数
            //         "tourist_ticket_total" => 0, // 游客限购票类总数
            //         "id"                   => 0
            //     ];
            // }
            $showOrderLimitTime = '';
            if ($ticketInfos['product']['p_type'] == 'H' && isset($ticketInfos['ext']['show_order_limit_time'])) {
                $showOrderLimitTime = (new \Business\Product\Ticket())->getTimeLimitedSaleTag(['show_order_limit_time' => $ticketInfos['ext']['show_order_limit_time']]);
            }
            $resList[] = [
                'id'             => $packInfo['pid'], //这边的id在旧的封装里面取的是pid
                'parent_tid'     => $packInfo['pid'],
                'lid'            => $packInfo['lid'],
                'tid'            => $packInfo['tid'],
                'pid'            => $packInfo['pid'],
                'aid'            => $packInfo['aid'],
                'num'            => $packInfo['num'],
                'cost_price'     => $packInfo['cost_price'],
                'ltitle'         => $ticketInfos['land']['title'],
                'imgpath'        => $ticketInfos['land']['imgpath'],
                'ttitle'         => $ticketInfos['ticket']['title'],
                'refund_rule'    => $ticketInfos['ticket']['refund_rule'],
                'ddays'          => $ticketInfos['ticket']['ddays'],
                'pay'            => $ticketInfos['ticket']['pay'],
                'order_start'    => $ticketInfos['ticket']['order_start'],
                'order_end'      => $ticketInfos['ticket']['order_end'],
                'delaytype'      => $ticketInfos['ticket']['delaytype'],
                'delaydays'      => $ticketInfos['ticket']['delaydays'],
                'if_verify'      => $ticketInfos['ticket']['if_verify'],
                'use_early_days' => $ticketInfos['ticket']['use_early_days'],
                'apply_did'      => $ticketInfos['ticket']['apply_did'],
                'dhour'          => $ticketInfos['land_f']['dhour'],
                'p_type'         => $ticketInfos['land']['p_type'],
                'buy_limit'      => $ticketInfos['land_f']['buy_limit'],
                'buy_limit_date' => $ticketInfos['land_f']['buy_limit_date'],
                'buy_limit_num'  => $ticketInfos['land_f']['buy_limit_num'],
                'buy_limit_low'  => $ticketInfos['ticket']['buy_limit_low'],
                'buy_limit_up'   => $ticketInfos['ticket']['buy_limit_up'],
                'tourist_info'   => $ticketInfos['land_f']['tourist_info'],
                'check_terminal_info'   => $ticketInfos['ticket']['chk_terminal_info'],
                // 'land_buy_limit_rule_config'   => $landBuyLimitRuleConfig,
                'refund_before_early_time'  => (int)$ticketInfos['ext']['refund_before_early_time'] ?? 0,
                'refund_after_early_time'   => (int)$ticketInfos['ext']['refund_after_early_time'] ?? 0,
                'refund_audit'              => $ticketInfos['ticket']['refund_audit'] ?? 0,
                'refund_num'                => (int)$ticketInfos['ext']['refund_num'] ?? -1,
                'show_order_limit_time'     => $showOrderLimitTime,
                'ifprint_refund_rule'     => $ticketInfos['ext']['ifprint_refund_rule'] ?? 1,
            ];
        }

        return $resList;
    }

    /**
     * 获取套票的子票信息
     * <AUTHOR>
     * @date 2020/2/4
     * @return array
     */
    public function getSonTickets($parentTid)
    {
        $parentTid = intval($parentTid);
        if (!$parentTid) {
            return [];
        }

        //获取所有的子票
        $packBiz = new \Business\Product\PackTicket();
        $res     = $packBiz->getTickets($parentTid);

        if (!$res) {
            return [];
        }

        $packArr = [];
        foreach ($res as $item) {
            $packArr[$item['tid']] = $item;
        }

        //获取门票数据
        $sonTidArr = array_column($res, 'tid');

        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($sonTidArr, 'id,pid,refund_rule,pre_sale', 'p_name', 'p_type,venus_id');

        if (!$ticketArr) {
            return [];
        }
        $resList = [];
        foreach ($ticketArr as $ticketInfos) {
            $tid      = $ticketInfos['ticket']['id'];
            $packInfo = $packArr[$tid];
            $data     = [
                'pid'      => $packInfo['pid'],
                'aid'      => $packInfo['aid'],
                'lid'      => $packInfo['lid'],
                'title'    => $ticketInfos['product']['p_name'],
                'num'      => $packInfo['num'],
                'tid'      => $tid,
                'p_type'   => $ticketInfos['land']['p_type'],
                'venus_id' => $ticketInfos['land']['venus_id'],
                'zone_id' => $ticketInfos['land_f']['zone_id'],
                'tourist_info' => $ticketInfos['land_f']['tourist_info'],
                'refund_rule'  => $ticketInfos['ticket']['refund_rule'],
                'pre_sale'  => $ticketInfos['ticket']['pre_sale'],//是否期票 0否 1是
                'is_agreement_ticket'  => $ticketInfos['ext']['is_agreement_ticket'] ?? 0, //是否协议票 0否 1是
            ];
            //子票为门票 添加 rule_status more_credential_content more_credentials auto_sale_rule auto_sale_rule_status
            if ($ticketInfos['land']['p_type'] == 'A') {
                //填写更多信息游客取票人证件
                $data['more_credential_content'] = $ticketInfos['ext']['more_credentials'] ? json_decode($ticketInfos['ext']['more_credentials'],true)['content'] : [];
                $data['more_credentials']        = !empty($data['more_credential_content']) ? 1:0;
                //自动起售停售
                $data['auto_sale_rule']          = $ticketInfos['ext']['auto_sale_rule'] ? json_decode($ticketInfos['ext']['auto_sale_rule'],true) : [];
                //rule_status = 5 自动起售停售
                $data['rule_status'] = empty($data['auto_sale_rule']) ? 1 : 5;
                $data['auto_sale_rule_status'] = false;
                if ($data['auto_sale_rule']) {
                    $time = time();
                    if (strtotime($data['auto_sale_rule']['start']) > $time || strtotime($data['auto_sale_rule']['end']) < $time) {
                        $data['auto_sale_rule_status'] = false;
                    }else{
                        $data['auto_sale_rule_status'] = true;
                    }
                }
            }

            //子票是演出的需要获取子票对应的可售卖时间
            if ($ticketInfos['land']['p_type'] == 'H') {
                $ticketInfo['show_order_limit_time'] = $ticketInfos['ext']['show_order_limit_time'] ?? '';
                $data['show_order_limit_time'] = (new \Business\Product\Ticket())->getTimeLimitedSaleTag($ticketInfo);
            }

            // $field = 'p.id as pid,l.id as lid,p.p_name as title,pack.num,pack.tid,l.p_type,l.venus_id';
            $resList[] = $data;
        }

        return $resList;
    }

    /**
     * 获取套票有效验证（游玩）日期区间
     * <AUTHOR>
     * @date 2020/2/4
     *
     * @param  string  $playDate
     *
     * @return array
     */
    public function useDate($playDate = '', $parentTid = 0)
    {
        //获取所有的子票
        $childTickets = $this->getChildTickets($parentTid);

        $this->paymode = $childTickets[0]['pay']; // 支付方式
        $this->advance = $childTickets[0]['ddays']; // 提前购买天数
        $orderDate     = date('Y-m-d 00:00:00'); // 下单时间

        // 获取最大提前天数
        if (is_null($childTickets) || !$childTickets) {
            return [
                'sDate'   => '2015-04-02 00:00:00', // 有效开始时间
                'eDate'   => '2015-04-01 00:00:00', // 有效结束时间
                'oDate'   => '2015-04-01 00:00:00', // 开始下单时间
                'mDate'   => '2015-04-01 00:00:00', // 最大开始时间
                'section' => 0,
            ];
        }

        foreach ($childTickets as $key => $data) {
            if ($data['ddays'] > $this->advance) {
                $this->advance = $data['ddays'];
            }

            // 提前天数最大值
            $this->paymode            = $data['pay']; // 支付方式
            $this->paymode_continer[] = $data['pay'];
        }

        // 初始第一个子票信息
        $iniDate = $this->_effectiveDateSection($childTickets[0], $orderDate, $playDate);
        foreach ($childTickets as $key => $data) {
            $date_t = $this->_effectiveDateSection($data, $orderDate, $playDate);

            if ($key == 0) {
                $iniDate = $date_t;
            }

            // 结束时间都取最小值-- 2018-08-30需求   --- 252 到258 都是旧逻辑,有问题可以马上注释 261到268, 开放252到258
            if ($date_t['sDate'] < $iniDate['sDate']) {
                $iniDate['sDate'] = $date_t['sDate'];
            }

            // 结束时间都取最大值-- 2018-08-30需求
            if ($date_t['eDate'] > $iniDate['eDate']) {
                $iniDate['eDate'] = $date_t['eDate'];
            }

            if ($date_t['section'] == 1) {
                // 计算套票最大下单时间
                if ($iniDate['section'] == 1) {
                    $iniDate['oDate'] = $iniDate['oDate'] > $date_t['oDate'] ? $date_t['oDate'] : $iniDate['oDate'];
                    $iniDate['mDate'] = $iniDate['mDate'] > $date_t['mDate'] ? $date_t['mDate'] : $iniDate['mDate'];
                } else {
                    $iniDate['section'] = 1;
                    $iniDate['oDate']   = $date_t['oDate'];
                    $iniDate['mDate']   = $date_t['mDate'];
                }
            }
        }
        if ($iniDate['section'] == 1) {
            $iniDate['oDate'] = date('Y-m-d 23:59:59', (strtotime($iniDate['oDate']) - $this->advance * 86400));
        }
        $this->usedate = $iniDate;

        return $iniDate;
    }

    /**
     * 检查套票是否合法有效
     * <AUTHOR>
     * @date 2020/2/4
     *
     * @param  int  $parentTid
     *
     * @return bool
     */
    public function checkEffectivePack($parentTid = 0)
    {
        // 获取套票的有效时间段 如果开始时间大于结束时间，表示无效
        $useDate = $this->useDate('', $parentTid); // 获取时间交集
        if ($useDate['sDate'] > $useDate['eDate']) {
            $this->message[] = '套票的有效时间有误';

            return false;
        }

        // 所有支付方式都必须一直
        if (count(array_count_values($this->paymode_continer)) > 1) {
            $this->message[] = '支付方式存在不一致';

            return false;
        }

        return true;
    }

    /**
     * 检测套票数据是否合法
     *
     * @param  string  $jsonStr  json字符串 - [{"lid":"8264","pid":"14624","aid":"3385","tid":"17536","num":"1","cost_price":1}]﻿
     *
     * @return bool
     */
    public function checkPackData($jsonStr)
    {
        $arr_list       = json_decode($jsonStr, true);
        $limit_key_list = ['parent_tid', 'lid', 'pid', 'aid', 'tid', 'num', 'cost_price'];
        foreach ($arr_list as $item) {
            foreach ($item as $key => $val) {
                if (!in_array($key, $limit_key_list) || !is_numeric($val)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 处理票列表信息的演出数据
     * <AUTHOR>
     * @date 2021/4/9
     *
     * @param  array  $data  数据
     * @param  array  $showParams  参数
     *
     * @return mixed
     */
    public function handlePacketShowParams(array $data, array $showParams = [])
    {
        //演出参数为空，原样返回
        if (empty($showParams)) {
            return $data;
        }

        //验证存在的参数,都不存在，直接返回  is_show_bind 需要过滤， 其他不用
        if (empty($showParams['is_show_pack']) && empty($showParams['is_show_bind'])) {
            return $data;
        }
        //验证是不是有演出票
        $javaList = empty($data['data']['list']) ? [] : $data['data']['list'];
        if (empty($javaList)) {
            return $data;
        }
        //验证当前列表票信息是否包含演出
        $allLandType = array_column($javaList, 'landType');
        if (!in_array('H', $allLandType)) {
            return $data;
        }

        //这边演出增加入场时间，
        $showPack             = empty($showParams['is_show_pack']) ? 0 : $showParams['is_show_pack'];
        $showBind             = empty($showParams['is_show_bind']) ? 0 : $showParams['is_show_bind'];
        $javaList             = $this->_handleTicketsAddShowInfo($javaList, $showBind, $showPack);
        $data['data']['list'] = $javaList;

        return $data;
    }

    /**
     * 套票子票是演出票下单成功返回处理
     * <AUTHOR>
     * @date 2021/4/14
     *
     * @param  array  $packageShowInfo  子票演出信息
     * @param  array  $childList  子票信息
     * @param  array  $orderInfo  订单信息
     *
     * @return array
     */
    public function handlePaySuccessParams(array $packageShowInfo, array $childList, array $orderInfo)
    {
        if (empty($packageShowInfo)) {
            return [];
        }
        if (empty($orderInfo['begintime'])) {
            $date = date("Y-m-d");
        } else {
            $date = $orderInfo['begintime'];
        }
        $aid = $orderInfo['aid'];
        //初始化参数
        foreach ($packageShowInfo as $key => $value) {
            //返回格式数据
            $packageShowInfo[$key]['ticket_name'] = '';
            $packageShowInfo[$key]['item_name']   = '';
            $packageShowInfo[$key]['begin_time']  = '';
            $packageShowInfo[$key]['end_time']    = '';
            $packageShowInfo[$key]['round_name']  = '';
            $packageShowInfo[$key]['round_time']  = '';
        }
        //补充演出信息
        foreach ($packageShowInfo as $key => $value) {
            if (isset($childList[$key])) {
                $packageShowInfo[$key]['ticket_name'] = $childList[$key]['ticket_name'];
                $packageShowInfo[$key]['item_name']   = $childList[$key]['item_name'];
            } else {
                continue;
            }
            $vid   = isset($value['venusid']) ? $value['venusid'] : 0;
            $zone  = isset($value['zoneid']) ? $value['zoneid'] : 0;
            $round = isset($value['roundid']) ? $value['roundid'] : 0;
            if (!$vid || !$round) {
                continue;
            }
            //获取场次信息
            $showBiz = new \Business\Product\Show();
            $res     = $showBiz->getRoundList($vid, $date, $aid, $zone, false);
            $code    = $res['code'];
            //数据出错了
            if ($code == 0 && !empty($res['data'])) {
                continue;
            }
            $roundList             = $this->_handleShowInfoRoundList($res['data'], $zone);
            $roundListMap          = array_key($roundList, 'round_id');
            $roundInfo             = is_array($roundListMap[$round]) ? $roundListMap[$round] : [];
            $packageShowInfo[$key] = array_merge($packageShowInfo[$key], $roundInfo);
            //移除多余字段
            unset($packageShowInfo[$key]['storage']);
            unset($packageShowInfo[$key]['round_id']);
            unset($packageShowInfo[$key]['round_id']);
        }

        return $packageShowInfo;
    }

    /**
     * 处理套票子票演出数据
     * <AUTHOR>
     * @date 2021/4/9
     *
     * @param  array  $data
     *
     * @return array
     */
    public function handlePacketSonShowParams(array $data, $loginMemberId = 0)
    {
        //验证票信息
        $tickets = empty($data['ticket']) ? [] : $data['ticket'];
        if (empty($tickets)) {
            return $data;
        }
        //验证当前列表票信息是否包含演出
        $allLandType = array_column($tickets, 'p_type');
        if (!in_array('H', $allLandType)) {
            return $data;
        }
        $data['pack_show_channel'] = implode(',', $this->_getPackShowSaleChannel());
        $ticketIdArr               = array_column($tickets, 'tid');
        $ticketsListMap            = $this->_getShowExtInfo($ticketIdArr);

        // 开通小程序pro商家，不支持套票演出子票。玩聚商家支持
        $packShowWxxcxproSids = \load_config('pack_show_wxxcxpro_sids');
        if ((ENV == 'PRODUCTION') && (!$loginMemberId || in_array($loginMemberId, $packShowWxxcxproSids))) {
            $packShowChannels = explode(',', $data['pack_show_channel']);
            foreach ($packShowChannels as $k => $v) {
                // 关闭小程序
                if ($v == 20) {
                    unset($packShowChannels[$k]);
                }
            }
            $data['pack_show_channel'] = implode(',', $packShowChannels);
        }

        //数据处理
        foreach ($tickets as $key => $value) {
            //有效期调整为入场时间
            $entranceTime                         = isset($ticketsListMap[$value["tid"]]) ? $ticketsListMap[$value["tid"]]['entrance_time_limit'] : '';
            if (!empty($entranceTime)) {
                $tickets[$key]['entrance_time_limit'] = $this->_handleShowEntranceTimeLimit($entranceTime);
            }
        }

        $data['ticket'] = $tickets;

        return $data;
    }

    /**
     * 演出产品列表增加是否是套票子票字段
     * <AUTHOR>
     * @date 2021/4/12
     *
     * @param  array  $data  产品数据
     *
     * @return array
     * @throws
     */
    public function handleProductListSonShowParams(array $data)
    {
        //验证当前列表票信息是否包含演出
        $allLandType = array_column($data, 'type');
        if (!in_array('H', $allLandType)) {
            return $data;
        }
        //数据处理
        foreach ($data as $key => $value) {
            //演出景区才处理
            if (isset($value['type']) && $value['type'] == 'H') {
                $value['tickets'] = is_array($value['tickets']) ? $value['tickets'] : [];
                //景区票处理
                foreach ($value['tickets'] as $k => $v) {
                    //套票子票增加标识字段
                    $data[$key]['tickets'][$k]['showPack'] = $this->_existChildMainTid($v["ticketId"]);

                    ////捆绑票处理（是否是套票子票），防止特殊情况操作，前端可以过滤掉 TODO::暂时不使用
                    //$v['child_tickets'] = (isset($v['child_tickets']) && is_array($v['child_tickets'])) ? $v['child_tickets'] : [];
                    //foreach ($v['child_tickets'] as $j => $tmp) {
                    //    $data[$key]['tickets'][$k]['child_tickets'][$j]['showPack'] = $this->_existChildMainTid($tmp["tid"]);
                    //}
                }
            }
        }

        return $data;
    }

    /**
     * 下拉获取门票信息 处理演出套票信息
     * <AUTHOR>
     * @date 2021/4/28
     *
     * @param  array  $data
     *
     * @return array
     */
    public function handleProductTicketsSonShowParams(array $data)
    {
        if (empty($data)) {
            return [];
        }
        $tidArr = array_column($data, 'ticketId');
        if (empty($tidArr)) {
            return $data;
        }
        //通过票id获取景区信息
        $landModel   = new \Business\CommodityCenter\Land();
        $landInfoArr = $landModel->getLandInfoByArrTidToJava($tidArr);
        //验证是否有演出类型
        $checkShowType = array_column($landInfoArr, 'p_type');
        if (!in_array('H', $checkShowType)) {
            return $data;
        }

        $tickets = [];
        foreach ($landInfoArr as $tmp) {
            $tickets[$tmp['id']] = $tmp['p_type'];
        }

        //数据处理
        foreach ($data as $key => $value) {
            //演出景区才处理
            if (isset($tickets[$value['ticketId']]) && $tickets[$value['ticketId']] == 'H') {
                //票处理 增加标识字段
                $data[$key]['showPack'] = $this->_existChildMainTid($value['ticketId']);
            }
        }

        return $data;
    }

    /**
     * 打包子票时，演出票的验证(未捆绑有其他门票的演出票)
     * <AUTHOR>
     * @date 2021/4/13
     *
     * @param  string  $key
     *
     * @return array
     */
    public function checkShowBindForPackSonTicket($key)
    {
        //获取子票缓存
        $childInfo = $this->getCache($key);
        if (empty($childInfo)) {
            return $this->returnData(200, '');
        }

        $packData = json_decode($childInfo, true);
        $tidArr = [];
        $packData = $packData['package_data'];
        foreach ($packData as $tmp) {
            if ($tmp['num'] > 0) {
                $tidArr[] = $tmp['tid'];
            }
        }

        //这边不验证具体参数，为空直接返回true
        if (empty($tidArr)) {
            return $this->returnData(200, '');
        }
        //通过票id获取景区信息
        $landModel   = new \Business\CommodityCenter\Land();
        $landInfoArr = $landModel->getLandInfoByArrTidToJava($tidArr);
        //验证是否有演出类型
        $checkShowType = array_column($landInfoArr, 'p_type');
        if (!in_array('H', $checkShowType)) {
            return $this->returnData(200, '');
        }
        $tickets = [];
        foreach ($landInfoArr as $tmp) {
            $tickets[] = [
                'tid'      => $tmp['id'],
                'type'     => $tmp['p_type'],
                'pre_sale' => $tmp['pre_sale'],
                'title'    => $tmp['title'],
            ];
        }

        foreach ($tickets as $val) {
            //演出景区才处理
            if (isset($val['type']) && $val['type'] == 'H') {
                if ($val['pre_sale'] == 1) {
                    return $this->returnData(400, '子票：（'. $val['title'] .'）；打包演出类型作为子票，仅支持需选游玩日期');
                }
                $bindTickets = $this->_existBindTickets($val['tid']);
                //存在捆绑票, 返回错误
                if ($bindTickets) {
                    return $this->returnData(400, '打包子票只允许未捆绑有其他门票的演出票');
                }
            }
        }

        return $this->returnData(200, '');
    }

    /**
     * 演出捆绑子票，已是套票子票的不允许被捆绑
     * <AUTHOR>
     * @date 2021/4/13
     *
     * @param  array  $data
     *
     * @return array
     */
    public function checkPackSonTicketForShowBind(array $data)
    {
        if (empty($data)) {
            return $this->returnData(200, '');
        }
        $tidArr = [];
        foreach ($data as $tmp) {
            if (isset($tmp['p_type']) && $tmp['p_type'] == 'H') {
                $tidArr[] = $tmp['tid'];
            }
        }

        //已经是子票的不让绑定给演出票
        $showPack = $this->_existChildMainTid($tidArr);
        if ($showPack) {
            return $this->returnData(400, '子票参数错误：套票子票不允许再被捆绑');
        }

        return $this->returnData(200, '');
    }

    /**
     * 处理演出票添加参数
     * <AUTHOR>
     * @date 2021/4/9
     *
     * @param  array  $data 原数据
     * @param  int  $showBind  是否过滤演出绑定票
     * @param  int  $showPack 是否标记已是套票子票
     *
     * @return array
     * @throws
     */
    private function _handleTicketsAddShowInfo(array $data, int $showBind = 0, int $showPack = 0)
    {
        if (!$showBind && !$showPack) {
            return $data;
        }

        $landArr = [];
        foreach ($data as $key => $value) {
            //获取全部景区
            if (isset($value['landType']) && $value['landType'] == 'H') {
                $landArr[] = $value['landId'];
            }
        }

        //演出入场时间获取
        $ticketsListMap = $this->_getShowExtInfo(null, $landArr);

        //数据处理
        foreach ($data as $key => $value) {
            //演出景区才处理
            if (isset($value['landType']) && $value['landType'] == 'H') {
                $value['tickets'] = is_array($value['tickets']) ? $value['tickets'] : [];
                //景区票处理
                foreach ($value['tickets'] as $k => $v) {
                    //有捆绑就移除
                    if ($showBind != 0) {
                        $bindTickets = $this->_existBindTickets($v["ticketId"]);
                        //存在捆绑票移除
                        if ($bindTickets) {
                            unset($data[$key]['tickets'][$k]);
                            continue;
                        }
                    }
                    //套票子票增加标识字段
                    if ($showPack != 0) {
                        //已经是子票的不让成为主票
                        $data[$key]['tickets'][$k]['showPack'] = $this->_existChildMainTid($v["ticketId"]);
                    }
                    //有效期调整为入场时间
                    $entranceTime = isset($ticketsListMap[$v["ticketId"]]) ? $ticketsListMap[$v["ticketId"]]['entrance_time_limit'] : '';
                    $data[$key]['tickets'][$k]['validTag'] = $this->_handleShowEntranceTimeLimit($entranceTime);
                }
                //重新处理下排序
                $data[$key]['tickets'] = array_merge($data[$key]['tickets']);

                //票数调整
                $data[$key]['totalTicket'] = is_array($data[$key]['tickets']) ? count($data[$key]['tickets']) : 0;
            }
        }

        return $data;
    }

    /**
     * 是否存在套票主票
     * <AUTHOR>
     * @date 2021/4/12
     *
     * @param  int|array  $tid  子票id
     *
     * @return int 0否  1是
     * @throws
     */
    private function _existChildMainTid($tid)
    {
        $isShowPack = 0;
        if (!$tid) {
            return $isShowPack;
        }
        $parentTids = $this->getParentsByTid($tid);
        if (!empty($parentTids)) {
            //验证是否是套票，而不是捆绑票 (捆绑票主票类型是H)
            $landModel   = new \Business\CommodityCenter\Land();
            $landInfoArr = $landModel->getLandInfoByArrTidToJava($parentTids);
            //验证是否有套票类型
            $checkShowType = array_column($landInfoArr, 'p_type');
            if (in_array('F', $checkShowType)) {
                return $isShowPack = 1;
            }
        }

        return $isShowPack;
    }

    /**
     * 演出票是否捆绑其他票
     * <AUTHOR>
     * @date 2021/4/12
     *
     * @param  int  $tid 票id
     *
     * @return int  0否1是
     */
    private function _existBindTickets(int $tid)
    {
        $isShowBind = 0;
        if (!$tid) {
            return $isShowBind;
        }
        $PerformanceModel = new \Business\Product\Performance();
        $res = $PerformanceModel->getBindTickets($tid);
        if (!empty($res)) {
            $isShowBind = 1;
        }

        return $isShowBind;
    }

    /**
     * 演出子票入场时间处理
     * <AUTHOR>
     * @date 2021/4/9
     *
     * @param  string  $entranceTime
     *
     * @return string
     */
    private function _handleShowEntranceTimeLimit(string $entranceTime)
    {
        if (empty($entranceTime)) {
            return '开演前60分钟可入场';
        } else {
            $entranceTime = json_decode($entranceTime, true);
            return '开演前' . $entranceTime['s'] . '分钟可入场';
        }
    }

    /**
     * 获取演出独有的信息
     * <AUTHOR>
     * @date 2021/4/9
     *
     * @param  array|null  $ticketArr
     * @param  array|null  $landArr
     *
     * @return array
     */
    private function _getShowExtInfo(array $ticketArr = null, array $landArr = null)
    {
        if (empty($ticketArr) && empty($landArr)) {
            return [];
        }
        $javaApi          = new \Business\CommodityCenter\LandF();
        $ticketsListMap      = [];
        $landList = $javaApi->queryLandFByLandIdAndTicketIdAndProductId(
            $ticketArr,
            $landArr,
            null,
            'tid, entrance_time_limit, p_type'
        );
        $landList = is_array($landList) ? $landList : [];
        foreach ($landList as $tmp) {
            //获取入场时间
            $ticketsListMap[$tmp['tid']] = [
                'entrance_time_limit' => $tmp['entrance_time_limit'],
            ];
        }

        return $ticketsListMap;
    }

    /**
     * 获取套票包含演出限制销售渠道
     * <AUTHOR>
     * @date 2021/4/12
     *
     * @param  bool  $isBackId  是否只返回渠道id
     *
     * @return array
     */
    private function _getPackShowSaleChannel(bool $isBackId = true)
    {
        $channelMap = \load_config('pack_show_sale_channel');
        $data       = [];
        if ($isBackId) {
            return array_keys($channelMap);
        }
        foreach ($channelMap as $idx => $name) {
            $data[] = [
                'id'   => $idx,
                'name' => $name,
            ];
        }

        return $data;
    }

    /**
     * 处理场次信息
     * <AUTHOR>
     * @date 2021/4/12
     *
     * @param  int  $zoneId  分区id
     * @param  array  $data  场次库存信息
     *
     * @return array
     */
    private function _handleShowInfoRoundList(array $data, int $zoneId = 0)
    {
        if (empty($data)) {
            return [];
        }
        $roundList = [];
        $showBiz   = new \Business\PftShow\Show();
        foreach ($data as $val) {
            $roundTimeRes = $showBiz->getRoundTime($val['round_mode'], $val['bt'], $val['et']);
            $roundTime    = '';
            if ($roundTimeRes['code'] == 200) {
                $roundTime = $roundTimeRes['data'];
            }
            $storage     = isset($val['area_storage'][$zoneId]) ? $val['area_storage'][$zoneId] : 0;
            $roundList[] = [
                'round_id'   => intval($val['id']),
                'round_name' => $val['round_name'],
                'begin_time' => $val['bt'],
                'end_time'   => $val['et'],
                'storage'    => empty($zoneId) ? -1 : $storage,
                'round_time' => $roundTime,
            ];
        }

        return $roundList;
    }

    /**
     * 获取有效期数据
     * <AUTHOR>
     * @date 2020/2/4
     *
     * @param  array  $data
     * @param  string  $orderDate
     * @param  string  $playDate
     *
     * @return array
     */
    private function _effectiveDateSection(array $data, $orderDate = '', $playDate = '')
    {
        $arr = [];
        if ($orderDate == '') {
            $orderDate = date('Y-m-d 00:00:00');
        }

        // 下单时间
        if ($playDate == '') {
            $playDate = date('Y-m-d 00:00:00');
        }

        // 游玩时间
        $_tm = 1420041600;
        $_os = strtotime($data['order_start']);
        $_oe = strtotime($data['order_end']);

        if ($_os > $_tm && $_oe > $_tm) {
            // 提前天数
            if ($data['ddays'] > 0) {
                $date_tmp = date('Y-m-d 00:00:00', time() + $data['ddays'] * 86400);
            }
            // 这里要用最小的有效期
            if (!isset($date_tmp)) {
                $date_tmp = $data['order_start'];
            }
            $arr['sDate']   = ($date_tmp < $data['order_start']) ? $date_tmp : $data['order_start'];
            $arr['eDate']   = $data['order_end'];
            $arr['oDate']   = $data['order_end'];
            $arr['mDate']   = $data['order_end'];
            $arr['section'] = 1;
        } elseif ($_os <= 0 && $_oe > $_tm) {
            // 只有结束时间
            $arr['eDate']   = date('Y-m-d 00:00:00');
            $arr['eDate']   = $data['order_end'];
            $arr['oDate']   = $data['order_end'];
            $arr['mDate']   = $data['order_end'];
            $arr['section'] = 1;
        } else {
            if ($data['delaytype'] == 0) {
                // 游玩时间，这个逻辑比较奇葩
                // 不知道为什么设置了需要提前几天预定，然后有效期还要加上提前的几天
                // if ($data['delaydays'] == 0) {
                //     $data['delaydays'] = $this->advance;
                // }

                // 当天有效的获取最大提前的那一天
                $arr['sDate'] = $playDate;
                if (!empty($data['use_early_days'])) {
                    $arr['sDate'] = date('Y-m-d 00:00:00', strtotime($playDate) - $data['use_early_days'] * 86400);
                }
                $arr['eDate'] = date('Y-m-d 23:59:59', strtotime($playDate) + $data['delaydays'] * 86400);
            } else {
                $arr['sDate'] = date('Y-m-d 00:00:00', strtotime($orderDate) + $data['ddays'] * 86400);
                $arr['eDate'] = date('Y-m-d 23:59:59',
                    strtotime($arr['sDate']) + ($data['delaydays'] - $data['ddays']) * 86400);
            }
            $arr['section'] = 0;
            $arr['oDate']   = $arr['eDate'];
        }

        return $arr;
    }

    /**
     * 获取统一的缓存key
     * <AUTHOR>
     * @date 2020/2/4
     *
     * @param  int  $memberId  登录用户ID
     *
     * @return string
     */
    private function _getCacheKey($memberId)
    {
        $cacheKey = "pkg:{$memberId}";

        return $cacheKey;
    }

    /**
     * 检测套票数据是否合法
     *
     * @param  string  $jsonStr  json字符串 - ["package_data":{"lid":"8264","pid":"14624","aid":"3385","tid":"17536","num":"1","cost_price":1},"del_ticket":{"lid":"0","pid":"0","aid":"0","tid":"17536","num":"-1","cost_price":0}]﻿
     *
     * @return bool
     */
    public function checkPackDataV2($jsonStr)
    {
        $arr_list       = json_decode($jsonStr, true);
        $limit_key_list = ['parent_tid', 'lid', 'pid', 'aid', 'tid', 'num', 'cost_price'];
        foreach ($arr_list as $item) {
            if (!is_array($item)) {
                return false;
            }
            foreach ($item as $son){
                if (!is_array($son)) {
                    return false;
                }
                foreach ($son as $key => $val) {
                    if (!in_array($key, $limit_key_list) || !is_numeric($val)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }
}
