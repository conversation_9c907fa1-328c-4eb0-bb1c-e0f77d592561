<?php

/**
 * 下游订单
 *
 * Class OrderFail
 * @package Business\Ota\DownstreamOrder
 */

namespace Business\Ota\DownstreamOrder;

use Business\CommodityCenter\Ticket;
use Business\Member\Member;
use Business\Order\OrderList;
use Model\Order\OrderTools;
use Business\OtaApi\OtaBase;
use Model\Ota\ApiUserNoticeLogModel;

class Order extends OtaBase
{
    const __OTA_GET_VERIFY_CHANGE_LOG__ = 'Order/OrderNotice/getVerifyChangeLog';

    /**
     * 获取订单信息
     *
     * @param $sid
     * @param $ordernum
     *
     * @return array
     * Author : liucm
     * Date : 2022/1/4
     */
    public function getOrderInfo($sid, $ordernum)
    {
        $orderNumArr      = [$ordernum];
        $queryParams      = [$orderNumArr];
        $mainOrderInfoArr = \Business\JavaApi\Order\Query\Container::query('orderQuery', 'completeOrder',
            $queryParams);

        if ($mainOrderInfoArr['code'] != 200 || empty($mainOrderInfoArr['data'])) {
            return $this->returnData(204, '无此订单');
        }
        $orderInfo = $mainOrderInfoArr['data'][0];

        if (empty($orderInfo['remotenum']) || $orderInfo['remotenum'] == "-") {
            return $this->returnData(204, '该订单未对接下游系统');
        }

        $memberId    = $orderInfo['member'];
        $memberBuz   = new Member();
        $userInfo    = $memberBuz->getInfo($memberId);
        $system_name = $userInfo['dname'];

        //还需判断是否是子票
        $tmpOrderNum = $this->getParentOrderNum($ordernum);
        if ($tmpOrderNum != $ordernum) {
            return $this->returnData(204, '套票子票无法操作');
        }

        //是否OTA订单 1 = 是 , 2 = 否
        $isOtaOrder     = 2;
        $systemService  = new \Business\Ota\DownstreamSystemConfig();
        $downSysListRes = $systemService->getDownListConfigList();
        if ($downSysListRes['code'] == 200) {
            $downSysList = $downSysListRes['data'] ?? '';
            $systemArr   = $systemService->getDownListConfigListArr();
            if (!empty($downSysList)) {
                $downSysAids = array_column($downSysList, 'member_id');
                //分销商是OTA则系统名称显示OTA
                if (in_array($memberId, $downSysAids)) {
                    $system_name = $systemArr[$memberId]['sys_name'];
                    $isOtaOrder  = 1;
                }
            }
        }

        //type = 1 是分销商查询,判断供应商是不是自己。type =2，是OTA自己查询的查询，判断购买者是不是自己。
        if ($sid != 1) {
            if ($isOtaOrder == 1) {
                if ($orderInfo['aid'] != $sid && $orderInfo['member'] != $sid) {
                    return $this->returnData(1000, '非订单所有者', []);
                }
            } else {
                if ($orderInfo['member'] != $sid) {
                    return $this->returnData(1000, '非订单所有者', []);
                }
            }
        }
        $tid        = $orderInfo['tid'];
        $ticketInfo = (new Ticket())->queryTicketInfoById($tid, 'title,id', 'p_name,p_type', 'addtime,title',
            'lid,title');

        //有赞账号判断  ID:768763     杭州有赞科技有限公司（account： 217091）
        //             ID:********  有赞对接专用账号（account: ********）
        $isYouzanOrder = in_array($memberId, [768763, ********]) ? true : false;

        if (ENV != 'PRODUCTION') {
            $isYouzanOrder = in_array($memberId, [28227, 113]) ? true : false;
        }

        $isDouYinOrder = false;
        if ($system_name == '抖音团购') {
            $isDouYinOrder = true;
        }

        $isKuaiShouLocalOrder = false;
        if ($system_name == '快手本地生活') {
            $isKuaiShouLocalOrder = true;
        }

        $isDouYinLaiKeOrder = false;
        if ($system_name == '抖音来客') {
            $isDouYinLaiKeOrder = true;
        }

        $isMeiTuanFunOrder = false;
        if ($system_name == '美团玩乐') {
            $isMeiTuanFunOrder = true;
        }

        $returnData = [
            'order_time'      => $orderInfo['ordertime'] ?? '',
            //下单时间
            'pft_order_no'    => $ordernum,
            //票付通订单号
            'down_order_no'   => $orderInfo['remotenum'],
            //下游订单号
            'system_name'     => $system_name,
            //卖家名称
            'order_status'    => $orderInfo['status'],
            //订单状态 0=未使用,1=已使用,2=已过期,3=被取消,4=待确认(酒店)，待收货(特产),5=被终端修改,6=被终端撤销,7=部分使用,8=订单完结,9=被删除
            'pay_status'      => $orderInfo['pay_status'],
            //支付状态
            'quantity'        => $orderInfo['tnum'] ?? 0,
            //下单数量
            'land_name'       => $ticketInfo['ticket']['title'] ?? '',//景区名称
            //门票名称
            'ticket_name'     => $ticketInfo['land']['title'] ?? '',

            'is_taobao_order' => $orderInfo['ordermode'] == 17 ? true : false,

            'is_youzan_order' => $isYouzanOrder,//账号名称

            'is_douyin_order' => $isDouYinOrder,//是否抖音团购订单

            'is_kuaishoulocal_order' => $isKuaiShouLocalOrder, // 是否是快手本地生活订单

            'is_douyinlaike_order'   => $isDouYinLaiKeOrder,//是否抖音来客订单

            'is_meituanfun_order'    => $isMeiTuanFunOrder,//是否美团玩乐订单

            'mid' => $memberId,//下单人id
        ];

        return $this->returnData(200, 'success', $returnData);
    }

    public function getVerifyChangeLog($pftOrderNo, $memberInfo)
    {
        $res = $this->call(self::__OTA_GET_VERIFY_CHANGE_LOG__, [
            'order_no'    => $pftOrderNo,
            'member_info' => $memberInfo,
        ]);

        return $this->returnData($res['code'], $res['msg'], $res['data']);
    }

    public function getParentOrderNum($orderNum)
    {
        if (empty($orderNum)) {
            return '';
        }
        //判断当前订单号是否属于套票的子票  如果是子票 需要获取出主票的订单号用来绑定人脸
        //主票订单号
        $parentOrderNum  = $orderNum;
        $parentOrderInfo = (new OrderTools('slave'))->getPackOrdersInfoByOrderId($parentOrderNum, 'pack_order');
        if (!empty($parentOrderInfo[0]['pack_order'])) {
            if ($parentOrderInfo[0]['pack_order'] > 1) {
                $parentOrderNum = $parentOrderInfo[0]['pack_order'];
            }
        }

        return $parentOrderNum;
    }
}