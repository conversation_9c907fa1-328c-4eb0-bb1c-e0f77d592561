<?php
/**
 * 开放接口订单
 * @desc 该方法统一请求ota下openapi的方法
 * <AUTHOR>
 * @date   2019-02
 *
 */

namespace Business\Ota\Open;

use Business\Base;
use Business\CommodityCenter\Ticket;
use Business\Member\Member;
use Business\Order\ThirdSystem;
use Library\Constants\OrderConst;
use Library\Container;
use Library\Tools\Helpers;
use Library\Resque\Queue;

class Order extends Base
{
    private static $dingTalkRobot = '3d99ad78aa175fa9230d67dde46fdb672edcea14a6bb225c659963a52a9658ee';

    private $_newMode = false;

    private $_newModeSysList = [189, 191, 163, 165, 172, 173, 168, 169, 174, 175, 192, 177, 179, 184, 186, 188];

    /**
     * 调用ota下验证请求地址是否存活
     *
     * @param  string  $url  对方地址   Inside的时候为通过开放接口来转发开发对方的接口上
     *
     */
    public function checkAlive($url)
    {
        if (empty($url)) {
            return self::returnData(203, '请求地址有误');
        }
        if ($url == 'Inside') {
            return self::returnData(200, '内部转发配置成功');
        }
        // 调用 ota 下resetful接口方法
        $result = Helpers::callOpenApiSystemJsonRpc('ServerConfig.checkAlive', ['url' => $url]);

        if ($result['code'] != 200) {
            return self::returnData(203, $result['msg']);
        }

        return self::returnData(200, $result['msg']);
    }

    /**
     * 调用ota下推送订单
     *
     * @param  array 传值参数
     *
     * @return array
     *
     */
    public function pushOrder($paramArr)
    {
        $contactName     = isset($paramArr['TouristName']) ? $paramArr['TouristName'] : '';
        $contactPhone    = isset($paramArr['TouristTel']) ? $paramArr['TouristTel'] : '';
        $contactIdCard   = isset($paramArr['ContactIdCard']) ? $paramArr['ContactIdCard'] : '';
        $idCard          = isset($paramArr['IdCard']) ? $paramArr['IdCard'] : '';
        $touristNameList = isset($paramArr['TouristNameList']) ? $paramArr['TouristNameList'] : '';
        $idCardList      = isset($paramArr['IdCardList']) ? $paramArr['IdCardList'] : '';
        $mobileList      = isset($paramArr['MobileList']) ? $paramArr['MobileList'] : '';
        $remoteNum       = isset($paramArr['remotenum']) ? $paramArr['remotenum'] : '';
        $pftPriceJs      = isset($paramArr['PftPriceJs']) ? $paramArr['PftPriceJs'] : '';   // 结算价格
        $pftPriceGh      = isset($paramArr['PftPriceGh']) ? $paramArr['PftPriceGh'] : '';   // 供货价
        $pftPriceLs      = isset($paramArr['PftPriceLs']) ? $paramArr['PftPriceLs'] : '';   // 零售价
        $pftTimeBegin    = isset($paramArr['PftTimeBegin']) ? $paramArr['PftTimeBegin'] : '';
        $pftTimeEnd      = isset($paramArr['PftTimeEnd']) ? $paramArr['PftTimeEnd'] : '';
        $oneLevelSeller  = isset($paramArr['OneLevelSeller']) ? $paramArr['OneLevelSeller'] : '';
        $lastLevelSeller = isset($paramArr['LastLevelSeller']) ? $paramArr['LastLevelSeller'] : '';
        $sectionId       = isset($paramArr['sectionId']) ? $paramArr['sectionId'] : '';

        // 处理游客信息
        $touristNameIDArr = $this->_handleRealNameInfo($contactName, $idCard, $contactPhone, $touristNameList,
            $idCardList, $mobileList, false);

        $touristNameStr = '';
        $touristIdStr   = '';
        $mobileStr      = '';
        if (!empty($touristNameIDArr)) {
            $touristNameArr = array_column($touristNameIDArr, 'name');
            $touristIdArr   = array_column($touristNameIDArr, 'idcard');
            $mobileArr      = array_column($touristNameIDArr, 'mobile');
            $touristNameStr = implode(',', $touristNameArr);
            $touristIdStr   = implode(',', $touristIdArr);
            $mobileStr      = implode(',', $mobileArr);
        }

        $orderNum  = isset($paramArr['PftOrderSn']) ? $paramArr['PftOrderSn'] : '';
        $ticketNum = isset($paramArr['Tnum']) ? $paramArr['Tnum'] : '';
        $playTime  = isset($paramArr['PftPlayTime']) ? $paramArr['PftPlayTime'] : '';
        $fid       = isset($paramArr['Fid']) ? $paramArr['Fid'] : '';
        $aid       = isset($paramArr['ApplyId']) ? $paramArr['ApplyId'] : '';
        $tid       = isset($paramArr['TicketID']) ? $paramArr['TicketID'] : '';
        $lid       = isset($paramArr['LandId']) ? $paramArr['LandId'] : '';
        $payStatus = isset($paramArr['PayStatus']) ? $paramArr['PayStatus'] : '';
        $uuId      = isset($paramArr['PftUUid']) ? $paramArr['PftUUid'] : '';
        $pftCode   = isset($paramArr['PftCode']) ? $this->_decodePftCode($paramArr['PftCode']) : '';

        if ($payStatus == '0') {
            return self::returnData(1000, '不支持到付订单', []);
        }

        // 调用 ota 下resetful接口方法
        $data = [
            'contactName'     => $contactName,
            'contactPhone'    => $contactPhone,
            'contactIdCard'   => $contactIdCard,
            'touristName'     => $touristNameStr,
            'touristIDCard'   => $touristIdStr,
            'touristPhone'    => $mobileStr,
            'orderNum'        => $orderNum,
            'payStatus'       => $payStatus,
            'ticketNum'       => $ticketNum,
            'playTime'        => $playTime,
            'lid'             => $lid,
            'tid'             => $tid,
            'applyDid'        => $aid,
            'fid'             => $fid,
            'uuid'            => $uuId,
            'pftCode'         => $pftCode,
            'remoteNum'       => $remoteNum,
            'beginTime'       => $pftTimeBegin,
            'endTime'         => $pftTimeEnd,
            'settlePrice'     => $pftPriceJs,
            'retailPrice'     => $pftPriceLs,
            'costPrice'       => $pftPriceGh,
            'oneLevelSeller'  => $oneLevelSeller,
            'lastLevelSeller' => $lastLevelSeller,
            'sectionId'       => $sectionId,
        ];

        $version = $this->_getOpenApiVersion($lid);
        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Order.PushOrder', $data, $version);

        if ($result['code'] != 200) {
            // 如果不是业务上错误则记录数据准备重试请求
            if ($result['code'] != 1000) {
                // 网络型错误
                $retryDataArr = ['action' => 'OpenApi.Order.PushOrder', 'data' => $data, 'version' => $version];
                $notifyModel  = new \Model\Ota\Notify();
                $notifyModel->addNotify($orderNum, 998, 7, $retryDataArr);
            }

            return self::returnData($result['code'], $result['msg'], []);
        }

        $returnDataArr = [];
        if (!empty($result['data'])) {
            $returnDataArr = $result['data'];
        }

        return self::returnData(200, 'success', $returnDataArr);
    }

    /**
     * 调用ota下推送支付
     *
     * @param  array 传值参数
     *
     * @return array
     *
     */
    public function pushPay($paramArr)
    {
        $orderNum = isset($paramArr['orderNum']) ? $paramArr['orderNum'] : '';
        $aid      = isset($paramArr['ApplyId']) ? $paramArr['ApplyId'] : '';
        $pftCode  = isset($paramArr['pftCode']) ? $paramArr['pftCode'] : '';
        $tid      = isset($paramArr['TicketID']) ? $paramArr['TicketID'] : '';
        $lid      = isset($paramArr['LandId']) ? $paramArr['LandId'] : '';

        // 调用 ota 下resetful接口方法
        $data = [
            'orderNum' => $orderNum,
            'applyDid' => $aid,
        ];

        $version = $this->_getOpenApiVersion($lid);
        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Order.PushPay', $data, $version);

        if ($result['code'] != 200) {
            // 如果不是业务上错误则记录数据准备重试请求
            if ($result['code'] != 1000) {
                // 网络型错误
                $retryDaraArr = ['action' => 'OpenApi.Order.PushPay', 'data' => $data, 'version' => $version];
                $notifyModel  = new \Model\Ota\Notify();
                $notifyModel->addNotify($orderNum, 999, 7, $retryDaraArr, false);
            } else {
                // 失败处理
                $this->handleFailOrder($orderNum, $result['code'], $result['msg'], true);
            }

            return self::returnData($result['code'], $result['msg'], []);
        }

        //TODO::支付成功之后调用产品线的后续业务逻辑

        return self::returnData(200, 'success');
    }

    /**
     * 调用ota下推送支付
     *
     * @param  array 传值参数
     *
     * @return array
     *
     */
    public function pushResendMsg($paramArr)
    {
        $orderNum = isset($paramArr['Ordern']) ? $paramArr['Ordern'] : '';
        $lid      = isset($paramArr['LandId']) ? $paramArr['LandId'] : '';
        $aid      = isset($paramArr['ApplyId']) ? $paramArr['ApplyId'] : '';

        // 调用 ota 下resetful接口方法
        $data = [
            'orderNum' => $orderNum,
            'applyDid' => $aid,
        ];

        $version = $this->_getOpenApiVersion($lid);
        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Order.PushResendMsg', $data, $version);

        if ($result['code'] != 200) {
            return self::returnData($result['code'], $result['msg'], []);
        }

        return self::returnData(200, 'success');
    }

    /**
     * 在线支付成功后进行一次订单推送和支付推送
     *
     * @param  string  $orderNum  票付通订单号
     * @param  int  $lid  景点id
     * @param  int  $tid  门票id
     * @param  int  $fid  分销商id
     * @param  int  $aid  供应商id
     * @param  int  $tnum  购买数量
     * @param  string  $pftTimeBegin  分时开始时间
     * @param  string  $pftTimeEnd  分时结束时间
     *
     */
    public function afterPay2OrderAndPay($orderNum, $lid, $tid, $fid, $aid, $tnum, $pftTimeBegin = '', $pftTimeEnd = '')
    {
        $orderModel = new \Model\Order\OrderHandler();

        $orderInfoArr = $orderModel->getOrderInfo($orderNum,
            'ordernum, playtime, ordername, ordertel, remotenum, code, personid, tprice', 'series');

        // 获取门票绑定三方信息
        $otaProductBiz      = new \Business\Ota\Product();
        $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($tid);
        if (empty($thirdTicketConfArr)) {
            // 记录日志
        }

        $sourceT = $thirdTicketConfArr['sourceT'];
        $uuid    = $thirdTicketConfArr['uuid'];

        //获取身份证信息
        $touristArr      = $orderModel->getOrderTouristInfo($orderNum);
        $idCardList      = [];
        $touristNameList = [];
        $mobileList      = [];
        foreach ($touristArr as $item) {
            $idCardList[]      = $item['idcard'];
            $touristNameList[] = $item['tourist'];
            $mobileList[]      = $item['mobile'];
        }

        //$priceDate = $orderInfoArr['playtime'];
        //$commodityTicketBiz = new \Business\CommodityCenter\Ticket();
        //$javaRes            = $commodityTicketBiz->queryTicketInfoById($tid, 'id,pre_sale');
        //if (!empty($javaRes['ticket']['pre_sale']) && $javaRes['ticket']['pre_sale'] == 1) {
        //    $priceDate = date("Y-m-d");
        //}

        //$costPrice = 0;
        //$priceApi  = new \Business\JavaApi\Ticket\Price();
        //$priceList = $priceApi->batchBasePrice([(int)$tid], $priceDate);
        //if ($priceList['code'] == 200) {
        //    // 获取成本价格
        //    $costPrice = $priceList['data'][0]['costPrice'];
        //}

        if (!empty($orderInfoArr['series']) && is_array(unserialize($orderInfoArr['series']))) {
            //演出场次
            $sectionId = unserialize($orderInfoArr['series'])[1];
        }

        //请求参数
        $reqOrderData = [
            'PftOrderSn'      => $orderNum,                 //票付通订单号
            'Ordern'          => $orderNum,                 //票付通订单号，冗余的
            'Ltitle'          => '', //景区名称
            'Ttitle'          => '', //门票名称
            'PftUUid'         => $uuid,    //uuid
            //'PftPriceJs'      => $costPrice, //结算价
            //'PftPriceGh'      => $costPrice, //供货价
            //'PftPriceLs'      => $orderInfoArr['tprice'], //零售价
            'PftPriceJs'      => $orderInfoArr['tprice'], //结算价
            'PftPriceGh'      => $orderInfoArr['aprice'], //供货价
            'PftPriceLs'      => $orderInfoArr['lprice'], //零售价
            'PftPlayTime'     => $orderInfoArr['playtime'], //游玩时间
            'Tnum'            => $tnum,
            'Fid'             => $fid,
            'PayStatus'       => 1,
            'TicketID'        => $tid,
            'TouristName'     => $orderInfoArr['ordername'],
            'TouristTel'      => $orderInfoArr['ordertel'],
            'IdCard'          => $orderInfoArr['personid'],
            'remotenum'       => $orderInfoArr['remotenum'], //ota请求订单号作为第三方系统临时订单号请求
            'LandId'          => $lid, //景点ID
            'ApplyId'         => $aid, //原始供应商
            'TouristNameList' => $touristNameList ? trim(implode(',', $touristNameList), ',') : '', //所有身份证姓名
            'IdCardList'      => $idCardList ? trim(implode(',', $idCardList), ',') : '', //所有身份证号码
            'PftCode'         => base64_encode(strrev($orderInfoArr['code'])), //凭证码-> strrev(base64_decode($code))
            'sourceT'         => $sourceT, //票设置的对接属性
            'MobileList'      => $mobileList ? trim(implode(',', $mobileList), ',') : '', //所有游客手机号
            'PftTimeBegin'    => $pftTimeBegin,
            'PftTimeEnd'      => $pftTimeEnd,
            'sectionId'       => $sectionId ?? 0,
        ];
        $pushOrderRes = $this->pushOrder($reqOrderData);
        if ($pushOrderRes['code'] != 1000) {
            // 对方可能订单还没生成,不要直接请求支付,不然可能会造成对方无要支付的订单出错
            $payJobparams   = [
                'ordernum' => $orderNum,
                'LandId'   => $lid,
                'ApplyId'  => $aid,
            ];
            $openPayJobData = [
                'job_type' => 'third_open_pay',
                'job_data' => $payJobparams,
            ];
            //\Library\Resque\Queue::push('order', 'Order_Job', $openPayJobData);
            \Library\Resque\Queue::delay(strtotime("+ 2 seconds"), 'order', 'Order_Job', $openPayJobData);
        } else {
            // 失败的订单要取消订单
            $this->handleFailOrder($orderNum, $pushOrderRes['code'], $pushOrderRes['msg'], true);
        }
    }

    /**
     * 调用ota下推送取消
     *
     * @param  array 传值参数
     *
     * @return array
     *
     */
    public function pushCancel($paramArr)
    {
        // 参数处理
        $orderNum         = isset($paramArr['Ordern']) ? $paramArr['Ordern'] : '';
        $codes            = isset($paramArr['codes']) ? $paramArr['codes'] : '';
        $surplusTicketNum = isset($paramArr['Tnum']) ? $paramArr['Tnum'] : '';
        $fid              = isset($paramArr['Fid']) ? $paramArr['Fid'] : '';
        $aid              = isset($paramArr['ApplyId']) ? $paramArr['ApplyId'] : '';
        $idCardList       = isset($paramArr['IdCardList']) ? $paramArr['IdCardList'] : '';
        $pftSerialNum     = isset($paramArr['PftSerialNum']) ? $paramArr['PftSerialNum'] : '';
        $landId           = isset($paramArr['LandId']) ? $paramArr['LandId'] : '';
        $cancelNum        = isset($paramArr['cancelNum']) ? $paramArr['cancelNum'] : '';
        $refundOpenCode   = isset($paramArr['RefundOpenCode']) ? $paramArr['RefundOpenCode'] : [];
        $refundPersonInfo   = isset($paramArr['refundPersonInfo']) ? $paramArr['refundPersonInfo'] : [];
        // 调用 ota 下resetful接口方法
        $data = [
            'orderNum'      => $orderNum,
            'cancelNum'     => $cancelNum,         // 不在这边计算了直接传空 由ota那边代码计算取消张数
            'surplusNum'    => $surplusTicketNum,
            'fid'           => $fid,
            'applyDid'      => $aid,
            'codes'         => $codes,
            'touristIDCard' => $idCardList,
            'pftSerialNum'  => $pftSerialNum,
            'refundOpenCode'=> $refundOpenCode
        ];
        if (!empty($refundPersonInfo)) {
            $data['refundPersonInfo'] = $refundPersonInfo;
        }

        $version = $this->_getOpenApiVersion($landId);
        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Order.PushCancel', $data, $version);

        if ($result['code'] != 200) {
            return self::returnData($result['code'], $result['msg'], []);
        }

        return self::returnData(200, 'success', []);
    }

    /**
     * 调用ota下获取订单详情
     *
     * @param  string  $orderNum  票付通订单号
     *
     * @return array
     *
     */
    public function getApiOrderDetail($orderNum, $applyDid, $lid)
    {
        // 调用 ota 下resetful接口方法
        $data = [
            'orderNum' => $orderNum,
            'applyDid' => $applyDid,
        ];

        $version = $this->_getOpenApiVersion($lid);
        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Order.GetApiOrderDetail', $data, $version);

        if ($result['code'] != 200) {
            return self::returnData($result['code'], $result['msg'], []);
        }

        return self::returnData(200, 'success', $result['data']);
    }

    /**
     * 处理实名制信息
     *
     * @param  string  $name  下单游客姓名
     * @param  string  $idCard  下单游客身份证
     * @param  string  $touristNameList  多游客名字
     * @param  string  $idCardList  多游客身份证
     * @param  bool  $strict  是否严格要求身份证 true-要求身份证， false-非必须身份证
     *
     * @return array
     */
    private function _handleRealNameInfo($name, $idCard = '', $mobile = '', $touristNameList = '', $idCardList = '', $mobileList = [], $strict = true)
    {
        $realNameInfo = [];
        if (!empty($idCardList) && !empty($touristNameList)) {
            //优先判断是否存在多游客信息
            $idCardArr = explode(',', $idCardList);
            $nameArr   = explode(',', $touristNameList);
            $num       = count($idCardArr);
            $num       = ($num == count($nameArr)) ? $num : 0;

            if (!empty($mobileList)) {
                $mobileArr = explode(',', $mobileList);
            } else {
                $mobileArr = [];
            }
            for ($i = 0; $i < $num; $i++) {
                $realNameInfo[$i] = [
                    'name'   => $nameArr[$i],
                    'idcard' => $idCardArr[$i],
                    'mobile' => $mobileArr[$i],
                ];
            }
        } else {
            //不存在多游客信息，则尝试取下单的姓名和身份证
            if ($strict == true) {
                //严格要求身份证模式，必须判断是否有下单人的身份证和名字
                if (empty($idCard) || empty($name)) {
                    return [];
                }
            } else {
                //非严格要求身份证模式，只判断是否有下单人的名字
                if (empty($name)) {
                    return [];
                }
            }

            $realNameInfo[] = [
                'name'   => $name,
                'idcard' => $idCard,
                'mobile' => $mobile,
            ];
        }

        return $realNameInfo;
    }

    /**
     * 解密pftcode凭证码
     *
     * @param $pftCode  加密的凭证码
     *
     * @return string
     */
    private function _decodePftCode($pftCode)
    {
        return strrev(base64_decode($pftCode));
    }

    /**
     *
     * @param $csysId  对接系统id
     * @param $params array 下单返回的参数组
     * @param $orderType 下单类型 0-授信、余额， 1-在线支付， 2-超时补单
     */
    public function handleFailOrder($pftOrder, $errorCode, $errorMsg, $isNeedCancel = false)
    {
        // 通过订单号找到相关的信息通知
        $allApiOrderModel = new \Model\Ota\AllApiOrderModel();
        $apiOrderInfoArr  = $allApiOrderModel->getInfo('id,pftOrder,apiOrder,coopB,bCode', ['pftOrder' => $pftOrder]);
        $sysConfigModel   = new \Model\Ota\SysConfig();
        $configField      = 'id,name,sourceT,account_name,password_name,if_Pftcode,coopB,is_offline,white_ip,create_time';
        $sysConfigInfoArr = $sysConfigModel->getCsysInfoByCoopBs($apiOrderInfoArr['coopB'], $configField, true);
        $sysConfigInfoArr = $sysConfigInfoArr[$apiOrderInfoArr['coopB']];

        if ($isNeedCancel) {
            //在线支付下单,和超时补单，失败了需要取消订单
            $jobInfo = [
                'job_type' => 'package_cancel',
                'job_data' => [
                    'ordernum'   => $pftOrder,
                    'error_memo' => "{$sysConfigInfoArr['name']}对接下单失败",
                    'error_info' => $errorCode . '-' . $errorMsg,
                    'smsNotify'  => 0,
                ],
            ];
            Queue::push('order', 'Order_Job', $jobInfo);

            //同时发送短信给购买者
            $rs2 = Helpers::otaOrderFail($pftOrder);
        } else {
            $rs2 = '不发送';
        }

        //下单出问题就发送到钉钉告警群
        $rs1 = Helpers::sendDingTalkGroupRobotMessage("错误码:{$errorCode}\n - 错误信息:{$errorMsg}",
            "第三方系统【{$sysConfigInfoArr['name']}】对接故障", "门票tid:{$apiOrderInfoArr['bCode']}, 订单号:{$pftOrder}",
            self::$dingTalkRobot);
        pft_log("/ota_order/otaOpen/fail", json_encode([$rs1, $rs2, $pftOrder]));
    }

    /**
     * 调用ota下重新推送三方系统推送订单
     *
     * @param  array 传值参数
     *
     * @return array
     *
     */
    public function repushOrder($paramArr)
    {
        $orderNum = isset($paramArr['orderNum']) ? $paramArr['orderNum'] : '';
        $landId   = isset($paramArr['LandId']) ? $paramArr['LandId'] : '';

        // 调用 ota 下resetful接口方法
        $data = [
            'orderNum' => $orderNum,
        ];

        //$version = $this->_getOpenApiVersion($landId);
        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.ResendOrder.ResendOrderAction', $data);

        if ($result['code'] != 200) {
            return self::returnData($result['code'], $result['msg'], []);
        }

        return self::returnData(200, 'success', $result['data']);
    }

    /**
     * 通知第三方下单
     * 第三方订单号
     *
     */
    public function pushThirdOrder($orderNum)
    {
        if (empty($orderNum)) {
            return self::returnData(205, '订单号为空');
        }

        // 根据订单号查找订单信息
        $orderToolModel = new \Model\Order\OrderTools();
        $orderField     = 'member, aid, lid, tid, ss.playtime, tnum, ordername, contacttel, ordertel, 
            status, ss.pay_status, personid, code, begintime, endtime, remotenum, totalmoney, tprice';
        $detailField    = 'aids, ext_content';
        $orderInfoArr   = $orderToolModel->getOrderInfo($orderNum, $orderField, $detailField);
        if (empty($orderInfoArr)) {
            return self::returnData(205, '订单号信息不存在');
        }

        // 获取门票的绑定信息
        $otaProductBiz      = new \Business\Ota\Product();
        $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($orderInfoArr['tid']);
        if (empty($thirdTicketConfArr)) {
            return self::returnData(205, '该门票未绑定三方系统');
        }

        $subOrderModel = new \Model\Order\SubOrderQuery();
        $detailInfoArr = $subOrderModel->getInfoInDetailByOrder($orderNum, 'orderid, aids, series', 1, [], false);
        if (!empty($detailInfoArr['aids'])) {
            $aidsArr        = explode(',', $detailInfoArr['aids']);
            $oneLevelSeller = isset($aidsArr[1]) ? $aidsArr[1] : $orderInfoArr['member'];
        } else {
            $oneLevelSeller = $orderInfoArr['member'];
        }

        if (!empty($detailInfoArr['series']) && is_array(unserialize($detailInfoArr['series']))) {
            //演出场次
            $sectionId = unserialize($detailInfoArr['series'])[1];
        }

        if ($oneLevelSeller == 112) {
            $oneLevelSeller = $orderInfoArr['member'];
        }

        //$queryParams = [[$orderNum]];
        //$queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo','queryOrderTouristInfoByOrderId', $queryParams);
        //$touristsDetail = [];
        //if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
        //    $touristsDetail = $queryRes['data'][0];
        //}

        $orderQueryModel = new \Model\Order\OrderQuery();
        $touristsDetail  = $orderQueryModel->getTouristsInfo($orderNum, 0, $orderInfoArr['tnum']);

        $idcardStr        = '';
        $touristNameStr   = '';
        $touristMobileStr = '';
        if (!empty($touristsDetail)) {
            foreach ($touristsDetail as $tourist) {
                $idcardStr        .= $tourist['idcard'] . ',';
                $touristNameStr   .= $tourist['tourist'] . ',';
                $touristMobileStr .= $tourist['mobile'] . ',';
            }
            $idcardStr        = trim($idcardStr, ',');
            $touristNameStr   = trim($touristNameStr, ',');
            $touristMobileStr = trim($touristMobileStr, ',');
        }

        $priceDate = $orderInfoArr['playtime'];
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketById((int)$orderInfoArr['tid'], 'pre_sale');
        if (!empty($ticketArr) && $ticketArr['pre_sale'] == 1) {
            $priceDate = date("Y-m-d", time());
        }

        $priceApi  = new \Business\JavaApi\Ticket\Price();
        $priceList = $priceApi->batchBasePrice([(int)$orderInfoArr['tid']], $priceDate);
        if ($priceList['code'] != 200) {
            return self::returnData(205, '门票价格获取失败');
        }

        // 获取成本价格
        $costPrice = $priceList['data'][0]['costPrice'];
        // 获取零售价
        $retailPrice = $priceList['data'][0]['lPrice'];

        //请求参数
        $reqData = [
            'PftOrderSn'      => $orderNum, //票付通订单号
            'Ordern'          => $orderNum, //票付通订单号
            'Ltitle'          => '', //景区名称
            'Ttitle'          => '', //门票名称
            'PftUUid'         => $thirdTicketConfArr['uuid'], //uuid
            'PftPriceJs'      => $costPrice, //结算价
            'PftPriceGh'      => $costPrice, //供货价
            'PftPriceLs'      => $retailPrice, //零售价
            'PftPlayTime'     => $orderInfoArr['playtime'], //游玩时间
            'Tnum'            => $orderInfoArr['tnum'],
            'Fid'             => $orderInfoArr['member'],
            'PayStatus'       => $orderInfoArr['pay_status'],
            'TicketID'        => $orderInfoArr['tid'],
            'TouristName'     => $orderInfoArr['ordername'],
            'TouristTel'      => $orderInfoArr['ordertel'],
            'PlayDate'        => $orderInfoArr['begintime'], //这个参数有些奇怪,原来逻辑是游玩有效期
            'EndDate'         => $orderInfoArr['endtime'],
            'IdCard'          => $orderInfoArr['personid'],
            'remotenum'       => $orderInfoArr['remotenum'], //ota请求订单号作为第三方系统临时订单号请求
            'LandId'          => $orderInfoArr['lid'], //景点ID
            'ApplyId'         => $thirdTicketConfArr['applyId'], //原始供应商
            'OneLevelSeller'  => $oneLevelSeller, //一级分销商
            'LastLevelSeller' => $orderInfoArr['aid'], //一级分销商
            'TouristNameList' => $touristNameStr, //所有身份证姓名
            'IdCardList'      => $idcardStr, //所有身份证号码
            'PftCode'         => base64_encode(strrev($orderInfoArr['code'])), //凭证码-> strrev(base64_decode($code))
            'UUcode'          => $orderInfoArr['code'], //凭证码
            'sourceT'         => $thirdTicketConfArr['sourceT'], //票设置的对接属性
            'MobileList'      => $touristMobileStr, //所有游客手机号
            'sectionId'       => $sectionId ?? 0, //演出场次id
        ];

        if (!empty($orderInfoArr['ext_content'])) {
            // {"sectionTimeId":5250,"sectionTimeStr":"12:00-16:00","ticketVersion":47}
            $orderExtDetailInfoArr = json_decode($orderInfoArr['ext_content'], true);
            if (isset($orderExtDetailInfoArr['sectionTimeStr']) && !empty($orderExtDetailInfoArr['sectionTimeStr'])) {
                $sectionTimeArr        = explode('-', $orderExtDetailInfoArr['sectionTimeStr']);
                $reqData['PftTimeBegin'] = $sectionTimeArr[0] ?? '';
                $reqData['PftTimeEnd']   = $sectionTimeArr[1] ?? '';
            }
        }

        $thirdSystemBiz = new \Business\Order\ThirdSystem();
        $thirdRes       = $thirdSystemBiz->commonOrder($reqData);

        if ($thirdRes['code'] == 200) {
            $thirdCode     = $thirdRes['data']['VTcode'];
            $thirdOrdernum = $thirdRes['data']['VTordernum'];

            $updateData = [
                'vcode'     => $thirdCode,
                'tordernum' => $thirdOrdernum,
            ];

            $orderApi = new \Business\JavaApi\Order\Order();
            $res      = $orderApi->updateOrder($orderNum, $updateData);
        }

        return self::returnData($thirdRes['code'], $thirdRes['msg'], $thirdRes['data']);
    }

    /**
     * 根据景点id获取绑定的系统的版本号
     */
    private function _getOpenApiVersion($lid)
    {
        $otaQueryModel = new \Model\Ota\OtaQueryModel();
        $coopInfo      = $otaQueryModel->getCsysid($lid);
        $csysId        = $coopInfo['csysid'];

        if (in_array($csysId, $this->_newModeSysList)) {
            $this->_newMode = true;
        }

        // 获取系统的通知地址和白名单ip信息
        $sysConfigModel   = new \Model\Ota\SysConfig();
        $configField      = 'id, version';
        $sysConfigInfoArr = $sysConfigModel->getSysConfigInfo($csysId, $configField);

        return empty($sysConfigInfoArr['version']) ? 'V1' : $sysConfigInfoArr['version'];
    }


    /**
     * 获取绑定第三方的票的配置属性
     * <AUTHOR>
     * @date 2021/12/29
     *
     * @param array $ticketId 多个票ID
     *
     * @return array
     */
    public function getThirdTicketExtInfo(array $ticketId)
    {
        $ticketId = implode(',', $ticketId);

        $data = [
            'ticketId' => $ticketId,
        ];

        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Tools.getThirdTicketExtInfo', $data, 'Inside');

        if ($result['code'] != 200) {
            return self::returnData($result['code'], $result['msg'], []);
        }

        return self::returnData(200, 'success', $result['data']);
    }

    /**
     * 重发短信
     * <AUTHOR>
     * @date 2021/12/29
     *
     * @param array $orderNum 订单号
     *
     * @return array
     */
    public function getThirdOrderInfo(array $orderNum)
    {
        $orderNum = implode(',', $orderNum);

        $data = [
            'orderNum' => $orderNum,
        ];

        $result  = Helpers::callOpenApiSystemJsonRpc('OpenApi.Tools.getThirdOrderInfo', $data, 'Inside');

        if ($result['code'] != 200) {
            return self::returnData($result['code'], $result['msg'], []);
        }

        return self::returnData(200, 'success', $result['data']);
    }

    /**
     * 处理往第三方系统下单支付逻辑, 开放2.0版本
     * https://apifox.com/apidoc/shared-85ba6a28-382e-4a88-99a1-825c6c683748/api-24559962
     * @param $orderNum
     * @throws
     */
    public function commonOrderAndPay($orderNum)
    {
        $orderModel = Container::pull(\Model\Order\OrderHandler::class);
        $orderInfo = $orderModel->getOrderInfo($orderNum);

        $sectionTimeBegin = $sectionTimeEnd = '';
        if (!empty($orderInfo['ext_content'])) {
            $orderExtContent = json_decode($orderInfo['ext_content'], true);
            if (!empty($orderExtContent['sectionTimeStr'])) {
                [$sectionTimeBegin, $sectionTimeEnd] = explode('-', $orderExtContent['sectionTimeStr']);
            }
        }

        $aidArr = $orderInfo['aids'] ? explode(',', $orderInfo['aids']) : [];
        if (!empty($aidArr[1])) {
            $oneLevelSellerId = $aidArr[1];
        } else {
            $oneLevelSellerId = $orderInfo['member'] == 112 ? $orderInfo['aid'] : $orderInfo['member'];
        }

        //获取联系人信息
        $queryParams = [[$orderNum]];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderUserInfo','getOrderUserInfoByOrderNum', $queryParams);
        $orderUserInfo = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $orderUserInfo = $queryRes['data'][0];
        }
        //获取联系人身份信息
        $queryParams = [[$orderInfo['ordernum']], false, [], [], []];
        $queryRes    = \Business\JavaApi\Order\Query\Container::query('orderTouristInfo',
            'queryOrderTouristInfoByOrderId', $queryParams);
        //获取游客身份信息
        $touristArr        = [];
        if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
            $touristArr = $queryRes['data'] ?: [];
        }
        $sectionId = '';
        if (!empty($orderInfo['series']) && is_array(unserialize($orderInfo['series']))) {
            $orderSeries = unserialize($orderInfo['series']);
            if (is_array($orderSeries) && isset($orderSeries[1])) {
                $sectionId = $orderSeries[1];
            }
        }

        $ticketBiz = Container::pull(Ticket::class);
        $queryRes = $ticketBiz->queryTicketInfoById($orderInfo['tid']);
        $ticketInfo = $queryRes['ticket'];
        $landInfo = $queryRes['land'];

        $memberIdList = array_values(array_unique([$oneLevelSellerId, $orderInfo['aid'], $orderInfo['member']]));
        $memberBiz = Container::pull(Member::class);
        $memberList = $memberBiz->getMemberInfoByMulti($memberIdList, 'id', true);
        $oneLevelSeller = $memberList[$oneLevelSellerId] ?? [];
        $lastLevelSeller = $memberList[$orderInfo['aid']] ?? [];
        $buyUser = $memberList[$orderInfo['member']] ?? [];

        // 订单信息
        $orderInfoArr = [
            'ticket_num' => $orderInfo['tnum'], // 购买数量 必需
            'trave_date' => $orderInfo['playtime'], // 预计游玩日期 必需
            'valid_start_date' => $orderInfo['begintime'], // 订单有效开始日期 必需
            'valid_end_date' => $orderInfo['endtime'], // 订单有效结束日期 必需
            'fid' => $oneLevelSeller && $oneLevelSeller['dtype'] != 5 ? $oneLevelSellerId : $orderInfo['aid'], // 一级分销商id 必需
            'fid_name' => $oneLevelSeller && $oneLevelSeller['dtype'] != 5 ? $oneLevelSeller['dname'] : ($lastLevelSeller['dname'] ?? ''), // 一级分销商用户名 必需
            'last_fid' => $orderInfo['aid'], // 最后一级分销商，uu_ss_order.aid 必需
            'last_fid_name' => $lastLevelSeller['dname'] ?? '', // 最后一级分销商，uu_ss_order.aid 用户名 必需
            'buy_user_id' => $orderInfo['member'], // 末级购买用户id ， uu_ss_order.member 必需
            // 如果是散客则使用联系人姓名
            'buy_user_name' => $buyUser && $buyUser['dtype'] != 5 ? $buyUser['dname'] : $orderInfo['ordername'], // 末级购买用户名 必需
            'order_status' => $orderInfo['status'], // 订单状态 必需
            'pay_status' => $orderInfo['pay_status'], // 支付状态 必需
            'share_start_time' => $sectionTimeBegin, // 分时开始时间 可选
            'share_end_time' => $sectionTimeEnd, // 分时结束时间 可选
            'pft_code' => $orderInfo['code'], // 票付通凭证码 必需
            'remote_ota_order' => $orderInfo['remotenum'], // ota 远端订单号 必需
            'level_sellers' => $orderInfo['aids'], // 订单分销链用户id 表的aids 必需
        ];
        // 价格信息
        $priceInfoArr = [
            'settle_price' => $orderInfo['tprice'], // 和一级分销商的结算价 必需
            'retail_price' => $orderInfo['lprice'], // 零售价 必需
            'cost_price' => $orderInfo['aprice'], // 成本价 必需
        ];
        // 商品信息
        $productInfoArr = [
            'section_id' => $sectionId, // 场次id 必需
            'product_name' => $landInfo['title'], // 景点名 必需
            'ticket_name' => $ticketInfo['title'], // 门票名 必需
            'ticket_id' => $orderInfo['tid'], // 门票id 必需
            'product_id' => $orderInfo['lid'], // 景点id 必需
            'apply_id' => $orderInfo['apply_did'], // 供应商id 必需
        ];
        // 联系人信息
        $contactCredentialsInfo = [];
        if ($orderInfo['personid'] && $orderUserInfo['voucher_type']) {
            $contactCredentialsInfo[] = [
                'certificate_id' => $orderInfo['personid'], // 证件值 可选
                'certificate_type' => $orderUserInfo['voucher_type'], // 证件类型 可选
            ];
        }

        // 联系人信息
        $contactInfoArr = [
            'contact_name' => $orderInfo['ordername'] ?: '', // 联系人姓名 必需
            'contact_phone' => $orderInfo['ordertel'] ?: '', // 联系人手机号 必需
            'credentials_info' => $contactCredentialsInfo, // 证件信息 必须
        ];
        // 游客信息
        $touristInfoArr = [];
        foreach ($touristArr as $tourist) {
            if (!$tourist['idcard'] && !$tourist['voucher_type'] && !$tourist['mobile'] && !$tourist['tourist']) {
                continue;
            }
            $touristCredentialsInfo = [];
            if ($tourist['idcard'] && $tourist['voucher_type']) {
                $touristCredentialsInfo[] = [
                    'certificate_id' => $tourist['idcard'], // 证件值 可选
                    'certificate_type' => $tourist['voucher_type'], // 证件类型 可选
                ];
            }
            $touristInfoArr[] = [
                'tourist_name' => $tourist['tourist'], // 游客姓名 可选
                'tourist_phone' => $tourist['mobile'], // 游客手机号 可选
                'credentials_info' => $touristCredentialsInfo
            ];
        }

        $params = [
            'order_num' => $orderNum,
            'order_info' => $orderInfoArr ?: new \stdClass,
            'price_info' => $priceInfoArr ?: new \stdClass,
            'product_info' => $productInfoArr ?: new \stdClass,
            'contact_info' => $contactInfoArr ?: new \stdClass,
            'tourist_info' => $touristInfoArr ?: new \stdClass
        ];
        $thirdSystemBiz = Container::pull(ThirdSystem::class);
        $thirdRes       = $thirdSystemBiz->commonOrderV2($params);

        pft_log('third_call', json_encode(["v2.0", $params, $thirdRes], JSON_UNESCAPED_UNICODE));
        if ($thirdRes['code'] != 200 && $thirdRes['code'] != OrderConst::err598) {
            // 失败的订单要取消订单
            $this->handleFailOrder($orderNum, $thirdRes['code'], $thirdRes['msg'], true);
            throw new \Library\Exception($thirdRes['msg'], $thirdRes['code']);
        }
    }
}


