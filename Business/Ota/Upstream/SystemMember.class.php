<?php

namespace Business\Ota\Upstream;

class SystemMember extends UpstreamRpcBase
{
    /**
     * 获取系统列表
     *
     * @param $memberId
     * @param $page
     * @param $size
     * @param $name
     * @param $state
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/9
     * @throws \Exception
     */
    public function getSystemList($memberId, $page, $size, $name, $state, $sysType)
    {

        $queryData = compact('page', 'size', 'name', 'state', 'memberId', 'sysType');

        $res = $this->call('System/SystemMember/getSystemList', $queryData, 'plat');

        if ($res['code'] == 200) {
            $returnArr = $res['data'];
            return $this->returnData(200, $res['msg'], $returnArr);
        }

        return $this->returnData($res['code'], $res['msg'], []);

    }

    /**
     * 获取系统列表
     *
     * @param $memberId
     * @param $page
     * @param $size
     * @param $name
     * @param $sysType
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/9
     * @throws \Exception
     */
    public function getBindSystemList($memberId, $page, $size, $name, $sysType, $lid)
    {

        $queryData = compact('page', 'size', 'memberId', 'name', 'sysType', 'lid');

        $res = $this->call('System/SystemMember/getBindSystemList', $queryData, 'plat');

        if ($res['code'] != 200) {
            return $this->returnData(1000, $res['msg'], []);
        }

        $returnArr = [
            'list'  => [],
            'total' => 0,
        ];

        $returnArr['list']  = $res['data']['list'];
        $returnArr['total'] = $res['data']['total'];

        return $this->returnData(200, '', $returnArr);
    }

    /**
     * 获取用户开通的系统的数量
     *
     * @param $memberId
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/9
     * @throws \Exception
     */
    public function getSystemNum($memberId)
    {
        $returnData = [
            'total_num' => 0,
            'used_num'  => 0,
        ];

        $queryData   = compact('memberId');
        $totalNumRes = $this->call('System/System/getSystemNum', $queryData, 'plat');

        if ($totalNumRes['code'] == 200) {
            $returnData['total_num'] = $totalNumRes['data']['total_num'];
            $returnData['used_num']  = $totalNumRes['data']['used_num'];
        }

        return $this->returnData(200, '', $returnData);
    }

    /**
     * 获取绑定系统列表&&未绑定系统列表
     *
     * @param $memberId
     * @param $systemName
     * Author : liucm
     * Date : 2022/3/9
     */
    public function getSystemListByMemberId($memberId)
    {
        $isDisplay = 1;
        $queryData = compact('memberId', 'isDisplay');
        $res       = $this->call('System/SystemMember/getSystemListByMemberId', $queryData, 'plat');
        return $res;
    }

    /**
     * 用户开通系统
     *
     * @param $memberId
     * @param $systemId
     * @param $opId
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/9
     * @throws \Exception
     */
    public function addSystem($memberId, $systemId = '', $account = '', $secret = '', $configName = '', $id, $opId)
    {
        $res = $this->checkSysNum($memberId);
        if ($res['code'] != 200) {
            return $this->returnData(1000, $res['msg'], []);
        }
        $openSource = 1;
        $queryData  = compact('memberId', 'systemId', 'opId', 'account', 'configName', 'id', 'secret', 'openSource');

        $res = $this->call('System/SystemMember/addSystem', $queryData, 'plat');
        if ($res['code'] != 200) {
            return $this->returnData(1000, $res['msg'], []);
        }
        return $this->returnData(200, $res['msg'], []);
    }

    public function checkSysNum($memberId)
    {
        $queryData = compact('memberId');

        $res = $this->call('System/SystemMember/checkSysNum', $queryData, 'plat');
        return $res;
    }

    /**
     * Author : liucm
     * Date : 2022/3/16
     */
    public function systemSort($memberId, $id, $type)
    {
        $queryData = compact('memberId', 'id', 'type');

        $res = $this->call('System/SystemMember/systemSort', $queryData, 'plat');
        return $res;
    }

    /**
     * Author : liucm
     * Date : 2022/3/16
     */
    public function getSystemInfo($memberId, $systemId, $landId)
    {
        $queryData = compact('memberId', 'systemId', "landId");

        $res = $this->call('System/System/getSystemInfo', $queryData, 'plat');
        return $res;
    }

    /**
     * 添加商户级别的风险告警配置
     * 
     */
    public function addUserRiskAlertConf(array $params, $operId)
    {
        $queryData = $params;
        $queryData['oper_id'] = $operId;
        $res = $this->call('System/SystemRisk/addUserRiskAlertConf', $queryData, 'plat');
        if ($res['code'] == 200) {
            return $this->returnData(200, $res['msg'], []);
        }

        return $this->returnData($res['code'], $res['msg'], []); 
    }

    /**
     * 获取商户级别的风险告警配置
     * 
     */
    public function getUserRiskAlertConf(array $params)
    {
        $queryData = $params;
        $res = $this->call('System/SystemRisk/getUserRiskAlertConf', $queryData, 'plat');
        if ($res['code'] == 200) {
            return $this->returnData(200, $res['msg'], $res['data']);
        }

        return $this->returnData($res['code'], $res['msg'], []); 
    }

    /**
     * 分页获取线下票务系统操作日志
     *
     * @param  int  $memberId  商家id
     * @param  string  $startTime  开始时间
     * @param  string  $endTime  结束时间
     * @param  int  $systemId  系统id
     * @param  int  $operateType  操作类型 0全部 1绑定 2更新 3解绑
     * @param  int  $lid  景区id
     * @param  int  $tid  门票id
     * @param  int  $opid  操作人id
     * @param  int  $page  当前页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     */
    public function getSystemLogListPaging($memberId, $startTime, $endTime, $systemId = 0, $operateType = 0, $lid = 0, $tid = 0, $opid = 0, $page = 1, $pageSize = 10)
    {
        $queryData = compact('memberId', 'startTime', 'endTime', 'systemId', 'operateType', 'lid', 'tid', 'opid', 'page', 'pageSize');

        $res = $this->call('System/System/getSystemLogListPaging', $queryData, 'plat');
        return $res;
    }
}