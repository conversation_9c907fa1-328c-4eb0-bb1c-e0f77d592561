<?php

namespace Business\Ota\Upstream;

class SystemLandId extends UpstreamRpcBase
{
    /**
     * 添加系统配置
     *
     * @param $memberId
     * @param $systemId
     * @param $opId
     * @param $account
     * @param $secret
     * @param $configName
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/9
     * @throws \Exception
     */
    public function bindProductList($memberId, $landId, $page = 1, $size = 10, $landName, $systemName, $landStatus)
    {
        $returnArr = [
            'list'  => [],
            'total' => 0,
        ];

        $queryData = compact('memberId', 'landId', 'page', 'size', 'landName', 'systemName', 'landStatus');
        $res       = $this->call('System/SystemLandId/bindProductList', $queryData, 'plat');

        if (!empty($res) && $res['code'] == 200) {
            $returnArr['list']  = $res['data']['list'];
            $returnArr['total'] = $res['data']['total'];
        }

        return $this->returnData(200, '获取成功', $returnArr);
    }

    /**
     * 添加系统配置
     *
     * @param $memberId
     * @param $systemId
     * @param $opId
     * @param $account
     * @param $secret
     * @param $configName
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/9
     * @throws \Exception
     */
    public function bindProductDetail($memberId, $landId)
    {
        if (empty($memberId) || empty($landId)) {
            return $this->returnData(1000, '参数错误', []);
        }
        $queryData = compact('memberId', 'landId');
        $res       = $this->call('System/SystemLandId/bindProductDetail', $queryData, 'plat');

        if ($res['code'] == 200) {
            $returnData = $res['data'];
        } else {
            return $this->returnData($res['code'], $res['msg'], []);
        }

        return $this->returnData(200, '获取成功', $returnData);
    }

    /**
     * 添加或绑定产品
     *
     * @param $memberId
     * @param $landId
     * @param $systemId
     * @param $secretId
     * @param $noticeCode
     * @param $opId
     *
     * @return array
     * Author : liucm
     * Date : 2022/4/11
     */
    public function addOrEditBindProduct($memberId, $landId, $systemId, $secretId, $noticeCode, $opId)
    {
        if (empty($memberId) || empty($landId)) {
            return $this->returnData(1000, '参数错误', []);
        }
        $queryData = compact('memberId', 'landId', 'systemId', 'secretId', 'noticeCode', 'opId');
        $res       = $this->call('System/SystemLandId/addOrEditBindProduct', $queryData, 'plat');
        if ($res['code'] == 200) {
            return $this->returnData(200, $res['msg'], []);
        }
        return $this->returnData($res['code'], $res['msg'], []);

    }

    /**
     * 产品列表
     *
     * @param $memberId
     * @param  int  $page
     * @param  int  $size
     * @param  string  $keyword
     *
     * @return array
     * Author : liucm
     * Date : 2022/3/22
     */
    public function productList($memberId, $page = 1, $size = 10, $keyword = '', $type)
    {
        if (empty($memberId)) {
            return $this->returnData(1000, '参数错误', []);
        }

        $queryData = compact('memberId', 'page', 'keyword', 'size', 'type');
        $res       = $this->call('System/SystemLandId/productList', $queryData, 'plat');
        return $res;
    }

    /**
     * 绑定产品列表(自动拉取业务使用，增加绑定状态字段)
     *
     * @param $memberId
     * @param $systemId
     * @param $opId
     * @param $account
     * @param $secret
     * @param $configName
     *
     * @return array
     * Author : jinshansan
     * Date : 2023/8/10
     */
    public function bindProductListForAutoSync($memberId, $landId, $page = 1, $size = 10, $landName, $systemName, $landStatus, $confId, $systemId)
    {
        $returnArr = [
            'list'  => [],
            'total' => 0,
        ];

        $queryData = compact('memberId', 'landId', 'page', 'size', 'landName', 'systemName', 'landStatus', 'confId', 'systemId');
        $res       = $this->call('System/SystemLandId/bindProductListForAutoSync', $queryData, 'plat');

        if (!empty($res) && $res['code'] == 200) {
            $returnArr['list']  = $res['data']['list'];
            $returnArr['total'] = $res['data']['total'];
        }

        return $this->returnData(200, '获取成功', $returnArr);
    }
}