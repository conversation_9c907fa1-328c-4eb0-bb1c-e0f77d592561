<?php
/**
 * 对接订单处理
 *
 * <AUTHOR>
 * @date   2018-05
 */

namespace Business\Ota;

use Business\Base;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Order\Modify;
// use Business\Order\OrderOta;
// use Business\Ota\Order as OtaOrderBiz;
// use Library\Cache\Cache;
use Library\Constants\OrderConst;
use Library\Container;
use Library\MessageNotify\PFTSMSInterface;
use Library\MessageNotify\Platform\FzZwxSms;
use Library\MessageNotify\Platform\SmsFactory;
use Library\Tools\Helpers;
use Library\Util\EnvUtil;
use Model\Member\Member;
use Model\Order\AllApiOrderModel;
// use Model\Order\OrderTools;
// use Model\Ota\ApiOrderTicketRecord;
use Model\Ota\OtaQueryModel;
use Model\Product\Ticket;
// use Model\Ota\ManualTicketRecord;
use Library\JsonRpc\PftRpcClient;

class Order extends Base
{
    private $_maxNoticeNum = 10;
    // 定义通知失败就取消订单系统
    private $_noticeFailCancel = ['T'];

    //三方系统的配置文件
    private static $_csysConf = [];

    private $_isKafkaNotice = false;

    /**
     * all_api_order 中异步返码 出票中的订单
     *
     * @param  int  $sid  上级id
     * @param  int  $page  页码
     * @param  int  $pageNum  每页显示数量
     *
     * @return array
     *
     */
    public function apiOrderTicketingList($sid, $beginTime = '', $endTime = '', $page = 1, $pageNum = 10)
    {
        if (empty($sid) || $page < 1 || $pageNum < 1) {
            return $this->returnData(203, '参数错误');
        }

        $sysConfigModel = new \Model\Ota\SysConfig();
        $lidArr         = $sysConfigModel->getCsysLidArrByApplyDid($sid);
        if (empty($lidArr)) {
            return $this->returnData(204, '无数据');
        }

        $ticketIdArr = [];
        //该登录人供应商身份时拥有的票类ID  根据票类来限制供应商范围
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketBylandIdAndPay($lidArr, 'id', $sid);
        if (empty($ticketArr)) {
            return $this->returnData(204, '无数据');
        }
        $ticketIdArr = array_unique(array_column($ticketArr, 'id'));
        //$orderAbnormal = new \Model\Order\orderAbnormal();
        //$ticketIdArr   = $orderAbnormal->getTickIdByApplyIdLid($sid, $lidArr);
        //if (empty($ticketIdArr)) {
        //    return $this->returnData(204, '无数据');
        //}

        $allApiOrderModel = new \Model\Ota\AllApiOrderModel();
        $field            = 'id, pftOrder, apiOrder, coopB, cTime, remoteorder, oStatus, handleStatus';
        $listInfoArr      = $allApiOrderModel->getListByBcode($ticketIdArr, 10, 0, $beginTime, $endTime, $page,
            $pageNum, $field, $orderBy = 'id desc');

        if (empty($listInfoArr)) {
            return $this->returnData(204, '无数据');
        }

        $coopBArr   = array_column($listInfoArr, 'coopB');
        $sysInfoArr = $sysConfigModel->getCsysInfoByCoopBs($coopBArr, 'coopB,name', true);
        $data       = [];
        foreach ($listInfoArr as $key => $listValue) {
            $data[$key]              = $listValue;
            $data[$key]['coopBName'] = $sysInfoArr[$listValue['coopB']]['name'];
        }

        $listNum = $allApiOrderModel->countListByBcode($ticketIdArr, 10, 0, $beginTime, $endTime);

        $data = ['list' => $data, 'num' => $listNum];

        return $this->returnData(200, 'success', $data);
    }

    /**
     * ticket_record 查询出票记录表中待出票的订单
     * <AUTHOR>
     * @date 2021/12/20
     *
     * @param $sid
     * @param $beginTime
     * @param $endTime
     * @param $page
     * @param  string  $orderNum
     *
     * @return array
     */
    public function apiOrderTicketingListNew($sid, $beginTime, $endTime, $page, $searchType = "", $orderNum = "")
    {
        $queryData = [
            'sid'        => $sid,
            'beginTime'  => $beginTime,
            'endTime'    => $endTime,
            'page'       => $page,
            'searchType' => $searchType,
            'orderNum'   => $orderNum,
        ];

        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        $res = $lib->call('Order/OrderTicket/apiOrderTicketingListNew', [$queryData], 'plat');
        return $this->returnData($res['code'], $res['msg'], $res['data']);
    }

    /**
     * 待出票订单手动出票
     * <AUTHOR>
     * @date 2021/12/20
     *
     * @param $sid
     * @param $memberId
     * @param $orderNum
     * @param $apiCode
     * @param $apiQrCode
     *
     * @return array
     */
    public function manualTicket($sid, $memberId, $orderNum, $ticketType, $noticeDownStream, $apiCode, $apiQrCode = "")
    {
        $data = [
            'sid'              => $sid,
            'memberId'         => $memberId,
            'orderNum'         => $orderNum,
            'ticketType'       => $ticketType,
            'noticeDownStream' => $noticeDownStream,
            'apiCode'          => $apiCode,
            'apiQrCode'        => $apiQrCode,
        ];

        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        $res = $lib->call('Order/OrderTicket/manualTicket', [$data], 'plat');
        return $this->returnData($res['code'], $res['msg'], $res['data']);
    }

    /**
     * 获取手动出票记录
     * <AUTHOR>
     * @date 2021/12/25
     *
     * @param $beginTime
     * @param $endTime
     * @param  string  $orderNum
     * @param  string  $opId
     * @param  int  $page
     *
     * @return array
     */
    public function getManualTicketList($beginTime, $endTime, $sid, $orderNum = "", $opId = "", $page = 1)
    {
        $data = [
            'beginTime' => $beginTime,
            'endTime'   => $endTime,
            'sid'       => $sid,
            'orderNum'  => $orderNum,
            'opId'      => $opId,
            'page'      => $page,
        ];

        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        $res = $lib->call('Order/OrderTicket/getManualTicketList', [$data], 'plat');

        return $this->returnData($res['code'], $res['msg'], $res['data']);
    }


    /**
     * 出票超时通知配置获取
     * @param $sid
     * @return array
     * @throws \Exception
     */
    public function timeoutSmsConfigGet($sid)
    {
        $data = [
            'sid' => $sid
        ];
        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        return $lib->call('Order/OrderTicket/timeoutSmsConfigGet', [$data], 'plat');
    }

    /**
     * 出票超时通知配置批量获取
     * @param $sids
     * @return array
     * @throws \Exception
     */

    public function getOrderTimeoutSmsConfigMap($sids) {
        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        $data = [
            'sids' => $sids
        ];
        return $lib->call('Order/OrderTicket/getOrderTimeoutSmsConfigMap', [$data], 'plat');
    }


    /**
     * 出票超时通知配置
     * @param $sid
     * @param $noticeType
     * @param $noticeMobile
     * @param $status
     * @return array
     */
    public function timeoutSmsConfig($sid, $noticeType, $noticeMobile, $status)
    {
        $data = [
            'sid' => $sid,
            'noticeType' => $noticeType,
            'noticeMobile' => $noticeMobile,
            'status' => $status
        ];
        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        return $lib->call('Order/OrderTicket/timeoutSmsConfig', [$data], 'plat');
    }


    /**
     * 刷新订单的出票状态
     * <AUTHOR>
     * @date 2021/12/25
     *
     * @param $orderNum
     *
     * @return array
     */
    public function refreshOrderTicketStatus($orderNum)
    {
        $data = [
            'orderNum' => $orderNum,
        ];

        $lib = new \Library\JsonRpc\PftRpcClient('ota_supplier');
        $res = $lib->call('Order/OrderTicket/refreshOrderTicketStatus', [$data], 'plat');

        return $this->returnData($res['code'], $res['msg'], $res['data']);
    }

    /**
     * 传入时间参数生成sign
     *
     */
    private function _createSign($paramsArr)
    {
        ksort($paramsArr);
        $paramsStrExceptSign = '';
        foreach ($paramsArr as $val) {
            $paramsStrExceptSign .= $val;
        }

        return md5($paramsStrExceptSign);
    }


    /**
     * 异步通知凭证码 （eg:淘宝飞猪）
     * <AUTHOR>
     * @date    2018-5-16
     *
     * @param  string  $pftOrder  订单号
     * @param  string  $code  凭证码
     * @param  int  $noticeNum  单次队列重复通知次数
     * @param  bool  $isNeedRetry  目前队列执行时候为true，定时任务执行时为false
     *
     * @return array   返回的code = 204 205 207都没必要重推
     *
     */
    public function asyncCode($pftOrder, $code, $noticeNum = 1, $isNeedRetry = false, $qrcodeUrl = '', $credentials = '', $qrcodeValList = [])
    {
        if (!$pftOrder || !$code) {
            return $this->returnData(203, '参数缺失');
        }

        // 获取订单信息
        $orderTools   = new \Model\Order\OrderTools();
        $orderInfoArr = $orderTools->getOrderInfo($pftOrder, 'member, aid, lid, tid, ordermode, remotenum, tnum, status');

        if (!$orderInfoArr) {
            return $this->returnData(203, '订单信息不存在');
        }

        // 没有远端订单号不用通知远端
        if (empty($orderInfoArr['remotenum'])) {
            return $this->returnData(203, '远端订单号不存在');
        }

        // 记录错误次数准备短信通知
        $notifyModel   = new \Model\Ota\Notify();
        $notifyInfoArr = $notifyModel->getAsyncDataByOrderType($pftOrder, 1, 'id, notice_num, status, notice_time');

        if (!empty($notifyInfoArr) && $notifyInfoArr['status'] == 2) {
            // 通知失败的订单 可能已经取消订单了
            return $this->returnData(204, '通知失败订单不再通知');
        }

        if (!empty($notifyInfoArr) && $notifyInfoArr['status'] == 1) {
            return $this->returnData(204, '通知成功无需重试');
        }

        // 判断系统多次入队列总执行次数
        if (!empty($notifyInfoArr) && $notifyInfoArr['notice_num'] >= $this->_maxNoticeNum) {
            return $this->returnData(204, '已达最大通知次数');
        }

        // 添加控制, 出现同一秒第三方推送两次导致了程序未执行完就请求淘宝两次
        $key        = md5("lock:{$pftOrder}noticeCode");
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        if ($cacheRedis->lock($key, 1, 30) === false) {
            return $this->returnData(206, '请求频繁,30内重复通知');
        }

        // 出票内容记录到kafka
        if (in_array($orderInfoArr['ordermode'], [20, 58, 17, 64])) {
            $this->noticeTicketSuccess2Kafka($pftOrder, $code, $orderInfoArr, $qrcodeUrl, $credentials, $qrcodeValList);
        }

        // 判断订单的来源
        $openOtaOrderInfoArr = (new \Model\Ota\PftOpenOtaOrderModel())->getFirstByWhereRemoteOrder((string)$pftOrder, 'source');
        if (!empty($openOtaOrderInfoArr) && $openOtaOrderInfoArr['source'] == 1) {
            return $this->returnData(204, '新版api通知中心推送'); 
        }

        $coopB = 0;
        if ($orderInfoArr['ordermode'] == 17) {
            // 淘宝的订单
            $noticeRes = $this->_noticeNewTaobaoCode($orderInfoArr['remotenum'], $pftOrder);
            $coopB     = 'T';
        }

        if (in_array($orderInfoArr['ordermode'], [20, 58, 64])) {
            // ota的订单
            $noticeRes = $this->_noticeOtaCode($orderInfoArr['member'], $orderInfoArr['remotenum'], $pftOrder, $code, $qrcodeUrl);
            $coopB     = $orderInfoArr['member'];
        }

        // 更新记录先记录数据库
        if (empty($notifyInfoArr)) {
            $notifyModel->addAsyncCode($pftOrder, 1, $code, $coopB, $orderInfoArr['member'], $orderInfoArr['aid'],
                $orderInfoArr['remotenum']);
        }

        // 具体的系统实现方法返回成功通知
        if ($noticeRes['code'] == 200) {
            // 记录推送成功的数据
            $notifyModel->upAsyncDataByOrderType($pftOrder, 1, $code, 1);

            return $this->returnData(200, 'success');
        }

        // 具体的系统实现方法返回未配置通知地址的不再通知
        if ($noticeRes['code'] == 207) {
            // 记录失败的取消的订单 -- 更新记录为失败
            $notifyModel->upAsyncDataByOrderType($pftOrder, 1, $code, 2);

            return $this->returnData(207, $noticeRes['msg']);
        }

        // 具体的系统实现方法返回通知失败取消订单
        if ($noticeRes['code'] == 205) {
            // 记录失败的取消的订单 -- 更新记录为失败
            $notifyModel->upAsyncDataByOrderType($pftOrder, 1, $code, 2);

            return $this->returnData(205, 'cancel order');
        }

        // 更新记录通知失败的次数
        if (($this->_maxNoticeNum - $notifyInfoArr['notice_num']) == 1) {
            $notifyModel->upAsyncDataByOrderType($pftOrder, 1, $code, 2);

            return $this->returnData(204, 'notice num arrive max');
        }

        $notifyModel->upAsyncDataByOrderType($pftOrder, 1, $code, 0);

        // 记录到重推订单任务表中数据 --- 队列推送失败的订单
        if ($isNeedRetry) {
            $notifyModel->addNotify($pftOrder, 0, 5, ['code' => $code, 'qrcodeUrl' => $qrcodeUrl], false);
        }

        $msg = isset($noticeRes['msg']) ? $noticeRes['msg'] : '通知失败';

        return $this->returnData(206, $msg);
    }

    /**
     * 添加出票成功通知到kafka
     * @param string $pftOrder
     * @param string $code
     * @param array  $orderInfoArr
     * @param string $qrcodeUrl
     * @param string $credentials
     * 
     * @return true
     * 
     */
    public function noticeTicketSuccess2Kafka($pftOrder, $code, $orderInfoArr, $qrcodeUrl = '', $credentials = '', $qrcodeValList = [])
    {
        $hashids   = new \Library\Hashids\SmsHashids();
        $detailUrl = MOBILE_URL . $hashids->encode($pftOrder);

        // 凭证码可使用次数
        $availableNum = $orderInfoArr['tnum'];

        // 使用kafka通知出票成功的用户id
        $useKafkaNoticeMemberIdArr = ['10141128', '23433252', '3663752', '15773020', '27003241'];

        $redisKey   = 'ota:noticeticket:usekafka:memberid:' . $orderInfoArr['member'];
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');
        $cacheRes   = $cacheRedis->get($redisKey);
        if  (!empty($cacheRes)) {
            $useKafkaNoticeMemberIdArr[] = $orderInfoArr['member'];
            $this->_isKafkaNotice = true;
        } else {
            // 查询开启出票失败通知的白名单用户使用kafka来出票成功通知
            try {
                $rpcData      = ['memberId' => $orderInfoArr['member']];
                $pftRpcClient = new PftRpcClient('notice_order_status');
                $result       = $pftRpcClient->call('white/getWhiteMember', [$rpcData], 'verification');
                $resultData   = $result['data'];
                $resultCode   = $result['code'];
                $resultMsg    = $result['msg'];
            } catch (\Exception $e) {
                $resultMsg  = $e->getMessage();
                $resultCode = $e->getCode();
                $resultData = [];
            }

            if ($resultCode == 200) {
                $useKafkaNoticeMemberIdArr[] = $orderInfoArr['member'];
                $this->_isKafkaNotice = true;

                // 缓存已经切换的账号不再查询
                $cacheRedis->set($redisKey, 1, 600);
            }
        }

        // 有赞有容器、高德2.0 的订单走 kafka 出票通知 , 添加携程玩乐，添加自我游, 快手本地生活
        // if (!in_array($orderInfoArr['member'], $useKafkaNoticeMemberIdArr) && ENV == 'PRODUCTION') {
        if (!in_array($orderInfoArr['member'], $useKafkaNoticeMemberIdArr)) {
            return;
        }

        // 二维码链接数组
        $codeListArr = [];
        if (!empty($qrcodeUrl)) {
            $qrcodeUrlArr   = explode(',', $qrcodeUrl);
            $codeArr        = explode(',', $code);
            $credentialsArr = explode(',', $credentials);

            if (count($qrcodeUrlArr) > 1) {
                $availableNum = 1;
            }

            foreach ($qrcodeUrlArr as $qrcodeKey => $qrcodeItem) {
                $codeListArr[] = [
                    'code'           => isset($codeArr[$qrcodeKey]) ? $codeArr[$qrcodeKey] : '',
                    'qrcode'         => $qrcodeItem,
                    'qrcode_data'    => isset($qrcodeValList[$qrcodeKey]) ? $qrcodeValList[$qrcodeKey] : '',
                    'credentials_id' => isset($credentialsArr[$qrcodeKey]) ? $credentialsArr[$qrcodeKey] : '',
                    'available_num'  => $availableNum,
                ]; 
            }
        } else {
            $hashids  = new \Library\Hashids\SmsHashids();
            $codeList = explode(',', $code);
            $credentialsArr = explode(',', $credentials);

            if (count($codeList) > 1) {
                $availableNum = 1;
            }

            foreach ($codeList as $codeKey => $codeItem) {
                $beginString = substr($codeItem, 0, 1);
                if (is_numeric($codeItem) && $beginString != 0 && mb_strlen($codeItem, 'UTF8') < 18) {
                    $codeInfo = $hashids->encode((string) $codeItem);
                    $getPics  = 'https://open.12301.cc/code/' . $codeInfo . '.png';
                } elseif (preg_match('/[^a-zA-Z0-9]/', $codeItem)) {
                    $getPics = 'https://open.12301.cc/code/' . base64_encode($codeItem) . '.png?type=base64_code';
                } else {
                    if (is_numeric($codeItem)) {
                        $getPics = 'https://open.12301.cc/code/' . $codeItem . '.png?type=manual_code';
                    } else {
                        $getPics = 'https://open.12301.cc/qrcode.php?code=' . $codeItem . '&type=manual_code';
                    }
                }
                $codeListArr[] = [
                    'code'           => $codeItem,
                    'qrcode'         => $getPics,
                    'qrcode_data'    => $codeItem,
                    'credentials_id' => isset($credentialsArr[$codeKey]) ? $credentialsArr[$codeKey] : '',
                    'available_num'  => $availableNum,
                ];
            }
        }

        $codeType = 2;
        if (count($codeListArr) > 1) {
            $codeType = 3;
        }

        $isThird       = 0;
        // 判断是否是对接的订单
        $allApiCodeModel = new \Model\Ota\AllApiCodeModel();
        $apiCodeInfoArr  = $allApiCodeModel->getApiCodeInfo($pftOrder, 'id, apiCode, apiQrcode');
        if (!empty($apiCodeInfoArr)) {
            $isThird  = 1;
            $codeType = 4;
        }

        $dataKafka = [
            'orderNum' => $pftOrder,
            'status'   => $orderInfoArr['status'],
            'data' => [
                'type'   => 3, // 出票
                'params' => [
                    'ticket_status'    => 1, // 出票成功
                    'ticket_time'      => time(),
                    'fid'              => $orderInfoArr['member'],
                    'aid'              => $orderInfoArr['aid'],
                    'desc'             => '出票成功',
                    'code_type'        => $codeType, // 凭证类型:1-无需返码（凭身份证入园、凭订单号入园、凭手机号入园）2-凭证码3-门票码4-三方码5-外部码6-动态码
                    'order_detail_url' => $detailUrl, 
                    'qrcode_url_list'  => $codeListArr,
                    'code'             => $code,
                    'is_third'         => $isThird
                ],
            ],
        ];
        pft_log('debug/ticketKafka', json_encode($dataKafka));
        \Library\Kafka\KafkaProducer::push('platform', 'order_status_topic', $dataKafka, strval($pftOrder));
    }

    /**
     * 用户手动推送淘宝凭证码
     *
     * @param  string  $remoteNum  远端订单号
     * @param  string  $pftOrder  票付通订单号
     * @param  string  $code  发送凭证码
     * @param  int  $tnum  数量
     * @param  int  $orderMember  订单的member字段
     * @param  int  $orderAid  订单的aid 字段
     *
     * @return array
     */
    public function userAsyncTaobaoCode($remoteNum, $pftOrder, $code, $tnum, $orderMember, $orderAid)
    {
        $noticeRes = $this->_noticeTaobaoCode($remoteNum, $pftOrder, $code, $tnum, false);
        if ($noticeRes['code'] == 200) {
            // 添加到异步通知的数据
            $notifyModel   = new \Model\Ota\Notify();
            $notifyInfoArr = $notifyModel->getAsyncDataByOrderType($pftOrder, 1, 'id, notice_num, status, notice_time');
            if (empty($notifyInfoArr)) {
                $notifyModel->addAsyncCode($pftOrder, 1, $code, 'UT', $orderMember, $orderAid, $remoteNum);
            }
            // 记录推送成功的数据
            $notifyModel->upAsyncDataByOrderType($pftOrder, 1, $code, 1);

            return $this->returnData(200, 'success');
        }

        return $this->returnData(205, $noticeRes['msg']);

    }

    /**
     * 通过三方系统接口获取他们的消费码
     * <AUTHOR>
     * @date   2018-06-26
     *
     * @param  int  $coopB  三方系统coopB
     * @param  int  $applyDid  产品供应商
     * @param  string  $pftOrder  票付通订单号
     * @param  string  $apiOrder  三方订单号
     *
     * @return array
     */
    public function queryApiCode($coopB, $applyDid, $pftOrder, $apiOrder, $orderTime = '')
    {
        //加载配置文件
        $this->_loadCsys();

        $otaConf = self::$_csysConf[$coopB];
        $file    = $otaConf['file'];
        $class   = $otaConf['class'];

        include_once $file;
        $ota = new $class($applyDid);

        $params = [
            'pftOrder'  => $pftOrder,
            'apiOrder'  => $apiOrder,
            'orderTime' => $orderTime
        ];

        $res = $ota->queryApiCode($params);//ota那边请求方法要封装成这个

        //超时
        if ($res === false) {
            return $this->returnData(203, '接口请求超时');
        }

        //没有查询出
        if ($res['code'] == 204) {
            return $this->returnData(204, '没有查询出消费码');
        }

        //成功查询出
        if ($res['code'] == 200) {
            $apiCode = $res['data']['apiCode'];
            $qrcodeUrl = '';
            if (!empty($res['data']['qrcodeUrl'])) {
                $qrcodeUrl = $res['data']['qrcodeUrl'];
            } 

            return $this->returnData(200, '', ['apiCode' => $apiCode, 'qrcodeUrl' => $qrcodeUrl]);
        }
    }

    /***
     * 如果三方凭证码查询成功后，更新三方凭证码，并且通知OTA
     * <AUTHOR>
     * @date   2018-06-26
     *
     * @param  string  $pftOrder
     * @param  string  $apiOrder
     * @param  int  $tid
     * @param  string  $apiCode
     *
     * @return array
     */
    public function updateApiCode($pftOrder, $apiOrder, $tid, $apiCode, $isUpData = true, $apiQrcodeUrl = '', $manualTicket = false)
    {
        //初始化apiorder模型 -- 新美大是从表里面查数据,所以要过滤不更新表
        if ($isUpData) {
            $apiOrderModel = new AllApiOrderModel();

            $res = $apiOrderModel->updateApiCode($pftOrder, $apiCode, 'pftOrder');

            // 更新订单状态未使用
            $otaOrderBiz = new \Business\Ota\Order();
            $upOrderStatusRes = $otaOrderBiz->updateTicketIngOrderStatusByOrder($pftOrder, 0);
            if ($upOrderStatusRes['code'] != 200) {
                pft_log('ota_supplier/order/updateOrderStatus', json_encode([$pftOrder, $upOrderStatusRes]));
            }

            if ($res === false) {
                return $this->returnData(201, 'api凭证码更新失败');
            }

            // 更新 oStatus 10: 出票中 改为 0: 未使用
            $res = $apiOrderModel->updateOStatus($pftOrder);
            if ($res === false) {
                pft_log('ota_supplier/order/updateOrderStatus', json_encode([
                    'pftOrder' => $pftOrder,
                    'msg' => 'oStatus 状态更新失败',
                ]));
            }
        }

        $jobId = $this->_noticeDownstramSystem($apiOrder, $pftOrder, $tid, $apiCode , $apiQrcodeUrl, $manualTicket);

        if (!$jobId) {
            return $this->returnData(201, '通知入队列任务失败');
        }

        // 新美大在补单成功的时候就发短信，不用在查码后继续发了
        if ($isUpData) {
            $data = [
                'orderNum'  => $pftOrder,
                'apiCode'   => $apiCode,
                'apiQrcode' => $apiQrcodeUrl
            ];
            // 通知发送短信
            Helpers::callOpenApiSystemJsonRpc('OpenApi.Order.PftSendTicketMsg', $data, 'Inside');
        }

        return $this->returnData(200, '', ['jobId' => $jobId]);
    }

    /**
     * 多次查询三方凭证码都失败的话，将产品设置为不返码，而且发送短信通知
     * <AUTHOR>
     * @date   2018-06-26
     *
     * @param  string  $pftOrder  订单号
     * @param  int  $lid  景区ID
     *
     * @return bool
     */
    public function failApiCode($pftOrder, $lid, $aid)
    {
        //加载模型
        $otaQueryModel = new OtaQueryModel();
        $memberModel   = new Member();

        if ($lid) {
            //设置产品不返码
            $otaQueryModel->setCsysLandNoticeCode($lid, 3);
        }

        if ($aid) {
            //使用中网信发送短信通知
            $tel = $memberModel->getMemberInfo($aid, 'id', 'mobile')['mobile'];
            if ($tel) {
                $smsObj   = new \Library\MessageNotify\FzZwxSms();
                $smsNotiy = new \Library\MessageNotify\OrderNotify($pftOrder, 0, $aid, $tel);

                $tpl            = 'PRODUCT_NO_APICODE';
                $content['{1}'] = $pftOrder;

                return $smsNotiy->SendSMSV2($tel, $content, 0, $smsObj, $tpl);
            } else {
                return false;
            }
        }

        return false;
    }

    /***
     * 批量筛选出ota订单的供应商
     * <AUTHOR> Yiqiang
     * @date   2018-08-20
     *
     * @param  array  $orderList  订单列表
     * @param  int  $coopB  对接标识
     *
     * @return array
     */
    public function handleOrderAids($orderList, $coopB)
    {
        if (!$orderList && !is_array($orderList)) {
            return [];
        }

        $tidList = [];
        foreach ($orderList as $order) {
            if ($order['coopB'] == $coopB) {
                if (isset($tidList[$order['bCode']])) {
                    $tidList[$order['bCode']]++;
                } else {
                    $tidList[$order['bCode']] = 1;
                }
            }
        }

        $return = [];
        if (count($tidList) > 0) {
            $ticketModel = new Ticket();
            $infos       = $ticketModel->getTicketInfoByIdArr(array_keys($tidList), 'id,apply_did');
            foreach ($infos as $info) {
                $return[$info['apply_did']]['count'] += $tidList[$info['id']];
            }
        }

        return $return;
    }

    /***
     * 消费码查询成功通知下游系统
     * <AUTHOR> Yiqiang
     * @date   2018-07-02
     *
     * @param  string  $apiOrder  第三方订单号
     * @param  string  $pftOrder  票付通订单号
     * @param  int  $tid  票类id
     * @param  string  $apiCode  消费码
     */
    private function _noticeDownstramSystem($apiOrder, $pftOrder, $tid, $apiCode, $apiQrcodeUrl = '', $manualTicket = false)
    {
        //统一加载模型
        $sysConfigModel = new \Model\Ota\SysConfig();
        $apiOrderModel  = new \Model\Ota\AllApiOrderModel();
        $otaQueryModel  = new OtaQueryModel();

        $ticketInfoArr = $otaQueryModel->getTicketInfoById($tid, 'id,landid');
        $csysIdRes     = $sysConfigModel->getCsysByLid($ticketInfoArr['landid'], 'csysid, notice_code');
        $filter        = ['pftOrder' => $pftOrder];
        $row           = $apiOrderModel->getInfo('id, apiOrder, handleStatus, remoteorder, bCode', $filter);

        $jobId = '';
        if ((!empty($row['remoteorder']) && isset($csysIdRes['notice_code']) && in_array($csysIdRes['notice_code'],
                    [4])) || $manualTicket) {
            // 队列通知下游对接系统
            $queue = new \Library\Resque\Queue();
            $jobId = $queue->push('cooperation_system', 'AsyncCode_Job',
                [
                    'apiOrder'    => $apiOrder,
                    'pftOrder'    => $pftOrder,
                    'code'        => $apiCode,
                    'remoteOrder' => $row['remoteorder'],
                    'qrcodeUrl'   => $apiQrcodeUrl
                ]
            );
        } else {
            $jobId = '无需通知下游系统:' . $csysIdRes['notice_code'];
        }

        return $jobId;
    }

    /**
     * 淘宝异步通知验证码
     * @param string $remotenum 淘宝订单号
     * @param string $pftOrder  票付通订单号
     * 
     */
    private function _noticeNewTaobaoCode($remotenum, $pftOrder)
    {
        if (!($remotenum && $pftOrder)) {
            return $this->returnData(203, '参数错误');
        }

        $postData  = [
            'pftOrder'    => strval($pftOrder),
            'remoteOrder' => strval($remotenum),
        ];

        if (ENV == 'PRODUCTION') {
            $ticketUrl = 'http://ota.12301.cc/group/TaoBaoFeiZhuGate/AsyncCode';
        } elseif (ENV == 'TEST') {
            $ticketUrl = 'http://ota.12301dev.com/group/TaoBaoFeiZhuGate/AsyncCode'; 
        } else {
            $ticketUrl = 'http://ota.12301.local/group/TaoBaoFeiZhuGate/AsyncCode'; 
        }

        $headerArr = ['Content-Type: application/json;charset=utf-8'];
        $curlRes   = curl_post($ticketUrl, json_encode($postData), 80, 25, 'api/curl_post', $headerArr);

        if ($curlRes === false) {
            return $this->returnData(204, '通知超时');
        }

        if ($curlRes == 200) {
            return $this->returnData(200, 'success');
        }

        return $this->returnData(204, '通知失败');
    }

    /**
     * 淘宝异步通知验证码
     *
     * @param  string  $remotenum  淘宝订单号
     * @param  int  $pftOrder  票付通订单号
     * @param  int  $tnum  订单数量
     * @param  string  $code  凭证码
     * @param  bool  $isAutoCancel  是否自动取消
     *
     */
    private function _noticeTaobaoCode($remotenum, $pftOrder, $code, $tnum, $isAutoCancel = true, $qrcodeUrl = '')
    {
        if (!($remotenum && $pftOrder && $tnum)) {
            return $this->returnData(203, '参数错误');
        }

        // 淘宝的订单
        $taobaoModel   = new \Model\Taobao\Taobao();
        $taobaoInfoArr = $taobaoModel->pftTaobaoLog($pftOrder, 2);

        // 获取淘宝的请求的token
        if (empty($taobaoInfoArr)) {
            $taobaoInfoArr = $taobaoModel->pftTaobaoLog($remotenum, 1);
            if (empty($taobaoInfoArr)) {
                return $this->returnData(203, 'taobao token isn`t exist');
            }
        }

        include_once '/var/www/html/taobao/Des.class.php';
        include_once '/var/www/html/taobao/taobao/TopSdk.php';
        include_once '/var/www/html/taobao/TaobaoCode.class.php';

        $soapInside = Helpers::GetSoapInside();
        $key        = md5("tb:lock:{$remotenum}noticeCode");
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');

        $ecode      = str_replace([',', '_'], '', $code);
        if (mb_strlen($ecode, 'UTF-8') > 60) {
            $ecode = substr($ecode , 0 , 60);
        }
        
        $verifyCode = $ecode . ':' . $tnum;


        // 二维码链接数组
        $qrcodeUrlList = [];
        if (empty($qrcodeUrl)) {
            $hashids  = new \Library\Hashids\SmsHashids();
            $codeList = explode(',', $code);
            foreach ($codeList as $codeItem) {
                $beginString = substr($codeItem, 0, 1);
                if (is_numeric($codeItem) && $beginString != 0 && mb_strlen($code, 'UTF8') < 18) {
                    $codeInfo        = $hashids->encode((string) $codeItem);
                    $getPics         = 'https://open.12301.cc/code/' . $codeInfo . '.png';
                } else {
                    if (is_numeric($codeItem)) {
                        $getPics = 'https://open.12301.cc/code/' . $codeItem . '.png?type=manual_code';
                    } else {
                        $getPics = 'https://open.12301.cc/qrcode.php?code=' . $codeItem . '&type=manual_code';
                    }
                }
                $qrcodeUrlList[] = $getPics;
            }

            $qrcodeUrl = implode(',', $qrcodeUrlList);
        }

        $tb = new \TaobaoCode($soapInside, $cacheRedis, $key);

        $qrcodeUrlImgPath = '';
        if (!empty($qrcodeUrl)) {
            $qrCodeUrlArr     = explode(',', $qrcodeUrl);
            $qrcodeUrlImgPath = $tb->qrcodeListUpload($qrCodeUrlArr, $pftOrder); 
        }

        // 这个接口会循环通知五次给淘宝
        $noticeRes = $tb->eticketSend($remotenum, $verifyCode, $taobaoInfoArr['token'], $pftOrder, 1, '淘宝回调失败，请查看日志', $qrcodeUrlImgPath);

        // 失败的情况 记录通知失败次数
        if ($isAutoCancel && ($noticeRes === false || empty($noticeRes))) {
            // 通知失败，避免赔付取消订单
            $modifyBiz       = new Modify();
            $reqSerialNumber = $modifyBiz->getAuditInfoByOrder($pftOrder);
            $cancelRes       = $modifyBiz->cancelParamsCommonFormat($pftOrder, $taobaoInfoArr['method'],
                OrderConst::TAO_BAO_CANCEL, $reqSerialNumber);
            $sellerNick      = isset($taobaoInfoArr['seller_nick']) ? $taobaoInfoArr['seller_nick'] : '';
            // 短信通知商家 通知凭证失败或已申请取消
            $this->_noticeTaobaoFailSmsToBuyer($taobaoInfoArr['method'], $remotenum);
            $this->_dingTalkTaoBao($remotenum, '淘宝异步回调失败，请查看日志', $sellerNick, $taobaoInfoArr['method']);

            if ($cancelRes['code'] == 200) {
                return $this->returnData(205, 'notice fail and cancel order');
            }

            return $this->returnData(204, 'error');
        }

        $noticeResArr = json_decode(trim($noticeRes), true);
        if (is_array($noticeResArr) && $noticeResArr['code'] == 200) {
            // 通知成功的情况开始记录通知结果
            return $this->returnData(200, 'sucess');
        }

        return $this->returnData(204, 'error');
    }

    /**
     * 通过hook 进来的回调淘宝取消队列
     * @date   2019-04-08
     *
     * @param  array  $params  ['res' => 退票接口返回值, 'args' => '调用退票接口传的参数']
     *
     */
    public function noticeTaobaoCancel($params)
    {
        pft_log('taobao/cancel/debug', json_encode($params), 'day');

        $result = $params['res'];
        $args   = $params['args'];
        // 取消成功的进行通知
        if (!empty($result) && is_array($result) && $result['code'] == 200) {
            //订单号
            $pftOrder = $args[0];
            //剩余票数
            $surplusNum = $args[1];
            // 调用通知逻辑
            $params['pftOrder']   = $pftOrder;
            $params['surplusNum'] = $surplusNum;
            $cancelJobData        = [
                'job_type' => 'taobao_cancel_back',
                'job_data' => $params,
            ];
            \Library\Resque\Queue::push('order', 'Order_Job', $cancelJobData);

            pft_log('taobao/cancel/debug', '推送到队列成功,结果请查看 /alidata/log/site/cli/taobao/cancel/debug', 'day');
        }
    }

    /**
     * 取消淘宝码商订单
     * <AUTHOR>
     * @date   2019-07-08
     *
     * @param  string  $pftOrder  订单号
     * @param  int  $surplusNum
     * @param  array  $isNeedQueryOrder
     *
     * @return array
     */
    public function noticeTaobaoCancelOrderqueue($pftOrder, $surplusNum, $isNeedQueryOrder = true)
    {
        $res = $this->_noticeTaobaoCancelOrder($pftOrder, $surplusNum, $isNeedQueryOrder);

        return $res;
    }

    /**
     * 淘宝订单取消队列回调
     *
     * @param  string  $pftOrder  票付通订单号
     * @param  string  $remotenum  淘宝订单号
     * @param  string  $cancelNum  本次取消数量
     *
     */
    private function _noticeTaobaoCancelOrder($pftOrder, $surplusNum, $isNeedQueryOrder = true)
    {
        $pftOrder = strval($pftOrder);

        if ($isNeedQueryOrder) {
            // 查询平台订单
            $orderToolModel = new \Model\Order\OrderTools();
            $orderInfoArr   = $orderToolModel->getOrderInfo($pftOrder, 'id, ordermode');
            if (empty($orderInfoArr) || $orderInfoArr['ordermode'] != 17) {
                return $this->returnData(203, 'it isn`t taobao order');
            }
        }

        // 淘宝的订单
        $taobaoModel   = new \Model\Taobao\Taobao();
        $taobaoInfoArr = $taobaoModel->pftTaobaoLog($pftOrder, 2);

        // 获取淘宝的请求的token
        if (empty($taobaoInfoArr)) {
            return $this->returnData(203, 'taobao token isn`t exist');
        }

        include_once '/var/www/html/taobao/Des.class.php';
        include_once '/var/www/html/taobao/taobao/TopSdk.php';
        include_once '/var/www/html/taobao/TaobaoCode.class.php';

        $soapInside = Helpers::GetSoapInside();
        $key        = md5("tb:lock:{$taobaoInfoArr['order_id']}noticeCancel");
        $cacheRedis = \Library\Cache\Cache::getInstance('redis');

        $verifyCode = $taobaoInfoArr['ecode'];
        $originNum  = $taobaoInfoArr['num'];
        $remotenum  = $taobaoInfoArr['order_id'];

        $taobaoModel   = new \Model\Taobao\Taobao();
        $noticeLogList = $taobaoModel->getTaobaoDeteLog($remotenum, 'noticePftCancel',
            'id, f_method, vm_order_id, vm_token, vm_num, memo');
        if (empty($noticeLogList)) {
            return $this->returnData(204, '未查到本次退款申请');
        }

        $cancelNum         = 0;
        $validNoVerifyNum  = 0;
        $isNew             = false;
        $needCancelCodeArr = [];
        foreach ($noticeLogList as $item) {
            if (!empty($item['memo'])) {
                $tokenVal = json_decode($item['memo'], true);
                if ($surplusNum == $tokenVal['surplusNum']) {
                    $cancelNum        = $item['vm_num'];
                    $validNoVerifyNum = $tokenVal['validNoVerifyNum'];
                    if (!empty($tokenVal['needRefundCode']) && count($tokenVal['needRefundCode']) > 1) {
                        $needCancelCodeArr = $tokenVal['needRefundCode'];
                        $isNew = true;
                    }
                    break;
                }
            }
        }

        if (empty($cancelNum) || empty($validNoVerifyNum)) {
            return $this->returnData(204, '取消数量和本次未核销数量有误');
        }

        // 有门票码的就调用jsonrpc的
        if ($isNew) {
            // 利用jsonrpc接口请求
            $cancelTicketCodeInfoArr = [];
            foreach ($needCancelCodeArr as $cancelCodeItem) {
                $cancelTicketCodeInfoArr[] = [
                    'successCancelNum' => 1,
                    'validNoVerifyNum' => 1,
                    'cancelNum'        => 1,
                    'originNum'        => 1,
                    'code'             => $cancelCodeItem 
                ];
            }
            if (!empty($cancelTicketCodeInfoArr)) {
                $otaBaseApi = new \Business\OtaApi\OtaBase();
                $noticeResArr = $otaBaseApi->call('ThirdPartner/TaoBaoFeiZhu/cancelBack', ['remoteOrder' => $remotenum, 'cancelTicketCodeInfo' => $cancelTicketCodeInfoArr]);
            } else {
                $noticeResArr = ['code' => '1000', 'msg' => '未获取到需要操作的凭证码'];
            }
        } else {
            $tb               = new \TaobaoCode($soapInside, $cacheRedis, $key);
            $cancelBackResArr = $tb->cancelBack($remotenum, $cancelNum, $cancelNum, $originNum, $validNoVerifyNum,
                $verifyCode);
            $noticeResArr = json_decode(trim($cancelBackResArr), true);
        }

        if (is_array($noticeResArr) && $noticeResArr['code'] == 200) {
            // 通知成功的情况开始记录通知结果
            return $this->returnData(200, 'sucess');
        }

        return $this->returnData(204, 'error');
    }

    /**
     * 根据单号获取数据
     * 
     */
    public function getOtaOrderNoticeUrl($pftOrder)
    {
        // 获取redis缓存信息
        $noticeUrlKey = 'ota:noticeurl:order:' . $pftOrder;
        $cacheRedis   = \Library\Cache\Cache::getInstance('redis');
        $cacheRes     = $cacheRedis->get($noticeUrlKey);
        if (!empty($cacheRes)) {
            $cacheResArr    = json_decode($cacheRes, true);
            $statusBackUrl  = $cacheResArr['status_back_url'] ?? '';
            $ticketBackUrl  = $cacheResArr['ticket_back_url'] ?? '';
        } else {
            $otaNoticeModel = new \Model\Ota\OtaNoticeInfo();
            $queryRes       = $otaNoticeModel->getOtaNoticeUrlByPftOrder($pftOrder, 'status_back_url, ticket_back_url');
            $statusBackUrl  = $queryRes['status_back_url'] ?? '';
            $ticketBackUrl  = $queryRes['ticket_back_url'] ?? '';
        }
        return [
            'status_back_url' => $statusBackUrl,
            'ticket_back_url' => $ticketBackUrl
        ];
    }

    /**
     * 添加请求地址链接
     * 
     */
    public function addNoticeUrl($memberId, $remotenum, $pftOrder, $statusBackUrl, $ticketBackUrl)
    {
        $otaNoticeModel = new \Model\Ota\OtaNoticeInfo();
        $res = $otaNoticeModel->addOtaNoticeUrl($memberId, $remotenum, $pftOrder, $statusBackUrl, $ticketBackUrl);

        $noticeUrlList = [
            'status_back_url' => $statusBackUrl,
            'ticket_back_url' => $ticketBackUrl
        ];
        $noticeUrlJson = json_encode($noticeUrlList);

        // 添加redis缓存信息
        $noticeUrlKey = 'ota:noticeurl:order:' . $pftOrder;
        $cacheRedis   = \Library\Cache\Cache::getInstance('redis');
        $cacheRedis->set($noticeUrlKey, $noticeUrlJson, 3600 * 2);

        return $res;
    }

    /**
     * 通知ota 下单进来的地址凭证码
     *
     * @param $memeberId
     * @param $remoteNum
     * @param $orderNum
     * @param $code
     *
     * @return array
     */
    private function _noticeOtaCode($memberId, $remoteNum, $orderNum, $code, $qrcodeUrl = '')
    {
        if (!($memberId && $remoteNum && $orderNum && $code)) {
            return $this->returnData(204, '参数有误');
        }

        // 获取用户配置的出票回调地址
        $partnerMemberModel = new \Model\Member\PartnerMember();
        $memberModel        = new \Model\Member\Member();

        $memberInfoArr = $memberModel->getMemberInfo($memberId, 'id', 'id,account');
        if (empty($memberInfoArr)) {
            return $this->returnData(204, '账号信息有误');
        }

        $dataExInfoArr = $partnerMemberModel->getDataexByAccount($memberInfoArr['account']);
        if (empty($dataExInfoArr) || empty($dataExInfoArr['dticketURL'])) {
            return $this->returnData(207, '未配置出票回调地址');
        }

        $signKey    = $dataExInfoArr['account'] . $dataExInfoArr['pwd'];
        $verifyCode = md5($signKey);
        $ticketUrl  = $dataExInfoArr['dticketURL'];

        $otaOrderNoticeUrl = $this->getOtaOrderNoticeUrl($orderNum);
        if (!empty($otaOrderNoticeUrl) && !empty($otaOrderNoticeUrl['ticket_back_url'])) {
            $ticketUrl = $otaOrderNoticeUrl['ticket_back_url'];
        }

        // 二维码链接数组
        $qrcodeUrlList = [];
        if (!empty($qrcodeUrl)) {
            $qrcodeUrlArr = explode(',', $qrcodeUrl);
            $codeArr      = explode(',', $code);

            foreach ($qrcodeUrlArr as $qrcodeKey => $qrcodeItem) {
                $qrcodeUrlList[] = [
                    'qrcode'    => isset($codeArr[$qrcodeKey]) ? $codeArr[$qrcodeKey] : '',
                    'qrcodeUrl' => $qrcodeItem
                ]; 
            }
        } else {
            $hashids  = new \Library\Hashids\SmsHashids();
            $codeList = explode(',', $code);
            foreach ($codeList as $codeItem) {
                $beginString = substr($codeItem, 0, 1);
                if (is_numeric($codeItem) && $beginString != 0 && mb_strlen($code, 'UTF8') < 18) {
                    $codeInfo        = $hashids->encode((string) $codeItem);
                    $getPics         = 'https://open.12301.cc/code/' . $codeInfo . '.png';
                } elseif (preg_match('/[^a-zA-Z0-9]/', $codeItem)) {
                    $getPics = 'https://open.12301.cc/code/' . base64_encode($codeItem) . '.png?type=base64_code';
                } else {
                    if (is_numeric($codeItem)) {
                        $getPics = 'https://open.12301.cc/code/' . $codeItem . '.png?type=manual_code';
                    } else {
                        $getPics = 'https://open.12301.cc/qrcode.php?code=' . $codeItem . '&type=manual_code';
                    }
                }
                $qrcodeUrlList[] = [
                    'qrcode'    => $codeItem,
                    'qrcodeUrl' => $getPics
                ];
            }
        }

        $hashids   = new \Library\Hashids\SmsHashids();
        $detailUrl = MOBILE_URL . $hashids->encode($orderNum);

        $sign = openssl_encrypt($orderNum, 'aes-256-ecb', $dataExInfoArr['pwd']);

        $postData  = [
            'pftOrder'      => $orderNum,
            'remoteOrder'   => $remoteNum,
            'code'          => $code,
            'OrderState'    => 2,
            'VerifyCode'    => $verifyCode,
            // 'ticketStatus'  => 1,
            'detailUrl'     => $detailUrl,
            'qrcodeUrlList' => $qrcodeUrlList,
            'desc'          => '出票成功',
            'sign'          => $sign,
        ];

        $redis         = \Library\Cache\Cache::getInstance('redis');
        $cacheKey      = "wsdl:order:{$orderNum}";
        $wsdlOrderInfo = $redis->hGetAll($cacheKey);
        if (!empty($wsdlOrderInfo) && $wsdlOrderInfo['version'] == 2) {
            // 判断是否是对接的订单
            $isThird = 0;
            $allApiCodeModel = new \Model\Ota\AllApiCodeModel();
            $apiCodeInfoArr  = $allApiCodeModel->getApiCodeInfo($orderNum, 'id, apiCode, apiQrcode');
            if (!empty($apiCodeInfoArr)) {
                $isThird = 1;
            }

            $postData['ticketStatus'] = 1;
            $postData['isThird']      = $isThird;
        }

        // 特定账号对接，走通知中心
        if (!in_array($memberId, ['27003241']) && $this->_isKafkaNotice == false) {
            $curlRes   = curl_post($ticketUrl, json_encode($postData));
            if ($curlRes != 200) {
                $headerArr = ['Content-Type: application/json;charset=utf-8'];
                $curlRes   = curl_post($ticketUrl, json_encode($postData), 80, 25, 'api/curl_post', $headerArr);
            }

            pft_log('order/AsyncCodeJob',
            'OTA出票回调通知1：' . json_encode([$ticketUrl, $postData, $curlRes, $this->_isKafkaNotice], JSON_UNESCAPED_UNICODE));
        } else {
            $curlRes = 200;

            pft_log('order/AsyncCodeJob',
            'OTA出票回调通知2：' . json_encode([$ticketUrl, $postData, $curlRes, '切换到通知中心通知'], JSON_UNESCAPED_UNICODE));
        }

        if ($curlRes === false) {
            return $this->returnData(204, '通知超时');
        }

        if ($curlRes == 200) {
            return $this->returnData(200, 'success');
        }

        return $this->returnData(204, '通知失败');
    }

    /**
     * 通知淘宝凭证号失败的短信通知
     *
     * @param $memberId
     * @param $remoteNum
     *
     * @return array
     */
    private function _noticeTaobaoFailSmsToBuyer($memberId, $remoteNum)
    {
        if (!($memberId && $remoteNum)) {
            return $this->returnData(203, '参数有误');
        }
        $memberModel   = new \Model\Member\Member();
        $memberInfoArr = $memberModel->getMemberInfo($memberId, 'id', 'id,account,mobile');
        if (empty($memberInfoArr['mobile'])) {
            return $this->returnData(204, '未设置手机号');
        }

        $messageServiceApi = Container::pull(MessageService::class);
        [$approval, $res] = $messageServiceApi->dispatchMessageSend(MessageService::V2, 'notice_taobao_fail', $memberInfoArr['mobile'],
            [strval($remoteNum)], 0, '', '淘宝飞猪消费码失败通知', '', '', 0);
        if ($approval) {
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['淘宝飞猪消费码失败通知.notice_taobao_fail', __METHOD__, [$memberInfoArr['mobile'], [strval($remoteNum)]], $res], JSON_UNESCAPED_UNICODE));
            }
        } else {
            /** @deprecated 放量结束后删除 */
            //$smsLib = new FzZwxSms($memberInfoArr['mobile']);
            $smsLib = SmsFactory::getFactory($memberInfoArr['mobile']);
            $res = $smsLib->noticeTaobaoCodeFail($remoteNum);
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['淘宝飞猪消费码失败通知.notice_taobao_fail.old', __METHOD__, [$memberInfoArr['mobile'], [strval($remoteNum)]], $res], JSON_UNESCAPED_UNICODE));
            }
        }

        if ($res['code'] == 200) {
            return $this->returnData(200, 'success', []);
        }

        return $this->returnData(204, 'error', []);
    }

    /**
     * @param $remoteNum       淘宝订单号
     * @param $noticeMsg       通知消息
     * @param $sellerNick      码商客户名
     * @param  int  $memberId  分销商id
     *
     */
    private function _dingTalkTaoBao($remoteNum, $noticeMsg, $sellerNick = '', $memberId = 0)
    {
        if (!($remoteNum && $noticeMsg)) {
            return false;
        }
        $noticeMsg .= "\n淘宝订单号:[{$remoteNum}]";
        $noticeMsg .= "\n码商客户:{$sellerNick}";
        if ($memberId > 0) {
            //$model     = new \Model\Member\Member('slave');
            //$tmpArr    = $model->getMemberCacheById($memberId, 'dname,account');

            $queryParams = [[$memberId]];
            $queryRes    = \Business\JavaApi\Member\Query\Container::query('memberQuery', 'batchMemberInfoByIds',
                $queryParams);

            $tmpArr = [];
            if ($queryRes['code'] == 200 && !empty($queryRes['data'])) {
                $tmpArr = array_column($queryRes['data'], null, 'id')[$memberId];
            }

            $noticeMsg .= "\n分销商:{$tmpArr['dname']},账号:{$tmpArr['account']}";
        }
        Helpers::sendDingTalkGroupRobotMessage($noticeMsg,
            "淘宝马商下单失败", "请客服关注下", \Library\Constants\DingTalkRobots::FLY_PIG_ORDER);
    }

    /**
     * 钉钉告警通知
     *
     * @param $type  int  1:查码延迟  2:查码不得
     * @param $dataArr 业务数据
     *
     */
    public function dingTalkOtaReport($type, $dataArr)
    {
        $title  = '';
        $remark = '';
        $notice = '';
        switch ($type) {
            case 1:
                $orderNum = $dataArr['orderNum'];
                $cTime    = $dataArr['cTime'];

                if (!empty($orderNum) && !empty($cTime)) {
                    $title  = "报警啦--异步队列查码";
                    $remark = "延迟队列查码首次查码执行延迟严重";
                    $notice = "订单号:{$orderNum}, 下单时间:{$cTime}";
                }
                break;
            case 2:
                $orderNum    = $dataArr['orderNum'];
                $cTime       = $dataArr['cTime'];
                $remoteOrder = $dataArr['remoteOrder'];
                $orderMode   = $dataArr['orderMode'];

                if (!empty($orderNum) && !empty($cTime)) {
                    $title  = "报警啦--异步队列查码";
                    $remark = "多次查码无法得到凭证码,请关注";
                    $notice = "订单号:{$orderNum}, 下单时间:{$cTime}";

                    if (!empty($remoteOrder)) {
                        $notice .= ",远端订单号:{$remoteOrder}";
                    }
                    if ($orderMode == 17) {
                        $remark .= ",还是淘宝的订单哦";
                    }
                }
                break;
            default:
                break;
        }

        if (!empty($title) && !empty($remark) && !empty($notice)) {
            \Library\Tools\Helpers::sendDingTalkGroupRobotMessage($notice,
                $title, $remark, '624393b6dcf73e3d57f650f0f934cf7bec69c05625dfa7996b9fdc5f272eb4e8');
        }
    }

    /**
     * 加载三方系统的配置文件
     * <AUTHOR>
     * @date   2018-06-26
     *
     * @return array
     */
    private function _loadCsys()
    {
        if (!self::$_csysConf) {
            self::$_csysConf = include HTML_DIR . 'ota/common_zax/csys.inc.php';
            include_once HTML_DIR . 'ota/common/CtrlBase.class.php';

            if (!class_exists('ALLFunction')) {
                include_once HTML_DIR . 'ota/ALLFunction.php';
            }
        }
    }

    /**
     * @param $orderNum 票付通订单号
     * @param $lid      land 表id
     * @param $tid      门票id
     * @param $sid      供应商id
     *
     * @return bool
     */
    public function resendOtaMsg($orderNum)
    {
        // 对接第三方走第三方短信重发
        // 查询平台订单
        $orderToolModel = new \Model\Order\OrderTools();
        $orderInfoArr   = $orderToolModel->getOrderInfo($orderNum, 'id, lid, tid');
        if (empty($orderInfoArr)) {
            return false;
        }

        $tid = $orderInfoArr['tid'];
        $lid = $orderInfoArr['lid'];

        // 获取门票的绑定信息
        $otaProductBiz      = new \Business\Ota\Product();
        $thirdTicketConfArr = $otaProductBiz->getTicketConfigByTid($tid);
        if (empty($thirdTicketConfArr)) {
            return true;
        }

        $otaQueryModel = new OtaQueryModel();
        $ticketInfoArr = $otaQueryModel->getTicketInfoById($tid, 'id,apply_did');
        $sid           = $ticketInfoArr['apply_did'];

        $sourceT = $thirdTicketConfArr['sourceT'];

        // 请求三方系统
        if ($sourceT == 2) {
            $paramArr = [
                'Action'  => 'resend',
                'Ordern'  => $orderNum,
                'LandId'  => $lid,
                'ApplyId' => $sid,
            ];

            $otaSmsRes = Helpers::callThirdSystem($paramArr);
            if (!$otaSmsRes) {
                //第三方系统错误，重发失败
                return false;
            }

            $res  = explode('|', $otaSmsRes);
            $code = $res[0];
            if ($code != 200) {
                //第三方系统错误，重发失败
                return false;
            }

            //第三方系统已重发短信成功
            return true;
        }

        // 判断是否是对接开放接口的第三方
        if ($sourceT == 3) {
            // 调用推送开放接口重发短信
            $paramArr = [
                'Ordern'  => $orderNum,
                'LandId'  => $lid,
                'ApplyId' => $sid,
            ];

            $otaOpenOrderBiz = new \Business\Ota\Open\Order();
            $resendResArr    = $otaOpenOrderBiz->pushResendMsg($paramArr);
            if ($resendResArr['code'] == 200) {
                return true;
            } else {
                return false;
            }
        }

        return true;
    }

    /**
     * 通知美团动态码
     * 
     */
    public function noticeMtChangeVoucher($paramArr)
    {
        $resultFailArr = [];
        if (!empty($paramArr)) {
            foreach ($paramArr as $orderid => $item) {
                $voucherArr = [];
                foreach ($item as $key => $voucher) {
                    $voucherArr[$voucher['old_dynamic_code']] = $voucher['new_dynamic_code'];
                    $applyDid = $voucher['apply_did'];
                }
                $noticeRes = $this->_noticeMtChangeVoucher($applyDid, $orderid, $voucherArr);
                if ($noticeRes['code'] != 200) {
                    $orderOldVoucerArr = array_column($item, 'old_dynamic_code');
                    $resultFailArr[] = [
                        'orderid' => $orderid,
                        'voucher' => $orderOldVoucerArr,
                        'resCode' => $noticeRes['code']
                    ];
                }
            }
        }
        return ['code' => 200, 'msg' => 'notice sucess', 'data' => $resultFailArr];
    }

    /**
     * 通知美团动态码
     * @param int    $applyDid  供应商id 
     * @param string $pftOrder  票付通订单号
     * @param array  $voucherArr['oldvoucer' => 'newvoucher']  动态码
     * 
     */
    private function _noticeMtChangeVoucher($applyDid, $pftOrder, $voucherArr)
    {
        if (empty($pftOrder) || empty($voucherArr)) {
            return self::returnData(203, '参数有误', []); 
        }

        $requestArr = [
            'pftOrderNum' => $pftOrder,
            'applyDid'    => $applyDid,
            'voucher'     => $voucherArr,
            'voucherType' => 1, // 需要码核销
        ];
        $result = \Library\Tools\Helpers::callGroupSystem(10, $requestArr, 'Order_NoticeChangeCode');

        if ($result['code'] == 200) {
            return self::returnData(200, $result['msg'], []);
        }
        return self::returnData($result['code'], $result['msg'], []);
    }

    /**
     * 获取凭证码以及二维码
     * @param  int  $pftOrder  pft订单号
     *
     * @return array
     */
    public function getAllCode($pftOrder)
    {
        $codeList = [];
        $res      = $this->_getAllCode($pftOrder);
        if ($res['code'] != 200) {
            return self::returnData(203, '没找到码', []);
        }

        foreach ($res['data'] as $key => $value) {
            if (isset($value['code']) && !empty($value['code'])) {
                $qrcodeUrlStr = '';
                if (empty($value['qrcode'])) {
                    $codeArr = explode(',', $value['code']);
                    foreach ($codeArr as $item) {
                        $qrcodeUrlStr .= $this->_createQrImgUrl($item) . ',';
                    }
                } else {
                    $qrcodeUrlStr = $value['qrcode'];
                }

                $codeList = [
                    'code'     => $value['code'],
                    'qrcode'   => trim($qrcodeUrlStr, ','),
                    'codeType' => $key
                ];
            }
        }

        if (isset($codeList['code']) && !empty($codeList['code'])) {
            return self::returnData(200, 'success', $codeList);
        }

        return self::returnData(203, '没找到码', []);
    }

    /**
     * 获取凭证码以及二维码
     * @param int    $pftOrder  pft订单号
     *
     * @return mixed
     */
    private function _getAllCode(string $pftOrder)
    {
        $allCodeArr = [
            "allApiCode"      => [],
            "allApiOrderCode" => [],
            "outsideCode"     => [],
            "dynamicCode"     => [],
            "uuSsOrderCode"   => [],
        ];
        if (empty($pftOrder)) {
            return self::returnData(203, '参数有误', $allCodeArr);
        }

        //allApiCode 有二维码
        $allApiCodeModel = new \Model\Ota\AllApiCodeModel();
        $apiCodeInfoArr  = $allApiCodeModel->getApiCodeInfo($pftOrder, 'apiCode, apiQrcode');
        if (!empty($apiCodeInfoArr) && !empty($apiCodeInfoArr[0]['apiCode'])) {
            $apiCodeArr   = array_column($apiCodeInfoArr, 'apiCode');
            $apiQrcodeArr = array_filter(array_column($apiCodeInfoArr, 'apiQrcode'));
            if (!empty($apiCodeArr)) {
                $allCodeArr['allApiCode']['code']   = implode(',', $apiCodeArr);
                $allCodeArr['allApiCode']['qrcode'] = '';
                if (!empty($apiQrcodeArr)) {
                    $allCodeArr['allApiCode']['qrcode'] = implode(',', $apiQrcodeArr);
                }
                return self::returnData(200, 'success', $allCodeArr);
            }
        }

        //allApiOrder 没有二维码
        $allApiOrderModel = new \Model\Ota\AllApiOrderModel();
        $apiOrderInfoArr  = $allApiOrderModel->getInfoByPftOrder($pftOrder, 'apiCode');
        if (!empty($apiOrderInfoArr) && !empty($apiOrderInfoArr['apiCode'])) {
            if (!empty($apiOrderInfoArr['apiCode'])) {
                $allCodeArr['allApiOrderCode']['code']   = $apiOrderInfoArr['apiCode'];
                $allCodeArr['allApiOrderCode']['qrcode'] = '';
                return self::returnData(200, 'success', $allCodeArr);
            }
        }

        // 获取外部码
        $codeManageBiz = new \Business\ExternalCode\CodeManage();
        $codeListArr   = $codeManageBiz->getCodeByOrder($pftOrder, false);
        if ($codeListArr['code'] == 200 && !empty($codeListArr['data'])) {
            $allCodeArr['outsideCode']['code']   = implode(',', $codeListArr['data']);
            $allCodeArr['outsideCode']['qrcode'] = '';
            return self::returnData(200, 'success', $allCodeArr);
        }

        // 判断门票是否动态码 没有二维码
        $orderModel           = new \Model\Order\OrderTools();
        $orderInfo            = $orderModel->getOrderInfo($pftOrder, 'aid, ordernum, code, tid', false, 'vcode', false);
        $ticketId             = $orderInfo['tid'];
        $otaTicketBiz         = new \Business\Ota\Get\Ticket();
        $voucherChangeInfoArr = $otaTicketBiz->getVoucherChangeInfo($ticketId);
        if ($voucherChangeInfoArr['code'] == 200) {
            $voucherChangeType = $voucherChangeInfoArr['data']['voucherChangeType'];
            if ($voucherChangeType == 2) {
                // 获取当前可用动态码
                $enterLandBiz = new \Business\EnterLand\EnterLand();
                $codeListArr  = $enterLandBiz->orderValidDynamicCode($pftOrder, time());
                if (!empty($codeListArr)) {
                    $allCodeArr['dynamicCode']['code']   = implode(',', $codeListArr);
                    $allCodeArr['dynamicCode']['qrcode'] = '';
                }
                return self::returnData(200, 'success', $allCodeArr);
            }
        }

        // 应该还可以是门票码

        //uuSsOrderCode
        // $orderModel = new \Model\Order\OrderTools();
        $field      = 'code';
        $orderInfo  = $orderModel->getOrderInfo($pftOrder, $field);
        if ($orderInfo && $orderInfo['code']) {
            $allCodeArr['uuSsOrderCode']['code']   = $orderInfo['code'];
            $allCodeArr['uuSsOrderCode']['qrcode'] = '';
            return self::returnData(200, 'success', $allCodeArr);
        }

        return self::returnData(203, '未找到', []);
    }

    /**
     * 生成二维码链接
     * @param string $qrCodeStr
     * @return string
     * 
     */
    private function _createQrImgUrl($qrCodeStr)
    {
        $beginString = substr($qrCodeStr, 0, 1);
        if (is_numeric($qrCodeStr) && $beginString != 0 && mb_strlen($qrCodeStr, 'UTF8') < 18) {
            $hashids   = new \Library\Hashids\SmsHashids();
            $codeInfo  = $hashids->encode((string)$qrCodeStr);
            $qrcodeUrl = 'https://open.12301.cc/code/' . $codeInfo . '.png';
        } elseif (preg_match('/[^a-zA-Z0-9]/', $qrCodeStr)) {
            $qrcodeUrl = 'https://open.12301.cc/code/' . base64_encode($qrCodeStr) . '.png?type=base64_code';
        } else {
            $qrcodeUrl = 'https://open.12301.cc/code/' . $qrCodeStr . '.png?type=manual_code';
        }
        return $qrcodeUrl;
    }

    /**
     * 更新订单状态
     * @param string $pftOrder 票付通订单号
     * @param string $orderStatus 订单状态
     * 
     */
    public function updateTicketIngOrderStatusByOrder($pftOrder, $orderStatus)
    {
        if (empty($pftOrder) || $orderStatus === '') {
            return self::returnData(203, '参数错误', []);
        }

        // 获取订单状态进行更新操作
        // 根据订单号查找订单信息
        $orderReferModel = new \Model\Order\OrderTools('localhost');
        $orderField      = 'status, pay_status';
        $orderInfoArr    = $orderReferModel->getOrderInfo($pftOrder, $orderField);
        if ($orderInfoArr['status'] != 11) {
            return self::returnData(200, '订单当前状态不是未出票', []); 
        }

        $dataArr = [
            'status' => $orderStatus
        ];
        $orderUpdateApi = new \Business\JavaApi\Order\OrderInfoUpdate();
        $res = $orderUpdateApi->baseOrderInfoUpdate($pftOrder, $dataArr);
        if ($res['code'] != 200) {
            return self::returnData(203, $res['msg'], [$res]); 
        }

        return self::returnData(200, 'success', [$res]);  
    }

    /**
     * 根据pftOrder查询all_api_order信息
     * <AUTHOR>
     * @date 2021/5/28
     *
     * @param  string  $pftOrder 票付通订单号
     * @param  string  $field  查询字段
     *
     * @return array
     */
    public function getThirdInfoByPftOrder(string $pftOrder, string $field = 'apiOrder,apiCode')
    {
        if (empty($pftOrder) || empty($field)) {
            return [];
        }

        $allApiOrderModel = new \Model\Ota\AllApiOrderModel();
        $info  = $allApiOrderModel->getInfoByPftOrder($pftOrder, $field);

        return is_array($info) ? $info : [];
    }


    /**
     * 美团V1下单接口转发V2
     *
     *  @param  string  $method  请求方法
     *  @param  array  $params  请求参数
     *
     * @return array
     */
    public function mtOrderForwarding($method, $params)
    {
        if (!$method) {
            return $this->returnData(400, '请求地址错误');
        }
        $apiConfig = load_config('open_dis_center', 'api');

        $realUrl = $apiConfig['api_base_url'] . $method;

        //数据格式转换
        if (isset($params['body']['visitors']) && $params['body']['visitors']) {
            foreach ($params['body']['visitors'] as &$visitors) {
                if (isset($visitors['credentials'])) {
                    $visitors['credentials'] = (object) $visitors['credentials'];
                }
            }
        }

        $headerArr = ['Content-Type: application/json;charset=utf-8'];
        $res = curl_post($realUrl, json_encode($params), $apiConfig['api_port'] ?? 80, 25, '/api/curl_post', $headerArr);

        if (empty($res)) {
            return $this->returnData(200, '无数据');
        }

        return @json_decode($res, true);
    }
}
