<?php

namespace Business\Ota;

use Business\Base;
use Business\JavaApi\Product\Ticket as NewTicketApi;
use Business\JavaApi\Product\Land;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Member\Member as BizMember;
use Library\Container;
use Library\MessageNotify\PFTSMSInterface;
use Library\Util\EnvUtil;

class OtaOrderVerify extends Base
{
    private $_ApiOrderProductNameVerifyConfModel; //配置表
    private $_ApiOrderProductNameVerifyRuleModel; //规则表
    private $_retryCount = 2; //重试的最大次数

    public function __construct()
    {
        if (!$this->_ApiOrderProductNameVerifyConfModel) {
            $this->_ApiOrderProductNameVerifyConfModel = new \Model\Ota\ApiOrderProductNameVerifyConf();
        }
        if (!$this->_ApiOrderProductNameVerifyRuleModel) {
            $this->_ApiOrderProductNameVerifyRuleModel = new \Model\Ota\ApiOrderProductNameVerifyRule();
        }
    }

    /**
     * 根据用户id获取配置列表
     *
     * @param $memberId 用户id
     * @param  int  $page
     * @param  int  $limit
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getList($memberId, $page = 1, $limit = 10)
    {
        $list = $this->_ApiOrderProductNameVerifyConfModel->getApiOrderProductNameVerifyConfListByMemberId($memberId,
            $page, $limit, $field = '*');

        $landJavaApi  = new Land();
        $newTicketApi = new NewTicketApi();
        if (!empty($list)) {
            foreach ($list as &$value) {
                $lid     = $value['land_id'];
                $ruleArr = $this->_ApiOrderProductNameVerifyRuleModel->getApiOrderProductNameVerifyRuleByConfId($value['id']);
                if (!empty($ruleArr)) {
                    foreach ($ruleArr as &$rule) {
                        $tid           = $rule['ticket_id'];
                        $ticketRes     = $newTicketApi->queryTicketById($tid);
                        $rule['title'] = $ticketRes['data']['title'] ?? '';
                    }
                }
                $value['rule']        = $ruleArr;
                $landInfoArr          = $landJavaApi->queryLandSampleById((int)$lid);
                $value['title']       = $landInfoArr['data']['title'] ?? '';
                $value['system']      = $this->systemList()[$value['client_id']];
                $value['update_time'] = date('Y-m-d H:i:s', $value['update_time']);
            }
            return $this->returnData(200, '获取成功', $list);
        }
        return $this->returnData(500, '获取失败', []);
    }

    /**
     * 根据用户id获取配置列表总计
     *
     * @param $memberId 用户id
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getListCount($memberId)
    {
        $total = $this->_ApiOrderProductNameVerifyConfModel->getApiOrderProductNameVerifyConfCountByMemberId($memberId);

        return $this->returnData(200, '获取成功', ['total' => $total]);

    }

    /**
     * 设置配置启用/停用
     *
     * @param $id 自增id
     * @param $state  1 = 启用 2 = 停用
     * @param $memberId
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function setState($id, $state, $memberId)
    {
        $conf = $this->_ApiOrderProductNameVerifyConfModel->getApiOrderProductNameVerifyConfById($id,
            $field = 'member_id');
        if (empty($conf)) {
            return $this->returnData(500, '数据为空', []);
        }
        if ($conf['member_id'] != $memberId) {
            return $this->returnData(500, '无权限修改,请刷新重试', []);
        }
        $res = $this->_ApiOrderProductNameVerifyConfModel->setApiOrderProductNameVerifyConfStateById($id, $state);

        if ($res !== false) {
            return $this->returnData(200, '修改成功', []);
        }
        return $this->returnData(500, '修改失败', []);
    }

    /**
     * 根据id 获取配置详情
     *
     * @param $id 自增id
     * @param $memberId 用户id
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function getDetail($id, $memberId)
    {
        $res = $this->_ApiOrderProductNameVerifyConfModel->getApiOrderProductNameVerifyConfById($id);
        if (empty($res)) {
            return $this->returnData(500, '数据为空', []);
        }
        if ($res['member_id'] != $memberId) {
            return $this->returnData(500, '无权限获取,请刷新重试', []);
        }
        $landJavaApi  = new Land();
        $newTicketApi = new NewTicketApi();
        $ruleInfoArr  = $this->_ApiOrderProductNameVerifyRuleModel->getApiOrderProductNameVerifyRuleByConfId($id);
        if (!empty($ruleInfoArr)) {
            foreach ($ruleInfoArr as &$ruleInfo) {
                $tid               = $ruleInfo['ticket_id'];
                $ticketRes         = $newTicketApi->queryTicketById($tid);
                $ruleInfo['title'] = $ticketRes['data']['title'] ?? '';
            }
        }
        $lid           = $res['land_id'];
        $landInfoArr   = $landJavaApi->queryLandSampleById((int)$lid);
        $res['rule']   = $ruleInfoArr;
        $res['title']  = $landInfoArr['data']['title'] ?? '';
        $res['system'] = $this->systemList()[$res['client_id']];
        return $this->returnData(200, '获取成功', $res);
    }

    /**
     * 添加配置
     *
     * @param $opid 操作人id
     * @param $memberId 用户id
     * @param $sysid    系统id
     * @param $landId   景区id
     * @param $noticeType 通知类型 1=钉钉 2=短信
     * @param $noticePhone 通知手机号 多个,隔开
     * @param $noticeToken 通知钉钉token
     * @param $noticeSign  通知钉钉秘钥
     * @param $supplierId  供应商id
     * @param $state  1 = 启用 2 = 停用
     * @param $ruleArr  = [
     *  ticket_id,门票id
     *  rule_text 规则
     *  rule_id   规则id
     * ] 规则
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function addConf($opid, $memberId, $sysid, $landId, $noticeType, $noticePhone, $noticeToken, $noticeSign, $state, $ruleArr)
    {
        $confModel = $this->_ApiOrderProductNameVerifyConfModel;
        $ruleModel = $this->_ApiOrderProductNameVerifyRuleModel;

        $getRes = $confModel->getApiOrderProductNameVerifyConfByMemberId($memberId, $landId, $sysid);
        if (!empty($getRes)) {
            return $this->returnData(500, '已存在相同配置', []);
        }
        if (empty($ruleArr) || !is_array($ruleArr)) {
            return $this->returnData(500, '配置格式不正确', []);
        }

        $confModel->startTrans();
        try {
            $lock = false;
            $id   = $confModel->insertApiOrderProductNameVerifyConf($memberId, $landId, $sysid, $state,
                $noticeType, $noticePhone, $noticeToken, $noticeSign, $opid);
            if ($id === false) {
                $lock = true;
            }
            foreach ($ruleArr as $rule) {
                $ticketId   = $rule['ticket_id'];
                $ruleText   = $rule['rule_text'];
                $supplierId = $rule['supplier_id'];
                $ruleType   = 1;
                if (!empty($ruleText)) {
                    $ruleType = 2;
                }
                $insertRes = $ruleModel->insertApiOrderProductNameVerifyRule($id, $landId, $memberId, $supplierId,
                    $ticketId, $sysid, $ruleType, $ruleText, $opid);
                if ($insertRes === false) {
                    $lock = true;
                }
            }

            if ($lock == true) {
                $confModel->rollback();
                return $this->returnData(500, '新增失败', []);
            }
            $confModel->commit();
        } catch (\Exception $e) {
            $confModel->rollback();
            $msg = $e->getMessage();

            return $this->returnData(500, $msg, []);
        }
        return $this->returnData(200, '添加成功', []);

    }

    /**
     * 编辑配置
     *
     * @param $opid 操作人id
     * @param $verifyConfId 配置id
     * @param $memberId 用户id
     * @param $sysid    系统id
     * @param $landId   景区id
     * @param $noticeType 通知类型 1=钉钉 2=短信
     * @param $noticePhone 通知手机号 多个,隔开
     * @param $noticeToken 通知钉钉token
     * @param $noticeSign  通知钉钉秘钥
     * @param $supplierId  供应商id
     * @param $state  1 = 启用 2 = 停用
     * @param $ruleArr  = [
     *  ticket_id,门票id
     *  rule_text 规则
     *  rule_id   规则id
     * ] 规则
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function editConf($opid, $verifyConfId, $memberId, $landId, $noticeType, $noticePhone, $noticeToken, $noticeSign, $state, $ruleArr)
    {
        $confModel = $this->_ApiOrderProductNameVerifyConfModel;
        $ruleModel = $this->_ApiOrderProductNameVerifyRuleModel;
        if (empty($ruleArr) || !is_array($ruleArr)) {
            return $this->returnData(500, '配置格式不正确', []);
        }
        $confModel->startTrans();
        $lock = false;
        try {
            $confInfo    = $confModel->getApiOrderProductNameVerifyConfById($verifyConfId);
            $editConfRes = $confModel->editApiOrderProductNameVerifyConfByMemberId($verifyConfId, $state, $noticeType,
                $noticePhone, $noticeToken, $noticeSign, $opid);
            $clientId    = $confInfo['client_id'];
            if ($editConfRes === false) {
                $lock = true;
            }
            foreach ($ruleArr as $rule) {
                $ticketId   = $rule['ticket_id'];
                $ruleText   = $rule['rule_text'];
                $supplierId = $rule['supplier_id'];
                $ruleType   = 1;
                if (!empty($ruleText)) {
                    $ruleType = 2;
                }

                if (!empty($rule['rule_id'])) {
                    $ruleId      = $rule['rule_id'];
                    $editRuleRes = $ruleModel->editApiOrderProductNameVerifyRuleByMemberId($ruleId, $verifyConfId,
                        $memberId, $ticketId, $ruleType, $ruleText, $opid);
                    if ($editRuleRes === false) {
                        $lock = true;
                    }
                } else {
                    $insertRuleRes = $ruleModel->insertApiOrderProductNameVerifyRule($verifyConfId, $landId, $memberId,
                        $supplierId, $ticketId, $clientId, $ruleType, $ruleText, $opid);

                    if ($insertRuleRes === false) {
                        $lock = true;
                    }
                }
            }

            if ($lock == true) {
                $confModel->rollback();
                return $this->returnData(500, '编辑失败', []);
            }

            $confModel->commit();
        } catch (\Exception $e) {
            $confModel->rollback();
            $msg = $e->getMessage();

            return $this->returnData(500, $msg, []);
        }
        return $this->returnData(200, '编辑成功', []);

    }

    /**
     * 根据配置id删除配置和规则
     *
     * @param $verifyConfId
     * @param $sid
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function deleteConfAndRule($verifyConfId, $sid)
    {
        $confModel = $this->_ApiOrderProductNameVerifyConfModel;
        $ruleModel = $this->_ApiOrderProductNameVerifyRuleModel;

        $confInfo = $confModel->getApiOrderProductNameVerifyConfById($verifyConfId);

        if (empty($confInfo)) {
            return $this->returnData(500, '删除失败,不存在', []);
        }
        if ($sid != $confInfo['member_id']) {
            return $this->returnData(500, '无权限删除,请刷新重试', []);
        }

        $confModel->startTrans();
        $lock = false;
        try {
            $confDelRes = $confModel->deleteConfById($verifyConfId);
            if ($confDelRes === false) {
                $lock = true;
            }
            $ruleDelRes = $ruleModel->deleteRuleByConfId($verifyConfId);
            if ($ruleDelRes === false) {
                $lock = true;
            }

            if ($lock == true) {
                $confModel->rollback();
                return $this->returnData(500, '删除失败', []);
            }
            $confModel->commit();
        } catch (\Exception $e) {
            $confModel->rollback();
            $msg = $e->getMessage();

            return $this->returnData(500, $msg, []);
        }

        return $this->returnData(200, '删除成功');
    }

    /**
     * 根据规则id删除规则
     *
     * @param $ruleId
     * @param $sid
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/20
     */
    public function deleteRule($ruleId, $sid)
    {
        $ruleModel = $this->_ApiOrderProductNameVerifyRuleModel;
        $ruleInfo  = $ruleModel->getApiOrderProductNameVerifyRuleById($ruleId);
        if ($sid != $ruleInfo['member_id']) {
            return $this->returnData(500, '无权限删除,请刷新重试', []);
        }
        $res = $ruleModel->deleteVerifyRuleById($ruleId);
        if ($res === false) {
            return $this->returnData(200, '删除失败', $res);
        }
        return $this->returnData(200, '删除成功', $res);
    }

    /**
     * 系统列表
     *
     * @return string[]
     * Author : liucm
     * Date : 2021/8/20
     */
    public function systemList()
    {
        return [
            '1' => '飞猪',
        ];
    }

    public function verifyNameJob($systemId, $remoteOrder, $ticketName, $thirdBindCode, $account = '', $num = 0)
    {
        //根据系统和订单号加个锁,五分钟之内重复请求的就不推队列了
        $redis     = \Library\Cache\Cache::getInstance('redis');
        $redisLock = 'service:verifyNameJob:lock:' . $remoteOrder;

        $redisLockRes = $redis->get($redisLock);
        if ($redisLockRes) {
            return '请求重复';
        } else {
            $redis->set($redisLock, 1, '', 5 * 60);
        }

        //推送到队列处理 10秒后处理
        $jobId = \Library\Resque\Queue::delay(strtotime("+ 15 seconds"), 'cooperation_system', 'OtaOrderVerify_Job',
            [
                'job_type'        => 'verify_name',
                'system_id'       => $systemId,
                'remote_order'    => $remoteOrder,
                'ticket_name'     => $ticketName,
                'third_bind_code' => $thirdBindCode,
                'account'         => $account,
                'num'             => $num,
            ]
        );

        return '已推送至队列,jobId:' . $jobId;
    }

    public function dealVerifyNameJob($systemId, $remoteOrder, $apiTicketName, $thirdBindCode, $account, $num)
    {
        //通过远端订单号查询订单信息
        $getField = 'ordernum, remotenum, code, tnum, tprice, pay_status, playtime, begintime, 
                    endtime, status, tid, member, aid, ordertime, ctime, dtime';

        $orderToolModel = new \Model\Order\OrderTools();
        $orderInfoArr   = $orderToolModel->getOrderInfoByRemote(strval($remoteOrder), $getField);

        if (empty($orderInfoArr)) {
            if ($num < $this->_retryCount) {
                //查不到订单 推送到队列5分钟后再处理
                \Library\Resque\Queue::delay(strtotime("+ 300 seconds"), 'cooperation_system', 'OtaOrderVerify_Job',
                    [
                        'job_type'        => 'verify_name',
                        'system_id'       => $systemId,
                        'remote_order'    => $remoteOrder,
                        'ticket_name'     => $apiTicketName,
                        'third_bind_code' => $thirdBindCode,
                        'num'             => $num + 1,
                    ]
                );
                return '未查询到订单,队列重试';
            }
            return '队列重试失败,达到最大重试次数';
        }

        $orderNum    = $orderInfoArr['ordernum'];
        $apiOrderNum = $remoteOrder;

        $modelTicket = new \Model\Product\Ticket('slave');

        //飞猪特殊处理,通过pid 查lid和tid
        if ($systemId == 1) {
            // 产品ID, 自己的ID, 供应商ID
            [$pid, $memberId, $supplierId] = explode('|', $thirdBindCode);
            $ticketInfoArr = $modelTicket->getTicketInfoByPid($pid);
            $landId        = $ticketInfoArr['landid'];
            $ticketId      = $ticketInfoArr['id'];
            $ticketName    = $ticketInfoArr['title'];
        } else {
            $memberBiz  = new BizMember();
            $memberInfo = $memberBiz->getInfoByAccountAndStatus($account, 0);
            $memberId   = $memberInfo['id'];
            list($supplierId, $landId, $ticketId) = explode('|', $thirdBindCode);
            $ticketInfoArr = $modelTicket->getTicketInfoById($ticketId);
            $ticketName    = $ticketInfoArr['title'];
        }

        $ruleModel = $this->_ApiOrderProductNameVerifyRuleModel;
        $confModel = $this->_ApiOrderProductNameVerifyConfModel;

        $ruleInfo = $ruleModel->getApiOrderProductNameVerifyRuleByLandIdMemberIdSupplierIdTicketId($landId,
            $memberId, $supplierId, $ticketId, $systemId);
        if (empty($ruleInfo)) {
            return '未查询到配置';
        }
        $verifyConfId = $ruleInfo['verify_conf_id'];
        $confInfo     = $confModel->getApiOrderProductNameVerifyConfById($verifyConfId);

        if ($confInfo['state'] == 2) {
            return '功能已禁用';
        }

        //字符比较
        $ruleType   = $ruleInfo['rule_type'];
        $ruleText   = $ruleInfo['rule_text'];
        $verifyRes  = $this->_orderNameVerify($ruleType, $apiTicketName, $ticketName, $ruleText);
        $systemName = $this->systemList()[$ruleInfo['client_id']];
        $ruleText   = $ruleInfo['rule_text'];

        if ($verifyRes == false) {

            $webhook = $confInfo['notice_token'];
            $signKey = $confInfo['notice_sign'];
            //钉钉告警
            if ($confInfo['notice_type'] == 1) {
                $dingDingRes = $this->_dingDingAlarm($systemName, $ticketName, $apiTicketName, $orderNum, $apiOrderNum,
                    $ruleText, $webhook, $signKey);
                return '钉钉告警结果:' . $dingDingRes;
            }

            //短信告警
            if ($confInfo['notice_type'] == 2) {
                $sendTelArr = explode(',', $confInfo['notice_phone']);
                if (!empty($sendTelArr)) {
                    foreach ($sendTelArr as $sendTel) {
                        $smsRes[] = $this->_SMSAlarm($systemName, $apiTicketName, $orderNum, $sendTel, $memberId);
                    }
                }
                return '短信告警结果:' . json_encode($smsRes);
            }
        }

        return '无需告警';
    }

    /**
     * 根据规则对下单传入的名称进行校验
     *
     * @param $ruleType
     * @param $apiTicketName
     * @param $ticketName
     *
     * @return bool
     * Author : liucm
     * Date : 2021/8/23
     */
    private function _orderNameVerify($ruleType, $apiTicketName, $ticketName, $ruleText)
    {
        $apiTicketName = trim($apiTicketName);
        $ticketName    = trim($ticketName);
        $ruleText      = trim($ruleText);
        if ($ruleType == 1) {
            if ($ticketName != $apiTicketName) {
                return false;
            }
        }

        if ($ruleType == 2) {
            if (strpos($apiTicketName, $ruleText) === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * 发送钉钉消息
     *
     * @param $systemName
     * @param $ticketName
     * @param $apiTicketName
     * @param $orderNum
     * @param $apiOrderNum
     * @param $ruleText
     * @param $webhook
     * @param $signKey
     *
     * @return array|bool|mixed
     * Author : liucm
     * Date : 2021/8/23
     */
    private function _dingDingAlarm($systemName, $ticketName, $apiTicketName, $orderNum, $apiOrderNum, $ruleText, $webhook, $signKey)
    {
        $rule = "全部包含";
        if ($ruleText) {
            $rule = "校验规则:.包含以下文本“{$ruleText}”";
        }
        $sendMsg = '报警啦--门票名称校验不符告警';
        $sendMsg .= "\n -对接系统:" . $systemName;
        $sendMsg .= "\n -票付通门票名称:" . $ticketName;
        $sendMsg .= "\n -对接传入名称:" . $apiTicketName;
        $sendMsg .= "\n -备注:票付通订单号:{$orderNum},淘宝订单号:{$apiOrderNum}," . $rule;
        $sendMsg .= "\n -时间:" . date('Y-m-d H:i:s');

        $res = $this->_sendDingTalkGroupRobotMessageRaw($sendMsg, $webhook, $signKey);
        return $res;
    }

    /**
     * 发送短信告警
     *
     * @param $systemName
     * @param $apiTicketName
     * @param $orderNum
     * @param $sendTel
     * @param $applyDid
     *
     * @return array
     * Author : liucm
     * Date : 2021/8/23
     */
    private function _SMSAlarm($systemName, $apiTicketName, $orderNum, $sendTel, $applyDid)
    {
        $sendTel = strval($sendTel);

        $limit = $this->_limitTel($sendTel);

        if ($limit) {
            return ['msg' => '短信已超过10次'];
        }

        $msg = "{$systemName}传入{$apiTicketName}与校验规则不符,票付通订单号:{$orderNum}";

        $messageServiceApi = Container::pull(MessageService::class);
        [$approval, $sendRes] = $messageServiceApi->dispatchMessageSend(MessageService::V2, PFTSMSInterface::ISDIY, $sendTel,
            [$msg], $applyDid, $orderNum, '下单告警', '票付通', '', 1, true);
        if ($approval) {
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['下单告警', __METHOD__, [$sendTel, [$msg], $applyDid, $orderNum], $sendRes], JSON_UNESCAPED_UNICODE));
            }
        } else {
            /** @deprecated 放量结束后删除 */
            $fzZwxSmsLib = new \Library\MessageNotify\Platform\FzZwxSms($sendTel);
            $sendRes = $fzZwxSmsLib->customMsg($applyDid, '票付通', $msg, $sendTel, '', $orderNum);
            if (!EnvUtil::isRelease()) {
                pft_log('message_service', json_encode(['下单告警.old', __METHOD__, [$sendTel, [$msg], $applyDid, $orderNum], $sendRes], JSON_UNESCAPED_UNICODE));
            }
        }
        return $sendRes;
    }

    /**
     * 限制一分钟10次
     *
     * @param $sendTel
     *
     * @return bool
     * Author : liucm
     * Date : 2021/8/26
     */
    public function _limitTel($sendTel)
    {
        $redis        = \Library\Cache\Cache::getInstance('redis');
        $redisKey     = 'service:verifyNameJob:tel:' . $sendTel;
        $redisLockRes = $redis->get($redisKey);
        $time         = $redis->TTL($redisKey);
        if (empty($redisLockRes)) {
            $redis->set($redisKey, 1, '', 60);
            return false;
        }

        if ($redisLockRes > 9) {
            return true;
        }

        $redis->set($redisKey, $redisLockRes + 1, '', $time);
        return false;
    }

    /**
     * 钉钉告警
     *
     * @param $message
     * @param $webhook
     * @param $signKey
     * @param  false  $atAll
     * @param  array  $atMobile
     *
     * @return array|bool|mixed
     * Author : liucm
     * Date : 2021/8/23
     */
    private function _sendDingTalkGroupRobotMessageRaw($message, $webhook, $signKey, $atAll = false, $atMobile = [])
    {
        $signKey                 = strval($signKey);
        $webhook                 = strval($webhook);
        $timestamp               = $this->_getMicrotime();
        $signStr                 = $timestamp . "\n" . $signKey;
        $hash                    = hash_hmac('SHA256', $signStr, $signKey, true);
        $base64Str               = base64_encode($hash);
        $sign                    = urlencode($base64Str);
        $url                     = $webhook . "&timestamp={$timestamp}&sign={$sign}";
        $data                    = [
            'msgtype' => 'text',
        ];
        $data['text']['content'] = $message;

        if (!empty($atMobile)) {
            $data['at'] = [
                'isAtAll'   => $atAll,
                'atMobiles' => $atMobile,
            ];
        }

        $messageJson = json_encode($data, JSON_UNESCAPED_UNICODE);
        return curl_post($url, $messageJson, 80, 20, '/api/curl_post', ['Content-Type: application/json']);
    }

    /**
     * 获取毫秒时间戳
     * @return float
     */
    private function _getMicrotime()
    {
        list($msec, $sec) = explode(' ', microtime());
        $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

        return $msectime;
    }

    /**
     * 复制service 验证手机号的方法
     *
     * @param $tel
     *
     * @return bool
     * <AUTHOR>
     * @date 2021-07-05 11:41
     */
    public function ismobile($tel)
    {
        if (!preg_match('/^(14|15|13|18|17|19|16)\d{9}$/', $tel)) {
            return false;
        }

        return true;
    }
}
