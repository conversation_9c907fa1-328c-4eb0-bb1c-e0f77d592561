<?php
/**
 *
 * <AUTHOR>
 * @date   20241213
 *
 */
namespace Tpl;

class snapshot extends \Library\Controller {

    public function __construct() {
        $this->initPage();
    }

    public function index() {
        $this->display('snapshot/index');
    }
    // public function scenic() {
    //     $this->display('snapshot/scenic');
    // }
    // public function travel() {
    //     $this->display('snapshot/travel');
    // }
    // public function show() {
    //     $this->display('snapshot/show');
    // }
    // public function line() {
    //     $this->display('snapshot/line');
    // }
    public function package() {
        $this->display('snapshot/package');
    }

    // public function food() {
    //     $this->display('snapshot/food');
    // }
    // public function hotel() {
    //     $this->display('snapshot/hotel');
    // }
    // public function specialty() {
    //     $this->display('snapshot/specialty');
    // }
    // public function public() {
    //     $this->display('snapshot/public');
    // }
    // public function annualpackage() {
    //     $this->display('snapshot/annualpackage');
    // }
    // public function time() {
    //     $this->display('snapshot/time');
    // }
    // public function exchange() {
    //     $this->display('snapshot/exchange');
    // }
}