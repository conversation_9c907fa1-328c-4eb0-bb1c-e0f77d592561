<?php
/**
 * 入园人数
 * 为了相应的url比较好看，方法名都直接用小写的单词，不加下划线和驼峰
 *
 * <AUTHOR>
 * @date   2017-5-18
 *
 */
namespace Tpl;

class tickets extends \Library\Controller {

    public function __construct() {
        $this->initPage();
    }

    public function index() {
        $this->display('tickets/index');
    }
    public function scenic() {
        $this->display('tickets/scenic');
    }
    public function travel() {
        $this->display('tickets/travel');
    }
    public function quick() {
        $this->display('tickets-quick/scenic');
    }
    public function show() {
        $this->display('tickets/show');
    }
    public function line() {
        $this->display('tickets/line');
    }
    public function package() {
        $this->display('tickets/package');
    }

    public function food() {
        $this->display('tickets/food');
    }
    public function hotel() {
        $this->display('tickets/hotel');
    }
    public function specialty() {
        $this->display('tickets/specialty');
    }
    public function public() {
        $this->display('tickets/public');
    }
    public function annualpackage() {
        $this->display('tickets/annualpackage');
    }
    public function insurance() {
        $this->display('tickets/insurance');
    }
    public function time() {
        $this->display('tickets/time');
    }
    public function exchange() {
        $this->display('tickets/exchange');
    }
    public function snapshotShow() {
        $this->display('tickets/snapshotShow');
    }
}