<?php
/**
 * 下单和支付相关rpc接口
 * @auther dwer.cn
 * @date 2021-09-08
 *
 */

namespace JsonRpc\Order;

use Business\CardSolution\Order;
use Business\Lease\AutoBoxBiz;
use Business\Order\MergeOrder;
use Business\Pay\PayQuery;
use JsonRpc\Base;

class Pay extends Base
{
    //需要写日志的接口
    public $writeLogMethodList = [
        'get_pay_money', //获取支付金额
        'pay_order',
    ];

    /**
     * 获取支付金额
     * <AUTHOR>
     * @date 2021/9/8
     *
     * @param  string  $tradeId  下单接口返回的交易流水号
     *
     * @return array
     */
    public function get_pay_money(string $tradeId = '')
    {
        if (!$tradeId) {
            return $this->returnData(400, '缺少参数[trade_id]');
        }

        try {
            $mergeModel   = new MergeOrder();
            $isMergeOrder = $mergeModel->isCombineOrder($tradeId);

            if ($isMergeOrder) {
                //合并下单
                $mergeQueryRes = $mergeModel->getTradeIdTotleMoneyAndOnsaleMoney($tradeId);

                $totalMoney    = intval($mergeQueryRes['totalMoney']);
                $discountMoney = intval($mergeQueryRes['totalOnSaleMoney']);
            } else {
                //单独下单
                $ordernum        = $tradeId;
                $orderQueryModel = new \Model\Order\OrderQuery('localhost');
                $mergeQueryRes   = $orderQueryModel->get_order_total_fee($ordernum);

                $totalMoney    = intval($mergeQueryRes);
                $discountMoney = 0;
            }

            $data = [
                'total_money'    => $totalMoney,
                'discount_money' => $discountMoney,
            ];

            return $this->returnData(200, '成功', $data);

        } catch (\Exception $e) {
            $exceptionMsg = $e->getMessage();

            return $this->returnData(500, "系统异常，请重试[{$exceptionMsg}]");
        }
    }

    /**
     * 内部业务线支付接口
     * <AUTHOR>
     * @date 2021/9/8
     *
     * @param  string  $tradeId  交易流水号
     * @param  string  $payTradeNo  支付系统的支付流水号
     * @param  int  $source  支付渠道 0=支付宝, 1=微信原生渠道, 9=平安银行, 16=招行, 17=银联商务, 18=银联商务POS, 19=农商（威富通）,20=易宝支付, 21=银联云闪付, 22=农行支付, 23=建设银行, 24=浙江农信丰收互联, 25=江西农商行, 26=银联会员卡, 33=福州市民卡
     * @param  int  $totalMoney  收款金额 - 单位分
     * @param  bool  $isPftReceipt  是否票付通收款，独立收款的就是false
     * @param  array  $payInfo  收款账号等信息
     *
     * @return array
     */
    public function pay_order(string $tradeId = '', string $payTradeNo = '', int $source = 0, int $totalMoney = 0, bool $isPftReceipt = true, array $payInfo = [])
    {
        if (!$tradeId) {
            return $this->returnData(400, '缺少参数[trade_id]');
        }

        if (!$payTradeNo) {
            return $this->returnData(400, '缺少参数[pay_trade_no]');
        }

        if ($totalMoney <= 0) {
            return $this->returnData(400, '支付金额需要大于0');
        }

        try {
            $payApi   = new \Business\Pay\PayComplete();
            $payToPft = $isPftReceipt ? 1 : 0;

            $payRes = $payApi->payComplete($tradeId, $payTradeNo, $source, $totalMoney, $payToPft, $payInfo);

            if ($payRes['code'] == 200) {
                return $this->returnData(200, "支付成功");
            } else {
                $errCode = $payRes['code'];

                return $this->returnData(205, "支付失败[{$errCode}]");
            }
        } catch (\Exception $e) {
            $exceptionCode = $e->getCode();

            return $this->returnData(500, "系统异常，请重试[{$exceptionCode}]");
        }
    }
    /**
     * 三方在线支付查询信息
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @param  array  $content  数据
     *
     * @return array
     */
    public function onlinePayThirdQuery(array $content){
        if (empty($content)){
            return $this->returnData(204, '参数有误');
        }
        $payQueryBiz = new PayQuery();
        return $payQueryBiz->onlinePayThirdQueryService($content);
    }

    /**
     * 获取在线支付订单根据三方流水号和时间查询
     * <AUTHOR>
     * @date 2021/9/13
     *
     * @param  array  $content  数据
     *
     * @return array
     */
    public function getOnlinePayOrderByThirdNoAndTime(array $content)
    {
        if (empty($content)) {
            return $this->returnData(204, '参数有误');
        }
        $tradeNo   = $content['tradeNo'] ?? '';
        $beginTime = $content['beginTime'] ?? 0;
        $endTime   = $content['endTime'] ?? 0;
        if (!$tradeNo || !$beginTime || !$endTime) {
            return $this->returnData(204, '参数有误');
        }
        $payQueryBiz = new PayQuery();

        return $payQueryBiz->getOnlinePayOrderByThirdNoAndTimeService($tradeNo, $beginTime, $endTime);
    }

    /**
     * 一卡通支付完成后的后续操作
     *
     * @param array $requestData 回调的参数
     *
     *
     * {
     * "pay_channel":2,
     * "notify_type":1,
     * "out_trade_no":"63336381012091",
     * "amount":"11000",
     * "status":"SUCCESS",
     * "client_id":"1",
     * "attach":"",
     * "pay2pft":false,
     * "tradeno":"4200001162202110055239807332",
     * "appId":"f0ec96ad2c3848b5b810e7aadf369e2g",
     * "timestamp":"20211006113319",
     * "nonce":"z3kag5uevlwl0uglf3r2ijotdvvkncfe",
     * "signature":"F/N6XWF78cvFeXPvOZsTL1BWKkYgLlR765cqKY6gHWg="
     * "pay_id":"F/N6XWF78cvFeXPvOZsTL1BWKkYgLlR765cqKY6gHWg="
     * "pay_mode":"F/N6XWF78cvFeXPvOZsTL1BWKkYgLlR765cqKY6gHWg="
     * }
     *
     * @date 2022/08/12
     * @auther yangjianhui
     * @return array
     */
    public function afterOneCardPay($requestData)
    {
        if (empty($requestData) || !is_array($requestData)) {
            return $this->returnData(203, "参数错误");
        }
        if (empty($requestData['out_trade_no']) || empty($requestData['pay_id'])) {
            return $this->returnData(203, "支付订单号或者交易中心订单号不能为空");
        }
        $orderBiz = new Order();
        $result   = $orderBiz->afterOneCardPay($requestData['content']);
        if ($result['code'] != 200) {
            pft_log('pre_card_pay/error', json_encode($result, JSON_UNESCAPED_UNICODE) . "支付参数" .
                                          json_encode($requestData, JSON_UNESCAPED_UNICODE));
        }

        return $result;
    }

    //租赁柜支付结果通知
    public function autoBoxPayCompleteNotify(array $content): array
    {
        if (empty($content)) {
            return $this->returnData(40030202, '参数有误');
        }
        try {
            return AutoBoxBiz::getInstance()->autoBoxPayCompleteNotify($content);
        } catch (\Throwable $e) {
            pft_log('autoBoxPayCompleteNotify', json_encode($content, JSON_UNESCAPED_UNICODE) . " 异常信息: " . $e->getMessage());
            return $this->returnData(40030203, $e->getMessage());
        }
    }
}
