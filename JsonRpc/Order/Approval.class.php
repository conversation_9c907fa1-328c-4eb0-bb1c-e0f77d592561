<?php
namespace JsonRpc\Order;

use Business\Common\CommonConfig;
use Business\Order\Refund;
use Business\Order\RefundApprovalCenterService\AbstractApprovalCenterService;
use Business\Order\RefundApprovalCenterService\PackRefundApprovalService;
use Business\Order\RefundApprovalCenterService\PackTicketChangeApprovalService;
use Business\Order\RefundApprovalCenterService\RefundApprovalService;
use Business\Order\RefundApprovalCenterService\TicketChangeApprovalService;
use JsonRpc\Base;
use Model\Order\OrderTerminal;
use Model\Order\RefundAuditModel;
use Business\Product\Ticket as TicketBiz;

class Approval extends Base
{
   //请求日志： /alidata/log/site/jsonrpc/response/
   //应答日志：/alidata/log/site/jsonrpc/request/
    //需要写日志的接口
    public $writeLogMethodList = [
        'ticketRefund', //退票接口
        'ticketRejectedRefund',
        'completeTicketRefund',
        'mainTicketRefund',
        'completeMainTicketRefund',
        'mainTicketRejectedRefund',
        'sonTicketRefund',
        'completeSonTicketRefund',
        'sonTicketRejectedRefund',
        'rejectApproval',
        'ticketRevoke',
        'ticketRejectedRevoke',
        'completeTicketRevoke',
        'mainTicketRevoke',
        'completeMainTicketRevoke',
        'sonTickRevoke',
        'completeSonTicketRevoke',
        'sonTicketRejectedRevoke',
        'ticketChange',
        'completeTicketChange',
        'ticketChangeRejected',
        'mainTicketChange',
        'mainTicketChangeRejected',
        'completeMainTicketChange',
        'sonTicketChange',
        'completeSonTicketChange',
        'sonTicketChangeRejected',
        'rejectTicketChangeApproval',
        'getRefundApprovalList',
        'addRefundApproval',
        'updateRefundApproval',
        'updateRevokeApproval',
        'terminalRevoke',
        'batchAddRefundApproval'
    ];

    /**
     * 退票 业务节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function ticketRefund($refundParams,$approvalParams)
    {
        $refundApprovalService = new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $refundApprovalService->tickAgreeRefund($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 退票拒绝
     * @param $params
     * @return array
     */
    public function ticketRejectedRefund($params){
        $refundApprovalService =  new RefundApprovalService();
        return $refundApprovalService->tickRejectedRefund($params);
    }

    /**
     * 子票完成节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function completeTicketRefund($refundParams,$approvalParams)
    {
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $refundApprovalService->completeTickRefund($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 主票业务处理节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function mainTicketRefund($refundParams,$approvalParams)
    {
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        //发起子票退

        $auditData = $params['otherData']['audit_data']['main_request'];
        $auditData['moreData']['is_son_audit'] = true;
        $auditData['moreData']['mainApprovalCode'] = $approvalParams['processInstanceId'];
        $RefundAudit = new \Business\Order\RefundAudit();

        $res = $RefundAudit->addRefundAudit($auditData['orderNum'],$auditData['targetTicketNum'],$auditData['operatorID'],$auditData['source']
            ,$auditData['requestTime'],$auditData['orderInfo'],$auditData['orderInfo']['tnum'],$auditData['remoteSn'],$auditData['pftSn'],$auditData['refundQuantity']
            ,$auditData['sysException'],$auditData['personIdList'],$auditData['timeOrderRefund'],$auditData['refundAuditExt'],$auditData['moreData']);
        $hasSubOrderInApproval = $res['data']['hasSubOrderInApproval'] ?? true;
        pft_log('order/refund_audit',json_encode(['主票发起子票',$res,$refundParams,$approvalParams],JSON_UNESCAPED_UNICODE));
//        if(!$params['waitReturn']){
//            $refundApprovalService->approvalNotify($params,$res);
//        }
        if($res['code']!= 200){
            $refundAuditModel = new \Business\Order\RefundAudit();
            $refundAuditModel->rejectMainOrder($auditData['orderNum'],$res['msg'],$auditData['operatorID'],$auditData['moreData']);
            pft_log('order/refund_audit',json_encode(['主票发起子票拒绝',$auditData],JSON_UNESCAPED_UNICODE));
            $res['code'] = $res['data']['err_code']?? 0;
            $res['msg'] = "{$res['data']['orderNum']}_{$res['data']['inner_msg']}_{$res['data']['err_msg']}";
            $checkJobData = [
                'job_type' => 'order_approval_notice',
                'job_data' => [
                    'params' => $params,
                    'res' => $res
                ],
            ];
            \Library\Resque\Queue::push('order', 'Order_Job', $checkJobData);
            return $res;
        }
        else{
            pft_log('order/refund_audit',json_encode(['主票发起子票退票',$hasSubOrderInApproval,$params,$res],JSON_UNESCAPED_UNICODE));
            $res['msg'] = '需等待子票完成退票审核';
            if(!$hasSubOrderInApproval){
                $res['msg'] = '审核成功,子票均审核通过,联动退子票';
            }
            $checkJobData = [
                'job_type' => 'order_approval_notice',
                'job_data' => [
                    'params' => $params,
                    'res' => $res
                ],
            ];
            \Library\Resque\Queue::push('order', 'Order_Job', $checkJobData);
            return $res;
        }
    }

    /**
     * 主票完成处理节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function completeMainTicketRefund($refundParams,$approvalParams)
    {
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        //由于套票重试套票，没法知道已退的票是哪张，所以不允许重试退票。
        //TODO 后续可根据remoteSn(reqSerialNumber) 来辨别
        $refundApprovalService = new RefundApprovalService();
        $nodePassName = $refundApprovalService::NOTIFY_NODE_PASS;
        $res = $refundApprovalService->queryApprovalFormInfo($params['processInstanceId']);
        if(!empty($res)){
            $data = array_column($res,null,'activityId');
            $allowRetryStatus = ['-1'];
//            $allowRefundStatus = ['-1','2','4','5'];
//            if(!array_key_exists($nodePassName,$data) || !in_array($data[$nodePassName]['status'],$allowRefundStatus)){
//                return ['code'=> 214, 'msg' => '主票审批状态异常，不允许退票', 'data'=>[]];
//            }
            if(!array_key_exists($nodePassName,$data) || !in_array($data[$nodePassName]['status'],$allowRetryStatus)){
                $res = ['code'=> 204, 'msg' => '主票不支持重试', 'data'=>[]];
                $checkJobData = [
                    'job_type' => 'order_approval_notice',
                    'job_data' => [
                        'params' => $params,
                        'res' => $res
                    ],
                ];
                \Library\Resque\Queue::push('order', 'Order_Job', $checkJobData);
                return $res;
            }
            $packRefundApprovalService = new PackRefundApprovalService();
            $objectParams['procInsId'] = $params['processInstanceId'];
            $objectParams['orderNum'] = $params['orderNum'];
            $operatorID = $params['operatorID'];
            $judgeResult = $packRefundApprovalService->judgePackApproval($objectParams,$operatorID);
            pft_log('order/refund',json_encode(['completeMainTicketRefund',$judgeResult,$params['orderNum'],$params['orderNum']],JSON_UNESCAPED_UNICODE));
            if($judgeResult['main_approval_exist'] && $judgeResult['main_approval_audit_wait']){
                $allSubApprovalFinish = $judgeResult['all_sub_approval_finish'];
                if(!$allSubApprovalFinish){
                    $allSubApprovalArray = [];
                    foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                        if($subInfo['nodeInfo']['has_approval'] && $subInfo['nodeInfo']['exist_node']
                            && !$subInfo['nodeInfo']['son_wait_node'] && $params['orderNum']!= $subInfo['orderNum']){
                            $allSubApprovalArray[$subInfo['orderNum']] = $processInstanceId;
                        }
                    }
                    if(count($allSubApprovalArray)<1){
                        $allSubApprovalFinish = true;
                    }
                }
                $res = ['code'=> 294, 'msg' => '需等待所有子票完成退票审核', 'data'=>[]];
                if(!$allSubApprovalFinish){
                    $checkJobData = [
                        'job_type' => 'order_approval_notice',
                        'job_data' => [
                            'params' => $params,
                            'res' => $res
                        ],
                    ];
                    \Library\Resque\Queue::push('order', 'Order_Job', $checkJobData);
                    return $res;
                }
            }
        }

        $PackRefundApprovalService =  new PackRefundApprovalService();
        $res = $PackRefundApprovalService->completeMainTickRefund($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 主票拒绝节点
     * @param $params
     * @return array
     */
    public function mainTicketRejectedRefund($params){
        $refundApprovalService =  new PackRefundApprovalService();
        return $refundApprovalService->mainTickRejectedRefund($params);
    }

    /**
     * 子票业务节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function sonTicketRefund($refundParams,$approvalParams)
    {
        $packApprovalService =  new PackRefundApprovalService();
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $packApprovalService->sonTickRefund($params);
        if($res['code'] == 200){
            sleep(1);
            //存在跳过前审节点和三方审核重复的情况，这么做是为了反正子票审核通过被覆写
            $judgeResult = $refundApprovalService->judgeApprovalNodeByProcessIdForSubInfo($params['processInstanceId']);
            if($judgeResult['has_approval'] && $judgeResult['son_wait_node']){
                return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
            }
        }
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 子票完成节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function completeSonTicketRefund($refundParams,$approvalParams)
    {
        sleep(1);
        $packApprovalService =  new PackRefundApprovalService();
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);

        $packRefundApprovalService = new PackRefundApprovalService();

        $operatorID = $params['operatorID'];
        $mainOrder = $params['otherData']['pack_order'];
        $currentObjectParams['procInsId'] = $params['processInstanceId'];
        $currentObjectParams['orderNum'] = $params['orderNum'];
        $judgeResult = $refundApprovalService->getApprovalNodeInfo($currentObjectParams,$operatorID,$refundApprovalService::NOTIFY_NODE_BUSINESS);
        if($judgeResult['has_approval'] && $judgeResult['exist_node']){
            $objectParams['procInsId'] = $params['otherData']['audit_data']['mainApprovalCode'];
            $objectParams['orderNum'] = $params['otherData']['pack_order'];
            $judgeResult = $packRefundApprovalService->judgePackApproval($objectParams,$operatorID);
            pft_log('order/refund',json_encode(['completeSonTicketRefund',$judgeResult,$mainOrder,$params['orderNum']],JSON_UNESCAPED_UNICODE));
            if($judgeResult['main_approval_exist'] && $judgeResult['main_approval_audit_wait']){
                $allSubApprovalFinish = $judgeResult['all_sub_approval_finish'];
                if(!$allSubApprovalFinish){
                    $allSubApprovalArray = [];
                    foreach ($judgeResult['sub_approval_node_info'] as $processInstanceId => $subInfo){
                        if($subInfo['nodeInfo']['has_approval'] && $subInfo['nodeInfo']['exist_node']
                            && !$subInfo['nodeInfo']['son_wait_node'] && $params['orderNum']!= $subInfo['orderNum']){
                            $allSubApprovalArray[$subInfo['orderNum']] = $processInstanceId;
                        }
                    }
                    if(count($allSubApprovalArray)<1){
                        $allSubApprovalFinish = true;
                    }
                }
                if(!$allSubApprovalFinish){
                    return ['code'=> 204, 'msg' => '非法退票请求，等待其他子票审核', 'data'=>[]];
                }
            }
        }
        $res = $packApprovalService->completeSonTickRefund($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 子票拒绝节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function sonTicketRejectedRefund($params){
        $refundApprovalService =  new PackRefundApprovalService();
        return $refundApprovalService->sonTickRejectedRefund($params);
    }

    /**
     * 拒绝回调
     * @param $content
     * @param $applyId
     * @param $mainAccountId
     * @param $status
     * @param $processBusinessKey
     * @param $processBusinessCode
     * @param $comment
     * @param $opId
     * @param $processInstanceId
     * @return array
     */
    public function rejectApproval($content,$applyId,$mainAccountId,$status,$processBusinessKey,$processBusinessCode,$comment,$opId,$processInstanceId){
        $params = [
            'processContent' => $content,
            'applyId' => $applyId,
            'mainAccountId' => $mainAccountId,
            'status' => $status,
            'processBusinessKey' => $processBusinessKey,
            'processBusinessCode' => $processBusinessCode,
            'approvalComment' => $comment,
            'opId' => $opId,
            'processInstanceId' => $processInstanceId
        ];
        if(in_array($params['processContent']['refundParams']['modifyType'],[0,1])){
            switch ($content['refundParams']['otherData']['ifPack']){
                case 1:
                    $res = self::mainTicketRejectedRevoke($params);
                    break;
                case 2:
                    $res = self::sonTicketRejectedRevoke($params);
                    break;
                default:
                    $res = self::ticketRejectedRevoke($params);
                    break;
            }
        }
        else{switch ($content['refundParams']['otherData']['ifPack']){
            case 1:
                $res = self::mainTicketRejectedRefund($params);
                break;
            case 2:
                $res = self::sonTicketRejectedRefund($params);
                break;
            default:
                $res = self::ticketRejectedRefund($params);
                break;
        }}
        if($res['code'] == 243){
            $res['code'] = 200;
        }
        if($res['code'] == 200){
            $refundApprovalService =  new RefundApprovalService();
            $refundApprovalService->updateProcessExt($processInstanceId,-1,6);
        }
        return $res;
    }

    //撤销撤改

    /**
     * 撤销撤改
     * @param $RevokeParams
     * @param $approvalParams
     * @return array
     */
    public function ticketRevoke($RevokeParams,$approvalParams)
    {
        $refundApprovalService = new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
        $params = $refundApprovalService->convertParams($RevokeParams,$approvalParams,$noticeType);
        $res = $refundApprovalService->tickAgreeRevoke($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 撤销撤改拒绝
     * @param $params
     * @return array|mixed
     */
    public function ticketRejectedRevoke($params){
        $refundApprovalService =  new RefundApprovalService();
        return $refundApprovalService->tickRejectedRevoke($params);
    }

    /**
     * 撤销撤改完成
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function completeTicketRevoke($refundParams,$approvalParams)
    {
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $refundApprovalService->completeTickRevoke($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 主票撤销撤改 暂不支持
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function mainTicketRevoke($refundParams,$approvalParams)
    {
        return ['code'=> 200, 'msg' => '主票不支持撤销撤改', 'data'=>[]];
    }

    /**
     * 主票撤销撤改 暂不支持
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function completeMainTicketRevoke($refundParams,$approvalParams)
    {
        return ['code'=> 200, 'msg' => '主票不支持撤销撤改', 'data'=>[]];
    }

    /**
     * 主票撤销撤改 暂不支持
     * @param $params
     * @return array
     */
    public function mainTicketRejectedRevoke($params){
        return ['code'=> 200, 'msg' => '主票不支持撤销撤改', 'data'=>[]];
    }

    /**
     * 子票撤销撤改 业务节点
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function sonTickRevoke($refundParams,$approvalParams)
    {
        $packApprovalService =  new PackRefundApprovalService();
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_BUSINESS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $packApprovalService->sonTickRevoke($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 子票撤销撤改完成
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function completeSonTicketRevoke($refundParams,$approvalParams)
    {
        $packApprovalService =  new PackRefundApprovalService();
        $refundApprovalService =  new RefundApprovalService();
        $noticeType = $refundApprovalService::NOTIFY_TYPE_PASS;
        $params = $refundApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $packApprovalService->completeSonTickRevoke($params);
        $refundApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    /**
     * 子撤销撤改拒绝
     * @param $refundParams
     * @param $approvalParams
     * @return array
     */
    public function sonTicketRejectedRevoke($params){
        $refundApprovalService =  new PackRefundApprovalService();
        return $refundApprovalService->sonTickRejectedRevoke($params);
    }

    public function ticketChange($refundParams,$approvalParams){

        return ['code'=> 200, 'msg' => '暂不支持改签业务节点', 'data'=>[]];
    }

    public function completeTicketChange($refundParams,$approvalParams){
        $ticketChangeApprovalService =  new TicketChangeApprovalService();
        $noticeType = $ticketChangeApprovalService::NOTIFY_TYPE_PASS;
        $params = $ticketChangeApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $ticketChangeApprovalService->completeTickTicketChange($params);
        $ticketChangeApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    public function ticketChangeRejected($params){

        $ticketChangeApprovalService =  new TicketChangeApprovalService();
        return $ticketChangeApprovalService->tickRejectedTicketChange($params);
    }

    public function mainTicketChange($params){
        return ['code'=> 200, 'msg' => '暂不支持套票改签', 'data'=>[]];
    }

    public function mainTicketChangeRejected($params){
        return ['code'=> 200, 'msg' => '暂不支持套票改签', 'data'=>[]];
    }

    public function completeMainTicketChange($params){
        return ['code'=> 200, 'msg' => '暂不支持套票改签', 'data'=>[]];
    }

    public function sonTicketChange($params){
        return ['code'=> 200, 'msg' => '暂不支持改签业务节点', 'data'=>[]];
    }

    public function completeSonTicketChange($refundParams,$approvalParams){
        sleep(2);
        $PackTicketChangeApprovalService =  new PackTicketChangeApprovalService();
        $ticketChangeApprovalService =  new TicketChangeApprovalService();
        $noticeType = $ticketChangeApprovalService::NOTIFY_TYPE_PASS;
        $params = $ticketChangeApprovalService->convertParams($refundParams,$approvalParams,$noticeType);
        $res = $PackTicketChangeApprovalService->completeSonTicketChange($params);
        $ticketChangeApprovalService->approvalNotify($params,$res);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>[]];
    }

    public function sonTicketChangeRejected($params){
        $PackTicketChangeApprovalService =  new PackTicketChangeApprovalService();
        return $PackTicketChangeApprovalService->sonTicketChangeRejected($params);
    }

    public function rejectTicketChangeApproval($content,$applyId,$mainAccountId,$status,$processBusinessKey,$processBusinessCode,$comment,$opId,$processInstanceId){
        $params = [
            'processContent' => $content,
            'applyId' => $applyId,
            'mainAccountId' => $mainAccountId,
            'status' => $status,
            'processBusinessKey' => $processBusinessKey,
            'processBusinessCode' => $processBusinessCode,
            'approvalComment' => $comment,
            'opId' => $opId,
            'processInstanceId' => $processInstanceId
        ];
        switch ($content['refundParams']['otherData']['ifPack']){
            case 1:
                $res = self::mainTicketChangeRejected($params);
                break;
            case 2:
                $res = self::sonTicketChangeRejected($params);
                break;
            default:
                $res = self::ticketChangeRejected($params);
                break;
        }
        if($res['code'] == 200){
            $refundApprovalService =  new RefundApprovalService();
            $refundApprovalService->updateProcessExt($processInstanceId,-1,7);
        }
        return $res;
    }

    //开给票务的rpc接口

    /**
     * 获取审核列表
     * @param $busParam
     * @return array
     */
    public function getRefundApprovalList($busParam){
        $orderTerminal = new OrderTerminal();
        $orderArr = $busParam['orderNumArr'] ?? [];
        $statusArr = $busParam['statusArr']?? [];
        $sTypeArr = $busParam['sTypeArr']?? [];
        $data = $orderTerminal->getRefundApprovalList($orderArr, $statusArr , $sTypeArr);
        return ['code'=> 200, 'msg' => '请求成功', 'data'=>$data];
    }


    public function addRefundApproval($busParam){
        $orderNum = $busParam['orderNum'];
        $modifyNum = $busParam['modifyNum'];
        $terminal = $busParam['terminal'];
        $salerId = $busParam['salerId'];
        $lid = $busParam['lid'];
        $tid = $busParam['tid'];
        $modifyType = $busParam['modifyType'];
        $targetTNum = $busParam['targetTNum'];
        $operatorId = $busParam['operatorId'] ?? 1;
        $auditStatus = $busParam['auditStatus'] ?? 0;
        $auditorId = $busParam['auditorId']?? 0;
        $requestTime = $busParam['requestTime']?? 0;
        $auditTime = $busParam['auditTime']?? 0;
        $remoteSn = $busParam['remoteSn'] ?? "{$orderNum}_{$modifyType}_{$targetTNum}";
        $systemSn = $busParam['systemSn'] ?? "{$orderNum}_{$modifyType}_{$modifyNum}";
        $sysException = $busParam['sysException'] ?? false;
        $otherData = $busParam['otherData'];
        $auditData = json_decode($otherData['audit_data'],true);
        $auditNote = $busParam['auditNote'] ?? $auditData['cancel_audit_remark'];

        $auditData['op_id'] = $operatorId; //将操作人id也添加进该审批数据中
        $otherData['audit_data'] = $auditData;

        //增加退票审核备注校验
//        $sid = $otherData['apply_did']; //获取供应商id
//        $commonConfig = new CommonConfig();
//        $resConfig = $commonConfig->getRefundApplyConfig($sid);
//        if($resConfig['code'] !=200){
//            return $resConfig;
//        }
//        $isNeedRefundApply = $resConfig['data']['need_refund_apply'];
//        if($isNeedRefundApply && empty($auditNote)){
//            return ['code'=> 203, 'msg' => '需填写退票申请备注', 'data'=>[]];
//        }

        if(in_array($modifyType,[0,1])){
            $otherData['revoke_data'] = [
                'dadmin'     => $otherData['dadmin'],
                'ordernum'   => $orderNum,
                'terminal'   => $terminal,
                'salerid'    => $salerId,
                'lid'        => $lid,
                'tid'        => $tid,
                'stype'      => $modifyType,
                'tnum'       => $targetTNum,
                'dstatus'    => 0,
                'stime'      => date('Y-m-d H:i:s'),
                'fxid'       => $operatorId,
                'ltitle'     => $otherData['land_name'],
                'ttitle'     => $otherData['ticket_name'],
                'aids'       => $otherData['aids'],
                'concat_id'  => $otherData['concat_id'],
                'apply_did'  => $otherData['apply_did'],
                'ifpack'     => $otherData['ifpack'],
                'dcodeURL'   => $otherData['dcodeURL'],
                'pack_order' => $otherData['pack_order'],
                'p_type'     => $otherData['p_type'],
                'audit_data' => $otherData['audit_data'],
            ];
            $ticketModel  = new \Model\Product\Ticket();
            $revoke_audit = $ticketModel->getTicketInfoById($tid, 'revoke_audit');
            $otherData['is_need_approval'] = true;
            if ($revoke_audit['revoke_audit'] == 0 || $otherData['p_type'] == 'I' ) {
                $otherData['is_need_approval']  = false;
            }
        }
        if(in_array($modifyType,[2,3])){
            $ticketBiz = new TicketBiz();
            $ticketRes = $ticketBiz->getListForOrderNew($tid);
            $tmpTicketInfo = $ticketRes['ticket_info'];
            $ticketExtInfo = $ticketRes['ticket_ext_info'] ?: [];
            $ticketInfo = array_merge($tmpTicketInfo, $ticketExtInfo);
            if(isset($ticketInfo['Mdetails']) && $ticketInfo['Mdetails'] == 1 && $ticketInfo['sourceT'] > 0){
                $otherData['isCancelThirdSystem'] = true;
                $otherData['isThirdOrder'] = true;
            }
        }
        $refundAuditModel = new RefundAuditModel();
        $sqlId = $refundAuditModel->addRefundAudit($orderNum,$modifyNum,$terminal,$salerId,$lid,$tid,$modifyType,$targetTNum,
            $operatorId,$auditStatus,$auditorId,$requestTime,$auditNote,$auditTime,$remoteSn,$systemSn,$sysException,$otherData);
        $isWhite = (new RefundApprovalService())::isTradingVolumeMerchant($orderNum);
        if(!$isWhite && !$otherData['is_need_approval']){
            $approvalComment =  "订单撤销/撤改({$auditData['cancel_audit_remark']})";
            $revokeParams['approvalComment'] = $approvalComment; //备注
            $revokeParams['auditStatus'] = 1;
            $revokeParams['terminalChangeId'] = $sqlId;
            $revokeParams['operatorID'] = $operatorId;
            self::updateRevokeApproval($revokeParams);
        }
        if(is_bool($sqlId)){
            return ['code'=> 204, 'msg' => '发起审核失败', 'data'=>[]];
        }
        else{
            return ['code'=> 200, 'msg' => '发起审核成功', 'data'=>['sql_id'=>$sqlId]];
        }
    }

    public function updateRefundApproval($busParam){
        $RefundApprovalService = new RefundApprovalService();
        $refundParams['auditStatus'] = $busParam['auditStatus'];
        $refundParams['orderNum'] = $busParam['orderNum'];//订单号modifyType
        $refundParams['modifyType'] = $busParam['modifyType']; //变更类型   0：撤改   1：撤销  2：修改  3：取消 4：改签
        $refundParams['otherData']['terminal_change_id'] = $busParam['terminalChangeId']; //审核ID
        $refundParams['approvalComment'] = $busParam['approvalComment']; //备注
        $refundParams['operatorID'] = $busParam['operatorID'];
        $refundParams['isMainRefund'] = $busParam['isMainRefund'] ?? false;
        $refundParams['isApprovalFund'] = $busParam['isApprovalFund'] ?? false;
        return $RefundApprovalService->commonTickRefund($refundParams);
    }

    public function updateRevokeApproval($busParam){
        $RefundApprovalService = new RefundApprovalService();
        $revokeParams['approvalComment'] = $busParam['approvalComment']; //备注
        $revokeParams['auditStatus'] = $busParam['auditStatus'];
        $revokeParams['id'] = $busParam['terminalChangeId'];
        $revokeParams['operatorID'] = $busParam['operatorID'];
        return $RefundApprovalService->commonTickRevoke($revokeParams);
    }

    public function terminalRevoke($busParam){
        $refund = new Refund();
        $orderNum = $busParam['orderNum'];
        $surplusNum = $busParam['surplusNum'];
        $terminal = $busParam['terminal'];
        $opMember = $busParam['opMember'];
        $cancelIdxArr = $busParam['cancelIdxArr'];
        $isRefundDeposit = $busParam['isRefundDeposit'];
        $isRefundOverPay = $busParam['isRefundOverPay'];
        $revokeNum = $busParam['revokeNum'];
        $cancelAuditRemark = $busParam['cancelAuditRemark'];
        $isSmsNotice = $busParam['isSmsNotice'];
        $cancelSpecialArr = $busParam['cancelSpecialArr'];
        return $refund->TerminalRevoke($orderNum, $surplusNum, $terminal, $opMember, $cancelIdxArr, $isRefundDeposit, $isRefundOverPay,
            $revokeNum, $cancelAuditRemark, $isSmsNotice, $cancelSpecialArr);
    }

    public function batchAddRefundApproval($busParam){
        if(empty($busParam)){
            return ['code'=> 204, 'msg' => '参数异常，发起审核失败', 'data'=>[]];
        }
//        $modifyType = reset($busParam)['modifyType'];
        $refundAuditModel = new RefundAuditModel();
        return $refundAuditModel->batchAddRefundAudit($busParam);
    }
}