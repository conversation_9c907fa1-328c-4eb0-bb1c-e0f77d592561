<?php

namespace JsonRpc\Order\OrderRequestDO;

class CombineOrderRequestDO extends RequestBase
{
    /**
     * 是否是散客购票,冗余字段,等同于下面的is_sale实际使用的是is_sale
     * 必填
     * @var bool $sale
     */
    public $sale;
    /**
     * 购买用户id
     * 必填
     * @var int $member_id
     */
    public $member_id;
    /**
     * 购买用户，当前操作人id
     * 必填
     * @var int $op_id
     */
    public $op_id;
    /**
     * 是否是散客购票
     * 必填
     * @var bool $is_sale
     */
    public $is_sale;
    /**
     * 游玩日期.格式：YYYY-MM-DD
     * 必填
     * @var string $begin_time
     */
    public $begin_time;
    /**
     * 购买渠道.枚举：[0=正常分销商下单,1= 普通用户支付,2=用户手机支付,9=会员卡购票,10=云票务,
     * 11=微信商城,12=自助机,13=pc店铺,14=闸机购票,15=智能终端,16=计调下单,17=淘宝码商,18=年卡,
     * 19=微信端,20=OTA,22=一卡通,23=套票子票,24=抱团,33=e福州下单,34=拼团下单,35=积分商城,
     * 38=招行,39=银联商务,40=银联pos支付,41=威富通,42=易宝,43=微票房,44=报团计调下单,45=云闪付,
     * 46=断网离线订单,49=桌面云票务,50=APP,55=特殊团队预约,56=微信小程序,57=抖音-微票房,59=团购导码,58=阿里供销]
     * 必填
     * @var int $channel
     */
    public $channel;
    /**
     * 支付方式，C端是传在线支付.枚举：[0：余额，1：在线，2：授信]
     * 0 = 余额支付,1 = 支付宝,2 = 授信支付,3 = 产品自销,4 = 现场支付,9 = 现金支付
     * 必填
     * @var int $pay_mode
     */
    public $pay_mode;
    /**
     * 商品数据：门票列表数组对象（套票只需要传主票的tid）
     * 必填
     * @var CombineOrderRequestTicketListDO[] $ticket_list
     */
    public $ticket_list = [];

    /**
     * 取票人姓名
     * 必填
     * @var string $order_name
     */
    public $order_name;
    /**
     * 取票人手机号
     * 必填
     * @var string $order_tel
     */
    public $order_tel;
    /**
     * 取票人证件号
     * 非必填
     * @var int $person_id
     */
    public $person_id;

    /**
     * 取票人证件号类型
     * 1,身份证 2,护照 3,军官证 4,回乡证 5,台胞证 99其他
     * 非必填
     * @var int $voucher_type
     */
    public $voucher_type;
    /**
     * 下单备注
     * 非必填
     * @var string $emeo
     */
    public $emeo;
    /**
     * 取票人填写更多信息数组（联系人信息数组）
     * 非必填
     * @var CombineOrderMoreCredentialRequestDO[] $more_credential
     */
    public $more_credential = [];

    /**
     * 团购信息
     * 非必填
     * @var CombineOrderRequestTeamDO $team
     */
    public $team = [];

    /**
     * 小程序是否使用窗口价.默认为 true.枚举：[false:使用零售价  true:使用窗口价]
     * （只有渠道为43/56的时候才生效，其他C端渠道使用的是零售价）
     * 非必填
     * @var bool $use_window_price
     */
    public $use_window_price;
    /**
     * 订单是否使用积分抵现 false否 true是
     * 非必填
     * @var bool $use_point
     */
    public $use_point;
    /**
     * 是否使用优惠券折扣-营销中心优惠卷使用
     * 非必填
     * @var bool $use_coupon
     */
    public $use_coupon;
    /**
     * 是否使用优惠券折扣-下单前置校验使用
     * 非必填
     * @var bool $use_discount
     */
    public $use_discount;
    /**
     * ?暂时不知道是什么
     * @var string $mch_sid
     */
    public $mch_sid;
    /**
     * 一级渠道
     * 非必填
     * @var int $first_sale_channel
     */
    public $first_sale_channel;

    /**
     * 下单销售渠道
     * 非必填
     * @var int $sale_channel
     */
    public $sale_channel;

    /**
     * 二级渠道
     * 非必填
     * @var int $second_sale_channel
     */
    public $second_sale_channel;

    /**
     * 优惠券列表：['coupon_num' => '券码','coupon_amount' => '面额']
     * 非必填
     * @var array $coupon_list
     */
    public $coupon_list = [];

    /**
     * 终端号.默认为空
     * @var int $terminal
     */
    public $terminal;

    /**
     * 交易订单号【仅购物车使用】
     * @var string $trade_order_id
     */
    public $trade_order_id;

    public function validate()
    {
        if (empty($this->ticket_list)) {
            $this->addErr("缺少下单参数[ticket_list]");
        }
    }

    public function propertyMap()
    {
        return [
            'sale' => 'sale',
            'member_id' => 'member_id',
            'op_id' => 'op_id',
            'is_sale' => 'is_sale',
            'begin_time' => 'begintime',
            'channel' => 'channel',
            'pay_mode' => 'paymode',
            'ticket_list' => 'ticket_list',
            'order_name' => 'ordername',
            'order_tel' => 'ordertel',
            'person_id' => 'personid',
            'voucher_type' => 'voucher_type',
            'emeo' => 'emeo',
            'more_credential' => 'more_credential',
            'use_window_price' => 'useWindowPrice',
            'use_point' => 'use_point',
            'use_discount' => 'use_discount',
            'mch_sid' => 'mch_sid',
            'first_sale_channel' => 'firstSaleChannel',
            'sale_channel' => 'saleChannel',
            'second_sale_channel' => 'secondSaleChannel',
            'coupon_list' => 'coupon_list',
            'team' => 'team',
            'trade_order_id' => 'tradeOrderId',
        ];
    }

    /**
     * 设置子级的对象属性
     * @return string[]
     */
    public function childrenObjMap()
    {
        return [
            'ticket_list'=>'JsonRpc\Order\OrderRequestDO\CombineOrderRequestTicketListDO',
            'more_credential'=>'JsonRpc\Order\OrderRequestDO\CombineOrderMoreCredentialRequestDO',
            'team'=>'JsonRpc\Order\OrderRequestDO\CombineOrderRequestTeamDO',
        ];
    }

    /**
     * 合并付款ticket_list转换
     * @return array
     */
    public function toCombineTicketList()
    {
        $ticket_list = $this->ticket_list;
        if (empty($ticket_list)) {
            return [];
        }
        foreach ($ticket_list as $item) {
            if (empty($item->tnum)) {
                $this->addErr("套票缺少子票参数[tnum]");
            }
            if (empty($item->pid)) {
                $this->addErr("套票缺少主票参数[pid]");
            }
            if (empty($item->aid)) {
                $this->addErr("套票缺少子票参数[aid]");
            }
            if (empty($item->upper_supplier)) {
                $this->addErr("套票缺少子票参数[upper_supplier]");
            }
            //套票子票`分时预约时间段信息`
            if (!empty($item->package_time_share_info)) {
                foreach ($item->package_time_share_info as $pkShareItem) {
                    $packageTimeShareInfo[$pkShareItem->tid] = [
                        'time_id' => $pkShareItem->time_id,
                        'time_str' => $pkShareItem->time_str,
                    ];
                }
            } else {
                $packageTimeShareInfo = [];
            }

            //套票子票是演出票
            if (!empty($item->package_show_info)) {
                foreach ($item->package_show_info as $pkShowItem) {
                    $packageShowInfo[$pkShowItem->tid] = [
                        'venusid' => $pkShowItem->venue_id,
                        'zoneid' => $pkShowItem->area_id,
                        'roundid' => $pkShowItem->round_id,
                    ];
                }
            } else {
                $packageShowInfo = [];
            }

            $combineProductList[] = [
                'pre_sale' => $item->pre_sale,
                'tnum' => $item->tnum,
                'aid' => $item->aid,
                'pid' => $item->pid,
                'upper_supplier' => $item->upper_supplier,
                'round_id' => $item->round_id,
                'venue_id' => $item->venue_id,
                'area_id' => $item->area_id,
                'seat_ids' => $item->seat_ids,
                'time_share_info' => $item->time_share_info,    //单票分时预约时间段信息
                'package_time_share_info' => $packageTimeShareInfo,
                'package_show_info' => $packageShowInfo,
                'use_point' => $item->use_point,
                'use_coupon' => $item->use_coupon,
                'use_discount' => $item->use_discount,
                'discount_detail' => $item->discount_detail,
                'useMarketingDiscount' => $item->use_marketing_discount
            ];
        }
        return $combineProductList;
    }

}
