<?php

namespace JsonRpc\Order;

use \Exception;
use JsonRpc\Base;
use JsonRpc\Order\OrderRequestDO\GetRefundAuditRecordRequestDO;
use Library\Exception\ApiException;
use Model\Order\OrderTerminal;
use Model\TradeRecord\OrderRefund;

class RefundAudit extends Base
{
    //需要写日志的接口
    public $writeLogMethodList = ['orderTerminalFunc'];

    /**
     * 直接映射到uu_order_terminal_change model
     * 请求日志： /alidata/log/site/jsonrpc/response/
     * 应答日志：/alidata/log/site/jsonrpc/request/
     * @param $funcName
     * @param $funcParams
     * @return array
     */
    public function orderTerminalFunc($funcName,$funcParams){
        $orderTerminal = new OrderTerminal();
        $reflect = new \ReflectionClass($orderTerminal);
        $hasMethod = $reflect->hasMethod($funcName);
        if(!$hasMethod){
            return $this->returnData(500,'Terminal方法不存在');
        }
        try {
            $res = call_user_func_array([$orderTerminal, $funcName], $funcParams);
            return $this->returnData(200,'返回成功',$res);
        } catch (Exception $e) {
            //接口超时，接口返回的httpcode!=200，接口返回内容错误都会直接被捕获
            return $this->returnData($e->getCode(),$e->getMessage());
        }
    }

    public function getOrderTerminalChangeList($param){
        $funcParams = [
            'orderNumArr'=> $param['orderNumArr'],
            'field' => $param['field'] ?? '*'
        ];
        $dstatus = $param['dstatus'] ?? -1;
        if($dstatus >= 0){
            $funcParams['extraWhere']['dstatus'] = is_array($dstatus) ? ['in', $dstatus] : $dstatus;
        }
        return self::orderTerminalFunc('getOrderChangeRecordList',$funcParams);
    }

    public function getAuditList($funcName,$param){
        $refundAudit = new \Business\Order\RefundAudit();
//        return json_encode([$param],JSON_UNESCAPED_UNICODE);
        $res = $refundAudit->getAuditList($param['operatorID'], $param['landTitle'], $param['noticeType'], $param['timeType'],
            $param['beginTime'], $param['endTime'],$param['auditStatus'], $param['orderNum'], $param['page'], $param['limit'],
            $param['auditType'], $param['landId'] , $param['tid'], $param['subSid']);
        return $this->returnData($res['code'],$res['msg'],$res['data']);
    }

    /**
     * 获取审批退票结果记录
     * @return array
     */
    public function getRefundAuditRecord()
    {
        try{
            $params = $this->requestParams();
            $requestDo = new GetRefundAuditRecordRequestDO();
            $requestDo->setAttributes($params);
            $requestDo->validate();
            //参数校验-规则待梳理
            if ($requestDo->hasError()) {
                apiReport()->invalidParams($requestDo->getErrorAsString());
            }
            $refundModel = new OrderRefund();
            $refundInfo  = $refundModel->getRefundJournal($requestDo->order_num, $requestDo->req_serial_number);
            return $this->returnData(200, 'success', $refundInfo);
        }catch (ApiException $apiException) {
            return $this->returnData($apiException->getCode(), $apiException->getMessage());
        }
    }
}
