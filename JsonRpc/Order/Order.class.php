<?php
/**
 * 下单和支付相关rpc接口
 * @auther dwer.cn
 * @date 2021-06-19
 *
 */

namespace JsonRpc\Order;

use Business\Order\OrderList;
use Business\Order\OrderOta;
use Business\Order\PackageSubmit;
use Business\Order\Query;
use Business\Order\Refund;
use JsonRpc\Base;
use JsonRpc\Order\OrderRequestDO\CombineOrderRequestTicketListDO;
use JsonRpc\Order\OrderRequestDO\OrderRequestDO;
use JsonRpc\Order\OrderRequestDO\CombineOrderRequestDO;
use Library\Constants\MemberConst;
use Library\Exception\ApiException;
use Library\Resque\Queue;
use Model\Order\OrderTools;

class Order extends Base
{
    //需要写日志的接口
    public $writeLogMethodList = [
        'common_order', //单个产品下单
        'combine_order', //合并下单
        'common_refund',
        'common_add_num',
        'online_pay',
        'async_rollback_order', //异步回滚订单数据，将数据写入队列，通过异步任务区回滚这些数据
        'non_payment_auto_cancel', //超时未支付自动取消
    ];

    public function unusedOrderDetail($params)
    {
        if (empty($params['fid'])){
            return $this->returnData(204, '参数错误', []);
        }
        $hasSaleMoney = true;
        $settleBlance = new \Model\Finance\SettleBlance();
        $unusedOrderInfo = $settleBlance->unusedOrderDetailForJava($params['fid'], $params['isIncludeLastedCheck'], $params['orderStatus'], $hasSaleMoney);

        $unusedOrderInfo['page_data'] = array_values($unusedOrderInfo['page_data']);

        return $this->returnData(200, 'success', $unusedOrderInfo);
    }
    /**
     * 通用的下单接口，包含了内部下单、支付、和往第三方系统下单
     * <AUTHOR>
     * @data   2019-03-21
     *
     * @param  int  $memberId  购买用户ID
     * @param  int  $aid  供应商ID
     * @param  int  $tid  门票ID
     * @param  int  $tnum  购买数量
     * @param  date  $playdate  开始游玩时间：2019-02-26
     * @param  int  $orderChannel  购买渠道:
     * @param  int  $paymode  支付方式：0=账户余额，1=在线支付，2=授信支付, 3=自供自销, 4=现场支付
     * @param  bool  $isNeedInsidePay  是否需要进行内部支付（美团订单是采用授信或是余额支付的，但是支付和下单的流程是分开的）
     * @param  array  $saleSettingArr  售价信息相关数组
     *                  {
     *                      'is_sale' : true, //是否散客购买
     *                      'upper_supplier' : 0, //更上一级的供应商，如果末级散客没有设置分销链的时候就有这个参数
     *                      'discount_list' ： [] //各种优惠策略数组，包括优惠券、茉莉积分、会员卡优惠之类的
     *                      'use_point':false, //是否使用积分
     *                      'use_coupon' : true,//是否使用优惠券
     *                      'use_discount' : true,//是否使用折扣
     *                      'use_marketing_discount' : true,//是否使用折扣
     *                      'point_use_info' : [],//积分抵现使用详情
     *                      'bargainPricePolicyId' : '',//分销优惠特价快照ID
     *                      'specifyReducedPolicyId' : '',//分销优惠减免快照ID
     *                      'member_discount_code' : '',//判断是否是用会员优惠
     *                  }
     * @param  array  $contactArr  联系人信息数组
     *                  {
     *                      'ordername' ： '张三', //取票人姓名
     *                      'ordertel' ： '15039802110', //取票人手机
     *                      'is_sms' ： true, //是否需要发短信
     *                      'contacttel' : '15039802112', //联系人手机
     *                  }
     * @param  string  $remotenum  远程订单号
     * @param  array  $idcardArr  购买用户身份证数组
     *                  [{
     *                      'name' : '张三丰', //身份证姓名
     *                      'idcard' ： '350421187790238765' //身份证号码
     *                  },{}]
     * @param  array  $linkOrderInfoArr  关联订单信息数组，比如联票主票，套票主票，统一凭证码等
     *                  {
     *                      'ecode' : '344323', //统一凭证码
     *                      'link_type' : 'common' //关联订单类型  common=正常订单, package_son=套票子票, link_son=联票子票
     *                      'parent_ordernum' : '54567623', //主票订单号
     *                  }
     * @param  array  $saleSiteArr  购票网点的信息数组
     *                  {
     *                      'site_id' : 111344, //站点Id
     *                      'terminal' : 18877, //购票时候的终端号
     *                      'stuff_id' : 1222221, //购票网点售票员ID
     *                      'subStuffId': 111, //子商户操作人
     *                  }
     * @param  array  $remarkArr  购票详情等信息数组
     *                  {
     *                      'memo' : '西大门进入', //订单备注信息
     *                      'origin' : '14|362',//客源地数据
     *                      'serial_number' : 'SAA20189023', //线下自填的流水号
     *                  }
     * @param  array  $showInfoArr  演出相关数据数组
     *                  {
     *                      'venue_id' : 112, //演出场馆ID
     *                      'round_id' : 9998, //演出场次ID
     *                      'area_id' : 221, //演出分区ID
     *                      'seat_ids': '9998,9997,9996', //所选座位 - 自助选座的情况
     *                      'sub_area_id' :  3334,// 小分区ID
     *                      "link_seat_mark":"show:13947:15591275126965|create|1"
     *                      'childTicketShowInfoArr' : {
     *                          {"180750":{
     *                              "showInfoArr":{"tid":180750,"venue_id":"42","round_id":"13947","area_id":"197",
     *                                      "show_begin_time":"2019-05-29 20:00","bind_num":"1","main_tid":"180749",
     *                                      "link_seat_mark":"show:13947:15591275126965|select|1"
     *                          }
     *                      }
     *                  }
     * @param  array  $specialTicketSettingArr  特性票类的信息数组
     *                  {
     *                      'is_verify_special' : true, //true=通过,不再进行特殊票种判断，false=未通过，继续特殊票种判断
     *                  }
     * @param  array  $orderExtensionParams  订单扩展参数
     *                  {
     *                      'firstSaleChannel' : 20, //一级渠道
     *                      'secondSaleChannel' : 105, //二级渠道：56-微信 101-小红书 102-抖音 103-支付宝 104-百度 105-PC官网
     *                      'tradeOrderId' : '1233423', //交易订单号【购物车下单必传】
     *                      'tradeOrderId' : '1233423', //交易订单号【购物车下单必传】
     *                      'discountDetail' : '1233423', //交易订单号【购物车下单必传】
     *                  }
     * @return [
     *    'code' : 200,
     *    'msg' : '下单成功',
     *    'data' : [
     *        'ordernum' : '24377875', //订单号
     *        'remotenum' : 'remote_24377875', //远程订单号
     *        'tid' ： 1111,//门票ID
     *        'lid' ： 1111,//景区ID
     *        'pid' ： 1111,//产品ID
     *        'tnum' ： 1111,//购买数量
     *        'tprice' ： 1111,//门票单价
     *        'totalmoney' ： 25000, //总金额 - 分
     *        'counter_price' : 50000,//门市价 - 分
     *        'retail_price' : 50000,//零售价 - 分
     *        'code' : '222398',//凭证码
     *        'paymode' : 1,//支付方式
     *        'round_info' : '座位分区:B区,座位号:3-5_3-4_3-3',//演出座位信息
     *        'is_pay' : false,//是否已经支付
     *        'is_ordered' : false,//是否已经通过远端订单号下过订单进来了
     *    ],
     * ]
     */
    public function common_order($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode, $isNeedInsidePay = true, array $saleSettingArr = [], array $contactArr = [], $remotenum = '',
        array $idcardArr = [], $linkOrderInfoArr = [], array $saleSiteArr = [], array $remarkArr = [], array $showInfoArr = [], array $specialTicketSettingArr = [], $clientMark = '', $orderExtensionParams = [])
    {
        //请求日志
        $requestId = str_replace('.', '', microtime(true));
        $startTime = microtime(true);

        //根据下单渠道决定是否使用JAVA接口
        $isUseJavaVersion = true;

        $request = [
            'member'    => $memberId,
            'aid'       => $aid,
            'tid'       => $tid,
            'tnum'      => $tnum,
            'playdate'  => $playdate,
            'channel'   => $orderChannel,
            'paymode'   => $paymode,
            'need_pay'  => $isNeedInsidePay,
            'sale'      => $saleSettingArr,
            'contact'   => $contactArr,
            'idcard'    => $idcardArr,
            'link_info' => $linkOrderInfoArr,
            'sale_site' => $saleSiteArr,
            'remark'    => $remarkArr,
            'show'      => $showInfoArr,
            'special'   => $specialTicketSettingArr,
            'extension' => $orderExtensionParams,
        ];

        $clientIp = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '-';

        //记录日志
        $word = json_encode([
            'client'  => $clientIp,
            'req_id'  => $requestId,
            'req'     => $request,
            'is_java' => $isUseJavaVersion,
            'jsonrpc' => 1,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('open_submit', $word, 3);
        //直接调用业务封装成
        $submitBiz = new \Business\Order\BaseSubmit($isUseJavaVersion);
        $res       = $submitBiz->commonSubmit($memberId, $aid, $tid, $tnum, $playdate, $orderChannel, $paymode,
            $isNeedInsidePay, $saleSettingArr, $contactArr, $remotenum, $idcardArr, $linkOrderInfoArr, $saleSiteArr,
            $remarkArr, $showInfoArr, $specialTicketSettingArr,$orderExtensionParams);

        if ($clientMark && is_array($res['data'])) {
            $res['data']['client_mark'] = $clientMark;
        }

        $endTime  = microtime(true);
        $costTime = $endTime - $startTime;

        //记录日志
        $word = json_encode([
            'req_id'    => $requestId,
            'cost_time' => $costTime,
            'res'       => $res,
            'jsonrpc'   => 1,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('open_submit', $word, 3);

        return $res;
    }

    /**
     * 通用下单接口json Rpc格式传参
     * 参数参见：OrderRequestDO对象
     * @return array|mixed
     */
    public function common_order_json()
    {
        try {
            $params = $this->requestParams();
            $orderRequestDo = new OrderRequestDO();
            $orderRequestDo->setAttributes($params);
            $orderRequestDo->validate();
            //参数校验-规则待梳理
            if ($orderRequestDo->hasError()) {
                apiReport()->invalidParams($orderRequestDo->getErrorAsString());
            }
        }catch (ApiException $apiException) {
            return $this->returnData($apiException->getCode(), $apiException->getMessage());
        }

        //请求日志
        $startTime = microtime(true);
        $requestId = str_replace('.', '', $startTime);

        //根据下单渠道决定是否使用JAVA接口
        //todo 这里应该是要增加$orderRequestDo->orderChannel渠道判断吧
        $isUseJavaVersion = true;
        //记录日志
        $clientIp = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '-';
        $word = json_encode([
            'client'  => $clientIp,
            'req_id'  => $requestId,
            'req'     => $orderRequestDo->toArray(),
            'is_java' => $isUseJavaVersion,
            'jsonrpc' => 2,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('open_submit', $word, 3);
        //直接调用业务封装成
        $submitBiz = new \Business\Order\BaseSubmit();
        $res       = $submitBiz->commonSubmit(
            $orderRequestDo->memberId,
            $orderRequestDo->aid,
            $orderRequestDo->tid,
            $orderRequestDo->tnum,
            $orderRequestDo->playdate,
            $orderRequestDo->orderChannel,
            $orderRequestDo->paymode,
            $orderRequestDo->isNeedInsidePay,
            $orderRequestDo->saleSettingArr,
            $orderRequestDo->contactArr,
            $orderRequestDo->remotenum,
            $orderRequestDo->idcardArr,
            $orderRequestDo->linkOrderInfoArr,
            $orderRequestDo->saleSiteArr,
            $orderRequestDo->remarkArr,
            $orderRequestDo->showInfoArr,
            $orderRequestDo->specialTicketSettingArr,
            $orderRequestDo->orderExtensionParams);

        if ($orderRequestDo->clientMark && is_array($res['data'])) {
            $res['data']['client_mark'] = $orderRequestDo->clientMark;
        }

        $endTime  = microtime(true);
        $costTime = $endTime - $startTime;

        //记录日志
        $word = json_encode([
            'req_id'    => $requestId,
            'cost_time' => $costTime,
            'res'       => $res,
            'jsonrpc'   => 2,
        ], JSON_UNESCAPED_UNICODE);
        pft_log('open_submit', $word, 3);

        return $res;
    }

    /**
     * 在线支付合并下单
     * <AUTHOR>
     * @date 2021-09-06
     */
    public function combine_order(array $params = [])
    {
        try {
            //参数初步处理
            if (!$params) {
                return $this->returnData(400, '缺少下单参数[params]');
            }

            if (!isset($params['ticket_list'])) {
                return $this->returnData(400, '缺少下单参数[ticket_list]');
            }

            $word = json_encode([
                'key' => "combine_order",
                'param' => $params,
            ], JSON_UNESCAPED_UNICODE);
            pft_log('open_submit', $word, 3);

            //处理游客信息
            $ticketModel  = new \Model\Product\Ticket();
            $newIdCardArr = [];

            foreach ($params['ticket_list'] as &$item) {
                if(isset($item['idcard_arr'])) {
                    $pid        = $item['pid'];
                    $ticketInfo = $ticketModel->getTicketInfoByPid($pid, 'id');

                    if (!$ticketInfo) {
                        continue;
                    }
                    $ticketId = $ticketInfo['id'];

                    $newIdCardArr[$ticketId] = $item['idcard_arr'];
                }
            }

            $orderParams                   = $params;
            $orderParams['combine_pids']   = $params['ticket_list'];
            $orderParams['moreCredential'] = $params['more_credential'] ? ['contacts' => $params['more_credential']] : ['contacts' => []];
            $orderParams['idcard_arr']     = $newIdCardArr;

            if(isset($params['use_window_price'])) {
                $orderParams['useWindowPrice'] = boolval($params['use_window_price']);
            }

            $platformSubmit = new \Business\Order\PlatformSubmit();
            $res            = $platformSubmit->combineSubmit($orderParams);

            if ($res[0] != 200) {
                return $this->returnData($res[0], $res[1]);
            }

            //返回数据
            $resData = $res[2];

            $list = [];
            foreach ($resData['list'] as $item) {
                $list[] = [
                    'ordernum' => $item['ordernum'],
                    'tid'      => $item['tid'],
                    'pid'      => $item['pid'],
                    'lid'      => $item['lid'],
                    'tnum'     => $item['tnum'],
                ];
            }

            //返回成功数据
            $data = [
                'trade_id' => $resData['tradeId'],
                'list'     => $list,
            ];

            return $this->returnData(200, '成功', $data);

        } catch (\Exception $e) {
            $exceptionCode = $e->getCode();

            return $this->returnData(500, "系统异常，请重试[{$exceptionCode}]");
        }
    }

    /**
     * jsonRpc合并下单
     * 接口文档见apifox
     * @return array
     */
    public function combine_order_json()
    {
        try {
            $requestParams = $this->requestParams();
            $orderRequestDo = new CombineOrderRequestDO();
            if (empty($requestParams)) {
                $orderRequestDo->addErr('缺少下单参数[params]');
            }
            $orderRequestDo->setAttributes($requestParams);
            $orderRequestDo->validate();
            //参数初步处理
            if ($orderRequestDo->hasError()) {
                apiReport()->invalidParams($orderRequestDo->getErrorAsString());
            }
            //日志记录
            $word = json_encode([
                'key' => "combine_order_json",
                'param' => $orderRequestDo->toArray(),
            ], JSON_UNESCAPED_UNICODE);
            pft_log('open_submit', $word, 3);

            //处理游客信息,游客信息需要加上tid作为外键
            $ticketModel  = new \Model\Product\Ticket();
            $newIdCardArr = [];
            /**
             * @var $ticket CombineOrderRequestTicketListDO
             */
            foreach ($orderRequestDo->ticket_list as &$ticket) {
                if (empty($ticket->tid) && !empty($ticket->pid)) {
                    //传了pid没传tid，需要去换取tid
                    $ticketInfo = $ticketModel->getTicketInfoByPid($ticket->pid, 'id');
                    if (!$ticketInfo) {
                        continue;
                    }
                    $ticket->tid = $ticketInfo['id'];
                } elseif (!empty($ticket->tid) && empty($ticket->pid)) {
                    //传了tid没传pid，需要去换取pid
                    $ticketInfo = $ticketModel->getTicketInfoById($ticket->tid, 'pid');
                    if (!$ticketInfo) {
                        continue;
                    }
                    $ticket->pid = $ticketInfo['pid'];
                }
                $idCardToArr = [];
                foreach ($ticket->idcard_arr as $idCardObj) {
                    $idCardToArr[] = $idCardObj->toArray();
                }
                $newIdCardArr[$ticket->tid] = $idCardToArr;
            }
            //实际传递到business的合并支付下单逻辑函数的参数
            $orderParams                   = $orderRequestDo->toArray();
            //套票参数组装
            $orderParams['combine_pids']   = $orderRequestDo->toCombineTicketList();
            $orderParams['moreCredential'] = $orderParams['more_credential'] ? ['contacts' => $orderParams['more_credential']] : ['contacts' => []];
            $orderParams['idcard_arr']     = $newIdCardArr;
            if(isset($params['use_window_price'])) {
                $orderParams['useWindowPrice'] = boolval($params['use_window_price']);
            }

            $platformSubmit = new \Business\Order\PlatformSubmit();
            $res            = $platformSubmit->combineSubmit($orderParams);
            if ($res[0] != 200) {
                $wordRes = json_encode([
                    'key' => "combine_order_json",
                    'res' => $res,
                ], JSON_UNESCAPED_UNICODE);
                pft_log('open_submit', $wordRes, 3);
                return $this->returnData($res[0], $res[1]);
            }
            //返回数据
            $resData = $res[2];
            $list = [];
            foreach ($resData['list'] as $item) {
                $list[] = [
                    'order_num' => $item['ordernum'],
                    'tid' => $item['tid'],
                    'pid' => $item['pid'],
                    'lid' => $item['lid'],
                    'tnum' => $item['tnum'],
                    'is_pay' => $item['is_pay'],
                    'total_money' => $item['totalmoney'],
                    'counter_price' => $item['counter_price'],
                    'retail_price' => $item['retail_price'],
                    'pay_mode' => $item['paymode'],
                    'is_sms' => $item['is_sms'],
                ];
            }

            //返回成功数据
            $data = [
                'trade_id' => $resData['tradeId'],
                'list'     => $list,
            ];

            return $this->returnData(200, '成功', $data);
        } catch (ApiException $apiException) {
            return $this->returnData($apiException->getCode(), $apiException->getMessage());
        } catch (\Exception $e) {
            $exceptionCode = $e->getCode();
            return $this->returnData(500, "系统异常，请重试[{$exceptionCode}]");
        }
    }

    /**
     * 订单增加票数
     * <AUTHOR>
     * @date   2019-07-31
     *
     * @param  string  $ordernum  主订单号
     * @param  int  $tnum  增加后的票数
     * @param  int  $memberId  操作人主账号id
     * @param  int|integer  $oper  操作人id
     * @param  array  $touristInfoArray  [['tourist' => '姓名', 'idcard' => '身份证号']]
     * @param  array  $extra  额外的参数
     *                                       [
     *                                           'team_order' => '团单单号'
     *                                       ]
     *
     * @return array
     */
    public function common_add_num($ordernum, $tnum, $memberId, $oper = 0, $touristInfoArray = [], $extra = [])
    {
        $request = func_get_args();

        $biz = new \Business\Order\OrderAddNum();
        $res = $biz->orderAddNum($ordernum, $tnum, $memberId, $oper, $touristInfoArray, $extra);

        $response = $res;

        $logData = [
            'request'  => $request,
            'response' => $response,
            'jsonrpc'  => 1,
        ];

        pft_log('order/modify', json_encode($logData), JSON_UNESCAPED_UNICODE);

        return $res;
    }

    /**
     * 在线支付订单修改为已支付_V0.0.1
     *
     * @param  string  $ordernum  订单号
     * @param  string  $trade_no  交易号
     * @param  int  $sourceT  支付渠道,pft_alipay_rec.sourceT
     * @param  int  $pay_total_fee  支付金额(分为单位)
     * @param  null  $daction  取消订单是否原路退回（散客需要，分销商不需要）
     * @param  string  $sell_info  商家收款信息
     * @param  string  $buyer_info  买家支付信息
     * @param  int  $pay_status  支付状态
     * @param  bool|true  $pay_to_pft  是否票付通账号收款
     * @param  int  $pay_channel  支付来源,订单追踪表的source字段
     * @param  string  $payOrderNum  支付原始订单号
     * @param  int  $payTermianl  支付的终端号，终端购票汇总使用
     * @param  int  $payTrackOpId  支付的操作人
     * @param  int  $annualCardOrderType  年卡下单类型  默认值为false, 0年卡下单 1年卡续费
     * @param  array  $extData 扩展信息 ['subOpId' => '子商户操作人']
     * @return array
     *  {"code":200, "msg":"支付成功"}
     *  {"code":204, "msg":"支付失败，错误码:104"}
     */
    public function online_pay($ordernum = '', $trade_no = '', $sourceT = 1, $pay_total_fee = 0, $daction = null, $sell_info = '',
        $buyer_info = '', $pay_status = 1, $pay_to_pft = true, $pay_channel = 0,
        $payOrderNum = '', $payTermianl = 0, $payTrackOpId = 0, $annualCardOrderType = false, $extData = [])
    {
        $payApi = new \Business\Pay\PayComplete();
        $res    = $payApi->adjustChangeOrderPay(
            $ordernum,
            $trade_no,
            $sourceT,
            $pay_total_fee,
            $sell_info,
            $buyer_info,
            $pay_status,
            $pay_to_pft,
            $pay_channel,
            $payOrderNum,
            $payTermianl,
            $payTrackOpId,
            $extData
        );

        if ($res == 100) {
            //支付成功
            return $this->returnData(200, '支付成功');
        } else {
            $errMsg = json_encode(["支付失败，错误码", $res], JSON_UNESCAPED_UNICODE);

            return $this->returnData(204, $errMsg);
        }
    }

    /**
     * 异步回滚订单数据，将数据写入队列，通过异步任务区回滚这些数据
     * <AUTHOR>
     * @date   2022/6/14
     *
     * @param  array  $params    请求参数
     *
     * @return array
     */
    public function async_rollback_order(array $params)
    {
        if (empty($params)) {
            return $this->returnData(400, '缺少参数[params]');
        }

        $ordernum     = $params['ordernum'] ?? ''; //订单号
        $rollbackType = $params['rollbackType'] ?? 'ordered'; //回滚类型 ordered:下单后回滚, payed:支付后回滚， showed演出回滚
        $showInfo     = $params['showInfo'] ?? []; //演出相关的数据
        $memberId     = $params['memberId'] ?? 0; //购买用户id
        $remotenum    = $params['remotenum'] ?? ''; //远程订单号
        $errMsg       = $params['errMsg'] ?? ''; //订单错误原因

        if (empty($ordernum) || !$memberId) {
            return $this->returnData(200, '参数错误');
        }

        //将数据写入队列
        $queueData = [
            'ordernum'     => $ordernum,
            'rollbackType' => $rollbackType,
            'showInfo'     => $showInfo,
            'memberId'     => $memberId,
            'remotenum'    => $remotenum,
            'errMsg'       => $errMsg,
        ];

        $cancelJobData = [
            'job_type' => 'rollback_order',
            'job_data' => $queueData,
        ];
        $queueId       = Queue::push('order', 'Order_Job', $cancelJobData);

        //将数据写入日志
        $logData = json_encode([
            'key'          => 'asyncRollbackOrder',
            'ordernum'     => $ordernum,
            'rollbackType' => $rollbackType,
            'showInfo'     => $showInfo,
            'memberId'     => $memberId,
            'remotenum'    => $remotenum,
            'errMsg'       => $errMsg,
            'queueId'      => $queueId,
        ]);
        pft_log('order/debug', $logData);

        return $this->returnData(200, '', ['queueId' => $queueId]);
    }

    /**
     * 超时未支付自动取消
     * <AUTHOR>
     * @date   2022/6/15
     *
     * @param  array  $params    请求参数
     *
     * @return array
     * @throws
     */
    public function non_payment_auto_cancel(array $params)
    {
        $log = [];
        try {
            if (empty($params)) {
                return $this->returnData(400, '缺少参数[params]');
            }

            $ordernum  = $params['ordernum'] ?? ''; //订单号
            $tnum      = $params['tnum'] ?? 0; //购买数量
            $ordermode = $params['ordermode'] ?? 0; //购买渠道  默认0
            
            if (empty($ordernum)) {
                throw new \Exception('缺少参数[ordernum]', 400);
            }

            if (!$tnum) {
                throw new \Exception('缺少参数[tnum]', 400);
            }

            Queue::push('order', 'NonPaymentAutoCancel_Job', [
                'ordernum'  => $ordernum,
                'tnum'      => $tnum,
                'ordermode' => $ordermode,
            ]);
        } catch (\Exception $e) {
            $log = [
                'code' => $e->getCode(),
                'msg'  => $e->getMessage(),
                'line' => $e->getLine(),
            ];
        }

        pft_log('order/auto_cancel', json_encode(['non_payment_auto_cancel_JsonRpc_Controller',$params, $log], JSON_UNESCAPED_UNICODE));

        return $this->returnData(200, '', []);
    }

    /**
     * 获取三方订单凭证码
     * author queyourong
     * date 2022/9/22
     *
     * @param array $params
     *
     * @return array
     */
    public function handleThirdCodeReturn(array $params)
    {
        $orderNum     = $params['ordernum'] ?? '';
        $code         = $params['code'] ?? '';
        $isThirdOrder = $params['is_third_order'] ?? false;
        $orderStatus  = $params['order_status'] ?? 0;
        if(empty($orderNum)) {
            return $this->returnData(400, '参数错误');
        }

        [$noticeType, $code, $thirdCode, $thirdApiQrCode, $apiCodeStatusMap] = (new OrderOta())->handleThirdCodeReturn($orderNum, $code, $isThirdOrder, $orderStatus);

        $result = [
            'notice_type'         => $noticeType,
            'code'                => $code,
            'third_code'          => $thirdCode,
            'third_api_qr_code'   => $thirdApiQrCode,
            'api_code_status_map' => $apiCodeStatusMap,
        ];

        return $this->returnData(200, '获取成功', $result);
    }

    /**
     * 根据账号获取是否有订单详情手机号查看权限
     * @param $sidArr
     *
     * @return array
     * <AUTHOR> lingfeng
     * 2022/12/12 11:49
     */
    public function verifyAuthority($sidArr)
    {
        $memberConfig = new \Business\AdminConfig\AccessAccount();
        $result = $memberConfig->verifyAuthority($sidArr);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取订单退票手续费
     *
     * @param  string  $orderNum  订单号
     * @param  int  $cancelNum  取消的数量
     * @param  int  $status  订单的当前状态
     *
     * @date 2023/03/21
     * @auther yangjianhui
     * @return array
     */
    public function getOrderRefundServiceFee($orderNum, $cancelNum, $status)
    {
        if (empty($orderNum) || empty($cancelNum)) {
            return $this->returnData(203, '参数错误');
        }
        $refundBiz = new Refund();
        try {
            $realFee   = $refundBiz->getFeeByOrderNum($orderNum, $cancelNum, $status);
        } catch (\Exception $e) {
            return $this->returnData($e->getCode(), $e->getMessage());
        }

        return $this->returnData(200, '获取成功', $realFee);
    }

    /**
     * 获取并判断套票主票是否为全部出票状态
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function getPackOrderStatus(array $params){
        if (empty($params)) {
            return $this->returnData(400, '缺少参数[params]');
        }
        $orderNum  = $params['ordernum'] ?? ''; //订单号
        $channel  = $params['channel'] ?? ''; //渠道
        if (empty($orderNum)) {
            throw new \Exception('缺少参数[ordernum]', 400);
        }
        $packageSubmit = new PackageSubmit();
        $res = $packageSubmit->checkPackOrderStatus($orderNum);
        if(empty($res)){
            return $this->returnData(400, '套票数据异常', $res);
        }
        if(!empty($channel) && $channel =='meituan'){
            //美团只要我们返回出票失败，这个时候他们美团那边是会退款，所以这边做个特殊处理，只要主票为取消才通知
            $packOrder = new OrderTools();
            $orderInfoArr = $packOrder->getOrderInfo($orderNum);
            if (!$orderInfoArr) {
                return $this->returnData(203, '订单信息不存在');
            }
            else{
                if($res['is_order_completed'] == $packageSubmit::REVIEW_STATUS_FAIL){
                    if($orderInfoArr['status'] != 3 && $orderInfoArr['status'] != 6){
                        $res['is_order_completed'] = $packageSubmit::REVIEW_STATUS_CHECK;
                    }
                }
            }
        }
        @pft_log('order/package', json_encode(['开放查询出票结果',$orderNum,$channel,$res], JSON_UNESCAPED_UNICODE));
        return $this->returnData(200, '查询成功', $res);
    }

    /**
     * 获取订单审核详情
     * @param $params
     * @return array
     */
    public function orderListByOrderNum($params){
        if (empty($params)) {
            return $this->returnData(400, '缺少参数[bizParams]');
        }
        $result = [];
        $orderNum      = $params['orderNum'] ?? '';
        $size     = $params['size'] ?? 50;
        if (!$orderNum || !$size ) {
            return $this->returnData(400, '参数错误');
        }

        $orderQuery = new Query();
        return $orderQuery->orderListByOrderNum($orderNum,$size,$result);
    }

    /**
     * 获取指定订单号 票券信息 支持分页
     * @param array $params
     * @return array
     */
    public function touristListByOrderNum(array $params)
    {
        if (empty($params)) {
            return $this->returnData(400, '缺少参数[bizParams]');
        }
        $orderNum      = $params['orderNum'] ?? [];
        $page     = $params['page'] ?? 1;
        $size     = $params['size'] ?? 50;
        if (!$orderNum) {
            return $this->returnData(400, '参数错误');
        }
        $touristIdx      = $params['touristIdx'] ?? null;

        $orderQuery = new Query();
        return $orderQuery->touristListByOrderNum($orderNum,$page,$size,$touristIdx);
    }

    /**
     * 获取证件类型
     *
     * @date 2024/01/04
     * @auther yangjianhui
     * @return array
     */
    public function identity()
    {
        $queryBiz = new \Business\Order\Query();

        return $queryBiz->getIdentity();
    }

    /**
     * 获取演出订单的操作按钮
     * @return array
     */
    public function handleButtonForShow($params)
    {
        //当前登录分销商ID
        $sid = $params['sid'] ?? 0;
        $memberType = $params['dType'] ?? 0;
        $orderIdArr = $params['orderIdArr'] ?? [];
        if (!$sid || !$orderIdArr) {
            return $this->returnData();
        }
        $orderIdArr = array_slice($orderIdArr, 0, 500);
        $orderList = new OrderList();
        $buttonList = $orderList->handleButtonForShow($orderIdArr, $sid, $memberType);
        return $this->returnData(200, '获取成功', $buttonList);
    }

    /**
     * 批量订单操作验证，完结，取消，变更有效期 - 演出
     * @return array
     */
    public function batchHandleOrderForShow($params)
    {
        //当前登录分销商ID
        $sid = intval($params['sid'] ?? 0); //当前登录供应商ID
        $memberType = intval($params['dType'] ?? 0); //当前登录供应商类型
        $operatorId = intval($params['operatorId'] ?? 0); //操作人ID
        $orderIdArr = $params['orderIdArr'] ?? []; //订单号列表，最多500
        $handleType = intval($params['handleType'] ?? 0); //操作类型
        $delayBegin = strval($params['delayBegin'] ?? ''); //延期开始时间
        $delayEnd = strval($params['delayEnd'] ?? ''); //延期结束时间
        $isChange = intval($params['isChange'] ?? 0); //不变更已入园/已取票状态门票的有效期
        $isSkipUpstreamOrderValidate = intval($params['isSkipUpstreamOrderValidate'] ?? 0); //是否跳过上游订单验证

        if (!$sid || !$operatorId || !$orderIdArr || !is_array($orderIdArr)) {
            return $this->returnData();
        }
        $actionTypeArr = [
            1, //批量验证
            2, //批量完结
            3, //批量取消
            4, //批量延期
        ];
        if (!in_array($handleType, $actionTypeArr)) {
            return $this->returnData(400, '批量操作类型必传');
        }
        if ($memberType == MemberConst::ROLE_SUB_MERCHANT) {
            return $this->returnData(1403, '该商户无操作权限');
        }
        $orderIdArr = array_slice($orderIdArr, 0, 500);
        $loginInfo = [
            'sid' => $sid,
            'dtype' => $memberType,
            'memberID' => $operatorId
        ];
        $params = [
            'handle_type' => $handleType,
            'delay_begin' => $delayBegin,
            'delay_end' => $delayEnd,
            'is_change' => $isChange,
            'is_skip_upstream_order_validate' => $isSkipUpstreamOrderValidate,
        ];
        $batchOperateBiz = new \Business\Order\BatchOperate();
        return $batchOperateBiz->batchHandleOrderForShow($loginInfo, $orderIdArr, $params);
    }

    public function acquireConnection($poolKey, $maxConnections, $timeout, $sleepSeconds, $redisInstanceName)
    {
        $limiter = new \Library\RateLimit\ConnectionPoolRateLimiter($poolKey, $maxConnections, $timeout, $redisInstanceName);
        $conn = $limiter->acquireConnection();
        $length = $limiter->length();
        sleep($sleepSeconds);
        $limiter->releaseConnection($conn);
        if (!$conn) {
            \pft_log('ConnectionPoolRateLimiter', "获取连接失败，当前连接数: {$length}");
            return $this->returnData(400, '获取连接失败', ['msg' => "获取连接失败，当前连接数: {$length}"]);
        }
        \pft_log('ConnectionPoolRateLimiter', "获取连接成功，当前连接数: {$length}");
        return $this->returnData(200, '获取连接成功', ['msg' => "获取连接成功，当前连接数: {$length}"]);
    }

    public function redisTokenBucket($poolKey, $rate, $capacity, $redisInstanceName)
    {
        $limiter = new \Library\RateLimit\RedisTokenBucket($poolKey, $rate, $capacity, $redisInstanceName);
        if ($limiter->isLimit()) {
            return $this->returnData(400, '超过限流阈值');
        }
        return $this->returnData(200, '未超过限流阈值');
    }




    /**
     * 供应商/分销商/集团账户查询订单列表
     *
     * @param  array $params
     * [
     * @param  int  $queryMemberId  查询用户ID
     * @param  int  $sid  对应主账号ID
     * @param  int  $memberType  查询用户类型
     * @param  array  $memberArr  订单所属者ID数组
     * @param  string  $dbStart  年份开始日期
     * @param  string  $dbEnd  年份结束日期
     * @param  int  $page  页码
     * @param  int  $size  条数
     * @param  array  $orderNumArr  查询订单号数组
     * @param  string  $orderName  联系人姓名
     * @param  string  $personId  身份证号
     * @param  array|string  $mobile  联系人手机号，[{mobileArea,mobile}]
     * @param  string  $orderStart  下单开始时间
     * @param  string  $orderEnd  下单结束时间
     * @param  string  $playStart  游玩开始时间
     * @param  string  $playEnd  游玩结束时间
     * @param  string  $dTimeStart  验证开始时间
     * @param  string  $dTimeEnd  验证结束时间
     * @param  string  $beginTimeStart  有效期开始时间
     * @param  string  $beginTimeEnd  有效期结束时间
     * @param  int  $status  订单状态
     * @param  int  $payStatus  支付状态
     * @param  int  $payMode  支付方式
     * @param  int  $orderMode  下单方式
     * @param  string  $pType  产品类型
     * @param  int  $operateId  操作用户ID
     * @param  int  $checkResource  验证渠道
     * @param  array  $lidArr  产品id数组
     * @param  array  $pidArr  pid数组
     * @param  array  $sellerIdArr  供应商ID数组
     * @param  array  $buyerIdArr  分销商ID数组
     * @param  array  $tidArr  tid数组
     * @param  int  $orderSource  订单来源 1=全部， 2=资源中心
     * @param  int  $ifPrint  打印情况
     * @param  int  $mark  标记情况
     * @param  string  $beginFirstTimeStart  首次入园时间
     * @param  string  $beginFirstTimeEnd  首次入园时间
     * @param  array  $ptypeArr  产品类型数组
     * @param  int  $identityPhoto  身份证照片，全部:不传或传0，是:1，否:2
     * ]
     *
     * @return array
     */
    public function getBusinessListByOrderService($paramArr)
    {
        if (empty($paramArr)) {
            return $this->returnData(203, "参数有误");
        }
        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }
        $queryRes = (new OrderList())->getBusinessListByOrderService($paramArr['queryMemberId'], $paramArr['sid'], $paramArr['memberType'],
            [$paramArr['sid']], false, false, $paramArr['page'], $paramArr['size'], $ordernumArr, false,
            false, false, $paramArr['order_time_start'], $paramArr['order_time_end'],
            false, false, false, false, false, false,
            $paramArr['status'], $paramArr['pay_status'], false,  $paramArr['order_mode'], false, $paramArr['book_order_staff'] ?? false,
            false, false, false, false, false, false,
             false, $paramArr['if_print'], false, false,
            false, false, true, -1, false,
            0, 0, '', $paramArr['check_code']);

        return $queryRes;
    }

}

