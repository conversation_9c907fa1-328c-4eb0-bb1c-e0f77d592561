<?php
/**
 * jsonrpc 实现基类
 * @auther dwer.cn
 * @date 2021-06-19
 */

namespace JsonRpc;

use Library\Constants\Code\BizCode;

class Base
{
    //需要写日志的接口
    public $writeLogMethodList = [];

    public function __construct()
    {
        if (!defined('PFT_INSIDE_RPC')) {
            exit('Access Deny');
        }
    }

    /**
     * 统一的数据返回
     * @date   2017-02-14
     * <AUTHOR>
     *
     * @param  string  $code  业务码
     * @param  string  $msg  相关信息
     * @param  array  $data  数组数据
     *
     * @return array
     */
    protected function returnData($code = 200, $msg = '', $data = [])
    {
        $res = ['code' => $code, 'msg' => $msg, 'data' => $data];

        return $res;
    }


    protected function requestParams()
    {
        $input      = @file_get_contents('php://input');
        $inputArray = json_decode($input, true);
        if (!is_array($inputArray) || empty($inputArray)) {
            return [];
        }

        if (!isset($inputArray['params'])) {
            return [];
        }

        return $inputArray['params'];
    }

    /**
     * 根据参数名获取值
     * author queyourong
     * date 2022/4/28
     *
     * @param string $param
     * @param null $default
     *
     * @return mixed|null
     */
    protected function requestParam(string $param, $default = null)
    {
        static $params;
        if(!$params) {
            $params = $this->requestParams();
        }

        $target = $params;
        $key = explode(".", $param);
        while ($segment = array_shift($key)) {
            if(is_array($target) && isset($target[$segment])) {
                $target = $target[$segment];
            } else {
                $target = $default;
            }
        }
        return $target;
    }
	
	
	public function success($data = NULL, $msg = 'success', $code= BizCode::SUCCESS){
		return $this->returnData($code,$msg, $data);
	}
	
	public function fail($emsg, $code=BizCode::ERROR_LOGIC, $data= NULL){
		return $this->returnData($code,$emsg, $data);
	}
}


