<?php
/**
 * 租赁订单押金相关业务导出
 */

namespace JsonRpc\Export;

use Business\Authority\AuthContext as AuthContextBiz;
use Business\Authority\AuthLogic as AuthLogicBiz;
use Business\Authority\DataAuthLogic as DataAuthLogicBiz;
use Business\JsonRpcApi\ScenicLocalService\LeaseDeposit;
use Library\Controller;
use Model\Report\Template;

class LeaseExport extends Controller
{
    // 成功
    const SUCCESS = 200;
    const ERROR = 500;
    // 参数不正确或参数缺失
    const PARAMS_INVALID = 10001;

    private $_chooseHead = [
        'date' => '日期',
        'site_id' => '购票站点',
        'operator_id' => '售票员',
        'lid' => '产品名称',
        'item_id' => '物品名称',
        'channel_id' => '下单渠道',
        'fid' => '分销商',
        'pay_type_order' => '支付方式（租金）',
        'pay_type_deposit' => '支付方式（押金）',
        'order_unit_price' => '租金单价',
        'deposit_unit_price' => '押金单价',
    ];
    private $_choseExportHead = [
       'take_num' => '领用数量',
       'return_num' => '归还数量',
       'unreturned_num' => '未归还数量',
       'order_money' => '租金总额',
       'deposit_money' => '押金总额',
       'unreturned_deposit_money' => '未归还押金',
       'revoke_order_money' => '撤销租金总额',
       'revoke_deposit_money' => '撤销押金总额',
       'revoke_num' => '撤销数量',
       'exceptional_return_deposit_money' => '异常归还押金',
       'exceptional_return_num' => '异常归还数量',
    ];

    /**
     * @param array $req
     * {
     *  "pageNum": 2,
     *  "request": {
     *  "start_time": "2023-11-09 18:00:00",
     *  "type": "2",
     *  "end_time": "2023-11-09 19:00:00"
     * },
     * "opMember":{
     *   "fid": 6970,
     *    "opId": 6970
     *  }
     * }
     */
    public function run(array $req)
    {
        $pageNum = $req['pageNum'] ?? 0;
        $params = $req['request'] ?? [];
        $opMember = $req['opMember'] ?? [];
        $fid = intval($opMember['fid'] ?? 0);
        $opId = intval($opMember['opId'] ?? 0);
        if (!$pageNum) {
            $this->apiReturn(self::PARAMS_INVALID, '提交了无效的分页参数');
        }
        if (!$fid || !$opId) {
            $this->apiReturn(self::PARAMS_INVALID, '提交了无效的用户参数');
        }
        $business = new LeaseDeposit();
        $pageSize = 20;
        $queryRes = $business->leaseChangeLogExport($params, $fid, $pageNum, $pageSize);
        $this->dealExportRes($queryRes, $pageSize, $pageNum);
    }
    public function rentReportExport(array $req)
    {
        $pageNum = $req['pageNum'] ?? 0;
        $params = $req['request'] ?? [];
        $opMember = $req['opMember'] ?? [];
        $fid = intval($opMember['fid'] ?? 0);
        $opId = intval($opMember['opId'] ?? 0);
        if (!$pageNum) {
            $this->apiReturn(self::PARAMS_INVALID, '提交了无效的分页参数');
        }
        if (!$fid || !$opId) {
            $this->apiReturn(self::PARAMS_INVALID, '提交了无效的用户参数');
        }
        $business = new LeaseDeposit();
        $pageSize = 20;
        $queryRes = $business->rentReportExport($params, $fid, $opId, $pageNum, $pageSize);
        $this->dealExportRes($queryRes, $pageSize, $pageNum);
    }
    public function rentReportDetailExport(array $req)
    {
        $pageNum = $req['pageNum'] ?? 0;
        $params = $req['request'] ?? [];
        $opMember = $req['opMember'] ?? [];
        $fid = intval($opMember['fid'] ?? 0);
        $opId = intval($opMember['opId'] ?? 0);
        if (!$pageNum) {
            $this->apiReturn(self::PARAMS_INVALID, '提交了无效的分页参数');
        }
        if (!$fid || !$opId) {
            $this->apiReturn(self::PARAMS_INVALID, '提交了无效的用户参数');
        }
        $business = new LeaseDeposit();
        $pageSize = 20;
        $queryRes = $business->rentReportDetailExport($params, $fid, $opId, $pageNum, $pageSize);
        $this->dealExportRes($queryRes, $pageSize, $pageNum);
    }

    /**
     * @param array $queryRes
     * @param int $pageSize
     * @param $pageNum
     */
    public function dealExportRes(array $queryRes, int $pageSize, $pageNum)
    {
        if ($queryRes['code'] != 200 || empty($queryRes['data']) || empty($queryRes['data']['list'])) {
            $this->apiReturn(self::SUCCESS, ['list' => [], 'isOver' => true], $queryRes['msg']);
        }
        $total = $queryRes['data']['total'];
        $totalPage = ceil($total / $pageSize);
        $this->apiReturn(self::SUCCESS, ['list' => $queryRes['data']['list'],
            'isOver' => $pageNum >= $totalPage]);
    }

    public function getSelfDefinedStructureForRent($searchConfigId)
    {
        //获取统计指标
        $templateModel = new Template();
        $template = $templateModel->getTemplateByConfigId($searchConfigId);
        //统计纬度
        $target = json_decode($template['target'], true);
        $item = json_decode($template['item'], true);
        $tmpExportHead = [];
        if (!empty($item)) {
            foreach ($item as $value) {
                $tmpExportHead[$value] = $this->_chooseHead[$value];
            }
        }
        foreach ($target as $value) {
            $tmpExportHead[$value] = $this->_choseExportHead[$value];
        }
        $head = [];
        foreach ($tmpExportHead as $value) {
            $head[] = [$value];
        }
        return [
            'head' => $head,
            'field' => array_keys($tmpExportHead)
        ];
    }

    public function getDataAuth($memberId, $dtype, $sdtype, $lidArr): array
    {
        // 获取数据权限
        $dataAuthLimit = DataAuthLogicBiz::getInstance()->getDataJobsLimit($memberId, $dtype, $sdtype);
        //数据权限过滤
        $dataAuthCondition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lidArr]);
        $return = [
            'lidArr' => $lidArr,
            'notLidList' => [],
        ];
        if ($dataAuthCondition !== false) {
            if (isset($dataAuthCondition['lidList'])) {
                $return['lidArr'] = $dataAuthCondition['lidList'];
            }
            if (isset($dataAuthCondition['notLidList'])) {
                $return['notLidList'] = $dataAuthCondition['notLidList'];
            }
        }
        return $return;
    }
    public function opIdFilter($opIds, $loginInfo)
    {
        $hasAllScope = (new AuthLogicBiz())->memberDataResource($loginInfo['sid'], $loginInfo['memberID'], $loginInfo['dtype']);
        if (!$hasAllScope) {
            //只有个人数据权限的只能看自己的数据
            $opIds = [$loginInfo['memberID']];
        }
        return $opIds;
    }
}