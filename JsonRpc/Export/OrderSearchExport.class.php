<?php
/**
 * 订单查询导出
 * <AUTHOR>
 * @date   2025/02/19
 */

namespace JsonRpc\Export;

use Business\Order\OrderSearchExport as OrderSearchExportBiz;

class OrderSearchExport extends DownloadCenterTwo
{
    /**
     * 订单查询分页导出
     * <AUTHOR>
     * @date   2025/02/19
     *
     * @param  string  $lastSequence
     * @param  int     $pageNum
     * @param  array   $request
     * @param  int     $fid
     * @param  int     $opId
     *
     * @return array
     * @throws
     */
    protected function build(string $lastSequence, int $pageNum, array $request, int $fid, int $opId)
    {
        $biz = new OrderSearchExportBiz();
        $res = $biz->exportPaginate($request, $lastSequence, $pageNum);
        if ($res['code'] != 200) {
            throw new \Exception($res['msg'] ?? '导出失败, 未知错误');
        }
        return $res['data'] ?? [
            'list'         => [],
            'isOver'       => true,
            'lastSequence' => uniqid(),
        ];
    }
}
