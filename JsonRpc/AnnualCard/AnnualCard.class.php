<?php
/**
 * 年卡RPC服务
 * <AUTHOR>  Li
 * @date   2021-12-14
 */

namespace JsonRpc\AnnualCard;

use Business\AnnualCard\AnnualCardManage;
use Business\AnnualCard\AnnualStorageCache;
use Business\Face\AnnualAliFaceBiz;
use Business\JsonRpcApi\ScenicLocalService\Face;
use Business\AnnualCard\CardManage;
use Business\Product\AnnualCardConst;
use Controller\AnnualCard\AnnualArchiveTrait;
use JsonRpc\Base;
use Business\Product\AnnualCard as annualBiz;
use Business\Product\AnnualCardVerifyBiz;
use Library\Util\AnnualUtil;
use Model\Product\AnnualCard as annualModel;
use Library\Tools\Vcode;
use Model\Product\AnnualCard as CardModel;

class AnnualCard extends Base
{
    use AnnualArchiveTrait, AnnualStorageCache;
    public $writeLogMethodList = [
        'consume',
        'activeAnnualCardForshop',
        'activeVirtualCardForshop',
    ];

    private $_annualBiz       = null;
    private $_annualModel     = null;
    private $_annualVerifyBiz = null;
    private $_annualManageBiz = null;

    /**
     * 年卡刷卡消费
     * <AUTHOR>  Li
     * @date   2021-12-14
     *
     * @param  array  $consumeMap  ['pid' => tnum,..]
     * @param  string  $identify  刷卡标识
     * @param  string  $type  刷卡标识类型
     * @param  int  $sid  供应商id
     * @param  int  $salerId  景区id
     * @param  int  $operatorId  操作人id
     * @param  int  $asynchronization  是否同步下单（0否[异步下单] 1是[同步下单]）  当传入参数为0时  才会受到票属性控制 传入1表示当前业务一定需要同步下单，否则业务会异常
     * @param  array  $optionParams 额外消费参数
     *
     * @return array
     */
    public function consume($consumeMap, $identify, $type, $sid = 0, $salerId = 0, $operatorId = 0, $asynchronization = 0, $optionParams = [])
    {
        if (!is_array($consumeMap) || !$consumeMap) {
            return $this->returnData(204, '请选择消费门票');
        }
        if (!$identify || !$type) {
            return $this->returnData(204, '刷卡标识缺失');
        }
        $result = $this->getAnnualBiz()->consume($consumeMap, $identify, $type, $sid, $salerId, $operatorId, $asynchronization, $optionParams);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 发送短信验证码
     *
     * <AUTHOR>  Li
     * @date   2022-01-05
     *
     * @param  string  $mobile  手机号
     * @param  string  $identify  本次发送业务的标识
     * @param  array  $data  短信内容
     * @param  int  $length  验证码长度
     * @param  boolean  $mixed  false : 纯数字 | true : 字母数字混合
     * @param  int  $expire  过期时间
     * @param  int  $interval  发送间隔秒数 0=默认不限制
     *
     * @return array
     */
    public function sendVcode($mobile, $identify = 'annual-consume', $data = [], $length = 6, $mixed = false, $expire = 300, $interval = 20, $sid = 0)
    {
        if (!$mobile || !ismobile($mobile) || !$identify || !in_array($identify, ['annual-consume', 'annual_activate'])) {
            return $this->returnData(203, '缺少必要参数');
        }

        if ($identify == 'annual-consume' && empty($data)) {
            $data = [
                '{1}'  => '年卡消费',
                'code' => '{2}',
            ];
        }

        if ($identify == 'annual_activate' && empty($data)) {
            $data = [
                '{1}'  => '年卡激活',
                'code' => '{2}',
            ];
        }

        $verifyCodeRes = Vcode::sendVcode($mobile, $identify, $identify, $data, $length, $mixed, $expire, $interval, $sid);

        return $this->returnData($verifyCodeRes['code'], $verifyCodeRes['msg']);
    }

    /**
     * 检测手机短信验证码有效性
     * <AUTHOR>  Li
     * @date  2022-03-24
     *
     * @param  string  $idCard  身份证号 可以传0
     * @param  string  $mobile  手机号
     * @param  string  $code  验证码
     * @param  int  $pid  年卡产品id
     * @param  string  $identify  发送短信的标识 annual_activate年卡激活 annual-consume年卡特权消费
     *
     * @return array
     */
    public function checkMobileCode($idCard, $mobile, $code, $identify, $pid)
    {
        if (!$mobile || !$code) {
            return $this->returnData(204, '参数错误');
        }

        $checkResult = $this->getAnnualBiz()->checkMobileCode($idCard, $mobile, $code, $pid, $identify);
        return $this->returnData($checkResult['code'], $checkResult['msg']);
    }

    /**
     * 获取会员详细信息/获取指定用户的年卡列表
     * @param  int  $sid  供应商id
     * @param  int  $annualValid 玩聚小程序年卡tab页： 1-正常年卡 0-失效年卡
     * @param  int  $memberId  供应商id
     * @return array
     * @throws \Exception
     *<AUTHOR>  Li
     * @date   2022-02-28
     */
    public function getMyAnnualCardList(int $sid, int $memberId, $page = 1, $pageSize = 10, int $annualValid = -1)
    {
        if (!$sid || !$memberId) {
            return $this->returnData(203, '相关参数缺失');
        }
        $page = $page <= 0 ? 1 : $page;
        $pageSize = $pageSize <= 0 ? 10 : $pageSize;
        $annualModel   = new \Business\Mall\MemberVip($memberId, $sid);
        $annualCardRes = $annualModel->getMyAnnualCardList($page, $pageSize, $annualValid);
        return $this->returnData($annualCardRes['code'], $annualCardRes['msg'], $annualCardRes['data']);
    }


    /**
     * 获取年卡产品列表
     *
     * <AUTHOR>
     *
     * @param  int  $aid     供应商id
     * @param  int  $fid     分销商id
     *
     * @return array
     */
    public  function getAnnualLandList($aid, $fid = 0)
    {
        if(!$aid){
            return $this->returnData(204,'参数错误');
        }
        $annualBiz = new \Business\Product\AnnualCard();
        return $annualBiz->getAnnualLandList($aid, $fid);
    }


    /**
     * 获取年卡产品列表
     *
     * <AUTHOR>
     *
     * @param  int  $aid     供应商id
     * @param  int  $fid     分销商id
     * @param  int  $lid     景区id
     *
     * @return array
     */
    public  function getAnnualProductList($aid, $fid = 0, $lid = 0, $page = 1, $size = 10)
    {
        if(!$aid){
            return $this->returnData(204,'参数错误');
        }
        $annualBiz = new \Business\Product\AnnualCard();

        return $annualBiz->getAnnualProductList($aid, $fid, $lid, $page, $size);
    }

    /**
     * 获取年卡详情
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  int  $cardId  年卡id
     * @param  int  $sid  供应商id
     * @param  int  $memberId  用户id
     * @param  bool  $needCheckMember  是否需要校验卡的用户,true需要，false不需要
     * @param  int  $isPage  是否分页获取年卡权益
     *
     * @return array
     * @throws \Exception
     */
    public function getMyAnnualDetail(int $cardId, int $sid, int $memberId, bool $needCheckMember = true, int $isPage = 0, $renewalChannel = 0)
    {
        if (!$cardId || !$sid || !$memberId) {
            return $this->returnData(203, '相关参数缺失');
        }

        $annualModel   = new \Business\Mall\MemberVip($memberId, $sid);
        $annualCardRes = $annualModel->getMyAnnualDetail($cardId, $isPage, $needCheckMember, $renewalChannel);

        return $this->returnData($annualCardRes['code'], $annualCardRes['msg'], $annualCardRes['data']);
    }

    /**
     * 获得二维码里数据
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  int  $sid  供应商id
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return array
     */
    public function getQcodeData(int $sid, string $virtualNo)
    {
        if (!$sid || !$virtualNo) {
            return $this->returnData(203, '相关参数缺失');
        }

        $annualBiz = new \Business\Product\AnnualCard();
        $result    = $annualBiz->getQcodeData($virtualNo, $sid);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取会员历史消费信息
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  int  $sid  供应商id
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     * @param  string  $action  查询还是统计
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return array
     */
    public function getHistoryOrder($sid, $virtualNo, $action = 'select', $page = 1, $size = 15)
    {
        if (!$sid || !$action || !$page || !$size || !$virtualNo) {
            return $this->returnData(203, '相关参数缺失');
        }

        $annualPrivBiz = new \Business\Product\AnnualCardPrivilege();
        $orderList     = $annualPrivBiz->getHistoryOrder($sid, 0, $page, $size, $action, $virtualNo);

        return $this->returnData(200, '获取会员历史消费信息成功', $orderList);
    }

    /**
     * 激活实体卡
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  string  $mobile  手机号
     * @param  string  $cardNo  实体卡号
     * @param  string  $idCard  身份证
     * @param  array  $avatar  头像
     * @param  int  $source  来源
     * @param  string  $memberName  年卡会员姓名
     * @param  int  $sid  供应商id
     * @param  string  $pic  审核材料
     * @param  string  $applyRemark  审核备注
     *
     * @return array
     */
    public function activeAnnualCardForshop(
        $mobile, $cardNo, $idCard = '', $avatar = [], $source = 11,
        $memberName = '', $sid = 0, $pic = '', $applyRemark = '', $voucherType = 1,
        $affiliatesPerson = ''
    )
    {
        $params = compact(
            'mobile', 'cardNo', 'idCard', 'avatar', 'source',
            'memberName', 'sid', 'pic', 'applyRemark', 'voucherType',
            'affiliatesPerson'
        );
        AnnualUtil::info([
            'tag' => 'activeAnnualCardForshop_request',
            'params' => $params,
        ]);
        if (!$mobile || !$cardNo || !$memberName) {
            return $this->returnData(203, '相关参数缺失');
        }
        $_sss = microtime_float();
        $result    = $this->getAnnualBiz()->activeAnnualCardForshop(
            $mobile, $cardNo, $idCard, $avatar, $source,
            $memberName, $sid, 0,  0, '',
            $pic, $applyRemark, false, $voucherType, 0,
            $affiliatesPerson
        );
        $ms = (microtime_float()-$_sss);
        if ($ms > 9) {
            AnnualUtil::info([
                'tag' => 'activeAnnualCardForshop',
                'args' => func_get_args(),
                'time_cost' => $ms,
            ]);
        }
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     *  激活虚拟卡：云票务、小程序、微商城激活虚拟年卡
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  string  $virtualNo  虚拟卡号
     * @param  int  $memberId  用户id
     * @param  int  $sid  供应商id
     * @param  int  $pid  年卡套餐产品id
     * @param  string  $ordernum  订单号
     * @param  array  $avatars  用户人脸数组
     * @param  array  $userInfo  激活用户信息
     * @param  int  $source  来源，微商城11
     * @param  string  $pic  审核材料
     * @param  string  $applyRemark  审核备注
     *
     * @return array
     */
    public function activeVirtualCardForshop(
        string $virtualNo, int $memberId, int $sid, int $pid, array $userInfo,
        array $avatars, string $ordernum = '', int $source = 0, $pic = '', $applyRemark = '',
        $opId = 0, $affiliatesPerson = ''
    )
    {
        AnnualUtil::debug([
            'tag' => 'activeVirtualCardForShop',
            'args' => func_get_args(),
        ]);
        if (!$memberId || !$virtualNo || !$sid || !$pid || !isset($userInfo['name'], $userInfo['mobile'], $userInfo['idCard'])) {
            return $this->returnData(203, '相关参数缺失');
        }

        $result = $this->getAnnualBiz()->activeVirtualCardForshop($virtualNo, $memberId, $sid, $pid, $ordernum,
            (object)$userInfo, $avatars, $source, $pic, $applyRemark, false, $opId, $affiliatesPerson);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 虚拟卡激活操作
     * author queyourong
     * date 2022/6/22
     *
     * @param int $sid 商家id
     * @param string $virtualNo 虚拟卡号
     * @param string $mobile 手机号
     * @param string $idCard 身份证
     * @param string $name 姓名
     * @param int $province 省id
     * @param int $city 市id
     * @param string $address 地址
     * @param array $avatar 头像
     * @param int $source 下单来源
     * @param int $opId 操作人id
     *
     */
    public function activeVirtualCard(
        int $sid, string $virtualNo, string $mobile, string $idCard, string $name,
        int $province, int $city, string $address, array $avatar, int $source,
        $opId, $pic, $applyRemark, $voucherType = 1, $affiliatesPerson = ''
    )
    {
        AnnualUtil::debug([
            'tag' => 'activeVirtualCard',
            'args' => func_get_args(),
        ]);
        $annualBiz = new AnnualBiz();
        $result = $annualBiz->activeVirtualCard($sid, $virtualNo, $mobile, $idCard, $name, $province, $city, $address, $avatar,
            $source, $opId, $pic, $applyRemark, '', $voucherType, $affiliatesPerson);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 保存人脸信息
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  int  $sid  供应商id
     * @param  string  $virtualNo  虚拟卡号
     * @param  array  $avatars  人脸数组
     * @param  string  $affiliatesPerson  附属卡信息
     *
     * @return array
     */
    public function saveGroupPhotos(string $virtualNo, array $avatars, int $sid = 0, string $affiliatesPerson = '')
    {
        if (!$sid || !$virtualNo || !$avatars)   {
            return $this->returnData(203, '相关参数缺失');
        }

        $result = $this->getAnnualBiz()->saveAnnualGroupPhotos($virtualNo, $avatars, $sid, $affiliatesPerson);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 检查实体卡能不能用
     * <AUTHOR>  Li
     * @date   2022-02-28
     *
     * @param  int  $sid  供应商id
     * @param  string  $cardNo  实体卡卡号/虚拟卡号
     * @param  int  $type  0实体卡1虚拟卡
     * @param  int  $source  来源
     *
     * @return array
     */
    public function checkCardNo(int $sid = 0, string $cardNo = '', $type = 0, $source = 0)
    {

        if (!$sid || !$cardNo) {
            return $this->returnData(203, '相关参数缺失');
        }

        $result    = $this->getAnnualBiz()->checkCardNo($cardNo, $sid, $type, $source);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }
	
	
	public function searchInactiveVirtualCards($sid, $telephone, $source =0,$page=1,$pageSize=500) {
		try {
			$result = $this->getAnnualBiz()->searchInactiveCardList($sid,$telephone,$source,$page,$pageSize);
			return $this->returnData(200, 'success',$result);
		} catch(\Throwable $e){
			$emsg = $e->getMessage();
			$ecode = 203;   // todo 替换为常量
			return $this->returnData($ecode, $emsg,null);
		}
	}

    /**
     * 是否需要填写身份证信息
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     * @param  int  $sid  供应商id
     * @param  int  $tid  票id
     *
     * @return array
     */
    public function isNeedID(int $sid, int $tid)
    {
        if (empty($sid) || empty($tid)) {
            return $this->returnData(203, "相关参数缺失", []);
        }

        $javaApi = new \Business\CommodityCenter\Ticket();
        $tidAttr = $javaApi->queryTicketAttrsById($tid);
        $needNo  = 1;
        foreach ($tidAttr as $item) {
            if ($item['key'] == 'annual_identity_info') {
                $needNo = $item['val'];
            }
        }
        return $this->returnData(200, '获取是否需要填写身份证信息成功', ['need' => $needNo]);
    }

    /**
     * 获取能上传几张照片
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     * @param  string  $cardNo  实体卡
     * @param  int  $sid  供应商id
     * @param  int  $type  卡类型0实体卡1虚拟卡
     *
     * @return array
     */
    public function getFamilyNum(string $cardNo, int $sid, int $type = 0)
    {
        if (!$cardNo) {
            return $this->returnData(203, '缺少必要参数');
        }

        $result = $this->getAnnualBiz()->getFamilyNum($cardNo, $sid, $type);
        if ($result['code'] != 200) {
            return $this->returnData(204, $result['msg']);
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取人脸的头像
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return array
     */
    public function getFacePic(string $virtualNo)
    {
        if (!$virtualNo) {
            return $this->returnData(203, '缺少必要参数');
        }

        $result = $this->getAnnualBiz()->getFacePic($virtualNo);
        if ($result['code'] != 200) {
            return $this->returnData(204, $result['msg']);
        }

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取年卡库存
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     * @param  int  $sid  供应商id
     * @param  int  $pid  门票pid
     * @param  string  $type  虚拟卡/物理卡
     *
     * @return array
     */
    public function getAnnualCardStorage(int $sid, int $pid, string $type = 'virtual')
    {
        if (!$sid || !$pid) {
            return $this->returnData(203, '缺少必要参数');
        }
        $cache   = $this->getAnnualCardStorageCache($sid, $pid, $type);
        if ($cache) {
            return $this->returnData(200, '获取年卡库存成功', $cache);
        }
        $res = $this->getAnnualModel()->getAnnualCardStorage($sid, $pid, $type);
        $this->setAnnualCardStorageCache($res, $sid, $pid, $type);

        return $this->returnData(200, '获取年卡库存成功', $res);
    }

    /**
     * 根据虚拟卡号查询年卡信息
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     * @param  string  $virtualNo  虚拟卡号
     *
     * @return array
     */
    public function getAnnualCardDetailByVirtualNo(string $virtualNo): array
    {
        if (!$virtualNo) {
            return $this->returnData(203, '缺少必要参数');
        }

        $cardInfo = $this->getAnnualModel()->getAnnualCard($virtualNo, 'virtual_no');
        if ($cardInfo === false) {
            return $this->returnData(204, '根据虚拟卡号查询年卡信息失败');
        }
        $ticketModel     = new \Model\Product\Ticket();
        $ticketInfo      = $ticketModel->getTicketInfoByPid($cardInfo['pid']);
        $cardInfo['tid'] = $ticketInfo['tid'];
        return $this->returnData(200, '根据虚拟卡号查询年卡信息成功', $cardInfo);
    }

    /**
     * 需要确认调用的地方 然后传递sid
     * 人脸检测
     * @param string $imageUrl 头像地址
     * @param  $sid
     * @return array
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     */
    public function detectFace(string $imageUrl, $sid = 0): array
    {
        if (!$imageUrl) {
            return $this->returnData(203, '缺少必要参数');
        }

        if (strpos($imageUrl, "https://") !== false) {
            $imageUrl = str_replace("https://", "http://", $imageUrl);
        }
        $imageUrl = @file_get_contents("$imageUrl");
        if (empty($imageUrl)) {
            return $this->returnData(205,"检测人脸失败");
        }
        $face   = new \Business\PftSystem\FaceCompare('baidu', $sid);
        $result = $face->detectFace(base64_encode($imageUrl), [
                'image_type'=>'BASE64',
                'liveness_control'=>'NORMAL',
            ],
            $sid
        );
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 计算当前用户有效的年卡数量
     * <AUTHOR>  Li
     * @date  2022-03-03
     *
     * @param  int  $memberId  年卡用户id
     *
     * @return array
     */
    public function getEffectCardNumberByMemberId(int $memberId)
    {
        if (!$memberId) {
            return $this->returnData(203, '用户id不能为空');
        }

        $num = $this->getAnnualModel()->countEffectCardNumberByMemberId($memberId);

        return $this->returnData(200, '数量获取成功', $num);
    }

    /**
     *  获取年卡会员列表
     * <AUTHOR>  Li
     * @date  2022-04-14
     *
     * @param  int  $sid  供应商id
     * @param  string  $identify  搜索条件
     * @param  int  $searchType  搜索类型  搜索类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
     * @param  string  $action  查询或统计，select查询，count统计
     * @param  int  $page  当前页数
     * @param  int  $size  每页条数
     * @param  int  $status  年卡状态
     * @param  string  $saleStart  出售开始时间
     * @param  string  $saleEnd  出售结束时间
     * @param  string  $activeStart  激活结束时间
     * @param  string  $activeEnd  激活结束时间
     * @param  int  $distributorId  查询指定分销商id
     * @param  int  $lid  景区id
     * @param  int  $tid  门票id
     *
     * @return array
     */
    public function getMemberList(int $sid, string $identify = '', int $searchType = 0, string $action = 'select', int $page = 1, int $size = 10, int $status = 1, string $saleStart = '', string $saleEnd = '',
        string $activeStart = '', string $activeEnd = '', int $distributorId = 0, int $lid = 0, int $tid = 0)
    {
        if (empty($sid) || (!empty($identify) && !in_array($searchType, [0, 1, 2, 3, 4, 5]))) {
            return $this->returnData(200, '相关参数缺失');
        }

        $pidArr = [];
        if (!empty($lid)) {
            //通过传入景区id 获取到pid
            $javaApi = new \Business\CommodityCenter\LandF();
            $pidArr  = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([], [$lid], [], 'pid', true);
        }

        if (!empty($tid)) {
            //通过传入门票id 获取到pid
            $ticketModel    = new \Model\Product\Ticket();
            $tmpPid         = $ticketModel->getTicketInfoById($tid, 'pid');
            $pidArr            = [$tmpPid['pid']];
        }

        $options = [
            'page_size'      => $size,
            'page'           => $page,
            'status'         => $status,
            'identify'       => $identify,
            'sale_start'     => $saleStart,
            'sale_end'       => $saleEnd,
            'active_start'   => $activeStart,
            'active_end'     => $activeEnd,
            'distributor_id' => $distributorId,
            'pid'            => $pidArr,
            'search_type'    => $searchType,
        ];

        $annualCardList = $this->getAnnualModel()->getMemberList($sid, $options, $action);
        if ($annualCardList) {
            foreach ($annualCardList as $key => &$item) {
                $item['status_name'] = AnnualCardConst::ANNUAL_CARD_STATUS_MAP[$item['status']] ?? '未知状态';
                //获取一次年卡状态
                //$annualStratus                         = AnnualCardConst::getAnnualFinalStatus($item['annual_status'], $item['avalid_end']);
                //$annualCardList[$key]['annual_status'] = $annualStratus;
                //$annualCardList[$key]['status_name']   = AnnualCardConst::ANNUAL_STATUS_MAP[$annualStratus] ?? '未知状态';
                //增加过期状态判断
                //禁用和挂失状态优先级更高， 这两种状态无需处理过期状态
                //if (!in_array($annualStratus, [AnnualCardConst::ANNUAL_STATUS_BAN, AnnualCardConst::ANNUAL_STATUS_LOSS]) && $item['avalid_begin'] && $item['avalid_end'] && $item['avalid_end'] < time()) {
                //    $annualCardList[$key]['status']      = 5;
                //    $annualCardList[$key]['status_name'] = '已过期';
                //}
            }
        }

        return $this->returnData(200, '年卡信息获取成功', $annualCardList);
    }

    /**
     * 获取年卡用户信息修改日志
     * <AUTHOR>  LI
     * @date  2022-07-07
     *
     * @param  int  $sid  供应商id
     * @param  string  $virtualNo  虚拟卡号
     * @param  int  $operatorId  操作人id
     * @param  string  $beginTime  开始日期
     * @param  string  $endTime  结束日期
     *
     * @return  array
     */
    public function getEditOperationRecord(int $sid, string $virtualNo = '', int $operatorId = 0, string $beginTime = '', string $endTime = '')
    {
        if (!$sid || !$virtualNo) {
            return $this->returnData(203, '相关参数缺失');
        }

        $result = $this->getAnnualBiz()->getEditOperationRecord($sid, $virtualNo, $operatorId, $beginTime, $endTime);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 年卡信息修改
     * <AUTHOR>  Li
     * @date  2022-07-14
     *
     * @param  int  $sid  供应商id
     * @param  int  $cardId  年卡id
     * @param  string  $dname  用户名称
     * @param  string  $mobile  手机号
     * @param  string  $idCardNo  供应商id
     * @param  string  $address  住址
     * @param  int  $province  省份id
     * @param  int  $city  城市id
     * @param  array  $avatar  头像数组
     * @param  int  $operatorId  操作人id
     * @param  int  $isWeb  true云票务
     *
     * @return  array
     */
    public function editCardInfo(int $sid, int $cardId, string $dname, string $mobile, string $idCardNo = '', string $address = '',
        int $province = 0, int $city = 0, array $avatar = [], int $operatorId = 0, bool $isWeb = false, $avalidEnd = '', $voucherType = 1, $affiliatesPerson = '')
    {
        if (!$sid || !$cardId || !$dname || !$mobile) {
            return $this->returnData(203, '相关参数缺失');
        }

        $paramsArr = [
            'card_id'           => $cardId,
            'name'              => $dname,
            'mobile'            => $mobile,
            'province'          => $province,
            'city'              => $city,
            'id_card'           => $idCardNo,
            'address'           => $address,
            'photos'            => $avatar,
            'avatar'            => $avatar,
            'opid'              => $operatorId,
            'isWeb'             => $isWeb, //是否云票务
            'avalid_end'        => $avalidEnd,
            'voucher_type'      => $voucherType,
            'affiliates_person' => $affiliatesPerson,
        ];

        $result = $this->getAnnualBiz()->editCardInfo($sid, (object)$paramsArr);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 检测门票对应实名制属性和人脸录入属性
     * <AUTHOR>  Li
     * @date  2022-07-07
     *
     * @param  int  $tid  门票id
     * @param  int  $source  渠道来源
     * @param  int  $type  检测类型 1下单 2激活
     *
     * @return  array
     */
    public function checkAnnualRealNameAndPhoto(int $tid, int $source, int $type = 1)
    {
        if (!$tid || !in_array($type, [1, 2])) {
            return $this->returnData(203, '参数异常');
        }

        $result = $this->getAnnualVerifyBiz()->checkAnnualRealNameAndPhoto($tid, $source, $type);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 为一键激活批量创建用户
     * <AUTHOR>  Li
     * @date   2022-07-29
     *
     * @param  array  $cardList  年卡一键激活卡列表
     *
     * @return array
     */
    public function batchCreateAccount(array $cardList)
    {
        $result = $this->getAnnualBiz()->batchCreateAccount($cardList);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 云票务获取年卡信息
     * <AUTHOR>  Li
     * @date   2022-10-17
     *
     * @param  int  $sid  供应商id
     * @param  int  $status  卡状态
     * @param  string  $identify  查询标识
     * @param  int  $page  是否是续费中查询 1 是 0 否
     * @param  int  $type  当前页数
     * @param  int  $size  每页条数
     * @param  bool  $isNew  是否新版本
     * @param  int  $pid  产品id true 是 false 否
     * @param  int  $searchType  查询类型 0物理卡 1 实体卡 2虚拟卡 3手机号 4身份证 5姓名
     * @param  int  $source  来源 0平台 10云票务
     * @param  int  $checkType  查询类型 1下单 2激活
     *
     * @return array
     * @throws \Exception
     */
    public function getMemberListForWeb($sid, $status, $identify, $page = 1, $size = 50, $type = 0, $isNew = false, $pid = 0, $searchType = -1, $source = 10, $checkType = 2, $timeType = 0)
    {
        $result = $this->getAnnualBiz()->getMemberList($sid, $status, $identify, $type, $isNew, $pid, $searchType, $source, $checkType, $page, $size, $timeType);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }
    /**
     * 年卡归档日期下拉列表
     * <AUTHOR>
     * @Date 2023/8/29 10:00
     * @return array
     */
    public function getCardArchiveConfig()
    {
        $result = $this->getAnnualManageBiz()->getCardArchiveConfig();
        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }
    /**
     * 云票务获取年卡信息
     * <AUTHOR>  Li
     * @date   2022-10-17
     *
     * @param  int  $sid  供应商id
     * @param  string  $identify  查询标识
     * @param  int  $tid  特权门票id
     * @param  int  $page  当前页数
     * @param  int  $pageSize  每页条数
     *
     * @return array
     * @throws \Exception
     */
    public function getMemberCheckCardList($sid, $identify, $tid = 0, $page = 1, $pageSize = 1000)
    {
        $result = $this->getAnnualBiz()->getMemberCheckCardList($sid, $identify, $tid, $page, $pageSize);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取年卡续费产品
     * <AUTHOR>  Li
     * @date   2022-10-22
     *
     * @param  int  $memberId  登录的用户id
     * @param  int  $applyId  年卡供应商id
     * @param  int  $landId  年卡产品景区id
     * @param  int  $cardFid  年卡所属用户id
     * @param  string  $identify  搜索年卡条件（身份证、手机号、物理卡号等）
     * @param  int  $type  获取类型 1续费 2升级
     * @param  int  $cardId  年卡id
     *
     * @return array
     * @throws \Exception
     */
    public function getRenewalCardList($memberId, $applyId, $landId, $cardFid, $identify = '', $type = 1, $cardId = 0)
    {
        $result = $this->getAnnualBiz()->getRenewalCardList($memberId, $applyId, $landId, $cardFid, $identify, $type, $cardId);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 年卡补卡操作
     * <AUTHOR>  Li
     * @date   2022-10-18
     *
     * @param  int  $sid  供应商id
     * @param  int  $cardId  年卡id
     * @param  string  $cardNo  实体卡号
     * @param  string  $physicsNo  物理卡号
     * @param  int  $memberId  用户id
     *
     * @return  array
     */
    public function operationAnnual($sid, $cardId, $cardNo, $physicsNo, $memberId, $isWeb = false)
    {
        if (!$sid || !$cardId || !$physicsNo || !$memberId) {
            return $this->returnData(203, '参数错误');
        }

        $checkRes = $this->getAnnualBiz()->checkAnnualCardNo($sid, $cardNo);
        if ($checkRes['code'] != 200) {
            return $this->returnData($checkRes['code'], $checkRes['msg']);
        }

        $options = [
            'memberid'   => $memberId,
            'id'         => $cardId,
            'card_no'    => $cardNo,
            'physics_no' => $physicsNo,
            'isWeb' => $isWeb,
        ];

        $operationRes = $this->getAnnualBiz()->operationAnnual($sid, $options);
        if ($operationRes['code'] != 200) {
            return $this->returnData(203, $operationRes['msg']);
        }

        return $this->returnData($operationRes['code'], $operationRes['msg']);
    }

    /**
     * 操作年卡状态
     *
     * @param int $sid 商家id
     * @param int $opId 操作人id
     * @param int $id 年卡id
     * @param int $type 操作类型
     * @param int $status 状态
     * @date 2024/04/02
     * @auther yangjianhui
     * @return array
     */
    public function changeAnnualStatus($sid, $opId, $id, $type, $status, $avalidEnd)
    {
        if (empty($sid) || empty($opId) || empty($id)) {
            return $this->returnData(203, '参数缺失');
        }
        $options = [
            'id'         => $id,
            'sid'        => $sid,
            'status'     => $status,
            'type'       => $type,
            'opt_member' => $opId,
            'opt_sid'    => $sid,
        ];
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        $annualModel = new CardModel(0, 0, $table);
        $result = $annualModel->operationAnnual($sid, $options);
        if ($result === false) {
            return $this->returnData(204, '操作失败');
        }

        return $this->returnData(200, '操作成功');
    }

    /**
     * 获取卡套餐对应的特权信息
     * <AUTHOR>  Li
     * @date   2022-10-18
     *
     * @param  int  $cardId  年卡id
     * @param  int  $sid  供应商id
     *
     * @return  array
     */
    public function getCardPrivilegeInfo($cardId, $sid)
    {
        if (!$cardId || !$sid) {
            return $this->returnData(203, '缺少必要参数');
        }

        $cardInfo = $this->getAnnualModel()->getCardInfoById($cardId, 'id,sid,pid');
        if (empty($cardInfo)) {
            return $this->returnData(203, '年卡数据异常');
        }

        $packagePrivilegeRes = (new \Business\AnnualCard\Package())->getPeriodPackagePrivilegeInfo($cardInfo['id'], $sid, '', [1, 2, 3, 4, 5]);
        if ($packagePrivilegeRes['code'] != 200) {
            return $this->returnData(203, $packagePrivilegeRes['msg']);
        }

        return $this->returnData(200,'获取成功', $packagePrivilegeRes['data']);
    }

    /**
     * 获取年卡套餐操作记录
     * <AUTHOR>
     * @Date 2023/8/9 18:05
     * @return array
     */
    public function getCardPackageRecordList(int $sid, int $cardId, string $virtualNo, int $page = 1, int $size = 10, string $ordernum = '', int $type = 0, int $operStart = 0, int $operEnd = 0, int $state = 0)
    {
        if (!$cardId || !$sid) {
            return $this->returnData(203, '缺少必要参数');
        }
        $result = (new \Business\AnnualCard\Package())->getPackageRecordList($sid, $cardId, $virtualNo, $page, $size, $ordernum, $type, $operStart, $operEnd, $state);
        if ($result['code'] != 200) {
            return $this->returnData(203, $result['msg']);
        }
        return $this->returnData(200,'获取成功', $result['data']);
    }
    /**
     * 补卡在线支付
     * <AUTHOR>  Li
     * @date   2022-10-22
     *
     * @param  int  $cardId  年卡id
     * @param  string  $cardNo  实体卡号
     * @param  string  $physicsNo  物理卡号
     * @param  int  $payWay  支付方式 0=支付宝 1=微信 9=现金
     * @param  string  $authCode  支付的条形码
     * @param  string  $payMoney  支付的金额 单位（分）
     * @param  int  $sid  供应商ID
     * @param  int  $memberId  会员ID
     * @param  int  $ordermode  下单渠道 10 ：云票务
     * @param  int  $pid  产品ID
     * @param  int  $operid  操作员ID(登录者id)
     *
     * @return  array
     */
    public function onlinePay($cardId, $cardNo, $physicsNo, $payWay, $authCode, $payMoney, $sid, $memberId, $ordermode, $pid, $operid, $isWeb = false, $avalidEnd = '')
    {
        if (!$cardId || !$cardNo) {
            return $this->returnData(203, '年卡id缺失');
        }

        //验证实体卡号唯一性
        $checkRes = $this->getAnnualBiz()->checkAnnualCardNo($sid, $cardNo, $avalidEnd);
        if ($checkRes['code'] != 200) {
            return $this->returnData($checkRes['code'], $checkRes['msg']);
        }

        $options = [
            'memberid'   => $memberId,
            'id'         => $cardId,
            'card_no'    => $cardNo,
            'physics_no' => $physicsNo,
            'isWeb' => $isWeb,
            'ordermode' => $ordermode,
            'operid' => $operid,
        ];

        $payRes = $this->getAnnualBiz()->onlinePay($payWay, $authCode, $payMoney, $sid, $memberId, $options, $ordermode, $pid, $operid, $avalidEnd);

        return $this->returnData($payRes['code'], $payRes['msg']);
    }
    /**
     * 云票务删除实体卡
     * <AUTHOR>
     * @Date 2023/8/29 15:58
     */
    public function releaseEntityCard($sid, $cardFid, $cardNo, $cardId, $isWeb = false, $avalidEnd = '', $optMember = 0)
    {
        if (!$cardId || !$sid || !$cardFid || !$cardNo) {
            return $this->returnData(203, '参数错误');
        }
        $table = $this->getArchiveAnnualCardTable($avalidEnd);
        return $this->getAnnualBiz()->releaseEntityCard($sid, $cardFid, $cardNo, $cardId, $optMember, $isWeb, $table);
    }

    /**
     * 获取有效卡套餐特权信息
     *
     * @param  int  $cardId  年卡id
     * @param  int  $sid  供应商id
     * @param  array  $onlyTidArr  只需要保留的特权门票id数组
     *
     * @return array
     */
    public function getPackagePrivilegeInfo($cardId, $sid, $onlyTidArr = [])
    {
        if (!$cardId) {
            return $this->returnData(203, '年卡id缺失');
        }

        $cardInfo = $this->getAnnualModel()->getCardInfoById($cardId, 'id,sid,pid');
        if (empty($cardInfo)) {
            return $this->returnData(203, '年卡数据异常');
        }

        $privilegeInfoRes = (new \Business\AnnualCard\Package())->getPeriodPackagePrivilegeInfo($cardId, $sid, '', [2]);
		//AnnualUtil::debug([
			//'看下获取的套餐'=> $privilegeInfoRes,
			//'debug' => (new \Business\AnnualCard\Package())->getPeriodPackagePrivilegeInfo($cardId, $sid, '', [1,2]),
		//]);
		if($privilegeInfoRes['code'] != 200){
			$allPrivArr = (new \Business\AnnualCard\Package())->getPeriodPackagePrivilegeInfo($cardId, $sid, '', [1,2]);
			if($allPrivArr['code'] == 200 && count($allPrivArr['data'])){
				foreach($allPrivArr['data'] as $_){
					if($_['state'] == 1 && $_['delay_enable'] == 1 && (int)$_['start_time'] > 0) {
						$activeTime = date('Y-m-d H:i', (int)$_['start_time']);
						$emsg = "该年卡未到生效时间，{$activeTime}后生效";
						AnnualUtil::debug(['title' => $emsg]);
						return $this->returnData(203, $emsg, null);
					}
				}
			}
		}
        $privilegeHandRes = (new \Business\AnnualCard\Privilege())->privilegeHandler($privilegeInfoRes['data'], 0, $onlyTidArr);

        return $this->returnData($privilegeHandRes['code'], $privilegeHandRes['msg'], $privilegeHandRes['data']);
    }

    /**
     * 匹配年卡
     * <AUTHOR>  Li
     * @date   2022-11-23
     *
     * @param  string  $identify  刷卡标识
     * @param  string  $type  刷卡标识类型
     * @param  integer  $sid  供应商id
     * @param  integer  $pid  特权门票pid
     *
     * @return array
     */
    public function parseAnnualCard($sid, $identify, $type, $pid = 0)
    {
        $parseRes = $this->getAnnualBiz()->parseAnnualCard($sid, $identify, $type, $pid);

        return $this->returnData($parseRes['code'], $parseRes['msg'], $parseRes['data']);
    }

    /**
     * 选择优先使用的年卡
     *
     * <AUTHOR>
     * @date 2023/05/24
     *
     *
     * @param  int  $sid  供应商id
     * @param  array  $cardData  年卡列表
     * @param  int  $pid  特权门票pid
     * @param  bool  $mulit  查找多张年卡
     *
     * @return array
     */
    public function selectPriorityCard($sid, $cardData, $pid = 0, $mulit = false, $tnum = 1, $virtualNoToIdx = [])
    {
        if (empty($sid) || empty($cardData)) {
            return $this->returnData(203, '参数有误');
        }
        return (new \Business\AnnualCard\CardManage())->selectPriorityCard($sid, $cardData, $pid, $mulit, $tnum, $virtualNoToIdx);
    }

    /**
     * 检测年卡特权次数
     *
     * <AUTHOR>
     * @date 2023/05/24
     *
     * @param  int  $sid  供应商id
     * @param  int  $cardId  年卡id
     * @param  int  $packageId  套餐id
     * @param  int  $tid  特权门票id
     *
     * @return array
     */
    public function privilegeCheck($sid, $cardId, $packageId, $tid)
    {
        if (!$cardId || !$sid || !$packageId || !$tid) {
            return $this->returnData(203, '参数错误');
        }
        $selectRes = (new \Business\AnnualCard\CardManage())->privilegeCheck($sid, $cardId, $packageId, $tid);

        return $selectRes;
    }


    /**
     * 获取指定套餐关联操作产品列表
     * <AUTHOR>  Li
     * @date  2023-02-08
     *
     * @param  int  $cardId  年卡id
     * @param  int  $aid  上级供应商id
     * @param  int  $mid  分销商id
     * @param  int  $type  获取类型 1续费 2升级
     *
     * @return  array
     * @throws \Exception
     */
    public function getPackageRelationSaleList($cardId, $aid, $mid, $type = 1)
    {
        if (!$aid || !$mid || !$cardId || !in_array($type, [1, 2])) {
            return $this->returnData(203, '缺少必要参数');
        }

        $result = (new \Business\AnnualCard\ProductBiz())->getPackageRelationSaleList($cardId, $aid, $mid, $type);

        return $this->returnData($result['code'], $result['msg'], $result['data']);
    }

    private function getAnnualBiz()
    {
        if ($this->_annualBiz == null) {
            $this->_annualBiz = new annualBiz();
        }
        return $this->_annualBiz;
    }

    private function getAnnualVerifyBiz()
    {
        if ($this->_annualVerifyBiz == null) {
            $this->_annualVerifyBiz = new AnnualCardVerifyBiz();
        }
        return $this->_annualVerifyBiz;
    }
    private function getAnnualManageBiz()
    {
        if ($this->_annualManageBiz == null) {
            $this->_annualManageBiz =  new CardManage();
        }
        return $this->_annualManageBiz;
    }
    public function getAnnualModel()
    {
        if ($this->_annualModel == null) {
            $this->_annualModel = new annualModel();
        }
        return $this->_annualModel;
    }

    public function getAnnualCardInfo($virtualNo)
    {
        $cardInfo = $this->getAnnualBiz()->getAnnualCardInfo($virtualNo);
        return $this->returnData(200, '获取成功', $cardInfo);
    }

    /**
     * 根据供应商id获取百度人脸独立qps配置
     * <AUTHOR>
     * @Date 2023/9/5 15:55
     * @param $mid
     * @return mixed
     */
    public function getBaiduQpsConf($mid = 0)
    {
        $faceRpc = new Face();
        $res = $faceRpc->getBaiduQpsConf($mid);
        if (!isset($res['code']) || $res['code'] != 200) {
            $config = load_config('newbaidu', 'face');
            $env = ENV;
            return $config[$env];
        }
        return $res['data'];
    }
    /*
     * jsonrpc 添加年卡操作记录
     * <AUTHOR>
     * @Date 2023/8/21 17:44
     * @return array
     */
    public function addCardManageRecordWithCardInfo(int $type, array $options, array $cardInfo)
    {
        $cardManageService = new \Business\AnnualCard\AnnualCardManage();
        return $cardManageService->addCardManageRecordWithCardInfo($type, $options, $cardInfo);
    }

    /**
     * 批量获取年卡关联人信息
     *
     * @param $cardIdArr
     *
     * @return array
     */
    public function batchGetAffiliatesPerson($cardIdArr)
    {
        if (empty($cardIdArr)) {
            return $this->returnData(203, '缺少必要参数');
        }
        $annualCardManage = new AnnualCardManage();

        return $annualCardManage->batchGetAffiliatesPerson($cardIdArr);
    }

    /**
     * 添加年卡操作记录
     *
     * @param  int  $cardId
     *
     * @return array
     */
    public function getAffiliatesPerson($cardId)
    {
        if (empty($cardId)) {
            return $this->returnData(203, '缺少必要参数');
        }
        $annualCardManage = new AnnualCardManage();

        return $annualCardManage->getAffiliatesPerson($cardId);
    }
}
