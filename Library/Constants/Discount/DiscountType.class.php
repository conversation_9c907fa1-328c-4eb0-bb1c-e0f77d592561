<?php
/**
 * 下单渠道定义
 * <AUTHOR>
 * @date 2020/8/13
 */

namespace Library\Constants\Discount;

defined('PFT_INIT') or exit('Access Denied');

class DiscountType
{
    const DISCOUNT_POINT    = 1;  //积分
    const DISCOUNT_COUPONS  = 2;  //优惠券
    const DISCOUNT_MEMBER_REBATE    = 3;  //会员折扣
    const DISCOUNT_DEDUCT   = 4;  //结算减免
    const DISCOUNT_SPECIAL  = 5;  //结算特价
    const DISCOUNT_FIRST_ORDER  = 6;  //首单立减
    const DISCOUNT_FLASH_SALE  = 7;  //限时抢购
    /**----------订单详情----------*/
    const DISCOUNT_ORDER_MAP = [
        'points',
        'pointsAmount',
        'couponAmount',
        'use_point',
        'use_coupon' ,
        'use_discount',
        'ticket_price',
        'ticket_member_discount_price',
    ];


    /**----------订单详情----------*/
    //订单详情 优惠明细字段
    const DISCOUNT_TYPE_RES_MAP   = [
        self::DISCOUNT_POINT  =>[
            'discountCode' => 'point_num',//支付积分
            'discountAmount' => 'point_amount',//积分抵扣总面额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            //以下为特殊字段，需业务处理
            'point_use_amount' => 'point_use_amount' //实际优惠金额 总优惠金额 - 优惠退款金额
        ],
        self::DISCOUNT_COUPONS  =>[
            'discountCode' => 'new_coupons',//券码
            'discountAmount' => 'new_coupons_amount',//优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
        ],
        self::DISCOUNT_MEMBER_REBATE => [
            'discountCode' => 'discount_rate',//折扣比例
            'discountAmount' => 'discount_amount',//抵扣面额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
        ],
        self::DISCOUNT_DEDUCT => [
            'discountCode' => '',//
            'discountAmount' => 'deduct_amount',//减免优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
        ],
        self::DISCOUNT_SPECIAL => [
            'discountCode' => '',//
            'discountAmount' => 'special_amount',//特价票优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
        ],
        self::DISCOUNT_FIRST_ORDER => [
            'discountCode' => '',//立减金额
            'discountAmount' => 'first_order_reduce_amount',//优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            'discountDesc' => 'first_order_reduce_num',
        ],
        self::DISCOUNT_FLASH_SALE => [
            'discountCode' => '',
            'discountAmount' => 'flash_sale_amount', //限时抢购优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
        ]
    ];
    //订单详情 优惠结果层级字段
    const DISCOUNT_TYPE_FIELD_MAP   = [
        self::DISCOUNT_POINT  => 'points',
        self::DISCOUNT_COUPONS  =>'new_coupons',
        self::DISCOUNT_MEMBER_REBATE => 'member_discount',
        self::DISCOUNT_DEDUCT => 'settlement_discount',
        self::DISCOUNT_SPECIAL =>'settlement_discount',
        self::DISCOUNT_FIRST_ORDER =>'first_order',
        self::DISCOUNT_FLASH_SALE =>'flash_sale',//限时抢购
    ];
    //实付金额减去分销优惠金额 优惠明细字段映射表
    const SETTLEMENT_DISCOUNT_FIELD_MAP   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
    ];
    const SETTLEMENT_DISCOUNT_FIELD_MAP_FOR_B   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS]  => ['new_coupons_amount'],
    ];

    /**----------订单列表 汇总----------*/
    //买入成本 使用旧版优惠 当前登录等于分销商
    const TOTAL_COST_FIELD_MAP_DISTRIBUTION   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
    ];
    const TOTAL_COST_FIELD_MAP_DISTRIBUTION_FOR_B = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount', 'special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS] => ['new_coupons_amount'],
    ];
    //销售成本 使用旧版优惠 当前登录等于分销商
    const TOTAL_SELL_FIELD_MAP_DISTRIBUTION   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS]  => ['new_coupons_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];
    const TOTAL_SELL_FIELD_MAP_DISTRIBUTION_FOR_B   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];

    //买入成本 使用旧版优惠 当前登录等于供应商
    const TOTAL_COST_FIELD_MAP_APPLY   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
    ];
    const TOTAL_COST_FIELD_MAP_APPLY_FOR_B   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS]  => ['new_coupons_amount'],
    ];
    //销售成本 使用旧版优惠 当前登录等于供应商
    const TOTAL_SELL_FIELD_MAP_APPLY   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS]  => ['new_coupons_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];

    const TOTAL_SELL_FIELD_MAP_APPLY_FOR_B   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];

    //买入成本 使用旧版优惠 通用
    const TOTAL_COST_FIELD_MAP_OLD_COMMON   = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
    ];
    //销售成本 使用旧版优惠 通用
    const TOTAL_SELL_FIELD_MAP_OLD_COMMON    = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS]  => ['new_coupons_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];

    //买入成本
    const TOTAL_COST_FIELD_MAP_COMMON    = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
    ];
    //买入成本
    const TOTAL_COST_FIELD_MAP_COMMON_FOR_B = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount', 'special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS] => ['new_coupons_amount'],
    ];
    //销售成本
    const TOTAL_SELL_FIELD_MAP_COMMON    = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_COUPONS]  => ['new_coupons_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];
    //销售成本
    const TOTAL_SELL_FIELD_MAP_COMMON_FOR_B    = [
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_POINT]  => ['point_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_MEMBER_REBATE]  => ['discount_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_DEDUCT]  => ['deduct_amount','special_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FIRST_ORDER]  => ['first_order_reduce_amount'],
        self::DISCOUNT_TYPE_FIELD_MAP[self::DISCOUNT_FLASH_SALE]  => ['flash_sale_amount'],
    ];

    /**----------分销优惠金额 汇总----------*/


    /**----------订单导出----------*/
    //订单导出 优惠明细字段
    const DISCOUNT_TYPE_RES_MAP_EXPORT   = [
        self::DISCOUNT_POINT  =>[
            'discountCode' => 'points',//支付积分
            'discountAmount' => 'point_amount',//积分抵扣总面额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            //以下为特殊字段，需业务处理
            'point_use_amount' => 'point_use_amount' //实际优惠金额 总优惠金额 - 优惠退款金额
        ],
        self::DISCOUNT_COUPONS  =>[
            'discountCode' => 'new_coupon_list',//券码
            'discountAmount' => 'new_coupon_amount',//优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            //以下为特殊字段，需业务处理
            'use_coupon_amount' => 'use_coupon_amount'
        ],
        self::DISCOUNT_MEMBER_REBATE => [
            'discountCode' => '',//折扣比例
            'discountAmount' => 'use_discount_amount',//抵扣面额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            //以下为特殊字段，需业务处理
            'member_discount_amount' => 'member_discount_amount'
        ],
        self::DISCOUNT_DEDUCT => [
            'discountCode' => '',//
            'discountAmount' => 'use_settlement_deduct_amount',//减免优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            //以下为特殊字段，需业务处理
            'settlement_deduct_amount' => 'settlement_deduct_amount'
        ],
        self::DISCOUNT_SPECIAL => [
            'discountCode' => '',//
            'discountAmount' => 'use_settlement_special_amount',//特价票优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            //以下为特殊字段，需业务处理
            'settlement_special_amount' => 'settlement_special_amount'
        ],
        self::DISCOUNT_FIRST_ORDER => [
            'discountCode' => '',
            'discountAmount' => 'first_order_reduce_amount',//优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
            'discountDesc' => 'first_order_reduce_num',//立减金额
        ],
        self::DISCOUNT_FLASH_SALE => [
            'discountCode' => '',
            'discountAmount' => 'flash_sale_amount',//限时抢购优惠金额
            'refundDiscountCode' => '',//优惠退款金额
            'refundDiscountAmount' => '',//优惠退款金额
        ],
    ];
    //卖出分销优惠金额
    const SALE_SETTLEMENT_DISCOUNT = [
        'settlement_deduct_amount',
        'settlement_special_amount'
    ];
    //买入分销优惠金额
    const BUY_SETTLEMENT_DISCOUNT = [
        'settlement_deduct_amount',
        'settlement_special_amount'
    ];
    //买入分销优惠金额 (b端自采涉优惠券优惠的场景)
    const BUY_SETTLEMENT_DISCOUNT_FOR_B = [
        'settlement_deduct_amount',
        'settlement_special_amount',
        'new_coupon_amount'
    ];
    //销售优惠总额  是否顶级供应商
    const SALE_DISCOUNT_AMOUNT_TRUE = [
        'member_discount_amount',
        'coupons_amount',
        'point_amount',
        'discount_amount',
        'new_coupon_amount',
        'first_order_reduce_amount',
        'flash_sale_amount'
    ];
    const SALE_DISCOUNT_AMOUNT_FALSE = [
        'member_discount_amount',
        'coupons_amount',
        'point_amount',
        'new_coupon_amount',
        'first_order_reduce_amount',
        'flash_sale_amount'
    ];
    //销售优惠总额  是否顶级供应商
    const SALE_DISCOUNT_AMOUNT_TRUE_FOR_B  = [
        'member_discount_amount',
        'coupons_amount',
        'point_amount',
        'discount_amount',
        'first_order_reduce_amount',
        'flash_sale_amount'
    ];
    const SALE_DISCOUNT_AMOUNT_FALSE_FOR_B  = [
        'member_discount_amount',
        'coupons_amount',
        'point_amount',
        'first_order_reduce_amount',
        'flash_sale_amount'
    ];
    //下单时的分销优惠金额
    const ORDER_SETTLEMENT = [
        'use_settlement_deduct_amount',
        'use_settlement_special_amount',
    ];
    //下单时优惠金额（不含折扣）
    const ORDER_DISCOUNT_AMOUNT = [
        'use_discount_amount',
        'coupons_amount',
        'point_use_amount',
        'use_coupon_amount',
        'first_order_reduce_amount',
        'flash_sale_amount'
    ];
    const ORDER_DISCOUNT_AMOUNT_FOR_B = [
        'use_discount_amount',
        'coupons_amount',
        'point_use_amount',
        'first_order_reduce_amount',
        'flash_sale_amount'
    ];


    /**----------报表优惠相关----------*/

    /**
     * 注：新增优惠类型需要报表计算该优惠时，请同时修改。
     * 报表目前分营销优惠和分销优惠，对应定义里面需要配置优惠类型，报表才能对应计算。
     */

    //报表标识游客优惠
    const TOURIST_DISCOUNT = 'discount';

    //报表标识分销优惠
    const SETTLEMENT_DISCOUNT = 'settlement_discount';

    //全部优惠类型
    const ALL_DISCOUNT_TYPE = [
        self::DISCOUNT_POINT,
        self::DISCOUNT_COUPONS,
        self::DISCOUNT_MEMBER_REBATE,
        self::DISCOUNT_DEDUCT,
        self::DISCOUNT_SPECIAL,
        self::DISCOUNT_FIRST_ORDER,
        self::DISCOUNT_FLASH_SALE,
    ];

    //游客优惠类型
    const TOURIST_DISCOUNT_TYPE = [
        self::DISCOUNT_POINT,
        self::DISCOUNT_COUPONS,
        self::DISCOUNT_MEMBER_REBATE,
        self::DISCOUNT_FIRST_ORDER,
        self::DISCOUNT_FLASH_SALE,
    ];

    //分销优惠类型
    const SETTLEMENT_DISCOUNT_TYPE = [
        self::DISCOUNT_DEDUCT,
        self::DISCOUNT_SPECIAL,
    ];

}