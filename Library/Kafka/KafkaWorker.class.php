<?php
/**
 * kafka客户端
 * <AUTHOR>
 * @date 2021/3/17
 */

namespace Library\Kafka;


class KafkaWorker
{
    private $consumerKafka;

    private $consumerConfig;

    private $expectInvoiceBillableOffset = [];
    private $dir = 'Consumer';


    public function __construct($config)
    {
        $host                 = $config['host'];
        $groupId              = $config['group_id'];
        $autoOffsetReset      = $config['auto_offset_reset'];
        $this->consumerConfig = $config['consumer'];

        if (!empty($config['expect_invoice_billable_offset'])) {
            $expectInvoiceBillableOffset = explode(',', $config['expect_invoice_billable_offset']);
            foreach ($expectInvoiceBillableOffset as $value) {
                list($partition, $offset) = explode(':', $value);
                $this->expectInvoiceBillableOffset[$partition] = $offset;
            }
        }

        $conf = new \RdKafka\Conf();
        $conf->set('group.id', $groupId);
        $conf->set('metadata.broker.list', $host);
        $conf->set('enable.auto.commit', 'false');
        $conf->set('auto.offset.reset', $autoOffsetReset);

        $this->consumerKafka = new \RdKafka\KafkaConsumer($conf);

    }

    public function consumer()
    {
        $topics = array_keys($this->consumerConfig);

        $this->consumerKafka->subscribe($topics);

        $object = [];
        foreach ($this->consumerConfig as $key => $value) {
            $papa         = explode('/', $value);
            $class        = "\\{$this->dir}\\{$papa[0]}\\{$papa[1]}";
            $object[$key] = new $class();
        }

        while (true) {
            $msg = $this->consumerKafka->consume(5 * 1000);

            if (null === $msg || $msg->err === -191) {
                // Constant check required by librdkafka 0.11.6. Newer librdkafka versions will return NULL instead.
                continue;
            } elseif ($msg->err) {
                // echo $msg->errstr(), "\n";
                //break;
                continue; //防止进程退出导致kafka一直重连
            } else {
                $time = date('Y-m-d H:i:s');
                echo("[{$time}] get message on topic {$msg->topic_name} at partiton {$msg->partition}\n");
                if (time() <= strtotime('2025-05-30 23:59:59')) {
                    pft_log('kafka/consumer', json_encode(['message' => $msg, 'time' => $time]));
                }

                $temp               = json_decode($msg->payload, true);
                $temp['partition']  = $msg->partition;
                $temp['offset']     = $msg->offset;
                $temp['topic_name'] = $msg->topic_name;
                $temp['expect_invoice_billable_offset'] = $this->expectInvoiceBillableOffset;

                try {
                    $topic                = $temp['topic_name'];
                    $object[$topic]->args = $temp;
                    $object[$topic]->perform();
                } catch(\Throwable $ex){}

                $this->consumerKafka->commit($msg);

                //$temp = [
                //    'partition'  => 1,
                //    'offset'     => 1,
                //    'topic_name' => 'ticketAttOpTopic',
                //    'opType'     => 'multiDistJob',
                //    'param'       => [
                //        'action' => 'update_product_price',
                //        'tid' => 123,
                //    ],
                //
                //];

            }

        }

        return true;
    }

    //private function _getData()
    //{
    //    //每次获取200条
    //    $i    = 200;
    //    $list = [];
    //
    //    while ($i--) {
    //        $msg = $this->consumerKafka->consume(5 * 1000);
    //        if (null === $msg || $msg->err === -191) {
    //            // Constant check required by librdkafka 0.11.6. Newer librdkafka versions will return NULL instead.
    //            continue;
    //        } elseif ($msg->err) {
    //            // echo $msg->errstr(), "\n";
    //            break;
    //        } else {
    //            $temp               = json_decode($msg->payload, true);
    //            $temp['partition']  = $msg->partition;
    //            $temp['offset']     = $msg->offset;
    //            $temp['topic_name'] = $msg->topic_name;
    //            $list[]             = $temp;
    //
    //            $this->consumerKafka->commit($msg);
    //        }
    //    }
    //
    //    //$list[] = [
    //    //    'partition'  => 1,
    //    //    'offset'     => 1,
    //    //    'topic_name' => 'ticketAttOpTopic',
    //    //    'opType'     => 'multiDistJob',
    //    //    'param'       => [
    //    //        'action' => 'update_product_price',
    //    //        'tid' => 123,
    //    //    ],
    //    //
    //    //];
    //    //
    //    //$list[] = [
    //    //    'partition'  => 1,
    //    //    'offset'     => 1,
    //    //    'topic_name' => 'ticketAttOpTopic',
    //    //    'opType'     => 'delTerminalCache',
    //    //    'param'       => [
    //    //        'tid' => 321,
    //    //    ],
    //    //];
    //
    //
    //    return $list;
    //}
}