<?php

namespace Library\MessageNotify;

use Business\JavaApi\Product\ChannelInspectConvert;
use Business\JavaApi\TicketApi;
use Business\JsonRpcApi\MessageService\MessageService;
use Business\Member\MemberWx;
use Business\Notice\WxConfig;
use Business\Order\OrderSearch;
use Business\PftSystem\SysKeyMonitor;
use Library\Cache\Cache;
use Library\Constants\DingTalkRobots;
use Library\Constants\Order\OrderStatus\CommonOrderStatus;
use Library\Container;
use Library\Model;
use Library\Resque\Queue;
use Library\Tools;
use Library\Tools\Helpers;
use Model\JumpAppletConfig\JumpAppletConfig;
use Model\Member\Member;
use Model\Order\OrderCommon;
use Model\Order\OrderQuery;
use Model\Order\OrderTools;
use Model\Order\SmsJournal;
use Model\Product\Ticket;
use Model\SmsDiy\SmsDiyApply;
use Model\Wechat\OfficialAccountMsgSetting;
use Model\Wechat\WxMember;
use Model\Member\MemberRelationship;
use Model\Order\TeamOrderSearch;
use Business\Member\PlatformExpense;
use Library\Constants\Table\MainTableConst;

class OrderNotify
{
    private $order_tel;
    private $unit;
    private $pid;
    private $p_type;
    private $order_num;
    private $team_order_num;
    private $hotel_order = 'hotel:';
    private $sellerId;

    private $title;
    private $buyerId;
    private $not_to_buyer;
    private $not_to_seller;
    const MEMBER_AUTH = 'new_order';  //微信推送权限标示
    const ORDER_CHANNEL23 = 23;
    /**
     * @var Model
     */
    private $model;

    private static $modelMaster = null;
    private        $smsSystem;
    //单条短信的长度
    private $_codeLength = 1500;

    private static $allowDiySmsType = [
        'C',  //酒店
        'C_NO_QR', //酒店无连接
        'NEW_C_NO_QR', //新的酒店
        'NEW_C', //新的酒店无连接
        'DEFAULT', //默认模板
        'DEFAULT_NO_QR', //默认无连接
        'J', //特产通知
        'ANNUAL_ORDER', //年卡
    ]; //允许发送自定义短信的类型

    //一定走创蓝的手机号配置
    private $chuangLanMobile = [
        '18150023819',
        '18060796020'
    ];

    /**
     * OrderNotify constructor.
     *
     * @param  string  $ordernum  订单号
     * @param  int  $buyerId  下单人ID
     * @param  int  $aid  顶级供应商ID
     * @param  string  $mobile  手机号
     * @param  int  $pid
     * @param  int  $sellerID
     * @param  int  $ptype
     * @param  string  $title
     * @param  int  $not_to_buyer
     * @param  int  $not_to_seReportPushller
     *
     * @throws \Exception
     */
    public function __construct($ordernum, $buyerId, $aid, $mobile, $pid = 0, $sellerID = 0, $ptype = 0, $title = '', $not_to_buyer = 0, $not_to_seller = 0)
    {

        $this->model     = new Model('slave');
        $this->smsSystem = new SmsSystem();

        $this->order_num     = $ordernum;
        $this->sellerId      = $sellerID;
        $this->buyerId       = $buyerId;
        $this->order_tel     = $mobile;
        //把带有区号86的手机号格式化成不带区号
        if (strpos($mobile, '-') !== false) {
            $mobileArr = explode('-', $mobile);
            if ($mobileArr[0] == '86') {
                $this->order_tel = $mobileArr[1];
            }
        }
        $this->p_type        = $ptype;
        $this->unit          = $ptype === 'C' ? '间' : '张';
        $this->aid           = $aid;
        $this->pid           = $pid;
        $this->title         = $title;
        $this->not_to_buyer  = $not_to_buyer;
        $this->not_to_seller = $not_to_seller;
        if (!$ordernum) {
            throw new \Exception("手机号{$mobile}:订单号格式错误无法发送短信" . json_encode(func_get_args()));
        }
        pft_log('sms/data', '__construct args:' . json_encode(func_get_args(), JSON_UNESCAPED_UNICODE));
    }

    /**
     * 获取主库连接
     *
     * @return Model|null
     */
    private static function getMasterModel()
    {
        if (is_null(self::$modelMaster)) {
            self::$modelMaster = new Model('localhost_wsdl');
        }

        return self::$modelMaster;
    }

    /**
     * 获取微信通知订单详情页的地址
     *
     * @param  int  $ordernum  订单号
     *
     * @return string
     */
    private function _getDetailUrl($ordernum, $ptype)
    {
        if($ptype == 'Q'){
            $infos = (new \Business\JsonRpcApi\TravelVoucherService\OrderQuery())->getOrderInfoDetail([$ordernum]);
            if(empty($infos)){
                pft_log('detailUrl/fail', json_encode($infos));
            }
            $infos = reset($infos);
            $url = $infos['order_detail_url'] ?? '';
        }
        else{
            //统一使用新版微商城的订单详情页
            $url = MOBILE_DOMAIN . 'c/order_detail.html?ordernum=';
            //订单号加密
            $ordernum = self::url_sms($ordernum);
            $url .= $ordernum;
        }
        return $url;
    }

    /**
     * 获取短信里面与订单相关的信息
     *
     * @return array
     */
    public function getOrderInfo()
    {
        $orderToolMdl = new OrderTools('slave');
        $order_info   = $orderToolMdl->getOrderInfo($this->order_num,
            'ss.ordernum,ss.member,ss.lid,ss.tid,ss.aid,ss.tnum,ss.ordername,ss.ordertel,ss.begintime,ss.endtime,ss.code,ss.remsg,ss.paymode,ss.ordermode,ss.ordertime,ss.playtime,ss.status,ss.visitors,ss.remotenum',
            'de.concat_id,de.memo,de.product_ext,de.ext_content,de.series');
        if (empty($order_info)) {
            return false;
        }

        //if ($order_info['remsg'] >= 3) {
        //    return 116;
        //}
        // 渠道售检需要参数
        $channelConvertData = [
            [
                'ticketId'  => $order_info['tid'],
                'sid'       => $order_info['aid'],
                'fid'       => $order_info['member'],
                'ordermode' => $order_info['ordermode'],
                'visitors'  => $order_info['visitors'],
            ],
        ];
        $tickets_list       = [$order_info['tid'] => $order_info['tnum']];
        //检测必要的参数是否为null
        if (!$this->order_tel) {
            $this->order_tel = $order_info['ordertel'];
        }
        if (!$this->buyerId) {
            $this->buyerId = $order_info['member'];
        }
        if (!$this->aid) {
            $this->aid = $order_info['aid'];
        }

        $time_list  = [$order_info['begintime']];
        $linksOrder = $orderToolMdl->getLinkOrdersInfo($this->order_num,
            'ss.tid,ss.tnum,ss.begintime,ss.member,ss.aid,ss.ordermode,ss.visitors,ss.ordernum');

        if ($linksOrder) {
            foreach ($linksOrder as $item) {
                if ($item['ordernum'] == $this->order_num) {
                    continue;
                }
                $tickets_list[$item['tid']] = $item['tnum'];
                $time_list[]                = $item['begintime'];
                $channelConvertData[]       = [
                    'ticketId'  => $item['tid'],
                    'sid'       => $item['aid'],
                    'fid'       => $item['member'],
                    'ordermode' => $item['ordermode'],
                    'visitors'  => $item['visitors'],
                ];
            }
        } else {
            $time_list[] = $order_info['begintime'];
        }
        $begin_time = $order_info['begintime'];
        $end_time   = $order_info['endtime'];

        if (!$this->pid) {
            $landApi = new \Business\CommodityCenter\Land();
            $landArr = $landApi->queryLandMultiQueryById([$order_info['lid']]);
            $land    = $landArr[0] ?? [];

            $this->p_type = $land['p_type'];
            switch ($land['p_type']) {
                case 'J':
                    $this->unit = '件';
                    break;
                case 'C':
                    $this->unit = '间';
                    break;
                default:
                    $this->unit = '张';
                    break;
            }
            $this->title    = $land['title'];
            $this->sellerId = $land['apply_did'];
            unset($land);
        }
        //酒店类型比较特殊
        if ($this->p_type === 'C') {
            $isJointMainOrder = true;  //是不是联票主票
            $cacheRedis       = \Library\Cache\CacheRedis::getInstance('redis');
            if ($order_info['ordernum'] != $order_info['concat_id'] && !empty($order_info['concat_id'])) {       //只有主票联票会发短信
                $cacheRedis->setex($this->hotel_order . $order_info['ordernum'], 300, '1');
                $isJointMainOrder = false;
            }

            sort($time_list);
            $begin_time = array_shift($time_list);
            $end_time   = array_pop($time_list);
            $end_time   = date('Y-m-d', strtotime('+1 days', strtotime($end_time)));
            if ($isJointMainOrder) {
                $extContent = json_decode($order_info['ext_content'], true) ?? [];
                if (isset($extContent['jointTicketDate']) && isset($extContent['jointTicketDate']['endDate']) && isset($extContent['jointTicketDate']['startDate'])) {
                    $begin_time = $extContent['jointTicketDate']['startDate'];
                    $end_time   = $extContent['jointTicketDate']['endDate'];
                }
            }
        }
        $tid_list = array_keys($tickets_list);

        $map = count($tid_list) > 1 ? ['tid' => ['in', $tid_list]] : ['tid' => $tid_list[0]];
        // 通过渠道获取
        $channelInspectConvertBiz = new ChannelInspectConvert();
        $channelTicketInfoRes     = $channelInspectConvertBiz->batchConvert($channelConvertData);
        if ($channelTicketInfoRes['code'] != 200) {
            //发送告警通知
            Helpers::sendDingTalkGroupRobotMessage(
                '短信发送获取门票属性异常，参数:' . json_encode($channelConvertData) . ' ， 返回参数:' . json_encode($channelTicketInfoRes),
                "短信发送获取门票属性异常",
                "订单号:{$this->order_num}," . gethostname(),
                DingTalkRobots::SMS_API_BASEGU,
                'markdown'
            );

            return false;
        } else {
            $extInfo               = [];
            $channelTicketInfoData = $channelTicketInfoRes['data'];
            foreach ($channelTicketInfoData as $item) {
                $extInfo[] = [
                    'sendVoucher'   => $item['uuLandFDTO']['sendvoucher'],
                    'pid'           => $item['uuLandFDTO']['pid'],
                    'confirm_sms'   => $item['uuLandFDTO']['confirmSms'],
                    'confirm_wx'    => $item['uuLandFDTO']['confirmWx'],
                    'fax'           => $item['uuLandDTO']['fax'],
                    'p_type'        => $item['uuLandDTO']['pType'],
                    'terminal_type' => $item['uuLandDTO']['terminalType'],
                    'tid'           => $item['uuJqTicketDTO']['id'],
                ];
            }
            // 排序
            array_multisort($tid_list, SORT_ASC, $extInfo);
        }
        $javaApi    = new \Business\CommodityCenter\Ticket();
        $tickets    = $javaApi->queryTicketInfoByIds($tid_list, 'id,pid,title,getaddr,pre_sale', '', '', '', true);
        $pname      = '';
        $getaddr    = '';
        $pid_list   = [];
        $preSaleArr = [];
        $order_ticket = [];
        foreach ($tickets_list as $tid => $tnum) {
            foreach ($tickets as $ticket) {
                if (!isset($ticket['ticket'][$tid])) {
                    continue;
                }
                if ($tid == $order_info['tid']) {
                    $master_pid = $ticket['ticket'][$tid]['pid'];
                }
                $order_ticket[] = ['tid' => $tid, 'title' => $ticket['ticket'][$tid]['title'], 'tnum' => $tnum . $this->unit];
                $getaddr    = $ticket['ticket'][$tid]['getaddr'];
                $pname      .= "\n{$ticket['ticket'][$tid]['title']}{$tnum}{$this->unit},";
                $pid_list[] = $ticket['ticket'][$tid]['pid'];
                $preSaleArr[$tid] = $ticket['ticket'][$tid]['pre_sale'];
            }
        }

        $externalCode = [];
        //当为门票类型时判断是否外部发码
        if ($this->p_type === 'A') {
            $extContent = json_decode($order_info['product_ext'], true) ?? [];
            if (isset($extContent['externalSendCode']) && $extContent['externalSendCode']) {
                $externalBiz = new \Business\ExternalCode\CodeManage();
                $externalRes = $externalBiz->getExternalCodeInfoByOrdernum($this->sellerId, $this->order_num);
                if (isset($externalRes['code']) && $externalRes['code'] == 200) {
                    $str         = implode(',', $externalRes['data']['list']);
                    $expireTime  = $externalRes['data']['expire_time'];
                    $showDetails = $externalRes['data']['show_details']; //短信是否显示订单详情  1是2否
                    $showCode    = $externalRes['data']['show_code']; //短信是否显示外部码  1是2否
                    $expireType  = $externalRes['data']['expire_type']; //发送给游客的有效期：1导码设置的有效期；2下单选择的游玩日期；3固定有效期
                    $startDt     = $externalRes['data']['start_dt']; //固定有效期开始时间
                    $endDt       = $externalRes['data']['end_dt']; //固定有效期结束时间
                    $tid         = $order_info['tid'];
                    $orderTime   = $order_info['ordertime'];
                    $playTime    = $order_info['playtime'];
                    $preSale     = isset($preSaleArr[$tid]) ? $preSaleArr[$tid] : 1; //默认为1不需要选择游玩日期

                    $externalCode['code']         = $str;
                    $externalCode['show_details'] = $showDetails;
                    $externalCode['show_code']    = $showCode;
                    $externalCode['remark']       = isset($externalRes['data']['remark']) ? $externalRes['data']['remark'] : ''; //短息备注说明
                    $externalCode['expire_time']  = $expireTime != 0 ? date("Y-m-d", $expireTime) : 0;

                    //短信有效期处理
                    $handleRes = $externalBiz->handleExternalCodeOrderTime($expireType, $preSale, $orderTime, $playTime,
                        $expireTime, $startDt, $endDt);
                    if (isset($handleRes['code']) && $handleRes['code'] == 200) {
                        $externalCode['begin_time'] = $handleRes['data']['begin_time'];
                        $externalCode['end_time']   = $handleRes['data']['end_time'];
                    } else {
                        $logData = [
                            'getOrderInfo' => '外部码有效期处理失败',
                            'sid'          => $this->sellerId,
                            'ordernum'     => $this->order_num,
                            'tid'          => $tid,
                            'ordertime'    => $orderTime,
                            'playtime'     => $playTime,
                            'pre_sale'     => $preSale,
                        ];
                        pft_log('codemsg/fail', json_encode($logData));

                        return false;
                    }
                } else {
                    pft_log('codemsg/fail', json_encode([
                        'getOrderInfo' => '外部码信息获取失败',
                        'sid'          => $this->sellerId,
                        'ordernum'     => $this->order_num,
                    ]));

                    return false;
                }
            }
        }

        //分时预约信息$extContent
        $extContentArr = json_decode($order_info['ext_content'], true) ?? [];
        $timeShareInfo = '';
        if (isset($extContentArr['sectionTimeStr'])) {
            $timeShareInfo = $extContentArr['sectionTimeStr'];
        }

        // 查询门票扩展信息
        $biz        = new TicketApi();
        $res        = $biz->getTicketList($order_info['tid']);
        $newExtInfo = $res['data'][0]['ext'];
        $this->pid  = $master_pid;
        /* foreach ($extInfo as $item) {
            if ($item['terminal_type'] == 2) {
                $order_info['code'] = str_pad($order_info['code'], 8, '0', STR_PAD_LEFT);
                break;
            }
        } */

        return [
            'lid'               => $order_info['lid'],
            'ltittle'           => $this->title,
            'buyer'             => $order_info['ordername'],
            'pname'             => rtrim($pname, ','),
            'pid'               => $master_pid,
            'code'              => $order_info['code'],
            'begintime'         => $begin_time,
            'endtime'           => $end_time,
            'getaddr'           => $getaddr,
            'memo'              => $order_info['memo'],//订单备注信息
            'pid_list'          => $pid_list,
            'extAttrs'          => $extInfo,
            'paymode'           => $order_info['paymode'],
            'ordermode'         => $order_info['ordermode'],
            'ordernum'          => $this->order_num,
            'playtime'          => $order_info['playtime'],
            'tid'               => $order_info['tid'],
            'new_ext_info'      => isset($newExtInfo) ? $newExtInfo : [],
            'order_status'      => $order_info['status'],
            'order_product_ext' => $order_info['product_ext'],
            'ext_content'       => $extContentArr,
            'supply_id'         => $order_info['aid'],
            'external_code'     => $externalCode,
            'sectionTimeStr'    => $timeShareInfo,
            'order_ticket'      => $order_ticket, //订单 商品名和数量
            'receive_address'   => '', //特产自定地址默认空
            'series'            => $order_info['series'], //演出信息
            'remotenum'         => $order_info['remotenum'], //远端订单号
        ];
    }

    /**
     * 获取短信模板配置
     *
     * @return mixed
     */
    private function SmsTemplate()
    {
        $Model = new Model('slave');
        $row   = $Model->table('uu_sms_format')->field('url_show,cformat,dtype,sms_account,sms_sign,is_diy,use_state,sms_id')
                       ->where(['pid' => $this->pid])
                       ->find();
        if (!$row) {
            $row = $Model->table('uu_sms_format')->field('url_show,cformat,dtype,sms_account,sms_sign,is_diy,use_state,sms_id')
                         ->where(['aid' => $this->sellerId, 'pid' => 0])
                         ->find();
        }

        return $row;
    }

    /**
     * 发送短信入口
     *
     * @param  int  $code
     * @param  bool  $manualQr
     * @param  bool  $is_resend
     * @param  int  $newMobile  重发新手机号，
     *                         0-不变
     *
     * @return array|bool
     */
    public function Send($code = 0, $manualQr = false, $is_resend = false, $newMobile = 0)
    {
        pft_log('ota_debug', 'Send:' . json_encode(func_get_args()));
        $r1    = true;
        $infos = $this->getOrderInfo();
        if (!is_array($infos) && $code == 0) {
            return $infos;
        }
        pft_log('wxNotice/default/WxNotify', json_encode($infos,JSON_UNESCAPED_UNICODE));
        if ($this->not_to_buyer != 1 || $infos['ordermode'] == 24) {
            $r1 = $this->BuyerNotifyV2($infos, $code, $manualQr, $is_resend, $newMobile);
            $jumpAppletConfigModel = new JumpAppletConfig();
            $jumpAppletConfigModel->msgNumOrUrlOpenNumIncByLid($infos['lid'], 1);//跳转小程序统计短信发送次数
        }
        else{
            //所有套票子票（包含演出）买入都不做通知
            //TODO 后续应拆分 短信发送标识 和 微信发送标识 由业务端自己判断，消息中心不做此类特殊逻辑处理
            //https://www.tapd.cn/43247536/prong/stories/view/1143247536001111021
            if($infos['ordermode'] != self::ORDER_CHANNEL23){
                //下单无视配置 发送微信通知给买家
                //https://www.tapd.cn/43247536/prong/stories/view/1143247536001101254
                $r1 = self::BuyerNotifyForWx($infos);
            }
        }
        if ($this->not_to_seller != 1) {
            $this->SellerNotify($infos, $is_resend);
        }

        return $r1;
    }

    /**
     * 消息中心-发送微信通知
     * @param int $code
     * @param false $manualQr
     * @param false $is_resend
     * @param int $newMobile
     * @return array|bool
     */
    public function SendWxNotice($isNoticeProvider, $isNoticeProviderStuff, $isNoticeDistributor, $isNoticeBuyer,$pType,$infos=[])
    {
        if($this->p_type == 'Q'){
            pft_log('wxNotice/SendWxNotice/Send', 'Send:' . json_encode(func_get_args()));
        }
        $res = true;
        //先区分 产品类型
        //再获取订单信息
        if($pType == 'Q'){
            $infos['supply_id'] = $infos['aid'];
            $infos['isNoticeDistributor'] = $isNoticeDistributor;
            $infos['extAttrs'] = [
                'sendVoucher'   => 1,
                'pid'           => $infos['pid'],
                'confirm_wx'    => 1,
                'p_type'        => 'Q',
                'tid'           => $infos['tid'],
            ];
        }
        else{
            $infos = $this->getOrderInfo();
        }
        if (!is_array($infos)) {
            pft_log('wxNotice/SendWxNotice/falseInfo', 'info:' . json_encode($infos));
            return false;
        }
        if($this->p_type == 'Q'){
            pft_log('wxNotice/SendWxNotice/SendInfo', 'SendInfo:' . json_encode($infos));
        }
        $res = $this->BuyerNotifyForWx($infos);
//        if ($isNoticeBuyer == true || $infos['ordermode'] == 24) {
//            $res = $this->BuyerNotifyForWx($infos);
//        }
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Seller', "微信通知商家的订单信息为3：" .$isNoticeProvider.$isNoticeProviderStuff.$isNoticeDistributor . json_encode($infos));
        }
        $this->SellerNotifyForWx($infos, $isNoticeProvider, $isNoticeProviderStuff, $isNoticeDistributor);
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Seller', "微信通知商家的订单信息为4：".$isNoticeProvider.$isNoticeProviderStuff.$isNoticeDistributor . json_encode($infos));
        }
        return $res;
    }

    /**
     * 微信通知-购买者
     * @param  array  $infos
     * @return bool
     */
    public function BuyerNotifyForWx(Array $infos)
    {
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/Buyer', ' info1：' . json_encode($infos, JSON_UNESCAPED_UNICODE));
        }
        //获取模板消息缓存数据
        $sendInfo    = \Business\OpenplatformOfficialAccount\OfficialAccountMsgSetting::getWxTemplateInfo($infos['supply_id']);
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/WxNotifyCustomer','getWxTemplateInfo_sendInfo:'.json_encode($sendInfo));
        }
        $appId       = $sendInfo['appid'];
        $tplId       = $sendInfo['template_id'];
        $sendToThird = $sendInfo['send_to_third'];
        $openidList  = $this->WxNotifyChk($this->buyerId, $appId);
        pft_log('wxNotice/default/WxNotify', json_encode([__METHOD__, 'buyerId'=>$this->buyerId, 'appId'=>$appId, 'openidList'=>$openidList],JSON_UNESCAPED_UNICODE));
        if ($openidList !== false) {
            $this->WxNotifyCustomer(
                $openidList,
                date('Y-m-d H:i'),
                $infos['buyer'],
                $this->title,
                $infos['pname'],
                $this->order_num,
                0,
                $infos['lid'],
                $sendToThird,
                $appId,
                $tplId
            );
        }
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/Buyer', ' info2：' . json_encode($infos, JSON_UNESCAPED_UNICODE));
        }
        return true;
    }

    /**
     * 向供应商发送通知信息
     * @param  array  $infos  参数
     * @param  bool  $is_resend  是否重发短信
     * @return bool
     */
    public function SellerNotifyForWx(Array $infos,$isNoticeProvider ,$isNoticeProviderStuff,$isNoticeDistributor)
    {
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Seller', "微信通知商家的订单信息为1：" . json_encode($infos, JSON_UNESCAPED_UNICODE));
        }

        //查看哪些产品需要发送短信给供应商
        $confirmWx      = 0;//接收微信通知标记
        $isNoticeDistributor = $infos['isNoticeDistributor'] ?? true;//供销链上的中间级分销商将收到订单微信通知标记
        if (isset($infos['extAttrs']['confirm_wx']) && $infos['extAttrs']['confirm_wx']) {
            $confirmWx = 1;
        }
//        //供销链上的中间级分销商将收到订单微信通知 reserve_success_distributor景区/线路/酒店/套票/餐饮/演出A=景点，B=线路，C=酒店，F=套票，G=餐饮，H=演出 J=特产
//        if (($infos['new_ext_info']['reserve_success_distributor'] || $infos['new_ext_info']['buy_success_distributor']) && in_array($this->p_type,['J','A','B','C','F','G','H'])) {
//            $isNoticeDistributor = 1;
//        }
        //微信通知
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Seller', "微信通知商家的订单信息为2：" . json_encode($infos, JSON_UNESCAPED_UNICODE));
        }
        $this->sendWechatNoticeToSellerForMessageCenter($confirmWx, $infos, $isNoticeProvider ,$isNoticeProviderStuff,$isNoticeDistributor);
        return true;
    }



    /**
     * 购买者短信通知
     *
     * @param  array  $infos
     * @param  int  $code  大于0标识为必须发送短信
     * @param  string  $manualQr  自定义二维码地址
     * @param  bool  $is_resend  是否为重发短信标识
     * @param  int  $newMobile  重发新手机号，
     *                           0-不变
     *
     * @return bool
     */
    public function BuyerNotifyV2(Array $infos, $code, $manualQr, $is_resend, $newMobile = 0)
    {
        //发送短信备注
        $memo = '';
        //是否累加订单发送短信次数
        $isChargeSms = false;
        //是否短信拆分成多条发送
        $isSplit = false;
        //默认模板长度
        $tmplateLength = 0;
        //获取模板消息缓存数据
        $sendInfo    = \Business\OpenplatformOfficialAccount\OfficialAccountMsgSetting::getWxTemplateInfo($infos['supply_id']);
        $appId       = $sendInfo['appid'];
        $tplId       = $sendInfo['template_id'];
        $sendToThird = $sendInfo['send_to_third'];

        //$lid = $infos['lid'] ?? null;
        //套票子票 - 均不发送微信通知给买方【即实际为打包方】
        if($infos['ordermode'] == self::ORDER_CHANNEL23 && $infos['extAttrs'][0]['p_type'] == 'H'){
            //原先是演出子票特殊处理，无视逻辑强行发送，此处微信通知，做特殊处理
            goto SMS_PROCESS;
        }
        $openidList  = $this->WxNotifyChk($this->buyerId, $appId);
        pft_log('wxNotice/default/WxNotify', json_encode([__METHOD__, 'buyerId'=>$this->buyerId, 'appId'=>$appId, 'openidList'=>$openidList],JSON_UNESCAPED_UNICODE));
        if ($openidList !== false) {
            $this->WxNotifyCustomer(
                $openidList,
                date('Y-m-d H:i'),
                $infos['buyer'],
                $this->title,
                $infos['pname'],
                $this->order_num,
                0,
                $infos['lid'],
                $sendToThird,
                $appId,
                $tplId
            );
        }
        SMS_PROCESS:
        if (isset($infos['ext_content']['is_not_send_sms']) && $infos['ext_content']['is_not_send_sms'] == 1) {
            pft_log('sms/orderSmsDebug', json_encode(['下单端要求不发送短信', $this->order_num, $infos['ext_content']['is_not_send_sms']], JSON_UNESCAPED_UNICODE));

            return true;
        }

        //是否发送凭证码（短信）到取票人手机  0 发送 1 不发送
        if ($infos['extAttrs'][0]['sendVoucher'] == 1 && empty($code)) {
            // 酒店订单
            // 如果配置了 供应商人工审核确认订单
            // 如果是待确认状态  且  门票配置了  通知游客订单待确认
            if ($this->p_type == 'C' && !empty($infos['order_status']) && isset($infos['new_ext_info']['order_room_check']) && $infos['new_ext_info']['order_room_check'] && $infos['order_status'] == 4 && $infos['new_ext_info']['notice_to_tourist'] == 1) {

            }else{
                return true;
            }
        }
        // 判断是否团单,并该团单下面的订单是否已发送数据
        $cacheRedis   = \Library\Cache\CacheRedis::getInstance('redis');
        $teamOrderNum = '';
        if (in_array($infos['ordermode'], [24, 44]) && !$this->checkTeamOrderNotice($cacheRedis)) {
            return true;
        }
        if ($this->_checkHotelOrderIsSend()) {
            return true;
        }
        //发送短信的手机号
        $mobile = $newMobile ?: $this->order_tel;

        if ($mobile == '') {
            pft_log('sms/fail', "订单号{$this->order_num}:手机号为空无法发送短信");

            return true;
        }

        if (!Helpers::isMobile($mobile)) {
            pft_log('sms/fail', "订单号{$this->order_num}的手机号{$mobile}格式有误无法发送短信");
            return true;
        }

        //游客短信拦截
        try {
            \Library\Hook::Listen('tourist_sms_interceptor', $infos);
        } catch (\Exception $e) {
            return true;
        }

        $updateOrderFlag = $is_resend == true ? 2 : 1;
        $this->p_type    = $p_type = strtoupper($this->p_type);
        $code            = empty($code) ? $infos['code'] : $code;
        // 使用第三方系统的闸机验证的，需要在凭证号前面加前缀，这里先从配置读取，如果后期此类用户很多，考虑做一个配置的表。
        $codePrefixConf = load_config('lid_code_prefix', 'product');
        if (isset($codePrefixConf[$infos['lid']])) {
            $code = $codePrefixConf[$infos['lid']] . $code;
        }

        // 判断这笔订单是否可以绑定人脸
        $landModel = new \Model\Product\Land('slave');

        //有自定义抬头--切换至创蓝发送
        $sms_tpl = $this->SmsTemplate();
        pft_log('sms/formatTpl','tpl: '.json_encode($sms_tpl));
        $channel = $this->smsSystem->getSmsChannelForDiySign($sms_tpl['sms_sign'],$sms_tpl['is_diy'],$this->p_type,$infos);
        pft_log('sms/formatTpl',json_encode(['channel'=>$channel,'mobile'=>$mobile,'order_info'=>$infos],JSON_UNESCAPED_UNICODE));

//        //测试代码
//        if (in_array($this->order_tel, $this->chuangLanMobile) && empty($channel)) {
//            $channel = 'chuangLan';
//        }

        $smsObj             = $this->smsSystem->getPlatformObjectV2($channel);
        $smsObj->order_num  = $this->order_num;
        $smsObj->smsTpl     = empty($sms_tpl) ? [] : $sms_tpl; //赋值模板信息
        $lidWithoutSeatConf = load_config('lid_without_seatinfo', 'product');

        /**
         * 演出订单短信是否不显示座位信息
         */
        $smsObj->withoutSeatInfo = in_array($infos['lid'], $lidWithoutSeatConf);
        $smsObj->p_type          = $sms_tpl['sms_sign'] == '鼓浪屿景区' ? 'GLY' : $this->p_type;
        // 获取门票是否设置不显示座位号
        $ticketExtInfo = [];
        $ticketBiz     = new \Business\JavaApi\CommodityCenter\Ticket();
        $getTicketInfo = $ticketBiz->queryTicketAttrsById($infos['tid']);
        if ($getTicketInfo['code'] == 200) {
            foreach ($getTicketInfo['data'] as $attr) {
                if ($attr['key'] == 'is_show_seat') {
                    $ticketExtInfo[$attr['ticket_id']] = $attr;
                }
            }
        }
        //$ticketModel   = new Ticket();
        //$ticketExtInfo = $ticketModel->getTicketExtConfig([$infos['tid']], 'ticket_id,key,val', ['is_show_seat']);
        if (!empty($ticketExtInfo[$infos['tid']]['val']) && $ticketExtInfo[$infos['tid']]['val'] == 2) {
            $smsObj->withoutSeatInfo = true;
        }

        if (in_array($infos['ordermode'], [24, 44])) {
            // 团队订单
            $need_qr    = 'TEAM_ORDER';
            // $smsContent = ['{1}' => $this->team_order_num . '，点击查看二维码 ' . "12301.cc/qr.php/" . $this->team_order_num  . ' '];
            $smsContent = ['{1}' => $this->team_order_num . '，点击查看二维码 ' . "12301.cc/teamqr.html#/" . $this->team_order_num  . ' '];
        } elseif ($sms_tpl['url_show'] == 'O') {
            $need_qr    = 'WITH_ORDERNUM';
            $smsContent = $smsObj->SmsContentForOrdernum(
                $this->order_num,
                self::smsVariableFormat($this->title . $infos['pname']),
                $infos['getaddr'],
                $infos['begintime'],
                $infos['endtime']
            );
        } else {
            if ($this->p_type == 'C') {
                if (!empty($infos['order_status'])) {
                    // 酒店订单
                    // 如果配置了 供应商人工审核确认订单
                    if (isset($infos['new_ext_info']['order_room_check']) && $infos['new_ext_info']['order_room_check']) {
                        // 如果是待确认状态  且  门票配置了  通知游客订单待确认
                        if ($infos['order_status'] == 4 && $infos['new_ext_info']['notice_to_tourist'] == 1) {
                            $need_qr           = 'HOTEL_CONFIRM';
                            $smsContent['{1}'] = $this->order_num;
                            $memo              = '短信通知游客等待订单确认';
                            $isChargeSms       = true;
                        } elseif ($infos['order_status'] == 3) {
                            // 如果状态是被取消
                            $need_qr           = 'HOTEL_REFUND';
                            $smsContent['{1}'] = $this->order_num;
                            $smsContent['{2}'] = $infos['extAttrs'][0]['fax'];
                        } else {
                            // 不发短信
                            return true;
                        }
                    }
                } else {
                    $need_qr = true;
                    if ($sms_tpl['url_show'] == 'N') {
                        $need_qr = false;
                    }
                    $smsContent = $smsObj->SmsContent(
                        self::smsVariableFormat($this->title . $infos['pname']),
                        $infos['getaddr'],
                        $infos['begintime'],
                        $infos['endtime'],
                        $need_qr,
                        $code,
                        1,
                        $manualQr
                    );
                    //酒店的根据不同的入住有效期
                    $hotelData  = $this->_hotelTicketTimeHandle($smsContent, $need_qr, $infos['tid']);
                    $smsContent = $hotelData['content'];
                    $need_qr    = $hotelData['need_qr'];
                }
            } elseif ($this->p_type == 'A' && isset($infos['external_code']) && !empty($infos['external_code'])) {
                //外部码走多条短信模式
                $isSplit       = true;
                $tmplateLength = 300;
                //外部码字节数判断拆分发送短信
                $need_qr    = 'CUSTOMIZE'; //走通用模板， 为了支持抬头
                $code       = $infos['external_code']['code'];
                $expireTime = $infos['external_code']['expire_time'];
                $remark     = $infos['external_code']['remark'];

                $smsContent = $smsObj->SmsContent(
                    self::smsVariableFormat($this->title . $infos['pname']),
                    $remark,
                    $infos['begintime'],
                    $expireTime,
                    $need_qr,
                    $code,
                    1,
                    $manualQr,
                    false,
                    true,
                    $infos['external_code']
                );
            } else {
                if ($infos['order_status'] == CommonOrderStatus::WAIT_APPOINTMENT_CODE) {   //待预约状态
                    $need_qr    = 'ORDER_NEED_APPOINTMENT';
                    $getAddr = in_array($this->sellerId, [3385, 38208548]) ? ($infos['getaddr'] ?? '') : '';
                    $smsContent = $this->_handleNeedAppointment($this->order_num, $infos['begintime'],
                        $infos['endtime'], self::smsVariableFormat($this->title . $infos['pname']), $getAddr);
                } else {
                    $need_qr = true;
                    if ($sms_tpl['url_show'] == 'N') {
                        $need_qr = false;
                    }
                    $smsContent = $smsObj->SmsContent(
                        self::smsVariableFormat($this->title . $infos['pname']),
                        $infos['getaddr'],
                        $infos['begintime'],
                        $infos['endtime'],
                        $need_qr,
                        $code,
                        1,
                        $manualQr
                    );
                }
            }
        }

        if ($this->p_type == 'J') {
            $productExtInfo = $infos['order_product_ext'] ? json_decode($infos['order_product_ext'], true) : [];
            $deliveryType   = isset($productExtInfo['deliveryType']) ? $productExtInfo['deliveryType'] : 0;
            $smsContent     = [];
            if ($deliveryType == 0) {  //快递
                $need_qr           = 'J';
                $smsContent['{1}'] = self::smsVariableFormat($infos['pname']);
                $smsContent['{2}'] = " 12301.cc/" . OrderNotify::url_sms($this->order_num) . ' ';
            } else {        //自提
                $need_qr           = 'J_CODE';
                $smsContent['{1}'] = self::smsVariableFormat($infos['pname']);
                $smsContent['{2}'] = $code;
                $smsContent['{3}'] = " 12301.cc/" . OrderNotify::url_sms($this->order_num) . ' ';
                $specialApi = new \Business\JavaApi\Ticket\SpecialtyTicket();
                $specialRes = $specialApi->querySpecialGoodsDeliveryInfo($infos['tid']);
                if ($specialRes['code'] == 200) {
                    $pickPointData = $specialRes['data']['pickPoint'];
                    //自提点信息 地址
                    $infos['receive_address'] = $pickPointData['address'];
                }
            }
        }

        // 特殊处理的套票子票所有有效期 --- 如果不发套票所有子票下的内容可以忽略这段
        if ($this->p_type == 'F') {
            $javaApi      = new \Business\CommodityCenter\LandF();
            $arrLandArr   = $javaApi->queryLandFByLandIdAndTicketIdAndProductId([$infos['tid']], [], [],
                'tid, sms_send_subticket_valid_time');
            $landFInfoArr = $arrLandArr[0];

            if ($landFInfoArr['sms_send_subticket_valid_time'] == 1) {
                // 套票要发送子票的所有有效期的情况
                $bookingBiz = new \Business\Order\Booking();
                $packBiz    = new \Business\Product\PackTicket();

                $sonTicketInfoArr = $packBiz->getTickets($infos['tid']);
                $sonTidArr        = array_column($sonTicketInfoArr, 'tid');
                $orderTimeArr     = $bookingBiz->orderTime($sonTidArr, $infos['playtime'], $infos['endtime']);
                $orderNotifyBiz   = new \Business\Order\OrderNotify();
                if ($orderTimeArr['code'] == 200) {
                    $packSonContent = '；其中';
                    foreach ($orderTimeArr['data'] as $timeVal) {
                        $lttitle             = $timeVal['ltitle'] . $timeVal['ttitle'];
                        $checkIsCountdownRes = $orderNotifyBiz->checkOrderValidityIsCountdown($timeVal['beginTime'],
                            $timeVal['endTime']);
                        $_bt                 = $checkIsCountdownRes['begin_time'];
                        $_et                 = $checkIsCountdownRes['end_time'];
                        if ($_bt == $_et) {
                            $time_note = "，消费日期:{$_bt}；";
                        } else {
                            $time_note = "，有效期:{$_bt}至{$_et}；";
                        }

                        $packSonContent .= $lttitle . $time_note;
                    }
                    $smsContent['{2}'] = trim($smsContent['{2}'] . $packSonContent, '；');
                }
            }
        }


        if($this->p_type == 'I') {
            $need_qr = 'ANNUAL_ORDER';
            //【%sign%】点击查看订单详情：{1} ，有效期:{2}。您已成功购买了{3}，虚拟卡号:{4}。{5}此为凭证,请妥善保管。
            //点击查看订单详情：{年卡订单链接} ，有效期:{有效期}。您已成功购买了{产品}{票}1张，虚拟卡号:{虚拟卡号}。{使用须知}此为凭证,请妥善保管。
            $smsContent['{1}'] = " 12301.cc/" . OrderNotify::url_sms($this->order_num) . ' ';
            //查询订单票属性快照, 每个订单都会有该字段, 为了保险起见还是加个默认值
            $ticketVersion = $infos['ext_content']['ticketVersion'] ?? 0;
            $snapshotTicketInfo = (new \Business\Product\Ticket())->getTicketSnapShotByCache($infos['tid'], $ticketVersion);
            //订单有效期类型，0：游玩日期后X天有效，1：下单日期后X天有效 2：设定时间段内有效， 3：游玩日期当天有效
            if($snapshotTicketInfo['uuJqTicketDTO']['delaytype'] == 2) {
                $smsContent['{2}'] = "{$infos['begintime']}至{$infos['endtime']}";
            } else {
                //目前年卡没有下单后和激活后的有效期, 统一都是激活后有效期
                $validPeriodDays = $snapshotTicketInfo['uuJqTicketDTO']['delaydays'] ?? 0;
                //$smsContent['{2}'] = "卡片激活后{$validPeriodDays}天有效";
                $smsContent['{2}'] = "请在{$validPeriodDays}天内激活";
            }
            $smsContent['{3}'] = self::smsVariableFormat($this->title . $infos['pname']);
            $annualModel = new \Model\Product\AnnualCard();
            $annualInfo = $annualModel->orderSuccess($this->order_num);
            $virtualNo = $annualInfo[0]['virtual_no'] ?? '';
            $cardNo = $annualInfo[0]['physics_no'] ?? '';
            $smsContent['{4}'] = $virtualNo;
            $smsContent['{5}'] = $infos['getaddr'] ?? '';
            $infos['annual_order'] = [
                'expire'     => $smsContent['{2}'],
                'virtual_no' => $virtualNo,
                'card_no'    => $cardNo,
            ];
        }


        //自定义短信息增加字段
        $diyData = [];
        if ($need_qr === 'ORDER_NEED_APPOINTMENT') {
            $isDiy   = $this->_isDiySmsTemp($smsObj, true);
        } else {
            $isDiy   = $this->_isDiySmsTemp($smsObj, $need_qr);
        }
        if ($isDiy) {
            $smsId = $smsObj->smsTpl['sms_id'];
            $diyData = $smsObj->smsContentForEnName($infos, $mobile, $manualQr, $code, $this->title,$smsId);
        } else {
            //非自定义模板、待预约、抬头自定义情况下转换成通用模板,走自定义通道以支持短信抬头自定义
            if ($need_qr === 'ORDER_NEED_APPOINTMENT' && $sms_tpl['sms_sign'] != '票付通' && !is_null($sms_tpl['sms_sign'])) {
                $diyData = $smsContent = $this->_handleReservationSms($smsContent, $smsObj);
            }
        }
        pft_log('/sms/data',"短信内容" . json_encode([$mobile, $smsContent, $updateOrderFlag, $smsObj, $need_qr, $sms_tpl['sms_sign'],
                $diyData, $memo, $isChargeSms,$is_resend]));

        //外部码判断短信内容长度是否需要拆分，这个目前只适用外部码使用，后续可以优化兼容下
        if ($isSplit === true) {
            //5用来临时存储外部码，不为空则需要在短信内展示外部码
            if (!empty($smsContent["{5}"])) {
                //获取码的长度
                $codeLength = utf8Length($smsContent["{5}"]);
                $codeArr    = explode(',', $smsContent["{5}"]);
                unset($smsContent["{5}"]);
            } else {
                //获取码的长度
                $codeLength = utf8Length($smsContent["{1}"]);
                $codeArr    = [];

            }
            //计算出码可发送的长度
            $reallyLength = $this->_codeLength - $tmplateLength;
            //假如字段超过之后需要拆分成两条短信发送
            $num = ceil($codeLength / $reallyLength);

            if ($num > 1) {
                //这边要先获取每次发送的码数，再进行分割
                // $splitCode = array_chunk($codeArr, $num);
                $splitCode     = [];
                $splitCodeItem = [];
                foreach ($codeArr as $codeTmp) {
                    //累加长度
                    $splitCodeItemLength = utf8Length(implode(',', $splitCodeItem));
                    //加1为逗号字数。超过可发送字数，就计数到下一条
                    if (($splitCodeItemLength + utf8Length($codeTmp) + 1) > $reallyLength) {
                        $splitCode[]   = $splitCodeItem;
                        $splitCodeItem = [];
                    }
                    $splitCodeItem[] = $codeTmp;
                }
                if (!empty($splitCodeItem)) {
                    $splitCode[] = $splitCodeItem;
                }
            } else {
                $splitCode[0] = $codeArr;
            }

            //发送失败的数组
            $errorMsm = [];
            $firstSmsContent = $smsContent['{1}'] ?? '';//获取短信前半部分
            for ($i = 0; $i < count($splitCode); $i++) {//上限需要取实际可以发送的数量
                $codeArrData       = !empty($splitCode[$i]) ? $splitCode[$i] : []; //防止不展示外部码的时候数组下标不存在
                $smsContent['{1}'] = $firstSmsContent . implode('，', $codeArrData); //采用中文逗号，避免链接被合并

                $diyData = $smsContentDetail = $this->_handleExternalCodeSms($smsContent, $smsObj); //外部码短信模板转换成通用模板

                $res = $this->SendSMSV2($mobile, $smsContentDetail, $updateOrderFlag, $smsObj, $need_qr, $sms_tpl['sms_sign'], $diyData,'',$isChargeSms,$is_resend);
                if ($res === false) {
                    $errorMsm[] = [
                        'mobile'          => $mobile,
                        'smsContent'      => $smsContentDetail,
                        'updateOrderFlag' => $updateOrderFlag,
                        'smsObj'          => $smsObj,
                        'need_qr'         => $need_qr,
                        'sms_sign'        => $sms_tpl['sms_sign'],
                        'diyData'         => $diyData,
                    ];
                }
            }

            //有存在失败的 再重试一次
            if (!empty($errorMsm)) {
                foreach ($errorMsm as $item) {
                    $res = $this->SendSMSV2($item['mobile'], $item['smsContent'], $item['updateOrderFlag'],
                        $item['smsObj'], $item['need_qr'], $item['sms_sign'], $item['diyData'],'',$isChargeSms,$is_resend);
                    if ($res === false) {
                        pft_log('debug/external_sms',
                            json_encode(['ac' => '发码短信重试失败', 'content' => $item, 'res' => $res],
                                JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            //直接返回true
            $res = true;
        } else {
            $res = $this->SendSMSV2($mobile, $smsContent, $updateOrderFlag, $smsObj, $need_qr, $sms_tpl['sms_sign'],
                $diyData, $memo, $isChargeSms,$is_resend);
        }
        if ($res === true && $infos['ordermode'] == 24) {
            // 如果短信发送成功并且是团单 则记录，避免其他子订单也发送团单, 记录5分钟
            $cacheRedis->setex($this->team_order_num, 300, '1');
        }
        pft_log('/sms/data',
            'code:' . $code .'res'.json_encode($res) . json_encode($smsContent, JSON_UNESCAPED_UNICODE) . ' info' . json_encode($infos,
                JSON_UNESCAPED_UNICODE));

        return $res;
    }

    public function commonSendSmsForCustomSign($content,$updateOrder,$memo){
        //有自定义抬头--切换至创蓝发送
        $sms_tpl = $this->SmsTemplate();
        pft_log('sms/formatTpl','tpl: '.json_encode($sms_tpl));
        $channel = $this->smsSystem->getSmsChannelForDiySign($sms_tpl['sms_sign'],$sms_tpl['is_diy']);
        pft_log('sms/formatTpl','channel: '.$channel);
        $smsObj             = $this->smsSystem->getPlatformObjectV2($channel);
        $smsObj->order_num  = $this->order_num;
        $smsObj->smsTpl     = empty($sms_tpl) ? [] : $sms_tpl; //赋值模板信息
        $smsObj->p_type          = $this->p_type;
        //发送短信的手机号
        $mobile = $this->order_tel ?? '';

        if ($mobile == '') {
            pft_log('sms/fail', "订单号{$this->order_num}:手机号为空无法发送短信");
            return true;
        }
        if (!Helpers::isMobile($mobile)) {
            pft_log('sms/fail', "订单号{$this->order_num}的手机号{$mobile}格式有误无法发送短信");
            return true;
        }
        $res = $this->SendSMSV2($mobile, $content, $updateOrder, $smsObj, PFTSMSInterface::ISDIY, $sms_tpl['sms_sign'],
            '', $memo, true,false);
        pft_log('/sms/data', json_encode([$res,$mobile,$content,$memo,$sms_tpl], JSON_UNESCAPED_UNICODE));
        return $res;
    }
    /**
     * 外部码短信模板 转换成 通用模板 以支持短信抬头
     * <AUTHOR>
     * @date 2021/7/27
     *
     * @param array $smsContent 短信内容
     * @param PFTSMSInterface $smsObj
     *
     * @return array
     */
    private function _handleExternalCodeSms($smsContent, $smsObj)
    {
        $externalCode = '{1}。您已成功购买了{2}。订单号：{3}，{4}。'; //短信模板
        $send_data    = array_values($smsContent);
        $sms          = str_replace(array_keys($smsContent), $send_data, $externalCode); //替换模板信息

        //赋值下模板
        $smsObj->smsTpl['cformat'] = '{1}';

        return ['{1}' => $sms];
    }

    /**
     * 待预约短信模板 转换成 通用模板 以支持短信抬头
     * <AUTHOR>
     * @date 2021/9/2
     *
     * @param array $smsContent 短信内容
     * @param PFTSMSInterface $smsObj
     *
     * @return array
     */
    private function _handleReservationSms($smsContent, $smsObj)
    {
        $externalCode = '点击查看订单详情：{1}，{2}。您已成功购买{3}。前往游玩时需提前预约，可点击订单详情进行预约，此为凭证，请妥善保管。'; //短信模板
        $send_data    = array_values($smsContent);
        $sms          = str_replace(array_keys($smsContent), $send_data, $externalCode); //替换模板信息

        //赋值下模板
        $smsObj->smsTpl['cformat'] = '{1}';

        return ['{1}' => $sms];
    }

    /**
     * 检测团单是否需要发送短信
     *
     * @param $cacheRedis
     *
     * @return bool
     */
    private function checkTeamOrderNotice($cacheRedis)
    {
        $teamOrderSearchModel = new TeamOrderSearch();
        $mainOrderInfo        = $teamOrderSearchModel->getMainOrderInfoBySonOrder($this->order_num);
        if (empty($mainOrderInfo)) {
            return false;
        }

        $teamOrderNum  = $mainOrderInfo['main_ordernum'];
        $teamOrderSend = $cacheRedis->get($teamOrderNum);

        if ($teamOrderSend) {
            // 如果存在缓存记录则不发送短信
            return false;
        }
        $this->team_order_num = $teamOrderNum;

        return true;
    }

    /**
     * 向供应商发送通知信息
     *
     * @param  array  $infos  参数
     *                          pid_list
     *                          预定的产品id
     *                          ordername
     *                          下单人姓名
     *                          pname
     *                          购买的产品
     *                          note
     *                          备注信息
     *                          play_time
     *                          消费日期
     * @param  bool  $is_resend  是否重发短信
     *
     * @return bool
     */
    public function SellerNotify(Array $infos, $is_resend = false)
    {
        pft_log('wx/notify', "微信通知商家的订单信息为" . json_encode($infos, JSON_UNESCAPED_UNICODE));
        //查看哪些产品需要发送短信给供应商
        $sms_notify_num = null;
        $confirmWx      = 0;//接收微信通知标记

        //短信说明
        $memo           = '';
        //是否给订单短信发送次数累加
        $isChargeSms    = false;
        $wxSuccessDistributor = 0;//供销链上的中间级分销商将收到订单微信通知标记
        foreach ($infos['extAttrs'] as $row) {
            if ($row['confirm_wx']) {
                $confirmWx = 1;
            }
            if (($row['confirm_sms'] == 1 || $row['confirm_sms'] == 3 || ($infos['new_ext_info']['notice_to_supplier'] == 1 && $infos['new_ext_info']['order_room_check'] == 1
                                                                          && $infos['order_status'] == 4)) && $row['fax']
            ) {
                $sms_notify_num = $row['fax'];
                break;
            }
        }

        //供销链上的中间级分销商将收到订单微信通知 reserve_success_distributor景区/线路/酒店/套票/餐饮/演出A=景点，B=线路，C=酒店，F=套票，G=餐饮，H=演出 J=特产
        if (($infos['new_ext_info']['reserve_success_distributor'] || $infos['new_ext_info']['buy_success_distributor']) && in_array($this->p_type,['J','A','B','C','F','G','H'])) {
            $wxSuccessDistributor = 1;
        }

        switch ($this->p_type) {
            case 'A':
                $_time_name = "消费日期:";
                break;
            case 'B':
                $_time_name = "游玩时间:";
                break;
            case 'C':
                $_time_name = "入住日期:";
                break;
            default:
                $_time_name = '';
                break;
        }

        //微信通知
        $this->sendWechatNoticeToSeller($confirmWx, $infos, $wxSuccessDistributor);

        //短信通知
        if (!is_null($sms_notify_num) && Helpers::isMobile($sms_notify_num)) {

            if (in_array($this->order_tel, $this->chuangLanMobile)) {
                $channel = 'chuangLan';
            }

            $smsObj = $this->smsSystem->getPlatformObjectV2($channel);

            if ($this->p_type == 'C') {
                if ($this->_checkHotelOrderIsSend()) {
                    return true;
                }
                if ($infos['new_ext_info']['order_room_check'] == 1) {
                    if ($infos['order_status'] == 4) {
                        if ($infos['new_ext_info']['notice_to_supplier'] == 1) {
                            $smsObj->p_type = 'HOTEL_CALL_CONFIRM';
                            $isChargeSms    = true;
                            $memo           = '短息通知供应商确认酒店订单';
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                } else {
                    $smsObj->p_type = 'C_BOSS';
                }
            } elseif ($this->p_type == 'J') {
                $productExtInfo = $infos['order_product_ext'] ? json_decode($infos['order_product_ext'], true) : [];
                $deliveryType   = isset($productExtInfo['deliveryType']) ? $productExtInfo['deliveryType'] : 0;
                if ($deliveryType == 0) {   //快递
                    $smsObj->p_type = 'J_BOSS_DELIVERY';
                } else {  //自提
                    $smsObj->p_type = 'J_BOSS_SELF';
                }
            } else {
                $smsObj->p_type = 'DEFAULT_BOSS';
            }

            $smsContent = $smsObj->SmsContentBoss($infos, $this->title, $this->order_num, $this->order_tel,
                $_time_name);

            return $this->SendSMSV2($sms_notify_num, $smsContent, 3, $smsObj,null, '', [], $memo, $isChargeSms,$is_resend);
        }

        return true;
    }

    /**
     * 发送微信通知给供应商
     * @param $confirmWx
     * @param $infos
     * @return bool
     */
    private function sendWechatNoticeToSellerForMessageCenter($confirmWx, $infos,$isNoticeProvider =true ,$isNoticeProviderStuff = true, $noticeDistributor = false)
    {
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Seller', "WxNotifyProvider：" . json_encode($infos, JSON_UNESCAPED_UNICODE));
            pft_log('wxNotice/jobConsumer/Seller', "noticeDistributor：" . $noticeDistributor);
            pft_log('wxNotice/jobConsumer/Seller', "noticeDistributor：" . $confirmWx);
        }
        if (!$confirmWx && !$noticeDistributor) {
            return false;
        }
        //通知供应商
        if ($confirmWx && $isNoticeProvider) {
            $wxConfigLib = new WxConfig();
            $wxOpenId = $wxConfigLib->wxNotifyChkBySupplyCahce($this->sellerId,PFT_WECHAT_APPID, $infos['lid']);
            if($this->p_type == 'Q'){
                pft_log('wxNotice/jobConsumer/WxNotify', json_encode([__METHOD__, 'sellerId'=>$this->sellerId, 'lid'=>$infos['lid'], '$wxOpenId'=>$wxOpenId]));
            }
            if ($wxOpenId) {
                //如果绑定了多个微信号
                foreach ($wxOpenId as $openid) {
                    $this->WxNotifyProvider(
                        $openid,
                        $this->order_num,
                        $this->title,
                        $infos['pname'],
                        $this->pid
                    );
                }
            }
        }

        //中间商
        if ($noticeDistributor) {
            $middlemanArr = [];
            //根据订单号获取中间商
            if ('Q' == $this->p_type) {
                $orderMiddlemanList = (new OrderSearch())->getOrderMiddlemanListFromNewOrderCenter($this->order_num);
            } else {
                $orderMiddlemanList = (new OrderSearch())->getOrderMiddlemanList($this->order_num);
            }
            if($this->p_type == 'Q'){
                pft_log('wxNotice/jobConsumer/Seller', "orderMiddlemanList：" . json_encode($orderMiddlemanList));
            }
            foreach ($orderMiddlemanList as $item){
                $middlemanArr = array_merge($middlemanArr,$item);
            }
            if ($middlemanArr) {
                $this->_wxNotifyMiddleman($infos['pname'], $middlemanArr);
            }
        }

        //供应商员工
        if($isNoticeProviderStuff){
            $this->sendWechatNoticeToStaff($infos);
        }
        return  true;
    }

    /**
     * 发送微信通知给供应商
     * <AUTHOR> Chen
     * @date 2019-07-02
     *
     * @param $confirmWx
     * @param $infos
     *
     * @return bool
     */
    private function sendWechatNoticeToSeller($confirmWx, $infos, $noticeDistributor = 0)
    {
        if (!$confirmWx && !$noticeDistributor) {
            return false;
        }

        //$lid = $infos['lid'] ?? null;

        //通知供应商
        if ($confirmWx) {
            $wxConfigLib = new WxConfig();
            if ($wxOpenId = $wxConfigLib->wxNotifyChkBySupplyCahce($this->sellerId,PFT_WECHAT_APPID, $infos['lid'])) {
                //如果绑定了多个微信号
                if (is_array($wxOpenId)) {
                    foreach ($wxOpenId as $openid) {
                        $this->WxNotifyProvider(
                            $openid,
                            $this->order_num,
                            $this->title,
                            $infos['pname'],
                            $this->pid
                        );
                    }
                } else {
                    $this->WxNotifyProvider(
                        $wxOpenId,
                        $this->order_num,
                        $this->title,
                        $infos['pname'],
                        $this->pid
                    );
                }
            }
        }

        //中间商
        if ($noticeDistributor) {
            $middlemanArr = [];
            //根据订单号获取中间商
            $orderMiddlemanList = (new OrderSearch())->getOrderMiddlemanList($this->order_num);
            foreach ($orderMiddlemanList as $item){
                $middlemanArr = array_merge($middlemanArr,$item);
            }

            //$lid = $infos['lid'] ?? null;

            if ($middlemanArr) {
                $this->_wxNotifyMiddleman($infos['pname'], $middlemanArr);
            }

        }
        $this->sendWechatNoticeToStaff($infos);
    }

    /**
     * pft公众号发送微信通知订单给中间级分销商
     * <AUTHOR>
     * @date 2020-12-22
     *
     * @param string $pname 产品名称
     * @param array $userId 分销商id
     *
     * @return bool
     */
    private function _wxNotifyMiddleman(string $pname,array $userId){

        if (!$userId || !$pname) {
            return false;
        }

        foreach ($userId as $user){
            $wxOpenId = $this->WxNotifyChk((int) $user);
            if($this->p_type == 'Q'){
                pft_log('wxNotice/jobConsumer/WxNotify', json_encode([__METHOD__, '$user'=>$user, '$wxOpenId'=>$wxOpenId]));
            }
            if ($wxOpenId) {
                //如果绑定了多个微信号
                if (is_array($wxOpenId)) {
                    foreach ($wxOpenId as $openid) {
                        $this->WxNotifyProvider(
                            $openid,
                            $this->order_num,
                            $this->title,
                            $pname,
                            $this->pid,
                            true
                        );
                    }
                } else {
                    $this->WxNotifyProvider(
                        $wxOpenId,
                        $this->order_num,
                        $this->title,
                        $pname,
                        $this->pid,
                        true
                    );
                }
            }
        }
        return true;
    }

    /**
     * @param  array  $infos
     *
     * @return bool
     */
    private function sendWechatNoticeToStaff(array $infos)
    {
        //获取模型
        $MemberRelationship = new MemberRelationship();
        $MemberModel        = new Member('slave');
        //获取供应商底下的员工id数组
        $sidArr = $MemberRelationship->getStaffList($this->sellerId);
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Seller', "sidArr：" . json_encode($sidArr));
        }
        if (!$sidArr) {
            return false;
        }
        $member_auth = $MemberModel->getMemberInfoByMulti($sidArr, 'id', 'id,member_auth');
        $moduleIdArr = $this->judgeMemberAuth($member_auth, self::MEMBER_AUTH);
        if ($moduleIdArr) {
            //获取供应商底下的员工微信id
            $staffOpenId = $this->WxNotifyStaffChk($moduleIdArr);
            if($this->p_type == 'Q'){
                pft_log('wxNotice/jobConsumer/WxNotify', json_encode([__METHOD__, '$sidArr'=>$sidArr, '$staffOpenId'=>$staffOpenId]));
            }
            //微信通知供应商的员工
            foreach ($staffOpenId as $opid) {
                $this->WxNotifyProvider(
                    $opid,
                    $this->order_num,
                    $this->title,
                    $infos['pname'],
                    $this->pid
                );
            }
        }

        return true;
    }

    /**
     * 处理员工微信推送权限
     *
     * @param  array  $member_auth
     * @param  string  $auth
     *
     * @return array
     */

    public function judgeMemberAuth($member_auth, $auth)
    {
        if (empty($member_auth)) {
            return [];
        }
        $sid = [];
        foreach ($member_auth as $key => $value) {
            $authArr = explode(',', $value['member_auth']);
            if (in_array($auth, $authArr)) {
                $sid[] = $value['id'];
            }
        }

        return $sid;
    }

    /**
     * 新消息中心切流代码
     * @param $mobile
     * @param $content
     * @param $update_order
     * @param PFTSMSInterface|null $smsObj
     * @param $need_qr
     * @param $sms_sign
     * @param $diyData
     * @param $memo
     * @return array [$useSign]
     * @throws \Exception
     */
    public function SendSMSV2Approval($mobile, $content, $update_order = 0, PFTSMSInterface $smsObj = null,
        $need_qr = null, $sms_sign = '', $diyData = [],$memo = '',$isChargeSms = false,$is_resend = 0)
    {
        $messageServiceApi = Container::pull(MessageService::class);
        if (!empty($diyData)) {
            [$useCustomSign, $templateCode] = $messageServiceApi->getTemplateCodeBySign(PFTSMSInterface::ISDIY, $sms_sign);
        } else {
            [$useCustomSign, $templateCode] = $messageServiceApi->getTemplateCodeBySign($smsObj->getTemplateType($need_qr), $sms_sign);
        }
        $applyDid = $this->sellerId ?: $this->aid;
        if (!$templateCode || !$messageServiceApi->dispatch2MessageServicePolicy(MessageService::V1, $applyDid, $templateCode, 'OrderNotify')) {
            return [false, false];
        }
        if (!empty($diyData)) {
            [$content] = $smsObj->replaceDiySms(['content' => $smsObj->smsTpl['cformat']], $diyData, false);
        }
        $sendParams = array_values($content);
        $serialNo = MessageService::createSerialNo();
        if ($update_order == 2) {
            //重发短信到手机号
            $memo = '重发到手机：' . $mobile;
        }
        $res = $messageServiceApi->messageSend($mobile, $templateCode, $sendParams, $this->sellerId,
            $this->sellerId ? 1 : 0, $serialNo, $this->order_num, $memo, $sms_sign);
        pft_log('sms/debugSendSMSV2', json_encode(["SendSMSV2Approval", $mobile, $templateCode, $sendParams, $this->sellerId
            , $serialNo, $this->order_num, $memo, $res]));
        $sendResData = $res['data'] ?? [];
//        $res = ['code' => $sendRes['code'], 'content' => $sendResData['content'] ?? '',
//            'msgCnt' => $sendResData['msgCnt'] ?? 0, 'msg' => $sendRes['msg'], 'sid' => mt_rand(1, 10)];


        if ($res['code'] != 200) {
            //发送告警通知
            Helpers::sendDingTalkGroupRobotMessage(
                "各位小主不好了！\n新消息中心短信发送失败\n流水号：{$serialNo}\n订单号:{$this->order_num}\n错误信息:{$res['msg']}",
                "短信发送异常",
                "订单号:{$this->order_num}," . gethostname(),
                DingTalkRobots::SMS_API_BASEGU,
                'markdown'
            );
            return [$serialNo, false];
        } else {
            $wordscnt = utf8Length($sendResData['content']);
            $msgcnt   = $sendResData['msgCnt'] ?? ceil($wordscnt / 67);
            $logModel = new SmsJournal();
            $logModel->AddJournal(
                $this->aid,
                $this->order_num,
                $mobile,
                $wordscnt,
                $msgcnt,
                ($update_order == 3 ? 'Y' : 'N'),
                $sendResData['content'],
                $sendResData['channelId'] ?? $smsObj->platformid,
                mt_rand(1, 10),
                $res['code'],
                $res['msg'],
                $is_resend,
                2
            );
            if ($this->sellerId && $isChargeSms && $update_order != 1) {
                //短信累加
                (new OrderCommon())->updateSmsTimes($this->order_num);
            }
        }
        //第一次发送订单短信
        if ($update_order == 1) {
            $queryParams = [$this->order_num, $this->aid];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('UuSsOrderModify',
                'updateOrderFirstSendSmsStatusByOrderNum', $queryParams);
            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                pft_log('updatePayMode/error', json_encode([
                    'ac'       => 'updateOrderFirstSendSmsStatusByOrderNum',
                    'ordernum' => $this->order_num,
                    'queryRes' => $queryRes,
                ]));
            }
        } elseif ($update_order == 2) { //重发订单短信
            $queryParams = [$this->order_num, $this->aid];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('UuSsOrderModify',
                'updateOrderSendSmsStatusByOrderNum', $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                pft_log('updatePayMode/error', json_encode([
                    'ac'       => 'updateOrderSendSmsStatusByOrderNum',
                    'ordernum' => $this->order_num,
                    'queryRes' => $queryRes,
                ]));
            }
        }
        return [$serialNo, true];
    }

    /**
     * 发送短信统一接口V2
     *
     * @param  string  $mobile  手机号
     * @param  string|array  $content  短信内容
     * @param  int  $update_order  是否更新发送次数
     *                                       1:不更新 2:更新
     *                                       3:供应商短信
     * @param  PFTSMSInterface  $smsObj
     * @param  bool  $need_qr  是否需要二维码链接
     * @param  string  $sms_sign  短信签名
     * @param  string  $memo  短信扣费说明
     * @param  bool  $isChargeSms  是否给订单短信发送次数累加
     * @param  array  $diyData  自定义短信数据
     *
     * @return bool
     */
    public function SendSMSV2($mobile, $content, $update_order = 0, PFTSMSInterface $smsObj = null, $need_qr = null, $sms_sign = '', $diyData = [],$memo = '',$isChargeSms = false,$is_resend = 0)
    {
        pft_log('sms/debugSendSMSV2', json_encode([$mobile, $content, $smsObj, $need_qr, $sms_sign, $diyData], JSON_UNESCAPED_UNICODE));
//        //处理不需要二维码的短信链接，供应商短信不需要此参数
//        //$template_type = $smsObj->p_type . ($need_qr===false ? '_NO_QR' : '');
        [$approval, $res] = $this->SendSMSV2Approval($mobile, $content, $update_order, $smsObj, $need_qr, $sms_sign, $diyData, $memo, $isChargeSms, $is_resend);
        if ($approval) {
            return $res;
        }
        if (!empty($diyData)) {
            $res = $smsObj->doSendDiySMS($mobile, $diyData, $sms_sign);
            pft_log('sms/debugSendSMSV2','response:'.json_encode($res));
        } else {
            $res = $smsObj->doSendSMS($mobile, $content, $need_qr, $sms_sign);
            pft_log('sms/debugSendSMSV2','response2:'.json_encode($res));
        }
        $wordscnt = utf8Length($res['content']);
        $msgcnt   = isset($res['msgCnt']) ? $res['msgCnt'] : ceil($wordscnt / 67);
        $logModel = new SmsJournal();
        $logModel->AddJournal(
            $this->aid,
            $this->order_num,
            $mobile,
            $wordscnt,
            $msgcnt,
            ($update_order == 3 ? 'Y' : 'N'),
            $res['content'],
            $smsObj->platformid,
            $res['sid'],
            $res['code'],
            $res['msg'],
            $is_resend
        );
        if ($res['code'] != 200) {
            $error_times = SysKeyMonitor::increaseSmsErrorTimes($smsObj->platformid);
            //发送告警通知
            Helpers::sendDingTalkGroupRobotMessage(
                "各位小主不好了！\n短信平台[{$smsObj->platform}]发送错短信失败\n订单号:{$this->order_num}\n错误信息:{$res['msg']}",
                "短信发送异常",
                "订单号:{$this->order_num}," . gethostname(),
                DingTalkRobots::SMS_API_BASEGU,
                'markdown'
            );
            if ($error_times > 5000) {
                $slave_platform = $this->smsSystem->getPlatformId(1);
                $this->smsSystem->setSmsPlatform($slave_platform);
                $platform_list = load_config('platform_list', 'sms_ac');
                //发送告警通知
                Helpers::sendDingTalkGroupRobotMessage(
                    "各位小主不好了！\n短信平台[{$smsObj->platform}]发送错误次数过多\n订单号:{$this->order_num}\n系统已自动切换为:" . $platform_list[$slave_platform],
                    "短信发送异常",
                    "订单号:{$this->order_num}," . gethostname(),
                    DingTalkRobots::SMS_API_BASEGU
                );
            }

            return false;
        }


        //第一次发送订单短信
        if ($update_order == 1) {
            $queryParams = [$this->order_num, $this->aid];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('UuSsOrderModify',
                'updateOrderFirstSendSmsStatusByOrderNum', $queryParams);
            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                pft_log('updatePayMode/error', json_encode([
                    'ac'       => 'updateOrderFirstSendSmsStatusByOrderNum',
                    'ordernum' => $this->order_num,
                    'queryRes' => $queryRes,
                ]));
            }

            //self::getMasterModel()->table(MainTableConst::TABLE_SS_ORDER)
            //    ->where(['ordernum' => $this->order_num, 'remsg' => 0])
            //    ->data(['remsg' => 1])
            //    ->limit(1)
            //    ->save();
        } //重发订单短信
        elseif ($update_order == 2) {
            $queryParams = [$this->order_num, $this->aid];
            $queryRes    = \Business\JavaApi\Order\Query\Container::query('UuSsOrderModify',
                'updateOrderSendSmsStatusByOrderNum', $queryParams);

            if ($queryRes['code'] != 200 || empty($queryRes['data'])) {
                pft_log('updatePayMode/error', json_encode([
                    'ac'       => 'updateOrderSendSmsStatusByOrderNum',
                    'ordernum' => $this->order_num,
                    'queryRes' => $queryRes,
                ]));
            }

            //$this->UpdateSmsLogTimes($this->order_num);
            //重发短信到手机号
            $memo = '重发到手机：' . $mobile;
        }
        $expenseBiz = new PlatformExpense();
        $chargeSms = $expenseBiz->chargeSms($this->sellerId, $msgcnt, $this->order_num, $fromMemberId = 1, $only_cut = false, $memo);
        if ($chargeSms && $isChargeSms && $update_order != 1) {
            //短信累加
            (new OrderCommon())->updateSmsTimes($this->order_num);
        }
        return true;
    }

    /**
     * 获取多个会员微信id
     *
     * <AUTHOR>
     * @time   2018-06-27
     */
    public function WxNotifyStaffChk($staffArr, $appid = false)
    {
        $wx    = new WxMember();
        $appid = $appid == false ? PFT_WECHAT_APPID : $appid;
        $data  = $wx->getArrWxInfo($staffArr, $appid);

        $openid = [];
        foreach ($data as $row) {
            if ($row['verifycode'] == 1) {
                $openid[] = $row['fromusername'];
            }
        }

        return $openid;
    }

    /**
     * 检查是否可以通过微信发送通知
     *
     * @param  int  $fid  会员id
     * @param  bool|string  $appid  微信appid
     * @param  bool|string  $isSeller  通知给供应商
     *
     * @return bool|string|array false/微信openid
     */
    public function WxNotifyChk($fid, $appid = false)
    {
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/WxNotifyChk','$fid:'.$fid.'_$appid:'.$appid);
        }
        $appid    = $appid == false ? PFT_WECHAT_APPID : $appid;
        $memberWx = new MemberWx();
        $openid   = $memberWx->handleWxnotifyListCache($appid, $fid, '', 'get');
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/WxNotifyChk','$openid1:'.$openid);
        }
        if ($openid) {
            return $openid;
        }
        $wx     = new WxMember();
        $data   = $wx->getWxInfo($fid, $appid, false, '', true);
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/WxNotifyChk','$data:'.$data);
        }
        $tmp    = array();
        $openid = array();

        foreach ($data as $row) {
            //开启订单通知 为null为默认开启，orderNotice = 1
            $orderNotice = isset($row['notice_info']) ? json_decode($row['notice_info'], true) : null;
            if ($row['verifycode'] == 1 && ($orderNotice['orderNotice'] == 1 || $orderNotice === null)) {
                $openid[] = $row['fromusername'];
            }
            $tmp[] = $row['fromusername'];
        }
        $openid = array_unique($openid);
        $cnt    = count($openid);
        pft_log('wx/notify', 'Chk openid:' . implode(',', $openid) . ' appid:' . $appid . ' mid:' . $fid);
        /*if ($cnt == 0 && count($tmp)) {
            //如果都没有设置的话，那么随机选择一个
            $sft = array_shift($tmp);
            $memberWx->handleWxnotifyListCache($appid, $fid, $sft, 'set');

            return $sft;
        } else*/if (count($openid)) {
            if($this->p_type =='Q'){
                pft_log('wxNotice/jobConsumer/WxNotifyChk','$openid2:'.$openid);
            }
            $memberWx->handleWxnotifyListCache($appid, $fid, implode($openid, ';'), 'set');

            return $openid;
        }
        $memberWx->handleWxnotifyListCache($appid, $fid, 'NULL', 'set');
        if($this->p_type =='Q'){
            pft_log('wxNotice/jobConsumer/WxNotifyChk','$fid:'.$fid.'$appid'.$appid);
        }
        return false;
    }

    /**
     * @param  string  $wx_open_id  openid
     * @param  string  $ordernum  订单号
     * @param  string  $p_name  产品名称
     * @param  string  $remark  文字说明
     * @param  string  $pid  产品编号
     *
     * @return string
     */
    public function WxNotifyProvider($wx_open_id, $ordernum, $p_name, $remark, $pid = '',$noticeDistributor = false)
    {
        //        {{first.DATA}}
        //        订单号：{{OrderId.DATA}}
        //        产品编号：{{ProductId.DATA}}
        //        产品名称：{{ProductName.DATA}}
        //        {{remark.DATA}}
        //        您好！您有新订单：
        //2015年5月30日 22:02:13,更新：通知供应商只能通过票付通公众号来通知。
        $data = [
            'first'       => ['value' => '您好！您有新订单：', 'color' => '#173177'],
            'OrderId'     => ['value' => $ordernum, 'color' => '#173177'],
            'ProductId'   => ['value' => $pid, 'color' => '#ff9900'],
            'ProductName' => ['value' => $p_name, 'color' => '#173177'],
            'remark'      => ['value' => $remark, 'color' => ''],
        ];

        $loop = 0;
        //中间级分销商不可打开查看详情
        if ($noticeDistributor) {
            $url = '';
        }else{
            $url = $this->_getDetailUrl($ordernum,$this->p_type);
        }

        do {
            $loop  += 1;
            if($this->p_type == 'Q'){
                pft_log('wxNotice/jobConsumer/res',json_encode([
                    'openid' => $wx_open_id,
                    'data'   => $data,
                    'tplid'  => 'HOTEL_ORDER_MSG',
                    'color'  => '#FF0000',
                    'url'    => $url,
                ]));
            }
            $jobId = Queue::push(
                'notify',
                'WxNotify_Job',
                [
                    'openid' => $wx_open_id,
                    'data'   => $data,
                    'tplid'  => 'HOTEL_ORDER_MSG',
                    'color'  => '#FF0000',
                    'url'    => $url,
                ]
            );
            if ($jobId) {
                break;
            }
        } while ($loop < 3);

        return $jobId;
    }

    /**
     * 微信通知购买者
     *
     * @param  string  $wxOpenId  微信openid
     * @param  string  $time
     * @param  string  $name
     * @param  string  $itemName
     * @param  string  $itemData
     * @param  string  $ordernum
     * @param  int  $totalPay
     * @param  int  $landId  景区id
     * @param  bool  $sendToThird  通知给游客的新订单消息是否通过商家的公众号发送  false:否  true:是
     * @param  string  $appId  供应商appid
     * @param  string  $tplId  供应商模板消息id
     *
     * @return string
     */
    public function WxNotifyCustomer(
        $wxOpenId,
        $time,
        $name,
        $itemName,
        $itemData,
        $ordernum,
        $totalPay = 0,
        $landId = 0,
        $sendToThird = false,
        $appId = '',
        $tplId = ''
    )
    {
        if($this->p_type == 'Q'){
            pft_log('wxNotice/jobConsumer/Queue_push',"p_type：".$this->p_type);
        }
        switch ($this->p_type) {
            case 'A':
                $type = '门票订单';
                break;
            case 'B':
                $type = '线路订单';
                break;
            case 'C':
                $type = '酒店订单';
                break;
            case 'F':
                $type = '套票订单';
                break;
            case 'G':
                $type = '餐饮订单';
                break;
            case 'J':
                $type = '特产订单';
                break;
            case 'Q':
                $type = '旅游券订单';
                break;
            default:
                $type = '门票订单';
                break;
        }
        $remark = ($totalPay > 0 ? "消费金额：$totalPay 元\n" : '') . "订单号：{$ordernum}";
        $url    = $this->_getDetailUrl($ordernum,$this->p_type);

        $adverComment = '';
        if ($landId != 0) {
            $advertising = $this->getAdvertising($landId);
            if (!empty($advertising)) {
                if ($advertising['position'] == 1) {
                    $remark .= "\n\n\n" . $advertising['comment'];
                } else {
                    $adverComment = $advertising['comment'] . "\n\n";
                }
                $url = $advertising['url'];
            }
        }

        $data       = [
            'first'         => ['value' => $adverComment . '订单提交成功', 'color' => '#173177'],
            'tradeDateTime' => ['value' => $time, 'color' => '#173177'],
            'orderType'     => ['value' => $type, 'color' => '#ff9900'],
            'customerInfo'  => ['value' => $name, 'color' => '#173177'],
            'orderItemName' => ['value' => $itemName, 'color' => '#173177'],
            'orderItemData' => ['value' => $itemData, 'color' => '#173177'],
            'remark'        => ['value' => $remark, 'color' => ''],
        ];
        $openidList = [];
        if (is_array($wxOpenId)) {
            $openidList = $wxOpenId;
        } else {
            $openidList[] = $wxOpenId;
        }
        $logData = [];
        foreach ($openidList as $openid) {
            if ($sendToThird === false) {
                $params = [
                    'data'   => $data,
                    'openid' => $openid,
                    'tplid'  => 'NEW_ORDER',
                    'url'    => $url,
                    'color'  => '#FF0000',
                ];
            } else {
                //商家公众号有托管并且有配置自己的模板消息
                $params = [
                    'data'   => $data,
                    'openid' => $openid,
                    'tplid'  => $tplId,
                    'url'    => $url,
                    'color'  => '#FF0000',
                    'appid'  => $appId,
                ];
            }
            if($this->p_type == 'Q'){
                pft_log('wxNotice/jobConsumer/Queue_push','push'.json_encode($params));
            }
            $jobId     = Queue::push(
                'notify',
                'WxNotify_Job',
                $params
            );
            $logData[] = ['name' => 'WxNotify_Job', 'jobId' => $jobId, 'params' => $params];
        }
        pft_log('wx/template_msg', json_encode($logData, JSON_UNESCAPED_UNICODE));

        return true;
    }

    /**
     * 短信凭证号加密编码
     *
     * @param  $n
     *
     * @return string
     */
    public static function url_sms($n)
    {
        $hashids = new \Library\Hashids\SmsHashids();

        return $hashids->encode($n);
    }

    /**
     * 短信凭证号解密编码
     *
     * @param  $n
     *
     * @return array|string
     */
    public static function url_sms_decode($n)
    {

        $hashids = new \Library\Hashids\SmsHashids();
        $decode  = $hashids->decode($n);
        if (empty($decode)) {
            $mPos = strpos($n, 'M');
            if ($mPos !== false && $mPos == 0) {
                $n = substr($n, 1);
            }
            $decode = $hashids->decode($n);
        }

        return $decode;
    }

    /***
     * 微信预订通知获取广告
     *
     * @authon caiyiqiang
     *
     * @param  int  $landId  景区id
     *
     * @return array
     */
    private function getAdvertising($landId)
    {
        $adModel     = new \Model\Advert\AdCO();
        $moduleModel = new \Model\AppCenter\ModuleList();
        $pageId      = 2;
        $adModuleId  = 13;

        $status = $moduleModel->checkModuleStauts(intval($this->aid), $adModuleId);
        pft_log('wx/template_msg', $this->aid . '的广告模块使用状态:' . json_encode($status));

        if ($status['status'] != 1) {
            return [];
        }

        $res = $adModel->getAdById($pageId, $this->aid, $landId);

        if (!$res) {
            //如果没有单独设置就查询全部产品
            $res = $adModel->getAdById($pageId, $this->aid);
        }
        if ($res) {
            $url   = MOBILE_DOMAIN . 'api/index.php?c=StatisticsPvUv&a=tempUrl&';
            $query = [
                'url'          => $res[0]['ad_url'],
                'pageIdentify' => '',
                'pageName'     => $res[0]['page_name'],
                'type'         => $res[0]['page_type'],
                'aid'          => $this->aid,
            ];

            return [
                'url'      => $url . http_build_query($query),
                'comment'  => $res[0]['ad_comment'],
                'position' => $res[0]['position'],
            ];
        }

        return [];
    }

    /***
     * 酒店订单判断有效期
     *
     * @authon linchen
     *
     * @param  array  $smsContent  内容
     *
     * @return array
     */
    private function _hotelTicketTimeHandle($smsContent, $need_qr, $tid)
    {
        $ticketModel = new Ticket();
        $ticketInfo  = $ticketModel->getTicketInfoById($tid, 'delaytype,if_verify,delaydays');
        if ($ticketInfo['delaytype'] || $ticketInfo['delaydays']) {
            $orderNotify      = new \Business\Order\OrderNotify();
            $orderTool        = new OrderTools('slave');
            $orderInfoByHotel = $orderTool->getOrderInfo($this->order_num, 'begintime,endtime,playtime');
            $beginTime        = $orderInfoByHotel['begintime'];
            $playTime         = $orderInfoByHotel['playtime'];
            $endTime          = $orderInfoByHotel['endtime'];
            $playRes          = $orderNotify->checkOrderValidityIsCountdown($playTime, $endTime);
            $beginRes         = $orderNotify->checkOrderValidityIsCountdown($beginTime, $endTime);
            $cEndtime         = date('Y年m月d日', strtotime($endTime));


            $playTimeYear = date('Y年m月d日', strtotime($playTime));
            $beginTimeYear = date('Y年m月d日', strtotime($beginTime));
            $endTimeYear = date('Y年m月d日', strtotime($endTime));

            if ($need_qr === false) {
                $need_qr = 'NEW_C_NO_QR';
                unset($smsContent['{3}']);
                unset($smsContent['{4}']);
                if ($ticketInfo['if_verify'] == 1 && $ticketInfo['delaytype'] == 2) {
                    $smsContent['{3}'] = '有效期：' . $playTimeYear . '至' . $endTimeYear;
                } else {
                    $smsContent['{3}'] = '有效期：' . $beginTimeYear . '至' . $endTimeYear;
                }
                $smsContent['{4}'] = $smsContent['{5}'];
                unset($smsContent['{5}']);
            } else {
                $need_qr = 'NEW_C';
                $getaddr = $smsContent['{6}'];
                unset($smsContent['{4}']);
                unset($smsContent['{5}']);
                unset($smsContent['{6}']);
                if ($ticketInfo['if_verify'] == 1 && $ticketInfo['delaytype'] == 2) {
                    $smsContent['{4}'] = '有效期：' . $playTimeYear . '至' . $endTimeYear;
                } else {
                    $smsContent['{4}'] = '有效期：' . $beginTimeYear . '至' . $endTimeYear;
                }
                $smsContent['{5}'] = $getaddr;
            }
            if (strtotime($playTime) == strtotime($endTime)) {
                if ($need_qr === false) {
                    $smsContent['{3}'] = '有效期：' . $cEndtime;
                } else {
                    $smsContent['{4}'] = '有效期：' . $cEndtime;
                }
            }
        }

        return ['content' => $smsContent, 'need_qr' => $need_qr];
    }

    /***
     * 判断是不是酒店联票的子票要不要发短信
     *
     * @authon linchen
     *
     * @return bool
     */
    private function _checkHotelOrderIsSend()
    {
        $cacheRedis = \Library\Cache\CacheRedis::getInstance('redis');
        $lockInfo   = $cacheRedis->get($this->hotel_order . $this->order_num);
        if ($lockInfo) {
            return true;    //存在了跳过这条
        }

        return false;
    }

    /***
     * 处理需要预约的游客短信内容
     *
     * @authon linchen
     * @date 2020-06-08
     *
     * @return array
     */
    private function _handleNeedAppointment($orderNum, $beginTime, $endTime, $title, $getaddr = '')
    {
        $smsContent = [];

        if ($getaddr) {
            $getaddr = str_replace(['【', '】'], ['“', '”'], $getaddr);
            $title = $title . '。' . $getaddr;
        }

        $orderNotifyBiz      = new \Business\Order\OrderNotify();
        $checkIsCountdownRes = $orderNotifyBiz->checkOrderValidityIsCountdown($beginTime, $endTime);
        $_bt                 = $checkIsCountdownRes['begin_time'];
        $_et                 = $checkIsCountdownRes['end_time'];
        //TODO  这个特产上线后用TOOL里面获取
        $smsContent['{1}'] = " 12301.cc/" . OrderNotify::url_sms($orderNum) . ' ';
        $smsContent['{2}'] = '有效期：' . $_bt . '至' . $_et;
        $smsContent['{3}'] = $title;

        return $smsContent;
    }

    /**
     * 判断是不是自定义短信模板
     * <AUTHOR>
     * @date 2021/1/19
     *
     * @param  PFTSMSInterface|null  $smsObj
     * @param  null  $need_qr
     *
     * @return bool
     */
    private function _isDiySmsTemp(PFTSMSInterface $smsObj = null, $need_qr = null)
    {
        $templateType = empty($smsObj->getTemplateType($need_qr)) ? '' : $smsObj->getTemplateType($need_qr);
        $checkSendDiy = in_array($templateType, self::$allowDiySmsType) ? true : false;
        $checkMethod  = method_exists($smsObj, 'doSendDiySMS') ? true : false;
        $checkIsDiy   = (isset($smsObj->smsTpl['is_diy']) && $smsObj->smsTpl['is_diy'] == 1) ? true : false;
        $checkDiyUse  = (isset($smsObj->smsTpl['use_state']) && $smsObj->smsTpl['use_state'] == 1) ? true : false;
        if ($checkSendDiy && $checkIsDiy && $checkMethod && $checkDiyUse) {
            return true;
        }

        return false;
    }

    /**
     * 获取字符串的hash后的再取摘要
     * author queyourong
     * date 2022/7/4
     * @param $value
     *
     * @return string
     */
    public static function getHash($value)
    {
        $hash = hash('sha256',$value);

        $prefix = substr($hash, 0, 2);
        $middle = substr($hash, floor(strlen($hash) / 2), 4);
        $suffix = substr($hash, -3, 2);

        return substr(md5(strrev($prefix . $suffix) . $middle), 0, 6);
    }

    /**
     * 获取国际
     * @param $mobileArea
     * @param $mobile
     * @return mixed|string
     */
    public static function getInternationalCall($mobileArea, $mobile) {
        return !empty($mobileArea) && $mobileArea != '86' && $mobile ? "{$mobileArea}-{$mobile}" : $mobile;
    }

    /**
     * 短信变量参数格式化
     * @param $variable
     * @return array|string|string[]
     */
    public static function smsVariableFormat($variable)
    {
        $variable = str_replace(["\n", " ", "　",], '', $variable);
        return str_replace(['【', '】'], ['[', ']'], $variable);
    }
}
