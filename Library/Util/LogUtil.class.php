<?php
/**
 * Created by PhpStorm.
 * User: huangxiaolong
 * Date: 2022/5/24
 * Time: 17:37
 */

namespace Library\Util;

class LogUtil
{
    // fpm 模式: tail -f -n 100 /alidata/log/mtts/aio/
    // cli 模式： tail -f -n 100 /alidata/log/mtts/cli/aio/

    /**
     * 非生产环境日志记录
     * @param $params
     * @param $path
     * @return bool|void
     */
    static public function debug($params, $path = 'aio', $logMode = 'day')
    {
        if (ENV == 'PRODUCTION' && !defined('IS_PFT_GRAY')) return;
        return self::log($params, $path, $logMode);
    }

    static public function info($params, $path = 'aio', $logMode = 'day')
    {
        return self::log($params, $path, $logMode);
    }

    /**
     * 底层日志记录，这里增加的调用方的文件名，方法名和行数。
     * @param $params
     * @param $path
     * @param $logMode
     * @return bool
     * @todo 如果有调用链信息，可以在这里做集成
     */
    private static function log($params, $path, $logMode)
    {
        if (!empty($_SERVER['REQUEST_ID']) && is_array($params)) {
            $params['rid'] = $_SERVER['REQUEST_ID'];
        }
        $log['log'] = $params;
        $str = JsonUtil::encode($log);
        return pft_log($path, $str, $logMode);
    }

    public static function cons($title, $data, $msg)
    {
        return [
            'title' => $title,
            'data' => $data,
            'msg' => $msg,
        ];
    }
}
