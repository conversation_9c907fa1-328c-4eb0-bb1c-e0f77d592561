<?php
/**
 * 预订报表的详细信息导出
 * <AUTHOR>
 * @date   2017-4-10
 */

namespace Task;

use Business\DownloadCenter\CustomField\Driver\ExportDetailOrderTwo;
use Business\DownloadCenter\CustomField\Factory as CustomFieldFactory;
use Business\JavaApi\Member\MemberRelationQuery;
use Business\Admin\ModuleConfig;
use Business\Statistics\statistics;
use Library\Controller;
use Library\CSVGener;
use Library\Tools\Helpers;
use Model\MassData\ExcelTask;
use Model\Product\Land;
use Model\Report\Template;
use Business\DownloadCenter\ExportDetail as ExportDetailBiz;
use Business\DownloadCenter\HandleExportDetailMapping as HandleExportDetailMappingBiz;

class OrderReportExportV2 extends Controller
{

    private $_CSV;
    private $_excelPath;
    private $_excelName;
    private $_titleArr;
    private $_head;
    private $_orderHeadV2;
    private $_begTime    = null;
    private $_endTime    = null;
    private $_landId     = null;
    private $_disId      = null;
    private $_countWay   = null;
    private $_searchType = null;
    private $_fid = null;
    private $_sid = null;

    //新版预订报表统计指标对应的名称
    private $_needTarget = [
        'order_ticket'      => '预订票数',
        'cancel_ticket'     => '取消票数',
        'revoke_ticket'     => '撤销票数',
        'print_num'         => '取票数量',
    ];

    //归档数据标识
    private $_archiverDb = false;
    //归档年份标识
    private $_yearMark   = '';

    //查询窗口实收金额
    private $_queryWindowRealMoney = false;

    //折扣优惠指标
    private $_needDiscountTarget = [
        '销售优惠金额',
        '采购优惠金额',
        '取消销售优惠金额',
        '取消采购优惠金额',
    ];

    /**
     * 该导出任务不进行判断 所有请求进入下载中心
     * return true
     */
    public function check($limit = '')
    {
        return ['code' => 200];
    }

    /**
     * 进入导出操作
     */
    public function run($task)
    {
        $fid    = $task['fid'];
        $noNeed = false;
        $target = Template::$noNeedTarget;
        if ($target[$fid]) {
            $noNeed = true;
        }

        //参数解析 并生成存放路径
        $params = $this->_handleParams($task, $noNeed);
        //实例化CSV操作类
        $this->_CSV = CSVGener::getInstance();
        //生成
        $this->_buildExcel($params, $noNeed);

        //压缩
        $helpClass = new Helpers();
        $helpClass->execZip($task['id']);
        //生成 更新数据库
        $excelTaskModel = new ExcelTask();
        $excelTaskModel->endTask($task['id']);
        if (empty($excelTaskModel)) {
            throw new \Exception("更新状态为已生成失败");
        }
    }
    
    /**
     * 参数解析
     */
    private function _handleParams($task, $noNeed = false)
    {
        //请求数据
        $request = $task['request'];
        //操作人id
        $fid    = $task['fid'];
        $params = json_decode($request, true);
        $isLand = isset($params['is_land']) ? $params['is_land'] : false;

        $this->_begTime    = $params['begin_date'];
        $this->_endTime    = $params['end_date'];
        $this->_landId     = $params['land_id'];
        $this->_disId      = $params['reseller_id'];
        $this->_countWay   = $params['count_way'];
        $this->_searchType = $params['search_type'];
        $this->_dateAllowQuery($this->_begTime, $this->_endTime);

        //子商户参数处理
        if (!empty($params['sub_supplier_id']) && !empty($params['sub_merchant_id'])) {
            $fid = $params['sub_supplier_id'];
            $params['member_id'] = $fid;
            unset($params['sub_supplier_id']);
        }

        $this->_fid  = $params['member_id'] ?? $task['member_id'];

        $this->_sid = $fid;

        $showSelfSonTicket = intval($params['show_self_son_ticket'] ?? 0); //1显示套票子票数据 0不显示
        //1显示捆绑票子票数据 0不显示
        $showBindSonTicket = intval($params['show_bind_son_ticket'] ?? 0);

        // 获取模板
        $statisticsBusiness = new statistics();
        $templateModel = new \Model\Report\Template();
        $template      = $templateModel->getTemplateByConfigId($params['search_config_id']);
        $params['fid'] = $fid;

        //统计纬度
        $target = json_decode($template['target'], true);
        //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
        $titleArr = [];
        $customizeTarget = ['window_real_money', 'window_cancel_money'];

        // 获取日期类型
        $dateType = $params['date_type'] ?? 1;

        //是否有查询窗口实收金额
        //if (array_intersect($customizeTarget, $target)) {
        //按日导出明细的时候，才判断展示窗口收支字段
        if ($dateType == 1) {
            //开通窗口实收金额功能的用户
            $openWindowRealMoneyUser = ModuleConfig::getSidByAppId(ModuleConfig::STATIS_REPORT_WINDOW_REAL_MONEY);
            if (in_array($fid, $openWindowRealMoneyUser)) {
                $this->_queryWindowRealMoney = true;
            } else {
                foreach ($target as $key => $value) {
                    if (in_array($value, $customizeTarget)) {
                        unset($target[$key]);
                    }
                }
            }
        }
       // }

        foreach ($target as $key => $value) {
            if (!array_key_exists($value, $this->_needTarget)) {
                continue;
            }

            $titleArr[] = $value;
        }

        $this->_titleArr = $titleArr;
        // 通过分销商数组获取分销商id
        if (isset($params['reseller_group'])) {
            $params['reseller_group'] = $statisticsBusiness->getResellerByGroup($params['reseller_group']);
        }

        // 判断模板是否有设置默认参数，同时无搜索条件
        $itemValue    = json_decode($template['item_value'], true);
        $itemNotValue = json_decode($template['item_not_value'], true);

        empty($itemValue) && $itemValue = [];
        empty($itemNotValue) && $itemNotValue = [];

        list($params['reseller_id'], $params['reseller_group'], $params['pid'], $params['lid'], $params['tid'], $params['operate_id'],
            $params['channel'], $params['pay_way'], $params['site_id'], $params['card_type'], $params['card_pid'], $params['terminal_type'],
            $params['not_pid'], $params['not_tid'])  = $statisticsBusiness->handleSearchCondition($params, $itemValue, $itemNotValue);
        //if (!empty($itemValue) && !$statisticsBusiness->reportSearchCondition($params)) {
        //    list($params['reseller_id'], $params['reseller_group'], $params['pid'], $params['lid'], $params['tid'], $params['operate_id'], $params['channel'], $params['pay_way'], $params['site_id']) = $statisticsBusiness->templateValueMap($itemValue);
        //}

        $exHead = [];
       // foreach ($titleArr as $value) {
            //$exHead[] = $this->_needTarget[$value];
      //  }

        if ($isLand) {
            $this->_orderHeadV2 = load_config('orderScenicHeadV2', 'excel_head');
        } else {
            $this->_orderHeadV2 = load_config('orderHeadV2', 'excel_head');

            //追加字段
            $exHead[] = '分时时间段';
            $exHead[] = '支付中心订单号';
            $exHead[] = '交易订单号';

            //售后字段
            $exHead[] = '售后数量';
            $exHead[] = '售后退回金额';
            $exHead[] = '售后收入金额';
            $exHead[] = '售后编号';
        }

        if (!$this->_queryWindowRealMoney && $noNeed) {
            unset($this->_orderHeadV2[16]); //移除出售单价
            unset($this->_orderHeadV2[19]); //移除下单金额
        }

        if ($this->_queryWindowRealMoney) {
            $exHead[] = '窗口收入金额';
            $exHead[] = '窗口支出金额';
        }

        //非资源账号的需要补充表头
        if (!$isLand && $this->_needDiscountTarget) {
            //需要额外的折扣优惠维度
            foreach ($this->_needDiscountTarget as $item) {
                $exHead[] = $item;
            }
        }

        $head        = $this->_reportNewHeadMerge($this->_orderHeadV2, $exHead, count($this->_orderHeadV2));
        $this->_head = $head;

        $params = $statisticsBusiness->paramsHandleNew($params, $fid);

        if ($params['code'] === false) {
            throw new \Exception($params['msg']);
        }

        $params = $params['where'];
        //EXCEL文件名
        $excelType = load_config('excelType', 'excel');
        $this->_excelName = $excelType[$task['excel_type']]['desc'];

        //EXCEL存放路径
        $this->_excelPath = EXCEL_DIR . '/' . $task['id'] . '/' . $task['id'] . '_';
        if ($isLand) {
            $params['is_land'] = $isLand;
        }

        //是否过滤子票数据
        $params['ext_filter'] = [
            'show_self_son_ticket' => $showSelfSonTicket,//套票子票
            'show_bind_son_ticket' => $showBindSonTicket,//捆绑票子票
            'filter_son_ticket'    => true,//是否处理子票过滤
        ];

        return $params;
    }

    /**
     *
     */
    private function _buildExcel($params, $noNeed = false)
    {
        $statisticsBusiness = new statistics();
        $page               = 1;
        $pageSize           = 200;

        $extInfo = $statisticsBusiness->getUploadInfoExt($this->_begTime, $this->_endTime, $this->_landId,
            $this->_disId, $this->_countWay, $this->_searchType);

        // 获取日期类型
        $dateType = $params['date_type'];
        unset($params['date_type']);

        if ($dateType == 3) {
            $file = $this->_excelPath . $this->_excelName . '_' . str_replace(':', '：', $this->_begTime ) . '-' . str_replace(':', '：', $this->_endTime ). '.csv';
        } else {
            $file = $this->_excelPath . $this->_excelName . '_' . $extInfo['begTime'] . '-' . $extInfo['endTime'] . '.csv';
        }

        $extFilter = [];
        if (isset($params['ext_filter'])) {
            $extFilter = $params['ext_filter'];
        }
        unset($params['ext_filter']);

        //获取导出自定义字段配置
        $customField = CustomFieldFactory::getDriver(ExportDetailOrderTwo::class, $this->_sid, $this->_fid);

        $oldHead = $this->_head;
        //获取新的抬头
        $head = HandleExportDetailMappingBiz::handleNewHeadByIdx($oldHead, $customField);

        $this->_CSV->CSVFileSet($file);
        $this->_CSV->CSVWrite($head);

        $loginInfo = ['memberID'=>$this->_fid];
        if ($dateType == 2) {
            $type = 3;
        } elseif ($dateType == 3) {
            //小时报表,先不走归档查询下
            // $this->_archiverDb = false;
            $type = 18;
        } elseif ($this->_queryWindowRealMoney) {
            $type = 20;
            $this->_titleArr[] = 'window_real_money';
            $this->_titleArr[] = 'window_cancel_money';
        } else {
            $type = 1;
        }

        $queryTime = ['begin' => strtotime($this->_begTime), 'end' => strtotime($this->_endTime) + (3600 * 24 -1)];

        while (true) {
            $yearMark = $this->_archiverDb ? $this->_yearMark : '';
            $data = $statisticsBusiness->setCSVObject($this->_CSV)->getOrderInfoV2($params, $this->_titleArr, $page, $pageSize, $type,
                $this->_archiverDb, $yearMark, $noNeed, $loginInfo, $queryTime, $oldHead, $customField, $extFilter);

            if (empty($data)) {
                break;
            }
           // echo '<pre/>';var_dump($this->_head, $data);exit;
            $data = is_array($data) ? $data : [];

            foreach ($data as $item) {
                //订单号转换成字符串
                $item[0] .= "\t";
                //按照导出配置重新排序下
                $item = HandleExportDetailMappingBiz::handleDataFiledByIdx($item, $oldHead, $customField);

                $this->_CSV->CSVWrite($item);
            }

            $page++;
        }
    }

    /**
     * 新版报表要求统计灵活，动态拼接excel title
     * <AUTHOR>
     *
     * @param $head
     * @param $ex
     * @param $offset
     *
     * @return mixed
     */
    private function _reportNewHeadMerge($head, $ex, $offset)
    {
        array_splice($head, $offset, 0, $ex);
        return $head;
    }

    /**
     * 判断能否查询日期参数间的报表数据
     * <AUTHOR>
     * @date 2019-01-28
     */
    private function _dateAllowQuery($startDate, $endDate)
    {
        $currentYear = date('Y');
        $startYear   = date('Y', strtotime($startDate));

        //当前历史数据只有到2016年为止
        if ((int)$startYear < 2016) {
            return false;
        }

        $endYear = date('Y', strtotime($endDate));
        $isAllow = false;

        //查询几年前的数据
        $diffYear = $currentYear - $startYear;
        //去年数据是否归档
        $isArchiverLastYear = $this->_isArchiverLastYear();

        //查询的数据未跨年
        if ($endYear == $startYear) { //非今年数据

            //查询去年数据
            if (1 == $diffYear) {
                //去年数据是否归档
                $this->_archiverDb = $isArchiverLastYear;
            }

            //查询去年之前数据
            if (1 < $diffYear) {
                $this->_archiverDb = true;
            }

            $this->_yearMark = $startYear;
            $isAllow         = true;

        } else { //跨年查询:被归档的年份前端不能进行跨年查询

            if ($endYear == $currentYear && 1 == $diffYear) {//仅支持的跨年查询
                //去年数据是否归档
                $this->_archiverDb = $isArchiverLastYear;
                //去年报表数据已被归档则不支持跨年查询
                !$isArchiverLastYear && $isAllow = true;
            }
        }

        return $isAllow;
    }

    /**
     * 去年报表数据是否归档
     * <AUTHOR>
     * @date 2019-01-28
     */
    private function _isArchiverLastYear()
    {
        return date('md') >= '0701' ? true : false;
    }
}