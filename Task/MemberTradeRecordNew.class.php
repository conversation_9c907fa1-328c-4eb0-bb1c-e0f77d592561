<?php
/**
 * 会员交易记录导出
 * <AUTHOR>
 * @date   2017-5-9
 */

namespace Task;

use Business\DownloadCenter\CustomField\Driver\ExportDetailTradeRecord;
use Business\DownloadCenter\CustomField\Driver\ExportDetailAvailableRecord;
use Business\DownloadCenter\CustomField\Driver\ExportDetailFrozenRecord;
use Business\DownloadCenter\CustomField\Factory;
use Business\DownloadCenter\CustomField\Server;
use Business\Order\OrderQueryMove;
use Business\JavaApi\Trade\Query as TradeQueryApi;


use Library\Controller;
use Library\CSVGener;
use Library\Tools\Helpers;
use Model\MassData\ExcelTask;

class MemberTradeRecordNew extends Controller
{

    private $_CSV;
    private $_excelPath;
    private $_excelName;
    private $_isAdmin = false;
    private $maxLineLimit = 100000;
    private $writtenLine = 0;
    private $fileNames = [];
    /**
     * @var OrderQueryMove
     */
    private $orderMoveBiz;
    /**
     * @var \Business\Member\Member
     */
    private $memberBiz;
    /**
     * @var \Business\JsonRpcApi\TravelVoucherService\OrderQuery
     */
    private $tvOrderBiz;


    /**
     * 检测是否需要进入报表中心  此功能默认需要
     * @return array
     */
    public function check($limit, $saccount)
    {
        //假日模式不允许导出
        $vacationBiz = new \Business\PftSystem\VacationModeBiz();
        $vacationRes = $vacationBiz->judgeForPage('tradeRecord', $saccount);
        if ($vacationRes === false) {
            return ['code' => 204, 'msg' => '当前系统处于假日模式,导出功能消耗系统资源较大,暂不可用,如有需要请待假期结束后使用,敬请谅解!'];
        } else {
            return ['code' => 200];
        }
    }

    /**
     * 开始执行
     *
     * @param $task
     *
     * @throws \Exception
     */
    public function run($task)
    {
        //权限
        if (!defined("IS_EXCEL_EXPORT")) {
            throw new \Exception("异常");
        }

        //参数解析 并生成存放路径
        $params = $this->_handleParams($task);

        //实例化CSV操作类
        $this->_CSV = CSVGener::getInstance();

        //生成
        $this->_buildExcel($task, $params);

        //压缩
        $helpClass = new Helpers();
        $helpClass->execZipByFiles($task['id'], $this->fileNames);
        //生成 更新数据库
        $excelTaskModel = new ExcelTask();
        $excelTaskModel->endTask($task['id']);
        if (empty($excelTaskModel)) {
            throw new \Exception("更新状态为已生成失败");
        }
    }

    /**
     * 导出可用金额
     * <AUTHOR>
     * @date   2024/1/23
     *
     * @param $task
     *
     * @throws
     */
    public function runAvailableBalance($task)
    {
        $task['tradeScope'] = TradeQueryApi::TRADE_SCOPE_AVAILABLE;
        $this->run($task);
    }

    /**
     * 导出冻结金额
     * <AUTHOR>
     * @date   2024/1/23
     *
     * @param $task
     *
     * @throws
     */
    public function runFrozenBalance($task)
    {
        $task['tradeScope'] = TradeQueryApi::TRADE_SCOPE_FROZEN;
        $this->run($task);
    }

    /**
     * 参数解析
     *
     * @param $task
     *
     * @return array|mixed
     * @throws \Exception
     */
    private function _handleParams($task)
    {
        //请求数据
        $request = $task['request'];
        //操作人id
        
        $params = json_decode($request, true);

        //优先处理参数
        $midArr = [$task['fid']];
        if (!empty($params['aid'])) $midArr[] = $params['aid'];
        if (!empty($params['fid'])) $midArr[] = $params['fid'];
        $memberBiz = new \Business\Member\Member();
        $memberInfoMap = $memberBiz->getMemberInfoByMulti($midArr, 'id', true);

        if ($task['fid'] == 1 && $params['fid']) {
            // 管理员模式下以集团/普通用户视角查询数据
            $params['opMember'] = $memberInfoMap[$params['fid']] ?? [];
        } else {
            $params['opMember'] = $memberInfoMap[$task['fid']] ?? [];
        }
        //管理员标记
        if ($task['fid'] == 1) {
            $this->_isAdmin = true;
        }

        $option = [];
        if ($params['aid'] && isset($memberInfoMap[$params['aid']])) {
            $option['aAccountId'] = $memberInfoMap[$params['aid']]['account_id'];
        }
        if ($params['order_id']) {
            $option['order_id'] = $params['order_id'];
        }
        if ($params['trade_no']) {
            $option['trade_no'] = $params['trade_no'];
        }

        $option['tradeScope']= $task['tradeScope'] ?? TradeQueryApi::TRADE_SCOPE_TOTAL;

        $params['option'] = $option;
        if (ENV == 'TEST') {
            $this->maxLineLimit = 10000;
        }
        //EXCEL文件名
        $excelType        = load_config('excelType', 'excel');
        $this->_excelName = $excelType[$task['excel_type']]['desc'];
        //EXCEL存放路径
        $this->_excelPath = EXCEL_DIR . $task['id'] . '/' . $task['id'] . '_';

        return $params;
    }

    /**
     * 生成
     *
     * @param $params
     */
    private function _buildExcel($task, $params)
    {
        $tradeQueryBiz = new \Business\Finance\TradeQuery();
        $this->orderMoveBiz = new OrderQueryMove();
        $this->memberBiz = new \Business\Member\Member();
        $this->tvOrderBiz = new \Business\JsonRpcApi\TravelVoucherService\OrderQuery();

        //交易记录->总金额（元交易记录导出）、可用金额导出、冻结金额导出
        $tradeScope = $params['option']['tradeScope'];
        if ($tradeScope == TradeQueryApi::TRADE_SCOPE_FROZEN) {
            //冻结金额导出
            $driverName = ExportDetailFrozenRecord::class;
            $params['subject_code'] = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
        } else if ($tradeScope == TradeQueryApi::TRADE_SCOPE_AVAILABLE) {
            //可用金额导出
            $driverName = ExportDetailAvailableRecord::class;
            $params['subject_code'] = \Library\Constants\Account\BookSubject::PLATFORM_SUBJECT_CODE;
        } else {
            //总金额导出
            $driverName = ExportDetailTradeRecord::class;
        }

        $customField = Factory::getDriver($driverName, $task['fid'], $task['member_id']);

        $this->createCSV($params, $customField);

        $list = [];
        $requestId = uniqid() . rand(1000, 9999);
        $page = 1;
        //$size = 6000;
        $queryHot = $params['query_hot'] ?? 0;
        $size = $queryHot ? 100 : 1000;
        while (1) {
            $queryRes = $tradeQueryBiz->getTradeListByPageQuery($params['opMember'], $params['begin_time'], $params['end_time'],
                $params['subject_code'], $params['item_code'], $params['trade_type'], $page++, $size, $params['option'], $requestId, $queryHot);
            if ($queryRes['code'] != 200) {
                throw new \Exception("数据获取异常");
            }
            $tmpList = $queryRes['data']['list'];
            // 获取不到翻页数据，结束循环
            if (!$tmpList) {
                break;
            }
            $list = array_merge($list, $tmpList);
            if (count($list) >= 10000) {
                $tradeList = array_splice($list, 0, 10000);
                $this->exportTradeList($tradeList, $params, $customField);
            }
            //数据量不足每页大小，提前结束循环
            if (count($tmpList) < $size) {
                break;
            }
        }
        if (count($list)) {
            $this->exportTradeList($list, $params, $customField);
        }
    }

    /**
     * 创建新的csv
     * @param array $params
     * @param Server $customField
     * @return void
     */
    private function createCSV(array $params, $customField) {
        $index = count($this->fileNames);
        //产品要求  文件名改为 交易记录_起始日期_终止日期_交易账户
        $file = $this->_getExcekFileName($params, $index);
        $head = load_config('excel_head_new', 'trade_record');
        if (!$this->_isAdmin) {
            //不是管理员，需要移除 企业名称|账户名称
            unset($head['com_name']);
            unset($head['account_name']);
            unset($head['member_id']);
            unset($head['to_member_id']);
            $head = $customField->sort($head);
        }
        $this->_CSV->CSVUnset();
        $this->_CSV->CSVFileSet($file, 4);
        $this->_CSV->CSVWrite($head);
        $this->writtenLine = 0;
        $this->fileNames[] = $file;
    }

    private function exportTradeList(array $list, array $params = [], ?Server $customField = null)
    {
        $orderArr = [];

        $tvOrderNumArr = [];
        foreach ($list as $item) {
            if ($item['order_status'] !== '') {
                $orderArr[] = (string)$item['order_id'];
            }

            if (isset($item['p_type']) && $item['p_type'] == 'Q') {
                $tvOrderNumArr[] = (string)$item['order_id'];
            }
        }
        //去重
        $orderArr = array_unique($orderArr);

        //获取下单的追踪记录
        $trackList  = [];
        while($orderArr) {
            $chunk = array_splice($orderArr, 0, 1000);

            //订单查询迁移1.1
            $tmpList = $this->orderMoveBiz->getListByOrderNumNew($chunk);

            if ($tmpList) {
                $trackList = array_merge($trackList, $tmpList);
            }
        }
        while($tvOrderNumArr) {
            $chunk = array_splice($tvOrderNumArr, 0, 1000);

            //订单查询迁移1.1
            $tmpList = $this->tvOrderBiz->getOrderInfoDetail($chunk);
            if ($tmpList) {
                $trackList = array_merge($trackList, $tmpList);
            }
        }

        $trackMap      = [];
        $tvOrderStatus = [];
        $tickets   = [];

        //产品名称
        $tidArr = array_column($list, 'tid');
        if(empty($tidArr)) {
            $tidArr = array_column($trackList, 'tid');
        }
        $javaApi   = new \Business\CommodityCenter\Ticket();
        $ticketArr = $javaApi->queryTicketInfoByIds($tidArr, 'title,id,pid', 'id', 'title,imgpath,id');
        foreach ($ticketArr as $ticketInfo) {
            $tickets[$ticketInfo['ticket']['id']] = [
                'id'      => $ticketInfo['ticket']['id'],
                'ttitle'  => $ticketInfo['ticket']['title'],
                'tid'     => $ticketInfo['ticket']['id'],
                'pid'     => $ticketInfo['ticket']['pid'],
                'title'   => $ticketInfo['land']['title'],
                'imgpath' => $ticketInfo['land']['imgpath'],
                'landid'  => $ticketInfo['land']['id'],
            ];
        }
        if ($trackList) {
            foreach ($trackList as $item) {
                $trackMap[$item['ordernum']] = $item['tid'];
                if (isset($item['p_type']) && $item['p_type'] == 'Q') {
                    $tvOrderStatus[$item['ordernum']] = $item['order_status'];
                }
            }
        }
        foreach ($list as $val) {
            if ((isset($trackMap[$val['order_id']]) && isset($val['p_type'])) || isset($tickets[$val['tid']]) ) {
                if ($val['p_type'] == 'Q') {
                    $val['pro_name'] = $tickets[$trackMap[$val['order_id']]]['title'];
                } else {
                    //TODO 临时方案  为了处理归档数据问题，名称优先从交易中心 接口 追踪记录订单查询 中获取，没有再从追踪记录中获取
                    //又因为旅游券、订单状态等特殊逻辑 又得保留旧逻辑，现改称优先从订单中获取名称 没有命中再从追踪记录中获取
                    $val['pro_name'] = $tickets[$val['tid']]['title'] . '-' . $tickets[$val['tid']]['ttitle'];
                    if(empty($val['pro_name'])){
                        $val['pro_name'] = $tickets[$trackMap[$val['order_id']]]['title'] . '-' . $tickets[$trackMap[$val['order_id']]]['ttitle'];
                    }
                }
            }

            $comName = !empty($val['self']['com_name']) ? $val['self']['com_name'] : '--';
            $accountName = '--';
            if (!empty($val['self']['acc_name']) && !empty($val['self']['account'])) {
                $accountName = $val['self']['acc_name'] . '(' . $val['self']['account'] . ')';
            }
            $memberId = '--';
            if ($val['self']['id']) {
                $memberId = $val['self']['id'];
            }

            $orderStatus = $tvOrderStatus[$val['order_id']] ?? ($val['order_status'] ?: '--');
            $tmpLine     = [
                'rectime'      => date('Y-m-d H:i:s', $val['trade_time']),
                'orderid'      => $val['order_id'] . "\t",
                'orderstatus'  => $orderStatus,
                'trade_no'     => $val['trade_no'] . "\t",
                'trade_id'     => $val['trade_id'],
                'cmb_id'       => $val['cmb_id'] ? $val['cmb_id'] . "\t" : '--',
                'pay_id'       => $val['pay_id'] ? $val['pay_id'] . "\t" : '--',
                'com_name'     => $comName,
                'member_id'    => $memberId,
                'account_name' => $accountName,
                'to_member_id' => ($val['opposite']['id'] ?? '--'),
                'to_merchant'  => $val['opposite']['acc_name'],
                'to_account'   => $val['opposite']['subject'],
                'fee_type'     => $val['item_name'],
                'pro_name'     => $val['pro_name'] ?? '',
                'subject_book' => $val['self']['subject'],
                'money'        => sprintf('%.2f', intval($val['money']) / 100),
                'balance'      => sprintf('%.2f', intval($val['left_money']) / 100),
                'memo'         => $val['remark'],
            ];

            if (!$this->_isAdmin) {
                //不是管理员，需要移除 企业名称|账户名称
                unset($tmpLine['com_name']);
                unset($tmpLine['account_name']);
                unset($tmpLine['member_id']);
                unset($tmpLine['to_member_id']);
                $tmpLine = $customField->sort($tmpLine);
            }

            $this->writeDataLine($tmpLine, $params, $customField);
        }
    }

    private function writeDataLine(array $tmpLine, array $params, $customField) {
        if ($this->writtenLine >= $this->maxLineLimit) {
            $this->createCSV($params, $customField);
        }
        $this->_CSV->CSVWrite($tmpLine);
        $this->writtenLine++;
    }

    /**
     * 获取excel文件名（含路径）
     * <AUTHOR>
     * @date 2022/2/8
     *
     * @param  array  $params  参数列表
     * @params int    $index
     * @return string
     */
    private function _getExcekFileName(array $params, int $index = 0)
    {
        $fileName = $this->_excelPath . $this->_excelName;
        $begTime  = $params['begin_time'] ?? '';
        $endTime  = $params['end_time'] ?? '';

        if (!empty($begTime) && !empty($endTime)) {
            $begTime = date('Ymd', strtotime($begTime));
            $endTime   = date('Ymd', strtotime($endTime));
            $fileName   .= '_' . $begTime . '-' . $endTime;
        }

        $indexStr = $index ? '.' . $index : '';
        return $fileName . $indexStr . '.csv';
    }
}
