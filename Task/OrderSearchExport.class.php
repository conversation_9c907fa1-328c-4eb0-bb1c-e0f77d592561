<?php
/**
 * 订单查询的导出
 * <AUTHOR>
 * @date   2017-11-21
 */

namespace Task;

use Business\Authority\DataAuthLogic;
use Business\DownloadCenter\CustomField\Driver\ExportDetailOrderSearch;
use Business\DownloadCenter\CustomField\Factory;
use Business\JavaApi\Order\Query\OrderPage;
use Library\Util\ArrayUtil;
use Model\MassData\ExcelTask;

class OrderSearchExport extends \Library\Controller
{
    private $_CSV;
    private $_excelPath;
    //选择哪个数据库
    private $_selectType;

    /**
     * 检测是否进入报表下载中心 默认需要
     *
     * @param  string  $limit
     *
     * @return array
     */
    public function check($limit = '')
    {
        return ['code' => 200];
    }

    /**
     * 调试测试使用
     * <AUTHOR>
     * @date 2020/12/29
     *
     * @return array
     */
    public function cliRun($id = null)
    {
        $id   = $id ?: 14652;
        $lib  = new ExcelTask();
        $task = $lib->getTaskById($id);

        $this->run($task);
    }

    /**
     * 导出
     *
     * @param $task
     */
    public function run($task)
    {
        try {
            //参数解析 并生成存放路径
            $params = $this->_handleParams($task);
            //实例化CSV操作类
            $this->_CSV = \Library\CSVGener::getInstance();
            //生成
            $this->_buildExcelFromOrderService($params, $task);
            //压缩
            $helpClass = new \Library\Tools\Helpers();
            $helpClass->execZip($task['id']);
            //生成 更新数据库
            (new ExcelTask())->endTask($task['id']);
        } catch (\Exception $e) {
            pft_log('order_search_export', [$task['id'], $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 参数处理
     */
    private function _handleParams($task)
    {
        //请求数据
        $request = $task['request'];

        $params            = json_decode($request, true);
        $this->_selectType = $params['time_type'];

        $res = $this->getOrderBusiness()->handleOrderParam($params, $task['fid']);
        if ($res['code'] === false) {
            throw new \Exception($res['msg']);
        }

        //EXCEL文件名
        $excelType = load_config('excelType', 'excel');

        $this->_excelName = $excelType[$task['excel_type']]['desc'];

        //EXCEL存放路径
        $this->_excelPath = EXCEL_DIR . '/' . $task['id'] . '/' . $task['id'] . '_';

        return $params;
    }

    /**
     * 通过订单服务获取订单数据
     * <AUTHOR>
     * @date 2020/12/28
     *
     * @param array $params
     * @param array $task
     *
     * @return void
     */
    private function _buildExcelFromOrderService($params, $task)
    {
        $fid = $task['fid'];
        $startTime = str_replace(':', '_', $params['order_time_start']);
        $endTime   = str_replace(':', '_', $params['order_time_end']);
        $file = $this->_excelPath . '_订单查询' . '_' . $startTime . '-' .  $endTime . '.csv';

        $head = load_config('order_search', 'exportHead');
        $head = (new \Business\Order\OrderList())->_handleSpecialOrderExport($fid, $head, [], [], true); //特殊处理下
        $this->_CSV->CSVFileSet($file);
        $memberModel = new \Model\Member\Member();
        $currentFid  = $params['member_id'];
        $memberInfo  = $memberModel->getMemberInfo($currentFid, 'id', 'dtype, account');
        $memberType  = $memberInfo['dtype'];
        $account     = $memberInfo['account'];
        $sid         = $fid;
        if (in_array($memberType, [2, 3])) {
            // 资源方账号登录，获取供应商ID
            $landApi   = new \Business\CommodityCenter\Land();
            $applyInfoRes = $landApi->queryLandMultiQueryByAdminAndPaging([], 1, 1, '', 'id desc', false, [], [], [], [],
                [$account]);
            if (!empty($applyInfoRes['list'])) {
                $sid = $applyInfoRes['list'][0]['apply_did'];
            }
        }

        if ($fid == 1 || in_array($memberType, [2, 3])) {
            //管理员 不用分组
            $key = array_search('分组', $head);
            unset($head[$key]);
            $key = array_search('下单员工', $head);
            unset($head[$key]);
            if (in_array($memberType, [2, 3])){
                $key = array_search('未验证数', $head);
                unset($head[$key]);
                $key = array_search('已过期数', $head);
                unset($head[$key]);
                $key = array_search('已完结数', $head);
                unset($head[$key]);
                $key = array_search('合并付款订单号', $head);
                unset($head[$key]);
            }
            $key = array_search('销售优惠总金额', $head);
            unset($head[$key]);
            $key = array_search('支付积分', $head);
            unset($head[$key]);
            $key = array_search('采购实付金额（扣除优惠金额）', $head);
            unset($head[$key]);
            $key = array_search('采购优惠总金额', $head);
            unset($head[$key]);
            $key = array_search('售后退回金额', $head);
            unset($head[$key]);
            $key = array_search('售后收入金额', $head);
            unset($head[$key]);
        }

        //分销商账号只要在供应商、分销商角色的导出上增加即可（商家端），资源账号、子商户、管理端导出不需要增加这个字段
        $sdtype = $params['sdtype'] ?? 0; //在新的订单查询导出中会加入该字段
        if ($fid == 1 || !in_array($sdtype, [0, 1]) || ($memberType==6 && !in_array($sdtype, [0, 1]))) {
            unset($head['reseller_account']); //去掉分销商账号
        }

        //从创建导出任务的入口增加透传判断是否显示子商户字段
        if (empty($params['needSubMerchantField']) || $fid == 1 || in_array($memberType, [2, 3])){
            $key = array_search('商户', $head);
            unset($head[$key]);
        }

        /** @var ExportDetailOrderSearch $customField */
        $customField = Factory::getDriver(ExportDetailOrderSearch::class, $task['fid'], $task['member_id']);
        $head = $customField->sort($head, null);
        $this->_CSV->CSVWrite($head);

        //子商户订单查询导出
        if (!empty($params['sub_supplier_id']) && !empty($params['sub_sid'])) {
            $currentFid = $params['sub_supplier_id'];
            $sid        = $params['sub_supplier_id'];
        }

        if ($fid == 1) {
            //管理员账户
            $this->_queryAdminList($fid, $fid, $memberType, $params, $head);
        } elseif (in_array($memberType, [2, 3])) {
            //景区账户
            $this->_queryScenicList($currentFid, $sid, $account, $memberType, $params, $head);
        } else {
            //供应商/分销商/集团账号
            $this->_queryBusinessList($currentFid, $sid, $memberType, $params, $customField, $head);
        }
    }

    /**
     * 查询管理员订单列表
     * <AUTHOR>
     * @date 2020/12/28
     *
     * @param  int  $memberId  查询用户id
     * @param  int  $sid  主账号ID
     * @param  array  $params  参数数组
     * @param  array  $head  表头数组
     *
     * @return array
     */
    private function _queryAdminList($memberId, $sid, $memberType, $params, $head)
    {
        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $params['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        //如果有传资源库ID的话
        $lidArr = false;
        if ($params['lid']) {
            $lidArr = $params['lid'] ? [$params['lid']] : false;
        } else {
            if ($params['source_id']) {
                $landApi      = new \Business\CommodityCenter\Land();
                $resourceList = $landApi->queryLandMultiQueryByResourceId([$params['source_id']]);
                $lidArr       = array_column($resourceList, 'id');
            }
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $params['pid'] ? [$params['pid']] : false;

        $params['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($params['tid']);
        $tidArr        = $params['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            $this->_CSV->CSVWrite(['一次最多可选20个票种，已超过，请重试']);
            return [];
        }

        //供应商、分销商数组，预留数组，后期会扩展成数组查询
        $sellerIdArr = $params['aid'] ? [$params['aid']] : false;

        if ($params['reseller_id']) {
            //数组转换
            $buyerIdArr = is_array($params['reseller_id']) ? $params['reseller_id'] : [$params['reseller_id']];
        } else {
            $buyerIdArr = false;
        }

        $ordernumArr = false;
        if (isset($params['ordernum']) && $params['ordernum']) {
            $ordernumArr = is_array($params['ordernum']) ? $params['ordernum'] : [$params['ordernum']];
        }

        if ($params['order_mode'] !== false) {
            $ordermodeIn = is_array($params['order_mode']) ? $params['order_mode'] : [$params['order_mode']];
        } else {
            $ordermodeIn = false;
        }

        if ($params['pay_mode'] !== false) {
            $pmodeIn = is_array($params['pay_mode']) ? $params['pay_mode'] : [$params['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        //同一次查询标识
        $requestId = OrderPage::getRequestId('adm_');

        $scrollKey = '';
        while (true) {
            $queryRes = $this->getOrderBusiness()->getAdminExportByOrderService($memberId, $sid, $memberType,
                $dbStart, $dbEnd, 1, 1000, $ordernumArr, $params['ordername'],
                $params['person_id'], $params['userMobileSubject'], $params['order_time_start'], $params['order_time_end'],
                $params['play_time_start'], $params['play_time_end'], $params['dtime_start'], $params['dtime_end'],
                $params['begin_time_start'], $params['begin_time_end'], $params['status'], $params['pay_status'],
                $pmodeIn, $ordermodeIn, $params['p_type'], $params['operate_id'], $params['check_resource'], $lidArr,
                $pidArr, $sellerIdArr, $buyerIdArr, $tidArr,$params['order_source'], $params['if_print'], $params['mark'],
                $params['begin_first_time_start'],$params['begin_first_time_end'], $params['is_combine'], $params['upstreamOrderId'],
                $params['afterSaleState'],$params['check_code'], $params['sub_type'], $requestId,$params['round_id'],
                $params['cancel_time_start'] ?? false, $params['cancel_time_end'] ?? false,
                $params['touristMobileSubject'] ?? false, $params['touristIdentificationCode'] ?? false,
                $params['cmbId'], $params['personid'], $params['remotenum'], $params['apiOrder'], $scrollKey
            );

            if ($queryRes['code'] != 200) {
                $errMsg = $queryRes['msg'];
                throw new \Exception("订单数据查询失败[{$errMsg}]", 500);
            }

            $list = $queryRes['data']['list'];
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                //订单号转换成字符串
                $item['ordernum'] .= "\t";

                $sortItem = [];
                foreach ($head as $key => $title) {
                    $sortItem[] = $item[$key];
                }
                $this->_CSV->CSVWrite($sortItem);
            }

            $scrollKey = $queryRes['data']['scrollKey'];
            if (empty($scrollKey)) {
                break;
            }
        }
    }

    /**
     * 查询集团/供应商/分销商订单列表
     * <AUTHOR>
     * @date 2020/12/28
     *
     * @param  int  $memberId  查询用户id
     * @param  int  $sid  主账号ID
     * @param  array  $memberArr  查询账号ID数组
     * @param  array  $paramArr  参数数组
     * @param  ExportDetailOrderSearch $customField
     * @param  array $head
     *
     * @return void
     */
    private function _queryBusinessList($memberId, $sid, $memberType, $paramArr, $customField, $head)
    {
        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        $lidArr = [];
        if ($paramArr['lid']) {
            //直接按选定的景区查询
            $lidArr = [$paramArr['lid']];
        } else {
            //如果有按资源库查询的话
            if ($paramArr['source_id']) {
                $landApi      = new \Business\CommodityCenter\Land();
                $resourceList = $landApi->queryLandMultiQueryByResourceId([$paramArr['source_id']]);
                $lidArr       = array_column($resourceList, 'id');
            }
        }

        if ($memberType == 7) {
            //集团账号
            $orderSearchBusiness = new \Business\Order\OrderSearch();
            $memberArr           = $orderSearchBusiness->getRelationMember($sid);
        } else {
            $memberArr = [$sid];
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;

        $paramArr['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($paramArr['tid']);
        $tidArr          = $paramArr['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            $this->_CSV->CSVWrite(['一次最多可选20个票种，已超过，请重试']);
            return [];
        }

        //供应商、分销商数组，预留数组，后期会扩展成数组查询
        $sellerIdArr = $paramArr['aid'] ? [$paramArr['aid']] : false;

        if ($paramArr['reseller_id']) {
            //数组转换
            $buyerIdArr = is_array($paramArr['reseller_id']) ? $paramArr['reseller_id'] : [$paramArr['reseller_id']];
        } else {
            $buyerIdArr = false;
        }

        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        if ($paramArr['order_mode'] !== false) {
            $ordermodeIn = is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']];
        } else {
            $ordermodeIn = false;
        }

        if ($paramArr['pay_mode'] !== false) {
            $pmodeIn = is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($memberId, $memberType);
        $condition = $dataAuthLimit->transInOrNotCondition(['lidList' => $lidArr]);
        if ($condition === false) {
            return;
        }
        $lidArr = $condition['lidList'] ?? false;
        $notLidArr = $condition['notLidList'] ?? false;

        $scrollKey = '';
        while (true) {
            $queryRes = $this->getOrderBusiness()->getBusinessExportByOrderService($memberId, $sid, $memberType,
                $memberArr, $dbStart, $dbEnd, 1, 1000, $ordernumArr, $paramArr['ordername'],
                $paramArr['person_id'], $paramArr['userMobileSubject'], $paramArr['order_time_start'], $paramArr['order_time_end'],
                $paramArr['play_time_start'], $paramArr['play_time_end'], $paramArr['dtime_start'], $paramArr['dtime_end'],
                $paramArr['begin_time_start'], $paramArr['begin_time_end'], $paramArr['status'], $paramArr['pay_status'],
                $pmodeIn, $ordermodeIn, $paramArr['p_type'], $paramArr['operate_id'], $paramArr['check_resource'], $lidArr,
                $pidArr, $sellerIdArr, $buyerIdArr, $tidArr, $paramArr['order_source'], $paramArr['if_print'], $paramArr['mark'],
                $paramArr['begin_first_time_start'],$paramArr['begin_first_time_end'], $paramArr['is_combine'], $paramArr['upstreamOrderId'],
                $paramArr['sub_sid'],$paramArr['afterSaleState'],$paramArr['check_code'], $notLidArr, $customField,
                $paramArr['sub_type'], '', $paramArr['round_id'], $paramArr['cancel_time_start'] ?? false,
                $paramArr['cancel_time_end'] ?? false, $paramArr['touristMobileSubject'] ?? false,
                $paramArr['touristIdentificationCode'] ?? false,
                $paramArr['cmbId'], $paramArr['personid'], $paramArr['remotenum'], $paramArr['apiOrder'], $scrollKey, intval($paramArr['identity_photo'] ?? 0)
            );

            if ($queryRes['code'] != 200) {
                $errMsg = $queryRes['msg'];
                throw new \Exception("订单数据查询失败[{$errMsg}]", 500);
            }

            $list = $queryRes['data']['list'];
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                //订单号转换成字符串
                $item['ordernum'] .= "\t";
                //处理不显示子商户字段
                if (empty($paramArr['needSubMerchantField'])){
                    unset($item['subSname']);
                }
                if (!isset($head['reseller_account']) && isset($item['reseller_account'])) {
                    unset($item['reseller_account']);
                }

                $item = $customField->sort($item);
                $this->_CSV->CSVWrite($item);
            }

            $scrollKey = $queryRes['data']['scrollKey'];
            if (empty($scrollKey)) {
                break;
            }
        }
    }

    /**
     * 查询景区订单列表
     * <AUTHOR>
     * @date 2020/12/28
     *
     * @param  int  $memberId  查询用户id
     * @param  int  $sid  主账号ID
     * @param  array  $paramArr  参数数组
     *
     * @return void
     */
    private function _queryScenicList($memberId, $sid, $account, $memberType, $paramArr, $head = [])
    {
        //景区数据查询
        $paramArr['salerid'] = $account;

        //查询时间段处理
        $timeTypeConf = load_config('time_type', 'orderSearch');
        $selectType   = $paramArr['time_type'];
        if (isset($timeTypeConf[$selectType])) {
            $dbStart = $timeTypeConf[$selectType][0];
            $dbEnd   = $timeTypeConf[$selectType][1] ?? date('Y-m-d');
        } else {
            $dbStart = false;
            $dbEnd   = false;
        }

        //预留数组，后期会扩展成数组查询
        $pidArr = $paramArr['pid'] ? [$paramArr['pid']] : false;

        $paramArr['tid'] = $this->getOrderBusiness()->paramParseCommaSeparated($paramArr['tid']);
        $tidArr          = $paramArr['tid'] ?: false;
        if (is_array($tidArr) && count($tidArr) > 20) {
            $this->_CSV->CSVWrite(['一次最多可选20个票种，已超过，请重试']);
            return [];
        }

        $ordernumArr = false;
        if (isset($paramArr['ordernum']) && $paramArr['ordernum']) {
            $ordernumArr = is_array($paramArr['ordernum']) ? $paramArr['ordernum'] : [$paramArr['ordernum']];
        }

        if ($paramArr['order_mode'] !== false) {
            $ordermodeIn = is_array($paramArr['order_mode']) ? $paramArr['order_mode'] : [$paramArr['order_mode']];
        } else {
            $ordermodeIn = false;
        }

        if ($paramArr['pay_mode'] !== false) {
            $pmodeIn = is_array($paramArr['pay_mode']) ? $paramArr['pay_mode'] : [$paramArr['pay_mode']];
        } else {
            $pmodeIn = false;
        }

        $headKeys = array_keys($head);

        $scrollKey = '';
        while (true) {
            $queryRes = $this->getOrderBusiness()->getScenicExportByOrderService($memberId, $sid, $memberType,
                $paramArr['salerid'], $dbStart, $dbEnd, 1, 1000, $ordernumArr, $paramArr['ordername'],
                $paramArr['person_id'], $paramArr['userMobileSubject'], $paramArr['order_time_start'], $paramArr['order_time_end'],
                $paramArr['play_time_start'], $paramArr['play_time_end'], $paramArr['dtime_start'], $paramArr['dtime_end'],
                $paramArr['begin_time_start'], $paramArr['begin_time_end'], $paramArr['status'], $paramArr['pay_status'],
                $pmodeIn, $ordermodeIn, $paramArr['p_type'], $paramArr['operate_id'], $paramArr['check_resource'], $pidArr, $tidArr,
                $paramArr['order_source'], $paramArr['if_print'], $paramArr['mark'], $paramArr['begin_first_time_start'],$paramArr['begin_first_time_end'],
                $paramArr['upstreamOrderId'],$paramArr['afterSaleState'],$paramArr['check_code'], $paramArr['sub_type'],'',$paramArr['round_id'],
                $paramArr['cancel_time_start'] ?? false, $paramArr['cancel_time_end'] ?? false,
                $paramArr['touristMobileSubject'] ?? false, $paramArr['touristIdentificationCode'] ?? false,
                $paramArr['cmbId'], $paramArr['personid'], $paramArr['remotenum'], $paramArr['apiOrder'], $scrollKey
            );

            if ($queryRes['code'] != 200) {
                $errMsg = $queryRes['msg'];
                throw new \Exception("订单数据查询失败[{$errMsg}]", 500);
            }

            $list = $queryRes['data']['list'];
            if (empty($list)) {
                break;
            }

            foreach ($list as $item) {
                //订单号转换成字符串
                $item['ordernum'] .= "\t";
                $item = ArrayUtil::intersectMerge($headKeys, $item, '');

                $this->_CSV->CSVWrite($item);
            }

            $scrollKey = $queryRes['data']['scrollKey'];
            if (empty($scrollKey)) {
                break;
            }
        }
    }

    private function getOrderBusiness()
    {
        if (!isset($this->_orderBusiness)) {
            $this->_orderBusiness = new \Business\Order\OrderList($this->_selectType);
        }

        return $this->_orderBusiness;
    }

    private function getOrderModel()
    {
        if (!isset($this->_orderReferModel)) {
            $this->_orderReferModel = new \Model\Order\OrderRefer($this->_selectType);
        }

        return $this->_orderReferModel;
    }
}
