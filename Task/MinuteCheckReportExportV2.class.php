<?php
/**
 * 验证报表（分钟）导出
 * <AUTHOR>
 * @date   2023/10/16
 */

namespace Task;

use Business\Authority\DataAuthLogic;
use Business\DownloadCenter\CustomField\Driver\ExportDetailCheckedMinuteTwo;
use Business\DownloadCenter\CustomField\Factory as CustomFieldFactory;
use Business\DownloadCenter\HandleExportDetailMapping as HandleExportDetailMappingBiz;
use Business\Report\MinuteReport as MinuteReportBiz;
use Business\Report\ReportBase as ReportBaseBiz;
use Business\Statistics\statistics;
use Business\Statistics\StatisticsHandle as StatisticsHandleBiz;
use Library\Controller;
use Library\CSVGener;
use Library\Tools\Helpers;
use Model\MassData\ExcelTask;

class MinuteCheckReportExportV2 extends Controller
{
    private $_excelPath;
    private $_excelName;
    private $_titleArr;
    private $_head;
    private $_CSV;
    private $_begTime    = null;
    private $_endTime    = null;
    private $_landId     = null;
    private $_disId      = null;
    private $_countWay   = null;
    private $_searchType = null;
    private $_sid        = null;
    private $_fid        = null;

    //新版验证报表统计指标对应的名称
    private $_needCheckTarget = [
        'finish_ticket'    => '完结票数',
        'today_ticket'     => '当日下单验证票数',
        'not_today_ticket' => '非当日下单验证票数',
        'total_ticket'     => '验证总数',
        'all_ticket'       => '总票数',
        'revoke_ticket'    => '撤销票数',
    ];

    //归档数据标识
    private $_archiverDb = false;
    //归档年份标识
    private $_yearMark = '';

    //折扣优惠指标
    private $_needDiscountTarget = [
        '验证优惠金额',
        '采购优惠金额',
        '取消验证优惠金额',
        '取消采购优惠金额',
    ];

    /**
     * 该导出任务不进行判断 所有请求进入下载中心
     * return true
     */
    public function check($limit = '')
    {
        return ['code' => 200];
    }

    /**
     * 导出
     */
    public function run($task)
    {
        //参数解析 并生成存放路径
        $params = $this->_handleParams($task);
        //实例化CSV操作类
        $this->_CSV = CSVGener::getInstance();
        //生成
        $this->_buildDetail($params);
        //压缩
        $helpClass = new Helpers();
        $helpClass->execZip($task['id']);
        //生成 更新数据库
        $excelTaskModel = new ExcelTask();
        $excelTaskModel->endTask($task['id']);
        if (empty($excelTaskModel)) {
            throw new \Exception("更新状态为已生成失败");
        }
    }

    public function runExcel($task)
    {
        //参数解析 并生成存放路径
        $params = $this->_handleExcelParams($task);
        //实例化CSV操作类
        $this->_CSV = CSVGener::getInstance();
        //生成
        $this->_buildExcel($params);

        //压缩
        $helpClass = new Helpers();
        $helpClass->execZip($task['id']);
        //生成 更新数据库
        $excelTaskModel = new ExcelTask();
        $excelTaskModel->endTask($task['id']);
        if (empty($excelTaskModel)) {
            throw new \Exception("更新状态为已生成失败");
        }
    }

    private function _handleParams($task)
    {
        //请求数据
        $request = $task['request'];
        //操作人id
        $fid    = $task['fid'];
        $params = json_decode($request, true);

        if (strtotime($params['end_date'])) {
            //查询结束时间右边不包含
            $params['end_date'] = date('Y-m-d H:i:s', strtotime($params['end_date']) - 1);
        }

        $reportBaseBiz = new ReportBaseBiz();

        $this->_begTime    = $params['begin_date'];
        $this->_endTime    = $params['end_date'];
        $this->_landId     = $params['land_id'];
        $this->_disId      = $params['reseller_id'];
        $this->_countWay   = $params['count_way'];
        $this->_searchType = $params['search_type'];

        $reportBaseBiz->dateAllowQuery($this->_begTime, $this->_endTime, '2023');
        $this->_yearMark   = $reportBaseBiz->getYearMark();
        $this->_archiverDb = $reportBaseBiz->getArchiverDb();

        $showSelfSonTicket = intval($params['show_self_son_ticket'] ?? 0); //1显示套票子票数据 0不显示
        //1显示捆绑票子票数据 0不显示
        $showBindSonTicket = intval($params['show_bind_son_ticket'] ?? 0);

        $this->_sid    = $fid;
        $this->_fid    = $params['member_id'];
        $params['fid'] = $fid;

        // 获取模板
        $statisticsBusiness = new statistics();

        $templateModel = new \Model\Report\Template();
        $template      = $templateModel->getTemplateByConfigId($params['search_config_id']);

        //统计纬度
        $target = json_decode($template['target'], true);
        //可以被统计的纬度 清除掉没用的 整理纬度对应的汉字描述
        $titleArr = [];

        foreach ($target as $key => $value) {
            if (!array_key_exists($value, $this->_needCheckTarget)) {
                continue;
            }

            $titleArr[] = $value;
        }

        $this->_titleArr = $titleArr;

        // 通过分销商数组获取分销商id
        if (isset($params['reseller_group'])) {
            $params['reseller_group'] = $statisticsBusiness->getResellerByGroup($params['reseller_group']);
        }

        // 判断模板是否有设置默认参数，同时无搜索条件
        $itemValue    = json_decode($template['item_value'], true);
        $itemNotValue = json_decode($template['item_not_value'], true);

        empty($itemValue) && $itemValue = [];
        empty($itemNotValue) && $itemNotValue = [];

        //处理下单行异步导出明细
        if (isset($params['date'])) {
            $params['date'] = trim(explode('~', $params['date'])[0] ?? '');
            if (empty($params['date'])) {
                throw new \Exception('日期查询参数错误');
            }

            //转为日期格式
            $params['date'] = date("Ymd", strtotime($params['date']));
        }

        //操作人可能为0的情况，放行
        $operateId = '';
        if ($params['operate_id'] === 0 || $params['operate_id'] === '0') {
            $operateId = 0;
        }

        list($params['reseller_id'], $params['reseller_group'], $params['pid'], $params['lid'], $params['tid'], $params['operate_id'], $params['channel'], $params['pay_way'], $params['site_id'], $params['card_type'], $params['card_pid'], $params['terminal_type'], $params['not_pid'], $params['not_tid']) = $statisticsBusiness->handleSearchCondition($params,
            $itemValue, $itemNotValue);

        if ($operateId === 0 && $params['operate_id'] === '') {
            $params['operate_id'] = 0;
        }

        $exHead = [];

        $checkHeadV2 = load_config('checkHeadV2', 'excel_head');

        //追加字段
        $exHead[] = '分时时间段';
        $exHead[] = '支付中心订单号';
        $exHead[] = '交易订单号';

        //售后字段
        $exHead[] = '售后数量';
        $exHead[] = '售后退回金额';
        $exHead[] = '售后收入金额';
        $exHead[] = '售后编号';

        //非资源账号的需要补充表头
        if ($this->_needDiscountTarget) {
            //需要额外的折扣优惠维度
            foreach ($this->_needDiscountTarget as $item) {
                $exHead[] = $item;
            }
        }

        $this->_head = $reportBaseBiz->reportNewHeadMerge($checkHeadV2, $exHead, count($checkHeadV2));

        $statisticsBusiness = new statistics();
        $params             = $statisticsBusiness->paramsHandleNew($params, $fid);
        if ($params['code'] === false) {
            throw new \Exception($params['msg']);
        }

        $params = $params['where'];

        if ($params['date']) {
            $beginMinute = date('YmdHi', strtotime($this->_begTime));
            $endMinute   = date('YmdHi', strtotime($this->_endTime));

            $params['date_minute'] = ['between', [$beginMinute, $endMinute]];
        }

        //EXCEL文件名
        $excelType = load_config('excelType', 'excel');

        $this->_excelName = $excelType[$task['excel_type']]['desc'];

        //EXCEL存放路径
        $this->_excelPath = EXCEL_DIR . '/' . $task['id'] . '/' . $task['id'] . '_';

        //是否过滤子票数据
        $params['ext_filter'] = [
            'show_self_son_ticket' => $showSelfSonTicket,//套票子票
            'show_bind_son_ticket' => $showBindSonTicket,//捆绑票子票
            'filter_son_ticket'    => true,//是否处理子票过滤
        ];

        return $params;
    }

    private function _buildDetail($params)
    {
        $statisticsBusiness = new statistics();
        $page               = 1;
        $pageSize           = 200;

        $extInfo = $statisticsBusiness->getUploadInfoExt($this->_begTime, $this->_endTime, $this->_landId,
            $this->_disId, $this->_countWay, $this->_searchType);

        if (isset($params['date_type'])) {
            unset($params['date_type']);
        }

        $extFilter = [];
        if (isset($params['ext_filter'])) {
            $extFilter = $params['ext_filter'];
        }
        unset($params['ext_filter']);

        $file = $this->_excelPath . $this->_excelName . '_' . $extInfo['begTime'] . '-' . $extInfo['endTime'] . '.csv';

        $loginfo = ['sid' => $this->_sid, 'memberID' => $this->_fid];

        //获取导出自定义字段配置
        $customField = CustomFieldFactory::getDriver(ExportDetailCheckedMinuteTwo::class, $this->_sid, $this->_fid);

        $oldHead = $this->_head;
        //获取新的抬头
        $head = HandleExportDetailMappingBiz::handleNewHeadByIdx($oldHead, $customField);

        $this->_CSV->CSVFileSet($file);
        $this->_CSV->CSVWrite($head);

        $type = 26;

        //分时段导出处理
        $dateList = $this->_splitTimeRange(strtotime($this->_begTime), strtotime($this->_endTime), 2);
        foreach ($dateList as $date) {
            $startMinute              = date('YmdHi', strtotime($date['start']));
            $endMinute                = date('YmdHi', strtotime($date['end']));
            $startDate                = date('Ymd', strtotime($date['start']));
            $endDate                  = date('Ymd', strtotime($date['end']));
            $paramsNew                = $params;
            $paramsNew['date_minute'] = ['between', [$startMinute, $endMinute]];
            $paramsNew['date']        = ['between', [$startDate, $endDate]];
            pft_log('report_export/minute_export_detail', json_encode(["开始处理分时段数据：" . $startMinute. '~' . $endMinute, "分时段数据：" . $startDate. '~' . $endDate], JSON_UNESCAPED_UNICODE));
            $page = 1;
            while (true) {
                $yearMark = $this->_archiverDb ? $this->_yearMark : '';
                $data     = $statisticsBusiness->getCheckInfoV2($paramsNew, $this->_titleArr, $page, $pageSize, $type,
                    false, $this->_archiverDb, $yearMark, $loginfo, $customField, $extFilter);

                if (empty($data)) {
                    break;
                }

                foreach ($data as $item) {
                    //订单号转换成字符串
                    $item[0] .= "\t";
                    //按照导出配置重新排序下
                    $item = HandleExportDetailMappingBiz::handleDataFiledByIdx($item, $oldHead, $customField);

                    $this->_CSV->CSVWrite($item);
                }

                $page++;
            }
        }
    }

    /**
     * 导出excel参数处理
     * <AUTHOR>
     * @date   2023/10/17
     *
     * @param $task
     *
     * @return mixed
     * @throws
     */
    private function _handleExcelParams($task)
    {
        //请求数据
        $request = $task['request'];
        //操作人id
        $fid    = $task['fid'];
        $params = json_decode($request, true);

        if (!empty($params['member_id'])) {
            $memberModel = new \Model\Member\Member();
            $memberInfo  = $memberModel->getMemberInfo($params['member_id'], 'id', 'dtype');
            //如果不是员工账号 忽略optMember
            if (!isset($memberInfo['dtype'])) {
                throw new \Exception("获取用户信息失败");
            }
        } else {
            throw new \Exception("用户参数缺失");
        }

        $params['login_info'] = [
            'sid'      => $fid,
            'memberID' => $params['member_id'],
            'dtype'    => $memberInfo['dtype'] ?? '',
        ];

        //EXCEL文件名
        $excelType        = load_config('excelType', 'excel');
        $this->_excelName = $excelType[$task['excel_type']]['desc'];

        //EXCEL存放路径
        $this->_excelPath = EXCEL_DIR . '/' . $task['id'] . '/' . $task['id'] . '_';

        return $params;
    }

    private function _buildExcel($params)
    {
        $loginInfo = $params['login_info'] ?? [];
        if (isset($params['login_info'])) {
            unset($params['login_info']);
        }

        $minuteReportBiz     = new MinuteReportBiz();
        $statisticsHandleBiz = new StatisticsHandleBiz();

        $page     = 1;
        $pageSize = 200;

        $file = $this->_excelPath . $this->_excelName . '_' . date("YmdHi",
                strtotime($params['begin'])) . '-' . date("YmdHi", strtotime($params['end'])) . '.csv';

        $this->_CSV->CSVFileSet($file);

        // 获取数据权限
        $dataAuthLimit = DataAuthLogic::getInstance()->getDataJobsLimit($loginInfo['memberID'], $loginInfo['dtype']);

        do {
            $params['page']      = $page;
            $params['page_size'] = $pageSize;

            $res = $minuteReportBiz->minuteCheckTwoList($params, $loginInfo, $dataAuthLimit);
            if (empty($res['data']['list'])) {
                break;
            }

            $data       = $res['data'] ?? [];
            $targetItem = $data['title_list'] ?? [];
            $excelData  = $data['list'] ?? [];
            $sum        = $data['sum'] ?? [];
            $title      = $data['title'] ?? '';
            $target     = $data['target'] ?? [];

            $headTitleKeys = array_keys($targetItem);

            if ($page == 1) {
                //不存在抬头字段，直接导出空文件
                if (empty($targetItem)) {
                    $this->_CSV->CSVWrite(['报表导出-异常-无数据']);
                    break;
                }

                if (!empty($title)) {
                    $this->_CSV->CSVWrite([$title]);
                }

                //抬头
                $this->_CSV->CSVWrite(array_values($targetItem));

                //需要调整下总计字段排序
                $sum         = $statisticsHandleBiz->handleListHeadFieldSort($sum, $target);
                $maxCountNum = count($targetItem) - count($sum);
                for ($i = 0; $i < $maxCountNum; $i++) {
                    if ($i == 0) {
                        //补充总计字段
                        array_unshift($sum, '总计：');
                        continue;
                    }
                    //站位空格
                    array_unshift($sum, '');
                }

                //总计
                $this->_CSV->CSVWrite(array_values($sum));
            }

            //导出数据处理
            $excelData = $statisticsHandleBiz->handleBackDataFormat($excelData, $headTitleKeys);
            foreach ($excelData as $item) {
                $this->_CSV->CSVWrite($item);
            }

        } while ($page++);
    }

    /**
     * 时段分割
     * <AUTHOR>
     * @date   2025/05/09
     *
     * @param  int|null  $startTimestamp    开始时间戳
     * @param  int|null  $endTimestamp      结束时间戳
     * @param  int  $stepDays          步进天数
     *
     * @return array
     */
    private function _splitTimeRange($startTimestamp, $endTimestamp, $stepDays = 1)
    {
        if (empty($startTimestamp) || empty($endTimestamp)) {
            return [];
        }

        $result = [];

        $currentStart = strtotime(date('Y-m-d 00:00:00', $startTimestamp));

        while ($currentStart < $endTimestamp) {
            // 计算当前步进的结束时间
            $currentEnd = strtotime("+$stepDays days", $currentStart) - 1;

            // 如果超过结束时间，则使用最终时间
            if ($currentEnd > $endTimestamp) {
                $currentEnd = $endTimestamp;
            }

            if (empty($result) && $currentStart != $startTimestamp) {
                $currentStart = $startTimestamp;
            }

            // 添加到结果数组
            $result[] = [
                'start' => date('Y-m-d H:i:s', $currentStart),
                'end'   => date('Y-m-d H:i:s', $currentEnd),
            ];

            // 下一个起始时间为当前结束时间 + 1 秒
            $currentStart = $currentEnd + 1;
        }

        return $result;
    }
}